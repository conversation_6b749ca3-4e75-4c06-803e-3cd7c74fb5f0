<template>
  <div v-loading="loadingManager"
       class="box_Board">
    <div class="border_table">
      <!-- 搜索 -->
      <div class="border_table_header"
           style="display:flex;justify-content: space-between;align-items: center;">
        <div class="vertical"></div>
        <div class="border_table_header_title">分管理人结构
          <!-- <img alt="" src="../../../../../assets/img/question.png"> -->
        </div>
        <div class="border_table_header_search">
          <img alt=""
               src="../../../../../assets/img/download.png"
               @click="downloadExcel()">
        </div>
      </div>
      <el-divider></el-divider>
      <!-- 表格 -->
      <el-table class='kanbantable' :data="tableData"
                border
                stripe>
        <el-table-column align="gotoleft"
                         label=""
                         fixed
                         min-width='200px'
                         prop="value0" />
        <el-table-column v-for="(item,index) in column"
                         :key="index"
                         :label="item.title+'（亿）'"
                         :prop="item.value" />
        <el-empty :image-size="180" />
      </el-table>
    </div>
  </div>
</template>

<script>
import { getPerformanceBoardManagerAccount } from '@/api/pages/tkdesign/performance'
import { filter_json_to_excel_inside, changColumnToRow, filter_json_to_excel_inside_multiHeader } from '@/utils/exportExcel.js';

export default {
  props: [
    'params',
  ],
  data () {
    return {
      loadingManager: false,
      pageIndex: 1,// 当前页码
      pageSize: 10,// 页面显示几条数据
      tableData: [],// 页面表格数据源
      column: []
    };
  },
  mounted () {
  },
  watch: {
    params: {
      deep: true,
      immediate: true,
      handler (n) {
        this.getPerformanceBoardManagerAccount()
      }
    },
  },
  methods: {

    // 每页条数改变时触发的回调
    sizeChange (value) {
      this.pageSize = value
    },
    sizeChangeRecord (value) {
      this.pageSizeRecord = value
    },

    /**
     * 分管理人结构
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param measure 1-期末账面价值；2-平均资金占用
     * @param typeFlag 1-分管理人结构；2-分资产类型结构
     * @param exclude 是否剔除税延及专属商业养老。true；false
     * @param commission 是否是税费后。ture；false
     *
     */
    getPerformanceBoardManagerAccount () {
      if (this.params?.time?.length == 0) return false
      // console.log(this.params.time, this.moment(this.params.time[0]).format('YYYY-MM-DD'), this.moment(this.params.time[1]).format('YYYY-MM-DD'))
      this.loadingManager = true
      let params = {
        startDate: this.moment(this.params.time[0]).format('YYYY-MM-DD'),
        endDate: this.moment(this.params.time[1]).format('YYYY-MM-DD'),
        measure: this.params.measure,
        typeFlag: 1,
        commission: this.params.checkedTax,
        exclude: this.params.checkedOld
      }
      getPerformanceBoardManagerAccount(params).then(res => {
        this.loadingManager = false
        if (res && (res.mtycode == 200 || res.code == 200)) {
          let arr = []
          let data = res.data
          this.column = [];
          data.data && data.data.map((header, index) => {
            this.column.push({ title: header, value: `value${index + 1}` });
          })
          data.dataList.forEach((item) => {
            if (item.valueList.length > 0) {
              const line = {};
              line.value0 = item.name;
              item.valueList.forEach((citem, index) => {
                line[`value${index + 1}`] = citem.value == 'NaN' ? '--' : (citem.value / *********).toFixed(2);
              })
              arr.push(line);
            }
          })
          // console.log(arr);
          this.tableData = arr
        }
      })
    },
    downloadExcel () {
      const title = [
        { label: '', value: 'value0', format: '' },
      ];
      this.column && this.column.map(col => {
        const temp = {
          label: col.title,
          value: col.value,
        }
        title.push(temp);
      })

      filter_json_to_excel_inside(title, this.tableData, [], '分管理人业绩');
    },
  },

}
</script>
<style>
.kanbantable .el-table__fixed .cell {
	line-height: 47px !important;
}
</style>
<style lang="scss" scoped>
.box_Board {
	padding: 0 24px 16px 24px;
}

.vertical {
	width: 6px;
	height: 20px;
	border-radius: 35px;
	background: #4096ff;
	position: absolute;
}

.border_table {
	padding: 16px 24px;
	background: white;

	.border_table_header {
		margin-top: 7px;

		.border_table_header_title {
			color: rgba(0, 0, 0, 0.85);
			text-align: center;
			font-size: 16px;
			font-style: normal;
			font-weight: 500;
			line-height: 26px; /* 150% */
			margin-left: 18px;
		}

		.border_table_header_search {
			display: flex;
			cursor: pointer;

			.search-security {
				width: 250px;
				margin-right: 10px;
			}
		}
	}
}

.header_inactive {
	font-size: 14px;
	font-weight: 400;
	line-height: 22px;
	text-align: left;
	color: rgba(0, 0, 0, 0.45);
}

.header_active {
	font-size: 14px;
	font-weight: 400;
	line-height: 22px;
	text-align: left;
	color: rgba(0, 0, 0, 0.85);
}

.demo-date-picker {
	display: flex;
	width: 100%;
	padding: 0;
	flex-wrap: wrap;
}

.demo-date-picker .block {
	padding: 30px 0;
	text-align: center;
	border-right: solid 1px var(--el-border-color);
	flex: 1;
}

.demo-date-picker .block:last-child {
	border-right: none;
}

.demo-date-picker .demonstration {
	display: block;
	color: var(--el-text-color-secondary);
	font-size: 14px;
	margin-bottom: 20px;
}
</style>
