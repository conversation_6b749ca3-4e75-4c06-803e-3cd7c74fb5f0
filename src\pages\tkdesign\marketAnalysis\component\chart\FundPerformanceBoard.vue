<template>
  <div v-show="show">
    <div style="page-break-inside: avoid; position: relative">
      <div>
        <div style="page-break-inside: avoid; position: relative">
          <div class="charts_fill_class">
            <v-chart v-show="!industryrequestflag"
                     class="charts_one_class"
                     ref="ValuationPercentileChart"
                     autoresize
                     v-loading="industryrequestflag"
                     :options="industryoption"
                     @legendselectchanged="legendselectchanged" />
            <el-empty v-show="industryrequestflag"
                      description="暂无数据"></el-empty>
          </div>
        </div>
        <div class="legend-custom flex_center">
          <div v-for="item in legendName"
               :key="item.key"
               class="legend-icon">
            <div v-if="item.type == 'custom'"
                 class="flex_start mr-24">
              <!-- color:rgba(0,0,0,0.45) -->

              <div @click="changeShowHide(item.name)"
                   style="height: 100%">
                <div class="legend-line mr-8"
                     :style="`${hideList.includes(item.name) ? 'background:rgba(0,0,0,0.45)' : 'background:' + item.color}`"></div>
              </div>
              <div>
                <el-cascader ref="indexType"
                             v-model="indexCode"
                             :options="IndexTypeOption"
                             @change="resolveFather"
                             placeholder="选择指数">
                </el-cascader>
              </div>
            </div>
            <div v-else
                 class="flex_start mr-24"
                 @click="changeShowHide(item.name)">
              <div class="legend-line mr-8"
                   :style="`${hideList.includes(item.name) ? 'background:rgba(0,0,0,0.45)' : 'background:' + item.color}`"></div>
              <div :style="`${hideList.includes(item.name) ? 'color:rgba(0,0,0,0.45)' : ''}`">{{ item.name }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// 行业评价
import VChart from 'vue-echarts';
import { exportTitle, exportChart } from '@/utils/exportWord.js';
import { lineChartOption } from '@/utils/chartStyle.js';
import { filter_json_to_excel } from '../../../../../utils/exportExcel';
export default {
  name: 'ValuationPercentileChart',
  components: {
    VChart
  },
  data () {
    // date		String		时间
    // excess		String		超额收益
    // contrastReturn				对比指数收益
    // returnCum				指数收益
    // drawdown				回撤
    let legendName = {
      medicine: { name: '指数收益', key: 'returnCum', color: '#4096ff' },
      contrastReturn: { name: '沪深300', key: 'contrastReturn', color: '#4096ff', type: 'custom' },
      excessReturn: { name: '超额收益曲线', key: 'excess', color: '#7388A9' },
      maximumDrawdown: { name: '回撤', key: 'drawdown', color: '#389E0D' }
    };
    return {
      industryrequestflag: true,
      show: true,
      info: {},
      legendName: legendName,
      industryoption: null,
      selected: {},
      color: ['#4096ff', '#4096ff', '#7388A9', '#389E0D'],
      indexCode: '',
      IndexTypeOption: [],
      hideList: [],
      tableHeader: [],
      tableData: []
    };
  },
  methods: {
    exportExcel () {
      let list = this.tableHeader.map((item) => {
        return {
          ...item,
          value: item.prop,
          format: ''
        };
      });
      filter_json_to_excel(list, this.tableData, '主动权益基金整体拟合业绩图表');
    },
    // 获取父组件传递数据
    getData (data, info) {
      this.show = true;
      this.info = info;
      this.IndexTypeOption = this.info.IndexTypeOption;
      this.indexCode = this.info.indexCode;
      // data = this.formatData(data);
      this.industryrequestflag = false;
      this.industryoption = this.getIndustryoption(data, info);
      console.log(this.industryoption);
    },
    // 切换图例的显示隐藏
    changeShowHide (key) {
      let index = this.hideList.indexOf(key);
      if (index != -1) {
        this.hideList.splice(index, 1);
      } else {
        this.hideList.push(key);
      }
      let nameList = Array.from(new Set(this.industryoption.series.map((v) => v.name)));
      let legend = {
        show: false,
        data: nameList,
        selected: {}
      };
      this.hideList.map((v) => {
        legend.selected[v] = false;
      });
      this.industryoption = {
        ...this.industryoption,
        legend
      };
    },
    // 计算最大最小值
    computedMinMax (data) {
      let value_all = [];
      data.map((obj) => {
        value_all.push(...[obj.excess, obj.returnCum, obj.contrastReturn]);
      });
      return { min: Math.min(...value_all), max: Math.max(...value_all) };
    },
    getIndustryoption (chartData, info = {}) {
      let data = chartData;
      let icon = {
        dashed:
          'path://M304.43 532.76H221.4c-11.47 0-20.76-9.3-20.76-20.76s9.29-20.76 20.76-20.76h83.03c11.47 0 20.76 9.3 20.76 20.76s-9.29 20.76-20.76 20.76zM581.19 532.76H442.81c-11.47 0-20.76-9.3-20.76-20.76s9.29-20.76 20.76-20.76h138.38c11.47 0 20.76 9.3 20.76 20.76s-9.3 20.76-20.76 20.76zM802.59 532.76h-83.03c-11.47 0-20.76-9.3-20.76-20.76s9.29-20.76 20.76-20.76h83.03c11.47 0 20.76 9.3 20.76 20.76s-9.29 20.76-20.76 20.76z'
      };
      this.legendName.contrastReturn.name = info.contrastReturnName;

      let currentLegend = [];
      currentLegend = [
        { name: this.legendName.medicine.name, icon: 'line' },
        { name: this.legendName.contrastReturn.name, icon: 'line' },
        { name: this.legendName.excessReturn.name, icon: 'rect' },
        { name: this.legendName.maximumDrawdown.name, icon: 'line' }
      ];
      this.tableData = data;
      this.tableHeader = [
        {
          label: '日期',
          prop: 'date'
        },
        {
          label: this.legendName.medicine.name,
          prop: this.legendName.medicine.key
        },
        {
          label: this.legendName.contrastReturn.name,
          prop: this.legendName.contrastReturn.key
        },
        {
          label: this.legendName.excessReturn.name,
          prop: this.legendName.excessReturn.key
        },
        {
          label: this.legendName.maximumDrawdown.name,
          prop: this.legendName.maximumDrawdown.key
        }
      ];

      return lineChartOption({
        toolbox: 'none',
        color: this.color,
        grid: {
          top: '36px'
        },
        legend: { data: [] },
        tooltip: {
          backgroundColor: '#ffffff',
          formatter: function (obj) {
            var value = `<div style="font-size:14px;">` + obj?.[0].axisValue + `</div>`;
            for (let i = 0; i < obj.length; i++) {
              value +=
                `<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
                `<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
                obj?.[i].color +
                `;"></div>` +
                `<div style="font-family: PingFang SC;">` +
                obj?.[i].seriesName +
                '</div></div>' +
                `<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
                (Number(obj?.[i].value?.[1]) * 100).toFixed(2) +
                '%</div>' +
                `</div>`;
            }
            return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
          }
        },
        xAxis: [
          {
            data: data.map((item) => {
              return item.date;
            }),
            isAlign: true,
            axisLabelRotate: -45
          }
        ],
        yAxis: [
          {
            name: '收益率',
            min: this.computedMinMax(data).min,
            max: this.computedMinMax(data).max,
            formatter: function (obj) {
              return (obj * 100 > 10 || -(obj * 100) > 10 ? (obj * 100).toFixed(0) : (obj * 100).toFixed(2)) + '%';
            }
          },
          {
            name: '回撤',
            max: 0,
            min: 0 - this.computedMinMax(data).max - this.computedMinMax(data).max,
            formatter: function (obj) {
              return (obj * 100 > 10 || -(obj * 100) > 10 ? (obj * 100).toFixed(0) : (obj * 100).toFixed(2)) + '%';
            }
          }
        ],
        series: [
          {
            name: this.legendName.medicine.name,
            type: 'line',
            symbol: 'none',
            data: data.map((item) => {
              return [item.date, item[this.legendName.medicine.key]];
            })
          },
          {
            name: this.legendName.contrastReturn.name,
            type: 'line',
            symbol: 'none',
            data: data.map((item) => {
              //
              //todo: typeLegendName
              return [item.date, item[this.legendName.contrastReturn.key]];
            })
          },
          // {
          //   name: this.legendName.zhongzhen1000.name,
          //   type: 'line',
          //   symbol:'none',
          //   areaStyle:{},
          //   lineStyle:{
          //     opacity:0
          //  },
          //   data: data.map((item) => {
          //     return item[this.legendName.zhongzhen1000.key]||'--'
          //   })
          // },
          {
            name: this.legendName.excessReturn.name,
            type: 'line',
            // yAxisIndex: 1,
            areaStyle: {},
            lineStyle: {
              opacity: 0
            },
            data: data.map((item) => {
              return [item.date, item[this.legendName.excessReturn.key]];
            })
          },
          {
            name: this.legendName.maximumDrawdown.name,
            type: 'line',
            areaStyle: {},
            symbol: 'none',
            yAxisIndex: 1,
            // lineStyle: {
            //   type: 'dashed'
            // },
            data: data.map((item) => {
              return [item.date, item[this.legendName.maximumDrawdown.key]];
            })
          }
        ]
      });
    },
    // 向父组件传递数据
    resolveFather (val) {
      this.indexCode = val;
      this.$emit('resolveFather', this.indexCode);
    },
    // // 格式化接收数据
    // formatData (data) {
    //   let errData = [];
    //   let infoData = [];
    //   data.map((item) => {
    //     if (item.industry_rank == '--') {
    //       errData.push(item);
    //     } else {
    //       infoData.push(item);
    //     }
    //   });
    //   infoData = infoData
    //     .sort((a, b) => {
    //       return b.industry_rank - a.industry_rank;
    //     })
    //     .map((item) => {
    //       if (this.info.flag == 2) {
    //         return {
    //           ...item,
    //           weight_top: item.weight_top * 100,
    //           weight_other: item.weight_other * 100,
    //           weight_bottom: item.weight_bottom * 100
    //         };
    //       } else {
    //         return { ...item };
    //       }
    //     });
    //   let allData = [...infoData].filter((item) => {
    //     return item.industry_name !== '--';
    //   });
    //   if (allData.length == 0) {
    //     return [...infoData];
    //   } else {
    //     return allData;
    //   }
    // },
    // 数据获取失败
    hideLoading () {
      this.industryrequestflag = false;
      this.show = false;
    },
    legendselectchanged (params) {
      this.selected = params.selected;
      this.$emit('legendselectchanged', params);
    }
    // createPrintWord () {
    //   this.$refs['industryEvaluation'].mergeOptions({ toolbox: { show: false } });
    //   let height = this.$refs['industryEvaluation'].$el.clientHeight;
    //   let width = this.$refs['industryEvaluation'].$el.clientWidth;
    //   let chart = this.$refs['industryEvaluation'].getDataURL({
    //     type: 'png',
    //     pixelRatio: 2,
    //     backgroundColor: '#fff'
    //   });
    //   this.$refs['industryEvaluation'].mergeOptions({ toolbox: { show: true } });
    //   return this.show ? [...exportTitle('行业评价'), ...exportChart(chart, { width, height })] : [];
    // }
  }
};
</script>
<style lang="scss" scoped>
.legend-custom {
	width: 100%;
	.legend-icon {
		cursor: pointer;
		.legend-line {
			width: 12px;
			height: 2px;
		}
	}
}
</style>
