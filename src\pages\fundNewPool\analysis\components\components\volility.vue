<template>
  <div v-show="show"
       v-loading="loading">
    <div style="display: flex; justify-content: space-between; align-items: center; margin: 4px 0">
      <div class="title"
           style="font-style: normal; font-weight: 500; font-size: 16px; line-height: 24px; color: rgba(0, 0, 0, 0.85);padding-top:16px">收益波动比</div>
    </div>
    <div>
      <v-chart ref="volility"
               class="charts_two_class"
               style="width: 100%; height: 400px"
               v-loading="loading"
               autoresize
               :options="option"
               element-loading-text="暂无数据"
               element-loading-spinner="el-icon-document-delete"
               element-loading-background="rgba(239, 239, 239, 0.5)"
               @click="goFundDetail"></v-chart>
    </div>
  </div>
</template>

<script>
import { getCombinationRisk } from "@/api/pages/tools/pool.js";

import { alphaGo } from "@/assets/js/alpha_type.js";

export default {
  props: {
    dateC: {},

  },
  data () {
    return {
      loading: true,
      option: {},
      zoom: 1,
      show: true,
      info: {},
      data: [],
      max: 0,
      ismanager: false,
    };
  },
  methods: {
    async getData (info) {
      this.info = info;
      this.loading = true;
      let { data, mtycode, mtymessage } = await getCombinationRisk({
        ids: [{ code: this.info.code, type: "pool" }],
        insert_time: this.info.date,
        yearqtr: this.info.quarter,
        flag: 5,
        ismanager: this.ismanager,
        type: this.info.type
      });
      this.loading = false;
      if (mtycode == 200) {
        this.show = true;
        let datas = [];
        datas.push(data?.fund_pb_roe?.value);
        datas.push(data?.date);
        let max = 0;
        for (let i = 0; i < data.fund_pb_roe.value.length; i++) {
          for (let j = 0; j < data.fund_pb_roe.value[i].length; j++) {
            if (max < Number(data.fund_pb_roe.value[i][j][4]))
              max = Number(data.fund_pb_roe.value[i][j][4]);
          }
        }
        this.data = datas;
        this.max = max;
        this.drawvalua(datas, max);
      } else {
        this.show = false;
      }
    },
    refresInfo (info) {
      this.info = info;
      this.drawvalua(this.data, this.max);
    },
    drawvalua (data, max) {
      let tempaa = "";
      if (this.fundtype == "hkequity" || this.fundtype == "equityhk") {
        tempaa = "港股分布（基于一致ROE）";
      } else {
        tempaa = "转债股性债性";
      }
      if (
        data[0] == null ||
        data[0] == {} ||
        data[0] == [] ||
        data[0] == "数据缺失" ||
        data[0] == "" ||
        data[1] == null ||
        data[1] == {} ||
        data[1] == [] ||
        data[0] == "数据缺失" ||
        data[0] == ""
      ) {
        this.show = false;
      } else {
        //每个点的样式
        var itemStyle = {
          color: "#4096FF",
          opacity: 0.8,
          shadowBlur: 5,
          shadowOffsetX: 0,
          shadowOffsetY: 0,
          shadowColor: "rgba(0, 0, 0, 0.5)"
        };
        //每个点的大小
        var sizeFunction = function (x) {
          var y = Math.sqrt(x / 5e8) + 0.1;
          return y * 80;
        };
        // Schema:tooptip提示插件包含的元素
        var schema = [
          {
            name: "Income",
            index: 0,
            text: "roe_est",
            unit: ""
          },
          {
            name: "LifeExpectancy",
            index: 1,
            text: "pb",
            unit: ""
          },
          {
            name: "Population",
            index: 2,
            text: "点大小",
            unit: ""
          },
          {
            name: "Country",
            index: 3,
            text: "估值",
            unit: ""
          }
        ];

        // 绘制图表
        let option = {
          baseOption: {
            timeline: {
              axisType: "category",
              orient: "horizontal",
              autoPlay: true,
              inverse: true, //顺序 从小到大 1800->2015
              playInterval: 5000, //数据点渲染间隔
              left: null,
              left: 0,
              bottom: 0,
              height: "40px",
              width: "98%",
              label: {
                fontSize: "14px",
                color: "#000" //时间（标签）节点的样式
              },
              symbol: "none",
              lineStyle: {
                color: "#000" //分隔线样式
              },
              checkpointStyle: {
                //当前时间节点样式（所在目标点样式）
                color: "#fff",
                borderColor: "#000",
                borderWidth: 2
              },
              controlStyle: {
                showNextBtn: false, //上一个
                showPrevBtn: false, //下一个
                color: "#666",
                borderColor: "#666"
              },
              emphasis: {
                label: {
                  color: "#409eff"
                },
                controlStyle: {
                  color: "#aaa",
                  borderColor: "#aaa"
                }
              },
              data: []
            },
            backgroundColor: "#ffffff",
            // title: [
            // 	{
            // 		text: data[1][0],
            // 		textAlign: 'center',
            // 		left: '63%',
            // 		top: '65%',
            // 		textStyle: {
            // 			fontSize: '18px',
            // 			color: 'rgba(0, 0, 0, 0.2)'
            // 		}
            // 	},
            // 	{
            // 		text: tempaa,
            // 		left: 'center',
            // 		top: 10,
            // 		textStyle: {
            // 			color: '#000',
            // 			fontWeight: 'normal',
            // 			fontSize: '18px'
            // 		}
            // 	}
            // ],
            tooltip: {
              padding: 5,
              backgroundColor: "#222",
              borderColor: "#777",
              borderWidth: 1,
              textStyle: {
                fontSize: "18px"
              },
              formatter: function (obj) {
                var value = obj.value;
                return (
                  "名称:" +
                  value[3] +
                  "<br>" +
                  "年度:" +
                  value[2] +
                  "<br>" +
                  "年化收益:" +
                  (value[0] * 100).toFixed(2) +
                  "%" +
                  "<br>" +
                  "年化波动率:" +
                  (value[1] * 100).toFixed(2) +
                  "%" +
                  "<br>" +
                  "规模:" +
                  (value[4]).toFixed(2) +
                  "亿<br>" +
                  "所属类:" +
                  value[6] +
                  "<br>"
                );
              }
            },
            grid: {
              top: "32px",
              // containLabel: true
              left: "48px",
              right: "80px",
              bottom: "64px"
            },
            xAxis: {
              margin: 12,
              scale: true,
              type: "value",
              name: "年化收益",
              // min: 0,
              // nameGap: 25,
              // nameLocation: 'middle',
              nameTextStyle: {
                "font-family": "Helvetica Neue",
                "font-style": "normal",
                "font-weight": 400,
                "font-size": "12px",
                "line-height": "20px",
                color: "rgba(0, 0, 0, 0.65)"
              },
              splitLine: {
                show: true,
                lineStyle: {
                  type: "dashed"
                }
              },
              axisLine: {
                show: false,
                lineStyle: {
                  color: "#e9e9e9"
                }
              },
              axisLabel: {
                fontSize: "18px",
                color: "rgba(0,0,0,0.65)",
                formatter (val) {
                  return (val * 100).toFixed(1) + "%";
                }
              }
            },
            yAxis: {
              margin: 16,
              // scale: true,
              type: "value",
              name: "年化波动率",
              // min: 0,
              nameTextStyle: {
                "font-family": "Helvetica Neue",
                "font-style": "normal",
                "font-weight": 400,
                "font-size": "12px",
                "line-height": "20px",
                color: "rgba(0, 0, 0, 0.65)"
              },
              axisLine: {
                show: false,
                lineStyle: {
                  color: "#e9e9e9"
                }
              },
              splitLine: {
                show: true,
                lineStyle: {
                  type: "dashed"
                }
              },

              axisLabel: {
                fontSize: "16px",
                color: "rgba(0,0,0,0.65)",
                formatter (val) {
                  return (val * 100).toFixed(1) + "%";
                }
              },
              formatter (val) {
                return (val * 100).toFixed(2) + "%";
              }
            },
            visualMap: [
              // {
              // 	show: false,
              // 	// categories: ['估值', '估值self'],
              // 	calculable: true,
              // 	precision: 0.1,
              // 	textGap: 30,
              // 	textStyle: {
              // 		fontSize: '18px',
              // 		color: '#ccc'
              // 	},
              // 	inRange: {
              // 		fontSize: '18px',
              // 		color: (function () {
              // 			var colors = ['#4096ff']; //[ '#9dc5c8','#edc1a5', '#e88f70','#bcd3bb',  '#e1e8c8', '#7b7c68', '#e5b5b5', '#f0b489', '#928ea8', '#bda29a'];
              // 			return colors.concat(colors);
              // 		})()
              // 	}
              // },
              {
                show: false,
                right: "0px",
                top: "150px",
                min: 0,
                max: max,
                dimension: 4,
                itemWidth: 30,
                itemHeight: 120,
                precision: 0.1,
                text: ["配置权重%"],
                textGap: 0.0,
                textStyle: {
                  color: "black",
                  fontSize: 10
                },
                inRange: {
                  symbolSize: [10, 20]
                },
                controller: {
                  inRange: {
                    color: ["black"]
                  }
                }
              }
            ],
            series: [
              {
                type: "scatter",
                itemStyle: itemStyle,
                data: data[0][0]
              }
            ],
            animationDurationUpdate: 1000,
            animationEasingUpdate: "quinticInOut"
          },
          options: []
        };
        for (var n = 0; n < data[1].length; n++) {
          option.baseOption.timeline.data.push(data[1][n]);
          option.options.push({
            toolbox: {
              feature: {
                saveAsImage: {}
              },
              top: -4,
              width: 104
            },
            // title: {
            // 	show: true,
            // 	text: data[1][n] + '',
            // 	textStyle: {
            // 		fontSize: '18px'
            // 	}
            // },
            graphic: [
              {
                type: "group",
                left: "center",
                top: "50",
                children: [
                  {
                    type: "text",
                    z: 100,
                    left: "center",
                    top: "50",
                    style: {
                      fill: "#333",
                      text: data[1][n] + "",
                      font: "14px" + " Microsoft YaHei",
                      colors: "#4096FF"
                    }
                  }
                ]
              }
            ],
            series: [
              // {
              // 	name: data[1][1],
              // 	type: 'line',
              // 	symbol: 'none',
              // 	itemStyle: {
              // 		color: '#409eff'
              // 	},
              // 	data: data[0][n].slice(data[0][n].length - 2, data[0][n].length)
              // },
              {
                name: data[1][n],
                type: "scatter",
                itemStyle: itemStyle,
                data: data[0][n].slice(0, data[0][n].length).map(item => {
                  let obj = this.info.code_list?.find(v => v.code == item?.[5]);
                  return {
                    value: item,
                    symbol:
                      obj?.flag == 1
                        ? "path://M12.4156 4.82735L8.94433 4.32286L7.39258 1.17696C7.35019 1.09083 7.28047 1.0211 7.19433 0.978716C6.97832 0.872075 6.71582 0.960943 6.60781 1.17696L5.05605 4.32286L1.58476 4.82735C1.48906 4.84102 1.40156 4.88614 1.33457 4.9545C1.25358 5.03774 1.20895 5.14973 1.21049 5.26586C1.21203 5.38199 1.25961 5.49276 1.34277 5.57383L3.8543 8.02247L3.26094 11.4801C3.24702 11.5605 3.25592 11.6432 3.28663 11.7189C3.31733 11.7945 3.36862 11.86 3.43466 11.908C3.50071 11.9559 3.57887 11.9845 3.66029 11.9903C3.74171 11.9961 3.82313 11.9789 3.89531 11.9408L7.00019 10.3084L10.1051 11.9408C10.1898 11.9859 10.2883 12.001 10.3826 11.9846C10.6205 11.9436 10.7805 11.718 10.7395 11.4801L10.1461 8.02247L12.6576 5.57383C12.726 5.50684 12.7711 5.41934 12.7848 5.32364C12.8217 5.08438 12.6549 4.8629 12.4156 4.82735Z"
                        : "circle",
                    itemStyle: {
                      color:
                        obj?.flag == 2
                          ? obj?.color
                          : obj?.flag == 1
                            ? "#FFD600"
                            : "#fafafa",
                      opacity: 0.8,
                      shadowBlur: 5,
                      shadowOffsetX: 0,
                      shadowOffsetY: 0,
                      shadowColor: "rgba(0, 0, 0, 0.5)"
                    }
                  };
                })
              }
            ]
          });
        }
        this.option = option;
        console.log(option);
        this.show = true;
      }
    },
    // 放大/缩小
    setOption (type) {
      if (type == "add") {
        this.zoom = this.zoom + 1;
      } else {
        this.zoom = this.zoom - 1;
      }
      this.option.series[0].zoom = this.zoom;
    },
    // 前往详情页
    goFundDetail (val) {
      // console.log(val);
      alphaGo(val.data[5], val.data[3], this.$route.path);
    },
    createPrintWord () {
      this.$refs["volility"].mergeOptions({ toolbox: { show: false } });
      let height = this.$refs["volility"]?.$el.clientHeight;
      let width = this.$refs["volility"]?.$el.clientWidth;
      let chart = this.$refs["volility"].getDataURL({
        type: "png",
        pixelRatio: 3,
        backgroundColor: "#fff"
      });
      this.$refs["volility"].mergeOptions({ toolbox: { show: false } });
      return [
        ...this.$exportWord.exportTitle("收益波动比"),
        ...this.$exportWord.exportChart(chart, { width, height })
      ];
    }
  },
  mounted () {
    this.ismanager = String(this.$route.query.ismanager) == 'true' ? true : false
  },
};
</script>

<style></style>
