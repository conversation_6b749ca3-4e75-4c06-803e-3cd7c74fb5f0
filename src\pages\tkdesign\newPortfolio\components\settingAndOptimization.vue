<template>
	<div class="setting-optimization-content" v-loading="pageLoading">
		<div class="box-container" style="margin-bottom: 16px" @click="closeDialog">
			<VerticalLineHeader title="设置优化参数"></VerticalLineHeader>
			<div class="section-container amount_setting_wrapper">
				<div class="sub_title">现金设置</div>
				<div class="amount_content">
					<div>
						当前可用自基金<span class="amount_value">{{ parameterJson.money | numFormat }}</span
						>元
					</div>
					<el-button class="amount_btn" type="primary" @click="handleMoneyEditClick">资金调配</el-button>
				</div>
			</div>
			<div class="section-container">
				<div class="sub_title">模型选择</div>
				<div class="modle-select-wrapper">
					<el-radio-group v-model="parameterJson.model" size="small">
						<el-radio v-for="radioItem in modelOptionList" :label="radioItem.value" border>{{ radioItem.label }}</el-radio>
					</el-radio-group>
				</div>
				<div v-if="parameterJson.model && parameterJson.model !== modelOptions['equal_weight'].value" class="model-form-wrapper">
					<div v-if="parameterJson.model === modelOptions['risk_budget'].value" class="risk-weight-set-wrapper">
						<div class="risk-weight-set-title">
							设置风险权重<span class="risk-weight-set-subtitle">权重总和必须等于100.00%，剩余风险权重0%，风险权重不能为0%。</span>
						</div>
						<el-table border class="risk-weight-set-table" :data="parameterJson.riskWeightList">
							<el-table-column width="296" align="gotoleft" prop="name" :label="`${pageName}名称`"> </el-table-column>
							<el-table-column width="296" align="gotoleft" prop="code" :label="`${pageName}代码`"> </el-table-column>
							<el-table-column align="gotoleft" prop="targetRisk" label="目标风险权重（%）">
								<template slot-scope="scope">
									<el-input type="number" style="width: 240px" v-model="scope.row.targetRisk"></el-input>
								</template>
							</el-table-column>
							<template slot="empty">
								<el-empty image-size="160"></el-empty>
							</template>
						</el-table>
					</div>
					<div class="model-form-inner-wrapper">
						<div class="inner-title">模型参数设置：</div>
						<el-form
							v-if="parameterJson.model === modelOptions['mean_variance'].value"
							key="mean_variance"
							ref="model_form"
							class="model_form_wrapper"
							:model="parameterJson"
							:rules="modelFormRules"
							label-position="right"
							label-width="108px"
							size="mini"
						>
							<el-form-item required label="选择优化规则:" prop="rules" style="width: 100%">
								<el-radio-group v-model="parameterJson.rules" size="medium">
									<el-radio v-for="radioItem in optimizationRulesOption" :label="radioItem.value" border>{{ radioItem.label }}</el-radio>
								</el-radio-group>
							</el-form-item>
							<el-form-item
								v-if="parameterJson.rules === optimizationRulesOption.RiskMin.value"
								key="targetReturn"
								label="目标收益:"
								required
								prop="targetReturn"
							>
								<el-input style="width: 174px" v-model="parameterJson.targetReturn">
									<div slot="suffix" style="color: rgba(0, 0, 0, 0.65)">%</div>
								</el-input>
							</el-form-item>
							<el-form-item
								v-if="parameterJson.rules === optimizationRulesOption.ReturnsMax.value"
								label="目标波动率:"
								key="targetVolatility"
								required
								prop="targetVolatility"
							>
								<el-input style="width: 174px" v-model="parameterJson.targetVolatility">
									<div slot="suffix" style="color: rgba(0, 0, 0, 0.65)">%</div>
								</el-input>
							</el-form-item>
							<el-form-item label="持仓产品上限:" key="productToplimit" prop="productToplimit">
								<el-input style="width: 174px" v-model="parameterJson.productToplimit">
									<div slot="suffix" style="color: rgba(0, 0, 0, 0.65)">个</div>
								</el-input>
							</el-form-item>
						</el-form>

						<el-form
							v-if="parameterJson.model === modelOptions['risk_parity'].value"
							ref="model_form"
							class="model_form_wrapper"
							key="risk_parity"
							:model="parameterJson"
							:rules="modelFormRules"
							label-position="right"
							label-width="84px"
							size="mini"
						>
							<el-form-item label="收益下限:" prop="returnFloor">
								<el-input style="width: 174px" v-model="parameterJson.returnFloor">
									<div slot="suffix" style="color: rgba(0, 0, 0, 0.65)">%</div>
								</el-input>
							</el-form-item>
							<el-form-item label="波动率上限:" prop="volatilityToplimit">
								<el-input style="width: 174px" v-model="parameterJson.volatilityToplimit">
									<div slot="suffix" style="color: rgba(0, 0, 0, 0.65)">%</div>
								</el-input>
							</el-form-item>
						</el-form>
						<el-form
							v-if="parameterJson.model === modelOptions['risk_budget'].value"
							ref="model_form"
							class="model_form_wrapper"
							:model="parameterJson"
							:rules="modelFormRules"
							label-position="right"
							label-width="84px"
							key="risk_budget"
							size="mini"
						>
							<el-form-item label="收益下限:" prop="returnFloor">
								<el-input style="width: 174px" placeholder="0" v-model="parameterJson.returnFloor">
									<div slot="suffix" style="color: rgba(0, 0, 0, 0.65)">%</div>
								</el-input>
							</el-form-item>
							<el-form-item label="波动率上限:" prop="volatilityToplimit">
								<el-input style="width: 174px" placeholder="0" v-model="parameterJson.volatilityToplimit">
									<div slot="suffix" style="color: rgba(0, 0, 0, 0.65)">%</div>
								</el-input>
							</el-form-item>
							<el-form-item label="夏普率下限:" prop="sharpeFloor">
								<el-input style="width: 174px" placeholder="0" v-model="parameterJson.sharpeFloor"> </el-input>
							</el-form-item>
						</el-form>
						<el-form
							v-if="parameterJson.model === modelOptions['life_cycle'].value"
							ref="model_form"
							class="model_form_wrapper"
							:model="parameterJson"
							:rules="modelFormRules"
							label-position="right"
							label-width="120px"
							key="life_cycle"
							size="mini"
						>
							<el-form-item required label="初始财富:" prop="initialWealth">
								<el-input style="width: 174px" placeholder="0" v-model="parameterJson.initialWealth">
									<div slot="suffix" style="color: rgba(0, 0, 0, 0.65)">元</div>
								</el-input>
							</el-form-item>
							<el-form-item required label="当前年龄:" prop="currentAge">
								<el-input style="width: 174px" placeholder="0" v-model="parameterJson.currentAge">
									<div slot="suffix" style="color: rgba(0, 0, 0, 0.65)">岁</div>
								</el-input>
							</el-form-item>
							<el-form-item required label="目标退休年龄:" prop="retAge">
								<el-input style="width: 174px" placeholder="0" v-model="parameterJson.retAge">
									<div slot="suffix" style="color: rgba(0, 0, 0, 0.65)">岁</div>
								</el-input>
							</el-form-item>
							<!-- v-if="parameterJson.rules === optimizationRulesOption.RiskMin.value" -->
							<el-form-item label="年收入:" required prop="annualIncome">
								<el-input style="width: 174px" placeholder="0" v-model="parameterJson.annualIncome">
									<div slot="suffix" style="color: rgba(0, 0, 0, 0.65)">元</div>
								</el-input>
							</el-form-item>
							<el-form-item required label="目标收益:" prop="targetReturn">
								<el-input style="width: 174px" placeholder="0" v-model="parameterJson.targetReturn">
									<div slot="suffix" style="color: rgba(0, 0, 0, 0.65)">%</div>
								</el-input>
							</el-form-item>
							<el-form-item required label="风险承受能力:" prop="riskTolerance">
								<el-input style="width: 174px" placeholder="请输入0-10的数字" v-model="parameterJson.riskTolerance"> </el-input>
							</el-form-item>
						</el-form>
					</div>
				</div>
			</div>
			<div class="section-container" v-loading="positionLoading">
				<div class="positon-set-search">
					<div class="sub_title">持仓设置</div>
					<div class="ps_search_form">
						<!-- <el-input
                           style="width:240px"
                            placeholder="添加基金"
                            prefix-icon="el-icon-search"
                            v-model="form.fund_code">
                        </el-input> -->
						<!-- <el-select
							prefix-icon="el-icon-search"
							v-model="form.fund_code"
							multiple
							filterable
							remote
							reserve-keyword
							placeholder="添加基金"
							:remote-method="remoteMethod"
							:loading="fundSearchLoading"
						>
							<el-option v-for="item in fundSearchList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
						</el-select> -->
						<el-autocomplete
							v-if="!isConfigTab"
							prefix-icon="el-icon-search"
							v-model="addCodeInput"
							:fetch-suggestions="remoteMethod"
							:placeholder="`添加${pageName}`"
							:trigger-on-focus="false"
							class="add-input-wrapper"
							@select="handleAddSetCode"
						>
							<template slot-scope="{ item }">
								<span>{{ item.value }}</span>
								<span>{{ item.label }}</span>
							</template>
						</el-autocomplete>
						<div style="margin-left: 12px">
							构造日期：<el-date-picker
								format="yyyy-MM-dd"
								value-format="yyyy-MM-dd"
								:disabled="constructionDateDisable"
								v-model="parameterJson.constructionDate"
								align="right"
								type="date"
								placeholder="选择日期"
								@change="changeConstructionDate"
							>
							</el-date-picker>
						</div>
						<el-button
							v-if="
								parameterJson.model &&
								parameterJson.model !== modelOptions['equal_weight'].value &&
								parameterJson.model !== modelOptions['life_cycle'].value
							"
							style="margin-left: 12px"
							type="primary"
							@click="handleImportAssets"
							>批量导入资产分析模型</el-button
						>
					</div>
				</div>
				<el-table border class="position-set-table" :data="parameterJson.positionList" max-height="400px">
					<el-table-column align="gotoleft" label="序号" type="index" width="50"></el-table-column>
					<el-table-column align="gotoleft" prop="name" :label="`${pageName}名称`"> </el-table-column>
					<el-table-column align="gotoleft" prop="code" :label="`${pageName}代码`"> </el-table-column>
					<el-table-column align="gotoleft" prop="price" label="构造日期价格">
						<template slot-scope="scope">
							<span>{{ stringTool.fix2(scope.row.price) }}</span>
						</template>
					</el-table-column>
					<!-- <el-table-column align="gotoleft" prop="original_position" label="原仓位"> </el-table-column> -->
					<!-- <el-table-column align="gotoleft" prop="prior_risk" label="事前风险"> </el-table-column>
					 -->

					<el-table-column align="gotoleft" prop="minRatio" label="投资比例下限">
						<template slot-scope="scope">
							<el-input v-model="scope.row.minRatio"></el-input>
						</template>
					</el-table-column>
					<el-table-column align="gotoleft" prop="maxRatio" label="投资比例上限">
						<template slot-scope="scope">
							<el-input v-model="scope.row.maxRatio"></el-input>
						</template>
					</el-table-column>
					<el-table-column align="gotoleft" prop="returnValue" label="期望收益">
						<template slot-scope="scope">
							<span :style="cellStyle(scope.row['returnValue'])">{{ stringTool.fix2px(scope.row.returnValue) || '--' }}</span>
						</template>
					</el-table-column>
					<el-table-column align="gotoleft" prop="volatilityValue" label="预期波动率">
						<template slot-scope="scope">
							<span>{{ stringTool.fix2px(scope.row.volatilityValue) }}</span>
						</template>
					</el-table-column>
					<el-table-column align="gotoleft" label="操作">
						<template slot-scope="scope">
							<!-- <el-button
								v-if="this.parameterJson.model !== modelOptions['equal_weight'].value && this.parameterJson.model !== modelOptions['life_cycle'].value"
								size="mini"
								type="text"
								@click="handleOneAsset(scope.$index, scope.row)"
								>资产分析模型</el-button
							> -->
							<el-button size="mini" type="text" @click="handlePositionDelete(scope.$index, scope.row)">删除</el-button>
						</template>
					</el-table-column>
					<template slot="empty">
						<el-empty image-size="160"></el-empty>
					</template>
				</el-table>
				<div class="tabel-dec">
					共{{ (parameterJson.positionList && parameterJson.positionList.length) || 0 }}只{{ pageName }} 组合事前风险：{{
						parameterJson.priorRisk
					}}
				</div>
			</div>
			<div class="section-container">
				<div class="sub_title">收益相关系数矩阵</div>
				<CorrelationCoefficientChart ref="correlation-coefficient-chart"></CorrelationCoefficientChart>
			</div>
			<div class="section-container" v-if="!isConfigTab">
				<div class="sub_title">再平衡周期</div>
				<div>
					<el-radio-group v-model="parameterJson.rebalanceCycle" class="setting-radio-wrapper">
						<el-radio
							v-for="item in rebalanceCycleRadioList"
							border
							:key="item.value"
							:label="item.value"
							@click.native.prevent="handleRebalanceCycleClick(item.value)"
						>
							{{ item.label }}
						</el-radio>
					</el-radio-group>
				</div>
				<div class="custom-cycle-wrapper">
					<el-popover
						v-if="parameterJson.rebalanceCycle === 'custom'"
						trigger="manual"
						placement="bottom"
						width="400"
						v-model="customSelectVisable"
						popper-class="custom-cycle-popper-wrapper"
					>
						<div>
							<div v-if="customDateList && customDateList.length > 0">
								<el-checkbox-group v-model="parameterJson.customDate">
									<div class="cc-add-date-item" v-for="item in customDateList" :key="item.value">
										<el-checkbox :label="item.label" :value="item.value"></el-checkbox>
									</div>
								</el-checkbox-group>
							</div>
							<el-empty image-size="80" v-else description="暂无数据"></el-empty>

							<div class="cc-options-add-wrapper">
								<div v-show="addDateFormShow">
									<el-date-picker
										value-format="yyyyMMdd"
										v-model="addDateInput"
										align="right"
										type="date"
										placeholder="选择日期"
										:picker-options="pickerOptions"
									>
									</el-date-picker>
									<div style="margin-top: 8px">
										<el-button type="primary" @click="handleAddDate">确定</el-button>
										<el-button @click="handleCancleAddDate">取消</el-button>
									</div>
								</div>
								<div v-show="!addDateFormShow" style="display: flex; justify-content: center">
									<el-button type="text" style="font-size: 14px" @click="addDateFormShow = !addDateFormShow">新增日期</el-button>
								</div>
							</div>
						</div>
						<div slot="reference">
							<div>
								<el-select
									popper-class="custom-cycle-selsect"
									style="width: 240px"
									v-model="parameterJson.customDate"
									multiple
									placeholder="请选择"
									@click.native="customSelectVisable = !customSelectVisable"
								>
									<el-option v-for="item in customDateList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
								</el-select>
							</div>
						</div>
					</el-popover>
				</div>
			</div>
			<div class="section-container" v-if="!isConfigTab">
				<div class="sub_title">再平衡目标</div>
				<div>
					<el-radio-group v-model="parameterJson.rebalanceTarget" class="setting-radio-wrapper">
						<el-radio border label="optimal" @click.native.prevent="handleRebalanceTargetClick('optimal')">按最优配置比</el-radio>
						<el-radio border label="initial" @click.native.prevent="handleRebalanceTargetClick('initial')">回到初始权重</el-radio>
					</el-radio-group>
				</div>
			</div>
			<div class="section-footer">
				<el-button v-if="!isEditPage" style="margin-top: 12px" @click="handleBack">上一步</el-button>
				<el-button type="primary" @click="handleStartOptimize">预览及微调</el-button>
			</div>
		</div>
		<el-dialog title="资产分析模型" custom-class="asset_analysis_model_wrapper" :visible.sync="assetAnalysisModelVisable">
			<div class="asset_analysis_model_body">
				<div class="aam_cul">计算：{{ modelCulText }}</div>
				<el-tabs v-model="modelActiveName">
					<el-tab-pane label="期望收益" name="first">
						<div class="template_wrapper">
							请点击下载<span style="display: flex; align-items: center"
								><svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
									<g clip-path="url(#clip0_10391_24644)">
										<path
											d="M14.6667 0.916504H6.41667C5.86667 0.916504 5.5 1.28317 5.5 1.83317V6.4165L14.6667 10.9998L18.3333 12.3748L22 10.9998V6.4165L14.6667 0.916504Z"
											fill="#21A366"
										/>
										<path d="M5.5 6.4165H14.6667V10.9998H5.5V6.4165Z" fill="#107C41" />
										<path
											d="M22.0001 1.83317V6.4165H14.6667V0.916504H21.0834C21.5417 0.916504 22.0001 1.37484 22.0001 1.83317Z"
											fill="#33C481"
										/>
										<path
											d="M14.6667 11H5.5V20.1667C5.5 20.7167 5.86667 21.0833 6.41667 21.0833H21.0833C21.6333 21.0833 22 20.7167 22 20.1667V15.5833L14.6667 11Z"
											fill="#185C37"
										/>
										<path
											opacity="0.5"
											d="M12.65 5.5H5.5V18.3333H12.4667C13.1083 18.3333 13.75 17.6917 13.75 17.05V6.6C13.75 5.95833 13.2917 5.5 12.65 5.5Z"
											fill="black"
										/>
										<path
											d="M11.7333 17.4168H1.1C0.458333 17.4168 0 16.9585 0 16.3168V5.6835C0 5.04183 0.458333 4.5835 1.1 4.5835H11.825C12.375 4.5835 12.8333 5.04183 12.8333 5.6835V16.4085C12.8333 16.9585 12.375 17.4168 11.7333 17.4168Z"
											fill="#107C41"
										/>
										<path
											d="M3.1167 14.6668L5.50003 11.0002L3.30003 7.3335H5.0417L6.23337 9.62516C6.4167 9.90016 6.4167 10.0835 6.50837 10.1752L6.78337 9.62516L8.0667 7.3335H9.7167L7.5167 11.0002L9.80837 14.6668H7.97503L6.60003 12.1002C6.60003 12.0085 6.50837 11.9168 6.4167 11.7335C6.4167 11.8252 6.32503 11.9168 6.23337 12.1002L4.85837 14.6668H3.1167Z"
											fill="white"
										/>
										<path d="M14.6667 11H22.0001V15.5833H14.6667V11Z" fill="#107C41" />
									</g>
									<defs>
										<clipPath id="clip0_10391_24644">
											<rect width="22" height="22" fill="white" />
										</clipPath>
									</defs>
								</svg>
								<a href="" download="资产分析模板.xlsx" @click.prevent="handelModelExport">资产分析模板</a> </span
							>，按此模版整理数据后再通过按钮导入。
							<el-date-picker
								style="width: 250px; margin-right: 8px"
								v-model="modelDate"
								:disabled="modelDateDisabled"
								:unlink-panels="true"
								type="daterange"
								format="yyyy-MM-dd"
								value-format="yyyy-MM-dd"
								range-separator="至"
								start-placeholder="开始日期"
								end-placeholder="结束日期"
								@change="changeModelDate"
							>
							</el-date-picker>
							<el-upload
								:auto-upload="false"
								:file-list="appendixList2"
								:on-change="changeAppendix2"
								:show-file-list="false"
								:multiple="1"
								accept=".xls,.xlsx"
							>
								<el-button type="primary">上传文件导入</el-button>
							</el-upload>
						</div>
						<el-table border class="fund-analysis-table" :data="parameterJson.data.returnList" max-height="400px">
							<el-table-column
								v-for="item in expectedColumnList"
								:key="item.value"
								:prop="item.value"
								:label="item.label"
								:sortable="item.sortable"
								align="gotoleft"
							>
								<template slot-scope="scope">
									<span :style="item.color ? cellStyle(scope.row[item.value]) : ''" v-if="item['canEdit']">
										<!-- <span v-if="!showEdit(scope.$index, item.value)" @dblclick="startEdit(scope.$index, item.value, 'returnList')">
											<span v-if="item.format">{{ item.format(scope.row[item.value]) }}</span>
											<span v-else>{{ scope.row[item.value] }}</span>
										</span> -->
										<el-input v-model="scope.row[item.value]"></el-input>
									</span>
									<span v-else>
										<span v-if="item.format">{{ item.format(scope.row[item.value]) }}</span>
										<span v-else>{{ scope.row[item.value] }}</span>
									</span>
								</template>
							</el-table-column>
							<template slot="empty">
								<el-empty image-size="160"></el-empty>
							</template>
						</el-table>
					</el-tab-pane>
					<el-tab-pane label="相关系数" name="second">
						<div class="template_wrapper">
							请点击下载<span style="display: flex; align-items: center"
								><svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
									<g clip-path="url(#clip0_10391_24644)">
										<path
											d="M14.6667 0.916504H6.41667C5.86667 0.916504 5.5 1.28317 5.5 1.83317V6.4165L14.6667 10.9998L18.3333 12.3748L22 10.9998V6.4165L14.6667 0.916504Z"
											fill="#21A366"
										/>
										<path d="M5.5 6.4165H14.6667V10.9998H5.5V6.4165Z" fill="#107C41" />
										<path
											d="M22.0001 1.83317V6.4165H14.6667V0.916504H21.0834C21.5417 0.916504 22.0001 1.37484 22.0001 1.83317Z"
											fill="#33C481"
										/>
										<path
											d="M14.6667 11H5.5V20.1667C5.5 20.7167 5.86667 21.0833 6.41667 21.0833H21.0833C21.6333 21.0833 22 20.7167 22 20.1667V15.5833L14.6667 11Z"
											fill="#185C37"
										/>
										<path
											opacity="0.5"
											d="M12.65 5.5H5.5V18.3333H12.4667C13.1083 18.3333 13.75 17.6917 13.75 17.05V6.6C13.75 5.95833 13.2917 5.5 12.65 5.5Z"
											fill="black"
										/>
										<path
											d="M11.7333 17.4168H1.1C0.458333 17.4168 0 16.9585 0 16.3168V5.6835C0 5.04183 0.458333 4.5835 1.1 4.5835H11.825C12.375 4.5835 12.8333 5.04183 12.8333 5.6835V16.4085C12.8333 16.9585 12.375 17.4168 11.7333 17.4168Z"
											fill="#107C41"
										/>
										<path
											d="M3.1167 14.6668L5.50003 11.0002L3.30003 7.3335H5.0417L6.23337 9.62516C6.4167 9.90016 6.4167 10.0835 6.50837 10.1752L6.78337 9.62516L8.0667 7.3335H9.7167L7.5167 11.0002L9.80837 14.6668H7.97503L6.60003 12.1002C6.60003 12.0085 6.50837 11.9168 6.4167 11.7335C6.4167 11.8252 6.32503 11.9168 6.23337 12.1002L4.85837 14.6668H3.1167Z"
											fill="white"
										/>
										<path d="M14.6667 11H22.0001V15.5833H14.6667V11Z" fill="#107C41" />
									</g>
									<defs>
										<clipPath id="clip0_10391_24644">
											<rect width="22" height="22" fill="white" />
										</clipPath>
									</defs>
								</svg>
								<a href="" download="资产分析模板.xlsx" @click.prevent="handelModelExport">资产分析模板</a> </span
							>，按此模版整理数据后再通过按钮导入。
							<el-date-picker
								style="width: 250px; margin-right: 8px"
								:unlink-panels="true"
								v-model="modelDate"
								:disabled="modelDateDisabled"
								format="yyyy-MM-dd"
								value-format="yyyy-MM-dd"
								type="daterange"
								range-separator="至"
								start-placeholder="开始日期"
								end-placeholder="结束日期"
								@change="changeModelDate"
							>
							</el-date-picker>
							<el-upload
								:auto-upload="false"
								:file-list="appendixList2"
								:on-change="changeAppendix2"
								:show-file-list="false"
								:multiple="1"
								accept=".xls,.xlsx"
							>
								<el-button type="primary">上传文件导入</el-button>
							</el-upload>
						</div>
						<el-table border class="fund-analysis-table" :data="parameterJson.data.coefficientList" max-height="400px">
							<el-table-column
								v-for="item in coefficientColumnList"
								:key="item.value"
								:prop="item.value"
								:label="item.label"
								:sortable="item.sortable"
								align="gotoleft"
							>
								<template slot-scope="scope">
									<template v-if="item['canEdit']">
										<el-input v-model="scope.row[item.value]"></el-input>
									</template>
									<template v-else>
										<span v-if="item.format">{{ item.format(scope.row[item.value]) }}</span>
										<span v-else>{{ scope.row[item.value] }}</span>
									</template>
								</template>
							</el-table-column>
							<template slot="empty">
								<el-empty image-size="160"></el-empty>
							</template>
						</el-table>
					</el-tab-pane>
					<el-tab-pane label="波动率" name="third">
						<div class="template_wrapper">
							请点击下载<span style="display: flex; align-items: center"
								><svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
									<g clip-path="url(#clip0_10391_24644)">
										<path
											d="M14.6667 0.916504H6.41667C5.86667 0.916504 5.5 1.28317 5.5 1.83317V6.4165L14.6667 10.9998L18.3333 12.3748L22 10.9998V6.4165L14.6667 0.916504Z"
											fill="#21A366"
										/>
										<path d="M5.5 6.4165H14.6667V10.9998H5.5V6.4165Z" fill="#107C41" />
										<path
											d="M22.0001 1.83317V6.4165H14.6667V0.916504H21.0834C21.5417 0.916504 22.0001 1.37484 22.0001 1.83317Z"
											fill="#33C481"
										/>
										<path
											d="M14.6667 11H5.5V20.1667C5.5 20.7167 5.86667 21.0833 6.41667 21.0833H21.0833C21.6333 21.0833 22 20.7167 22 20.1667V15.5833L14.6667 11Z"
											fill="#185C37"
										/>
										<path
											opacity="0.5"
											d="M12.65 5.5H5.5V18.3333H12.4667C13.1083 18.3333 13.75 17.6917 13.75 17.05V6.6C13.75 5.95833 13.2917 5.5 12.65 5.5Z"
											fill="black"
										/>
										<path
											d="M11.7333 17.4168H1.1C0.458333 17.4168 0 16.9585 0 16.3168V5.6835C0 5.04183 0.458333 4.5835 1.1 4.5835H11.825C12.375 4.5835 12.8333 5.04183 12.8333 5.6835V16.4085C12.8333 16.9585 12.375 17.4168 11.7333 17.4168Z"
											fill="#107C41"
										/>
										<path
											d="M3.1167 14.6668L5.50003 11.0002L3.30003 7.3335H5.0417L6.23337 9.62516C6.4167 9.90016 6.4167 10.0835 6.50837 10.1752L6.78337 9.62516L8.0667 7.3335H9.7167L7.5167 11.0002L9.80837 14.6668H7.97503L6.60003 12.1002C6.60003 12.0085 6.50837 11.9168 6.4167 11.7335C6.4167 11.8252 6.32503 11.9168 6.23337 12.1002L4.85837 14.6668H3.1167Z"
											fill="white"
										/>
										<path d="M14.6667 11H22.0001V15.5833H14.6667V11Z" fill="#107C41" />
									</g>
									<defs>
										<clipPath id="clip0_10391_24644">
											<rect width="22" height="22" fill="white" />
										</clipPath>
									</defs>
								</svg>
								<a href="" download="资产分析模板.xlsx" @click.prevent="handelModelExport">资产分析模板</a></span
							>，按此模版整理数据后再通过按钮导入。
							<el-date-picker
								style="width: 250px; margin-right: 8px"
								:unlink-panels="true"
								v-model="modelDate"
								:disabled="modelDateDisabled"
								format="yyyy-MM-dd"
								value-format="yyyy-MM-dd"
								type="daterange"
								range-separator="至"
								start-placeholder="开始日期"
								end-placeholder="结束日期"
								@change="changeModelDate"
							>
							</el-date-picker>
							<el-upload
								:auto-upload="false"
								:file-list="appendixList2"
								:on-change="changeAppendix2"
								:show-file-list="false"
								:multiple="1"
								accept=".xls,.xlsx"
							>
								<el-button type="primary">上传文件导入</el-button>
							</el-upload>
						</div>
						<el-table border class="fund-analysis-table" :data="parameterJson.data.volatilityList" max-height="400px">
							<el-table-column
								v-for="item in volatilityColumnList"
								:key="item.value"
								:prop="item.value"
								:label="item.label"
								:sortable="item.sortable"
								align="gotoleft"
							>
								<template slot-scope="scope">
									<template v-if="item['canEdit']">
										<el-input v-model="scope.row[item.value]"></el-input>
									</template>
									<template v-else>
										<span v-if="item.format">{{ item.format(scope.row[item.value]) }}</span>
										<span v-else>{{ scope.row[item.value] }}</span>
									</template>
								</template>
							</el-table-column>
							<template slot="empty">
								<el-empty image-size="160"></el-empty>
							</template>
						</el-table>
					</el-tab-pane>
				</el-tabs>
			</div>
			<div slot="footer">
				<el-button @click="assetAnalysisModelVisable = !assetAnalysisModelVisable">取消</el-button>
				<el-button @click="updateModelTemplate" type="primary">确定</el-button>
			</div>
		</el-dialog>
		<el-dialog title="资金调配" :visible.sync="moneyEditDialog">
			调整后： <el-input style="width: 240px" v-model="money"></el-input>
			<div slot="footer">
				<el-button @click="moneyEditDialog = !moneyEditDialog">取消</el-button>
				<el-button type="primary" @click="handleMoneyEditSure">确定</el-button>
			</div>
		</el-dialog>
	</div>
</template>
<script>
const { export_json_to_excel2 } = require('@/vendor/Export2Excel');
import VerticalLineHeader from '../../components/VerticalLineHeader.vue';
import CorrelationCoefficientChart from './chart/CorrelationCoefficientChart.vue';
import OptimizationPlanChart from './chart/OptimizationPlanChart.vue';
import {
	getModelTemplate,
	getParameterDetails,
	getPositionSettingList,
	getReturnsCorrelationMatrix,
	getSearchList
} from '@/api/pages/tkAnalysis/portfolio.js';
import stringTool from '@/pages/tkdesign/components/string.tool';
export default {
	components: {
		VerticalLineHeader,
		OptimizationPlanChart,
		CorrelationCoefficientChart
	},
	props: {
		// "1": "fund",基金
		// "2": "manager",基金经理
		// "3": "company",基金公司
		// "4": "combination",组合
		// "5": "pool",基金池
		// "6": "index",指数
		flag: {
			type: String,
			default: ''
		},
		//fund:基金，index:指数
		marketType: {
			type: String,
			default: ''
		},
		otherInfo: {
			type: Object,
			default: () => {
				return {};
			}
		}
	},
	data() {
		// 夏普率最大化:Sharp_max;目标收益下风险最小化:return_riskMin;目标风险下收益最大化:return_riskMax;
		let rulesOption = {
			Sharpe: {
				label: '夏普率最大化',
				value: 'Sharp_max'
			},
			RiskMin: {
				label: '目标收益下风险最小化',
				value: 'return_riskMin'
			},
			ReturnsMax: {
				label: '目标风险下收益最大化',
				value: 'return_riskMax'
			}
		};
		const modelOptions = {
			mean_variance: {
				label: '均值方差模型',
				value: 'mean_variance'
			},
			equal_weight: {
				label: '等权重',
				value: 'equal_weight'
			},
			risk_parity: {
				label: '风险平价模型',
				value: 'risk_parity'
			},
			risk_budget: {
				label: '风险预算模型',
				value: 'risk_budget'
			},
			life_cycle: {
				label: '生命周期模型',
				value: 'life_cycle'
			}
		};
		function ltZero(value) {
			if (value - 0 >= 0) {
				return {
					success: true
				};
			}
			return {
				msg: '输入值不能为负数'
			};
		}
		const checkRiskTolerance = (rule, value, callback) => {
			if (!value) {
				return callback(new Error('请输入风险承受能力'));
			}
			let numValue = Number(value);
			if (isNaN(numValue)) {
				callback(new Error('请输入0-10的数字'));
			} else {
				if (numValue > 10 || numValue < 0) {
					callback(new Error('请输入0-10的数字'));
				} else {
					callback();
				}
			}
		};
		return {
			checkRiskTolerance,
			stringTool,
			//资产分析模型tab选择值
			modelActiveName: 'first',
			//自定义再选周期时间输入
			addDateInput: '',
			//自定义再选周期时间输入是否展示
			addDateFormShow: false,
			//自定义再选周期时间下拉列表展示隐藏
			customSelectVisable: false,
			//自定再选周期时间列表
			customDateList: [],

			isEditPage: false,
			//策略/组合id
			setDetailId: '',
			//添加场景下前一个页面带来的数据
			addInfo: {
				selectList: []
			},
			//策略/组合研究参数详情对象数据
			parameterJson: {
				//模型，均值方差模型：mean_variance； 等权重:equal_weight； 风险平价模型：risk_parity； 风险预算模型：risk_budget ；生命周期模型：life_cycle;
				model: modelOptions.mean_variance.value,
				//现金，默认：10,000,000.00;
				money: '10000000',
				//模型参数设置 选择优化规则，夏普率最大化:Sharp_max;目标收益下风险最小化:return_riskMin;目标风险下收益最大化:return_riskMax;
				rules: rulesOption.Sharpe.value,
				//模型参数设置 持仓产品上限
				productToplimit: '',
				//模型参数设置 收益下限
				returnFloor: '',
				//模型参数设置 目标收益
				targetReturn: '',
				//模型参数设置 目标波动率
				targetVolatility: '',
				//模型参数设置 波动率上限
				volatilityToplimit: '',
				//模型参数设置 夏普率下限
				sharpeFloor: '',
				//模型参数设置 设置风险权重
				//code 基金/股票代码 name 基金/股票名称 targetRisk 目标风险
				riskWeightList: [],
				priorRisk: '', //组合事前风险
				//模型参数设置 持仓设置列表
				//code基金/股票代码 name基金/股票名称 price构造日期价格 maxRatio投资比例上限 minRatio投资比例下限
				positionList: [],
				//构造日期
				constructionDate: '',
				//资产分析模型模板数据
				data: {
					//期望收益
					returnList: [],
					//收益相关系数
					coefficientList: [],
					//波动率
					volatilityList: []
				},
				//再平衡周期，1d,3d,6d,1y,持有到期:all;
				rebalanceCycle: '',
				//自定义时间，传数组
				customDate: [],
				//再平衡目标，按最优配置比:optimal;回到初始权重:initial;
				rebalanceTarget: '',
				//策略产品
				ployProject: undefined,
				ployId: undefined
			},
			rebalanceCycleRadioList: [
				{ value: '1d', label: '每1个月' },
				{ value: '3d', label: '每3个月' },
				{ value: '6d', label: '每6个月' },
				{ value: '1y', label: '每1年' },
				{ value: 'all', label: '持有到期' },
				{ value: 'custom', label: '自定义' }
			],
			//资金调配弹窗 临时金额
			money: '',
			//资金调配弹窗是否展示
			moneyEditDialog: false,
			//资产分析模型弹窗展示
			assetAnalysisModelVisable: false,
			//持仓设置 查询时基金code列表
			setCodeList: [],
			//持仓设置 添加基金时输入内容
			addCodeInput: '',
			modelOptions: modelOptions,
			modelOptionList: Object.values(modelOptions),
			optimizationRulesOption: rulesOption,
			modelForm: {},
			form: {},
			//资产分析模型开始结束时间
			modelDate: [],
			//资产分析模型开始结束时间选择禁用
			modelDateDisabled: false,
			//用户是否主动填写过资产分析模板时间
			userChangeModelDate: false,
			currentEditInfo: {
				index: '',
				prop: '',
				value: ''
			},
			//资产分析模型导入-期望收益列表表头
			expectedColumnList: [
				{
					label: '资产代码',
					value: 'code'
				},
				{
					label: '资产名称',
					value: 'name'
				},
				{
					label: '值',
					oriLabel: '值',
					canEdit: true,
					color: true,
					value: 'value',
					format: stringTool.fix2px
				},
				{
					label: '预期开始时间',
					value: 'startDate'
				},
				{
					label: '预期结束时间',
					value: 'endDate'
				}
			],
			//资产分析模型导入-相关系数列表表头
			coefficientColumnList: [
				{
					label: '资产1代码',
					value: 'codeFirst'
				},
				{
					label: '资产1名称',
					value: 'nameFirst'
				},
				{
					label: '资产2代码',
					value: 'codeSecond'
				},
				{
					label: '资产2名称',
					value: 'nameSecond'
				},
				{
					label: '相关系数',
					value: 'value',
					format: stringTool.fix2
				},
				{
					label: '预期开始时间',
					value: 'startDate'
				},

				{
					label: '预期结束时间',
					value: 'endDate'
				}
			],
			//资产分析模型导入-波动率列表表头
			volatilityColumnList: [
				{
					label: '资产代码',
					value: 'code'
				},
				{
					label: '资产名称',
					value: 'name'
				},
				{
					label: '预期波动率',
					value: 'value',
					canEdit: true,
					rule: ltZero,
					format: stringTool.fix2px
				},
				{
					label: '预期开始时间',
					value: 'startDate'
				},
				{
					label: '预期结束时间',
					value: 'endDate'
				}
			],
			fundSearchList: [],
			fundSearchLoading: true,
			//风险预算模型-设置风险权重 列表
			// rwSetTableData: [
			// 	{
			// 		fund_code: 'eer',
			// 		fund_name: 'wwee',
			// 		target_risk_weight: '46%'
			// 	}
			// ],
			//持仓设置 列表
			// positionSetableData: [
			// 	{
			// 		fund_name: '4',
			// 		fund_code: '3',
			// 		latest_price: '233',
			// 		original_position: 'df',
			// 		prior_risk: 'we',
			// 		investment_radio_max: 'er',
			// 		investment_radio_min: 'df'
			// 	}
			// ],
			optimizedTableData: [
				{
					fund_name: '4',
					fund_code: '3',
					latest_price: '233',
					original_position: 'df',
					prior_risk: 'we',
					optimized_weight: 'dd'
				}
			],
			//上传文件导入，文件列表
			appendixList2: [],
			listItemInfo: {},
			//持仓设置列表loading
			positionLoading: false,
			pageLoading: false
		};
	},
	computed: {
		//在配置策略内选择指数  组合策略选择基金
		pageName() {
			if (this.marketType === 'fund') {
				return '基金';
			}
			if (this.marketType === 'index') {
				return '指数';
			}
			return '';
		},
		modelFormRules() {
			return {
				rules: [{ require: true, message: '请选择', trigger: 'change' }],
				targetVolatility: [{ required: true, message: '请输入目标波动率', trigger: ['blur', 'change'] }],
				initialWealth: [{ required: true, message: '请输入初始财富', trigger: ['blur', 'change'] }],
				currentAge: [{ required: true, message: '请输入当前年龄', trigger: ['blur', 'change'] }],
				retAge: [{ required: true, message: '请输入目标退休年龄', trigger: ['blur', 'change'] }],
				annualIncome: [{ required: true, message: '请输入年收入', trigger: ['blur', 'change'] }],
				targetReturn: [{ required: true, message: '请输入目标收益', trigger: ['blur', 'change'] }],
				riskTolerance: [{ validator: this.checkRiskTolerance, trigger: ['blur', 'change'] }]
			};
		},
		//资产分析模型 计算文案展示逻辑
		modelCulText() {
			//如果是均值方差模型展示
			if (this.parameterJson.model === this.modelOptions['mean_variance'].value) {
				return '均值和方差';
			}
			//风险平价模型 风险预算模型
			if (
				this.parameterJson.model === this.modelOptions['risk_parity'].value ||
				this.parameterJson.model === this.modelOptions['risk_budget'].value
			) {
				return '方差';
			}
			return '';
		},
		//构造日期是否可修改
		constructionDateDisable() {
			//  组合--配置策略进来的构造日期为前一个页面带进来的 禁用
			return this.isConfigTab;
		},
		//是否是组合--配置策略tab进来的
		isConfigTab() {
			if (this.addInfo.ployId) {
				return true;
			}
			return false;
		}
	},
	filters: {
		//千分符处理
		numFormat(value) {
			if (value == '--') return value;
			if (!value) {
				return '--';
			}
			let res = value.toString().replace(/\d+/, function (n) {
				// 先提取整数部分
				return n.replace(/(\d)(?=(\d{3})+$)/g, function ($1) {
					return $1 + ',';
				});
			});
			return res;
		}
	},
	methods: {
		initData() {
			this.userChangeModelDate = false;
			this.isEditPage = this.$route.query.isEditPage;
			//表示是需要查询详情信息
			if (this.isEditPage) {
				//获取基本信息
				console.log((this.$route.query.listItemInfo && JSON.parse(this.$route.query.listItemInfo)) || {});
				this.listItemInfo = (this.$route.query.listItemInfo && JSON.parse(this.$route.query.listItemInfo)) || {};
				this.setDetailId = this.listItemInfo.id;
				this.getDetailData();
				//获取相关系数矩阵相关信息
				// this.getCorrelationData();
			} else {
				//获取前一个页面所得的基本信息,调用先赢接口获取信息
				this.setAddInfoData();
			}
		},
		startEdit(index, prop, oriDataName) {
			this.currentEditInfo = {
				index,
				prop,
				value: this.parameterJson.data[oriDataName][index][prop]
			};
			this.$nextTick(() => {
				this.$refs['editInput' + this.modelActiveName][0].focus();
			});
		},
		//表格输入框失去焦点
		stopEdit(index, prop, oriDataName, rule) {
			this.currentEditInfo.index = '';
			this.currentEditInfo.prop = '';
			if (rule) {
				let result = rule(this.currentEditInfo.value);
				if (!result.success) {
					this.$message(result.msg);
					this.currentEditInfo.value = '';
					return;
				}
			}
			this.parameterJson.data[oriDataName][index][prop] = this.currentEditInfo.value;
			this.currentEditInfo.value = '';
		},
		//是否展示编辑输入框
		showEdit(index, prop) {
			if (index === this.currentEditInfo.index && prop === this.currentEditInfo.prop) {
				return true;
			}
			return false;
		},
		//导出资产分析模板导出
		handelModelExport() {
			// 资产代码	资产名称	值	预期开始时间	预期结束时间
			let sheet0 = {
				sheetName: '预期收益率',
				th: ['资产代码', '资产名称', '值', '预期开始时间', '预期结束时间'],
				jsonData: []
			};
			// 资产1代码	资产1名称	资产2代码	资产2名称	相关系数	预期开始时间	预期结束时间
			let sheet1 = {
				sheetName: '预期相关系数',
				th: ['资产1代码', '资产1名称', '资产2代码', '资产2名称', '相关系数', '预期开始时间', '预期结束时间'],
				jsonData: []
			};
			// 资产代码	资产名称	预期波动率	预期开始时间	预期结束时间
			let sheet2 = {
				sheetName: '预期波动率',
				th: ['资产代码', '资产名称', '预期波动率', '预期开始时间', '预期结束时间'],
				jsonData: []
			};
			let returnList = this.parameterJson?.data?.returnList || [];
			let coefficientList = this.parameterJson?.data?.coefficientList || [];
			let volatilityList = this.parameterJson?.data?.volatilityList || [];
			if (returnList.length > 0) {
				for (let i = 0; i < returnList.length; i++) {
					// th: ['资产代码', '资产名称', '值', '预期开始时间', '预期结束时间'],
					sheet0.jsonData[i] = [];
					sheet0.jsonData[i][0] = returnList[i].code;
					sheet0.jsonData[i][1] = returnList[i].name;
					sheet0.jsonData[i][2] = returnList[i].value;
					sheet0.jsonData[i][3] = returnList[i].startDate;
					sheet0.jsonData[i][4] = returnList[i].endDate;
				}
			}
			if (coefficientList.length > 0) {
				for (let i = 0; i < coefficientList.length; i++) {
					// 资产1代码	资产1名称	资产2代码	资产2名称	相关系数	预期开始时间	预期结束时间
					sheet1.jsonData[i] = [];
					sheet1.jsonData[i][0] = coefficientList[i].codeFirst;
					sheet1.jsonData[i][1] = coefficientList[i].nameFirst;
					sheet1.jsonData[i][2] = coefficientList[i].codeSecond;
					sheet1.jsonData[i][3] = coefficientList[i].nameSecond;
					sheet1.jsonData[i][4] = coefficientList[i].value;
					sheet1.jsonData[i][5] = coefficientList[i].startDate;
					sheet1.jsonData[i][6] = coefficientList[i].endDate;
				}
			}
			if (volatilityList.length > 0) {
				['资产代码', '资产名称', '预期波动率', '预期开始时间', '预期结束时间'];
				for (let i = 0; i < volatilityList.length; i++) {
					sheet2.jsonData[i] = [];
					sheet2.jsonData[i][0] = volatilityList[i].code;
					sheet2.jsonData[i][1] = volatilityList[i].name;
					sheet2.jsonData[i][2] = volatilityList[i].value;
					sheet2.jsonData[i][3] = volatilityList[i].startDate;
					sheet2.jsonData[i][4] = volatilityList[i].endDate;
				}
			}

			export_json_to_excel2([sheet0, sheet1, sheet2], '资产分析模板');
		},
		//点击再平衡目标选项
		handleRebalanceTargetClick(value) {
			// 如果点击的是当前选中的项，则取消选择（设置为 null） 不是的话则需要将value值赋值给变量（由于使用了prevent修饰符，导致默认的变量赋值无效）
			if (this.parameterJson.rebalanceTarget === value) {
				this.parameterJson.rebalanceTarget = null;
			} else {
				this.parameterJson.rebalanceTarget = value;
			}
		},
		//点击再平衡周期选项
		handleRebalanceCycleClick(value) {
			// 如果点击的是当前选中的项，则取消选择（设置为 null） 不是的话则需要将value值赋值给变量（由于使用了prevent修饰符，导致默认的变量赋值无效）
			if (this.parameterJson.rebalanceCycle === value) {
				this.parameterJson.rebalanceCycle = null;
			} else {
				this.parameterJson.rebalanceCycle = value;
			}
		},
		//添加自定义再选周期时间
		handleAddDate() {
			this.parameterJson.customDate.push(this.addDateInput);
			this.customDateList.push({
				label: this.addDateInput,
				value: this.addDateInput
			});
			this.addDateInput = '';
			this.addDateFormShow = false;
			// this.customSelectVisable = false;
		},
		handleCancleAddDate() {
			this.addDateFormShow = !this.addDateFormShow;
			this.customSelectVisable = false;
		},
		//点击外围关闭弹窗
		closeDialog() {
			this.customSelectVisable = false;
		},
		//编辑页面数据初始处理
		setEditInfoData() {
			let selectList = this.parameterJson?.positionList;
			this.addInfo = {
				selectList: selectList || [],
				ployProject: this.parameterJson?.ployProject || undefined,
				ployId: this.parameterJson?.ployId || undefined,
				constructionDate: this.parameterJson?.constructionDate || ''
			};
			this.addInfo.codeList = this.addInfo.selectList.map((item) => {
				return item.code;
			});
			this.setCodeList = this.addInfo.codeList;
		},
		//获取前一个页面所得的基本信息
		setAddInfoData() {
			this.addInfo = {
				selectList: this.otherInfo?.selectList || [],
				ployProject: this.otherInfo?.ployProject || undefined,
				ployId: this.otherInfo?.ployId || undefined,
				constructionDate: this.otherInfo?.constructionDate || ''
			};
			//如果有ployId标识为组合--配置策略进来的构造日期为前一个页面带进来的
			if (this.addInfo.ployId) {
				this.parameterJson.constructionDate = this.addInfo.constructionDate;
			} else {
				//构造日期默认一年前
				this.parameterJson.constructionDate = this.moment(this.moment()).subtract(3, 'years').format('YYYY-MM-DD');
			}
			//策略产品列表 （更新持仓不更新该列表，当前版本在需要使用ployProject的地方会禁用增加持仓逻辑）
			this.parameterJson.ployProject = this.addInfo?.ployProject;
			this.parameterJson.ployId = this.addInfo.ployId;
			//如果为新建，需要主动调接口查询相应的数据
			this.getInfoData();
		},
		//查询页面信息
		getInfoData() {
			this.addInfo.codeList = this.addInfo.selectList.map((item) => {
				return item.code;
			});
			this.parameterJson.riskWeightList = this.addInfo?.selectList?.map((item) => {
				return {
					name: item.name,
					code: item.code,
					targetRisk: ''
				};
			});

			this.setCodeList = this.addInfo.codeList;
			this.changeConstructionDate();
		},
		// 组合信息 编辑时获取
		async getDetailData() {
			this.pageLoading = true;
			// 策略/组合id
			let params = {
				id: this.setDetailId
			};
			let reqData = await getParameterDetails(params);
			let { data, mtycode, mtymessage } = reqData || {};
			if (mtycode == 200) {
				const parameterJsonRes = data.parameterJson || {};
				this.parameterJson = parameterJsonRes;
				this.parameterJson.data = {};
				this.parameterJson.data.returnList = parameterJsonRes?.data?.returnList || [];
				this.parameterJson.data.coefficientList = parameterJsonRes?.data?.coefficientList || [];
				this.parameterJson.data.volatilityList = parameterJsonRes?.data?.volatilityList || [];
				console.log(this.parameterJson);
				this.setEditInfoData();
				this.initCorrelationChart(this.parameterJson.data.coefficientList);
				this.initPositionListData();
			} else {
				this.$message.warning(mtymessage);
			}
			this.pageLoading = false;
		},
		//点击资金调配按钮
		handleMoneyEditClick() {
			this.money = this.parameterJson.money;
			this.moneyEditDialog = true;
		},
		//资金调配弹窗确认
		handleMoneyEditSure() {
			this.parameterJson.money = this.money;
			this.moneyEditDialog = false;
		},
		//获取持仓设置列表
		async getPositionSettingList() {
			this.positionLoading = true;
			let params = {
				codeList: this.setCodeList,
				marketType: this.marketType,
				constructionDate: this.parameterJson.constructionDate
			};
			let reqData = await getPositionSettingList(params);
			let { data, mtycode, mtymessage } = reqData || {};
			if (mtycode == 200) {
				this.parameterJson.positionList = data?.positionList || [];
				this.parameterJson.priorRisk = data.priorRisk;
				// this.setCodeList =
				// 	this.parameterJson?.positionList?.map((item) => {
				// 		return item.code;
				// 	}) || [];
			} else {
				this.$message.warning(mtymessage);
			}
			this.positionLoading = false;
			this.initPositionListData();
		},
		//添加持仓设置基金
		async handleAddSetCode(value) {
			this.addInfo.selectList.push({
				name: value.label,
				code: value.value
			});
			this.getInfoData();
		},
		//删除持仓设置行
		handlePositionDelete(index, value) {
			let delIndex = this.addInfo.selectList.findIndex((item) => {
				return item.code === value.code;
			});
			if (delIndex >= 0) {
				this.addInfo.selectList.splice(delIndex, 1);
				this.getInfoData();
			}
		},
		//相关系数矩阵
		// async getCorrelationData() {
		// 	//新增则由上一个页面带参数查询 编辑则通过id查询
		// 	let params = {};
		// 	if (this.isEditPage) {
		// 		params = {
		// 			id: this.setDetailId
		// 		};
		// 	} else {
		// 		//todo
		// 		params = {
		// 			codeList: this.addInfo.codeList
		// 		};
		// 	}
		// 	let req = await getReturnsCorrelationMatrix(params);
		// 	let { data, code, message } = req || {};
		// 	if (code == 200) {
		// 		this.correlationData = data || [];
		// 	} else {
		// 		this.$message.warning(message);
		// 		this.correlationData = [];
		// 	}
		// 	this.initCorrelationChart(this.correlationData);
		// },
		//构造日期变化时 需要重新调用持仓设置 接口
		changeConstructionDate() {
			if (this.userChangeModelDate) {
				this.$alert('构造日期变更同时资产分析模型起始时间也会相应变更', '温馨提示', {
					confirmButtonText: '确定',
					callback: (action) => {
						this.getPositionSettingList();
						//重新调用资产分析模型接口
						//默认选近三年时间
						let date = this.moment(this.parameterJson.constructionDate);
						let defaultEndDate = this.moment(date).format('YYYY-MM-DD');
						let defaultStartDate = this.moment(date).subtract(3, 'years').format('YYYY-MM-DD');
						this.modelDate = [defaultStartDate, defaultEndDate];
						this.getModelTemplateData();
					}
				});
				this.userChangeModelDate = false;
				return;
			}
			this.getPositionSettingList();
			//重新调用资产分析模型接口
			//默认选近三年时间
			let date = this.moment(this.parameterJson.constructionDate);
			let defaultEndDate = this.moment(date).format('YYYY-MM-DD');
			let defaultStartDate = this.moment(date).subtract(3, 'years').format('YYYY-MM-DD');
			this.modelDate = [defaultStartDate, defaultEndDate];
			this.getModelTemplateData();
		},
		//资产分析模型 开始时间结束时间变化
		changeModelDate() {
			this.userChangeModelDate = true;
			//重新调用资产分析模型接口
			this.getModelTemplateData();
		},
		//获取资产分析模型数据
		async getModelTemplateData() {
			this.modelLoading = true;
			let params = {
				codeList: this.setCodeList,
				startDate: this.modelDate[0],
				endDate: this.modelDate[1],
				marketType: this.marketType
			};
			let reqData = await getModelTemplate(params);
			let { data, mtycode, mtymessage } = reqData || {};
			if (mtycode == 200) {
				this.parameterJson.data.returnList = data.returnList || [];
				this.parameterJson.data.coefficientList = data.coefficientList || [];
				this.parameterJson.data.volatilityList = data.volatilityList || [];
				this.initCorrelationChart(this.parameterJson.data.coefficientList);
			} else {
				this.$message.warning(mtymessage);
			}
			this.modelLoading = false;
			this.initPositionListData();
		},
		//将模型中的数据更新到持仓设置列表内
		initPositionListData() {
			//如果持仓或者模型数据没有请求回来，就先不进行处理了
			if (this.positionLoading || this.modelLoading) {
				return;
			}
			this.parameterJson.positionList = this.parameterJson.positionList.map((item) => {
				let cuttentReturnItem = this.parameterJson?.data?.returnList?.find((returnItem) => {
					return item.code === returnItem.code;
				});
				let volatilityItem = this.parameterJson?.data?.volatilityList?.find((returnItem) => {
					return item.code === returnItem.code;
				});
				return {
					...item,
					returnValue: cuttentReturnItem?.value,
					volatilityValue: volatilityItem?.value
				};
			});
		},
		updateModelTemplate() {
			this.assetAnalysisModelVisable = !this.assetAnalysisModelVisable;
			this.initCorrelationChart(this.parameterJson.data.coefficientList);
			this.initPositionListData();
		},
		//绘制收益相关矩阵
		initCorrelationChart(data) {
			let tableHeader = [];
			data.forEach((item) => {
				let index = tableHeader.findIndex((header) => {
					return header.value === item.codeFirst;
				});
				if (index < 0) {
					tableHeader.push({
						label: item.nameFirst,
						value: item.codeFirst
					});
				}
			});
			let correlationColumnList = this.FUNC.deepClone(tableHeader);
			let corrlationRowList = this.FUNC.deepClone(tableHeader);
			correlationColumnList.unshift({
				label: '',
				value: 'name'
			});
			let arrLength = tableHeader.length;
			let correlationData1 = Array(arrLength);
			for (let i = 0; i < correlationColumnList.length; i++) {
				for (let j = 0; j < corrlationRowList.length; j++) {
					if (!correlationData1[j]) {
						correlationData1[j] = {};
					}
					//如果当前tableheader name 为名称 则设置当前的名称列
					if (correlationColumnList[i]?.value === 'name') {
						correlationData1[j]['name'] = this.FUNC.deepClone(corrlationRowList[j].label);
						continue;
					}
					let value = data.find((item) => {
						return item.codeFirst === correlationColumnList[i]?.value && item.codeSecond === corrlationRowList[j].value;
					});
					correlationData1[j][correlationColumnList[i]?.value] = value ? this.FUNC.deepClone(value.value) : '--';
				}
			}
			this.$nextTick(() => {
				this.$refs['correlation-coefficient-chart']?.getData(correlationColumnList, correlationData1);
			});
			// this.$nextTick(() => {
			// 	this.$refs['correlation-coefficient-chart']?.getData(data, {});
			// });
		},
		async remoteMethod(query, callBack) {
			// //未输入内容时不调用接口，接口会报错
			// if (!query) {
			// 	callBack([]);
			// 	return;
			// }
			this.FUNC.debounceFunc(() => {
				return this.handleGetSearchList(query, callBack);
			}, 1000)();
		},
		async handleGetSearchList(query, callBack) {
			this.fundSearchLoading = true;
			this.fundSearchList = [];
			let params = {
				flag: this.flag,
				message: query
			};
			let reqData = await getSearchList(params);
			this.fundSearchLoading = false;
			let { data, mtycode: code, mtymessage: message } = reqData || {};
			if (code == 200) {
				this.fundSearchList = data.map((item) => {
					return {
						label: item.name,
						value: item.code
					};
				});
				callBack(this.fundSearchList);
			} else {
				this.$message.warning(message);
			}
		},
		handleNext() {
			this.$emit('nextStep', this.parameterJson, this.listItemInfo);
		},
		handleBack() {
			this.$emit('backStep');
		},
		handleStartOptimize() {
			//如果模型选择 为风险预算模型 校验设置风险权重值相加为100
			if (this.parameterJson.model === this.modelOptions['risk_budget'].value) {
				let arr =
					this.parameterJson.riskWeightList.map((item) => {
						return item.targetRisk;
					}) || [];
				let sum = arr.reduce((prev, curr) => {
					return prev - 0 + (curr - 0);
				});
				if (sum !== 100) {
					this.$message.warning('设置风险权重:权重总和必须等于100.00%，剩余风险权重0%，风险权重不能为0%。');
					return;
				}
			}
			if (this.$refs['model_form']) {
				this.$refs['model_form'].validate((valid) => {
					if (valid) {
						this.handleNext();
					} else {
						this.$message.warning('模型选择信息填写有误');
						return false;
					}
				});
			} else {
				this.handleNext();
			}

			//调用获取优化方案接口
		},
		resetForm() {
			this.$refs['model_form'].resetFields();
		},
		//资产分析模型  excel持仓导入
		changeAppendix2(file, fileList) {
			//禁用开始结束时间选择
			this.modelDateDisabled = true;
			this.modelDate = [];
			const _this = this;
			this.parameterJson.data.returnList = [];
			this.parameterJson.data.coefficientList = [];
			this.parameterJson.data.volatilityList = [];
			// const fileName = file.name;
			const reader = new FileReader();
			reader.readAsArrayBuffer(file.raw);
			reader.onload = function () {
				const buffer = reader.result;
				const bytes = new Uint8Array(buffer);
				const length = bytes.byteLength;
				let binary = '';
				for (let i = 0; i < length; i++) {
					binary += String.fromCharCode(bytes[i]);
				}
				// const XLSX = require('xlsx');
				const wb = XLSX.read(binary, {
					type: 'binary'
				});
				//第一个工作表
				const outdata0 = XLSX.utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]]);
				//第一个工作表
				const outdata1 = XLSX.utils.sheet_to_json(wb.Sheets[wb.SheetNames[1]]);
				//第三个工作表
				const outdata2 = XLSX.utils.sheet_to_json(wb.Sheets[wb.SheetNames[2]]);
				console.log('***', outdata0, outdata1, outdata2);
				outdata0.forEach((i) => {
					// console.log(i);
					let result = {};
					_this.expectedColumnList.forEach((column) => {
						result[column.value] = i[column.oriLabel || column.label];
					});
					_this.parameterJson.data.returnList.push(result); //此处是把数据添加到表格中
				});
				outdata1.forEach((i) => {
					// console.log(i);
					let result = {};
					_this.coefficientColumnList.forEach((column) => {
						result[column.value] = i[column.label];
					});
					_this.parameterJson.data.coefficientList.push(result); //此处是把数据添加到表格中
				});
				outdata2.forEach((i) => {
					// console.log(i);
					let result = {};
					_this.volatilityColumnList.forEach((column) => {
						result[column.value] = i[column.label];
					});
					_this.parameterJson.data.volatilityList.push(result); //此处是把数据添加到表格中
				});
			};
		},
		//批量导入资产分析模型
		handleImportAssets() {
			this.assetAnalysisModelVisable = true;
		},
		// 动态样式设置
		cellStyle(value) {
			if (value - 0 < 0) {
				return 'color:red';
			}
		}
	}
};
</script>
<style lang="scss">
.custom-cycle-popper-wrapper {
	.cc-add-date-item {
		padding: 5px 12px;
	}
	.cc-options-add-wrapper {
		border-top: 1px solid #e9e9e9;
		padding: 11px 8px 0;
		margin-top: 5px;
	}
}
.custom-cycle-selsect {
	display: none;
}

.asset_analysis_model_wrapper {
	min-width: 900px;
	.el-dialog__header {
		padding: 16px 24px;
		border-bottom: 1px solid #e9e9e9;
	}
	.el-dialog__body {
		padding: 0 20px;
	}
	.el-date-editor .el-range-separator {
		width: unset;
	}
	.el-upload {
		height: unset;
	}
}
</style>
<style lang="scss" scoped>
.add-input-wrapper {
	::v-deep .el-input__inner {
		padding: 0 30px !important;
	}
}
.setting-optimization-content {
	.sub_title {
		color: rgba(0, 0, 0, 0.85);
		font-size: 16px;
		font-style: normal;
		font-weight: 500;
		line-height: 24px; /* 150% */
		margin-bottom: 12px;
	}
	.box-container {
		border-radius: 4px;
		border: 1px solid #d4d8e5;
		background: #fff;
		box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.1);
		padding: 0 20px 20px;
		.optimized-wrapper {
			display: flex;
		}
		.optimized-table-wrapper {
			width: 50%;
			.tabel-dec {
				display: flex;
				padding: 8px;
				justify-content: flex-end;
				align-items: center;
				gap: 8px;
				flex: 1 0 0;
				border-right: 1px solid #e9e9e9;
				border-left: 1px solid #e9e9e9;
				border-bottom: 1px solid #e9e9e9;
				background: #fff;
				color: rgba(0, 0, 0, 0.45);
			}
		}
		.amount_setting_wrapper {
			.amount_content {
				display: flex;
			}
			.amount_btn {
				margin-left: 12px;
			}
		}
		.risk-factor-table-wrapper {
			display: flex;
			.rft-header {
				flex: auto;
				.header-item {
					padding: 12px 8px;
					border: 1px solid #e9e9e9;
					background: #f5f5f5;
				}
			}
			.table-item-wrapper {
				flex: auto;
			}
			.table-item {
				padding: 12px 8px;
				border-top: 1px solid #e9e9e9;
				border-right: 1px solid #e9e9e9;
				border-bottom: 1px solid #e9e9e9;
				background: #fff;
			}
		}
		.section-container {
			margin-bottom: 20px;
			.positon-set-search {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 12px;
				.ps_search_form {
					display: flex;
					align-items: center;
				}
			}
			.tabel-dec {
				display: flex;
				padding: 8px;
				justify-content: flex-end;
				align-items: center;
				gap: 8px;
				flex: 1 0 0;
				border-right: 1px solid #e9e9e9;
				border-left: 1px solid #e9e9e9;
				border-bottom: 1px solid #e9e9e9;
				background: #fff;
				color: rgba(0, 0, 0, 0.45);
			}
			.risk-weight-set-wrapper {
				.risk-weight-set-title {
					color: rgba(0, 0, 0, 0.85);
					font-size: 14px;
					font-style: normal;
					font-weight: 500;
					line-height: 22px; /* 157.143% */
					margin-bottom: 12px;
				}
				.risk-weight-set-subtitle {
					margin-left: 12px;
					color: rgba(0, 0, 0, 0.45);
					font-weight: 400;
					line-height: 22px; /* 157.143% */
				}
			}
			.custom-cycle-wrapper {
				margin-top: 12px;
				width: fit-content;
			}
		}
		.modle-select-wrapper {
			::v-deep .el-radio-group {
				.el-radio {
					margin-right: unset;
				}
				.el-radio__input {
					display: none;
				}
				.el-radio__label {
					padding-left: 6px;
				}
			}
			margin-bottom: 20px;
		}
		.model-form-wrapper {
			::v-deep .el-form-item__label {
				padding-right: 8px;
			}
			padding: 20px;
			background: #ecf5ff;
			::v-deep .el-radio-group {
				.el-radio {
					margin-right: unset;
				}
			}
			.model-form-inner-wrapper {
				// display: flex;
				.inner-title {
					color: rgba(0, 0, 0, 0.85);
					font-size: 14px;
					font-style: normal;
					font-weight: 500;
					line-height: 22px; /* 157.143% */
					margin: 8px 0;
				}
				.model_form_wrapper {
					display: flex;
					width: 100%;
					flex-wrap: wrap;
					.el-form-item {
						// display: flex;
						margin-right: 20px;
					}
				}
			}
		}
		.section-footer {
			text-align: center;
			margin-top: 20px;
		}
		.amount_value {
			color: rgba(0, 0, 0, 0.65);
			font-size: 24px;
			font-style: normal;
			font-weight: 500;
			line-height: 32px; /* 133.333% */
		}
	}
	.asset_analysis_model_body {
		position: relative;
		.aam_cul {
			position: absolute;
			top: 13px;
			right: 0;
			color: rgba(0, 0, 0, 0.45);
			font-size: 14px;
			font-style: normal;
			font-weight: 400;
			line-height: 22px; /* 157.143% */
		}
		::v-deep .el-tabs {
			.el-tabs__item {
				height: unset;
				line-height: 48px;
			}
		}
		.template_wrapper {
			display: flex;
			align-items: center;
			padding-top: 5px;
			padding-bottom: 20px;
			flex-wrap: wrap;
		}
	}
}
.setting-radio-wrapper {
	::v-deep .el-radio__inner {
		display: none;
	}
	.el-radio {
		margin-right: unset;
	}
}
.el-radio.is-bordered + .el-radio.is-bordered {
	margin-left: 12px;
}
.cr-orange {
	color: #4096ff;
	font-size: 14px;
	font-style: normal;
	font-weight: 400;
	line-height: 22px; /* 157.143% */
}
</style>
