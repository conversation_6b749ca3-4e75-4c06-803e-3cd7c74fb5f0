+<!--  -->
<template>
  <div class="alphaHeaderView">
    <el-tabs :value="tabName" @tab-click="clickTab" type="border-card">
      <!-- <el-tab-pane label="alpha筛选器">
        <div v-if="show == 1">
          <alphaHeaderFilter2></alphaHeaderFilter2>
        </div>
      </el-tab-pane> -->
      <el-tab-pane label="筛选">
        <div >
          <scoreFilter></scoreFilter>
        </div>
      </el-tab-pane>
      <!-- <el-tab-pane label="打分卡">
        <div v-if="show == 3">
          <scoreRank></scoreRank>
        </div>
      </el-tab-pane> -->
    </el-tabs>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import alphaHeaderFilter2 from "./alpha/index.vue";
// import alphaHeaderFilter from '@/components/page/alphaFilter/index';
import scoreFilter from "./beta/index.vue";
import scoreRank from "./score/index.vue";
// import scoreCard from '@/components/page/scoreandrankNew/index';
export default {
  //import引入的组件需要注入到对象中才能使用
  components: {
    // alphaHeaderFilter, scoreCard,
    alphaHeaderFilter2,
    scoreFilter,
    scoreRank
  },
  data() {
    //这里存放数据
    return {
      tabName: "0",
      show: 0
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    clickTab(e) {
      try {
        this.localStorage.setItem("mty_filterTab", JSON.stringify(e.index));
        this.show = Number(e.index) + 1;
      } catch (err) {}
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    let that = this;
    if (that.$route.query.model_name) {
      this.tabName = "0";
      this.show = 1;
    } else {
      if (
        this.localStorage.getItem("mty_filterTab") != null &&
        this.localStorage.getItem("mty_filterTab") != undefined &&
        this.localStorage.getItem("mty_filterTab") != "undefined" &&
        this.localStorage.getItem("mty_filterTab") != "null"
      ) {
        this.tabName =
          typeof JSON.parse(this.localStorage.getItem("mty_filterTab")) ==
          "number"
            ? this.localStorage.getItem("mty_filterTab")
            : JSON.parse(this.localStorage.getItem("mty_filterTab"));
        this.show =
          Number(JSON.parse(this.localStorage.getItem("mty_filterTab"))) + 1;
      } else {
        this.show = 1;
        this.tabName = "0";
      }
    }
    if (this.FUNC.isEmpty(this.$route.query.code)) {
      // window.localStorage.setItem('keepactiveIndex', JSON.stringify(2));
      this.$nextTick(() => {
        this.tabName = this.$route.query.type == "alpha" ? "0" : "1";
        this.show = this.$route.query.type == "alpha" ? 1 : 2;
        if (this.$route.query.type == "beta") {
          this.localStorage.setItem("mty_filterNew_index", JSON.stringify(1));
        }
      });
    }
  },
  beforeCreate() {}, //生命周期 - 创建之前
  beforeMount() {}, //生命周期 - 挂载之前
  beforeUpdate() {}, //生命周期 - 更新之前
  updated() {}, //生命周期 - 更新之后
  beforeDestroy() {}, //生命周期 - 销毁之前
  destroyed() {}, //生命周期 - 销毁完成
  activated() {} //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
.alphaHeaderView {
  width: 100%;
  padding: 16px 24px 24px 24px;
  // height: calc(100% - 120px);
  // margin-bottom: 90px;
  // padding-bottom:30px;
  ::v-deep .el-tabs--border-card {
    border: 0px;
    -webkit-box-shadow: 0 0 0 0;
    box-shadow: 0 0 0 0;
  }
  .el-tabs {
    height: 100% !important;
    ::v-deep .el-tabs__content {
      padding: 0px !important;
      height: calc(100% - 50px) !important;
      .el-tab-pane {
        height: 100% !important;
      }
    }
  }
}
</style>
