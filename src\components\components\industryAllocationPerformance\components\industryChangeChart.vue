<template>
	<div>
		<div v-loading="cardLoading">
			<div style="display: flex; justify-content: flex-end; align-items: center; margin: 16px 0">
				<div style="width: 200px; margin-right: 20px">
					<el-progress
						v-show="percentageShow"
						:text-inside="true"
						:stroke-width="24"
						:percentage="percentageFilter"
						:status="percentageFilter >= 100 ? 'success' : ''"
					></el-progress>
				</div>
				<div style="cursor: pointer" @click="play">
					<i
						:class="pauseFlag ? 'el-icon-video-pause' : 'el-icon-video-play'"
						style="color: #4096ff; margin-right: 24px; font-size: 16px"
					></i>
				</div>
				<div>
					<el-button @click="createGif">导出GIF</el-button>
				</div>
			</div>
			<div class="charts_fill_class">
				<v-chart
					ref="industryChangeChart"
					v-loading="loading"
					class="charts_one_class"
					:style="`height: ${innerheight}px`"
					autoresize
					element-loading-text="暂无数据"
					element-loading-spinner="el-icon-document-delete"
					element-loading-background="rgba(239, 239, 239, 0.5)"
					:options="option"
				/>
			</div>
		</div>
	</div>
</template>

<script>
import VChart from 'vue-echarts';
import gifshot from 'gifshot';
export default {
	data() {
		return {
			option: {},
			loading: true,
			list: [],
			industryList: [],
			yearList: [],
			color: [],
			index: 0,
			timeOut: null,
			interval: null,
			changeOption: {},
			cardLoading: false,
			innerheight: 600,
			pauseFlag: true,
			info: {},
			percentage: 0,
			loadingExport: null,
			percentageShow: false
		};
	},
	components: {
		VChart
	},
	computed: {
		percentageFilter() {
			return this.percentage.toFixed(2);
		}
	},
	methods: {
		getCharts(data, type, info) {
			this.info = info;
			if (this.timeOut && this.interval) {
				clearInterval(this.timeOut);
				clearTimeout(this.interval);
				this.timeOut = null;
				this.interval = null;
			}
			let that = this;
			this.loading = false;
			// let myChart = echarts.init(document.getElementById('industryChangeChart'));
			if (type == 'manager' || type == 'company') {
				data.map((item) => {
					item.weight = item.weight * 100;
				});
			}

			let max = data.sort((a, b) => {
				return b.weight - a.weight;
			})[0].weight;
			this.list = [];
			this.industryList = Array.from(
				new Set(
					data.map((item) => {
						return item.industry_name;
					})
				)
			);
			this.innerheight = this.industryList * 15 < 400 ? 400 : this.industryList * 15 > 1000 ? 1000 : this.industryList * 15;
			this.yearList = Array.from(
				new Set(
					data.map((item) => {
						return item.yearqtr;
					})
				)
			).sort();
			this.yearList.map((yearqtr, index) => {
				this.list.push({
					yearqtr,
					data: []
				});
				this.industryList.map((industry) => {
					this.list[index].data.push({
						industry,
						data: 0
					});
				});
			});
			data.map((item) => {
				this.list.map((obj, index) => {
					if (item.yearqtr == obj.yearqtr) {
						obj.data
							.map((industry) => {
								if (item.industry_name == industry.industry) {
									industry.data = item.weight ? Number(item.weight).toFixed(2) : 0;
								}
							})
							.sort((a, b) => {
								return b.data > a.data;
							});
					}
				});
			});
			this.color = [
				'rgba(253, 156, 255, 1)',
				'rgba(254, 208, 238, 1)',
				'rgba(254, 174, 174, 1)',
				'rgba(253, 208, 159, 1)',
				'rgba(251, 227, 142, 1)',
				'rgba(169, 244, 208, 1)',
				'rgba(208, 232, 255, 1)',
				'rgba(159, 212, 253, 1)',
				'rgba(174, 201, 254, 1)',
				'rgba(219, 174, 255, 1)',
				'rgba(154, 137, 255, 1)',
				// '#FD9CFF',
				// '#FED0EE',
				// '#FEAEAE',
				// '#FDD09F',
				// '#FBE38E',
				// '#A9F4D0',
				// '#D0E8FF',
				// '#9FD4FD',
				// '#AEC9FE',
				// '#DBAEFF',
				// '#9A89FF'
				'#4096ff',
				'#4096ff',
				'#7388A9',
				'#6F80DD',
				'#6C96F2',
				'#FD6865',
				'#83D6AE',
				'#88C9E9',
				'#ED589D',
				'#FA541C',
				'#18C2A0',
				'#E85D2D'
			];
			this.index = 0;
			this.option = {
				xAxis: {
					margin: 12,
					max: Math.ceil(max),
					axisLabel: {
						fontSize: '16px',
						color: 'rgba(0,0,0,0.65)'
					},
					axisLine: {
						lineStyle: {
							color: '#e9e9e9'
						}
					},
					axisTick: {
						show: false
					}
				},
				grid: { top: '0', left: '56x', right: 0, bottom: '40px', containLabel: true },
				yAxis: {
					margin: 16,
					type: 'category',
					data: this.industryList,
					inverse: true,
					axisLabel: {
						show: false,
						fontSize: '8px',
						color: 'rgba(0,0,0,0.65)'
					},
					axisLine: {
						lineStyle: {
							color: '#e9e9e9'
						}
					},
					axisTick: {
						show: false
					},
					animationDuration: 100,
					animationDurationUpdate: 100
				},
				series: [
					{
						type: 'bar',
						realtimeSort: true,
						seriesLayoutBy: 'column',
						label: {
							show: true,
							position: 'left',
							valueAnimation: true,
							color: '#000000',
							formatter: function (val) {
								return that.industryList[val.dataIndex];
							}
						},
						data: this.list
							.filter((item) => {
								return this.yearList[this.index] == item.yearqtr;
							})[0]
							.data.map((item) => {
								return item.data;
							})
					}
				],
				legend: {
					show: false,
					textStyle: {
						fontSize: '16px'
					}
				},
				graphic: {
					elements: [
						{
							type: 'text',
							right: '160px',
							bottom: '60px',
							style: {
								text: this.yearList[this.index],
								font: 'bolder 80px monospace',
								fill: 'rgba(100, 100, 100, 0.25)'
							},
							z: 100
						}
					]
				}
			};
			this.timeOut = null;
			this.interval = null;
			this.setRun();
		},
		run() {
			let that = this;
			this.index = this.index + 1;
			if (this.index == this.yearList.length) {
				this.index = 0;
			}
			let val = [
				...this.list.filter((item) => {
					return this.yearList[this.index] == item.yearqtr;
				})[0].data
			];
			let sortList = val
				.map((item, i) => {
					return { name: item.industry, value: item.data };
				})
				.sort((a, b) => {
					return b.value - a.value;
				});
			let option = {
				animationDuration: 0,
				animationDurationUpdate: 1000,
				animationEasing: 'linear',
				animationEasingUpdate: 'linear',
				yAxis: {
					margin: 16,
					type: 'category',
					data: sortList.map((item) => {
						return item.name;
					}),
					max: sortList.filter((item) => {
						return item.value > 0;
					}).length,
					axisLabel: {
						show: false,
						fontSize: '8px',
						color: 'rgba(0,0,0,0.65)'
					},
					axisLine: {
						lineStyle: {
							color: '#e9e9e9'
						}
					},
					axisTick: {
						show: false
					},
					inverse: true,
					animationDuration: 100,
					animationDurationUpdate: 100
				},
				graphic: {
					elements: [
						{
							type: 'text',
							right: '160px',
							bottom: '60px',
							style: {
								text: this.yearList[this.index],
								font: 'bolder 80px monospace',
								fill: 'rgba(100, 100, 100, 0.25)'
							},
							z: 100
						}
					]
				},
				series: [
					{
						type: 'bar',
						data: sortList.map((item) => {
							return {
								...item,
								itemStyle: {
									color:
										that.color[
											that.industryList.findIndex((val) => {
												return item.name == val;
											})
										]
								}
							};
						}),
						barWidth: '20px',
						realtimeSort: true,
						label: {
							show: true,
							position: 'left',
							formatter: function (val) {
								return val.name;
							}
						}
					}
				]
			};
			this.$refs['industryChangeChart']?.mergeOptions(option);
		},
		setRun() {
			let that = this;
			if (this.timeOut && this.interval) {
				clearInterval(this.timeOut);
				clearTimeout(this.interval);
				this.timeOut = null;
				this.interval = null;
			} else {
				this.timeOut = setTimeout(function () {
					that.run();
				}, 0);
				this.interval = setInterval(function () {
					that.run();
				}, 2000);
			}
		},
		play() {
			this.pauseFlag = !this.pauseFlag;
			this.cardLoading = true;
			setTimeout(() => {
				this.cardLoading = false;
			}, 2000);
			this.setRun();
		},
		createGif() {
			this.percentageShow = true;
			this.percentage = 0;
			let timeOut = (this.list.length - 1) * 10;
			let addNum = 100 / timeOut;
			let imgArr = [];
			let img;
			for (let index = 0; index < timeOut; index++) {
				let height = this.$refs['industryChangeChart'].$el.clientHeight;
				let width = this.$refs['industryChangeChart'].$el.clientWidth;
				setTimeout(() => {
					img = this.$refs['industryChangeChart'].getDataURL({
						type: 'png',
						backgroundColor: 'white',
						pixelRatio: 2
					});
					imgArr.push(img);
					this.percentage = this.percentage + addNum;
					if (index == timeOut - 1) {
						this.loadingExport = this.$loading({
							lock: true,
							text: '正在下载GIF中,请稍等...',
							spinner: 'el-icon-loading',
							background: 'rgba(0, 0, 0, 0.7)'
						});
						gifshot.createGIF(
							{
								gifWidth: width,
								gifHeight: height,
								interval: 0.1,
								images: imgArr
							},
							(obj) => {
								if (!obj.error) {
									// console.log(obj.image);
									this.downloadGif(obj.image);
								}
							}
						);
					}
				}, index * 200);
			}
		},
		// 下载图片地址和保存的图片名称
		downloadGif(imgsrc, name) {
			let that = this;
			var image = new Image();
			image.setAttribute('crossOrigin', 'anonymous');
			image.onload = function () {
				var a = document.createElement('a'); // 生成一个a元素
				var event = new MouseEvent('click'); // 创建一个单击事件
				a.download = that.info?.name + '行业季度配置变化图'; // 设置图片名称
				a.href = imgsrc; // 将生成的URL设置为a.href属性
				a.dispatchEvent(event); // 触发a的单击事件
			};
			this.loadingExport.close();
			image.src = imgsrc;
		}
	}
};
</script>
