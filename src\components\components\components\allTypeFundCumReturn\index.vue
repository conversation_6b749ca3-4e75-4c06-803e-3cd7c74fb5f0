<template>
	<div class="allTypeFundCumReturn">
		<div class="flex_card">
			<div class="small_template" style="flex: 2">
				<div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px">
					<div class="title">各类型基金业绩</div>
					<div>
						<el-date-picker
							v-model="value2"
							type="daterange"
							align="right"
							unlink-panels
							range-separator="至"
							start-placeholder="开始日期"
							end-placeholder="结束日期"
							format="yyyy-MM-dd"
							value-format="yyyy-MM-dd"
							:picker-options="pickerOptions"
							@change="changeDate"
						>
						</el-date-picker>
					</div>
				</div>
				<div>
					<div class="charts_fill_class" v-loading="loading">
						<v-chart
							v-show="!loading"
							ref="allTypeFundCumReturn"
							:options="option"
							element-loading-text="暂无数据"
							element-loading-spinner="el-icon-document-delete"
							element-loading-background="rgba(239, 239, 239, 0.5)"
							class="charts_one_class"
							style="height: 378px"
							autoresize
						></v-chart>
						<el-empty v-show="loading" description="暂无数据"></el-empty>
					</div>
				</div>
			</div>

			<new-fund ref="newFund"></new-fund>
		</div>
	</div>
</template>

<script>
import VChart from 'vue-echarts';

// 各类型基金收益
import { getAllTypeFundCumReturn } from '@/api/pages/SystemOther.js';

import { barChartOption } from '@/utils/chartStyle.js';
import newFund from '@/components/components/components/newFund/index.vue';
export default {
	components: { VChart, newFund },
	data() {
		return {
			pickerOptions: {
				shortcuts: [
					{
						text: '最近一周',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
							picker.$emit('pick', [start, end]);
						}
					},
					{
						text: '最近一个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
							picker.$emit('pick', [start, end]);
						}
					},
					{
						text: '最近三个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
							picker.$emit('pick', [start, end]);
						}
					}
				]
			},
			value2: [this.moment().subtract(1, 'year').format('YYYY-MM-DD'), this.moment().format('YYYY-MM-DD')],
			loading: true,
			option: {},
			cumReturnDate: {}
		};
	},
	methods: {
		// 获取数据
		getData(data, info) {
			this.$refs['newFund']?.getData(info);
			let xAxis = [];
			data?.map((item) => {
				xAxis.push({
					name: this.FUNC.textConverter(this.COMMON.fundType_zh_en, item.type, 'en', 'zh'),
					value: item.cum_return
				});
			});
			this.loading = false;
			this.option = barChartOption({
				xAxis: [
					{
						type: 'category',
						// data: ['主动权益', '被动权益', '固收+', '可转债', '纯债', 'FOF', '货币']
						data: xAxis.map((item) => {
							return item.name;
						})
					}
				],
				tooltip: {
					formatter: function (val) {
						return val?.[0]?.marker + val?.[0]?.name + ': ' + (val?.[0]?.value ? (val?.[0]?.value).toFixed(2) : '--') + '%';
					}
				},
				yAxis: [
					{
						type: 'value'
					}
				],
				series: [
					{
						name: '收益率',
						// data: [120, 200, 150, 80, 70, 110, 130],
						data: xAxis.map((item) => {
							return {
								value: item.value * 1 ? (item.value * 100).toFixed(2) * 1 : '--',
								itemStyle: {
									color: item.value > 0 ? '#E85D2D' : '#18C2A0'
								}
							};
						}),
						type: 'bar'
					}
				]
			});
		},
		// 获取各类型基金业绩
		async getAllTypeFundCumReturn(info) {
			this.info = info;
			let data = await getAllTypeFundCumReturn({
				code: this.info.code,
				type: this.info.type.join(','),
				start_date: this.cumReturnDate?.start_date || '',
				end_date: this.cumReturnDate?.end_date || ''
			});
			if (data?.mtycode == 200) {
				this.getData(data?.data, this.info);
			}
		},
		// 切换时间
		changeDate(val) {
			this.loading = true;
			this.cumReturnDate['start_date'] = val?.[0];
			this.cumReturnDate['end_date'] = val?.[1];
			this.getAllTypeFundCumReturn(this.info);
		}
	}
};
</script>

<style lang="scss" scoped>
.allTypeFundCumReturn {
	.small_template {
		height: 455px;
	}
}
</style>
