<!--  -->
<template>
	<div>
		<div
			v-show="haveName != ''"
			style="
				font-weight: 400;
				font-size: 14px;
				line-height: 22px;
				color: rgba(0, 0, 0, 0.85);
				margin-left: 0px;
				margin-right: 16px;
				margin-bottom: 4px;
			"
		>
			{{ haveName }}
		</div>
		{{ address }}
		<div class="boxOnlyYSF">
			<!-- <div v-show="showTime" style="margin-right: 16px">
				<el-cascader @change="change" placeholder="请选择时间范围" style="width: 100px" v-model="date" :options="option"> </el-cascader>
			</div> -->
			<div>
				<operator :operator="operator" @resolveMathRange="resolveMathRange"></operator>
			</div>
			<div style="margin-right: 16px">
				<el-select style="width: 100px" @change="change" v-model="selectoption" placeholder="请选择">
					<el-option
						v-for="item in haveName == '特定行业能力' ? dataIndustry2.alpha[0]['申万(2021)'] : dataIndustry2.alpha[4]"
						:disabled="item.forbidstate"
						:key="item.value"
						:label="haveName == '特定行业能力' ? item.label : item.lable"
						:value="haveName == '特定行业能力' ? item.label : item.lable"
					>
					</el-option>
				</el-select>
			</div>
			<div style="display: flex; align-items: center">
				<div>
					<el-input type="number" @input="inputChange" placeholder="权重（%），例60" v-model="input"></el-input>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import operator from '@/pages/filter/fund/beta/componentsFilter/components/operator2.vue';
export default {
	props: {
		dataIndustry: {
			type: Object,
			default: {}
		},
		showTime: {
			type: Boolean,
			default: true
		},
		haveName: {
			type: String,
			default: ''
		},
		dataX: {
			type: Object,
			default: {}
		},
		placeholder: {
			type: String
		},
		indexFlag: {
			type: Number
		},
		baseIndexFlag: {
			type: Number
		},
		optionsselect: {
			type: Array,
			default: []
		}
	},
	//import引入的组件需要注入到对象中才能使用
	components: { operator },
	data() {
		//这里存放数据
		return {
			optionsindustry: [],
			iconFlag: '',
			operator: '',
			showBox: false,
			input: '',
			mathRange: { mathRange: 'line' },
			date: '',
			dataIndustry2: {
				alpha: [
					{
						'申万(2021)': [
							{ value: '360000', label: '轻工制造', forbidstate: false },
							{ value: '630000', label: '电力设备', forbidstate: false },
							{ value: '620000', label: '建筑装饰', forbidstate: false },
							{ value: '340000', label: '食品饮料', forbidstate: false },
							{ value: '350000', label: '纺织服饰', forbidstate: false },
							{ value: '460000', label: '社会服务', forbidstate: false },
							{ value: '610000', label: '建筑材料', forbidstate: false },
							{ value: '370000', label: '医药生物', forbidstate: false },
							{ value: '480000', label: '银行', forbidstate: false },
							{ value: '640000', label: '机械设备', forbidstate: false },
							{ value: '220000', label: '基础化工', forbidstate: false },
							{ value: '650000', label: '国防军工', forbidstate: false },
							{ value: '110000', label: '农林牧渔', forbidstate: false },
							{ value: '230000', label: '钢铁', forbidstate: false },
							{ value: '730000', label: '通信', forbidstate: false },
							{ value: '410000', label: '公用事业', forbidstate: false },
							{ value: '710000', label: '计算机', forbidstate: false },
							{ value: '740000', label: '煤炭', forbidstate: false },
							{ value: '750000', label: '石油石化', forbidstate: false },
							{ value: '240000', label: '有色金属', forbidstate: false },
							{ value: '420000', label: '交通运输', forbidstate: false },
							{ value: '330000', label: '家用电器', forbidstate: false },
							{ value: '280000', label: '汽车', forbidstate: false },
							{ value: '490000', label: '非银金融', forbidstate: false },
							{ value: '770000', label: '美容护理', forbidstate: false },
							{ value: '270000', label: '电子', forbidstate: false },
							{ value: '510000', label: '综合', forbidstate: false },
							{ value: '720000', label: '传媒', forbidstate: false },
							{ value: '430000', label: '房地产', forbidstate: false },
							{ value: '450000', label: '商贸零售', forbidstate: false },
							{ value: '760000', label: '环保', forbidstate: false }
						]
					},
					{
						'申万二级(2021)': []
					},
					{
						'申万三级(2021)': []
					},
					{
						恒生一级: [],
						stockclass: []
					},
					[
						{ value: '手游概念', lable: '手游概念', forbidstate: false },
						{ value: 'LED', lable: 'LED', forbidstate: false },
						{ value: '阿里概念', lable: '阿里概念', forbidstate: false },
						{ value: '富时概念', lable: '富时概念', forbidstate: false },
						{ value: '全息技术', lable: '全息技术', forbidstate: false },
						{ value: '光刻胶', lable: '光刻胶', forbidstate: false },
						{ value: '医疗美容', lable: '医疗美容', forbidstate: false },
						{ value: '工业互联', lable: '工业互联', forbidstate: false },
						{ value: 'UWB概念', lable: 'UWB概念', forbidstate: false },
						{ value: '国产替代', lable: '国产替代', forbidstate: false },
						{ value: '医废处理', lable: '医废处理', forbidstate: false },
						{ value: '虚拟现实', lable: '虚拟现实', forbidstate: false },
						{ value: '机构重仓', lable: '机构重仓', forbidstate: false },
						{ value: '长寿药', lable: '长寿药', forbidstate: false },
						{ value: '电子烟', lable: '电子烟', forbidstate: false },
						{ value: '在线教育', lable: '在线教育', forbidstate: false },
						{ value: '一带一路', lable: '一带一路', forbidstate: false },
						{ value: '超级真菌', lable: '超级真菌', forbidstate: false },
						{ value: '触摸屏', lable: '触摸屏', forbidstate: false },
						{ value: '石墨烯', lable: '石墨烯', forbidstate: false },
						{ value: 'PPP模式', lable: 'PPP模式', forbidstate: false },
						{ value: '数字货币', lable: '数字货币', forbidstate: false },
						{ value: '透明工厂', lable: '透明工厂', forbidstate: false },
						{ value: '黄金概念', lable: '黄金概念', forbidstate: false },
						{ value: '水利建设', lable: '水利建设', forbidstate: false },
						{ value: '新能源', lable: '新能源', forbidstate: false },
						{ value: '养老概念', lable: '养老概念', forbidstate: false },
						{ value: '分拆预期', lable: '分拆预期', forbidstate: false },
						{ value: '网络游戏', lable: '网络游戏', forbidstate: false },
						{ value: '智能穿戴', lable: '智能穿戴', forbidstate: false },
						{ value: '屏下摄像', lable: '屏下摄像', forbidstate: false },
						{ value: '煤化工', lable: '煤化工', forbidstate: false },
						{ value: '影视概念', lable: '影视概念', forbidstate: false },
						{ value: '旧基建', lable: '旧基建', forbidstate: false },
						{ value: '免疫治疗', lable: '免疫治疗', forbidstate: false },
						{ value: '军民融合', lable: '军民融合', forbidstate: false },
						{ value: '燃料电池', lable: '燃料电池', forbidstate: false },
						{ value: '数字孪生', lable: '数字孪生', forbidstate: false },
						{ value: '参股保险', lable: '参股保险', forbidstate: false },
						{ value: '无线耳机', lable: '无线耳机', forbidstate: false },
						{ value: '超级电容', lable: '超级电容', forbidstate: false },
						{ value: '雄安新区', lable: '雄安新区', forbidstate: false },
						{ value: '氦气概念', lable: '氦气概念', forbidstate: false },
						{ value: '生物疫苗', lable: '生物疫苗', forbidstate: false },
						{ value: '降解塑料', lable: '降解塑料', forbidstate: false },
						{ value: '专精特新', lable: '专精特新', forbidstate: false },
						{ value: '无人驾驶', lable: '无人驾驶', forbidstate: false },
						{ value: '云游戏', lable: '云游戏', forbidstate: false },
						{ value: '新能源车', lable: '新能源车', forbidstate: false },
						{ value: '网红直播', lable: '网红直播', forbidstate: false },
						{ value: '共享经济', lable: '共享经济', forbidstate: false },
						{ value: '国产芯片', lable: '国产芯片', forbidstate: false },
						{ value: '天基互联', lable: '天基互联', forbidstate: false },
						{ value: '航母概念', lable: '航母概念', forbidstate: false },
						{ value: '大飞机', lable: '大飞机', forbidstate: false },
						{ value: '节能环保', lable: '节能环保', forbidstate: false },
						{ value: '字节概念', lable: '字节概念', forbidstate: false },
						{ value: '北京冬奥', lable: '北京冬奥', forbidstate: false },
						{ value: '通用航空', lable: '通用航空', forbidstate: false },
						{ value: '代糖概念', lable: '代糖概念', forbidstate: false },
						{ value: '超导概念', lable: '超导概念', forbidstate: false },
						{ value: '迪士尼', lable: '迪士尼', forbidstate: false },
						{ value: '蚂蚁概念', lable: '蚂蚁概念', forbidstate: false },
						{ value: '边缘计算', lable: '边缘计算', forbidstate: false },
						{ value: '氮化镓', lable: '氮化镓', forbidstate: false },
						{ value: '可燃冰', lable: '可燃冰', forbidstate: false },
						{ value: 'CRO', lable: 'CRO', forbidstate: false },
						{ value: '智慧政务', lable: '智慧政务', forbidstate: false },
						{ value: '食品安全', lable: '食品安全', forbidstate: false },
						{ value: '退税商店', lable: '退税商店', forbidstate: false },
						{ value: '北斗导航', lable: '北斗导航', forbidstate: false },
						{ value: '猪肉概念', lable: '猪肉概念', forbidstate: false },
						{ value: '国产软件', lable: '国产软件', forbidstate: false },
						{ value: '乳业', lable: '乳业', forbidstate: false },
						{ value: '高校', lable: '高校', forbidstate: false },
						{ value: '移动支付', lable: '移动支付', forbidstate: false },
						{ value: '地摊经济', lable: '地摊经济', forbidstate: false },
						{ value: 'MSCI大盘', lable: 'MSCI大盘', forbidstate: false },
						{ value: '二胎概念', lable: '二胎概念', forbidstate: false },
						{ value: '抖音小店', lable: '抖音小店', forbidstate: false },
						{ value: '基金重仓', lable: '基金重仓', forbidstate: false },
						{ value: '增强现实', lable: '增强现实', forbidstate: false },
						{ value: '云计算', lable: '云计算', forbidstate: false },
						{ value: 'EDA概念', lable: 'EDA概念', forbidstate: false },
						{ value: '快递概念', lable: '快递概念', forbidstate: false },
						{ value: '万达概念', lable: '万达概念', forbidstate: false },
						{ value: '京东金融', lable: '京东金融', forbidstate: false },
						{ value: '风能', lable: '风能', forbidstate: false },
						{ value: '转基因', lable: '转基因', forbidstate: false },
						{ value: '新材料', lable: '新材料', forbidstate: false },
						{ value: '铁路基建', lable: '铁路基建', forbidstate: false },
						{ value: '工业4.0', lable: '工业4.0', forbidstate: false },
						{ value: 'ETC', lable: 'ETC', forbidstate: false },
						{ value: '充电桩', lable: '充电桩', forbidstate: false },
						{ value: '贬值受益', lable: '贬值受益', forbidstate: false },
						{ value: 'MSCI中盘', lable: 'MSCI中盘', forbidstate: false },
						{ value: '标普概念', lable: '标普概念', forbidstate: false },
						{ value: '5G概念', lable: '5G概念', forbidstate: false },
						{ value: '在线旅游', lable: '在线旅游', forbidstate: false },
						{ value: '体外诊断', lable: '体外诊断', forbidstate: false },
						{ value: '进口博览', lable: '进口博览', forbidstate: false },
						{ value: '医疗器械', lable: '医疗器械', forbidstate: false },
						{ value: '基本金属', lable: '基本金属', forbidstate: false },
						{ value: 'IPv6', lable: 'IPv6', forbidstate: false },
						{ value: '智能电网', lable: '智能电网', forbidstate: false },
						{ value: '券商概念', lable: '券商概念', forbidstate: false },
						{ value: '高送转', lable: '高送转', forbidstate: false },
						{ value: '租售同权', lable: '租售同权', forbidstate: false },
						{ value: '维生素', lable: '维生素', forbidstate: false },
						{ value: '无线充电', lable: '无线充电', forbidstate: false },
						{ value: '预亏预减', lable: '预亏预减', forbidstate: false },
						{ value: '中超概念', lable: '中超概念', forbidstate: false },
						{ value: '稀土永磁', lable: '稀土永磁', forbidstate: false },
						{ value: '天然气', lable: '天然气', forbidstate: false },
						{ value: '广电', lable: '广电', forbidstate: false },
						{ value: 'VPN', lable: 'VPN', forbidstate: false },
						{ value: '证金持股', lable: '证金持股', forbidstate: false },
						{ value: '锂电池', lable: '锂电池', forbidstate: false },
						{ value: '车联网', lable: '车联网', forbidstate: false },
						{ value: '深股通', lable: '深股通', forbidstate: false },
						{ value: '海绵城市', lable: '海绵城市', forbidstate: false },
						{ value: '电商概念', lable: '电商概念', forbidstate: false },
						{ value: '举牌概念', lable: '举牌概念', forbidstate: false },
						{ value: '预制菜', lable: '预制菜', forbidstate: false },
						{ value: '中字头', lable: '中字头', forbidstate: false },
						{ value: 'Facebook', lable: 'Facebook', forbidstate: false },
						{ value: '3D打印', lable: '3D打印', forbidstate: false },
						{ value: '半导体', lable: '半导体', forbidstate: false },
						{ value: '页岩气', lable: '页岩气', forbidstate: false },
						{ value: '债转股', lable: '债转股', forbidstate: false },
						{ value: '智慧城市', lable: '智慧城市', forbidstate: false },
						{ value: '流感', lable: '流感', forbidstate: false },
						{ value: '病毒防治', lable: '病毒防治', forbidstate: false },
						{ value: 'MicroLED', lable: 'MicroLED', forbidstate: false },
						{ value: '3D摄像头', lable: '3D摄像头', forbidstate: false },
						{ value: '无人机', lable: '无人机', forbidstate: false },
						{ value: '美丽中国', lable: '美丽中国', forbidstate: false },
						{ value: '3D玻璃', lable: '3D玻璃', forbidstate: false },
						{ value: '农业种植', lable: '农业种植', forbidstate: false },
						{ value: '乡村振兴', lable: '乡村振兴', forbidstate: false },
						{ value: '装配建筑', lable: '装配建筑', forbidstate: false },
						{ value: '纾困概念', lable: '纾困概念', forbidstate: false },
						{ value: '免税概念', lable: '免税概念', forbidstate: false },
						{ value: '白酒', lable: '白酒', forbidstate: false },
						{ value: '油气设服', lable: '油气设服', forbidstate: false },
						{ value: '国家安防', lable: '国家安防', forbidstate: false },
						{ value: '海工装备', lable: '海工装备', forbidstate: false },
						{ value: '2025规划', lable: '2025规划', forbidstate: false },
						{ value: 'OLED', lable: 'OLED', forbidstate: false },
						{ value: '送转预期', lable: '送转预期', forbidstate: false },
						{ value: '参股银行', lable: '参股银行', forbidstate: false },
						{ value: '百度概念', lable: '百度概念', forbidstate: false },
						{ value: '参股期货', lable: '参股期货', forbidstate: false },
						{ value: '互联金融', lable: '互联金融', forbidstate: false },
						{ value: 'MLCC', lable: 'MLCC', forbidstate: false },
						{ value: '地热能', lable: '地热能', forbidstate: false },
						{ value: '智能电视', lable: '智能电视', forbidstate: false },
						{ value: '肝素概念', lable: '肝素概念', forbidstate: false },
						{ value: '纳米银', lable: '纳米银', forbidstate: false },
						{ value: '尾气治理', lable: '尾气治理', forbidstate: false },
						{ value: '垃圾分类', lable: '垃圾分类', forbidstate: false },
						{ value: '社保重仓', lable: '社保重仓', forbidstate: false },
						{ value: '草甘膦', lable: '草甘膦', forbidstate: false },
						{ value: '参股券商', lable: '参股券商', forbidstate: false },
						{ value: '互联医疗', lable: '互联医疗', forbidstate: false },
						{ value: '预盈预增', lable: '预盈预增', forbidstate: false },
						{ value: '超级品牌', lable: '超级品牌', forbidstate: false },
						{ value: '区块链', lable: '区块链', forbidstate: false },
						{ value: '特斯拉', lable: '特斯拉', forbidstate: false },
						{ value: '钛白粉', lable: '钛白粉', forbidstate: false },
						{ value: '成渝特区', lable: '成渝特区', forbidstate: false },
						{ value: '人脑工程', lable: '人脑工程', forbidstate: false },
						{ value: '新基建', lable: '新基建', forbidstate: false },
						{ value: '三板精选', lable: '三板精选', forbidstate: false },
						{ value: '商汤概念', lable: '商汤概念', forbidstate: false },
						{ value: 'WiFi', lable: 'WiFi', forbidstate: false },
						{ value: '赛马概念', lable: '赛马概念', forbidstate: false },
						{ value: '超清视频', lable: '超清视频', forbidstate: false },
						{ value: '胎压监测', lable: '胎压监测', forbidstate: false },
						{ value: '疫苗冷链', lable: '疫苗冷链', forbidstate: false },
						{ value: '健康中国', lable: '健康中国', forbidstate: false },
						{ value: 'RCS概念', lable: 'RCS概念', forbidstate: false },
						{ value: '远程办公', lable: '远程办公', forbidstate: false },
						{ value: '太阳能', lable: '太阳能', forbidstate: false },
						{ value: '彩票概念', lable: '彩票概念', forbidstate: false },
						{ value: '辅助生殖', lable: '辅助生殖', forbidstate: false },
						{ value: '蝗虫防治', lable: '蝗虫防治', forbidstate: false },
						{ value: '基因测序', lable: '基因测序', forbidstate: false },
						{ value: '养老金', lable: '养老金', forbidstate: false },
						{ value: '稀缺资源', lable: '稀缺资源', forbidstate: false },
						{ value: '核能核电', lable: '核能核电', forbidstate: false },
						{ value: '小金属', lable: '小金属', forbidstate: false },
						{ value: 'MiniLED', lable: 'MiniLED', forbidstate: false },
						{ value: '智能机器', lable: '智能机器', forbidstate: false },
						{ value: '口罩', lable: '口罩', forbidstate: false },
						{ value: '苹果概念', lable: '苹果概念', forbidstate: false },
						{ value: '消毒剂', lable: '消毒剂', forbidstate: false },
						{ value: '汽车拆解', lable: '汽车拆解', forbidstate: false },
						{ value: '新零售', lable: '新零售', forbidstate: false },
						{ value: '氢能源', lable: '氢能源', forbidstate: false },
						{ value: '生物识别', lable: '生物识别', forbidstate: false },
						{ value: '精准医疗', lable: '精准医疗', forbidstate: false },
						{ value: '创投', lable: '创投', forbidstate: false },
						{ value: '青蒿素', lable: '青蒿素', forbidstate: false },
						{ value: '蓝宝石', lable: '蓝宝石', forbidstate: false },
						{ value: 'GDR概念', lable: 'GDR概念', forbidstate: false },
						{ value: '传感器', lable: '传感器', forbidstate: false },
						{ value: '阿兹海默', lable: '阿兹海默', forbidstate: false },
						{ value: '冷链物流', lable: '冷链物流', forbidstate: false },
						{ value: '网络安全', lable: '网络安全', forbidstate: false },
						{ value: 'PCB', lable: 'PCB', forbidstate: false },
						{ value: '地塞米松', lable: '地塞米松', forbidstate: false },
						{ value: '智能家居', lable: '智能家居', forbidstate: false },
						{ value: '小米概念', lable: '小米概念', forbidstate: false },
						{ value: '人工智能', lable: '人工智能', forbidstate: false },
						{ value: '知识产权', lable: '知识产权', forbidstate: false },
						{ value: '物联网', lable: '物联网', forbidstate: false },
						{ value: '特高压', lable: '特高压', forbidstate: false },
						{ value: '工业大麻', lable: '工业大麻', forbidstate: false },
						{ value: '中药', lable: '中药', forbidstate: false },
						{ value: '氟化工', lable: '氟化工', forbidstate: false },
						{ value: 'QFII重仓', lable: 'QFII重仓', forbidstate: false },
						{ value: '化工原料', lable: '化工原料', forbidstate: false },
						{ value: '量子通信', lable: '量子通信', forbidstate: false },
						{ value: '独角兽', lable: '独角兽', forbidstate: false },
						{ value: '数据中心', lable: '数据中心', forbidstate: false },
						{ value: 'HIT电池', lable: 'HIT电池', forbidstate: false },
						{ value: '华为概念', lable: '华为概念', forbidstate: false },
						{ value: '体育产业', lable: '体育产业', forbidstate: false },
						{ value: '生态农业', lable: '生态农业', forbidstate: false },
						{ value: '数字中国', lable: '数字中国', forbidstate: false },
						{ value: '中芯概念', lable: '中芯概念', forbidstate: false },
						{ value: '海洋经济', lable: '海洋经济', forbidstate: false },
						{ value: '独家药品', lable: '独家药品', forbidstate: false },
						{ value: '鸡肉概念', lable: '鸡肉概念', forbidstate: false },
						{ value: '电子竞技', lable: '电子竞技', forbidstate: false },
						{ value: '军工', lable: '军工', forbidstate: false },
						{ value: '人造肉', lable: '人造肉', forbidstate: false },
						{ value: '单抗概念', lable: '单抗概念', forbidstate: false },
						{ value: 'MSCI中国', lable: 'MSCI中国', forbidstate: false },
						{ value: '壳资源', lable: '壳资源', forbidstate: false },
						{ value: '大数据', lable: '大数据', forbidstate: false },
						{ value: '油价相关', lable: '油价相关', forbidstate: false },
						{ value: '股权激励', lable: '股权激励', forbidstate: false },
						{ value: '油改概念', lable: '油改概念', forbidstate: false },
						{ value: '上海自贸', lable: '上海自贸', forbidstate: false },
						{ value: '富士康', lable: '富士康', forbidstate: false },
						{ value: '美团概念', lable: '美团概念', forbidstate: false }
					]
				]
			},
			option: [
				{ value: 'now', label: '近期表现', children: [] }
				// {
				// 	value: 'from',
				// 	label: '从那时起',
				// 	children: [
				// 		{
				// 			value: '2015-08-26',
				// 			label: '2015-08-26'
				// 		}
				// 	]
				// }
			],
			selectoption: ''
		};
	},
	//监听属性 类似于data概念
	computed: {
		address() {
			if (this.FUNC.isEmpty(this.dataIndustry)) {
				// this.option = this.optionsselect;
				this.dataIndustry2 = {
					alpha: [
						{
							'申万(2021)': this.dataIndustry.industry_tree.map((v) => {
								return { value: v.industry_code, label: v.industry_name, forbidstate: false };
							})
						},
						{
							'申万二级(2021)': []
						},
						{
							'申万三级(2021)': []
						},
						{
							恒生一级: [],
							stockclass: []
						},
						[
							...this.dataIndustry.stockclass.map((v) => {
								return { value: v, lable: v, forbidstate: false };
							})
						]
					]
				};
			}
		}
	},

	//监控data中的数据变化
	watch: {
		dataX(val) {
			if (val.dataResult && val.dataResult.length > 0) {
				this.showBox = true;
				this.iconFlag = val.dataResult[0].flag;
				this.input = val.dataResult[0].value;
				this.selectoption = val.dataResult[0].date;
				this.option = val.dataResult[0].option;
				this.operator = val?.dataResult?.[0]?.operation?.mathRange || 'rank(0-100)';
				// this.getDate();
			}
		}
	},
	//方法集合
	methods: {
		resolveMathRange(obj) {
			this.mathRange = obj;
			this.resolveFather();
		},
		resolveFather() {
			this.$emit(
				'boxOnlyYSFNameChange',
				this.baseIndexFlag,
				this.indexFlag,
				this.input,
				this.iconFlag,
				this.selectoption,
				this.dataIndustry,
				this.FUNC.isEmpty(this.input) && this.FUNC.isEmpty(this.selectoption),
				this.mathRange
			);
		},
		change() {
			this.$emit(
				'boxOnlyYSFNameChange',
				this.baseIndexFlag,
				this.indexFlag,
				this.input,
				this.iconFlag,
				this.selectoption,
				this.dataIndustry,
				this.FUNC.isEmpty(this.input) && this.FUNC.isEmpty(this.selectoption),
				this.mathRange
			);
		},

		inputChange() {
			this.$emit(
				'boxOnlyYSFNameChange',
				this.baseIndexFlag,
				this.indexFlag,
				this.input,
				this.iconFlag,
				this.selectoption,
				this.dataIndustry,
				this.FUNC.isEmpty(this.input) && this.FUNC.isEmpty(this.selectoption),
				this.mathRange
			);
		}
	},
	//生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	//生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		// this.getDate();
		if (JSON.stringify(this.dataX) != '{}') {
			if (this.dataX.dataResult && this.dataX.dataResult.length > 0) {
				this.showBox = true;
				this.iconFlag = this.dataX.dataResult[0].flag;
				this.input = this.dataX.dataResult[0].value;
				this.selectoption = this.dataX.dataResult[0].date;
				this.option = this.dataX.dataResult[0].option;
				this.operator = this.dataX?.dataResult[0]?.operation?.mathRange || 'rank(0-100)';
			}
		}
	},
	beforeCreate() {}, //生命周期 - 创建之前
	beforeMount() {}, //生命周期 - 挂载之前
	beforeUpdate() {}, //生命周期 - 更新之前
	updated() {}, //生命周期 - 更新之后
	beforeDestroy() {}, //生命周期 - 销毁之前
	destroyed() {}, //生命周期 - 销毁完成
	activated() {} //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
.boxOnlyYSF {
	display: flex;
	align-items: center;
}
</style>
