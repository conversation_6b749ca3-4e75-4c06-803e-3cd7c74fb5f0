<template>
	<div class="overview-chart" v-loading="chartLoading">
		<div class="invest-tab">
			<div
				v-for="(type, i) in typeList"
				:key="i"
				class="tab-btn"
				:class="{ 'active-invest': investType == type.type }"
				@click="changeInvestType(type.type)"
			>
				{{ type.name }}
			</div>
		</div>
		<div class="invest-chart">
			<div v-show="showChart" id="main" class="main"></div>
			<div v-show="!showChart" class="no-data main">
				<el-empty description="暂无数据"></el-empty>
			</div>
		</div>
	</div>
</template>

<script>
import { fontSize } from '../../../assets/js/echartsrpxtorem'; //注意路径
import axios from '../../../api/index';
export default {
	data() {
		return {
			// 权益-equity; 债券-bond; 货币-cash;
			investType: 'equity',
			chartLoading: false,
			showChart: true,
			colorCard: {
				bond_ret: '#4096FF',
				avg_ret: '#929694',
				index_flag1: '#40BFDD',
				index_flag2: '#C2B12F'
			},
			chartColor: [],
			typeList: []
		};
	},
	props: {
		code: {
			require: true
		},
		name: {
			require: true,
			type: String
		}
	},
	created() {
		this.getTypeList();
	},
	mounted() {
		this.generateChart();
	},
	methods: {
		// 获取数据类型
		getTypeList() {
			axios.get(this.$baseUrl + '/Company/HoldType/?code=' + this.code).then((res) => {
				if (res.status == 200) {
					this.typeList = res.data.data
						.filter((item) => {
							return (
								item.type == 'equity' || item.type == 'equityhk' || item.type == 'bond' || item.type == 'purebond' || item.type == 'money'
							);
						})
						.map((item) => {
							let sort = null;
							switch (item.type) {
								case 'equity':
									sort = 0;
									break;
								case 'equityhk':
									sort = 1;
									break;
								case 'bond':
									sort = 2;
									break;
								case 'purebond':
									sort = 4;
									break;
								case 'money':
									sort = 6;
									break;
							}
							return { type: item.type, name: this.FUNC.textConverter(this.COMMON.fundType_zh_en, item.type, 'en', 'zh'), sort };
						})
						.sort((a, b) => {
							return a.sort - b.sort;
						});
				}
			});
		},
		// 选择数据类型
		changeInvestType(type) {
			if (!type) return;
			this.investType = type;
			this.generateChart();
		},
		generateChart() {
			this.chartLoading = true;
			let params = {
				code: this.code,
				type: this.investType
			};
			let url = this.$baseUrl + '/Company/ReturnWithAsset/?' + this.FUNC.paramsToString(params);
			axios
				.get(url)
				.then((res) => {
					if (res.status == 200) {
						let resReturn = res.data.data.cum_return[0]; // cum_return 累计收益 每日
						let resNetasset = res.data.data.netasset[0]; // netasset 规模 季度

						let dateDayList = []; // 每日日期
						let dateQuarterList = []; // 季度日期
						let returnList = []; // 每日值-累计收益
						let netassetList = []; // 季度值-规模

						// 最早&最晚 季度&每日
						let earlyQ =
							resReturn && resNetasset
								? resReturn.yearqtr[0] < resNetasset.yearqtr[0]
									? resReturn.yearqtr[0]
									: resNetasset.yearqtr[0]
								: resReturn
								? resReturn.yearqtr[0]
								: resNetasset.yearqtr[0];
						let lastQ =
							resReturn && resNetasset
								? resReturn.yearqtr[resReturn.yearqtr.length - 1] > resNetasset.yearqtr[resNetasset.yearqtr.length - 1]
									? resReturn.yearqtr[resReturn.yearqtr.length - 1]
									: resNetasset.yearqtr[resNetasset.yearqtr.length - 1]
								: resReturn
								? resReturn.yearqtr[resReturn.yearqtr.length - 1]
								: resNetasset.yearqtr[resNetasset.yearqtr.length - 1];
						let earlyD = this.FUNC.returnQuarter(earlyQ, 'start', earlyQ.indexOf('Q') == -1 ? 'date' : 'quarter');
						let lastD = this.FUNC.returnQuarter(lastQ, 'end', lastQ.indexOf('Q') == -1 ? 'date' : 'quarter');

						// 连续日期 季度&每日
						dateDayList = this.FUNC.generateDateList(earlyD, lastD);
						dateQuarterList = this.FUNC.dateGenerateQuarterList(earlyD, lastD);

						// 生成季度规模值 netassetList
						dateQuarterList.forEach((item) => {
							if (resNetasset) {
								if (resNetasset.yearqtr.includes(item)) {
									let index = resNetasset.yearqtr.indexOf(item);
									let value = resNetasset.netasset[index];
									netassetList.push(value / 10 ** 8); // 元换算成亿
								} else {
									netassetList.push(null);
								}
							}
						});

						// 生成每日累计收益值 cum_return
						let date = resReturn ? resReturn.date.slice() : [];
						let cum_return = resReturn ? resReturn.cum_return.slice() : [];
						let preRet = null;
						let curDate = date.shift();
						let curRet = cum_return.shift();

						dateDayList.forEach((item) => {
							if (resReturn) {
								if (item == curDate) {
									returnList.push((curRet * 100).toFixed(2));
									preRet = curRet;
									curDate = date.shift();
									curRet = cum_return.shift();
								} else if (item < curDate) {
									returnList.push((preRet * 100).toFixed(2));
								} else if (item > resReturn.date[resReturn.date.length - 1]) {
									returnList.push(null);
								} else {
									returnList.push((curRet * 100).toFixed(2));
									preRet = curRet;
									curDate = date.shift();
									curRet = cum_return.shift();
								}
							}
						});

						let myChart = echarts.init(document.getElementById('main'));
						let option = {
							color: ['#409eff', '#4096ff'],
							tooltip: {
								textStyle: {
									fontSize: fontSize(14)
								},
								trigger: 'axis',
								formatter: (params) => {
									let str = `日期: ${params[1] ? params[1].axisValue : ''} 季度: ${params[0] ? params[0].axisValue : ''} <br />`;
									for (let i = params.length - 1; i >= 0; i--) {
										if (params[i].value != undefined && params[i].value != null && params[i].value != '') {
											let dotHtml =
												'<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' +
												params[i].color +
												'"></span>';
											str += dotHtml + `${params[i].seriesName}: `;
											let value;
											if (params[i].seriesName == '规模') {
												str += `${parseFloat(params[i].value.toFixed(2)) + '亿'}<br />`;
											} else {
												str += `${parseFloat(params[i].value).toFixed(2) + '%'}<br />`;
											}
										}
									}
									return str;
								}
							},
							legend: {
								textStyle: {
									fontSize: fontSize(14)
								}
								// data: ['累计收益', '规模']
							},
							xAxis: [
								{
									data: dateQuarterList,
									silent: false,
									splitLine: {
										show: false
									},
									splitArea: {
										show: false
									},
									axisLabel: {
										fontSize: fontSize(14)
									}
								},
								{
									//每日
									nameTextStyle: {
										fontSize: fontSize(14)
									},
									axisTick: {
										show: false
									},
									axisLabel: {
										show: false,
										textStyle: {
											fontSize: fontSize(10)
										}
									},
									data: dateDayList,
									type: 'category'
								}
							],
							yAxis: [
								{
									// 该项在相应季度的数值
									nameTextStyle: {
										fontSize: fontSize(14)
									},
									axisLabel: {
										show: true,
										textStyle: {
											fontSize: fontSize(14)
										}
									},
									name: '管理规模(亿)',
									type: 'value',
									// min: 0
									scale: true,
									splitLine: {
										show: true,
										lineStyle: {
											type: 'dashed'
										}
									}
								},
								{
									//该项在相应每日的数值
									nameTextStyle: {
										fontSize: fontSize(14)
									},
									axisLabel: {
										show: true,
										textStyle: {
											fontSize: fontSize(14)
										}
									},
									name: '累计收益(%)',
									type: 'value',
									scale: true,
									splitLine: {
										show: false
									}
								}
							],
							series: [
								{
									name: '规模',
									type: 'bar',
									symbol: 'none',
									// barCategoryGap: '0%',
									barWidth: '60%',
									data: netassetList,
									yAxisIndex: 0,
									xAxisIndex: 0
								},
								{
									name: '累计收益',
									type: 'line',
									symbol: 'none',
									data: returnList,
									yAxisIndex: 1,
									xAxisIndex: 1
								}
							]
						};
						myChart.setOption(option, true);
						this.showChart = true;
					} else {
						console.error('error: ', res);
						this.showChart = false;
					}
					this.chartLoading = false;
				})
				.catch((error) => {
					this.chartLoading = false;
					this.showChart = false;
					console.error('error: ', error);
				});
		},
		generateDynamicChart() {
			let mockDate = this.FUNC.generateDateList('2021-06-01', '2021-07-01');
			let myChart = echarts.init(document.getElementById('main'));
			let seriesList = [];

			if (this.showFlagList.fund_ret) {
				seriesList.push({
					name: this.name,
					type: 'line',
					symbol: 'none',
					data: this.FUNC.mockDataList(30, 10)
				});
				this.chartColor.push(this.colorCard.fund_ret);
			}

			if (this.showFlagList.avg_ret) {
				seriesList.push({
					name: '同类平均',
					type: 'line',
					symbol: 'none',
					data: this.FUNC.mockDataList(30, 10)
				});
				this.chartColor.push(this.colorCard.avg_ret);
			}

			if (this.showFlagList.index_flag1) {
				seriesList.push({
					name: this.index_flag1,
					type: 'line',
					symbol: 'none',
					data: this.FUNC.mockDataList(30, 10)
				});
				this.chartColor.push(this.colorCard.index_flag1);
			}

			if (this.showFlagList.index_flag2) {
				seriesList.push({
					name: this.index_flag2,
					type: 'line',
					symbol: 'none',
					data: this.FUNC.mockDataList(30, 10)
				});
				this.chartColor.push(this.colorCard.index_flag2);
			}

			let option = {
				color: this.chartColor,
				tooltip: {
					trigger: 'axis'
				},
				grid: {
					left: '20',
					equity: '0',
					bottom: '0',
					top: '20',
					with: fontSize(800),
					containLabel: true
				},
				xAxis: {
					type: 'category',
					boundaryGap: false,
					data: mockDate
				},
				yAxis: {
					axisLine: { show: false },
					axisTick: { show: false },
					splitLine: {
						lineStyle: {
							type: 'dashed'
						}
					},
					type: 'value'
				},
				series: seriesList
			};

			myChart.setOption(option, true);
		}
	}
};
</script>

<style lang="scss" scoped>
.overview-chart {
	.invest-tab {
		display: flex;
		.tab-btn {
			width: 100px;
			height: 50px;
			text-align: center;
			line-height: 50px;
			font-size: 16px;
			color: #333333;
			background: #fafafa;
			cursor: pointer;
		}
		.active-invest {
			font-weight: bold;
			background: #fff;
			border-bottom: 3px solid #40AFFF;
		}
	}
	.invest-chart {
		border: 1px solid #e9e9e9;
		border-radius: 5px;
		margin-top: 15px;
		min-height: 270px;
	}
}
.main {
	height: 300px !important;
}
</style>
