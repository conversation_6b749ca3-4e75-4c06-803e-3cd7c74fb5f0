import request from '@/utils/request';

/**
 * 分管理人-业绩看板
 * @param params
 * @returns {*}
 */
export function getSpectacularsManager(params) {
    return request({
        url: '/api/taikang/board/spectaculars/manager',
        // url:'https://mock.apifox.com/m2/3691500-0-default/*********',
        method: 'get',
        params
    });
}
/**
 * 分账户-业绩看板
 * @param params
 * @returns {*}
 */
export function getPerformanceBoardAccount(params) {
    return request({
        url: '/api/taikang/board/performanceBoard/account',
        // url:'https://mock.apifox.com/m2/3691500-0-default/*********',
        method: 'get',
        params
    });
}
/**
 * 直投MOM-业绩看板
 * @param params
 * @returns {*}
 */
export function getPerformanceBoardMOM(params) {
    return request({
        url: '/api/taikang/board/performanceBoard/mom',
        // url:'https://mock.apifox.com/m2/3691500-0-default/*********',
        method: 'get',
        params
    });
}
/**
 * 直投FOF-业绩看板
 * @param params
 * @returns {*}
 */
export function getPerformanceBoardFOF(params) {
    return request({
        url: '/api/taikang/board/performanceBoard/fof',
        // url:'https://mock.apifox.com/m2/3691500-0-default/*********',
        method: 'get',
        params
    });
}

/**
 * 投连-业绩看板
 * @param params
 * @returns {*}
 */
export function getPerformanceBoardLinked(params) {
    return request({
        url: '/api/taikang/board/performanceBoard/linked',
        // url:'https://mock.apifox.com/m2/3691500-0-default/*********',
        method: 'get',
        params
    });
}
/**
 * 分管理人-结构看板
 * @param params
 * @returns {*}
 */
export function getPerformanceBoardManagerAccount(params) {
    return request({
        url: '/api/taikang/board/structuralSignage/managerAccount',
        // url:'https://mock.apifox.com/m2/3691500-0-default/*********',
        method: 'get',
        params
    });
}
/**
 * 分资产结构-结构看板
 * @param params
 * @returns {*}
 */
export function getPerformanceBoardManagerAccount2(params) {
    return request({
        url: '/api/taikang/board/structuralSignage/assetsType',
        // url:'https://mock.apifox.com/m2/3691500-0-default/*********',
        method: 'get',
        params
    });
}
/**
 * 分管理人分资产-结构看板
 * @param params
 * @returns {*}
 */
export function getPerformanceBoardManagerAssets(params) {
    return request({
        url: '/api/taikang/board/structuralSignage/managerAssets',
        // url:'https://mock.apifox.com/m2/3691500-0-default/*********',
        method: 'get',
        params
    });
}
/**
 * 配置查询
 * @param params
 * @returns {*}
 */
export function getDataBoardQueryConfig(params) {
    return request({
        url: '/api/taikang/board/dataBoard/queryConfig',
        // url:'https://mock.apifox.com/m2/3691500-0-default/*********',
        method: 'get',
        params
    });
}

export function getPivotTableData(data) {
    return request({
        url: '/api/taikang/board/dataBoard/dataPivotTable',
        // url:'https://mock.apifox.com/m2/3691500-0-default/134788772',
        method: 'post',
        data
    });
}
/**
 * 获取已有投后数据v3
 * @param params
 * @returns {*}
 */
export function getImportYearList(params) {
  return request({
      url: '/api/taikang/board/dataBoard/getImportYearList',
      method: 'get',
      params
  });
}
/**
 * 获取已有数据MSCI
 * @param params
 * @returns {*}
 */
export function getImportMsciLastDate(params) {
  return request({
      url: '/api/taikang/board/dataBoard/getImportMsciLastDate',
      method: 'get',
      params
  });
}
/**
 * 导入投后数据v3
 * @param params
 * @returns {*}
 */
export function importBaseData(data) {
  return request({
      url: '/api/taikang/board/dataBoard/importBaseData',
      method: 'post',
      data
  });
}
