<template>
	<el-card v-loading="loadingzichanpeizhi">
		<div slot="header" style="display: flex" class="clearfix">
			<div class="points"></div>
			<span>大类别占净值比</span>
		</div>
		<el-scrollbar :style="`height:${getFontSize(600)}`">
			<el-table :height="getFontSize(500)" :data="newhold" class="table" ref="multipleTable" header-cell-class-name="table-header">
				<el-table-column sortable prop="yearqtr" label="日期" :width="returnf" align="gotoleft"> </el-table-column>
				<el-table-column sortable prop="currency" label="货币" align="center" :width="getfontSize(100)">
					<template slot-scope="scope">{{ scope.row.currency | fix3 }}</template>
				</el-table-column>
				<el-table-column sortable prop="returnofcurrency" align="center" label="货币回报" :width="getfontSize(130)">
					<template slot-scope="scope">{{ scope.row.returnofcurrency | fix3 }}</template>
				</el-table-column>
				<el-table-column sortable prop="interest" align="center" label=" 利率" :width="getfontSize(100)">
					<template slot-scope="scope">{{ scope.row.interest | fix3 }}</template>
				</el-table-column>
				<el-table-column sortable prop="returnofinterest" align="center" label=" 利率回报" :width="getfontSize(130)">
					<template slot-scope="scope">{{ scope.row.returnofinterest | fix3 }}</template>
				</el-table-column>
				<el-table-column sortable prop="equity" align="center" label="权益" :width="getfontSize(100)">
					<template slot-scope="scope">{{ scope.row.equity | fix3 }}</template>
				</el-table-column>
				<el-table-column sortable prop="returnofequity" align="center" label="权益回报" :width="getfontSize(130)">
					<template slot-scope="scope">{{ scope.row.returnofequity | fix3 }}</template>
				</el-table-column>
				<el-table-column sortable prop="credit" align="center" label="信用" :width="getfontSize(100)">
					<template slot-scope="scope">{{ scope.row.credit | fix3 }}</template>
				</el-table-column>
				<el-table-column sortable prop="returnofcredit" align="center" label="信用回报" :width="getfontSize(130)">
					<template slot-scope="scope">{{ scope.row.returnofcredit | fix3 }}</template>
				</el-table-column>
			</el-table>
			<!-- <div>在信用、利率、权益和货币 4 个大类别中，国富强化收益债券 A 平均投资占比最大的是信用，平均占 比 62.99%，最高占比 121.82%，最低占比 29.69%，属于稳定的中配; 对于固收 + 类型的产品，权益平 均暴露 16.85%, 最高暴露 43.68%(报告于 2012 Q2)，最低暴露 0.00%(报告于 2008 Q4)，属于有节制 择机的高配。</div> -->
		</el-scrollbar>
	</el-card>
</template>

<script>
// 大类别占净值比

export default {
	name: 'largeCategoryNetWorth',
	data() {
		return {
			newhold: []
		};
	},
	filters: {
		fix3(value) {
			return parseInt(value * 1000) / 1000;
		}
	},
	methods: {
		getData() {
			let tradeUrl = '';
			let indexUrl = '';
			let indexParams = {
				code: this.code,
				index_code: this.indexType
			};
			if (this.isHuoBi) {
				indexUrl = this.$baseUrl + '/index_return/?' + this.FUNC.paramsToString(indexParams);
				tradeUrl = this.$baseUrl + '/allocation/?' + this.FUNC.paramsToString({ code: this.code });
				dateKey = 'date';
			} else if (this.fundType == 'fundCompany') {
				indexUrl = this.$baseUrl + '/Company/IndexReturn/?' + this.FUNC.paramsToString(indexParams);
				tradeUrl = this.$baseUrl + '/Company/BondTypeAnalysis/?' + this.FUNC.paramsToString({ code: this.code });
				dateKey = 'yearqtr';
				lineKey = 'date';
			} else {
				indexUrl = this.$baseUrl + '/index_return/?' + this.FUNC.paramsToString(indexParams);
				tradeUrl = this.$baseUrl + '/allocationasset/?' + this.FUNC.paramsToString({ code: this.code, name: this.name });
				dateKey = 'time';
			}
			var that = this;
			this.$axios.all([axios.get(tradeUrl), axios.get(indexUrl)]).then(([tradeRes, indexRes]) => {
				if (this.originType == 'fundCompany') {
					tradeRes.data = tradeRes.data.data;
				}
				let emitData = that.isHuoBi ? tradeRes.data.asset_weight : tradeRes.data.asset_ratioinN;
				that.receiveNewHold(emitData);
			});
		},
		receiveNewHold(data) {
			this.newhold = data;
			this.loadingzichanpeizhi = false;
		}
	}
};
</script>

<style></style>
