<template>
  <div class="industryCapability">
    <industry-capability-circle name="行业能力圈"
                                v-loading="loading"
                                ref="industryCapabilityCircle"
                                @resolveFather="changeRank"
                                :data_type="data_type"></industry-capability-circle>
    <capability-statistics v-if="data_type == 'fund'"
                           name="行业能力统计"
                           ref="capabilityStatistics"></capability-statistics>
  </div>
</template>

<script>
// 行业能力圈
import industryCapabilityCircle from './industryCapabilityCircle.vue';
// 行业能力统计
import capabilityStatistics from './capabilityStatistics.vue';

import { getIndustryRankInfo } from '@/api/pages/tools/pool.js';
export default {
  components: { industryCapabilityCircle, capabilityStatistics },
  props: {
    data_type: {
      type: String,
      default: 'fund'
    },
  },
  data () {
    return {
      ismanager: '',
      info: {},
      loading: true,
      industry_list: [],
      industry_circle: []
    };
  },
  methods: {
    getData (info) {
      this.info = info;
      this.getIndustryRankInfo();
    },
    refresInfo (info) {
      this.info = info;
      this.formatIndustryCircle(this.industry_circle);
      this.formatIndustryAbility(this.industry_circle);
    },
    // 修改计算分位
    async changeRank (rank) {
      this.loading = true;
      let ids = [{ code: this.info.code, type: 'pool' }];
      if (this.data_type == 'pool') {
        ids = [
          ...ids,
          ...this.info['code_list'].map((item) => {
            return { code: item.code, type: this.data_type == 'fund' ? this.ismanager ? 'manager' : 'fund' : this.data_type };
          })
        ];
      }
      let data = await getIndustryRankInfo({
        ids,
        yearqtr: this.info.quarter,
        insert_time: this.info.date,
        percent_rank: rank,
        industry_standard: '申万(2021)',
        method: '分位',
        flag: 5,
        ismanager: this.ismanager,
        type: this.info.type || 'equity',
      });
      if (data?.mtycode == 200) {
        this.formatIndustryCircle(data?.data, 'pool');
      }
      this.loading = false;
    },
    // 获取行业能力
    async getIndustryRankInfo () {
      this.loading = true;
      let data = await getIndustryRankInfo({
        ids: [
          { code: this.info.code, type: 'pool' },
          ...this.info['code_list'].map((item) => {
            return { code: item.code, type: this.data_type == 'fund' ? this.ismanager ? 'manager' : 'fund' : this.data_type };
          })
        ],
        yearqtr: this.info.quarter,
        insert_time: this.info.date,
        percent_rank: 0.66,
        method: '分位',
        industry_standard: '申万(2021)',
        flag: 5,
        ismanager: this.ismanager,
        type: this.info.type || 'equity',

      });
      if (data?.mtycode == 200) {
        this.industry_circle = data?.data;
        this.formatIndustryCircle(data?.data);
        this.formatIndustryAbility(data?.data);
      } else {
        try {
          this.formatIndustryCircle([]);
        } catch { }
        try {
          this.formatIndustryAbility([]);
        } catch { }
      }

      this.loading = false;
    },
    // 行业能力圈
    formatIndustryCircle (data, flag) {
      let capability = [];
      data.map((item) => {
        let index = capability.findIndex((v) => v.code === item.poolId);
        if (index == -1) {

          // let name, color, type, flag;
          // if (this.data_type == 'fund') {
          // 	name = item.flag == 'pool' ? this.info.name : this.info['code_list'].find((v) => v.code == item.fund_pool_id)?.name;
          // 	color = item.flag == 'pool' ? '#4096ff' : this.info['code_list'].find((v) => v.code == item.fund_pool_id)?.color;
          // 	type = item.flag == 'pool' ? 0 : this.info['code_list'].find((v) => v.code == item.fund_pool_id)?.flag;
          // } else {
          let name =
            this.info.code == item.poolId ? this.info.name : this.info['code_list'].find((v) => v.code == item.poolId)?.name;
          let color = this.info.code == item.poolId ? '#4096ff' : this.info['code_list'].find((v) => v.code == item.poolId)?.color;
          let type = this.info.code == item.poolId ? 0 : this.info['code_list'].find((v) => v.code == item.poolId)?.flag;
          let flag = this.info.code == item.poolId ? 'pool' : 'fund';
          // }

          capability.push({
            code: item.poolId,
            flag,
            name,
            color,
            type,
            children: [
              {
                industry_code: item.asset_class,
                asset_class: item.asset_class,
                industry_rank: item.industry_rank
              }
            ]
          });
        } else {
          capability[index].children.push({
            industry_code: item.asset_class,
            industry_name: item.asset_class,
            industry_rank: item.industry_rank
          });
        }
      });
      console.log(capability);
      // console.log(capability, 'sss');
      if (flag == 'pool') {
        if (this.data_type == 'fund') {
          this.$refs['industryCapabilityCircle'].getPoolData(capability, this.info);
        } else {
          this.$refs['industryCapabilityCircle'].getData(capability, this.info);
        }
      } else {
        this.$refs['industryCapabilityCircle'].getData(capability, this.info);
      }
    },
    // 行业能力统计
    formatIndustryAbility (data) {
      let industry_list = [];
      data
        .filter((v) => v.flag == 'fund' || v.flag == "manager")
        .map((item) => {
          let industry_index = industry_list.findIndex((v) => v.industry_code == item.asset_class);
          let name = item.flag == 'pool' ? this.info.name : this.info['code_list'].find((v) => v.code == item.poolId)?.name;
          let color = item.flag == 'pool' ? '#4096ff' : this.info['code_list'].find((v) => v.code == item.poolId)?.color;
          let flag = item.flag == 'pool' ? 0 : this.info['code_list'].find((v) => v.code == item.poolId)?.flag;
          if (industry_index == -1) {
            industry_list.push({
              industry_code: item.asset_class,
              industry_name: item.asset_class,
              children: [
                {
                  code: item.poolId,
                  name,
                  color,
                  rank: item.industry_rank,
                  flag
                }
              ]
            });
          } else {
            industry_list[industry_index].children.push({
              code: item.poolId,
              name,
              color,
              rank: item.industry_rank,
              flag
            });
          }
        });
      this.$refs['capabilityStatistics']?.getData(industry_list, this.info);
    }
  },
  mounted () {
    this.ismanager = String(this.$route.query.ismanager) == 'true' ? true : false
  },
};
</script>
<style lang="scss" scoped></style>
