<!--  -->
<template>
  <div>
    <div class="flex item-center justify-between">
      <div class="area-title">整体财务指标(FOF)</div>
      <div class="border_table_header_search">
        <span class="selector">持仓类型：</span>
        <el-select v-model="wholeData.type"
                   class="search-security"
                   placeholder="请选择"
                   @change="getWholeIndexData('')"
                   style="width: 240px">
          <el-option v-for="item in position"
                     :key="item.value"
                     :label="item.label"
                     :value="item.value"
                     style="width: 240px"> </el-option>
        </el-select>
        <span class="selector">考察指标：</span>
        <el-select v-model="wholeData.inspect"
                   class="search-security"
                   placeholder="请选择"
                   @change="getWholeChart"
                   style="width: 240px">
          <el-option v-for="item in index"
                     :key="item.value"
                     :label="item.label"
                     :value="item.value"
                     style="width: 240px"> </el-option>
        </el-select>
        <img alt=""
             src="../../../../../assets/img/download.png"
             class="download"
             @click="downloadExcel('整体财务指标')" />
      </div>
    </div>
    <div class="area-body"
         v-loading="wholeData.loading">
      <v-chart autoresize
               ref="ChartComponent_ztcwzb2"
               v-if="!show.financeEmptyFOF"
               element-loading-text="暂无数据"
               element-loading-spinner="el-icon-document-delete"
               element-loading-background="rgba(239, 239, 239, 0.5)"
               style="height: 340px; width: 100% !important"
               :options="wholeData.options" />
      <el-empty v-else
                :image-size="200"></el-empty>
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import {
  getObjectStockFinance,
} from '@/api/pages/analysis/report';
import { lineChartOption } from '@/utils/chartStyle';
import VChart from 'vue-echarts';
import { filter_json_to_excel_inside, changColumnToRow, filter_json_to_excel_inside_multiHeader } from '@/utils/exportExcel.js';
import { export_json_to_excel_multiHeader } from '@/vendor/Export2Excel.js';
export default {
  //import引入的组件需要注入到对象中才能使用
  components: { VChart },
  data () {
    //这里存放数据
    return {
      index: [
        {
          value: 'pe',
          label: 'PE'
        },
        {
          value: 'pb',
          label: 'PB'
        },
        {
          value: 'roe',
          label: 'ROE'
        },
        {
          value: 'income_yoy',
          label: '营业收入同比增长'
        },
        {
          value: 'dividendratiolyr',
          label: '股息率'
        },
        {
          value: 'net_income_yoy',
          label: '净利润增长率'
        }
      ],
      wholeData: {
        list: [],
        options: {},
        type: 'all',
        inspect: 'pe',
        time: '',
        loading: false
      },
      position: [
        {
          value: 'all',
          label: '全持仓'
        },
        {
          value: 'big',
          label: '重仓股'
        }
      ],
      show: {
        financeEmptyFOF: false
      }
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    getBetweenDates (startDate, endDate) {
      let dates = [];
      let currentDate = new Date(startDate);

      while (currentDate <= endDate) {
        dates.push(currentDate.toISOString().slice(0, 10));
        currentDate.setDate(currentDate.getDate() + 1);
      }

      return dates;
    },
    /**
     * 获取数据补充
     */
    supplementData (arr) {
      // console.log(arr);
      // let minDate = this.moment(this.$route.query.startDate).format('YYYY-MM-DD');
      // let maxDate = this.moment(this.$route.query.endDate).format('YYYY-MM-DD');
      // let dateArr = this.getBetweenDates(new Date(minDate), new Date(maxDate))?.filter(item => {
      //   if (item.indexOf('03-31') >= 0 || item.indexOf('06-30') >= 0 || item.indexOf('09-30') >= 0 || item.indexOf('12-31') >= 0)
      //     return true //
      //   else return false
      // }
      // );
      // let dataArr = [];
      // dateArr.forEach((item, index) => {
      //   console.log(item);
      //   dataArr.push({
      //     data: {
      //       date: item
      //     }
      //   });
      //   arr.forEach((citem) => {
      //     if (item === citem.data.date) {
      //       dataArr[index].data = {
      //         ...citem.data
      //       };
      //     }
      //   });
      // });
      // console.log(dataArr);
      // dataArr.forEach((item, index) => {
      //   if (!item.data.pb) {
      //     item.data = {
      //       ...dataArr[index - 1].data,
      //       date: item.data.date
      //     };
      //   }
      // });
      return arr;
    },
    exportMSG () {
      return {
        type: this.wholeData.type,
        inspect: this.wholeData.inspect,
      }
    },
    /**
        * 获取整体财务指标
        */
    getWholeIndexData (timeArr) {
      this.wholeData.loading = true;
      let data = {
        reportID: Number(this.$route.query.id),
        factors: [0],
        holdType: this.wholeData.type,
        startFrom: Number(this.moment(this.$route.query.startDate).format('YYYYMMDD')),
        endTo: Number(this.moment(this.$route.query.endDate).format('YYYYMMDD')),
        industryStandard: 3,
        reportTemplate: 'FOF'
      };
      if (timeArr) {
        data.startFrom = timeArr[0];
        data.endTo = timeArr[1];
      }
      getObjectStockFinance(data).then((res) => {
        this.wholeData.loading = false;
        if (res.code === 200) {
          this.show.financeEmptyFOF = false;
          this.wholeData.list = this.supplementData(res.data.rows);
          this.getWholeChart();
        } else {
          this.show.financeEmptyFOF = true;
        }
      });
    },
    /**
   * 生成整体指标Chart
   */
    getWholeChart () {
      let that = this
      let allArr = [];
      this.wholeData.list.forEach((item, index) => {
        if (item.data[`index_${this.wholeData.inspect}`] !== '--') {
          allArr.push(item.data[`index_${this.wholeData.inspect}`]);
        } else {
          if (index > 0) {
            item.data[`index_${this.wholeData.inspect}`] = this.wholeData.list[index - 1].data[`index_${this.wholeData.inspect}`];
            allArr.push(item.data[`index_${this.wholeData.inspect}`]);
          }
        }
      });
      const line = [
        {
          name: '分析对象',
          type: 'line',
          symbol: 'none',
          lineStyle: {
            width: 3
          },
          data: this.wholeData.list.map((item) => item.data[this.wholeData.inspect])
        },
        {
          name: '参考基准',
          type: 'line',
          symbol: 'none',
          lineStyle: {
            width: 3
          },
          data: allArr
        }
      ];
      this.wholeData.options = lineChartOption({
        grid: { left: '24px', right: '48px', top: '24px', bottom: '36px' }, // 位置
        dataZoom: false,
        toolbox: false,
        tooltip: {
          formatter: function (obj) {
            var value = `<div style="font-size:14px;">` + obj?.[0].axisValue + `</div>`;
            for (let i = 0; i < obj.length; i++) {
              value +=
                `<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
                `<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
                obj?.[i].color +
                `;"></div>` +
                `<div style="font-family: PingFang SC;">` +
                obj?.[i].seriesName +
                '</div></div>' +
                `<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
                Number(obj?.[i].value).toFixed(2) +
                (that.wholeData.inspect == 'pb' || that.wholeData.inspect == 'pe' ? '' : '%') +
                '</div>' +
                `</div>`;
            }
            return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
          }
        },
        xAxis: [
          {
            name: '日期',
            data: this.wholeData.list.map((item) => item.data.date)
          }
        ],
        yAxis: [
          {
            type: 'value',
            scale: true,
            formatter: function (value, index) {
              // Y轴的自定义刻度值，对应上图
              return `${value}`;
            }
          }
        ],
        series: line
      });
    },
    downloadExcel (name) {
      if (name === '报告期前十大集中度') {
        const title = [
          { label: '', value: 'date' },
          { label: this.reportName, value: 'top10_concert', format: 'fix2p' }
        ];
        filter_json_to_excel_inside(title, this.topTenData, ['data'], name);
      } else if (name === '报告期双边换手率') {
        const title1 = [
          { label: '', value: 'date' },
          { label: this.reportName, value: 'turnover', format: 'fix2p' }
        ];
        filter_json_to_excel_inside(title1, this.twoSideData, ['data'], name);
      } else if (name === '持仓风格') {
        const format = ['', 'fixb0', 'fixb0', 'fixb0', 'fixb0'];
        const data = changColumnToRow(this.ccfgColumn, format);
        export_json_to_excel_multiHeader([this.ccfgTitle], null, data, name);
      } else if (name === '当前权益持仓风格（只分析股票）') {
        console.log('listTable', this.listTable);
        const title1 = [
          { label: '持仓风格', value: 'name' },
          { label: '投资比例', value: 'data', format: 'fix2b' }
        ];
        filter_json_to_excel_inside(title1, this.listTable, [], name);
      } else if (name === '整体财务指标') {
        console.log(this.wholeData.list);
        let indexName = '';
        if (this.wholeData.list && this.wholeData.list.length > 0) {
          const temp = this.wholeData.list[0];
          indexName = temp?.data?.index_name;
        }
        const type = this.wholeData.type === 'all' ? '全持仓' : '重仓股';
        const title = [
          { label: '', value: 'date' },
          { label: this.reportName + '(PE)', value: 'pe', format: 'fix2b' },
          { label: this.reportName + '(PB)', value: 'pb', format: 'fix2b' },
          { label: this.reportName + '(ROE)', value: 'roe', format: 'fix2b' },
          { label: this.reportName + '(股息率)', value: 'dividendratiolyr', format: 'fix2b' },
          { label: indexName + '(PE)', value: 'index_pe', format: 'fix2b' },
          { label: indexName + '(PB)', value: 'index_pb', format: 'fix2b' },
          { label: indexName + '(ROE)', value: 'index_roe', format: 'fix2b' }
        ];
        filter_json_to_excel_inside(title, this.wholeData.list, ['data'], name + '(' + type + ')');
      }
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () {

  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () {

  },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { }, //如果页面有keep-alive缓存功能，这个函数会触发
}
</script>
<style scoped lang="scss">
@import '../../../tkdesign';

.border_table_header_search {
	display: flex;
	justify-content: flex-end;
	position: relative;
	margin-bottom: 16px;

	.selector {
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
		line-height: 22px;
		margin: 5px 0 0 25px;
		color: rgba(0, 0, 0, 0.85);
	}

	.search-security {
		width: 250px;
	}
}

.download {
	padding-left: 25px;
	width: 57px;
	height: 32px;
}

.area-chart {
	display: flex;
	justify-content: space-between;
	flex-wrap: wrap;

	.piece {
		width: 100%;
		display: flex;

		.card {
			width: 10px;
			height: 10px;
			margin-right: 4px;
		}
	}

	.piece-list {
		width: 95%;

		.piece-row {
			display: flex;
		}

		.piece-name {
			color: rgba(0, 0, 0, 0.65);
			text-align: center;
			font-size: 12px;
			font-style: normal;
			font-weight: 400;
			line-height: 100px;
			width: 50px;
		}

		.piece-color_row {
			display: flex;
			width: calc(100% - 50px);
			margin-top: 1px;

			.piece-color {
				width: 33%;
				height: 100px;
				line-height: 40px;
				margin-right: 1px;
				text-align: center;
			}
		}
	}

	.chart-card_half {
		width: calc(50% - 8px);
		padding: 0 20px 20px;
		border: 1px solid #d9d9d9;
		border-radius: 4px;
	}

	.chart-card {
		width: 100%;
		padding: 0 20px 0px;
		border: 1px solid #d9d9d9;
		border-radius: 4px;
		margin-top: 16px;
	}

	.chart-card_title {
		display: flex;
		justify-content: space-between;
		color: rgba(0, 0, 0, 0.85);
		font-family: PingFang;
		height: 46px;
		line-height: 46px;
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
		border-bottom: 1px solid #d9d9d9;
	}

	.chart-card_header_bg {
		display: flex;
		flex-direction: row;
		align-items: center;
		border-bottom: 1px solid #d9d9d9;
		justify-content: space-between;
	}

	.chart-card_body {
		display: flex;

		.sidebar {
			width: 120px;
			padding: 20px 0px;
			gap: 40px;
			box-shadow: 19px 0px 20px 0px rgba(0, 0, 0, 0.04);
			background-color: #ffffff;
			height: 484px;

			.card {
				width: 12px;
				height: 8px;
				margin-right: 5px;
			}
		}
	}
}
</style>
