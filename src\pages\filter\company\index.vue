<template>
	<div class="analysis_main">
		<div
			style="
				font-family: 'PingFang';
				font-style: normal;
				font-weight: 500;
				font-size: 20px;
				line-height: 28px;
				color: rgba(0, 0, 0, 0.85);
				margin-left: 12px;
			"
		>
			基金公司
		</div>
		<div class="chart_one">
			<div class="title" style="margin-bottom: 20px">筛选条件</div>
			<el-form ref="elForm" :model="netasset" style="display: flex; align-item: center" size="medium" label-width="100px">
				<el-form-item label="基金公司当前规模" style="flex: 1">
					<template slot="label">
						<div style="display: flex; align-items: center">
							<div>公司规模</div>
							<el-tooltip class="item" effect="dark" content="请输入数字，如0-2，表示0亿-2亿范围" placement="right-start">
								<svg width="14" height="14" viewBox="0 0 14 14" fill="none">
									<path
										fill-rule="evenodd"
										clip-rule="evenodd"
										d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
										fill="black"
										fill-opacity="0.45"
									/>
								</svg>
							</el-tooltip>
							<span>&nbsp;:&nbsp;</span>
						</div>
					</template>
					<template>
						<div class="tiptablebox">
							<div>
								<el-input type="number" style="width: 104px !important" v-model="netasset.lbound" class="inputbox"
									><i slot="suffix"><i class="yifont">亿</i></i></el-input
								>
							</div>
							<div>&nbsp; 至 &nbsp;</div>
							<div>
								<el-input type="number" style="width: 104px !important" v-model="netasset.ubound" class="inputbox"
									><i slot="suffix"><i class="yifont">亿</i></i></el-input
								>
							</div>
						</div>
					</template>
				</el-form-item>
				<el-form-item label="基金公司成立年限" style="flex: 1">
					<template slot="label">
						<div style="display: flex; align-items: center">
							成立年限<el-tooltip class="item" effect="dark" content="请输入数字，如3，表示基金公司成立不小于3年" placement="right-start">
								<svg width="14" height="14" viewBox="0 0 14 14" fill="none">
									<path
										fill-rule="evenodd"
										clip-rule="evenodd"
										d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
										fill="black"
										fill-opacity="0.45"
									/>
								</svg>
							</el-tooltip>
							<span>&nbsp;:&nbsp;</span>
						</div>
					</template>
					<div class="tiptablebox">
						<div>
							<el-input type="number" style="width: 104px !important" v-model="netasset.time" class="inputbox"
								><i slot="suffix"><i class="yifont">年</i></i></el-input
							>
						</div>
					</div>
				</el-form-item>
				<el-form-item style="flex: 1; text-align: right">
					<div>
						<el-button type="info" @click="getAllData">重置</el-button>
						<el-button type="primary" @click="getAllData">筛选</el-button>
					</div>
				</el-form-item>
			</el-form>
		</div>
		<div class="chart_one">
			<div class="title">筛选结果</div>
			<div v-loading="loading" class="border_bottom_nones">
				<el-table
					:default-sort="{ prop: 'code' }"
					:data="tableData"
					class="table"
					height="calc(100vh - 585px)"
					ref="multipleTable"
					header-cell-class-name="table-header"
					@sort-change="sortChange"
				>
					<el-table-column prop="name" align="gotoleft" label="基金公司名称">
						<template slot-scope="{ row }">
							<a style="border-bottom: 1px solid #4096FF" @click="godetail(row.company_code, row.fund_co)">{{ row.fund_co }}</a>
						</template>
					</el-table-column>
					<el-table-column sortable prop="company_code" label="基金公司代码" align="gotoleft" width="140px"> </el-table-column>
					<el-table-column sortable prop="start_date" label="成立时间" align="gotoleft">
						<template slot-scope="{ row }">
							{{ row.start_date !== '--' ? row.start_date.slice(0, 10) : '--' }}
						</template>
					</el-table-column>
					<!-- <el-table-column sortable prop="totalasset" label="公司规模(亿元)">
							<template slot-scope="{ row }">
								{{ row.totalasset !== '--' ? (row.totalasset / 100).toFixed(2) : row.totalasset }}
							</template>
						</el-table-column> -->
					<el-table-column sortable prop="fund_num" label="基金数量" align="gotoleft"> </el-table-column>
					<el-table-column sortable prop="manager_num" label="基金经理数量" align="gotoleft"> </el-table-column>
					<el-table-column sortable prop="sum_netasset" label="管理规模(亿元)" align="gotoleft">
						<template slot-scope="{ row }">
							{{ row.sum_netasset !== '--' ? row.sum_netasset.toFixed(2) : row.sum_netasset }}
						</template>
					</el-table-column>
					<!-- <el-table-column sortable prop="score" label="慧捕基评分"> </el-table-column> -->
				</el-table>
				<div class="pagination">
					<el-pagination
						background
						layout="total, prev, pager, next"
						:current-page.sync="pageIndex"
						:page-size="pageSize"
						:total="pageTotal"
						@current-change="handlePageChange"
					></el-pagination>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import axios from '@/api/index';

export default {
	props: [],
	data() {
		return {
			pageIndex: 1,
			pageSize: 20,
			pageTotal: 0,
			allData: [],
			tableData: [],
			netasset: {
				ubound: '',
				lbound: '',
				time: ''
			},
			loading: false
		};
	},
	computed: {},
	watch: {},
	filters: {
		fix6(value) {
			return value.substring(0, 10);
		},
		fix3(value) {
			return parseInt(value * 1000) / 1000;
		},
		fix2p(value) {
			return (value * 100).toFixed(2) + '%';
		}
	},
	created() {
		this.getAllData();
	},
	mounted() {
		// FIXME:
		// this.tableData.push({
		// 	name: '国泰基金',
		// 	code: '80000221'
		// });
	},
	// watch: {
	// 	netasset: {
	// 		handler: function(val, oldVal) {
	// 			this.getAllData();
	// 		},
	// 		deep: true
	// 	}
	// },
	methods: {
		getAllData() {
			let _this = this;
			let params = {
				ubound: this.netasset.ubound,
				lbound: this.netasset.lbound,
				time: this.netasset.time
			};
			let url = this.$baseUrl + '/Company/FilterCompany/';
			this.loading = true;
			axios({
				url,
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				data: {
					data: {
						netasset: { ubound: params.ubound, lbound: params.lbound },
						time: params.time
					}
				}
			})
				.then((res) => {
					_this.pageIndex = 1;
					_this.pageTotal = res.data.data.length;
					_this.allData = res.data.data;
					_this.tableData = _this.allData.slice(0, 20);
					console.log(_this.tableData);
					_this.loading = false;
				})
				.catch((err) => {
					//console.log('error: ', err);
					_this.loading = false;
				});
		},
		// 分页
		handlePageChange() {
			this.tableData = this.allData.slice(
				(this.pageIndex - 1) * 20,
				this.pageIndex * 20 - 1 > this.allData.length ? this.allData.length : this.pageIndex * 20 - 1
			);
		},
		// 跳转
		godetail(id, name) {
			//带参进去
			this.$router.push({ path: '/fundCompany/' + id, hash: '', query: { id: id, name: name } });
		},
		// 设置table行样式
		elcellstyle({ row, column, rowIndex, columnIndex }) {
			// ////console.log(row[0])
			if (columnIndex == 2) {
				if (row['1w'] >= 0) {
					return 'color: #E85D2D;';
				} else return 'color: #18C2A0;';
			}
			if (columnIndex == 3) {
				if (row['1m'] >= 0) {
					return 'color: #E85D2D;';
				} else return 'color: #18C2A0;';
			}
			if (columnIndex == 4) {
				if (row['1q'] >= 0) {
					return 'color: #E85D2D;';
				} else return 'color: #18C2A0;';
			}
			if (columnIndex == 5) {
				if (row['1y'] >= 0) {
					return 'color: #E85D2D;';
				} else return 'color: #18C2A0;';
			}
		},
		// 点击筛选
		sortChange({ prop, order }) {
			let valueArr = [];
			let strArr = [];
			this.allData.map((obj) => {
				if (obj[prop] == '--' || obj[prop] == '——') {
					strArr.push(obj);
				} else {
					valueArr.push(obj);
				}
			});
			valueArr.sort((a, b) => {
				// 从大到小排序
				if (prop == 'start_date') {
					if (order == 'descending') {
						return new Date(b[prop]).getTime() - new Date(a[prop]).getTime();
					} else if (order == 'ascending') {
						return new Date(a[prop]).getTime() - new Date(b[prop]).getTime();
					}
				} else {
					if (order == 'descending') {
						return b[prop] - a[prop];
					} else if (order == 'ascending') {
						return a[prop] - b[prop];
					}
				}
			});
			strArr.length ? valueArr.push(...strArr) : '';
			this.allData = valueArr;
			this.handlePageChange();
		}
	}
};
</script>

<style>
.sceollbox .el-scrollbar__wrap {
	overflow-x: hidden !important;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
	-webkit-appearance: none !important;
	-moz-appearance: none !important;
	-o-appearance: none !important;
	-ms-appearance: none !important;
	appearance: none !important;
	margin: 0;
}
input[type='number'] {
	-webkit-appearance: textfield;
	-moz-appearance: textfield;
	-o-appearance: textfield;
	-ms-appearance: textfield;
	appearance: textfield;
}

.el-input__inner {
	/* border: 0;
    border-bottom: #000 solid 1px; */
}
.inputbox {
	border: 0px;
	width: 80px !important;
	outline: medium;
	text-align: center;

	padding: 0;
	-webkit-appearance: none;
	appearance: none;
	margin: 0;
}
.el-form-item {
	margin-bottom: 0;
}
/* .el-form-item__label {
	width: 200px !important;
	text-align: left;
}
.el-form-item__content {
	margin-left: 200px !important;
} */
</style>
<style lang="scss" scoped>
.row {
	margin: -10px;
	display: flex;
}

.left {
	width: 155px;
	flex: 0 0 auto;
	margin-right: 10px;
}

.right {
	position: relative;
	flex: 1 1 100px;
}
</style>
