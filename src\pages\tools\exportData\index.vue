<template>
	<div class="export-data mt-16">
		<el-menu :default-active="activeType" mode="horizontal" @select="menuSelect">
			<el-menu-item v-for="item in menuList" :key="item.key" :index="item.key">{{ item.label }}</el-menu-item>
		</el-menu>
		<div v-for="item in menuList" :key="item.key">
			<component v-show="activeType == item.key" :is="item.value" :ref="item.key" :type="activeType"></component>
		</div>
	</div>
</template>

<script>
import tk from './components/tk.vue';
import owl from './components/owl.vue';
import { getFieldList } from '@/api/pages/ApiTaikang.js';
export default {
	components: { tk, owl },
	data() {
		return {
			activeIndex: '',
			menuList: [],
			condition_list: [],
			activeType: '',
			all_condition: []
		};
	},
	mounted() {
		this.getFieldList();
	},
	methods: {
		getConditionList(item) {
			let index = this.condition_list.findIndex((v) => v.value == item.value);
			if (index == -1) {
				this.condition_list.push({ ...item });
			}
			this.$refs['keyCompute']?.getCondition(item);
		},
		// 获取字段列表
		async getFieldList() {
			let list = [];
			let data = await getFieldList({ fieldName: '' });
			if (data.code == 200) {
				data.data.map((item) => {
					let index = list.findIndex((v) => v.key == item.table);
					if (index == -1) {
						list.push({ label: item.tableName, key: item.table, value: 'tk' });
					}
				});
			}
			this.menuList = [
				...list,
				{
					label: '捕基能手接口数据',
					key: 'owl',
					value: 'owl'
				}
			];
			this.activeIndex = this.menuList[0].value;
			this.activeType = this.menuList[0].key;
			this.$nextTick(() => {
				this.$refs[this.activeType][0]?.getData(this.activeType);
			});
		},
		menuSelect(val) {
			this.activeType = val;
			if (val == 'owl') {
				this.activeIndex = 'owl';
			} else {
				this.activeIndex = 'tk';
			}
			this.$refs[this.activeType][0]?.getData(this.activeType);
		}
	}
};
</script>

<style lang="scss" scoped>
.export-data {
	height: 980px;
	background: #ffffff;
	border-radius: 4px;
	border: 1px solid #d4d8e5;
	box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.1);
	.export-condition {
		width: 240px;
		height: 919px;
		border-right: 1px solid #d4d8e5;
	}
	.export-config {
		width: 100%;
		height: 919px;
		div {
			font-size: 14px;
			color: rgba(0, 0, 0, 0.85);
		}
	}
}
</style>
