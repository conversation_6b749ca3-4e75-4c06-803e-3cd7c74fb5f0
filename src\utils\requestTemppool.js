import axios from "axios";
import store from "../store/store";
import qs from "qs";
import Vue from "vue";
import router from "@/router"; //引入router
// import axiosrequest from '@/api/index.js';
import { VueEasyJwt } from "vue-easy-jwt";
import { CheckboxGroup } from "element-ui";

// -- 中断重复请求接口
// const CancelToken = axios.CancelToken
function generateReqKey(config) {
  const { method, url, params, data } = config;
  return [method, url, qs.stringify(params), qs.stringify(data)].join("&");
}

const pendingRequest = new Map();
function addPendingRequest(config) {
  const requestKey = generateReqKey(config);
  config.cancelToken =
    config.cancelToken ||
    new axios.CancelToken((cancel) => {
      if (!pendingRequest.has(requestKey)) {
        pendingRequest.set(requestKey, cancel);
      }
    });
}

function removePendingRequest(config) {
  const requestKey = generateReqKey(config);
  if (pendingRequest.has(requestKey)) {
    const cancelToken = pendingRequest.get(requestKey);
    cancelToken(requestKey);
    pendingRequest.delete(requestKey);
  }
}
// --end

const jwt = new VueEasyJwt();
let flag = 0;
let loadingInstance;
let baseUrlx = process.env.VUE_APP_BASE_URL;
const service = axios.create({
  baseURL: 'http://192.168.199.5:8897',
  // headers: {
  // 	authorization: 'Bearer ' + store.state.token
  // }
  // timeout: 60000
});
const handleData = ({ config, data, status, statusText }) => {
  if (loadingInstance) loadingInstance.close();
  return data;
};

window._axiosPromiseArr = [];

service.interceptors.request.use(
  (config) => {
    config.cancelToken = new axios.CancelToken((cancel) => {
      window._axiosPromiseArr.push({ cancel });
    });
    // console.log(config);
    // -- 中断重复请求接口
    // removePendingRequest(config); // 检查是否存在重复请求
    // addPendingRequest(config); // 将当前请求信息添加到 pendingRequest对象中
    //
    // if (jwt.isExpired(store.state.token)) {
    // if (
    // 	config.url == '/MtyContent/userInfo/' ||
    // 	config.url == '/home/<USER>/' ||
    // 	config.url == '/home/<USER>/' ||
    // 	config.url == '/alphamsg/' ||
    // 	config.url == '/home/<USER>/' ||
    // 	config.url == '/home/<USER>/' ||
    // 	config.url == '/home/<USER>/' ||
    // 	config.url == '/home/<USER>/'
    // ) {
    // 	config.headers.authorization = '';
    // 	return config;
    // }
    // let tempconfig = axios
    // 	.post(baseUrlx + '/api-token-refresh/', {
    // 		refresh: store.state.retoken
    // 	})
    // 	.then((res) => {
    // 		if (res && res.data && res.data.access) {
    // 			store.dispatch('changetoken', res.data.access);
    // 			config.headers.authorization = 'Bearer ' + store.state.token;
    // 			return config;
    // 		} else {
    // 			// 首页的无token也过去
    // 			if (
    // 				config.url == '/MtyContent/userInfo/' ||
    // 				config.url == '/home/<USER>/' ||
    // 				config.url == '/home/<USER>/' ||
    // 				config.url == '/alphamsg/' ||
    // 				config.url == '/home/<USER>/' ||
    // 				config.url == '/home/<USER>/' ||
    // 				config.url == '/home/<USER>/' ||
    // 				config.url == '/home/<USER>/'
    // 			) {
    // 				config.headers.authorization = '';
    // 				return config;
    // 			}
    // 			store.dispatch('changetoken', '');
    // 			store.dispatch('changeusername', '');
    // 			store.dispatch('changeuserid', '');
    // 			store.dispatch('changerefreshtoken', '');
    // 			store.dispatch('changeuserrole', '');
    // 			localStorage.removeItem('username');
    // 			localStorage.removeItem('token');
    // 			localStorage.removeItem('id');
    // 			localStorage.removeItem('retoken');
    // 			localStorage.removeItem('userType');
    // 			router.push({ path: '/dashboard', query: { flaglogin: 'login' } });
    // 			// Vue.prototype.$logins();
    // 			// router.replace({
    // 			// 	path: 'login'
    // 			// });
    // 			if (config.url.indexOf('alpha_type') >= 0 && router.history.current.path == '/dashboard') {
    // 				Vue.prototype.$logins();
    // 				if (flag == 0) {
    // 					flag += 1;
    // 					// Vue.prototype.$message('登录状态过期,请重新登录');
    // 				}
    // 			} else {
    // 				if (flag == 0) {
    // 					flag += 1;
    // 					Vue.prototype.$message('登录状态过期,请重新登录');
    // 				}
    // 			}
    // 			// if (flag == 0) {
    // 			// 	flag += 1;
    // 			// 	Vue.prototype.$message('登录状态过期,请重新登录');
    // 			// }
    config.headers.authorization = "Bearer " + store.state.token;
    return config;
    // 		}
    // 	})
    // 	.catch((err) => {
    // 		// console.log(err);
    // 		// console.log('sdsdklk');
    // 		store.dispatch('changetoken', '');
    // 		store.dispatch('changeusername', '');
    // 		store.dispatch('changeuserid', '');
    // 		store.dispatch('changerefreshtoken', '');
    // 		store.dispatch('changeuserrole', '');
    // 		localStorage.removeItem('username');
    // 		localStorage.removeItem('token');
    // 		localStorage.removeItem('id');
    // 		localStorage.removeItem('retoken');
    // 		localStorage.removeItem('userType');
    // 		if (router.history.current.fullPath == '/dashboard?flaglogin=logins') {
    // 			router.push({ path: router.history.current.fullPath, query: { flaglogin: 'login' } });
    // 		} else if (router.history.current.fullPath == '/dashboard?flaglogin=login') {
    // 			router.push({ path: router.history.current.fullPath, query: { flaglogin: 'logins' } });
    // 		} else {
    // 			router.push({ path: '/dashboard', query: { flaglogin: 'login' } });
    // 		}
    // 		// Vue.prototype.$logins();
    // 		// router.replace({
    // 		// 	path: 'login'
    // 		// });
    // 		if (flag == 0) {
    // 			flag += 1;
    // 			Vue.prototype.$message('登录状态过期,请重新登录');
    // 		}
    // 		config.headers.authorization = 'Bearer ' + store.state.token;
    // 		return config;
    // 	});
    // 	return tempconfig;
    // } else {
    // 	config.headers.authorization = 'Bearer ' + store.state.token;
    // 	return config;
    // }
  },
  (error) => {
    return Promise.reject(error);
  }
);

/**
 * 请求拦截器
 */
service.interceptors.response.use(
  (response) => {
    // 终端重复请i去
    // removePendingRequest(response.config); // 从 pendingRequest对象中移除请求
    //
    if (
      response.status === 401 ||
      response.mtycode === 6100003 ||
      (response.data &&
        response.data.mtycode &&
        response.data.mtycode == 6100003)
    ) {
      // console.log('response', response);
      if (flag == 0) {
        flag += 1;
        store.dispatch("changetoken", "");
        store.dispatch("changeusername", "");
        store.dispatch("changeuserid", "");
        store.dispatch("changerefreshtoken", "");
        store.dispatch("changeuserrole", "");
        localStorage.removeItem("username");
        localStorage.removeItem("token");
        localStorage.removeItem("id");
        localStorage.removeItem("retoken");
        localStorage.removeItem("userType");
        router.push({ path: "/dashboard", query: { flaglogin: "login" } });
        Vue.prototype.$message("登录状态过期,请重新登录");
      }
    } else {
      flag = 0;
      return response;
    }
  },
  (error) => {
    // removePendingRequest(error.config || {}); // 从 pendingRequest对象中移除请求
    if (axios.isCancel(error)) {
      console.log("请求已取消");
      // console.log(error.message);
      return Promise.reject({ code: 400 });
    } else {
      // 自行处理异常请求
      // console.log('error', error);
      if (error.response.status == 401) {
        if (flag == 0) {
          flag += 1;
          store.dispatch("changetoken", "");
          store.dispatch("changeusername", "");
          store.dispatch("changeuserid", "");
          store.dispatch("changerefreshtoken", "");
          store.dispatch("changeuserrole", "");
          localStorage.removeItem("username");
          localStorage.removeItem("token");
          localStorage.removeItem("id");
          localStorage.removeItem("retoken");
          localStorage.removeItem("userType");
          router.push({ path: "/dashboard", query: { flaglogin: "login" } });
          // Vue.prototype.$logins();
          // router.replace({
          // 	path: 'login'
          // });

          // Vue.prototype.$message('登录状态过期,请重新登录');
        }
      }
    }
    return Promise.reject();
  }
);
/**
 * @description axios响应拦截器
 */
service.interceptors.response.use(
  (response) => handleData(response),
  (error) => {
    try {
      const { response } = error;
      console.log(response);
      if (response === undefined) {
        // Vue.prototype.$message('未可知错误');
        return {};
      } else return handleData(response);
    } catch {}
  }
);

export default service;
