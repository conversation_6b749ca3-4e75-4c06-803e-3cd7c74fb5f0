<!--  -->
<template>
  <div v-loading="loading"
       class="holdindustry">
    <div style="display: flex; align-items: center; width: 100%; position: relative">
      <div style="display: flex; align-items: center">
        <div class="TitltCompare">信用挖掘</div>
      </div>
      <div style="right: 0px; position: absolute; display: flex">
        <el-button @click="nowindex = '债券信用评级'"
                   :style="nowindex == '债券信用评级' ? 'color:white;background:#4096FF' : ''">债券信用评级</el-button>
        <el-button @click="nowindex = '资产支持证券信用评级'"
                   :style="nowindex == '资产支持证券信用评级' ? 'color:white;background:#4096FF' : ''">资产支持证券信用评级</el-button>
        <el-button @click="outexcel()"
                   icon="el-icon-download"></el-button>
        <el-button @click="dochangge()"
                   style="margin-left: 10px"
                   :icon="tablepic ? 'el-icon-picture' : 'el-icon-tickets'"></el-button>
      </div>
    </div>
    <div v-show="showdetailchoose"
         style="text-align: right">
      <div class="block">
        <el-date-picker v-model="value2"
                        type="month"
                        @change="changgedate"
                        placeholder="选择季度"> </el-date-picker>
      </div>
    </div>
    <div style="height: 16px"></div>
    <div class="TIPBOX"
         style="padding: 20px; background: #f9f9f9; text-align: left">
      <div style="font-size: 12px; font-weight: 600">TIP:</div>
      <div style="font-size: 12px">bs:债券短期信用评级</div>
      <div style="font-size: 12px">bl:债券长期信用评级</div>
      <div style="font-size: 12px">as:资产支持证券短期信用评级</div>
      <div style="font-size: 12px">al:资产支持证券短期信用评级</div>
    </div>
    <div style="height: 16px"></div>
    <div v-show="tablepic">
      <div v-for="(item, index) in nowindex == '资产支持证券信用评级' ? tableTranF2 : tableTranF1"
           :key="index"
           style="margin-top: 16px">
        <div style="font-weight: 500; font-size: 16px; line-height: 24px; color: rgba(0, 0, 0, 0.65); opacity: 0.45">
          {{ item.value }}
        </div>
        <sTable :data="item.list"
                typeFlag="1"></sTable>
      </div>
    </div>
    <div v-show="!tablepic">
      <div style="display: flex; align-items: center; flex-wrap: wrap; justify-content: center">
        <div v-for="(item, index) in name.split(',')"
             :key="index"
             style="display: flex; align-items: center; justify-content: center; margin-left: 10px">
          <div style="font-size: 16px; font-weight: 400">{{ list[index] }}:{{ item }}</div>
        </div>
      </div>
      <div style="page-break-inside: avoid; text-align: left">
        <v-chart ref="creditdown"
                 v-loading="empty2"
                 autoresize
                 element-loading-text="暂无数据"
                 element-loading-spinner="el-icon-document-delete"
                 element-loading-background="rgba(239, 239, 239, 0.5)"
                 style="page-break-inside: avoid; width: 100%; height: 400px"
                 :options="optionpbroe2"></v-chart>
      </div>
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { BondFundCreditDown } from '@/api/pages/tools/compare.js';
import sTable from '../SelfTable.vue';
import VCharts from 'vue-echarts';
export default {
  //import引入的组件需要注入到对象中才能使用
  components: { 'v-chart': VCharts, sTable },
  props: {
    comparetype: {
      type: String,
      default: 'manager' //fund
    },
    id: {
      type: String,
      default: '30189741,30441407'
    },
    type: {
      type: String,
      default: 'equity'
    },
    name: {
      type: String,
      default: '萧楠,胡昕炜'
    }
  },
  filters: {
    fix3 (value) {
      if (value == '--' || value == null || value == '') {
        return value;
      } else {
        return (value * 100).toFixed(2) + '%';
      }
    },
    fix2 (value) {
      return Number(value).toFixed(2) + '亿';
    },
    fixY (value) {
      if (value == '--' || value == null || value == '') {
        return '--';
      } else {
        return (Number(value) * 100).toFixed(2) + '%';
      }
    },
    fixr (value) {
      if (value == 'NaN%') return '--';
      else {
        return value;
      }
    }
  },
  data () {
    //这里存放数据
    return {
      tableTranF1: [],
      tableTranF2: [],
      loading: false,
      list: ['a', 'b', 'c', 'd', 'e'],
      showdetailchoose: false,
      radio: '2',
      value2: '',
      nowindex: '债券信用评级',
      tablepic: false,
      arrlist: [],
      optionpbroe2: {},
      tablecolumnsf: [],
      tablecolumns: [],
      alldata: [],
      bondnames: [],
      absname: [],
      bondarr: [],
      absarr: [],
      colors: [
        '#ff9003',
        '#4096FF',
        '#e040fb',
        '#ff3d00',

        '#a6a5a4',
        '#503a65',
        '#d9b3ff',
        '#79d2d2',
        '#e65C00',
        '#c7bd9a',
        '#02b340',
        'e9fac8',
        '#FFB6C1',
        '#DB7093',
        '#DA70D6',
        '#800080',
        '#9370DB',
        '#6A5ACD',
        '#4169E1',
        '#B0C4DE',
        '#4682B4',
        '#5F9EA0',
        '#8FBC8F',
        '#EEE8AA',
        '#FFD700',
        '#FFA500',
        '#FF6347',
        '#CD5C5C',
        '#B22222',
        '#D3D3D3',
        '#A9A9A9',
        '#FA8072',
        '#929694'
      ]
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {
    nowindex () {
      if (this.nowindex == '债券信用评级') {
        if (this.tablepic == true) {
          this.bonddoit();
        } else {
          this.drawpicbond();
        }
      } else {
        if (this.tablepic == true) {
          this.absdoit();
        } else {
          this.drawpicabs();
        }
      }
    },
    tablepic () {
      if (this.tablepic == false) {
        // this.drawpic();
      }
    }
  },
  //方法集合
  methods: {
    dochangge () {
      this.tablepic = !this.tablepic;
      if (this.nowindex == '债券信用评级') {
        if (this.tablepic == true) {
          this.bonddoit();
        } else {
          this.drawpicbond();
        }
      } else {
        if (this.tablepic == true) {
          this.absdoit();
        } else {
          this.drawpicabs();
        }
      }
    },
    bonddoit () {
      this.tablecolumnsf = [
        {
          dataIndex: 'name',
          key: 'name',
          title: '名称'
        },
        {
          dataIndex: 'yearqtr',
          key: 'Yearqtr',
          defaultSortOrder: 'ascend',
          sorter: (a, b) => {
            if (a.yearqtr > b.yearqtr) return 1;
            else {
              return -1;
            }
          },
          title: '季度'
        }
      ];
      for (let i = 0; i < this.bondnames.length; i++) {
        let temp = '';
        if (this.bondnames[i].indexOf('债券短期信用评级') >= 0) temp = this.bondnames[i].replace('债券短期信用评级', 'bs');
        else if (this.bondnames[i].indexOf('债券长期信用评级') >= 0) temp = this.bondnames[i].replace('债券长期信用评级', 'bl');
        if (this.bondnames[i].indexOf('合计') < 0) {
          this.tablecolumnsf.push({
            dataIndex: this.bondnames[i],
            key: this.bondnames[i],
            title: temp,
            scopedSlots: {
              customRender: 'xx'
            }
          });
        }
      }
      this.arrlist = this.bondarr;
    },
    absdoit () {
      this.tablecolumnsf = [
        {
          dataIndex: 'name',
          key: 'name',
          title: '名称'
        },
        {
          dataIndex: 'yearqtr',
          key: 'Yearqtr',

          sorter: (a, b) => {
            if (a.yearqtr > b.yearqtr) return 1;
            else {
              return -1;
            }
          },
          defaultSortOrder: 'ascend',
          title: '季度'
        }
      ];
      for (let i = 0; i < this.absname.length; i++) {
        let temp = '';
        if (this.absname[i].indexOf('资产支持证券短期信用评级') >= 0) temp = this.absname[i].replace('资产支持证券短期信用评级', 'as');
        if (this.absname[i].indexOf('资产支持证券长期信用评级') >= 0) temp = this.absname[i].replace('资产支持证券长期信用评级', 'al');
        if (this.absname[i].indexOf('合计') < 0) {
          this.tablecolumnsf.push({
            dataIndex: this.absname[i],
            key: this.absname[i],
            title: temp,
            scopedSlots: {
              customRender: 'xx'
            }
          });
        }
      }
      this.arrlist = this.absarr;
    },
    outexcel () {
      this.bonddoit();
      this.absdoit();
      const { export_json_to_excel } = require('@/vendor/Export2Excel');
      var list = [];
      var list2 = [];
      let tHeader = ['名称', '季度'];
      let tHeader2 = ['名称', '季度'];
      let filterVal = [];

      tHeader = tHeader.concat(this.bondnames);
      tHeader2 = tHeader2.concat(this.absname);
      // filterVal = this.bondnames.concat(this.absname);
      // //console.log(this.datatable)

      // //console.log(temparr)
      for (let i = 0; i < this.bondarr.length; i++) {
        list[i] = [];
        list[i][0] = this.bondarr[i].name;
        list[i][1] = this.bondarr[i].yearqtr;
        for (let j = 0; j < this.bondnames.length; j++) {
          list[i][j + 2] = this.bondarr[i][this.bondnames[j]];
        }
      }
      for (let i = 0; i < this.absarr.length; i++) {
        list2[i] = [];
        list2[i][0] = this.absarr[i].name;
        list2[i][1] = this.absarr[i].yearqtr;
        for (let j = 0; j < this.absname.length; j++) {
          list2[i][j + 2] = this.absarr[i][this.absname[j]];
        }
      }

      export_json_to_excel(tHeader, list, '债券信用评级');
      export_json_to_excel(tHeader2, list2, '资产支持证券信用评级');
    },
    getdata () {
      Object.assign(this.$data, this.$options.data());
      this.loading = true;
      if (this.comparetype == 'manager') {
        this.getmanager();
      } else {
        this.gefunddata();
      }
    },
    async getmanager (val) {
      this.arrlist = [];
      let data = await ManagerSwindustryHold({
        manager_code: this.id,
        type: this.type,
        manager_name: this.name,
        flag: this.radio,
        yearqtr: val
      });
      this.loading = false;

      if (data) {
        //  //console.log(data)
        //  //console.log('hereindustry')
        //  let temp
        for (let i = 0; i < data.data.length; i++) {
          //  //console.log('2312312')
          this.arrlist = this.arrlist.concat(data.data[i]);
        }
      }
      //  //console.log(this.arrlist)
    },
    async gefunddata () {
      this.arrlist = [];

      let data = await BondFundCreditDown({
        fund_code: this.id,
        type: this.type,
        fund_name: this.name
      });
      this.loading = false;

      if (data) {
        this.alldata = data.data;
        this.bondnames = [];
        this.absname = [];
        this.bondarr = [];
        this.absarr = [];
        //console.log(data);
        //console.log('hereindustry222');
        // 补齐日期数据
        let datelist = [];
        for (let i = 0; i < data.data.length; i++) {
          for (let j = 0; j < data.data[i].length; j++) {
            if (datelist.indexOf(this.alldata[i][j].yearqtr) < 0) {
              datelist.push(this.alldata[i][j].yearqtr);
            }
          }
        }
        for (let i = 0; i < datelist.length; i++) {
          for (let j = 0; j < data.data.length; j++) {
            if (this.alldata[j].findIndex((item) => item.yearqtr == datelist[j]) < 0) {
              data.data[j].push({
                code: data.data[j][0].code,
                yearqtr: datelist[i],
                '债券短期信用评级:A-1': 'nan',
                '债券短期信用评级:合计': 'nan',
                '债券短期信用评级:未评级': 'nan',
                '债券长期信用评级:AAA': 'nan',
                '债券长期信用评级:AAA以下': 'nan',
                '债券长期信用评级:合计': 'nan',
                '资产支持证券长期信用评级:AAA': 'nan',
                '资产支持证券长期信用评级:合计': 'nan',
                资产支持证券长期信用评级: 'nan'
              });
            }
          }
        }
        datelist.sort((a, b) => {
          if (a > b) return 1;
          else return -1;
        });
        datelist.reverse();
        // 补齐缺失code数据
        if (data.data.length < this.$route.query.id.split(',').length) {
          let codelist = [];
          for (let i = 0; i < data.data.length; i++) {
            codelist.push(data.data[i][0].code);
          }
          for (let i = 0; i < this.$route.query.id.split(',').length; i++) {
            if (codelist.findIndex((item) => item == this.$route.query.id.split(',')[i]) < 0) {
              data.data[data.data.length] = [];
              for (let j = 0; j < data.data[0].length; j++) {
                data.data[data.data.length - 1].push({
                  code: this.$route.query.id.split(',')[i],
                  yearqtr: data.data[0][0].yearqtr,
                  '债券短期信用评级:A-1': 'nan',
                  '债券短期信用评级:合计': 'nan',
                  '债券短期信用评级:未评级': 'nan',
                  '债券长期信用评级:AAA': 'nan',
                  '债券长期信用评级:AAA以下': 'nan',
                  '债券长期信用评级:合计': 'nan',
                  '资产支持证券长期信用评级:AAA': 'nan',
                  '资产支持证券长期信用评级:合计': 'nan',
                  '资产支持证券长期信用评级:未评级': 'nan'
                });
              }
            }
          }
        }
        data.data.sort((a, b) => {
          if (this.$route.query.id.split(',').indexOf(a[0].code) > this.$route.query.id.split(',').indexOf(b[0].code)) return 1;
          else return -1;
        });

        for (let i = 0; i < data.data.length; i++) {
          data.data[i].sort((a, b) => {
            if (a.yearqtr > b.yearqtr) return 1;
            else return -1;
          });
          for (let j = 0; j < data.data[i].length; j++) {
            let item = {
              code: data.data[i][j].code,
              name: this.name.split(',')[this.id.split(',').indexOf(data.data[i][j].code)],
              yearqtr: data.data[i][j].yearqtr
            };
            let item2 = {
              code: data.data[i][j].code,
              name: this.name.split(',')[this.id.split(',').indexOf(data.data[i][j].code)],
              yearqtr: data.data[i][j].yearqtr
            };
            for (let key in data.data[i][j]) {
              //遍历对象属性
              // //console.log(key)
              //遍历对象属性值
              // //console.log(obj[key])
              if (key.indexOf('债券') >= 0) {
                if (this.bondnames.indexOf(key) < 0) {
                  this.bondnames.push(key);
                }
                item[key] = (Number(data.data[i][j][key]) * 100).toFixed(2) + '%';
              }
              if (key.indexOf('资产支持') >= 0) {
                if (this.absname.indexOf(key) < 0) {
                  this.absname.push(key);
                }
                item2[key] = (Number(data.data[i][j][key]) * 100).toFixed(2) + '%';
              }
            }
            this.bondarr.push(item);
            this.absarr.push(item2);
          }
        }
        this.tableTranF1 = [];
        this.tableTranF2 = [];
        try {
          for (let j = 0; j < datelist.length; j++) {
            this.tableTranF1[j] = {
              value: datelist[j],
              list: [['bs:A-1'], ['bs:未评级'], ['bs:合计'], ['bl:AAA'], ['bl:AAA以下'], ['bl:合计']]
            };
            this.tableTranF2[j] = {
              value: datelist[j],
              list: [['al:AAA'], ['al:未评级'], ['al:合计']]
            };
            for (let i = 0; i < data.data.length; i++) {
              if (data.data[i].findIndex((item) => item.yearqtr == datelist[j]) >= 0) {
                this.tableTranF1[j].list[0].push(
                  this.FUNC.isEmpty(data.data[i][data.data[i].findIndex((item) => item.yearqtr == datelist[j])]['债券短期信用评级:A-1'])
                    ? (
                      Number(data.data[i][data.data[i].findIndex((item) => item.yearqtr == datelist[j])]['债券短期信用评级:A-1']) * 100
                    ).toFixed(2) + '%'
                    : '--'
                );
                this.tableTranF1[j].list[1].push(
                  this.FUNC.isEmpty(data.data[i][data.data[i].findIndex((item) => item.yearqtr == datelist[j])]['债券短期信用评级:未评级'])
                    ? (
                      Number(data.data[i][data.data[i].findIndex((item) => item.yearqtr == datelist[j])]['债券短期信用评级:未评级']) * 100
                    ).toFixed(2) + '%'
                    : '--'
                );
                this.tableTranF1[j].list[2].push(
                  this.FUNC.isEmpty(data.data[i][data.data[i].findIndex((item) => item.yearqtr == datelist[j])]['债券短期信用评级:合计'])
                    ? (
                      Number(data.data[i][data.data[i].findIndex((item) => item.yearqtr == datelist[j])]['债券短期信用评级:合计']) * 100
                    ).toFixed(2) + '%'
                    : '--'
                );
                this.tableTranF1[j].list[3].push(
                  this.FUNC.isEmpty(data.data[i][data.data[i].findIndex((item) => item.yearqtr == datelist[j])]['债券长期信用评级:AAA'])
                    ? (
                      Number(data.data[i][data.data[i].findIndex((item) => item.yearqtr == datelist[j])]['债券长期信用评级:AAA']) * 100
                    ).toFixed(2) + '%'
                    : '--'
                );
                this.tableTranF1[j].list[4].push(
                  this.FUNC.isEmpty(data.data[i][data.data[i].findIndex((item) => item.yearqtr == datelist[j])]['债券长期信用评级:AAA以下'])
                    ? (
                      Number(data.data[i][data.data[i].findIndex((item) => item.yearqtr == datelist[j])]['债券长期信用评级:AAA以下']) *
                      100
                    ).toFixed(2) + '%'
                    : '--'
                );
                this.tableTranF1[j].list[5].push(
                  this.FUNC.isEmpty(data.data[i][data.data[i].findIndex((item) => item.yearqtr == datelist[j])]['债券长期信用评级:合计'])
                    ? (
                      Number(data.data[i][data.data[i].findIndex((item) => item.yearqtr == datelist[j])]['债券长期信用评级:合计']) * 100
                    ).toFixed(2) + '%'
                    : '--'
                );

                this.tableTranF2[j].list[0].push(
                  this.FUNC.isEmpty(
                    data.data[i][data.data[i].findIndex((item) => item.yearqtr == datelist[j])]['资产支持证券长期信用评级:AAA']
                  )
                    ? (
                      Number(
                        data.data[i][data.data[i].findIndex((item) => item.yearqtr == datelist[j])]['资产支持证券长期信用评级:AAA']
                      ) * 100
                    ).toFixed(2) + '%'
                    : '--'
                );
                this.tableTranF2[j].list[1].push(
                  this.FUNC.isEmpty(
                    data.data[i][data.data[i].findIndex((item) => item.yearqtr == datelist[j])]['资产支持证券长期信用评级:未评级']
                  )
                    ? (
                      Number(
                        data.data[i][data.data[i].findIndex((item) => item.yearqtr == datelist[j])]['资产支持证券长期信用评级:未评级']
                      ) * 100
                    ).toFixed(2) + '%'
                    : '--'
                );
                this.tableTranF2[j].list[2].push(
                  this.FUNC.isEmpty(
                    data.data[i][data.data[i].findIndex((item) => item.yearqtr == datelist[j])]['资产支持证券长期信用评级:合计']
                  )
                    ? (
                      Number(
                        data.data[i][data.data[i].findIndex((item) => item.yearqtr == datelist[j])]['资产支持证券长期信用评级:合计']
                      ) * 100
                    ).toFixed(2) + '%'
                    : '--'
                );
              } else {
                this.tableTranF1[j].list[0].push('--');
                this.tableTranF1[j].list[1].push('--');
                this.tableTranF1[j].list[2].push('--');
                this.tableTranF1[j].list[3].push('--');
                this.tableTranF1[j].list[4].push('--');
                this.tableTranF1[j].list[5].push('--');

                this.tableTranF2[j].list[0].push('--');
                this.tableTranF2[j].list[1].push('--');
                this.tableTranF2[j].list[2].push('--');
              }
            }
          }
        } catch (e) {
          console.log(e);
        }
        //     //console.log(this.bondnames)
        //     //console.log(this.absname)
        //  let temp
        this.bonddoit();
        this.drawpicbond();
      }
    },
    drawpicabs () {
      let that = this;
      let dataarr = []; //排序
      let serdatalist = [];
      let seriessdata = [];
      for (let i = 0; i < this.absarr.length; i++) {
        if (dataarr.indexOf(this.absarr[i].yearqtr) < 0) {
          dataarr.push(this.absarr[i].yearqtr);
        }
      }
      for (let i = 0; i < this.absname.length; i++) {
        serdatalist.push({ name: this.absname[i], value: [] });
        for (let k = 0; k < this.name.split(',').length; k++) {
          serdatalist[i].value.push({ name: this.name.split(',')[k], value: [] });
        }
        for (let j = 0; j < this.absarr.length; j++) {
          if (this.absarr[j][this.absname[i]]) {
            serdatalist[i].value[this.name.split(',').indexOf(this.absarr[j].name)].value.push([
              this.absarr[j].yearqtr,
              this.absarr[j][this.absname[i]].split('%')[0],
              this.absarr[j].code,
              this.absarr[j].name
            ]);
          } else {
            serdatalist[i].value[this.name.split(',').indexOf(this.absarr[j].name)].value.push([
              this.absarr[j].yearqtr,
              '',
              this.absarr[j].code,
              this.absarr[j].name
            ]);
          }
        }
      }
      // //console.log('sdsadkasl')
      // //console.log(serdatalist)

      for (let i = 0; i < serdatalist.length; i++) {
        if (serdatalist[i].name.indexOf('合计') < 0) {
          for (let j = 0; j < serdatalist[i].value.length; j++) {
            seriessdata.push({
              name: serdatalist[i].name,
              type: 'bar',
              label: {
                show: true,
                formatter: function (params) {
                  //标签内容

                  return that.list[that.name.split(',').indexOf(serdatalist[i].value[j].name)];
                },
                position: 'top'
              },
              itemStyle: {
                barBorderColor: this.colors[i],
                color: this.colors[i]
              },
              data: serdatalist[i].value[j].value
            });
          }
        }
      }
      // //console.log(seriessdata)
      this.optionpbroe2 = {
        color: this.colors,
        legend: {},
        dataZoom: [
          {
            type: 'slider',
            show: true,
            height: 14,
            bottom: 10,
            borderColor: 'transparent',
            backgroundColor: '#fafafa',
            // 拖拽手柄样式 svg 路径
            handleIcon:
              'M512 512m-208 0a6.5 6.5 0 1 0 416 0 6.5 6.5 0 1 0-416 0Z M512 192C335.264 192 192 335.264 192 512c0 176.736 143.264 320 320 320s320-143.264 320-320C832 335.264 688.736 192 512 192zM512 800c-159.072 0-288-128.928-288-288 0-159.072 128.928-288 288-288s288 128.928 288 288C800 671.072 671.072 800 512 800z',
            handleColor: '#aab6c6',
            handleSize: 20,
            handleStyle: {
              borderColor: '#aab6c6',
              shadowBlur: 4,
              shadowOffsetX: 1,
              shadowOffsetY: 1,
              shadowColor: '#e5e5e5'
            },
            start: 0,
            end: 100
          }
        ],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          },
          textStyle: {
            fontSize: 14
          },
          formatter: (params) => {
            //  //console.log(params)
            let str = `时间: ${params[0].axisValue} <br />`;
            for (let i = params.length - 1; i >= 0; i--) {
              if (params[i].value[1] != 'NaN') {
                let dotHtml =
                  '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' +
                  params[i].color +
                  '"></span>';
                let temp = '';
                if (params[i].seriesName.indexOf('资产支持证券短期信用评级') >= 0)
                  temp = params[i].seriesName.replace('资产支持证券短期信用评级', 'as');
                else if (params[i].seriesName.indexOf('资产支持证券长期信用评级') >= 0)
                  temp = params[i].seriesName.replace('资产支持证券长期信用评级', 'al');

                str +=
                  dotHtml +
                  `${temp}: ${that.list[that.name.split(',').indexOf(params[i].value[3])] + '       :' + params[i].value[1] + '%'}<br />`;
              }
            }
            return str;
          }
        },
        grid: {
          top: '15%',
          left: '3%',
          right: '4%',
          bottom: '10%',
          containLabel: true
        },

        xAxis: [
          {
            type: 'category',
            data: dataarr,
            axisLabel: {
              show: true,
              textStyle: {
                fontSize: 14
              },
              interval: 0,
              rotate: 40
            }
          }
        ],
        yAxis: [
          {
            type: 'value',

            axisTick: {
              show: false
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed'
              }
            },
            axisLabel: {
              show: true,
              textStyle: {
                fontSize: 14
              },
              formatter (value) {
                return value + '%';
              }
            }
          }
        ],
        series: seriessdata
      };
    },
    NumAscSort (a, b) {
      if (a > b) {
        return 1;
      } else {
        return -1;
      }
    },
    drawpicbond () {
      let that = this;
      let dataarr = []; //排序
      let serdatalist = [];
      let seriessdata = [];
      for (let i = 0; i < this.bondarr.length; i++) {
        if (dataarr.indexOf(this.bondarr[i].yearqtr) < 0) {
          dataarr.push(this.bondarr[i].yearqtr);
        }
      }
      dataarr.sort(this.NumAscSort);
      // //console.log(['2012 Q2','2009 Q2','2020 Q4','1985 Q2','2320 Q2','2012 Q1','2009 Q3'].sort(this.NumAscSort))
      for (let i = 0; i < this.bondnames.length; i++) {
        serdatalist.push({ name: this.bondnames[i], value: [] });
        for (let k = 0; k < this.name.split(',').length; k++) {
          serdatalist[i].value.push({ name: this.name.split(',')[k], value: [] });
        }
        for (let j = 0; j < this.bondarr.length; j++) {
          if (this.bondarr[j][this.bondnames[i]]) {
            serdatalist[i].value[this.name.split(',').indexOf(this.bondarr[j].name)].value.push([
              this.bondarr[j].yearqtr,
              this.bondarr[j][this.bondnames[i]].split('%')[0],
              this.bondarr[j].code,
              this.bondarr[j].name
            ]);
          } else {
            serdatalist[i].value[this.name.split(',').indexOf(this.bondarr[j].name)].value.push([
              this.bondarr[j].yearqtr,
              '',
              this.bondarr[j].code,
              this.bondarr[j].name
            ]);
          }
        }
      }
      // //console.log('sdsadkasl')
      // //console.log(serdatalist)

      for (let i = 0; i < serdatalist.length; i++) {
        if (serdatalist[i].name.indexOf('合计') < 0) {
          for (let j = 0; j < serdatalist[i].value.length; j++) {
            seriessdata.push({
              name: serdatalist[i].name,
              type: 'bar',
              label: {
                show: true,
                formatter: function (params) {
                  //标签内容

                  return that.list[that.name.split(',').indexOf(serdatalist[i].value[j].name)];
                },
                position: 'top'
              },
              itemStyle: {
                barBorderColor: this.colors[i],
                color: this.colors[i]
              },
              data: serdatalist[i].value[j].value
            });
          }
        }
      }
      // //console.log(seriessdata)
      this.optionpbroe2 = {
        color: this.colors,
        legend: {},
        dataZoom: [
          {
            type: 'slider',
            show: true,
            height: 14,
            bottom: 10,
            borderColor: 'transparent',
            backgroundColor: '#fafafa',
            // 拖拽手柄样式 svg 路径
            handleIcon:
              'M512 512m-208 0a6.5 6.5 0 1 0 416 0 6.5 6.5 0 1 0-416 0Z M512 192C335.264 192 192 335.264 192 512c0 176.736 143.264 320 320 320s320-143.264 320-320C832 335.264 688.736 192 512 192zM512 800c-159.072 0-288-128.928-288-288 0-159.072 128.928-288 288-288s288 128.928 288 288C800 671.072 671.072 800 512 800z',
            handleColor: '#aab6c6',
            handleSize: 20,
            handleStyle: {
              borderColor: '#aab6c6',
              shadowBlur: 4,
              shadowOffsetX: 1,
              shadowOffsetY: 1,
              shadowColor: '#e5e5e5'
            },
            start: 0,
            end: 100
          }
        ],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          },
          textStyle: {
            fontSize: 14
          },
          formatter: (params) => {
            //  //console.log(params)
            let str = `时间: ${params[0].axisValue} <br />`;
            for (let i = params.length - 1; i >= 0; i--) {
              if (params[i].value[1] != 'NaN') {
                let dotHtml =
                  '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' +
                  params[i].color +
                  '"></span>';
                let temp = '';
                if (params[i].seriesName.indexOf('债券短期信用评级') >= 0) temp = params[i].seriesName.replace('债券短期信用评级', 'bs');
                else if (params[i].seriesName.indexOf('债券长期信用评级') >= 0)
                  temp = params[i].seriesName.replace('债券长期信用评级', 'bl');
                str +=
                  dotHtml +
                  `${temp}: ${that.list[that.name.split(',').indexOf(params[i].value[3])] + '       :' + params[i].value[1] + '%'}<br />`;
              }
            }
            return str;
          }
        },
        grid: {
          top: '15%',
          left: '3%',
          right: '4%',
          bottom: '10%',
          containLabel: true
        },

        xAxis: [
          {
            type: 'category',
            data: dataarr,
            axisLabel: {
              show: true,
              textStyle: {
                fontSize: 14
              },
              interval: 0,
              rotate: 40
            }
          }
        ],
        yAxis: [
          {
            type: 'value',

            axisTick: {
              show: false
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed'
              }
            },
            axisLabel: {
              show: true,
              textStyle: {
                fontSize: 14
              },
              formatter (value) {
                return value + '%';
              }
            }
          }
        ],
        series: seriessdata
      };
    },
    createPrintWord () {
      let name = this.name
        .split(',')
        .map((item, index) => {
          return this.list[index] + ':' + item;
        })
        .join('  ');
      let height = this.$refs['creditdown']?.$el.clientHeight;
      let width = this.$refs['creditdown']?.$el.clientWidth;
      let chart = this.$refs['creditdown'].getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      });
      return [
        ...this.$exportWord.exportTitle('信用挖掘'),
        ...this.$exportWord.exportDescripe(name),
        ...this.$exportWord.exportChart(chart, { width, height })
      ];
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () { },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
</style>
<style>
.holdindustry .el-input__inner {
	/* padding-left: 30px !important; */
}
</style>
