import { getTypeInfo } from '../api/pages/Analysis.js';
import common from './common';

// 全局方法
const FUNC = {
	/**
	 * 将一个日期的值转为季度值, 日期格式为"YYYY-MM-DD"
	 * 复制出向前推44天，向后推45天，共90天相同值的二维数组
	 * (date, value) => [... [date-1, value], [date, value], [date+1, value] ...]
	 */
	transformToQuarter: function (date, value) {
		if (!date || value === undefined) {
			//console.log('缺少应有参数');
			return;
		}
		let result = [];
		let aDayMili = 1000 * 60 * 60 * 24;
		let dateMili = Date.parse(date);
		result.push([FUNC.transformDate(dateMili), value]);
		// 向前推44天
		for (let i = 1; i <= 44; i++) {
			let earlDate = FUNC.transformDate(dateMili - i * aDayMili);
			result.unshift([earlDate, value]);
		}
		// 向后推44天
		for (let i = 1; i <= 45; i++) {
			let earlDate = FUNC.transformDate(dateMili + i * aDayMili);
			result.push([earlDate, value]);
		}
		return result;
	},
	/**
	 * 将一个日期转为季度值返回
	 * @param {日期} date
	 */
	dateToQuarter: function (date) {
		if (!date) console.log('缺少应有参数');
		date = FUNC.transformDate(date);
		let year = date.slice(0, 4);
		let monthDay = date.slice(5);
		let list = [
			{ early: '01-01', last: '03-31', q: 'Q1' },
			{ early: '04-01', last: '06-30', q: 'Q2' },
			{ early: '07-01', last: '09-30', q: 'Q3' },
			{ early: '10-01', last: '12-31', q: 'Q4' }
		];
		for (let item of list) {
			if (monthDay >= item.early && monthDay <= item.last) {
				return year + ' ' + item.q;
			}
		}
	},
	/*
	判空
	*/
	isEmpty(value) {
		if (
			value &&
			value != '' &&
			value != '--' &&
			value != '- -' &&
			JSON.stringify(value) != '[]' &&
			JSON.stringify(value) != '{}' &&
			value != 'NAN' &&
			value != 'nan'
		)
			return true;
		else return false;
	},
	/**
	 * 将一个季度的值转为一个连续的3个月的值,如"2021 Q1" (return的数组length不一定为90)
	 * (quarter, value) => [... [date1, value], [date2, value], [date3, value] ...]
	 * (ps:由于目前数据问题较多,为了保证图表的正常显示,暂时在没有必须参数时返回"[]")
	 */
	quarterTransformToDay: function (quarter, value) {
		if (!quarter || value === undefined) {
			//console.log('缺少应有参数');
			return [];
		}

		let result = [];
		let { earlyDate, lateDate } = FUNC.earlyAndLateDate([quarter]);
		let aDayMili = 1000 * 60 * 60 * 24;
		let currentMili = Date.parse(earlyDate),
			lateMili = Date.parse(lateDate);

		while (currentMili <= lateMili) {
			result.push([FUNC.transformDate(currentMili), value]);
			currentMili += aDayMili;
		}
		return result;
	},
	/**
	 * 将时间戳格式的日期转为"YYYY-MM-DD hh:mm:ss"格式的日期字符串
	 * date-日期; dateDelimiter-日期分隔符; needTime-是否显示时间; timeDelimiter-时间分隔符; delimiter-日期与时间直接分隔符;
	 */
	transformDate: function (originDate, needTime = false, dateDelimiter = '-', timeDelimiter = ':', delimiter = ' ') {
		if (!originDate) {
			//console.log('缺少应有参数');
			return;
		}

		let year, month, day, result;
		let date = new Date(originDate);

		year = date.getFullYear();
		month = date.getMonth() + 1 >= 10 ? date.getMonth() + 1 : '0' + (date.getMonth() + 1);
		day = date.getDate() >= 10 ? date.getDate() : '0' + date.getDate();
		result = year + dateDelimiter + month + dateDelimiter + day;
		if (needTime) {
			let hour, minute, second;
			hour = date.getHours() >= 10 ? date.getHours() : '0' + date.getHours();
			minute = date.getMinutes() >= 10 ? date.getMinutes() : '0' + date.getMinutes();
			second = date.getSeconds() >= 10 ? date.getSeconds() : '0' + date.getSeconds();
			result = result + delimiter + hour + timeDelimiter + minute + timeDelimiter + second;
		}
		return result;
	},
	/**
	 * 将参数对象拼接成参数字符串, eg: obj = {a: 1, b:2} => str = 'a=1&b=2';(目前不包含问号)
	 */
	paramsToString: function (paramsObj) {
		// console.log(paramsObj);
		if (!paramsObj) {
			//console.log('缺少应有参数');
			return;
		}

		let notFirst = false,
			paramsString = '';

		for (let key in paramsObj) {
			if (notFirst) {
				paramsString += '&' + key + '=' + paramsObj[key];
			} else {
				paramsString += key + '=' + paramsObj[key];
				notFirst = true;
			}
		}
		return paramsString;
	},
	/**
	 * 根据起始日期和终止日期，生成连续的日期数组
	 * beginDate-开始日期;endDate-终止日期;
	 */
	generateDateList: function (beginDate = new Date(), endDate = new Date()) {
		let begin = Date.parse(beginDate);
		let end = Date.parse(endDate);
		let aDayMili = 1000 * 60 * 60 * 24,
			result = [],
			current = begin;
		while (current <= end) {
			result.push(FUNC.transformDate(current));
			current += aDayMili;
		}
		return result;
	},
	/**
	 * 根据传入的季度日期list,返回最早月份的第一天和最晚月份的最后一天,也可用于返回数字列表最小最大值(type=number)
	 * dateList-季度列表, type-返回类型: "day"-返回日期, "number"-返回list中的最小最大值
	 */
	earlyAndLateDate: function (dateList, type = 'day') {
		if (!Array.isArray(dateList) || dateList.length == 0) {
			//console.log('缺少应有参数');
			return;
		}
		let arr = dateList.slice();
		let eYear, eMonth, eDay, lYear, lMonth, lDay;
		let early = arr[0],
			late = arr[0];
		let earlyDate = '',
			lateDate = '';

		arr.forEach((item) => {
			early = item && item < early ? item : early;
			late = item > late ? item : late;
		});

		if (type == 'number') {
			return { min: early, max: late };
		}

		// 最早月份第一天
		eYear = early.split(' Q')[0];
		eMonth = parseInt(early.split(' Q')[1]) * 3 - 2;
		eMonth = eMonth < 10 ? '0' + eMonth : eMonth;
		eDay = '01';
		earlyDate = eYear + '-' + eMonth + '-' + eDay;
		// 最晚月份最后一天
		lYear = late.split(' Q')[0];
		lMonth = parseInt(late.split(' Q')[1]) * 3;
		if ([1, 3, 5, 7, 8, 10, 12].includes(lMonth)) {
			lDay = '31';
		} else if ([4, 6, 9, 11]) {
			lDay = '30';
		} else {
			lDay = lYear % 4 === 0 ? '29' : '28';
		}
		lMonth = lMonth < 10 ? '0' + lMonth : lMonth;
		lateDate = lYear + '-' + lMonth + '-' + lDay;

		return { earlyDate, lateDate };
	},
	/**
	 * 从日期数组中返回季度最后一天日期,返回值有"日期"和"季度"两种格式
	 * (季度格式相关逻辑未写,需要时再补充)
	 * (若原数组缺少季度最后一天,则往前取最后一天)
	 * @param {*} dateList
	 * @param {*} returnType
	 */
	returnQuarterEndDayList(dateList, returnType = 'day') {
		let result = [];
		dateList.forEach((item, index) => {
			let current = dateList[index].slice(5);
			let currentYear = dateList[index].slice(0, 4);
			if (index == dateList.length - 1) {
				result.push(item);
			} else {
				let next = dateList[index + 1].slice(5);
				let nextYear = dateList[index + 1].slice(0, 4);
				if (
					(current <= '03-31' && next > '03-31') ||
					(current > '03-31' && current <= '06-30' && next > '06-30') ||
					(current > '06-30' && current <= '09-30' && next > '09-30') ||
					(current > '09-30' && current <= '12-31' && currentYear < nextYear)
				) {
					result.push(item);
				}
			}
		});
		return result;
	},
	/**
	 * 根据第一个日期和最后一个日期，生成连续的季度日期
	 * @param {*} start 日期/季度格式 eg: 2021-01-01 / 2021 Q1
	 * @param {*} end 日期/季度格式 eg: 2021-01-01 / 2021 Q1
	 * @param {*} originDateType 传入数据格式 date / quarter eg: 2021-01-01 / 2021 Q1
	 */
	dateGenerateQuarterList: function (start, end = new Date(), originDateType = 'date') {
		if (!start || !end) console.log('缺少应有参数');
		let result = [];
		let qList = ['Q1', 'Q2', 'Q3', 'Q4'];

		let startYear = start.slice(0, 4);
		let endYear = end.slice(0, 4);
		let startQ = originDateType == 'date' ? FUNC.returnQuarter(start).slice(5) : start.slice(5);
		let endQ = originDateType == 'date' ? FUNC.returnQuarter(end).slice(5) : end.slice(5);
		let yList = Array.from({ length: endYear - startYear + 1 }, (item, index) => (item = parseInt(startYear) + index));
		// eg: yList = ['2018', '2019', '2020', '2021']

		for (let y of yList) {
			if (y == startYear) {
				qList.forEach((q) => {
					if (q >= startQ) {
						result.push(y + ' ' + q);
					}
				});
			} else if (y == endYear) {
				qList.forEach((q) => {
					if (q <= endQ) {
						result.push(y + ' ' + q);
					}
				});
			} else {
				qList.forEach((q) => {
					result.push(y + ' ' + q);
				});
			}
		}
		return result;
	},
	/**
	 * 根据第一个日期和最后一个日期，生成连续的季度日期
	 * @param {*} start 日期/季度格式 eg: 2021-01-01 / 2021 Q1
	 * @param {*} end 日期/季度格式 eg: 2021-01-01 / 2021 Q1
	 * @param {*} originDateType 传入数据格式 date / quarter eg: 2021-01-01 / 2021 Q1
	 */
	dateGenerateQuarterList2: function (start, end = new Date(), originDateType = 'date') {
		if (!start || !end) console.log('缺少应有参数');
		let result = [];
		let qList = ['Q1', 'Q2', 'Q3', 'Q4'];

		let startYear = start.slice(0, 4);
		let endYear = end.slice(0, 4);
		let startQ = originDateType == 'date' ? FUNC.returnQuarter(start).slice(5) : start.slice(5);
		let endQ = originDateType == 'date' ? FUNC.returnQuarter(end).slice(5) : end.slice(5);
		let yList = Array.from({ length: endYear - startYear + 1 }, (item, index) => (item = parseInt(startYear) + index));
		// eg: yList = ['2018', '2019', '2020', '2021']
		if (yList?.length == 1) {
			for (let y of yList) {
				if (y == startYear) {
					qList.forEach((q) => {
						if (q >= startQ && q <= endQ) {
							result.push(y + ' ' + q);
						}
					});
				} else if (y == endYear) {
					qList.forEach((q) => {
						if (q <= endQ) {
							result.push(y + ' ' + q);
						}
					});
				} else {
					qList.forEach((q) => {
						result.push(y + ' ' + q);
					});
				}
			}
			return result;
		}
		for (let y of yList) {
			if (y == startYear) {
				qList.forEach((q) => {
					if (q >= startQ) {
						result.push(y + ' ' + q);
					}
				});
			} else if (y == endYear) {
				qList.forEach((q) => {
					if (q <= endQ) {
						result.push(y + ' ' + q);
					}
				});
			} else {
				qList.forEach((q) => {
					result.push(y + ' ' + q);
				});
			}
		}
		return result;
	},
	// 季度转日期
	getQuarterStartDate(quarter, year) {
		const quarterStartMonth = (quarter - 1) * 3; // 获取季度的开始月份，如第一季度为0，第二季度为3，以此类推
		const quarterStartDate = new Date(year, quarterStartMonth + 1, 15); // 创建一个日期对象，年份为 year，月份为季度的开始月份，日期为 1

		return quarterStartDate
			.toLocaleDateString('zh-CN', {
				year: 'numeric',
				month: '2-digit',
				day: '2-digit'
			})
			.replace(/\//g, '-');
	},
	// 季度转日期
	getQuarterEndDate(quarter, year) {
		const quarterStartMonth = (quarter - 1) * 3; // 获取季度的开始月份，如第一季度为0，第二季度为3，以此类推
		const quarterStartDate = new Date(year, quarterStartMonth + 2, 0); // 创建一个日期对象，年份为 year，月份为季度的开始月份，日期为 1

		return quarterStartDate.toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' }).replace(/\//g, '-');
	},
	/**
	 * 返回日期对应的季度数据
	 * data: 日期数据
	 * type: 返回值类型; type可选值: quarter-年份季度,eg:'2021 Q3'; start-年份季度第一日,eg:'2021-01-01'; end-年份季度最后一日,eg:'2021-03-31'
	 * dataType: 初始传入日期数据类型，支持传入两种数据，date-日期(eg: 2021-01-01); quarter-季度(eg: 2021 Q1);
	 */
	returnQuarter: function (data, type = 'quarter', dataType = 'date') {
		if (!data) return;
		let result = '';
		if (dataType == 'date') {
			let year = FUNC.transformDate(data).slice(0, 4);
			let month = FUNC.transformDate(data).slice(5, 7);
			for (let q of common.quarterData) {
				if (q.month.includes(month)) {
					if (type == 'quarter') {
						result = year + ' ' + q.quarter;
					} else if (type == 'start') {
						result = year + '-' + q.startDate;
					} else if (type == 'end') {
						result = year + '-' + q.endDate;
					}
				}
			}
		} else if (dataType == 'quarter') {
			let year = data.slice(0, 4);
			let quarter = data.slice(5);
			for (let q of common.quarterData) {
				if (quarter == q.quarter) {
					if (type == 'quarter') {
						result = year + ' ' + q.quarter;
					} else if (type == 'start') {
						result = year + '-' + q.startDate;
					} else if (type == 'end') {
						result = year + '-' + q.endDate;
					}
				}
			}
		}
		return result;
	},
	/**
	 * 防抖-以最后一次为准
	 * 引用方式: FUNC.debounceFunc(myFunc, 1000)()
	 * fn-组件内业务函数; delay-间隔时间;
	 */
	timer: null,
	debounceFunc(fn, delay) {
		return function () {
			if (FUNC.timer) {
				clearTimeout(FUNC.timer);
			}
			let context = this;
			let args = arguments;
			FUNC.timer = setTimeout(() => {
				fn.apply(context, args);
			}, delay);
		};
	},
	/**
	 * 节流-一段时间内仅执行一次
	 * 引用方式: FUNC.throttleFnc(myFunc, 1000)()
	 * fn-组件内业务函数; delay-间隔时间;
	 */
	throttleFunc(fn, delay) {
		return function () {
			if (!FUNC.timer) {
				let context = this;
				let args = arguments;
				FUNC.timer = true;
				fn.apply(context, args);
				setTimeout(() => {
					FUNC.timer = false;
				}, delay);
			}
		};
	},
	/**
	 * 排序
	 * dataList-数字数组
	 * nameList-名称数组
	 * order-顺序: rise-升序(默认) fall-降序
	 */
	sortFunc: function (dataList, nameList, order = 'rise') {
		let len = dataList.length;
		for (let i = 0; i < len; i++) {
			for (let j = 0; j < len - 1 - i; j++) {
				if (dataList[j] > dataList[j + 1]) {
					[dataList[j], dataList[j + 1]] = [dataList[j + 1], dataList[j]];
					let a = nameList[j + 1];
					let b = nameList[j];
					nameList[j] = a;
					nameList[j + 1] = b;
				}
			}
		}
		if (order == 'fall') {
			dataList.reverse();
			nameList.reverse();
		}
		return {
			dataList,
			nameList
		};
	},
	/**
	 * 判断是否是有效对象类型 (Array, Object)
	 * 对象类型值中含有有效数据
	 */
	isValidObj(data) {
		return data && typeof data == 'object' && Object.keys(data).length > 0;
	},
	/**
	 * 将秒或毫秒转为时分秒
	 * originTime: 传入毫秒/秒数据; isMill: 传入数据是否是毫秒,默认为否
	 */
	transformSecondToTime: function (originTime, isMill = false) {
		if (originTime == undefined || isNaN(parseInt(originTime))) {
			//console.log('缺少应有参数');
			return;
		}

		let time = originTime;
		let result = '';
		if (isMill) {
			time = Math.ceil(originTime / 1000);
		}
		let second = 0,
			minute = 0,
			hour = 0;
		// 除于60s
		if (time > 60) {
		}
		second = time % 60;
		second = second < 10 ? '0' + second : String(second);
		time = time - second;
		minute = (time / 60) % 60;
		minute = minute < 10 ? '0' + minute : String(minute);
		time = time - minute * 60;
		hour = time / 60 / 60;
		hour = hour < 10 ? '0' + hour : String(hour);
		return (result = hour + ':' + minute + ':' + second);
	},
	/**
	 * 比较两组一维数组的增减
	 * oldList: 旧数组, lastList: 新数组
	 */
	compareTwoListChange: (oldList, lastList) => {
		if (!Array.isArray(oldList) || !Array.isArray(lastList)) {
			//console.log('参数错误，请检查后重试');
			return;
		}
		let arr1 = oldList.slice();
		let arr2 = lastList.slice();
		if (arr1.length == 0 && arr2.length == 0) {
			return { add: [], del: [] };
		} else if (arr1.length == 0 && arr2.length > 0) {
			return { add: arr2, del: [] };
		} else if (arr1.length > 0 && arr2.length == 0) {
			return { add: [], del: arr1 };
		} else {
			arr1.forEach((item, index) => {
				if (arr2.includes(item)) {
					let index2 = arr2.indexOf(item);
					arr1[index] = undefined;
					arr2[index2] = undefined;
				}
			});
			let del = arr1.filter((item) => item);
			let add = arr2.filter((item) => item);
			return { add, del };
		}
	},
	/**
	 * 文本转换器
	 * list-对照表, val-传入值, inputKey-输入值对应key, outputKey-输出值对应key
	 * defaultText-未找到对应数据时的默认返回值, 未传该值时默认返回传入值;
	 */
	textConverter: function (list, val, inputKey, outputKey, defaultText) {
		if (!Array.isArray(list) || typeof inputKey !== 'string' || typeof outputKey !== 'string') {
			//console.log('参数错误，请检查后重试');
			return;
		}

		let result;
		let findResult = list.find((item) => item[inputKey] == val);
		result = findResult ? findResult[outputKey] : undefined;
		return result !== undefined ? result : defaultText === undefined ? result : defaultText;
	},
	/**
	 * 判断基金基本类型
	 * type-当前获取到的类型; code-基金code; needOriginType-是否需要alpha_type接口返回的原始type;
	 * ps: 1.type,code两者有一即可调用该函数;
	 *     2.该函数在业务代码中的调用方式为:this.FUNC.returnFundMainType(...).then(res => // do something)
	 */
	async returnFundMainType({ type, code, needOriginType = false }) {
		if (!code && !type) {
			//console.log('缺少必要参数');
			return;
		}
		// 若未传入type,则使用code调取alpha_type接口获取type
		if (!type) {
			let data = await getTypeInfo({ code: code });
			type = data.alpha_type.type || '';
			if (needOriginType) return type;
		}
		let mainType = '';
		if (['equity', 'equitywindow', 'equityclosed', 'neutral', 'equitywithhk'].includes(type)) mainType = 'equity';
		if (['equityindex', 'equityindexwindow', 'equityindexclosed', 'equityenhance', 'equityhk-index'].includes(type))
			mainType = 'equityindex';
		if (['fof', 'fof:target-date', 'fof:target-risk'].includes(type)) mainType = 'fof';
		if (['bond', 'bondwindow', 'bondclosed'].includes(type)) mainType = 'bond';
		if (['cbond', 'cbondwindow', 'cbondclosed'].includes(type)) mainType = 'cbond';
		if (['purebond', 'purebondwindow', 'purebondclosed', 'obond', 'obondwindow', 'obondclosed'].includes(type)) mainType = 'purebond';
		if (['bill', 'billwindow', 'billclosed'].includes(type)) mainType = 'bill';
		if (['money', 'moneywindow', 'moneyclosed'].includes(type)) mainType = 'money';
		if (['hkequity', 'hkequitywindow', 'hkequityclosed', 'equityhk'].includes(type)) mainType = 'hkequity';
		// QDII类型暂未添加
		return mainType;
	},
	/**
	 * 颜色转换器-16进制转rgb
	 */
	colorToRgb(color, toRgba = false, transparentValue = 1) {
		let sColor = color.toString().toLowerCase();
		let reg = /^#([0-9a-fA-F]{3}|[0-9a-fA-F]{6})$/;
		if (!sColor || !reg.test(sColor)) {
			//console.log('未传入颜色值或传入颜色值不为十六进制');
			return;
		}
		if (sColor.length === 4) {
			let sColorNew = '#';
			for (let i = 1; i < 4; i++) {
				sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1));
			}
			sColor = sColorNew;
		}
		// 处理十六位颜色值
		let sColorChange = [];
		for (let i = 1; i < 7; i += 2) {
			sColorChange.push(parseInt('0x' + sColor.slice(i, i + 2)));
		}
		sColor = toRgba ? `RGBA(${sColorChange.join(',')},${transparentValue})` : `RGB(${sColorChange.join(',')})`;
		return sColor;
	},
	/**
	 * 判断一维对象Object的可迭代值是否相等/全等
	 * obj1: 对象1, obj2: 对象2, checkType-是否检验对象内值的类型(是否需要全等)
	 */
	equalityObject(obj1, obj2, checkType = false) {
		if (typeof obj1 !== 'object' || typeof obj1 !== 'object') {
			//console.log('参数不为对象类型,请检查后重试');
			return false;
		}
		if (obj1 === null && obj2 === null) {
			//console.log('请注意,两个参数对象均为null');
			return true;
		}
		let keys1 = Object.keys(obj1) || [];
		let keys2 = Object.keys(obj2) || [];
		let allKeys = Array.from(new Set(keys1.concat(keys2)));
		let isEquality = true;
		for (let key of allKeys) {
			if (obj1[key] != obj2[key]) {
				isEquality = false;
			}
			if (checkType && toString.call(obj1[key]) !== toString.call(obj2[key])) {
				isEquality = false;
			}
		}
		return isEquality;
	},
	/**
	 * 根据屏幕像素转换px和mm
	 * @func conversion_getDPI 获取屏幕一英寸为多少像素
	 * @func pxConversionMm 根据getDPI方法返回值,将px转为mm
	 * @func mmConversionPx 根据getDPI方法返回值,将mm转为px
	 */
	conversion_getDPI() {
		var arrDPI = new Array();
		if (window.screen.deviceXDPI) {
			arrDPI[0] = window.screen.deviceXDPI;
			arrDPI[1] = window.screen.deviceYDPI;
		} else {
			var tmpNode = document.createElement('DIV');
			tmpNode.style.cssText = 'width:1in;height:1in;position:absolute;left:0px;top:0px;z-index:99;visibility: hidden';
			document.body.appendChild(tmpNode);
			arrDPI[0] = parseInt(tmpNode.offsetWidth);
			arrDPI[0] = parseInt(tmpNode.offsetHeight);
			tmpNode.parentNode.removeChild(tmpNode);
		}
		return arrDPI;
	},
	pxConversionMm(value) {
		var inch = value / this.conversion_getDPI()[0];
		var c_value = inch * 25.4;
		return c_value;
	},
	mmConversionPx(value) {
		var inch = value / 25.4;
		var c_value = inch * this.conversion_getDPI()[0];
		return c_value;
	},
	/**
	 * 深拷贝工具函数
	 * @param origin 传入数据
	 */
	deepClone(origin) {
		if (typeof origin === 'object' && origin !== null) {
			let cloneOrigin = Array.isArray(origin) ? [] : {};
			for (const key in origin) {
				cloneOrigin[key] = FUNC.deepClone(origin[key]);
			}
			return cloneOrigin;
		} else {
			return origin;
		}
	}
};

export default FUNC;
