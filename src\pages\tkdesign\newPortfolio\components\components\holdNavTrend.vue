<template>
    <div class="plate-wrapper fund-performance-wrapper">
        <combinationComponentHeader title="持仓净值走势" showMoreBtn @download="exportExcel">
            <template slot="right">
                <el-form  ref="form" :model="form" label-width="80px" class="title-right-form">
                    <!-- <el-select v-model="comparisonValue" placeholder="请选择基准">
						<el-option
                            v-for="item in options"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                            </el-option>
					</el-select> -->
                    <div style="margin-right: 16px;">
                        <FormTimePicker v-model="preset_time" @input="handleFormChange"></FormTimePicker>
                    </div>
                </el-form>
            </template>
        </combinationComponentHeader>
        <!-- <div id="fund-performance-board-chart-container" style="height: 355px;"></div> -->
        <lineChart ref="fund-performance-board-chart-container3" @tableData="getTableData"></lineChart>
    </div>
</template>
<script>
import combinationComponentHeader from './combinationComponentHeader.vue';
import lineChart from '../chart/lineChart.vue';
import FormTimePicker from './formTimePicker.vue';
import { filter_to_excel } from "@/utils/exportExcel.js";
const dayjs = require('dayjs');
export default {
    name:'TheFundPerformancePlate',
    components:{
        combinationComponentHeader,
        lineChart,
        FormTimePicker
    },
    data(){
        return {
            form:{},
            tableData:[],
            options: [{
				value: '组合风险收益',
				label: '组合风险收益'
			}],
            comparisonValue:'组合风险收益',
            preset_time: {
                radioValue: '1',
                startDate: dayjs().subtract(1, 'year').format('YYYY-MM-DD'),
                endDate: dayjs().format('YYYY-MM-DD')
            },
            params:{},
            tableHeader:[
                {
                    prop: 'date',
                    label: '净值日期',
                },
                {
                    prop: 'returnCum',
                    label: '组合累计净值',
                },
                {
                    prop: 'contrastReturn',
                    label: '沪深300',
                },
                {
                    prop: 'drawdown',
                    label: '回撤',
                },
                {
                    prop: 'excess',
                    label: '超额收益',
                }
                ]
        }
    },
    methods:{
        handleFormChange(val) {
            this.preset_time = val;
            this.getData(this.params);
		},
        getData(param){
            this.params = param;
            let chartDom = this.$refs['fund-performance-board-chart-container3'];
            chartDom?.getData({
                ...this.preset_time,
               
                ...param
            })
        },
        getTableData(val,name){
            this.tableHeader[2].label = name;
            this.tableData = val || [];
        },
        exportExcel(){
            let list = this.tableHeader.map((item) => {
				return {
					...item,
					format: ''
				};
			});
			filter_to_excel(list, this.tableData, '持仓净值走势');
        }
    },
}
</script>
<style lang="scss" scoped>
.fund-performance-wrapper {
    .select-form-wrapper {
        margin-bottom: 16px;
    }
    .content-table-wrapper {
        margin-bottom: 32px;
    }
}
</style>