<!--  -->
<template>
	<div class="industryTheme">
		<operator v-if="is_range" ref="operator" @resolveMathRange="resolveMathRange"></operator>
		<el-dropdown @command="command2">
			<el-button type="primary">
				<span>{{ haveName }}</span> <i class="el-icon-arrow-down el-icon--right"></i>
			</el-button>
			<el-dropdown-menu v-if="haveName.indexOf('模式') == -1" class="industry_theme_drapDown2" slot="dropdown">
				<el-dropdown-item v-for="(item, index) in quarterList" :command="item.value" :key="index">{{
					haveName.indexOf('主题') >= 0 ? item.lable : item.label
				}}</el-dropdown-item>
			</el-dropdown-menu>
			<el-dropdown-menu v-else class="industry_theme_drapDown2" slot="dropdown">
				<el-dropdown-item v-for="(item, index) in buyModeList" :command="item.value" :key="index">{{ item.label }}</el-dropdown-item>
			</el-dropdown-menu>
		</el-dropdown>
		<span v-if="haveName.indexOf('模式') == -1" :style="industry_name != '' ? 'margin-left:16px;margin-right:16px;' : 'width:10px'">{{
			quarterList.findIndex((item) => item.value == industry_name) >= 0
				? quarterList[quarterList.findIndex((item) => item.value == industry_name)][haveName.indexOf('主题') >= 0 ? 'lable' : 'label']
				: industry_nameT
		}}</span>
		<span v-else :style="industry_name != '' ? 'margin-left:16px;margin-right:16px;' : 'width:10px'">{{
			buyModeList.findIndex((item) => item.value == industry_name) >= 0
				? buyModeList[buyModeList.findIndex((item) => item.value == industry_name)][haveName.indexOf('主题') >= 0 ? 'lable' : 'label']
				: industry_nameT
		}}</span>
		<el-dropdown @command="command">
			<el-button type="primary">
				{{ iconFlag != '' ? (iconFlag == 'all' ? '所有' : iconFlag) : '运算符' }}<i class="el-icon-arrow-down el-icon--right"></i>
			</el-button>
			<el-dropdown-menu slot="dropdown">
				<el-dropdown-item command="all">所有</el-dropdown-item>
				<el-dropdown-item command="<">&lt;</el-dropdown-item>
				<el-dropdown-item command="=">=</el-dropdown-item>
				<el-dropdown-item command=">">&gt;</el-dropdown-item>
				<el-dropdown-item command="<=">&lt;=</el-dropdown-item>
				<el-dropdown-item command=">=">&gt;=</el-dropdown-item>
			</el-dropdown-menu>
		</el-dropdown>
		<div v-show="showBox" style="margin-left: 0px; display: flex; align-items: center">
			<!-- <div style="padding:5px;background:#ecf5ff;border:1px #f8f8f8;">
            {{iconFlag=='all'?'所有':iconFlag}}
        </div> -->
			<div style="margin-left: 16px">
				<el-input type="number" @input="inputChange" :placeholder="placeholder" v-model="input"></el-input>
			</div>
		</div>
	</div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import operator from '@/pages/filter/fund/beta/componentsFilter/components/operator.vue';

export default {
	props: {
		is_range: {
			type: Boolean,
			default: false
		},
		haveName: {
			type: String,
			default: ''
		},
		dataX: {
			type: Object,
			default: {}
		},
		placeholder: {
			type: String,
			default: '输入50,即持仓占比为50%'
		},
		indexFlag: {
			type: Number
		},
		baseIndexFlag: {
			type: Number
		},
		dataIndustry: {
			type: Object
		}
	},
	//import引入的组件需要注入到对象中才能使用
	components: { operator },
	data() {
		//这里存放数据
		return {
			iconFlag: '',
			showBox: false,
			input: '',
			buyModeList: [
				{
					label: '所有上升频率',
					value: '所有上升频率'
				},
				{
					label: '急速上升频率',
					value: '急速上升频率'
				},
				{
					label: '所有下跌频率',
					value: '所有下跌频率'
				},
				{
					label: '箱式频率',
					value: '箱式频率'
				}
			],
			quarterList: [
				{
					label: '周期',
					value: '周期'
				},
				{
					label: '制造',
					value: '制造'
				},
				{
					label: '医药',
					value: '医药'
				},
				{
					label: '消费',
					value: '消费'
				},
				{
					label: '金融地产',
					value: '金融地产'
				},
				{
					label: 'TMT',
					value: 'TMT'
				}
			],
			industry_name: '',
			industry_nameT: '',
			mathRange: { mathRange: 'avg' }
		};
	},
	//监听属性 类似于data概念
	computed: {},
	//监控data中的数据变化
	watch: {
		dataX(val) {
			// console.log(val);
			if (val.dataResult && val.dataResult.length > 0) {
				this.showBox = true;
				this.iconFlag = val.dataResult[0].flag;
				this.input = val.dataResult[0].value;
				this.industry_name = val.dataResult[0].industryValue;
				this.industry_nameT = val.dataResult[0].industryName;
				if (this.$refs['operator']) {
					this.$refs['operator'].getFlag(val.dataResult[0].mathRange);
				}
			}
		}
	},
	//方法集合
	methods: {
		resolveMathRange(obj) {
			this.mathRange = obj;
			this.resolveFather();
		},
		resolveFather() {
			this.$emit(
				'industryThemeChange',
				this.baseIndexFlag,
				this.indexFlag,
				this.input,
				this.iconFlag,
				this.industry_name,
				this.haveName.indexOf('模式') !== -1
					? this.buyModeList.findIndex((item) => item.value == this.industry_name) >= 0
						? this.buyModeList[this.buyModeList.findIndex((item) => item.value == this.industry_name)]['label']
						: ''
					: this.quarterList.findIndex((item) => item.value == this.industry_name) >= 0
					? this.quarterList[this.quarterList.findIndex((item) => item.value == this.industry_name)][
							this.haveName.indexOf('主题') >= 0 ? 'lable' : 'label'
					  ]
					: '',
				this.FUNC.isEmpty(this.industry_name) && this.FUNC.isEmpty(this.input) && this.FUNC.isEmpty(this.iconFlag),
				this.mathRange
			);
		},
		command(e) {
			this.iconFlag = e;
			this.industry_nameT =
				this.haveName.indexOf('模式') !== -1
					? this.buyModeList.findIndex((item) => item.value == this.industry_name) >= 0
						? this.buyModeList[this.buyModeList.findIndex((item) => item.value == this.industry_name)]['label']
						: ''
					: this.quarterList.findIndex((item) => item.value == this.industry_name) >= 0
					? this.quarterList[this.quarterList.findIndex((item) => item.value == this.industry_name)][
							this.haveName.indexOf('主题') >= 0 ? 'lable' : 'label'
					  ]
					: '';
			this.showBox = true;
			this.resolveFather();
		},
		command2(e) {
			this.industry_name = e;

			this.resolveFather();
		},
		command3(e) {
			this.yearqtr = e;
			this.resolveFather();
		},
		inputChange() {
			this.resolveFather();
		}
	},
	//生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	//生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		if (JSON.stringify(this.dataX) != '{}') {
			if (this.dataX.dataResult && this.dataX.dataResult.length > 0) {
				this.showBox = true;
				this.iconFlag = this.dataX.dataResult[0].flag;
				this.input = this.dataX.dataResult[0].value;
				this.industry_nameT = this.dataX.dataResult[0].industryName;
				this.industry_name = this.dataX.dataResult[0].industryValue;
				if (this.$refs['operator']) {
					this.$refs['operator'].getFlag(this.dataX.dataResult[0].mathRange);
				}
			}
		}
	},
	beforeCreate() {}, //生命周期 - 创建之前
	beforeMount() {}, //生命周期 - 挂载之前
	beforeUpdate() {}, //生命周期 - 更新之前
	updated() {}, //生命周期 - 更新之后
	beforeDestroy() {}, //生命周期 - 销毁之前
	destroyed() {}, //生命周期 - 销毁完成
	activated() {} //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style>
.industry_theme_drapDown2 {
	overflow: auto !important;
}
</style>
<style lang="scss" scoped>
//@import url(); 引入公共css类
.industryTheme {
	display: flex;
	align-items: center;
}
</style>
