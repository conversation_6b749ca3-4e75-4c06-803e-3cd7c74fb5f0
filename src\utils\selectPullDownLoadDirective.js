import Vue from 'vue';

const pullDownLoad = {
	inserted: function(el, binding) {
		let dom = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');

		dom.addEventListener('scroll', function() {
			const CONDITION = this.scrollHeight - this.clientHeight <= this. clientHeight;
			// const CONDITION = Math.abs(this.scrollHeight - this.clientHeight - this.scrollTop) < 1;
			if(CONDITION) {
				binding.value();
			}
		});
	}
};

export default {
	install(Vue) {
		Vue.directive('pullDownLoad', pullDownLoad);
	}
};



