<template>
	<div class="choose_email">
		<el-tree ref="tree" :data="data" show-checkbox node-key="id" :props="defaultProps" :expand-on-click-node="false">
			<div class="custom-tree-node" slot-scope="{ node, data }">
				<div>
					<span v-show="!data.edit">{{ data.label }}</span>
					<el-input v-show="data.edit" v-model="data.label" placeholder="" @change="submitInput(data)"></el-input>
				</div>
				<div>
					<el-button type="text" size="mini" @click="() => showEdit(data)" v-show="!node.isLeaf">
						<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
							<g clip-path="url(#clip0_3455_12467)">
								<path
									d="M3.02638 10.7486C3.05763 10.7486 3.08888 10.7454 3.12013 10.7408L5.74825 10.2798C5.7795 10.2736 5.80919 10.2595 5.83107 10.2361L12.4545 3.61263C12.469 3.59817 12.4805 3.581 12.4883 3.5621C12.4962 3.5432 12.5002 3.52294 12.5002 3.50247C12.5002 3.48201 12.4962 3.46175 12.4883 3.44284C12.4805 3.42394 12.469 3.40677 12.4545 3.39232L9.85763 0.793878C9.82794 0.764191 9.78888 0.748566 9.74669 0.748566C9.7045 0.748566 9.66544 0.764191 9.63576 0.793878L3.01232 7.41732C2.98888 7.44075 2.97482 7.46888 2.96857 7.50013L2.50763 10.1283C2.49243 10.212 2.49786 10.2981 2.52345 10.3792C2.54905 10.4604 2.59403 10.534 2.6545 10.5939C2.75763 10.6939 2.88732 10.7486 3.02638 10.7486ZM4.0795 8.02357L9.74669 2.35794L10.892 3.50325L5.22482 9.16888L3.83575 9.41419L4.0795 8.02357ZM12.7498 12.0611H1.24982C0.973254 12.0611 0.749817 12.2845 0.749817 12.5611V13.1236C0.749817 13.1923 0.806067 13.2486 0.874817 13.2486H13.1248C13.1936 13.2486 13.2498 13.1923 13.2498 13.1236V12.5611C13.2498 12.2845 13.0264 12.0611 12.7498 12.0611Z"
									fill="black"
									fill-opacity="0.85"
								/>
							</g>
							<defs>
								<clipPath id="clip0_3455_12467">
									<rect width="14" height="14" fill="white" />
								</clipPath>
							</defs>
						</svg>
					</el-button>
					<el-button type="text" size="mini" @click="() => deleteItem(node, data)">
						<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path
								d="M4.62476 1.87357H4.49976C4.56851 1.87357 4.62476 1.81732 4.62476 1.74857V1.87357H9.37476V1.74857C9.37476 1.81732 9.43101 1.87357 9.49976 1.87357H9.37476V2.99857H10.4998V1.74857C10.4998 1.197 10.0513 0.748566 9.49976 0.748566H4.49976C3.94819 0.748566 3.49976 1.197 3.49976 1.74857V2.99857H4.62476V1.87357ZM12.4998 2.99857H1.49976C1.22319 2.99857 0.999756 3.222 0.999756 3.49857V3.99857C0.999756 4.06732 1.05601 4.12357 1.12476 4.12357H2.06851L2.45444 12.2954C2.47944 12.8283 2.92007 13.2486 3.45288 13.2486H10.5466C11.081 13.2486 11.5201 12.8298 11.5451 12.2954L11.931 4.12357H12.8748C12.9435 4.12357 12.9998 4.06732 12.9998 3.99857V3.49857C12.9998 3.222 12.7763 2.99857 12.4998 2.99857ZM10.4263 12.1236H3.57319L3.19507 4.12357H10.8044L10.4263 12.1236Z"
								fill="black"
								fill-opacity="0.85"
							/>
						</svg>
					</el-button>
				</div>
			</div>
		</el-tree>
	</div>
</template>

<script>
import { putEmailGroup, deleteEmailGroup, deleteEmailItem } from '@/api/pages/NodeServer.js';
export default {
	data() {
		return {
			data: [],
			defaultProps: {
				children: 'children',
				label: 'label',
				email: 'email'
			}
		};
	},
	methods: {
		// 获取父组件传递数据
		getData(list) {
			this.data = [];
			list?.map((item) => {
				let index = this.data.findIndex((obj) => {
					return obj.id == item.group_id;
				});
				if (index == -1) {
					this.data.push({
						id: item.group_id,
						label: item.group_name,
						edit: false,
						children: [
							{
								id: item.email_id,
								email: item.email,
								label: item.name + `(${item.email})`
							}
						]
					});
				} else {
					this.data[index].children.push({
						id: item.email_id,
						email: item.email,
						label: item.name + `(${item.email})`
					});
				}
			});
		},
		// 显示编辑框
		showEdit(data) {
			let index = this.data.findIndex((obj) => {
				return obj.id == data.id;
			});
			this.data[index].edit = !this.data[index].edit;
		},
		// 提交改变的分组名称
		submitInput(data) {
			this.putEmailGroup(data);
			this.showEdit(data);
		},
		// 修改分组名称
		async putEmailGroup(value) {
			let postData = {
				name: value?.label,
				id: value?.id
			};
			let data = await putEmailGroup(postData);
			if (data?.mtycode == 200) {
				this.$message.success('修改成功');
				this.$emit('refreshList');
			} else {
				this.$message.warning(data?.mtymessage || '修改失败');
			}
		},
		// 删除
		deleteItem(node, data) {
			if (data?.children) {
				this.deleteEmailGroup(data?.id);
			} else {
				this.deleteEmailItem(data?.id);
			}
		},
		// 删除分组
		async deleteEmailGroup(id) {
			let data = await deleteEmailGroup({ id });
			if (data?.mtycode == 200) {
				this.$message.success(data?.mtymessage || '删除成功');
				this.$emit('refreshList');
			} else {
				this.$message.warning(data?.mtymessage || '删除失败');
			}
		},
		// 删除邮箱
		async deleteEmailItem(id) {
			let data = await deleteEmailItem({ id });
			if (data?.mtycode == 200) {
				this.$message.success(data?.mtymessage || '删除成功');
				this.$emit('refreshList');
			} else {
				this.$message.warning(data?.mtymessage || '删除失败');
			}
		},
		// 获取当前选中节点
		getCurrentKey() {
			return this.$refs['tree'].getCheckedKeys(true);
		},
		setCurrentKey(keys) {
			this.$refs['tree'].setCheckedKeys(keys);
		},
		renderContent(h, { node, data, store }) {
			if (node.isLeaf) {
				return (
					<div class="custom-tree-node" slot-scope="{ node, data }">
						<div>
							<span>{node.label}</span>
						</div>
						<div>
							<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path
									d="M4.62476 1.87357H4.49976C4.56851 1.87357 4.62476 1.81732 4.62476 1.74857V1.87357H9.37476V1.74857C9.37476 1.81732 9.43101 1.87357 9.49976 1.87357H9.37476V2.99857H10.4998V1.74857C10.4998 1.197 10.0513 0.748566 9.49976 0.748566H4.49976C3.94819 0.748566 3.49976 1.197 3.49976 1.74857V2.99857H4.62476V1.87357ZM12.4998 2.99857H1.49976C1.22319 2.99857 0.999756 3.222 0.999756 3.49857V3.99857C0.999756 4.06732 1.05601 4.12357 1.12476 4.12357H2.06851L2.45444 12.2954C2.47944 12.8283 2.92007 13.2486 3.45288 13.2486H10.5466C11.081 13.2486 11.5201 12.8298 11.5451 12.2954L11.931 4.12357H12.8748C12.9435 4.12357 12.9998 4.06732 12.9998 3.99857V3.49857C12.9998 3.222 12.7763 2.99857 12.4998 2.99857ZM10.4263 12.1236H3.57319L3.19507 4.12357H10.8044L10.4263 12.1236Z"
									fill="black"
									fill-opacity="0.85"
								/>
							</svg>
						</div>
					</div>
				);
			} else {
				if (data.edit) {
					return (
						<div class="custom-tree-node" slot-scope="{ node, data }">
							<div></div>
							<div class="icon_list">
								<div style="margin-right:16px">
									<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
										<g clip-path="url(#clip0_3455_12467)">
											<path
												d="M3.02638 10.7486C3.05763 10.7486 3.08888 10.7454 3.12013 10.7408L5.74825 10.2798C5.7795 10.2736 5.80919 10.2595 5.83107 10.2361L12.4545 3.61263C12.469 3.59817 12.4805 3.581 12.4883 3.5621C12.4962 3.5432 12.5002 3.52294 12.5002 3.50247C12.5002 3.48201 12.4962 3.46175 12.4883 3.44284C12.4805 3.42394 12.469 3.40677 12.4545 3.39232L9.85763 0.793878C9.82794 0.764191 9.78888 0.748566 9.74669 0.748566C9.7045 0.748566 9.66544 0.764191 9.63576 0.793878L3.01232 7.41732C2.98888 7.44075 2.97482 7.46888 2.96857 7.50013L2.50763 10.1283C2.49243 10.212 2.49786 10.2981 2.52345 10.3792C2.54905 10.4604 2.59403 10.534 2.6545 10.5939C2.75763 10.6939 2.88732 10.7486 3.02638 10.7486ZM4.0795 8.02357L9.74669 2.35794L10.892 3.50325L5.22482 9.16888L3.83575 9.41419L4.0795 8.02357ZM12.7498 12.0611H1.24982C0.973254 12.0611 0.749817 12.2845 0.749817 12.5611V13.1236C0.749817 13.1923 0.806067 13.2486 0.874817 13.2486H13.1248C13.1936 13.2486 13.2498 13.1923 13.2498 13.1236V12.5611C13.2498 12.2845 13.0264 12.0611 12.7498 12.0611Z"
												fill="black"
												fill-opacity="0.85"
											/>
										</g>
										<defs>
											<clipPath id="clip0_3455_12467">
												<rect width="14" height="14" fill="white" />
											</clipPath>
										</defs>
									</svg>
								</div>
								<div>
									<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
										<path
											d="M4.62476 1.87357H4.49976C4.56851 1.87357 4.62476 1.81732 4.62476 1.74857V1.87357H9.37476V1.74857C9.37476 1.81732 9.43101 1.87357 9.49976 1.87357H9.37476V2.99857H10.4998V1.74857C10.4998 1.197 10.0513 0.748566 9.49976 0.748566H4.49976C3.94819 0.748566 3.49976 1.197 3.49976 1.74857V2.99857H4.62476V1.87357ZM12.4998 2.99857H1.49976C1.22319 2.99857 0.999756 3.222 0.999756 3.49857V3.99857C0.999756 4.06732 1.05601 4.12357 1.12476 4.12357H2.06851L2.45444 12.2954C2.47944 12.8283 2.92007 13.2486 3.45288 13.2486H10.5466C11.081 13.2486 11.5201 12.8298 11.5451 12.2954L11.931 4.12357H12.8748C12.9435 4.12357 12.9998 4.06732 12.9998 3.99857V3.49857C12.9998 3.222 12.7763 2.99857 12.4998 2.99857ZM10.4263 12.1236H3.57319L3.19507 4.12357H10.8044L10.4263 12.1236Z"
											fill="black"
											fill-opacity="0.85"
										/>
									</svg>
								</div>
							</div>
						</div>
					);
				} else {
					return (
						<div class="custom-tree-node" slot-scope="{ node, data }">
							<div>
								<span>{node.label}</span>
							</div>
							<div class="icon_list">
								<div style="margin-right:16px">
									<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
										<g clip-path="url(#clip0_3455_12467)">
											<path
												d="M3.02638 10.7486C3.05763 10.7486 3.08888 10.7454 3.12013 10.7408L5.74825 10.2798C5.7795 10.2736 5.80919 10.2595 5.83107 10.2361L12.4545 3.61263C12.469 3.59817 12.4805 3.581 12.4883 3.5621C12.4962 3.5432 12.5002 3.52294 12.5002 3.50247C12.5002 3.48201 12.4962 3.46175 12.4883 3.44284C12.4805 3.42394 12.469 3.40677 12.4545 3.39232L9.85763 0.793878C9.82794 0.764191 9.78888 0.748566 9.74669 0.748566C9.7045 0.748566 9.66544 0.764191 9.63576 0.793878L3.01232 7.41732C2.98888 7.44075 2.97482 7.46888 2.96857 7.50013L2.50763 10.1283C2.49243 10.212 2.49786 10.2981 2.52345 10.3792C2.54905 10.4604 2.59403 10.534 2.6545 10.5939C2.75763 10.6939 2.88732 10.7486 3.02638 10.7486ZM4.0795 8.02357L9.74669 2.35794L10.892 3.50325L5.22482 9.16888L3.83575 9.41419L4.0795 8.02357ZM12.7498 12.0611H1.24982C0.973254 12.0611 0.749817 12.2845 0.749817 12.5611V13.1236C0.749817 13.1923 0.806067 13.2486 0.874817 13.2486H13.1248C13.1936 13.2486 13.2498 13.1923 13.2498 13.1236V12.5611C13.2498 12.2845 13.0264 12.0611 12.7498 12.0611Z"
												fill="black"
												fill-opacity="0.85"
											/>
										</g>
										<defs>
											<clipPath id="clip0_3455_12467">
												<rect width="14" height="14" fill="white" />
											</clipPath>
										</defs>
									</svg>
								</div>
								<div>
									<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
										<path
											d="M4.62476 1.87357H4.49976C4.56851 1.87357 4.62476 1.81732 4.62476 1.74857V1.87357H9.37476V1.74857C9.37476 1.81732 9.43101 1.87357 9.49976 1.87357H9.37476V2.99857H10.4998V1.74857C10.4998 1.197 10.0513 0.748566 9.49976 0.748566H4.49976C3.94819 0.748566 3.49976 1.197 3.49976 1.74857V2.99857H4.62476V1.87357ZM12.4998 2.99857H1.49976C1.22319 2.99857 0.999756 3.222 0.999756 3.49857V3.99857C0.999756 4.06732 1.05601 4.12357 1.12476 4.12357H2.06851L2.45444 12.2954C2.47944 12.8283 2.92007 13.2486 3.45288 13.2486H10.5466C11.081 13.2486 11.5201 12.8298 11.5451 12.2954L11.931 4.12357H12.8748C12.9435 4.12357 12.9998 4.06732 12.9998 3.99857V3.49857C12.9998 3.222 12.7763 2.99857 12.4998 2.99857ZM10.4263 12.1236H3.57319L3.19507 4.12357H10.8044L10.4263 12.1236Z"
											fill="black"
											fill-opacity="0.85"
										/>
									</svg>
								</div>
							</div>
						</div>
					);
				}
			}
		}
	}
};
</script>
<style lang="scss" scoped>
.choose_email {
	.custom-tree-node {
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;
		.icon_list {
			display: flex;
		}
	}
}
</style>
