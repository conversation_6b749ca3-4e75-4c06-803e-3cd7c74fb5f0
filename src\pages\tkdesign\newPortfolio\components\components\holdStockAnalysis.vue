<template>
    <div class="plate-wrapper fund-performance-board-wrapper">
        <combinationComponentHeader title="当前股票持仓分析" showMoreBtn>
            <template slot="right">
                
                
            </template>
        </combinationComponentHeader>
  
        <el-table border stripe :data="tableDataNow">
            <el-table-column  align="gotoleft" prop="name" show-overflow-tooltip label="名称">
                
            </el-table-column>
            <el-table-column  align="gotoleft" prop="date" show-overflow-tooltip label="代码"></el-table-column>
            <el-table-column  align="gotoleft" prop="nav" sortable label="持有方式"></el-table-column>
            <el-table-column  align="gotoleft" prop="nav" sortable label="占权益比">
                <templete slot-scope="scope">{{ scope.row.rate | fix2p }}</templete>
            </el-table-column>
            <el-table-column  align="gotoleft" sortable prop="cum_return" label="PB">
                <templete slot-scope="scope">{{ scope.row.cum_return | fix2p }}</templete>
            </el-table-column>
            <el-table-column  align="gotoleft" sortable prop="cum_return" label="PE">
                <templete slot-scope="scope">{{ scope.row.cum_return | fix2p }}</templete>
            </el-table-column>
            <el-table-column  align="gotoleft" sortable prop="cum_return" label="行业">
                <templete slot-scope="scope">{{ scope.row.cum_return | fix2p }}</templete>
            </el-table-column>
            <el-table-column  align="gotoleft" sortable prop="cum_return" label="个股收益贡献度">
                <templete slot-scope="scope">{{ scope.row.cum_return | fix2p }}</templete>
            </el-table-column>
           

            <template slot="empty">
                <el-empty image-size="160"></el-empty>
            </template>
        </el-table>
        <div style="display: flex;justify-content: space-between;gap:12px;align-items: center;margin-top: 20px;">
            <el-table border stripe :data="tableDataNow" style="flex: 1;">
            <el-table-column  align="gotoleft" prop="name" show-overflow-tooltip label="行业">
                
            </el-table-column>
            <el-table-column  align="gotoleft" prop="date" show-overflow-tooltip label="占已披露股票行业比"></el-table-column>
           

            <template slot="empty">
                <el-empty image-size="160"></el-empty>
            </template>
        </el-table>
        <div class="right" style="flex: 1;">
            <treeMapChart ref="fund-performance-board-chart-container" ></treeMapChart>
        </div>
    </div>
    </div>
</template>
<script>
import combinationComponentHeader from './combinationComponentHeader.vue';
import treeMapChart from '../chart/treeMapChart.vue'
export default {
    name:'holdStockAnalysis',
    components:{
        combinationComponentHeader,
        treeMapChart
    },
    data(){
        return {
            form:{},
            IndexStyleOption:[],
            tableData:[],
            legendName : {
                'indicatorPoints':'指标点位',
                'ttm':'TTM',
                'dividedIntoPoints':'分为点',
                'positiveStandardDeviation':'标准差（+1）',
                'negativeStandardDeviation':'标准差（-1）',
                'average':"平均值"
            },
            tableDataNow: [{
            date: '2016-05-02',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1518 弄',
            areaIncome:'df',
			description:'上海市普陀区金沙江路 1518 弄',
			rate:'100%',
			cum_return:'100%',
			nav:'1.0000',
			totalmv:'1.2000'
            }, {
            date: '2016-05-04',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1517 弄',
            areaIncome:'df',
			description:'上海市普陀区金沙江路 1518 弄',
			rate:'100%',
			cum_return:'100%',
			nav:'1.0000',
			totalmv:'1.2000'
            }, {
            date: '2016-05-01',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1519 弄',
            areaIncome:'df',
			description:'上海市普陀区金沙江路 1518 弄',
			rate:'100%',
			cum_return:'100%',
			nav:'1.0000',
			totalmv:'1.2000'
            }, {
            date: '2016-05-03',
            name: '王小虎',
            address: '上海市普陀区金沙江路 1516 弄',
            areaIncome:'df',
			description:'上海市普陀区金沙江路 1518 弄',
			rate:'100%',
			cum_return:'100%',
			nav:'1.0000',
			totalmv:'1.2000'
            }],
        }
    },
    mounted(){
        let chartDom = this.$refs['fund-performance-board-chart-container'];
        chartDom?.getData();
        let option;
        option = {
        title: {
        },
        tooltip: {
            trigger: 'axis'
        },
        legend: {
            // textStyle:{
            //     color:rgba(0, 0, 0, 0.65)
            // },
            selected: {
                // 不选中'TTM'
                'TTM': false
            },
            data: [
            {
                name: this.legendName.indicatorPoints,
                // 强制设置图形为圆。
                // icon: 'none',
                // 设置文本为红色
                // textStyle: {
                // color: 'red'
                // }
            },
            {
                name: this.legendName.ttm,
                icon: 'rect',
                // 设置文本为红色
                // textStyle: {
                // color: 'red'
                // }
            },
            {
                name: this.legendName.dividedIntoPoints,
                icon: 'rect',
                backgroundColor:'#4096ff'
            },
            {
                name: this.legendName.positiveStandardDeviation,
                // 强制设置图形为圆。
                // icon: 'circle',
                // 设置文本为红色
                // textStyle: {
                // color: 'red'
                // }
            },
            {
                name: this.legendName.average,
                // 强制设置图形为圆。
                // icon: 'circle',
                // 设置文本为红色
                // textStyle: {
                // color: 'red'
                // }
            },
            {
                name: this.legendName.negativeStandardDeviation,
                // 强制设置图形为圆。
                // icon: 'circle',
                // 设置文本为红色
                // textStyle: {
                // color: 'red'
                // }
            }
            ]
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: ['2024-06-03', '2024-06-03', '2024-06-03']
        },
        yAxis: [
            {
                name: this.legendName.dividedIntoPoints,
                type: 'value',
                interval:25,
            },
            {
                name: this.legendName.indicatorPoints,
                type: 'value',
                interval:355,
                scale: true,
            }
        ],
        series: [
            {
            name: this.legendName.indicatorPoints,
            type: 'line',
            lineStyle:{
                color:'#4096ff'
            },
            symbol: 'none',
            data: [1320, 1132, 601, 234, 120, 90, 20],
            yAxisIndex: 1
            },
            {
            name: this.legendName.ttm,
            type: 'line',
            symbol: 'none',
            lineStyle:{
                opacity:0
            },
            areaStyle: {},
            data: [1320, 1132, 21, 54, 260, 830, 710],
            yAxisIndex: 1
            },
            {
            name: this.legendName.dividedIntoPoints,
            type: 'line',
            symbol: 'none',
            lineStyle:{
                opacity:0
            },
            areaStyle: {
                color:'#4096ff',
                opacity:0.25,
            },
            data: [10, 13, 13, 45, 56, 57, 56],
            yAxisIndex: 0
            },
            {
            name: this.legendName.positiveStandardDeviation,
            type: 'line',
            symbol: 'none',
            lineStyle: {
                type: 'dashed'
            },
            data: [100,100,100,100,100,100,100],
            yAxisIndex: 1
            },
            {
            name: this.legendName.average,
            type: 'line',
            symbol: 'none',
            lineStyle: {
                type: 'dashed'
            },
            data: [30, 182, 434, 791, 390, 30, 10],
            yAxisIndex: 1
            },
            {
            name: this.legendName.negativeStandardDeviation,
            type: 'line',
            symbol: 'none',
            lineStyle: {
                type: 'dashed'
            },
            data: [30, 16, 13, 791, 390, 30, 10],
            yAxisIndex: 1
            }
        ]
        };
        // option && this.myChart.setOption(option);
    },
}
</script>
<style lang="scss" scoped>
.fund-performance-board-wrapper {
    .select-form-wrapper {
        margin-bottom: 16px;
    }
    .content-table-wrapper {
        margin-bottom: 32px;
    }
}

</style>