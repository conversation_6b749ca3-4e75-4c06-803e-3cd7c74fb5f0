.box_Board {
	padding: 0px 24px 16px 24px;
}
.header_box {
	margin-top: 16px;
	margin-bottom: 16px;
}
.header_unactive {
	font-size: 14px;
	font-weight: 400;
	line-height: 22px;
	text-align: left;
	color: rgba(0, 0, 0, 0.45);
}
.header_active {
	font-size: 14px;
	font-weight: 400;
	line-height: 22px;
	text-align: left;
	color: rgba(0, 0, 0, 0.85);
}
.plate-wrapper {
	border-radius: 4px;
    background: #FFF;
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.10);
    padding: 0 24px;
	margin-bottom: 16px;
}
.lq-date-picker {
	left: unset!important;
    right: 16px!important;
}
// .lq-date-picker .el-date-table td.end-date span {
//             border-radius: 0 50% 50% 0;
// }

// .lq-date-picker .el-date-table td.start-date span {
// 	border-radius: 50% 0 0 50%;
// }
// .lq-date-picker .el-date-table td.in-range div,.lq-date-picker .el-date-table td.in-range div:hover {
// 	background: rgba(255, 145, 3, 0.15);
// }
.lq-radio-group {
	.el-radio-button__orig-radio:checked+.el-radio-button__inner {
		color: #4096ff;
		background-color: unset;
		box-shadow: unset;
	}
	.el-input__inner,.el-button,.el-radio-button__inner {
		border-radius: unset;
		border: 1px solid #D9D9D9;
	}
	.lq-radio-button:first-child .el-input__inner,.lq-radio-button:first-child.el-button,.lq-radio-button:first-child .el-radio-button__inner {
		border-radius: 4px 0 0 4px;
		border-right: unset;
	}
	.lq-radio-button:last-child .el-input__inner,.lq-radio-button:last-child.el-button,.lq-radio-button:last-child .el-radio-button__inner {
		border-radius: 0 4px 4px 0;
		border-left: unset;
	}
	.lq-radio-button.is-active .el-input__inner{
        border: 1px solid #4096ff;
	}
	 .el-input__inner::-webkit-input-placeholder {
		color: rgba(0, 0, 0, 0.65);
		font-size: 14px;
	  }
	 .el-input__inner:-ms-input-placeholder {
		color: rgba(0, 0, 0, 0.65);
		font-size: 14px;
	  }
	  .el-input__inner::-ms-input-placeholder {
		color: rgba(0, 0, 0, 0.65);
		font-size: 14px;
	  }
	  .el-input__inner::placeholder {
		color: rgba(0, 0, 0, 0.65);
		font-size: 14px;
	  }
	.is-active {
	   .el-input__inner {
		  border-color: #4096ff;
	  }
	   .el-input__suffix {
		.el-input__icon {
		  color: #4096ff;
		}
	  }
	  .el-input__inner::-webkit-input-placeholder {
		color: #4096ff;
	  }
	   .el-input__inner:-ms-input-placeholder {
		color: #4096ff;
	  }
	   .el-input__inner::-ms-input-placeholder {
		color: #4096ff;
	  }
	    .el-input__inner::placeholder {
		color: #4096ff;
	  }
	}
}
.plain-active {
	.el-input__inner {
		border-color: #4096ff;
	}
	 .el-input__suffix {
		.el-input__suffix-inner {
			.el-input__icon {
				color: #4096ff;
			  }
		}
	}
	.el-input__inner::-webkit-input-placeholder {
	  color: #4096ff;
	}
	 .el-input__inner:-ms-input-placeholder {
	  color: #4096ff;
	}
	 .el-input__inner::-ms-input-placeholder {
	  color: #4096ff;
	}
	  .el-input__inner::placeholder {
	  color: #4096ff;
	}
}

.template-cascader {
    .el-cascader-node {
        height: unset;
    }
}
