const moment = require('moment');
var echarts = require('echarts');
/**
 *
 * @param {折线图}
 * color: 接收一个颜色数组(不传递使用默认) Array
 * tooltip: 接收一个function(不传递为无) Function
 * dataZoom: 接收一个布尔值(默认为false,不显示) Boolean
 * legend: 接收一个对象
 * xAxis: 接收一个对象({name(String,默认不显示),type(String,默认category),formatter(Function,默认为空),boundaryGap(Boolean,默认为false),isAlign(Boolean,默认为false),data(Array,必传)})
 * yAxis: 接收一个对象({name(String,默认不显示),type(String,默认value),formatter(Function,默认为空),data(Array,默认为空)})
 * series: 接收一个数组
 * @returns
 */
export function lineChartOption({ toolbox, color, tooltip, dataZoom, legend, xAxis, yAxis, series, grid }) {
	if (!xAxis || !xAxis?.length || typeof xAxis !== 'object') {
		return;
	}
	if (!series || !series.length || typeof series !== 'object') {
		return;
	}
	let option = {
		toolbox: {
			feature: {
				// dataZoom: {
				// 	yAxisIndex: 'none'
				// },
				// dataView: { readOnly: false },
				magicType: { type: ['line', 'bar'] },
				restore: {},
				saveAsImage: {}
			},
			top: -4,
			width: 104
		}
	};
	option['toolbox'] =
		toolbox == 'none'
			? {}
			: toolbox == false
			? {
					feature: {
						saveAsImage: { pixelRatio: 3 }
					},
					top: -4,
					width: 104
			  }
			: {
					feature: {
						magicType: { type: ['line', 'bar'] },
						restore: {},
						saveAsImage: { pixelRatio: 3 }
					},
					top: -4,
					width: 104
			  };
	option['color'] = color?.length
		? color
		: [
				'#4096ff',
				'#4096ff',
				'#7388A9',
				'#E85D2D',
				'#9A89FF',
				'#6C96F2',
				'#FD6865',
				'rgba(253, 156, 255, 1)',
				'#83D6AE',
				'rgba(174, 201, 254, 1)',
				'#88C9E9',
				'rgba(169, 244, 208, 1)',
				'#6F80DD',
				'rgba(154, 137, 255, 1)',
				'#FD9CFF',
				'rgba(219, 174, 255, 1)',
				'#FED0EE',
				'rgba(159, 212, 253, 1)',
				'#ED589D',
				'#FEAEAE',
				'rgba(208, 232, 255, 1)',
				'#FDD09F',
				'rgba(251, 227, 142, 1)',
				'#FBE38E',
				'#A9F4D0',
				'rgba(253, 208, 159, 1)',
				'#D0E8FF',
				'#9FD4FD',
				'rgba(254, 174, 174, 1)',
				'#AEC9FE',
				'#DBAEFF',
				'rgba(254, 208, 238, 1)',
				'#FA541C'
		  ];
	option['tooltip'] = {
		trigger: 'axis',
		padding: 0,
		backgroundColor: tooltip?.backgroundColor ? tooltip?.backgroundColor : '#ffffff',
		formatter: tooltip?.formatter ? tooltip?.formatter : undefined,
		axisPointer: {
			type: tooltip?.type ? tooltip?.type : 'line'
		}
	};
	dataZoom
		? (option['dataZoom'] = [
				{
					type: 'slider',
					zoomOnMouseWheel: false,
					preventDefaultMouseMove: false,
					start: 0,
					end: 100
				}
				// {
				// 	start: 0,
				// 	zoomOnMouseWheel: false,
				// 	preventDefaultMouseMove: false,
				// 	end: 100
				// }
		  ])
		: '';
	option['grid'] = {
		left: '16px',
		right: grid?.right
			? grid?.right
			: (xAxis?.[0]?.name && xAxis?.[0]?.show !== false) || (yAxis?.length >= 2 ? (yAxis?.[1]?.show == false ? false : true) : false)
			? '32px'
			: '0',
		bottom: dataZoom
			? '64px'
			: legend?.data?.length ||
			  yAxis.some((item) => {
					return item.name;
			  })
			? '38px'
			: '18px',
		top: grid?.top ? grid?.top : '18px', // 无图例18px
		containLabel: true
	};
	option['legend'] = Object.assign(
		{
			type: 'scroll',
			pageIcons: {
				horizontal: [
					'path://M11.7487 6.92214L6.30673 0.634973C6.15096 0.455009 5.85102 0.455009 5.69359 0.634973L0.251579 6.92214C0.049409 7.15658 0.231693 7.5 0.558148 7.5L11.4422 7.5C11.7686 7.5 11.9509 7.15658 11.7487 6.92214Z',
					'path://M0.251255 1.07786L5.69327 7.36503C5.84904 7.54499 6.14898 7.54499 6.30641 7.36503L11.7484 1.07786C11.9506 0.843416 11.7683 0.499999 11.4419 0.5L0.557824 0.5C0.231369 0.5 0.0490849 0.843417 0.251255 1.07786Z'
				]
			},
			icon: 'path://M63.6 489.6h896.7v44.8H63.6z',
			bottom: '0',
			itemGap: 40,
			right: 128,
			width: '80%',
			// left: 128
			left: 'center',
			pageButtonGap: 16
		},
		legend
	);
	option['xAxis'] = xAxis?.map((item) => {
		return {
			...item,
			show: item?.show == false ? false : true,
			offset: 8,
			nameGap: 8,
			name: item?.name ? item?.name : '',
			nameTextStyle: {
				fontFamily: 'PingFang',
				fontStyle: 'normal',
				fontWeight: 400,
				fontSize: 12,
				color: 'rgba(0, 0, 0, 0.65)'
			},
			type: item?.type ? item?.type : 'category',
			splitArea: item?.splitArea || undefined,
			boundaryGap: item?.boundaryGap ? item?.boundaryGap : false,
			axisLine: Object.assign(
				{
					lineStyle: {
						color: '#e9e9e9'
					}
				},
				item.axisLine || {}
			),
			axisLabel: Object.assign(
				{
					fontSize: 12,
					color: 'rgba(0, 0, 0, 0.65)',
					showMinLabel: true,
					showMaxLabel: true,
					hideOverlap: true,
					formatter: item?.formatter ? item?.formatter : undefined,
					rotate: item?.rotate ? item?.rotate : 0
				},
				item.axisLabel || {}
			),
			min: item.min === 'unset' ? undefined : 'dataMin',
			max: item.max === 'unset' ? undefined : 'dataMax',
			data: item?.isAlign
				? item?.data
				: item?.data?.map((obj, index) => {
						if (index == 0 || index == item.data?.length - 1) {
							return {
								value: obj,
								textStyle: { align: index == 0 ? 'left' : 'right' }
							};
						} else {
							return obj;
						}
				  })
		};
	});
	option['yAxis'] = yAxis?.map((item) => {
		return {
			show: item?.show == false ? false : true,
			offset: 8,
			nameGap: 20,
			scale: item?.scale == false ? false : true,
			type: item?.type ? item?.type : 'value',
			name: item?.name ? item?.name : '',
			nameTextStyle: {
				align: 'left',
				fontFamily: 'PingFang',
				fontStyle: 'normal',
				fontWeight: 400,
				fontSize: 12,
				color: 'rgba(0, 0, 0, 0.65)'
			},
			max: typeof item?.max == 'number' ? item.max : null,
			min: typeof item?.min == 'number' ? item.min : null,
			axisLine: {
				show: false
			},
			axisTick: {
				show: false
			},
			splitLine: {
				show: item?.splitLine == false ? false : true,
				lineStyle: {
					color: '#e9e9e9',
					type: 'dashed'
				}
			},
			axisLabel: {
				fontSize: 12,
				color: 'rgba(0, 0, 0, 0.65)',
				formatter: item?.formatter ? item?.formatter : undefined
			},
			data: item?.data ? item?.data : undefined
		};
	});
	option['series'] = series;
	return option;
}
