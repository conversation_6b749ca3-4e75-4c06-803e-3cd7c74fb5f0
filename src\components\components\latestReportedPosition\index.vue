<template>
	<div class="chart_one" v-show="show">
		<div style="display: flex; align-items: center; justify-content: space-between">
			<div class="title" style="margin-bottom: 24px">报告持仓分析</div>
			<div>
				<el-select v-model="quarter" placeholder="" @change="changeQuarter">
					<el-option v-for="item in quarterList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>
				<el-button class="print_show" icon="el-icon-document-delete" style="margin-left: 16px" @click="exportExcel">导出Excel</el-button>
			</div>
		</div>
		<div
			style="
				font-family: 'PingFang';
				font-style: normal;
				font-weight: 400;
				font-size: 14px;
				line-height: 22px;
				color: rgba(0, 0, 0, 0.65);
				margin-bottom: 16px;
			"
		>
			{{ name + newtime }}
		</div>
		<el-table
			v-loading="loading"
			:data="newholding"
			:default-sort="{ prop: 'weight', order: 'descending' }"
			class="table"
			style="height: 600px; overflow-y: scroll"
			ref="multipleTable"
			header-cell-class-name="table-header"
		>
			<el-table-column v-for="item in column" :key="item.value" align="gotoleft" :prop="item.value" :label="item.label" sortable>
				<template #header>
					<long-table-popover-chart
						v-if="item.popover"
						:data="formatTableData()"
						date_key="years"
						:data_key="item.value"
						:show_name="item.label"
					>
						<span>{{ item.label }}</span>
					</long-table-popover-chart>
					<span v-else>{{ item.label }}</span>
				</template>
				<template slot-scope="{ row }">
					<span>{{ item.format ? item.format(row[item.value]) : row[item.value] }}</span>
				</template>
			</el-table-column>
			<template slot="empty">
				<el-empty image-size="160"></el-empty>
			</template>
		</el-table>
	</div>
</template>

<script>
import { exportTitle, exportTable } from '@/utils/exportWord.js';
import { filter_json_to_excel } from '@/utils/exportExcel.js';
// 最新报告持仓分析
export default {
	name: 'latestReportedPosition',
	data() {
		return {
			loading: true,
			newholding: [],
			newtime: '',
			name: '',
			show: true,
			quarterList: [],
			quarter: '',
			column: [
				{
					label: '代码',
					value: 'stock_code',
					popover: false
				},
				{
					label: '名称',
					value: 'name',
					popover: false
				},
				{
					label: '占净值比',
					value: 'weight',
					format: this.fix3p,
					popover: true
				},
				{
					label: 'PB',
					vale: 'pb',
					format: this.fix3,
					popover: true
				},
				{
					label: 'PB 分位',
					value: 'pb_rank',
					format: this.fix3,
					popover: true
				},
				{
					label: 'PE',
					value: 'pe',
					format: this.fix3,
					popover: true
				},
				{
					label: 'PE 分位',
					value: 'pe_rank',
					format: this.fix3,
					popover: true
				},
				{
					label: '抱团数',
					value: 'rank',
					popover: true
				},
				{
					label: '行业',
					value: 'swname',
					popover: true
				}
			]
		};
	},
	methods: {
		// 获取持仓季度列表
		getDateList(data) {
			this.quarterList = data
				?.sort((a, b) => {
					return this.moment(b).isAfter(a) ? 1 : -1;
				})
				?.map((item) => {
					return { label: item, value: item };
				});
			this.quarter = this.quarterList?.[0]?.value;
			this.$emit('resolveFather', this.quarter);
		},
		// 切换季度
		changeQuarter() {
			this.loading = true;
			this.$emit('resolveFather', this.quarter);
		},
		getData(data) {
			this.loading = false;
			this.newholding = data;
			this.newtime = data?.[0]?.yearqtr;
		},
		hideLoading() {
			this.show = false;
		},
		formatTableData() {
			let data = [];
			this.newholding.map((item) => {
				let obj = { ...item };
				for (const key in item) {
					let format = this.column.find((obj) => {
						return obj.value == key;
					})?.format;
					if (format) {
						let val = format(item[key]);
						obj[key] = typeof val == 'string' ? (val.includes('%') ? val?.split('%')?.[0] * 1 : !isNaN(val) ? val * 1 : val) : val;
					}
				}
				data.push(obj);
			});
			return data;
		},
		fix3(value) {
			if (value == '--') {
				return value;
			} else return parseInt(value * 1000) / 1000;
		},
		fix3p(value) {
			if (value == '--') {
				return value;
			} else return parseInt(value * 1000) / 1000 + '%';
		},
		exportExcel() {
			let list = [
				{
					label: '代码',
					fill: 'header',
					value: 'stock_code'
				},
				{
					label: '名称',
					fill: 'header',
					value: 'name'
				},
				{
					label: '占净值比',
					value: 'weight',
					format: 'fix3b'
				},
				{
					label: 'PB',
					value: 'pb',
					format: 'fix3'
				},
				{
					label: 'PB 分位',
					value: 'pb_rank',
					format: 'fix3'
				},
				{
					label: 'PE',
					value: 'pe',
					format: 'fix3'
				},
				{
					label: 'PE 分位',
					value: 'pe_rank',
					format: 'fix3'
				},
				{
					label: '抱团数',
					value: 'number'
				},
				{
					label: '行业',
					value: 'swname'
				}
			];
			filter_json_to_excel(
				list,
				this.newholding.sort((a, b) => {
					return b.weight - a.weight;
				}),
				'最新报告持仓分析'
			);
		},
		createPrintWord() {
			let list = [
				{
					label: '代码',
					fill: 'header',
					value: 'stock_code'
				},
				{
					label: '名称',
					fill: 'header',
					value: 'name'
				},
				{
					label: '占净值比',
					value: 'weight',
					format: 'fix3b'
				},
				{
					label: 'PB',
					value: 'pb',
					format: 'fix3'
				},
				{
					label: 'PB 分位',
					value: 'pb_rank',
					format: 'fix3'
				},
				{
					label: 'PE',
					value: 'pe',
					format: 'fix3'
				},
				{
					label: 'PE 分位',
					value: 'pe_rank',
					format: 'fix3'
				},
				{
					label: '抱团数',
					value: 'number'
				},
				{
					label: '行业',
					value: 'swname'
				}
			];
			if (this.newholding.length) {
				return [
					...exportTitle('最新报告持仓分析'),
					...exportTable(
						list,
						this.newholding
							.sort((a, b) => {
								return b.weight - a.weight;
							})
							.slice(0, 10),
						'',
						true
					)
				];
			} else {
				return [];
			}
		}
	}
};
</script>

<style></style>
