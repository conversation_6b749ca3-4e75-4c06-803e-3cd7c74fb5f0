<!--  -->
<template>
  <div class="tablefundrank">
    <div style="margin-top: 24px">
      <div v-for="(item, index) in fund_hold"
           :key="index"
           style="margin-top: 16px">
        <div style="font-weight: 500; font-size: 16px; line-height: 24px; color: rgba(0, 0, 0, 0.65); opacity: 0.45">
          {{ item.value }}
        </div>
        <sTable :data="item.list"
                typeFlag="1"></sTable>
      </div>
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { FundRiskMsg } from '@/api/pages/tools/compare.js';
import sTable from '../SelfTable.vue';
export default {
  //import引入的组件需要注入到对象中才能使用
  props: {
    comparetype: {
      type: String,
      default: 'manager' //fund
    },
    id: {
      type: String,
      default: '30189741,30441407'
    },
    type: {
      type: String,
      default: 'equity'
    },
    name: {
      type: String,
      default: '萧楠,胡昕炜'
    },
    codelist: {
      type: String,
      default: '110011,000057'
    },
    codename: {
      type: String,
      default: '易方达中小盘,中银消费'
    }
  },
  filters: {
    fix3 (value) {
      return (value * 100).toFixed(2) + '%';
    },
    fix2 (value) {
      return Number(value).toFixed(2) + '亿';
    }
  },
  components: { sTable },
  data () {
    //这里存放数据
    return {
      managerholdcolumns: [
        { dataIndex: 'fund_name', key: 'fund_name', title: '基金名称' },
        { dataIndex: 'year', key: 'year', title: '年份', align: 'left', defaultSortOrder: 'ascend', sorter: (a, b) => a.year - b.year },
        {
          dataIndex: 'meter_cum_return',
          key: 'Meter_cum_return',
          title: '收益率',
          align: 'left',
          scopedSlots: { customRender: 'Meter_cum_return' },
          sorter: (a, b) => a.meter_cum_return - b.meter_cum_return
        },
        {
          dataIndex: 'rank_cum_return',
          key: 'Rank_cum_return',
          title: '排名（越大越前）',
          align: 'left',
          scopedSlots: { customRender: 'Rank_cum_return' },
          sorter: (a, b) => a.rank_cum_return - b.rank_cum_return
        },
        {
          dataIndex: 'meter_volatility',
          key: 'meter_volatility',
          title: '波动率',
          align: 'left',
          scopedSlots: { customRender: 'meter_volatility' },
          sorter: (a, b) => a.meter_volatility - b.meter_volatility
        },
        {
          dataIndex: 'rank_volatility',
          key: 'rank_volatility',
          title: '排名（越大越前）',
          align: 'left',
          scopedSlots: { customRender: 'rank_volatility' },
          sorter: (a, b) => a.rank_volatility - b.rank_volatility
        }
      ],
      fund_hold: []
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    getdata (val) {
      if (this.comparetype == 'manager') {
        this.getmanager(val);
      } else {
        this.gefunddata();
      }
    },
    async getmanager (val) {
      let codelist = '';
      let codename = '';
      if (val && val != '' && val != null) {
        // //console.log('12121')s
        for (let i = 0; i < val.length; i++) {
          codelist += val[i].value + ',';
          codename += val[i].fundname + ',';
        }

        let _this = this;
        let data = await FundRiskMsg({
          manager_code: this.id,
          fund_code: codelist,
          type: this.type,
          index_code: this.value,
          fund_name: codename,
          manager_name: this.name
        });
        if (data) {
          let now = this.moment().year();
          let last = this.moment().year() - 1;
          let laster = this.moment().year() - 2;
          let flag1 = 0;
          let flag2 = 0;
          let flag3 = 0;

          let dateKey = [String(now), String(last), String(laster)];
          let temp = [];
          let tempall = codelist.split(',');
          for (let i = 0; i < data.data.length; i++) {
            if (temp.indexOf(data.data[i][0].fund_code) < 0) {
              temp.push(data.data[i][0].fund_code);
            }
            if (tempall.indexOf(data.data[i][0].fund_code) < 0) {
              tempall.push(data.data[i][0].fund_code);
            }
          }
          let t = tempall.filter((item) => !temp.includes(item));
          // console.log('xxxxxx');
          for (let k = 0; k < t.length; k++) {
            if (t[k] != '') {
              let arryT = [];
              for (let j = 0; j < dateKey.length; j++) {
                arryT.push({
                  fund_code: t[k],
                  meter_cum_return: '--',
                  meter_maxdrawdown: '--',
                  meter_sharpe: '--',
                  meter_volatility: '--',
                  rank_cum_return: '--',
                  rank_maxdrawdown: '--',
                  rank_sharpe: '--',
                  rank_volatility: '--',
                  year: dateKey[j],
                  fund_name: this.$route.query.name.split(',')[this.$route.query.id.split(',').indexOf(t[k])]
                });
              }
              data.data.push(arryT);
            }
          }
          // //console.log(data)
          // //console.log('line')
          this.fund_hold = [];
          let dataList = data.data.sort((a, b) => {
            if (codelist.split(',').indexOf(a[0].fund_code) > codelist.split(',').indexOf(b[0].fund_code)) return 1;
            else return -1;
          });

          this.fund_hold = [
            { value: now, list: [['收益率'], ['收益率排名'], ['波动率'], ['波动率排名']] },
            { value: last, list: [['收益率'], ['收益率排名'], ['波动率'], ['波动率排名']] },
            { value: laster, list: [['收益率'], ['收益率排名'], ['波动率'], ['波动率排名']] }
          ];
          for (let i = 0; i < dataList.length; i++) {
            if (dataList[i].findIndex((item) => item.year == String(now)) > -1) {
              if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(now))].meter_cum_return)) {
                flag1 = 1;
                this.fund_hold[0].list[0].push(
                  (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(now))].meter_cum_return) * 100).toFixed(2) + '%'
                );
              } else {
                this.fund_hold[0].list[0].push('--');
              }
              if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(now))].rank_cum_return)) {
                flag1 = 1;
                this.fund_hold[0].list[1].push(
                  (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(now))].rank_cum_return) * 100).toFixed(2) + '%'
                );
              } else {
                this.fund_hold[0].list[1].push('--');
              }
              if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(now))].meter_volatility)) {
                flag1 = 1;
                this.fund_hold[0].list[2].push(
                  (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(now))].meter_volatility) * 100).toFixed(2) + '%'
                );
              } else {
                this.fund_hold[0].list[2].push('--');
              }
              if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(now))].rank_volatility)) {
                flag1 = 1;
                this.fund_hold[0].list[3].push(
                  (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(now))].rank_volatility) * 100).toFixed(2) + '%'
                );
              } else {
                this.fund_hold[0].list[3].push('--');
              }
            } else {
              this.fund_hold[0].list[0].push('--');
              this.fund_hold[0].list[1].push('--');
              this.fund_hold[0].list[2].push('--');
              this.fund_hold[0].list[3].push('--');
            }
            if (dataList[i].findIndex((item) => item.year == String(laster)) > -1) {
              if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(laster))].meter_cum_return)) {
                flag3 = 1;
                this.fund_hold[2].list[0].push(
                  (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(laster))].meter_cum_return) * 100).toFixed(2) +
                  '%'
                );
              } else {
                this.fund_hold[2].list[0].push('--');
              }
              if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(laster))].rank_cum_return)) {
                flag3 = 1;
                this.fund_hold[2].list[1].push(
                  (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(laster))].rank_cum_return) * 100).toFixed(2) + '%'
                );
              } else {
                this.fund_hold[2].list[1].push('--');
              }
              if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(laster))].meter_volatility)) {
                flag3 = 1;
                this.fund_hold[2].list[2].push(
                  (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(laster))].meter_volatility) * 100).toFixed(2) +
                  '%'
                );
              } else {
                this.fund_hold[2].list[2].push('--');
              }
              if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(laster))].rank_volatility)) {
                flag3 = 1;
                this.fund_hold[2].list[3].push(
                  (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(laster))].rank_volatility) * 100).toFixed(2) + '%'
                );
              } else {
                this.fund_hold[2].list[3].push('--');
              }
            } else {
              this.fund_hold[2].list[0].push('--');
              this.fund_hold[2].list[1].push('--');
              this.fund_hold[2].list[2].push('--');
              this.fund_hold[2].list[3].push('--');
            }
            if (dataList[i].findIndex((item) => item.year == String(last)) > -1) {
              if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(last))].meter_cum_return)) {
                flag2 = 1;
                this.fund_hold[1].list[0].push(
                  (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(last))].meter_cum_return) * 100).toFixed(2) + '%'
                );
              } else {
                this.fund_hold[1].list[0].push('--');
              }
              if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(last))].rank_cum_return)) {
                flag2 = 1;
                this.fund_hold[1].list[1].push(
                  (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(last))].rank_cum_return) * 100).toFixed(2) + '%'
                );
              } else {
                this.fund_hold[1].list[1].push('--');
              }
              if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(last))].meter_volatility)) {
                flag2 = 1;
                this.fund_hold[1].list[2].push(
                  (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(last))].meter_volatility) * 100).toFixed(2) + '%'
                );
              } else {
                this.fund_hold[1].list[2].push('--');
              }
              if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(last))].rank_volatility)) {
                flag2 = 1;
                this.fund_hold[1].list[3].push(
                  (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(last))].rank_volatility) * 100).toFixed(2) + '%'
                );
              } else {
                this.fund_hold[1].list[3].push('--');
              }
            } else {
              this.fund_hold[1].list[0].push('--');
              this.fund_hold[1].list[1].push('--');
              this.fund_hold[1].list[2].push('--');
              this.fund_hold[1].list[3].push('--');
            }
          }
          if (flag3 == 0) this.fund_hold.splice(2, 1);
          if (flag2 == 0) this.fund_hold.splice(1, 1);
          if (flag1 == 0) this.fund_hold.splice(0, 1);
        }
      }
    },
    gefunddata () { },
    createPrintWord () {
      let arr = [];
      let name = this.name.split(',');
      this.fund_hold.map((item) => {
        arr.push({ ...item, list: [['', ...name], ...item.list] });
      });
      let table = [];
      arr.map((item) => {
        table.push(...this.$exportWord.exportTitle(item.value), ...this.$exportWord.exportCompareTable(item.list));
      });
      return [...table];
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () { },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
</style>
