<template>
	<div>
		<el-dialog title="筛选" :visible.sync="visible" width="80%">
			<div>
				<filter-components ref="filterComponents" filter_type="pool" @resolveFather="getCodeList" :pool_id="info.code"></filter-components>
			</div>
			<!-- <div slot="footer">
				<el-button @click="visible = false">取 消</el-button>
				<el-button type="primary" @click="visible = false">确 定</el-button>
			</div> -->
		</el-dialog>
	</div>
</template>

<script>
import filterComponents from '@/pages/filter/fund/beta/filter.vue';
export default {
	components: { filterComponents },
	data() {
		return {
			visible: false,
			info: {}
		};
	},
	methods: {
		getData(info) {
			this.info = info;
			this.visible = true;
			this.$nextTick(() => {
				this.$refs['filterComponents'].Init();
			});
		},
		getCodeList(val) {
			this.visible = false;
			this.$emit(
				'resolveFather',
				val.map((v) => v.code)
			);
		}
	}
};
</script>

<style></style>
