* {
	margin: 0;
	padding: 0;
}

@font-face {
	font-family: 'PingFang';
	src:"./fonts/PingFangUsed.ttf";
	font-weight: normal;
	font-style: normal;
}
@font-face {
	font-family: 'DINC';
	src:"./fonts/PingFangUsed.ttf";
	font-weight: normal;
	font-style: normal;
}

html,
body,
#app,
.wrapper {
	width: 100%;
	height: 100%;
	overflow: hidden;
}

body {
	font-family: "PingFang", "Helvetica Neue",Helvetica,"Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif;
	/*   */
}

a {
	text-decoration: none;
	color: #4096ff !important;
}

.TIPBOX {
	/* 提示框显示隐藏 */
	/* display: none */
}
.fontcolorred {
	color: red;
	font-weight: 600;
}
.points {
	display: inline-block;
	width: 6px;
	height: 16px;
	background: red;
	box-shadow: 3px 3px 1px #ffb300;

	margin-right: 5px;
	margin-top: 4px;
}
.pointssearch {
	width: 6px;
	height: 16px;
	background: red;
	box-shadow: 3px 3px 1px #ffb300;
	margin-top: 3px;
	margin-right: 15px;
}
.content-box {
	/* position: absolute;
	left: 240px;
	right: 0; */
	/* top: 70px; */
	/* bottom: 0; */
	/* padding-bottom: 30px; */
	-webkit-transition: left 0.3s ease-in-out;
	transition: left 0.3s ease-in-out;
	background: #f9f5f9;
    overflow-x: auto;
    flex: 1;
    overflow-y: scroll;
}

.content {
	font-family: 'PingFang';
    width: 100%;
    max-width: 1720px;
	min-width: 1200px;
	background: #f9f5f9;
	margin: 0 auto;
	/* background: #e5e5e5; */
	/* overflow-y: scroll; */
	box-sizing: border-box;
	/* padding: 24px; */
}
/* 移除左侧菜单栏后不再需要这个样式 */
/* .content-collapse {
	left: 65px;
} */

.container {
	padding: 30px;
	background: #fff;
	border: 1px solid #ddd;
	border-radius: 5px;
}
.el-icon-download{
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
}
.el-icon-download:hover{
    color: #4096ff
}
.crumbs {
	margin: 10px 0;
	height: 18px;
}

.pagination {
	margin: 20px 0;
	text-align: right;
	height: 30px;
}

.plugins-tips {
	padding: 20px 10px;
	margin-bottom: 20px;
}
.fontsize50 {
	font-size: 50px;
	font-weight: 600;
}
.font-fgB {
	font-family: DINC;
}

/* 响应式样式 */
@media screen and (max-width: 768px) {
  .top-menu-container {
    overflow-x: auto;
    width: 100%;
  }

  .top-menu {
    white-space: nowrap;
  }

  .content {
    min-width: auto;
    padding: 0 10px;
  }

  .header-user-con {
    margin-left: auto;
  }

  .searchfundormanager .el-input__inner {
    width: 200px !important;
  }

  .platform-title {
    display: none !important;
  }

  .header .logo {
    min-width: auto !important;
  }

  .btn-bell, .btn-fullscreen {
    margin-left: 2px;
  }

  .user-name {
    margin-left: 5px;
    margin-right: 10px !important;
  }

  .el-dropdown-menu {
    min-width: 100px !important;
  }
}
.is-gotoleft .cell {
	text-align: left !important;
}
.el-table .el-table__cell.is-center .cell{
    justify-content: center;
}

.flex1{
	flex: 1;
}

.color4096FF{
	width: 42px;
	margin: auto;
	height: 15.4px;
	background: #4096FF;
	border: 1px solid #4096FF;
}
/* element表格排序伪元素去除 */
.noTableBorder .el-table .caret-wrapper:after{
    position: absolute;
    top:0.4em;
    height: 20px;
    right:-8px;
    content: '';
    width:2px;
    background: rgba(0, 0, 0, 0);
    border-radius: 0px;
}
/* element menu active样式 */
div .el-menu--horizontal>.el-menu-item.is-active{
    border-bottom: 2px solid #4096ff !important;
    color: #4096ff !important;
}
/* 顶部菜单样式 */
.el-menu--horizontal>.el-submenu.is-active .el-submenu__title {
    border-bottom: 2px solid #ffffff !important;
    color: #ffffff !important;
}
.videoIconDes{
	width: 20px; height: 20px; color: #FF9F43; cursor: pointer
}
.is-center .cell{
    text-align: center;
}
.cell {
	text-align: right;
}
.mgb20 {
	margin-bottom: 20px;
}
.tiptablebox {
	display: flex;
}

.move-enter-active,
.move-leave-active {
	transition: opacity 0.5s;
}
/* eltable不断变长问题 */
/* end */
.tablemargin {
	margin-left: 38px;
}
.yifont {
	width: 14px;
	height: 20px;
	font-size: 14px;
	font-family: PingFangSC-Regular, PingFang;
	font-weight: 400;
	color: #333333;
	line-height: 20px;
}

.icon_color {
	color: rgba(0, 0, 0, 0.45);
    background-color: #fff;
}
.move-enter,
.move-leave {
	opacity: 0;
}
.marginight20px {
	margin-right: 17.5px;
	margin-bottom: 10px;
}

/*BaseForm*/

.form-box {
	width: 600px;
}

.form-box .line {
	text-align: center;
}
.cell {
	font-size: 15px;
}

.searchinputtitle {
	text-align: right !important;
	display: table !important;
}


/*Upload*/

.pure-button {
	width: 150px;
	height: 40px;
	line-height: 40px;
	text-align: center;
	color: #fff;
	border-radius: 3px;
}

.g-core-image-corp-container .info-aside {
	height: 45px;
}

.width160 {
	width: 160px;
}
.firstpage {
	width: 1590px;
	margin-left: 44px;
}
.width170 {
	width: 170px;
}
/*VueEditor*/

.ql-container {
	min-height: 400px;
}

.ql-snow .ql-tooltip {
	transform: translateX(117.5px) translateY(10px) !important;
}

.editor-btn {
	margin-top: 20px;
}
/*markdown*/

.v-note-wrapper .v-note-panel {
	min-height: 500px;
}
/* 动态颜色匹配 */
/* 良中差优 */
.fontcolorliang {
	color: #fea93b;
	font-weight: 900;
}
.fontcolorzhong {
	color: #40c668;
	font-weight: 900;
}
.fontcoloryou {
	color: #f9825d;
	font-weight: 900;
}
.fontcolorcha {
	color: #36c461;
	font-weight: 900;
}
.fontcolorhighzhong {
	color: #dfb600;
	font-weight: 900;
}
.fontcolorpiandi {
	color: #6fc589;
	font-weight: 900;
}
/* 数字大小区分颜色 */
.Numbercomputedgreen {
	color: #36c461;
	font-weight: 900;
}
.marginleft10 {
	margin-left: 10px;
}
.mar1 {
	margin-left: 24px;
}
.mar2 {
	margin-left: 10px;
}
.mar3 {
	margin-left: 38px;
}
.Numbercomputedred {
	color: #e85d2d;
	font-weight: 900;
}
.fundbenchmarkcolor {
	font-size: 11px;
	margin: 2px;
	padding: 2px;
}
.height10 {
	height: 10px;
}
/* 低中高加黑 */
.highlow {
	background: black;
	color: white;
}
.nohighlow {
	color: black;
}
.width60 {
	width: 60px !important;
	margin-left: 10px;
}
.homebodyfontsize {
	font-size: 16px !important;
}
.searchpadding {
	height: 5px;
}

input::-webkit-input-placeholder {
	/* WebKit browsers */

	font-size: 13px;
}

input:-moz-placeholder {
	/* Mozilla Firefox 4 to 18 */

	font-size: 13px;
}

input::-moz-placeholder {
	/* Mozilla Firefox 19+ */

	font-size: 13px;
}

input:-ms-input-placeholder {
	/* Internet Explorer 10+ */

	font-size: 13px;
}
.zonghepingjiasix {
	margin: 20px;
	border: 1px solid rgba(223, 223, 223, 0.4);
	width: 456px;
	cursor: pointer;
}
.zonghepingjiasix2 {
	margin: 20px;
	border: 1px solid rgba(223, 223, 223, 0.4);
	width: 1448px;
	cursor: pointer;
}
.zonghepingjiasix666 {
	margin: 20px;
	border: 1px solid rgba(223, 223, 223, 0.4);
	width: 952px;
	cursor: pointer;
}
.zonghepingjia_4_2 {
	padding-top: 5px;
	padding-bottom: 5px;
	background: #f5f5f5;
	margin: 10px;
	text-align: center;
	width: 92px;
	cursor: pointer;
}
.zonghepingjia_4_2x {
	padding-top: 5px;
	padding-bottom: 5px;
	background: #f5f5f5;
	margin: 10px;
	text-align: center;
	width: 112px;
	cursor: pointer;
}
.zonghepingjia_4_2_zp {
	padding-top: 5px;
	padding-bottom: 5px;
	background: #f5f5f5;
	margin: 10px;
	text-align: center;
	width: 130px;
	cursor: pointer;
}
.zonghepingjia_3_2 {
	padding-top: 5px;
	padding-bottom: 5px;
	background: #f5f5f5;
	margin: 10px;
	text-align: center;
	width: 124px;
	cursor: pointer;
}
.zonghepingjia_2_2 {
	padding-top: 5px;
	padding-bottom: 5px;
	background: #f5f5f5;
	color: black;
	/* margin:3px; */
	border: 1px solid #fff;
	text-align: center;
	width: 100px;
	cursor: pointer;
}
.zonghepingjia_2_1 {
	padding-top: 5px;
	padding-bottom: 5px;
	background: #f5f5f5;
	margin: 10px;
	text-align: center;
	width: 187px;
	cursor: pointer;
}

.formatlength {
	width: 250px;
}
.marginleft20:hover {
	/* border:1px solid black; */
	cursor: pointer;
}
.margin20px {
	height: 20px;
}
.margin10 {
	margin: 8px;
}
.mar20 {
	margin: 20px;
}
.mar50 {
	margin: 50px;
}
.marginright20 {
	margin-right: 20px;
	margin-bottom: 10px;
}
.marginleft20 {
	margin-left: 20px;
}

.zonghetitile {
	font-size: 20px;
	margin-left: 10px;
	margin-top: 10px;
}
.borderbk {
	border: 1px solid #e9e9e9 !important;
}
.spana1 {
	height: 14px;
	font-size: 14px;
	font-family: PingFangSC-Regular, PingFang;
	font-weight: 400;
	color: #333333;
}
.paddingright10 {
	padding-right: 10px;
}
/* 表格滚动条样式 */
.el-table__body-wrapper::-webkit-scrollbar{
	width: 4px;
	height: 8px;
}
/* .content .analysis_main::-webkit-scrollbar{
    width: 5px;
	height: 10px;
} */
.content::-webkit-scrollbar {
    width: 8px;
   }
.content::-webkit-scrollbar-track {
    -webkit-border-radius: 2em;
    -moz-border-radius: 2em;
    border-radius:2em;
   }
.content::-webkit-scrollbar-thumb {
    -webkit-border-radius: 2em;
    -moz-border-radius: 2em;
    border-radius:2em;
   }
/*定义滑块 内阴影+圆角*/
*::-webkit-scrollbar {
	width: 8px;
	height: 10px;
} /*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
*::-webkit-scrollbar-track {
	background-color: rgba(255, 255, 255, 0);
} /*定义滚动条轨道 内阴影+圆角*/
*::-webkit-scrollbar-thumb {
	background-color: rgba(144, 147, 153, 0.5);
	border-radius: 6px;
} /*定义滑块 内阴影+圆角*/
.scrollbarHide::-webkit-scrollbar {
	display: none;
}
.scrollbarShow::-webkit-scrollbar {
	display: block;
}
.ve-table .ve-table-container.ve-table-border-around {
	border: 0px !important;
	border-bottom: 1px solid #eee !important;
}
/* 首页盒子 */
.firstpagebox {
	/* width: 514px; */
	/* margin-left: 24px; */
	border: 1px solid #e9e9e9;
}
.firstpagebox {
	/* width: 514px; */
	/* margin-left: 24px; */
	border: 1px solid #e9e9e9;
}
.stylebackfull {
	width: 42px;
	margin: auto;
	height: 15.4px;
	background: #4096FF;
	border: 1px solid #4096FF;
}
.stylebackfull4096ff {
	width: 42px;
	margin: auto;
	height: 15.4px;
	background: #4096ff;
	border: 1px solid #4096ff;
}
.stylebackfull9013fe {
	width: 42px;
	margin: auto;
	height: 15.4px;
	background: #9013fe;
	border: 1px solid #9013fe;
}
.stylebacknull {
	width: 42px;
	margin: auto;
	height: 15.4px;
	background: #ecf3fb;
	border: 1px solid #d2e7ff;
}
.marginleft21 {
	margin-left: 3px;
}
.textsizeyejibond {
	width: 57px;
	font-size: 14px;
	font-family: PingFangSC-Regular, PingFang;
	font-weight: 400;
	color: #5c6e8f;
	line-height: 18px;
	margin-top: 5px;
	margin-bottom: 10px;
}
.textsizeyeji {
	width: 70px;
	font-size: 14px;
	font-family: PingFangSC-Regular, PingFang;
	font-weight: 400;
	color: #5c6e8f;
	line-height: 18px;
	margin-top: 5px;
	margin-bottom: 10px;
}
.marginbottom10px {
	margin-bottom: 10px;
}
.managerbox1 {
	width: 267px;
	height: 200px;
}
.box3x2 {
	/* min-width: 125px; */
	height: 50px;
	background: #FFFFFF;
    box-shadow: 0px 2px 8px 2px rgba(0, 0, 0, 0.03);
    border-radius: 4px;
	/* margin-right: 10px; */
	text-align: center;
	margin-bottom: 8px;
	display: flex;
	flex-direction: column;
	justify-content: center;
}
.width150 {
	width: 130px;
}
.picmanager {
	width: 200px;
	height: 200px;
}
.boxstyle1 {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 104px;
	height: 30px;
	background: rgba(11, 191, 155, 0.1);
	border: 1px solid #0bbf9b;
	text-align: center;
	margin: 5px;
}
.boxstyle2 {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 104px;
	height: 30px;
	background: rgba(232, 93, 45, 0.1);
	border: 1px solid #e85d2d;
	text-align: center;
	margin: 5px;
}
.boxstyle11 {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 150px;
	height: 30px;
	background: rgba(11, 191, 155, 0.1);
	border: 1px solid #0bbf9b;
	text-align: center;
	margin: 5px;
}
.boxstyle22 {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 150px;
	height: 30px;
	background: rgba(232, 93, 45, 0.1);
	border: 1px solid #e85d2d;
	text-align: center;
	margin: 5px;
}

.boxstyle3 {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 104px;
	height: 30px;
	background: rgba(32, 118, 255, 0.1);
	border: 1px solid #4096ff;
	text-align: center;
	margin: 5px;
}
.boxclass1 {
	width: 100px;
	height: 14px;
	font-size: 14px;
	font-family: PingFangSC-Medium, PingFang;
	font-weight: 500;
	color: #0bbf9b;
	line-height: 14px;
}
.boxclass2 {
	width: 100px;
	height: 14px;
	font-size: 14px;
	font-family: PingFangSC-Medium, PingFang;
	font-weight: 500;
	color: #e85d2d;
	line-height: 14px;
}
.boxclass11 {
	width: 70px;
	height: 14px;
	font-size: 14px;
	font-family: PingFangSC-Medium, PingFang;
	font-weight: 500;
	color: #1890FF;
	line-height: 14px;
}
.boxclass22 {
	width: 70px;
	height: 14px;
	font-size: 14px;
	font-family: PingFangSC-Medium, PingFang;
	font-weight: 500;
	color: #e85d2d;
	line-height: 14px;
}
.boxclass3 {
	width: 100px;
	height: 14px;
	font-size: 14px;
	font-family: PingFangSC-Medium, PingFang;
	font-weight: 500;
	color: #4576e9;
	line-height: 14px;
}
.margin40px {
	height: 40px;
}
.headerbox1 {
	width: 723px;
	margin-left: 10px;
	margin-right: 10px;
}
.marginleft211 {
	margin-left: 6px;
}
.marginleft22 {
	margin-left: 12px;
}
.marginleft2111 {
	margin-left: 26px;
}
.marginright1 {
	margin-right: 46px;
}

.height400 {
	height: 400px !important;
}
.headerbox2 {
	width: 838px;
	box-shadow: 2px 6px 25px 0px rgba(239, 239, 239, 0.5);
	border-radius: 2px;
}
.detailmsg {
	margin-left: 10px;
}
.margin15 {
	font-weight: 400;
	color: #333333;
	margin-top: 15px;
	font-family: PingFangSC-Regular, PingFang;
}
.scorebox1 {
	width: 220px !important;
	height: 60px;
	background: #fafafa;
	border-radius: 2px;
	/* margin-top: 15px; */
}
.scorebox2 {
	/* width: 481px; */
	/* height: 135px; */
	background: #fafafa;
	border-radius: 2px;
	margin-top: 15px;
	margin-left: 15px;
}
.sirclepro {
	width: 60px;
	height: 40px;
	/* margin-left: 5px;
	margin-top: 3px; */
}
.circlenumber {
	display: flex;
	flex-direction: column;
	justify-content: center;
	text-align: center;
	width: 120px;
	height: 60px;
	font-size: 13px;
	font-family: PingFangSC-Regular, PingFang;
	font-weight: 400;
	color: #5c6e8f;
	line-height: 18px;
}
.frontrank {
	font-size: 20px;
	font-family: 'Helvetica Neue';
	font-weight: normal;
	font-weight: 500;
    color: #4096ff;
}
.ql-editor {
	font-size: 14px !important;
}
.progrsserhead {
	width: 200px;
	margin-left: 10px;
}
.width80pxheaderstyle {
	width: 80px !important;
	margin-left: 10px;
}
.echartsoneline {
	width: 100% !important;
	height: 500px !important;
}
.echartshalfline {
	width: 100% !important;
	height: 350px !important;
}
.guanzhubox {
	width: 452px;
	/* height: 43px; */
	/* background: #4096ff; */
	border-radius: 4px;
	margin-top: 20px;
	color: white;
}
.margintop20 {
	margin-top: 20px;
}
.datebox {
	width: 838px;
	height: 60px;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	align-self: center;
}
.linecolum {
	width: 1px;
	height: 8px;
	border: 1px solid #b7b7b7;
}
.linebox {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	margin-left: 5px;
	margin-right: 5px;
}
.yearbutton {
	font-size: 14px !important;
	font-family: PingFangSC-Regular, PingFang;
	font-weight: 400 !important;
	color: #333333;
}

.yearbuttonact {
	font-size: 14px !important;
	font-family: PingFangSC-Regular, PingFang;
	font-weight: 400 !important;
	color: #4096ff !important;
}
.height60 {
	margin-top: 10px;
}
.benchmarkline1 {
	width: 23px;
	height: 2px;
	background: #4096FF;
}
.benchmarkline2 {
	width: 23px;
	height: 2px;
	background: #929694;
}
.benchmarkline3 {
	width: 23px;
	height: 2px;
	background: #40bfdd;
}
.benchmarkline4 {
	width: 23px;
	height: 2px;
	background: #c2b12f;
}
.fonst {
	width: 55px;
	height: 14px;
	font-size: 12px;
	font-family: PingFangSC-Regular, PingFang;
	font-weight: 400;
	color: #666666;
	line-height: 14px;
}
.headbenchmarkbox {
	display: flex;
	margin-right: 10px;
	width: 180px;
	cursor: pointer;
	justify-content: center;
	align-items: center;
}
.headbenchmarkbox2 {
	display: flex;
	margin-right: 10px;
	width: 200px;
	justify-content: center;
	align-items: center;
}
.headwidth160 {
	width: 100px;
	margin-left: 5px;
}
.avgbox {
	width: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
}
.selfde {
	margin-right: 40px;
	font-size: 14px;
	font-family: PingFangSC-Regular, PingFang;
	font-weight: 400;
	color: #666666;
	line-height: 20px;
}
.avgde {
	margin-right: 40px;
	font-size: 14px;
	font-family: PingFangSC-Regular, PingFang;
	font-weight: 400;
	color: #666666;
	line-height: 20px;
}
.height20border {
	height: 20px;
}
.height10border {
	height: 10px;
}
.height40border {
	height: 40px;
}
.borderbottom2px {
	font-size: 16px;
}
.fs14 {
	font-size: 14px;
}
.borderbottom2px::after {
	content: '';
	width: 70px;
	height: 1px;
	display: block;

	/* border-bottom: 2px solid #40AFFF ; */
}
.borderbottom2px2::after {
	content: 'dashdashdksajdasdj';
	width: 70px;
	height: 1px;
	display: block;

	border-bottom: 2px solid #40AFFF;
}
.savemodel {
	border-top: 1px solid #e5e5e5;
	border-bottom: 1px solid #e5e5e5;
	padding: 20px;
}
.recommodfont {
	width: 190px;
	height: 16px;
	font-size: 20px;
	font-family: PingFangSC-Medium, PingFang;
	font-weight: 500;
	color: #333333;
	line-height: 16px;
	/* -webkit-background-clip: text;
    -webkit-text-fill-color: transparent; */
}
.modelbox {
	margin-left: 20px;
	width: 160px;
	height: 60px;
	text-align: center;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 20px;

	background: rgba(180, 190, 208, 0.12);
	border-radius: 3px;
	border: 1px solid #e6e6e6;
}
.modelnamebox {
	font-size: 16px;
	font-family: PingFangSC-Regular, PingFang;
	font-weight: 400;
	color: #333333;
}
.selfmodelbox {
	margin: 20px;
	width: 520px;
	background: rgba(255, 255, 255, 0.07);
	box-shadow: 0px 2px 8px 0px rgba(224, 224, 224, 0.5);
	border-radius: 3px;
}
.selfmodelde1 {
	font-size: 16px;
	font-family: PingFangSC-Regular, PingFang;
	font-weight: 400;
	color: #5c6e8f;
	margin-top: 10px;
	margin-left: 40px;
}
.selfmodelde2 {
	margin-left: 10px;
	font-size: 16px;
	font-family: PingFangSC-Regular, PingFang;
	font-weight: 400;
	color: #2e384a;
}
/**
  * 全局使用flexible.js自适应,
  * 目前所有的elementUI选框布局需要important强制覆盖
  * 推荐使用其他布局方式改进
  **/


/**
  * 通用表格样式
  * (根据UI基金公司设计图表格的样式)
  **/
.common-table-card {
	border: 1px solid #e9e9e9;
	border-radius: 5px;
}
.common-table-card .card-title-icon {
	display: inline-block;
	margin: 0 10px 0 5px;
	height: 6px;
	width: 6px;
	background: #40AFFF;
	vertical-align: middle;
}
.common-table-card .card-title-text {
	height: 50px;
	line-height: 50px;
	font-size: 16px;
	font-weight: 400;
	color: #333333;
}
.common-table-card .amount-font-color {
	color: #0bbf9b;
}

/* 组合管理 */
@font-face {
	font-family: PingFang;
	src: url('./fonts/PingFang Bold.ttf');
}
.comMainTitle{
	font-family: 'PingFang';
	font-style: normal;
	font-weight: 500;
	font-size: 20px;
	line-height: 28px;
	/* width: 80px; */
	height: 28px;
	padding-left: 32px;
	padding-top: 24px;
	padding-bottom: 24px;
	/* position: absolute; */
}
.comMainTitle2{
	font-family: 'PingFang';
	font-style: normal;
	font-weight: 500;
	font-size: 20px;
	line-height: 28px;
}
.comBox{
	/* position: absolute; */
	/* width: 1152px;
	height: 730px; */
	margin-left: 12px;
	margin-right: 12px;
	margin-top: 16px;
	/* margin-bottom: 24px; */

	background: #FFFFFF;
	box-shadow: 0px 5px 12px 4px rgba(0, 0, 18, 0.02);
	border-radius: 4px;
}
.comBoxContent{
	margin:24px;
    margin-bottom: 0;
}
/* start box page */
.headerFontSmall{
	font-family: 'PingFang';
	font-style: normal;
	font-weight: 400;
	font-size: 14px;
	line-height: 24px;
	color: rgba(0, 0, 0, 0.46);
}
.headerFontSmallBlack{
	font-family: 'PingFang';
	font-style: normal;
	font-weight: 400;
	font-size: 14px;
	color: rgba(0, 0, 0, 0.85);
}
.headerFontSmallBold{
	font-family: 'PingFang';
	font-style: normal;
	font-weight: 500;
	font-size: 14px;
	line-height: 24px;
	color: rgba(0, 0, 0, 0.85);
	width:40px;
	text-align: center;
	margin-top:8px;
	cursor: pointer;
}
.headerFontMid{
	font-family: 'PingFang';
	font-style: normal;
	font-weight: 500;
	font-size: 16px;
	line-height: 24px;
	color: rgba(0, 0, 0, 0.85);
}
.headerFontBig{
	font-family: 'PingFang';
	font-style: normal;
	font-weight: 500;
	font-size: 20px;
	line-height: 28px;
	color: rgba(0, 0, 0, 0.85);
}
.boxMain{
	background:white;
	padding: 16px 24px 24px 24px;
}
.boxTitleHeader{
	padding-bottom:16px;
}
.ContentMainBox{
	padding:16px 24px 24px 24px;
	background:#fff;
}
.HolePageBox{
	padding: 16px 24px 24px 24px;
}
.normalFont{
font-family: 'PingFang';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 22px;
color: rgba(0, 0, 0, 0.85);
}
.normalFontQ{
	font-family: 'PingFang';
font-style: normal;
	font-weight: 400;
font-size: 14px;
line-height: 22px;
color: rgba(0, 0, 0, 0.65);


}
.TitltCompare{
	font-style: normal;
	font-weight: 500;
	font-size: 16px;
	color: rgba(0, 0, 0, 0.85);
	flex: none;
	order: 0;
	flex-grow: 0;
}
.secondTitileCompare{
	margin-top:24px;
	font-style: normal;
	font-weight: 500;
	font-size: 20px;
	line-height: 28px;
	color: rgba(0, 0, 0, 0.85);
}
/* .el-table--small .el-table__cell{
	padding :8px 0px 0px 0px !important
} */
/* end box page */
/* 根据ui需求调整样式 */
.el-table .el-table__cell{
	padding:0px 0px !important;
}
.el-table__header .el-table__cell{
    background-color: #fafafa !important;
}
/* element dialog 标题样式 */
.el-dialog .el-dialog__header{
    padding: 16px 24px;
}
/* element dialog body样式 */
.el-dialog .el-dialog__body{
    padding: 0 24px 12px 24px;
}
/* element表头统一样式 */
.el-table tr th.el-table__cell>.cell{
    height: 48px;
	font-family: 'PingFang';
    background-color: #fafafa;
	font-weight: 500;
    font-size: 14px;
	color: rgba(0, 0, 0, 0.65);
    /* line-height: 54px; */
	line-height: 22px ;
    align-items: center;
    display:flex;
}
/* element表头带排序统一样式 */
.el-table tr th.is-sortable>.cell{
    display: flex;
    align-items: center;
    justify-content: space-between;
    line-height:14px;
}
/* element表头统一去除padding */
.el-table .has-gutter tr th{
    padding: 0;
}
/* element单行数据统一样式 */
.el-table .el-table__body-wrapper .el-table__row{
    height: 48px;
    border-bottom: 1px solid #FAFAFA;
}
/* element表格对齐方式 */
.el-table .el-table__body-wrapper .el-table__row td .is-gotoleft .cell{
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.el-table thead.is-group th.el-table__cell {
	background: #fafafa !important;
}
/* element表格筛选icon */
.el-table__column-filter-trigger .el-icon-arrow-down:before {
	font-family: 'iconfont' !important;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	font-size: 24px;
	font-style: normal;
	content: '\e601';
}
/* element表格筛选icon样式 */
.el-table__column-filter-trigger .el-icon-arrow-down {
	color: #c0c4cc;
	transform: scale(0.6);
}
.chart_one .el-table__column-filter-trigger {
	line-height: 22px;
	flex: 1;
	text-align: right;
}
.el-table .el-table-column--selection .cell:after{
    width:0px;
}
.el-table .el-table__header .is-right >.cell{
	justify-content: right;
}

.el-pagination.is-background .el-pager li {
	background-color: #fff !important;
	/* color: rgba(0, 0, 0, 0.65) !important; */
	border: 1px solid rgba(0, 0, 0, 0.15);
border-radius: 4px;
min-width: 32px !important;
}
.el-pager li{
	padding: 2px 5px !important;
	height: 32px !important;
}
.el-pager li.active {
	color: #4096ff !important;
	border: 1px solid #4096ff !important;
    border-radius: 4px;

}
.el-pagination.is-background .btn-prev{
	background-color: #fff !important;
}
.el-pagination.is-background .btn-next{
	background-color: #fff !important;
}
.el-pagination{
	align-items: center;
}
.el-pagination__jump{
	margin-left: 16px !important;
}
.el-popper[x-placement^=bottom] .popper__arrow{
	top: 5px !important;
	border-bottom-color:#fff
}
.el-popper[x-placement^=bottom] .popper__arrow::after{
	top:0px !important
}
.el-popper[x-placement^=top] .popper__arrow{
	bottom: 5px !important;
	border-bottom-color:#fff
}
.el-popper[x-placement^=top] .popper__arrow::after{
	bottom:0px !important
}
.el-select-group .el-select-dropdown__item{
	padding-left: 12px !important;
	padding-right: 12px !important;
}
.el-select-dropdown__item{
	height: 32px !important;
}
.el-select-group__title{
	padding-left: 12px !important;
}
.el-select-dropdown__item{
	padding:0 12px !important;
}
.el-cascader-node.is-active{
	background:rgba(255, 145, 3, 0.1) !important;
}
.el-cascader-node{
padding:0 30px 0 12px !important
}
.el-cascader-node:not(.is-disabled):focus, .el-cascader-node:not(.is-disabled):hover {
    background: #f5f5f5 !important;
}
.el-cascader-menu .el-icon-check:before {
    content: "";
}
.el-cascader-node__label{
	padding:0px !important
}.el-select-dropdown__item.hover, .el-select-dropdown__item:hover {
    background-color: #F5F5F5 !important;
}
.el-select-dropdown{
	box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12) !important;
}
.el-picker-panel{
	box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12) !important;

}
.el-select-dropdown{
	border:0px !important
}
.el-input__inner{
	padding:0 12px !important;
	border: 1px solid #d9d9d9 ;
	color:rgba(0, 0, 0, 0.65) !important;
	font-size: 14px !important;
}
.el-date-editor .el-input__inner{
	/* padding:0 12px !important; */
	padding-left:30px !important;
	border: 1px solid #d9d9d9 ;
	color:rgba(0, 0, 0, 0.65) !important;
	font-size: 14px !important;
}
.el-range-editor--small .el-range-separator {
    line-height: 24px;
    font-size: 13px;
}
.el-date-editor .el-range-separator{
	height: auto !important;
}
::-webkit-input-placeholder {
	/* WebKit browsers，webkit内核浏览器 */
	color: rgba(0, 0, 0, 0.25);
	font-family: PingFang;
	font-weight: 400;
	font-size: 14px !important;
	}
	:-moz-placeholder {
	/* Mozilla Firefox 4 to 18 */
	color: rgba(0, 0, 0, 0.25);
	font-family: PingFang;
	font-weight: 400;
	font-size: 14px  !important;;
	}
	::-moz-placeholder {
	/* Mozilla Firefox 19+ */
	color: rgba(0, 0, 0, 0.25);
	font-family: PingFang;
	font-weight: 400;
	font-size: 14px !important;;
	}
	:-ms-input-placeholder {
	/* Internet Explorer 10+ */
	color: rgba(0, 0, 0, 0.25);
	font-size: 14px  !important;;
	font-family: PingFang;
	font-weight: 400;
	}
	.el-select-dropdown__item.selected{
		background:rgba(255, 145, 3, 0.1)
	}
	.el-popper[x-placement^=bottom] {
		margin-top: 4px !important;
	}
	.el-popper[x-placement^=top] {
		margin-bottom: 4px !important;
	}
	.el-range-editor--small .el-range-input {
		font-size: 14px !important;
	}
	.el-button--primary{
		border:0px !important;
		border-radius: 4px !important;
	}
	/* .el-button:hover{
		background:#4096ff;

	}
	.el-button--small :hover{
		background:#4096ff !important
	} */
	/* .el-button--primary:focus{
		background: #1c8dd4 !important;
	border-color: #1c8dd4 !important;
	} */
.el-button--primary:hover {
	background: #4096ff !important;
	border-color: #4096ff !important;
}
.el-button--primary{
	/* opacity: 1 !important;
	transition: .3s !important */
}
.el-button--primary:active{
	/*其他样式*/
	/* opacity: 0 !important;
	box-shadow: 10px 60px 60px 6px #000 !important;
	width: 160px !important;
	transition: .3s !important; */
	background: #1c8dd4 !important;
	/* content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:#4096FF;
  background-image: radial-gradient(circle, #ccc 10%, transparent 10.1%);
  transform: scale(10);
  opacity: 0;
  transition: all .6s; */
  }
  /*点击*/
  .el-button--primary:active::after{
	/* box-shadow: none !important;
	width: 360px !important;
	opacity: 0.4 !important; */
	/*transition: 0s !important; 取消过渡*/
	/* transform: scale(0);
  background:#606d83;
  opacity: .5;
  transition: 0s; */
  }
  /* driver */
  #driver-page-overlay{
	  opacity:0.1 !important;

  }
  #driver-highlighted-element-stage{
	  background-color: rgba(255,255,255,0.3) !important;
  }
