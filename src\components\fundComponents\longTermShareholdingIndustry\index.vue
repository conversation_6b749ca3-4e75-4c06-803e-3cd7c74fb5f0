<template>
	<div class="chart_one">
		<div v-loading="loading">
			<div class="title">{{ title }}</div>
			<div class="flex_card">
				<div style="page-break-inside: avoid; position: relative" class="small_template">
					<div class="title" style="position: absolute">长期持股行业次数</div>
					<div class="charts_center_class">
						<v-chart
							ref="industryConfigPerformanceNumber"
							:options="numberPie"
							v-loading="numberLoading"
							element-loading-text="暂无数据"
							element-loading-spinner="el-icon-document-delete"
							element-loading-background="rgba(239, 239, 239, 0.5)"
							class="charts_analysis_class"
							autoresize
						/>
					</div>
				</div>
				<div style="page-break-inside: avoid; position: relative" class="small_template">
					<div class="title" style="position: absolute">长期持股行业权重</div>
					<div class="charts_center_class">
						<v-chart
							ref="industryConfigPerformanceWeight"
							:options="weightPie"
							v-loading="weightLoading"
							element-loading-text="暂无数据"
							element-loading-spinner="el-icon-document-delete"
							element-loading-background="rgba(239, 239, 239, 0.5)"
							class="charts_analysis_class"
							autoresize
						/>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { exportTitle, exportChart } from '@/utils/exportWord.js';
// 长期持股行业分布
import VChart from 'vue-echarts';
export default {
	name: 'longTermShareholdingIndustry',
	components: { VChart },
	data() {
		return {
			title: '长期持股行业分布',
			loading: false,
			weightLoading: false,
			numberLoading: false,
			weightPie: {},
			numberPie: {},
			styleColor: ['#4096ff', '#4096ff', '#7388A9', '#6F80DD', '#6C96F2', '#FD6865', '#83D6AE', '#88C9E9', '#ED589D', '#FA541C'],
			stocks_number: 0,
			sotcks_weight: []
		};
	},
	methods: {
		getData(weight, number, data) {
			this.stocks_number = 0;
			data?.map((item) => {
				this.stocks_number = this.stocks_number + item.times;
			});
			this.formatAvgWeight(data);
			this.getIndustryWeight(weight);
			this.getIndustryNumber(number);
		},
		// 计算各个行业的平均权重
		formatAvgWeight(data) {
			this.sotcks_weight = [];
			data?.map((item) => {
				let index = this.sotcks_weight.findIndex((obj) => {
					return obj.label == item.swname;
				});
				if (index == -1) {
					this.sotcks_weight.push({
						label: item.swname,
						value: item.weight * item.times,
						num: item.times
					});
				} else {
					this.sotcks_weight[index].value = this.sotcks_weight[index].value + item.weight * item.times;
					this.sotcks_weight[index].num = this.sotcks_weight[index].num + item.times;
				}
			});
			this.sotcks_weight = this.sotcks_weight.map((item) => {
				return {
					...item,
					weight: item.value / item.num
				};
			});
		},
		// 获取长期持股行业分布图-次数-number
		getIndustryNumber(data) {
			let series = [];
			data?.weight.forEach((item, index) => {
				series.push({
					value: item,
					name: `${data?.swname[index] == '--' ? '其他' : data?.swname[index]}` + `(${Math.floor(this.stocks_number * item)}次)`
				});
			});
			let options = {
				color: this.styleColor,
				tooltip: {
					trigger: 'item',
					formatter: (item) => {
						return `${item.data.name} ${(parseFloat(item.data.value) * 100).toFixed(2)}%`;
					}
				},
				toolbox: {
					feature: {
						saveAsImage: {}
					},
					top: -4,
					width: 104
				},
				legend: {
					show: false
				},
				series: [
					{
						type: 'pie',
						radius: '70%',
						data: series,
						label: {
							fontSize: '16px'
						},
						emphasis: {
							itemStyle: {
								shadowBlur: 10,
								shadowOffsetX: 0,
								shadowColor: 'rgba(0, 0, 0, 0.2)'
							}
						}
					}
				]
			};
			this.numberPie = options;
			this.numberLoading = false;
		},
		// 获取长期持股行业分布图-权重-weight
		getIndustryWeight(data) {
			let series = [];
			data?.weight.forEach((item, index) => {
				let i = this.sotcks_weight.findIndex((obj) => {
					return obj.label == data?.swname[index];
				});
				series.push({
					value: item,
					name: `${data?.swname[index] == '--' ? '其他' : data?.swname[index]}` + `(${this.sotcks_weight[i].weight.toFixed(2)}%)`
				});
			});
			let options = {
				color: this.styleColor,

				tooltip: {
					trigger: 'item',
					formatter: (item) => {
						return `${item.data.name} ${(parseFloat(item.data.value) * 100).toFixed(2)}%`;
					}
				},
				toolbox: {
					feature: {
						saveAsImage: {
							pixelRatio: 3
						}
					},
					top: -4,
					width: 104
				},
				legend: {
					show: false
				},
				series: [
					{
						type: 'pie',
						radius: '70%',
						data: series,
						label: {
							fontSize: '16px'
						},
						emphasis: {
							itemStyle: {
								shadowBlur: 10,
								shadowOffsetX: 0,
								shadowColor: 'rgba(0, 0, 0, 0.2)'
							}
						}
					}
				]
			};
			this.weightPie = options;
			this.weightLoading = false;
		},
		createPrintWord() {
			this.$refs['industryConfigPerformanceNumber'].mergeOptions({ toolbox: { show: false } });
			this.$refs['industryConfigPerformanceWeight'].mergeOptions({ toolbox: { show: false } });
			let height = this.$refs['industryConfigPerformanceNumber'].$el.clientHeight;
			let width = this.$refs['industryConfigPerformanceNumber'].$el.clientWidth;
			let chart1 = this.$refs['industryConfigPerformanceNumber'].getDataURL({
				type: 'png',
				pixelRatio: 2,
				backgroundColor: '#fff'
			});
			let chart2 = this.$refs['industryConfigPerformanceWeight'].getDataURL({
				type: 'png',
				pixelRatio: 2,
				backgroundColor: '#fff'
			});
			this.$refs['industryConfigPerformanceNumber'].mergeOptions({ toolbox: { show: true } });
			this.$refs['industryConfigPerformanceWeight'].mergeOptions({ toolbox: { show: true } });
			return [
				...exportTitle('长期持股行业次数'),
				...exportChart(chart1, { width, height }),
				...exportTitle('长期持股行业权重'),
				...exportChart(chart2, { width, height })
			];
		}
	}
};
</script>

<style></style>
