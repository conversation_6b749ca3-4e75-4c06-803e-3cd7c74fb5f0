<template>
	<div>
		<div v-loading="managermodelload" id="fundManagerEvaluation">
			<div class="fixed-height-short">
				<div style="display: flex; align-items: center; justify-content: space-between">
					<div style="cursor: pointer; font-szie: 16px; font-weight: 500">
						<strong class="borderbottom2px">基金经理评价: {{ manager_name }}</strong>
					</div>
					<i class="el-icon-download" @click="exportImage"></i>
				</div>
				<div style="display: flex">
					<div class="charts_center_class" style="justify-content: start; flex: 1">
						<v-chart
							v-loading="managermodelload"
							element-loading-text="暂无数据"
							element-loading-spinner="el-icon-document-delete"
							element-loading-background="rgba(239, 239, 239, 0.5)"
							class="charts_two_class"
							style="width: 282px"
							autoresize
							ref="fundManagerEvaluation"
							:options="managermodel"
						/>
					</div>
					<div style="flex: 1; display: flex; flex-direction: column; justify-content: center">
						<div style="display: flex; flex-wrap: wrap; justify-content: space-around">
							<div class="box3x2" style="flex: 1">
								行业宽度<br /><span>{{ pingjia_kuandu }}</span>
							</div>
							<div class="box3x2" style="flex: 1">
								行业稳定度<br /><span>{{ pingjia_wendingdu }}</span>
							</div>
						</div>
						<div style="display: flex; flex-wrap: wrap; justify-content: space-around">
							<div class="box3x2" style="flex: 1">
								盈利要求<br /><span>{{ pingjia_nengli }}</span>
							</div>
							<div class="box3x2" style="flex: 1">
								规模要求<br /><span>{{ pingjia_guimo }}</span>
							</div>
						</div>
						<div style="display: flex; flex-wrap: wrap; justify-content: space-around">
							<div class="box3x2" style="flex: 1">
								风格<br /><span>{{ pingjia_fengge }}</span>
							</div>
							<div class="box3x2" style="flex: 1">
								换手率<br /><span>{{ pingjia_huanshoulv }}</span>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
// 基金经理评价
import { exportTitle, exportTable, exportChart } from '@/utils/exportWord.js';

import { getBasicInfo, getCapabilityInfo } from '@/api/pages/Analysis.js';
export default {
	name: 'fundManagerEvaluation',
	data() {
		return {
			pingjia_kuandu: '',
			manager_name: '',
			manager_code: '',
			pingjia_nengli: '',
			pingjia_guimo: '',
			pingjia_wendingdu: '',
			pingjia_fengge: '',
			pingjia_huanshoulv: '',
			pingjia_dongtaitiaojie: '',
			pingjia_shenglvwen: '',
			pingjia_fengkong: '',
			pingjia_wendingxing2: '',
			pingjia_guanliguimo: '',
			managermodel: {},
			managermodelload: true,
			info: {}
		};
	},
	methods: {
		// 获取当前管理基金经理
		async getBasicInfo() {
			let data = await getBasicInfo({
				code: this.info.code,
				type: this.info.type,
				flag: this.info.flag,
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			if (data.mtycode == 200) {
				this.manager_name = data.data?.manager_name;
				this.manager_code = data.data?.manager_code;
			}
		},
		// 获取基金经理能力
		async getCapabilityInfo() {
			let data = await getCapabilityInfo({
				codes: [this.manager_code],
				type: this.info.type,
				flag: [2],
				// start_date: this.info.start_date,
				// end_date: this.info.end_date,
				item: ['大类资产能力']
			});
			console.log('getCapabilityInfo', data);
		},
		async getData(info) {
			this.info = info;
			await this.getBasicInfo();
			this.getCapabilityInfo();
			// this.pingjia_kuandu = data.pingjia_kuandu;
			// this.pingjia_nengli = data.pingjia_nengli;
			// this.pingjia_wendingdu = data.pingjia_wendingdu;
			// this.pingjia_guimo = data.pingjia_guimo;
			// this.pingjia_fengge = data.pingjia_fengge;
			// this.pingjia_huanshoulv = data.pingjia_huanshoulv;
			// this.pingjia_dongtaitiaojie = data.pingjia_dongtaitiaojie;
			// this.pingjia_shenglvwen = data.pingjia_shenglvwen;
			// this.pingjia_fengkong = data.pingjia_fengkong;
			// this.pingjia_wendingxing2 = data.pingjia_wendingxing2;
			// this.pingjia_guanliguimo = data.pingjia_guanliguimo;
			this.formatManagerChart(data.chart);
		},
		// 格式化图数据
		formatManagerChart(chart) {
			this.managermodelload = false;
			let tempscroe = [];
			let temparrmanager = chart;
			for (let k = 0; k < temparrmanager?.length; k++) {
				if (temparrmanager[k] == '低' || temparrmanager[k] == '低') {
					tempscroe.push(25);
				} else if (temparrmanager[k] == '差' || temparrmanager[k] == '偏低') {
					tempscroe.push(40);
				} else if (temparrmanager[k] == '中') {
					tempscroe.push(60);
				} else if (temparrmanager[k] == '良' || temparrmanager[k] == '偏高') {
					tempscroe.push(80);
				} else if (temparrmanager[k] == '优' || temparrmanager[k] == '高' || temparrmanager[k] == '极高') {
					tempscroe.push(100);
				} else {
					tempscroe.push(0);
				}
			}
			this.managermodel = {
				radar: {
					splitLine: {
						// (这里是指所有圆环)坐标轴在 grid 区域中的分隔线。
						lineStyle: {
							color: '#0000001F',
							opacity: 0.3,
							// 分隔线颜色
							width: 2,
							// 分隔线线宽
							type: [5, 10]
						}
					},
					splitArea: {
						// 坐标轴在 grid 区域中的分隔区域，默认不显示。
						show: true,
						areaStyle: {
							// 分隔区域的样式设置。
							color: ['rgb(255,255,255)']
							// color: ['rgb(246,250,255)', 'rgb(246,250,255)']
							// 分隔区域颜色。分隔区域会按数组中颜色的顺序依次循环设置颜色。默认是一个深浅的间隔色。
						}
					},
					axisLine: {
						// (圆内的几条直线)坐标轴轴线相关设置
						lineStyle: {
							color: '#0000001F',
							// 坐标轴线线的颜色。
							width: 0.3,
							// 坐标轴线线宽。
							type: 'solid'
							// 坐标轴线线的类型。
						}
					},
					name: {
						textStyle: {
							fontSize: '12px'
						}
					},

					center: ['50%', '50%'],
					// radius: '70px',
					// shape: 'circle',
					indicator: [
						{
							name: '收益能力',
							max: 100,
							color: '#000000A6'
						},
						{
							name: '风控',
							max: 100,
							color: '#000000A6'
						},
						{
							name: '胜率稳定性',
							max: 100,
							color: '#000000A6'
						},
						{
							name: '管理规模',
							max: 100,
							color: '#000000A6'
						},
						{
							name: this.info.type == 'bond' ? '择时' : '动态调节',
							max: 100,
							color: '#000000A6'
						}
					]
				},
				series: [
					{
						itemStyle: {
							// 单个拐点标志的样式设置。
							normal: {
								borderColor: '#4096FF',
								// 拐点的描边颜色。[ default: '#000' ]
								borderWidth: 3
								// 拐点的描边宽度，默认不描边。[ default: 0 ]
							}
						},
						lineStyle: {
							// 单项线条样式。
							normal: {
								color: '#4096FF',
								opacity: 0.5 // 图形透明度
							}
						},
						areaStyle: {
							// 单项区域填充样式
							normal: {
								color: 'rgb(223,236,255)', // 填充的颜色。[ default: "#000" ]
								opacity: 0.7
							}
						},
						name: '基金经理能力',
						type: 'radar',
						data: [
							{
								value: tempscroe,
								name: '基金经理能力'
							}
						]
					}
				]
			};
			// setTimeout(() => {
			// 	console.log(
			// 		this.$refs['picone'].getConnectedDataURL({
			// 			type: 'jpg',
			// 			pixelRatio: 2,
			// 			backgroundColor: '#fff'
			// 		})
			// 	);
			// }, 1000);
		},
		godetailfront(val) {
			let temp = null;
			let tempcode = null;
			for (let i = 0; i < this.managernow.length; i++) {
				if (val.split(':')[1] == this.managernow[i].name) {
					temp = this.managernow[i].name;
					tempcode = this.managernow[i].manager_code;
				}
			}
			if (temp != null) {
				this.godetail(tempcode, temp);
			}
		},
		computedclassfontcolor(val) {
			if (val == '低') return 'fontcolorcha';
			if (val == '良') return 'fontcolorliang';
			if (val == '中') return 'fontcolorzhong';

			if (val == '优' || val == '高') return 'fontcoloryou';
			if (val == '差') return 'fontcolorcha';
		},
		computedclassfontcolorlowhigh(val) {
			if (val == '低') return 'fontcolorzhong';
			if (val == '偏低') return 'fontcolorpiandi';
			if (val == '中') return 'fontcolorhighzhong';
			if (val == '偏高') return 'fontcolorliang';
			if (val == '高') return 'fontcoloryou';
			if (val == '极高') return 'fontcolorred';
		},
		exportImage() {
			this.html2canvas(document.getElementById('fundManagerEvaluation'), { scale: 3 }).then(function (canvas) {
				let base64Str = canvas.toDataURL('image/png');
				let aLink = document.createElement('a');
				aLink.style.display = 'none';
				aLink.href = base64Str;
				aLink.download = '基金经理详情.jpg';
				// 触发点击-然后移除
				document.body.appendChild(aLink);
				aLink.click();
				document.body.removeChild(aLink);
			});
		},
		createPrintWord() {
			let height = this.$refs['fundManagerEvaluation'].$el.clientHeight;
			let width = this.$refs['fundManagerEvaluation'].$el.clientWidth;
			let chart = this.$refs['fundManagerEvaluation'].getDataURL({
				type: 'png',
				pixelRatio: 3,
				backgroundColor: '#fff'
			});
			let list = [
				{ label: '行业宽度', value: 'pingjia_kuandu' },
				{ label: '行业稳定度', value: 'pingjia_wendingdu' },
				{ label: '盈利要求', value: 'pingjia_nengli' },
				{ label: '规模要求', value: 'pingjia_guimo' },
				{ label: '风格', value: 'pingjia_fengge' },
				{ label: '换手率', value: 'pingjia_huanshoulv' }
			];
			let { pingjia_kuandu, pingjia_wendingdu, pingjia_nengli, pingjia_guimo, pingjia_fengge, pingjia_huanshoulv } = this;
			let data = [{ pingjia_kuandu, pingjia_wendingdu, pingjia_nengli, pingjia_guimo, pingjia_fengge, pingjia_huanshoulv }];
			return [
				...exportTitle('基金经理评价:' + this.managernamepingjia),
				...exportChart(chart, { width, height }),
				...exportTable(list, data)
			];
		}
	}
};
</script>

<style></style>
