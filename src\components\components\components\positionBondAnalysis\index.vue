<template>
	<div class="chart_one">
		<div style="display: flex; justify-content: space-between; align-items: center">
			<div class="title">持仓债券分析</div>
			<div>
				<el-select v-model="value" placeholder="" @change="changeYearQtr">
					<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>
			</div>
		</div>
		<el-table
			style="margin-top: 16px"
			class="fund-analysis-table"
			:data="bondTopTenData"
			v-loading="bondTopTenLoading"
			max-height="400px"
			:default-sort="{ prop: 'value', order: 'descending' }"
		>
			<el-table-column
				v-for="item in column"
				:key="item.value"
				:prop="item.value"
				:label="item.label"
				:sortable="item.sortable ? item.sortable : false"
				align="gotoleft"
			>
				<template #header>
					<long-table-popover-chart
						v-if="item.popover"
						:data="bondTopTenData"
						date_key="bond_name"
						:data_key="item.value"
						:show_name="item.label"
						:formatter="
							function (val) {
								return val;
							}
						"
					>
						<span>{{ item.label }}</span>
					</long-table-popover-chart>
					<span v-else>{{ item.label }}</span>
				</template>
			</el-table-column>
			<template slot="empty">
				<el-empty image-size="160"></el-empty>
			</template>
		</el-table>
	</div>
</template>

<script>
export default {
	data() {
		return {
			bondTopTenLoading: true,
			value: '',
			options: [],
			bondTopTenData: [],
			column: [
				{
					label: '债券名称',
					value: 'bond_name',
					popover: false
				},
				{
					label: '债券代码',
					value: 'bond_code',
					popover: false
				},
				{
					label: '个券类型',
					value: 'bondtypelevel2desc',
					popover: false
				},
				{
					label: '行业',
					value: 'swlevel1',
					popover: false
				},
				{
					label: '个券评级',
					value: 'creditrating',
					popover: false
				},
				{
					label: '占总净值比例',
					value: 'ratioinN',
					sortable: true,
					format: this.fix2b,
					popover: true
				},
				{
					label: '持券数量(万)',
					value: 'holdings',
					sortable: true,
					format: this.fix4,
					popover: true
				},
				{
					label: '持仓市值(亿)',
					value: 'value',
					sortable: true,
					format: this.fix8,
					popover: true
				}
			],
			allData: []
		};
	},
	methods: {
		// 取消loading
		hideLoading() {
			this.bondTopTenLoading = false;
			this.bondTopTenData = [];
		},
		// 切换季度
		changeYearQtr(val) {
			this.value = val;
			this.splitData();
		},
		// 切割数据
		splitData() {
			this.bondTopTenData = this.allData
				.filter((item) => {
					return item.yearqtr == this.value;
				})
				.sort((a, b) => Number(b.value) - Number(a.value));
		},
		// 获取债券前十大table
		getData(data) {
			this.options = [];
			this.allData = data.map((item) => {
				let formatData = { ...item };
				for (const key in item) {
					let index = this.column.findIndex((obj) => {
						return obj.value == key;
					});
					if (index != -1) {
						if (this.column[index]?.format) {
							formatData[key] = this.column[index]?.format(item[key]);
						}
					}
				}
				return formatData;
			});
			this.allData.map((item) => {
				let index = this.options.findIndex((obj) => {
					return obj.value == item.yearqtr;
				});
				if (index == -1) {
					this.options.push({
						label: item.yearqtr,
						value: item.yearqtr
					});
				}
			});
			this.options.sort((a, b) => {
				return this.moment(this.moment(a.value, 'YYYY QQ').format()).isAfter(this.moment(b.value, 'YYYY QQ').format()) ? -1 : 1;
			});
			this.value = this.options?.[0]?.value;
			this.splitData();

			this.bondTopTenLoading = false;
		},
		// 分化
		fix2b(val) {
			return val * 1 ? (val * 1).toFixed(2) * 1 : '--';
		},
		// 万化
		fix4(val) {
			return val * 1 ? (val / 10 ** 4).toFixed(2) * 1 : '--';
		},
		// 亿化
		fix8(val) {
			return val * 1 ? (val / 10 ** 8).toFixed(2) * 1 : '--';
		}
	}
};
</script>

<style></style>
