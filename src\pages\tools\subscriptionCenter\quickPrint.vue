<template>
	<div class="report_template">
		<div v-show="data.length != 0">
			<el-table
				:data="data"
				border
				default-expand-all
				style="width: 100%; min-height: calc(100vh - 293px)"
				row-key="key"
				:tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
			>
				<el-table-column v-for="item in column" :key="item" :prop="item.value" :label="item.label" align="gotoleft">
					<template slot-scope="{ row }">
						<div v-show="item.value == 'setting'">
							<el-link
								:disabled="row['is_file_over'] !== 1"
								v-show="!row['children'] && row['print_type'] == 1"
								style="margin-right: 16px"
								@click="downloadFile(row, 'word')"
								>下载Word</el-link
							>
							<el-link
								:disabled="row['is_file_over'] !== 1 && row['print_type'] !== 1"
								v-show="!row['children'] && row['print_type'] == 1"
								style="margin-right: 16px"
								@click="downloadFile(row, 'pdf')"
								>下载PDF</el-link
							>
							<el-link v-show="!row['children'] && row['print_type'] == 2" style="margin-right: 16px" @click="goPrint(row)"
								>手动下载</el-link
							>
							<div v-if="row['children'] && row['key'].indexOf('first') != -1">
								<el-link style="margin-right: 16px" @click="deleteItem(row)">删除</el-link>
								<el-link
									v-if="
										row['children'].every((obj) => {
											return obj.is_file_over == 1;
										})
									"
									style="margin-right: 16px"
									@click="addItem(row)"
									>添加</el-link
								>
							</div>
						</div>
						<div v-show="item.value == 'is_file_over'">
							<div v-show="row[item.value] == false" @click="get(row)"><i class="el-icon-loading"></i>下载中</div>
							<div v-show="row[item.value] == true">下载完成</div>
						</div>

						<span v-show="item.value != 'setting' && item.value != 'is_file_over'">{{ row[item.value] }}</span>
					</template>
				</el-table-column>
				<template slot="empty">
					<el-empty image-size="160"></el-empty>
				</template>
			</el-table>
		</div>
		<el-dialog title="添加打印项" v-loading="dialogVisibleLoading" :visible.sync="dialogVisible" width="1000px">
			<div>
				<subscription-select ref="subscriptionSelect" v-show="isPushInfos"></subscription-select>
			</div>
			<div slot="footer">
				<el-button @click="dialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="submitItemInfos">确 定</el-button>
			</div>
		</el-dialog>
		<add-subscription
			ref="addSubscription"
			style="width: 100%; min-height: calc(100vh - 305px)"
			v-show="data.length == 0"
			:quick-print="true"
			@resolveFather="resolveFather"
		></add-subscription>
	</div>
</template>

<script>
import { mapMutations, mapState, Store } from 'vuex';
import addSubscription from './addSubscription.vue';

import subscriptionSelect from './components/subscriptionSelect.vue';

import {
	getSubscriptionList,
	deleteSubscriptionList,
	getReportUrl,
	putTaskStatus,
	addTaskList,
	postSubscriptionList
} from '@/api/pages/NodeServer.js';
// 获取池子详情
import { getPoolDetail } from '@/api/pages/SystemMixed.js';
export default {
	components: { addSubscription, subscriptionSelect },
	data() {
		return {
			id: null,
			data: [],
			user_id: localStorage.getItem('id'),
			dialogVisible: false,
			column: [
				{
					label: '名称',
					value: 'name'
				},
				{
					label: '任务状态',
					value: 'is_file_over'
				},
				{
					label: '操作',
					value: 'setting'
				}
			],
			dialogVisibleLoading: false
		};
	},
	computed: {
		...mapState(['postData']),
		//下载完毕后才会显示该组件
		isPushInfos() {
			return this.data.every((item) => {
				return item.children.every((val) => {
					return val.is_file_over == 1;
				});
			});
		}
	},
	mounted() {
		this.$event.$on('printOver', () => {
			this.getData();
		});
	},
	methods: {
		get(row) {
			console.log('rrrow', row);
		},
		// 获取数据
		async getData() {
			this.loading = true;
			console.log('addSubscription', this.$refs['addSubscription']);
			this.$refs['addSubscription']?.getReportTemplateList();
			let data = await getSubscriptionList({
				user_id: this.user_id,
				is_quick: true
			});
			if (data?.mtycode == 200) {
				this.id = data?.data?.data?.[0]?.id;
				let arr = [];
				let list = [];
				data?.data?.data.map((obj, index) => {
					console.log('obj.list', obj);
					obj.list.map(async (item, i) => {
						if (item.flag == 'pool') {
							let pool = await getPoolDetail({ id: item.code, have: '' });
							if (pool?.mtycode == 200) {
								arr.push({
									id: item.id,
									key: 'second' + i,
									name: item.name,
									print_type: item.print_type,
									is_file_over: item.is_file_over,
									children: pool.data?.table.map((val, j) => {
										return {
											key: 'third' + j,
											name: val.name,
											code: val.code,
											is_file_over: item.is_file_over
										};
									})
								});
								return list;
							}
						} else {
							if (item.print_type == 1) {
								arr.push({
									id: item.id,
									key: 'second' + i,
									user_id: item.user_id,
									name: item.name,
									print_type: item.print_type,
									code: item.code,
									is_file_over: item.is_file_over
								});
							} else {
								arr.push({
									id: item.id,
									key: 'second' + i,
									user_id: item.user_id,
									name: item.name,
									print_type: item.print_type || 2,
									code: item.code,
									is_file_over: 1 //print_type为2  写死为1。后端不做处理
								});
							}
						}
					});
					list.push({
						subscription_id: obj.id,
						print_type: obj.print_type,
						key: 'first' + index,
						name: obj.name || '打印队列',
						is_file_over: obj.list.some((item) => {
							if (item.print_type == 2) {
								return true;
							} else {
								return item.is_file_over == 1;
							}
						}),
						// obj.list.forEach((val) => {
						// 	if (val.print_type == 2) {
						// 		return (val.is_file_over = true);
						// 	}
						// }),
						// obj.list.every((val) => {
						// console.log('vvvak', val);
						// return val.is_file_over == true;
						// if (val.print_type == 2) {
						// 	// console.log('666');
						// 	return true;
						// } else {
						// 	return val.is_file_over == true;
						// }
						// }),
						children: arr
					});
					console.log('llist', list);
				});

				this.data = list;
				console.log(list);
			} else {
				this.data = [];
			}
		},
		// 删除订阅
		async deleteItem(item) {
			let data = await deleteSubscriptionList({ id: item.subscription_id });
			if (data?.mtycode == 200) {
				this.$message.success(data?.mtymessage || '删除成功');
				this.getData();
			} else {
				this.$message.warning(data?.mtymessage || '删除失败');
			}
		},
		// 创建完成
		resolveFather() {
			// setInterval(() => {
			//刷新数据
			this.getData();
			// }, 5000);
		},
		// 手动下载
		goPrint(row) {
			alphaGo(row.code, row.name, this.$route.path);
			//   this.$router.push(
			//     `/reportCenter?subcriptionId=${this.id}&code=${row.code}`
			//   );
		},
		// 添加基金
		addItem(row) {
			this.dialogVisible = true;
			console.log(row);
		},
		// 提交新增任务列表
		async submitItemInfos() {
			this.dialogVisibleLoading = true;
			let subscription_list_send_info = this.$refs['subscriptionSelect'].getItemInfos();

			let obj = [...subscription_list_send_info];
			let obj2 = [];
			for (let index = 0; index < obj.length; index++) {
				const item = obj[index];
				if (item.info.flag == 'pool') {
					let data = await getPoolDetail({ id: item.info.code, have: '' });
					if (data.mtycode == 200) {
						let list = data.data.table.map((val) => {
							return {
								code: val.code,
								name: val.name,
								type: val.type,
								flag: 'fund'
							};
						});
						obj2.push(...list);
					}
				} else {
					obj2.push(item.info);
				}
			}
			let data = await addTaskList({
				subscription_list_send_info: obj2,
				subscription_id: this.id
			});
			if (data?.mtycode == 200) {
				this.$message.success(data?.mtymessage || '添加成功');
				this.dialogVisible = false;
				this.getData();
			} else {
				this.$message.warning(data?.mtymessage || '添加失败');
			}
			this.dialogVisibleLoading = false;
		},
		// 下载文件
		async downloadFile(row, type) {
			console.log(row);
			let data = await getReportUrl({
				id: row.id,
				user_id: row.user_id,
				name: row.name,
				type
			});
			if (data?.mtycode == 200) {
				var a = document.createElement('a');
				a.href = data?.data.url;
				a.download = row.name + '.' + (type == 'word' ? 'docx' : 'pdf');
				document.body.appendChild(a);
				a.click();
			}
		},
		// 新增订阅
		addSubscription() {
			this.$router.push('/addSubscription');
		},
		// 新增订阅任务
		async postSubscriptionList(postData) {
			let data = await postSubscriptionList(postData);
			console.log('data', data);
			if (data?.mtycode == 200) {
				this.$message.success(data?.mtymessage || '添加成功');
				if (this.quickPrint) {
					this.$emit('resolveFather');
				} else {
					this.goBack();
				}
			} else {
				this.$message.warning(data?.mtymessage || '添加失败');
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.report_template {
	margin: 0 12px;
}
</style>
