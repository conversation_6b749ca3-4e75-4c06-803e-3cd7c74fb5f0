<template>
	<div id="positionStyle" class="position_style">
		<analysis-card-title title="全持仓风格分析" :image_id="showType == '数据' ? '' : 'positionStyle'" @downloadExcel="exportExcel">
			<div class="flex_start">
				<el-radio-group class="ml-12" v-model="showType">
					<el-radio-button label="图表"></el-radio-button>
					<el-radio-button label="数据"></el-radio-button>
				</el-radio-group>
			</div>
		</analysis-card-title>
		<!-- <analysis-card-title title="全持仓风格分析" image_id="positionStyle"> </analysis-card-title> -->
		<div v-show="showType == '图表'" class="flex_start" id="positionStyleChart">
			<div class="left_legend py-20">
				<div v-for="item in itemList" :key="item.label" class="mb-4 flex_start legend_item" @click="changeShow(item.label)">
					<div class="item_icon mr-8" :style="item.show ? `background-color:${item.color}` : `background-color:#D9D9D9`"></div>
					<div :style="item.show ? `color:rgba(0, 0, 0, 0.85);` : `color:rgba(0, 0, 0, 0.45);`">
						{{ item.label }}
					</div>
				</div>
			</div>
			<div class="charts_fill_class ml-20" style="flex: 1" v-loading="chicangloading">
				<el-empty v-show="!show" :image-size="160"></el-empty>
				<v-chart
					v-show="show"
					ref="positionStyle"
					v-loading="chicangloading"
					style="width: 100%; height: 424px"
					autoresize
					element-loading-text="暂无数据"
					element-loading-spinner="el-icon-document-delete"
					element-loading-background="rgba(239, 239, 239, 0.5)"
					:options="optionex"
				/>
			</div>
		</div>
		<div v-show="showType == '数据'">
			<el-table :data="table_data" border stripe style="width: 100%" height="424px" @sort-change="sortChange">
				<el-table-column prop="date" label="时间" align="gotoleft" sortable> </el-table-column>
				<el-table-column v-for="item in itemList" :key="item.label" sortable="custom" :prop="item.label" :label="item.label" align="center">
					<template slot-scope="{ row }">
						<div v-show="item">{{ row[item.label] | fix2p }}</div>
					</template>
				</el-table-column>
			</el-table>
		</div>
	</div>
</template>

<script>
import { lineChartOption } from '@/utils/chartStyle.js';

// 持仓风格
import { getHoldStyle } from '@/api/pages/Analysis.js';
import { filter_json_to_excel } from '@/utils/exportExcel.js';

// 持仓风格
export default {
	name: 'positionStyle',
	data() {
		return {
			showType: '图表',
			optionex: {},
			chicangloading: true,
			show: true,
			info: {},
			color: [
				'#4096ff',
				'#89B3FA',
				'#D0DFF9',
				'#4096ff',
				'#FFB462',
				'#F8D3AB',
				'#83BE57',
				'#A5D084',
				'#D0ECBA',
				'#904371',
				'#BF5694',
				'#DCA1C4',
				'#E8684A',
				'#EF907A',
				'#F9C5B9',
				'#F6BD16',
				'#FFDD79',
				'#FFEDB9',
				'#7388A9',
				'#9EB2D2',
				'#CADAF2',
				'#EB2F96',
				'#F66DB8',
				'#FFBAE0',
				'#13C2C2',
				'#68E4DD',
				'#B5F5EC',
				'#B43438'
			],
			data: [],
			table_data: [],
			itemList: []
		};
	},
	filters: {
		fix2p(val) {
			return val * 1 && !isNaN(val) ? (val * 100).toFixed(2) + '%' : '--';
		}
	},
	methods: {
		// 获取持仓风格数据
		async getHoldStyle() {
			this.chicangloading = true;
			let data = await getHoldStyle({
				flag: this.info.flag,
				code: this.info.code,
				type: this.info.type,
				start_date: this.info.start_date,
				end_date: this.info.end_date,
				method: 'A股价值成长大小盘'
			});
			let hkdata = {};
			if (this.info.type.indexOf('hk') !== -1) {
				hkdata = await getHoldStyle({
					flag: this.info.flag,
					code: this.info.code,
					type: this.info.type,
					start_date: this.info.start_date,
					end_date: this.info.end_date,
					method: '港股价值成长'
				});
			}
			this.chicangloading = false;
			if (data?.mtycode == 200 || hkdata?.mtycode == 200) {
				if (hkdata?.data) {
					this.data = [...data?.data, ...hkdata?.data];
					this.filterItemList(this.data);
					this.drawLine(this.data);
				} else {
					this.data = [...data?.data];
					this.filterItemList(this.data);
					this.drawLine(this.data);
				}
			} else {
				this.hideLoading();
			}
			this.formatTableData();
		},
		// 获取父组件传递数据
		async getData(info) {
			this.info = info;
			await this.getHoldStyle();
		},
		// 切换具体图例的显隐
		changeShow(name) {
			let selected = {};
			// 获取当前点击的索引
			let index = this.itemList.findIndex((v) => v.label == name);
			// 修改数组中当前图例的显示状态
			this.$set(this.itemList, index, {
				...this.itemList[index],
				show: !this.itemList[index].show
			});
			// 将数组转化成echart接收格式
			this.itemList.map((item) => {
				selected[item.label] = item.show;
			});
			this.optionex = {
				...this.optionex,
				legend: { ...this.optionex.legend, selected }
			};
			console.log(this.optionex);
		},
		// 画图
		drawLine(data) {
			let { series, date_list } = this.filterData(data);
			this.optionex = lineChartOption({
				toolbox: 'none',
				tooltip: {
					formatter: (params) => {
						let str = `<div style="display:flex;align-items:center;maring-bottom:8px;"><div style="margin-right:4px">时间:</div><div>${params[0].axisValue}</div></div>`;
						for (let i = 0; i < params.length; i++) {
							let dotHtml = `<div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:${
								this.itemList.find((v) => v.label == params[i].seriesName)?.color
							}"></div>`;
							str += `<div style="margin-bottom:8px;display:flex;align-items:center;justify-content:space-between;"><div style="display:flex;align-items:center;">${dotHtml}<div>${
								params[i].seriesName
							}:</div></div><div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">${
								(params[i].value * 100).toFixed(3) + '%'
							}</div></div>`;
						}
						return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${str}</div>`;
					}
				},
				dataZoom: true,
				xAxis: [{ type: 'category', data: date_list }],
				yAxis: [
					{
						type: 'value',
						max: 1,
						formatter: function (val) {
							return val * 100 + '%';
						}
					}
				],
				series
			});
		},
		// 过滤图例
		filterItemList(data) {
			let list = Array.from(new Set(data.map((v) => v.style)));
			this.itemList = list.map((v, i) => {
				return { label: v, color: this.color[i], show: true };
			});
		},
		// 格式化表格数据
		formatTableData() {
			let table_data = [];
			this.data.map((item) => {
				let index = table_data.findIndex((v) => v.date == item.date);
				let obj = {};
				obj[item.style] = item.weight;
				if (index == -1) {
					table_data.push({ date: item.date, ...obj });
				} else {
					table_data[index] = { ...table_data[index], ...obj };
				}
			});
			this.table_data = table_data.sort((a, b) => {
				return this.moment(this.moment(b.date, 'YYYY-MM-DD').format()).isBefore(this.moment(a.date, 'YYYY-MM-DD').format()) ? -1 : 1;
			});
		},
		// 表格排序
		sortChange(sortVal) {
			let order = sortVal.order;
			let key = sortVal.prop;
			if (!order) {
				this.table_data = this.table_data.slice();
			} else if (order == 'ascending' && key) {
				let haveValList = this.table_data.filter((item) => !isNaN(parseFloat(item[key])));
				let noValList = this.table_data.filter((item) => isNaN(parseFloat(item[key])));
				haveValList.sort((a, b) => a[key] - b[key]);
				this.table_data = [...haveValList, ...noValList];
			} else if (order == 'descending' && key) {
				let haveValList = this.table_data.filter((item) => !isNaN(parseFloat(item[key])));
				let noValList = this.table_data.filter((item) => isNaN(parseFloat(item[key])));
				haveValList.sort((a, b) => b[key] - a[key]);
				this.table_data = [...haveValList, ...noValList];
			}
		},
		// 隐藏模块
		hideLoading() {
			this.show = false;
		},
		exportImage() {
			let chart = this.$refs['positionStyle'].getDataURL({
				type: 'png',
				pixelRatio: 3,
				backgroundColor: '#fff',
				excludeComponents: ['dataZoom']
			});
			let aLink = document.createElement('a');
			aLink.style.display = 'none';
			aLink.href = chart;
			aLink.download = '持仓风格.jpg';
			// 触发点击-然后移除
			document.body.appendChild(aLink);
			aLink.click();
			document.body.removeChild(aLink);
		},
		// 过滤接收数据
		filterData(data) {
			let result = data.sort((a, b) => {
				return this.moment(this.moment(a.date, 'YYYY-MM-DD').format()).isBefore(this.moment(b.date, 'YYYY-MM-DD').format()) ? -1 : 1;
			});
			let date_list = Array.from(new Set(result.map((v) => v.date)));
			let series = [];
			this.itemList.map((item) => {
				let arr = result.filter((v) => v.style == item.label);
				series.push({
					name: item.label,
					type: 'line',
					stack: '总量',
					barWidth: '100%',
					symbol: 'none',
					lineStyle: {
						color: item.color
					},
					areaStyle: {
						color: item.color,
						opacity: 0.25
					},
					data: arr.map((v) => (v.weight ? v.weight : 0))
				});
			});
			return { date_list, series };
		},
		exportExcel() {
			let list = [
				{ label: '持仓风格', value: 'label' },
				{ label: '投资比例', value: 'weight', format: 'fix2p' }
			];
			filter_json_to_excel(list, this.data, '全持仓风格分析');
		},
		async createPrintWord(info) {
			await this.getData(info);
			let height = document.getElementById('positionStyleChart').clientHeight;
			let width = document.getElementById('positionStyleChart').clientWidth;
			let canvas = await this.html2canvas(document.getElementById('positionStyleChart'), { scale: 3 });
			return this.show
				? [
						...this.$exportWord.exportTitle('全持仓风格分析'),
						...this.$exportWord.exportChart(canvas.toDataURL('image/jpg'), {
							width,
							height
						})
					]
				: [];
		}
	}
};
</script>

<style lang="scss" scoped>
.position_style {
	.left_legend {
		width: 140px;
		height: 464px;
		margin-left: -20px;
		margin-top: -20px;
		margin-bottom: -20px;
		padding-left: 20px;
		background: #fff;
		box-shadow: 8px 0px 20px 0px rgba(0, 0, 0, 0.08);
		.legend_item {
			cursor: pointer;
			color: rgba(0, 0, 0, 0.65);
			font-family: PingFang SC;
			font-size: 12px;
			font-style: normal;
			font-weight: 400;
			.item_icon {
				width: 12px;
				height: 8px;
			}
		}
	}
}
</style>
