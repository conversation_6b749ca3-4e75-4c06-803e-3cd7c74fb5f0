<template>
  <div class="industry-ability">
    <div style="margin-bottom: 10px">
      <div style="display: flex; align-items: center; width: 100%; position: relative">
        <div style="display: flex; align-items: center">
          <div class="TitltCompare">行业能力宽度</div>
        </div>
        <!-- FIXME: 目前接口无日期字段, 故参数设置icon暂不显示 -->
        <div v-if="false"
             style="right: 0px; position: absolute">
          <i @click="showdetailchoose = !showdetailchoose"
             :style="showdetailchoose ? 'font-size:20px;cursor: pointer;color:#4096FF' : 'font-size:20px;cursor: pointer;'"
             class="el-icon-setting"></i>
        </div>
      </div>
      <div v-show="showdetailchoose"
           style="text-align: right">
        <div style="margin: 10px 0">
          <el-date-picker v-model="dateRange"
                          type="daterange"
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期">
          </el-date-picker>
        </div>
      </div>
    </div>
    <!-- 胜率超过50%的行业 -->
    <div v-loading="loading1"
         v-show="showNULL"
         style="margin-top: 16px">
      <sTable :data="winningDataTran"
              typeFlag="splice1"></sTable>
    </div>
    <!-- 持仓权重超过5%的行业 -->
    <div v-loading="loading2"
         v-show="showNULL2"
         style="margin-top: 16px">
      <sTable :data="weightDataTran"
              typeFlag="splice1"></sTable>
    </div>
    <!-- 回报最高的5个行业 -->
    <div v-loading="loading3"
         v-show="showNULL3"
         style="margin-top: 16px">
      <sTable :data="returnDataTran"
              typeFlag="splice2"></sTable>
    </div>
  </div>
</template>

<script>
import sTable from '../SelfTable.vue';
import {
  FundIndustryExcessSummary,
  FundIndustryWeightSummary,
  FundLongHoldIndustryCumReturn,
  ManagerIndustryExcessSummary,
  ManagerIndustryWeightSummary,
  ManagerIndustryCumReturnSummary
} from '@/api/pages/tools/compare.js';
import { getFundOrBase } from '@/api/pages/components/yejiheader.js';
export default {
  data () {
    return {
      dateRange: [],
      indexList: [],
      showdetailchoose: false,
      winningData: [],
      winningDataTran: [],
      columnsWinning: [],
      weightData: [],
      weightDataTran: [],
      columnsWeight: [],
      returnData: [],
      returnDataTran: [],
      columnReturn: [],
      loading1: false,
      loading2: false,
      loading3: false,
      showNULL: true,
      showNULL2: true,
      showNULL3: true
    };
  },
  components: { sTable },
  props: {
    comparetype: {
      type: String,
      default: 'manager' //fund
    },
    id: {
      type: String,
      default: '30189741,30441407'
    },
    type: {
      type: String,
      default: 'equity'
    },
    name: {
      type: String,
      default: '萧楠,胡昕炜'
    }
  },
  filters: {
    fix0 (value, multiple = 1, suffix) {
      if (value == '--' || value == null || value == '' || isNaN(value)) {
        return '--';
      } else {
        let str = (value * multiple).toFixed();
        str = suffix ? str + suffix.toString() : str;
        return str;
      }
    },
    fix1 (value, multiple = 1, suffix) {
      if (value == '--' || value == null || value == '' || isNaN(value)) {
        return '--';
      } else {
        let str = (value * multiple).toFixed(1);
        str = suffix ? str + suffix.toString() : str;
        return str;
      }
    },
    fix2 (value, multiple = 1, suffix) {
      if (value == '--' || value == null || value == '' || isNaN(value)) {
        return '--';
      } else {
        let str = (value * multiple).toFixed(2);
        str = suffix ? str + suffix.toString() : str;
        return str;
      }
    },
    fix3 (value, multiple = 1, suffix) {
      if (value == '--' || value == null || value == '' || isNaN(value)) {
        return '--';
      } else {
        let str = (value * multiple).toFixed(3);
        str = suffix ? str + suffix.toString() : str;
        return str;
      }
    }
  },
  watch: {
    dateRange () {
      this.getdata();
    }
  },
  methods: {
    rowAttribute () {
      return {
        style: {
          'font-size': '12px'
        }
      };
    },
    getdata () {
      // Object.assign(this.$data, this.$options.data());
      this.loading1 = true;
      this.loading2 = true;
      this.loading3 = true;

      this.getHighWinning();
      this.getHighWeight();
      this.getHighReturn();
    },
    async getHighWinning () {
      this.showNULL = true;
      let data;
      if (this.comparetype == 'fund') {
        data = await FundIndustryExcessSummary({ fund_code: this.id, type: this.type, fund_name: this.name });
      } else if (this.comparetype == 'manager') {
        data = await ManagerIndustryExcessSummary({ manager_code: this.id, type: this.type, manager_name: this.name });
      }
      if (this.FUNC.isEmpty(data.data)) {
        this.loading1 = false;
        let datatable = data.data;
        let codeName = this.comparetype == 'fund' ? 'fund_code' : 'manager_code';
        let nameKey = this.comparetype == 'fund' ? 'fund_name' : 'manager_name';
        let excessKey = this.comparetype == 'fund' ? 'excess' : 'excess_rank';
        let timesKey = this.comparetype == 'fund' ? 'times' : 'number_y';
        this.columnsWinning = [];
        let list = [];
        // ----------------------------------------------------------------
        // //console.log('sadsdsds??xxxxxxx');
        // //console.log(data.data);
        let tempindustry = [];
        let tempindustrynum = [];
        let tempobj = [];
        for (let i = 0; i < data.data.length; i++) {
          for (let j = 0; j < data.data[i].length; j++) {
            if (this.FUNC.isEmpty(data.data[i][j].industry_name)) {
              if (tempindustry.indexOf(data.data[i][j].industry_name) < 0) {
                tempindustry.push(data.data[i][j].industry_name);
                tempindustrynum.push(1);
              } else {
                tempindustrynum[tempindustry.indexOf(data.data[i][j].industry_name)] += 1;
              }
            }
          }
        }
        for (let i = 0; i < tempindustrynum.length; i++) {
          tempobj.push({
            value: tempindustrynum[i],
            label: tempindustry[i]
          });
        }
        let orderobj = tempobj.sort(function (a, b) {
          return b.value - a.value;
        });
        tempindustry = [];
        for (let i = 0; i < orderobj.length; i++) {
          tempindustry.push(orderobj[i].label);
        }
        datatable = [];
        for (let k = 0; k < tempindustry.length; k++) {
          for (let i = 0; i < data.data.length; i++) {
            if (datatable[i]) {
            } else {
              datatable[i] = [];
            }
            let tempindex = -1;
            for (let j = 0; j < data.data[i].length; j++) {
              if (tempindustry[k] == data.data[i][j].industry_name) {
                tempindex = j;
              }
            }
            if (tempindex != -1) {
              datatable[i].push(data.data[i][tempindex]);
            } else {
              if (this.comparetype == 'fund')
                datatable[i].push({
                  industry_name: tempindustry[k],
                  excess: '--',
                  times: '--',
                  quantile: '--',
                  fund_name: data.data[i][0][nameKey],
                  [codeName]: data.data[i][0][codeName]
                });
              else
                datatable[i].push({
                  industry_name: tempindustry[k],
                  excess: '--',
                  times: '--',
                  quantile: '--',
                  manager_name: data.data[i][0][nameKey],
                  [codeName]: data.data[i][0][codeName]
                });
            }
          }
        }
        // ----------------------------------------------------------------
        for (let i = 0; i < datatable.length; i++) {
          if (datatable[i].length > 0) {
            this.columnsWinning.push({
              title: datatable[i][0][nameKey],
              children: [
                {
                  dataIndex: 'industry_name' + i,
                  key: 'industry_name' + i,
                  title: '行业',
                  scopedSlots: {
                    customRender: 'industry_name' + i
                  }
                },
                {
                  dataIndex: 'excess' + i,
                  key: 'excess' + i,
                  title: '超指数胜率',
                  sorter: (a, b) => a['excess' + i] - b['excess' + i],
                  scopedSlots: {
                    customRender: 'excess' + i
                  }
                },
                {
                  dataIndex: 'times' + i,
                  key: 'times' + i,
                  title: '配置次数',
                  sorter: (a, b) => a['times' + i] - b['times' + i],
                  scopedSlots: {
                    customRender: 'times' + i
                  }
                },
                {
                  dataIndex: 'quantile' + i,
                  key: 'quantile' + i,
                  title: '胜率全市场中位数',
                  sorter: (a, b) => a['quantile' + i] - b['quantile' + i],
                  scopedSlots: {
                    customRender: 'quantile' + i
                  }
                }
              ]
            });
          }
          let arr = datatable[i];
          let indexkey = this.$route.query.id.split(',').findIndex((item) => item == arr[0][codeName]);
          arr.forEach((item, index) => {
            if (!list[index]) {
              list[index] = {};
            }
            list[index]['code' + indexkey] = item[codeName];
            list[index]['industry_name' + indexkey] = item.industry_name;
            list[index]['excess' + indexkey] = item[excessKey];
            list[index]['times' + indexkey] = item[timesKey];
            list[index]['quantile' + indexkey] = item.quantile;
          });
        }
        this.winningData = list;
        this.winningDataTran = [['行业']];
        for (let i = 0; i < this.$route.query.id.split(',').length; i++) {
          this.winningDataTran[0].push('超指数胜率/次数/中位数');
        }
        for (let j = 0; j < this.winningData.length; j++) {
          this.winningDataTran[j + 1] = [];
          this.winningDataTran[j + 1].push(
            this.FUNC.isEmpty(this.winningData[j].industry_name0)
              ? this.winningData[j].industry_name0
              : this.FUNC.isEmpty(this.winningData[j].industry_name1)
                ? this.winningData[j].industry_name1
                : this.FUNC.isEmpty(this.winningData[j].industry_name2)
                  ? this.winningData[j].industry_name2
                  : this.FUNC.isEmpty(this.winningData[j].industry_name3)
                    ? this.winningData[j].industry_name3
                    : '--'
          );
          for (let i = 0; i < this.$route.query.id.split(',').length; i++) {
            this.winningDataTran[j + 1].push(
              (this.FUNC.isEmpty(this.winningData[j]['excess' + i])
                ? (Number(this.winningData[j]['excess' + i]) * 100).toFixed(2) + '%'
                : '--') +
              '/' +
              (this.FUNC.isEmpty(this.winningData[j]['times' + i]) ? Number(this.winningData[j]['times' + i]).toFixed(0) : '--') +
              '/' +
              (this.FUNC.isEmpty(this.winningData[j]['quantile' + i])
                ? (Number(this.winningData[j]['quantile' + i]) * 100).toFixed(2) + '%'
                : '--')
            );
          }
        }
      } else {
        this.showNULL = false;
        this.loading1 = false;
      }
    },
    async getHighWeight () {
      this.showNULL2 = true;
      let data, multiple;
      if (this.comparetype == 'fund') {
        data = await FundIndustryWeightSummary({ fund_code: this.id, type: this.type, fund_name: this.name });
        multiple = 1;
      } else if (this.comparetype == 'manager') {
        data = await ManagerIndustryWeightSummary({ manager_code: this.id, type: this.type, manager_name: this.name });
        multiple = 100; // 基金经理的权重和中位数值需要*100
      }
      this.loading2 = false;
      if (this.FUNC.isEmpty(data.data)) {
        let codeName = this.comparetype == 'fund' ? 'fund_code' : 'manager_code';
        let nameKey = this.comparetype == 'fund' ? 'fund_name' : 'manager_name';
        let datatable = data.data;
        let tempindustry = [];
        let tempindustrynum = [];
        let tempobj = [];
        for (let i = 0; i < data.data.length; i++) {
          for (let j = 0; j < data.data[i].length; j++) {
            if (this.FUNC.isEmpty(data.data[i][j].industry_name)) {
              if (tempindustry.indexOf(data.data[i][j].industry_name) < 0) {
                tempindustry.push(data.data[i][j].industry_name);
                tempindustrynum.push(1);
              } else {
                tempindustrynum[tempindustry.indexOf(data.data[i][j].industry_name)] += 1;
              }
            }
          }
        }
        for (let i = 0; i < tempindustrynum.length; i++) {
          tempobj.push({
            value: tempindustrynum[i],
            label: tempindustry[i]
          });
        }
        let orderobj = tempobj.sort(function (a, b) {
          return b.value - a.value;
        });
        tempindustry = [];
        for (let i = 0; i < orderobj.length; i++) {
          tempindustry.push(orderobj[i].label);
        }
        datatable = [];
        for (let k = 0; k < tempindustry.length; k++) {
          for (let i = 0; i < data.data.length; i++) {
            if (datatable[i]) {
            } else {
              datatable[i] = [];
            }
            let tempindex = -1;
            for (let j = 0; j < data.data[i].length; j++) {
              if (tempindustry[k] == data.data[i][j].industry_name) {
                tempindex = j;
              }
            }
            if (tempindex != -1) {
              datatable[i].push(data.data[i][tempindex]);
            } else {
              if (this.comparetype == 'fund')
                datatable[i].push({
                  industry_name: tempindustry[k],
                  avg_weight: '--',
                  number: '--',
                  quantile: '--',
                  fund_name: data.data[i][0][nameKey],
                  [codeName]: data.data[i][0][codeName]
                });
              else
                datatable[i].push({
                  industry_name: tempindustry[k],
                  avg_weight: '--',
                  number: '--',
                  quantile: '--',
                  manager_name: data.data[i][0][nameKey],
                  [codeName]: data.data[i][0][codeName]
                });
            }
          }
        }

        this.columnsWeight = [];
        let list = [];

        for (let i = 0; i < datatable.length; i++) {
          if (datatable[i].length > 0) {
            this.columnsWeight.push({
              title: datatable[i][0][nameKey],
              children: [
                {
                  dataIndex: 'industry_name' + i,
                  key: 'industry_name' + i,
                  title: '行业',
                  scopedSlots: {
                    customRender: 'industry_name' + i
                  }
                },
                {
                  dataIndex: 'avg_weight' + i,
                  key: 'avg_weight' + i,
                  title: '平均配置权重',
                  sorter: (a, b) => a['avg_weight' + i] - b['avg_weight' + i],
                  scopedSlots: {
                    customRender: 'avg_weight' + i
                  }
                },
                {
                  dataIndex: 'number' + i,
                  key: 'number' + i,
                  title: '配置次数',
                  sorter: (a, b) => a['number' + i] - b['number' + i],
                  scopedSlots: {
                    customRender: 'number' + i
                  }
                },
                {
                  dataIndex: 'quantile' + i,
                  key: 'quantile' + i,
                  title: '平均配置全市场中位数',
                  sorter: (a, b) => a['quantile' + i] - b['quantile' + i],
                  scopedSlots: {
                    customRender: 'quantile' + i
                  }
                }
              ]
            });
          }
          let arr = datatable[i];
          let indexkey = this.$route.query.id.split(',').findIndex((item) => item == arr[0][codeName]);
          arr.forEach((item, index) => {
            if (!list[index]) {
              list[index] = {};
            }
            list[index]['code' + indexkey] = item[this.comparetype == 'fund' ? 'fund_code' : 'manager_code'];
            list[index]['industry_name' + indexkey] = item.industry_name;
            list[index]['avg_weight' + indexkey] = item.avg_weight * multiple;
            list[index]['number' + indexkey] = item.number;
            list[index]['quantile' + indexkey] = item.quantile * multiple;
          });
        }
        this.weightData = list;
        this.weightDataTran = [['行业']];
        for (let i = 0; i < this.$route.query.id.split(',').length; i++) {
          this.weightDataTran[0].push('平均配置权重/次数/中位数');
        }
        for (let j = 0; j < this.weightData.length; j++) {
          this.weightDataTran[j + 1] = [];
          this.weightDataTran[j + 1].push(
            this.FUNC.isEmpty(this.weightData[j].industry_name0)
              ? this.weightData[j].industry_name0
              : this.FUNC.isEmpty(this.weightData[j].industry_name1)
                ? this.weightData[j].industry_name1
                : this.FUNC.isEmpty(this.weightData[j].industry_name2)
                  ? this.weightData[j].industry_name2
                  : this.FUNC.isEmpty(this.weightData[j].industry_name3)
                    ? this.weightData[j].industry_name3
                    : '--'
          );
          for (let i = 0; i < this.$route.query.id.split(',').length; i++) {
            this.weightDataTran[j + 1].push(
              (this.FUNC.isEmpty(this.weightData[j]['avg_weight' + i])
                ? Number(this.weightData[j]['avg_weight' + i]).toFixed(2) + '%'
                : '--') +
              '/' +
              (this.FUNC.isEmpty(this.weightData[j]['number' + i]) ? Number(this.weightData[j]['number' + i]).toFixed(0) : '--') +
              '/' +
              (this.FUNC.isEmpty(this.weightData[j]['quantile' + i]) ? Number(this.weightData[j]['quantile' + i]).toFixed(2) + '%' : '--')
            );
          }
        }
      } else {
        this.loading2 = false;
        this.showNULL2 = false;
      }
    },
    async getHighReturn () {
      this.showNULL3 = true;

      // 请求数据
      let data;
      if (this.comparetype == 'fund') {
        data = await FundLongHoldIndustryCumReturn({ fund_code: this.id, type: this.type, fund_name: this.name });
      } else if (this.comparetype == 'manager') {
        data = await ManagerIndustryCumReturnSummary({ manager_code: this.id, type: this.type, manager_name: this.name });
      }
      this.loading3 = false;
      if (this.FUNC.isEmpty(data.data)) {
        // 排序&截取前五
        let dataList = [];
        for (let i = 0; i < data.data.length; i++) {
          dataList.push(this.sort(data.data[i], 'industry_return').reverse().slice(0, 5));
        }
        // 调用table生成函数
        if (this.comparetype == 'fund') {
          this.grenerateTableData(
            [
              { name: '行业', key: 'industry_name' },
              { name: '累计贡献', key: 'industry_return' },
              { name: '累计贡献全市场中位数', key: 'quantile' },
              { name: '累计贡献全市场排名前', key: 'rank' }
            ],
            ['industry_return', 'quantile', 'rank'],
            dataList,
            'columnReturn',
            'returnData',
            'fund_name'
          );
        } else if (this.comparetype == 'manager') {
          this.grenerateTableData(
            [
              { name: '行业', key: 'industry_name' },
              { name: '累计贡献', key: 'industry_return' },
              { name: '累计贡献全市场中位数', key: 'quantile' },
              { name: '累计贡献全市场排名前', key: 'rank' }
            ],
            ['industry_return', 'quantile', 'rank'],
            dataList,
            'columnReturn',
            'returnData',
            'manager_name'
          );
        }
        this.returnDataTran = [['行业累计贡献']];
        for (let i = 0; i < this.$route.query.id.split(',').length; i++) {
          this.returnDataTran[0].push('行业/贡献/中位数/排名前');
        }
        for (let j = 0; j < this.returnData.length; j++) {
          this.returnDataTran[j + 1] = [];
          this.returnDataTran[j + 1].push(' ');

          for (let i = 0; i < this.$route.query.id.split(',').length; i++) {
            this.returnDataTran[j + 1].push(
              (this.FUNC.isEmpty(this.returnData[j]['industry_name' + i]) ? this.returnData[j]['industry_name' + i] : '--') +
              '/' +
              (this.FUNC.isEmpty(this.returnData[j]['industry_return' + i])
                ? (Number(this.returnData[j]['industry_return' + i]) * 100).toFixed(2) + '%'
                : '--') +
              '/' +
              (this.FUNC.isEmpty(this.returnData[j]['quantile' + i])
                ? (Number(this.returnData[j]['quantile' + i]) * 100).toFixed(2) + '%'
                : '--') +
              '/' +
              (this.FUNC.isEmpty(this.returnData[j]['rank' + i]) ? (Number(this.returnData[j]['rank' + i]) * 100).toFixed(2) + '%' : '--')
            );
          }
        }
      } else {
        this.loading3 = false;
        this.showNULL3 = false;
      }
    },
    /** grenerateTableData 使用示例 **/
    // this.grenerateTableData(
    // 	[
    // 		{ name: '行业', key: 'industry_name' },
    // 		{ name: '平均配置权重', key: 'avg_weight' },
    // 		{ name: '配置次数', key: 'number' },
    // 		{ name: '平均配置全市场中位数', key: 'quantile' }
    // 	],
    // 	['avg_weight', 'number', 'quantile'],
    // 	data.data,
    // 	'columnsWeight',
    // 	'weightData',
    // 	'fund_name'
    // );
    grenerateTableData (keyList, sorterKeyList, dataList, columnsTarget, dataTarget, nameKey = 'fund_name') {
      let list = [];
      let columnsList = [];
      let codeName = this.comparetype == 'fund' ? 'fund_code' : 'manager_code';
      for (let i = 0; i < dataList.length; i++) {
        if (dataList[i].length > 0) {
          let childrenList = [];
          keyList.forEach((item) => {
            let obj = {};
            obj = {
              dataIndex: item.key + i,
              key: item.key + i,
              title: item.name,
              scopedSlots: {
                customRender: item.key + i
              }
            };
            if (sorterKeyList.includes(item.key)) {
              obj.sorter = (a, b) => a[item.key + i] - b[item.key + i];
            }
            childrenList.push(obj);
          });
          columnsList.push({
            title: dataList[i][0][nameKey],
            children: childrenList
          });
        }

        let arr = dataList[i];
        let indexkey = this.$route.query.id.split(',').findIndex((item) => item == arr[0][codeName]);
        arr.forEach((item, index) => {
          if (!list[index]) {
            list[index] = {};
          }
          keyList.forEach((keyItem) => {
            list[index][keyItem.key + indexkey] = item[keyItem.key];
          });
        });
      }
      this[columnsTarget] = columnsList;
      this[dataTarget] = list;
    },
    sort (dataList, keyword) {
      let len = dataList.length;
      for (let i = 0; i < len; i++) {
        for (let j = 0; j < len - 1 - i; j++) {
          if (dataList[j][keyword] > dataList[j + 1][keyword]) {
            [dataList[j], dataList[j + 1]] = [dataList[j + 1], dataList[j]];
          }
        }
      }
      return dataList;
    },
    createPrintWord () {
      let word = [];
      if (this.showNULL) {
        let data = [];
        this.winningDataTran.map((obj, index) => {
          var list = [];
          obj.map((item, i) => {
            let itemData = item.split('/');
            list.push(...itemData);
          });
          data.push(list);
        });
        let name = this.name.split(',');
        data.unshift(['', ...name]);
        word.push(
          ...this.$exportWord.exportDescripe('胜率超过50%的行业'),
          ...this.$exportWord.exportCompareTable(
            data,
            [
              {
                row: 0,
                cell: 1,
                rowSpan: 3
              },
              {
                row: 0,
                cell: 2,
                rowSpan: 3
              },
              {
                row: 0,
                cell: 3,
                rowSpan: 3
              },
              {
                row: 0,
                cell: 4,
                rowSpan: 3
              }
            ],
            true
          )
        );
      }
      if (this.showNULL2) {
        let data = [];
        this.weightDataTran.map((obj, index) => {
          var list = [];
          obj.map((item, i) => {
            let itemData = item.split('/');
            list.push(...itemData);
          });
          data.push(list);
        });
        let name = this.name.split(',');
        data.unshift(['', ...name]);
        word.push(
          ...this.$exportWord.exportDescripe('持仓权重超过5%的行业'),
          ...this.$exportWord.exportCompareTable(
            data,
            [
              {
                row: 0,
                cell: 1,
                rowSpan: 3
              },
              {
                row: 0,
                cell: 2,
                rowSpan: 3
              },
              {
                row: 0,
                cell: 3,
                rowSpan: 3
              },
              {
                row: 0,
                cell: 4,
                rowSpan: 3
              }
            ],
            true
          )
        );
      }
      if (this.showNULL3) {
        let data = [];
        this.returnDataTran.map((obj, index) => {
          var list = [];
          obj.map((item, i) => {
            let itemData = item.split('/');
            if (index != 0 && i != 0) {
              list.push(...itemData);
            }
          });
          if (index != 0) {
            data.push(list);
          }
        });
        let name = this.name.split(',');
        data.unshift([...name]);
        word.push(
          ...this.$exportWord.exportDescripe('行业累计贡献(回报最高的5个行业)'),
          ...this.$exportWord.exportCompareTable(
            data,
            [
              {
                row: 0,
                cell: 0,
                rowSpan: 4
              },
              {
                row: 0,
                cell: 1,
                rowSpan: 4
              },
              {
                row: 0,
                cell: 2,
                rowSpan: 4
              },
              {
                row: 0,
                cell: 3,
                rowSpan: 4
              }
            ],
            true
          )
        );
      }

      return [...this.$exportWord.exportTitle('行业能力宽度'), ...word];
      // let data = [];
      // this.arrlist.map((item) => {
      // 	data.push(...item);
      // });
      // let name = this.name.split(',');
      // data.unshift(['', ...name]);
      // return [...this.$exportWord.exportTitle('行业配置'), ...this.$exportWord.exportCompareTable(data, [], true)];
    }
  }
};
</script>

<style></style>
