<!--  -->
<template>
  <div class="boxIndexOnlyYSF">
    <!-- <el-input style="width:200px;margin-right:10px" v-model="inputIndex" placeholder=""></el-input>
     -->
    <div class="tagForFilter"
         style="display: flex; flex-wrap: wrap; align-items: center; margin-right: 10px">
      <!-- <el-select ref="saveTagInput2"  style="width:150px" class="input-new-tag"
             v-model="inputValue2" :fetch-suggestions="querySearch2" placeholder="指数代码" size='small'
             @select="handleInputConfirm2"></el-select> -->
      <operator v-if="is_range"
                ref="operator"
                @resolveMathRange="resolveMathRange"></operator>
      <el-select v-model="inputValue2"
                 popper-class="autoFilterPop"
                 style="width: 150px"
                 filterable
                 remote
                 reserve-keyword
                 placeholder="指数代码"
                 :remote-method="querySearch2"
                 @change="handleInputConfirm2"
                 :loading="loading">
        <el-option v-for="item in options"
                   :key="item.value"
                   :label="item.value"
                   :value="item.code"> </el-option>
      </el-select>
    </div>
    <!--  -->
    <span style="font-weight: 600; margin-left: 8px; margin-right: 16px">{{ placeholder }}</span>
    <el-dropdown @command="command">
      <el-button type="primary">
        {{ iconFlag != '' ? (iconFlag == 'all' ? '所有' : iconFlag) : '运算符' }}<i class="el-icon-arrow-down el-icon--right"></i>
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="all">所有</el-dropdown-item>
        <el-dropdown-item command="<">&lt;</el-dropdown-item>
        <el-dropdown-item command="=">=</el-dropdown-item>
        <el-dropdown-item command=">">&gt;</el-dropdown-item>
        <el-dropdown-item command="<=">&lt;=</el-dropdown-item>
        <el-dropdown-item command=">=">&gt;=</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <div v-show="showBox"
         style="margin-left: 0px; display: flex; align-items: center">
      <!-- <div style="padding:5px;background:#ecf5ff;border:1px #f8f8f8;">
            {{iconFlag=='all'?'所有':iconFlag}}
        </div> -->
      <div style="margin-left: 16px">
        <el-input type="number"
                  @input="inputChange"
                  :placeholder="'输入50,即' + placeholder + '为50%'"
                  v-model="input"></el-input>
      </div>
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { Search } from '@/api/pages/Analysis.js';
import operator from '@/pages/filter/fund/beta/componentsFilter/components/operator.vue';
export default {
  props: {
    is_range: {
      type: Boolean,
      default: false
    },
    haveName: {
      type: String,
      default: ''
    },
    dataX: {
      type: Object,
      default: {}
    },
    placeholder: {
      type: String
    },
    indexFlag: {
      type: Number
    },
    baseIndexFlag: {
      type: Number
    },
    type:{
      type: String,
      default: 'index'
    }
  },
  //import引入的组件需要注入到对象中才能使用
  components: { operator },
  data () {
    //这里存放数据
    return {
      options: [],
      iconFlag: '',
      showBox: false,
      input: '',
      inputIndex: '',
      index_code: [],
      inputVisible2: false,
      inputValue2: '',
      mathRange: { mathRange: 'avg' }
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {
    dataX (val) {
      if (val.dataResult && val.dataResult.length > 0) {
        this.showBox = true;
        this.iconFlag = val.dataResult[0].flag;
        this.input = val.dataResult[0].value;
        this.inputValue2 = val.dataResult[0].index_code;
        this.options = this.dataX.dataResult[0].index_code_options;
        if (this.$refs['operator']) {
          this.$refs['operator'].getFlag(val.dataResult[0].mathRange);
        }
      }
    }
  },
  //方法集合
  methods: {
    resolveMathRange (obj) {
      this.mathRange = obj;
      this.resolveFather();
    },
    resolveFather () {
      this.$emit(
        'indexOnlyChange',
        this.baseIndexFlag,
        this.indexFlag,
        this.input,
        this.iconFlag,
        this.inputValue2,
        this.options,
        this.FUNC.isEmpty(this.inputValue2) && this.FUNC.isEmpty(this.input) && this.FUNC.isEmpty(this.iconFlag),
        this.mathRange
      );
    },
    handleInputConfirm2 (e) {
      this.resolveFather();
    },

    async querySearch2 (e) {
      // TOFO如果是个股则切换成个股搜索框
      let res = await Search({
        message: e,
        flag: this.type == 'stock' ? '9' : '6'
      });
      let temparr = [];
      if (res.mtycode == 200) {
        let data = res.data;
        for (let i = 0; i < data.length; i++) {
            temparr.push(data[i]);
            let temp = '';
            temp = temp + data[i].code + '_' + data[i].name;
            if (data[i].start_from != '--' && data[i].start_from) {
              temp = temp + '_' + data[i].start_from;
            }
            temparr[temparr.length - 1]['value'] = temp;
        }
      }
      this.options = temparr;
    },
    handleClose2 (tag) {
      this.index_code.splice(this.index_code.indexOf(tag), 1);
    },
    command (e) {
      this.iconFlag = e;
      this.showBox = true;
      this.resolveFather();
    },
    inputChange () {
      this.resolveFather();
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () {
    if (JSON.stringify(this.dataX) != '{}') {
      if (this.dataX.dataResult && this.dataX.dataResult.length > 0) {
        this.showBox = true;
        this.iconFlag = this.dataX.dataResult[0].flag;
        this.input = this.dataX.dataResult[0].value;
        this.inputValue2 = this.dataX.dataResult[0].index_code;
        this.options = this.dataX.dataResult[0].index_code_options;
        if (this.$refs['operator']) {
          this.$refs['operator'].getFlag(this.dataX.dataResult[0].mathRange);
        }
      }
    }
  },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style>
.autoFilterPop .el-autocomplete-suggestion__list li {
  white-space: inherit !important;
}
</style>
<style lang="scss" scoped>
//@import url(); 引入公共css类
.boxIndexOnlyYSF {
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  .tagForFilter {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    .el-tag {
      margin-right: 10px !important;
      // margin-left:10px !important;
      margin-top: 5px;
      line-height: 32px;
      height: 32px;
    }
  }
}
</style>
