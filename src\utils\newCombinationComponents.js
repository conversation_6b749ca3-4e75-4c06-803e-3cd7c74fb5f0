
export let newPortfolioDetailComponentsList = [
	{
		label: '组合监控',
		key: 'combinationMonitoring',
		class: ['*'],
		templateList: [
			{
				name: '组合持仓监控',
				is: 'combinationMonitoringTable',
				value: 'combinationMonitoringTable',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getPerformanceComparisonData',
				methods: 'getPerformanceComparisonIndex',
				getRequestData: 'getPerformanceComparison'
			},
			{
				name: '组合配置情况',
				is: 'combinationConfiguration',
				value: 'combinationConfiguration',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getPerformanceComparisonData',
				methods: 'getPerformanceComparisonIndex',
				getRequestData: 'getPerformanceComparison'
			},
			{
				name: '持仓净值走势',
				is: 'holdNavTrend',
				value: 'holdNavTrend',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getDynamicPullbackData',
				methods: 'getDynamicPullbackIndex',
				getRequestData: 'getDynamicPullback'
			},
			{
				name: '组合风险收益',
				is: 'combinationRiskEarning',
				value: 'combinationRiskEarning',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getCorrelationCoefficientData',
				methods: '',
				getRequestData: 'getCorrelationCoefficient'
			}
		]
	},
	{
		label: '风险收益分析',
		key: 'riskEarningAnalysis',
		class: ['*'],
		templateList: [
			{
				name: '组合持仓监控',
				is: 'incomeContributionDegree',
				value: 'incomeContributionDegree',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getPerformanceComparisonData',
				methods: 'getPerformanceComparisonIndex',
				getRequestData: 'getPerformanceComparison'
			},
			{
				name: '持仓净值走势',
				is: 'monthIncome',
				value: 'monthIncome',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getDynamicPullbackData',
				methods: 'getDynamicPullbackIndex',
				getRequestData: 'getDynamicPullback'
			},
			{
				name: '组合风险收益',
				is: 'totalRiskReturn',
				value: 'totalRiskReturn',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getCorrelationCoefficientData',
				methods: '',
				getRequestData: 'getCorrelationCoefficient'
			}
		]
	},
	{
		label: '资配与风格',
		key: 'newAssetAllocationAnalysis',
		class: ['*'],
		templateList: [
		/* 	{
				name: '最新各类型基金配置情况',
				is: 'newFundAllocation',
				value: 'newFundAllocation',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getPerformanceComparisonData',
				methods: 'getPerformanceComparisonIndex',
				getRequestData: 'getPerformanceComparison'
			}, */
			{
				name: '基金标签',
				is: 'fundLabelAnalysis',
				value: 'fundLabelAnalysis',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getPerformanceComparisonData',
				methods: 'getPerformanceComparisonIndex',
				getRequestData: 'getPerformanceComparison'
			},
			/* {
				name: '当前股票持仓分析',
				is: 'holdStockAnalysis',
				value: 'holdStockAnalysis',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getPerformanceComparisonData',
				methods: 'getPerformanceComparisonIndex',
				getRequestData: 'getPerformanceComparison'
			}, */

			{
				name: '风格分析',
				is: 'styleAnalysis',
				value: 'styleAnalysis',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getPerformanceComparisonData',
				methods: 'getPerformanceComparisonIndex',
				getRequestData: 'getPerformanceComparison'
			},
			{
				name: '当前权益持仓风格',
				is: 'equityHoldStyle',
				value: 'equityHoldStyle',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getDynamicPullbackData',
				methods: 'getDynamicPullbackIndex',
				getRequestData: 'getDynamicPullback'
			},
			{
				name: '整体财务指标',
				is: 'overallFinancialIndicator',
				value: 'overallFinancialIndicator',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getCorrelationCoefficientData',
				methods: '',
				getRequestData: 'getCorrelationCoefficient'
			}
		]
	},
	{
		label: '行业分析',
		key: 'businessAnalysis',
		class: ['*'],
		templateList: [
			{
				name: '行业持仓分析',
				is: 'industryPositionAnalysis',
				value: 'industryPositionAnalysis',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getPerformanceComparisonData',
				methods: 'getPerformanceComparisonIndex',
				getRequestData: 'getPerformanceComparison'
			},
			{
				name: '行业操作复盘',
				is: 'industryOperationReview',
				value: 'industryOperationReview',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getCorrelationCoefficientData',
				methods: '',
				getRequestData: 'getCorrelationCoefficient'
			},
			{
				name: 'Brinson归因',
				is: 'brinsonAttribution',
				value: 'brinsonAttribution',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getDynamicPullbackData',
				methods: 'getDynamicPullbackIndex',
				getRequestData: 'getDynamicPullback'
			},
			
			{
				name: '行业配置表现',
				is: 'industryAllocation',
				value: 'industryAllocation',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getCorrelationCoefficientData',
				methods: '',
				getRequestData: 'getCorrelationCoefficient'
			}
		]
	},
	{
		label: '个股分析',
		key: 'individualShareAnalysis',
		class: ['*'],
		templateList: [
			{
				name: '组合个股持仓复盘',
				is: 'portfolioStockResumption',
				value: 'portfolioStockResumption',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getPerformanceComparisonData',
				methods: 'getPerformanceComparisonIndex',
				getRequestData: 'getPerformanceComparison'
			},
			{
				name: '当前持仓债券分析',
				is: 'holdBondsAnalysis',
				value: 'holdBondsAnalysis',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getPerformanceComparisonData',
				methods: 'getPerformanceComparisonIndex',
				getRequestData: 'getPerformanceComparison'
			},
		]
	}
];
