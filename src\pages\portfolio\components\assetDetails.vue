<!--  -->
<template>
  <div class="assetDetails">
    <!-- 调整持仓主页 -->
    <el-dialog :modal-append-to-body="false"
               v-loading="loadingx"
               :append-to-body="true"
               :close-on-click-modal="false"
               class="ADdialog"
               width="1000px"
               :visible.sync="show"
               title="调整持仓">
      <div slot="title">
        <span style="
						font-family: 'PingFang';
						font-style: normal;
						font-weight: 500;
						font-size: 16px;
						line-height: 24px;
						color: rgba(0, 0, 0, 0.85);
						width: 100%;
					">调整持仓</span>
      </div>
      <div style="width: 100%; height: 1px; background: rgba(0, 0, 0, 0.06); margin-bottom: 16px"></div>
      <div style="display: flex; align-items: center; margin-bottom: 16px">
        <div>调整详情：</div>
        <div style="margin-right: 24px">
          <el-select @change="getDetailsHold"
                     v-model="Time"
                     style="width: 144px"
                     placeholder="请选择">
            <el-option v-for="item in options"
                       :key="item"
                       :label="item"
                       :value="item"> </el-option>
          </el-select>
        </div>

        <div>调仓日期：</div>
        <div id="step1"
             style="margin-right: 24px">
          <el-date-picker v-model="updateDate"
                          @change="changeDate"
                          type="date"
                          value-format="yyyy-MM-dd"
                          style="width: 144px"
                          placeholder="请选择">
          </el-date-picker>
        </div>
      </div>
      <div style="display: flex; align-items: top; margin-bottom: 16px; width: 100%">
        <div>调整说明：</div>
        <div>
          <el-input style="width: 890px"
                    :autosize="{ minRows: 2, maxRows: 4 }"
                    v-model="description"
                    type="textarea"
                    placeholder="请输入"></el-input>
        </div>
      </div>

      <div style="display: flex; justify-content: space-between; margin-bottom: 8px; align-items: center">
        <div style="display: flex; align-items: center">
          <el-button id="step2"
                     @click="showMoney = true"
                     :disabled="Time != TimeOrigin"
                     type="">现金存取</el-button>
          <div class="step3"
               style="display: flex; margin-left: 8px">
            <el-button @click="showAddTrad = true"
                       :disabled="Time != TimeOrigin"
                       type="">交易录入</el-button>
            <el-button @click="showAddTradFile = true"
                       :disabled="Time != TimeOrigin"
                       type="">交易流水文件导入</el-button>
            <el-button @click="showAddTradFileHold = true"
                       :disabled="Time != TimeOrigin"
                       type="">持仓文件导入</el-button>
            <!-- <el-button @click="showEquityAll" :disabled="Time != TimeOrigin" style="margin-right: 8px" type="">等权重调仓</el-button> -->
          </div>
          <span>币种：CNY</span>
        </div>
        <div>
          <span> 剩余金额:{{ Number(moneyMin).toFixed(2) }} </span>
        </div>
      </div>
      <div style="
					font-family: 'PingFang';
					font-style: normal;
					font-weight: 400;
					font-size: 12px;
					line-height: 20px;
					color: rgba(0, 0, 0, 0.65);
				">
        <p>ps:1.调仓时，请先选择调仓日期，并确保剩余金额足够，随后进行调仓</p>
        <p>ps:2.交易录入与交易流水文件导入在买入时根据剩余金额百分比买入，在卖出时根据该产品持有金额百分比卖出</p>
        <p>ps:3.持仓文件导入与等权重调仓根据当前总金额百分比计算</p>
      </div>
      <div v-loading="loadingtable"
           style="margin-bottom: 16px">
        <el-table height="460px"
                  :data="tableData">
          <el-table-column prop="code"
                           align="gotoleft"
                           show-overflow-tooltip
                           label="代码"></el-table-column>
          <el-table-column prop="name"
                           align="gotoleft"
                           show-overflow-tooltip
                           label="简称"></el-table-column>
          <el-table-column prop="nav"
                           align="gotoleft"
                           width="120px"
                           sortable
                           label="最新收盘价">
            <templete slot-scope="scope">{{ scope.row.nav | fix2 }}</templete>
          </el-table-column>
          <el-table-column prop="holdings"
                           align="gotoleft"
                           sortable
                           label="持仓数量">
            <template slot-scope="scope">{{ scope.row.holdings | fixW }}</template>
          </el-table-column>
          <el-table-column prop="totalmv"
                           align="gotoleft"
                           sortable
                           label="持仓市值">
            <templete slot-scope="scope">{{ scope.row.totalmv | fixW }}</templete>
          </el-table-column>
          <el-table-column prop="weight"
                           align="gotoleft"
                           sortable
                           label="权重">
            <templete slot-scope="scope">{{ scope.row.weight | fix2p }}</templete>
          </el-table-column>
          <el-table-column prop="begin_nav"
                           align="gotoleft"
                           sortable
                           label="成本价">
            <templete slot-scope="scope">{{ scope.row.begin_nav | fix2 }}</templete>
          </el-table-column>
          <el-table-column prop="date"
                           align="gotoleft"
                           width="120px"
                           sortable
                           label="收盘日期"> </el-table-column>
          <!-- <el-table-column v-if="Time == TimeOrigin" sortable align="gotoleft" label="操作">
						<template slot-scope="scope"
							><span
								style="cursor: pointer; color: #4096ff"
								@click="updateFund(scope.row.code, scope.row.name, scope.row.holdings, scope.row.begin_nav)"
								>修改</span
							></template
						>
					</el-table-column> -->
        </el-table>
      </div>
      <div style="display: flex; justify-content: space-between; align-items: center">
        <div>
          <el-button :disabled="Time != TimeOrigin"
                     @click="resetUpdate"
                     type="">重置修改</el-button>
        </div>
        <div>
          <el-button @click="show = false"
                     type="">取消</el-button>
          <el-button :disabled="Time != TimeOrigin"
                     @click="submitChange()"
                     class="step3"
                     type="primary">确认</el-button>
        </div>
      </div>
    </el-dialog>
    <!-- 现金存取 -->
    <el-dialog :close-on-click-modal="false"
               class="ADdialog"
               :visible.sync="showMoney"
               width="334px"
               title="现金存取">
      <div slot="title">
        <span style="
						font-family: 'PingFang';
						font-style: normal;
						font-weight: 500;
						font-size: 16px;
						line-height: 24px;
						color: rgba(0, 0, 0, 0.85);
						width: 100%;
					">调整持仓</span>
      </div>
      <div style="width: 100%; height: 1px; background: rgba(0, 0, 0, 0.06); margin-bottom: 16px"></div>
      <div style="margin-bottom: 16px">调仓日期：{{ updateDate }}</div>
      <div style="margin-bottom: 16px">
        <span style="margin-left: 14px"></span>调整前：<el-input style="width: 214px"
                  v-model="money"
                  disabled></el-input>
      </div>
      <div style="margin-bottom: 16px">
        <span style="margin-left: 42px"></span>存：<el-input v-model="addMoney.in"
                  @input="changein"
                  style="width: 214px"
                  placeholder=""></el-input>
      </div>
      <div style="margin-bottom: 16px">
        <span style="margin-left: 42px"></span>取：<el-input v-model="addMoney.out"
                  @input="changeout"
                  style="width: 214px"
                  placeholder=""></el-input>
      </div>
      <div style="margin-bottom: 16px">
        <span style="margin-left: 14px"></span>调整后：<el-input style="width: 214px"
                  disabled
                  v-model="addMoney.result"></el-input>
      </div>
      <div style="text-align: right">
        <el-button @click="showMoney = false"
                   type="">取消</el-button>
        <el-button @click="moneyInOut()"
                   type="primary">确认</el-button>
      </div>
    </el-dialog>
    <!-- 交易录入 -->
    <el-dialog :close-on-click-modal="false"
               class="ADdialog"
               width="326px"
               :visible.sync="showAddTrad"
               title="交易录入">
      <div slot="title">
        <span style="
						font-family: 'PingFang';
						font-style: normal;
						font-weight: 500;
						font-size: 16px;
						line-height: 24px;
						color: rgba(0, 0, 0, 0.85);
						width: 100%;
					">交易录入</span>
      </div>
      <div style="width: 100%; height: 1px; background: rgba(0, 0, 0, 0.06); margin-bottom: 16px"></div>
      <div style="margin-bottom: 16px">调仓日期：{{ updateDate }}</div>
      <div style="margin-bottom: 16px; display: flex; align-items: center">
        证券代码：
        <el-select style="width: 214px"
                   v-model="trad.code"
                   :remote-method="searchpeople"
                   @change="changeCode"
                   filterable
                   remote
                   prefix-icon="el-icon-search"
                   :loading="loading"
                   placeholder="输入查询基金/股票">
          <el-option-group v-for="groups in havefundmanager"
                           :key="groups.label"
                           :label="groups.label">
            <el-option v-for="group in groups.options"
                       :key="group.code"
                       :label="
								group.flag == 'fund' || group.flag == 'stock'
									? `${group.code}-${group.name}-${group.fund_co.split('基金')[0]}`
									: group.flag == 'manager'
									? `${group.name}-${group.fund_co.split('基金')[0]}`
									: group.flag == 'company'
									? group.name
									: `${group.name}-${group.code}`
							"
                       :value="group.code + '|' + group.name + '|' + group.flag">
            </el-option>
          </el-option-group>
        </el-select>
      </div>
      <div style="margin-bottom: 16px">
        交易方向：<el-radio-group v-model="trad.action">
          <el-radio :label="true">买入</el-radio>
          <el-radio :label="false">卖出</el-radio>
        </el-radio-group>
      </div>
      <div style="margin-bottom: 16px">
        交易权重：<el-input v-model="trad.number"
                  style="width: 214px"
                  placeholder="">
          <i style="display: flex; align-items: center; height: 100%"
             slot="suffix">%</i></el-input>
      </div>
      <div v-loading="loadingfalg"
           style="margin-bottom: 8px">
        成交价格：<el-radio-group @change="changeTradPrice"
                        v-model="trad.PriceFlag">
          <!-- <el-radio :label="true">自定义</el-radio> -->
          <el-radio :label="false">前收价</el-radio>
        </el-radio-group>
      </div>
      <div style="margin-bottom: 16px">
        <el-input v-model="trad.price"
                  :disabled="!trad.PriceFlag"
                  style="width: 214px; margin-left: 70px"
                  placeholder=""></el-input>
      </div>
      <div style="display: flex; align-items: top; margin-bottom: 16px; width: 100%">
        <div>调整说明：</div>
        <div>
          <el-input style="width: 214px"
                    :autosize="{ minRows: 2, maxRows: 4 }"
                    v-model="trad.description"
                    type="textarea"
                    placeholder="请输入"></el-input>
        </div>
      </div>
      <div style="text-align: right">
        <el-button @click="showAddTrad = false"
                   type="">取消</el-button>
        <el-button type="primary"
                   @click="submitTrad">确认</el-button>
      </div>
    </el-dialog>

    <!-- 交易流水文件导入 -->
    <el-dialog v-loading="loadingTradInput"
               :close-on-click-modal="false"
               class="ADdialog"
               width="800px"
               :visible.sync="showAddTradFile"
               title="交易流水文件导入">
      <div slot="title">
        <span style="
						font-family: 'PingFang';
						font-style: normal;
						font-weight: 500;
						font-size: 16px;
						line-height: 24px;
						color: rgba(0, 0, 0, 0.85);
						width: 100%;
					">交易流水文件导入</span>
      </div>
      <div style="width: 100%; height: 1px; background: rgba(0, 0, 0, 0.06); margin-bottom: 16px"></div>
      <div style="margin-bottom: 8px; display: flex; justify-content: space-between; align-items: center">
        <div>
          <div>
            请点击下载<span style="color: #4096ff; cursor: pointer"
                  @click="exportExcel">“交易流水模板”</span>，按此模板整理数据后在通过按钮导入
          </div>
          <div>导入数据将覆盖未提交部分交易录入，请注意</div>
          <div>证券买卖日期统一为调仓日期{{ updateDate }}</div>
          <div>仅支持上传xls,xlsx格式文件</div>
        </div>
        <div>
          <!-- <el-button type="primary">上传文件导入</el-button> -->
          <el-upload :auto-upload="false"
                     :file-list="appendixList"
                     :on-change="changeAppendix"
                     :on-remove="changeAppendix"
                     :show-file-list="false"
                     :multiple="false"
                     accept=".xls,.xlsx">
            <el-button type="primary">上传文件导入</el-button>
          </el-upload>
        </div>
      </div>
      <div style="margin-bottom: 16px">
        <el-table height="400px"
                  :data="dataTableModel">
          <el-table-column align="gotoleft"
                           prop="a"
                           label="证券代码"></el-table-column>
          <el-table-column align="gotoleft"
                           prop="c"
                           label="买卖权重"></el-table-column>
          <el-table-column align="gotoleft"
                           prop="e"
                           label="买卖方向"></el-table-column>
          <el-table-column align="gotoleft"
                           prop="f"
                           label="调仓说明"></el-table-column>
        </el-table>
      </div>
      <div style="text-align: right">
        <el-button @click="showAddTradFile = false"
                   type="">取消</el-button>
        <el-button type="primary"
                   @click="addExcel()">确认</el-button>
      </div>
    </el-dialog>
    <!-- 持仓文件导入 -->
    <el-dialog v-loading="loadingTradInput"
               :close-on-click-modal="false"
               class="ADdialog"
               width="800px"
               :visible.sync="showAddTradFileHold"
               title="交易流水文件导入">
      <div slot="title">
        <span style="
						font-family: 'PingFang';
						font-style: normal;
						font-weight: 500;
						font-size: 16px;
						line-height: 24px;
						color: rgba(0, 0, 0, 0.85);
						width: 100%;
					">
          持仓文件导入</span>
      </div>
      <div style="width: 100%; height: 1px; background: rgba(0, 0, 0, 0.06); margin-bottom: 16px"></div>
      <div style="margin-bottom: 8px; display: flex; justify-content: space-between; align-items: center">
        <div>
          <div>
            请点击下载<span style="color: #4096ff; cursor: pointer"
                  @click="exportExcel2">“持仓模板”</span>，按此模板整理数据后在通过按钮导入
          </div>
          <div>导入数据将覆盖未提交部分交易录入，请注意</div>
          <div>证券买卖日期统一为调仓日期{{ updateDate }}</div>
          <div>仅支持上传xls,xlsx格式文件</div>
        </div>
        <div>
          <!-- <el-button type="primary">上传文件导入</el-button> -->
          <el-upload :auto-upload="false"
                     :file-list="appendixList2"
                     :on-change="changeAppendix2"
                     :on-remove="changeAppendix2"
                     :show-file-list="false"
                     :multiple="false"
                     accept=".xls,.xlsx">
            <el-button type="primary">上传文件导入</el-button>
          </el-upload>
        </div>
      </div>
      <div style="margin-bottom: 16px">
        <el-table height="400px"
                  :data="dataTableModel2">
          <el-table-column align="gotoleft"
                           prop="a"
                           label="证券代码"></el-table-column>
          <el-table-column align="gotoleft"
                           prop="c"
                           label="持有权重"></el-table-column>
          <el-table-column align="gotoleft"
                           prop="e"
                           label="调仓说明"></el-table-column>
        </el-table>
      </div>
      <div style="text-align: right">
        <el-button @click="showAddTradFileHold = false"
                   type="">取消</el-button>
        <el-button type="primary"
                   @click="addExcel2()">确认</el-button>
      </div>
    </el-dialog>
    <!-- 等权重调仓 -->
    <el-dialog v-loading="loadingxS"
               :close-on-click-modal="false"
               class="ADdialog"
               width="334px"
               :visible.sync="showequial"
               title="等权重调仓">
      <div slot="title">
        <span style="
						font-family: 'PingFang';
						font-style: normal;
						font-weight: 500;
						font-size: 16px;
						line-height: 24px;
						color: rgba(0, 0, 0, 0.85);
						width: 100%;
					">等权重调仓</span>
      </div>
      <div style="width: 100%; height: 1px; background: rgba(0, 0, 0, 0.06); margin-bottom: 16px"></div>
      <div style="margin-bottom: 16px"><span style="margin-left: 8px"></span>总资产：{{ moneyall }}</div>
      <div style="margin-bottom: 16px; margin-left: 8px">
        <div>目标调仓比例(%)：</div>
        <div>
          <el-input style="width: 220px"
                    v-model="equial.edit"
                    placeholder=""></el-input>
        </div>
      </div>
      <div style="margin-bottom: 16px; margin-left: 8px">
        目标市值：<el-input style="width: 220px"
                  v-model="equial.all"
                  placeholder=""></el-input>
      </div>
      <div style="text-align: right">
        <el-button @click="showequial = false"
                   type="">取消</el-button>
        <el-button @click="DoComEquityWeight"
                   type="primary">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import Driver from 'driver.js';
// import 'driver.js/dist/driver.min.css';
import { getComQuaList, getQuaDetail, fundPrice, CombinationHolding, ComEquityWeight } from '@/api/pages/SystemMixed.js';
// import { header_search_all } from '@/api/pages/tools/compare.js';
export default {
  props: {},
  //import引入的组件需要注入到对象中才能使用
  components: {},
  filters: {
    fix2p (value) {
      return value &&
        value != '' &&
        value != '--' &&
        value != '- -' &&
        JSON.stringify(value) != '[]' &&
        JSON.stringify(value) != '{}' &&
        value != 'NAN' &&
        value != 'nan'
        ? (Number(value) * 100).toFixed(2) + '%'
        : '--';
    },
    fix2 (value) {
      return value &&
        value != '' &&
        value != '--' &&
        value != '- -' &&
        JSON.stringify(value) != '[]' &&
        JSON.stringify(value) != '{}' &&
        value != 'NAN' &&
        value != 'nan'
        ? Number(value).toFixed(2)
        : '--';
    },
    fixY (value) {
      return value &&
        value != '' &&
        value != '--' &&
        value != '- -' &&
        JSON.stringify(value) != '[]' &&
        JSON.stringify(value) != '{}' &&
        value != 'NAN' &&
        value != 'nan'
        ? Number(value / 100000000).toFixed(2) + '亿'
        : '--';
    },
    fixW (value) {
      return value &&
        value != '' &&
        value != '--' &&
        value != '- -' &&
        JSON.stringify(value) != '[]' &&
        JSON.stringify(value) != '{}' &&
        value != 'NAN' &&
        value != 'nan'
        ? Number(value / 10000).toFixed(2) + '万'
        : '--';
    }
  },
  data () {
    //这里存放数据
    return {
      moneyMin: 0,
      loadingxS: false,
      loadingx: false,
      loadingTradInput: false,
      appendixList2: [],
      appendixList: [],
      loadingfalg: false,
      showMoney: false,
      show: false,
      comid: '',
      updateDate: '',
      options: [],
      Time: '',
      TimeOrigin: '',
      description: '',
      money: 0,
      moneyall: 0,
      allmoney: 0,
      tableData: [],
      showAddTrad: false,
      showAddTradFile: false,
      showAddTradFileHold: false,
      showequial: false,
      // 现金存取
      addMoney: {
        in: '0.00',
        out: '0.00',
        result: '0.00',
        result2: '0.00'
      },
      moneyChange: 0,
      // 交易录入
      trad: {
        code: '',
        action: true,
        number: '0.00',
        price: '0.00',
        PriceFlag: true,
        description: ''
      },
      // 等权重调仓
      equial: {
        edit: '0.00',
        all: '0.00'
      },
      loadingtable: false,
      dataTable: [],
      tableDataDetail: [],
      havefundmanager: [],
      dataTableModel: [
        { a: '000001.OF', c: '5', e: '买入', f: '调仓说明' },
        { a: '000001.OF', c: '5', e: '买入', f: '调仓说明' },
        { a: '000001.OF', c: '5', e: '买入', f: '调仓说明' },
        { a: '000001.OF', c: '5', e: '买入', f: '调仓说明' },
        { a: '000001.OF', c: '5', e: '买入', f: '调仓说明' },
        { a: '000001.OF', c: '5', e: '买入', f: '调仓说明' },
        { a: '000001.OF', c: '5', e: '买入', f: '调仓说明' }
      ],
      dataTableModel2: [
        { a: '000001.OF', c: '10', e: '调仓说明' },
        { a: '000001.OF', c: '15', e: '调仓说明' },
        { a: '000001.OF', c: '15', e: '调仓说明' },
        { a: '000001.OF', c: '15', e: '调仓说明' },
        { a: '000001.OF', c: '15', e: '调仓说明' },
        { a: '000001.OF', c: '15', e: '调仓说明' },
        { a: '000001.OF', c: '15', e: '调仓说明' }
      ],
      showUpdate: false
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    showEquityAll () {
      this.showequial = true;
      this.equial.all = this.moneyall;
      this.equial.edit = this.tableData?.length == 0 ? 0 : (100 / this.tableData?.length).toFixed(2);
    },
    changeCode () {
      this.trad.PriceFlag = false;
      this.changeTradPrice();
    },
    changeDate () {
      if (this.FUNC.isEmpty(this.TimeOrigin)) {
        if (this.updateDate < this.TimeOrigin) {
          this.$message.warning('调仓日期不能小于最后一次调仓日期');
          this.updateDate = '';
        }
      }
    },
    showdialog (id, date) {
      // 清空状态
      this.Time = this.TimeOrigin;
      this.tableData = [];
      this.moneyChange = 0;
      this.loadingTradInput = false;

      this.dataTableModel = [
        { a: '000001.OF', c: '5', e: '买入', f: '调仓说明' },
        { a: '000001.OF', c: '5', e: '买入', f: '调仓说明' },
        { a: '000001.OF', c: '5', e: '买入', f: '调仓说明' },
        { a: '000001.OF', c: '5', e: '买入', f: '调仓说明' },
        { a: '000001.OF', c: '5', e: '买入', f: '调仓说明' },
        { a: '000001.OF', c: '5', e: '买入', f: '调仓说明' },
        { a: '000001.OF', c: '5', e: '买入', f: '调仓说明' }
      ];
      this.dataTableModel2 = [
        { a: '000001.OF', c: '10', e: '调仓说明' },
        { a: '000001.OF', c: '15', e: '调仓说明' },
        { a: '000001.OF', c: '15', e: '调仓说明' },
        { a: '000001.OF', c: '15', e: '调仓说明' },
        { a: '000001.OF', c: '15', e: '调仓说明' },
        { a: '000001.OF', c: '15', e: '调仓说明' },
        { a: '000001.OF', c: '15', e: '调仓说明' }
      ];
      this.tableDataDetail = [];
      this.appendixList2 = [];
      this.appendixList = [];
      this.showAddTradFile = false;
      this.showAddTradFileHold = false;
      // end
      this.comid = id;
      this.show = true;
      if (JSON.parse(localStorage.getItem('portfolioDriver')) != '1') {
        this.setDriver();
      }
      this.getData(date);
    },
    setDriver () {
      const driver = new Driver({
        animate: true,
        doneBtnText: '我知道了',
        closeBtnText: '跳过', //  关闭按钮文案
        nextBtnText: '下一步', // 下一步的按钮文案
        prevBtnText: '上一步' // 上一步的按钮文案
      });
      setTimeout(() => {
        // Define the steps for introduction
        driver.defineSteps([
          {
            element: '#step1',
            popover: {
              // className: 'step1',
              title: '操作提示1',
              description: '请先选择调仓日期，此处为所有提交操作的上传日期，例如上传交易流水的日期',
              position: 'right'
            }
          },
          {
            element: '#step2',
            popover: {
              title: '操作提示2',
              description: '本系统模拟组合不支持融资。如果调仓前现金不足，则会拒绝调仓请求。请确保调仓时现金足够。',
              position: 'top'
            }
          },
          {
            element: '.step3',
            popover: {
              title: '操作提示3',
              description: '随后便可进行调仓操作，需注意使用交易录入按钮进行调仓落入全部完成后，需要在表格下方点击确定才能提交调仓操作。',
              position: 'right'
            }
          }
        ]);

        // Start the introduction

        driver.start();
      }, 500);
      try {
        window.localStorage.setItem('portfolioDriver', JSON.stringify('1'));
      } catch (err) {
        console.log(err);
        this.$message.warning('数据量过大,无法载入缓存');
      }
    },
    // 根据combinationid获取调仓季度列表
    async getData (date) {
      // 清空状态
      this.Time = this.TimeOrigin;
      this.tableData = [];
      this.moneyChange = 0;
      this.loadingTradInput = false;

      this.dataTableModel = [
        { a: '000001.OF', c: '5', e: '买入', f: '调仓说明' },
        { a: '000001.OF', c: '5', e: '买入', f: '调仓说明' },
        { a: '000001.OF', c: '5', e: '买入', f: '调仓说明' },
        { a: '000001.OF', c: '5', e: '买入', f: '调仓说明' },
        { a: '000001.OF', c: '5', e: '买入', f: '调仓说明' },
        { a: '000001.OF', c: '5', e: '买入', f: '调仓说明' },
        { a: '000001.OF', c: '5', e: '买入', f: '调仓说明' }
      ];
      this.dataTableModel2 = [
        { a: '000001.OF', c: '10', e: '调仓说明' },
        { a: '000001.OF', c: '15', e: '调仓说明' },
        { a: '000001.OF', c: '15', e: '调仓说明' },
        { a: '000001.OF', c: '15', e: '调仓说明' },
        { a: '000001.OF', c: '15', e: '调仓说明' },
        { a: '000001.OF', c: '15', e: '调仓说明' },
        { a: '000001.OF', c: '15', e: '调仓说明' }
      ];
      this.tableDataDetail = [];
      this.appendixList2 = [];
      this.appendixList = [];
      this.showAddTradFile = false;
      this.showAddTradFileHold = false;
      // end
      this.updateDate = this.moment(new Date()).format('YYYY-MM-DD');
      let { data, mtymessage, mtycode } = await getComQuaList({
        combination_id: this.comid
      });
      if (mtycode == 200) {
        this.options = data.reverse();
        if (this.options.length > 0) {
          this.Time = this.options[0];
          this.TimeOrigin = this.options[0];
          this.getDetailsHold();
        }
        if (data.length == 0) {
          this.Time = '';
          this.TimeOrigin = '';
          this.money = 0;
          this.moneyMin = 0;
          this.description = '';
          this.tableData = [];
          this.updateDate = date || '';
          // if (
          // 	localStorage.getItem('mty_portfolio_founddate') != null &&
          // 	localStorage.getItem('mty_portfolio_founddate') != undefined &&
          // 	localStorage.getItem('mty_portfolio_founddate') != 'undefined'
          // ) {
          // 	this.updateDate = JSON.parse(localStorage.getItem('mty_portfolio_founddate'));
          // }
        }
      }
      // 空数据不处理未创建相关组合
    },
    // 获取持仓详情Time为不通时间列表
    async getDetailsHold () {
      this.loadingtable = true;
      let { data, mtymessage, mtycode, description, surplus_assets } = await getQuaDetail({ combination_id: this.comid, date: this.Time });
      if (mtycode == 200) {
        this.tableData = data || [];
        this.description = description;
        this.money = Number(surplus_assets) || 0;
        this.moneyMin = Number(surplus_assets) || 0;
        if (this.tableData.length > 0) {
          this.moneyall = this.tableData[0].asset;
        } else {
          this.moneyall = 0;
          this.tableData = [];
        }
      } else {
        this.$message.warning(mtymessage);
        this.money = 0;
        this.moneyall = 0;
        this.moneyMin = 0;
        this.description = '';
        this.tableData = [];
      }
      this.loadingtable = false;
    },
    // 重置修改
    resetUpdate () {
      this.Time = this.TimeOrigin;
      this.tableData = [];
      this.moneyChange = 0;
      this.loadingTradInput = false;

      this.dataTableModel = [
        { a: '000001.OF', c: '5', e: '买入', f: '调仓说明' },
        { a: '000001.OF', c: '5', e: '买入', f: '调仓说明' },
        { a: '000001.OF', c: '5', e: '买入', f: '调仓说明' },
        { a: '000001.OF', c: '5', e: '买入', f: '调仓说明' },
        { a: '000001.OF', c: '5', e: '买入', f: '调仓说明' },
        { a: '000001.OF', c: '5', e: '买入', f: '调仓说明' },
        { a: '000001.OF', c: '5', e: '买入', f: '调仓说明' }
      ];
      this.dataTableModel2 = [
        { a: '000001.OF', c: '10', e: '调仓说明' },
        { a: '000001.OF', c: '15', e: '调仓说明' },
        { a: '000001.OF', c: '15', e: '调仓说明' },
        { a: '000001.OF', c: '15', e: '调仓说明' },
        { a: '000001.OF', c: '15', e: '调仓说明' },
        { a: '000001.OF', c: '15', e: '调仓说明' },
        { a: '000001.OF', c: '15', e: '调仓说明' }
      ];
      this.tableDataDetail = [];
      this.appendixList2 = [];
      this.appendixList = [];
      this.showAddTradFile = false;
      this.showAddTradFileHold = false;

      this.getDetailsHold();
    },
    // 现金存取
    moneyInOut () {
      if (Number(this.addMoney.result) < 0) this.$message.error('不支持借款');
      else {
        this.moneyChange += this.addMoney.result - this.money;
        this.money = this.addMoney.result;
        this.moneyMin = this.addMoney.result2;
        this.showMoney = false;
        this.addMoney.in = '0.00';
        this.addMoney.out = '0.00';
      }
    },
    // 存
    changein () {
      this.addMoney.result = Number(this.money) + Number(this.addMoney.in) - Number(this.addMoney.out);
      this.addMoney.result2 = Number(this.moneyMin) + Number(this.addMoney.in) - Number(this.addMoney.out);
    },
    // 取
    changeout () {
      this.addMoney.result = Number(this.money) + Number(this.addMoney.in) - Number(this.addMoney.out);
      this.addMoney.result2 = Number(this.moneyMin) + Number(this.addMoney.in) - Number(this.addMoney.out);
    },
    // 交易录入
    // 搜索股票基金
    async searchpeople (query) {
      ////console.log(query)
      ////console.log(this.values)
      /* 	let that = this;
      that.havefundmanager = [];
      let data = await header_search_all({ message: query });
      if (this.FUNC.isEmpty(data)) {
        let temparr = [
          {
            label: '基金产品',
            options: []
          },
          {
            label: '股票',
            options: []
          }
        ];
        for (let i = 0; i < data.length; i++) {
          if (data[i].flag === 'fund') {
            temparr[0].options.push(data[i]);
          } else if (data[i].flag == 'stock') {
            temparr[1].options.push(data[i]);
          }
        }
        that.havefundmanager = temparr;
        that.loading = false;
      } */
    },
    submitTrad () {
      let tempmoney = 0;
      if (this.moneyall == 0) {
        tempmoney = this.money;
      } else {
        tempmoney = this.moneyall;
      }
      // if (this.trad.action == true) {
      // 	if (Number(this.moneyMin) < Number(tempmoney * this.trad.number * 0.01)) {
      // 		this.$message.error('剩余金额不足');
      // 		return false;
      // 	}
      // } else {
      // 	let moneyX = 0;
      // 	for (let i = 0; i < this.tableData.length; i++) {
      // 		if (this.tableData[i].code == this.trad.code.split('|')[0]) {
      // 			moneyX += Number(this.tableData[i].totalmv);
      // 		}
      // 	}
      // 	if (moneyX < Number(tempmoney * this.trad.number * 0.01)) {
      // 		this.$message.error('可卖出数量不足');
      // 		return false;
      // 	}
      // }
      if (String(this.trad.price) == '0.00') {
        this.$message.error('成交价格不能为0');
      } else if (String(this.trad.number) == '0.00' || String(this.trad.number) == '0') {
        this.$message.error('交易权重不能为0');
      } else if (!this.FUNC.isEmpty(this.trad.code)) {
        this.$message.error('基金代码不能为空');
      } else {
        // console.log(this.trad.action);
        // if (
        // 	Number(this.moneyMin) -
        // 		(this.trad.action ? Number(tempmoney * this.trad.number * 0.01) : -1 * Number(tempmoney * this.trad.number * 0.01)) >
        // 	0
        // ) {
        // 	this.moneyMin =
        // 		Number(this.moneyMin) -
        // 		(this.trad.action ? Number(tempmoney * this.trad.number * 0.01) : -1 * Number(tempmoney * this.trad.number * 0.01));

        if (this.trad.action == true) {
          this.tableData.push({
            code: this.trad.code.split('|')[0],
            name: this.trad.code.split('|')[1],
            holdings: (tempmoney * this.trad.number * 0.01) / this.trad.price,
            begin_nav: this.trad.price,
            totalmv: tempmoney * this.trad.number * 0.01,
            nav: '--',
            date: '--',
            weight: this.trad.number * 0.01
          });
          this.tableDataDetail.push({
            code: this.trad.code.split('|')[0],
            holdings: this.trad.number * 0.01,
            begin_nav: this.trad.price,
            description: this.trad.description,
            totalmv: tempmoney * this.trad.number * 0.01,
            nav: '--',
            date: '--',
            weight: '--'
          });
          // this.money = Number(this.money) - tempmoney * this.trad.number;
        } else {
          this.tableData.push({
            code: this.trad.code.split('|')[0],
            name: this.trad.code.split('|')[1],
            holdings: (tempmoney * this.trad.number * -1 * 0.01) / this.trad.price,
            begin_nav: this.trad.price,
            totalmv: tempmoney * this.trad.number * -1 * 0.01,
            nav: '--',
            weight: this.trad.number * -1 * 0.01,
            date: '--'
          });
          this.tableDataDetail.push({
            code: this.trad.code.split('|')[0],
            holdings: this.trad.number * -1 * 0.01,
            begin_nav: this.trad.price,
            description: this.trad.description,
            totalmv: tempmoney * this.trad.number * -1 * 0.01,
            nav: '--',
            weight: '--',
            date: '--'
          });
          // this.money = Number(this.money) + tempmoney * this.trad.number;
        }
        this.showAddTrad = false;
        // } else {
        // 	this.$message.error('剩余金额不足');
        // }
      }
    },
    // 切换签收价和自定义
    // TODO
    async changeTradPrice () {
      if (this.trad.PriceFlag) {
      } else {
        // 请求牵手价格
        this.loadingfalg = true;
        let { data, mtymessage, mtycode } = await fundPrice({
          id: this.trad.code.split('|')[0],
          date: this.updateDate,
          flag: this.trad.code.split('|')[2]
        });
        if (mtycode == 200) {
          this.trad.price = Number(data).toFixed(2);
        } else {
          this.$message.error('前收价格获取失败');
          this.trad.price = '0.00';
        }
        this.loadingfalg = false;
      }
    },
    // 交易模板导入导出

    exportExcel () {
      const { export_json_to_excel } = require('@/vendor/Export2Excel');
      var list = [];
      let tHeader = [];

      tHeader = ['证券代码', '买卖权重', '买卖方向', '调仓说明'];
      // ////console.log(this.colums)
      if (this.tableData.length == 0) {
        for (let i = 0; i < this.dataTableModel.length; i++) {
          list[i] = [];
          list[i][0] = this.dataTableModel[i].a;
          list[i][1] = this.dataTableModel[i].c;
          list[i][2] = this.dataTableModel[i].e;
          list[i][3] = this.dataTableModel[i].f;
        }
      } else {
        for (let i = 0; i < this.tableData.length; i++) {
          list[i] = [];
          list[i][0] = this.tableData[i].code;
          list[i][1] = 5;
          list[i][2] = '买入';
          list[i][3] = '根据市场行情调整持仓......';
        }
        list[this.tableData.length] = [];
        list[this.tableData.length][0] = '000057';
        list[this.tableData.length][1] = '5';
        list[this.tableData.length][2] = '卖出';
        list[this.tableData.length][3] = '根据市场行情调整持仓......';
      }
      export_json_to_excel(tHeader, list, '交易流水模板');
    },
    // 持仓模板导出
    exportExcel2 () {
      const { export_json_to_excel } = require('@/vendor/Export2Excel');
      var list = [];
      let tHeader = [];

      tHeader = ['证券代码', '持有权重', '调仓说明'];
      // ////console.log(this.colums)
      if (this.tableData.length == 0) {
        for (let i = 0; i < this.dataTableModel2.length; i++) {
          list[i] = [];
          list[i][0] = this.dataTableModel2[i].a;
          list[i][1] = this.dataTableModel2[i].c;
          list[i][2] = this.dataTableModel2[i].e;
        }
      } else {
        for (let i = 0; i < this.tableData.length; i++) {
          list[i] = [];
          list[i][0] = this.tableData[i].code;
          list[i][1] = 15;
          list[i][2] = '调仓说明';
        }
        list[this.tableData.length] = [];
        list[this.tableData.length][0] = '000057';
        list[this.tableData.length][1] = '15';
        list[this.tableData.length][2] = '调仓说明';
      }
      export_json_to_excel(tHeader, list, '持仓模板');
    },
    // 更新表格数据
    updateFund (code, name, holdings, begin_nav) {
      this.showUpdate = true;
    },
    // 手动提交change
    async submitChange () {
      if (this.moneyMin == 0 && this.options.length == 0) {
        this.$message.warning('请存入现金，无剩余金额无法调仓');
        return false;
      }
      this.loadingx = true;
      let portfolio_holding = [];
      for (let i = 0; i < this.tableData.length; i++) {
        if (portfolio_holding.findIndex((item) => item.code == this.tableData[i].code) >= 0) {
          portfolio_holding[portfolio_holding.findIndex((item) => item.code == this.tableData[i].code)].holdings =
            Number(portfolio_holding[portfolio_holding.findIndex((item) => item.code == this.tableData[i].code)].holdings) +
            Number(this.tableData[i].holdings);
          portfolio_holding[portfolio_holding.findIndex((item) => item.code == this.tableData[i].code)].totalmv =
            Number(portfolio_holding[portfolio_holding.findIndex((item) => item.code == this.tableData[i].code)].totalmv) +
            Number(this.tableData[i].totalmv);
          portfolio_holding[portfolio_holding.findIndex((item) => item.code == this.tableData[i].code)].begin_nav =
            portfolio_holding[portfolio_holding.findIndex((item) => item.code == this.tableData[i].code)].totalmv /
            portfolio_holding[portfolio_holding.findIndex((item) => item.code == this.tableData[i].code)].holdings;
        } else {
          portfolio_holding.push(this.FUNC.deepClone(this.tableData[i]));
        }
      }
      let { data, mtymessage, mtycode } = await CombinationHolding({
        description: this.description,
        date: this.updateDate,
        surplus_assets: this.money,
        portfolio_holding: portfolio_holding,
        portfolio_holding_detail: this.tableDataDetail,
        combination_id: this.comid,
        flag: 0
      });
      if (mtycode == 200) {
        this.tableDataDetail = [];
        this.getData();
        this.$emit('updatePort');
        if (this.$route.path.indexOf('portfolioAnalysis') >= 0) {
          this.show = false;
          location.reload();
        } else {
          this.show = true;
        }
        this.loadingx = false;
        this.$message.success('成功');
      } else {
        this.$message.error(mtymessage);
        this.tableDataDetail = [];
        this.getData();
        this.loadingx = false;
      }
    },
    // 等权重调仓
    // TODO
    async DoComEquityWeight () {
      this.loadingxS = true;
      let { data, mtymessage, mtycode } = await ComEquityWeight({
        combination_id: this.comid,
        weight: this.equial.edit,
        totalmv: this.equial.all,
        date: this.updateDate
      });
      if (mtycode == 200) {
        this.$message.success('成功调仓，生成新一期记录');
        this.showequial = false;
        this.loadingxS = false;
        this.$emit('updatePort');
        if (this.$route.path.indexOf('portfolioAnalysis') >= 0) {
          window.reload();
          this.show = false;
        } else {
          this.show = true;
        }
        this.getData();
      } else {
        this.loadingxS = false;
        this.$message.error(mtymessage);
      }
    },
    // excel导入
    changeAppendix (file, fileList) {
      const _this = this;
      _this.dataTableModel = [];
      const fileName = file.name;
      const reader = new FileReader();
      reader.readAsArrayBuffer(file.raw);
      reader.onload = function () {
        const buffer = reader.result;
        const bytes = new Uint8Array(buffer);
        const length = bytes.byteLength;
        let binary = '';
        for (let i = 0; i < length; i++) {
          binary += String.fromCharCode(bytes[i]);
        }
        // const XLSX = require('xlsx');
        const wb = XLSX.read(binary, {
          type: 'binary'
        });
        const outdata = XLSX.utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]]);
        outdata.forEach((i) => {
          let obj = {
            a: i['证券代码'],
            c: i['买卖权重'],
            e: i['买卖方向'],
            f: i['调仓说明']
          };
          if (i['证券代码'] == '' && i['买卖权重'] == '' && i['买卖方向'] == '') {
          } else {
            _this.dataTableModel.push(obj); //此处是把数据添加到表格中
          }
        });
      };
    },
    // excel持仓导入
    changeAppendix2 (file, fileList) {
      const _this = this;
      _this.dataTableModel2 = [];
      const fileName = file.name;
      const reader = new FileReader();
      reader.readAsArrayBuffer(file.raw);
      reader.onload = function () {
        const buffer = reader.result;
        const bytes = new Uint8Array(buffer);
        const length = bytes.byteLength;
        let binary = '';
        for (let i = 0; i < length; i++) {
          binary += String.fromCharCode(bytes[i]);
        }
        // const XLSX = require('xlsx');
        const wb = XLSX.read(binary, {
          type: 'binary'
        });
        const outdata = XLSX.utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]]);
        // console.log(outdata);
        outdata.forEach((i) => {
          console.log(i);
          let obj = {
            a: i['证券代码'],
            c: i['持有权重'],
            e: i['调仓说明']
          };
          if (i['证券代码'] == '' || i['持有权重'] == '') {
          } else {
            _this.dataTableModel2.push(obj); //此处是把数据添加到表格中
          }
        });
      };
    },
    // 交易流水导入
    async addExcel () {
      if (this.moneyMin == 0 && this.options.length == 0) {
        this.$message.warning('请存入现金，无剩余金额无法调仓');
        return false;
      }
      this.loadingTradInput = true;
      try {
        let falgs = false;
        for (let i = 0; i < this.dataTableModel.length; i++) {
          if (this.dataTableModel[i].a != '000001.OF') {
            falgs = true;
          }
        }
        if (this.dataTableModel.length != 7) {
          falgs = true;
        }
        if (falgs) {
          let portfolio_holding = [];
          for (let i = 0; i < this.dataTableModel.length; i++) {
            if (
              !this.FUNC.isEmpty(this.dataTableModel[i].a) ||
              !this.FUNC.isEmpty(this.dataTableModel[i].c) ||
              !this.FUNC.isEmpty(this.dataTableModel[i].e)
            ) {
              this.$message.error('excel有未填项,无法写入数据');
              this.loadingTradInput = false;
              return false;
            }
          }
          let { data, mtymessage, mtycode } = await CombinationHolding({
            description: this.description,
            date: this.updateDate,
            surplus_assets: this.money,
            portfolio_holding: this.tableData,
            portfolio_holding_detail: this.dataTableModel.map((item) => {
              return {
                begin_nav: item.d,
                code: item.a,
                date: '--',
                holdings: item.e == '卖出' ? -1 * item.c * 0.01 : item.c * 0.01,
                name: item.b,
                nav: '--',
                weight: '--',
                totalmv: item.e == '卖出' ? -1 * item.d * item.c : item.d * item.c,
                description: item.f
              };
            }),
            combination_id: this.comid,
            flag: 0
          });
          if (mtycode == 200) {
            this.getData();
            this.$emit('updatePort');
            if (this.$route.path.indexOf('portfolioAnalysis') >= 0) {
              this.show = false;
              window.reload();
            } else {
              this.show = true;
            }
            this.showAddTradFile = false;
            this.loadingTradInput = false;
            this.$message.success('导入成功');
          } else {
            this.$message.error(mtymessage);
            this.loadingTradInput = false;
            this.resetUpdate();
          }
        } else {
          this.loadingTradInput = false;
          this.$message.warning('请勿使用演示模板数据');
        }
      } catch (err) {
        this.$message.error('异常原因导致中断');
        console.log(err);
        this.resetUpdate();
      }
    },
    // 持仓文件导入
    async addExcel2 () {
      if (this.moneyMin == 0 && this.options.length == 0) {
        this.$message.warning('请存入现金，无剩余金额无法调仓');
        return false;
      }
      this.loadingTradInput = true;
      try {
        let falgs = false;
        for (let i = 0; i < this.dataTableModel2.length; i++) {
          if (this.dataTableModel2[i].a != '000001.OF') {
            falgs = true;
          }
        }
        if (this.dataTableModel2.length != 7) {
          falgs = true;
        }
        if (falgs) {
          let portfolio_holding = [];
          let temparrX = [];
          for (let i = 0; i < this.dataTableModel2.length; i++) {
            if (!this.FUNC.isEmpty(this.dataTableModel2[i].a) || !this.FUNC.isEmpty(this.dataTableModel2[i].c)) {
              this.loadingTradInput = false;
              this.$message.error('excel有未填项,无法写入数据');
              return false;
            }
            if (temparrX.indexOf(this.dataTableModel2[i].a) < 0) {
              temparrX.push(this.dataTableModel2[i].a);
            } else {
              this.loadingTradInput = false;
              this.$message.error('导入失败，导入持仓中同一只产品出现了两次');
              return false;
            }
          }
          let { data, mtymessage, mtycode } = await CombinationHolding({
            description: this.description,
            date: this.updateDate,
            surplus_assets: this.money,
            portfolio_holding: this.dataTableModel2.map((item) => {
              return {
                begin_nav: item.d,
                code: item.a,
                date: '--',
                holdings: item.c * 0.01,
                name: item.b,
                nav: '--',
                weight: '--',
                totalmv: item.d * item.c,
                description: item.e
              };
            }),
            portfolio_holding_detail: [],
            combination_id: this.comid,
            flag: 1
          });
          if (mtycode == 200) {
            this.$emit('updatePort');
            if (this.$route.path.indexOf('portfolioAnalysis') >= 0) {
              this.show = false;
              window.reload();
            } else {
              this.show = true;
            }
            this.getData();
            this.showAddTradFile = false;
            this.loadingTradInput = false;
            this.$message.success('导入成功');
          } else {
            this.$message.error(mtymessage);
            this.loadingTradInput = false;
            this.resetUpdate();
          }
        } else {
          this.$message.warning('请勿使用演示模板数据');
        }
      } catch (err) {
        this.$message.error('异常原因导致中断');
        console.log(err);
        this.resetUpdate();
      }
      this.loadingTradInput = false;
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () { },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style>
.ADdialog .el-dialog__body {
	padding-top: 0 !important;
}
</style>
<style lang="scss" scoped>
//@import url(); 引入公共css类
</style>
