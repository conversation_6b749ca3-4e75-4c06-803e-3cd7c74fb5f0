<template>
	<div class="chart_one" v-show="!chicangfenleiempty" id="positionClass">
		<analysis-card-title title="持仓分类" image_id="positionClass"></analysis-card-title>
		<div class="charts_fill_class">
			<v-chart
				ref="positionClass"
				v-loading="chicangfenleiempty"
				element-loading-text="暂无数据"
				element-loading-spinner="el-icon-document-delete"
				element-loading-background="rgba(239, 239, 239, 0.5)"
				class="charts_one_class"
				autoresize
				:options="picall2"
			/>
		</div>
	</div>
</template>

<script>
import { exportTitle, exportChart } from '@/utils/exportWord.js';
import { barChartOption } from '@/utils/chartStyle.js';
import { getBondClassDetails } from '@/api/pages/Analysis.js';
// 持仓分类
export default {
	name: 'positionClass',
	data() {
		return {
			chicangfenleiempty: true,
			picall2: {},
			info: {}
		};
	},
	methods: {
		async getBondClassDetails() {
			let data = await getBondClassDetails({
				code: this.info.code,
				type: this.info.type,
				flag: this.info.flag,
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			if (data?.mtycode == 200) {
				this.getChartData(
					data?.data.sort((a, b) => {
						return this.moment(this.moment(a.yearqtr, 'YYYY QQ').format()).isBefore(this.moment(b.yearqtr, 'YYYY QQ').format()) ? -1 : 1;
					})
				);
			} else {
				this.chicangfenleiempty = true;
			}
		},
		getData(info) {
			this.info = info;
			this.getBondClassDetails();
		},
		getChartData(data) {
			// 持仓分类图
			if (data.length == 0) {
				this.chicangfenleiempty = true;
			} else {
				this.chicangfenleiempty = false;
				let tempser = [];
				for (let i = 0; i < data.length; i++) {
					let index = tempser.findIndex((v) => v.name == data[i].bondClass);
					if (index == -1) {
						tempser.push({
							name: data[i]?.bondClass,
							type: 'bar',
							stack: '总量',
							barWidth: '85%',
							itemStyle: {
								normal: {
									label: {
										show: true,
										position: 'inside', //数据在中间显示
										textStyle: {
											fontSize: '14px'
										},
										//  formatter: '{c}%' //百分比显示
										formatter: function (val) {
											if (val.value > 10) {
												return Number(val.value).toFixed(2) + '%';
											} else {
												return '';
											}
										}
									}
								}
							},
							data: [[data[i]?.yearqtr, data[i]?.ratioinN]]
						});
					} else {
						tempser[index].data.push([data[i]?.yearqtr, data[i]?.ratioinN]);
					}
				}
				this.picall2 = barChartOption({
					toolbox: 'none',
					// tooltip: {
					// 	formatter: (params) => {
					// 		let str = `时间: ${params[0].axisValue} <br />`;
					// 		for (let i = params.length - 1; i >= 0; i--) {
					// 			let dotHtml =
					// 				'<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' +
					// 				params[i].color +
					// 				'"></span>';
					// 			str += dotHtml + `${params[i].seriesName}: ${params[i].value + '%'}<br />`;
					// 		}
					// 		return str;
					// 	}
					// },
					tooltip: {
						formatter: (params) => {
							let str = '';
							str += `日期: ${params[0].axisValue} <br />`;
							for (let i = params.length - 1; i >= 0; i--) {
								let dotHtml =
									'<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' +
									params[i].color +
									'"></span>';
								str += `<div style="display:flex;justify-content:space-between;align-item:center;">
                                        <div>${dotHtml}${params[i].seriesName}:</div>
                                        <div>${!isNaN(params[i]?.value?.[1]) ? Number(params[i]?.value?.[1]).toFixed(2) + '%' : '--'}</div>
                                     </div>`;
							}
							return `<div style="padding:12px">${str}</div>`;
						}
					},
					grid: {
						top: '12px',
						bottom: '92px'
					},
					legend: {
						bottom: '0',
						data: data.map((v) => v.bondClass)
					},
					dataZoom: {
						bottom: '48px',
						start: 0,
						end: 100
					},
					xAxis: [
						{
							data: Array.from(new Set(data.map((v) => v.yearqtr)))
						}
					],
					yAxis: [
						{
							type: 'value',
							formatter: function (value) {
								return value + '%';
							}
						}
					],
					series: tempser
				});
				console.log(this.picall2);
			}
		},
		createPrintWord() {
			if (this.chicangfenleiempty) {
				return [];
			} else {
				this.$refs['positionClass'].mergeOptions({ toolbox: { show: false } });
				let height = this.$refs['positionClass'].$el.clientHeight;
				let width = this.$refs['positionClass'].$el.clientWidth;
				let chart = this.$refs['positionClass'].getDataURL({
					type: 'png',
					pixelRatio: 2,
					backgroundColor: '#fff'
				});
				this.$refs['positionClass'].mergeOptions({ toolbox: { show: true } });
				return [...exportTitle('持仓分类'), ...exportChart(chart, { width, height })];
			}
		}
	}
};
</script>

<style></style>
