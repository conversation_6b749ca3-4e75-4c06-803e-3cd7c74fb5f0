<template>
  <div class="plate-wrapper valuation-percentile-wrapper"
       v-loading="loading">
    <VerticalLineHeader :title="title"
                        showDownloadBtn
                        @downloadClick="exportExcel">
      <template slot="right">
        <el-form ref="form"
                 :model="form"
                 label-width="80px"
                 class="title-right-form">
          <el-select v-model="fundType"
                     placeholder="请选择基金类型"
                     style="margin-right: 12px"
                     @change="handleSelectHeader">
            <el-option v-for="item in fundOptions"
                       :key="item"
                       :label="item"
                       :value="item"> </el-option>
          </el-select>
          <el-select v-model="companyType"
                     clearable
                     placeholder="请选择基金公司"
                     style="margin-right: 12px; width: 140px"
                     @change="handleSelectHeader"
                     filterable>
            <el-option v-for="item in companyOptions"
                       :key="item"
                       :label="item"
                       :value="item"> </el-option>
          </el-select>
          <el-select v-model="yearValue"
                     clearable
                     placeholder="请选择季度"
                     style="margin-right: 12px; width: 120px"
                     @change="handleSelectHeader">
            <el-option v-for="item in yearOptions"
                       :key="item"
                       :label="item"
                       :value="item"> </el-option>
          </el-select>
          <el-form-item size="small"
                        label="截止日期:"
                        style="margin-right: 12px">
            <el-date-picker type="date"
                            placeholder="选择日期"
                            v-model="deadline"
                            value-format="yyyy-MM-dd"
                            style="width: 120px"
                            @change="handleSelectHeader"
                            :picker-options="pickerOptions"></el-date-picker>
          </el-form-item>
          <div style="margin-right: 12px">
            <el-radio-group class="radio-group-wrapper"
                            v-model="dateFlag"
                            size="small"
                            style="margin-left: 0 !important"
                            @change="handleSelectHeader">
              <el-radio-button label="0">近期业绩</el-radio-button>
              <el-radio-button label="1">自然年份业绩</el-radio-button>
            </el-radio-group>
          </div>
        </el-form>
      </template>
    </VerticalLineHeader>
    <div class="select-form-wrapper"
         style="margin-bottom: 20px">
      <div>
        <el-switch v-if="marketType === 'industry'"
                   v-model="value1"
                   @change="changeSwitch"
                   inactive-text="打开持仓估计"> </el-switch>
      </div>
      <!-- <el-button type="info"
                 plain>平均持有仓位：{{ averageEquityPosition }}%</el-button> -->
    </div>
    <slot name="table">
      <el-table style="width: 100%"
                :data="tableDataNow"
                :stripe="true"
                :border="true"
                @sort-change="handleSortChange">
        <template v-for="item in tableHeaderCopy">
          <el-table-column :prop="item.prop"
                           :label="item.label"
                           min-width="160"
                           :key="item.label"
                           :sortable="false"
                           v-if="marketType !== 'industry' && item.prop === 'name'"
                           align="gotoleft">
            <template slot-scope="scope">
              <el-popover placement="top"
                          width="450"
                          trigger="click"
                          @hide="hide"
                          @after-enter="showPopover(scope.$index, scope.row)"
                          popper-class="popperDialog"
                          :ref="`popover-${scope.$index}`">
                <div class="mb-16 flex_between">
                  <div>{{ scope.row[item.prop] }}</div>
                  <div v-if="title.includes('基金')">
                    <el-link @click="goDetail(scope.row.code, scope.row[item.prop])">查看详情</el-link>
                  </div>
                </div>
                <lineChart :ref="`lineChart-${scope.$index}`"></lineChart>
                <el-link slot="reference"
                         @click="scope._self.$refs[`popover-${scope.$index}`][0].doClose()">{{
									scope.row[item.prop]
								}}</el-link>
              </el-popover>
            </template>
          </el-table-column>

          <el-table-column :prop="item.prop"
                           :label="item.label"
                           min-width="120"
                           :sortable="index === 0 ? false : 'custom'"
                           :key="item.prop + item.prop"
                           v-else
                           align="gotoleft">
            <template slot-scope="scope">
              <span v-if="item.formatter">
                {{ item.formatter(scope.row[item.prop]) }}
              </span>
              <span v-else>
                {{ scope.row[item.prop] || 0 }}
              </span>
            </template>
          </el-table-column>
        </template>
      </el-table>
    </slot>

    <el-pagination style="display: flex; justify-content: right; padding-top: 16px; padding-bottom: 16px"
                   class="pagination-footer-wrapper"
                   @size-change="handleSizeChange"
                   @current-change="handleCurrentChange"
                   :current-page.sync="pageInfo.currentPage"
                   :page-sizes="[10, 20, 30, 40]"
                   :page-size="pageInfo.pageSize"
                   layout="total, sizes, prev, pager, next, jumper"
                   :total="pageInfo.total">
    </el-pagination>
  </div>
</template>
<script>
import VerticalLineHeader from './VerticalLineHeader.vue';
import lineChart from './chart/lineChart.vue';
import {
  getFundAllType,
  getAllFundCompany,
  getTimeDate,
  getMarketConfiguration,
  getRateLastYear
} from '@/api/pages/tkAnalysis/captial-market.js';
import { format } from '@/utils/getfulldate';
import stringTool from '@/pages/tkdesign/components/string.tool';
import { filter_json_to_excel } from '@/utils/exportExcel.js';
import { alphaGo } from '@/assets/js/alpha_type.js';
export default {
  name: 'heavyWarehouseInfomation',
  components: {
    VerticalLineHeader,
    lineChart
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    tableHeader: {
      type: Array,
      default () {
        return [
          {
            prop: 'name',
            label: '重仓行业'
          },
          {
            prop: 'averageWeight',
            label: '平均配置权重',
            formatter: (val) => {
              return stringTool.fix2pxx(val);
            }
          },
          {
            prop: 'lastSeasonAmplitude',
            label: '近一季变化幅度',
            formatter: (val) => {
              return stringTool.fix2pxx(val);
            }
          },
          {
            prop: 'yearToDate',
            label: '年初至今',
            formatter: (val) => {
              return stringTool.fix2px(val);
            }
          },
          {
            prop: 'lastWeek',
            label: '近1周',
            formatter: (val) => {
              return stringTool.fix2px(val);
            }
          },
          {
            prop: 'lastMounth',
            label: '近1月',
            formatter: (val) => {
              return stringTool.fix2px(val);
            }
          },
          {
            prop: 'lastSeason',
            label: '近1季',
            formatter: (val) => {
              return stringTool.fix2px(val);
            }
          },
          {
            prop: 'lastHalfYears',
            label: '近6月',
            formatter: (val) => {
              return stringTool.fix2px(val);
            }
          },
          {
            prop: 'lastYear',
            label: '近1年',
            formatter: (val) => {
              return stringTool.fix2px(val);
            }
          },
          {
            prop: 'quarterIncome',
            label: '季度间收益率',
            formatter: (val) => {
              return stringTool.fix2px(val);
            }
          }
        ];
      }
    },
    marketType: {
      type: String,
      default: 'fund'
    },
    tableHeaderCopy: []
  },
  data () {
    return {
      loading: true,
      fundType: '',
      fundOptions: [],
      companyType: '',
      companyOptions: [],
      yearValue: '',
      yearOptions: [],
      deadline: '',
      dateFlag: '0',
      form: {},
      pickerOptions: {
        disabledDate: (date) => {
          return date.getTime() > Date.now();
        }
      },
      options: [
        {
          value: '组合风险收益',
          label: '组合风险收益'
        }
      ],
      visible: false,
      comparisonValue: '',
      value1: true,
      headerForm: {
        indexType: '001',
        displayDimension: '',
        endDate: ''
      },
      pageInfo: {
        pageSize: 10,
        currentPage: 0,
        total: 0
      },
      tableData: [],
      averageEquityPosition: 0,
      tableDataNow: []
    };
  },
  async created () {
    this.deadline = this.moment().subtract(1, 'day').format('YYYY-MM-DD');
    this.tableHeaderCopy = this.tableHeader;
    await this.getFundAllType();
    this.getAllFundCompany();
    await this.getTimeDate();
    this.getData();
  },
  methods: {
    goDetail (code, name) {
      alphaGo(code, name, this.$route.path);
    },
    changeSwitch () {
      this.getData();
    },
    // 导出excel
    exportExcel () {
      let list = this.tableHeader.map((item) => {
        return {
          ...item,
          value: item.prop,
          format: ''
        };
      });
      filter_json_to_excel(list, this.tableData, this.title);
    },
    dulData () {
      let { currentPage, pageSize } = this.pageInfo;
      this.tableDataNow = this.tableData.slice((currentPage - 1) * pageSize, currentPage * pageSize);
    },
    async getFundAllType () {
      return await getFundAllType({
        marketType: this.marketType
      }).then((res) => {
        if (res.code === 200) {
          this.fundOptions = res.data.windType || [];
          this.fundType = this.fundOptions[0];
        }
      });
    },
    getAllFundCompany () {
      getAllFundCompany().then((res) => {
        if (res.code === 200) {
          this.companyOptions = res.data.company || [];
        }
      });
    },
    async getTimeDate () {
      return await getTimeDate().then((res) => {
        if (res.code === 200) {
          this.yearOptions = res.data || [];
          this.yearValue = this.yearOptions[0];
        }
      });
    },

    handleSelectHeader () {
      //接口调用
      this.getData();
    },
    handleSortChange (value) {
      //接口调用
      const { column, prop, order } = value;
      this.tableData.sort((item1, item2) => {
        const a1 = item1[prop] || 0;
        const a2 = item2[prop] || 0;
        let orderVal = order === 'ascending' ? -(a1 - a2) : a1 - a2;
        return orderVal;
      });
      this.dulData();
    },
    handleSizeChange (value) {
      this.pageInfo.currentPage = 1;
      this.pageInfo.pageSize = value;
      this.dulData();
    },
    handleCurrentChange (value) {
      this.pageInfo.currentPage = value;
      this.dulData();
    },
    showPopover (index, val) {
      this.getStockReturn(index, val);
    },
    async getStockReturn (index, value) {
      let data = await getRateLastYear({ marketType: this.marketType, code: value.code, name: value.name });
      if (data?.code == 200) {
        this.$refs[`lineChart-${index}`][0].getData(data?.data);
      }
    },
    getData () {
      this.pageInfo.currentPage = 1;
      this.loading = true;
      const date = this.deadline ? this.deadline : '';
      getMarketConfiguration({
        marketType: this.marketType,
        deadline: date,
        windType: this.fundType,
        company: this.companyType,
        dateFlag: this.dateFlag,
        yearqtr: this.yearValue,
        positionFlag: this.value1
      })
        .then((res) => {
          this.loading = false;
          if (res.code === 200) {
            this.tableData = res.data.dataList;
            this.averageEquityPosition = this.fix2(res.data.averageEquityPosition * 100);
            this.pageInfo.total = this.tableData.length;
            if (this.dateFlag === '1') {
              let dateList = [];
              this.tableData.forEach((item) => {
                item.naturalList.forEach((res) => {
                  dateList.push(res.naturalDate);
                });
              });
              dateList = [
                ...new Set(
                  dateList?.sort((a, b) => {
                    if (a < b) return 1;
                    else return -1;
                  })
                )
              ];
              const arr = dateList.map((item) => {
                return {
                  prop: item,
                  label: item,
                  formatter: (val) => {
                    return stringTool.fix2px(val);
                  }
                };
              });
              this.tableHeaderCopy =
                this.marketType === 'industry'
                  ? [
                    this.tableHeader[0],
                    {
                      prop: 'averageWeight',
                      label: '平均配置权重',
                      formatter: (val) => {
                        return stringTool.fix2pxx(val);
                      }
                    },
                    {
                      prop: 'lastSeasonAmplitude',
                      label: '近一季变化幅度',
                      formatter: (val) => {
                        return stringTool.fix2pxx(val);
                      }
                    },
                    ...arr
                  ]
                  : this.marketType === 'stock'
                    ? [
                      this.tableHeader[0],
                      {
                        prop: 'averageWeight',
                        label: '平均配置权重',
                        formatter: (val) => {
                          return stringTool.fix2pxx(val);
                        }
                      },
                      {
                        prop: 'positionNum',
                        label: '持仓基金数'
                      },
                      {
                        prop: 'positionValue',
                        label: '持仓市值（亿）'
                      },
                      {
                        prop: 'proportion',
                        label: '占股票流动市值比',
                        formatter: (val) => {
                          return stringTool.fix2px(val);
                        }
                      },
                      {
                        prop: 'lastSeasonAmplitude',
                        label: '近一季变化幅度',
                        formatter: (val) => {
                          return stringTool.fix2pxx(val);
                        }
                      },
                      ...arr
                    ]
                    : [
                      this.tableHeader[0],
                      {
                        prop: 'averageWeight',
                        label: '平均配置权重',
                        formatter: (val) => {
                          return stringTool.fix2pxx(val);
                        }
                      },
                      {
                        prop: 'positionNum',
                        label: '持仓基金数'
                      },
                      {
                        prop: 'lastSeasonAmplitude',
                        label: '近一季变化幅度',
                        formatter: (val) => {
                          return stringTool.fix2pxx(val);
                        }
                      },
                      ...arr
                    ];
              this.tableData = this.tableData?.map((item) => {
                item.naturalList?.forEach((element) => {
                  item[element.naturalDate] = element.meter;
                });
                return item;
              });
            } else {
              this.tableHeaderCopy = this.tableHeader;
            }
          } else {
            this.tableData = [];
          }
          this.dulData();
        })
        .catch(() => {
          this.tableData = [];
          this.loading = false;
        });
    },
    fix2 (value) {
      return !isNaN(value) && typeof (value * 1) == 'number' ? value.toFixed(2) : '--';
    }
  }
};
</script>
<style lang="scss" scoped>
.select-form-wrapper {
	display: flex;
	justify-content: space-between;
}
.detail-download {
	width: 32px;
	height: 32px;
	line-height: 32px;
	text-align: center;
	border: 1px solid rgba(217, 217, 217, 1);
	border-radius: 4px;
	box-sizing: border-box;
	vertical-align: middle;
	img {
		// display: block;
	}
}
</style>
