<template>
  <div>
    <div>
      <el-button @click="dialogVisible = true" icon="el-icon-plus">
        添加个股因子
      </el-button>
    </div>
    <el-dialog
        :before-close="cancel"
        :visible.sync="dialogVisible"
        class="dialog"
        title="添加个股因子"
        width="700px">
      <div class="dialog_box">
        <div class="dialog_box_main">
          <div class="main_target">
            <div class="main_target_tabs">
              <div class="tab-pane_select">
                <el-row>
                  <el-col :span="8" class="tab-pane_select_left">
                    <div style="background-color: #fff4e6;border-right:3px solid #4096ff;color:#4096ff">
                      默认分类
                    </div>
                  </el-col>
                  <el-col :span="16" class="tab-pane_select_right">
                    <el-checkbox-group class="right_checkBox" v-model="checkedList">
                      <el-checkbox v-for="(item,index) in checkItems" :label="item">{{ getZH(item) }}</el-checkbox>
                    </el-checkbox-group>
                  </el-col>
                </el-row>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="dialog_footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="confirm">保存</el-button>
      </div>
    </el-dialog>
  </div>

</template>

<script>

export default {
  data() {
    return {
      dialogVisible: false,
      checkedList: [],
      oldList: [],
      checkItems: [
        'beta',
        'momentum',
        'size',
        'growth',
        'bp',
        'leverage',
        'liquidity',
        'nonlinearsize',
        'earningyield'
      ],
    }
  },
  methods: {
    /**
     * 获取中文
     */
    getZH(data) {
      switch (data) {
        case 'beta':
          return '贝塔因子';
          break;
        case 'momentum':
          return '动量因子';
          break;
        case 'size':
          return '市值因子';
          break;
        case 'growth':
          return '成长因子';
          break;
        case 'bp':
          return '估值因子';
          break;
        case 'leverage':
          return '杠杆因子';
          break;
        case 'liquidity':
          return '流动性因子';
          break;
        case 'nonlinearsize':
          return '非线性市值因子';
          break;
        case 'earningyield':
          return '盈利';
          break;
        default:
          return '--'
          break;
      }
    },
    /**
     * 保存
     */
    confirm() {
      this.oldList = JSON.stringify(this.checkedList)
      this.dialogVisible = false
      this.$emit('updateColumn', this.checkedList)
    },
    /**
     * 取消
     */
    cancel() {
      this.dialogVisible = false
      this.checkedList = JSON.parse(this.oldList)
    },
  },
  mounted() {
    this.oldList = JSON.stringify(this.checkedList)
  },
}
</script>

<style lang="scss" scoped>
@import "../../../../tkdesign";


.pd {
  padding: 8px 16px;
}

.dialog_box {
  border-bottom: #f4f4f4 solid 1px;
  border-top: #f4f4f4 solid 1px;
  margin-top: 10px;
  font-size: 16px;

  .dialog_box_main {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px 0;

    .main_target {
      height: 100%;
      width: 100%;
      border: 1px solid #E9E9E9;
      border-radius: 4px;

      .main_target_search {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #E9E9E9;
        padding: 12px;

        .main_target_search_title {
          width: 100px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .main_target_tabs {

        .tab-pane_select {

          .tab-pane_select_left {
            border-right: 1px solid #E9E9E9;
            height: 400px;
            padding-top: 10px;

            div {
              padding-left: 16px;
              line-height: 40px;
            }

            div:hover {
              background-color: #fff4e6;
              border-right: 3px solid #4096ff;
              color: #4096ff;
            }
          }

          .tab-pane_select_right {
            padding: 10px 16px;

            .right_checkBox {
              display: flex;

              flex-direction: column;

              .el-checkbox {
                margin: 10px 0;
              }
            }
          }
        }
      }
    }
  }
}

.dialog_footer {
  display: flex;
  justify-content: flex-end;
  padding: 10px;
}
</style>

<style>
.dialog .el-dialog__body {
  padding: 0;
}

.main_target_tabs .el-tabs__header {
  margin-bottom: 0;
}
</style>
