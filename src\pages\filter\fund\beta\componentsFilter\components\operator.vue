<template>
	<div style="display: flex; align-items: center">
		<el-dropdown @command="command" style="margin-right: 16px">
			<el-button type="primary"> {{ iconFlag != '' ? iconFlag : '算子' }}<i class="el-icon-arrow-down el-icon--right"></i> </el-button>
			<el-dropdown-menu slot="dropdown">
				<el-dropdown-item command="平均">平均</el-dropdown-item>
				<el-dropdown-item command="最大">最大</el-dropdown-item>
				<el-dropdown-item command="最小">最小</el-dropdown-item>
				<!-- <el-dropdown-item command="范围">范围</el-dropdown-item> -->
				<el-dropdown-item command="满足率">满足率</el-dropdown-item>
			</el-dropdown-menu>
		</el-dropdown>
		<el-input
			v-show="iconFlag == '满足率'"
			v-model="num"
			@input="submit"
			placeholder="满足率(%)>="
			style="width: 110px; margin-right: 16px"
		></el-input>
	</div>
</template>

<script>
export default {
	data() {
		return {
			iconFlag: '平均',
			num: '',
			icon: ''
		};
	},
	methods: {
		command(val) {
			this.iconFlag = val;
			this.submit();
		},
		getFlag(val) {
			if (this.icon != val) {
				this.icon = val;
				if (val != undefined) {
					this.iconFlag = this.formatKey(val);
				}
				this.submit();
			}
		},
		submit() {
			this.$emit('resolveMathRange', { mathRange: this.formatKey(this.iconFlag) });
		},
		formatKey(val) {
			switch (val) {
				case '平均':
					return 'avg';
				case '最大':
					return 'max';
				case '最小':
					return 'min';
				case '范围':
					return 'range';
				case '满足率':
					return this.num;
				case 'avg':
					return '平均';
				case 'max':
					return '最大';
				case 'min':
					return '最小';
				case 'range':
					return '范围';
				default:
					this.num = val;
					return '满足率';
			}
		}
	}
};
</script>

<style></style>
