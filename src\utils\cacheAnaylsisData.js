// 获取缓存数据
export function getCacheData(key) {
	let cacheData = this.$store.state?.cacheData;
	let codeData = cacheData?.[this.info.code];
	let apiData = null;
	if (codeData) {
		if (
			(typeof this.info.type == 'object' && this.info.type?.length > 0) ||
			(typeof this.info.typeList == 'object' && this.info.typeList?.length > 0)
		) {
			apiData = codeData[key];
		} else {
			apiData = codeData[key]?.[this.info.type];
		}
	}
	return apiData;
}
// 将数据存入缓存
export function setCacheData(key, data) {
	let cacheData = this.$store.state?.cacheData;
	let setData = cacheData?.[this.info.code];
	let codeData = {};
	if (setData) {
		setData[key] = {};
		if (
			(typeof this.info.type == 'object' && this.info.type?.length > 0) ||
			(typeof this.info.typeList == 'object' && this.info.typeList?.length > 0)
		) {
			setData[key] = data;
		} else {
			setData[key][this.info.type] = data;
		}
	} else {
		codeData[this.info.code] = {};
	}
	let saveData = {
		...cacheData,
		...codeData
	};
	this.$store.commit('setCacheData', saveData);
}
