<template>
  <div class="tags" v-if="showTags">
    <div class="tags-close-box" style="left: 0">
      <el-button
        icon="el-icon-arrow-left"
        style="
          border-left: 1px solid #e9e9e9;
          background: #fff;
          font-size: 16px;
          padding: 0;
          width: 40px;
          height: 38px;
          border-top: none;
          border-right: 1px solid #e9e9e9;
          border-bottom: none;
          border-left: none;
        "
        @click="scrollList('left')"
      >
      </el-button>
    </div>
    <div
      class="flex_start"
      style="margin-left: 40px; transition: transform 0.3s"
      ref="tags"
    >
      <div
        class="tags-li flex_between"
        v-for="(item, index) in tagsList"
        :class="{ active: isActive(item.path) }"
        :key="index"
        :ref="'tag' + index"
      >
        <router-link :to="item.path" class="tags-li-title">
          {{ item.title }}
        </router-link>
        <div class="tags-li-icon" @click="closeTags(index)">
          <i class="el-icon-close"></i>
        </div>
      </div>
    </div>
    <div class="tags-close-box">
      <el-button
        icon="el-icon-arrow-right"
        style="
          border-left: 1px solid #e9e9e9;
          background: #fff;
          font-size: 16px;
          padding: 0;
          width: 40px;
          height: 38px;
          border-top: none;
          border-right: none;
          border-bottom: none;
          border-left: 1px solid #e9e9e9;
        "
        @click="scrollList('right')"
      >
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tagsList: [],
      scroll: 0,
      scroll_index: 0,
    };
  },
  methods: {
    scrollList(type) {
      if (type == "right") {
        if (this.scroll_index == this.tagsList.length - 1) return;
        let x = this.$refs["tag" + this.scroll_index]?.[0].clientWidth + 1;
        this.scroll += x;
        this.scroll_index += 1;
      } else {
        if (this.scroll_index == 0) return;
        this.scroll_index -= 1;
        let x = this.$refs["tag" + this.scroll_index]?.[0].clientWidth;
        this.scroll -= x;
      }
      this.$refs["tags"].style.transform = "translateX(-" + this.scroll + "px)";
    },
    isActive(path) {
      return path === this.$route.fullPath;
    },
    // 关闭单个标签
    closeTags(index) {
      const delItem = this.tagsList.splice(index, 1)[0];
      window.sessionStorage.setItem(
        "mty_TagsList",
        JSON.stringify(this.tagsList)
      );
      const item = this.tagsList[index]
        ? this.tagsList[index]
        : this.tagsList[index - 1];
      if (item) {
        delItem.path === this.$route.fullPath && this.$router.push(item.path);
      } else {
        this.$router.push("/");
      }
    },
    // 关闭全部标签
    closeAll() {
      this.tagsList = [];
      window.sessionStorage.setItem(
        "mty_TagsList",
        JSON.stringify(this.tagsList)
      );
      this.$router.push("/");
    },
    // 关闭其他标签
    closeOther() {
      const curItem = this.tagsList.filter((item) => {
        return item.path === this.$route.fullPath;
      });
      this.tagsList = curItem;
      window.sessionStorage.setItem(
        "mty_TagsList",
        JSON.stringify(this.tagsList)
      );
    },
    // 设置标签
    setTags(route) {
      if (sessionStorage.getItem("mty_TagsList") != null) {
        this.tagsList = JSON.parse(
          window.sessionStorage.getItem("mty_TagsList")
        );
      }
      const isExist = this.tagsList.some((item) => {
        return item.path === route.fullPath;
      });
      if (!isExist) {
        if (this.tagsList.length >= 15) {
          this.tagsList.splice(15, this.tagsList.length);
        }
        // if (route.meta.tagShow) {
        this.tagsList.unshift({
          title: route.query.name
            ? route.query.name + "详情"
            : route.meta.title,
          path: route.fullPath,
          name: route.matched[1].components.default.name,
        });
        // }
      }
      // console.log(this.tagsList);
      window.sessionStorage.setItem(
        "mty_TagsList",
        JSON.stringify(this.tagsList)
      );
      this.$event.$emit("tags", this.tagsList);
      let that = this;
      try {
        this.tagsList = this.tagsList.filter(function (item, index) {
          return (
            item.title !== "系统首页" ||
            that.tagsList.findLastIndex(
              (items) => items.title == "系统首页"
            ) === index
          );
        });
      } catch {
        (err) => {};
      }
    },
    handleTags(command) {
      command === "other" ? this.closeOther() : this.closeAll();
    },
  },
  computed: {
    showTags() {
      // UI未体现，暂时隐藏
      return false;
      // return this.tagsList.length > 0;
    },
  },
  watch: {
    $route(newValue, oldValue) {
      this.setTags(newValue);
    },
  },
  created() {
    this.setTags(this.$route);
    // 监听关闭当前页面的标签页
    this.$event.$on("close_current_tags", () => {
      for (let i = 0, len = this.tagsList.length; i < len; i++) {
        const item = this.tagsList[i];
        if (item.path === this.$route.fullPath) {
          if (i < len - 1) {
            this.$router.push(this.tagsList[i + 1].path);
          } else if (i > 0) {
            this.$router.push(this.tagsList[i - 1].path);
          } else {
            this.$router.push("/");
          }
          this.tagsList.splice(i, 1);
          break;
        }
      }
    });
  },
};
</script>

<style scoped>
.a .tags-li-title .router-link-exact-active .router-link-active {
  color: white;
}
.tags {
  width: 100%;
  position: relative;
  font-family: "PingFang";
  height: 38px;
  overflow: hidden;
  background: #fff;
  /* padding-right: 120px; */
  /* box-shadow: 0 5px 10px #ddd; */
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.08);

  border-bottom: 1px solid #e9e9e9;
}

.tags ul {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
}

.tags-li {
  flex: 1;
  white-space: nowrap;
  max-width: 130px;
  font-size: 12px;
  cursor: pointer;
  line-height: 19px;
  border-right: 1px solid #e9eaec;
  background: #fff;
  padding: 9px 16px;
  vertical-align: middle;
  color: #666;
  -webkit-transition: all 0.3s ease-in;
  -moz-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
}
.tags-li .el-button--small {
  padding: 0;
}
.tags-li:not(.active):hover {
  background: #f8f8f8;
}

.tags-li.active {
  color: #fff;
  background: #4096ff;
}

.tags-li-title {
  max-width: 130px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-right: 8px;
  color: #666;
}

.tags-li.active .tags-li-title {
  color: #fff !important;
}
/* .el-button--primary {
	background: #4096ff !important;
} */
.tags-close-box {
  position: absolute;
  right: 0;
  top: 0;
  box-sizing: border-box;
  padding-top: 1px;
  text-align: center;
  width: 40px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  /* box-shadow: -3px 0 15px 3px rgba(0, 0, 0, 0.1); */
  z-index: 10;
}
</style>
