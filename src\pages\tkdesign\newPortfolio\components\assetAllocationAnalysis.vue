<!-- 怎么挣钱-资配分析 -->
<template>
	<div>
		<div class="flex_card">
			<div v-for="item in templateList" :key="item.value" v-show="item.isshow" :class="item.type">
				<component :is="item.is" :ref="item.value" @resolveFather="item.methods" v-loading="loading"></component>
			</div>
		</div>
	</div>
</template>

<script>
// 最新报告持仓分析
import latestReportedPosition from '@/components/components/components/latestReportedPosition/index.vue';
// 已披露股票行业配置
import stockIndustryAllocation from '@/components/components/components/stockIndustryAllocation/index.vue';
// 基金资产配置分析
import fundAssetAllocationAnalysis from '@/components/components/components/fundAssetAllocationAnalysis/index.vue';
// 大类别占净值比
import largeCategoryNetWorth from '@/components/components/fundComponents/largeCategoryNetWorth/index.vue';
// 行业配置表现
import industryAllocationPerformance from '@/components/components/components/industryAllocationPerformance/index.vue';
// 持仓债券分析
import positionBondAnalysis from '@/components/components/fundComponents/positionBondAnalysis/index.vue';
// 风格择时能力
import styleTimingAbility from '@/components/components/components/styleTimingAbility/index.vue';
// 转债调性(股票性质和债券性质)
import cbondstyle from '@/components/components/components/styleTimingAbility_2/index.vue';
// 股票风格特征
import characteristicsBarra from '@/components/components/fundComponents/stockStyle/index.vue';
// What-if 假想验证：调仓时的资产配置作对了吗
import whatIfAssetAllocation from '@/components/components/fundComponents/whatIfAssetAllocation/index.vue';
// What-if 假想验证：能否打败固定比例再平衡虚拟基准
import whatIfBeatFixedProportion from '@/components/components/fundComponents/whatIfBeatFixedProportion/index.vue';
// 规模及持有人结构
import sizeStructure from '@/components/components/fundComponents/sizeStructure/index.vue';
// 基金持仓分析
import fundPositionAnalysis from '@/components/components/fundComponents/fundPositionAnalysis/index.vue';
// 最新各类型基金配置情况
import newFundAllocation from '@/components/components/fundComponents/newFundAllocation/index.vue';
// 权益基金alpha/beta/smartbeta分解
import equityFundDecompose from '@/components/components/fundComponents/equityFundDecompose/index.vue';
// 权益基金标签分析
import equityFundTagAnalysis from '@/components/components/fundComponents/equityFundTagAnalysis/index.vue';
// 权益基金股票持仓分析
import equityStockPositionAnalysis from '@/components/components/fundComponents/equityStockPositionAnalysis/index.vue';
// 长期持股行业分布
import longTermShareholdingIndustry from '@/components/components/fundComponents/longTermShareholdingIndustry/index.vue';
// 长期持有个股
import longTermHoldingShares from '@/components/components/fundComponents/longTermHoldingShares/index.vue';
// 直投股票capm分析
import capmStockAnalysis from '@/components/components/fundComponents/capmStockAnalysis/index.vue';
// 产品分析(放大权重至100%)
import fundTypeAnalysis from '@/components/components/fundComponents/fundTypeAnalysis/index.vue';
// 短期流动性管理
// import mobilityManage from "@/components/components/fundComponents/mobilityManage/index.vue";

import {
	getHoldStocks,
	getMoneyScale,
	getFundHold,
	getIndexList,
	getIndexReturn,
	getAllocationDetails,
	getFofAllocationDetails,
	getFundMessage,
	getFofHoldFund,
	getFofAllocationMsg,
	getFofAlphaRank,
	getFoFEquityTag,
	getFoFHoldingNewest,
	getStocksLongHold,
	getCapmBenchmark,
	getCapmAnalysis,
	getCharacteristicsBarra,
	getIndexBasicMsg,
	getFofImmediateAsset,
	getFofLiquidity,
	getIndustryInfo,
	getBondAnalysise,
	getStyleTiming,
	getWhatIf,
	cbondstylebenchmark
} from '@/api/pages/Analysis.js';

export default {
	components: {
		latestReportedPosition,
		stockIndustryAllocation,
		fundAssetAllocationAnalysis,
		largeCategoryNetWorth,
		industryAllocationPerformance,
		positionBondAnalysis,
		styleTimingAbility,
		whatIfAssetAllocation,
		whatIfBeatFixedProportion,
		sizeStructure,
		fundPositionAnalysis,
		newFundAllocation,
		equityFundDecompose,
		equityFundTagAnalysis,
		equityStockPositionAnalysis,
		longTermShareholdingIndustry,
		longTermHoldingShares,
		capmStockAnalysis,
		fundTypeAnalysis,
		// mobilityManage,
		characteristicsBarra,
		cbondstyle
	},
	data() {
		return {
			info: {},
			allocationAnalysisData: {},
			bondAnalysiseData: {},
			styleTimeData: {},
			whatIfData: {},
			industryPerformanceData: {},
			indexReturnData: {},
			analysisIndexData: {},
			fofEquityTagData: {},
			templateList: [],
			indexList: null,
			industryInfo: null,
			bondAnalysise: null,
			styleTiming: null,
			whatIf: null,
			characteristicsBarra: null,
			moneyScale: null,
			fundHold: null,
			fundMessage: null,
			fofAlphaRank: null,
			fofEquityTag: null,
			fofHoldingNewest: null,
			stocksLongHold: null,
			capmBenchmark: null,
			bondAnalysise: null,
			styleTiming: null,
			indexBasicMsgEquity: null,
			indexBasicMsgPureBond: null,
			indexBasicMsgCbond: null,
			indexBasicMsgCommodity: null,
			indexBasicMsgNeutral: null,
			fofLiquidity: null,
			cbondstylebenchmark: null,
			holdStocks: null,
			positionClass: null,
			requestOver: 0,
			requestAll: 0,
			loading: true,
			capmAnalysis: {}
		};
	},
	methods: {
		// 获取打印数据
		createPrintWord() {
			let printData = [];
			this.templateList.map((item) => {
				if (item.isshow) {
					if (this.$refs[item.value]?.[0].createPrintWord) {
						printData.push(...this.$refs[item.value]?.[0].createPrintWord());
					}
				}
			});
			return printData;
		},
		// 接收/返回组件列表
		getTemplateList(list) {
			if (list) {
				this.templateList = list;
			} else {
				return this.templateList;
			}
		},
		// 获取父组件传递数据
		getData(data) {
			this.info = data;
			this.requestOver = 0;
			this.watch();
			this.loading = true;
			this.initPostData();
			this.formatTemplatList();
		},
		// 添加watch函数式监听(因为watch侦听器在页面切换时失效)
		watch() {
			let unwatch = this.$watch('requestOver', (val) => {
				if (val == this.requestAll) {
					this.templateList.map((item) => {
						if (item.getData && item.getData !== 'None') {
							this?.[item.getData]();
						}
					});
					this.loading = false;
					this.$emit('overRequest', 'assetAllocationAnalysis');
					unwatch();
				}
			});
		},
		// 初始化请求数据
		initPostData() {
			// 报告期持仓统计指数收益
			this.indexReturnData = {
				type: this.info.type,
				index_codes: [],
				start_date: '',
				end_date: ''
			};
			this.analysisIndexData = {
				type: this.info.type,
				index_codes: [],
				start_date: '',
				end_date: ''
			};
			// 基金标签分析
			this.fofEquityTagData = {
				tag: 'type',
				fund_code: this.info.code,
				type: this.info.type
			};
			this.capmAnalysis = {
				index_code: '',
				fund_code: this.info.code,
				type: this.info.type
			};
		},
		// 格式化模板列表
		formatTemplatList() {
			let requestList = [];
			this.requestAll = 0;
			this.templateList.map((item) => {
				if (item.methods && typeof item.methods == 'string') {
					item.methods = this?.[item.methods];
				}
				if (item.typelist.indexOf(this.info.type) !== -1 || item.typelist.indexOf('*') !== -1) {
					if (requestList.indexOf(item.getRequestData) == -1) {
						if (item.getRequestData && item.getRequestData !== 'None') {
							this?.[item.getRequestData]();
							requestList.push(item.getRequestData);
							this.requestAll = this.requestAll + 1;
						}
					}
				}
			});
		},
		// 获取最新报告持仓数据
		async getHoldStocks() {
			if (this.getCacheData('holdStocks')) {
				this.holdStocks = this.getCacheData('holdStocks');
			} else {
				let data = await getHoldStocks({
					flag: this.info.flag,
					code: this.info.code,
					type: this.info.type,
					start_date: '',
					end_date: ''
				});
				this.holdStocks = data;
				this.setCacheData('holdStocks', data);
			}
			this.requestOver = this.requestOver + 1;
		},
		// 最新报告持仓
		getLatestReportedPositionData() {
			let data = this.holdStocks?.data?.data;
			let mtycode = this.holdStocks?.mtycode;
			let mtymessage = this.holdStocks;
			if (mtycode == 200) {
				this.$refs['latestReportedPosition']?.[0].getData(data);
			} else {
				this.$message.warning('最新报告持仓' + (mtymessage || '暂无数据'));
			}
		},
		// 已披露股票行业配置
		getStockIndustryAllocationData() {
			let weight_sum_data = this.holdStocks?.data?.weight_sum_data;
			let mtycode = this.holdStocks?.mtycode;
			let mtymessage = this.holdStocks;
			if (mtycode == 200) {
				this.$refs['stockIndustryAllocation']?.[0].getData(weight_sum_data);
			} else {
				this.$message.warning('最新报告持仓' + (mtymessage || '暂无数据'));
			}
		},
		// 获取规模及持有人结构
		async getMoneyScale() {
			let moneyScale = null;
			if (this.getCacheData('moneyScale')) {
				moneyScale = this.getCacheData('moneyScale');
				this.moneyScale = moneyScale?.res1;
				this.fundHold = moneyScale?.res2;
			} else {
				let res1 = await getMoneyScale({
					code: this.info.code,
					type: this.info.type
				});
				let res2 = await getFundHold({
					code: this.info.code,
					type: this.info.type
				});
				moneyScale = { res1, res2 };
				this.moneyScale = moneyScale?.res1;
				this.fundHold = moneyScale?.res2;
				this.setCacheData('moneyScale', moneyScale);
			}

			this.requestOver = this.requestOver + 1;
		},
		// 规模及持有人结构
		getMoneyScaleData() {
			this.$refs['sizeStructure']?.[0].getData(this.moneyScale, this.fundHold);
		},
		// 获取报告期持仓统计指数列表
		async getIndexList() {
			if (this.getCacheData('indexList')) {
				this.indexList = this.getCacheData('indexList');
			} else {
				let postData = { type: this.info.type };
				let data = await getIndexList(postData);
				this.indexList = data;
				this.setCacheData('indexList', data);
			}
			this.requestOver = this.requestOver + 1;
		},
		// 报告期持仓统计指数
		getIndexListData() {
			this.$refs['fundAssetAllocationAnalysis']?.[0].getIndexList(this.indexList);
		},
		// 基金资产配置分析统计指数
		getAnalysisIndexListData() {
			this.$refs['fundAssetAllocationAnalysis']?.[0].getIndexList(this.indexList);
		},
		// 获取报告期持仓统计指数收益
		async getIndexReturn(tradstyleData, temp, postData) {
			let data = await getIndexReturn(postData);
			if (data?.mtycode == 200) {
				this.$refs[temp]?.[0].getData(tradstyleData, data?.data);
			} else {
				this.$message.warning('报告期持仓统计指数收益' + (data?.mtymessage || '暂无数据'));
			}
		},
		// 获取报告期持仓统计数据
		async getAllocationDetails() {
			let data = await getAllocationDetails({
				code: this.info.code,
				type: this.info.type
			});
			this.indexReturnData.start_date = this.FUNC.earlyAndLateDate(data.yearqtr)?.earlyDate;
			this.indexReturnData.end_date = this.FUNC.earlyAndLateDate(data.yearqtr)?.lateDate;
			this.getIndexReturn(data, 'fundAssetAllocationAnalysis', this.indexReturnData);
		},
		// 基金资产配置分析
		async getFofAllocationDetails() {
			let data = await getFofAllocationDetails({
				fund_code: this.info.code,
				type: this.info.type
			});
			if (data?.mtycode == 200) {
				this.analysisIndexData.start_date = this.FUNC.earlyAndLateDate(data?.data.yearqtr).earlyDate;
				this.analysisIndexData.end_date = this.FUNC.earlyAndLateDate(data?.data.yearqtr).lateDate;
				this.getIndexReturn(data, 'fundAssetAllocationAnalysis', this.analysisIndexData);
			} else {
				this.$message.warning('基金资产配置分析' + (data?.mtymessage || '暂无数据'));
				this.$refs['fundAssetAllocationAnalysis']?.[0].hideLoading();
			}
		},
		// 获取持仓分类数据
		async getPositionClass() {
			if (this.getCacheData('positionClass')) {
				this.positionClass = this.getCacheData('positionClass');
			} else {
				let data = await getAllocationDetails({
					flag: this.info.flag,
					type: this.info.type,
					code: this.info.code,
					start_date: '',
					end_date: '',
					status: 'True'
				});
				if (data?.mtycode == 200) {
					this.positionClass = data?.data;
				}
			}
			this.requestOver = this.requestOver + 1;
		},
		// 获取持仓分类
		getPositionClassData() {
			this.$refs['fundAssetAllocationAnalysisBond']?.[0].getDataCompany(this.formatAllocationData(this.positionClass));
		},
		// 格式化大类资产配置数据
		formatAllocationData(data) {
			let obj = {};
			if (data?.length && typeof data == 'object') {
				data.map((item) => {
					obj[item.name] = item.value;
				});
			}
			return obj;
		},
		// 获取行业配置表现数据
		async getIndustryInfo() {
			if (this.getCacheData('industryInfo')) {
				this.industryInfo = this.getCacheData('industryInfo');
			} else {
				let data = await getIndustryInfo({
					flag: this.info.flag,
					code: this.info.code,
					type: this.info.type,
					industry_section: '申万(2021)'
				});
				this.industryInfo = data;
				this.setCacheData('industryInfo', data);
			}
			this.requestOver = this.requestOver + 1;
		},
		// 行业配置表现
		getIndustryInfoData() {
			let data = this.industryInfo;
			if (data?.mtycode == 200) {
				this.$refs['industryAllocationPerformance']?.[0].getData(data?.data, this.info);
			} else {
				this.$refs['industryAllocationPerformance']?.[0].hideLoading();
				this.$message.warning('行业配置表现' + (data?.mtymessage || '暂无数据'));
			}
		},
		// 获取股票风格特征
		async getCharacteristicsBarra() {
			if (this.getCacheData('characteristicsBarra')) {
				this.characteristicsBarra = this.getCacheData('characteristicsBarra');
			} else {
				let data = await getCharacteristicsBarra({
					code: this.info.code,
					type: this.info.type
				});
				this.characteristicsBarra = data;
				this.setCacheData('characteristicsBarra', data);
			}
			this.requestOver = this.requestOver + 1;
		},
		// 股票风格特征
		getCharacteristicsBarraData() {
			this.$refs['characteristicsBarra']?.[0].getData(this.characteristicsBarra, this.info);
		},
		// 获取基金成立日
		async getFundMessage() {
			if (this.getCacheData('fundMessageAnalysis')) {
				this.fundMessage = this.getCacheData('fundMessageAnalysis');
			} else {
				let data = await getFundMessage({
					fund_code: this.info.code,
					type: this.info.type
				});
				this.fundMessage = data;
				this.setCacheData('fundMessageAnalysis', data);
			}
			this.requestOver = this.requestOver + 1;
		},
		// 获取基金持仓分析
		async getFofHoldFund(yearqtr) {
			let data = await getFofHoldFund({
				yearqtr,
				fund_code: this.info.code,
				type: this.info.type
			});
			if (data?.mtycode == 200) {
				this.$refs['fundPositionAnalysis']?.[0].getData(data?.data);
			} else {
				this.$message.warning('基金持仓分析' + (data?.mtymessage || '暂无数据'));
			}
		},

		// 获取基金持仓分析时间
		getFundPositionAnalysisData() {
			this.$refs['fundPositionAnalysis']?.[0].getTime(this.fundMessage);
		},
		// 获取最新各类基金配置情况
		async getFofAllocationMsg(yearqtr) {
			let data = await getFofAllocationMsg({
				yearqtr,
				fund_code: this.info.code,
				type: this.info.type
			});
			if (data?.mtycode == 200) {
				this.$refs['newFundAllocation']?.[0].getData(data?.data, this.info);
			} else {
				this.$message.warning('最新各类基金配置情况' + (data?.mtymessage || '暂无数据'));
			}
		},
		// 获取最新各类基金配置情况
		getNewFundAllocationData() {
			this.$refs['newFundAllocation']?.[0].getTime(this.fundMessage);
		},
		// 权益基金alpha/beta/smartbeta分解
		async getFofAlphaRank() {
			if (this.getCacheData('fofAlphaRank')) {
				this.fofAlphaRank = this.getCacheData('fofAlphaRank');
			} else {
				let data = await getFofAlphaRank({
					fund_code: this.info.code,
					type: this.info.type
				});
				this.fofAlphaRank = data;
				this.setCacheData('fofAlphaRank', data);
			}
			this.requestOver = this.requestOver + 1;
		},
		// 权益基金alpha/beta/smartbeta分解
		getFofAlphaRankData() {
			let data = this.fofAlphaRank;
			if (data?.mtycode == 200) {
				this.$refs['equityFundDecompose']?.[0].getData(data?.data);
			} else {
				this.$message.warning('权益基金alpha/beta/smartbeta分解' + (data?.mtymessage || '暂无数据'));
			}
		},
		// 获取权益基金标签
		async getFoFEquityTag() {
			let data = await getFoFEquityTag(this.fofEquityTagData);
			if (data?.mtycode == 200) {
				this.$refs['equityFundTagAnalysis']?.[0].getData(data?.data);
			} else {
				this.$message.warning('权益基金标签' + (data?.mtymessage || '暂无数据'));
			}
			this.requestOver = this.requestOver + 1;
		},
		// 权益基金标签
		// getFoFEquityTagData() {
		// 	let data = this.fofEquityTag;
		// 	if (data?.mtycode == 200) {
		// 		this.$refs['equityFundTagAnalysis']?.[0].getData(data?.data);
		// 	} else {
		// 		this.$message.warning('权益基金标签' + (data?.mtymessage || '暂无数据'));
		// 	}
		// },
		// 获取权益基金标签传递参数
		getEquityTag(tag) {
			this.fofEquityTagData.tag = tag;
			this.getFoFEquityTag();
		},
		// 权益股票持仓分析
		async getFoFHoldingNewest() {
			if (this.getCacheData('fofHoldingNewest')) {
				this.fofHoldingNewest = this.getCacheData('fofHoldingNewest');
			} else {
				let data = await getFoFHoldingNewest({
					fund_code: this.info.code,
					type: this.info.type
				});
				this.fofHoldingNewest = data;
				this.setCacheData('fofHoldingNewest', data);
			}
			this.requestOver = this.requestOver + 1;
		},
		// 权益股票持仓分析
		getFoFHoldingNewestData() {
			let data = this.fofHoldingNewest;
			if (data?.mtycode == 200) {
				this.$refs['equityStockPositionAnalysis']?.[0].getData(data?.data);
			} else {
				this.$message.warning('权益股票持仓分析' + (data?.mtymessage || '暂无数据'));
				this.$refs['equityStockPositionAnalysis']?.[0].hideLoading();
			}
		},
		// 获取长期持有个股数据
		async getStocksLongHold() {
			if (this.getCacheData('stocksLongHold')) {
				this.stocksLongHold = this.getCacheData('stocksLongHold');
			} else {
				let data = await getStocksLongHold({
					flag: this.info.flag,
					code: this.info.code,
					type: this.info.type
				});
				this.stocksLongHold = data;
				this.setCacheData('stocksLongHold', data);
			}
			this.requestOver = this.requestOver + 1;
		},
		// 长期持有个股数据
		getStocksLongHoldData() {
			let data = this.stocksLongHold;
			if (data?.mtycode == 200) {
				this.$refs['longTermHoldingShares']?.[0].getData(data?.data, this.info);
				this.$refs['longTermShareholdingIndustry']?.[0].getData(data?.weight, data?.number, data?.data);
			} else {
				this.$message.warning('长期持有个股数据' + (data?.mtymessage || '暂无数据'));
			}
		},
		// 获取capm直投股票基准列表
		async getCapmBenchmark() {
			if (this.getCacheData('capmBenchmark')) {
				this.capmBenchmark = this.getCacheData('capmBenchmark');
			} else {
				let data = await getCapmBenchmark({
					fund_code: this.info.code,
					type: this.info.type
				});
				this.capmBenchmark = data;
				this.setCacheData('capmBenchmark', data);
			}
			this.requestOver = this.requestOver + 1;
		},
		// capm直投股票基准
		getCapmBenchmarkData() {
			let data = this.capmBenchmark;
			if (data?.mtycode == 200) {
				this.$refs['capmStockAnalysis']?.[0].getBenchmarkList(data?.data);
			} else {
				this.$message.warning('capm直投股票基准' + (data?.mtymessage || '暂无数据'));
			}
		},
		// 获取capm直投股票数据
		async getCapmAnalysis() {
			let data = await getCapmAnalysis(this.capmAnalysis);
			if (data?.mtycode == 200) {
				this.$refs['capmStockAnalysis']?.[0].getData(data?.data);
			} else {
				this.$message.warning('capm直投股票' + (data?.mtymessage || '暂无数据'));
			}
			this.requestOver = this.requestOver + 1;
		},
		//
		getCapmAnalysisIndex(index) {
			this.capmAnalysis.index_code = index;
			this.getCapmAnalysis();
		},
		// 获取持仓债券数据
		async getBondAnalysise() {
			if (this.getCacheData('bondAnalysise')) {
				this.bondAnalysise = this.getCacheData('bondAnalysise');
			} else {
				let data = await getBondAnalysise({
					fund_code: this.info.code,
					type: this.info.type
				});
				this.bondAnalysise = data;
				this.setCacheData('bondAnalysise', data);
			}
			this.requestOver = this.requestOver + 1;
		},
		// 持仓债券
		getBondAnalysiseData() {
			this.$refs['positionBondAnalysis']?.[0].getData(this.bondAnalysise, this.info);
		},
		// 获取风格择时能力
		async getStyleTiming() {
			if (this.getCacheData('styleTiming')) {
				this.styleTiming = this.getCacheData('styleTiming');
			} else {
				let data = await getStyleTiming({
					code: this.info.code,
					type: this.info.type,
					flag: this.info.flag
				});
				this.styleTiming = data;
				this.setCacheData('styleTiming', data);
			}
			this.requestOver = this.requestOver + 1;
		},
		// 风格择时能力
		getStyleTimingData() {
			let data = this.styleTiming;
			if (data?.mtycode == 200) {
				this.$refs['styleTimingAbility']?.[0].getData(data?.data, this.info);
			} else {
				this.$message.warning('风格择时能力' + (data?.mtymessage || '暂无数据'));
				this.$refs['styleTimingAbility']?.[0].hideLoading();
			}
		},
		// 获取转债调性
		async getcbondstylebenchmark() {
			if (this.getCacheData('cbondstylebenchmark')) {
				this.cbondstylebenchmark = this.getCacheData('cbondstylebenchmark');
			} else {
				let data = await cbondstylebenchmark({
					fund_code: this.info.code,
					type: this.info.type,
					flag: this.info.flag
				});
				this.cbondstylebenchmark = data;
				this.setCacheData('cbondstylebenchmark', data);
			}
			this.requestOver = this.requestOver + 1;
		},
		getCbondstylebenchmarkData() {
			let data = this.cbondstylebenchmark;
			if (data?.mtycode == 200) {
				this.$refs['cbondstylebenchmark']?.[0].getData(data?.data, this.info);
			} else {
				this.$message.warning('转债调性' + (data?.mtymessage || '暂无数据'));
				this.$refs['cbondstylebenchmark']?.[0].hideLoading();
			}
		},
		// What-if 假想验证：调仓时的资产配置作对了吗
		async getWhatIf() {
			if (this.getCacheData('whatIf')) {
				this.whatIf = this.getCacheData('whatIf');
			} else {
				let data = await getWhatIf({
					code: this.info.code,
					flag: this.info.flag,
					type: this.info.type
				});
				this.whatIf = data;
				this.setCacheData('whatIf', data);
			}
			this.requestOver = this.requestOver + 1;
		},
		// What-if 假想验证：调仓时的资产配置作对了吗
		getWhatIfAssetAllocationData() {
			this.$refs['whatIfAssetAllocation']?.[0].getData(this.whatIf);
		},
		// What-if 假想验证：能否打败固定比例再平衡虚拟基准
		getWhatIfBeatFixedProportionData() {
			this.$refs['whatIfBeatFixedProportion']?.[0].getData(this.whatIf);
		},
		// 获取基金分析基准列表
		// async getIndexBasicMsg() {
		// 	this.analysisList.map(async (item) => {
		// 		let { data, mtycode } = await getIndexBasicMsg({ type: item });
		// 		if (mtycode == 200) {
		// 			this.$nextTick(() => {
		// 				this.$refs['fundTypeAnalysis' + item][0].getBenchmarkList(data, item, this.info);
		// 			});
		// 		}
		// 	});
		// },
		// 获取权益基金分析基准列表
		async getIndexBasicMsgEquity() {
			if (this.getCacheData('indexBasicMsgEquity')) {
				this.indexBasicMsgEquity = this.getCacheData('indexBasicMsgEquity');
			} else {
				let data = await getIndexBasicMsg({ type: 'equity' });
				this.indexBasicMsgEquity = data;
				this.setCacheData('indexBasicMsgEquity', data);
			}
			this.requestOver = this.requestOver + 1;
		},
		// 权益基金分析基准
		getIndexBasicMsgEquityData() {
			let data = this.indexBasicMsgEquity;
			if (data?.mtycode == 200) {
				this.$nextTick(() => {
					this.$refs['fundTypeAnalysisequity']?.[0].getBenchmarkList(data?.data, 'equity', this.info);
				});
			}
		},
		// 获取纯债基金分析基准列表
		async getIndexBasicMsgPureBond() {
			if (this.getCacheData('indexBasicMsgPureBond')) {
				this.indexBasicMsgPureBond = this.getCacheData('indexBasicMsgPureBond');
			} else {
				let data = await getIndexBasicMsg({ type: 'purebond' });
				this.indexBasicMsgPureBond = data;
				this.setCacheData('indexBasicMsgPureBond', data);
			}
			this.requestOver = this.requestOver + 1;
		},
		// 纯债基金分析基准
		getIndexBasicMsgPureBondData() {
			let data = this.indexBasicMsgPureBond;
			if (data?.mtycode == 200) {
				this.$nextTick(() => {
					this.$refs['fundTypeAnalysispurebond']?.[0].getBenchmarkList(data?.data, 'purebond', this.info);
				});
			}
		},
		// 获取转债基金分析基准列表
		async getIndexBasicMsgCbond() {
			if (this.getCacheData('indexBasicMsgCbond')) {
				this.indexBasicMsgCbond = this.getCacheData('indexBasicMsgCbond');
			} else {
				let data = await getIndexBasicMsg({ type: 'cbond' });
				this.indexBasicMsgCbond = data;
				this.setCacheData('indexBasicMsgCbond', data);
			}
			this.requestOver = this.requestOver + 1;
		},
		// 转债基金分析基准
		getIndexBasicMsgCbondData() {
			let data = this.indexBasicMsgCbond;
			if (data?.mtycode == 200) {
				this.$nextTick(() => {
					this.$refs['fundTypeAnalysiscbond']?.[0].getBenchmarkList(data?.data, 'cbond', this.info);
				});
			}
		},
		// 获取商品分析基准列表
		async getIndexBasicMsgCommodity() {
			if (this.getCacheData('indexBasicMsgCommodity')) {
				this.indexBasicMsgCommodity = this.getCacheData('indexBasicMsgCommodity');
			} else {
				let data = await getIndexBasicMsg({ type: 'commodity' });
				this.indexBasicMsgCommodity = data;
				this.setCacheData('indexBasicMsgCommodity', data);
			}
			this.requestOver = this.requestOver + 1;
		},
		// 商品分析基准
		getIndexBasicMsgCommodityData() {
			let data = this.indexBasicMsgCommodity;
			if (data?.mtycode == 200) {
				this.$nextTick(() => {
					this.$refs['fundTypeAnalysiscommodity']?.[0].getBenchmarkList(data?.data, 'commodity', this.info);
				});
			}
		},
		// 获取对冲资产分析基准列表
		async getIndexBasicMsgNeutral() {
			if (this.getCacheData('indexBasicMsgNeutral')) {
				this.indexBasicMsgNeutral = this.getCacheData('indexBasicMsgNeutral');
			} else {
				let data = await getIndexBasicMsg({ type: 'neutral' });
				this.indexBasicMsgNeutral = data;
				this.setCacheData('indexBasicMsgNeutral', data);
			}
			this.requestOver = this.requestOver + 1;
		},
		// 对冲资产分析基准
		getIndexBasicMsgNeutralData() {
			let data = this.indexBasicMsgNeutral;
			if (data?.mtycode == 200) {
				this.$nextTick(() => {
					this.$refs['fundTypeAnalysisneutral']?.[0].getBenchmarkList(data?.data, 'neutral', this.info);
				});
			}
		},
		// 获取基金分析数据
		async getFofImmediateAsset(postData) {
			let data = await getFofImmediateAsset({
				...postData,
				fund_code: this.info.code,
				type: this.info.type
			});
			if (data?.mtycode == 200 && JSON.stringify(data?.data) != '{}') {
				this.$refs['fundTypeAnalysis' + postData.type]?.[0].getData(data?.data);
			} else {
				this.$refs['fundTypeAnalysis' + postData.type]?.[0].hideLoading();
			}
		},
		// 短期流动性管理
		async getFofLiquidity() {
			if (this.getCacheData('fofLiquidity')) {
				this.fofLiquidity = this.getCacheData('fofLiquidity');
			} else {
				let data = await getFofLiquidity({
					fund_code: this.info.code,
					type: this.info.type
				});
				this.fofLiquidity = data;
				this.setCacheData('fofLiquidity', data);
			}
			this.requestOver = this.requestOver + 1;
		},
		// 短期流动性管理
		getFofLiquidityData() {
			let data = this.fofLiquidity;
			if (data?.mtycode == 200) {
				this.$refs['mobilityManage']?.[0].getData(data?.data);
			} else {
				this.$refs['mobilityManage']?.[0].hideLoading();
				this.$message.warning('短期流动性管理' + (data?.mtymessage || '暂无数据'));
			}
		},
		// 获取子组件传递数据
		getAnalysisIndexCode(data) {
			this.analysisIndexData = { ...this.analysisIndexData, ...data };
			this.getFofAllocationDetails();
		},
		// 获取子组件传递数据
		getIndexCode(data) {
			this.indexReturnData = { ...this.indexReturnData, ...data };
			this.getPositionStatistics();
		},
		// 获取子组件传递数据
		getStatisticsIndexCode(data) {
			this.indexReturnData = { ...this.indexReturnData, ...data };
			this.getPositionStatistics();
		},
		// 获取子组件传递数据
		changeDate(e) {
			this.styleTimeData = {
				fund_code: this.info.code,
				type: this.info.type,
				flag: this.info.flag,
				start_date: e[0],
				end_date: e[1]
			};
			this.getcbondstylebenchmark();
		}
	}
};
</script>

<style></style>
