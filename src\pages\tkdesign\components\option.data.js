//调用字典接口获取对应字典列表时 所需要的key
export const DictKey = {
	type: 'type', //类型枚举key
	industry: 'industry', //行业枚举
	theme: 'theme', //主题
	selfSelectedPool: 'optionalPool', //自选池
	taKangType: 'taikang', //泰康分类
	style: 'style' //风格
};
// 行业:industry:;主题:theme;风格:style;泰康自定义行业:taikang；自选池：optionalPool;类型：windType
export const DisplayDimensionOption = {
	type: {
		name: '类型',
		value: 'type',
		dictKey: DictKey.type
	},
	industry: {
		name: '行业',
		value: 'industry',
		dictKey: DictKey.industry
	},
	theme: {
		name: '主题',
		value: 'theme',
		dictKey: DictKey.theme
	},
	selfSelectedPool: {
		name: '自选池',
		value: 'optionalPool',
		dictKey: DictKey.selfSelectedPool
	},
	taKangType: {
		name: '泰康分类',
		value: 'taikang',
		dictKey: DictKey.taKangType
	},
	style: {
		name: '风格',
		value: 'style',
		dictKey: DictKey.style
	}
};
