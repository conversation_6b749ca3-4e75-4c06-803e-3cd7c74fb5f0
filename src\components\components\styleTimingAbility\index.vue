<template>
	<div class="chart_one">
		<div class="title">风格择时能力</div>
		<div class="charts_center_class">
			<v-chart
				ref="styleTimingAbility"
				class="charts_one_class"
				autoresize
				v-loading="showempty"
				element-loading-text="暂无数据"
				element-loading-spinner="el-icon-document-delete"
				element-loading-background="rgba(239, 239, 239, 0.5)"
				:options="niuxiongoption"
			/>
			<!-- <div> 蛛网图维度'alpha', '利率水平','利率斜率','利率凸性','信用利差','信用挖掘','可转债配置','A 股配置'</div> -->
		</div>
	</div>
</template>
<script>
import { exportTitle, exportChart } from '@/utils/exportWord.js';
// 风格择时能力

import VCharts from 'vue-echarts';
export default {
	name: 'styleTimingAbility',
	filters: {
		fix6(value) {
			return value.substring(0, 10);
		},
		fix3(value) {
			return parseInt(value * 1000) / 1000;
		},
		fix4bf(value) {
			return (value * 100).toFixed(2) + '%';
		}
	},
	data() {
		return {
			niuxiongoption: {},
			showempty: true,
			sflag: 0,
			loadingtm: true
		};
	},
	methods: {
		// 获取父组件传递数据
		getData(data, info) {
			this.loadingtm = false;
			if (data) {
				this.showempty = false;
				let indicator =
					info.type == 'bond'
						? [
								{ name: 'alpha', max: 1 },
								{ name: '利率水平', max: 1 },
								{ name: '利率斜率', max: 1 },
								{ name: '信用利差', max: 1 },
								{ name: '信用挖掘', max: 1 },
								{ name: '可转债配置', max: 1 },
								{ name: 'A股配置', max: 1 }
						  ]
						: (info.type == info.type) == 'cbond' || info.type == 'gushoujia' || info.type == 'kezhuanzhai'
						? [
								{ name: 'alpha', max: 1 },
								{ name: '利率水平', max: 1 },
								{ name: '利率斜率', max: 1 },
								{ name: '信用利差', max: 1 },
								{ name: '信用挖掘', max: 1 },
								{ name: '可转债配置', max: 1 }
						  ]
						: [
								{ name: 'alpha', max: 1 },
								{ name: '利率水平', max: 1 },
								{ name: '利率斜率', max: 1 },
								{ name: '信用利差', max: 1 },
								{ name: '信用挖掘', max: 1 }
						  ];
				let value =
					info.type == 'bond' || info.type == 'cbond' || info.type == 'gushoujia' || info.type == 'kezhuanzhai'
						? [
								Number(data.alpha).toFixed(4),
								Number(data.timing_level).toFixed(4),
								Number(data.timing_slope).toFixed(4),
								Number(data.timing_credit).toFixed(4),
								Number(data.timing_defaultrisk).toFixed(4),
								Number(data.timing_convertiable).toFixed(4),
								Number(data.timing_equity).toFixed(4)
						  ]
						: (info.type == info.type) == 'cbond' || info.type == 'gushoujia' || info.type == 'kezhuanzhai'
						? [
								Number(data.alpha).toFixed(4),
								Number(data.timing_level).toFixed(4),
								Number(data.timing_slope).toFixed(4),
								Number(data.timing_credit).toFixed(4),
								Number(data.timing_defaultrisk).toFixed(4),
								Number(data.timing_convertiable).toFixed(4)
						  ]
						: [
								Number(data.alpha).toFixed(4),
								Number(data.timing_level).toFixed(4),
								Number(data.timing_slope).toFixed(4),
								Number(data.timing_credit).toFixed(4),
								Number(data.timing_defaultrisk).toFixed(4)
						  ];
				this.niuxiongoption = {
					tooltip: {
						color: ['#B7A7D7'],
						textStyle: {
							fontSize: '14px'
						}
					},
					grid: {
						top: '30px',
						containLabel: true
					},
					toolbox: {
						feature: {
							saveAsImage: { pixelRatio: 3 }
						},
						top: -4,
						width: 104
					},
					radar: {
						splitLine: {
							// (这里是指所有圆环)坐标轴在 grid 区域中的分隔线。
							lineStyle: {
								color: '#CFE5F3',
								opacity: 0.3,
								// 分隔线颜色
								width: 2
								// 分隔线线宽
							}
						},
						splitArea: {
							// 坐标轴在 grid 区域中的分隔区域，默认不显示。
							show: true,
							areaStyle: {
								// 分隔区域的样式设置。
								color: ['rgb(246,250,255)', 'rgb(246,250,255)']
								// 分隔区域颜色。分隔区域会按数组中颜色的顺序依次循环设置颜色。默认是一个深浅的间隔色。
							}
						},
						axisLine: {
							// (圆内的几条直线)坐标轴轴线相关设置
							lineStyle: {
								color: '#7ab1ff',
								// 坐标轴线线的颜色。
								width: 0.3,
								// 坐标轴线线宽。
								type: 'solid'
								// 坐标轴线线的类型。
							}
						},
						// shape: 'circle',
						name: {
							textStyle: {
								fontSize: '14px'
							}
						},
						indicator
					},
					series: [
						{
							itemStyle: {
								// 单个拐点标志的样式设置。
								normal: {
									borderColor: '#4096FF',
									// 拐点的描边颜色。[ default: '#000' ]
									borderWidth: 3
									// 拐点的描边宽度，默认不描边。[ default: 0 ]
								}
							},
							lineStyle: {
								// 单项线条样式。
								normal: {
									color: '#4096FF',
									opacity: 0.5 // 图形透明度
								}
							},
							areaStyle: {
								// 单项区域填充样式
								normal: {
									color: 'rgb(223,236,255)', // 填充的颜色。[ default: "#000" ]
									opacity: 0.7
								}
							},
							name: '风格择时能力',
							type: 'radar',
							data: [
								{
									value,
									name: '市场分位'
								}
							]
						}
					]
				};
			}
		},
		hideLoading() {
			this.loadingtm = false;
		},
		createPrintWord() {
			if (this.showempty) {
				return [];
			} else {
				this.$refs['styleTimingAbility'].mergeOptions({ toolbox: { show: false } });
				let height = this.$refs['styleTimingAbility'].$el.clientHeight;
				let width = this.$refs['styleTimingAbility'].$el.clientWidth;
				let chart = this.$refs['styleTimingAbility'].getDataURL({
					type: 'png',
					pixelRatio: 2,
					backgroundColor: '#fff'
				});
				this.$refs['styleTimingAbility'].mergeOptions({ toolbox: { show: true } });
				return [...exportTitle('风格择时能力'), ...exportChart(chart, { width, height })];
			}
		}
	},
	components: { 'v-chart': VCharts }
};
</script>
