<template>
  <div class="box_Board">
    <div class="header_box">
      <span class="header_inactive">投后&nbsp;/&nbsp;监控预警&nbsp;</span>
    </div>
    <div class="border_table">
      <el-tabs v-model="activeName">
        <!-- 预警记录 -->
        <el-tab-pane class="warning_record"
                     label="预警记录"
                     name="first">
          <!-- 头部区域 -->
          <div class="border_table_header">
            <!-- 左侧搜索区域 -->
            <div class="border_table_header_search">
              <el-input v-model="formData.name"
                        class="search"
                        clearable
                        @clear="getList(formData)"
                        placeholder="请输入产品名称或代码"
                        prefix-icon="el-icon-search" />
              <el-button type="primary"
                         @click="getList(formData)">查询</el-button>
              <el-button type="primary"
                         @click="clearAll()">批量已阅</el-button>
            </div>
            <!-- 右侧筛选区域 -->
            <!-- <div @click="exportExcel()"><i class=""><svg xmlns="http://www.w3.org/2000/svg"
                     width="16"
                     height="16"
                     viewBox="0 0 16 16"
                     fill="none">
                  <path d="M7.88736 10.6575C7.90072 10.6745 7.9178 10.6883 7.93729 10.6978C7.95678 10.7073 7.97818 10.7123 7.99986 10.7123C8.02154 10.7123 8.04294 10.7073 8.06243 10.6978C8.08192 10.6883 8.099 10.6745 8.11236 10.6575L10.1124 8.1271C10.1856 8.03424 10.1195 7.89674 9.99986 7.89674H8.67665V1.85389C8.67665 1.77531 8.61236 1.71103 8.53379 1.71103H7.46236C7.38379 1.71103 7.3195 1.77531 7.3195 1.85389V7.89496H5.99986C5.88022 7.89496 5.81415 8.03246 5.88736 8.12532L7.88736 10.6575ZM14.5356 10.0325H13.4641C13.3856 10.0325 13.3213 10.0967 13.3213 10.1753V12.9253H2.67843V10.1753C2.67843 10.0967 2.61415 10.0325 2.53557 10.0325H1.46415C1.38557 10.0325 1.32129 10.0967 1.32129 10.1753V13.711C1.32129 14.0271 1.57665 14.2825 1.89272 14.2825H14.107C14.4231 14.2825 14.6784 14.0271 14.6784 13.711V10.1753C14.6784 10.0967 14.6141 10.0325 14.5356 10.0325Z"
                        fill="black"
                        fill-opacity="0.45" />
                </svg></i></div> -->
            <div>
              预警日期：
              <el-radio-group v-model="formData.type"
                              @input="getList(formData)">
                <el-radio-button v-for="(item, index) in radioOptions"
                                 :label="index + 1"
                                 :key="index">
                  {{ item }}
                </el-radio-button>
              </el-radio-group>
            </div>
          </div>
          <!-- 表格区域 -->
          <el-table :data="tableData"
                    v-loadong="loading.listLoading"
                    @selection-change="handleSelectionChange"
                    height="calc(100vh - 500px)">
            <el-table-column type="selection"
                             width="55">
            </el-table-column>
            <el-table-column align="gotoleft"
                             :show-overflow-tooltip="true"
                             label="预警规则名称"
                             prop="ruleName" />
            <el-table-column align="gotoleft"
                             :show-overflow-tooltip="true"
                             label="目标值"
                             prop="targetValue" />
            <el-table-column align="gotoleft"
                             :show-overflow-tooltip="true"
                             label="触发值"
                             prop="triggerValue" />
            <el-table-column align="gotoleft"
                             :show-overflow-tooltip="true"
                             label="关联产品"
                             prop="fundName" />
            <el-table-column align="gotoleft"
                             label="触发日期"
                             :show-overflow-tooltip="true"
                             prop="triggerTime"
                             sortable />
            <el-table-column align="gotoleft"
                             label="预警信息"
                             :show-overflow-tooltip="true"
                             width='200'
                             prop="description">
              <!-- <template slot-scope="scope">
                <el-popover placement="top-start"
                            width="500"
                            trigger="hover">
                  <div style="max-height: 400px;overflow: auto;padding:16px">
                    <div v-for="(item,index) in scope.row.description.split('<br />')"
                         :key="index">
                      <div>{{ item }}</div>
                    </div>

                  </div>
                  <div type="text"
                       slot="reference">{{scope.row.description.slice(0,10) + '...'  }}</div>
                </el-popover>
              </template> -->
            </el-table-column>
            <el-table-column align="gotoleft"
                             label="条件类型"
                             :show-overflow-tooltip="true"
                             prop="warnType"
                             v-slot="{ row }">
              <template>
                {{ getWarnType(row.warnType) }}
              </template>
            </el-table-column>
            <el-table-column align="gotoleft"
                             :show-overflow-tooltip="true"
                             label="触发标的"
                             prop="indexName" />
            <el-table-column align="gotoleft"
                             :show-overflow-tooltip="true"
                             label="超限日期"
                             prop="notifyTimes" />
            <el-table-column align="gotoleft"
                             label="操作">
              <template slot-scope='scope'>
                <div><el-button type="text"
                             @click="readAlready(scope.row)">已阅</el-button></div>
              </template>
            </el-table-column>
            <template slot="empty">
              <el-empty :image-size="160"></el-empty>
            </template>
          </el-table>
          <!-- 分页器 -->
          <div class="pagination_board">
            <el-pagination :current-page.sync="pagination.pageIndex"
                           :page-size="pagination.pageSize"
                           :total="pagination.total"
                           background
                           layout="total, sizes, prev, pager, next"
                           @size-change="sizeChange"
                           @current-change="currentChange" />
          </div>
        </el-tab-pane>
        <!-- 已阅预警 -->
        <el-tab-pane class="warning_record"
                     label="已阅预警"
                     name="third">
          <readed ref="readed"></readed>
        </el-tab-pane>
        <!-- 预警设置 -->
        <el-tab-pane class="warning_set"
                     label="预警设置"
                     name="second">
          <el-row>
            <el-col :span="6"
                    class="warning_set_left">
              <div class="border_table_header">
                <div class="border_table_header_title">风险监控规则列表</div>
                <el-button icon="el-icon-plus"
                           @click="addList = true">
                  新增
                </el-button>
              </div>
              <div class="flex">
                <el-input v-model="ruleForm.name"
                          class="search"
                          clearable
                          placeholder="请输入风险监控条目名称"
                          prefix-icon="el-icon-search" />
                <el-button type="primary"
                           @click="getRuleList">查询</el-button>
              </div>
              <div class="flex">
                <el-select @change="getRuleList"
                           v-model="ruleForm.type"
                           placeholder="请选择时间段">
                  <el-option value="1"
                             label="全部"></el-option>
                  <el-option value="2"
                             label="近一周"></el-option>
                  <el-option value="3"
                             label="近一个月"></el-option>
                  <el-option value="4"
                             label="近三个月"></el-option>
                </el-select>
                <el-select v-model="ruleForm.activeType"
                           placeholder="请选择状态"
                           @change="getRuleList()">
                  <el-option value="1"
                             label="全部"></el-option>
                  <el-option value="2"
                             label="启用"></el-option>
                  <el-option value="3"
                             label="禁用"></el-option>
                </el-select>
              </div>
              <el-table v-loading="loading.ruleListLoading"
                        :data="ruleTable"
                        :highlight-current-row="true"
                        @row-click="getRuleDetail"
                        class="left_table"
                        height="calc(100vh - 586px)">
                <el-table-column :show-overflow-tooltip="true"
                                 align="gotoleft"
                                 label="风险监控条目"
                                 min-width="120"
                                 prop="rule.name" />
                <el-table-column align="center"
                                 label="状态"
                                 min-width="50"
                                 v-slot="{ row }">
                  <el-switch @change="changeActive(row)"
                             v-model="row.rule.active"
                             active-color="#4096ff"
                             inactive-color="#E9E9E9">
                  </el-switch>
                </el-table-column>
                <el-table-column align="center"
                                 label="监控对象数"
                                 min-width="92"
                                 prop="products.length" />
              </el-table>
            </el-col>
            <el-col :span="18"
                    class="warning_set_right">
              <div v-if="!showDetail"
                   class="emptyBox">
                <el-empty description="请选择左侧规则进行查看"></el-empty>
              </div>
              <div v-else
                   v-loading="loading.setLoading">
                <div class="right_header flex item-center justify-between">
                  <div>
                    <div class="right_header_title">
                      <div v-if="!showEditName">{{ ruleDetail.rule.name }}</div>
                      <div v-else>
                        <el-input v-model="ruleDetail.rule.name"></el-input>
                      </div>
                      <i class="header_title_icon"
                         :class="showEditName ? 'el-icon-finished' : 'el-icon-edit'"
                         @click="showEditName = !showEditName"></i>
                    </div>
                    <div class="flex">
                      <!-- <span class="mr">关联组合数{{ count }}</span> -->
                      <span class="mr"
                            v-show="ruleDetail.rule.createdAt">创建时间：{{
                          ruleDetail.rule.createdAt
                        }}</span>
                      <!-- <el-switch active-color="#4096ff"
                                 inactive-color="#E9E9E9"
                                 v-model="ruleDetail.rule.active" /> -->
                    </div>
                  </div>
                  <div class="right_header_button">
                    <el-button @click="deleteRule(ruleDetail.rule.id)">删除</el-button>
                  </div>
                </div>
                <!-- tabs -->
                <div class="right_tabs">
                  <el-tabs v-model="setTabsActiveName"
                           v-if="test">
                    <el-tab-pane label="关联组合设置"
                                 name="first">
                      <div slot="label">
                        <span style="padding:0 24px;">关联组合设置</span>
                      </div>
                      <CompositionSet ref="compositionSet"
                                      :detail="oldDetail"
                                      @updateData="updateProduct" />
                    </el-tab-pane>
                    <el-tab-pane label="风险监控触发处理设置"
                                 name="second">
                      <div slot="label">
                        <span style="padding:0 24px;">风险监控触发处理设置</span>
                      </div>
                      <DisposeSet ref="disposeSet"
                                  :detail="ruleDetail.notifications[0]"
                                  @uploadData="uploadNotifications" />
                    </el-tab-pane>
                    <el-tab-pane label="规则设置"
                                 name="third">
                      <div slot="label">
                        <span style="padding:0 24px;">规则设置</span>
                      </div>
                      <div class="tabsItemThird">
                        <RuleSet ref="ruleSet"
                                 :details="ruleDetail.details"
                                 @updateData="uploadRuleTable" />
                      </div>
                    </el-tab-pane>
                  </el-tabs>
                </div>
                <!-- 底部 -->
                <div class="footer">
                  <el-button type="primary"
                             @click="onsubmit">保存</el-button>
                  <el-button @click="cancel">取消</el-button>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-tab-pane>
      </el-tabs>
    </div>
    <!-- 新增风险监控规则列表弹窗 -->
    <el-dialog :visible.sync="addList"
               title="新增风险监控规则列表"
               width="500px">
      <div class="form-box"
           style="padding-top: 40px">
        <el-form v-model="newRuleForm"
                 label-width="100px">
          <el-form-item label="列表名称"
                        prop="name"
                        class="form_item">
            <el-input v-model="newRuleForm.rule.name"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="resetForm">取消</el-button>
        <el-button type="primary"
                   @click="addRule">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { filter_json_to_excel } from '@/utils/exportExcel.js';
import readed from './components/readed.vue';
import CompositionSet from './components/compositionSet.vue'
import DisposeSet from './components/disposeSet.vue'
import RuleSet from './components/ruleSet.vue'
import { getList, getRuleList, active, deleteRule, saveRule, readList } from "../../../api/pages/tkdesign/monitorWarning";
import message from "element-ui/packages/message";
import Readed from './components/readed.vue';

export default {
  components: {
    CompositionSet,
    DisposeSet,
    RuleSet,
    readed
  },
  data () {
    return {
      test: true,
      //新增弹框的表单数据
      newRuleForm: {
        rule: {
          active: false,
          name: ""
        },
        products: [],
        details: [],
        notifications: [
          {
            desiny: "",
            notifyMethod: {
              null: false,
              type: 'jsonb',
              value: '[]',
            }
          }
        ]
      },
      //新增弹框的规则
      rules: {
        name: [
          { required: true, message: '请输入列表名称', trigger: 'blur' },
        ],
      },
      // el-tabs绑定的标签
      activeName: 'first',
      setTabsActiveName: 'first',

      // 首页搜索区域input绑定的数据
      formData: {
        name: '',
        type: '1',
      },
      ruleForm: {
        name: '',
        type: '1',
        activeType: '1',
      },
      radioOptions: ['全部', '近一周', '近一月', '近三月'],
      pagination: {
        pageIndex: 1,// 当前页码
        pageSize: 10,// 页面显示几条数据
        total: 0,
      },
      tableData: [],// 预警记录表格数据源
      ruleTable: [],// 预警设置表格数据源

      showEditName: false,//编辑姓名输入框
      ruleDetail: null,// 规则的详情
      oldDetail: {},
      count: 0,
      showDetail: false,//展示详情
      addList: false,// 绑定弹窗dialog
      loading: {
        listLoading: false,
        ruleListLoading: false,
        setLoading: false
      },
      detailIfChange: false,
      multipleSelection: []
    };
  },

  watch: {
    ruleDetail: {
      handler (newValue, oldValue) {
        if (oldValue === null)
          return
        this.detailIfChange = true
      },
      deep: true
    }
  },

  methods: {
    handleSelectionChange (val) {
      console.log(val);
      this.multipleSelection = val;
    },
    async clearAll () {
      let { data, code, message } = await readList(this.multipleSelection.map(item => { return { id: item.id } }))
      if (code == '200') {
        this.getList(this.formData)
        this.$refs['readed'].getList(this.formData)
      }
    },
    async readAlready (params) {
      let { data, code, message } = await readList([{ id: params?.id || params }])
      if (code == '200') {
        this.getList(this.formData)
        this.$refs['readed'].getList(this.formData)
      }
    },
    exportExcel () {
      let list = [{
        value: 'ruleName',
        label: '预警规则名称',
        format: ''
      }, {
        value: 'targetValue',
        label: '目标值',
        format: ''
      }, {
        value: 'triggerValue',
        label: '触发值',
        format: ''
      }, {
        value: 'fundName',
        label: '关联产品',
        format: ''
      }, {
        value: 'triggerTime',
        label: '触发日期',
        format: ''
      }, {
        value: 'description',
        label: '预警信息',
        format: ''
      }, {
        value: 'warnType',
        label: '条件类型',
        format: ''
      }, {
        value: 'indexName',
        label: '触发标的',
        format: ''
      }, {
        value: 'notifyTimes',
        label: '超限日期',
        format: ''
      }];
      filter_json_to_excel(list, this.tableData, '预警记录');
    },
    /**
     * 更新风险监控触发处理
     * @param obj
     */
    uploadNotifications (obj) {
      this.ruleDetail.notifications[0].desiny = obj.desiny
      this.ruleDetail.notifications[0].notifyMethod.value = JSON.stringify(obj.notifyMethod)
    },
    /**
     * 更新风险监控触发处理
     * @param obj
     */
    uploadRuleTable (array) {
      this.ruleDetail.details = array
      // console.log(this.ruleDetail.details, 'ssss');
      // this.ruleDetail.details.description = array.description
      // this.ruleDetail.details.warnType = array.warnType
      // this.ruleDetail.details.params = JSON.stringify(array.params)
      // console.log(array, this.ruleDetail.details);
    },
    // 更新组合数
    updateProduct (arr) {
      this.ruleDetail.products = arr
      this.count = arr.length
    },
    // 每页条数改变时触发的回调
    sizeChange (value) {
      this.pagination.pageSize = value
      this.getList(this.formData)
    }
    ,
    // 当前页数改变时触发的回调
    currentChange (value) {
      this.pagination.pageIndex = value
      this.getList(this.formData)
    },
    /**
     *获取条件类型
     * @param type
     * @returns {string|*}
     */
    getWarnType (type) {
      console.log(type);
      const list = [
        { label: '组合收益率', value: 1 },
        { label: '组合超额收益率', value: 2 },
        { label: '组合回撤', value: 3 },
        { label: '组合单个资产持仓占比', value: 4 },//单证券市值占比
        { label: '组合单个资产持仓份额占市场份额比', value: 5 },//单股票持仓占股本比
        { label: '组合大类资产持仓占比', value: 6 },//证券资产占比
        { label: '个股集中度', value: 7 },
        { label: '行业集中度', value: 8 },
        { label: '黑名单占比', value: 9 },
        { label: '白名单占比', value: 10 },
        { label: '组合大类资产净买入金额', value: 11 },//净买入
        { label: '组合单个资产净买入金额', value: 12 },//净买入
        { label: '组合单个资产买入金额', value: 13 }//新增//新增
      ]
      return list.filter(v => v.value === type)[0].label
    },
    /**
     * 获取列表
     * @param data
     */
    getList (data) {
      this.loading.listLoading = true
      const params = {
        ...data,
        current: this.pagination.pageIndex,
        pageSize: this.pagination.pageSize,
        flag: '1'
      }
      getList(params).then((res) => {
        this.loading.listLoading = false
        if (res.code === 200) {
          // console.log(res.data);
          this.tableData = res.data
          this.pagination.total = res.total
        } else {
          this.tableData = []
          this.pagination.total = 0
        }
      })
    },
    /**
     * 获取风险监控规则列表
     * @param data
     */
    getRuleList () {
      this.loading.ruleListLoading = true
      getRuleList(this.ruleForm).then((res) => {
        this.loading.ruleListLoading = false
        if (res.code === 200) {
          if (res.data && res.data.length > 0) {
            res.data.map(item => {
              if (item.details && item.details.length > 0) {
                item.details.map(detailRule => {
                  if (detailRule.srcTypeMulti) {
                    console.log(detailRule.srcTypeMulti)
                    detailRule.srcTypeMultiArray = detailRule.srcTypeMulti.split(',');
                    console.log(detailRule.srcTypeMultiArray)
                  }
                })
              }
            })
          }
          console.log(res.data)
          this.ruleTable = res.data
        } else {
          this.ruleTable = []
        }
      })
    },

    addRule () {
      const row = {
        ...this.newRuleForm,
        rule: {
          ...this.newRuleForm.rule,
          active: false,
        },
      }
      this.ruleTable.unshift(row)
      this.addList = false
    },

    resetForm () {
      this.addList = false
      this.newRuleForm = {
        rule: {
          active: false,
          name: ""
        },
        products: [],
        details: [],
        notifications: [
          {
            desiny: "",
            notifyMethod: {
              null: false,
              type: 'jsonb',
              value: '[]',
            }
          }
        ]
      }
    },

    /**
     * 获取规则详情
     * @param row
     */
    getRuleDetail (row) {
      this.showDetail = true
      if (row.rule.id) {
        this.ruleDetail = this.ruleTable.filter(item => item.rule.id === row.rule.id)[0]
        this.oldDetail = JSON.parse(JSON.stringify(this.ruleDetail))
      } else {
        this.ruleDetail = this.newRuleForm
        this.oldDetail = JSON.parse(JSON.stringify(this.newRuleForm))
      }
      this.count = this.ruleDetail.products.length
    },
    /**
     * 启/停规则
     * @param type
     */
    changeActive (row) {
      const data = {
        active: row.rule.active,
        id: row.rule.id
      }
      active(data).then((res) => {
        if (res.code === 200) {
          if (data.active)
            this.$message.success('已使用该规则')
          else
            this.$message.warning('已禁用该规则')
        } else
          this.$message.error('操作失败')
      })
    },

    /**
     * 删除规则
     * @param id
     */
    deleteRule (id) {
      this.$confirm('确定删除么?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteRule(id).then((res) => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.showDetail = false
            this.getRuleList()
          } else
            this.$message.error('删除失败')
        })
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    /**
     * 取消规则编辑
     */
    cancel () {
      if (this.detailIfChange) {
        this.$confirm('确认取消保存已修改的内容吗？', '温馨提示', {
          confirmButtonText: '去保存',
          cancelButtonText: '不保存',
          type: 'warning'
        })
          .then(() => {
            this.onsubmit()
          })
          .catch(() => {
            let that = this
            that.test = false
            that.ruleDetail = that.oldDetail
            setTimeout(() => {
              that.test = true
            }, 0)
          })
      }
    },

    onsubmit () {
      const data = {
        details: [],
        notifications: [],
        products: [],
        rule: {},
      }
      data.rule = JSON.parse(JSON.stringify({
        userId: 1,
        ...this.ruleDetail.rule
      }))
      console.log(this.ruleDetail.products);
      data.products = this.ruleDetail.products.map(v => {
        return { fundCode: v.fundCode, fundName: v.fundName, productId: v.productId }
      })
      data.notifications = this.ruleDetail.notifications.map(v => {
        return { userId: v.userId, desiny: v.desiny, notifyMethod: JSON.parse(v.notifyMethod.value) }
      })
      data.details = this.ruleDetail.details.map(v => {
        if (v.valueTypeC) {
          return {
            ...v,
            compareType: v.compareType,
            description: v.description,
            indexCode: v.indexCode,
            threshold: v.threshold,
            valueType: v.valueType,
            warnType: v.warnType,
            srcType: v.valueTypeC
          }
        }
        else {
          return {
            ...v,
            compareType: v.compareType,
            description: v.description,
            indexCode: v.indexCode,
            threshold: v.threshold,
            valueType: v.valueType,
            warnType: v.warnType,
          }
        }

      })
      if (data.products.length !== 0) {
        saveRule(data).then((res) => {
          if (res.code === 200) {
            message.success('保存成功')
            this.getRuleList()
          } else
            message.error('保存失败')
        })
      } else {
        this.$message.error('关联对象数不能为空！')
        this.setTabsActiveName = 'first'
      }
    },
  },
  mounted () {
    this.getList(this.formData)
    this.getRuleList()
  },
}
</script>
<style lang="scss" scoped>
@import '../tkdesign';

.mr {
	margin-right: 16px;
}

.border_table_header_search {
	display: flex;

	.el-button {
		margin-left: 10px;
	}
}

.warning_set {
	.warning_set_left {
		height: calc(100vh - 390px);
		overflow: auto;
		scrollbar-width: none !important;
		border-right: 1px solid #e9e9e9;
		padding: 0 16px 0 0;

		.flex {
			margin-top: 8px;
			justify-content: space-between;
		}

		.flex > .el-select {
			width: 48%;
		}

		.left_table {
			margin-top: 8px;
		}

		.search {
			margin-right: 10px;
		}
	}

	.warning_set_left::-webkit-scrollbar {
		width: 4px;
	}

	.warning_set_right {
		height: calc(100vh - 390px);
		position: relative;

		.emptyBox {
			height: 100%;
			display: flex;
			justify-content: center;
		}

		.right_header {
			padding: 0 24px 16px;
			border-bottom: 1px solid #e9e9e9;

			.right_header_title {
				display: flex;
				align-items: center;
				position: relative;
				width: 200px;
				color: rgba(0, 0, 0, 0.85);
				font-size: 16px;
				font-style: normal;
				font-weight: 500;
				height: 32px;
				margin-bottom: 16px;

				.header_title_icon {
					position: absolute;
					right: 10px;
					top: 25%;
				}
			}
		}

		.right_tabs {
			.tabsItemThird {
				padding: 0 24px;
			}
		}

		.footer {
			background-color: white;
			z-index: 99;
			text-align: center;
			width: 100%;
			border-top: 1px solid #e9e9e9;
			padding: 24px 0 8px;
			position: absolute;
			bottom: 0;
		}
	}
}

.form-button {
	right: 10px !important;
}
</style>
<style>
.search .el-input__inner {
	padding: 0 30px !important;
}

.form-button {
	position: relative;
	top: 30px;
	left: 120px;
}
</style>

<style lang="scss">
.form-box {
	.el-form-item__content {
		margin-left: 100px !important;
		.el-input {
			width: auto !important;
		}
	}
}
</style>


