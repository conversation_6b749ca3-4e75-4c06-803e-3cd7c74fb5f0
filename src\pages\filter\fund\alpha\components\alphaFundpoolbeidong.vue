<template>
  <div class="homebodyfontsize tableindexheader"
       v-loading="getmsgload">
    <tempbasket ref="tempbasketfund"
                type="fund"></tempbasket>
    <div>
      <div style="margin-top: 10px; padding-bottom: 20px; background: white">
        <div style="display: flex">
          <div class="title"
               style="display: flex; background: white !important">
            <div class="pointssearch"></div>
            筛选条件
          </div>
        </div>
        <div style="margin-top: 10px">
          <!-- <div style="display:flex;width:100%;maring-top:10px" >
            <div style="display:flex;margin-left:30px">
            <el-input  v-model="fundname" placeholder="请输入基金名称" style="width:100%;margin-right:10px" class="fundnameinput"></el-input>
            <el-button  type="" icon="el-icon-search" @click="handleSearch">搜索</el-button>    
            </div> 
        </div> -->
          <div style="margin-left: 30px">
            <el-form ref="elForm"
                     :model="formData"
                     size="medium"
                     label-width="100px">
              <el-form-item label="被动权益">
                <el-radio-group v-model="formData.field101"
                                @change="beidongchange"
                                size="medium">
                  <el-radio v-for="(item, index) in field101Options"
                            :key="index"
                            :label="item.value"
                            :disabled="item.disabled">{{
										item.label
									}}</el-radio>
                </el-radio-group>
                <div class="searchpadding"></div>
              </el-form-item>
              <el-form-item v-if="kuanjishow"
                            label="宽基选择"
                            prop="field102">
                <el-select v-model="valuebase1"
                           class="width100"
                           collapse-tags
                           filterable
                           :multiple-limit="multiple"
                           multiple
                           placeholder="请选择宽基基准">
                  <el-option v-for="item in optionsbase1"
                             :key="item.value"
                             :label="item.label"
                             :value="item.value"> </el-option>
                </el-select>
              </el-form-item>

              <el-form-item v-if="hangyeshow"
                            label="行业选择"
                            prop="field104">
                <el-select v-model="valuebase2"
                           filterable
                           collapse-tags
                           :multiple-limit="multiple"
                           multiple
                           placeholder="请选择行业基准">
                  <el-option v-for="item in optionsbase2"
                             :key="item.value"
                             :label="item.label"
                             :value="item.value"> </el-option>
                </el-select>
              </el-form-item>

              <el-form-item v-if="fenggeshow"
                            label="主题选择"
                            prop="field106">
                <el-select v-model="valuebase3"
                           filterable
                           collapse-tags
                           :multiple-limit="multiple"
                           multiple
                           placeholder="请选择主题基准">
                  <el-option v-for="item in optionsbase3"
                             :key="item.value"
                             :label="item.label"
                             :value="item.value"> </el-option>
                </el-select>
              </el-form-item>

              <el-form-item v-if="sbetashow"
                            label="smartbeta选择"
                            prop="field106">
                <el-select v-model="valuebase4"
                           filterable
                           collapse-tags
                           :multiple-limit="multiple"
                           multiple
                           placeholder="请选择smartbeta基准">
                  <el-option v-for="item in optionsbase4"
                             :key="item.value"
                             :label="item.label"
                             :value="item.value"> </el-option>
                </el-select>
              </el-form-item>
            </el-form>
            <div>
              <div style="height: 10px"></div>
              <!-- <el-button type="" @click="submitForm">提交</el-button> -->
              <el-button size="middle"
                         style="background: #4096FF; color: white"
                         @click="resetForm">清除筛选条件</el-button>
              <!-- <el-button @click="printconsole">导出筛选结果</el-button> -->
            </div>
          </div>
          <div v-if="sbetashow == true">
            <v-chart v-loading="empty6"
                     element-loading-text="暂无数据"
                     element-loading-spinner="el-icon-document-delete"
                     element-loading-background="rgba(239, 239, 239, 0.5)"
                     style="height: 50vh; width: 100%"
                     :options="allocation" />
          </div>
        </div>
      </div>
      <div class="line2"
           style="margin-top: 5px; margin-bottom: 5px"></div>
      <div style="padding-bottom: 20px; background: white">
        <div style="display: flex">
          <div class="title"
               style="display: flex; background: white !important">
            <div class="pointssearch"></div>
            筛选结果
          </div>
          <div style="justify-content: flex-end; display: flex"
               class="marginight20px">
            <el-button size="medium"
                       style="background: #4096FF; color: white"
                       @click="printconsole">导出筛选结果</el-button>
          </div>
        </div>
        <!--    :row-class-name="tableRowClassName" -->
        <div v-loading="loading1"
             style="margin-left: 16px; margin-right: 16px"
             class="tablemargin">
          <el-table :default-sort="{ prop: 'code' }"
                    :data="tableData"
                    :cell-style="elcellstyle"
                    class="tableindex"
                    ref="multipleTable"
                    @sort-change="sort_change"
                    header-cell-class-name="table-header">
            <el-table-column prop="name"
                             :width="getfontSize(270)"
                             align="gotoleft"
                             label="基金名称">
              <template slot-scope="scope"><a @click="godetail(scope.row.code, scope.row.name)">{{ scope.row.name | isDefault }}</a></template>
            </el-table-column>
            <el-table-column prop="code"
                             :width="getfontSize(128)"
                             label="基金代码"
                             align="gotoleft"> </el-table-column>
            <el-table-column prop="manager_name"
                             :width="getfontSize(128)"
                             label="基金经理"
                             align="gotoleft">
              <template slot-scope="scope"><a @click="godetailP(scope.row.manager_code.split(',')[0], scope.row.manager_name.split(',')[0])">{{
									scope.row.manager_name.split(',')[0] | isDefault
								}}</a><span v-if="scope.row.manager_code.split(',').length >= 2">,<a @click="godetailP(scope.row.manager_code.split(',')[1], scope.row.manager_name.split(',')[1])">{{
										scope.row.manager_name.split(',')[1] | isDefault
									}}</a></span></template>
            </el-table-column>

            <el-table-column v-if="valuebase5 == false"
                             sortable="custom"
                             :width="getfontSize(250)"
                             prop="index_name"
                             label="类型名称"
                             align="gotoleft">
            </el-table-column>
            <el-table-column label="收益"
                             align="gotoleft">
              <!-- <el-table-column prop="1w" sortable='custom' label="近一周" align="gotoleft"> 
                   <template slot-scope="scope">{{scope.row['1w_cum_excessreturn']|fix2p}}</template>
                </el-table-column> -->
              <el-table-column prop="1m"
                               sortable="custom"
                               :width="getfontSize(128)"
                               label="近一月"
                               align="gotoleft">
                <template slot-scope="scope">{{ scope.row['1m_cum_return'] | fix2p }}</template>
              </el-table-column>
              <el-table-column prop="1q"
                               sortable="custom"
                               :width="getfontSize(128)"
                               label="近一季"
                               align="gotoleft">
                <template slot-scope="scope">{{ scope.row['1q_cum_return'] | fix2p }}</template>
              </el-table-column>
              <el-table-column prop="1y"
                               sortable="custom"
                               :width="getfontSize(128)"
                               label="近一年"
                               align="gotoleft">
                <template slot-scope="scope">{{ scope.row['1y_cum_return'] | fix2p }}</template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="信息率"
                             align="gotoleft">
              <el-table-column prop="1m_information"
                               :width="getfontSize(128)"
                               sortable="custom"
                               label="近一月"
                               align="gotoleft">
                <template slot-scope="scope">{{ scope.row['1m_information'] | fix2pxx }}</template>
              </el-table-column>
              <el-table-column prop="1q_information"
                               :width="getfontSize(128)"
                               sortable="custom"
                               label="近一季"
                               align="gotoleft">
                <template slot-scope="scope">{{ scope.row['1q_information'] | fix2pxx }}</template>
              </el-table-column>
              <el-table-column prop="1y_information"
                               :width="getfontSize(128)"
                               sortable="custom"
                               label="近一年"
                               align="gotoleft">
                <template slot-scope="scope">{{ scope.row['1y_information'] | fix2pxx }}</template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="跟踪误差"
                             align="gotoleft">
              <el-table-column prop="1m_trackingerror"
                               :width="getfontSize(128)"
                               sortable="custom"
                               label="近一月"
                               align="gotoleft">
                <template slot-scope="scope">{{ scope.row['1m_trackingerror'] | fix2p }}</template>
              </el-table-column>
              <el-table-column prop="1q_trackingerror"
                               :width="getfontSize(128)"
                               sortable="custom"
                               label="近一季"
                               align="gotoleft">
                <template slot-scope="scope">{{ scope.row['1q_trackingerror'] | fix2p }}</template>
              </el-table-column>
              <el-table-column prop="1y_trackingerror"
                               :width="getfontSize(128)"
                               sortable="custom"
                               label="近一年"
                               align="gotoleft">
                <template slot-scope="scope">{{ scope.row['1y_trackingerror'] | fix2p }}</template>
              </el-table-column>
            </el-table-column>
            <!-- <el-table-column  prop="netasset" sortable="custom" label="规模" :width="getfontSize(128)" align="gotoleft">
							<template slot-scope="scope">{{ scope.row['netasset'] | fix10Y }}亿</template>
						</el-table-column> -->
            <el-table-column prop="bigs"
                             :show-overflow-tooltip="true"
                             sortable="custom"
                             label="重仓股"
                             align="gotoleft"> </el-table-column>
            <!-- <el-table-column label="查看详情" width="100" align="gotoleft">
                    <template slot-scope="scope"><div @click="godetail(scope.row.code,scope.row.name)"><i  class="el-icon-tickets"></i></div></template>  
                </el-table-column> -->
            <el-table-column label="关注"
                             :width="getfontSize(100)"
                             align="gotoleft">
              <template slot-scope="scope">
                <div @click="addpool(scope.row.code, scope.row.name)"><i class="el-icon-circle-plus icon_color"></i></div>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <el-pagination background
                           layout="total,sizes, prev, pager, next"
                           :current-page.sync="pageIndex1"
                           :page-size="pageSize1"
                           :total="pageTotal1"
                           @size-change="handleSizeChange1"
                           @current-change="handlePageChange1"></el-pagination>
          </div>
        </div>
        <div v-if="kuanjishow == true || hangyeshow == true || fenggeshow == true"
             class="title"
             style="display: flex; background: white !important">
          <div class="pointssearch"></div>
          对比同类型主动权益
        </div>
        <div v-if="kuanjishow == true || hangyeshow == true || fenggeshow == true"
             style="margin-left: 16px; margin-right: 16px"
             class="tablemargin">
          <!--   :row-class-name="tableRowClassName" -->
          <el-table v-loading="loading1"
                    :default-sort="{ prop: 'code' }"
                    :data="tableData2"
                    :cell-style="elcellstyle"
                    ref="multipleTable"
                    @sort-change="sort_change2"
                    header-cell-class-name="table-header">
            <el-table-column prop="name"
                             :width="getfontSize(270)"
                             align="gotoleft"
                             label="基金名称">
              <template slot-scope="scope"><a style="border-bottom: 1px solid #4096ff"
                   @click="godetail2(scope.row.code, scope.row.name)">{{
									scope.row.name | isDefault
								}}</a></template>
            </el-table-column>
            <el-table-column :width="getfontSize(128)"
                             prop="code"
                             label="基金代码"
                             align="gotoleft"> </el-table-column>
            <el-table-column :width="getfontSize(128)"
                             prop="manager_name"
                             label="基金经理"
                             align="gotoleft">
              <template slot-scope="scope"><a style="border-bottom: 1px solid #4096ff"
                   @click="godetailP(scope.row.manager_code.split(',')[0], scope.row.manager_name.split(',')[0])">{{ scope.row.manager_name.split(',')[0] | isDefault }}</a><span v-if="scope.row.manager_code.split(',').length >= 2">,<a style="border-bottom: 1px solid #4096ff"
                     @click="godetailP(scope.row.manager_code.split(',')[1], scope.row.manager_name.split(',')[1])">{{ scope.row.manager_name.split(',')[1] | isDefault }}</a></span></template>
            </el-table-column>
            <el-table-column prop="netasset"
                             sortable="custom"
                             label="规模"
                             :width="getfontSize(128)"
                             align="gotoleft">
              <template slot-scope="scope">{{ scope.row['netasset'] | fix10Y }}亿</template>
            </el-table-column>
            <!-- <el-table-column sortable="custom" prop="index_name" label="类型名称" align="gotoleft"> </el-table-column> -->
            <!-- <el-table-column prop="1w" sortable='custom'   :width='getfontSize(128)'  label="近一周收益" align="gotoleft"> 
                   <template slot-scope="scope">{{scope.row['1w_cum_excessreturn']|fix2p}}</template>
                </el-table-column> -->
            <el-table-column prop="1m"
                             sortable="custom"
                             label="近一月收益"
                             align="gotoleft">
              <template slot-scope="scope">{{ scope.row['1m_cum_return'] | fix2p }}</template>
            </el-table-column>
            <el-table-column prop="1q"
                             sortable="custom"
                             label="近一季收益"
                             align="gotoleft">
              <template slot-scope="scope">{{ scope.row['1q_cum_return'] | fix2p }}</template>
            </el-table-column>
            <el-table-column prop="1y"
                             sortable="custom"
                             label="近一年收益"
                             align="gotoleft">
              <template slot-scope="scope">{{ scope.row['1y_cum_return'] | fix2p }}</template>
            </el-table-column>

            <el-table-column prop="bigs"
                             :show-overflow-tooltip="true"
                             sortable="custom"
                             label="重仓股"
                             align="gotoleft"> </el-table-column>
            <!-- <el-table-column label="查看详情" width="100" align="gotoleft">
                    <template slot-scope="scope"><div @click="godetail(scope.row.code,scope.row.name)"><i  class="el-icon-tickets"></i></div></template>  
                </el-table-column> -->
            <el-table-column label="关注"
                             :width="getfontSize(100)"
                             align="gotoleft">
              <template slot-scope="scope">
                <div @click="addpool(scope.row.code, scope.row.name)"><i class="el-icon-circle-plus icon_color"></i></div>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <el-pagination background
                           layout="total, sizes, prev, pager, next"
                           :current-page.sync="pageIndex2"
                           :page-size="pageSize2"
                           :total="pageTotal2"
                           @size-change="handleSizeChange2"
                           @current-change="handlePageChange2"></el-pagination>
          </div>
        </div>
      </div>
    </div>
    <el-dialog title="选择添加的基金池"
               :visible.sync="addfundvis"
               width="20%"
               destroy-on-close>
      基金代码:<br />
      <el-input type="text"
                :disabled="true"
                :value="choosefundid"
                label="基金代码"></el-input> 基金名称:<br />
      <el-input type="text"
                :disabled="true"
                :value="choosefundname"
                label="基金名称"></el-input>
      基金池：<br />
      <el-select style="width: 100%"
                 v-model="choosedpool"
                 placeholder="请选择您的基金池">
        <el-option v-for="item in options"
                   :key="item.value"
                   :label="item.label"
                   :value="item.value"> </el-option>
      </el-select>
      <br />理由:<br />
      <el-input type="textarea"
                v-model="choosereason"
                label="选择的理由"></el-input>
      <span slot="footer"
            class="dialog-footer">
        <el-button type="primary"
                   @click="saveEdit(form)">提 交</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { alphaGo } from '@/assets/js/alpha_type.js';
import { fontSize } from '@/assets/js/echartsrpxtorem'; //注意路径
// //require('echarts')
import tempbasket from './components/tempbasket';
import axios from '@/api/index';
import VCharts from 'vue-echarts';
export default {
  components: { 'v-chart': VCharts, tempbasket },
  props: [],
  data () {
    return {
      multiple: 5,
      pageIndex2: 1,
      pageSize2: 10,
      pageTotal2: 0,
      pageIndex1: 1,
      pageSize1: 10,
      pageTotal1: 0,
      tableData: [],
      alldata: [],
      tableData2: [],
      alldata2: [],
      empty6: false,
      getmsgload: true,
      valuebase1: [],
      valuebase2: [],
      valuebase3: [],
      valuebase4: [],
      valuebase5: false,
      optionsbase1: [],
      optionsbase2: [],
      optionsbase3: [],
      optionsbase4: [],
      optionsbase5: [],
      hangyeshow: false,
      fenggeshow: false,
      kuanjishow: true,
      sbetashow: false,
      ganggushow: false,
      choosedpool: null,
      choosereason: null,
      choosefundid: null,
      choosefundname: null,
      allocation: null,
      indexlist: [],
      options: [
        {
          value: '选项1',
          label: '池子1'
        },
        {
          value: '选项2',
          label: '池子2'
        },
        {
          value: '选项3',
          label: '池子3'
        },
        {
          value: '选项4',
          label: '池子4'
        },
        {
          value: '选项5',
          label: '池子5'
        }
      ],
      loading1: false,
      addfundvis: false,

      fundname: null,
      formData: {
        field101: '宽基',
        field102: undefined,
        field103: undefined,
        field104: undefined,
        field105: undefined,
        field106: undefined
      },
      field101Options: [
        {
          label: '宽基',
          value: '宽基'
        },
        {
          label: '行业',
          value: '行业'
        },
        {
          label: '主题',
          value: '主题'
        },
        {
          label: 'smartbeta',
          value: 'smartbeta'
        },
        {
          label: '港股',
          value: '港股'
        }
      ],
      is_request_active: false
    };
  },
  computed: {},
  watch: {
    valuebase1 (val) {
      // handler(newValue, oldValue) {
      if (val.length > 0) {
        this.submitForm();
      }

      // 　　　　　　////console.log(newValue)
      //         ////console.log(oldValue)
      //       ////console.log('22')
      // 　　　　},
      // 　　　　deep: true
    },
    valuebase2 (val) {
      // handler(newValue, oldValue) {
      if (val.length > 0) {
        this.submitForm();
      }
      // 　　　　　　////console.log(newValue)
      //         ////console.log(oldValue)
      //       ////console.log('22')
      // 　　　　},
      // 　　　　deep: true
    },
    valuebase3 (val) {
      // handler(newValue, oldValue) {
      if (val.length > 0) {
        this.submitForm();
      }
      // 　　　　　　////console.log(newValue)
      //         ////console.log(oldValue)
      //       ////console.log('22')
      // 　　　　},
      // 　　　　deep: true
    },
    valuebase4 (val) {
      ////console.log('4')
      // handler(newValue, oldValue) {
      if (val.length > 0) {
        this.submitForm();
      }
      // 　　　　　　////console.log(newValue)
      //         ////console.log(oldValue)
      //       ////console.log('22')
      // 　　　　},
      // 　　　　deep: true
    }
  },
  filters: {
    fix10Y (value) {
      return (Number(value) / 100000000).toFixed(2);
    },
    fix6 (value) {
      return value.substring(0, 10);
    },
    fix3 (value) {
      return parseInt(value * 1000) / 1000;
    },
    fix2pxx (value) {
      if (value == '' || value == null || value == 'nan' || value == '--') {
        return '—';
      } else {
        return Number(value).toFixed(2) + '%';
      }
    },
    fix2p (value) {
      if (value === '' || value == null || value == 'nan' || value == '--') {
        return '—';
      } else {
        return (value * 100).toFixed(2) + '%';
      }
    },
    fixp (value) {
      return value.toFixed(2) + '%';
    },
    isDefault (value) {
      return value == '--' ? '' : value;
    }
  },
  created () {
    if (this.localStorage.getItem('formDataindex') != null && this.localStorage.getItem('formDataindex') != 'null') {
      this.formData = JSON.parse(this.localStorage.getItem('formDataindex'));
      if (this.formData.field101 == '宽基') {
        this.valuebase1 = JSON.parse(this.localStorage.getItem('valuebase1'));
      } else if (this.formData.field101 == '行业') {
        this.valuebase2 = JSON.parse(this.localStorage.getItem('valuebase2'));
        this.hangyeshow = true;
        this.kuanjishow = false;
      } else if (this.formData.field101 == '主题') {
        this.valuebase3 = JSON.parse(this.localStorage.getItem('valuebase3'));
        this.fenggeshow = true;
        this.kuanjishow = false;
      } else if (this.formData.field101 == 'smartbeta') {
        this.valuebase4 = JSON.parse(this.localStorage.getItem('valuebase4'));
        this.sbetashow = true;
        this.kuanjishow = false;
      } else if (this.formData.field101 == '港股') {
        this.valuebase5 = JSON.parse(this.localStorage.getItem('valuebase5'));
        this.ganggushow = true;
        this.kuanjishow = false;
      }
    }
    //  if(this.localStorage.getItem('alldataindex')!=null){
    //   this.alldata = JSON.parse(
    //     this.localStorage.getItem("alldataindex")
    //   );
    //  }
    let that = this;
    axios
      .get(that.$baseUrl + '/index_condition/')
      .then((res) => {
        ////console.log(res.data)
        that.getmsgload = false;
        that.optionsbase1 = res.data.indexes;
        that.optionsbase2 = res.data.industries;
        that.optionsbase3 = res.data.theme;
        that.optionsbase4 = res.data.barra;
      })
      .catch((error) => {
        //that.$message('数据缺失');
        that.getmsgload = false;
      });
    axios
      .get(that.$baseUrl + '/barra_ten/')
      .then((res) => {
        ////console.log(res.data)
        ////console.log('drawline')
        if (res.data.date == null || res.data.date == [] || res.data.date == '' || res.data.date.length <= 0) {
          that.empty6 = true;
        } else {
          that.allocation = {
            title: {
              text: 'barra因子半年收益',
              textStyle: {
                fontSize: fontSize(14)
              }
            },
            tooltip: {
              textStyle: {
                fontSize: fontSize(14)
              },
              formatter: function (obj) {
                var value = obj[0].axisValue + `<br />`;
                for (let i = 0; i < obj.length; i++) {
                  value +=
                    `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:` +
                    obj[i].color +
                    `;"></span>` +
                    obj[i].seriesName +
                    ':' +
                    (Number(obj[i].data) * 100).toFixed(2) +
                    `<br />`;
                }
                return value;
              },
              trigger: 'axis'
            },
            legend: {
              textStyle: {
                fontSize: fontSize(14)
              }
            },
            xAxis: {
              nameTextStyle: {
                fontSize: fontSize(14)
              },
              axisLabel: {
                show: true,
                textStyle: {
                  fontSize: fontSize(14)
                }
              },
              type: 'category',
              data: res.data.date,
              boundaryGap: false //x轴两边不留空白
            },
            yAxis: {
              axisLine: { show: false },
              axisTick: { show: false },
              splitLine: {
                show: true,
                lineStyle: {
                  type: 'dashed'
                }
              },
              nameTextStyle: {
                fontSize: fontSize(14)
              },
              axisLabel: {
                show: true,
                textStyle: {
                  fontSize: fontSize(14)
                }
              },
              type: 'value',
              scale: true,
              axisLabel: {
                formatter: function (obj) {
                  //   //console.log(obj)
                  // var value = obj.value;
                  return (obj * 100).toFixed(1) + '%';
                }
              }
            },
            series: [
              {
                data: res.data.beta,
                type: 'line',
                symbol: 'none',
                smooth: true,
                name: '贝塔'
              },
              {
                data: res.data.bp,
                symbol: 'none',
                type: 'line',
                smooth: true,
                name: 'bp'
              },
              {
                data: res.data.earningyield,
                symbol: 'none',
                type: 'line',
                smooth: true,
                name: '收益'
              },
              {
                data: res.data.growth,
                symbol: 'none',
                type: 'line',
                smooth: true,
                name: '成长'
              },
              {
                data: res.data.leverage,
                symbol: 'none',
                type: 'line',
                smooth: true,
                name: '杠杆'
              },
              {
                data: res.data.liquidity,
                symbol: 'none',
                type: 'line',
                smooth: true,
                name: '流动性'
              },
              {
                data: res.data.momentum,
                symbol: 'none',
                type: 'line',
                smooth: true,
                name: '动量'
              },
              {
                data: res.data.nonlinearsize,
                symbol: 'none',
                type: 'line',
                smooth: true,
                name: '非线性化'
              },
              {
                data: res.data.residualvolatility,
                symbol: 'none',
                type: 'line',
                smooth: true,
                name: '剩余流动性'
              },
              {
                data: res.data.size,
                symbol: 'none',
                type: 'line',
                smooth: true,
                name: '规模'
              }
            ]
          };
        }
      })
      .catch((error) => {
        //that.$message('数据缺失');
        that.empty6 = true;
      });
  },
  mounted () { },
  methods: {
    getfontSize (val) {
      return fontSize(val);
    },
    my_desc_sort (name) {
      //  ////console.log(name)
      return function (a, b) {
        if (a[name] == '--') {
          return 1;
        }
        if (b[name] == '--') {
          return -1;
        }
        if (a[name] > b[name]) {
          return -1;
        } else if (a[name] < b[name]) {
          return 1;
        } else {
          return 0;
        }
      };
    },
    my_asc_sort (name) {
      return function (a, b) {
        if (a[name] == '--') {
          return -1;
        }
        if (b[name] == '--') {
          return 1;
        }
        if (a[name] < b[name]) {
          return -1;
        } else if (a[name] > b[name]) {
          return 1;
        } else {
          return 0;
        }
      };
    },

    sort_change (column) {
      console.log(this.alldata);
      this.pageIndex = 1; // return to the first page after sorting
      if (column.prop === 'index_name') {
        if (column.order === 'descending') {
          this.alldata = this.alldata.sort(this.my_desc_sort('index_name'));
        } else if (column.order === 'ascending') {
          this.alldata = this.alldata.sort(this.my_asc_sort('index_name'));
        }
      } else if (column.prop === '1y') {
        if (column.order === 'descending') {
          this.alldata = this.alldata.sort(this.my_desc_sort('1y_cum_return'));
        } else if (column.order === 'ascending') {
          this.alldata = this.alldata.sort(this.my_asc_sort('1y_cum_return'));
        }
      } else if (column.prop === '1m') {
        if (column.order === 'descending') {
          this.alldata = this.alldata.sort(this.my_desc_sort('1m_cum_return'));
        } else if (column.order === 'ascending') {
          this.alldata = this.alldata.sort(this.my_asc_sort('1m_cum_return'));
        }
      } else if (column.prop === '1q') {
        if (column.order === 'descending') {
          this.alldata = this.alldata.sort(this.my_desc_sort('1q_cum_return'));
        } else if (column.order === 'ascending') {
          this.alldata = this.alldata.sort(this.my_asc_sort('1q_cum_return'));
        }
      } else if (column.prop === '1w') {
        if (column.order === 'descending') {
          this.alldata = this.alldata.sort(this.my_desc_sort('1w_cum_return'));
        } else if (column.order === 'ascending') {
          this.alldata = this.alldata.sort(this.my_asc_sort('1w_cum_return'));
        }
      } else if (column.prop === '1y_information') {
        if (column.order === 'descending') {
          this.alldata = this.alldata.sort(this.my_desc_sort('1y_information'));
        } else if (column.order === 'ascending') {
          this.alldata = this.alldata.sort(this.my_asc_sort('1y_information'));
        }
      } else if (column.prop === '1m_information') {
        if (column.order === 'descending') {
          this.alldata = this.alldata.sort(this.my_desc_sort('1m_information'));
        } else if (column.order === 'ascending') {
          this.alldata = this.alldata.sort(this.my_asc_sort('1m_information'));
        }
      } else if (column.prop === '1q_information') {
        if (column.order === 'descending') {
          this.alldata = this.alldata.sort(this.my_desc_sort('1q_information'));
        } else if (column.order === 'ascending') {
          this.alldata = this.alldata.sort(this.my_asc_sort('1q_information'));
        }
      } else if (column.prop === '1y_trackingerror') {
        if (column.order === 'descending') {
          this.alldata = this.alldata.sort(this.my_desc_sort('1y_trackingerror'));
        } else if (column.order === 'ascending') {
          this.alldata = this.alldata.sort(this.my_asc_sort('1y_trackingerror'));
        }
      } else if (column.prop === '1m_trackingerror') {
        if (column.order === 'descending') {
          this.alldata = this.alldata.sort(this.my_desc_sort('1m_trackingerror'));
        } else if (column.order === 'ascending') {
          this.alldata = this.alldata.sort(this.my_asc_sort('1m_trackingerror'));
        }
      } else if (column.prop === '1q_trackingerror') {
        if (column.order === 'descending') {
          this.alldata = this.alldata.sort(this.my_desc_sort('1q_trackingerror'));
        } else if (column.order === 'ascending') {
          this.alldata = this.alldata.sort(this.my_asc_sort('1q_trackingerror'));
        }
      }
      this.tableData = this.alldata.slice(0, 9); // show only one page
    },
    sort_change2 (column) {
      this.pageIndex = 1; // return to the first page after sorting
      if (column.prop === 'index_name') {
        if (column.order === 'descending') {
          this.alldata2 = this.alldata2.sort(this.my_desc_sort('index_name'));
        } else if (column.order === 'ascending') {
          this.alldata2 = this.alldata2.sort(this.my_asc_sort('index_name'));
        }
      } else if (column.prop === '1y') {
        if (column.order === 'descending') {
          this.alldata2 = this.alldata2.sort(this.my_desc_sort('1y_cum_return'));
        } else if (column.order === 'ascending') {
          this.alldata2 = this.alldata2.sort(this.my_asc_sort('1y_cum_return'));
        }
      } else if (column.prop === '1m') {
        if (column.order === 'descending') {
          this.alldata2 = this.alldata2.sort(this.my_desc_sort('1m_cum_return'));
        } else if (column.order === 'ascending') {
          this.alldata2 = this.alldata2.sort(this.my_asc_sort('1m_cum_return'));
        }
      } else if (column.prop === '1q') {
        if (column.order === 'descending') {
          this.alldata2 = this.alldata2.sort(this.my_desc_sort('1q_cum_return'));
        } else if (column.order === 'ascending') {
          this.alldata2 = this.alldata2.sort(this.my_asc_sort('1q_cum_return'));
        }
      } else if (column.prop === '1w') {
        if (column.order === 'descending') {
          this.alldata2 = this.alldata2.sort(this.my_desc_sort('1w_cum_return'));
        } else if (column.order === 'ascending') {
          this.alldata2 = this.alldata2.sort(this.my_asc_sort('1w_cum_return'));
        }
      } else if (column.prop === '1y_information') {
        if (column.order === 'descending') {
          this.alldata2 = this.alldata2.sort(this.my_desc_sort('1y_information'));
        } else if (column.order === 'ascending') {
          this.alldata2 = this.alldata2.sort(this.my_asc_sort('1y_information'));
        }
      } else if (column.prop === '1m_information') {
        if (column.order === 'descending') {
          this.alldata2 = this.alldata2.sort(this.my_desc_sort('1m_information'));
        } else if (column.order === 'ascending') {
          this.alldata2 = this.alldata2.sort(this.my_asc_sort('1m_information'));
        }
      } else if (column.prop === '1q_information') {
        if (column.order === 'descending') {
          this.alldata2 = this.alldata2.sort(this.my_desc_sort('1q_information'));
        } else if (column.order === 'ascending') {
          this.alldata2 = this.alldata2.sort(this.my_asc_sort('1q_information'));
        }
      } else if (column.prop === '1y_trackingerror') {
        if (column.order === 'descending') {
          this.alldata2 = this.alldata2.sort(this.my_desc_sort('1y_trackingerror'));
        } else if (column.order === 'ascending') {
          this.alldata2 = this.alldata2.sort(this.my_asc_sort('1y_trackingerror'));
        }
      } else if (column.prop === '1m_trackingerror') {
        if (column.order === 'descending') {
          this.alldata2 = this.alldata2.sort(this.my_desc_sort('1m_trackingerror'));
        } else if (column.order === 'ascending') {
          this.alldata2 = this.alldata2.sort(this.my_asc_sort('1m_trackingerror'));
        }
      } else if (column.prop === '1q_trackingerror') {
        if (column.order === 'descending') {
          this.alldata2 = this.alldata2.sort(this.my_desc_sort('1q_trackingerror'));
        } else if (column.order === 'ascending') {
          this.alldata2 = this.alldata2.sort(this.my_asc_sort('1q_trackingerror'));
        }
      }
      this.tableData2 = this.alldata2.slice(0, 9); // show only one page
    },
    tableRowClassName ({ row }) {
      // ////console.log(row)
      if (this.indexlist.length == 2) {
        if (row.index_name == this.indexlist[0]) {
          return 'warning0';
        } else {
          return 'warning1';
        }
      }
      if (this.indexlist.length == 3) {
        if (row.index_name == this.indexlist[0]) {
          return 'warning0';
        } else if (row.index_name == this.indexlist[1]) {
          return 'warning1';
        } else {
          return 'warning2';
        }
      }
      if (this.indexlist.length == 4) {
        if (row.index_name == this.indexlist[0]) {
          return 'warning0';
        } else if (row.index_name == this.indexlist[1]) {
          return 'warning1';
        } else if (row.index_name == this.indexlist[2]) {
          return 'warning2';
        } else {
          return 'warning3';
        }
      }
      if (this.indexlist.length == 5) {
        if (row.index_name == this.indexlist[0]) {
          return 'warning0';
        } else if (row.index_name == this.indexlist[1]) {
          return 'warning1';
        } else if (row.index_name == this.indexlist[2]) {
          return 'warning2';
        } else if (row.index_name == this.indexlist[3]) {
          return 'warning3';
        } else if (row.index_name == this.indexlist[4]) {
          return 'warning4';
        }
      }
    },
    elcellstyle ({ row, column, rowIndex, columnIndex }) {
      // ////console.log(row[0])
      // ////console.log(row[0])
      if (this.valuebase5 == true) {
        // if (columnIndex == 3) {
        // 	if (row['1w_cum_return'] >= 0) {
        // 		return 'color: #E85D2D;';
        // 	} else return 'color: #20995B;';
        // }
        if (columnIndex == 3) {
          if (row['1m_cum_return'] >= 0) {
            return 'color: #E85D2D;';
          } else return 'color: #20995B;';
        }
        if (columnIndex == 4) {
          if (row['1q_cum_return'] >= 0) {
            return 'color: #E85D2D;';
          } else return 'color: #20995B;';
        }
        if (columnIndex == 5) {
          if (row['1y_cum_return'] >= 0) {
            return 'color: #E85D2D;';
          } else return 'color: #20995B;';
        }
      } else {
        // if(columnIndex ==4){
        //      if(row['1w_cum_excessreturn']>=0) {
        //         return 'color: #E85D2D;';

        //      }else
        //           return 'color: #18C2A0;'

        //   }
        if (columnIndex == 4) {
          if (row['1m_cum_return'] >= 0) {
            return 'color: #E85D2D;';
          } else return 'color: #18C2A0;';
        }
        if (columnIndex == 5) {
          if (row['1q_cum_return'] >= 0) {
            return 'color: #E85D2D;';
          } else return 'color: #18C2A0;';
        }
        if (columnIndex == 6) {
          if (row['1y_cum_return'] >= 0) {
            return 'color: #E85D2D;';
          } else return 'color: #18C2A0;';
        }
      }
    },
    handlePageChange1 () {
      this.tableData = this.alldata.slice((this.pageIndex1 - 1) * this.pageSize1, this.pageIndex1 * this.pageSize1);
    },
    handleSizeChange1 (val) {
      this.pageSize1 = val;
      this.tableData = this.alldata.slice((this.pageIndex1 - 1) * this.pageSize1, this.pageIndex1 * this.pageSize1);
    },
    handlePageChange2 () {
      this.tableData2 = this.alldata.slice((this.pageIndex2 - 1) * this.pageSize2, this.pageIndex2 * this.pageSize2);
    },
    handleSizeChange2 (val) {
      this.pageSize2 = val;
      this.tableData2 = this.alldata.slice((this.pageIndex2 - 1) * this.pageSize2, this.pageIndex2 * this.pageSize2);
    },
    godetail (id, name) {
      //带参进去
      alphaGo(id, name, this.$route.path);
    },
    godetail2 (id, name) {
      //带参进去
      alphaGo(id, name, this.$route.path);
    },
    addpool (id, name) {
      let that = this;
      axios
        .post(that.$baseUrl + '/pool/basket_fund/', { fund_code: id })
        .then((res) => {
          that.$message('新增成功' + '  ' + id + ' ' + name);
        })
        .catch((err) => {
          //  that.$message('失败')
          ////console.log(err)
          //that.$message('数据缺失')
        });
    },
    printconsole () {
      const { export_json_to_excel } = require('@/vendor/Export2Excel');
      var list = [];
      let tHeader = [];
      let filterVal = [];

      tHeader = [
        '基金名称',
        '基金经理姓名',
        '基金代码',
        ' ',
        '近一月收益',
        '近一季收益',
        '近一年收益',
        '近一月信息率',
        '近一季信息率',
        '近一年信息率',
        '近一月跟踪误差',
        '近一季跟踪误差',
        '近一年跟踪误差',
        '类型',
        '重仓股'
      ];
      filterVal = [
        'name',
        'manager',
        'code',
        '1w_cum_excessreturn',
        '1m_cum_excessreturn',
        '1q_cum_excessreturn',
        '1y_cum_excessreturn',
        '1m_information',
        '1q_information',
        '1y_information',
        '1m_trackingerror',
        '1q_trackingerror',
        '1y_trackingerror',
        'index_name',
        'big10'
      ];
      ////console.log(this.alldata)
      for (let i = 0; i < this.alldata.length; i++) {
        list[i] = [];
        list[i][0] = this.alldata[i].name;
        list[i][1] = this.alldata[i].manager_name;
        list[i][2] = this.alldata[i].code;
        list[i][4] = this.alldata[i]['1m_cum_excessreturn'];
        list[i][5] = this.alldata[i]['1q_cum_excessreturn'];
        list[i][6] = this.alldata[i]['1y_cum_excessreturn'];
        //  list[i][7] = this.alldata[i]['1w_information']
        list[i][7] = this.alldata[i]['1m_information'];
        list[i][8] = this.alldata[i]['1q_information'];
        list[i][9] = this.alldata[i]['1y_information'];
        //  list[i][11] = this.alldata[i]['1w_trackingerror']
        list[i][10] = this.alldata[i]['1m_trackingerror'];
        list[i][11] = this.alldata[i]['1q_trackingerror'];
        list[i][12] = this.alldata[i]['1y_trackingerror'];
        list[i][13] = this.alldata[i]['index_name'];
        list[i][14] = this.alldata[i]['bigs'];
      }

      export_json_to_excel(tHeader, list, '被动权益筛选结果');
    },
    godetailP (id, name) {
      //带参进去
      this.$router.push({ path: '/fundmanagerdetail/' + id, hash: '', query: { id: id, name: name } });
    },
    submitForm () {
      let that = this;
      that.getpost();
      // ////console.log(this.valuebase1)
      // ////console.log(this.valuebase2)
      // ////console.log(this.valuebase3)
      // ////console.log(this.valuebase4)
      // ////console.log(this.valuebase5)
      // ////console.log(that.formData.field101)
      // if(that.formData.field101=='宽基'){
      //   if(that.valuebase1.length<=0){
      //       that.$message('请选择宽基基准')
      //   }
      //   else that.getpost()
      // }
      // else if(that.formData.field101=='行业'){
      //   if(that.valuebase2.length<=0){
      //       that.$message('请选择行业基准')
      //   }
      //   else that.getpost()
      // }
      //  else if(that.formData.field101=='主题'){
      //   if(that.valuebase3.length<=0){
      //       that.$message('请选择主题基准')
      //   }
      //   else that.getpost()
      // }
      //  else if(that.formData.field101=='smartbeta'){
      //   if(that.valuebase4.length<=0){
      //       that.$message('请选择smartbeta基准')
      //   }
      //   else that.getpost()
      // }
      //  else if(that.formData.field101=='港股'){
      //   that.getpost()
      // }
    },
    getpost () {
      let that = this;
      that.loading1 = true;
      if (that.is_request_active) return;
      that.is_request_active = true;
      axios
        .post(that.$baseUrl + '/index_filter/', {
          valuebase1: this.valuebase1,
          valuebase2: this.valuebase2,
          valuebase3: this.valuebase3,
          valuebase4: this.valuebase4,
          valuebase5: this.valuebase5
        })
        .then((res) => {
          that.is_request_active = false;
          //console.log(res.data);
          //console.log('筛选结果');
          that.loading1 = false;
          if (that.valuebase5 == true) {
            that.indexlist = [];
            that.alldata = res.data;
            that.tableData = that.alldata.slice(0, 9);
            that.pageTotal1 = that.alldata.length;
            that.pageindex1 = 1;
          } else {
            that.indexlist = res.data.index_list;
            that.alldata = res.data.index_result;
            // let alldata = [];
            // for (let index = 0; index < res.data.index_result['code'].length; index++) {
            // 	let obj = {};
            // 	for (var key in res.data.index_result) {
            // 		let value = res.data.index_result[key][index];
            // 		obj[key] = value;
            // 	}
            // 	alldata.push(obj);
            // }
            // that.alldata = alldata;
            that.tableData = that.alldata.slice(0, 9);
            that.pageTotal1 = that.alldata.length;
            that.pageindex1 = 1;
          }

          if (that.valuebase1.length > 0 || that.valuebase2.length > 0 || that.valuebase3.length > 0) {
            that.alldata2 = res.data.equity_result;
            //  //console.log(res.data.equity_result == null)
            //  //console.log(JSON.stringify(res.data.equity_result) == "{}" )
            //  //console.log(res.data.equity_result.length )
            if (JSON.stringify(res.data.equity_result) != '{}') {
              if (that.alldata2.length > 9) {
                that.tableData2 = that.alldata2.slice(0, 9);
                that.pageTotal2 = that.alldata2.length;
                that.pageindex2 = 1;
              } else {
                that.tableData2 = that.alldata2;
                that.pageTotal2 = that.alldata2.length;
                that.pageindex2 = 1;
              }
            } else {
              that.tableData2 = [];
              that.pageTotal2 = that.alldata2.length;
              that.pageindex2 = 1;
            }
          }

          this.localStorage.setItem('formDataindex', JSON.stringify(that.formData));
          this.localStorage.setItem('valuebase1', JSON.stringify(that.valuebase1));
          this.localStorage.setItem('valuebase2', JSON.stringify(that.valuebase2));
          this.localStorage.setItem('valuebase3', JSON.stringify(that.valuebase3));
          this.localStorage.setItem('valuebase4', JSON.stringify(that.valuebase4));
          this.localStorage.setItem('valuebase5', JSON.stringify(that.valuebase5));
          this.localStorage.setItem('alldataindex', JSON.stringify(that.alldata));
        })
        .catch((error) => {
          //console.log(error);
          that.$message('筛选失败，请联系我们，联系电话：18717782280');
          that.loading1 = false;
          that.tableData2 = [];
          that.tableData = [];
          that.pageTotal1 = 0;
          that.pageindex1 = 1;
        });
    },
    resetForm () {
      this.valuebase1 = [];
      this.valuebase2 = [];
      this.valuebase3 = [];
      this.valuebase4 = [];
      this.valuebase5 = false;
      this.alldata = [];
      this.alldata2 = [];
      this.pageIndex1 = 1;
      this.pageIndex2 = 1;
      this.tableData = [];
      this.tableData2 = [];
      this.pageTotal1 = 0;
      this.pageTotal2 = 0;
    },
    handleSearch () {
      //搜索基金
    },
    beidongchange (val) {
      ////console.log(val)
      if (val == '宽基') {
        this.kuanjishow = true;
        this.hangyeshow = false;
        this.fenggeshow = false;
        this.sbetashow = false;
        this.ganggushow = false;

        // this.valuebase2 =[]
        //  this.valuebase3 =[]
        //   this.valuebase4 =[]
        this.valuebase5 = false;
        this.alldata = [];
        this.alldata2 = [];
        this.pageIndex1 = 1;
        this.pageIndex2 = 1;
        this.tableData = [];
        this.tableData2 = [];
        this.pageTotal1 = 0;
        this.pageTotal2 = 0;
      } else if (val == '行业') {
        this.kuanjishow = false;
        this.hangyeshow = true;
        this.fenggeshow = false;
        this.sbetashow = false;
        this.ganggushow = false;
        this.valuebase1 = [];

        this.valuebase3 = [];
        this.valuebase4 = [];
        this.valuebase5 = false;
        this.alldata = [];
        this.alldata2 = [];
        this.pageIndex1 = 1;
        this.pageIndex2 = 1;
        this.tableData = [];
        this.tableData2 = [];
        this.pageTotal1 = 0;
        this.pageTotal2 = 0;
      } else if (val == '主题') {
        this.kuanjishow = false;
        this.hangyeshow = false;
        this.fenggeshow = true;
        this.sbetashow = false;
        this.ganggushow = false;
        this.valuebase1 = [];
        this.valuebase2 = [];

        this.valuebase4 = [];
        this.valuebase5 = false;
        this.alldata = [];
        this.alldata2 = [];
        this.pageIndex1 = 1;
        this.pageIndex2 = 1;
        this.tableData = [];
        this.tableData2 = [];
        this.pageTotal1 = 0;
        this.pageTotal2 = 0;
      } else if (val == 'smartbeta') {
        this.kuanjishow = false;
        this.hangyeshow = false;
        this.fenggeshow = false;
        this.sbetashow = true;
        this.ganggushow = false;
        this.valuebase1 = [];
        this.valuebase2 = [];
        this.valuebase3 = [];

        this.valuebase5 = false;
        this.alldata = [];
        this.alldata2 = [];
        this.pageIndex1 = 1;
        this.pageIndex2 = 1;
        this.tableData = [];
        this.tableData2 = [];
        this.pageTotal1 = 0;
        this.pageTotal2 = 0;
      } else if (val == '港股') {
        this.kuanjishow = false;
        this.hangyeshow = false;
        this.fenggeshow = false;
        this.sbetashow = false;
        this.ganggushow = true;
        this.valuebase1 = [];
        this.valuebase2 = [];
        this.valuebase3 = [];
        this.valuebase4 = [];
        this.valuebase5 = true;
        this.alldata = [];
        this.alldata2 = [];
        this.pageIndex1 = 1;
        this.pageIndex2 = 1;
        this.tableData = [];
        this.tableData2 = [];
        this.pageTotal1 = 0;
        this.pageTotal2 = 0;
        this.submitForm();
      }
    }
  }
};
</script>
<style>
.width100 {
	width: 300px;
}
/* input[readonly]{
background-color: #f1f1f5
} */
.el-form-item {
	margin-bottom: 0;
}
.tableindexheader .el-form-item__label {
	width: 200px !important;
	text-align: left;
}
.tableindexheader .el-form-item__content {
	margin-left: 200px;
}
</style>
<style lang="scss" scoped>
.managerDetailPage {
	margin-left: 2%;
	width: 96%;
	background: #d0d7df;
	padding: 20px;
}

.row {
	margin: -10px;
	display: flex;
}

.left {
	width: 155px;
	flex: 0 0 auto;
	margin-right: 10px;
}

.right {
	position: relative;
	flex: 1 1 100px;
}
</style>
<style lang="scss">
.comment-section {
	padding: 5px 15px 0 15px;
}

.comment {
	background: linear-gradient(90deg, #3b64f2, #1b8eff);
	color: white;
	font-size: 12px;
	padding: 12px 24px;
}

.comment.center {
	text-align: center;
}

.section {
	padding: 15px 15px 0 15px;
}

.double-table {
	display: flex;
	flex-basis: 10px;
	justify-content: space-between;

	.single-table {
		flex: 1;
	}

	.cell {
		font-size: 14px !important;
		font-weight: 400 !important;
		text-align: center !important;
		padding: 0 !important;
	}

	th {
		padding: 5px 0 !important;
	}
}

.split-cell {
	display: flex;
	align-items: center;
	justify-content: center;

	div {
		width: 40px;
	}
}
</style>
<style>
.tableindex .el-table__cell {
	border-right: 0px !important;
}
.tableindexheader .el-table--border {
	border: 0px !important;
}
.el-table .warning0 {
	background: #fff !important;
}
.el-table .warning1 {
	background: #f2f2f2 !important;
}
.el-table .warning2 {
	background: #eae9e9 !important;
}
.el-table .warning3 {
	background: #dddddd !important;
}
.el-table .warning4 {
	background: #c9c9c9 !important;
}
</style>
<style lang="scss" scoped>
.title {
	width: 99%;

	font-weight: 600;
	padding: 10px 15px;
}
.sub-title {
	font-size: 14px;
	font-weight: 600;
	border-left: 2px solid dodgerblue;
	margin-bottom: 6px;
	padding-left: 3px;
	line-height: 22px;
	height: 22px;
	flex: 1 1 auto;
}
.title-change-fund {
	display: flex;
	align-items: center;
	margin-bottom: 4px;
	label {
		font-size: 14px;
		margin-right: 10px;
	}
}
</style>
