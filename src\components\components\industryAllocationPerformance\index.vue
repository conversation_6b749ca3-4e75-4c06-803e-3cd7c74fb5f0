<template>
	<div v-show="show" class="chart_one">
		<div v-loading="cardLoading">
			<div
				style="
					display: flex;
					align-items: center;
					justify-content: space-between;
					position: relative;
					height: 56px;
					border-bottom: 1px solid #e9e9e9;
				"
				class="card-header"
			>
				<div class="header-title" style="display: flex; align-items: center">
					<span class="title">{{ title }}</span>
					<el-tooltip class="item" effect="dark" :content="EXPLAIN.abilityPage['行业配置表现']" placement="right-start">
						<svg width="14" height="14" viewBox="0 0 14 14" fill="none">
							<path
								fill-rule="evenodd"
								clip-rule="evenodd"
								d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
								fill="black"
								fill-opacity="0.45"
							/>
						</svg>
					</el-tooltip>
					<i class="el-icon-video-camera-solid videoIconDes" style="height: 100%; font-size: 14px" @click="openvideo"></i>
				</div>
				<div class="header-btn">
					<el-button
						v-for="(item, i) in ['行业收益图', '行业超额收益表', '行业配置比例图', '行业季度配置变化图']"
						:key="i"
						@click="changeShowCharts(item)"
						plain
						:class="showCharts == item ? 'btn_active' : 'btn_info'"
						>{{ item }}</el-button
					>
				</div>
			</div>
			<industry-records v-show="showCharts == '行业分布图'"></industry-records>
			<share-dataset v-show="showCharts == '行业配置比例图'" ref="shareDataset" :canvasClass="'canvasBig'"></share-dataset>
			<div
				v-show="showCharts == '行业收益图'"
				:id="'industryConfigBar' + (info.type || '') + (info.code || '') + (info.dateTime || '')"
				v-loading="loading"
				element-loading-spinner="el-icon-document-delete"
				element-loading-background="rgba(239, 239, 239, 0.5)"
				class="charts_one_class"
				style="height: 56px"
			></div>
			<div
				v-show="showCharts == '行业收益图'"
				:id="'industryConfigPerformance' + (info.type || '') + (info.code || '') + (info.dateTime || '')"
				v-loading="loading"
				element-loading-spinner="el-icon-document-delete"
				element-loading-background="rgba(239, 239, 239, 0.5)"
				class="charts_one_class"
			></div>
			<industry-change-chart v-show="showCharts == '行业季度配置变化图'" ref="industryChangeChart"></industry-change-chart>

			<div v-show="showCharts == '行业超额收益表'">
				<industry-excess-table ref="industryExcessTable"></industry-excess-table>
			</div>
		</div>
	</div>
</template>

<script>
import { exportTitle, exportChart, exportTable } from '@/utils/exportWord.js';
import { getAllocationDetails } from '@/api/pages/SystemOther.js';
import VChart from 'vue-echarts';
import shareDataset from './components/shareDataset';
import industryRecords from './components/industryRecords';
// import industryChangeChart from './components/industryChangeChart';
import industryExcessTable from './components/industryExcessTable';
export default {
	data() {
		return {
			title: '行业配置表现',
			visible_hypz: true,
			show: true,
			allocation: {},
			allocationBar: {},
			loadingallo: true,
			equityWeght: [],
			tempvalueselect: '采掘',
			showCharts: '行业收益图',
			industryList: [], // 行业配置表现数据
			industryNameList: [], // 行业配置表现-筛选可选项
			choosemsg: '',
			industries: [
				'非银金融',
				'银行',
				'房地产',
				'基础化工',
				'公用事业',
				'交通运输',
				'石油石化',
				'建筑材料',
				'煤炭',
				'钢铁',
				'环保',
				'建筑装饰',
				'有色金属',
				'国防军工',
				'汽车',
				'机械设备',
				'综合',
				'电力设备',
				'传媒',
				'通信',
				'计算机',
				'电子',
				'医药生物',
				'农林牧渔',
				'家用电器',
				'商贸零售',
				'社会服务',
				'轻工制造',
				'纺织服饰',
				'食品饮料',
				'美容护理'
			],
			noteData: '',
			serIndex: 0,
			loading: true,
			cardLoading: true,
			info: {},
			echartBar: null,
			requestData: null
		};
	},
	components: {
		VChart,
		shareDataset,
		industryRecords,
		// industryChangeChart,
		industryExcessTable
	},
	methods: {
		openvideo() {
			window.open('https://www.bilibili.com/video/BV1iW4y1m7R1?share_source=copy_web');
		},
		// 行业配置表现-筛选方法
		filterIndustryName(value, row) {
			return row.industry_name === value;
		},
		hideLoading() {
			this.show = false;
			this.cardLoading = false;
		},
		async getAllocationDetails() {
			let data = await getAllocationDetails({
				flag: this.info.flag,
				type: this.info.type,
				code: this.info.code,
				start_date: '',
				end_date: '',
				have: 'stock'
			});
			if (data?.mtycode == 200) {
				this.equityWeght = [];
				let yearqtr = data?.data.filter((item) => {
					return item.name == 'yearqtr';
				})?.[0].value;
				let equity_weight = data?.data.filter((item) => {
					return item.name == 'equity_weight';
				})?.[0].value;
				yearqtr.map((item, index) => {
					this.equityWeght.push({
						name: item,
						value: equity_weight[index]
					});
				});
				this.equityWeght.sort((a, b) => {
					return this.moment(this.moment(a.name, 'YYYY QQ').format()).isBefore(this.moment(b.name, 'YYYY QQ').format()) ? -1 : 1;
				});
			}
		},
		/* 写一个函数,计算两个对象数组重合的部分 */
		getSame(arr1, arr2) {
			var arr = [];
			for (var j = 0; j < arr2.length; j++) {
				for (var i = 0; i < arr1.length; i++) {
					if (arr1[i] == arr2[j].name) {
						arr.push(arr2[j]);
					}
				}
			}
			return arr;
		},
		async getData(data, info) {
			let dateTime = new Date().getTime();
			info.dateTime = dateTime;
			this.info = info;
			// await this.getAllocationDetails();
			this.allocationBar = {};
			this.$nextTick(() => {
				this.cardLoading = false;
				let maxtemp = 27;
				let scale = [];
				let date = [];
				// let list = [];
				let that = this;
				// this.industries.map((item) => list.push({ text: item, value: item }));
				// this.industryNameList = list;
				let requestData = data;
				requestData = requestData.sort((a, b) => {
					return this.moment(a.yearqtr, 'YYYY Q')._d.getTime() - this.moment(b.yearqtr, 'YYYY Q')._d.getTime();
				});
				// 港股
				// if (this.info.type == 'equityhk' || this.info.type == 'hkequity' || this.info.type == 'equitywithhk') {
				this.industries = [];
				for (let i = 0; i < requestData.length; i++) {
					if (this.industries.indexOf(requestData[i].industry_name) < 0) {
						this.industries.push(requestData[i].industry_name);
					}
				}
				maxtemp = this.industries.length - 1;
				let list = [];
				this.industries.map((item) => list.push({ text: item, value: item }));
				this.industryNameList = list;
				this.tempvalueselect = this.industries[0];
				// }

				let maxp = 0;
				for (let c = 0; c < requestData.length; c++) {
					if (maxp < requestData[c].weight) {
						maxp = requestData[c].weight;
					}
				}
				// 处理表格数据-tableDataArr; tableDataObj
				let tableDataArr = [],
					tableDataObj = {};
				requestData.forEach((item) => {
					if (!tableDataObj[item.industry_name]) {
						tableDataObj[item.industry_name] = [];
					}
					tableDataObj[item.industry_name].push(item);
				});

				for (let key in tableDataObj) {
					tableDataArr.push(...tableDataObj[key]);
				}
				this.industryList = tableDataArr.map((item) => {
					return {
						...item,
						industry_index_return: item.bogey_return ? item.bogey_return * 1 : item.industry_return * 1 - item.excess_return * 1,
						industry_excess_return: item.excess_return ? item.excess_return * 1 : item.industry_return * 1 - item.bogey_return * 1
					};
				});
				// this.$refs['industryExcessTable']?.getData({
				// 	industryNameList: this.industryNameList,
				// 	industryList: this.industryList
				// });

				// 处理图表数据-data
				let tableData = [];
				let whereend = null;
				let industries = this.industries.slice();
				for (let i = 0; i < requestData.length; i++) {
					let temp = industries.indexOf(requestData[i].industry_name);
					tableData.push([
						requestData[i].yearqtr,
						temp,
						requestData[i].industry_return,
						requestData[i].weight,
						requestData[i].industry_return - 0 > 0 ? 'True' : 'False'
					]);
					if (date.indexOf(requestData[i].yearqtr) == -1) {
						date.push(requestData[i].yearqtr);
					}
				}
				date = date.sort((a, b) => {
					return this.moment(a, 'YYYY Q')._d.getTime() - this.moment(b, 'YYYY Q')._d.getTime();
				});
				this.equityWeght = this.getSame(date, this.equityWeght);
				scale = this.formatterData(requestData);

				this.requestData = requestData;
				// this.$refs['shareDataset'].getCharts(requestData);
				// this.$refs['industryChangeChart'].getCharts(requestData, this.pageType, this.info);
				let options = {
					xAxis: {
						margin: 12,
						nameTextStyle: {
							fontSize: '14px',
							color: 'rgba(0,0,0,0.65)'
						},
						axisLabel: {
							show: true,
							textStyle: {
								fontSize: '14px',
								color: 'rgba(0,0,0,0.65)'
							}
						},
						axisLine: {
							lineStyle: {
								color: '#e9e9e9'
							}
						},
						type: 'category',
						boundaryGap: false,
						splitLine: {
							show: false,
							lineStyle: {
								color: '#e9e9e9',
								type: 'dashed',
								width: 1
							}
						},
						axisTick: {
							show: false
						},
						data: date,
						interval: 0.25,
						length: date.length
					},
					dataZoom: [
						{
							// 这个dataZoom组件，默认控制x轴。
							type: 'slider', // 这个 dataZoom 组件是 slider 型 dataZoom 组件
							start: whereend, // 左边在 10% 的位置。
							end: 100, // 右边在 60% 的位置。
							show: true,
							filterMode: 'empty'
						}
					],
					yAxis: {
						margin: 16,
						nameTextStyle: {
							fontSize: '14px',
							color: 'rgba(0,0,0,0.65)'
						},
						axisTick: {
							show: false
						},
						type: 'value',
						axisLine: {
							show: false,
							color: '#e9e9e9'
						},
						splitLine: {
							show: true,
							lineStyle: {
								color: '#e9e9e9',
								type: 'dashed',
								width: 1
							}
						},
						splitArea: {
							show: false,
							areaStyle: {
								color: '#f5f7fa'
							}
						},
						min: 0,
						max: maxtemp,
						interval: 1,
						axisLabel: {
							show: true,
							textStyle: {
								fontSize: '14px',
								color: 'rgba(0,0,0,0.65)'
							},
							formatter: function (value, index) {
								if (value < that.industries.length) return that.industries[value].toString();
							}
						}
					},
					tooltip: {
						textStyle: {
							fontSize: '14px'
						},
						trigger: 'item',
						axisPointer: {
							type: 'cross'
						},
						formatter(params) {
							if (
								that.serIndex !==
								date
									.map((item) => {
										item = JSON.stringify(item);
										return item.slice(0, 4) + ' Q' + item.slice(-1);
									})
									.indexOf(params.name)
							) {
								that.serIndex = date
									.map((item) => {
										item = JSON.stringify(item);
										return item.slice(0, 4) + ' Q' + item.slice(-1);
									})
									.indexOf(params.name);
								that.barScattar(echartBar);
							}

							return (
								'日期：' +
								params.data[0] +
								'季度，行业：' +
								industries[params.data[1]] +
								'，权重：' +
								params.data[3].toFixed(2) +
								'%，行业估算收益率：' +
								parseInt(params.data[2] * 100).toFixed(2) +
								'%'
							);
						}
					},
					grid: {
						left: this.info.type == 'equityhk' ? '72px' : '64px',
						right: '32px',
						bottom: '64px',
						top: '18px',
						// containLabel: true,
						backgroundColor: '#fafafa'
					},
					visualMap: [
						{
							show: false,
							type: 'piecewise', // 定义为分段型 visualMap
							right: '0',
							bottom: '20px',
							dimension: 4,
							splitNumber: 2,
							precision: 1,
							itemWidth: 10,
							itemHeight: 10,
							textGap: 5,
							textStyle: {
								color: 'black'
							},
							// categories 定义了 visualMap-piecewise 组件显示出来的项。
							categories: ['True', 'False'],
							// 表示 目标系列 的视觉样式 和 visualMap-piecewise 共有的视觉样式。
							inRange: {
								symbol: ['triangle', 'circle']
							},
							// 表示 visualMap-piecewise 本身的视觉样式。
							controller: {
								inRange: {
									color: ['black', 'black'],
									symbol: ['triangle', 'circle']
								}
							}
						},
						{
							show: false,
							right: '0px',
							top: '150px',
							dimension: 3,
							min: 0,
							max: maxp,
							itemWidth: 30,
							itemHeight: 120,
							precision: 0,
							text: ['配置权重%'],
							textGap: 5,
							textStyle: {
								color: 'black',
								fontSize: 10
							},
							inRange: {
								symbolSize: [6, 24]
							},
							controller: {
								inRange: {
									color: ['black']
								}
							}
						},
						{
							show: false,
							right: '5px',
							top: '5%',
							dimension: 2,
							min: -1.0,
							max: 1.0,
							itemHeight: 100,
							precision: 1,
							text: ['1', '-1.0'],
							textGap: 0,
							textStyle: {
								color: 'black',
								fontSize: 10
							},
							inRange: {
								color: ['green', '#A3A3A3', 'red']
								// color: [
								// 	'rgba(35, 195, 67, 1)',
								// 	'rgba(35, 195, 67, 0.8)',
								// 	'rgba(35, 195, 67, 0.6)',
								// 	'rgba(35, 195, 67, 0.4)',
								// 	'rgba(247, 101, 96, 1)',
								// 	'rgba(247, 101, 96, 0.8)',
								// 	'rgba(247, 101, 96, 0.6)',
								// 	'rgba(247, 101, 96, 0.4)'
								// ]
							},
							outOfRange: {
								color: ['rgba(255,255,255,.2)']
							},
							controller: {
								inRange: {
									// color: ['#23C343', '#23C343CC', '#23C34399', '#23C34366', '#F7656066', '#F7656099', '#F76560CC', '#F76560']
									color: ['green', '#A3A3A3', 'red']
								}
							}
						}
					],
					series: [
						{
							name: '大类资产配置',
							type: 'scatter',
							data: tableData,
							symbolSize: 5
						}
					]
				};
				this.allocation = options;
				this.allocationBar = {
					xAxis: {
						show: false,
						margin: 12,
						nameTextStyle: {
							fontSize: '14px'
						},
						axisLabel: {
							show: false,
							textStyle: {
								fontSize: '14px'
							}
						},
						type: 'category',
						boundaryGap: false,
						splitLine: {
							show: false,
							lineStyle: {
								color: 'white',
								type: 'solid',
								width: 1
							}
						},
						data: this.equityWeght.map((item) => {
							return item.name;
						}),
						interval: 0.25,
						length: this.equityWeght.length
					},
					yAxis: {
						show: false,
						margin: 16,
						nameTextStyle: {
							fontSize: '14px'
						},
						type: 'value',
						axisLine: {
							show: false
						},
						splitLine: {
							show: true,
							lineStyle: {
								color: 'white',
								type: 'solid',
								width: 1
							}
						},
						splitArea: {
							show: true,
							areaStyle: {
								color: '#f5f7fa'
							}
						},
						axisLabel: {
							show: true,
							textStyle: {
								fontSize: '14px'
							},
							formatter: function (value, index) {
								return '非银金融';
							}
						}
					},
					dataZoom: [
						{
							// 这个dataZoom组件，默认控制x轴。
							type: 'slider', // 这个 dataZoom 组件是 slider 型 dataZoom 组件
							start: whereend, // 左边在 10% 的位置。
							end: 100, // 右边在 60% 的位置。
							show: false
						}
					],
					tooltip: {
						trigger: 'axis',
						textStyle: {
							fontSize: '14px'
						},
						axisPointer: {
							type: 'shadow'
						},
						formatter: function (params) {
							return `季度：${params[0].name} <br/> 总权重：${params[0].value}%`;
						}
					},
					grid: {
						left: this.info.type == 'equityhk' ? '72px' : '64px',
						right: '32px',
						bottom: '-100px',
						top: '20px',
						height: 50,
						// containLabel: true,
						backgroundColor: '#fafafa'
					},
					series: [
						{
							name: '配置总额',
							data: this.equityWeght.map((item) => {
								return Number(item.value).toFixed(2);
							}),
							type: 'bar',
							itemStyle: {
								color: 'rgba(0, 0, 0, 0.04)'
							},
							barWidth: '10px',
							emphasis: {
								itemStyle: {
									color: '#4096ff'
								},
								label: {
									color: 'rgba(0, 0, 0, 0.65)',
									textStyle: {
										fontSize: '14px'
									}
								}
							},
							label: {
								show: true,
								position: 'top'
							}
						}
					]
				};
				let echartScatter = echarts.init(document.getElementById('industryConfigPerformance' + info.type + info.code + info.dateTime));
				let echartBar = echarts.init(document.getElementById('industryConfigBar' + info.type + info.code + info.dateTime));
				this.echartBar = echartScatter;
				echartScatter.setOption(this.allocation);
				echartBar.setOption(this.allocationBar);
				echartScatter.resize();
				echartBar.resize();
				window.addEventListener('resize', function () {
					echartScatter.resize();
					echartBar.resize();
				});
				this.loading = false;
				echartScatter.on('datazoom', function (params) {
					this.allocationBar = {
						xAxis: {
							margin: 12,
							nameTextStyle: {
								fontSize: '14px'
							},
							axisLabel: {
								show: true,
								textStyle: {
									fontSize: '14px'
								}
							},
							type: 'category',
							boundaryGap: false,
							splitLine: {
								show: true,
								lineStyle: {
									color: 'white',
									type: 'solid',
									width: 1
								}
							},
							data: date,
							interval: 0.25,
							length: date.length
						},
						yAxis: {
							margin: 16,
							show: false,
							nameTextStyle: {
								fontSize: '14px'
							},

							type: 'value',
							axisLine: {
								show: false
							},
							splitLine: {
								show: true,
								lineStyle: {
									color: 'white',
									type: 'solid',
									width: 1
								}
							},
							splitArea: {
								show: true,
								areaStyle: {
									color: '#4096ff'
								}
							},
							min: 0,
							max: maxtemp,
							interval: 1,
							axisLabel: {
								show: true,
								textStyle: {
									fontSize: '14px'
								},
								formatter: function (value, index) {
									return '非银金融';
								}
							}
						},
						dataZoom: [
							{
								// 这个dataZoom组件，默认控制x轴。
								type: 'slider', // 这个 dataZoom 组件是 slider 型 dataZoom 组件
								start: params.start, // 左边在 10% 的位置。
								end: params.end, // 右边在 60% 的位置。
								show: false
							}
						],
						tooltip: {
							trigger: 'axis',
							axisPointer: {
								type: 'shadow'
							},
							formatter: function (params) {
								return `季度：${params[0].name} <br/> 总权重：${params[0].value}%`;
							}
						},
						grid: {
							left: '0',
							right: '10px',
							bottom: '-50px',
							top: '20px',
							containLabel: true,
							backgroundColor: '#fafafa'
						},
						series: [
							{
								name: '配置总额',
								data: scale.map((item) => {
									return {
										value: (Number(item) * 100).toFixed(2),
										symbolSize: Number(item).toFixed(2)
									};
								}),
								type: 'bar',
								itemStyle: {
									color: 'rgba(0, 0, 0, 0.04)'
								},
								emphasis: {
									itemStyle: {
										color: '#4096ff'
									},
									label: {
										color: 'rgba(0, 0, 0, 0.65)'
									}
								},
								label: {
									show: true,
									position: 'top'
								}
							}
						]
					};
				});
				echarts.connect([echartBar, echartScatter]);
			});
		},
		formatterScatter() {},
		changeShowCharts(item) {
			this.showCharts = item;
			if (item == '行业配置比例图') {
				this.$refs['shareDataset'].getCharts(this.requestData);
			} else if (item == '行业超额收益表') {
				this.$refs['industryExcessTable']?.getData({
					industryNameList: this.industryNameList,
					industryList: this.industryList
				});
			} else if (item == '行业季度配置变化图') {
				this.$refs['industryChangeChart'].getCharts(this.requestData, this.pageType, this.info);
			}
		},
		// 联动
		barScattar(bar) {
			bar.dispatchAction({
				type: 'hideTip'
			});
			bar.dispatchAction({
				type: 'highlight',
				seriesIndex: 10,
				dataIndex: 10
			});
			this.serIndex++;
		},
		// 格式化规模柱状图数据
		formatterData(data) {
			// 深拷贝数组
			let arr = [];
			data.map((item) => {
				arr.push(item);
			});
			let yearData = [];
			arr.map((item) => {
				let index = yearData.findIndex((obj) => {
					return obj.yearqtr == item.yearqtr;
				});
				if (index == -1) {
					yearData.push({ yearqtr: item.yearqtr, weight: item.weight });
				} else {
					yearData[index].weight = yearData[index].weight * 1 + item.weight * 1;
				}
			});
			return yearData
				.sort((a, b) => {
					return this.moment(a.yearqtr, 'YYYY Q')._d.getTime() - this.moment(b.yearqtr, 'YYYY Q')._d.getTime();
				})
				.map((item) => {
					return item.weight;
				});
		},
		createPrintWord() {
			if (this.show) {
				let height = document.getElementById('industryConfigPerformance' + this.info.type + this.info.code)?.clientHeight || 400;
				let width = document.getElementById('industryConfigPerformance' + this.info.type + this.info.code)?.clientWidth || 1200;
				let chart = this.echartBar.getDataURL({
					type: 'png',
					pixelRatio: 2,
					backgroundColor: '#fff'
				});
				let column = [
					{
						label: '时间',
						value: 'yearqtr'
					},
					{
						label: '行业',
						value: 'industry_name'
					},
					{
						label: '行业估算收益率',
						value: 'industry_return',
						fill: 'red_or_green',
						format: 'fix2p'
					},
					{
						label: '行业基准收益率',
						value: 'industry_index_return',
						fill: 'red_or_green',
						format: 'fix2p'
					},
					{
						label: '行业超额收益率',
						value: 'industry_excess_return',
						fill: 'red_or_green',
						format: 'fix2p'
					},
					{
						label: '权重',
						value: 'weight',
						format: 'fix2b'
					}
				];
				let table = this.industryList;
				return [
					...exportTitle('行业收益图'),
					...exportChart(chart, { width, height })
					// ...exportTitle('行业超额收益表'),
					// ...exportTable(column, table)
				];
			} else {
				return [];
			}
		}
	}
};
</script>

<style scoped lang="scss">
.card-header {
	display: flex;
	align-items: center;
}
.heightx600 {
	height: 600px !important;
}
.heightx100 {
	height: 100px !important;
}
.heightx50 {
	height: 50px !important;
}
.header-btn .btn_info {
	color: rgba(0, 0, 0, 0.65);
	border-color: #d9d9d9;
	background-color: #ffffff;
}
.header-btn .btn_info:hover {
	color: #4096ff;
	border-color: #4096ff;
	background-color: #ffffff;
}
.header-btn .btn_active:hover {
	color: #4096ff;
	border-color: #4096ff;
	background-color: #ffffff;
}
.header-btn .btn_active {
	color: #4096ff;
	border-color: #4096ff;
	background-color: #ffffff;
}
.charts_one_class {
	height: 680px;
}
</style>
