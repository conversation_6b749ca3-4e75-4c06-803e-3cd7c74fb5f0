<template>
	<div class="chart_one">
		<div class="title">新发基金</div>
		<div style="margin-top: 24px" v-loading="newFundLoading">
			<el-table :data="newFundDataSort" max-height="400px" @sort-change="sortNewFund">
				<el-table-column
					v-for="item in column"
					:key="item.value"
					align="gotoleft"
					:prop="item.value"
					:label="item.label"
					:width="item.width"
					:sortable="item.sortable ? item.sortable : false"
				>
					<template slot-scope="scope">
						<span>
							{{ item.format ? item.format(scope.row[item.value]) : scope.row[item.value] }}
						</span>
					</template>
				</el-table-column>
				<template slot="empty">
					<el-empty image-size="160"></el-empty>
				</template>
			</el-table>
		</div>
	</div>
</template>

<script>
// 新发基金

export default {
	name: 'newDevelopmentFund',
	data() {
		return {
			newFundLoading: true,
			newFundData: [],
			newFundDataSort: [],
			column: [
				{
					label: '发行时间',
					value: 'founddate',
					sortable: true
				},
				{
					label: '基金代码',
					value: 'code'
					// width: '150px'
				},
				{
					label: '基金名称',
					value: 'name'
					// width: '350px'
				},
				{
					label: '基金类型',
					value: 'type'
					// width: '150px'
				},
				{
					label: '募集规模(亿元)',
					value: 'netasset',
					format: this.fix8,
					sortable: true
					// width: '150px'
				}
				// {
				// 	label: '投资方向',
				// 	value: 'principle'
				// },
				// {
				// 	label: '基金经理',
				// 	value: 'manager_name',
				// 	width: '120px'
				// },
				// {
				// 	label: '认购结束期',
				// 	value: 'reddmsdate',
				// 	width: '120px'
				// },
				// {
				// 	label: '成立日期',
				// 	value: 'date',
				// 	width: '120px'
				// }
			]
		};
	},
	methods: {
		getData(data) {
			this.newFundLoading = false;
			this.newFundData = data.slice().map((item) => {
				return {
					...item,
					type: this.FUNC.textConverter(this.COMMON.fundType_zh_en, item.type, 'en', 'zh') || '--',
					isstotalunit:
						parseFloat(item.isstotalunit / 10 ** 8).toFixed(2) == 'NaN' ? '--' : parseFloat(item.isstotalunit / 10 ** 8).toFixed(2)
				};
			});
			this.newFundDataSort = this.newFundData.map((item) => {
				return { ...item };
			});
		},
		getNewFundList() {
			this.newFundLoading = true;
			axios
				.get(this.$baseUrl + '/Company/NewFund/?code=' + this.code)
				.then((res) => {
					if (res.status == 200 && res.data.mtycode == 200) {
						this.newFundData = res.data.data.slice();
						this.newFundDataSort = res.data.data.slice();
					} else {
						console.error('error: ', res);
					}
					this.newFundLoading = false;
				})
				.catch((err) => {
					this.newFundLoading = false;
					console.error('error: ', err);
				});
		},
		sortNewFund(sortVal) {
			this.newFundDataSort =
				sortVal.order && sortVal.prop ? this.sortChange(sortVal, this.newFundDataSort.slice()) : this.newFundData.slice();
		},
		// 亿元化
		fix8(val) {
			return val * 1 ? (val / 10 ** 8).toFixed(2) : '--';
		},
		// 跳转
		godetail(code, name) {
			//带参进去
			alphaGo(code, name, this.$route.path);
		}
	}
};
</script>

<style></style>
