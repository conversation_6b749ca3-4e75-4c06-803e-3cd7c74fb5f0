<template>
	<div :id="'industryEvaluation' + (showDescription ? '' : 'Description')">
		<div style="page-break-inside: avoid; position: relative">
			<analysis-card-title title="行业高低配" :image_id="'industryEvaluation' + (showDescription ? '' : 'Description')">
				<QuickTimePicker v-model="preset_time" @change="changeTimePicker"></QuickTimePicker>
			</analysis-card-title>
			<div v-loading="industryrequestflag" class="charts_fill_class">
				<v-chart
					v-show="!industryrequestflag"
					class="charts_one_class"
					:ref="'industryEvaluation' + (showDescription ? '' : 'Description')"
					autoresize
					v-loading="industryrequestflag"
					:options="industryoption"
					@finished="finished = true"
				/>
				<el-empty v-show="industryrequestflag" description="暂无数据"></el-empty>
			</div>
			<div class="mt-20" v-if="showDescription">
				<analysis-description :is_column="true" :description="description"></analysis-description>
			</div>
		</div>
	</div>
</template>

<script>
import QuickTimePicker from '@/pages/tkdesign/marketAnalysis/component/QuickTimePicker.vue';
// 模型使用说明
import analysisDescription from '@/components/components/components/analysisDescription/index.vue';

// 行业评价
import { barChartOption } from '@/utils/chartStyle.js';

// 行业评价
import { getIndustryDetlaCapability } from '@/api/pages/Analysis.js';
export default {
	name: 'industryEvaluation',
	components: {
		QuickTimePicker,
		analysisDescription
	},
	props: {
		showDescription: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			industryrequestflag: true,
			industryoption: {},
			info: {},
			preset_time: {
				radioValue: '1'
			},
			finished: false
		};
	},
	computed: {
		description() {
			return `根据各个行业指数的历史表现进行横截面排名，得到每个行业在某个季度的表现是 top （67%以上分位），bottom（33%以下分位数）或者是 other（其他分位数）。根据这样的时 间段标签，计算基金经理/基金在不同时段配置某行业的平均权重及在行业上获得 alpha 的排 名。`;
		}
	},
	methods: {
		openvideo() {
			window.open('https://www.bilibili.com/video/BV1Cd4y1D7wx?share_source=copy_web');
		},
		// 监听时间选择框改变
		changeTimePicker(val) {
			if (val?.radioValue) {
				if (val.radioValue == 'custom') {
					this.info.start_date = val.startDate;
					this.info.end_date = val.endDate;
				} else {
					this.info.start_date = this.moment().subtract(val.radioValue, 'years').format('YYYY-MM-DD');
					this.info.end_date = this.moment().format('YYYY-MM-DD');
				}
			}
			this.getIndustryDetlaCapability();
		},
		// 获取行业评价数据
		async getIndustryDetlaCapability() {
			this.industryrequestflag = true;
			let data = await getIndustryDetlaCapability({
				code: this.info.code,
				flag: this.info.flag,
				type: this.info.type,
				industry_section: '申万(2021)',
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			this.industryrequestflag = false;
			if (data?.mtycode == 200) {
				return data?.data;
			} else {
				this.hideLoading();
				return [];
			}
		},
		// 获取父组件传递数据
		async getData(info) {
			this.info = {
				...info,
				start_date: this.moment().subtract(this.preset_time.radioValue, 'years').format('YYYY-MM-DD'),
				end_date: this.moment().format('YYYY-MM-DD')
			};
			let data = await this.getIndustryDetlaCapability();
			let min_size = data.sort((a, b) => a.mean_weight * 1 - b.mean_weight * 1)[0].mean_weight;
			let max_size = data.sort((a, b) => b.mean_weight * 1 - a.mean_weight * 1)[0].mean_weight;
			let res = this.formatData(data);

			let series = [];
			res.map((item, index) => {
				let industry_top = [index, '行业头部时段', item.weight_top ? (item.weight_top == '--' ? '--' : item.weight_top.toFixed(2)) : '--'];
				let industry_middle = [
					index,
					'行业腰部时段',
					item.weight_middle ? (item.weight_middle == '--' ? '--' : item.weight_middle.toFixed(2)) : '--'
				];
				let industry_bottom = [
					index,
					'行业尾部时段',
					item.weight_bottom ? (item.weight_bottom == '--' ? '--' : item.weight_bottom.toFixed(2)) : '--'
				];
				series.push(industry_top, industry_middle, industry_bottom);
			});
			this.industryrequestflag = false;

			this.industryoption = barChartOption({
				toolbox: 'none',
				color: ['#4096ff', '#4096ff', '#7388A9', '#E8684A'],
				// legend: {
				// 	bottom: '0',
				// 	data: [
				// 		{ name: '头部时段权重', icon: 'bar' },
				// 		{ name: '尾部时段权重', icon: 'bar' },
				// 		{ name: '腰部时段权重', icon: 'bar' },
				// 		{ name: 'alpha排名', icon: 'line' }
				// 	]
				// },
				grid: {
					top: '12px',
					bottom: '38px'
				},
				tooltip: {
					// 坐标轴指示器，坐标轴触发有效
					type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
					formatter: function (obj) {
						var value = `<div style="font-size:14px;">` + obj?.[0].axisValue + `</div>`;
						for (let i = 0; i < obj.length; i++) {
							value +=
								`<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
								`<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
								obj?.[i].color +
								`;"></div>` +
								`<div style="font-family: PingFang SC;">` +
								obj?.[i].value?.[1] +
								'平均配置权重</div></div>' +
								`<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
								(Number(obj?.[i].value?.[2]) * 1).toFixed(2) +
								'%</div>' +
								`</div>`;
						}
						return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
					}
				},
				yAxis: [
					{
						type: 'category',
						data: ['行业头部时段', '行业腰部时段', '行业尾部时段'],
						isAlign: true
						// rotate: -45
					}
				],
				xAxis: [
					{
						data: res.map((item) => {
							return item.industry_name;
						}),
						isAlign: true,
						rotate: -45
					}
					// {
					// 	type: 'value',
					// 		return value + '%';
					// 	formatter(value) {
					// 	}
					// }
					// {
					// 	type: 'value',
					// 	splitLine: false
					// }
				],
				series: [
					{
						name: '行业择时能力',
						type: 'scatter',
						symbolSize: function (val) {
							let size = min_size + ((val[2] * 1) / max_size) * (max_size - min_size);
							return size;
						},
						data: series.filter((v) => v[2] != '--'),
						animationDelay: function (idx) {
							return idx * 5;
						}
					}
					// {
					// 	name: '头部时段权重',
					// 	type: 'bar',
					// 	barGap: 0,
					// 	// barWidth: 15,
					// 	data: res.map((item) => {
					// 		return item.weight_top ? (item.weight_top == '--' ? '--' : item.weight_top.toFixed(2)) : '--';
					// 	}),
					// 	itemStyle: {
					// 		color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
					// 			{ offset: 0, color: '#4096ff' }, // 渐变起始颜色
					// 			{ offset: 1, color: '#85AEFF' } // 渐变结束颜色
					// 		])
					// 	}
					// },
					// {
					// 	name: '腰部时段权重',
					// 	type: 'bar',
					// 	barGap: 0,
					// 	data: res.map((item) => {
					// 		return item.weight_middle ? (item.weight_middle == '--' ? '--' : item.weight_middle.toFixed(2)) : '--';
					// 	}),
					// 	itemStyle: {
					// 		color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
					// 			{ offset: 0, color: '#FFAB3E' }, // 渐变起始颜色
					// 			{ offset: 1, color: '#FFC67D' } // 渐变结束颜色
					// 		])
					// 	}
					// },
					// {
					// 	name: '尾部时段权重',
					// 	type: 'bar',
					// 	barGap: 0,
					// 	data: res.map((item) => {
					// 		return item.weight_bottom ? (item.weight_bottom == '--' ? '--' : item.weight_bottom.toFixed(2)) : '--';
					// 	}),
					// 	itemStyle: {
					// 		color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
					// 			{ offset: 0, color: '#7388A9' }, // 渐变起始颜色
					// 			{ offset: 1, color: '#99AECE' } // 渐变结束颜色
					// 		])
					// 	}
					// },

					// {
					// 	name: 'alpha排名',
					// 	type: 'line',
					// 	smooth: true,
					// 	symbol: 'none',
					// 	yAxisIndex: 1,
					// 	data: res.map((item) => {
					// 		return item.industry_rank ? (item.industry_rank == '--' ? '--' : item?.industry_rank?.toFixed(2)) : '--';
					// 	})
					// }
				]
			});
		},
		// 格式化接收数据
		formatData(data) {
			let errData = [];
			let infoData = [];
			let new_data = data.map((item) => {
				let obj = { ...item };
				obj['weight_' + item.flag] = item.mean_weight;
				return obj;
			});
			new_data.map((item) => {
				if (item.industry_rank == '--') {
					errData.push(item);
				} else {
					infoData.push(item);
				}
			});
			infoData = infoData.sort((a, b) => {
				return b.industry_rank - a.industry_rank;
			});
			let allData = [...infoData].filter((item) => {
				return item.industry_name !== '--';
			});
			let return_data = [];
			allData.map((item) => {
				let index = return_data.findIndex((v) => v.industry_code == item.industry_code);
				if (index == -1) {
					return_data.push({ ...item });
				} else {
					return_data[index] = { ...return_data[index], ...item };
				}
			});
			if (allData.length == 0) {
				return [...infoData];
			} else {
				return return_data;
			}
		},
		// 数据获取失败
		hideLoading() {
			this.industryrequestflag = false;
		},
		async createPrintWord(info) {
			await this.getData(info);
			let print_word = [];
			let key = 'industryEvaluation' + (this.showDescription ? '' : 'Description');
			return await new Promise((resolve, reject) => {
				setTimeout(() => {
					this.$nextTick(async () => {
						let height = document.getElementById(key).clientHeight;
						let width = document.getElementById(key).clientWidth;
						let canvas = await this.html2canvas(document.getElementById(key), {
							scale: 3
						});
						print_word = [
							...this.$exportWord.exportTitle('行业高低配'),
							...this.$exportWord.exportChart(canvas.toDataURL('image/jpg'), {
								width,
								height
							})
						];
						resolve(print_word);
					});
				}, 1000);
			});
		}
	}
};
</script>

<style></style>
