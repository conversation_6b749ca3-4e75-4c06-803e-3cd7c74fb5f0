<template>
	<div
		class="analysis_main"
		v-loading="!isactive"
		element-loading-background="rgba(239, 239, 239, 0.5)"
		element-loading-spinner="el-icon-s-check"
	>

		<div class="fund-content-container">
			<!-- 侧边栏菜单 -->
			<div class="sidebar-menu" style="overflow-x: hidden;" :class="{ 'sidebar-collapsed': isCollapsed }" @mouseenter="expandSidebar" @mouseleave="collapseSidebar">
				<el-menu
					:default-active="activeIndex"
					class="el-menu-demo type_menu"
					mode="vertical"
					style="margin:0px !important;overflow-x: hidden;"
					@select="handleSelect"
					:collapse="isCollapsed"
				>
					<el-menu-item v-for="item in activeComponentsList" :key="item.key" :index="item.key">
						<i :class="getIconForMenuItem(item.key)" class="menu-icon"></i>
						<span slot="title">{{ item.label }}</span>
					</el-menu-item>
				</el-menu>
				<div class="print-btn-container">
					<el-tooltip :content="isCollapsed ? '打印word' : ''" placement="right" :disabled="!isCollapsed">
						<el-button type="primary" id="printWord" class="print-btn" @click="print" :icon="isCollapsed ? 'el-icon-printer' : ''">
							<span v-if="!isCollapsed">打印word</span>
						</el-button>
					</el-tooltip>
				</div>
			</div>

			<!-- 主内容区域 -->
			<div class="main-content">

		<!-- 产品头部信息 -->
		<header-info ref="header" @managerMsg="managerMsg"></header-info>
				<div v-for="item in activeComponentsList" :key="item.key" v-show="activeIndex == item.key || printActive == true">
					<component :is="item.key" :ref="item.key"></component>
				</div>
			</div>
		</div>
		<!-- 自定义模板 -->
		<drag-list-components
			ref="dragListComponents"
			:templateId="user_id"
			@initTemplate="initTemplate"
			@getUserConfigInfo="getUserConfigInfo"
			@getSortModelList="getSortModelList"
			@closeDraw="closeDraw"
			@deleteComponents="deleteComponents"
		></drag-list-components>
	</div>
</template>

<script>
// 基金业头部信息
import headerInfo from './components/header.vue';
// 一页通
import onePagePass from './components/onePagePass.vue';
// 业绩表现
import performance from './components/performance.vue';
// 风格分析
import styleAnalysis from './components/styleAnalysis.vue';
// 能力分析
import abilityAnalysis from './components/abilityAnalysis.vue';
// 权益分析
import equityAnalysis from './components/equityAnalysis.vue';
// 归因分析
import attributionAnalysis from './components/attributionAnalysis.vue';

// 债券分析
import bondAnalysis from './components/bondAnalysis.vue';
// 转债分析
import cbondAnalysis from './components/cbondAnalysis.vue';
// FOF分析
import fofAnalysis from './components/fofAnalysis.vue';
// 货币分析-怎么挣钱
import moneyAnalysis from './components/moneyAnalysis.vue';

// 组件json配置
import { componentsList } from '../../utils/componentsClass.js';

export default {
	components: {
		headerInfo,
		onePagePass,
		performance,
		styleAnalysis,
		abilityAnalysis,
		equityAnalysis,
		attributionAnalysis,
		bondAnalysis,
		cbondAnalysis,
		fofAnalysis,
		moneyAnalysis
	},
	data() {
		return {
			activeIndex: 'onePagePass',
			code: '',
			name: '',
			type: '',
			ability: {},
			info: {},
			componentsList: [],
			activeComponentsList: [],
			templateList: [],
			printActive: false,
			loading: null,
			overComponents: [],
			type2: '',
			type3: '',
			isactive: true,
			manager_info: null,
			isbookvalue: false,
			printTimeOut: null,
			modelList: null,
			model_id: null,
			allComponents: [],
			post_id: null,
			user_id: null,
			isSubscription: false,
			defaultModelId: null,
			isCollapsed: true, // 默认折叠侧边栏
		};
	},
	mounted() {
		console.warn('mounted');
		this.init();
	},
	// watch: {
	// 	//监听相同路由下参数变化的时候，从而解决当跳转到同页面不刷新问题
	// 	$route(to, from) {
	// 		this.init();
	// 	}
	// },
	beforeRouteEnter(to, from, next) {
		console.warn(to, from);
		next((vm) => {
			if (from.path == '/addSubscription') {
				vm.isSubscription = true;
			} else {
				vm.isSubscription = false;
			}
		});
	},
	methods: {
		// 监听事件总线点击跳转详情操作
		onChangeBusEvent() {
			this.$event.$on('position-analysis', (name) => {
				let result = this.COMMON.jump_template_id.find((v) => v.name == name);
				if (result?.menu != this.activeIndex && result?.menu) {
					this.activeIndex = result?.menu;
					this.getData();
				}
				this.$nextTick(() => {
					const element = document.getElementById(result?.id);
					if (element) {
						element.scrollIntoView({ behavior: 'smooth' });
					}
				});
			});
			this.$event.$on('changeManagerAnalysisType', (type) => {
				this.info.type = type;
				this.getUserConfig();
			});
		},
		async init() {
			this.onChangeBusEvent();
			let is_manager = this.$route.path?.includes('fundmanagerdetail');
			this.componentsList = [];
			/**
			 * flag
			 * 1:基金
			 * 2:基金经理
			 * 3:基金公司
			 */
			let type_list = [];
			let flag = 1;
			if (is_manager) {
				flag = 2;
				this.code = this.$route.query.id;
				this.name = this.$route.query.name;
				type_list = this.filterManagerType(this.$route.query.hold.split(','));
				this.type = type_list[0]?.value;
			} else {
				flag = 1;
				this.code = this.$route.query.id;
				this.name = this.$route.query.name;
				this.type = this.$route.query.type;
				this.type2 = this.$route.query.type2;
				this.type3 = this.$route.query.type3;
				this.defaultModelId = this.$route.query?.model_id;
				this.isactive = String(this.$route.query.isactive) == 'true' ? true : false;
				this.isbookvalue = String(this.$route.query.isbookvalue) == 'true' ? true : false;
				this.type = this.type?.includes('fof') ? 'fof' : this.type;
			}

			this.info = {
				code: this.code,
				name: this.name,
				type: this.type,
				type_list,
				classType: flag == 1 ? 'fund' : 'manager',
				flag,
				type2: this.type2,
				type3: this.type3,
				isbookvalue: this.isbookvalue,
				start_date: '',
				end_date: ''
			};
			await this.getUserConfig();
		},
		filterManagerType(list) {
			let fundType_zh_en = [
				{ en: 'activeequity', zh: '主动权益' },
				{ en: 'bond', zh: '固收+' },
				{ en: 'cbond', zh: '可转债' },
				{ en: 'purebond', zh: '纯债' },
				{ en: 'bill', zh: '中短债' },
				{ en: 'obond', zh: '其他债券' },
				{ en: 'fof', zh: 'FOF' }
			];
			return fundType_zh_en
				.filter((v) => list.includes(v.en))
				.map((v) => {
					return { label: v.zh, value: v.en };
				});
		},
		// 获取用户设置模版
		async getUserConfig() {
			await this.getUserConfigInfo(this.modelList?.[this.modelList.length - 1]?.model_id, true);
		},
		// 获取组件模版详情
		async getUserConfigInfo(id, flag) {
			// this.componentsList = this.deepCopy(this.info.flag == 1 ? componentsList : managerComponentsList);
			this.componentsList = this.deepCopy(componentsList);
			// 遍历模板，处理数据
			this.filterComponents();
			// 过滤当前类型下的所有组件
			this.getTemplateList();

			if (flag) {
				this.$nextTick(() => {
					this.activeIndex = this.activeComponentsList?.[0]?.key;
					this.$refs[this.activeIndex]?.[0].getTemplateList(this.filterTemplateList(this.activeIndex));
					this.$refs[this.activeIndex]?.[0].getData(this.info);
					this.$refs['header'].getData(this.info);
					// this.getCapabilityInfo();
					if (this.isSubscription) {
						this.openTemplateList();
					}
				});
			}
		},
		// 模版新增/修改
		async editUserConfig(postData) {
			let data = {};
			let user_id = localStorage.getItem('id');
			if (!this.user_id || user_id != this.user_id || postData.id == '') {
				postData.id = this.post_id || 1;
				data = await postUserConfigInfo([postData]);
			} else {
				data = await putUserConfigInfo([postData]);
			}
			if (data?.mtycode == 200) {
				this.$message.success('模版修改成功');
				if (this.isSubscription) {
					this.$router.back();
				}
			} else {
				this.$message.warning('模版修改' + (data?.mtymessage || '失败'));
			}
		},
		async closeDraw() {
			this.$refs['dragListComponents'].closeDraw();
			let loading1 = this.$loading({
				lock: true,
				text: '正在应用模版中,请稍等...',
				spinner: 'el-icon-loading',
				background: 'rgba(0, 0, 0, 0.7)'
			});
			await this.getUserConfig();
			this.$refs['dragListComponents'].getData(this.activeComponentsList, this.allComponents, this.info.flag);
			loading1.close();
		},
		// 打开自定义模版抽屉
		openTemplateList() {
			this.$refs['dragListComponents'].openCrawer();
			this.$refs['dragListComponents'].getData(this.activeComponentsList, this.allComponents, this.info.flag);
		},
		// 执行word导出
		downloadWord() {
			this.$nextTick(() => {
				setTimeout(async () => {
					let header = await this.$refs['header'].createPrintWord();
					let current = 2;
					let downloadList = [...header];
					let promise_list = [];
					this.activeComponentsList.map((item) => {
						if (this.$refs[item.key]?.[0].createPrintWord) {
							let list = this.$refs[item.key]?.[0].createPrintWord(this.info);
							// if (item.key !== 'onePagePass') {
							// 	switch (current) {
							// 		case 2:
							// 			downloadList.push(...this.$exportWord.exportFirstTitle('二、' + item.label));
							// 			break;
							// 		case 3:
							// 			downloadList.push(...this.$exportWord.exportFirstTitle('三、' + item.label));
							// 			break;
							// 		case 4:
							// 			downloadList.push(...this.$exportWord.exportFirstTitle('四、' + item.label));
							// 			break;
							// 		case 5:
							// 			downloadList.push(...this.$exportWord.exportFirstTitle('五、' + item.label));
							// 			break;
							// 		case 6:
							// 			downloadList.push(...this.$exportWord.exportFirstTitle('六、' + item.label));
							// 			break;
							// 		case 7:
							// 			downloadList.push(...this.$exportWord.exportFirstTitle('七、' + item.label));
							// 			break;
							// 		case 8:
							// 			downloadList.push(...this.$exportWord.exportFirstTitle('八、' + item.label));
							// 			break;
							// 		case 9:
							// 			downloadList.push(...this.$exportWord.exportFirstTitle('九、' + item.label));
							// 			break;
							// 		case 10:
							// 			downloadList.push(...this.$exportWord.exportFirstTitle('十、' + item.label));
							// 			break;
							// 	}
							// 	current = current + 1;
							// }
							promise_list.push(list);
						}
					});
					promise_list = await Promise.all(promise_list);
					console.log('downloadList1', promise_list);
					promise_list.map((item) => {
						item.map((obj) => {
							downloadList.push(...obj);
						});
					});
					console.log('downloadList2', downloadList);
					let imgType = 'image/png';
					var xhr = new XMLHttpRequest();
					xhr.responseType = 'arraybuffer';
					xhr.open('GET', 'https://cdn.owl-portfolio.com/img/logoForWord.png', true);
					xhr.onload = () => {
						var result = xhr.response;
						var file = new File([result], 'foo.' + imgType.match(/\/([A-Za-z]+)/)[1], {
							type: imgType
						});
						var reader = new FileReader();
						reader.onload = (evt) => {
							// callBack(evt.target.result);
							this.$exportWord.downloadWord(this.info, [...downloadList], evt.target.result);
							this.loading.close();
							this.printActive = false;
						};
						reader.readAsDataURL(file);
					};
					xhr.send(null);
				}, 2000);
			});
		},
		// 点击打印
		print() {
			this.loading = this.$loading({
				lock: true,
				text: '正在生成word报告,请稍等...',
				spinner: 'el-icon-loading',
				background: 'rgba(0, 0, 0, 0.7)'
			});
			this.printActive = true;
			let that = this;
			if (this.printTimeOut) {
				this.printTimeOut = null;
				clearTimeout(this.printTimeOut);
			}
			// 一分钟超时中断
			this.printTimeOut = setTimeout(() => {
				if (that.printActive) {
					that.loading.close();
					that.printActive = false;
					that.$message.warning('打印超时,请检查网络后重试');
				}
			}, 90000);
			this.overComponents = this.activeComponentsList
				.map((item) => {
					if (this.$refs[item.key]?.[0].createPrintWord) {
						return item.key;
					}
				})
				.filter((item) => {
					return item !== undefined;
				});
			this.overComponents.map((item) => {
				this.$refs[item]?.[0].getTemplateList(this.filterTemplateList(item));
				// this.$refs[item]?.[0].getData(this.info);
				// this.$refs[item]?.[0].createPrintWord(this.info);
				// if (item == 'onePagePass') {
				// 	this.$refs[item]?.[0].getManager(this.manager_info);
				// 	this.$refs[item]?.[0].getCapabilityEvaluation(this.ability);
				// }
			});
			this.downloadWord();
		},
		// 深拷贝
		deepCopy(obj) {
			// 只拷贝对象
			if (typeof obj !== 'object') return;
			// 根据obj的类型判断是新建一个数组还是一个对象
			let newObj = obj instanceof Array ? [] : {};
			for (let key in obj) {
				// 遍历obj,并且判断是obj的属性才拷贝
				if (obj.hasOwnProperty(key)) {
					// 判断属性值的类型，如果是对象递归调用深拷贝
					newObj[key] = typeof obj[key] === 'object' ? this.deepCopy(obj[key]) : obj[key];
				}
			}
			return newObj;
		},
		filterComponents() {
			this.componentsList = this.componentsList.map((item) => {
				return {
					...item,
					templateList: item?.templateList
						.filter((obj) => {
							if (window.localStorage.getItem('mty_modulesName') == 'GDBank') {
								return true;
							} else return obj.is !== 'GDBankDetailequity' && obj.is !== 'GDBankDetailbond';
						})
						.map((val) => {
							let templateList = componentsList.filter((com) => {
								return item.key == com.key;
							})?.[0].templateList;
							let index = templateList.findIndex((temp) => {
								return temp.value == val.value;
							});
							return {
								...val,
								getData: templateList[index].getData || val.getdata,
								getRequestData: templateList[index].getRequestData || val.getrequestdata,
								methods: templateList[index].methods || val.func_name3
							};
						})
				};
			});
			this.allComponents = this.deepCopy(this.componentsList);
			this.activeComponentsList = this.componentsList.filter((item) => {
				return item.class.indexOf(this.info.type) !== -1;
			});
		},
		filterTemplateList(key) {
			let templateList = this.activeComponentsList.filter((item) => {
				return item.key == key;
			})?.[0]?.templateList;
			return templateList.filter((item) => {
				return item.typelist.indexOf(this.info.type) !== -1;
			});
		},
		// 监听menu的切换
		handleSelect(key) {
			if (this.activeIndex == key) {
				return;
			}
			this.activeIndex = key;
			this.getData();
		},
		changeDate(val) {
			this.info = { ...this.info, ...val };
			// 遍历模板，处理数据
			// this.filterComponents();
			// 过滤当前类型下的所有组件
			// this.getTemplateList();

			this.getData();
			// this.$nextTick(() => {
			// 	this.activeIndex = this.activeComponentsList?.[0]?.key;
			// 	this.$refs[this.activeIndex]?.[0].getTemplateList(this.filterTemplateList(this.activeIndex));
			// 	this.$refs[this.activeIndex]?.[0].getData(this.info);
			// 	this.$refs['header'].getData(this.info);
			// 	this.getCapabilityInfo();
			// 	if (this.isSubscription) {
			// 		this.openTemplateList();
			// 	}
			// });
		},
		getData() {
			if (this.activeIndex == 'onePagePass') {
				this.$refs['onePagePass']?.[0].getTemplateList(this.filterTemplateList('onePagePass'));
				this.$refs['onePagePass']?.[0].getData(this.info);
				this.$refs['onePagePass']?.[0].getManager(this.manager_info);
				this.$refs['onePagePass']?.[0].getCapabilityEvaluation(this.ability);
			} else {
				this.$refs[this.activeIndex]?.[0].getTemplateList(this.filterTemplateList(this.activeIndex));
				this.$refs[this.activeIndex]?.[0].getData(this.info);
			}
		},
		getTemplateList() {
			let templateComponentsList = this.componentsList.filter((item) => {
				return item.class.indexOf(this.info.type) !== -1;
			});
			templateComponentsList.map((item) => {
				item.templateList = item.templateList.filter((obj) => {
					return obj.typelist.indexOf(this.info.type) !== -1;
				});
			});
			// this.$refs['dragListComponents'].getData(templateComponentsList);
		},
		// 回归初始模版
		initTemplate() {
			let list = this.componentsList.map((item) => {
				return {
					...item,
					templateList: item?.templateList
						.filter((obj) => {
							if (window.localStorage.getItem('mty_modulesName') == 'GDBank') {
								return true;
							} else return obj.is !== 'GDBankDetailequity' && obj.is !== 'GDBankDetailbond';
						})
						.map((item) => {
							return {
								...item,
								getData: item.getData || item.getdata,
								getRequestData: item.getRequestData || item.getrequestdata
							};
						})
				};
			});
			let templateComponentsList = list.filter((item) => {
				return item.class.indexOf(this.info.type) !== -1;
			});
			templateComponentsList.map((item) => {
				item.templateList = item.templateList.filter((obj) => {
					return obj.typelist.indexOf(this.info.type) !== -1;
				});
			});
			this.$refs['dragListComponents'].getData(templateComponentsList, componentsList, this.info.flag);
		},
		// 获取排序后模板
		getSortModelList(list, form) {
			let postList = list.map((item) => {
				return {
					...item,
					show: true,
					templateList: item.templateList.map((obj, index) => {
						return {
							...obj,
							getdata: obj.getData || obj.getdata,
							getrequestdata: obj.getRequestData || obj.getrequestdata,
							func_name3: obj?.methods || obj?.func_name3,
							number: index + 1
						};
					})
				};
			});
			if (window.localStorage.getItem('mty_modulesName') == 'GDBank') {
				let GDTemp = componentsList
					.filter((item) => {
						return item.key == 'onePagePass';
					})?.[0]
					?.templateList?.filter((item) => {
						return item.is == 'GDBankDetailequity' || item.is == 'GDBankDetailbond';
					})
					.map((item) => {
						return {
							...item,
							getdata: item.getData || item.getdata,
							getrequestdata: item.getRequestData || item.getrequestdata,
							func_name3: item?.methods || item?.func_name3
						};
					});
				let index = postList.findIndex((item) => {
					return item.key == 'onePagePass';
				});
				if (
					postList?.[index]?.templateList?.findIndex((item) => {
						return item.is == 'GDBankDetailequity' || item.is == 'GDBankDetailbond';
					}) == -1
				) {
					postList?.[index]?.templateList?.unshift(
						GDTemp.filter((item) => {
							if (this.info.type == 'equity' || this.info.type == 'equitywithhk') {
								return item.is == 'GDBankDetaileequity';
							} else if (this.info.type == 'bond' || this.info.type == 'cbond') {
								return item.is == 'GDBankDetailbond';
							} else {
								return false;
							}
						})
					);
				}
			}
			let postData = {
				id: this.model_id,
				model_name: '基金默认模版',
				model_type: this.info.type,
				industry: '',
				ismanager: false,
				isdefault: true,
				componentsList: postList,
				...form
			};
			this.editUserConfig(postData);
		},
		// 获取排序后组件列表
		getSortList(data) {
			this.$refs['onePagePass']?.[0].getTemplateList(list);
		},
		// 接收基金经理code
		managerMsg(e) {
			this.manager_info = e;
			this.$refs['onePagePass']?.[0].getManager(e);
		},

		// 展开侧边栏
		expandSidebar() {
			this.isCollapsed = false;
		},

		// 折叠侧边栏
		collapseSidebar() {
			this.isCollapsed = true;
		},

		// 获取菜单项的图标
		getIconForMenuItem(key) {
			const iconMap = {
				'onePagePass': 'el-icon-document',
				'performance': 'el-icon-data-line',
				'styleAnalysis': 'el-icon-pie-chart',
				'abilityAnalysis': 'el-icon-s-data',
				'equityAnalysis': 'el-icon-s-finance',
				'attributionAnalysis': 'el-icon-s-marketing',
				'bondAnalysis': 'el-icon-money',
				'cbondAnalysis': 'el-icon-s-finance',
				'fofAnalysis': 'el-icon-s-cooperation',
				'moneyAnalysis': 'el-icon-coin'
			};

			return iconMap[key] || 'el-icon-menu';
		},
		// 获取基金/基金经理能力评价
		async getCapabilityInfo() {
			let capabilityEvaluation = null;
			let key = this.activeComponentsList?.[0]?.key;
			if (this.getCacheData('capabilityEvaluation')) {
				capabilityEvaluation = this.getCacheData('capabilityEvaluation');
				this.$refs['header'].getCapabilityEvaluation(capabilityEvaluation);
				if (this.$refs[key]?.[0].getCapabilityEvaluation) {
					this.$refs[key]?.[0].getCapabilityEvaluation(capabilityEvaluation);
				}
			} else {
				let postData = {
					type: this.info.type,
					codes: [this.info.code],
					flag: ['1'],
					start_date: this.info.start_date,
					end_date: this.info.end_date,
					item: []
				};
				let data = await getCapabilityInfo(postData);
				if (data?.mtycode == 200) {
					this.ability = data?.data;
					// this.ability.name = this.name;
					capabilityEvaluation = this.ability;
					this.setCacheData('capabilityEvaluation', capabilityEvaluation);
					this.$refs['header'].getCapabilityEvaluation(capabilityEvaluation);
					if (this.$refs[key]?.[0].getCapabilityEvaluation) {
						this.$refs[key]?.[0].getCapabilityEvaluation(capabilityEvaluation);
					}
				} else {
					this.$message.warning('基金能力评价' + (data?.mtymessage || '暂无数据'));
				}
			}
		},
		// 删除模版
		async deleteComponents(id) {
			let data = await deleteUserConfigInfo({ id });
			if (data?.mtycode == 200) {
				this.$message.success('模版删除成功');
				await this.getUserConfig();
				this.$refs['dragListComponents'].getData(this.activeComponentsList, this.allComponents, this.info.flag);
			} else {
				this.$message.warning('模版删除' + (data?.mtymessage || '失败'));
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.fund-page-layout {
	display: flex;
	height: calc(100vh - 56px);
	position: relative;
}

.sidebar-menu {
	// margin-top: 50px;
	width: 200px;
	min-width: 200px;
	background: #fff;
	border-right: 1px solid #e6e6e6;
	display: flex;
	flex-direction: column;
	position: fixed;
	top: 56px; /* 顶部导航栏高度 */
	left: 0;
	bottom: 0;
	z-index: 10;
	overflow-y: auto;
	transition: all 0.3s ease;

	&.sidebar-collapsed {
		width: 64px;
		min-width: 64px;
	}

	.type_menu {
		flex: 1;
		border-right: none;

		::v-deep.el-menu-item {
			padding: 0 16px;
			height: 50px;
			line-height: 50px;
			font-size: 14px;

			&.is-active {
				color: #4096ff;
				background-color: #ecf5ff;
				border-left: 3px solid #4096ff;
			}

			&:hover {
				background-color: #f5f7fa;
			}
		}

		.menu-icon {
			margin-right: 5px;
			font-size: 18px;
		}
	}

	.print-btn-container {
		padding: 16px 0;
		display: flex;
		justify-content: center;
	}

	.print-btn {
		width: calc(100% - 32px);
		margin: 0 16px;
		text-align: center;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
}

.right-content {
	flex: 1;
	margin-left: 200px; /* 与侧边栏宽度相同，确保内容不会被侧边栏覆盖 */
	display: flex;
	flex-direction: column;
	overflow: hidden;
}

.header-container {
	padding: 16px;
	background: #fff;
	border-bottom: 1px solid #e6e6e6;
}

.main-content {
	flex: 1;
	margin-top:-50px;
	margin-left: 200px;
	padding: 16px;
	background: #fff;
	overflow: auto;
	transition: margin-left 0.3s ease;
}

.sidebar-collapsed ~ .main-content {
	margin-left: 64px;
}

/* 响应式样式 */
@media screen and (max-width: 768px) {
	.fund-page-layout {
		flex-direction: column;
		height: auto;
	}

	.sidebar-menu {
		margin-top: 50px;
		position: static;
		width: 100%;
		min-width: 100%;
		border-right: none;
		border-bottom: 1px solid #e6e6e6;

		.type_menu {
			display: flex;
			flex-wrap: wrap;

			::v-deep.el-menu-item {
				flex: 1;
				min-width: 120px;
				text-align: center;

				&.is-active {
					border-left: none;
					border-bottom: 3px solid #4096ff;
				}
			}
		}
	}

	.right-content {
		margin-left: 0;
	}
}
</style>
<style scoped>
.stopManaged {
	z-index: 9999999;
	position: absolute;
	width: 200px;
	height: 200px;
}
</style>
<style lang="scss">
.box-card {
	width: 550px;
	height: 280px;
	border: 1px solid #e9e9e9;
	display: flex;
	justify-content: center;
	align-items: center;
}
.flex_card {
	width: 100%;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	flex-wrap: wrap;
}
</style>
