<template>
    <div class="plate-wrapper valuation-percentile-wrapper" v-loading="loading">
        <combinationComponentHeader title="组合风险收益" @download="exportExcel">
            <template slot="right">
                <!-- <el-form  ref="form" :model="form" label-width="80px" class="title-right-form" style="margin-right: 16px;">
                    <el-select v-model="comparisonValue" placeholder="请选择基准">
						<el-option
                            v-for="item in options"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                            </el-option>
					</el-select>
                   
                </el-form> -->
            </template>
        </combinationComponentHeader>
        <el-table border stripe :data="tableDataNow">
            <el-table-column min-width="120px" align="gotoleft" prop="returnCum" show-overflow-tooltip label="区间总收益率">
                <templete slot-scope="scope">{{ scope.row.returnCum | fix2p }}</templete>
            </el-table-column>
            <el-table-column min-width="120px" align="gotoleft" prop="excess" show-overflow-tooltip label="区间相对收益率">
                <templete slot-scope="scope">{{ scope.row.excess | fix2p }}</templete>
            </el-table-column>
            <el-table-column min-width="120px" align="gotoleft" prop="yesterday"  label="昨日收益率">
                <templete slot-scope="scope">{{ scope.row.yesterday | fix2p }}</templete>
            </el-table-column>
            <el-table-column min-width="120px" align="gotoleft" prop="yearToDate"  label="年初至今">
                <templete slot-scope="scope">{{ scope.row.yearToDate | fix2p }}</templete>
            </el-table-column>
            <el-table-column min-width="120px" align="gotoleft"  prop="lastWeek" label="近1周">
                <templete slot-scope="scope">{{ scope.row.lastWeek | fix2p }}</templete>
            </el-table-column>
            <el-table-column min-width="120px" align="gotoleft"  prop="lastMonth" label="近1月">
                <templete slot-scope="scope">{{ scope.row.lastMonth | fix2p }}</templete>
            </el-table-column>
            <el-table-column min-width="120px" align="gotoleft"  prop="lastSeason" label="近1季">
                <templete slot-scope="scope">{{ scope.row.lastSeason | fix2p }}</templete>
            </el-table-column>
            <el-table-column min-width="120px" align="gotoleft"  prop="lastHalfYears" label="近6月">
                <templete slot-scope="scope">{{ scope.row.lastHalfYears | fix2p }}</templete>
            </el-table-column>
            <el-table-column min-width="120px" align="gotoleft"  prop="lastYear" label="近1年">
                <templete slot-scope="scope">{{ scope.row.lastYear | fix2p }}</templete>
            </el-table-column>
            <el-table-column min-width="120px" align="gotoleft"  prop="lastThreeYear" label="近3年">
                <templete slot-scope="scope">{{ scope.row.lastThreeYear | fix2p }}</templete>
            </el-table-column>
            <el-table-column min-width="120px" align="gotoleft"  prop="aveVolatility" label="年化波动">
                <templete slot-scope="scope">{{ scope.row.aveVolatility | fix2p }}</templete>
            </el-table-column>
            <el-table-column min-width="120px" align="gotoleft"  prop="alpha" label="alpha">
                <templete slot-scope="scope">{{ scope.row.alpha || '--'  }}</templete>
            </el-table-column>
            <el-table-column min-width="120px" align="gotoleft"  prop="maxDrawdown" label="区间最大回撤">
                <templete slot-scope="scope">{{ scope.row.maxDrawdown | fix2p }}</templete>
            </el-table-column>

            <template slot="empty">
                <el-empty image-size="160"></el-empty>
            </template>
        </el-table>
    </div>
</template>
<script>
import { filter_to_excel } from "@/utils/exportExcel.js";
import combinationComponentHeader from './combinationComponentHeader.vue';
import { combinationRiskReturn} from '@/api/pages/tkAnalysis/portfolio.js';
export default {
    name:'TheFundPerformanceQuantilePlate',
    components:{
        combinationComponentHeader
    },
    filters: {
		fix6(value) {
			return value.substring(0, 10);
		},
		fix3(value) {
			return parseInt(value * 1000) / 1000;
		},
		fix5(value) {
			return parseInt(value * 100000) / 100000;
		},
		fix2p(value) {
			return value &&
				value != '' &&
				value != '--' &&
				value != '- -' &&
				JSON.stringify(value) != '[]' &&
				JSON.stringify(value) != '{}' &&
				value != 'NAN' &&
				value != 'nan'
				? (Number(value) * 100).toFixed(2) + '%'
				: '--';
		},
		fix2(value) {
			return value &&
				value != '' &&
				value != '--' &&
				value != '- -' &&
				JSON.stringify(value) != '[]' &&
				JSON.stringify(value) != '{}' &&
				value != 'NAN' &&
				value != 'nan'
				? Number(value).toFixed(2)
				: '--';
		},
	},
    data(){
        return {
            options: [{
				value: '年初至今',
				label: '年初至今'
			}],
            comparisonValue:'年初至今',
            tableDataNow: [],
            loading:false,
            tableHeader:[{
                prop:'returnCum',
                label:'区间总收益'
            },{
                prop:'excess',
                label:'区间相对收益'
            },{
                prop:'yesterday',
                label:'昨日收益'
            },{
                prop:'yearToDate',
                label:'年初至今'
            },{
                prop:'lastWeek',
                label:'近1周'
            },{
                prop:'lastMonth',
                label:'近1月'
            },{
                prop:'lastSeason',
                label:'近1季'
            },{
                prop:'lastHalfYears',
                label:'近6月'
            },{
                prop:'lastYear',
                label:'近1年'
            },{
                prop:'lastThreeYear',
                label:'近3年'
            },{
                prop:'aveVolatility',
                label:'年化波动'
            },{
                prop:'alpha',
                label:'alpha'
            },{
                prop:'maxDrawdown',
                label:'区间最大回撤'
            }]
        }
    },
    methods:{
        async getData(param){
            this.loading = true;
            //获取组合详情
			let {mtycode,data} = await combinationRiskReturn({
				...param,
				id:param.combinationId
			});
			if (mtycode == 200) {
				this.tableDataNow = [data] || [];
			} 
            this.loading = false;
        },
        exportExcel(){
            let list = this.tableHeader.map((item) => {
				return {
					...item,
					format: ''
				};
			});
			filter_to_excel(list, this.tableDataNow, '组合风险收益');
        }
    },
    mounted(){
       
    },
}
</script>
<style lang="scss" scoped></style>