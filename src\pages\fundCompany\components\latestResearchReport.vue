<template>
	<div class="latest-research-report">
		<!-- <span>最新研报</span> -->
    <el-card>
			<div slot="header" class="header-title">
				<i class="points"></i>
				<span>最新报告</span>
			</div>
			<div class="fund-analysis card-box">
				<el-table class="fund-analysis-table" :data="newReportData">
					<el-table-column prop="a" label="报告名称"></el-table-column>
					<el-table-column prop="b" label="发布日期"></el-table-column>
					<el-table-column prop="c" label="点赞数量"></el-table-column>
					<el-table-column prop="d" label="观看数量"></el-table-column>
					<el-table-column prop="e" label="发布人"></el-table-column>
				</el-table>
			</div>
		</el-card>
	</div>
</template>

<script>
export default {
	data() {
		return {
      newReportData: []
    };
	},
  created() {},
  mounted() {
    this.getReportList()
  },
  methods: {
    getReportList() {
      // do something
    }
  }
};
</script>

<style scoped lang="scss">
.header-title {
  font-size: 16px;
}
</style>