<template>
	<div class="chart_one" v-show="show">
		<div style="display: flex; align-items: center; justify-content: space-between">
			<div
				style="
					flex: 1;
					font-family: 'PingFang';
					font-style: normal;
					font-weight: 400;
					font-size: 14px;
					line-height: 22px;
					color: rgba(0, 0, 0, 0.65);
					margin-bottom: 16px;
					margin-top: 24px;
				"
			>
				已披露股票行业配置
			</div>
			<div style="flex: 1">
				<el-button icon="el-icon-document-delete" style="margin-left: -110px" @click="exportExcel">导出Excel</el-button>
			</div>
		</div>
		<div style="position: relative">
			<div style="display: flex; width: 100%">
				<el-table
					v-loading="loading"
					style="flex: 1"
					:data="industrypeizhi"
					height="400px"
					class="industry_peizhi_h400 table"
					ref="multipleTable"
					:default-sort="{ prop: 'weight', order: 'descending' }"
					header-cell-class-name="table-header"
				>
					<el-table-column sortable prop="swname" label="行业" align="gotoleft"> </el-table-column>
					<el-table-column sortable prop="weight" label="占净值比" align="gotoleft">
						<template slot-scope="scope">{{ scope.row.weight | fix3 }}%</template>
					</el-table-column>
				</el-table>
				<div class="charts_center_class" style="flex: 1" v-loading="loading">
					<v-chart
						ref="stockIndustryAllocation"
						v-loading="loading"
						element-loading-text="暂无数据"
						element-loading-spinner="el-icon-document-delete"
						element-loading-background="rgba(239, 239, 239, 0.5)"
						class="charts_one_class"
						autoresize
						:options="optionbaogao"
					></v-chart>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { exportTitle, exportTable, exportChart } from '@/utils/exportWord.js';
import { filter_json_to_excel } from '@/utils/exportExcel.js';
// 已披露股票行业配置
import VChart from 'vue-echarts';
export default {
	name: 'stockIndustryAllocation',
	components: {
		VChart
	},
	data() {
		return {
			industrypeizhi: [],
			loading: true,
			color: ['#4096ff', '#4096ff', '#7388A9', '#6F80DD', '#6C96F2', '#FD6865', '#83D6AE', '#88C9E9', '#ED589D', '#FA541C'],
			optionbaogao: {},
			show: true
		};
	},
	filters: {
		fix3(value) {
			if (value == '--') {
				return value;
			} else return parseInt(value * 1000) / 1000;
		}
	},
	methods: {
		getData(data) {
			if (this.FUNC.isValidObj(data) && this.FUNC.isValidObj(data)) {
				this.loading = false;
				this.industrypeizhi = data;
				let series = this.formatData(data);
				this.optionbaogao = {
					color: this.color,
					toolbox: {
						feature: {
							saveAsImage: { pixelRatio: 3 }
						},
						top: -4,
						width: 104
					},
					series: {
						type: 'sunburst',
						emphasis: {
							focus: 'ancestor'
						},
						data: series,
						// radius: [0, '90%'],
						label: {
							rotate: 'radial'
						}
					}
				};
			}
		},
		// 格式化数据
		formatData(data) {
			let industryList = [];
			data.map((item) => {
				let swname = item.swname.split('-');
				if (item.weight * 1 < 1) {
					industryList.push({
						sw1: '其他',
						sw2: '其他',
						sw3: '其他',
						weight: item.weight
					});
				} else {
					industryList.push({
						sw1: swname?.[0],
						sw2: swname?.[1],
						sw3: swname?.[2],
						weight: item.weight
					});
				}
			});
			let series = [];
			industryList.map((item) => {
				// 一级行业是否相同
				let sw1Index = series.findIndex((obj) => {
					return obj.name == item.sw1;
				});
				if (sw1Index == -1) {
					series.push({
						name: item.sw1,
						children: [
							{
								name: item.sw2,
								children: [
									{
										name: item.sw3,
										value: item.weight
									}
								]
							}
						]
					});
				} else {
					// 二级行业是否相同
					let sw2Index = series[sw1Index].children.findIndex((obj) => {
						return obj.name == item.sw2;
					});
					if (sw2Index == -1) {
						series[sw1Index].children.push({
							name: item.sw2,
							children: [
								{
									name: item.sw3,
									value: item.weight
								}
							]
						});
					} else {
						// 三级行业是否相同
						let sw3Index = series[sw1Index].children[sw2Index].children.findIndex((obj) => {
							return obj.name == item.sw3;
						});
						if (sw3Index == -1) {
							series[sw1Index].children[sw2Index].children.push({
								name: item.sw3,
								value: item.weight
							});
						} else {
							series[sw1Index].children[sw2Index].children[sw3Index].value =
								series[sw1Index].children[sw2Index].children[sw3Index].value + item.weight;
						}
					}
				}
			});
			return series;
		},
		// 无数据隐藏
		hideLoading() {
			this.show = false;
		},
		exportExcel() {
			let list = [
				{
					label: '行业',
					fill: 'header',
					value: 'swname'
				},
				{
					label: '占净值比',
					value: 'weight',
					format: 'fix2b'
				}
			];
			filter_json_to_excel(
				list,
				this.industrypeizhi.sort((a, b) => {
					return b.weight - a.weight;
				}),
				'已披露股票行业配置'
			);
		},
		createPrintWord() {
			let list = [
				{
					label: '行业',
					fill: 'header',
					value: 'swname'
				},
				{
					label: '占净值比',
					value: 'weight',
					format: 'fix2b'
				}
			];
			this.$refs['stockIndustryAllocation'].mergeOptions({ toolbox: { show: false, legend: { type: 'plain' } } });
			let height = this.$refs['stockIndustryAllocation']?.$el.clientHeight;
			let width = this.$refs['stockIndustryAllocation']?.$el.clientWidth;
			let chart = this.$refs['stockIndustryAllocation'].getDataURL({
				type: 'png',
				pixelRatio: 2,
				backgroundColor: '#fff'
			});
			this.$refs['stockIndustryAllocation'].mergeOptions({ toolbox: { show: true, legend: { type: 'scroll' } } });
			return [
				...exportTitle('已披露股票行业配置'),
				...exportTable(
					list,
					this.industrypeizhi
						.sort((a, b) => {
							return b.weight - a.weight;
						})
						.slice(0, 10)
				),
				...exportChart(chart, { width, height })
			];
		}
	}
};
</script>

<style></style>
