<template>
	<div class="chart_one">
		<div class="title">高管变动</div>
		<div v-loading="loading">
			<el-table :data="data">
				<el-table-column v-for="item in column" :key="item.value" :prop="item.value" :label="item.label" align="gotoleft">
				</el-table-column>
			</el-table>
		</div>
	</div>
</template>

<script>
export default {
	data() {
		return {
			data: [],
			column: [
				{
					label: '高管岗位',
					value: 'job'
				},
				{
					label: '高管名称',
					value: 'name'
				},
				{
					label: '高管变动后名称',
					value: 'change_name'
				},
				{
					label: '变动日期',
					value: 'time'
				}
			],
			loading: true
		};
	},
	methods: {
		getData() {
			this.loading = false;
			this.data = [
				{
					job: '某某岗位',
					name: '李某某',
					change_name: '李某某',
					time: '2013-03-04'
				}
			];
		}
	}
};
</script>

<style></style>
