<template>
	<div style="padding: 24px">
		<el-table :data="data" style="width: 100%; min-height: calc(100vh - 339px)">
			<el-table-column v-for="item in column" :key="item.value" :prop="item.value" :label="item.label" align="gotoleft">
				<template slot-scope="{ row }">
					<div v-show="item.value == 'setting'">
						<el-link @click="downloadFile(row, 'word')">下载Word</el-link>
						<el-link @click="downloadFile(row, 'pdf')" style="margin-left: 16px">下载PDF</el-link>
					</div>
					<div v-show="item.value != 'setting'">{{ row[item.value] }}</div>
				</template>
			</el-table-column>
			<template slot="empty">
				<el-empty image-size="160"></el-empty>
			</template>
		</el-table>
	</div>
</template>

<script>
import { getReportList, getReportUrl } from '@/api/pages/NodeServer.js';

export default {
	data() {
		return {
			data: [],
			column: [
				{
					label: '文件名称',
					value: 'name'
				},
				{
					label: '更新时间',
					value: 'time'
				},
				{
					label: '操作',
					value: 'setting'
				}
			]
		};
	},
	methods: {
		// 获取数据
		getData() {
			this.getReportList();
		},
		// 获取报告列表
		async getReportList() {
			let data = await getReportList({ user_id: localStorage.getItem('id') });
			if (data?.mtycode == 200) {
				this.data = data?.data.map((item) => {
					return {
						...item,
						time: this.moment(item.time).format('YYYY-MM-DD HH:mm:ss')
					};
				});
			}
		},
		// 下载文件
		async downloadFile(row, type) {
			let data = await getReportUrl({
				id: row.id,
				user_id: row.user_id,
				name: row.name,
				type
			});
			if (data?.mtycode == 200) {
				var a = document.createElement('a');
				a.href = data?.data.url;
				a.download = row.name + '.' + (type == 'word' ? 'docx' : 'pdf');
				document.body.appendChild(a);
				a.click();
			}
		}
	}
};
</script>

<style></style>
