<!--  -->
<template>
	<div class="industryTheme">
		<!-- <el-select @change="command2()" v-model="value" filterable :placeholder="'请选择' + haveName">
			<el-option v-for="item in quarterList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
		</el-select> -->
		<operator v-if="is_range" ref="operator" @resolveMathRange="resolveMathRange"></operator>
		<div>
			<el-cascader
				@change="command2()"
				popper-class="industrySum"
				placeholder="多行业选择"
				:options="quarterList"
				v-model="value"
				:props="{ multiple: true }"
				filterable
			></el-cascader>
		</div>
		<el-dropdown style="margin-left: 16px" @command="command">
			<el-button type="primary">
				{{ iconFlag != '' ? (iconFlag == 'all' ? '所有' : iconFlag) : '运算符' }}<i class="el-icon-arrow-down el-icon--right"></i>
			</el-button>
			<el-dropdown-menu slot="dropdown">
				<el-dropdown-item command="all">所有</el-dropdown-item>
				<el-dropdown-item command="<">&lt;</el-dropdown-item>
				<el-dropdown-item command="=">=</el-dropdown-item>
				<el-dropdown-item command=">">&gt;</el-dropdown-item>
				<el-dropdown-item command="<=">&lt;=</el-dropdown-item>
				<el-dropdown-item command=">=">&gt;=</el-dropdown-item>
			</el-dropdown-menu>
		</el-dropdown>
		<div v-show="showBox" style="margin-left: 0px; display: flex; align-items: center">
			<div style="margin-left: 16px">
				<el-input type="number" @input="inputChange" placeholder="输入50,即持仓占比为50%" v-model="input"></el-input>
			</div>
		</div>
	</div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import operator from '@/pages/filter/fund/beta/componentsFilter/components/operator.vue';

export default {
	props: {
		is_range: {
			type: Boolean,
			default: false
		},
		haveName: {
			type: String,
			default: ''
		},
		dataX: {
			type: Object,
			default: {}
		},
		placeholder: {
			type: String
		},
		indexFlag: {
			type: Number
		},
		baseIndexFlag: {
			type: Number
		},
		dataIndustry: {
			type: Object
		}
	},
	//import引入的组件需要注入到对象中才能使用
	components: { operator },
	data() {
		//这里存放数据
		return {
			value: '',
			iconFlag: '',
			showBox: false,
			input: '',
			quarterList: [],
			industry_name: '',
			industry_nameT: '',
			options: [],
			mathRange: { mathRange: 'avg' }
		};
	},
	//监听属性 类似于data概念
	computed: {},
	//监控data中的数据变化
	watch: {
		dataIndustry(val) {
			this.getObject();
		},
		dataX(val) {
			if (val.dataResult && val.dataResult.length > 0) {
				this.showBox = true;
				this.iconFlag = val.dataResult[0].flag;
				this.input = val.dataResult[0].value;
				this.industry_name = val.dataResult[0].industryValue;
				this.value = this.industry_name;
				this.industry_nameT = val.dataResult[0].industryName;
				if (this.$refs['operator']) {
					this.$refs['operator'].getFlag(val.dataResult[0].mathRange);
				}
			}
		}
	},
	//方法集合
	methods: {
		resolveMathRange(obj) {
			this.mathRange = obj;
			this.resolveFather();
		},
		resolveFather() {
			this.$emit(
				'industrySumChange',
				this.baseIndexFlag,
				this.indexFlag,
				this.input,
				this.iconFlag,
				this.industry_name,
				this.returnNaME(),
				this.FUNC.isEmpty(this.industry_name) && this.FUNC.isEmpty(this.input) && this.FUNC.isEmpty(this.iconFlag),
				this.mathRange
			);
		},
		command(e) {
			this.iconFlag = e;
			this.industry_nameT =
				this.quarterList.findIndex((item) => item.value == this.industry_name) >= 0
					? this.quarterList[this.quarterList.findIndex((item) => item.value == this.industry_name)][
							this.haveName.indexOf('主题') >= 0 ? 'lable' : 'label'
					  ]
					: '';
			this.showBox = true;
			this.resolveFather();
		},
		command2() {
			this.industry_name = this.value;
			this.resolveFather();
		},
		returnNaME() {
			let list = [];
			for (let k = 0; k < this.value.length; k++) {
				let x = this.quarterList.findIndex((item) => item.label == this.value[k][0]);
				list.push(this.quarterList[x].children[this.quarterList[x].children.findIndex((item) => item.value == this.value[k][1])].label);
			}

			return list;
		},
		inputChange() {
			this.resolveFather();
		},
		getObject() {
			if (JSON.stringify(this.dataIndustry) != '{}') {
				this.quarterList = [
					{ label: '申万(2021)', value: '申万(2021)', children: this.dataIndustry.alpha[0]['申万(2021)'] },
					{ label: '申万二级(2021)', value: '申万二级(2021)', children: this.dataIndustry.alpha[1]['申万二级(2021)'] },
					{ label: '申万三级(2021)', value: '申万三级(2021)', children: this.dataIndustry.alpha[2]['申万三级(2021)'] },
					{ label: '恒生一级', value: '恒生一级', children: this.dataIndustry.alpha[3]['恒生一级'] }
				];
			}
			this.$nextTick(() => {
				// 添加这段代码
				const $el = document.querySelectorAll('.el-cascader-panel .el-cascader-node[aria-owns]');
				Array.from($el).map((item) => item.removeAttribute('aria-owns'));
			});
		}
	},
	//生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	//生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		if (JSON.stringify(this.dataX) != '{}') {
			if (this.dataX.dataResult && this.dataX.dataResult.length > 0) {
				this.showBox = true;
				this.iconFlag = this.dataX.dataResult[0].flag;
				this.input = this.dataX.dataResult[0].value;
				this.industry_nameT = this.dataX.dataResult[0].industryName;
				this.industry_name = this.dataX.dataResult[0].industryValue;
				this.value = this.industry_name;
				if (this.$refs['operator']) {
					this.$refs['operator'].getFlag(this.dataX.dataResult[0].mathRange);
				}
			}
		}
		this.getObject();
	},
	beforeCreate() {}, //生命周期 - 创建之前
	beforeMount() {}, //生命周期 - 挂载之前
	beforeUpdate() {}, //生命周期 - 更新之前
	updated() {
		if (this.quarterList.length == 0) {
			this.getObject();
		}
	}, //生命周期 - 更新之后
	beforeDestroy() {}, //生命周期 - 销毁之前
	destroyed() {}, //生命周期 - 销毁完成
	activated() {} //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style>
.industry_theme_drapDown {
	height: 300px !important;
	overflow: auto !important;
}
.industrySum .el-cascader-panel .el-cascader-node__label {
	margin-left: 8px !important;
}
</style>
<style lang="scss" scoped>
//@import url(); 引入公共css类
.industryTheme {
	display: flex;
	align-items: center;
}
</style>
