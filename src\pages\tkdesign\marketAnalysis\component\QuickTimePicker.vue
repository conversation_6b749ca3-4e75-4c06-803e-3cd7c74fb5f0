<template>
	<el-radio-group class="quick-time-picker" :value="value.radioValue" @input="handleInput" size="small">
		<el-radio-button v-for="item in list" :key="item.value" :label="item.value">{{ item.label }}</el-radio-button>
		<DatePickerBtn
			label="custom"
			:class="value.radioValue === 'custom' ? 'is-active' : ''"
			@click.native="handleCustomClick"
			btnType="normal"
			@change="handleDateChange"
		></DatePickerBtn>
	</el-radio-group>
</template>
<script>
import DatePickerBtn from './DatePickerBtn.vue';
export default {
	name: 'QuickTimePicker',
	components: {
		DatePickerBtn
	},
	props: {
		value: {
			type: Object,
			default: () => {
				return {
					radioValue: '',
					startDate: '',
					endDate: ''
				};
			}
		},
		list: {
			type: Array,
			default: [
				{ label: '1Y', value: '1' },
				{ label: '2Y', value: '2' },
				{ label: '3Y', value: '3' }
			]
		}
	},
	data() {
		return {};
	},
	methods: {
		//原生ladio选中时会触发
		handleInput(value) {
			let result = {
				radioValue: value
			};
			this.$emit('input', result);
			this.$emit('change', result);
		},
		handleDateChange(value) {
			let result = {
				radioValue: 'custom',
				startDate: value[0],
				endDate: value[1]
			};
			this.$emit('input', result);
			this.$emit('change', result);
		},
		handleCustomClick() {}
	}
};
</script>
<style lang="scss" scoped>
.is-active {
	::v-deep .el-button {
		color: #fff;
		background-color: #4096ff;
		border-color: #4096ff;
		path {
			fill: #fff !important;
		}
	}
}
.quick-time-picker {
	display: flex;
	::v-deep .date-icon-btn {
		border-radius: 0 4px 4px 0;
		border-left: unset;
		height: 32px;
	}
}
::v-deep .el-radio-button__orig-radio:checked + .el-radio-button__inner {
	.icon-path {
		fill: #ffffff !important;
	}
}
::v-deep .el-radio-button__inner:hover {
	.icon-path {
		fill: #4096ff !important;
	}
}
::v-deep .el-radio-button--small .el-radio-button__inner {
	box-sizing: content-box;
	line-height: 14px;
	height: 14px;
	padding-bottom: 8px;
	padding-top: 8px;
}
</style>
