<!--  -->
<template>
	<div v-loading="loading" class="comDetailPage">
		<assetDetails
			@updatePort="updatePort"
			v-model="showDetails"
			:combinationName="comDetailInfo.combinationName"
			:endDate="endDate"
			ref="assetDetails"
			:comId="comDetailInfo.combinationId"
		></assetDetails>
		<!-- <div class="comMainTitle">
      <span @click="goback"
            style="color: rgba(0, 0, 0, 0.46); font-size: 14px;cursor: pointer;">模拟组合管理&nbsp;/&nbsp;</span><span>组合详情</span>
    </div> -->
		<div class="comBox">
			<div class="comBoxContent" style="padding-top: 24px; padding-bottom: 24px; margin-top: 0px">
				<div
					style="
						font-family: 'PingFang';
						font-style: normal;
						font-weight: 500;
						font-size: 16px;
						line-height: 24px;
						color: rgba(0, 0, 0, 0.85);
					"
				>
					<span>
						{{ comDetailInfo.combinationName }}
					</span>
					<span
						class="new-date"
						style="
							float: right;
							color: rgba(0, 0, 0, 0.65);
							font-size: 14px;
							font-weight: 400;
							line-height: 22px;
							letter-spacing: 0em;
							text-align: left;
						"
					>
						数据更新：{{ comDetailInfo.date }}
					</span>
				</div>
				<div style="display: flex; gap: 8px; margin-top: 8px">
					<el-tag
						class="portfolio-tag"
						v-for="(label, index) in labelList"
						:key="index"
						:disable-transitions="false"
						closable
						@close="handleClose(index)"
						>{{ label }}</el-tag
					>
					<el-input
						class="input-new-tag"
						v-if="inputVisible"
						v-model="inputValue"
						ref="saveTagInput"
						size="small"
						@keyup.enter.native="handleInputConfirm"
					>
					</el-input>
					<el-button v-else class="button-new-tag" size="small" @click="showInput">+添加标签</el-button>
					<!-- <el-tag class="portfolio-tag" >成立日期：2023-05-01</el-tag> -->
				</div>
				<div style="display: flex; align-items: center; justify-content: space-evenly; margin-top: 20px; gap: 10px">
					<div
						class="info-item"
						v-for="(key, index) in infoList"
						:key="index"
						:style="{ background: key.img ? (parseFloat(comDetailInfo[key.propName]) < 0 ? greenColor : redColor) : key.backgroundColor }"
					>
						<div class="item-top">
							{{ key.top }}{{ key.propName === 'rate' || key.propName === 'excess' ? '(' + comDetailInfo.date + ')' : '' }}
						</div>
						<div
							class="item-bottom"
							:class="[key.bottomColor ? (parseFloat(comDetailInfo[key.propName]) < 0 ? 'item-green' : 'item-red') : '']"
						>
							<div>
								<template v-if="key.propName === 'rate' || key.propName === 'excess'">
									{{ comDetailInfo[key.propName] | fix2p }}
								</template>
								<template v-else>
									{{ comDetailInfo[key.propName] | fix2 }}
								</template>
							</div>

							<div
								v-if="key.bottom2"
								:class="[parseFloat(comDetailInfo[key.bottom2]) < 0 ? 'item-green' : 'item-red']"
								style="font-size: 16px; vertical-align: text-bottom; line-height: 16px; height: 16px"
							>
								{{ comDetailInfo[key.bottom2] | fix2p }}
								<!-- <template v-if="key.propName === 'rate'">
                      {{comDetailInfo[key.propName] |  fix2p }}
                    </template>
                    <template v-else>
                    {{comDetailInfo[key.propName] |  fix2 }}
                    </template> -->
							</div>
						</div>
						<img v-if="key.img" :src="parseFloat(comDetailInfo[key.propName]) < 0 ? greenImg : redImg" />
					</div>
				</div>

				<div style="display: flex; margin-top: 20px">
					<el-button type="primary" @click="changeFund">调整持仓</el-button>
					<!-- <el-button type="primary" @click="goChangeCombination">模型优化持仓</el-button> -->
				</div>
			</div>
		</div>
		<el-dialog :title="current" width="30%" :rows="4" :visible.sync="dialogVisible">
			<div>
				<el-input type="textarea" v-model="changeDescription" placeholder="请输入不调仓说明"></el-input>
			</div>
			<div slot="footer">
				<el-button @click="dialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="writeDescription">确 定</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
// import {
//   getCombinationHolding,
//   getCombinationDescription,
//   postCombinationDescription,
//   putCombinationDescription
// } from '@/api/pages/combination/index.js';
// import { getComQuaList } from '@/api/pages/portfolio/index.js';
// import { alphaGo } from '@/assets/js/alpha_type.js';
import { refreshHoldingDetails, addCombinationLabel } from '@/api/pages/tkAnalysis/portfolio.js';
import assetDetails from './assetDetails.vue';
import VCharts from 'vue-echarts';
export default {
	components: { 'v-chart': VCharts, assetDetails },
	props: {
		comDetailInfo: {
			type: Object,
			default() {
				return {};
			}
		},
		endDate: {
			type: String,
			default: ''
		}
	},
	filters: {
		fix2p(value) {
			return value !== '' &&
				value != '--' &&
				value != '- -' &&
				JSON.stringify(value) != '[]' &&
				JSON.stringify(value) != '{}' &&
				value != 'NAN' &&
				value != 'nan' &&
				value !== undefined &&
				value !== null
				? (Number(value) * 100).toFixed(2) + '%'
				: '--';
		},
		fix2(value) {
			return value !== '' &&
				value != '--' &&
				value != '- -' &&
				JSON.stringify(value) != '[]' &&
				JSON.stringify(value) != '{}' &&
				value != 'NAN' &&
				value != 'nan' &&
				value !== undefined &&
				value !== null
				? Number(value).toFixed(2)
				: '--';
		},
		fixY(value) {
			return value &&
				value != '' &&
				value != '--' &&
				value != '- -' &&
				JSON.stringify(value) != '[]' &&
				JSON.stringify(value) != '{}' &&
				value != 'NAN' &&
				value != 'nan'
				? Number(value / 100000000).toFixed(2) + '亿'
				: '--';
		}
	},

	watch: {
		comDetailInfo: {
			handler(newVal, oldVal) {
				this.labelList = newVal.label || [];
			},
			deep: true,
			immediate: true
		}
	},
	data() {
		//这里存放数据
		return {
			inputVisible: false,
			inputValue: '',
			showDetails: false,
			id: '',
			labelList: [],
			tableData: [],
			tableDataAll: [],
			greenImg: require('@/assets/img/tkdesign/Group_1133_green.png'),
			redImg: require('@/assets/img/tkdesign/Group_1133.png'),
			greenColor: 'linear-gradient(270deg, #E2F7E7 0%, rgba(226, 247, 231, 0.25) 100%)',
			redColor: 'linear-gradient(270deg, #FFECE7 0%, rgba(255, 236, 231, 0.25) 100%)',
			infoList: [
				{
					propName: 'rate',
					top: '日涨跌幅',
					showDate: true,
					bottom: '0.26%',
					img: require('@/assets/img/tkdesign/Group_1133.png'),
					backgroundColor: 'linear-gradient(270deg, #FFECE7 0%, rgba(255, 236, 231, 0.25) 100%)',
					bottomColor: 'rgba(207, 19, 34, 1)'
				},
				{
					propName: 'excess',
					top: '当日超额',
					showDate: true,
					bottom: '-13.04%',
					img: require('@/assets/img/tkdesign/Group_1133_green.png'),
					backgroundColor: 'linear-gradient(270deg, #E2F7E7 0%, rgba(226, 247, 231, 0.25) 100%)',
					bottomColor: 'rgba(56, 158, 13, 1)'
				},
				{
					propName: 'profitAndLoss',
					top: '当日盈亏(万)',
					bottom: '100000',
					bottom2: 'perProfitAndLoss',
					backgroundColor: 'linear-gradient(270deg, rgba(255, 145, 3, 0.2) 0%, #ecf5ff 100%)'
				},
				{
					propName: 'profitAndLossSum',
					top: '累计盈亏(万)',
					bottom: '100000',
					bottom2: 'perProfitAndLossSum',
					backgroundColor: 'linear-gradient(270deg, rgba(255, 145, 3, 0.2) 0%, #ecf5ff 100%)'
				},
				{
					propName: 'totalAssets',
					top: '总资产(万)',
					bottom: '1023.00',
					backgroundColor: 'linear-gradient(270deg, rgba(255, 145, 3, 0.2) 0%, #ecf5ff 100%)'
				},
				{
					propName: 'position',
					top: '持仓市值(万)',
					bottom: '9.00',
					backgroundColor: 'linear-gradient(270deg, rgba(255, 145, 3, 0.2) 0%, #ecf5ff 100%)'
				}
			],
			pageSIze: '10',
			money: 0,
			nameCom: '慧捕基组合1',
			createdName: '慧捕基',
			createdTime: '2022-01-01',
			description: '',
			loading: false,
			loadingTable: false,
			dialogVisible: false,
			changeDescription: '',
			dataList: [],
			info: {},
			options: [],
			desList: [],
			time: '',
			option: {},
			current: ''
		};
	},
	//方法集合
	methods: {
		handleClose(tag) {
			this.handleInputConfirm(tag);
		},
		showInput() {
			this.inputVisible = true;
			this.$nextTick((_) => {
				this.$refs.saveTagInput.$refs.input.focus();
			});
		},

		async handleInputConfirm(val) {
			this.loading = true;
			let inputValue = this.inputValue.trim();
			let labelList = JSON.parse(JSON.stringify(this.labelList));
			if (typeof val === 'number') {
				labelList.splice(val, 1);
			} else if (inputValue) {
				labelList.push(inputValue);
			}

			if (labelList.length !== this.labelList.length) {
				let { mtycode, mtymessage, data } = await addCombinationLabel({
					combinationId: this.comDetailInfo.combinationId,
					label: labelList.join(',')
				});
				if (mtycode != '200') {
					this.$message.error(mtymessage);
					return;
				}
				this.labelList = data.label;
			}
			this.inputVisible = false;
			this.inputValue = '';
			this.loading = false;
		},
		goChangeCombination() {
			this.$router.push({
				path: '/combinationStrategySteps',
				query: {
					active: 1,
					listItemInfo: JSON.stringify(this.comDetailInfo),
					isEditPage: true
				}
			});
		},
		updatePort() {
			this.$emit('updatePortOut');
		},
		async changeFund() {
			this.loading = true;
			let { mtycode, mtymessage } = await refreshHoldingDetails({ combinationId: this.comDetailInfo.combinationId });
			this.loading = false;

			if (mtycode != '200') {
				this.$message.error(mtymessage);
				return;
			}
			this.showDetails = true;
		},
		goback() {
			this.$router.push('/portfolioSelf');
		},
		// 获取不调仓说明列表
		async getCombinationDescription(type) {
			/* let data = await getCombinationDescription({ combination_id: this.info.code });
      if (data?.mtycode == 200) {
        this.desList = data?.data;
        if (type) {
          this.filterChartData();
        } */
			// }
		},
		// 添加不调仓说明
		async postCombinationDescription() {
			/* let data = await postCombinationDescription({
        combination_id: this.info.code,
        description: this.changeDescription,
        date: this.current
      });
      if (data?.mtycode == 200) {
        this.$message.success('添加成功');
      }
      this.dialogVisible = false;
      this.getCombinationDescription(1); */
			this.changeDescription = '';
		},
		// 修改不调仓说明
		async putCombinationDescription() {
			/*  let data = await putCombinationDescription({
         combination_id: this.info.code,
         description: this.changeDescription,
         date: this.current
       });
       if (data?.mtycode == 200) {
         this.$message.success('修改成功');
       }
       this.dialogVisible = false;
       this.getCombinationDescription(1); */
			this.changeDescription = '';
		},
		handleSizeChange(val) {
			this.pageSIze = val;
			this.currentPage = 1;
			this.handleCurrentChange(1);
		},
		handleCurrentChange(val) {
			this.tableData = this.tableDataAll.slice((val - 1) * this.pageSIze, val * this.pageSIze);
		},
		// 添加不调仓说明
		writeDescription() {
			if (this.changeDescription) {
				let index = this.desList.findIndex((item) => {
					return item.date == this.current;
				});
				if (index == -1) {
					this.postCombinationDescription();
				} else {
					this.putCombinationDescription();
				}
			}
		},
		// 查看基金详情
		goDetail(val) {
			// alphaGo(val.code, val.name, this.$route.path);
		},
		fix5(val) {
			return Number(val) ? val.toFixed(5) : '--';
		},
		fix2m(val) {
			if (val * 1) {
				// let val = value * 1 >= 0 ? value * 1 : value * -1;
				return val >= 10 ** 4 || val <= -1 * 10 ** 4
					? (val / 10 ** 4).toFixed(2) + '万元'
					: val >= 10 ** 8 || val <= -1 * 10 ** 8
					? (val / 10 ** 8).toFixed(2) + '亿元'
					: (val * 1).toFixed(2) + '元';
			} else {
				return '--';
			}
			// return (Number(val) ? val.toFixed(2) : '--') + '元';
		},
		fix2p(val) {
			return val * 100 ? (val * 100).toFixed(2) + '%' : '--';
		},
		// 万
		fix4(val) {
			return Number(val) ? (val / 10000).toFixed(2) : '--';
		},
		// 默认
		formatDefault(val) {
			return val;
		},
		getChangeTime(val) {
			if (this.current) {
				let index = this.options.indexOf(this.current);
				if (index == -1) {
					let i = this.desList.findIndex((item) => {
						return item.date == this.current;
					});
					if (i != -1) {
						this.changeDescription = this.desList[i].description;
					}
					this.dialogVisible = true;
				} else {
					this.time = this.current;
					this.getCombinationHolding();
					this.filterChartData();
				}
			}
		},
		async getCombinationHolding() {
			// this.loading = true;
			/* let data = await getCombinationHolding({ combination_id: this.info.code, date: this.time });
      if (data?.mtycode == 200) {
        this.description = data?.description;
        this.dataList = data?.data?.map((item) => {
          return {
            ...item,
            fluctuation: Number(item.rate * item.nav) ? item.rate * item.nav : '--', // 涨跌
            profit_loss: item.rate * item.totalmv, // 当日盈亏
            cumulative_profit_loss: item.cum_return * item.totalmv, // 累计盈亏
            now_nav: item.cum_return * item.totalmv + item.totalmv // 持仓金额
          };
        });
        this.$emit('getFundList', data?.data);
        this.money = data?.surplus_asset;
        this.description = data?.description; */
			// }
			this.loading = false;
		},
		filterChartData() {
			let xAxis = this.FUNC.generateDateList(this.moment(this.options?.[0]).format('YYYY-MM-DD'), this.moment().format('YYYY-MM-DD'));
			let series = [];
			xAxis.map((item, index) => {
				if (this.options.indexOf(item) == -1) {
					if (
						this.desList.findIndex((obj) => {
							return obj.date == item;
						}) == -1
					) {
						series.push({
							data: [
								{
									value: [item, 0],
									symbolSize: '6',
									itemStyle: {
										color: '#ffffff'
									},
									emphasis: {
										itemStyle: {
											color: '#4096ff'
										}
									}
								}
							],
							type: 'line'
						});
					} else {
						series.push({
							data: [
								{
									value: [item, 0],
									symbol: 'none'
									// symbolSize: '10',
									// itemStyle: {
									// 	color: '#4096ff'
									// },
									// emphasis: {
									// 	itemStyle: {
									// 		color: '#4096ff'
									// 	}
									// }
								}
							],
							type: 'line',
							markPoint: {
								data: [
									{
										type: 'max',
										name: 'Max',
										// symbol: 'path://M6.98831 11L0.926135 0.499999L13.0505 0.5L6.98831 11Z',
										symbolSize: 10,
										symbolOffset: [0, 0],
										itemStyle: {
											color: '#4096ff'
										},
										label: {
											show: true,
											formatter: item,
											color: 'transparent'
										}
									}
								]
							}
						});
					}
				} else {
					series.push({
						data: [{ value: [item, 0], symbol: 'none' }],
						type: 'line',
						markPoint: {
							emphasis: {
								disabled: false,
								label: {
									show: true,
									position: 'top',
									formatter: `调仓日期:${item}`,
									backgroundColor: 'rgba(0, 0, 0, 0.45)',
									borderRadius: 4,
									padding: [0, 9],
									width: 164,
									height: 26,
									color: 'white',
									lineHeight: 32,
									align: 'center',
									fontFamily: 'PingFang',
									fontStyle: 'normal',
									fontWeight: 400,
									fontSize: '12px'
								}
							},
							data: [
								{
									type: 'max',
									name: 'Max',
									symbol: 'path://M6.98831 11L0.926135 0.499999L13.0505 0.5L6.98831 11Z',
									symbolSize: 14,
									symbolOffset: [0, '-50%'],
									itemStyle: {
										color: item == this.time ? 'red' : '#4096ff'
									},
									label: {
										show: true,
										formatter: item,
										color: 'transparent'
									}
								}
							]
						}
					});
				}
			});
			this.option = {
				xAxis: {
					type: 'category',
					data: xAxis,
					axisTick: { show: false },
					axisLine: { symbol: ['none', 'arrow'], symbolSize: [7, 7] },
					axisLabel: { fontSize: 12 }
				},
				tooltip: {
					trigger: 'axis',
					formatter: (val) => {
						this.current = val?.[0].name;
						if (this.options.indexOf(val?.[0].name) == -1) {
							let index = this.desList.findIndex((obj) => {
								return obj.date == val?.[0].name;
							});
							if (index == -1) {
								return `${val?.[0].name}<br />点击设置不调仓说明`;
								// return `<div style="width:200px">${val?.[0].name}<br />点击设置不调仓说明</div>`;
							} else {
								// return `<div style="width:200px;height:100px">${val?.[0].name}:<br />${this.desList[index]?.description}`;
								return `${val?.[0].name}:<br />${this.desList[index]?.description}`;
							}
						}
					}
				},
				grid: {
					left: '8px',
					right: '8px',
					bottom: '0',
					containLabel: true
				},
				yAxis: {
					type: 'value',
					show: false,
					min: 0
				},
				series
			};
		},
		async getHoldList() {
			/* let data = await getComQuaList({ combination_id: this.info.code });
      if (data?.mtycode == 200) {
        this.options = data?.data;
        this.time = data?.data[data?.data.length - 1];
      }*/
			this.getCombinationHolding();
			this.filterChartData();
		},
		createPrintWord() {
			let list = [
				{
					label: '证券代码',
					value: 'code'
				},
				{
					label: '证券简称',
					value: 'name'
				},
				{
					label: '最新价',
					value: 'nav'
				},
				{
					label: '涨跌',
					value: 'fluctuation', // rate*nav
					format: 'fix4'
				},
				{
					label: '涨跌幅',
					format: 'fix2p',
					value: 'rate'
				},
				{
					label: '持仓数量',
					value: 'share',
					format: 'fix2m'
				},
				{
					label: '持仓金额',
					value: 'now_nav',
					format: 'fix2m'
				},
				{
					label: '买入金额',
					value: 'totalmv',
					format: 'fix2m'
				},
				{
					label: '权重',
					format: 'fix2p',
					value: 'weight'
				},
				{
					label: '当日盈亏',
					format: 'fix2m',
					value: 'profit_loss' // rate*totalmv
				},
				{
					label: '累计盈亏',
					format: 'fix2m',
					value: 'cumulative_profit_loss' // cum_return * totalmv
				},
				{
					label: '持有收益率',
					format: 'fix2p',
					value: 'cum_return'
				},

				{
					label: '成本价格',
					value: 'begin_nav'
				},
				{
					label: '末次买入时间',
					value: 'max_date'
				},
				{
					label: '基金说明',
					value: 'description'
				}
			];
			return [
				...this.$exportWord.exportPortfolioInfo(this.info, this.time),
				...this.$exportWord.exportDescripe(`调仓说明:${this.description}`),
				...this.$exportWord.exportTable(list, this.dataList, '', true)
			];
		}
	}
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
.button-new-tag {
	margin-left: 10px;
	height: 24px;
	line-height: 22px;
	padding-top: 0;
	padding-bottom: 0;
	padding-left: 8px;
	padding-right: 8px;
}
.input-new-tag {
	width: 90px;
	margin-left: 10px;
	height: 24px;
	line-height: 22px;
	vertical-align: bottom;
	::v-deep .el-input__inner {
		height: 24px;
		line-height: 22px;
	}
}
.comDetailPage {
	// padding-bottom: 24px;
	background: #f9f5f9;
}
.portfolio-tag {
	border: 1px;
	color: rgba(24, 144, 255, 1);
	border: 1px solid rgba(145, 213, 255, 1);
	background: rgba(145, 213, 255, 0.2);
	::v-deep .el-tag__close {
		color: rgba(24, 144, 255, 1);
		top: 1px;
	}

	::v-deep .el-tag__close::before:hover {
		background: rgba(145, 213, 255, 0.2);
	}
	::v-deep .el-tag__close:hover {
		color: rgba(24, 144, 255, 1);
		background-color: rgba(145, 213, 255, 0.2);
	}
}
.info-item {
	flex: 1;
	height: 80px;
	border-radius: 2px;
	position: relative;
	padding: 8px;
	display: flex;
	align-items: flex-start;
	justify-content: space-between;
	flex-direction: column;
	img {
		position: absolute;
		width: 47px;
		height: 46px;
		top: -50%;
		right: 20px;
		transform: translateY(58px);
	}
	.item-top {
		font-family: PingFang SC;
		font-size: 14px;
		font-weight: 400;
		line-height: 22px;
		letter-spacing: 0em;
		text-align: left;
		color: rgba(0, 0, 0, 0.45);
	}
	.item-bottom {
		font-family: Helvetica Neue;
		font-size: 28px;
		font-weight: 500;
		line-height: 28px;
		height: 28px;
		letter-spacing: 0em;
		text-align: left;
		color: rgba(0, 0, 0, 0.85);
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: flex-end;
	}
	.item-red {
		color: rgba(207, 19, 34, 1);
	}
	.item-green {
		color: rgba(56, 158, 13, 1);
	}
}
// .info-item:nth-child(1){

//   // background: linear-gradient(270deg, #FFECE7 0%, rgba(255, 236, 231, 0.25) 100%);
//   .item-bottom{
//     color: rgba(207, 19, 34, 1);

//   }

// }
</style>
