<template>
	<div class="chart_one" v-loading="loading">
		<div style="display: flex; align-items: center; justify-content: space-between; position: relative" class="clearfix">
			<div class="title" style="flex: 1; text-align: left">
				<span>滚动胜率</span
				><el-tooltip class="item" effect="dark" :content="EXPLAIN.performancePage['滚动胜率']" placement="right-start">
					<svg width="14" height="14" viewBox="0 0 14 14" fill="none">
						<path
							fill-rule="evenodd"
							clip-rule="evenodd"
							d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
							fill="black"
							fill-opacity="0.45"
						/>
					</svg>
				</el-tooltip>
			</div>
			<div>
				<span style="font-size: 14px">比较基准选择：</span>
				<el-select v-model="benchmarkvalue" @change="changeBenchmarkValue" placeholder="请选择比较基准">
					<el-option v-for="item in benchmarkoptions" :key="item.index" :label="item.name" :value="item.index"> </el-option>
				</el-select>
				<el-button class="print_show" icon="el-icon-document-delete" @click="exportExcel" style="margin-left: 8px">导出Excel</el-button>
			</div>
		</div>
		<el-table :data="fourdata" class="table" style="margin-top: 24px" ref="multipleTable" header-cell-class-name="table-header">
			<el-table-column v-for="item in column" :key="item.value" align="gotoleft" :prop="item.value" :label="item.label">
				<template #header>
					<long-table-popover-chart
						v-if="item.popover"
						:data="formatTableData()"
						date_key="hold_length"
						:data_key="item.value"
						:show_name="item.label"
					>
						<span>{{ item.label }}</span>
					</long-table-popover-chart>
					<span v-else>{{ item.label }}</span>
				</template>
				<template slot-scope="{ row }">
					<span>{{ item.format ? item.format(row[item.value]) : row[item.value] }}</span>
				</template>
			</el-table-column>
			<template slot="empty">
				<el-empty image-size="160"></el-empty>
			</template>
		</el-table>
	</div>
</template>

<script>
import { exportTitle, exportTable, exportDescripe } from '@/utils/exportWord.js';
import { filter_json_to_excel } from '@/utils/exportExcel.js';
// 滚动胜率
export default {
	name: 'holdingPressure',
	data() {
		return {
			benchmarkoptions: [],
			benchmarkvalue: '',
			loading: true,
			column: [
				{ label: '持有期', value: 'hold_length', popover: false },
				{ label: '基金胜', value: 'wins', popover: true },
				{ label: '基准胜', value: 'loses', popover: true },
				{ label: '总比较数', value: 'comparison' },
				{ label: '基金胜率', value: 'prob_win', popover: true, format: this.fix2p },
				{ label: '基准胜率', value: 'prob_lose', popover: true, format: this.fix2p }
			],
			postData: {},
			fourdata: []
		};
	},
	methods: {
		// 获取基准列表
		getBenchmarkList(data) {
			this.benchmarkoptions = data;
			this.benchmarkvalue = this.benchmarkoptions?.[0]?.index;
			this.postData.benchmark = this.benchmarkvalue;
			this.$emit('resolveFather', this.postData);
		},
		// 获取数据
		getData(data) {
			this.loading = false;
			this.fourdata = data;
		},
		// 监听基准变化
		changeBenchmarkValue() {
			this.loading = true;
			this.postData.benchmark = this.benchmarkvalue;
			this.$emit('resolveFather', this.postData);
		},
		formatTableData() {
			let data = [];
			this.fourdata.map((item) => {
				let obj = { ...item };
				for (const key in item) {
					let format = this.column.find((obj) => {
						return obj.value == key;
					})?.format;
					if (format) {
						let val = format(item[key]);
						obj[key] = typeof val == 'string' ? (val.includes('%') ? val?.split('%')?.[0] * 1 : !isNaN(val) ? val * 1 : val) : val;
					}
				}
				data.push(obj);
			});
			return data;
		},
		fix2p(val) {
			return val == '--' ? val : !isNaN(val) ? Number(val * 100)?.toFixed(2) + '%' : '--';
		},
		// 导出为Excel
		exportExcel() {
			let list = [
				{ label: '持有期', value: 'hold_length', fill: 'header' },
				{ label: '基金胜', value: 'wins' },
				{ label: '基准胜', value: 'loses' },
				{ label: '总比较数', value: 'comparison' },
				{ label: '基金胜率', value: 'prob_win', format: 'fix2p' },
				{ label: '基准胜率', value: 'prob_lose', format: 'fix2p' }
			];
			filter_json_to_excel(list, this.fourdata, '滚动胜率');
		},
		// 导出
		createPrintWord() {
			let list = [
				{ label: '持有期', value: 'hold_length', fill: 'header' },
				{ label: '基金胜', value: 'wins' },
				{ label: '基准胜', value: 'loses' },
				{ label: '总比较数', value: 'comparison' },
				{ label: '基金胜率', value: 'prob_win', format: 'fix2p' },
				{ label: '基准胜率', value: 'prob_lose', format: 'fix2p' }
			];
			let benchmarkName = this.benchmarkoptions?.filter((item) => {
				return item.index == this.benchmarkvalue;
			})?.[0]?.name;
			let text = '对比基准为：' + benchmarkName;
			if (this.fourdata.length) {
				return [...exportTitle('滚动胜率'), ...exportDescripe(text), ...exportTable(list, this.fourdata, '', true)];
			} else {
				return [];
			}
		}
	}
};
</script>

<style></style>
