<template>
  <div class="returns">
    <div style="display: flex; align-items: center; width: 100%; position: relative; justify-content: space-between">
      <div style="display: flex; align-items: center">
        <div class="TitltCompare">共同持股分析</div>
      </div>
      <div style="text-align: right">
        <el-select v-model="stockCode"
                   filterable
                   prefix-icon="el-icon-search"
                   placeholder="选择相同持有股票">
          <el-option v-for="stock of listfundsanme"
                     :key="stock.code"
                     :label="stock.name"
                     :value="stock.code"></el-option>
        </el-select>
      </div>
    </div>

    <div v-loading="loading"
         style="page-break-inside: avoid; text-align: left">
      <v-chart ref="sameholdstock"
               v-loading="empty1"
               autoresize
               element-loading-text="未找到相同个股"
               element-loading-spinner="el-icon-document-delete"
               element-loading-background="rgba(239, 239, 239, 0.5)"
               style="page-break-inside: avoid; width: 100%; height: 400px"
               :options="optionpbroe"
               @legendselectchanged="legendselectchanged"
               @datazoom="datazoom"></v-chart>
    </div>
  </div>
</template>

<script>
import { FundStockHoldList, ManagerStockHoldList } from '@/api/pages/tools/compare.js';
import { FundAllHoldStocks, ManagerHoldSameStocksDetail } from '@/api/pages/tools/compare.js';
// import { getFundOrBase } from '@/api/pages/components/yejiheader.js';
// import { getAllDate } from '@/utils/getfulldate.js';
import VCharts from 'vue-echarts';
export default {
  props: {
    comparetype: {
      type: String,
      default: 'manager' //fund
    },
    id: {
      type: String,
      default: '30189741,30441407'
    },
    type: {
      type: String,
      default: 'equity'
    },
    name: {
      type: String,
      default: '萧楠,胡昕炜'
    }
  },
  //import引入的组件需要注入到对象中才能使用
  components: { 'v-chart': VCharts },
  data () {
    //这里存放数据
    return {
      showdetailchoose: false,
      fund_hold: [],
      empty1: false,
      stockCode: '',
      stockName: '',
      options: '',
      loading: false,
      optionpbroe: {},
      baseperson: '',
      // namebench: '沪深300',
      baseindexlist: [],
      basepersonfund: [],
      listfundsanme: [],
      alldata: null,
      datazoomObj: {
        start: 0,
        end: 100
      },
      selected: null
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {
    stockCode (val) {
      this.empty1 = false;
      this.listfundsanme.forEach((item) => {
        if (item.code == val) {
          this.stockName = item.name;
        }
      });
      this.generateOption();
    }
  },
  //方法集合
  methods: {
    datazoom (val) {
      this.datazoomObj = { start: val.start, end: val.end };
    },
    legendselectchanged (val) {
      this.selected = val.selected;
    },
    async getdata () {
      // console.log('sss');
      Object.assign(this.$data, this.$options.data());
      this.loading = true;
      this.empty1 = false;
      await this.searchpeople();
      this.generateOption();
    },
    // 获取基准
    async searchpeople () {
      // this.loading = false;
      let data;
      if (this.comparetype == 'fund') {
        data = await FundStockHoldList({
          fund_code: this.id,
          fund_name: this.name,
          type: this.type
        });
      } else {
        data = await ManagerStockHoldList({
          manager_code: this.id,
          manager_name: this.name,
          type: this.type
        });
      }

      if (data) {
        // //console.log(data);
        // //console.log('sjjjjsssyy%%')
        this.listfundsanme = data.data;
        if (this.listfundsanme.length > 0) {
          this.stockCode = this.listfundsanme[0].code;
          this.stockName = this.listfundsanme[0].name;
        } else {
          this.empty1 = true;
          this.loading = false;
        }
      } else {
        this.loading = false;
        this.empty1 = true;
      }
    },
    async generateOption () {
      let data;
      this.loading = true;
      if (this.FUNC.isEmpty(this.stockCode)) {
        if (this.comparetype == 'fund') {
          data = await FundAllHoldStocks({ fund_code: this.id, fund_name: this.name, type: this.type, stock_code: this.stockCode });
        } else {
          data = await ManagerHoldSameStocksDetail({
            manager_code: this.id,
            manager_name: this.name,
            type: this.type,
            stock_code: this.stockCode
          });
        }
        if (data) {
          if (JSON.stringify(data.data) == '{}' || JSON.stringify(data.data) == '[]' || JSON.stringify(data.data) == '') {
            this.empty1 = true;
          } else {
            let resRet = data.data.ret;
            let resStockData = data.data.stock_data;
            let nameKey = this.comparetype == 'fund' ? 'fund_name' : 'manager_name';
            let multipleWeight = this.comparetype == 'fund' ? 1 : 100;

            let seriess = [];
            let dateDayList = [], // 每日日期
              dateQuarterList = [], // 季度日期
              cumList = [], // 每日值-累计收益
              stockDataObj = {}; // 季度值-权重 名称: [值]

            // 获取最小最大季度
            let minQ = resStockData[0].yearqtr;
            let maxQ = resStockData[0].yearqtr;
            resStockData.forEach((item) => {
              minQ = minQ < item.yearqtr ? minQ : item.yearqtr;
              maxQ = maxQ > item.yearqtr ? maxQ : item.yearqtr;
            });

            // 生成连续季度日期
            dateQuarterList = this.FUNC.dateGenerateQuarterList(minQ, maxQ, 'quarter');
            // 初始化stockDataObj对象
            this.name.split(',').forEach((name) => {
              stockDataObj[name] = Array.from({ length: dateQuarterList.length }, (item) => (item = null));
            });
            // 生成stockDataObj数据
            resStockData.forEach((item) => {
              let i = dateQuarterList.indexOf(item.yearqtr);
              stockDataObj[item[nameKey]][i] = item.weight * multipleWeight;
            });

            for (let name in stockDataObj) {
              seriess.push({
                name: name,
                type: 'bar',
                data: stockDataObj[name]
              });
            }

            // 生成连续日期数据
            dateDayList = this.FUNC.generateDateList(
              this.FUNC.returnQuarter(minQ, 'start', 'quarter'),
              this.FUNC.returnQuarter(maxQ, 'end', 'quarter')
            );
            // 根据每日日期生成"每日值"
            let date = resRet.date.slice(),
              cum = resRet.cum_return.slice();
            let curDate = date.shift(),
              preCum = null,
              curCum = cum.shift();

            dateDayList.forEach((item) => {
              if (item == curDate) {
                cumList.push(Number(curCum * 100).toFixed(2));
                preCum = curCum;
                curDate = date.shift();
                curCum = cum.shift();
              } else if (item < curDate) {
                preCum === null ? cumList.push(null) : cumList.push(Number(preCum * 100).toFixed(2));
              } else if (item > resRet.date[resRet.date.length - 1]) {
                cumList.push(null);
              }
            });
            seriess.push({
              name: this.stockName,
              type: 'line',
              data: cumList,
              xAxisIndex: 1,
              yAxisIndex: 1
            });
            let that = this;
            this.optionpbroe = {
              color: [
                '#4096ff',
                '#4096ff',
                '#7388A9',
                '#6F80DD',
                '#4096FF',
                '#e040fb',
                '#ff3d00',
                '#929694',
                '#f4d1ff',
                '#e91e63',
                '#64dd17'
              ],
              tooltip: {
                trigger: 'axis',
                formatter: function (obj) {
                  let num1 = 2;
                  if (that.type == 'bond') {
                    num1 = 3;
                  }
                  if (obj.length - 1 > 0) {
                    let str = obj[0].axisValue + `<br />`;
                    for (let i = 0; i < obj.length - 1; i++) {
                      if (obj[i].seriesType == 'bar') {
                        str +=
                          `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:` +
                          obj[i].color +
                          `;"></span>` +
                          obj[i].seriesName +
                          ':' +
                          Number(obj[i].data).toFixed(num1) +
                          '%' +
                          `<br />`;
                      }
                    }
                    return str;
                  }
                }
                // axisPointer: {
                // 	type: 'shadow'
                // }
              },
              legend: {
                selected: this.selected
              },
              dataZoom: [
                {
                  type: 'slider',
                  show: true,
                  height: 14,
                  xAxisIndex: [0, 1],
                  bottom: 10,
                  borderColor: 'transparent',
                  backgroundColor: '#fafafa',
                  // 拖拽手柄样式 svg 路径
                  handleIcon:
                    'M512 512m-208 0a6.5 6.5 0 1 0 416 0 6.5 6.5 0 1 0-416 0Z M512 192C335.264 192 192 335.264 192 512c0 176.736 143.264 320 320 320s320-143.264 320-320C832 335.264 688.736 192 512 192zM512 800c-159.072 0-288-128.928-288-288 0-159.072 128.928-288 288-288s288 128.928 288 288C800 671.072 671.072 800 512 800z',
                  handleColor: '#aab6c6',
                  handleSize: 20,
                  handleStyle: {
                    borderColor: '#aab6c6',
                    shadowBlur: 4,
                    shadowOffsetX: 1,
                    shadowOffsetY: 1,
                    shadowColor: '#e5e5e5'
                  },
                  start: this.datazoomObj.start || 0,
                  end: this.datazoomObj.end || 100
                }
              ],
              grid: {
                left: '10px',
                right: '5%',
                bottom: '10%',
                top: '30px',
                containLabel: true
              },
              xAxis: [
                {
                  type: 'category',
                  boundaryGap: true,
                  data: dateQuarterList
                },
                {
                  show: false,
                  type: 'category',
                  boundaryGap: false,
                  data: dateDayList
                }
              ],
              yAxis: [
                {
                  name: '各持仓' + this.stockName + '权重',
                  axisLine: { show: false },
                  axisTick: { show: false },
                  splitLine: {
                    show: true,
                    lineStyle: {
                      type: 'dashed'
                    }
                  },
                  min: 0,
                  type: 'value',
                  axisLabel: {
                    formatter: function (obj) {
                      return obj + '%';
                    }
                  }
                },
                {
                  name: '累计收益率',
                  axisLine: { show: false },
                  axisTick: { show: false },
                  splitLine: {
                    show: false,
                    lineStyle: {
                      type: 'dashed'
                    }
                  },
                  min: (value) => {
                    return value.min;
                  },
                  type: 'value',
                  axisLabel: {
                    formatter: function (obj) {
                      return obj + '%';
                    }
                  }
                }
              ],
              series: seriess
            };
          }
        }
      } else {
      }
      this.loading = false;
    },
    createPrintWord () {
      let height = this.$refs['sameholdstock']?.$el.clientHeight;
      let width = this.$refs['sameholdstock']?.$el.clientWidth;
      let chart = this.$refs['sameholdstock'].getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      });
      return [
        ...this.$exportWord.exportFirstTitle('持仓分析'),
        ...this.$exportWord.exportTitle('共同持股分析'),
        ...this.$exportWord.exportChart(chart, { width, height })
      ];
    }
  }
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
</style>
