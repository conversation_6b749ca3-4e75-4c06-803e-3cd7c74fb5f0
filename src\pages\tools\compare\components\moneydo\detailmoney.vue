<!--  -->
<template>
  <div class="holdstock">
    <div style="display: flex; align-items: center; width: 100%; position: relative; justify-content: space-between">
      <div style="display: flex; align-items: center">
        <div class="TitltCompare">报告期债券资产配置</div>
      </div>
      <div>
        <el-select @change="change()"
                   v-model="value"
                   placeholder="请选择比较资产种类">
          <el-option v-for="item in options"
                     :key="item.value"
                     :label="item.label"
                     :value="item.value"> </el-option>
        </el-select>
      </div>
    </div>

    <div v-loading="loading"
         style="page-break-inside: avoid">
      <v-chart ref="detailmoney"
               v-loading="empty2"
               autoresize
               element-loading-text="暂无数据"
               element-loading-spinner="el-icon-document-delete"
               element-loading-background="rgba(239, 239, 239, 0.5)"
               style="page-break-inside: avoid; width: 100%; height: 400px"
               :options="optionpbroe2"></v-chart>
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { BondFundAllHold, BondHoldAsset } from '@/api/pages/tools/compare.js';
import VCharts from 'vue-echarts';
export default {
  props: {
    comparetype: {
      type: String,
      default: 'manager' //fund
    },
    id: {
      type: String,
      default: '30189741,30441407'
    },
    type: {
      type: String,
      default: 'equity'
    },
    name: {
      type: String,
      default: '萧楠,胡昕炜'
    }
  },
  //import引入的组件需要注入到对象中才能使用
  components: {
    'v-chart': VCharts
  },
  data () {
    //这里存放数据
    return {
      loading: false,
      empty2: false,
      showdetailchoose: false,
      options2: [],
      options: [],
      value: '',
      optionpbroe2: {}
    };
  },
  filters: {
    fix3 (value) {
      if (value == '--' || value == null || value == '') {
        return value;
      } else {
        return (value * 100).toFixed(2) + '%';
      }
    },
    fix2 (value) {
      return Number(value).toFixed(2) + '亿';
    }
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    change () {
      if (this.comparetype == 'manager') {
        // this.getmanager()
      } else {
        this.gefunddata();
      }
    },
    getdata () {
      Object.assign(this.$data, this.$options.data());
      this.loading = true;
      this.getlist();
    },
    async getlist () {
      this.options = [];
      let data = await BondFundAllHold({
        fund_code: this.id,
        type: this.type,
        fund_name: this.name
      });
      if (data) {
        // //console.log(data)
        // //console.log('xuanx ')
        for (let i = 0; i < data.data.length; i++) {
          this.options.push({
            label: data.data[i].bond_class,
            value: data.data[i].bond_class
          });
        }
        if (this.options.length > 0) {
          this.value = this.options[0].value;
        }
        this.change();
      }
    },
    async getmanager () {
      let data = await ManagerHoldRecent1y({ manager_code: this.id, type: this.type, manager_name: this.name });
      if (data) {
        // //console.log('chigu1y')
        // //console.log(data)
        this.yearqtrlist = [];
        if (data.data.length > 0) {
          for (let i = 0; i < data.data[0].length; i++) {
            if (this.yearqtrlist.indexOf(data.data[0][i].yearqtr) < 0) {
              {
                this.yearqtrlist.push(data.data[0][i].yearqtr);
              }
            }
          }
        }
        this.datatable = data.data;
        this.stock_holdcolumns = [];
        for (let i = 0; i < this.datatable.length; i++) {
          if (this.datatable[i].length > 0) {
            this.stock_holdcolumns.push({
              title: this.datatable[i][0].manager_name,
              children: [
                {
                  dataIndex: 'name' + i,
                  key: 'Name' + i,
                  title: '债券名称',
                  scopedSlots: {
                    customRender: 'Name' + i
                  }
                },
                {
                  dataIndex: 'weight' + i,
                  key: 'Weight' + i,
                  title: '权重',
                  sorter: (a, b) => a['weight' + i] - b['weight' + i],
                  scopedSlots: {
                    customRender: 'Weight' + i
                  }
                }
              ]
            });
          }
        }
        let max = this.yearqtrlist[0];
        for (let i = 0; i < this.yearqtrlist.length - 1; i++) {
          max = max < this.yearqtrlist[i + 1] ? yearqtrlist[i + 1] : max;
        }
        this.changgeyearqtr(max);
      }
    },
    async gefunddata () {
      this.empty2 = false;
      let data = await BondHoldAsset({
        fund_code: this.id,
        type: this.type,
        fund_name: this.name,
        name: this.value
      });
      this.loading = false;

      if (data) {
        if (JSON.stringify(data.data) == '{}' || JSON.stringify(data.data) == '[]' || JSON.stringify(data.data) == '') {
          this.empty2 = true;
        } else {
          // //console.log('chigu1y')
          // //console.log(data)
          let temn = '';
          for (let i = 0; i < this.options.length; i++) {
            if (this.options[i].value == this.value) temn = this.options[i].label;
          }
          let datelist = [];
          let seriess = [];
          for (let i = 0; i < data.data.length; i++) {
            data.data.sort((a, b) => {
              if (this.$route.query.name.split(',').indexOf(a.name) > this.$route.query.name.split(',').indexOf(b.name)) return 1;
              else return -1;
            });
            if (data.data[i].name == 'yearqtr') {
              datelist = data.data[i].value;
            } else {
              seriess.push({
                name: data.data[i].name,
                type: 'bar',
                data: data.data[i].value
              });
            }
          }

          this.optionpbroe2 = {
            title: {
              text: temn + '大类资产配置'
            },
            dataZoom: [
              {
                type: 'slider',
                show: true,
                height: 14,
                bottom: 10,
                borderColor: 'transparent',
                backgroundColor: '#fafafa',
                // 拖拽手柄样式 svg 路径
                handleIcon:
                  'M512 512m-208 0a6.5 6.5 0 1 0 416 0 6.5 6.5 0 1 0-416 0Z M512 192C335.264 192 192 335.264 192 512c0 176.736 143.264 320 320 320s320-143.264 320-320C832 335.264 688.736 192 512 192zM512 800c-159.072 0-288-128.928-288-288 0-159.072 128.928-288 288-288s288 128.928 288 288C800 671.072 671.072 800 512 800z',
                handleColor: '#aab6c6',
                handleSize: 20,
                handleStyle: {
                  borderColor: '#aab6c6',
                  shadowBlur: 4,
                  shadowOffsetX: 1,
                  shadowOffsetY: 1,
                  shadowColor: '#e5e5e5'
                },
                start: 0,
                end: 100
              }
            ],
            color: ['#4096ff', '#4096ff', '#7388A9', '#6F80DD', '#4096FF', '#929694', '#f4d1ff', '#e91e63', '#64dd17'],
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                // 坐标轴指示器，坐标轴触发有效
                type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
              },
              textStyle: {
                fontSize: 14
              },
              formatter: (params) => {
                //  //console.log(params)
                let str = `时间: ${params[0].axisValue} <br />`;
                for (let i = params.length - 1; i >= 0; i--) {
                  let dotHtml =
                    '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' +
                    params[i].color +
                    '"></span>';
                  str += dotHtml + `${params[i].seriesName}: ${Number(params[i].value).toFixed(2) + '%'}<br />`;
                }
                return str;
              }
            },
            legend: {},
            grid: {
              top: '12%',
              left: '10px',
              right: '4%',
              bottom: '10%',
              containLabel: true
            },

            xAxis: [
              {
                type: 'category',
                data: datelist,
                axisLabel: {
                  show: true,
                  textStyle: {
                    fontSize: 14
                  },
                  interval: 0,
                  rotate: 40
                }
              }
            ],
            yAxis: [
              {
                type: 'value',

                axisTick: {
                  show: false
                },
                splitLine: {
                  show: true,
                  lineStyle: {
                    type: 'dashed'
                  }
                },
                axisLabel: {
                  show: true,
                  textStyle: {
                    fontSize: 14
                  },
                  formatter (value) {
                    return value + '%';
                  }
                }
              }
            ],
            series: seriess
          };
        }
      }
    },
    createPrintWord () {
      let height = this.$refs['detailmoney']?.$el.clientHeight;
      let width = this.$refs['detailmoney']?.$el.clientWidth;
      let chart = this.$refs['detailmoney'].getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      });
      return [...this.$exportWord.exportTitle('报告期债券资产配置'), ...this.$exportWord.exportChart(chart, { width, height })];
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () { },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
</style>
