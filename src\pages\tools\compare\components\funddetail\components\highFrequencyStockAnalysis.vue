<template>
	<div class="high-frequency-stock-analysis">
		<div style="margin-bottom: 20px">
			<el-table :data="ana">
				<el-table-column prop="name" align="gotoleft" :label="type == 'manager' ? '名称' : '股票名称'"> </el-table-column>
				<el-table-column prop="swlevel1" align="gotoleft" label="申万一级"> </el-table-column>
				<el-table-column prop="swlevel2" align="gotoleft" label="申万二级"> </el-table-column>
				<el-table-column prop="swlevel3" align="gotoleft" label="申万三级"> </el-table-column>
				<el-table-column align="gotoleft" prop="cum_ret" label="区间涨跌幅">
					<template slot-scope="{ row }">
						<span>{{ (parseFloat(row.cum_ret) * 100).toFixed(3) + '%' }}</span>
					</template>
				</el-table-column>
				<el-table-column align="gotoleft" prop="cum_excess_ret" label="超指数表现">
					<template slot-scope="{ row }">
						<span>{{ (parseFloat(row.cum_excess_ret) * 100).toFixed(3) + '%' }}</span>
					</template>
				</el-table-column>
				<el-table-column align="gotoleft" prop="maxdd" label="最大回撤">
					<template slot-scope="{ row }">
						<span>{{ (parseFloat(row.maxdd) * 100).toFixed(3) + '%' }}</span>
					</template>
				</el-table-column>
			</el-table>
		</div>
		<div class="stock-trend" v-show="visible_gujia">
			<div class="stock-trend-header">
				<div class="header-title">股价走势特征</div>
				<el-radio-group class="select-weight-type" v-model="weightType">
					<el-radio-button label="stock_weight">占权益比</el-radio-button>
					<el-radio-button :label="type == 'manager' ? 'stock_weight_in_equity' : 'stock_weight_in_netasset'"> 占净资产比 </el-radio-button>
				</el-radio-group>
			</div>
			<!-- <v-chart class="stock-trend-chart" style="width: 100%; height: 40vh" :options="option1" /> -->
			<v-chart class="stock-trend-chart high_frequency_stock_h400" :options="optionStock" />
		</div>
		<div class="profit-valuation" v-show="visible_yingli">
			<div class="profit-valuation-header">
				<div class="header-title">盈利与估值</div>
			</div>
			<v-chart class="profit-valuation-chart high_frequency_stock_h400" :options="option2" />
		</div>
	</div>
</template>

<script>
import { fontSize } from '@/assets/js/echartsrpxtorem'; //注意路径
import axios from '@/api/index.js';
import VCharts from 'vue-echarts';
export default {
	data() {
		return {
			ana: [],
			option1: null,
			optionStock: null,
			option2: null,
			weightType: 'stock_weight',
			visible_yingli: true,
			visible_gujia: true
		};
	},
	props: {
		typemanager: {
			default: 'equity'
		},
		code: {
			require: true
		},
		sharesCode: {
			require: true
		},
		name: {
			require: true
		},
		type: {
			// fund-基金; manager-基金经理; fundGangGu-港股;
			require: true,
			default: 'fund'
		},
		fundtype: {
			require: false,
			default: 'equity'
		}
	},
	created() {},
	mounted() {
		this.handleSendRequest();
	},
	components: {
		'v-chart': VCharts
	},
	watch: {
		sharesCode() {
			this.handleSendRequest();
		},
		weightType() {
			this.handleSendRequest();
		}
	},
	methods: {
		// 获取原始数据
		handleSendRequest() {
			if (!this.type) return;
			let that = this;
			if (this.type == 'manager') {
				// 基金经理
				axios
					.get(this.$StyleUrl + '/stockmsg/?stock_code=' + this.sharesCode + '&manager_code=' + this.code + '&type=' + this.typemanager)
					.then((res) => {
						if (res.status == 200) {
							// do something
							that.ana = res.data.stock_day.index;
							// that.drawStockTrend(res.data.stock_day);
							that.drawStockTrendFeature(res.data.stock_day);
							that.drawProfitValuation(res.data.voluation);
						} else {
							console.error('err: ', res);
							that.visible_gujia = false;
						}
					})
					.catch((err) => {
						console.error('error: ', err);
						that.visible_gujia = false;
					});
			} else {
				// 基金
				axios
					.get(this.$StyleUrl + '/stock_/?stock_code=' + this.sharesCode + '&code=' + this.code + '&type=' + this.fundtype)
					.then((res) => {
						if (res.status == 200) {
							// do something
							that.ana = res.data.index;
							// that.drawStockTrend(res.data);
							that.drawStockTrendFeature(res.data);
						} else {
							console.error('err: ', res);
							that.visible_gujia = false;
						}
					})
					.catch((err) => {
						console.error('error: ', err);
						that.visible_gujia = false;
					});

				axios
					.get(this.$StyleUrl + '/voluation/?stock_code=' + this.sharesCode + '&code=' + this.code + '&type=' + this.fundtype)
					.then((res) => {
						if (res.status == 200) {
							that.drawProfitValuation(res.data);
						} else {
							console.error('err: ', res);
							that.visible_yingli = false;
						}
					})
					.catch((err) => {
						console.error('error: ', err);
						that.visible_yingli = false;
					});
			}
		},
		// 绘制股价走势特征-已废弃
		drawStockTrend(resData) {
			let linearr = [];
			let tempstring = '';
			let barArr = [];
			let multiplier = this.type == 'manager' ? 100 : 1;
			for (let i = 0; i < resData.stock_date.length; i++) {
				linearr.push({
					label: {
						formatter: '股票披露权重' + (Number(resData[this.weightType][i]) * multiplier).toFixed(2) + '%' + tempstring
					},
					xAxis: resData.stock_date[i],
					symbol: 'circle'
				});
				let transformToQuarterArr = this.FUNC.transformToQuarter(
					resData.stock_date[i],
					(Number(resData[this.weightType][i]) * multiplier).toFixed(2)
				);
				barArr.push(...transformToQuarterArr);
			}
			let datalinestock = resData.date;
			let stockdatas = resData.bench;
			let stockdatasret = resData.ret;
			for (let j = 0; j < resData.stock_date.length; j++) {
				if (resData.date.indexOf(resData.stock_date[j]) < 0) {
					for (let i = 1; i < resData.date.length; i++) {
						if (resData.date[i - 1] < resData.stock_date[j] && resData.stock_date[j] < resData.date[i]) {
							datalinestock.splice(i, 0, resData.stock_date[j]);
							stockdatas.splice(i, 0, resData.bench[i - 1]);
							stockdatasret.splice(i, 0, resData.ret[i - 1]);
						}
					}
				}
			}
			stockdatas = stockdatas.map((item) => (item * 100).toFixed(2));
			stockdatasret = stockdatasret.map((item) => (item * 100).toFixed(2));

			this.ana = resData.index; //高频股票申万123
			let quarterEndList = this.FUNC.returnQuarterEndDayList(datalinestock);

			// //console.log('quarterEndList: ', quarterEndList);
			this.option1 = {
				color: ['#409eff', '#c23531', '#4096ff'],
				// title: {
				// 	text: '股价走势特征',
				// 	textStyle: {
				// 		fontSize: fontSize(16)
				// 	}
				// },
				tooltip: {
					textStyle: {
						fontSize: fontSize(14)
					},
					trigger: 'axis',
					formatter: (params) => {
						let str = `时间: ${params[0].axisValue} <br />`;
						for (let i = params.length - 1; i >= 0; i--) {
							if (params[i].value != undefined && params[i].value != null && params[i].value != '') {
								let dotHtml =
									'<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' +
									params[i].color +
									'"></span>';
								if (Array.isArray(params[i].value)) {
									str += dotHtml + `${params[i].seriesName}: ${params[i].value[1] + '%'}<br />`;
								} else {
									str += dotHtml + `${params[i].seriesName}: ${params[i].value + '%'}<br />`;
								}
							}
						}
						return str;
					}
				},
				dataZoom: {
					start: datalinestock.length > 2000 ? '60' : '0',
					end: 100
				},
				legend: {
					textStyle: {
						fontSize: fontSize(14)
					},
					data: ['超上证A指部分股价走势特征', '股价走势特征', '股票披露权重']
				},
				xAxis: {
					//月份
					nameTextStyle: {
						fontSize: fontSize(14)
					},
					axisTick: {
						show: false
					},
					axisLabel: {
						show: true,
						textStyle: {
							fontSize: fontSize(10)
						},
						interval: 0,
						rotate: 30,
						formatter: function (value) {
							if (quarterEndList.includes(value)) {
								return value;
							} else {
								return ' ';
							}
						}
					},
					data: datalinestock,
					type: 'category'
				},
				yAxis: [
					{
						//该项在相应月份的数值
						nameTextStyle: {
							fontSize: fontSize(14)
						},
						axisLabel: {
							show: true,
							textStyle: {
								fontSize: fontSize(14)
							}
						},
						name: '收益率(%)',
						type: 'value',
						scale: true
					},
					{
						// 该项在相应季度的数值
						nameTextStyle: {
							fontSize: fontSize(14)
						},
						axisLabel: {
							show: true,
							textStyle: {
								fontSize: fontSize(14)
							}
						},
						splitLine: {
							show: false
						},
						name: '股票披露权重(%)',
						type: 'value',
						scale: true,
						min: 0
					}
				],
				series: [
					{
						name: '超上证A指部分股价走势特征',
						type: 'line',
						symbol: 'none',
						data: stockdatas,
						yAxisIndex: 0
					},
					{
						name: '股价走势特征',
						type: 'line',
						symbol: 'none',
						data: stockdatasret,
						yAxisIndex: 0
					},
					{
						name: '股票披露权重',
						type: 'bar',
						symbol: 'none',
						data: barArr,
						yAxisIndex: 1,
						barCategoryGap: '0%'
					}
				]
			};
		},
		// 绘制股价走势特征-需求更改后
		drawStockTrendFeature(resData) {
			this.visible_gujia = true;
			// //console.log('resData: ', resData);
			let dateDayList = [], // 每日日期
				stockRetList = [], // 每日值-股价走势 ret
				dateQuarterList = [], // 季度日期
				stockWeightList = [], // 季度值-股票披露权重
				stockBenchList = [], // 每日值-A指股价走势 bench
				stock_date_q = resData.stock_date.map((item) => this.FUNC.returnQuarter(item));

			// 生成"连续日期"
			dateDayList = this.FUNC.generateDateList(
				this.FUNC.returnQuarter(resData.date[0], 'start'),
				this.FUNC.returnQuarter(resData.date[resData.date.length - 1], 'end')
			);

			// 根据连续日期生成"连续季度"
			dateQuarterList = this.FUNC.dateGenerateQuarterList(dateDayList[0], dateDayList[dateDayList.length - 1]);

			// 根据季度日期生成"季度值"-股票披露权重
			let multiplier = this.type == 'manager' ? 100 : 1;
			dateQuarterList.forEach((item) => {
				if (stock_date_q.includes(item)) {
					let index = stock_date_q.indexOf(item);
					let value = (resData[this.weightType][index] * multiplier).toFixed(2);
					stockWeightList.push(value);
				} else {
					stockWeightList.push(null);
				}
			});

			// 根据每日日期生成"每日值"
			let date = resData.date.slice(),
				ret = resData.ret.slice(),
				bench = resData.bench.slice();

			let curDate = date.shift(),
				preRet = null,
				preBench = null,
				curRet = ret.shift(),
				curBench = bench.shift();

			dateDayList.forEach((item) => {
				if (item == curDate) {
					stockRetList.push((curRet * 100).toFixed(2));
					stockBenchList.push((curBench * 100).toFixed(2));
					preRet = curRet;
					preBench = curBench;
					curDate = date.shift();
					curRet = ret.shift();
					curBench = bench.shift();
				} else if (item < curDate) {
					stockRetList.push((preRet * 100).toFixed(2));
					stockBenchList.push((preBench * 100).toFixed(2));
				} else if (item > resData.date[resData.date.length - 1]) {
					stockRetList.push(null);
					stockBenchList.push(null);
				}
			});
			// cnosle.log(dateDayList, stockRetList, stockBenchList, 's')

			/* 固定y轴刻度 */
			// 1.获取y轴数据中的最大最小值
			const miny0 = Math.min(...stockWeightList.filter((item) => !isNaN(parseFloat(item))));
			const maxy0 = Math.max(...stockWeightList.filter((item) => !isNaN(parseFloat(item))));
			const miny1 = Math.min(
				...stockBenchList.filter((item) => !isNaN(parseFloat(item))),
				...stockRetList.filter((item) => !isNaN(parseFloat(item)))
			);
			const maxy1 = Math.max(
				...stockBenchList.filter((item) => !isNaN(parseFloat(item))),
				...stockRetList.filter((item) => !isNaN(parseFloat(item)))
			);
			// 2.计算比例尺-ratio;
			// PS: (grid-最大最小值区间10%; +-grid是为了不让最大最小值顶格显示,y0保留2位小数是因为y0轴数值过小,会出现小于1的值)
			const ratio = (maxy0 - miny0) / (maxy1 - miny1);
			const y0Grid = (maxy0 - miny0) / 10;
			const y1Grid = (maxy1 - miny1) / 10;
			// 3.获取包含比例尺的最大最小值
			const y0Min = miny0 < miny1 * ratio ? miny0 : miny1 * ratio;
			const y0Max = maxy0 > maxy1 * ratio ? maxy0 : maxy1 * ratio;
			const y1Min = miny0 < miny1 * ratio ? miny0 / ratio : miny1;
			const y1Max = maxy0 > maxy1 * ratio ? maxy0 / ratio : maxy1;

			// 判断指数名称
			let indexName =
				this.type == 'manager'
					? '超上证A指部分股价走势特征'
					: /^(hkequity|equityhk)/.test(this.fundtype)
					? '恒生指数'
					: /(index|yenhance)$/.test(this.fundtype)
					? '被动指数型基金指数'
					: '超上证A指部分股价走势特征';

			this.optionStock = {
				color: ['#409eff', '#c23531', '#BDE1EC'],
				tooltip: {
					textStyle: {
						fontSize: fontSize(14)
					},
					trigger: 'axis',
					formatter: (params) => {
						let str = '';

						if (params.length >= 2 && params[0].componentSubType == 'bar') {
							str = `日期: ${params[1].axisValue} 季度: ${params[0].axisValue} <br />`;
						} else if (params.length == 1 && params[0].componentSubType == 'bar') {
							str = `季度: ${params[0].axisValue} <br />`;
						} else if (params.length == 1 && params[0].componentSubType == 'line') {
							str = `日期: ${params[0].axisValue} <br />`;
						}
						// let str = `日期: ${params[1].axisValue} 季度: ${params[0].axisValue} <br />`;

						for (let i = params.length - 1; i >= 0; i--) {
							if (params[i].value != undefined && params[i].value != null && params[i].value != '') {
								let dotHtml =
									'<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' +
									params[i].color +
									'"></span>';
								str += dotHtml + `${params[i].seriesName}: ${params[i].value + '%'}<br />`;
							}
						}
						return str;
					}
				},
				// dataZoom: {
				// 	start: datalinestock.length > 2000 ? '60' : '0',
				// 	end: 100
				// },
				legend: {
					textStyle: {
						fontSize: fontSize(14)
					},
					data: [indexName, '股价走势特征', '股票披露权重']
				},
				xAxis: [
					{
						// 季度
						nameTextStyle: {
							fontSize: fontSize(14)
						},
						axisTick: {
							show: true,
							inside: true,
							alignWithLabel: true
						},
						axisLabel: {
							show: true,
							textStyle: {
								fontSize: fontSize(10)
							},
							interval: 0,
							rotate: 90
						},
						data: dateQuarterList,
						type: 'category'
					},
					{
						//每日
						nameTextStyle: {
							fontSize: fontSize(14)
						},
						axisTick: {
							show: false
						},
						axisLabel: {
							show: false
						},
						data: dateDayList,
						type: 'category'
					}
				],
				yAxis: [
					{
						// 该项在相应季度的数值
						nameTextStyle: {
							fontSize: fontSize(14)
						},
						axisLabel: {
							show: true,
							textStyle: {
								fontSize: fontSize(14)
							}
						},
						name: '股票披露权重(%)',
						type: 'value',
						// min: 0
						scale: true,
						splitLine: {
							show: true,
							lineStyle: {
								type: 'dashed'
							}
						},
						min: (y0Min - y0Grid).toFixed(2),
						max: (y0Max + y0Grid).toFixed(2)
					},
					{
						//该项在相应每日的数值
						nameTextStyle: {
							fontSize: fontSize(14)
						},
						axisLabel: {
							show: true,
							textStyle: {
								fontSize: fontSize(14)
							}
						},
						name: '收益率(%)',
						type: 'value',
						scale: true,
						splitLine: {
							show: false
						}
						// min: (y1Min - y1Grid).toFixed(0),
						// max: (y1Max + y1Grid).toFixed(0)
					}
				],
				series: [
					{
						name: indexName,
						type: 'line',
						symbol: 'none',
						data: stockBenchList,
						yAxisIndex: 1,
						xAxisIndex: 1
					},
					{
						name: '股价走势特征',
						type: 'line',
						symbol: 'none',
						data: stockRetList,
						yAxisIndex: 1,
						xAxisIndex: 1
					},
					{
						name: '股票披露权重',
						type: 'bar',
						symbol: 'none',
						// barCategoryGap: '0%',
						barWidth: '20%',
						data: stockWeightList,
						yAxisIndex: 0,
						xAxisIndex: 0
					}
				]
			};
		},
		// 绘制盈利与估值
		drawProfitValuation(resData) {
			if (!this.FUNC.isValidObj(resData.date) || (!this.FUNC.isValidObj(resData.pe) && !this.FUNC.isValidObj(resData.roe))) {
				this.visible_yingli = false;
				return;
			}
			this.visible_yingli = true;
			this.option2 = {
				color: ['#409eff', '#c23531'],
				tooltip: {
					textStyle: {
						fontSize: fontSize(14)
					},
					trigger: 'axis'
				},
				xAxis: {
					//月份
					nameTextStyle: {
						fontSize: fontSize(14)
					},
					axisLabel: {
						show: true,
						textStyle: {
							fontSize: fontSize(14)
						}
					},
					data: resData.date,
					type: 'category'
				},
				yAxis: [
					{
						//该项在相应月份的数值
						axisLine: {
							lineStyle: {
								color: '#409eff'
							}
						},
						nameTextStyle: {
							fontSize: fontSize(14)
						},
						axisLabel: {
							show: true,
							textStyle: {
								fontSize: fontSize(14),
								color: '#409eff'
							}
						},
						type: 'value',
						scale: true,
						splitLine: {
							show: false
						}
					},
					{
						//该项在相应月份的数值
						axisLine: {
							lineStyle: {
								color: '#c23531'
							}
						},
						nameTextStyle: {
							fontSize: fontSize(14)
						},
						axisLabel: {
							show: true,
							textStyle: {
								fontSize: fontSize(14),
								color: '#c23531'
							}
						},
						type: 'value',
						scale: true,
						splitLine: {
							show: false
						}
					}
				],
				legend: {
					textStyle: {
						fontSize: fontSize(14)
					}
				},
				series: [
					{
						name: 'pe',
						symbol: 'none',
						type: 'line',
						data: resData.pe
					},
					{
						type: 'line',
						name: 'roe',
						symbol: 'none',
						yAxisIndex: 1,
						data: resData.roe
					}
				]
			};
		}
	}
};
</script>

<style scoped lang="scss">
.high-frequency-stock-analysis {
	.profit-valuation-header,
	.stock-trend-header {
		margin: 10px 0;
		display: flex;
		justify-content: space-between;
		.header-title {
			font-size: 16px;
			font-weight: bold;
		}
	}
}
.high_frequency_stock_h400 {
	width: 100%;
	height: 400px;
}
</style>
