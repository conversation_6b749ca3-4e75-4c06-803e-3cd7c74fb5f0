<!--  -->
<template>
	<div class="dialogFilerDX">
		<div style="width: 100%">
			<!-- 筛选范围插槽 -->


			<div style="display: flex; width: 100%">
				<div style="width: 250px; border: 1px solid rgba(217, 217, 217, 0.5);border-right: 1px solid #e9e9e9">
					<slot name="filter-range"></slot>
					<div class="filter-section-container">
						<div class="tool-header">筛选条件</div>
						<div class="tool-content">
							<el-scrollbar>
								<el-tree :data="data" :props="defaultProps" @node-click="handleNodeClick">
									<!-- <div slot-scope="{ node, data }">
										<el-tooltip class="item" effect="dark" :content="tooltipList[node.label]" placement="right">
											<div>{{ node.label }}</div>
										</el-tooltip>
									</div> -->
								</el-tree>
							</el-scrollbar>
						</div>
					</div>
				</div>
				<div style="flex: 19;">
					<div style="text-align: left; height: 100%">
						<div style="min-height: 602px; height: calc(100% - 75px);border: 1px solid rgba(217, 217, 217, 0.5);">
							<div>
								<!-- 动态数据部分 -->
								<div v-for="(item, index) in listSelect" :key="'n' + index">
									<div v-if="item.labelIndex == 'a'">
										<div class="boxItemDetail">
											<div class="titleContent">基础信息</div>
											<div v-for="(items, index1) in item.data" :key="index1" class="contentBoxFilter">
												<div v-if="items.typeCate == '2'" style="display: flex; align-items: center" class="contentItem">
													<fundCateOnly
														:is_range="items.is_range"
														:haveName="items.labelName"
														@fundCateOnlyChange="fundCateOnlyChange"
														:fundType="fundType"
														:ref="'fundCateOnly' + index + '_' + index1"
														:radios="radios"
														:indexFlag="index1"
														:baseIndexFlag="index"
														:dataX="items"
													></fundCateOnly>
												</div>
												<div v-else-if="items.typeCate == '9'" style="display: flex; align-items: center" class="contentItem">
													<nameSelect
														:is_range="items.is_range"
														:haveName="items.labelName"
														@nameChange="nameChange"
														:fundType="fundType"
														:ref="'nameChange' + index + '_' + index1"
														:field103Options="items.conditions"
														:indexFlag="index1"
														:baseIndexFlag="index"
														:dataX="items"
													></nameSelect>
												</div>
												<div v-else-if="items.typeCate == '3'" style="display: flex; align-items: center" class="contentItem">
													<holdPersonOnly
														:haveName="items.labelName"
														@fundCateOnlyChange="fundCateOnlyChange"
														:fundType="fundType"
														:ref="'holdPersonOnly' + index + '_' + index1"
														:field103Options="items.conditions"
														:indexFlag="index1"
														:baseIndexFlag="index"
														:dataX="items"
													></holdPersonOnly>
												</div>
												<div v-else-if="items.typeCate == '4'" style="display: flex; align-items: center" class="contentItem">
													<holdPersonOnly2
														:is_range="items.is_range"
														:haveName="items.labelName"
														@fundCateOnlyChange="fundCateOnlyChange2"
														:fundType="fundType"
														:ref="'holdPersonOnly2' + index + '_' + index1"
														:field103Options="items.conditions"
														:indexFlag="index1"
														:baseIndexFlag="index"
														:dataX="items"
													></holdPersonOnly2>
												</div>
												<div v-else-if="items.typeCate == '1'" style="display: flex; align-items: center" class="contentItem">
													<boxOnlyYSF
														:is_range="items.is_range"
														:haveName="items.labelName"
														:ref="'boxOnlyYSF' + index + '_' + index1"
														@boxOnlyYSFChange="boxOnlyYSFChange"
														:indexFlag="index1"
														:baseIndexFlag="index"
														:placeholder="
															items.labelName.indexOf('时长') != -1 || items.labelName.indexOf('经验') != -1
																? '年'
																: items.labelName == '可申购金额'
																? '万'
																: '亿'
														"
														:dataX="items"
													></boxOnlyYSF>
												</div>
												<div class="contentDel" @click="del(index, index1)">
													<svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
														<path
															d="M3.62476 1.87354H3.49976C3.56851 1.87354 3.62476 1.81729 3.62476 1.74854V1.87354H8.37476V1.74854C8.37476 1.81729 8.43101 1.87354 8.49976 1.87354H8.37476V2.99854H9.49976V1.74854C9.49976 1.19697 9.05132 0.748535 8.49976 0.748535H3.49976C2.94819 0.748535 2.49976 1.19697 2.49976 1.74854V2.99854H3.62476V1.87354ZM11.4998 2.99854H0.499756C0.223193 2.99854 -0.********* 3.22197 -0.********* 3.49854V3.99854C-0.********* 4.06729 0.0560059 4.12354 0.124756 4.12354H1.06851L1.45444 12.2954C1.47944 12.8282 1.92007 13.2485 2.45288 13.2485H9.54663C10.081 13.2485 10.5201 12.8298 10.5451 12.2954L10.931 4.12354H11.8748C11.9435 4.12354 11.9998 4.06729 11.9998 3.99854V3.49854C11.9998 3.22197 11.7763 2.99854 11.4998 2.99854ZM9.42632 12.1235H2.57319L2.19507 4.12354H9.80444L9.42632 12.1235Z"
															fill="black"
															fill-opacity="0.45"
														/>
													</svg>
												</div>
											</div>
										</div>
									</div>
									<div v-else-if="item.labelIndex == 'n1'">
										<div class="boxItemDetail">
											<div class="titleContent">最新仓位</div>
											<div v-for="(items, index1) in item.data" :key="index1" class="contentBoxFilter">
												<div v-if="items.typeCate == '41'" style="display: flex; align-items: center" class="contentItem">
													<boxOnlyYSF
														:is_range="items.is_range"
														:haveName="items.labelName"
														@boxOnlyYSFChange="boxOnlyYSFChange"
														:placeholder="items.labelName == '报告久期' || items.labelName == '估算久期' ? '年' : '%'"
														:fundType="fundType"
														:ref="'boxNameYSF' + index + '_' + index1"
														:indexFlag="index1"
														:baseIndexFlag="index"
														:dataX="items"
													></boxOnlyYSF>
												</div>
												<div v-else-if="items.typeCate == '1'" style="display: flex; align-items: center" class="contentItem">
													<holdPersonOnly
														:is_range="items.is_range"
														:haveName="items.labelName"
														@fundCateOnlyChange="fundCateOnlyChange"
														:fundType="fundType"
														:ref="'holdPersonOnly' + index + '_' + index1"
														:field103Options="items.conditions"
														:indexFlag="index1"
														:baseIndexFlag="index"
														:dataX="items"
													></holdPersonOnly>
												</div>
												<div class="contentDel" @click="del(index, index1)">
													<svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
														<path
															d="M3.62476 1.87354H3.49976C3.56851 1.87354 3.62476 1.81729 3.62476 1.74854V1.87354H8.37476V1.74854C8.37476 1.81729 8.43101 1.87354 8.49976 1.87354H8.37476V2.99854H9.49976V1.74854C9.49976 1.19697 9.05132 0.748535 8.49976 0.748535H3.49976C2.94819 0.748535 2.49976 1.19697 2.49976 1.74854V2.99854H3.62476V1.87354ZM11.4998 2.99854H0.499756C0.223193 2.99854 -0.********* 3.22197 -0.********* 3.49854V3.99854C-0.********* 4.06729 0.0560059 4.12354 0.124756 4.12354H1.06851L1.45444 12.2954C1.47944 12.8282 1.92007 13.2485 2.45288 13.2485H9.54663C10.081 13.2485 10.5201 12.8298 10.5451 12.2954L10.931 4.12354H11.8748C11.9435 4.12354 11.9998 4.06729 11.9998 3.99854V3.49854C11.9998 3.22197 11.7763 2.99854 11.4998 2.99854ZM9.42632 12.1235H2.57319L2.19507 4.12354H9.80444L9.42632 12.1235Z"
															fill="black"
															fill-opacity="0.45"
														/>
													</svg>
												</div>
											</div>
										</div>
									</div>
									<div v-else-if="item.labelIndex == 'b'">
										<div class="boxItemDetail">
											<div class="titleContent">费用及限制</div>
											<div v-for="(items, index1) in item.data" :key="index1" class="contentBoxFilter">
												<div style="display: flex; align-items: center" class="contentItem">
													<boxOnlyYSF
														:is_range="items.is_range"
														:haveName="items.labelName"
														:ref="'boxOnlyYSF' + index + '_' + index1"
														@boxOnlyYSFChange="boxOnlyYSFChange"
														:indexFlag="index1"
														:baseIndexFlag="index"
														placeholder="元"
														:dataX="items"
													></boxOnlyYSF>
												</div>
												<div class="contentDel" @click="del(index, index1)">
													<svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
														<path
															d="M3.62476 1.87354H3.49976C3.56851 1.87354 3.62476 1.81729 3.62476 1.74854V1.87354H8.37476V1.74854C8.37476 1.81729 8.43101 1.87354 8.49976 1.87354H8.37476V2.99854H9.49976V1.74854C9.49976 1.19697 9.05132 0.748535 8.49976 0.748535H3.49976C2.94819 0.748535 2.49976 1.19697 2.49976 1.74854V2.99854H3.62476V1.87354ZM11.4998 2.99854H0.499756C0.223193 2.99854 -0.********* 3.22197 -0.********* 3.49854V3.99854C-0.********* 4.06729 0.0560059 4.12354 0.124756 4.12354H1.06851L1.45444 12.2954C1.47944 12.8282 1.92007 13.2485 2.45288 13.2485H9.54663C10.081 13.2485 10.5201 12.8298 10.5451 12.2954L10.931 4.12354H11.8748C11.9435 4.12354 11.9998 4.06729 11.9998 3.99854V3.49854C11.9998 3.22197 11.7763 2.99854 11.4998 2.99854ZM9.42632 12.1235H2.57319L2.19507 4.12354H9.80444L9.42632 12.1235Z"
															fill="black"
															fill-opacity="0.45"
														/>
													</svg>
												</div>
											</div>
										</div>
									</div>
									<div v-else-if="item.labelIndex == 'd'">
										<div class="boxItemDetail">
											<div class="titleContent">可申购金额</div>
											<div v-for="(items, index1) in item.data" :key="index1" class="contentBoxFilter">
												<div style="display: flex; align-items: center" class="contentItem">
													<boxOnlyYSF
														:is_range="items.is_range"
														:ref="'boxOnlyYSF' + index + '_' + index1"
														@boxOnlyYSFChange="boxOnlyYSFChange"
														:indexFlag="index1"
														:baseIndexFlag="index"
														placeholder="万"
														:dataX="items"
													></boxOnlyYSF>
												</div>
												<div class="contentDel" @click="del(index, index1)">
													<svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
														<path
															d="M3.62476 1.87354H3.49976C3.56851 1.87354 3.62476 1.81729 3.62476 1.74854V1.87354H8.37476V1.74854C8.37476 1.81729 8.43101 1.87354 8.49976 1.87354H8.37476V2.99854H9.49976V1.74854C9.49976 1.19697 9.05132 0.748535 8.49976 0.748535H3.49976C2.94819 0.748535 2.49976 1.19697 2.49976 1.74854V2.99854H3.62476V1.87354ZM11.4998 2.99854H0.499756C0.223193 2.99854 -0.********* 3.22197 -0.********* 3.49854V3.99854C-0.********* 4.06729 0.0560059 4.12354 0.124756 4.12354H1.06851L1.45444 12.2954C1.47944 12.8282 1.92007 13.2485 2.45288 13.2485H9.54663C10.081 13.2485 10.5201 12.8298 10.5451 12.2954L10.931 4.12354H11.8748C11.9435 4.12354 11.9998 4.06729 11.9998 3.99854V3.49854C11.9998 3.22197 11.7763 2.99854 11.4998 2.99854ZM9.42632 12.1235H2.57319L2.19507 4.12354H9.80444L9.42632 12.1235Z"
															fill="black"
															fill-opacity="0.45"
														/>
													</svg>
												</div>
											</div>
										</div>
									</div>
									<div v-else-if="item.labelIndex == 'e'">
										<div class="boxItemDetail">
											<div class="titleContent">基金分类</div>
											<div v-for="(items, index1) in item.data" :key="index1" class="contentBoxFilter">
												<div style="display: flex; align-items: center" class="contentItem">
													<fundCateOnly
														:is_range="items.is_range"
														@fundCateOnlyChange="fundCateOnlyChange"
														:fundType="fundType"
														:ref="'fundCateOnly' + index + '_' + index1"
														:indexFlag="index1"
														:baseIndexFlag="index"
														:dataX="items"
													></fundCateOnly>
												</div>
												<div class="contentDel" @click="del(index, index1)">
													<svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
														<path
															d="M3.62476 1.87354H3.49976C3.56851 1.87354 3.62476 1.81729 3.62476 1.74854V1.87354H8.37476V1.74854C8.37476 1.81729 8.43101 1.87354 8.49976 1.87354H8.37476V2.99854H9.49976V1.74854C9.49976 1.19697 9.05132 0.748535 8.49976 0.748535H3.49976C2.94819 0.748535 2.49976 1.19697 2.49976 1.74854V2.99854H3.62476V1.87354ZM11.4998 2.99854H0.499756C0.223193 2.99854 -0.********* 3.22197 -0.********* 3.49854V3.99854C-0.********* 4.06729 0.0560059 4.12354 0.124756 4.12354H1.06851L1.45444 12.2954C1.47944 12.8282 1.92007 13.2485 2.45288 13.2485H9.54663C10.081 13.2485 10.5201 12.8298 10.5451 12.2954L10.931 4.12354H11.8748C11.9435 4.12354 11.9998 4.06729 11.9998 3.99854V3.49854C11.9998 3.22197 11.7763 2.99854 11.4998 2.99854ZM9.42632 12.1235H2.57319L2.19507 4.12354H9.80444L9.42632 12.1235Z"
															fill="black"
															fill-opacity="0.45"
														/>
													</svg>
												</div>
											</div>
										</div>
									</div>
									<div v-else-if="item.labelIndex == 'g'">
										<div class="boxItemDetail">
											<div class="titleContent">风险特征</div>
											<div v-for="(items, index1) in item.data" :key="index1" class="contentBoxFilter">
												<div style="display: flex; align-items: center" class="contentItem">
													<boxNameYSF
														:is_range="items.is_range"
														:haveName="items.labelName"
														@boxOnlyYSFNameChange="boxOnlyYSFNameChange"
														placeholder="前百分之,如10,表示TOP90%"
														:valueTypes="items"
														:fundType="fundType"
														:ref="'boxNameYSF' + index + '_' + index1"
														:indexFlag="index1"
														:optionsselect="optionsselect[items.labelName]"
														:baseIndexFlag="index"
														:indexList="indexList"
														:dataX="items"
													></boxNameYSF>
												</div>
												<div class="contentDel" @click="del(index, index1)">
													<svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
														<path
															d="M3.62476 1.87354H3.49976C3.56851 1.87354 3.62476 1.81729 3.62476 1.74854V1.87354H8.37476V1.74854C8.37476 1.81729 8.43101 1.87354 8.49976 1.87354H8.37476V2.99854H9.49976V1.74854C9.49976 1.19697 9.05132 0.748535 8.49976 0.748535H3.49976C2.94819 0.748535 2.49976 1.19697 2.49976 1.74854V2.99854H3.62476V1.87354ZM11.4998 2.99854H0.499756C0.223193 2.99854 -0.********* 3.22197 -0.********* 3.49854V3.99854C-0.********* 4.06729 0.0560059 4.12354 0.124756 4.12354H1.06851L1.45444 12.2954C1.47944 12.8282 1.92007 13.2485 2.45288 13.2485H9.54663C10.081 13.2485 10.5201 12.8298 10.5451 12.2954L10.931 4.12354H11.8748C11.9435 4.12354 11.9998 4.06729 11.9998 3.99854V3.49854C11.9998 3.22197 11.7763 2.99854 11.4998 2.99854ZM9.42632 12.1235H2.57319L2.19507 4.12354H9.80444L9.42632 12.1235Z"
															fill="black"
															fill-opacity="0.45"
														/>
													</svg>
												</div>
											</div>
										</div>
									</div>
									<div v-else-if="item.labelIndex == 'h'">
										<div class="boxItemDetail">
											<div class="titleContent">风险收益特征</div>
											<div v-for="(items, index1) in item.data" :key="index1" class="contentBoxFilter">
												<div style="display: flex; align-items: center" class="contentItem">
													<boxNameYSF
														:is_range="items.is_range"
														:valueTypes="items"
														:haveName="items.labelName"
														@boxOnlyYSFNameChange="boxOnlyYSFNameChange"
														placeholder="前百分之,如10,表示TOP90%"
														:optionsselect="optionsselect[items.labelName]"
														:fundType="fundType"
														:ref="'boxNameYSF' + index + '_' + index1"
														:indexFlag="index1"
														:baseIndexFlag="index"
														:indexList="indexList"
														:dataX="items"
													></boxNameYSF>
												</div>
												<div class="contentDel" @click="del(index, index1)">
													<svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
														<path
															d="M3.62476 1.87354H3.49976C3.56851 1.87354 3.62476 1.81729 3.62476 1.74854V1.87354H8.37476V1.74854C8.37476 1.81729 8.43101 1.87354 8.49976 1.87354H8.37476V2.99854H9.49976V1.74854C9.49976 1.19697 9.05132 0.748535 8.49976 0.748535H3.49976C2.94819 0.748535 2.49976 1.19697 2.49976 1.74854V2.99854H3.62476V1.87354ZM11.4998 2.99854H0.499756C0.223193 2.99854 -0.********* 3.22197 -0.********* 3.49854V3.99854C-0.********* 4.06729 0.0560059 4.12354 0.124756 4.12354H1.06851L1.45444 12.2954C1.47944 12.8282 1.92007 13.2485 2.45288 13.2485H9.54663C10.081 13.2485 10.5201 12.8298 10.5451 12.2954L10.931 4.12354H11.8748C11.9435 4.12354 11.9998 4.06729 11.9998 3.99854V3.49854C11.9998 3.22197 11.7763 2.99854 11.4998 2.99854ZM9.42632 12.1235H2.57319L2.19507 4.12354H9.80444L9.42632 12.1235Z"
															fill="black"
															fill-opacity="0.45"
														/>
													</svg>
												</div>
											</div>
										</div>
									</div>
									<div v-else-if="item.labelIndex == 'm'">
										<div class="boxItemDetail">
											<!-- <div class="titleContent">其他</div> -->
											<div v-for="(items, index1) in item.data" :key="index1" v-show="!items.is_range" class="contentBoxFilter">
												<div style="display: flex; align-items: center">
													<div v-if="items.labelName.indexOf('模式') !== -1" style="display: flex; align-items: center" class="contentItem">
														<industryBig
															:is_range="items.is_range"
															:dataIndustry="dataIndustry"
															:haveName="items.labelName"
															@industryThemeChange="industryThemeChange"
															:placeholder="items.labelName.indexOf('模式') !== -1 ? '输入50,即模式占比为50%' : '前百分之,如10,表示TOP90%'"
															:fundType="fundType"
															:ref="'buyMode' + index + '_' + index1"
															:indexFlag="index1"
															:baseIndexFlag="index"
															:dataX="items"
														></industryBig>
													</div>
													<div v-else style="display: flex; align-items: center" class="contentItem">
														<boxOnlyYSF
															:is_range="items.is_range"
															:haveName="items.labelName"
															@boxOnlyYSFChange="boxOnlyYSFChange"
															:placeholder="
																items.labelName == 'ROE' || items.labelName == '前十大集中度'
																	? items.labelName
																	: items.labelName == '胜率' || items.labelName == '赔率' || items.labelName == '行业超低配'
																	? items.labelName + '的分位'
																	: '前百分之,如10,表示TOP90%'
															"
															:fundType="fundType"
															:ref="'boxNameYSF' + index + '_' + index1"
															:indexFlag="index1"
															:baseIndexFlag="index"
															:dataX="items"
														></boxOnlyYSF>
													</div>
													<div class="contentDel" @click="del(index, index1)">
														<svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
															<path
																d="M3.62476 1.87354H3.49976C3.56851 1.87354 3.62476 1.81729 3.62476 1.74854V1.87354H8.37476V1.74854C8.37476 1.81729 8.43101 1.87354 8.49976 1.87354H8.37476V2.99854H9.49976V1.74854C9.49976 1.19697 9.05132 0.748535 8.49976 0.748535H3.49976C2.94819 0.748535 2.49976 1.19697 2.49976 1.74854V2.99854H3.62476V1.87354ZM11.4998 2.99854H0.499756C0.223193 2.99854 -0.********* 3.22197 -0.********* 3.49854V3.99854C-0.********* 4.06729 0.0560059 4.12354 0.124756 4.12354H1.06851L1.45444 12.2954C1.47944 12.8282 1.92007 13.2485 2.45288 13.2485H9.54663C10.081 13.2485 10.5201 12.8298 10.5451 12.2954L10.931 4.12354H11.8748C11.9435 4.12354 11.9998 4.06729 11.9998 3.99854V3.49854C11.9998 3.22197 11.7763 2.99854 11.4998 2.99854ZM9.42632 12.1235H2.57319L2.19507 4.12354H9.80444L9.42632 12.1235Z"
																fill="black"
																fill-opacity="0.45"
															/>
														</svg>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
								<!-- 写死的 -->
								<div style="border-bottom: 1px solid #e9e9e9" class="boxItemDetailM">
									<div class="titleContent">计算区间</div>
									<div class="contentBoxFilter2">
										<div style="display: flex; align-items: center; margin-bottom: 8px">
											<el-radio v-model="radio" label="latest">最近一期持仓</el-radio>
											<el-radio v-model="radio" label="3">最近3年</el-radio>
											<el-radio v-model="radio" label="6">最近6年</el-radio>
											<el-radio v-model="radio" label="created">成立以来</el-radio>
											<el-radio v-model="radio" label="radioSelf">
												<span style="margin-right: 8px">自定义</span>
												<!-- <el-input v-model="radioSelf" placeholder="年" style="width: 60px"></el-input> -->
												<el-date-picker
													v-model="radioSelf"
													type="daterange"
													value-format="yyyy-MM-dd"
													range-separator="-"
													start-placeholder="开始日期"
													end-placeholder="结束日期"
												>
												</el-date-picker>
											</el-radio>
										</div>
										<div class="contentDel"></div>
									</div>
								</div>
								<!-- 动态数据部分 -->
								<div v-for="(item, index) in listSelect" :key="'m' + index">
									<div v-if="item.labelIndex == 'n'">
										<div class="boxItemDetail">
											<div class="titleContent">权益资产约束</div>
											<div v-for="(items, index1) in item.data" :key="index1" class="contentBoxFilter">
												<div v-if="items.typeCate == '4'" style="display: flex; align-items: center" class="contentItem">
													<boxOnlyYSF
														:is_range="items.is_range"
														:haveName="items.labelName"
														@boxOnlyYSFChange="boxOnlyYSFChange"
														:placeholder="items.labelName == '报告久期' || items.labelName == '估算久期' ? '年' : '%'"
														:fundType="fundType"
														:ref="'boxNameYSF' + index + '_' + index1"
														:indexFlag="index1"
														:baseIndexFlag="index"
														:dataX="items"
													></boxOnlyYSF>
												</div>
												<div v-else-if="items.typeCate == '1'" style="display: flex; align-items: center" class="contentItem">
													<holdPersonOnly
														:is_range="items.is_range"
														:haveName="items.labelName"
														@fundCateOnlyChange="fundCateOnlyChange"
														:fundType="fundType"
														:ref="'holdPersonOnly' + index + '_' + index1"
														:field103Options="items.conditions"
														:indexFlag="index1"
														:baseIndexFlag="index"
														:dataX="items"
													></holdPersonOnly>
												</div>
												<div class="contentDel" @click="del(index, index1)">
													<svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
														<path
															d="M3.62476 1.87354H3.49976C3.56851 1.87354 3.62476 1.81729 3.62476 1.74854V1.87354H8.37476V1.74854C8.37476 1.81729 8.43101 1.87354 8.49976 1.87354H8.37476V2.99854H9.49976V1.74854C9.49976 1.19697 9.05132 0.748535 8.49976 0.748535H3.49976C2.94819 0.748535 2.49976 1.19697 2.49976 1.74854V2.99854H3.62476V1.87354ZM11.4998 2.99854H0.499756C0.223193 2.99854 -0.********* 3.22197 -0.********* 3.49854V3.99854C-0.********* 4.06729 0.0560059 4.12354 0.124756 4.12354H1.06851L1.45444 12.2954C1.47944 12.8282 1.92007 13.2485 2.45288 13.2485H9.54663C10.081 13.2485 10.5201 12.8298 10.5451 12.2954L10.931 4.12354H11.8748C11.9435 4.12354 11.9998 4.06729 11.9998 3.99854V3.49854C11.9998 3.22197 11.7763 2.99854 11.4998 2.99854ZM9.42632 12.1235H2.57319L2.19507 4.12354H9.80444L9.42632 12.1235Z"
															fill="black"
															fill-opacity="0.45"
														/>
													</svg>
												</div>
											</div>
										</div>
									</div>
									<div v-else-if="item.labelIndex == 'c'">
										<div class="boxItemDetail">
											<div class="titleContent">基金大类资产约束</div>
											<div v-for="(items, index1) in item.data" :key="index1" class="contentBoxFilter">
												<div style="display: flex; align-items: center" class="contentItem">
													<boxOnlyYSF
														:is_range="items.is_range"
														:haveName="items.labelName"
														:ref="'boxOnlyYSF' + index + '_' + index1"
														@boxOnlyYSFChange="boxOnlyYSFChange"
														:indexFlag="index1"
														:baseIndexFlag="index"
														placeholder="持仓占比"
														:dataX="items"
													></boxOnlyYSF>
												</div>
												<div class="contentDel" @click="del(index, index1)">
													<svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
														<path
															d="M3.62476 1.87354H3.49976C3.56851 1.87354 3.62476 1.81729 3.62476 1.74854V1.87354H8.37476V1.74854C8.37476 1.81729 8.43101 1.87354 8.49976 1.87354H8.37476V2.99854H9.49976V1.74854C9.49976 1.19697 9.05132 0.748535 8.49976 0.748535H3.49976C2.94819 0.748535 2.49976 1.19697 2.49976 1.74854V2.99854H3.62476V1.87354ZM11.4998 2.99854H0.499756C0.223193 2.99854 -0.********* 3.22197 -0.********* 3.49854V3.99854C-0.********* 4.06729 0.0560059 4.12354 0.124756 4.12354H1.06851L1.45444 12.2954C1.47944 12.8282 1.92007 13.2485 2.45288 13.2485H9.54663C10.081 13.2485 10.5201 12.8298 10.5451 12.2954L10.931 4.12354H11.8748C11.9435 4.12354 11.9998 4.06729 11.9998 3.99854V3.49854C11.9998 3.22197 11.7763 2.99854 11.4998 2.99854ZM9.42632 12.1235H2.57319L2.19507 4.12354H9.80444L9.42632 12.1235Z"
															fill="black"
															fill-opacity="0.45"
														/>
													</svg>
												</div>
											</div>
										</div>
									</div>
									<div v-else-if="item.labelIndex == 'o'">
										<div class="boxItemDetail">
											<div class="titleContent">债券券种约束</div>
											<div v-for="(items, index1) in item.data" :key="index1" class="contentBoxFilter">
												<div style="display: flex; align-items: center" class="contentItem">
													<boxOnlyYSF
														:is_range="items.is_range"
														:haveName="items.labelName"
														:ref="'boxOnlyYSF' + index + '_' + index1"
														@boxOnlyYSFChange="boxOnlyYSFChange"
														:indexFlag="index1"
														:baseIndexFlag="index"
														placeholder="持仓占比"
														:dataX="items"
													></boxOnlyYSF>
												</div>
												<div class="contentDel" @click="del(index, index1)">
													<svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
														<path
															d="M3.62476 1.87354H3.49976C3.56851 1.87354 3.62476 1.81729 3.62476 1.74854V1.87354H8.37476V1.74854C8.37476 1.81729 8.43101 1.87354 8.49976 1.87354H8.37476V2.99854H9.49976V1.74854C9.49976 1.19697 9.05132 0.748535 8.49976 0.748535H3.49976C2.94819 0.748535 2.49976 1.19697 2.49976 1.74854V2.99854H3.62476V1.87354ZM11.4998 2.99854H0.499756C0.223193 2.99854 -0.********* 3.22197 -0.********* 3.49854V3.99854C-0.********* 4.06729 0.0560059 4.12354 0.124756 4.12354H1.06851L1.45444 12.2954C1.47944 12.8282 1.92007 13.2485 2.45288 13.2485H9.54663C10.081 13.2485 10.5201 12.8298 10.5451 12.2954L10.931 4.12354H11.8748C11.9435 4.12354 11.9998 4.06729 11.9998 3.99854V3.49854C11.9998 3.22197 11.7763 2.99854 11.4998 2.99854ZM9.42632 12.1235H2.57319L2.19507 4.12354H9.80444L9.42632 12.1235Z"
															fill="black"
															fill-opacity="0.45"
														/>
													</svg>
												</div>
											</div>
										</div>
									</div>
									<div v-else-if="item.labelIndex == 'f'">
										<div class="boxItemDetail">
											<div class="titleContent">行业特征</div>
											<div v-for="(items, index1) in item.data" :key="index1" class="contentBoxFilter">
												<div v-if="items.labelName == '行业超低配'" style="display: flex; align-items: center" class="contentItem">
													<boxOnlyYSF
														:is_range="items.is_range"
														:haveName="items.labelName"
														@boxOnlyYSFChange="boxOnlyYSFChange"
														:placeholder="
															items.labelName == 'ROE' ||
															items.labelName == '胜率' ||
															items.labelName == '赔率' ||
															items.labelName == '行业超低配'
																? items.labelName + '的分位'
																: '前百分之,如10,表示TOP90%'
														"
														:fundType="fundType"
														:ref="'boxNameYSF' + index + '_' + index1"
														:indexFlag="index1"
														:baseIndexFlag="index"
														:dataX="items"
													></boxOnlyYSF>
												</div>

												<div v-else style="display: flex; align-items: center" class="contentItem">
													<industry-prosperity
														:is_range="items.is_range"
														:dataIndustry="dataIndustry"
														:haveName="items.labelName"
														@industryProsperityChange="industryProsperityChange"
														:placeholder="items.labelName == '行业轮动' ? '行业轮动变动量' : '持仓的景气行业占比'"
														:fundType="fundType"
														:ref="'industryBig' + index + '_' + index1"
														:indexFlag="index1"
														:baseIndexFlag="index"
														:dataX="items"
													></industry-prosperity>
												</div>
												<div class="contentDel" @click="del(index, index1)">
													<svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
														<path
															d="M3.62476 1.87354H3.49976C3.56851 1.87354 3.62476 1.81729 3.62476 1.74854V1.87354H8.37476V1.74854C8.37476 1.81729 8.43101 1.87354 8.49976 1.87354H8.37476V2.99854H9.49976V1.74854C9.49976 1.19697 9.05132 0.748535 8.49976 0.748535H3.49976C2.94819 0.748535 2.49976 1.19697 2.49976 1.74854V2.99854H3.62476V1.87354ZM11.4998 2.99854H0.499756C0.223193 2.99854 -0.********* 3.22197 -0.********* 3.49854V3.99854C-0.********* 4.06729 0.0560059 4.12354 0.124756 4.12354H1.06851L1.45444 12.2954C1.47944 12.8282 1.92007 13.2485 2.45288 13.2485H9.54663C10.081 13.2485 10.5201 12.8298 10.5451 12.2954L10.931 4.12354H11.8748C11.9435 4.12354 11.9998 4.06729 11.9998 3.99854V3.49854C11.9998 3.22197 11.7763 2.99854 11.4998 2.99854ZM9.42632 12.1235H2.57319L2.19507 4.12354H9.80444L9.42632 12.1235Z"
															fill="black"
															fill-opacity="0.45"
														/>
													</svg>
												</div>
											</div>
										</div>
									</div>
									<div v-else-if="item.labelIndex == 'i'">
										<div class="boxItemDetail">
											<div class="titleContent">风格偏好</div>
											<div v-for="(items, index1) in item.data" :key="index1" class="contentBoxFilter">
												<div style="display: flex; align-items: center" class="contentItem">
													<boxCzJzOnly
														:is_range="items.is_range"
														:haveName="items.labelName"
														@boxCzJzOnlyChange="boxCzJzOnlyChange"
														placeholder="百分之"
														:fundType="fundType"
														:ref="'boxCzJzOnly' + index + '_' + index1"
														:indexFlag="index1"
														:baseIndexFlag="index"
														:dataX="items"
													></boxCzJzOnly>
												</div>
												<!-- <div v-if="items.labelName=='估值'" style="display:flex;align-items:center" class='contentItem'>
                                                      <boxCzJzOnly2 :haveName='items.labelName' @boxCzJzOnlyChange='boxCzJzOnlyChange' placeholder='前百分之,如10,表示TOP90%' :fundType="fundType" :ref="'boxCzJzOnly2'+index+'_'+index1"  :indexFlag='index1' :baseIndexFlag='index'    :dataX="items" ></boxCzJzOnly2>
                                                    </div> -->
												<div class="contentDel" @click="del(index, index1)">
													<svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
														<path
															d="M3.62476 1.87354H3.49976C3.56851 1.87354 3.62476 1.81729 3.62476 1.74854V1.87354H8.37476V1.74854C8.37476 1.81729 8.43101 1.87354 8.49976 1.87354H8.37476V2.99854H9.49976V1.74854C9.49976 1.19697 9.05132 0.748535 8.49976 0.748535H3.49976C2.94819 0.748535 2.49976 1.19697 2.49976 1.74854V2.99854H3.62476V1.87354ZM11.4998 2.99854H0.499756C0.223193 2.99854 -0.********* 3.22197 -0.********* 3.49854V3.99854C-0.********* 4.06729 0.0560059 4.12354 0.124756 4.12354H1.06851L1.45444 12.2954C1.47944 12.8282 1.92007 13.2485 2.45288 13.2485H9.54663C10.081 13.2485 10.5201 12.8298 10.5451 12.2954L10.931 4.12354H11.8748C11.9435 4.12354 11.9998 4.06729 11.9998 3.99854V3.49854C11.9998 3.22197 11.7763 2.99854 11.4998 2.99854ZM9.42632 12.1235H2.57319L2.19507 4.12354H9.80444L9.42632 12.1235Z"
															fill="black"
															fill-opacity="0.45"
														/>
													</svg>
												</div>
											</div>
										</div>
									</div>
									<div v-else-if="item.labelIndex == 'j'">
										<div class="boxItemDetail">
											<div class="titleContent" style="display: flex; align-items: center">
												<div>
													行业判断(全持仓)<span style="font-size: 14px; color: #c8c8cc" v-if="radios == 'equityhk'"
														>&nbsp;大行业及申万行业计算范围为A股+港股，恒生行业计算范围为港股</span
													>
												</div>
												<!-- <div style='margin-left:15px'>
                                                         <el-checkbox @change="changeMerge" v-model="checkedMerge">是否需要同时满足</el-checkbox>
                                                    </div> -->
											</div>
											<div v-for="(items, index1) in item.data" :key="index1" class="contentBoxFilter">
												<div
													v-if="items.labelName != '大行业' && items.labelName != '景气行业占比' && items.labelName.indexOf('多行业') < 0"
													style="display: flex; align-items: center"
													class="contentItem"
												>
													<industryTheme
														:is_range="items.is_range"
														:dataIndustry="dataIndustry"
														:haveName="items.labelName"
														@industryThemeChange="industryThemeChange"
														placeholder="仓位占比"
														:fundType="fundType"
														:ref="'industryTheme' + index + '_' + index1"
														:indexFlag="index1"
														:baseIndexFlag="index"
														:dataX="items"
													></industryTheme>
												</div>
												<div
													v-if="items.labelName == '大行业' || items.labelName == '景气行业占比'"
													style="display: flex; align-items: center"
													class="contentItem"
												>
													<industryBig
														:is_range="items.is_range"
														:dataIndustry="dataIndustry"
														:haveName="items.labelName"
														@industryThemeChange="industryThemeChange"
														placeholder="仓位占比"
														:fundType="fundType"
														:ref="'industryBig' + index + '_' + index1"
														:indexFlag="index1"
														:baseIndexFlag="index"
														:dataX="items"
													></industryBig>
												</div>
												<div v-if="items.labelName.indexOf('多行业') >= 0" style="display: flex; align-items: center" class="contentItem">
													<industrySum
														:is_range="items.is_range"
														:dataIndustry="dataIndustry"
														:haveName="items.labelName"
														@industrySumChange="industrySumChange"
														placeholder="仓位占比"
														:fundType="fundType"
														:ref="'industrySum' + index + '_' + index1"
														:indexFlag="index1"
														:baseIndexFlag="index"
														:dataX="items"
													></industrySum>
												</div>
												<div class="contentDel" @click="del(index, index1)">
													<svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
														<path
															d="M3.62476 1.87354H3.49976C3.56851 1.87354 3.62476 1.81729 3.62476 1.74854V1.87354H8.37476V1.74854C8.37476 1.81729 8.43101 1.87354 8.49976 1.87354H8.37476V2.99854H9.49976V1.74854C9.49976 1.19697 9.05132 0.748535 8.49976 0.748535H3.49976C2.94819 0.748535 2.49976 1.19697 2.49976 1.74854V2.99854H3.62476V1.87354ZM11.4998 2.99854H0.499756C0.223193 2.99854 -0.********* 3.22197 -0.********* 3.49854V3.99854C-0.********* 4.06729 0.0560059 4.12354 0.124756 4.12354H1.06851L1.45444 12.2954C1.47944 12.8282 1.92007 13.2485 2.45288 13.2485H9.54663C10.081 13.2485 10.5201 12.8298 10.5451 12.2954L10.931 4.12354H11.8748C11.9435 4.12354 11.9998 4.06729 11.9998 3.99854V3.49854C11.9998 3.22197 11.7763 2.99854 11.4998 2.99854ZM9.42632 12.1235H2.57319L2.19507 4.12354H9.80444L9.42632 12.1235Z"
															fill="black"
															fill-opacity="0.45"
														/>
													</svg>
												</div>
											</div>
										</div>
									</div>
									<div v-else-if="item.labelIndex == 'j1'">
										<div class="boxItemDetail">
											<div class="titleContent" style="display: flex; align-items: center">
												<div>行业判断(重仓)</div>
												<div>
													<span v-if="radios == 'equityhk'" style="font-size: 14px; color: #c8c8cc"
														>&nbsp;大行业及申万行业计算范围为A股+港股，恒生行业计算范围为港股</span
													>
												</div>
												<!-- <div style='margin-left:15px'>
                                                         <el-checkbox @change="changeMerge" v-model="checkedMerge">是否需要同时满足</el-checkbox>
                                                    </div> -->
											</div>
											<div v-for="(items, index1) in item.data" :key="index1" class="contentBoxFilter">
												<div
													v-if="items.labelName != '大行业' && items.labelName != '景气行业占比' && items.labelName.indexOf('多行业') < 0"
													style="display: flex; align-items: center"
													class="contentItem"
												>
													<industryTheme
														:is_range="items.is_range"
														:dataIndustry="dataIndustry"
														:haveName="items.labelName"
														@industryThemeChange="industryThemeChange"
														placeholder="仓位占比"
														:fundType="fundType"
														:ref="'industryTheme' + index + '_' + index1"
														:indexFlag="index1"
														:baseIndexFlag="index"
														:dataX="items"
													></industryTheme>
												</div>
												<div
													v-if="items.labelName == '大行业' || items.labelName == '景气行业占比'"
													style="display: flex; align-items: center"
													class="contentItem"
												>
													<industryBig
														:is_range="items.is_range"
														:dataIndustry="dataIndustry"
														:haveName="items.labelName"
														@industryThemeChange="industryThemeChange"
														placeholder="仓位占比"
														:fundType="fundType"
														:ref="'industryBig' + index + '_' + index1"
														:indexFlag="index1"
														:baseIndexFlag="index"
														:dataX="items"
													></industryBig>
												</div>
												<div v-if="items.labelName.indexOf('多行业') >= 0" style="display: flex; align-items: center" class="contentItem">
													<industrySum
														:is_range="items.is_range"
														:dataIndustry="dataIndustry"
														:haveName="items.labelName"
														@industrySumChange="industrySumChange"
														placeholder="仓位占比"
														:fundType="fundType"
														:ref="'industrySum' + index + '_' + index1"
														:indexFlag="index1"
														:baseIndexFlag="index"
														:dataX="items"
													></industrySum>
												</div>
												<div class="contentDel" @click="del(index, index1)">
													<svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
														<path
															d="M3.62476 1.87354H3.49976C3.56851 1.87354 3.62476 1.81729 3.62476 1.74854V1.87354H8.37476V1.74854C8.37476 1.81729 8.43101 1.87354 8.49976 1.87354H8.37476V2.99854H9.49976V1.74854C9.49976 1.19697 9.05132 0.748535 8.49976 0.748535H3.49976C2.94819 0.748535 2.49976 1.19697 2.49976 1.74854V2.99854H3.62476V1.87354ZM11.4998 2.99854H0.499756C0.223193 2.99854 -0.********* 3.22197 -0.********* 3.49854V3.99854C-0.********* 4.06729 0.0560059 4.12354 0.124756 4.12354H1.06851L1.45444 12.2954C1.47944 12.8282 1.92007 13.2485 2.45288 13.2485H9.54663C10.081 13.2485 10.5201 12.8298 10.5451 12.2954L10.931 4.12354H11.8748C11.9435 4.12354 11.9998 4.06729 11.9998 3.99854V3.49854C11.9998 3.22197 11.7763 2.99854 11.4998 2.99854ZM9.42632 12.1235H2.57319L2.19507 4.12354H9.80444L9.42632 12.1235Z"
															fill="black"
															fill-opacity="0.45"
														/>
													</svg>
												</div>
											</div>
										</div>
									</div>
									<div v-else-if="item.labelIndex == 'k'">
										<div class="boxItemDetail">
											<div class="titleContent">主题判断(全持仓)</div>
											<div v-for="(items, index1) in item.data" :key="index1" class="contentBoxFilter">
												<div style="display: flex; align-items: center" class="contentItem">
													<industryTheme
														:is_range="items.is_range"
														:dataIndustry="dataIndustry"
														:haveName="items.labelName"
														@industryThemeChange="industryThemeChange"
														placeholder="仓位占比"
														:fundType="fundType"
														:ref="'industryTheme' + index + '_' + index1"
														:indexFlag="index1"
														:baseIndexFlag="index"
														:dataX="items"
													></industryTheme>
												</div>
												<div class="contentDel" @click="del(index, index1)">
													<svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
														<path
															d="M3.62476 1.87354H3.49976C3.56851 1.87354 3.62476 1.81729 3.62476 1.74854V1.87354H8.37476V1.74854C8.37476 1.81729 8.43101 1.87354 8.49976 1.87354H8.37476V2.99854H9.49976V1.74854C9.49976 1.19697 9.05132 0.748535 8.49976 0.748535H3.49976C2.94819 0.748535 2.49976 1.19697 2.49976 1.74854V2.99854H3.62476V1.87354ZM11.4998 2.99854H0.499756C0.223193 2.99854 -0.********* 3.22197 -0.********* 3.49854V3.99854C-0.********* 4.06729 0.0560059 4.12354 0.124756 4.12354H1.06851L1.45444 12.2954C1.47944 12.8282 1.92007 13.2485 2.45288 13.2485H9.54663C10.081 13.2485 10.5201 12.8298 10.5451 12.2954L10.931 4.12354H11.8748C11.9435 4.12354 11.9998 4.06729 11.9998 3.99854V3.49854C11.9998 3.22197 11.7763 2.99854 11.4998 2.99854ZM9.42632 12.1235H2.57319L2.19507 4.12354H9.80444L9.42632 12.1235Z"
															fill="black"
															fill-opacity="0.45"
														/>
													</svg>
												</div>
											</div>
										</div>
									</div>
									<div v-else-if="item.labelIndex == 'k1'">
										<div class="boxItemDetail">
											<div class="titleContent">主题判断(重仓)</div>
											<div v-for="(items, index1) in item.data" :key="index1" class="contentBoxFilter">
												<div style="display: flex; align-items: center" class="contentItem">
													<industryTheme
														:is_range="items.is_range"
														:dataIndustry="dataIndustry"
														:haveName="items.labelName"
														@industryThemeChange="industryThemeChange"
														placeholder="仓位占比"
														:fundType="fundType"
														:ref="'industryTheme' + index + '_' + index1"
														:indexFlag="index1"
														:baseIndexFlag="index"
														:dataX="items"
													></industryTheme>
												</div>
												<div class="contentDel" @click="del(index, index1)">
													<svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
														<path
															d="M3.62476 1.87354H3.49976C3.56851 1.87354 3.62476 1.81729 3.62476 1.74854V1.87354H8.37476V1.74854C8.37476 1.81729 8.43101 1.87354 8.49976 1.87354H8.37476V2.99854H9.49976V1.74854C9.49976 1.19697 9.05132 0.748535 8.49976 0.748535H3.49976C2.94819 0.748535 2.49976 1.19697 2.49976 1.74854V2.99854H3.62476V1.87354ZM11.4998 2.99854H0.499756C0.223193 2.99854 -0.********* 3.22197 -0.********* 3.49854V3.99854C-0.********* 4.06729 0.0560059 4.12354 0.124756 4.12354H1.06851L1.45444 12.2954C1.47944 12.8282 1.92007 13.2485 2.45288 13.2485H9.54663C10.081 13.2485 10.5201 12.8298 10.5451 12.2954L10.931 4.12354H11.8748C11.9435 4.12354 11.9998 4.06729 11.9998 3.99854V3.49854C11.9998 3.22197 11.7763 2.99854 11.4998 2.99854ZM9.42632 12.1235H2.57319L2.19507 4.12354H9.80444L9.42632 12.1235Z"
															fill="black"
															fill-opacity="0.45"
														/>
													</svg>
												</div>
											</div>
										</div>
									</div>
									<div v-else-if="item.labelIndex == 'l'">
										<div class="boxItemDetail">
											<div class="titleContent">指数持仓重叠(全持仓)</div>
											<div v-for="(items, index1) in item.data" :key="index1" class="contentBoxFilter">
												<div style="display: flex; align-items: center" class="contentItem">
													<indexOnly
														:is_range="items.is_range"
														:dataIndustry="dataIndustry"
														:haveName="items.labelName"
														@indexOnlyChange="indexOnlyChange"
														placeholder="仓位占比"
														:fundType="fundType"
														:ref="'indexOnly' + index + '_' + index1"
														:indexFlag="index1"
														:baseIndexFlag="index"
														:dataX="items"
													></indexOnly>
												</div>
												<div class="contentDel" @click="del(index, index1)">
													<svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
														<path
															d="M3.62476 1.87354H3.49976C3.56851 1.87354 3.62476 1.81729 3.62476 1.74854V1.87354H8.37476V1.74854C8.37476 1.81729 8.43101 1.87354 8.49976 1.87354H8.37476V2.99854H9.49976V1.74854C9.49976 1.19697 9.05132 0.748535 8.49976 0.748535H3.49976C2.94819 0.748535 2.49976 1.19697 2.49976 1.74854V2.99854H3.62476V1.87354ZM11.4998 2.99854H0.499756C0.223193 2.99854 -0.********* 3.22197 -0.********* 3.49854V3.99854C-0.********* 4.06729 0.0560059 4.12354 0.124756 4.12354H1.06851L1.45444 12.2954C1.47944 12.8282 1.92007 13.2485 2.45288 13.2485H9.54663C10.081 13.2485 10.5201 12.8298 10.5451 12.2954L10.931 4.12354H11.8748C11.9435 4.12354 11.9998 4.06729 11.9998 3.99854V3.49854C11.9998 3.22197 11.7763 2.99854 11.4998 2.99854ZM9.42632 12.1235H2.57319L2.19507 4.12354H9.80444L9.42632 12.1235Z"
															fill="black"
															fill-opacity="0.45"
														/>
													</svg>
												</div>
											</div>
										</div>
									</div>
									<div v-else-if="item.labelIndex == 'l1'">
										<div class="boxItemDetail">
											<div class="titleContent">指数持仓重叠(重仓)</div>
											<div v-for="(items, index1) in item.data" :key="index1" class="contentBoxFilter">
												<div style="display: flex; align-items: center" class="contentItem">
													<indexOnly
														:is_range="items.is_range"
														:dataIndustry="dataIndustry"
														:haveName="items.labelName"
														@indexOnlyChange="indexOnlyChange"
														placeholder="仓位占比"
														:fundType="fundType"
														:ref="'indexOnly' + index + '_' + index1"
														:indexFlag="index1"
														:baseIndexFlag="index"
														:dataX="items"
													></indexOnly>
												</div>
												<div class="contentDel" @click="del(index, index1)">
													<svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
														<path
															d="M3.62476 1.87354H3.49976C3.56851 1.87354 3.62476 1.81729 3.62476 1.74854V1.87354H8.37476V1.74854C8.37476 1.81729 8.43101 1.87354 8.49976 1.87354H8.37476V2.99854H9.49976V1.74854C9.49976 1.19697 9.05132 0.748535 8.49976 0.748535H3.49976C2.94819 0.748535 2.49976 1.19697 2.49976 1.74854V2.99854H3.62476V1.87354ZM11.4998 2.99854H0.499756C0.223193 2.99854 -0.********* 3.22197 -0.********* 3.49854V3.99854C-0.********* 4.06729 0.0560059 4.12354 0.124756 4.12354H1.06851L1.45444 12.2954C1.47944 12.8282 1.92007 13.2485 2.45288 13.2485H9.54663C10.081 13.2485 10.5201 12.8298 10.5451 12.2954L10.931 4.12354H11.8748C11.9435 4.12354 11.9998 4.06729 11.9998 3.99854V3.49854C11.9998 3.22197 11.7763 2.99854 11.4998 2.99854ZM9.42632 12.1235H2.57319L2.19507 4.12354H9.80444L9.42632 12.1235Z"
															fill="black"
															fill-opacity="0.45"
														/>
													</svg>
												</div>
											</div>
										</div>
									</div>
									<div v-else-if="item.labelIndex == 'l2'">
										<div class="boxItemDetail">
											<div class="titleContent">指数相似度(全持仓)</div>
											<div v-for="(items, index1) in item.data" :key="index1" class="contentBoxFilter">
												<div style="display: flex; align-items: center" class="contentItem">
													<indexOnly
														:is_range="items.is_range"
														:dataIndustry="dataIndustry"
														:haveName="items.labelName"
														@indexOnlyChange="indexOnlyChange"
														placeholder="相似度"
														:fundType="fundType"
														:ref="'indexSimilar' + index + '_' + index1"
														:indexFlag="index1"
														:baseIndexFlag="index"
														:dataX="items"
													></indexOnly>
												</div>
												<div class="contentDel" @click="del(index, index1)">
													<svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
														<path
															d="M3.62476 1.87354H3.49976C3.56851 1.87354 3.62476 1.81729 3.62476 1.74854V1.87354H8.37476V1.74854C8.37476 1.81729 8.43101 1.87354 8.49976 1.87354H8.37476V2.99854H9.49976V1.74854C9.49976 1.19697 9.05132 0.748535 8.49976 0.748535H3.49976C2.94819 0.748535 2.49976 1.19697 2.49976 1.74854V2.99854H3.62476V1.87354ZM11.4998 2.99854H0.499756C0.223193 2.99854 -0.********* 3.22197 -0.********* 3.49854V3.99854C-0.********* 4.06729 0.0560059 4.12354 0.124756 4.12354H1.06851L1.45444 12.2954C1.47944 12.8282 1.92007 13.2485 2.45288 13.2485H9.54663C10.081 13.2485 10.5201 12.8298 10.5451 12.2954L10.931 4.12354H11.8748C11.9435 4.12354 11.9998 4.06729 11.9998 3.99854V3.49854C11.9998 3.22197 11.7763 2.99854 11.4998 2.99854ZM9.42632 12.1235H2.57319L2.19507 4.12354H9.80444L9.42632 12.1235Z"
															fill="black"
															fill-opacity="0.45"
														/>
													</svg>
												</div>
											</div>
										</div>
									</div>
									<div v-else-if="item.labelIndex == 'm'">
										<div class="boxItemDetail">
											<!-- <div class="titleContent">其他</div> -->
											<div v-for="(items, index1) in item.data" v-show="items.is_range" :key="index1" class="contentBoxFilter">
												<div style="display: flex; align-items: center">
													<div v-if="items.labelName.indexOf('模式') !== -1" style="display: flex; align-items: center" class="contentItem">
														<industryBig
															:is_range="items.is_range"
															:dataIndustry="dataIndustry"
															:haveName="items.labelName"
															@industryThemeChange="industryThemeChange"
															:placeholder="items.labelName.indexOf('模式') !== -1 ? '输入50,即模式占比为50%' : '前百分之,如10,表示TOP90%'"
															:fundType="fundType"
															:ref="'buyMode' + index + '_' + index1"
															:indexFlag="index1"
															:baseIndexFlag="index"
															:dataX="items"
														></industryBig>
													</div>
													<div v-else style="display: flex; align-items: center" class="contentItem">
														<boxOnlyYSF
															:is_range="items.is_range"
															:haveName="items.labelName"
															@boxOnlyYSFChange="boxOnlyYSFChange"
															:placeholder="
																items.labelName == '前十大集中度'
																	? items.labelName
																	: items.labelName == 'ROE' ||
																	  items.labelName == 'PE' ||
																	  items.labelName == 'PB' ||
																	  items.labelName == '胜率' ||
																	  items.labelName == '赔率' ||
																	  items.labelName == '行业超低配'
																	? items.labelName + '的分位'
																	: '前百分之,如10,表示TOP90%'
															"
															:fundType="fundType"
															:ref="'boxNameYSF' + index + '_' + index1"
															:indexFlag="index1"
															:baseIndexFlag="index"
															:dataX="items"
														></boxOnlyYSF>
													</div>

													<div class="contentDel" @click="del(index, index1)">
														<svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
															<path
																d="M3.62476 1.87354H3.49976C3.56851 1.87354 3.62476 1.81729 3.62476 1.74854V1.87354H8.37476V1.74854C8.37476 1.81729 8.43101 1.87354 8.49976 1.87354H8.37476V2.99854H9.49976V1.74854C9.49976 1.19697 9.05132 0.748535 8.49976 0.748535H3.49976C2.94819 0.748535 2.49976 1.19697 2.49976 1.74854V2.99854H3.62476V1.87354ZM11.4998 2.99854H0.499756C0.223193 2.99854 -0.********* 3.22197 -0.********* 3.49854V3.99854C-0.********* 4.06729 0.0560059 4.12354 0.124756 4.12354H1.06851L1.45444 12.2954C1.47944 12.8282 1.92007 13.2485 2.45288 13.2485H9.54663C10.081 13.2485 10.5201 12.8298 10.5451 12.2954L10.931 4.12354H11.8748C11.9435 4.12354 11.9998 4.06729 11.9998 3.99854V3.49854C11.9998 3.22197 11.7763 2.99854 11.4998 2.99854ZM9.42632 12.1235H2.57319L2.19507 4.12354H9.80444L9.42632 12.1235Z"
																fill="black"
																fill-opacity="0.45"
															/>
														</svg>
													</div>
												</div>
											</div>
										</div>
									</div>
									<div v-else-if="item.labelIndex == 'duration'">
										<div class="boxItemDetail">
											<div class="titleContent">久期</div>
											<div v-for="(items, index1) in item.data" v-show="items.is_range" :key="index1" class="contentBoxFilter">
												<div style="display: flex; align-items: center">
													<div style="display: flex; align-items: center" class="contentItem">
														<duration
															:is_range="items.is_range"
															:dataIndustry="dataIndustry"
															:haveName="items.labelName"
															@industryThemeChange="industryThemeChange"
															:placeholder="'输入久期，单位(年)'"
															:fundType="fundType"
															:ref="'buyMode' + index + '_' + index1"
															:indexFlag="index1"
															:baseIndexFlag="index"
															:dataX="items"
														></duration>
													</div>
													<div class="contentDel" @click="del(index, index1)">
														<svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
															<path
																d="M3.62476 1.87354H3.49976C3.56851 1.87354 3.62476 1.81729 3.62476 1.74854V1.87354H8.37476V1.74854C8.37476 1.81729 8.43101 1.87354 8.49976 1.87354H8.37476V2.99854H9.49976V1.74854C9.49976 1.19697 9.05132 0.748535 8.49976 0.748535H3.49976C2.94819 0.748535 2.49976 1.19697 2.49976 1.74854V2.99854H3.62476V1.87354ZM11.4998 2.99854H0.499756C0.223193 2.99854 -0.********* 3.22197 -0.********* 3.49854V3.99854C-0.********* 4.06729 0.0560059 4.12354 0.124756 4.12354H1.06851L1.45444 12.2954C1.47944 12.8282 1.92007 13.2485 2.45288 13.2485H9.54663C10.081 13.2485 10.5201 12.8298 10.5451 12.2954L10.931 4.12354H11.8748C11.9435 4.12354 11.9998 4.06729 11.9998 3.99854V3.49854C11.9998 3.22197 11.7763 2.99854 11.4998 2.99854ZM9.42632 12.1235H2.57319L2.19507 4.12354H9.80444L9.42632 12.1235Z"
																fill="black"
																fill-opacity="0.45"
															/>
														</svg>
													</div>
												</div>
											</div>
										</div>
									</div>
									<div v-else-if="item.labelIndex == 'credit'">
										<div class="boxItemDetail">
											<div class="titleContent">信用</div>
											<div v-for="(items, index1) in item.data" v-show="items.is_range" :key="index1" class="contentBoxFilter">
												<div style="display: flex; align-items: center">
													<div style="display: flex; align-items: center" class="contentItem">
														<credit
															:is_range="items.is_range"
															:dataIndustry="dataIndustry"
															:haveName="items.labelName"
															@industryThemeChange="industryThemeChange"
															:placeholder="'输入债券信用评级占比'"
															:fundType="fundType"
															:ref="'buyMode' + index + '_' + index1"
															:indexFlag="index1"
															:baseIndexFlag="index"
															:dataX="items"
														></credit>
													</div>
													<div class="contentDel" @click="del(index, index1)">
														<svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
															<path
																d="M3.62476 1.87354H3.49976C3.56851 1.87354 3.62476 1.81729 3.62476 1.74854V1.87354H8.37476V1.74854C8.37476 1.81729 8.43101 1.87354 8.49976 1.87354H8.37476V2.99854H9.49976V1.74854C9.49976 1.19697 9.05132 0.748535 8.49976 0.748535H3.49976C2.94819 0.748535 2.49976 1.19697 2.49976 1.74854V2.99854H3.62476V1.87354ZM11.4998 2.99854H0.499756C0.223193 2.99854 -0.********* 3.22197 -0.********* 3.49854V3.99854C-0.********* 4.06729 0.0560059 4.12354 0.124756 4.12354H1.06851L1.45444 12.2954C1.47944 12.8282 1.92007 13.2485 2.45288 13.2485H9.54663C10.081 13.2485 10.5201 12.8298 10.5451 12.2954L10.931 4.12354H11.8748C11.9435 4.12354 11.9998 4.06729 11.9998 3.99854V3.49854C11.9998 3.22197 11.7763 2.99854 11.4998 2.99854ZM9.42632 12.1235H2.57319L2.19507 4.12354H9.80444L9.42632 12.1235Z"
																fill="black"
																fill-opacity="0.45"
															/>
														</svg>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div
							style="text-align: right; margin-right: 24px; padding-top: 24px; border-top: 1px solid #e9e9e9"
							class="demo-drawer__footer"
						>
							<el-radio v-model="radioFilterOrOut" :label="false" @click.native.prevent="changeRadio">反选</el-radio>
							<el-button @click="out(1)" type="">重置</el-button>
							<el-button type="primary" @click="out(2)">筛选</el-button>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import axios from '@/api/index';
import boxOnlyYSF from './boxOnlyYSF';
import fundCateOnly from './fundCateOnly';
import holdPersonOnly from './holdPersonOnly';
import holdPersonOnly2 from './holdPersonOnly2';
import boxNameYSF from './boxNameYSF';
import industryTheme from './industryTheme';
import industrySum from './industrySum';
import indexOnly from './indexOnly';
import boxCzJzOnly from './boxCzJzOnly';
import boxCzJzOnly2 from './boxCzJzOnly2';
import industryBig from './industryBig';
import duration from './duration';
import credit from './credit';
import isCodition from './isCodition';
import nameSelect from './nameSelect';
import fundcate from './fundcate';
import industryProsperity from './industryProsperity';
import { filter_risk_future, getSinceWhat, getIndexList, getMeasureHint } from '@/api/pages/SystemAlpha.js';
export default {
	props: {
		dataIndustry: {
			type: Object
		},
		datacategories: {
			type: Object
		},
		listSelectX: {
			type: Array
		},
		radioType2: {
			type: String,
			default: 'latest'
		},
		radioInput2: {
			type: String,
			default: ''
		},
		isSame2: {
			type: Boolean,
			default: true
		},
		radioInput3: {
			type: String,
			default: ''
		},
		radioType3: {
			type: Boolean,
			default: true
		},
		radios: {
			type: String,
			default: 'equity'
		}
	},
	//import引入的组件需要注入到对象中才能使用
	components: {
		credit,
		duration,
		boxOnlyYSF,
		fundCateOnly,
		holdPersonOnly,
		holdPersonOnly2,
		boxNameYSF,
		industryTheme,
		indexOnly,
		boxCzJzOnly,
		boxCzJzOnly2,
		industryBig,
		isCodition,
		industryProsperity,
		industrySum,
		nameSelect,
		fundcate
	},
	data() {
		//这里存放数据
		return {
			radio2: 'b',
			radioSelf2: null,
			optionsselect: [],
			fundType: 'equity',
			listSelect: [],
			filterData: {},
			checkedMerge: false,
			radioFilterOrOut: true,
			radioSelf: null,
			radio: 'latest',
			indexList: [],
			tooltipList: {},
			data: [
				{
					label: '基础信息',
					children: [
						{
							label: '基金名称',
							typeCate: '9', //1为运算符
							type: 'a',
							is_range: false
						},
						{
							label: '基金经理规模',
							typeCate: '1', //1为运算符
							type: 'a',
							is_range: false
						},
						{
							label: '基金经理管理经验',
							typeCate: '1',
							type: 'a',
							is_range: false
						},
						{
							label: '基金规模',
							typeCate: '1',
							type: 'a',
							is_range: false
						},
						{
							label: '可申购金额',
							typeCate: '1',
							type: 'a',
							is_range: false
						},
						// {
						// 	label: '基金分类',
						// 	typeCate: '2', //2为基金分类特有
						// 	type: 'a',
						// 	is_range: false
						// },

						// {
						// 	label: '是否量化',
						// 	typeCate: '4',
						// 	type: 'a', //3为持有人特有
						// 	conditions: [
						// 		{
						// 			label: '是',
						// 			value: 'true'
						// 		},
						// 		{
						// 			label: '否',
						// 			value: 'false'
						// 		}
						// 	],
						// 	is_range: false
						// },
						{
							label: '是否绝对收益',
							typeCate: '4',
							type: 'a', //3为持有人特有
							conditions: [
								{
									label: '是',
									value: 'true'
								},
								{
									label: '否',
									value: 'false'
								}
							],
							is_range: false
						},
						{
							label: '基金持有人特征',
							typeCate: '3',
							type: 'a', //3为持有人特有
							conditions: [
								{
									label: '机构定制',
									value: '机构定制'
								},
								{
									label: '机构为主',
									value: '机构为主'
								},
								{
									label: '散户为主',
									value: '散户为主'
								}
							],
							is_range: false
						},
						{
							label: '基金存续时长',
							typeCate: '1',
							type: 'a',
							is_range: false
						},
						{
							label: '基金经理任职时长',
							typeCate: '1',
							type: 'a',
							is_range: false
						},
						{
							label: '申购类型',
							typeCate: '3',
							type: 'a',
							conditions: [
								{
									label: '开放式基金',
									value: '开放式基金'
								},
								{
									label: '封闭式基金',
									value: '封闭式基金'
								},
								{
									label: '定期开放式基金',
									value: '定期开放式基金'
								}
							],
							is_range: false
						}
					]
				},
				// {
				// 	label: '费用及限制',
				// 	children: [
				// 		{
				// 			label: '可申购金额',
				// 			typeCate: '1',
				// 			type: 'b'
				// 		},
				// 		{
				// 			label: '申购费',
				// 			typeCate: '1',
				// 			type: 'b'
				// 		},
				// 		{
				// 			label: '赎回费',
				// 			typeCate: '1',
				// 			type: 'b'
				// 		},
				// 		{
				// 			label: '管理费',
				// 			typeCate: '1',
				// 			type: 'b'
				// 		},
				// 		{
				// 			label: '托管费',
				// 			typeCate: '1',
				// 			type: 'b'
				// 		},
				// 		{
				// 			label: '销售服务费',
				// 			typeCate: '1',
				// 			type: 'b'
				// 		},
				// 		{
				// 			label: '综合费率',
				// 			typeCate: '1',
				// 			type: 'b'
				// 		}
				// 	]
				// },
				{
					label: '基金大类资产约束',
					children: [
						{
							label: '股票(净)',
							typeCate: '1', //4 name+运算符
							type: 'c',
							is_range: true
						},
						{
							label: '债券(净)',
							typeCate: '1', //4 name+运算符
							type: 'c',
							is_range: true
						},
						{
							label: '基金(净)',
							typeCate: '1', //4 name+运算符
							type: 'c',
							is_range: true
						},
						{
							label: '权证(净)',
							typeCate: '1', //4 name+运算符
							type: 'c',
							is_range: true
						},
						{
							label: '质押式回购(净)',
							typeCate: '1', //4 name+运算符
							type: 'c',
							is_range: true
						},
						{
							label: '现金及等价物(净)',
							typeCate: '1', //4 name+运算符
							type: 'c',
							is_range: true
						},
						{
							label: '资产支持证券(净)',
							typeCate: '1', //4 name+运算符
							type: 'c',
							is_range: true
						},
						// {
						// 	label: '主动投资占比(净)',
						// 	typeCate: '1', //4 name+运算符
						// 	type: 'c',
						// 	is_range: true
						// },
						// {
						// 	label: '被动投资占比(净)',
						// 	typeCate: '1', //4 name+运算符
						// 	type: 'c',
						// 	is_range: true
						// },
						{
							label: '其他占比(净)',
							typeCate: '1', //4 name+运算符
							type: 'c',
							is_range: true
						},
						{
							label: '股票(总)',
							typeCate: '1', //4 name+运算符
							type: 'c',
							is_range: true
						},
						{
							label: '债券(总)',
							typeCate: '1', //4 name+运算符
							type: 'c',
							is_range: true
						},
						{
							label: '基金(总)',
							typeCate: '1', //4 name+运算符
							type: 'c',
							is_range: true
						},
						{
							label: '权证(总)',
							typeCate: '1', //4 name+运算符
							type: 'c',
							is_range: true
						},
						{
							label: '质押式回购(总)',
							typeCate: '1', //4 name+运算符
							type: 'c',
							is_range: true
						},
						{
							label: '现金及等价物(总)',
							typeCate: '1', //4 name+运算符
							type: 'c',
							is_range: true
						},
						{
							label: '资产支持证券(总)',
							typeCate: '1', //4 name+运算符
							type: 'c',
							is_range: true
						},
						// {
						// 	label: '主动投资占比(总)',
						// 	typeCate: '1', //4 name+运算符
						// 	type: 'c',
						// 	is_range: true
						// },
						// {
						// 	label: '被动投资占比(总)',
						// 	typeCate: '1', //4 name+运算符
						// 	type: 'c',
						// 	is_range: true
						// },
						{
							label: '其他占比(总)',
							typeCate: '1', //4 name+运算符
							type: 'c',
							is_range: true
						}
					]
				},
				{
					label: '债券券种约束',
					children: [
						{
							label: '金融债(净)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '金融债(总)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '金融债(债)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '国债(净)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '国债(总)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '国债(债)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '同业存单(净)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '同业存单(总)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '同业存单(债)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '央行票据(净)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '央行票据(总)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '央行票据(债)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '地方政府债(净)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '地方政府债(总)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '地方政府债(债)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '政策性金融债(净)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '政策性金融债(总)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '政策性金融债(债)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '公司债(净)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '公司债(总)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '公司债(债)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '短期融资券(净)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '短期融资券(总)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '短期融资券(债)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '中期票据(净)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '中期票据(总)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '中期票据(债)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '中小企业私募债(净)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '中小企业私募债(总)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '中小企业私募债(债)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '可转债(净)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '可转债(总)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '可转债(债)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '企业债(净)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '企业债(总)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '企业债(债)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '其他债券(净)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '其他债券(总)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						},
						{
							label: '其他债券(债)',
							typeCate: '1', //4 name+运算符
							type: 'o',
							is_range: true
						}
					]
				},
				{
					label: '权益债券资产约束',
					children: [
						{
							label: 'A股仓位',
							typeCate: '4', //4 name+运算符
							type: 'n',
							is_range: true
						},
						{
							label: '港股仓位',
							typeCate: '4', //4 name+运算符
							type: 'n',
							is_range: true
						},
						{
							label: '美股仓位',
							typeCate: '4', //4 name+运算符
							type: 'n',
							is_range: true
						},
						{
							label: '台股仓位',
							typeCate: '4', //4 name+运算符
							type: 'n',
							is_range: true
						}
						// {
						// 	label: '美债仓位',
						// 	typeCate: '4', //4 name+运算符
						// 	type: 'n',
						// 	is_range: true
						// }
					]
				},
				{
					label: '最新仓位',
					children: [
						{
							label: '最新一期A股仓位',
							typeCate: '41', //4 name+运算符
							type: 'n1',
							is_range: false
						},
						{
							label: '最新一期港股仓位',
							typeCate: '41', //4 name+运算符
							type: 'n1',
							is_range: false
						},
						{
							label: '最新一期美股仓位',
							typeCate: '41', //4 name+运算符
							type: 'n1',
							is_range: false
						},
						{
							label: '最新一期台股仓位',
							typeCate: '41', //4 name+运算符
							type: 'n1',
							is_range: false
						},
						{
							label: '最新一期政策性金融债仓位',
							typeCate: '41', //4 name+运算符
							type: 'n1',
							is_range: false
						},
						{
							label: '最新一期金融债仓位',
							typeCate: '41', //4 name+运算符
							type: 'n1',
							is_range: false
						},
						{
							label: '最新一期国债仓位',
							typeCate: '41', //4 name+运算符
							type: 'n1',
							is_range: false
						},
						{
							label: '最新一期同业存单仓位',
							typeCate: '41', //4 name+运算符
							type: 'n1',
							is_range: false
						},
						{
							label: '最新一期央行票据仓位',
							typeCate: '41', //4 name+运算符
							type: 'n1',
							is_range: false
						},
						{
							label: '最新一期地方政府债仓位',
							typeCate: '41', //4 name+运算符
							type: 'n1',
							is_range: false
						},
						{
							label: '最新一期公司债仓位',
							typeCate: '41', //4 name+运算符
							type: 'n1',
							is_range: false
						},
						{
							label: '最新一期短期融资券仓位',
							typeCate: '41', //4 name+运算符
							type: 'n1',
							is_range: false
						},
						{
							label: '最新一期中期票据仓位',
							typeCate: '41', //4 name+运算符
							type: 'n1',
							is_range: false
						},
						{
							label: '最新一期中小企业私募债仓位',
							typeCate: '41', //4 name+运算符
							type: 'n1',
							is_range: false
						},
						{
							label: '最新一期可转债仓位',
							typeCate: '41', //4 name+运算符
							type: 'n1',
							is_range: false
						},
						{
							label: '最新一期企业债仓位',
							typeCate: '41', //4 name+运算符
							type: 'n1',
							is_range: false
						},
						{
							label: '最新一期其他债券仓位',
							typeCate: '41', //4 name+运算符
							type: 'n1',
							is_range: false
						}
						// {
						// 	label: '最新一期美债仓位',
						// 	typeCate: '41', //4 name+运算符
						// 	type: 'n1',
						// 	is_range: false
						// }
					]
				},
				{
					label: '风险特征',
					children: [
						{
							label: '波动率',
							value: 'volatility',
							typeCate: '4', //4 name+运算符
							type: 'g',
							is_range: false
						},
						{
							label: '最大回撤',
							value: 'maxdrawdown',
							typeCate: '4', //4 name+运算符
							type: 'g',
							is_range: false
						},
						{
							label: '平均下行周期',
							value: 'averagelength',
							typeCate: '4', //4 name+运算符
							type: 'g',
							is_range: false
						},
						{
							label: '平均恢复周期',
							value: 'averagerecovery',
							typeCate: '4', //4 name+运算符
							type: 'g',
							is_range: false
						},
						{
							label: '最大回撤比',
							value: 'maxdrawdown_ratio',
							typeCate: '4', //4 name+运算符
							type: 'g',
							is_range: false
						},
						{
							label: '在险价值',
							value: 'VaR05',
							typeCate: '4', //4 name+运算符
							type: 'g',
							is_range: false
						},
						{
							label: '期望损失',
							value: 'ES05',
							typeCate: '4', //4 name+运算符
							type: 'g',
							is_range: false
						},
						{
							label: '下行风险',
							value: 'downsidevolatility',
							typeCate: '4', //4 name+运算符
							type: 'g',
							is_range: false
						},
						{
							label: '波动率比',
							value: 'volatilityratio',
							typeCate: '4', //4 name+运算符
							type: 'g',
							is_range: false
						},
						{
							label: '痛苦指数',
							value: 'painindex',
							typeCate: '4', //4 name+运算符
							type: 'g',
							is_range: false
						}
					]
				},
				{
					label: '风险收益特征',
					children: [
						{
							label: '年化收益率',
							value: 'ave_return',
							typeCate: '4', //4 name+运算符
							type: 'h',
							is_range: false
						},
						{
							label: '累计收益率',
							value: 'cum_return',
							typeCate: '4', //4 name+运算符
							type: 'h',
							is_range: false
						},
						{
							label: '夏普率（rf==0）',
							value: 'sharpe0',
							typeCate: '4', //4 name+运算符
							type: 'h',
							is_range: false
						},
						{
							label: '夏普率（rf==4%）',
							value: 'sharpe04',
							typeCate: '4', //4 name+运算符
							type: 'h',
							is_range: false
						},
						{
							label: '夏普率（动态rf）',
							value: 'sharpe',
							typeCate: '4', //4 name+运算符
							type: 'h',
							is_range: false
						},
						{
							label: '卡码率',
							value: 'calmar',
							typeCate: '4', //4 name+运算符
							type: 'h',
							is_range: false
						},
						{
							label: '索提诺系数（rf==0）',
							value: 'sortino0',
							typeCate: '4', //4 name+运算符
							type: 'h',
							is_range: false
						},
						{
							label: '索提诺系数（rf==4%）',
							value: 'sortino04',
							typeCate: '4', //4 name+运算符
							type: 'h',
							is_range: false
						},
						{
							label: '索提诺系数（动态rf）',
							value: 'sortino',
							typeCate: '4', //4 name+运算符
							type: 'h',
							is_range: false
						},
						{
							label: '稳定系数',
							value: 'hurstindex',
							typeCate: '4', //4 name+运算符
							type: 'h',
							is_range: false
						},
						{
							label: '凯利系数',
							value: 'kelly',
							typeCate: '4', //4 name+运算符
							type: 'h',
							is_range: false
						},
						{
							label: '信息比率',
							value: 'information',
							typeCate: '4', //4 name+运算符
							type: 'h',
							is_range: false
						},
						{
							label: '上攻潜力（周）',
							value: 'upsidepotential',
							typeCate: '4', //4 name+运算符
							type: 'h',
							is_range: false
						},
						{
							label: '月胜率',
							value: 'monthly_win_ratio',
							typeCate: '4', //4 name+运算符
							type: 'h',
							is_range: false
						},
						{
							label: '詹森系数',
							value: 'jensen',
							typeCate: '4', //4 name+运算符
							type: 'h',
							is_range: false
						},
						{
							label: '特诺系数',
							value: 'treynor',
							typeCate: '4', //4 name+运算符
							type: 'h',
							is_range: false
						},
						{
							label: '上行捕获',
							value: 'bullreturn',
							typeCate: '4', //4 name+运算符
							type: 'h',
							is_range: false
						},
						{
							label: '下行捕获',
							value: 'bearreturn',
							typeCate: '4', //4 name+运算符
							type: 'h',
							is_range: false
						},
						{
							label: '择时gamma',
							value: 'gamma',
							typeCate: '4', //4 name+运算符
							type: 'h',
							is_range: false
						},
						{
							label: 'M2',
							value: 'msquared',
							typeCate: '4', //4 name+运算符
							type: 'h',
							is_range: false
						}
					]
				},
				{
					label: '风格偏好',
					children: [
						{
							label: '成长',
							typeCate: '6', //5 特有单选
							type: 'i',
							is_range: true
						},
						{
							label: '价值',
							typeCate: '6', //6 特有单选
							type: 'i',
							is_range: true
						},
						{
							label: '动量',
							typeCate: '6', //5 特有单选
							type: 'i',
							is_range: true
						},
						{
							label: '盈利',
							typeCate: '6', //6 特有单选
							type: 'i',
							is_range: true
						},
						{
							label: '贝塔',
							typeCate: '6', //5 特有单选
							type: 'i',
							is_range: true
						},
						{
							label: '规模',
							typeCate: '56', //5 特有单选
							type: 'i',
							is_range: true
						},
						{
							label: '估值',
							typeCate: '6', //6 特有单选
							type: 'i',
							is_range: true
						}
					]
				},
				{
					label: '行业判断(全持仓)',
					children: [
						{
							label: '大行业',
							typeCate: '10', //行业
							type: 'j',
							is_range: true
						},
						{
							label: '申万一级行业',
							typeCate: '7', //行业
							type: 'j',
							is_range: true
						},
						{
							label: '申万二级行业',
							typeCate: '7', //行业
							type: 'j',
							is_range: true
						},
						{
							label: '申万三级行业',
							typeCate: '7', //行业
							type: 'j',
							is_range: true
						},
						{
							label: '恒生一级行业',
							typeCate: '7', //行业
							type: 'j',
							is_range: true
						},
						{
							label: '多行业权重判断',
							typeCate: '7', //行业
							type: 'j',
							is_range: true
						}
					]
				},
				{
					label: '行业判断(重仓)',
					children: [
						{
							label: '大行业',
							typeCate: '10', //行业
							type: 'j1',
							is_range: true
						},
						{
							label: '申万一级行业',
							typeCate: '7', //行业
							type: 'j1',
							is_range: true
						},
						{
							label: '申万二级行业',
							typeCate: '7', //行业
							type: 'j1',
							is_range: true
						},
						{
							label: '申万三级行业',
							typeCate: '7', //行业
							type: 'j1',
							is_range: true
						},
						{
							label: '恒生一级行业',
							typeCate: '7', //行业
							type: 'j1',
							is_range: true
						},
						{
							label: '多行业权重判断',
							typeCate: '7', //行业
							type: 'j1',
							is_range: true
						}
					]
				},
				{
					label: '行业特征',
					children: [
						{
							label: '行业超低配',
							typeCate: '4', //name+运算符
							type: 'f',
							is_range: false
						},
						{
							label: '景气行业占比',
							typeCate: '7', //行业
							type: 'f',
							is_range: true
						},
						{
							label: '行业轮动',
							typeCate: '7', //行业
							type: 'f',
							is_range: true
						}
					]
				},
				{
					label: '股票财务指标',
					children: [
						{
							label: 'PB',
							typeCate: '4', //name+运算符
							type: 'm',
							is_range: true
						},
						{
							label: 'PE',
							typeCate: '4', //name+运算符
							type: 'm',
							is_range: true
						},
						{
							label: 'ROE',
							typeCate: '4', //name+运算符
							type: 'm',
							is_range: true
						}
					]
				},
				{
					label: '债券指标',
					children: [
						{
							label: '久期',
							typeCate: '4', //name+运算符
							type: 'duration',
							is_range: true
						},
						{
							label: '信用',
							typeCate: '4', //name+运算符
							type: 'credit',
							is_range: true
						}
					]
				},
				{
					label: '主题判断(全持仓)',
					typeCate: '7', //行业
					type: 'k',
					is_range: true
				},
				{
					label: '主题判断(重仓)',
					typeCate: '7', //行业
					type: 'k1',
					is_range: true
				},
				{
					label: '指数持仓重叠(全持仓)',
					typeCate: '8', //特有
					type: 'l',
					is_range: true
				},
				{
					label: '指数持仓重叠(重仓)',
					typeCate: '8', //特有
					type: 'l1',
					is_range: true
				},
				{
					label: '指数相似度(全持仓)',
					typeCate: '8', //特有
					type: 'l2',
					is_range: true
				},

				{
					label: '其他',
					children: [
						{
							label: '前十大集中度',
							typeCate: '4', //name+运算符
							type: 'm',
							is_range: true
						},

						{
							label: '换手率',
							typeCate: '4', //name+运算符
							type: 'm',
							is_range: true
						},
						{
							label: '股票关注期',
							typeCate: '7', //行业
							type: 'm',
							is_range: false
						},
						{
							label: '持有股票抱团度',
							typeCate: '7', //行业
							type: 'm',
							is_range: true
						},
						// {
						// 	label: '胜率',
						// 	typeCate: '7', //行业
						// 	type: 'm',
						// 	is_range: false
						// },
						// {
						// 	label: '赔率',
						// 	typeCate: '7', //行业
						// 	type: 'm',
						// 	is_range: false
						// },
						{
							label: '买入模式',
							typeCate: '7', //行业
							type: 'm',
							is_range: false
						},
						{
							label: '卖出模式',
							typeCate: '7', //行业
							type: 'm',
							is_range: false
						}
					]
				}
			],

			defaultProps: {
				children: 'children',
				label: 'label'
			}
		};
	},
	//监听属性 类似于data概念
	computed: {},
	//监控data中的数据变化
	watch: {
		listSelectX(val) {
			this.listSelect = this.listSelectX.map((item) => {
				let return_item = { ...item };
				item.data.map((obj, index) => {
					let filterData = this.data.filter((ditem) => {
						return ditem?.children
							? ditem.children.some((dobj) => {
									return dobj.label == obj.labelName;
							  })
							: ditem.label.includes(obj.labelName);
					});

					var is_range = filterData?.[0]?.children
						? filterData?.[0]?.children?.find((ditem) => {
								return ditem.label == obj.labelName;
						  })?.is_range
						: filterData.filter((ditem) => {
								return ditem.label.includes(obj.labelName);
						  })?.[0]?.is_range;
					return_item.data[index] = {
						...obj,
						is_range,
						dataResult: obj.dataResult.map((item) => {
							return is_range ? { ...item, mathRange: item.mathRange ? item.mathRange : '100' } : item;
						})
					};
				});
				return return_item;
			});
		}
	},
	//方法集合
	methods: {
		async getIndexList() {
			let { data } = await getIndexList();
			if (data?.mtycode == 200) {
				this.indexList = data?.data.map((v) => {
					return {
						label: v.index_name,
						value: v.index_code
					};
				});
			}
		},
		async getMeasureHint() {
			let { data } = await getMeasureHint();
			if (data?.mtycode == 200) {
				let obj = {};
				data.data?.map((item) => {
					let name = this.data;
					obj[item.name] = item.explain;
				});
				this.tooltipList = obj;
			}
		},
		async getSinceWhat() {
			let since = [];
			let { data } = await getSinceWhat();
			if (data?.mtycode == 200) {
				since = data.data.map((v) => {
					return { label: v.name, value: v.date };
				});
			}
			return since;
		},
		changeRadio() {
			this.radioFilterOrOut = !this.radioFilterOrOut;
			this.$emit('changeIsSanme', this.radioFilterOrOut);
		},
		changeMerge() {
			this.$emit('changeMerge', this.checkedMerge);
		},
		datacategoriesc(e1, e2, e3, range) {
			this.listSelect[e1].data[e2].dataResult = [{ value: e3, ...range }];
			this.updateCondition();
		},
		nameChange(e1, e2, e3, e4, range) {
			this.listSelect[e1].data[e2].dataResult = [{ flag: e4, value: e3, ...range }];
			this.updateCondition();
		},
		boxOnlyYSFNameChange(e1, e2, e3, e4, e5, e6, e7, range, valueType, benchmark, benchmarkName) {
			this.listSelect[e1].data[e2].dataResult = [
				{ flag: e4, value: e3, date: e5, option: e6, havedata: e7, ...range, valueType, benchmark, benchmarkName }
			];
			this.updateCondition();
		},
		industryThemeChange(e1, e2, e3, e4, e5, e6, e7, range) {
			this.listSelect[e1].data[e2].dataResult = [{ flag: e4, value: e3, industryValue: e5, industryName: e6, havedata: e7, ...range }];
			this.updateCondition();
		},
		industrySumChange(e1, e2, e3, e4, e5, e6, e7, range) {
			this.listSelect[e1].data[e2].dataResult = [{ flag: e4, value: e3, industryValue: e5, industryName: e6, havedata: e7, ...range }];
			this.updateCondition();
		},
		industryProsperityChange(e1, e2, e3, e4, e5, e6, e7, e8, range) {
			this.listSelect[e1].data[e2].dataResult = [
				{ flag: e4, value: e3, industryValue: e5, industryName: e6, hold_type: e7?.hold, period_type: e7?.year, havedata: e8, ...range }
			];
			this.updateCondition();
		},
		bigOnlyChange(e1, e2, e3, e4, range) {
			this.listSelect[e1].data[e2].dataResult = [{ value: e3, havedata: e4, ...range }];
			this.updateCondition();
		},
		indexOnlyChange(e1, e2, e3, e4, e5, e6, e7, range) {
			this.listSelect[e1].data[e2].dataResult = [{ flag: e4, value: e3, index_code: e5, index_code_options: e6, havedata: e7, ...range }];
			this.updateCondition();
		},
		boxOnlyYSFChange(e1, e2, e3, e4, e5, range) {
			this.listSelect[e1].data[e2].dataResult = [{ flag: e4, value: e3, havedata: e5, ...range }];
			this.updateCondition();
		},
		boxCzJzOnlyChange(e1, e2, e3, e4, e5, e6, e7, e8, range) {
			this.listSelect[e1].data[e2].dataResult = [{ flag: e4, value: e3, label: e5, yearqtr: e6, rank_value: e7, havedata: e8, ...range }];
			this.updateCondition();
		},
		fundCateOnlyChange(e1, e2, e3, e4, range) {
			this.listSelect[e1].data[e2].dataResult = [{ value: e3, havedata: e4, ...range }];
			this.updateCondition();
		},
		fundCateOnlyChange2(e1, e2, e3, e4) {
			this.listSelect[e1].data[e2].dataResult = [{ value: e3 }];
			this.updateCondition();
		},
		updateCondition() {
			this.$emit('resolveListSelect', this.listSelect);
		},
		del(item, index) {
			if (this.listSelect[item].data.length > 1) {
				this.listSelect[item].data.splice(index, 1);
			} else {
				this.listSelect.splice(item, 1);
			}
		},
		out(flag) {
			if (flag == 1) {
				this.$emit('closeDialog', '--');
			} else {
				for (let i = this.listSelect.length - 1; i >= 0; i--) {
					for (let j = this.listSelect[i].data.length - 1; j >= 0; j--) {
						if (this.listSelect[i].data[j].dataResult.length == 0 || this.listSelect[i].data[j].dataResult[0]?.havedata == false) {
							this.listSelect[i].data.splice(j, 1);
						}
						if (this.listSelect[i].data.length == 0) {
							this.listSelect.splice(i, 1);
						}
					}
				}

				let flagX = 0;
				for (let i = 0; i < this.listSelect.length; i++) {
					let temp = [];
					let tempName = [];

					for (let j = 0; j < this.listSelect[i].data.length; j++) {
						if (this.listSelect[i].data[j].dataResult[0].flag) {
							if (
								this.listSelect[i].data[j].dataResult[0].flag == 'all' ||
								this.listSelect[i].data[j].dataResult[0].flag == '>' ||
								this.listSelect[i].data[j].dataResult[0].flag == '<' ||
								this.listSelect[i].data[j].dataResult[0].flag == '<=' ||
								this.listSelect[i].data[j].dataResult[0].flag == '>=' ||
								this.listSelect[i].data[j].dataResult[0].flag == '='
							) {
								if (this.listSelect[i].data[j].dataResult[0].industryName) {
									if (tempName.indexOf(this.listSelect[i].data[j].labelName + this.listSelect[i].data[j].dataResult[0].industryName) < 0) {
										tempName.push(this.listSelect[i].data[j].labelName + this.listSelect[i].data[j].dataResult[0].industryName);
										temp[tempName.length - 1] = [];
										temp[tempName.length - 1].push(this.listSelect[i].data[j].dataResult[0]);
									} else {
										temp[
											tempName.indexOf(this.listSelect[i].data[j].labelName + this.listSelect[i].data[j].dataResult[0].industryName)
										].push(this.listSelect[i].data[j].dataResult[0]);
									}
								} else if (this.listSelect[i].data[j].dataResult[0].index_code) {
									if (tempName.indexOf(this.listSelect[i].data[j].labelName + this.listSelect[i].data[j].dataResult[0].index_code) < 0) {
										tempName.push(this.listSelect[i].data[j].labelName + this.listSelect[i].data[j].dataResult[0].index_code);
										temp[tempName.length - 1] = [];
										temp[tempName.length - 1].push(this.listSelect[i].data[j].dataResult[0]);
									} else {
										temp[tempName.indexOf(this.listSelect[i].data[j].labelName + this.listSelect[i].data[j].dataResult[0].index_code)].push(
											this.listSelect[i].data[j].dataResult[0]
										);
									}
								} else if (this.listSelect[i].data[j].dataResult[0].date) {
									if (tempName.indexOf(this.listSelect[i].data[j].labelName + this.listSelect[i].data[j].dataResult[0].date) < 0) {
										tempName.push(this.listSelect[i].data[j].labelName + this.listSelect[i].data[j].dataResult[0].date);
										temp[tempName.length - 1] = [];
										temp[tempName.length - 1].push(this.listSelect[i].data[j].dataResult[0]);
									} else {
										temp[tempName.indexOf(this.listSelect[i].data[j].labelName + this.listSelect[i].data[j].dataResult[0].date)].push(
											this.listSelect[i].data[j].dataResult[0]
										);
									}
								} else if (this.listSelect[i].data[j].dataResult[0].yearqtr) {
									if (
										tempName.indexOf(
											this.listSelect[i].data[j].labelName +
												this.listSelect[i].data[j].dataResult[0].yearqtr +
												this.listSelect[i].data[j].dataResult[0].rank_value
										) < 0
									) {
										tempName.push(
											this.listSelect[i].data[j].labelName +
												this.listSelect[i].data[j].dataResult[0].yearqtr +
												this.listSelect[i].data[j].dataResult[0].rank_value
										);
										temp[tempName.length - 1] = [];
										temp[tempName.length - 1].push(this.listSelect[i].data[j].dataResult[0]);
									} else {
										temp[
											tempName.indexOf(
												this.listSelect[i].data[j].labelName +
													this.listSelect[i].data[j].dataResult[0].yearqtr +
													this.listSelect[i].data[j].dataResult[0].rank_value
											)
										].push(this.listSelect[i].data[j].dataResult[0]);
									}
								} else {
									if (tempName.indexOf(this.listSelect[i].data[j].labelName) < 0) {
										tempName.push(this.listSelect[i].data[j].labelName);
										temp[tempName.length - 1] = [];
										temp[tempName.length - 1].push(this.listSelect[i].data[j].dataResult[0]);
									} else {
										temp[tempName.indexOf(this.listSelect[i].data[j].labelName)].push(this.listSelect[i].data[j].dataResult[0]);
									}
								}
							}
						}
					}
					// for (let x = 0; x < temp.length; x++) {
					// 	if (temp[x].length > 1) {
					// 		let min = [-9999999999];
					// 		let max = [999999999];
					// 		let DY = [];
					// 		for (let p = 0; p < temp[x].length; p++) {
					// 			if ((temp[x][p].flag == '<' || temp[x][p].flag == '<=') && (temp[x][p].value != '' || temp[x][p].value != null))
					// 				max.push(temp[x][p].value);
					// 			if ((temp[x][p].flag == '>' || temp[x][p].flag == '>=') && (temp[x][p].value != '' || temp[x][p].value != null))
					// 				min.push(temp[x][p].value);
					// 			if (temp[x][p].flag == '=' && (temp[x][p].value != '' || temp[x][p].value != null)) DY.push(temp[x][p].value);
					// 		}
					// 		if (DY.length > 1) {
					// 			flagX = 1;
					// 			this.$message.error(tempName[x] + '算数逻辑有误，不能存在两个等于。');
					// 		} else if (Math.min(...max) < Math.max(...min)) {
					// 			flagX = 1;
					// 			this.$message.error(tempName[x] + '算数逻辑有误，大于对应的值比小于对应的值小了。');
					// 		} else if (DY.length == 1) {
					// 			if (DY[0] < Math.max(...min) || DY[0] > Math.min(...max)) {
					// 				flagX = 1;
					// 				this.$message.error(tempName[x] + '算数逻辑有误，等于的值在大于小于的范围之外。');
					// 			} else {
					// 				for (let j = this.listSelect[i].data.length - 1; j >= 0; j--) {
					// 					for (let p = 0; p < temp[x].length; p++) {
					// 						if (
					// 							tempName[x].indexOf(this.listSelect[i].data[j].labelName) >= 0 &&
					// 							temp[x][p].flag != '=' &&
					// 							this.isEqual(temp[x][p], this.listSelect[i].data[j].dataResult[0])
					// 						) {
					// 							this.listSelect[i].data.splice(j, 1);
					// 						}
					// 					}
					// 				}
					// 				//    this.listSelect[i].data = [this.listSelect[i].data[this.listSelect[i].data.findIndex(item=>item.dataResult[0].flag == '='&&item.dataResult[0].value == DY[0])]]
					// 			}
					// 		} else {
					// 			flagX = 0;
					// 			let t1 = {};
					// 			let t2 = {};
					// 			if (Math.max(...min) != -9999999999) {
					// 				t1 =
					// 					this.listSelect[i].data[
					// 						this.listSelect[i].data.findIndex(
					// 							(item) =>
					// 								(item.dataResult[0].industryName
					// 									? tempName[x].indexOf(item.dataResult[0].industryName) >= 0
					// 									: item.dataResult[0].index_code
					// 									? tempName[x].indexOf(item.dataResult[0].index_code) >= 0
					// 									: true) &&
					// 								(item.dataResult[0].flag == '>' || item.dataResult[0].flag == '>=') &&
					// 								item.dataResult[0].value == Math.max(...min)
					// 						)
					// 					];
					// 			}
					// 			if (Math.min(...max) != 999999999) {
					// 				t2 =
					// 					this.listSelect[i].data[
					// 						this.listSelect[i].data.findIndex(
					// 							(item) =>
					// 								(item.dataResult[0].industryName
					// 									? tempName[x].indexOf(item.dataResult[0].industryName) >= 0
					// 									: item.dataResult[0].index_code
					// 									? tempName[x].indexOf(item.dataResult[0].index_code) >= 0
					// 									: true) &&
					// 								(item.dataResult[0].flag == '<' || item.dataResult[0].flag == '<=') &&
					// 								item.dataResult[0].value == Math.min(...max)
					// 						)
					// 					];
					// 			}
					// 			// this.listSelect[i].data= []
					// 			for (let j = this.listSelect[i].data.length - 1; j >= 0; j--) {
					// 				for (let p = 0; p < temp[x].length; p++) {
					// 					let falg = 0;
					// 					if (temp[x][p].industryName) {
					// 						falg =
					// 							tempName[x].indexOf(this.listSelect[i].data[j].labelName + this.listSelect[i].data[j].dataResult[0].industryName) >=
					// 							0;
					// 					} else if (temp[x][p].index_code) {
					// 						falg =
					// 							tempName[x].indexOf(this.listSelect[i].data[j].labelName + this.listSelect[i].data[j].dataResult[0].index_code) >=
					// 							0;
					// 					} else {
					// 						falg = tempName[x].indexOf(this.listSelect[i].data[j]?.labelName) >= 0;
					// 					}
					// 					if (
					// 						falg &&
					// 						(JSON.stringify(t1) == '{}' ? true : !this.isEqual(t1.dataResult[0], this.listSelect[i].data[j].dataResult[0])) &&
					// 						(JSON.stringify(t2) == '{}' ? true : !this.isEqual(t2.dataResult[0], this.listSelect[i].data[j].dataResult[0]))
					// 					) {
					// 						this.listSelect[i].data.splice(j, 1);
					// 					}
					// 				}
					// 			}
					// 			// if(JSON.stringify(t1)!='{}') this.listSelect[i].data.push(t1)
					// 			// if(JSON.stringify(t2)!='{}') this.listSelect[i].data.push(t2)
					// 		}
					// 	}
					// }
				}
				if (flagX == 0) {
					this.$emit('closeDialog', this.listSelect, this.radio, this.radioSelf, this.radioFilterOrOut, this.radio2, this.radioSelf2);
				}
			}
		},
		isEqual(objA, objB) {
			//相等
			if (objA === objB) return objA !== 0 || 1 / objA === 1 / objB;
			//空判断
			if (objA == null || objB == null) return objA === objB;
			//类型判断
			if (Object.prototype.toString.call(objA) !== Object.prototype.toString.call(objB)) return false;

			switch (Object.prototype.toString.call(objA)) {
				case '[object RegExp]':
				case '[object String]':
					//字符串转换比较
					return '' + objA === '' + objB;
				case '[object Number]':
					//数字转换比较,判断是否为NaN
					if (+objA !== +objA) {
						return +objB !== +objB;
					}

					return +objA === 0 ? 1 / +objA === 1 / objB : +objA === +objB;
				case '[object Date]':
				case '[object Boolean]':
					return +objA === +objB;
				case '[object Array]':
					//判断数组
					for (let i = 0; i < objA.length; i++) {
						if (!this.isEqual(objA[i], objB[i])) return false;
					}
					return true;
				case '[object Object]':
					//判断对象
					let keys = Object.keys(objA);
					for (let i = 0; i < keys.length; i++) {
						if (!this.isEqual(objA[keys[i]], objB[keys[i]])) return false;
					}

					keys = Object.keys(objB);
					for (let i = 0; i < keys.length; i++) {
						if (!this.isEqual(objA[keys[i]], objB[keys[i]])) return false;
					}

					return true;
				default:
					return false;
			}
		},
		handleNodeClick(e) {
			if (this.listSelect.length == 0 && e.type && e.type != null && e.type != undefined) {
				this.listSelect.push({
					labelIndex: e.type,
					data: [
						{
							labelName: e.label,
							typeCate: e.typeCate,
							conditions: e.conditions,
							is_range: e.is_range,
							dataResult: []
						}
					]
				});
			} else {
				if (e.type && e.type != null && e.type != undefined) {
					if (this.listSelect.findIndex((item) => item.labelIndex == e.type) >= 0) {
						this.listSelect[this.listSelect.findIndex((item) => item.labelIndex == e.type)].data.push({
							labelName: e.label,
							typeCate: e.typeCate,
							is_range: e.is_range,
							conditions: e.conditions,
							dataResult: []
						});
					} else {
						this.listSelect.push({
							labelIndex: e.type,
							data: [
								{
									labelName: e.label,
									typeCate: e.typeCate,
									is_range: e.is_range,
									conditions: e.conditions,
									dataResult: []
								}
							]
						});
					}
				}
			}
			this.$emit('resolveListSelect', this.listSelect);

			// 分类 push for循环 if判断类型
		},
		async getDate() {
			// this.getMeasureHint();
			this.getIndexList();
			let since = await this.getSinceWhat();
			let option = {};
			let that = this;
			let res = await filter_risk_future();
			option['最大回撤比'] = [{ value: 'recent', label: '近期表现', children: [] }];
			for (let i = 0; i < res.data.maxdrawdown_ratio.length; i++) {
				if (res.data.maxdrawdown_ratio[i] == '1w') option['最大回撤比'][0].children.push({ value: '1w', label: '一周' });
				if (res.data.maxdrawdown_ratio[i] == '2w') option['最大回撤比'][0].children.push({ value: '2w', label: '两周' });
				if (res.data.maxdrawdown_ratio[i] == '1m') option['最大回撤比'][0].children.push({ value: '1m', label: '一月' });
				if (res.data.maxdrawdown_ratio[i] == '2m') option['最大回撤比'][0].children.push({ value: '2m', label: '两月' });
				if (res.data.maxdrawdown_ratio[i] == '1q') option['最大回撤比'][0].children.push({ value: '1q', label: '一季' });
				if (res.data.maxdrawdown_ratio[i] == '2q') option['最大回撤比'][0].children.push({ value: '2q', label: '两季' });
				if (res.data.maxdrawdown_ratio[i] == '1y') option['最大回撤比'][0].children.push({ value: '1y', label: '一年' });
				if (res.data.maxdrawdown_ratio[i] == '2y') option['最大回撤比'][0].children.push({ value: '2y', label: '两年' });
				if (res.data.maxdrawdown_ratio[i] == '3y') option['最大回撤比'][0].children.push({ value: '3y', label: '三年' });
				if (res.data.maxdrawdown_ratio[i] == '5y') option['最大回撤比'][0].children.push({ value: '5y', label: '五年' });
			}
			option['波动率'] = [{ value: 'recent', label: '近期表现', children: [] }];

			for (let i = 0; i < res.data.volatility.length; i++) {
				if (res.data.volatility[i] == '1w') option['波动率'][0].children.push({ value: '1w', label: '一周' });
				if (res.data.volatility[i] == '2w') option['波动率'][0].children.push({ value: '2w', label: '两周' });
				if (res.data.volatility[i] == '1m') option['波动率'][0].children.push({ value: '1m', label: '一月' });
				if (res.data.volatility[i] == '2m') option['波动率'][0].children.push({ value: '2m', label: '两月' });
				if (res.data.volatility[i] == '1q') option['波动率'][0].children.push({ value: '1q', label: '一季' });
				if (res.data.volatility[i] == '2q') option['波动率'][0].children.push({ value: '2q', label: '两季' });
				if (res.data.volatility[i] == '1y') option['波动率'][0].children.push({ value: '1y', label: '一年' });
				if (res.data.volatility[i] == '2y') option['波动率'][0].children.push({ value: '2y', label: '两年' });
				if (res.data.volatility[i] == '3y') option['波动率'][0].children.push({ value: '3y', label: '三年' });
				if (res.data.volatility[i] == '5y') option['波动率'][0].children.push({ value: '5y', label: '五年' });
			}

			option['波动率比'] = [{ value: 'recent', label: '近期表现', children: [] }];

			for (let i = 0; i < res.data.volatilityratio.length; i++) {
				if (res.data.volatilityratio[i] == '1w') option['波动率比'][0].children.push({ value: '1w', label: '一周' });
				if (res.data.volatilityratio[i] == '2w') option['波动率比'][0].children.push({ value: '2w', label: '两周' });
				if (res.data.volatilityratio[i] == '1m') option['波动率比'][0].children.push({ value: '1m', label: '一月' });
				if (res.data.volatilityratio[i] == '2m') option['波动率比'][0].children.push({ value: '2m', label: '两月' });
				if (res.data.volatilityratio[i] == '1q') option['波动率比'][0].children.push({ value: '1q', label: '一季' });
				if (res.data.volatilityratio[i] == '2q') option['波动率比'][0].children.push({ value: '2q', label: '两季' });
				if (res.data.volatilityratio[i] == '1y') option['波动率比'][0].children.push({ value: '1y', label: '一年' });
				if (res.data.volatilityratio[i] == '2y') option['波动率比'][0].children.push({ value: '2y', label: '两年' });
				if (res.data.volatilityratio[i] == '3y') option['波动率比'][0].children.push({ value: '3y', label: '三年' });
				if (res.data.volatilityratio[i] == '5y') option['波动率比'][0].children.push({ value: '5y', label: '五年' });
			}
			option['年化收益率'] = [{ value: 'recent', label: '近期表现', children: [] }];
			// if (this.haveName == '年化收益率') {
			for (let i = 0; i < res.data.ave_return.length; i++) {
				if (res.data.ave_return[i] == '1w') option['年化收益率'][0].children.push({ value: '1w', label: '一周' });
				if (res.data.ave_return[i] == '2w') option['年化收益率'][0].children.push({ value: '2w', label: '两周' });
				if (res.data.ave_return[i] == '1m') option['年化收益率'][0].children.push({ value: '1m', label: '一月' });
				if (res.data.ave_return[i] == '2m') option['年化收益率'][0].children.push({ value: '2m', label: '两月' });
				if (res.data.ave_return[i] == '1q') option['年化收益率'][0].children.push({ value: '1q', label: '一季' });
				if (res.data.ave_return[i] == '2q') option['年化收益率'][0].children.push({ value: '2q', label: '两季' });
				if (res.data.ave_return[i] == '1y') option['年化收益率'][0].children.push({ value: '1y', label: '一年' });
				if (res.data.ave_return[i] == '2y') option['年化收益率'][0].children.push({ value: '2y', label: '两年' });
				if (res.data.ave_return[i] == '3y') option['年化收益率'][0].children.push({ value: '3y', label: '三年' });
				if (res.data.ave_return[i] == '5y') option['年化收益率'][0].children.push({ value: '5y', label: '五年' });
			}
			// }
			option['累计收益率'] = [{ value: 'recent', label: '近期表现', children: [] }];
			// if (this.haveName == '累计收益率') {
			for (let i = 0; i < res.data.cum_return.length; i++) {
				if (res.data.cum_return[i] == '1w') option['累计收益率'][0].children.push({ value: '1w', label: '一周' });
				if (res.data.cum_return[i] == '2w') option['累计收益率'][0].children.push({ value: '2w', label: '两周' });
				if (res.data.cum_return[i] == '1m') option['累计收益率'][0].children.push({ value: '1m', label: '一月' });
				if (res.data.cum_return[i] == '2m') option['累计收益率'][0].children.push({ value: '2m', label: '两月' });
				if (res.data.cum_return[i] == '1q') option['累计收益率'][0].children.push({ value: '1q', label: '一季' });
				if (res.data.cum_return[i] == '2q') option['累计收益率'][0].children.push({ value: '2q', label: '两季' });
				if (res.data.cum_return[i] == '1y') option['累计收益率'][0].children.push({ value: '1y', label: '一年' });
				if (res.data.cum_return[i] == '2y') option['累计收益率'][0].children.push({ value: '2y', label: '两年' });
				if (res.data.cum_return[i] == '3y') option['累计收益率'][0].children.push({ value: '3y', label: '三年' });
				if (res.data.cum_return[i] == '5y') option['累计收益率'][0].children.push({ value: '5y', label: '五年' });
				// }
			}
			option['夏普率（rf==0）'] = [{ value: 'recent', label: '近期表现', children: [] }];
			// if (this.haveName == '夏普率（rf=0）') {
			for (let i = 0; i < res.data.sharpe0.length; i++) {
				if (res.data.sharpe0[i] == '1w') option['夏普率（rf==0）'][0].children.push({ value: '1w', label: '一周' });
				if (res.data.sharpe0[i] == '2w') option['夏普率（rf==0）'][0].children.push({ value: '2w', label: '两周' });
				if (res.data.sharpe0[i] == '1m') option['夏普率（rf==0）'][0].children.push({ value: '1m', label: '一月' });
				if (res.data.sharpe0[i] == '2m') option['夏普率（rf==0）'][0].children.push({ value: '2m', label: '两月' });
				if (res.data.sharpe0[i] == '1q') option['夏普率（rf==0）'][0].children.push({ value: '1q', label: '一季' });
				if (res.data.sharpe0[i] == '2q') option['夏普率（rf==0）'][0].children.push({ value: '2q', label: '两季' });
				if (res.data.sharpe0[i] == '1y') option['夏普率（rf==0）'][0].children.push({ value: '1y', label: '一年' });
				if (res.data.sharpe0[i] == '2y') option['夏普率（rf==0）'][0].children.push({ value: '2y', label: '两年' });
				if (res.data.sharpe0[i] == '3y') option['夏普率（rf==0）'][0].children.push({ value: '3y', label: '三年' });
				if (res.data.sharpe0[i] == '5y') option['夏普率（rf==0）'][0].children.push({ value: '5y', label: '五年' });
			}
			option['夏普率'] = [{ value: 'recent', label: '近期表现', children: [] }];
			// if (this.haveName == '夏普率（rf=0）') {
			for (let i = 0; i < res.data.sharpe0.length; i++) {
				if (res.data.sharpe0[i] == '1w') option['夏普率'][0].children.push({ value: '1w', label: '一周' });
				if (res.data.sharpe0[i] == '2w') option['夏普率'][0].children.push({ value: '2w', label: '两周' });
				if (res.data.sharpe0[i] == '1m') option['夏普率'][0].children.push({ value: '1m', label: '一月' });
				if (res.data.sharpe0[i] == '2m') option['夏普率'][0].children.push({ value: '2m', label: '两月' });
				if (res.data.sharpe0[i] == '1q') option['夏普率'][0].children.push({ value: '1q', label: '一季' });
				if (res.data.sharpe0[i] == '2q') option['夏普率'][0].children.push({ value: '2q', label: '两季' });
				if (res.data.sharpe0[i] == '1y') option['夏普率'][0].children.push({ value: '1y', label: '一年' });
				if (res.data.sharpe0[i] == '2y') option['夏普率'][0].children.push({ value: '2y', label: '两年' });
				if (res.data.sharpe0[i] == '3y') option['夏普率'][0].children.push({ value: '3y', label: '三年' });
				if (res.data.sharpe0[i] == '5y') option['夏普率'][0].children.push({ value: '5y', label: '五年' });
			}
			// }
			option['夏普率（rf==4%）'] = [{ value: 'recent', label: '近期表现', children: [] }];
			// if (this.haveName == '夏普率（rf=4%）') {
			for (let i = 0; i < res.data.sharpe04.length; i++) {
				if (res.data.sharpe04[i] == '1w') option['夏普率（rf==4%）'][0].children.push({ value: '1w', label: '一周' });
				if (res.data.sharpe04[i] == '2w') option['夏普率（rf==4%）'][0].children.push({ value: '2w', label: '两周' });
				if (res.data.sharpe04[i] == '1m') option['夏普率（rf==4%）'][0].children.push({ value: '1m', label: '一月' });
				if (res.data.sharpe04[i] == '2m') option['夏普率（rf==4%）'][0].children.push({ value: '2m', label: '两月' });
				if (res.data.sharpe04[i] == '1q') option['夏普率（rf==4%）'][0].children.push({ value: '1q', label: '一季' });
				if (res.data.sharpe04[i] == '2q') option['夏普率（rf==4%）'][0].children.push({ value: '2q', label: '两季' });
				if (res.data.sharpe04[i] == '1y') option['夏普率（rf==4%）'][0].children.push({ value: '1y', label: '一年' });
				if (res.data.sharpe04[i] == '2y') option['夏普率（rf==4%）'][0].children.push({ value: '2y', label: '两年' });
				if (res.data.sharpe04[i] == '3y') option['夏普率（rf==4%）'][0].children.push({ value: '3y', label: '三年' });
				if (res.data.sharpe04[i] == '5y') option['夏普率（rf==4%）'][0].children.push({ value: '5y', label: '五年' });
				// }
			}
			option['夏普率（动态rf）'] = [{ value: 'recent', label: '近期表现', children: [] }];
			// if (this.haveName == '夏普率（动态rf）' || this.haveName == '夏普率') {
			for (let i = 0; i < res.data.sharpe.length; i++) {
				if (res.data.sharpe[i] == '1w') option['夏普率（动态rf）'][0].children.push({ value: '1w', label: '一周' });
				if (res.data.sharpe[i] == '2w') option['夏普率（动态rf）'][0].children.push({ value: '2w', label: '两周' });
				if (res.data.sharpe[i] == '1m') option['夏普率（动态rf）'][0].children.push({ value: '1m', label: '一月' });
				if (res.data.sharpe[i] == '2m') option['夏普率（动态rf）'][0].children.push({ value: '2m', label: '两月' });
				if (res.data.sharpe[i] == '1q') option['夏普率（动态rf）'][0].children.push({ value: '1q', label: '一季' });
				if (res.data.sharpe[i] == '2q') option['夏普率（动态rf）'][0].children.push({ value: '2q', label: '两季' });
				if (res.data.sharpe[i] == '1y') option['夏普率（动态rf）'][0].children.push({ value: '1y', label: '一年' });
				if (res.data.sharpe[i] == '2y') option['夏普率（动态rf）'][0].children.push({ value: '2y', label: '两年' });
				if (res.data.sharpe[i] == '3y') option['夏普率（动态rf）'][0].children.push({ value: '3y', label: '三年' });
				if (res.data.sharpe[i] == '5y') option['夏普率（动态rf）'][0].children.push({ value: '5y', label: '五年' });
				// }
			}
			option['卡码率'] = [{ value: 'recent', label: '近期表现', children: [] }];
			// if (this.haveName == '卡码率') {
			for (let i = 0; i < res.data.calmar.length; i++) {
				if (res.data.calmar[i] == '1w') option['卡码率'][0].children.push({ value: '1w', label: '一周' });
				if (res.data.calmar[i] == '2w') option['卡码率'][0].children.push({ value: '2w', label: '两周' });
				if (res.data.calmar[i] == '1m') option['卡码率'][0].children.push({ value: '1m', label: '一月' });
				if (res.data.calmar[i] == '2m') option['卡码率'][0].children.push({ value: '2m', label: '两月' });
				if (res.data.calmar[i] == '1q') option['卡码率'][0].children.push({ value: '1q', label: '一季' });
				if (res.data.calmar[i] == '2q') option['卡码率'][0].children.push({ value: '2q', label: '两季' });
				if (res.data.calmar[i] == '1y') option['卡码率'][0].children.push({ value: '1y', label: '一年' });
				if (res.data.calmar[i] == '2y') option['卡码率'][0].children.push({ value: '2y', label: '两年' });
				if (res.data.calmar[i] == '3y') option['卡码率'][0].children.push({ value: '3y', label: '三年' });
				if (res.data.calmar[i] == '5y') option['卡码率'][0].children.push({ value: '5y', label: '五年' });
				// }
			}
			option['索提诺系数（rf==0）'] = [{ value: 'recent', label: '近期表现', children: [] }];
			// if (this.haveName == '索提诺系数（rf=0）') {
			for (let i = 0; i < res.data.sortino0.length; i++) {
				if (res.data.sortino0[i] == '1w') option['索提诺系数（rf==0）'][0].children.push({ value: '1w', label: '一周' });
				if (res.data.sortino0[i] == '2w') option['索提诺系数（rf==0）'][0].children.push({ value: '2w', label: '两周' });
				if (res.data.sortino0[i] == '1m') option['索提诺系数（rf==0）'][0].children.push({ value: '1m', label: '一月' });
				if (res.data.sortino0[i] == '2m') option['索提诺系数（rf==0）'][0].children.push({ value: '2m', label: '两月' });
				if (res.data.sortino0[i] == '1q') option['索提诺系数（rf==0）'][0].children.push({ value: '1q', label: '一季' });
				if (res.data.sortino0[i] == '2q') option['索提诺系数（rf==0）'][0].children.push({ value: '2q', label: '两季' });
				if (res.data.sortino0[i] == '1y') option['索提诺系数（rf==0）'][0].children.push({ value: '1y', label: '一年' });
				if (res.data.sortino0[i] == '2y') option['索提诺系数（rf==0）'][0].children.push({ value: '2y', label: '两年' });
				if (res.data.sortino0[i] == '3y') option['索提诺系数（rf==0）'][0].children.push({ value: '3y', label: '三年' });
				if (res.data.sortino0[i] == '5y') option['索提诺系数（rf==0）'][0].children.push({ value: '5y', label: '五年' });
			}
			// }
			option['索提诺系数（rf==4%）'] = [{ value: 'recent', label: '近期表现', children: [] }];
			// if (this.haveName == '索提诺系数（rf=0.04）') {
			for (let i = 0; i < res.data.sortino04.length; i++) {
				if (res.data.sortino04[i] == '1w') option['索提诺系数（rf==4%）'][0].children.push({ value: '1w', label: '一周' });
				if (res.data.sortino04[i] == '2w') option['索提诺系数（rf==4%）'][0].children.push({ value: '2w', label: '两周' });
				if (res.data.sortino04[i] == '1m') option['索提诺系数（rf==4%）'][0].children.push({ value: '1m', label: '一月' });
				if (res.data.sortino04[i] == '2m') option['索提诺系数（rf==4%）'][0].children.push({ value: '2m', label: '两月' });
				if (res.data.sortino04[i] == '1q') option['索提诺系数（rf==4%）'][0].children.push({ value: '1q', label: '一季' });
				if (res.data.sortino04[i] == '2q') option['索提诺系数（rf==4%）'][0].children.push({ value: '2q', label: '两季' });
				if (res.data.sortino04[i] == '1y') option['索提诺系数（rf==4%）'][0].children.push({ value: '1y', label: '一年' });
				if (res.data.sortino04[i] == '2y') option['索提诺系数（rf==4%）'][0].children.push({ value: '2y', label: '两年' });
				if (res.data.sortino04[i] == '3y') option['索提诺系数（rf==4%）'][0].children.push({ value: '3y', label: '三年' });
				if (res.data.sortino04[i] == '5y') option['索提诺系数（rf==4%）'][0].children.push({ value: '5y', label: '五年' });
			}
			// }
			option['索提诺系数（动态rf）'] = [{ value: 'recent', label: '近期表现', children: [] }];
			// if (this.haveName == '索提诺系数（动态rf）') {
			for (let i = 0; i < res.data.sortino.length; i++) {
				if (res.data.sortino[i] == '1w') option['索提诺系数（动态rf）'][0].children.push({ value: '1w', label: '一周' });
				if (res.data.sortino[i] == '2w') option['索提诺系数（动态rf）'][0].children.push({ value: '2w', label: '两周' });
				if (res.data.sortino[i] == '1m') option['索提诺系数（动态rf）'][0].children.push({ value: '1m', label: '一月' });
				if (res.data.sortino[i] == '2m') option['索提诺系数（动态rf）'][0].children.push({ value: '2m', label: '两月' });
				if (res.data.sortino[i] == '1q') option['索提诺系数（动态rf）'][0].children.push({ value: '1q', label: '一季' });
				if (res.data.sortino[i] == '2q') option['索提诺系数（动态rf）'][0].children.push({ value: '2q', label: '两季' });
				if (res.data.sortino[i] == '1y') option['索提诺系数（动态rf）'][0].children.push({ value: '1y', label: '一年' });
				if (res.data.sortino[i] == '2y') option['索提诺系数（动态rf）'][0].children.push({ value: '2y', label: '两年' });
				if (res.data.sortino[i] == '3y') option['索提诺系数（动态rf）'][0].children.push({ value: '3y', label: '三年' });
				if (res.data.sortino[i] == '5y') option['索提诺系数（动态rf）'][0].children.push({ value: '5y', label: '五年' });
			}
			// }
			option['稳定系数'] = [{ value: 'recent', label: '近期表现', children: [] }];
			// if (this.haveName == '稳定系数') {
			for (let i = 0; i < res.data.hurstindex.length; i++) {
				if (res.data.hurstindex[i] == '1w') option['稳定系数'][0].children.push({ value: '1w', label: '一周' });
				if (res.data.hurstindex[i] == '2w') option['稳定系数'][0].children.push({ value: '2w', label: '两周' });
				if (res.data.hurstindex[i] == '1m') option['稳定系数'][0].children.push({ value: '1m', label: '一月' });
				if (res.data.hurstindex[i] == '2m') option['稳定系数'][0].children.push({ value: '2m', label: '两月' });
				if (res.data.hurstindex[i] == '1q') option['稳定系数'][0].children.push({ value: '1q', label: '一季' });
				if (res.data.hurstindex[i] == '2q') option['稳定系数'][0].children.push({ value: '2q', label: '两季' });
				if (res.data.hurstindex[i] == '1y') option['稳定系数'][0].children.push({ value: '1y', label: '一年' });
				if (res.data.hurstindex[i] == '2y') option['稳定系数'][0].children.push({ value: '2y', label: '两年' });
				if (res.data.hurstindex[i] == '3y') option['稳定系数'][0].children.push({ value: '3y', label: '三年' });
				if (res.data.hurstindex[i] == '5y') option['稳定系数'][0].children.push({ value: '5y', label: '五年' });
				// }
			}
			option['凯利系数'] = [{ value: 'recent', label: '近期表现', children: [] }];
			// if (this.haveName == '凯利系数') {
			for (let i = 0; i < res.data.kelly.length; i++) {
				if (res.data.kelly[i] == '1w') option['凯利系数'][0].children.push({ value: '1w', label: '一周' });
				if (res.data.kelly[i] == '2w') option['凯利系数'][0].children.push({ value: '2w', label: '两周' });
				if (res.data.kelly[i] == '1m') option['凯利系数'][0].children.push({ value: '1m', label: '一月' });
				if (res.data.kelly[i] == '2m') option['凯利系数'][0].children.push({ value: '2m', label: '两月' });
				if (res.data.kelly[i] == '1q') option['凯利系数'][0].children.push({ value: '1q', label: '一季' });
				if (res.data.kelly[i] == '2q') option['凯利系数'][0].children.push({ value: '2q', label: '两季' });
				if (res.data.kelly[i] == '1y') option['凯利系数'][0].children.push({ value: '1y', label: '一年' });
				if (res.data.kelly[i] == '2y') option['凯利系数'][0].children.push({ value: '2y', label: '两年' });
				if (res.data.kelly[i] == '3y') option['凯利系数'][0].children.push({ value: '3y', label: '三年' });
				if (res.data.kelly[i] == '5y') option['凯利系数'][0].children.push({ value: '5y', label: '五年' });
				// }
			}
			option['信息比率'] = [{ value: 'recent', label: '近期表现', children: [] }];
			// if (this.haveName == '信息比率') {
			for (let i = 0; i < res.data.information.length; i++) {
				if (res.data.information[i] == '1w') option['信息比率'][0].children.push({ value: '1w', label: '一周' });
				if (res.data.information[i] == '2w') option['信息比率'][0].children.push({ value: '2w', label: '两周' });
				if (res.data.information[i] == '1m') option['信息比率'][0].children.push({ value: '1m', label: '一月' });
				if (res.data.information[i] == '2m') option['信息比率'][0].children.push({ value: '2m', label: '两月' });
				if (res.data.information[i] == '1q') option['信息比率'][0].children.push({ value: '1q', label: '一季' });
				if (res.data.information[i] == '2q') option['信息比率'][0].children.push({ value: '2q', label: '两季' });
				if (res.data.information[i] == '1y') option['信息比率'][0].children.push({ value: '1y', label: '一年' });
				if (res.data.information[i] == '2y') option['信息比率'][0].children.push({ value: '2y', label: '两年' });
				if (res.data.information[i] == '3y') option['信息比率'][0].children.push({ value: '3y', label: '三年' });
				if (res.data.information[i] == '5y') option['信息比率'][0].children.push({ value: '5y', label: '五年' });
				// }
			}
			option['上攻潜力（周）'] = [{ value: 'recent', label: '近期表现', children: [] }];
			// if (this.haveName == '上攻潜力') {
			for (let i = 0; i < res.data.upsidepotential.length; i++) {
				if (res.data.upsidepotential[i] == '1w') option['上攻潜力（周）'][0].children.push({ value: '1w', label: '一周' });
				if (res.data.upsidepotential[i] == '2w') option['上攻潜力（周）'][0].children.push({ value: '2w', label: '两周' });
				if (res.data.upsidepotential[i] == '1m') option['上攻潜力（周）'][0].children.push({ value: '1m', label: '一月' });
				if (res.data.upsidepotential[i] == '2m') option['上攻潜力（周）'][0].children.push({ value: '2m', label: '两月' });
				if (res.data.upsidepotential[i] == '1q') option['上攻潜力（周）'][0].children.push({ value: '1q', label: '一季' });
				if (res.data.upsidepotential[i] == '2q') option['上攻潜力（周）'][0].children.push({ value: '2q', label: '两季' });
				if (res.data.upsidepotential[i] == '1y') option['上攻潜力（周）'][0].children.push({ value: '1y', label: '一年' });
				if (res.data.upsidepotential[i] == '2y') option['上攻潜力（周）'][0].children.push({ value: '2y', label: '两年' });
				if (res.data.upsidepotential[i] == '3y') option['上攻潜力（周）'][0].children.push({ value: '3y', label: '三年' });
				if (res.data.upsidepotential[i] == '5y') option['上攻潜力（周）'][0].children.push({ value: '5y', label: '五年' });
				// }
			}
			option['月胜率'] = [{ value: 'recent', label: '近期表现', children: [] }];
			// if (this.haveName == '月胜率') {
			for (let i = 0; i < res.data.monthly_win_ratio.length; i++) {
				if (res.data.monthly_win_ratio[i] == '1w') option['月胜率'][0].children.push({ value: '1w', label: '一周' });
				if (res.data.monthly_win_ratio[i] == '2w') option['月胜率'][0].children.push({ value: '2w', label: '两周' });
				if (res.data.monthly_win_ratio[i] == '1m') option['月胜率'][0].children.push({ value: '1m', label: '一月' });
				if (res.data.monthly_win_ratio[i] == '2m') option['月胜率'][0].children.push({ value: '2m', label: '两月' });
				if (res.data.monthly_win_ratio[i] == '1q') option['月胜率'][0].children.push({ value: '1q', label: '一季' });
				if (res.data.monthly_win_ratio[i] == '2q') option['月胜率'][0].children.push({ value: '2q', label: '两季' });
				if (res.data.monthly_win_ratio[i] == '1y') option['月胜率'][0].children.push({ value: '1y', label: '一年' });
				if (res.data.monthly_win_ratio[i] == '2y') option['月胜率'][0].children.push({ value: '2y', label: '两年' });
				if (res.data.monthly_win_ratio[i] == '3y') option['月胜率'][0].children.push({ value: '3y', label: '三年' });
				if (res.data.monthly_win_ratio[i] == '5y') option['月胜率'][0].children.push({ value: '5y', label: '五年' });
				// }
			}
			option['詹森系数'] = [{ value: 'recent', label: '近期表现', children: [] }];
			// if (this.haveName == '詹森系数') {
			for (let i = 0; i < res.data.jensen.length; i++) {
				if (res.data.jensen[i] == '1w') option['詹森系数'][0].children.push({ value: '1w', label: '一周' });
				if (res.data.jensen[i] == '2w') option['詹森系数'][0].children.push({ value: '2w', label: '两周' });
				if (res.data.jensen[i] == '1m') option['詹森系数'][0].children.push({ value: '1m', label: '一月' });
				if (res.data.jensen[i] == '2m') option['詹森系数'][0].children.push({ value: '2m', label: '两月' });
				if (res.data.jensen[i] == '1q') option['詹森系数'][0].children.push({ value: '1q', label: '一季' });
				if (res.data.jensen[i] == '2q') option['詹森系数'][0].children.push({ value: '2q', label: '两季' });
				if (res.data.jensen[i] == '1y') option['詹森系数'][0].children.push({ value: '1y', label: '一年' });
				if (res.data.jensen[i] == '2y') option['詹森系数'][0].children.push({ value: '2y', label: '两年' });
				if (res.data.jensen[i] == '3y') option['詹森系数'][0].children.push({ value: '3y', label: '三年' });
				if (res.data.jensen[i] == '5y') option['詹森系数'][0].children.push({ value: '5y', label: '五年' });
				// }
			}
			option['特诺系数'] = [{ value: 'recent', label: '近期表现', children: [] }];
			// if (this.haveName == '特诺系数') {
			for (let i = 0; i < res.data.treynor.length; i++) {
				if (res.data.treynor[i] == '1w') option['特诺系数'][0].children.push({ value: '1w', label: '一周' });
				if (res.data.treynor[i] == '2w') option['特诺系数'][0].children.push({ value: '2w', label: '两周' });
				if (res.data.treynor[i] == '1m') option['特诺系数'][0].children.push({ value: '1m', label: '一月' });
				if (res.data.treynor[i] == '2m') option['特诺系数'][0].children.push({ value: '2m', label: '两月' });
				if (res.data.treynor[i] == '1q') option['特诺系数'][0].children.push({ value: '1q', label: '一季' });
				if (res.data.treynor[i] == '2q') option['特诺系数'][0].children.push({ value: '2q', label: '两季' });
				if (res.data.treynor[i] == '1y') option['特诺系数'][0].children.push({ value: '1y', label: '一年' });
				if (res.data.treynor[i] == '2y') option['特诺系数'][0].children.push({ value: '2y', label: '两年' });
				if (res.data.treynor[i] == '3y') option['特诺系数'][0].children.push({ value: '3y', label: '三年' });
				if (res.data.treynor[i] == '5y') option['特诺系数'][0].children.push({ value: '5y', label: '五年' });
				// }
			}
			option['上行捕获'] = [{ value: 'recent', label: '近期表现', children: [] }];
			// if (this.haveName == '上行捕获') {
			for (let i = 0; i < res.data.bullreturn.length; i++) {
				if (res.data.bullreturn[i] == '1w') option['上行捕获'][0].children.push({ value: '1w', label: '一周' });
				if (res.data.bullreturn[i] == '2w') option['上行捕获'][0].children.push({ value: '2w', label: '两周' });
				if (res.data.bullreturn[i] == '1m') option['上行捕获'][0].children.push({ value: '1m', label: '一月' });
				if (res.data.bullreturn[i] == '2m') option['上行捕获'][0].children.push({ value: '2m', label: '两月' });
				if (res.data.bullreturn[i] == '1q') option['上行捕获'][0].children.push({ value: '1q', label: '一季' });
				if (res.data.bullreturn[i] == '2q') option['上行捕获'][0].children.push({ value: '2q', label: '两季' });
				if (res.data.bullreturn[i] == '1y') option['上行捕获'][0].children.push({ value: '1y', label: '一年' });
				if (res.data.bullreturn[i] == '2y') option['上行捕获'][0].children.push({ value: '2y', label: '两年' });
				if (res.data.bullreturn[i] == '3y') option['上行捕获'][0].children.push({ value: '3y', label: '三年' });
				if (res.data.bullreturn[i] == '5y') option['上行捕获'][0].children.push({ value: '5y', label: '五年' });
				// }
			}
			option['下行捕获'] = [{ value: 'recent', label: '近期表现', children: [] }];
			// if (this.haveName == '下行捕获') {
			for (let i = 0; i < res.data.bearreturn.length; i++) {
				if (res.data.bearreturn[i] == '1w') option['下行捕获'][0].children.push({ value: '1w', label: '一周' });
				if (res.data.bearreturn[i] == '2w') option['下行捕获'][0].children.push({ value: '2w', label: '两周' });
				if (res.data.bearreturn[i] == '1m') option['下行捕获'][0].children.push({ value: '1m', label: '一月' });
				if (res.data.bearreturn[i] == '2m') option['下行捕获'][0].children.push({ value: '2m', label: '两月' });
				if (res.data.bearreturn[i] == '1q') option['下行捕获'][0].children.push({ value: '1q', label: '一季' });
				if (res.data.bearreturn[i] == '2q') option['下行捕获'][0].children.push({ value: '2q', label: '两季' });
				if (res.data.bearreturn[i] == '1y') option['下行捕获'][0].children.push({ value: '1y', label: '一年' });
				if (res.data.bearreturn[i] == '2y') option['下行捕获'][0].children.push({ value: '2y', label: '两年' });
				if (res.data.bearreturn[i] == '3y') option['下行捕获'][0].children.push({ value: '3y', label: '三年' });
				if (res.data.bearreturn[i] == '5y') option['下行捕获'][0].children.push({ value: '5y', label: '五年' });
				// }
			}
			option['择时gamma'] = [{ value: 'recent', label: '近期表现', children: [] }];
			// if (this.haveName == '择时gamma') {
			for (let i = 0; i < res.data.gamma.length; i++) {
				if (res.data.gamma[i] == '1w') option['择时gamma'][0].children.push({ value: '1w', label: '一周' });
				if (res.data.gamma[i] == '2w') option['择时gamma'][0].children.push({ value: '2w', label: '两周' });
				if (res.data.gamma[i] == '1m') option['择时gamma'][0].children.push({ value: '1m', label: '一月' });
				if (res.data.gamma[i] == '2m') option['择时gamma'][0].children.push({ value: '2m', label: '两月' });
				if (res.data.gamma[i] == '1q') option['择时gamma'][0].children.push({ value: '1q', label: '一季' });
				if (res.data.gamma[i] == '2q') option['择时gamma'][0].children.push({ value: '2q', label: '两季' });
				if (res.data.gamma[i] == '1y') option['择时gamma'][0].children.push({ value: '1y', label: '一年' });
				if (res.data.gamma[i] == '2y') option['择时gamma'][0].children.push({ value: '2y', label: '两年' });
				if (res.data.gamma[i] == '3y') option['择时gamma'][0].children.push({ value: '3y', label: '三年' });
				if (res.data.gamma[i] == '5y') option['择时gamma'][0].children.push({ value: '5y', label: '五年' });
				// }
			}
			option['M2'] = [{ value: 'recent', label: '近期表现', children: [] }];
			// if (this.haveName == 'M2') {
			for (let i = 0; i < res.data.msquared.length; i++) {
				if (res.data.msquared[i] == '1w') option['M2'][0].children.push({ value: '1w', label: '一周' });
				if (res.data.msquared[i] == '2w') option['M2'][0].children.push({ value: '2w', label: '两周' });
				if (res.data.msquared[i] == '1m') option['M2'][0].children.push({ value: '1m', label: '一月' });
				if (res.data.msquared[i] == '2m') option['M2'][0].children.push({ value: '2m', label: '两月' });
				if (res.data.msquared[i] == '1q') option['M2'][0].children.push({ value: '1q', label: '一季' });
				if (res.data.msquared[i] == '2q') option['M2'][0].children.push({ value: '2q', label: '两季' });
				if (res.data.msquared[i] == '1y') option['M2'][0].children.push({ value: '1y', label: '一年' });
				if (res.data.msquared[i] == '2y') option['M2'][0].children.push({ value: '2y', label: '两年' });
				if (res.data.msquared[i] == '3y') option['M2'][0].children.push({ value: '3y', label: '三年' });
				if (res.data.msquared[i] == '5y') option['M2'][0].children.push({ value: '5y', label: '五年' });
				// }
			}
			option['年化波动率'] = [{ value: 'recent', label: '近期表现', children: [] }];
			// if (this.haveName == '年化波动率') {
			for (let i = 0; i < res.data.volatility.length; i++) {
				if (res.data.volatility[i] == '1w') option['年化波动率'][0].children.push({ value: '1w', label: '一周' });
				if (res.data.volatility[i] == '2w') option['年化波动率'][0].children.push({ value: '2w', label: '两周' });
				if (res.data.volatility[i] == '1m') option['年化波动率'][0].children.push({ value: '1m', label: '一月' });
				if (res.data.volatility[i] == '2m') option['年化波动率'][0].children.push({ value: '2m', label: '两月' });
				if (res.data.volatility[i] == '1q') option['年化波动率'][0].children.push({ value: '1q', label: '一季' });
				if (res.data.volatility[i] == '2q') option['年化波动率'][0].children.push({ value: '2q', label: '两季' });
				if (res.data.volatility[i] == '1y') option['年化波动率'][0].children.push({ value: '1y', label: '一年' });
				if (res.data.volatility[i] == '2y') option['年化波动率'][0].children.push({ value: '2y', label: '两年' });
				if (res.data.volatility[i] == '3y') option['年化波动率'][0].children.push({ value: '3y', label: '三年' });
				if (res.data.volatility[i] == '5y') option['年化波动率'][0].children.push({ value: '5y', label: '五年' });
			}
			// }
			option['最大回撤'] = [{ value: 'recent', label: '近期表现', children: [] }];
			// if (this.haveName == '最大回撤') {
			for (let i = 0; i < res.data.maxdrawdown.length; i++) {
				if (res.data.maxdrawdown[i] == '1w') option['最大回撤'][0].children.push({ value: '1w', label: '一周' });
				if (res.data.maxdrawdown[i] == '2w') option['最大回撤'][0].children.push({ value: '2w', label: '两周' });
				if (res.data.maxdrawdown[i] == '1m') option['最大回撤'][0].children.push({ value: '1m', label: '一月' });
				if (res.data.maxdrawdown[i] == '2m') option['最大回撤'][0].children.push({ value: '2m', label: '两月' });
				if (res.data.maxdrawdown[i] == '1q') option['最大回撤'][0].children.push({ value: '1q', label: '一季' });
				if (res.data.maxdrawdown[i] == '2q') option['最大回撤'][0].children.push({ value: '2q', label: '两季' });
				if (res.data.maxdrawdown[i] == '1y') option['最大回撤'][0].children.push({ value: '1y', label: '一年' });
				if (res.data.maxdrawdown[i] == '2y') option['最大回撤'][0].children.push({ value: '2y', label: '两年' });
				if (res.data.maxdrawdown[i] == '3y') option['最大回撤'][0].children.push({ value: '3y', label: '三年' });
				if (res.data.maxdrawdown[i] == '5y') option['最大回撤'][0].children.push({ value: '5y', label: '五年' });
			}
			// }
			option['平均下行周期'] = [{ value: 'recent', label: '近期表现', children: [] }];
			// if (this.haveName == '平均下行周期') {
			for (let i = 0; i < res.data.averagelength.length; i++) {
				if (res.data.averagelength[i] == '1w') option['平均下行周期'][0].children.push({ value: '1w', label: '一周' });
				if (res.data.averagelength[i] == '2w') option['平均下行周期'][0].children.push({ value: '2w', label: '两周' });
				if (res.data.averagelength[i] == '1m') option['平均下行周期'][0].children.push({ value: '1m', label: '一月' });
				if (res.data.averagelength[i] == '2m') option['平均下行周期'][0].children.push({ value: '2m', label: '两月' });
				if (res.data.averagelength[i] == '1q') option['平均下行周期'][0].children.push({ value: '1q', label: '一季' });
				if (res.data.averagelength[i] == '2q') option['平均下行周期'][0].children.push({ value: '2q', label: '两季' });
				if (res.data.averagelength[i] == '1y') option['平均下行周期'][0].children.push({ value: '1y', label: '一年' });
				if (res.data.averagelength[i] == '2y') option['平均下行周期'][0].children.push({ value: '2y', label: '两年' });
				if (res.data.averagelength[i] == '3y') option['平均下行周期'][0].children.push({ value: '3y', label: '三年' });
				if (res.data.averagelength[i] == '5y') option['平均下行周期'][0].children.push({ value: '5y', label: '五年' });
			}
			option['平均恢复周期'] = [{ value: 'recent', label: '近期表现', children: [] }];
			// }
			// if (this.haveName == '平均恢复周期') {
			for (let i = 0; i < res.data.averagerecovery.length; i++) {
				if (res.data.averagerecovery[i] == '1w') option['平均恢复周期'][0].children.push({ value: '1w', label: '一周' });
				if (res.data.averagerecovery[i] == '2w') option['平均恢复周期'][0].children.push({ value: '2w', label: '两周' });
				if (res.data.averagerecovery[i] == '1m') option['平均恢复周期'][0].children.push({ value: '1m', label: '一月' });
				if (res.data.averagerecovery[i] == '2m') option['平均恢复周期'][0].children.push({ value: '2m', label: '两月' });
				if (res.data.averagerecovery[i] == '1q') option['平均恢复周期'][0].children.push({ value: '1q', label: '一季' });
				if (res.data.averagerecovery[i] == '2q') option['平均恢复周期'][0].children.push({ value: '2q', label: '两季' });
				if (res.data.averagerecovery[i] == '1y') option['平均恢复周期'][0].children.push({ value: '1y', label: '一年' });
				if (res.data.averagerecovery[i] == '2y') option['平均恢复周期'][0].children.push({ value: '2y', label: '两年' });
				if (res.data.averagerecovery[i] == '3y') option['平均恢复周期'][0].children.push({ value: '3y', label: '三年' });
				if (res.data.averagerecovery[i] == '5y') option['平均恢复周期'][0].children.push({ value: '5y', label: '五年' });
			}
			// }
			option['在险价值'] = [{ value: 'recent', label: '近期表现', children: [] }];
			// if (this.haveName == '在险价值') {
			for (let i = 0; i < res.data.volatility.length; i++) {
				if (res.data.VaR05[i] == '1w') option['在险价值'][0].children.push({ value: '1w', label: '一周' });
				if (res.data.VaR05[i] == '2w') option['在险价值'][0].children.push({ value: '2w', label: '两周' });
				if (res.data.VaR05[i] == '1m') option['在险价值'][0].children.push({ value: '1m', label: '一月' });
				if (res.data.VaR05[i] == '2m') option['在险价值'][0].children.push({ value: '2m', label: '两月' });
				if (res.data.VaR05[i] == '1q') option['在险价值'][0].children.push({ value: '1q', label: '一季' });
				if (res.data.VaR05[i] == '2q') option['在险价值'][0].children.push({ value: '2q', label: '两季' });
				if (res.data.VaR05[i] == '1y') option['在险价值'][0].children.push({ value: '1y', label: '一年' });
				if (res.data.VaR05[i] == '2y') option['在险价值'][0].children.push({ value: '2y', label: '两年' });
				if (res.data.VaR05[i] == '3y') option['在险价值'][0].children.push({ value: '3y', label: '三年' });
				if (res.data.VaR05[i] == '5y') option['在险价值'][0].children.push({ value: '5y', label: '五年' });
				// }
			}
			option['期望损失'] = [{ value: 'recent', label: '近期表现', children: [] }];
			// if (this.haveName == '期望损失') {
			for (let i = 0; i < res.data.ES05.length; i++) {
				if (res.data.ES05[i] == '1w') option['期望损失'][0].children.push({ value: '1w', label: '一周' });
				if (res.data.ES05[i] == '2w') option['期望损失'][0].children.push({ value: '2w', label: '两周' });
				if (res.data.ES05[i] == '1m') option['期望损失'][0].children.push({ value: '1m', label: '一月' });
				if (res.data.ES05[i] == '2m') option['期望损失'][0].children.push({ value: '2m', label: '两月' });
				if (res.data.ES05[i] == '1q') option['期望损失'][0].children.push({ value: '1q', label: '一季' });
				if (res.data.ES05[i] == '2q') option['期望损失'][0].children.push({ value: '2q', label: '两季' });
				if (res.data.ES05[i] == '1y') option['期望损失'][0].children.push({ value: '1y', label: '一年' });
				if (res.data.ES05[i] == '2y') option['期望损失'][0].children.push({ value: '2y', label: '两年' });
				if (res.data.ES05[i] == '3y') option['期望损失'][0].children.push({ value: '3y', label: '三年' });
				if (res.data.ES05[i] == '5y') option['期望损失'][0].children.push({ value: '5y', label: '五年' });
			}
			// }
			option['下行风险'] = [{ value: 'recent', label: '近期表现', children: [] }];
			// if (this.haveName == '下行风险') {
			for (let i = 0; i < res.data.downsidevolatility.length; i++) {
				if (res.data.downsidevolatility[i] == '1w') option['下行风险'][0].children.push({ value: '1w', label: '一周' });
				if (res.data.downsidevolatility[i] == '2w') option['下行风险'][0].children.push({ value: '2w', label: '两周' });
				if (res.data.downsidevolatility[i] == '1m') option['下行风险'][0].children.push({ value: '1m', label: '一月' });
				if (res.data.downsidevolatility[i] == '2m') option['下行风险'][0].children.push({ value: '2m', label: '两月' });
				if (res.data.downsidevolatility[i] == '1q') option['下行风险'][0].children.push({ value: '1q', label: '一季' });
				if (res.data.downsidevolatility[i] == '2q') option['下行风险'][0].children.push({ value: '2q', label: '两季' });
				if (res.data.downsidevolatility[i] == '1y') option['下行风险'][0].children.push({ value: '1y', label: '一年' });
				if (res.data.downsidevolatility[i] == '2y') option['下行风险'][0].children.push({ value: '2y', label: '两年' });
				if (res.data.downsidevolatility[i] == '3y') option['下行风险'][0].children.push({ value: '3y', label: '三年' });
				if (res.data.downsidevolatility[i] == '5y') option['下行风险'][0].children.push({ value: '5y', label: '五年' });
			}
			option['痛苦指数'] = [{ value: 'recent', label: '近期表现', children: [] }];
			// }
			// if (this.haveName == '痛苦指数') {
			for (let i = 0; i < res.data.painindex.length; i++) {
				if (res.data.painindex[i] == '1w') option['痛苦指数'][0].children.push({ value: '1w', label: '一周' });
				if (res.data.painindex[i] == '2w') option['痛苦指数'][0].children.push({ value: '2w', label: '两周' });
				if (res.data.painindex[i] == '1m') option['痛苦指数'][0].children.push({ value: '1m', label: '一月' });
				if (res.data.painindex[i] == '2m') option['痛苦指数'][0].children.push({ value: '2m', label: '两月' });
				if (res.data.painindex[i] == '1q') option['痛苦指数'][0].children.push({ value: '1q', label: '一季' });
				if (res.data.painindex[i] == '2q') option['痛苦指数'][0].children.push({ value: '2q', label: '两季' });
				if (res.data.painindex[i] == '1y') option['痛苦指数'][0].children.push({ value: '1y', label: '一年' });
				if (res.data.painindex[i] == '2y') option['痛苦指数'][0].children.push({ value: '2y', label: '两年' });
				if (res.data.painindex[i] == '3y') option['痛苦指数'][0].children.push({ value: '3y', label: '三年' });
				if (res.data.painindex[i] == '5y') option['痛苦指数'][0].children.push({ value: '5y', label: '五年' });
			}

			for (const key in option) {
				option[key].push({ value: 'since', label: '从那时起', children: since });
			}
			this.optionsselect = option;
		},

		getFormData() {
			setTimeout(() => {
				this.listSelect = this.listSelectX.map((item) => {
					let return_item = { ...item };
					item.data.map((obj, index) => {
						let filterData = this.data.filter((ditem) => {
							return ditem?.children
								? ditem.children.some((dobj) => {
										return dobj.label == obj.labelName;
								  })
								: ditem.label.includes(obj.labelName);
						});

						var is_range = filterData?.[0]?.children
							? filterData?.[0]?.children?.find((ditem) => {
									return ditem.label == obj.labelName;
							  })?.is_range
							: filterData.filter((ditem) => {
									return ditem.label.includes(obj.labelName);
							  })?.[0]?.is_range;
						return_item.data[index] = {
							...obj,
							is_range,
							dataResult: obj.dataResult.map((item) => {
								return is_range ? { ...item, mathRange: item.mathRange ? item.mathRange : '100' } : item;
							})
						};
					});
					return return_item;
				});
				// this.listSelect = this.listSelectX;
				this.radioSelf = this.radioInput2;
				this.radioSelf2 = this.radioInput3;
				this.radio = this.radioType2;
				this.radio2 = this.radioType3 == 'radioSelf2' ? 'radioSelf2' : this.radioType3 == 'a' ? 'a' : 'b';
				this.radioFilterOrOut = this.isSame2;
			}, 0);
		}
	},
	//生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	//生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.listSelect = this.listSelectX;
		this.radioSelf = this.radioInput2;
		this.radioSelf2 = this.radioInput3;
		this.radio = this.radioType2;
		this.radio2 = this.radioType3 == 'radioSelf2' ? 'radioSelf2' : this.radioType3 == 'a' ? 'a' : 'b';
		this.radioFilterOrOut = this.isSame2;
	},
	beforeCreate() {}, //生命周期 - 创建之前
	beforeMount() {}, //生命周期 - 挂载之前
	beforeUpdate() {}, //生命周期 - 更新之前
	updated() {}, //生命周期 - 更新之后
	beforeDestroy() {}, //生命周期 - 销毁之前
	destroyed() {}, //生命周期 - 销毁完成
	activated() {} //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
.dialogFilerDX {
	.boxItemDetailM {
		padding-left: 24px;
		// margin-bottom: 16px;
		margin-top: 16px;
	}
	.boxItemDetail {
		padding-left: 24px;
		// margin-bottom: 16px;
		// margin-top: 16px;
		padding: 8px 24px;
	}
	.boxItemDetail:hover {
		// background: #e9e9e9;
	}
	::v-deep .el-dialog__header {
		padding: 0px !important ;
	}
	.titleContent {
		font-size: 16px;
		margin-bottom: 8px;
		line-height: 24px;
		color: rgba(0, 0, 0, 0.85);
	}
	.contentBoxFilter {
		padding-top: 8px;
		padding-bottom: 8px;
		margin-left: -24px;
		padding-left: 24px;
		// margin-top: 16px;
		display: flex;
		width: 100%;
		// height:40px;
		align-items: center;
		justify-content: start;
		.contentItem {
			padding-right: 16px;
		}

		.contentDel {
			margin-right: 16px;
			justify-content: end;
			// display: flex;
			display: none;
		}
	}
	.contentBoxFilter:hover {
		background: rgba(0, 0, 0, 0.04);
		.contentDel {
			margin-right: 16px;
			justify-content: end;
			// display: flex;
			display: flex;
		}
	}
}

/* 筛选范围和筛选条件样式 */
.filter-section-container {
  width: 100%;
  height: 558px;
  overflow-y: auto;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e9e9e9;
}

.tool-header {
  height: 40px;
  line-height: 40px;
  padding-left: 16px;
  font-weight: 500;
  font-size: 14px;
  color: #333;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e9e9e9;
}

.tool-content {
  padding: 10px 0;
  background-color: #fff;
}
</style>
