<template>
	<div class="flex_between">
		<!-- 基金名称 -->
		<div class="basic_info_name px-12 py-12 flex_between" style="width: 377px; align-items: flex-end">
			<div class="basic_info_fund_risk flex_start">
				<!-- <div style="color: #ff404f">风险等级:{{ data.risk_level }}</div> -->
				<!-- <div class="flex_start ml-4">
					<svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
						<g clip-path="url(#clip0_3044_36180)">
							<path
								fill-rule="evenodd"
								clip-rule="evenodd"
								d="M6.0001 0.600098C8.98215 0.600098 11.4001 3.01804 11.4001 6.0001C11.4001 8.98215 8.98215 11.4001 6.0001 11.4001C3.01804 11.4001 0.600098 8.98215 0.600098 6.0001C0.600098 3.01804 3.01804 0.600098 6.0001 0.600098ZM6.0001 1.51617C3.52429 1.51617 1.51617 3.52429 1.51617 6.0001C1.51617 8.4759 3.52429 10.484 6.0001 10.484C8.4759 10.484 10.484 8.4759 10.484 6.0001C10.484 3.52429 8.4759 1.51617 6.0001 1.51617ZM6.0001 8.16974C6.26638 8.16974 6.48224 8.3856 6.48224 8.65188C6.48224 8.91816 6.26638 9.13403 6.0001 9.13403C5.73382 9.13403 5.51796 8.91816 5.51796 8.65188C5.51796 8.3856 5.73382 8.16974 6.0001 8.16974ZM6.0001 3.15545C6.50635 3.15545 6.98367 3.32903 7.34528 3.64604C7.72135 3.9751 7.92867 4.41746 7.92747 4.89117C7.92747 5.58787 7.46822 6.21465 6.75706 6.48827C6.53407 6.57385 6.38461 6.79081 6.38461 7.02827V7.30188C6.38461 7.35492 6.34121 7.39831 6.28818 7.39831H5.70961C5.65657 7.39831 5.61318 7.35492 5.61318 7.30188V7.04273C5.61318 6.76429 5.69514 6.48947 5.85304 6.26045C6.00854 6.03626 6.2255 5.8651 6.48104 5.76746C6.89206 5.60956 7.15724 5.26604 7.15724 4.89117C7.15724 4.35961 6.63773 3.92688 6.0001 3.92688C5.36246 3.92688 4.84296 4.35961 4.84296 4.89117V4.98278C4.84296 5.03581 4.79956 5.0792 4.74653 5.0792H4.16796C4.11492 5.0792 4.07153 5.03581 4.07153 4.98278V4.89117C4.07153 4.41746 4.27885 3.9751 4.65492 3.64604C5.01653 3.33023 5.49385 3.15545 6.0001 3.15545Z"
								fill="black"
								fill-opacity="0.45"
							/>
						</g>
						<defs>
							<clipPath id="clip0_3044_36180">
								<rect width="12" height="12" fill="white" />
							</clipPath>
						</defs>
					</svg>
				</div> -->
			</div>
			<div class="basic_info_fund_name">
				<div>{{ info.name }}</div>
				<div>{{ info.code }}</div>
			</div>
			<!-- 暂无入池功能 -->
			<!-- <div class="basic_info_fund_action flex_between px-8 py-4">
				<div>+</div>
				<div>添加入池</div>
			</div> -->
		</div>
		<div class="basic_info_nav px-12 py-12" style="width: 222px">
			<div class="flex_start">
				<div class="flex_start mr-16">
					<div class="title_bar mr-8" style="background-color: #4096ff"></div>
					<div>单位净值</div>
				</div>
				<div style="font-size: 18px; text-align: center">{{ fix4(data.nav) }}</div>
			</div>
			<div class="flex_start">
				<div class="flex_start mr-16">
					<div class="title_bar mr-8" style="background-color: #4096ff"></div>
					<div>风险等级</div>
				</div>
				<div style="font-size: 18px; text-align: center; color: #cf1322">{{ data.risk_level }}</div>
			</div>
			<!--<div>
				<div class="flex_start mb-4">
					<div class="title_bar mr-8" style="background-color: #4096ff"></div>
					<div class="mr-8">累计净值</div>
					<div>{{ fix4(data.cnav) }}</div>
				</div>
				 <div class="flex_start">
					<div class="title_bar mr-8" style="background-color: #7388a9"></div>
					<div class="mr-8">复权净值</div>
					<div>{{ fix3(data.wnav) }}</div>
				</div>
			</div> -->
		</div>
	</div>
</template>

<script>
// 获取收益数据、能力数据
import { getReturnInfo, getCapabilityInfo } from '@/api/pages/Analysis.js';

export default {
	data() {
		return {
			info: {},
			data: { risk_level: 'R5.2', nav: 2.758, cnav: 3.176, wnav: 3.8169 }
		};
	},
	methods: {
		getData(info) {
			this.info = info;
			this.getReturnInfo();
			this.getCapabilityInfo();
		},
		async getReturnInfo() {
			let data = await getReturnInfo({ code: this.info.code, type: this.info.type, flag: this.info.flag });
			if (data?.mtycode == 200) {
				this.data = { ...this.data, ...data?.data };
			}
		},
		async getCapabilityInfo() {
			let data = await getCapabilityInfo({
				codes: [this.info.code],
				type: this.info.type,
				flag: [this.info.flag],
				item: ['风险等级'],
				starat_date: this.info.starat_date,
				end_date: this.info.end_date
			});
			if (data?.mtycode == 200) {
				let risk_level = data?.data.find((v) => v.item == '风险等级')?.description || '--';
				this.data = { ...this.data, risk_level };
			}
		},
		// 取数据点后三位
		fix4(val) {
			return val * 1 && !isNaN(val) ? (val * 1).toFixed(4) : '--';
		},
		createPrintWord() {
			let list = [
				{ label: '风险等级', value: 'risk_level' },
				{ label: '单位净值', value: 'nav' },
				{ label: '累计净值', value: 'cum_nav' },
				{ label: '复权净值', value: 'weight_nav' }
			];
			return [...this.$exportWord.exportDoubleColumnTable(list, this.data)];
		}
	}
};
</script>

<style lang="scss" scoped>
.basic_info_name {
	position: relative;
	border-radius: 4px;
	background: linear-gradient(266deg, #ecf5ff 14.58%, #4096ff 100%);
	box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.15);
	.basic_info_fund_name {
		font-size: 18px;
		font-weight: 500;
		color: #ffffff;
	}
	.basic_info_fund_risk {
		width: 97px;
		height: 22px;
		justify-content: flex-end;
		font-size: 12px;
		position: absolute;
		right: 0;
		top: 0;
		// border-radius: 0 0 10px 10px; /* 使其左下角为圆角 */
		// background: #fff1f0;
		overflow: hidden;
		// ::before {
		// 	content: '';
		// 	position: absolute;
		// 	bottom: -30px; /* 调整凹进来的深度 */
		// 	left: -4px;
		// 	width: 32px;
		// 	height: 52px; /* 调整凹进来的高度 */
		// 	background: linear-gradient(266deg, #ecf5ff 14.58%, #ecf5ff 100%);
		// 	border-radius: 0 24px 0px 0px; /* 使其左下角为圆角 */
		// 	// border: 1px solid #4096ff;
		// }
	}
	.basic_info_fund_action {
		font-size: 12px;
		color: #4096ff;
		border-radius: 4px;
		background: linear-gradient(180deg, #fff 0%, #ecf5ff 100%);
		/* 主按钮 */
		box-shadow: 0px 2px 0px 0px rgba(0, 0, 0, 0.04);
		cursor: pointer;
	}
}
.basic_info_nav {
	font-size: 14px;
	border-radius: 4px;
	background: linear-gradient(270deg, #ffffff 0%, #ecf5ff 100%);
	.title_bar {
		width: 3px;
		height: 14px;
		border-radius: 32px;
	}
}
</style>
