<template>
	<div :class="!quickPrint ? 'analysis_main mt-12' : ''">
		<!-- <el-breadcrumb v-if="!quickPrint" separator="/">
      <el-breadcrumb-item :to="{ path: '/subscriptionCenter' }"
        >订阅中心</el-breadcrumb-item
      >
      <el-breadcrumb-item>新增订阅</el-breadcrumb-item>
    </el-breadcrumb>-->
		<div class="chart_one">
			<div class="subscription_form">
				<div v-if="!quickPrint">
					<div class="subscription_form_item">
						<div class="subscription_title">名称</div>
						<div>
							<el-input v-model="name" placeholder="请输入" style="width: 440px"></el-input>
						</div>
					</div>
					<div class="subscription_form_item">
						<div class="subscription_title">简介</div>
						<div>
							<el-input type="textarea" v-model="des" placeholder="请输入内容" :rows="1" style="width: 668px"></el-input>
						</div>
					</div>
				</div>

				<subscription-select ref="subscriptionSelect"></subscription-select>
				<!-- <div class="subscription_form_item">
          <div class="subscription_title">选择报告模板</div>
          <div>
            <el-select v-model="reportTemplate" placeholder="请选择报告模板">
              <el-option
                v-for="item in reportTemplateList"
                :key="item.modelid"
                :label="item.model_name"
                :value="item.modelid"
              >
              </el-option>
            </el-select>
            <el-link style="margin-left: 16px" @click="goAddModel"
              >没有想要的？前往新增模版</el-link
            >
          </div>
        </div>-->
				<div v-if="!quickPrint">
					<div class="subscription_form_item">
						<el-checkbox v-model="status">使用定时发送</el-checkbox>
					</div>
					<div>
						<div class="subscription_form_item">
							<div class="subscription_title">
								配置定时频率
								<span style="color: rgba(0, 0, 0, 0.45)">(频率/天)</span>
							</div>
							<div>
								<el-select v-model="intervalType" placeholder="请选择" style="width: 104px; margin-right: 8px">
									<el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
								</el-select>
								<el-select v-show="dayList.length" v-model="intervalDay" placeholder="请选择" style="width: 104px; margin-right: 8px">
									<el-option v-for="item in dayList" :key="item.value" :label="item.label" :value="item.value"></el-option>
								</el-select>
								<el-select v-model="intervalTime" placeholder="请选择" style="width: 124px">
									<el-option v-for="item in timeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
								</el-select>
							</div>
						</div>
						<div class="subscription_form_item">
							<div class="subscription_title">
								<el-button type="primary" @click="chooseEmail">选择订阅邮箱</el-button>
							</div>
							<div class="email_list_tags">
								<!-- <el-input type="textarea" v-model="emailList" placeholder="" style="width: 668px"></el-input> -->
								<el-tag
									type="info"
									v-for="item in emailList"
									:key="item.email_id"
									closable
									style="margin: 4px 0 4px 4px"
									@close="closeTag(item.email_id)"
									>{{ item.name }}</el-tag
								>
							</div>
						</div>
					</div>
				</div>

				<div class="subscription_form_item">
					<el-button type="primary" @click="submit(1)">转为word</el-button>
					<el-button type="primary" @click="submit(2)">所见所得</el-button>
					<el-button @click="goBack">取消</el-button>
				</div>
			</div>
		</div>
		<email-global ref="emailGlobal" @resolveEmailIds="resolveEmailIds"></email-global>
	</div>
</template>

<script>
import { mapMutations, mapState, Store } from 'vuex';
import emailGlobal from './components/emailGlobal.vue';
import searchComponent from '@/components/components/components/search/index.vue';

import subscriptionSelect from './components/subscriptionSelect';

// 获取组件json配置
import { postSubscriptionList, getTaskDetail, putSubscriptionList, getReportTemplateList } from '@/api/pages/NodeServer.js';

import { getUserConfig } from '@/api/pages/SystemAlpha.js';
import { getPoolList, getPoolDetail } from '@/api/pages/SystemMixed.js';
import { getModelList } from '@/api/pages/Tools.js';

import { alphaGo } from '@/assets/js/alpha_type.js';
export default {
	components: {
		emailGlobal,
		searchComponent,
		subscriptionSelect
	},
	props: {
		quickPrint: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			custom_introduce_type: 'text',
			custom_introduce: '',
			name: '',
			des: '',
			imageId: '',
			image: '',
			imageIntroduce: '',
			reportTemplate: '',
			reportTemplateList: [],
			hoverIndex: null,
			customIndex: null,
			status: false,
			current: 0,
			longSubscription: true,
			imgage: '',
			options: [
				{
					label: '基金',
					value: 'fund'
				},
				{
					label: '基金经理',
					value: 'manager'
				},
				{
					label: '基金池',
					value: 'pool'
				}
			],
			info: {},
			model: '',
			modelList: [],
			interval: '',
			intervalList: [
				{
					label: '1天',
					value: '1'
				},
				{
					label: '2天',
					value: '2'
				},
				{
					label: '3天',
					value: '3'
				},
				{
					label: '4天',
					value: '4'
				},
				{
					label: '5天',
					value: '5'
				},
				{
					label: '6天',
					value: '6'
				}
			],
			emailList: [],
			userConfigList: [],
			subscriptionId: '',
			timeList: [
				{ label: '00:00-01:00', value: '0' },
				{ label: '01:00-02:00', value: '1' },
				{ label: '02:00-03:00', value: '2' },
				{ label: '03:00-04:00', value: '3' },
				{ label: '04:00-05:00', value: '4' },
				{ label: '05:00-06:00', value: '5' },
				{ label: '06:00-07:00', value: '6' },
				{ label: '07:00-08:00', value: '7' },
				{ label: '08:00-09:00', value: '8' },
				{ label: '09:00-10:00', value: '9' },
				{ label: '10:00-11:00', value: '10' },
				{ label: '11:00-12:00', value: '11' },
				{ label: '12:00-13:00', value: '12' },
				{ label: '13:00-14:00', value: '13' },
				{ label: '14:00-15:00', value: '14' },
				{ label: '15:00-16:00', value: '15' },
				{ label: '16:00-17:00', value: '16' },
				{ label: '17:00-18:00', value: '17' },
				{ label: '18:00-19:00', value: '18' },
				{ label: '19:00-20:00', value: '19' },
				{ label: '20:00-21:00', value: '20' },
				{ label: '21:00-22:00', value: '21' },
				{ label: '22:00-23:00', value: '22' },
				{ label: '23:00-24:00', value: '23' }
			],
			typeList: [
				{ label: '每天', value: 'day' },
				{ label: '每周', value: 'week' },
				{ label: '每月', value: 'month' }
			],
			weekList: [
				{ label: '每周一', value: '1' },
				{ label: '每周二', value: '2' },
				{ label: '每周三', value: '3' },
				{ label: '每周四', value: '4' },
				{ label: '每周五', value: '5' },
				{ label: '每周六', value: '6' },
				{ label: '每周日', value: '7' }
			],
			monthList: [
				{ label: '一号', value: '1' },
				{ label: '二号', value: '2' },
				{ label: '三号', value: '3' },
				{ label: '四号', value: '4' },
				{ label: '五号', value: '5' },
				{ label: '六号', value: '6' },
				{ label: '七号', value: '7' },
				{ label: '八号', value: '8' },
				{ label: '九号', value: '9' },
				{ label: '十号', value: '10' },
				{ label: '十一号', value: '11' },
				{ label: '十二号', value: '12' },
				{ label: '十三号', value: '13' },
				{ label: '十四号', value: '14' },
				{ label: '十五号', value: '15' },
				{ label: '十六号', value: '16' },
				{ label: '十七号', value: '17' },
				{ label: '十八号', value: '18' },
				{ label: '十九号', value: '19' },
				{ label: '二十号', value: '20' },
				{ label: '二十一号', value: '21' },
				{ label: '二十二号', value: '22' },
				{ label: '二十三号', value: '23' },
				{ label: '二十四号', value: '24' },
				{ label: '二十五号', value: '25' },
				{ label: '二十六号', value: '26' },
				{ label: '二十七号', value: '27' },
				{ label: '二十八号', value: '28' },
				{ label: '二十九号', value: '29' },
				{ label: '三十号', value: '30' },
				{ label: '三十一号', value: '31' }
			],
			intervalType: 'day',
			intervalDay: '1',
			intervalTime: '0',
			poolList: []
		};
	},
	computed: {
		dayList() {
			return this.intervalType == 'week' ? this.weekList : this.intervalType == 'month' ? this.monthList : [];
		}
	},
	async mounted() {
		this.longSubscription = this.$route.query?.longSubscription ? true : false;
		await this.getUserConfig();
		this.getPoolList();
		this.subscriptionId = this.$route.query?.id;
		this.getReportTemplateList();
		if (this.subscriptionId) {
			this.getTaskDetail(this.subscriptionId);
		}
	},
	methods: {
		...mapMutations(['setPostData']),
		// 获取池子列表
		async getPoolList() {
			let data = await getPoolList();
			this.poolList = data;
		},
		// 获取报告模板列表
		async getReportTemplateList() {
			//   let data = await getReportTemplateList({
			//     user_id: localStorage.getItem("id"),
			//   });
			//   if (data?.mtycode == 200) {
			//     this.reportTemplateList = data?.data;
			//   }
			let res = await getModelList({
				type: 'equity',
				ismanager: 'true',
				flag: 'print_report'
			});
			if (res.mtycode == 200) {
				let list = [];
				let data = res.data;
				for (const key in data) {
					if (key != 'user_list') {
						data[key].map((item) => {
							let index = list.findIndex((obj) => {
								return obj.modelid == item.modelid;
							});
							if (index == -1) {
								list.push({
									...item,
									modelid: item.modelid,
									template_id: item.model_args?.template_id,
									template_name: item.model_name,
									custom_logo: item.model_args?.custom_logo,
									template_description: item.model_args?.template_description,
									model_time: this.moment(item.model_time).format('YYYY-MM-DD'),
									ispublic: item.ispublic == 'False' ? '不公开' : '公开',
									model_type: key.includes('self') ? '个人模板' : key.includes('owl') ? '慧捕基模板' : '公开模板'
									// user_name: this.user_list.find((obj) => {
									// 	return obj.id == item.user_id;
									// }).username
								});
							} else {
								if (list[index].model_type !== '个人模板') {
									list[index] = {
										...item,
										modelid: item.modelid,
										template_id: item.model_args?.template_id,
										template_name: item.model_name,
										custom_logo: item.model_args?.custom_logo,
										template_description: item.model_args?.template_description,
										model_time: this.moment(item.model_time).format('YYYY-MM-DD'),
										ispublic: item.ispublic == 'False' ? '不公开' : '公开',
										model_type: key.includes('self') ? '个人模板' : key.includes('owl') ? '慧捕基模板' : '公开模板'
										// user_name: this.user_list.find((obj) => {
										// 	return obj.id == item.user_id;
										// }).username
									};
								}
							}
						});
					}
				}
				this.reportTemplateList = list;
			}
		},
		// 获取订阅详情信息
		async getTaskDetail(id) {
			let data = await getTaskDetail({ id });
			if (data?.mtycode == 200) {
				this.name = data?.data?.name;
				this.des = data?.data?.description;
				this.subscriptionList = data?.data?.list?.map((obj) => {
					let modelList = null;
					if (obj.flag == 'fund') {
						modelList = this.userConfigList.filter((item) => {
							return !item.ismanager && item.type == obj.type;
						});
					} else {
						modelList = this.userConfigList.filter((item) => {
							return item.ismanager;
						});
					}
					return {
						...obj,
						model: obj.template_id * 1,
						type: obj.flag,
						modelList,
						info: {
							code: obj.code,
							name: obj.name,
							flag: obj.flag,
							model: obj.template_id * 1,
							type: obj.type
						}
					};
				});
				this.reportTemplate = data?.data.template_id;
				this.status = data?.data?.is_timing_send;
				let frequency = data?.data?.frequency?.split(',');
				this.intervalType = frequency?.[0];
				this.intervalDay = frequency?.[1];
				this.intervalTime = frequency?.[2];
				this.emailList = data?.data?.email.map((item) => {
					return {
						...item,
						name: item.email_name
					};
				});
			}
		},
		// 获取用户设置模版
		async getUserConfig() {
			let { data, mtycode, mtymessage } = await getUserConfig();
			if (mtycode == 200) {
				this.userConfigList = data;
				// this.filterUserConfig();
			}
		},
		// 鼠标移入item
		mouseenterItem(index) {
			this.hoverIndex = index;
			this.current = index;
		},
		mouseenterCustomItem(index) {
			this.customIndex = index;
		},
		// 选择订阅邮箱
		async chooseEmail() {
			this.$refs['emailGlobal'].getData(this.emailList);
		},
		// 添加声明
		addCustomDescription() {
			this.customDescription.push({
				title: '',
				content: ''
			});
		},
		// 前往新增模版
		goAddModel() {
			this.$router.push('/addReportTemplate');
		},
		deleteCustomItem(index) {
			this.customDescription.splice(index, 1);
		},
		// 获取子组件传递邮箱id列表
		resolveEmailIds(ids) {
			this.$refs['emailGlobal']?.closeDialog();
			this.emailList = ids;
		},
		// 关闭选中邮箱标签
		closeTag(id) {
			let index = this.emailList.findIndex((item) => {
				return item.email_id == id;
			});
			this.emailList.splice(index, 1);
		},
		// 文件上传结束
		uploadOver(response) {
			if (response.mtycode == 200) {
				setTimeout(() => {
					this.image = response.data;
				}, 1000);
			} else {
				this.$message.warn('图片上传失败,请重新上传');
			}
		},
		// 介绍图片上传结束
		uploadIntroduceOver(response) {
			if (response.mtycode == 200) {
				setTimeout(() => {
					this.imageIntroduce = response.data;
				}, 1000);
			} else {
				this.$message.warn('图片上传失败,请重新上传');
			}
		},
		// 提交表单
		async submit(num) {
			// console.log('this num', num);
			let print_type = num;
			this.subscriptionList = this.$refs.subscriptionSelect.getItemInfos(); //父调子方法 获取所选数据
			// console.log('this.subscriptionList', this.subscriptionList);
			let name = this.name;
			let description = this.des;
			let status = this.status;
			let interval = `${this.intervalType},${this.intervalDay},${this.intervalTime}`;
			let email_ids = this.emailList.map((item) => {
				return item.email_id;
			});
			let subscriptionList = [];
			for (let index = 0; index < this.subscriptionList.length; index++) {
				let item = this.subscriptionList[index];
				if (item.info?.flag == 'pool') {
					let data = await getPoolDetail({ id: item.info.code, have: '' });
					if (data?.mtycode == 200) {
						data?.data.table.map((obj) => {
							subscriptionList.push({
								code: obj?.fund_code,
								name: obj?.name,
								flag: 'fund',
								type: obj?.type
							});
						});
					}
				} else {
					subscriptionList.push({
						code: item.info?.code,
						name: item.info?.name,
						flag: item.info?.flag,
						template_id: item?.model,
						type: item.info?.type
					});
				}
			}
			let template_id = this.reportTemplateList.find((item) => {
				return item.modelid == this.reportTemplate;
			})?.model_args?.template_id;
			let postData = {
				print_type,
				name,
				description,
				subscriptionList,
				frequency: interval,
				is_timing_send: status,
				email_list: email_ids,
				template_id,
				is_quick: this.longSubscription == false ? true : false,
				user_id: localStorage.getItem('id')
			};
			if (this.subscriptionId) {
				this.putSubscriptionList({
					...postData,
					subscription_id: this.subscriptionId
				});
			} else {
				console.log('postData is ', postData);
				// if (postData.print_type == 2) {
				// } else {
				this.setPostData(postData);
				this.postSubscriptionList(postData);
				// }
			}
		},
		// 新增订阅任务
		async postSubscriptionList(postData) {
			// console.log('111postdata', postData);
			let data = await postSubscriptionList(postData);

			if (data?.mtycode == 200) {
				if (this.quickPrint) {
					this.$emit('resolveFather');
				} else {
					this.goBack();
				}
			} else {
				this.$message.warning(data?.mtymessage || '添加失败');
			}
		},
		// 修改订阅任务
		async putSubscriptionList(postData) {
			let data = await putSubscriptionList(postData);
			if (data?.mtycode == 200) {
				this.$message.success(data?.mtymessage || '修改成功');

				this.$router.push('/subscriptionCenter');
			} else {
				this.$message.warning(data?.mtymessage || '修改失败');
			}
		},
		// 监听订阅类型选择
		changeType() {
			console.log(this.subscriptionList);
		},
		// 取消编辑
		goBack() {
			this.$router.push('/subscriptionCenter');
		}
	}
};
</script>

<style lang="scss" scoped>
.subscription_form {
	margin: 8px 24px 0 24px;
	font-family: 'PingFang';
	font-style: normal;
	font-weight: 400;
	font-size: 14px;
	line-height: 22px;
	color: rgba(0, 0, 0, 0.85);
	::v-deep.avatar-uploader .el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
		width: 100px;
		height: 100px;
	}
	::v-deep.content-uploader .el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
		width: 400px;
		height: 200px;
	}
	.avatar-uploader .el-upload:hover {
		border-color: #409eff;
	}
	.subscription_form_item {
		margin-bottom: 24px;
		.avatar-uploader-icon {
			font-size: 28px;
			color: #8c939d;
			width: 100px;
			height: 100px;
			line-height: 100px;
			text-align: center;
		}
		.avatar {
			width: 100px;
			height: 100px;
			display: block;
			object-fit: cover;
		}
		.subscription_title {
			margin-bottom: 8px;
		}
		.email_list_tags {
			width: 668px;
			border: 1px solid #d9d9d9;
			border-radius: 4px;
		}
		.subscription_content {
			display: flex;
			align-items: center;
			.delete_icon {
				margin-left: 16px;
				cursor: pointer;
			}
			.delete_icon:hover {
				svg {
					path {
						fill: #4096ff;
					}
				}
			}
		}
		.delete_icon {
			margin-top: 8px;
			margin-left: 16px;
			cursor: pointer;
		}
		.delete_icon:hover {
			svg {
				path {
					fill: #4096ff;
				}
			}
		}
		.add_subscription {
			display: flex;
			justify-content: center;
			align-items: center;
			cursor: pointer;
			width: 440px;
			height: 32px;
			background: #ffffff;
			border: 1px dashed #d9d9d9;
			box-shadow: 0px 2px 0px rgba(0, 0, 0, 0.016);
			border-radius: 4px;
		}
		.add_subscription:hover {
			border-color: #4096ff;
		}
	}
}
</style>
