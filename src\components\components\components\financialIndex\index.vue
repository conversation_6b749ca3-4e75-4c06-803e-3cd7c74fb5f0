<template>
	<div id="financialIndex">
		<analysis-card-title title="组合整体财务指标" image_id="financialIndex">
			<div class="flex_start">
				<!-- <div class="mr-12">
					<el-select v-model="pool" placeholder="请选择比较池">
						<el-option v-for="item in poolList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
					</el-select>
				</div>
				<div class="mr-12">
					<el-select v-model="type" placeholder="请选择持仓类型">
						<el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
					</el-select>
				</div> -->
				<div>
					<el-select v-model="key" placeholder="请选择比较指标" @change="setOption">
						<el-option v-for="item in keyList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
					</el-select>
				</div>
			</div>
		</analysis-card-title>
		<div class="charts_center_class" v-loading="loading">
			<v-chart
				style="width: 100%; height: 400px"
				ref="financialIndex"
				autoresize
				v-loading="loading"
				element-loading-text="暂无数据"
				element-loading-spinner="el-icon-document-delete"
				element-loading-background="rgba(239, 239, 239, 0.5)"
				:options="option"
			/>
		</div>
	</div>
</template>

<script>
import { lineChartOption } from '@/utils/chartStyle.js';

import { getStockFinanceInfo } from '@/api/pages/Analysis.js';

export default {
	data() {
		return {
			pool: '',
			poolList: [],
			type: '',
			typeList: [],
			key: 'pb',
			keyList: [
				{ label: '市净率', value: 'pb' },
				{ label: '市盈率', value: 'pe' },
				{ label: '净资产收益率', value: 'roe' },
				{ label: '市现率', value: 'pcf' },
				{ label: '股息率', value: 'dividendratiolyr' }
			],
			loading: true,
			option: {},
			data: [],
			info: {}
		};
	},
	methods: {
		async getData(info) {
			this.info = info;
			this.getStockFinanceInfo();
		},
		async getStockFinanceInfo() {
			this.loading = true;
			let data = await getStockFinanceInfo({
				code: this.info.code,
				type: this.info.type,
				flag: this.info.flag,
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			this.loading = false;
			if (data?.mtycode == 200) {
				this.data = data?.data;
			}
			this.setOption();
		},
		// 获取考察指标列表
		setOption() {
			let result = this.data.sort((a, b) => {
				return this.moment(this.moment(a.yearqtr, 'YYYY QQ').format()).isBefore(this.moment(b.yearqtr, 'YYYY QQ').format()) ? -1 : 1;
			});
			this.option = lineChartOption({
				color: ['#4096ff', '#4096ff', '#E8684A'],
				toolbox: 'none',
				legend: {
					bottom: '0',
					data: ['整体财务指标', '相对排名']
				},
				xAxis: [
					{
						data: result.map((v) => v.yearqtr)
					}
				],
				tooltip: {
					backgroundColor: '#ffffff',
					formatter: function (obj) {
						var value = `<div style="font-size:14px;">` + obj?.[0].axisValue + `</div>`;
						for (let i = 0; i < obj.length; i++) {
							value +=
								`<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
								`<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
								(obj?.[i].borderColor ? obj?.[i].borderColor : obj?.[i].color) +
								`;"></div>` +
								`<div style="font-family: PingFang SC;">` +
								obj?.[i].seriesName +
								'</div></div>' +
								`<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
								Number(obj?.[i].value?.[1]).toFixed(2) +
								'%</div>' +
								`</div>`;
						}
						return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
					}
				},
				grid: {
					top: '12px',
					left: '48px',
					right: '48px',
					bottom: '36px'
				},
				yAxis: [
					{
						name: '整体财务指标',
						type: 'value',
						nameLocation: 'middle', // 设置名称居中
						nameGap: 48, // 控制名称距离轴线的距离
						nameTextStyle: {
							align: 'center'
						}
					},
					{
						name: '相对排名',
						type: 'value',
						nameLocation: 'middle', // 设置名称居中
						nameGap: 48, // 控制名称距离轴线的距离
						nameRotate: 270,
						nameTextStyle: {
							align: 'center'
						}
					}
				],
				series: [
					{
						name: '整体财务指标',
						data: result.map((v, i) => [v.yearqtr, v[this.key]]),
						type: 'bar',
						yAxisIndex: 0,
						connectNulls: true,
						symbol: 'none',
						barGap: 0,
						itemStyle: {
							borderColor: '#4096ff',
							color: new echarts.graphic.LinearGradient(
								0,
								0,
								0,
								1, // 渐变方向，这里表示从上到下
								[
									{ offset: 0, color: '#4096ff' }, // 渐变起始颜色
									{ offset: 1, color: '#85AEFF' } // 渐变结束颜色
								]
							)
						}
					},
					{
						name: '相对排名',
						data: result.map((v, i) => [v.yearqtr, v[this.key + 'Rank'] * 100]),
						type: 'line',
						yAxisIndex: 1,
						connectNulls: true,
						symbol: 'none',
						itemStyle: {
							color: '#E8684A'
						},
						lineStyle: {
							color: '#E8684A'
						}
					}
				]
			});
		},
		async createPrintWord(info) {
			await this.getData(info);
			return await new Promise((resolve, reject) => {
				this.$nextTick(async () => {
					let height = this.$refs['financialIndex'].$el.clientHeight;
					let width = this.$refs['financialIndex'].$el.clientWidth;
					let chart = this.$refs['financialIndex'].getDataURL({
						type: 'jpg',
						pixelRatio: 3,
						backgroundColor: '#fff'
					});
					resolve([
						...this.$exportWord.exportTitle('组合整体财务指标'),
						// ...this.$exportWord.exportDescripe('基准指数为：' + this.indexTypeList.find((v) => v.value == this.indexType)?.label),
						...this.$exportWord.exportChart(chart, { width, height })
					]);
				});
			});
		}
	}
};
</script>

<style></style>
