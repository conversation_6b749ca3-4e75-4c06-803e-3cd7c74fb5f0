<!--  -->
<template>
	<div class="">
		<el-dialog :visible.sync="visdescriobe2" width="60vw" destroy-on-close>
			<template slot="title">
				<span style="font-size: 25px">筛选字段取值说明</span
				><el-input v-model="searchdetail" placeholder="请输入查询字段名" style="margin-left: 20px; width: 200px"></el-input>
			</template>
			<div class="sceollbox" style="overflow-x: hidden">
				<el-scrollbar style="height: 60vh; overflow-x: hidden">
					<div>
						<el-row>
							<el-col :span="24">
								<div style="display: flex; flex-wrap: wrap">
									<div style="width: 50%" v-for="(item, index) in riskdata1" :key="index">
										<div v-show="showlist1[index]">
											<el-card>
												<div slot="header" style="display: flex" class="clearfix">
													<div class="points"></div>
													<span>{{ item.description }}</span>
												</div>
												<div>
													{{ item.explain }}
												</div>
												<hr />
												<div>
													按日期划分：
													<br />
													最小值：{{ item.min }} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 25%：{{ item.persents25 }} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
													50%: {{ item.persents50 }} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 75%:
													{{ item.persents75 }} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 最大值：{{ item.max }} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
												</div>
												<hr />
												<div>
													按固定时间节点:
													<br />
													最小值：{{ item.min }} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 25%：{{ item.persents25 }} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
													50%: {{ item.persents50 }} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 75%:
													{{ item.persents75 }} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 最大值：{{ item.max }} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
												</div>
												<hr />
											</el-card>
										</div>
									</div>
									<div style="width: 50%" v-for="(item, index) in riskdata2" :key="index">
										<div v-show="showlist2[index]">
											<el-card>
												<div slot="header" style="display: flex" class="clearfix">
													<div class="points"></div>
													<span>{{ item.description }}</span>
												</div>
												<div>
													{{ item.explain }}
												</div>
												<hr />
												<div>
													按日期划分：
													<br />
													最小值：{{ item.min }} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 25%：{{ item.persents25 }} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
													50%: {{ item.persents50 }} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 75%:
													{{ item.persents75 }} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 最大值：{{ item.max }} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
												</div>
												<hr />
												<div>
													按固定时间节点:
													<br />
													最小值：{{ item.min }} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 25%：{{ item.persents25 }} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
													50%: {{ item.persents50 }} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 75%:
													{{ item.persents75 }} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 最大值：{{ item.max }} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
												</div>
												<hr />
											</el-card>
										</div>
									</div>
								</div>
							</el-col>
						</el-row>
					</div>
				</el-scrollbar>
			</div>
		</el-dialog>
	</div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
//例如：import 《组件名称》 from '《组件路径》';
import axios from '@/api/index.js';
export default {
	//import引入的组件需要注入到对象中才能使用
	components: {},
	props: {
		visdescriobe: {
			type: Boolean,
			default: false
		}
	},
	data() {
		//这里存放数据
		return {
			showlist1: [true, true, true, true, true, true, true, true, true, true],
			showlist2: [true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true],
			namelist1: [],
			namelist2: [],
			visdescriobe2: false,
			searchdetail: null,
			filtersearch1: true,
			filtersearch2: true,
			filtersearch3: true,
			filtersearch4: true,
			filtersearch5: true,
			filtersearch6: true,
			filtersearch7: true,
			filtersearch8: true,
			filtersearch9: true,
			filtersearch10: true,
			filtersearch11: true,
			filtersearch12: true,
			filtersearch13: true,
			filtersearch14: true,
			filtersearch15: true,
			filtersearch16: true,
			filtersearch17: true,
			filtersearch18: true,
			filtersearch19: true,
			filtersearch20: true,
			filtersearch21: true,
			filtersearch22: true,
			filtersearch23: true,
			filtersearch24: true,
			filtersearch25: true,
			filtersearch26: true,
			filtersearch27: true,
			filtersearch28: true,
			filtersearch29: true,
			filtersearch30: true,
			content1: null,
			msg1: {},
			msg2: {},
			msg3: {},
			msg4: {},
			msg5: {},
			msg6: {},
			msg7: {},
			msg8: {},
			msg9: {},
			msg10: {},
			msg11: {},
			msg12: {},
			msg13: {},
			msg14: {},
			msg15: {},
			msg16: {},
			msg17: {},
			msg18: {},
			msg19: {},
			msg20: {},
			msg21: {},
			msg22: {},
			msg23: {},
			msg24: {},
			msg25: {},
			msg26: {},
			msg27: {},
			msg28: {},
			msg29: {},
			msg30: {},
			search: null,
			tableDatas: [],
			riskdata1: [],
			riskdata2: []
		};
	},
	//监听属性 类似于data概念
	computed: {},
	//监控data中的数据变化
	watch: {
		searchdetail(val) {
			this.showlist1 = [false, false, false, false, false, false, false, false, false, false];
			this.showlist2 = [
				false,
				false,
				false,
				false,
				false,
				false,
				false,
				false,
				false,
				false,
				false,
				false,
				false,
				false,
				false,
				false,
				false,
				false,
				false,
				false
			];
			if (val == '') {
				this.showlist1 = [true, true, true, true, true, true, true, true, true, true];
				this.showlist2 = [
					true,
					true,
					true,
					true,
					true,
					true,
					true,
					true,
					true,
					true,
					true,
					true,
					true,
					true,
					true,
					true,
					true,
					true,
					true,
					true
				];
			} else {
				for (let i = 0; i < 10; i++) {
					if (this.namelist1[i].indexOf(val >= 0)) {
						this.showlist1[i] = true;
					}
				}
				for (let j = 0; j < 20; j++) {
					if (this.namelist2[j].indexOf(val >= 0)) {
						this.showlist2[j] = true;
					}
				}
			}
		}
	},
	//方法集合
	methods: {
		showshuoming() {
			this.visdescriobe2 = true;
			let that = this;
			axios
				.get(that.$baseUrl + '/filter_risk_range/')
				.then((res) => {
					that.riskdata1 = res.data;
					for (let i = 0; i < res.data.length; i++) {
						this.namelist1 = res.data[i].description;
					}
					//console.log('fengxiangusnxi');
					//console.log(res.data);
				})
				.catch((error) => {
					//that.$message('数据缺失');
				});
			axios
				.get(that.$baseUrl + '/filter_risk_return_range/')
				.then((res) => {
					//console.log('fengxiangusnxi222');
					//console.log(res.data);
					for (let i = 0; i < res.data.length; i++) {
						this.namelist2 = res.data[i].description;
					}
					////console.log(res.data)
					that.riskdata2 = res.data;
				})
				.catch((error) => {
					//that.$message('数据缺失');
				});
		}
	},
	//生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	//生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	beforeCreate() {}, //生命周期 - 创建之前
	beforeMount() {}, //生命周期 - 挂载之前
	beforeUpdate() {}, //生命周期 - 更新之前
	updated() {}, //生命周期 - 更新之后
	beforeDestroy() {}, //生命周期 - 销毁之前
	destroyed() {}, //生命周期 - 销毁完成
	activated() {} //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style></style>
