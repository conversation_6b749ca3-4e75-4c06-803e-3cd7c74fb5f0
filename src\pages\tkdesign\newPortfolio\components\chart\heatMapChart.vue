<template>
    <div v-show="show">
      <div style="page-break-inside: avoid; position: relative">
        <div v-loading="industryrequestflag">
          <div style="page-break-inside: avoid; position: relative">
            <div class="charts_fill_class">
              <v-chart v-show="!industryrequestflag"
                       class="charts_one_class"
                       ref="ValuationPercentileChart"
                       autoresize
                       v-loading="industryrequestflag"
                       :options="industryoption"
                       @legendselectchanged="legendselectchanged" />
              <el-empty v-show="industryrequestflag"
                        description="暂无数据"></el-empty>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script>
  // 行业评价
  import VChart from 'vue-echarts';
  export default {
    name: 'ValuationPercentileChart',
    components: {
      VChart
    },
    data () {
      return {
        industryrequestflag: true,
        show: true,
        info: {},
        industryoption:null,
        selected:{
        },
      };
    },
    methods: {
      // 获取父组件传递数据
      getData (data, info) {
        this.show = true;
        this.info = info;
        this.industryrequestflag = false;
        this.industryoption=this.getIndustryoption(data,info);
      },
      getIndustryoption(chartData,info={}){
            const hours = [
                '价值', '平衡', '成长'
            ];
            const days = [
                '大盘', '中盘', '小盘'
            ];
            const data = [[0, 0, 2], [0, 1, 1], [0, 2, 3], [1,0, 5], [2, 0, 4],[2,1,3],[2,2,4],[1,1,2],[1,2,2]]
                .map(function (item) {
                return [item[0], item[1], item[2] || '-'];
            });
            return {
            tooltip: {
                position: 'top'
            },
            grid: {
                height: '100%',
                width:'100%',
                left:'40',
                bottom:'10%',
                right:'100'
            },
            xAxis: {
                // show: false,
                type: 'category',
                position:'bottom',
                data: hours,
                splitArea: {
                show: true
                },
                axisTick: {
                    show: false
                },
                axisLabel: {
                    width: 56
                }
            },
            yAxis: {
                // show: false,
                type: 'category',
                data: days,
                splitArea: {
                show: true
                },
                axisTick: {
                    show: false
                }
            },
          
            visualMap: {
              type: 'piecewise',
              min: 0,
              max: 3,
              calculable: true,
              orient: 'vertical',
              right: '0',
              top: '0',
              precision: 0,
              inRange: {
                  color: ['#f4f4fc', '#145c2c', '#fcb404', '#e4341c'],
              },
              pieces: [
                  {
                      value: 0,
                      label: '不适用',
                  },
                  {
                      value: 1,
                      label: '正常状态',
                  },
                  {
                      value: 2,
                      label: '提示状态',
                  },
                  {
                      value: 3,
                      label: '预警状态',
                  },
              ],
          },
            series: [
                {
                name: 'Punch Card',
                type: 'heatmap',
                data: data,
                label: {
                    show: true
                },
                itemStyle: {
                  borderColor: "#FFF"
                },
                emphasis: {
                    itemStyle: {
                    shadowBlur: 10,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
                }
            ]
            };
      },
      // 数据获取失败
      hideLoading () {
        this.industryrequestflag = false;
        this.show = false;
      },
    }
  };
  </script>
  
  <style></style>
  