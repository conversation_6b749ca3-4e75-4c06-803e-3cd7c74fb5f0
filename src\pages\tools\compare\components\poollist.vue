<!--  -->
<template>
  <div class="poollist">
    <el-dialog title="选择比较的基金或基金经理"
               :close-on-click-modal="false"
               :close-on-press-escape="false"
               @close="cancelmodel"
               :visible.sync="showselect"
               width="40%"
               destroy-on-close>
      <div class="savemodel"
           style="width: 100%">
        <el-form class="formcompare">
          <el-form-item>
            <div style="display: flex">
              <div>
                <el-radio v-model="radio"
                          label="1">比较基金</el-radio>
                <el-radio v-model="radio"
                          label="2">比较基金经理</el-radio>
              </div>
            </div>
            <div class="height20border"></div>
            <div v-if="radio == '1'">
              <el-select v-model="values"
                         @change="changgeit(values)"
                         :remote-method="searchpeople"
                         filterable
                         remote
                         prefix-icon="el-icon-search"
                         :loading="loading"
                         :placeholder="placeholder">
                <el-option v-for="item in havefundmanager"
                           :key="item.code"
                           :label="(String(item.code).length == 6 ? item.code + '-' : ' ') + item.name + '-' + String(item.fund_co).split('基金')[0]"
                           :value="item.code"
                           :disabled="item.disabled">
                </el-option>
              </el-select>
            </div>
            <div v-if="radio == '2'">
              <el-select v-model="values"
                         @change="changgeit(values)"
                         :remote-method="searchpeople"
                         filterable
                         remote
                         prefix-icon="el-icon-search"
                         :loading="loading"
                         :placeholder="placeholder">
                <el-option v-for="item in havefundmanager2"
                           :key="item.code"
                           :label="(String(item.code).length == 8 ? item.code + '-' : ' ') + item.name + '-' + String(item.fund_co).split('基金')[0]"
                           :value="item.code"
                           :disabled="item.disabled">
                </el-option>
              </el-select>
            </div>
          </el-form-item>
          <el-table v-if="radio == '1'"
                    ref="multipleTable"
                    :height="returnwidth(300)"
                    @selection-change="handleSelectionChange"
                    :data="ana.filter((data) => !search || data.name.toLowerCase().includes(search.toLowerCase()))">
            <el-table-column type="selection"
                             :width="returnwidth(55)"> </el-table-column>
            <el-table-column v-if="radio == '1'"
                             align="gotoleft"
                             prop="name"
                             :show-overflow-tooltip="true"
                             label="基金名称">
              <!-- <template slot="header" slot-scope="scope">
                    <el-input
                     v-model="search"
                    size="mini"
                     placeholder="基金"/>
                     </template>  -->
            </el-table-column>
            <el-table-column v-if="radio == '1'"
                             align="gotoleft"
                             prop="manager_name"
                             :show-overflow-tooltip="true"
                             label="基金经理">
            </el-table-column>
            <el-table-column v-if="radio == '1'"
                             align="gotoleft"
                             :show-overflow-tooltip="true"
                             prop="netasset"
                             label="规模">
              <template slot-scope="scope">{{ (scope.row.netasset / 100000000).toFixed(2) }}亿</template>
            </el-table-column>
            <el-table-column v-if="radio == '1'"
                             align="gotoleft"
                             prop="type"
                             :show-overflow-tooltip="true"
                             label="类型"> </el-table-column>
            <!-- <el-table-column v-if='radio=="1"' align='gotoleft' prop="type" :width='returnwidth(55)' >
               <template slot-scope="scope"><i @click="deltable(scope.row,1)" style="color:#4096FF" class="el-icon-delete-solid icon_color"></i></template>
            </el-table-column> -->
          </el-table>
          <el-table v-loading="loadings"
                    ref="multipleTable2"
                    @selection-change="handleSelectionChange2"
                    v-if="radio == '2'"
                    height="500px"
                    :data="ana2.filter((data) => !search || data.name_x.toLowerCase().includes(search.toLowerCase()))">
            <el-table-column type="selection"
                             :width="returnwidth(55)"> </el-table-column>
            <el-table-column v-if="radio == '2'"
                             align="gotoleft"
                             :show-overflow-tooltip="true"
                             prop="name"
                             label="基金经理">
              <!-- <template slot="header" slot-scope="scope">
                    <el-input
                     v-model="search"
                     placeholder="基金经理"/>
                     </template>-->
            </el-table-column>
            <el-table-column v-if="radio == '2'"
                             :show-overflow-tooltip="true"
                             align="gotoleft"
                             prop="fund_co"
                             label="基金公司">
            </el-table-column>
            <el-table-column v-if="radio == '2'"
                             :show-overflow-tooltip="true"
                             align="gotoleft"
                             prop="sum_netasset"
                             label="规模">
              <template slot-scope="scope">{{ Number(scope.row.sum_netasset).toFixed(2) }}亿</template></el-table-column>
            <!-- <el-table-column v-if='radio=="2"' align='gotoleft' prop="type" :width='returnwidth(55)' >
               <template slot-scope="scope"><i @click="deltable(scope.row,2)"  style="color:#4096FF" class="el-icon-delete-solid icon_color"></i></template>
            </el-table-column> -->
          </el-table>
          <div class="height10border"></div>
          <div style="text-align: right"
               class="demo-drawer__footer">
            <el-button type="primary"
                       style="background: #d7dbe0 !important; color: balck !important; border: 1px solid #d7dbe0 !important"
                       @click="cancelmodel">取消</el-button>
            <el-button style="color: white; background: #4096FF"
                       @click="gotocooare">比较</el-button>
          </div>
        </el-form>
      </div>
    </el-dialog>
    <!-- --------------------------------------------------------------------------- -->
    <el-dialog title="选择比较的类型"
               :visible.sync="showitem"
               width="30%"
               destroy-on-close>
      <div class="savemodel"
           style="width: 100%">
        <el-form :model="usermodal">
          <el-form-item>
            <el-select v-model="value"
                       placeholder="请选择比较类型">
              <el-option v-for="item in options"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value"> </el-option>
            </el-select>
          </el-form-item>
          <div class="height10border"></div>
          <div style="text-align: right"
               class="demo-drawer__footer">
            <el-button type="primary"
                       style="background: #d7dbe0 !important; color: balck !important; border: 1px solid #d7dbe0 !important"
                       @click="showitem = fasle">取消</el-button>
            <el-button type="primary"
                       @click="submitmodal">进入比较</el-button>
          </div>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { fontSize } from '../../../../assets/js/echartsrpxtorem'; //注意路径
import { basketFund, delBasketFund, search_all, FundCodeBasicMsg, TypeMsg } from '@/api/pages/tools/compare.js';
export default {
  //import引入的组件需要注入到对象中才能使用
  components: {},
  data () {
    //这里存放数据
    return {
      showselect: false,
      radio: '1',
      ana: [],
      ana2: [],
      options: [],
      havefundmanager2: [],
      value: '',
      multipleSelection: [],
      multipleSelection2: [],
      havefundmanager: [],
      values: '',
      loadings: false,
      placeholder: '输入简称、代码、名称搜索',
      showitem: false,
      search: '',
      temparr: [],
      temparr2: []
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {
    // values(val){
    //     //console.log(val)
    //     this.getinsertmsg(val)
    // },
    radio (val) {
      if (val == '1') {
        this.placeholder = '输入简称、代码、名称搜索';
        // this.showtable()
      } else if (val == '2') {
        this.placeholder = '输入简称、代码、名称搜索';
        // this.showtablemanager()
      }
    }
  },
  //方法集合
  methods: {
    cancelmodel () {
      this.showselect = false;
      //    this.$router.push('/dashboard')
    },
    deltable (row, flag) {
      if (flag == 1) {
        //   this.ana.findIndex(item=>{return item.code == row.code})
        this.ana.splice(
          this.ana.findIndex((item) => {
            return item.code == row.code;
          }),
          1
        );
      } else {
        this.ana2.splice(
          this.ana.findIndex((item) => {
            return item.code == row.code;
          }),
          1
        );
      }
    },
    handleSelectionChange (val) {
      if (val != null && JSON.stringify(val) != '[]') this.multipleSelection = val;
      //  //console.log('oscolosr')
    },
    handleSelectionChange2 (val) {
      if (val != null && JSON.stringify(val) != '[]') this.multipleSelection2 = val;
      //  //console.log('oscolosr')
    },
    async changgeit (val) {
      //console.log(val)
      this.loadings = true;
      let data = await FundCodeBasicMsg({ code: val + ',' });
      if (data) {
        this.loadings = false;
        // //console.log(data)
        if (this.radio == '1') {
          if (data.data.length > 0) this.ana.push(data.data[0]);
          // //console.log(this.ana)
          this.$nextTick(() => {
            for (let i = 0; i < this.ana.length; i++) {
              this.$refs.multipleTable.toggleRowSelection(this.ana[i]);
            }
          });
          //  //console.log(this.ana)
        } else if (this.radio == '2') {
          if (JSON.stringify(data.data) == '[]') {
            this.$message.error('该基金经理管理权益时间过短或未管理权益产品，请选择其他基金经理');
          }
          if (data.data.length > 0) this.ana2.push(data.data[0]);
          this.$nextTick(() => {
            for (let i = 0; i < this.ana2.length; i++) {
              this.$refs.multipleTable2.toggleRowSelection(this.ana2[i]);
            }
          });
        }
      } else {
        this.loadings = false;
      }
      //console.log(this.ana)
    },
    async gotocooare () {
      if (this.multipleSelection.length > 4 && this.radio == '1') {
        this.$message.error('比较数请小于五位');
      } else if (this.multipleSelection2.length > 4 && this.radio == '2') {
        this.$message.error('比较数请小于五位');
      } else {
        //
        let tempcode = '';
        let tempname = '';
        let temptype = null;
        if (this.radio == '1') {
          for (let i = 0; i < this.multipleSelection.length; i++) {
            tempcode = tempcode + this.multipleSelection[i].code + ',';
            tempname = tempname + this.multipleSelection[i].name + ',';
          }
          tempcode = tempcode.slice(0, tempcode.length - 1);
          tempname = tempname.slice(0, tempname.length - 1);
          let data = await TypeMsg({ code: tempcode });
          if (data) {
            // //console.log(data)
            if (data.data) {
              if (data.data.length == 0) {
                this.$message.error('请选择具有相同类型的基金进行比较');
              } else if (data.data.length == 1) {
                temptype = data.data[0];
                if (
                  temptype == 'bond' ||
                  temptype == 'cbond' ||
                  temptype == 'purebond' ||
                  temptype == 'bill' ||
                  temptype == 'equity' ||
                  temptype == 'obond'
                ) {
                  this.$router.push({
                    path: '/fundcompare',
                    query: {
                      id: tempcode,
                      type: temptype,
                      name: tempname
                    }
                  });
                } else {
                  this.$message('暂时只提供主动权益，二级债，债券类产品的比较');
                }
              } else if (data.data.length > 1) {
                this.showitem = true;
                this.options = [];
                for (let i = 0; i < data.data.length; i++) {
                  this.options.push({ value: data.data[i], label: data.data[i] });
                }
              }
            }
          }
        } else if (this.radio == '2') {
          for (let i = 0; i < this.multipleSelection2.length; i++) {
            tempcode = tempcode + this.multipleSelection2[i].code + ',';
            tempname = tempname + this.multipleSelection2[i].name + ',';
          }
          tempcode = tempcode.slice(0, tempcode.length - 1);
          tempname = tempname.slice(0, tempname.length - 1);
          let data = await TypeMsg({ code: tempcode });
          if (data) {
            // //console.log(data)
            if (data.data) {
              if (data.data.length == 0) {
                this.$message.error('请选择具有相同类型的基金经理进行比较');
              } else if (data.data.length == 1) {
                temptype = data.data[0];
                if (temptype == 'equity') {
                  this.$router.push({
                    path: '/managercompare',
                    query: {
                      id: tempcode,
                      type: temptype,
                      name: tempname
                    }
                  });
                } else {
                  this.$message('对于基金经理的比较我们只提供主动权益类型的比较，债券类型的比较以具体产品比较更优');
                }
              } else if (data.data.length > 1) {
                this.showitem = true;
                this.options = [];
                for (let i = 0; i < data.data.length; i++) {
                  this.options.push({ value: data.data[i], label: data.data[i] });
                }
              }
            }
          }
        }
      }
    },
    submitmodal () {
      if (this.multipleSelection.length > 4 && this.radio == '1') {
        this.$message.error('比较数请小于五位');
      } else if (this.multipleSelection2.length > 4 && this.radio == '2') {
        this.$message.error('比较数请小于五位');
      } else {
        let tempcode = '';
        let tempname = '';
        if (this.radio == '1') {
          if (
            this.value == 'bond' ||
            this.value == 'cbond' ||
            this.value == 'purebond' ||
            this.value == 'bill' ||
            this.value == 'equity' ||
            this.value == 'obond'
          ) {
            for (let i = 0; i < this.multipleSelection.length; i++) {
              tempcode = tempcode + this.multipleSelection[i].code + ',';
              tempname = tempname + this.multipleSelection[i].name + ',';
            }
            tempcode = tempcode.slice(0, tempcode.length - 1);
            tempname = tempname.slice(0, tempname.length - 1);
            this.$router.push({
              path: '/fundcompare',
              query: {
                id: tempcode,
                type: this.value,
                name: tempname
              }
            });
          } else {
            this.$message('暂时只提供主动权益，二级债，债券类产品的比较');
          }
        } else if (this.radio == '2') {
          if (this.value == 'equity') {
            for (let i = 0; i < this.multipleSelection2.length; i++) {
              tempcode = tempcode + this.multipleSelection2[i].code + ',';
              tempname = tempname + this.multipleSelection2[i].name + ',';
            }
            tempcode = tempcode.slice(0, tempcode.length - 1);
            tempname = tempname.slice(0, tempname.length - 1);
            this.$router.push({
              path: '/managercompare',
              query: {
                id: tempcode,
                type: this.value,
                name: tempname
              }
            });
          } else {
            this.$message('对于基金经理的比较我们只提供主动权益类型的比较，债券类型的比较以具体产品比较更优');
          }
        }
      }
    },
    returnwidth (val) {
      return fontSize(val);
    },
    // async showtable() {
    //     let data = await basketFund()
    //     if (data) {
    //         //console.log(data)
    //         this.ana = data
    //     }
    // },
    // async showtablemanager() {
    //     let data = await basketFund()
    //     if (data) {
    //         //console.log(data)
    //         this.ana = data
    //     }
    // },
    // async delebasket(scope) {
    //     let data = await delBasketFund({
    //         id: scope.row.id
    //     })
    //     if (data) {
    //         scope._self.$refs[`popover-${scope.$index}`].doClose();
    //         // that.showtrue=false
    //         this.showtable();
    //     }

    // },
    async searchpeople (query) {
      ////console.log(query)
      ////console.log(this.values)
      let that = this;
      let data = await search_all({
        message: query
      });
      if (data) {
        let temparr = [];
        for (let i = 0; i < data.length; i++) {
          if (data[i].flag == 'fund' && this.radio == '1') {
            temparr.push(data[i]);
          } else if (data[i].flag == 'manager' && this.radio == '2') {
            temparr.push(data[i]);
          }
        }
        if (this.radio == '1') that.havefundmanager = temparr;
        if (this.radio == '2') that.havefundmanager2 = temparr;
        // //console.log(that.havefundmanager)
        that.loading = false;
      }
    },
    gotocoppare2 () {
      let id = '110011,000083';
      let name = '易方达中小盘,汇添富消费';
      let type = 'equity';
      // 如果只有一种类型
      this.$router.push({
        path: '/fundcompare',
        query: {
          id,
          type,
          name
        }
      });
    },
    gotocoppare3 () {
      let id = '30159178,30177032';
      let name = '张芊,谭昌杰';
      let type = 'bond';
      // 如果只有一种类型
      this.$router.push({
        path: '/managercompare',
        query: {
          id,
          type,
          name
        }
      });
    },
    gotocoppare4 () {
      let id = '000118,000215';
      let name = '广发聚鑫,广发趋势';
      let type = 'bond';
      // 如果只有一种类型
      this.$router.push({
        path: '/fundcompare',
        query: {
          id,
          type,
          name
        }
      });
    },
    gotocoppare5 () {
      let id = '30567663,30430634';
      let name = '刘爱民,杨宇俊';
      let type = 'purebond';
      // 如果只有一种类型
      this.$router.push({
        path: '/managercompare',
        query: {
          id,
          type,
          name
        }
      });
    },
    gotocoppare6 () {
      let id = '007459,007478';
      let name = '浙商惠睿纯债,中加恒泰定开';
      let type = 'purebond';
      // 如果只有一种类型
      this.$router.push({
        path: '/fundcompare',
        query: {
          id,
          type,
          name
        }
      });
    },
    gotocoppare () {
      // 判定基金还是基金经理跳转
      let id = '30189741,30441407';
      let name = '萧楠,胡昕炜';
      let type = 'equity';
      // 如果只有一种类型
      this.$router.push({
        path: '/managercompare',
        query: {
          id,
          type,
          name
        }
      });
      // 如果有多种类型弹窗选择在进行跳转
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () { },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style scoped>
.poollist {
	font-size: 16px;
}
</style>
<style>
.formcompare .el-input--mini .el-input__inner {
	height: 28px !important;
	line-height: 28px !important;
}
</style>
