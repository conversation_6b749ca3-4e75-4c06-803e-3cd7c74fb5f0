<template>
	<div :class="info.flag == 1 ? 'chart_one' : ''">
		<div
			style="display: flex; align-items: center; justify-content: space-between; position: relative; margin-bottom: 16px"
			class="clearfix"
		>
			<div class="title" style="flex: 1; text-align: left">风险收益关系&nbsp;{{ fengxiandate }}</div>
			<div style="display: flex; align-items: center">
				<div v-show="info.type !== 'equityindex' && info.type !== 'equity-index' && info.type !== 'portfolio'">
					基准选择：<el-select v-model="valuebase" @change="onChangeBenchmark" placeholder="请选择基准概念">
						<el-option v-for="item in optionsbase" :key="item.value" :label="item.label" :value="item.value"> </el-option>
					</el-select>
				</div>
				<el-button class="print_show" icon="el-icon-document-delete" @click="exportExcel" style="margin-left: 16px">导出Excel</el-button>
			</div>
		</div>
		<div v-loading="fengxianshowe" :class="info.flag == 1 ? '' : 'table_scoll'">
			<el-table :data="base1" class="table" ref="multipleTable" :cell-style="elcellstyle2" header-cell-class-name="table-header">
				<el-table-column prop="code" label="累计收益" width="240px" align="gotoleft">
					<template slot-scope="scope">
						<span>{{ scope.row['name'] }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="1week" label="近一周" align="right">
					<template slot-scope="scope"
						><span>{{ scope.row['1week'] | fix2p }}</span></template
					>
				</el-table-column>
				<el-table-column prop="1month" label="近一月" align="right">
					<template slot-scope="scope"
						><span>{{ scope.row['1month'] | fix2p }}</span></template
					>
				</el-table-column>
				<el-table-column prop="1quarter" label="近一季" align="right">
					<template slot-scope="scope"
						><span>{{ scope.row['1quarter'] | fix2p }}</span></template
					>
				</el-table-column>
				<el-table-column prop="1year" label="近一年" align="right">
					<template slot-scope="scope"
						><span>{{ scope.row['1year'] | fix2p }}</span></template
					>
				</el-table-column>
				<el-table-column prop="3year" label="近三年" align="right">
					<template slot-scope="scope"
						><span>{{ scope.row['3year'] | fix2p }}</span></template
					>
				</el-table-column>
				<template slot="empty">
					<el-empty image-size="160"></el-empty>
				</template>
			</el-table>
			<el-table :data="base2" class="table" ref="multipleTable" header-cell-class-name="table-header">
				<el-table-column prop="meter" label="最大回撤" width="240px" align="gotoleft">
					<template slot-scope="scope">
						<span>{{ scope.row['name'] }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="1week" label="近一周" align="right">
					<template slot-scope="scope"
						><span>{{ scope.row['1week'] | fix2p }}</span></template
					>
				</el-table-column>
				<el-table-column prop="1month" label="近一月" align="right">
					<template slot-scope="scope"
						><span>{{ scope.row['1month'] | fix2p }}</span></template
					>
				</el-table-column>
				<el-table-column prop="1quarter" label="近一季" align="right">
					<template slot-scope="scope"
						><span>{{ scope.row['1quarter'] | fix2p }}</span></template
					>
				</el-table-column>
				<el-table-column prop="1year" label="近一年" align="right">
					<template slot-scope="scope"
						><span>{{ scope.row['1year'] | fix2p }}</span></template
					>
				</el-table-column>
				<el-table-column prop="3year" label="近三年" align="right">
					<template slot-scope="scope"
						><span>{{ scope.row['3year'] | fix2p }}</span></template
					>
				</el-table-column>
				<template slot="empty">
					<el-empty image-size="160"></el-empty>
				</template>
			</el-table>
			<el-table :data="base3" class="table" ref="multipleTable" header-cell-class-name="table-header">
				<el-table-column prop="meter" label="年化夏普率" width="240px" align="gotoleft">
					<template slot-scope="scope">
						<span>{{ scope.row['name'] }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="1week" label="近一周" align="right">
					<template slot-scope="scope"
						><span>{{ scope.row['1week'] | fix2 }}</span></template
					>
				</el-table-column>
				<el-table-column prop="1month" label="近一月" align="right">
					<template slot-scope="scope"
						><span>{{ scope.row['1month'] | fix2 }}</span></template
					>
				</el-table-column>
				<el-table-column prop="1quarter" label="近一季" align="right">
					<template slot-scope="scope"
						><span>{{ scope.row['1quarter'] | fix2 }}</span></template
					>
				</el-table-column>
				<el-table-column prop="1year" label="近一年" align="right">
					<template slot-scope="scope"
						><span>{{ scope.row['1year'] | fix2 }}</span></template
					>
				</el-table-column>
				<el-table-column prop="3year" label="近三年" align="right">
					<template slot-scope="scope"
						><span>{{ scope.row['3year'] | fix2 }}</span></template
					>
				</el-table-column>
				<template slot="empty">
					<el-empty image-size="160"></el-empty>
				</template>
			</el-table>
		</div>
	</div>
</template>

<script>
import { exportTitle, exportTable } from '@/utils/exportWord.js';
import { filter_json_to_excel } from '@/utils/exportExcel.js';
import searchComponents from '@/components/components/components/search/index.vue';
// 风险收益关系
export default {
	components: { searchComponents },
	name: 'riskReturnRelationship',
	data() {
		return {
			optionsbase: [],
			valuebase: '',
			valuebasename: '',
			fengxianshowe: true,
			base1: [],
			base2: [],
			base3: [],
			info: {},
			postData: {}
		};
	},
	props: {
		indexInfo: {
			type: {
				type: Object,
				default: {}
			}
		}
	},
	// watch: {
	// 	indexInfo(n, o) {
	// 		this.getIndexInfo(n);
	// 	}
	// },
	filters: {
		fix3(value) {
			return parseInt(value * 1000) / 1000;
		},

		fix2p(value) {
			return value == '--' || !value ? '--' : (value * 100).toFixed(2) + '%';
		},
		fix2(value) {
			return value == '--' || !value ? '--' : value.toFixed(2) + '%';
		}
	},
	methods: {
		// 获取数据
		getData(data, info) {
			this.info = info;
			this.base1 = this.filterData(this.formatRiskRecent(data), 'cumreturn');
			this.base2 = this.filterData(this.formatRiskRecent(data), 'maxdrawdown');
			this.base3 = this.filterData(this.formatRiskRecent(data), 'sharpe0');
			this.fengxianshowe = false;
		},
		// 格式化数据
		filterData(data, key) {
			if (this.info.type == 'equityindex' || this.info.type == 'equity-index') {
				return data?.[key]
					?.filter((item) => {
						return this.info.code == item.code;
					})
					.map((item) => {
						return {
							...item,
							name:
								this.info.code == item.code
									? this.info.name
									: this.valuebase == item.code
									? this.optionsbase.filter((obj) => {
											return obj.value == item.code;
									  })[0]?.label
									: item.code
									? item.code
									: '--'
						};
					});
			} else {
				return data?.[key]?.map((item) => {
					return {
						...item,
						name:
							this.info.code == item.code
								? this.info.name
								: this.valuebase == item.code
								? this.optionsbase.filter((obj) => {
										return obj.value == item.code;
								  })[0]?.label
								: item.code
								? item.code
								: '--'
					};
				});
			}
		},
		// 格式化风险收益关系
		formatRiskRecent(data) {
			let self = data?.self;
			let reference = data?.reference;
			let arr = [];
			if (self?.length) {
				self.map((item) => {
					item['code'] = this.info.code;
				});
				arr.push(...self);
			}
			if (reference?.length) {
				reference.map((item) => {
					item['code'] = this.info.type == 'portfolio' ? this.indexInfo.name : this.valuebase;
				});
				arr.push(...reference);
			}
			let filterData = {};
			for (const key in arr?.[0]) {
				if (key !== 'code' && key !== 'recent') {
					filterData[key] = arr.map((item) => {
						let obj = {};
						obj[item.recent] = item[key];
						return {
							code: item.code,
							...obj
						};
					});
				}
			}
			for (const key in filterData) {
				let arr = [];
				filterData[key].map((item) => {
					let index = arr.findIndex((obj) => {
						return obj.code == item.code;
					});
					if (index == -1) {
						arr.push(item);
					} else {
						arr[index] = { ...arr[index], ...item };
					}
				});
				filterData[key] = arr;
			}
			return filterData;
		},
		// 获取基准列表
		getBenchmarkList(data, info) {
			this.optionsbase = data;
			this.valuebase =
				data?.filter((item) => {
					return item.meter !== 0;
				})?.[0]?.value || data?.[0]?.value;
			this.valuebasename =
				data?.filter((item) => {
					return item.meter !== 0;
				})?.[0]?.label || data?.[0]?.label;
			this.postData.index = this.valuebase;
			this.$emit('resolveFather', this.postData);
		},
		// 监听基准变化
		onChangeBenchmark() {
			this.fengxianshowe = true;
			this.postData.index = this.valuebase;
			this.$emit('resolveFather', this.postData);
		},
		// 表格动态样式
		elcellstyle2({ row, column, rowIndex, columnIndex }) {
			if (columnIndex == 1) {
				if (row['1week'] >= 0) {
					return 'color: #E85D2D;';
				} else return 'color: #18C2A0;';
			}
			if (columnIndex == 2) {
				if (row['1month'] >= 0) {
					return 'color: #E85D2D;';
				} else return 'color: #18C2A0;';
			}
			if (columnIndex == 3) {
				if (row['1quarter'] >= 0) {
					return 'color: #E85D2D;';
				} else return 'color: #18C2A0;';
			}
			if (columnIndex == 4) {
				if (row['1year'] >= 0) {
					return 'color: #E85D2D;';
				} else return 'color: #18C2A0;';
			}
			if (columnIndex == 5) {
				if (row['3year'] >= 0) {
					return 'color: #E85D2D;';
				} else return 'color: #18C2A0;';
			}
		},
		// 获取指数信息
		getIndexInfo(info) {
			this.indexInfo = { ...info, code: info.id };
			this.fengxianshowe = true;
			this.$emit('resolveFather', { index: info.id });
		},
		// 导出excel
		exportExcel() {
			let list = [
				{ label: '累计收益', value: 'namer', fill: 'header' },
				{ label: '近一周', value: '1weekr', format: 'fix2p', fill: 'red_or_green' },
				{ label: '近一月', value: '1monthr', format: 'fix2p', fill: 'red_or_green' },
				{ label: '近一季', value: '1quarterr', format: 'fix2p', fill: 'red_or_green' },
				{ label: '近一年', value: '1yearr', format: 'fix2p', fill: 'red_or_green' },
				{ label: '近三年', value: '3yearr', format: 'fix2p', fill: 'red_or_green' },
				{ label: '最大回撤', value: 'namem', fill: 'header' },
				{ label: '近一周', value: '1weekm', format: 'fix2p' },
				{ label: '近一月', value: '1monthm', format: 'fix2p' },
				{ label: '近一季', value: '1quarterm', format: 'fix2p' },
				{ label: '近一年', value: '1yearm', format: 'fix2p' },
				{ label: '近三年', value: '3yearm', format: 'fix2p' },
				{ label: '年化夏普率', value: 'names', fill: 'header' },
				{ label: '近一周', value: '1weeks', format: 'fix2b' },
				{ label: '近一月', value: '1months', format: 'fix2b' },
				{ label: '近一季', value: '1quarters', format: 'fix2b' },
				{ label: '近一年', value: '1years', format: 'fix2b' },
				{ label: '近三年', value: '3years', format: 'fix2b' }
			];
			let data = [];

			let base1 = this.base1.map((item) => {
				return {
					namer: item.name,
					'1weekr': item['1week'],
					'1monthr': item['1month'],
					'1quarterr': item['1quarter'],
					'1yearr': item['1year'],
					'3yearr': item['3year']
				};
			});
			let base2 = this.base2.map((item) => {
				return {
					namem: item.name,
					'1weekm': item['1week'],
					'1monthm': item['1month'],
					'1quarterm': item['1quarter'],
					'1yearm': item['1year'],
					'3yearm': item['3year']
				};
			});
			let base3 = this.base3.map((item) => {
				return {
					names: item.name,
					'1weeks': item['1week'],
					'1months': item['1month'],
					'1quarters': item['1quarter'],
					'1years': item['1year'],
					'3years': item['3year']
				};
			});
			base1.map((item, index) => {
				let obj = { ...item, ...base2?.[index], ...base3?.[index] };
				data.push(obj);
			});
			filter_json_to_excel(list, data, '风险收益关系');
		},
		// 导出
		createPrintWord() {
			let returnList = [
				{ label: '累计收益', value: 'name', fill: 'header' },
				{ label: '近一周', value: '1week', format: 'fix2p', fill: 'red_or_green' },
				{ label: '近一月', value: '1month', format: 'fix2p', fill: 'red_or_green' },
				{ label: '近一季', value: '1quarter', format: 'fix2p', fill: 'red_or_green' },
				{ label: '近一年', value: '1year', format: 'fix2p', fill: 'red_or_green' },
				{ label: '近三年', value: '3year', format: 'fix2p', fill: 'red_or_green' }
			];
			let maxdrawdownList = [
				{ label: '最大回撤', value: 'name', fill: 'header' },
				{ label: '近一周', value: '1week', format: 'fix2p' },
				{ label: '近一月', value: '1month', format: 'fix2p' },
				{ label: '近一季', value: '1quarter', format: 'fix2p' },
				{ label: '近一年', value: '1year', format: 'fix2p' },
				{ label: '近三年', value: '3year', format: 'fix2p' }
			];
			let sharpeList = [
				{ label: '年化夏普率', value: 'name', fill: 'header' },
				{ label: '近一周', value: '1week', format: 'fix2' },
				{ label: '近一月', value: '1month', format: 'fix2' },
				{ label: '近一季', value: '1quarter', format: 'fix2' },
				{ label: '近一年', value: '1year', format: 'fix2' },
				{ label: '近三年', value: '3year', format: 'fix2' }
			];
			if (this.base1?.length && this.base2?.length && this.base3?.length) {
				return [
					...exportTitle('风险收益关系'),
					...exportTable(returnList, this.base1),
					...exportTable(maxdrawdownList, this.base2),
					...exportTable(sharpeList, this.base3)
				];
			} else {
				return [];
			}
		}
	}
};
</script>

<style scoped>
.table_scoll {
	height: 215px;
	overflow-y: scroll;
}
</style>
