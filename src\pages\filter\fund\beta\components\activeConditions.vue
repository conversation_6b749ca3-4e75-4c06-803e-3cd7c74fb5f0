<template>
	<div style="margin-top: 16px; margin-left: 24px; display: flex; flex-wrap: wrap; align-items: center; margin-bottom: 16px">
		<div class="normalFont">已选条件：</div>
		<div class="newFilterTag" v-for="(item, index) in listSelect" :key="index" style="display: flex; flex-wrap: wrap; align-items: center">
			<div v-for="(items, index2) in item.data" :key="index2">
				<div>
					<el-tag
						@close="closeTags(index, index2)"
						:class="isDianDao ? 'noDianDaoCalss' : 'isDianDaoCalss'"
						style="
									margin-right:16px
									height: 32px;
									color: rgba(0, 0, 0, 0.85);
									background: rgba(0, 0, 0, 0.02);
									border: 1px solid #d9d9d9;
									border-radius: 4px;
									font-family: 'PingFang';
									font-style: normal;
									font-weight: 400;
									font-size: 14px;
									line-height: 22px;
									color: rgba(0, 0, 0, 0.85);
								"
						closable
						type=""
					>
						{{ items.labelName }}:
						<span v-if="items.dataResult.length == 1">
							<span v-if="items.labelName == '大行业'">{{ items.dataResult[0].industryValue }}</span>

							<span v-if="items.labelName == '估值'">{{ items.dataResult[0].yearqtr }}</span>
							<span v-if="item.labelIndex == 'g' || item.labelIndex == 'h'">{{
								items.dataResult[0].date.indexOf('1w') >= 0
									? '近一周'
									: items.dataResult[0].date.indexOf('2w') >= 0
									? '近两周'
									: items.dataResult[0].date.indexOf('1m') >= 0
									? '近一月'
									: items.dataResult[0].date.indexOf('2m') >= 0
									? '近两月'
									: items.dataResult[0].date.indexOf('1q') >= 0
									? '近一季'
									: items.dataResult[0].date.indexOf('2q') >= 0
									? '近半年'
									: items.dataResult[0].date.indexOf('1y') >= 0
									? '近一年'
									: items.dataResult[0].date.indexOf('2y') >= 0
									? '近两年'
									: items.dataResult[0].date.indexOf('3y') >= 0
									? '近三年'
									: items.dataResult[0].date.indexOf('5y') >= 0
									? '近五年'
									: '自' + items.dataResult[0].date[1]
							}}</span>
							<!-- <span v-if="">{{ items.dataResult[0].value.join(',') }}</span> -->

							<span v-if="items.typeCate == '5' || items.typeCate == '6'"
								>{{ items.dataResult[0].label }}&nbsp;{{ items.dataResult[0].yearqtr }}&nbsp;</span
							>
							<span v-if="items.typeCate == '7' && items.labelName != '多行业权重判断'">{{ items.dataResult[0].industryName }}</span>
							<span v-if="items.typeCate == '7' && items.labelName == '多行业权重判断'">{{
								items.dataResult[0].industryName.join('+')
							}}</span>
							<span v-if="items.typeCate == '8' && items.dataResult[0].index_code_options.length > 0">{{
								items.dataResult[0].index_code_options.findIndex((itemx) => itemx.code == items.dataResult[0].index_code) >= 0
									? items.dataResult[0].index_code_options[
											items.dataResult[0].index_code_options.findIndex((itemx) => itemx.code == items.dataResult[0].index_code)
									  ].value
									: ''
							}}</span>
							<span>{{ items.dataResult[0].flag }}</span>
							<span v-if="items.dataResult[0].value">{{
								items.typeCate == '2' || (item.labelIndex == 'n' && items.typeCate == '1')
									? items.dataResult[0].value.join('-')
									: items.typeCate == '3'
									? items.dataResult[0].value.join(' ')
									: items.typeCate == '1' && items.labelName == '指数基金跟踪类型'
									? items.dataResult[0].value.join('; ')
									: items.dataResult[0].value
							}}</span>
							<span v-if="items.typeCate == '1' && items.labelName == '基金经理管理经验'">年</span>
							<span v-else-if="items.typeCate == '1' && (items.labelName == '基金经理规模' || items.labelName == '基金规模')">亿</span>
							<span v-else-if="items.typeCate == '1' && items.labelName == '可申购金额'">万</span>
							<span
								v-else-if="rank_list.indexOf(items.labelName) || day_list.includes(items.labelName) || value_list.includes(items.labelName)"
							>
								{{ unit(items) }}
							</span>
							<span v-else-if="items.typeCate == '4' || items.typeCate == '7' || items.typeCate == '8'">%</span>
							<span v-if="items.dataResult[0].valueType == 'rank'">的同类</span>
						</span>
					</el-tag>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		listSelect: {
			type: Object,
			default: {}
		}
	},
	data() {
		return {
			rank_list: [
				'波动率',
				'最大回撤',
				'在险价值',
				'期望损失',
				'下行风险',
				'年化收益率',
				'累计收益率',
				'月胜率',
				'特诺系数',
				'上行捕获',
				'下行捕获',
				'M2'
			],
			day_list: ['平均下行周期', '平均恢复周期'],
			value_list: [
				'最大回撤比',
				'波动率比',
				'痛苦指数',
				'夏普率（rf==0）',
				'夏普率（rf==4%）',
				'夏普率（动态rf）',
				'卡码率',
				'索提诺系数（rf==0）',
				'索提诺系数（rf==4%）',
				'索提诺系数（动态rf）',
				'稳定系数',
				'凯莉系数',
				'信息比率',
				'上攻潜力（周）',
				'詹森系数',
				'择时gamma'
			]
		};
	},
	methods: {
		unit(item) {
			if (this.rank_list.includes(item.labelName) || item.dataResult[0].valueType == 'rank') {
				return '%';
			} else if (this.day_list.includes(item.labelName)) {
				return '天';
			} else {
				return '';
			}
		},
		closeTags(index, index2) {
			this.$emit('closeTags', (index, index2));
		}
	}
};
</script>

<style></style>
