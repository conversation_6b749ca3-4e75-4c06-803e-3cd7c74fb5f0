<script>
import VChart from 'vue-echarts';
import { lineChartOption } from '@/utils/chartStyle';
import { dateList, testData4, testData5, testData6, testData7, testData8, testData9 } from '@/utils/date';
import {
  getObjectConcertion,
  getObjectStockStyle,
  getObjectStockFinance,
  getObjectStyle,
  getObjectTurnover
} from '@/api/pages/analysis/report';
import { handleData } from '@/utils/count';
import { filter_json_to_excel_inside, changColumnToRow, filter_json_to_excel_inside_multiHeader } from '@/utils/exportExcel.js';
import { export_json_to_excel_multiHeader } from '@/vendor/Export2Excel.js';
import {
  downloadWord,
  exportTitleWithSubtitle,
  exportTableMergeHeader,
  exportTitle,
  exportFirstTitle,
  exportChart,
  exportTable,
  Format,
  exportSencondTitle
} from '@/utils/exportWord.js';
import financeFOF from './financeFOF';
import styleFOF from './styleFOF';
export default {
  props: ['reportName'],
  components: { VChart, financeFOF, styleFOF },
  data () {
    return {
      uploadState: false,
      radio: '图表',
      data: '',
      radio1: [],
      date1: [],
      show: {
        concentrateEmpty: true,
        changeHandsEmpty: true,
        styleEmpty: true,
        financeEmpty: true
      },
      concentrateOptions: {}, // 集中度
      changeHandsOptions: {}, //换手率
      styleOptions: {}, // 持仓风格
      financeOptions: {}, // 财务指标
      section: '',
      time: [
        {
          value: 1,
          label: '近1年'
        },
        {
          value: 2,
          label: '近3年'
        },
        {
          value: 3,
          label: '近5年'
        }
      ],
      inspect: '',
      index: [
        {
          value: 'pe',
          label: 'PE'
        },
        {
          value: 'pb',
          label: 'PB'
        },
        {
          value: 'roe',
          label: 'ROE'
        },
        {
          value: 'income_yoy',
          label: '营业收入同比增长'
        },
        {
          value: 'dividendratiolyr',
          label: '股息率'
        },
        {
          value: 'net_income_yoy',
          label: '净利润增长率'
        }
      ],
      target: [],
      targetLoading: false,
      position: [
        {
          value: 'all',
          label: '全持仓'
        },
        {
          value: 'big',
          label: '重仓股'
        }
      ],
      type: '',
      indicator: [
        {
          value: '选项1',
          label: 'TK内部一级行业'
        },
        {
          value: '选项2',
          label: 'TK内部二级行业'
        },
        {
          value: '选项3',
          label: 'TK内部三级行业'
        }
      ],
      equityStyle: '',
      stockStyleDate: [new Date(this.$route.query.startDate), new Date(this.$route.query.endDate)],
      list: [
        {
          name: '大盘',
          data: [0, 0, 0]
        },
        {
          name: '均衡',
          data: [0, 0, 0]
        },
        {
          name: '小盘',
          data: [0, 0, 0]
        }
      ],
      listTable: [],
      areaLoading: false,
      wholeData: {
        list: [],
        options: {},
        type: 'all',
        inspect: 'pe',
        time: '',
        loading: false
      },
      holdStyleObj: {
        date: [],
        data: []
      },
      params: {},
      visible: false,
      //设置时间范围
      pickerOptions: {
        disabledDate: (date) => {
          const startTime = this.moment(this.$route.query.startDate).subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss');
          const endTime = this.moment(this.$route.query.endDate).add(1, 'day').format('YYYY-MM-DD HH:mm:ss');
          const checkTime = this.moment(date, 'YYYY-MM-DD HH:mm:ss');
          return !checkTime.isBetween(startTime, endTime);
        }
      },
      showOneYear: true,
      showTwoYear: true,
      showThreeYear: true,
      //
      topTenData: [],
      twoSideData: [],
      // 持仓风格
      ccfgTitle: [],
      ccfgColumn: [],
      reportTemplate: 'ALL'
    };
  },

  methods: {
    handleData,
    /**
     * 选择时间
     */
    changeTime (row) {
      let timeArr = [];
      this.date1 = [];
      if (row.length) this.radio1 = [row[row.length - 1]];
      if (this.radio1.length) {
        timeArr = [Number(this.moment().subtract(this.radio1[0], 'years').format('YYYYMMDD')), Number(this.moment().format('YYYYMMDD'))];
      } else {
        timeArr = [
          Number(this.moment(this.$route.query.startDate).format('YYYYMMDD')),
          Number(this.moment(this.$route.query.endDate).format('YYYYMMDD'))
        ];
      }
      this.getConcertionData(timeArr);
      this.getReportingData(timeArr);
    },

    /**
     * 指定时间区间(风格)
     */
    selectStyleTime (date) {
      this.visible = false;
      this.radio1 = [];
      let timeArr = [];
      if (date) {
        timeArr = [Number(this.moment(date[0]).format('YYYYMMDD')), Number(this.moment(date[1]).format('YYYYMMDD'))];
      }
      this.getConcertionData(timeArr);
      this.getReportingData(timeArr);
      this.getStyleChartData(timeArr);
    },

    handConcertionData (data) {
      let arr = data.sort((a, b) => a.data.date.localeCompare(b.data.date)); // 按照日期排序
      let filledData = []; // 处理后的数据
      for (let i = 0; i < arr.length - 1; i++) {
        filledData.push(arr[i]);
        let currentDate = new Date(arr[i].data.date);
        let nextDate = new Date(arr[i + 1].data.date);
        let diff = (nextDate - currentDate) / (1000 * 60 * 60 * 24); // 计算相邻两日期的天数差值

        if (diff > 1) {
          // 补齐缺失的日期
          for (let j = 1; j < diff; j++) {
            let missingDate = new Date(currentDate.getTime() + j * 24 * 60 * 60 * 1000);
            filledData.push({
              data: {
                date: this.moment(missingDate).format('YYYY-MM-DD'),
                top10_concert: '0'
              }
            }); // 格式化并添加缺失的日期到补齐后的数组中
          }
        }
      }
      filledData.push(arr[arr.length - 1]);
      return filledData;
    },

    /**
     * 获取前10大集中度数据
     */
    getConcertionData (timeArr) {
      this.targetLoading = true;
      let params = {
        ...this.params
      };
      if (timeArr) {
        params.startFrom = timeArr[0];
        params.endTo = timeArr[1];
      }
      return getObjectConcertion(params).then((res) => {
        this.targetLoading = false;
        if (res.code === 200) {
          res.data.rows = this.handConcertionData(res.data.rows).sort((a, b) => a.data.date.localeCompare(b.data.date)); // 按照日期排序
          this.show.concentrateEmpty = false;
          return this.getTopTenChartData(res.data.rows);
        } else {
          this.show.concentrateEmpty = true;
          return null;
        }
      });
    },

    handReportingData (data) {
      let arr = data.sort((a, b) => a.data.date.localeCompare(b.data.date)); // 按照日期排序
      let filledData = []; // 处理后的数据
      for (let i = 0; i < arr.length - 1; i++) {
        filledData.push(arr[i]);
        let currentDate = new Date(arr[i].data.date);
        let nextDate = new Date(arr[i + 1].data.date);
        let diff = (nextDate - currentDate) / (1000 * 60 * 60 * 24); // 计算相邻两日期的天数差值

        if (diff > 1) {
          // 补齐缺失的日期
          for (let j = 1; j < diff; j++) {
            let missingDate = new Date(currentDate.getTime() + j * 24 * 60 * 60 * 1000);
            filledData.push({
              data: {
                date: this.moment(missingDate).format('YYYY-MM-DD'),
                avg_compoent_book_value: '0',
                sum_asset: '0',
                turnover: '0'
              }
            }); // 格式化并添加缺失的日期到补齐后的数组中
          }
        }
      }
      filledData.push(arr[arr.length - 1]);
      return filledData;
    },

    /**
     * 获取报告期换手率
     */
    getReportingData (timeArr) {
      this.targetLoading = true;
      let params = {
        ...this.params
      };
      if (timeArr) {
        params.startFrom = timeArr[0];
        params.endTo = timeArr[1];
      }
      getObjectTurnover(params).then((res) => {
        this.targetLoading = false;
        if (res.code === 200) {
          res.data.rows = this.handReportingData(res.data.rows).sort((a, b) => a.data.date.localeCompare(b.data.date));
          this.getReportingChartData(res.data.rows);
          this.show.changeHandsEmpty = false;
        } else {
          this.show.changeHandsEmpty = true;
        }
      });
    },

    /**
     * 获取权益持仓风格
     */
    getEquityStyle (date) {
      this.areaLoading = true;
      let data = {
        ...this.params,
        reportTemplate: 'MOM'
      };
      if (date) {
        data.startFrom = Number(this.stockStyleDate[0]);
        data.endTo = Number(this.stockStyleDate[1]);
      }
      return getObjectStockStyle(data).then((res) => {
        this.areaLoading = false;
        if (res.code === 200) {
          // this.show.styleEmpty = false
          this.listTable = [];
          let number = 0;
          res.data.rows.forEach((item) => {
            number += Number(item.data.weight);
          });
          res.data.rows.forEach((item) => {
            this.list[this.getNumber(item.data.bigsmall)].data[this.getNumber(item.data.valuegrowth)] = handleData(
              item.data.weight / number,
              true
            );
          });
          this.list.forEach((item) => {
            item.data.forEach((citem, cindex) => {
              this.listTable.push({
                name: `${item.name}${cindex === 0 ? '价值' : cindex === 1 ? '均衡' : '成长'}`,
                data: citem
              });
            });
          });
          return this.listTable;
        } else {
          // this.show.styleEmpty = true
        }
      });
    },

    handWholeIndexData () {
      let arr = data.sort((a, b) => a.data.date.localeCompare(b.data.date)); // 按照日期排序
      let filledData = []; // 处理后的数据
      for (let i = 0; i < arr.length - 1; i++) {
        filledData.push(arr[i]);
        let currentDate = new Date(arr[i].data.date);
        let nextDate = new Date(arr[i + 1].data.date);
        let diff = (nextDate - currentDate) / (1000 * 60 * 60 * 24); // 计算相邻两日期的天数差值

        if (diff > 1) {
          // 补齐缺失的日期
          for (let j = 1; j < diff; j++) {
            let missingDate = new Date(currentDate.getTime() + j * 24 * 60 * 60 * 1000);
            filledData.push({
              data: {
                date: this.moment(missingDate).format('YYYY-MM-DD'),
                avg_compoent_book_value: '0',
                sum_asset: '0',
                turnover: '0'
              }
            }); // 格式化并添加缺失的日期到补齐后的数组中
          }
        }
      }
      filledData.push(arr[arr.length - 1]);
      return filledData;
    },

    getBetweenDates (startDate, endDate) {
      let dates = [];
      let currentDate = new Date(startDate);

      while (currentDate <= endDate) {
        dates.push(currentDate.toISOString().slice(0, 10));
        currentDate.setDate(currentDate.getDate() + 1);
      }

      return dates;
    },

    /**
     * 获取数据补充
     */
    supplementData (arr) {
      let minDate = this.moment(this.$route.query.startDate).format('YYYY-MM-DD');
      let maxDate = this.moment(this.$route.query.endDate).format('YYYY-MM-DD');
      let dateArr = this.getBetweenDates(new Date(minDate), new Date(maxDate));
      let dataArr = [];
      dateArr.forEach((item, index) => {
        dataArr.push({
          data: {
            date: item
          }
        });
        arr.forEach((citem) => {
          if (item === citem.data.date) {
            dataArr[index].data = {
              ...citem.data
            };
          }
        });
      });
      console.log(dataArr, arr);
      dataArr.forEach((item, index) => {
        console.log(index);
        if (!item.data.pb) {
          console.log(item.data);
          if (index === 0) { }
          else {
            item.data = {
              ...dataArr[index - 1].data,
              date: item.data.date
            };
          }

        }
      });
      return dataArr;
    },

    /**
     * 获取整体财务指标
     */
    getWholeIndexData (timeArr) {
      this.wholeData.loading = true;
      let data = {
        reportID: Number(this.$route.query.id),
        factors: [0],
        holdType: this.wholeData.type,
        startFrom: Number(this.moment(this.$route.query.startDate).format('YYYYMMDD')),
        endTo: Number(this.moment(this.$route.query.endDate).format('YYYYMMDD')),
        industryStandard: 3,
        reportTemplate: 'MOM'
      };
      if (timeArr) {
        data.startFrom = timeArr[0];
        data.endTo = timeArr[1];
      }
      getObjectStockFinance(data).then((res) => {
        this.wholeData.loading = false;
        if (res.code === 200) {
          this.show.financeEmpty = false;
          this.wholeData.list = this.supplementData(res.data.rows);
          this.getWholeChart();
        } else {
          this.show.financeEmpty = true;
        }
      });
    },

    /**
     * 持仓风格数据
     */
    getStyleChartData (timeArr) {
      let data = {
        reportID: Number(this.$route.query.id),
        startFrom: Number(this.moment(this.$route.query.startDate).format('YYYYMMDD')),
        endTo: Number(this.moment(this.$route.query.endDate).format('YYYYMMDD'))
      };
      if (timeArr) {
        data.startFrom = timeArr[0];
        data.endTo = timeArr[1];
      }
      getObjectStyle(data).then((res) => {
        if (res.code === 200) {
          this.show.styleEmpty = false;

          let arr = [];
          let dateSet = new Set();

          // 时间排序
          res.data.rows.sort((a, b) => {
            const dateA = new Date(a.data.date);
            const dateB = new Date(b.data.date);
            return dateA - dateB;
          });

          // 构造legend
          res.data.rows.forEach((line) => {
            if (!line.data) return;
            const item = line.data;
            dateSet.add(item.date);

            let inArray = false;
            if (item.weight && !isNaN(item.weight)) {
              let num = parseFloat(item.weight) * 100;
              item.weight100 = num;
            }
            if (arr.length > 0) {
              arr.forEach((v) => {
                if (v.name === item.style) {
                  inArray = true;
                  v.data.push(item);
                }
              });
            }
            // console.log(item);
            if (!inArray) {
              arr.push({
                name: item.style,
                type: 'line',
                stack: 'Total',
                emphasis: {
                  focus: 'series'
                },
                symbol: 'none',
                areaStyle: {},
                data: [item]
              });
            }
          });
          // console.log(arr);
          const dateArray = Array.from(dateSet);
          // 填充legend内部data内缺少日期的数据
          arr &&
            arr.length > 0 &&
            arr.map((legend) => {
              const newDataArray = [];
              dateArray &&
                dateArray.length > 0 &&
                dateArray.map((date) => {
                  legend.data &&
                    legend.data.length > 0 &&
                    legend.data.map((item) => {
                      if (item.date === date) {
                        newDataArray.push(item.weight100 || 0);
                      } else {
                        // newDataArray.push(0);
                      }
                    });
                });
              legend.data = newDataArray;
            });

          // 构造导出excel数据
          const ccfgTitle = [''];
          const ccfgColumn = [dateArray];
          arr &&
            arr.length > 0 &&
            arr.map((legend) => {
              ccfgTitle.push(legend.name + '(%)');
              ccfgColumn.push(legend.data);
            });
          this.ccfgColumn = ccfgColumn;
          this.ccfgTitle = ccfgTitle;

          this.holdStyleObj.date = dateArray;
          this.holdStyleObj.data = arr;
          let temp = []
          for (let i = 0; i < dateArray.length; i++) {
            temp.push(res.data.rows[res.data.rows.findIndex(item => item.data.date === dateArray[i])].data.r_square)
          }
          this.holdStyleObj.data.push({
            name: 'R-square',
            type: 'line',
            color: '#ffffff',
            emphasis: {
              focus: 'series'
            },
            symbol: 'none',
            data: temp.map(item => { return item * 100 }),
            yAxisIndex: 1
          });
          this.getChartData(this.holdStyleObj);
        } else {
          this.show.styleEmpty = true;
        }
      });
    },

    /**
     * 返回number
     */
    getNumber (data) {
      if (data === '大盘' || data === '价值') return 0;
      if (data === '均衡') return 1;
      if (data === '小盘' || data === '成长') return 2;
    },

    /**
     * 获取前10大集中度
     */
    getTopTenChartData (arr) {
      arr.forEach((item, index) => {
        if (item.data['top10_concert'] === 'nan' || item.data['top10_concert'] === 'NaN') {
          if (index !== 0) {
            item.data['top10_concert'] = arr[index - 1].data['top10_concert'];
          } else {
            item.data['top10_concert'] = 0;
          }
        }
      });
      this.topTenData = arr;
      const line = [
        {
          name: '分析对象',
          type: 'line',
          symbol: 'none',
          lineStyle: {
            width: 3
          },
          data: arr.map((item) => (item.data['top10_concert'] === 'nan' ? 0 : item.data['top10_concert']))
        }
      ];
      this.concentrateOptions = lineChartOption({
        grid: { left: '24px', right: '48px', top: '24px', bottom: '36px' }, // 位置
        dataZoom: false,
        toolbox: false,
        tooltip: {
          formatter: function (obj) {
            var value = `<div style="font-size:14px;">` + obj?.[0].axisValue + `</div>`;
            for (let i = 0; i < obj.length; i++) {
              value +=
                `<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
                `<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
                obj?.[i].color +
                `;"></div>` +
                `<div style="font-family: PingFang SC;">` +
                obj?.[i].seriesName +
                '</div></div>' +
                `<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
                (Number(obj?.[i].value) * 100).toFixed(2) +
                '%</div>' +
                `</div>`;
            }
            return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
          }
        },
        xAxis: [
          {
            name: '日期',
            data: arr.map((item) => item.data.date.slice(0, 10))
          }
        ],
        yAxis: [
          {
            type: 'value',
            formatter: function (value, index) {
              // Y轴的自定义刻度值，对应上图
              return `${(value * 100).toFixed(2) + '%'}`;
            }
          }
        ],
        series: line
      });
      return this.concentrateOptions;
    },

    /**
     * 获取报告期换手率
     */
    getReportingChartData (arr) {
      arr.forEach((item, index) => {
        if (item.data.turnover === 'nan' || item.data.turnover === 'NaN') {
          if (index !== 0) {
            item.data.turnover = arr[index - 1].data.turnover;
          } else {
            item.data.turnover = 0;
          }
        }
      });
      this.twoSideData = arr;
      const line = [
        {
          name: '分析对象',
          type: 'line',
          symbol: 'none',
          lineStyle: {
            width: 3
          },
          data: arr.map((item) => handleData(item.data['turnover'], true))
        }
      ];
      this.changeHandsOptions = lineChartOption({
        grid: { left: '24px', right: '48px', top: '24px', bottom: '36px' }, // 位置
        dataZoom: false,
        toolbox: false,
        tooltip: {
          formatter: function (obj) {
            let value = `<div style="font-size:14px;">` + obj?.[0].axisValue + `</div>`;
            for (let i = 0; i < obj.length; i++) {
              value +=
                `<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
                `<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
                obj?.[i].color +
                `;"></div>` +
                `<div style="font-family: PingFang SC;">` +
                obj?.[i].seriesName +
                '</div></div>' +
                `<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
                Number(obj?.[i].value) +
                '%' +
                '</div>' +
                '</div>';
            }
            return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
          }
        },
        xAxis: [
          {
            name: '日期',
            data: arr.map((item) => item.data.date.slice(0, 10))
          }
        ],
        yAxis: [
          {
            type: 'value',
            formatter: function (value, index) {
              // Y轴的自定义刻度值，对应上图
              return `${value}%`;
            }
          }
        ],

        series: line
      });
    },

    /**
     * 持有类型图表
     */
    getChartData (holdStyleObj) {
      const legendArray = holdStyleObj.data.map((item) => item.name);
      // let temp1 = []
      // let temp2 = []
      for (let i = 0; i < holdStyleObj?.data?.length; i++) {
        if (holdStyleObj?.data[i].name == 'R_square') {
          holdStyleObj.data[i] = {
            // areaStyle: holdStyleObj?.data[i].areaStyle,
            data: holdStyleObj?.data[i].data,
            emphasis: holdStyleObj?.data[i].emphasis,
            name: holdStyleObj?.data[i].name,
            type: holdStyleObj?.data[i].type,
            color: '#ffffff',
            symbol: holdStyleObj?.data[i].symbol,
            yAxisIndex: 1
          };
        }
        // temp1.push(holdStyleObj?.data[i])
      }
      // console.log(holdStyleObj);
      this.styleOptions = lineChartOption({
        grid: { left: '124px', right: '90px', top: '50px', bottom: '60px' }, // 位置
        dataZoom: true,
        toolbox: false,
        legend: {
          data: legendArray,
          type: 'plain'
        },
        tooltip: {
          formatter: function (obj) {
            // console.log(obj);
            var value = `<div style="font-size:14px;">` + obj?.[0].axisValue + `</div>`;
            for (let i = 0; i < obj.length; i++) {
              value +=
                `<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
                `<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
                obj?.[i].color +
                `;"></div>` +
                `<div style="font-family: PingFang SC;">` +
                obj?.[i].seriesName +
                '</div></div>' +
                `<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
                (Number(obj?.[i].value).toFixed(2)) +
                '%</div>' +
                `</div>`;
            }
            return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
          }
        },
        xAxis: [
          {
            name: '日期',
            data: holdStyleObj.date,

          }
        ],
        series: holdStyleObj.data,
        yAxis: [
          {
            name: '风格',
            type: 'value',
            min: 0,
            max: 100,
            formatter: function (value, index) {
              //Y轴的自定义刻度值，对应上图
              return `${value}%`;
            }
          },
          {
            name: 'R-square',
            type: 'value',
            min: 0,
            max: 100,
            formatter: function (value, index) {
              //Y轴的自定义刻度值，对应上图
              return `${value}%`;
            }
          }
        ]
      });
      console.log(this.styleOptions);
    },

    /**
     * 生成整体指标Chart
     */
    getWholeChart () {
      let allArr = [];
      this.wholeData.list.forEach((item, index) => {
        if (item.data[`index_${this.wholeData.inspect}`] !== '--') {
          allArr.push(item.data[`index_${this.wholeData.inspect}`]);
        } else {
          if (index > 0) {
            item.data[`index_${this.wholeData.inspect}`] = this.wholeData.list[index - 1].data[`index_${this.wholeData.inspect}`];
            allArr.push(item.data[`index_${this.wholeData.inspect}`]);
          }
        }
      });
      const line = [
        {
          name: '分析对象',
          type: 'line',
          symbol: 'none',
          lineStyle: {
            width: 3
          },
          data: this.wholeData.list.map((item) => item.data[this.wholeData.inspect])
        },
        {
          name: '参考基准',
          type: 'line',
          symbol: 'none',
          lineStyle: {
            width: 3
          },
          data: allArr
        }
      ];
      let that = this
      this.wholeData.options = lineChartOption({
        grid: { left: '24px', right: '48px', top: '24px', bottom: '36px' }, // 位置
        dataZoom: false,
        toolbox: false,
        tooltip: {
          formatter: function (obj) {
            var value = `<div style="font-size:14px;">` + obj?.[0].axisValue + `</div>`;
            for (let i = 0; i < obj.length; i++) {
              value +=
                `<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
                `<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
                obj?.[i].color +
                `;"></div>` +
                `<div style="font-family: PingFang SC;">` +
                obj?.[i].seriesName +
                '</div></div>' +
                `<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
                Number(obj?.[i].value).toFixed(2) +
                (that.wholeData.inspect == 'pb' || that.wholeData.inspect == 'pe' ? '' : '%') +
                '</div>' +
                `</div>`;
            }
            return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
          }
        },
        xAxis: [
          {
            name: '日期',
            data: this.wholeData.list.map((item) => item.data.date)
          }
        ],
        yAxis: [
          {
            type: 'value',
            formatter: function (value, index) {
              // Y轴的自定义刻度值，对应上图
              return `${value}`;
            }
          }
        ],
        series: line
      });
    },

    /**
     * 获取颜色
     */
    getColor (item) {
      if (item > 50) return { backgroundColor: '#BF6C00' };
      if (item > 25 && item <= 50) return { backgroundColor: '#4096ff' };
      if (item > 10 && item <= 50) return { backgroundColor: '#FFBE6A' };
      if (item >= 0 && item <= 10) return { backgroundColor: '#FFECD2' };
    },

    /**
     * 计算展示时间
     */
    handDate (startDate, endDate) {
      let start = this.moment(startDate);
      let end = this.moment(endDate);
      let diffInYears = end.diff(start, 'years');

      if (diffInYears < 1) {
        this.showOneYear = false;
        this.showTwoYear = false;
        this.showThreeYear = false;
      }

      if (diffInYears === 1) {
        this.showOneYear = true;
        this.showTwoYear = false;
        this.showThreeYear = false;
        this.time = [
          {
            value: 1,
            label: '近1年'
          }
        ];
      }

      if (diffInYears === 2) {
        this.showOneYear = true;
        this.showTwoYear = true;
        this.showThreeYear = false;
        this.time = [
          {
            value: 1,
            label: '近1年'
          },
          {
            value: 2,
            label: '近3年'
          }
        ];
      }

      if (diffInYears >= 3) {
        this.showOneYear = true;
        this.showTwoYear = true;
        this.showThreeYear = true;
        this.time = [
          {
            value: 1,
            label: '近1年'
          },
          {
            value: 2,
            label: '近3年'
          },
          {
            value: 3,
            label: '近5年'
          }
        ];
      }
    },

    downloadExcel (name) {
      if (name === '报告期前十大集中度') {
        const title = [
          { label: '', value: 'date' },
          { label: this.reportName, value: 'top10_concert', format: 'fix2p' }
        ];
        filter_json_to_excel_inside(title, this.topTenData, ['data'], name);
      } else if (name === '报告期双边换手率') {
        const title1 = [
          { label: '', value: 'date' },
          { label: this.reportName, value: 'turnover', format: 'fix2p' }
        ];
        filter_json_to_excel_inside(title1, this.twoSideData, ['data'], name);
      } else if (name === '持仓风格') {
        const format = ['', 'fixb0', 'fixb0', 'fixb0', 'fixb0'];
        const data = changColumnToRow(this.ccfgColumn, format);
        export_json_to_excel_multiHeader([this.ccfgTitle], null, data, name);
      } else if (name === '当前权益持仓风格（只分析股票）') {
        const title1 = [
          { label: '持仓风格', value: 'name' },
          { label: '投资比例', value: 'data', format: 'fix2b' }
        ];
        filter_json_to_excel_inside(title1, this.listTable, [], name);
      } else if (name === '整体财务指标') {
        let indexName = '';
        if (this.wholeData.list && this.wholeData.list.length > 0) {
          const temp = this.wholeData.list[0];
          indexName = temp?.data?.index_name;
        }
        const type = this.wholeData.type === 'all' ? '全持仓' : '重仓股';
        const title = [
          { label: '', value: 'date' },
          { label: this.reportName + '(PE)', value: 'pe', format: 'fix2b' },
          { label: this.reportName + '(PB)', value: 'pb', format: 'fix2b' },
          { label: this.reportName + '(ROE)', value: 'roe', format: 'fix2b' },
          { label: this.reportName + '(股息率)', value: 'dividendratiolyr', format: 'fix2b' },
          { label: indexName + '(PE)', value: 'index_pe', format: 'fix2b' },
          { label: indexName + '(PB)', value: 'index_pb', format: 'fix2b' },
          { label: indexName + '(ROE)', value: 'index_roe', format: 'fix2b' }
        ];
        filter_json_to_excel_inside(title, this.wholeData.list, ['data'], name + '(' + type + ')');
      }
    },

    async waitDom () {
      return new Promise((resovel) => {
        setTimeout(() => {
          resovel('等待dom加载完成');
        }, 1000);
      });
    },
    async createPrintWord () {
      const getImg = (ref) => {
        const chart1 = this.$refs[ref];
        if (!chart1) return null;
        chart1.mergeOptions({ toolbox: { show: false } });
        const height = chart1.$el.clientHeight;
        const width = chart1.$el.clientWidth;
        const img = chart1.getConnectedDataURL({
          type: 'jpg',
          pixelRatio: 2,
          backgroundColor: '#fff',
          excludeComponents: ['dataZoom']
        });
        const chartImg = exportChart(img, { width, height });
        chart1.mergeOptions({ toolbox: { show: true } });
        return chartImg;
      };
      return Promise.all([
        this.waitDom(),
        this.getConcertionData(),
        this.getReportingData(),
        this.getStyleChartData(),
        this.getEquityStyle(),
        this.$refs.styleFOF.getEquityStyle(),
        this.getWholeIndexData(),
        this.$refs.financeFOF.getWholeIndexData()
      ]).then((arr) => {
        const head = exportFirstTitle('二、风格分析');
        // chart1
        const subhead11 = exportTitle('风格分析-报告期前十大集中度');
        const chart1 = getImg('ChartComponent_bgqqsdjzd');

        // chart2
        const subhead12 = exportTitle('风格分析-报告期双边换手率');
        const chartImg2 = getImg('ChartComponent_bgqsbhsl');

        // chart3
        const subhead13 = exportTitle('风格分析-持仓风格');
        const chartImg3 = getImg('ChartComponent_ccfg');

        // table4
        const subhead4 = exportTitle('当前权益持仓风格（只分析股票）');
        const array4 = arr[4] || [];
        const title4 = [
          { label: '资产类别', value: 'name' },
          { label: '期末规模(亿)', value: 'data', format: 'fixb' }
        ];
        const table4 = exportTable(title4, array4, {}, {});
        // chart5
        const type = this.wholeData.type === 'all' ? '全持仓' : '重仓股';
        const inspect = this.wholeData.inspect;
        const subhead5 = exportTitle(`整体财务指标MOM（持仓类型：${type},参考指标：${inspect}）`);
        const chartImg5 = getImg('ChartComponent_ztcwzb');
        // chart6
        const type2 = this.$refs.financeFOF.exportMSG.type === 'all' ? '全持仓' : '重仓股';
        const inspect2 = this.$refs.financeFOF.exportMSG.inspect;
        const subhead6 = exportTitle(`整体财务指标FOF（持仓类型：${type2},参考指标：${inspect2}）`);
        const chartImg6 = getImg('ChartComponent_ztcwzb2');
        return [
          ...(head || []),
          ...(subhead11 || []),
          ...(chart1 || []),
          ...(subhead12 || []),
          ...(chartImg2 || []),
          ...(subhead13 || []),
          ...(chartImg3 || []),
          ...(subhead4 || []),
          ...(table4 || []),
          ...(subhead5 || []),
          ...(chartImg5 || []),
          ...(subhead6 || []),
          ...(chartImg6 || [])
        ];
      });
    },
    uploadPage () {
      let that = this;
      that.params = {
        reportID: Number(that.$route.query.id),
        startFrom: Number(this.moment(that.$route.query.startDate).format('YYYYMMDD')),
        endTo: Number(this.moment(that.$route.query.endDate).format('YYYYMMDD')),
        industryStandard: 3,
        selectedCuts: that.$route.query.graininess
      };
      that.handDate(that.$route.query.startDate, that.$route.query.endDate);

      that.getConcertionData();
      that.getStyleChartData();
      setTimeout(() => {
        that.getReportingData();
        setTimeout(() => {
          // that.getChartData()
          setTimeout(() => {
            that.getEquityStyle();
            that.$refs.styleFOF.getEquityStyle();
            setTimeout(() => {
              that.getWholeIndexData();
              that.$refs.financeFOF.getWholeIndexData();
            }, 500);
          }, 500);
        }, 500);
      }, 500);
    }
  },
  mounted () {
    this.reportTemplate = this.$route.query?.reportTemplate || 'ALL';
    this.params = {
      reportID: Number(this.$route.query.id),
      startFrom: Number(this.moment(this.$route.query.startDate).format('YYYYMMDD')),
      endTo: Number(this.moment(this.$route.query.endDate).format('YYYYMMDD')),
      industryStandard: 3,
      selectedCuts: this.$route.query.graininess
    };
  }
};
</script>

<template>
  <div>
    <div class="page-box"
         v-loading="targetLoading">
      <div class="flex item-center justify-between">
        <div class="area-title">风格分析</div>
        <div class="border_table_header_search">
          <el-checkbox-group v-if="showOneYear || showTwoYear || showThreeYear"
                             v-model="radio1"
                             size="small"
                             @change="changeTime">
            <el-checkbox-button v-if="showOneYear"
                                :label="1">1y</el-checkbox-button>
            <el-checkbox-button v-if="showTwoYear"
                                :label="2">2y</el-checkbox-button>
            <el-checkbox-button v-if="showThreeYear"
                                :label="3">3y</el-checkbox-button>
          </el-checkbox-group>
          <el-popover placement="bottom"
                      width="400"
                      v-model="visible">
            <el-date-picker v-model="date1"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            @change="selectStyleTime"
                            :unlink-panels="true"
                            :picker-options="pickerOptions"
                            :default-value="[
								this.moment(this.$route.query.startDate).subtract(1, 'day').toDate(),
								this.moment(this.$route.query.endDate).subtract(1, 'day').toDate()
							]">
            </el-date-picker>
            <el-button label=""
                       slot="reference"><i class="el-icon-date"></i></el-button>
          </el-popover>
        </div>
      </div>
      <div class="area-body">
        <div class="area-chart">
          <div class="chart-card_half">
            <div class="chart-card_header_bg">
              <div class="chart-card_title">报告期前十大集中度</div>
              <img alt=""
                   src="../../../../../assets/img/download.png"
                   class="download"
                   @click="downloadExcel('报告期前十大集中度')" />
            </div>
            <v-chart ref="ChartComponent_bgqqsdjzd"
                     autoresize
                     v-if="!show.concentrateEmpty"
                     element-loading-text="暂无数据"
                     element-loading-spinner="el-icon-document-delete"
                     element-loading-background="rgba(239, 239, 239, 0.5)"
                     style="height: 340px; width: 100% !important"
                     :options="concentrateOptions" />
            <el-empty v-else
                      :image-size="200"></el-empty>
          </div>
          <div class="chart-card_half">
            <div class="chart-card_header_bg">
              <div class="chart-card_title">报告期双边换手率</div>
              <img alt=""
                   src="../../../../../assets/img/download.png"
                   class="download"
                   @click="downloadExcel('报告期双边换手率')" />
            </div>
            <v-chart autoresize
                     ref="ChartComponent_bgqsbhsl"
                     v-if="!show.changeHandsEmpty"
                     element-loading-text="暂无数据"
                     element-loading-spinner="el-icon-document-delete"
                     element-loading-background="rgba(239, 239, 239, 0.5)"
                     style="height: 340px; width: 100% !important"
                     :options="changeHandsOptions" />
            <el-empty v-else
                      :image-size="200"></el-empty>
          </div>
          <div class="chart-card">
            <div class="chart-card_header_bg">
              <div class="chart-card_title">持仓风格</div>
              <img alt=""
                   src="../../../../../assets/img/download.png"
                   class="download"
                   @click="downloadExcel('持仓风格')" />
            </div>
            <div class="chart-card_body">
              <v-chart autoresize
                       ref="ChartComponent_ccfg"
                       v-if="!show.styleEmpty"
                       element-loading-text="暂无数据"
                       element-loading-spinner="el-icon-document-delete"
                       element-loading-background="rgba(239, 239, 239, 0.5)"
                       style="height: 484px; width: 100% !important"
                       :options="styleOptions" />
              <el-empty style="width: 100%"
                        v-else
                        :image-size="200"></el-empty>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-show="reportTemplate == 'ALL' || reportTemplate == 'MOM'"
         class="page-box">
      <div class="flex item-center justify-between">
        <div class="area-title">MOM当前权益持仓风格（只分析股票）</div>
        <div class="border_table_header_search">
          <el-date-picker v-model="stockStyleDate"
                          type="daterange"
                          range-separator="~"
                          start-placeholder="开始日期"
                          :unlink-panels="true"
                          end-placeholder="结束日期"
                          value-format="yyyyMMdd"
                          @change="getEquityStyle(true)"
                          :picker-options="pickerOptions"
                          style="width: 290px; margin-right: 10px"></el-date-picker>
          <el-radio-group v-model="radio"
                          size="small">
            <el-radio-button label="图表"
                             name="图表" />
            <el-radio-button label="数据"
                             name="数据" />
          </el-radio-group>
          <img alt=""
               src="../../../../../assets/img/download.png"
               class="download"
               @click="downloadExcel('当前权益持仓风格（只分析股票）')" />
        </div>
      </div>

      <div class="area-body">
        <div class="area-chart"
             v-if="radio === '图表'"
             v-loading="areaLoading">
          <div class="piece">
            <div class="piece-list">
              <div v-for="(item, index) in list"
                   :key="index"
                   class="piece-row">
                <span class="piece-name">{{ item.name }}</span>
                <div class="piece-color_row">
                  <div class="piece-color"
                       v-for="(citem, cindex) in item.data"
                       :style="getColor(citem)"
                       :key="cindex">{{ citem }}%</div>
                </div>
              </div>
              <div class="piece-color_row">
                <span class="piece-name" />
                <div class="piece-color"
                     v-for="(citem, cindex) in 3"
                     :key="cindex">
                  {{ cindex === 0 ? '价值' : cindex === 1 ? '均衡' : '成长' }}
                </div>
              </div>
            </div>
            <div>
              投资比例：
              <div class="flex item-center">
                <div class="card"
                     style="background-color: #bf6c00" />
                > 50%
              </div>
              <div class="flex item-center">
                <div class="card"
                     style="background-color: #4096ff" />
                25% - 50%
              </div>
              <div class="flex item-center">
                <div class="card"
                     style="background-color: #ffbe6a" />
                10% - 25%
              </div>
              <div class="flex item-center">
                <div class="card"
                     style="background-color: #ffecd2" />
                0% - 10%
              </div>
            </div>
          </div>
        </div>
        <div class="area-chart"
             v-if="radio === '数据'"
             v-loading="areaLoading">
          <el-table :data="listTable"
                    border>
            <el-table-column align="center"
                             :key="index"
                             label="持仓风格"
                             prop="name" />
            <el-table-column align="center"
                             :key="index"
                             label="投资比例">
              <template slot-scope="{ row }">
                <div>{{ row.data }}%</div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div v-show="reportTemplate == 'ALL' || reportTemplate == 'FOF'"
         class="page-box">
      <styleFOF ref="styleFOF"> </styleFOF>
    </div>
    <div v-show="reportTemplate == 'ALL' || reportTemplate == 'MOM'"
         class="page-box">
      <div class="flex item-center justify-between">
        <div class="area-title">整体财务指标(MOM)</div>
        <div class="border_table_header_search">
          <span class="selector">持仓类型：</span>
          <el-select v-model="wholeData.type"
                     class="search-security"
                     placeholder="请选择"
                     @change="getWholeIndexData('')"
                     style="width: 240px">
            <el-option v-for="item in position"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"
                       style="width: 240px"> </el-option>
          </el-select>
          <span class="selector">考察指标：</span>
          <el-select v-model="wholeData.inspect"
                     class="search-security"
                     placeholder="请选择"
                     @change="getWholeChart"
                     style="width: 240px">
            <el-option v-for="item in index"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"
                       style="width: 240px"> </el-option>
          </el-select>
          <img alt=""
               src="../../../../../assets/img/download.png"
               class="download"
               @click="downloadExcel('整体财务指标')" />
        </div>
      </div>
      <div class="area-body"
           v-loading="wholeData.loading">
        <v-chart autoresize
                 ref="ChartComponent_ztcwzb"
                 v-if="!show.financeEmpty"
                 element-loading-text="暂无数据"
                 element-loading-spinner="el-icon-document-delete"
                 element-loading-background="rgba(239, 239, 239, 0.5)"
                 style="height: 340px; width: 100% !important"
                 :options="wholeData.options" />
        <el-empty v-else
                  :image-size="200"></el-empty>
      </div>
    </div>
    <div v-show="reportTemplate == 'ALL' || reportTemplate == 'FOF'"
         class="page-box">
      <financeFOF ref="financeFOF"></financeFOF>
    </div>
  </div>
</template>

<style scoped lang="scss">
@import '../../../tkdesign';

.border_table_header_search {
	display: flex;
	justify-content: flex-end;
	position: relative;
	margin-bottom: 16px;

	.selector {
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
		line-height: 22px;
		margin: 5px 0 0 25px;
		color: rgba(0, 0, 0, 0.85);
	}

	.search-security {
		width: 250px;
	}
}

.download {
	padding-left: 25px;
	width: 57px;
	height: 32px;
}

.area-chart {
	display: flex;
	justify-content: space-between;
	flex-wrap: wrap;

	.piece {
		width: 100%;
		display: flex;

		.card {
			width: 10px;
			height: 10px;
			margin-right: 4px;
		}
	}

	.piece-list {
		width: 95%;

		.piece-row {
			display: flex;
		}

		.piece-name {
			color: rgba(0, 0, 0, 0.65);
			text-align: center;
			font-size: 12px;
			font-style: normal;
			font-weight: 400;
			line-height: 100px;
			width: 50px;
		}

		.piece-color_row {
			display: flex;
			width: calc(100% - 50px);
			margin-top: 1px;

			.piece-color {
				width: 33%;
				height: 100px;
				line-height: 40px;
				margin-right: 1px;
				align-items: center;
				display: flex;
				justify-content: center;
				text-align: center;
			}
		}
	}

	.chart-card_half {
		width: calc(50% - 8px);
		padding: 0 20px 20px;
		border: 1px solid #d9d9d9;
		border-radius: 4px;
	}

	.chart-card {
		width: 100%;
		padding: 0 20px 0px;
		border: 1px solid #d9d9d9;
		border-radius: 4px;
		margin-top: 16px;
	}

	.chart-card_title {
		display: flex;
		justify-content: space-between;
		color: rgba(0, 0, 0, 0.85);
		font-family: PingFang;
		height: 46px;
		line-height: 46px;
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
		border-bottom: 1px solid #d9d9d9;
	}

	.chart-card_header_bg {
		display: flex;
		flex-direction: row;
		align-items: center;
		border-bottom: 1px solid #d9d9d9;
		justify-content: space-between;
	}

	.chart-card_body {
		display: flex;

		.sidebar {
			width: 120px;
			padding: 20px 0px;
			gap: 40px;
			box-shadow: 19px 0px 20px 0px rgba(0, 0, 0, 0.04);
			background-color: #ffffff;
			height: 484px;

			.card {
				width: 12px;
				height: 8px;
				margin-right: 5px;
			}
		}
	}
}
</style>
