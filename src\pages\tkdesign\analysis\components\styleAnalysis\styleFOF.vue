<!--  -->
<template>
  <div class=''>
    <div class="flex item-center justify-between">
      <div class="area-title">FOF当前权益持仓风格（只分析股票）</div>
      <div class="border_table_header_search">
        <el-date-picker v-model="stockStyleDate"
                        type="daterange"
                        range-separator="~"
                        start-placeholder="开始日期"
                        :unlink-panels="true"
                        end-placeholder="结束日期"
                        value-format="yyyyMMdd"
                        @change="getEquityStyle(true)"
                        :picker-options="pickerOptions"
                        style="width: 290px; margin-right: 10px"></el-date-picker>
        <el-radio-group v-model="radio"
                        size="small">
          <el-radio-button label="图表"
                           name="图表" />
          <el-radio-button label="数据"
                           name="数据" />
        </el-radio-group>
        <img alt=""
             src="../../../../../assets/img/download.png"
             class="download"
             @click="downloadExcel('当前权益持仓风格（只分析股票）')" />
      </div>
    </div>

    <div class="area-body">
      <div class="area-chart"
           v-if="radio === '图表'"
           v-loading="areaLoading">
        <div class="piece">
          <div class="piece-list">
            <div v-for="(item, index) in list"
                 :key="index"
                 class="piece-row">
              <span class="piece-name">{{ item.name }}</span>
              <div class="piece-color_row">
                <div class="piece-color"
                     v-for="(citem, cindex) in item.data"
                     :style="getColor(citem)"
                     :key="cindex">{{ citem }}%</div>
              </div>
            </div>
            <div class="piece-color_row">
              <span class="piece-name" />
              <div class="piece-color"
                   v-for="(citem, cindex) in 3"
                   :key="cindex">
                {{ cindex === 0 ? '价值' : cindex === 1 ? '均衡' : '成长' }}
              </div>
            </div>
          </div>
          <div>
            投资比例：
            <div class="flex item-center">
              <div class="card"
                   style="background-color: #bf6c00" />
              > 50%
            </div>
            <div class="flex item-center">
              <div class="card"
                   style="background-color: #4096ff" />
              25% - 50%
            </div>
            <div class="flex item-center">
              <div class="card"
                   style="background-color: #ffbe6a" />
              10% - 25%
            </div>
            <div class="flex item-center">
              <div class="card"
                   style="background-color: #ffecd2" />
              0% - 10%
            </div>
          </div>
        </div>
      </div>
      <div class="area-chart"
           v-if="radio === '数据'"
           v-loading="areaLoading">
        <el-table :data="listTable"
                  border>
          <el-table-column align="center"
                           :key="index"
                           label="持仓风格"
                           prop="name" />
          <el-table-column align="center"
                           :key="index"
                           label="投资比例">
            <template slot-scope="{ row }">
              <div>{{ row.data }}%</div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import VChart from 'vue-echarts';
import { lineChartOption } from '@/utils/chartStyle';
import { dateList, testData4, testData5, testData6, testData7, testData8, testData9 } from '@/utils/date';
import {
  getObjectConcertion,
  getObjectStockStyle,
  getObjectStockFinance,
  getObjectStyle,
  getObjectTurnover
} from '@/api/pages/analysis/report';
import { handleData } from '@/utils/count';
import { filter_json_to_excel_inside, changColumnToRow, filter_json_to_excel_inside_multiHeader } from '@/utils/exportExcel.js';
import { export_json_to_excel_multiHeader } from '@/vendor/Export2Excel.js';
import {
  downloadWord,
  exportTitleWithSubtitle,
  exportTableMergeHeader,
  exportTitle,
  exportFirstTitle,
  exportChart,
  exportTable,
  Format,
  exportSencondTitle
} from '@/utils/exportWord.js';
export default {
  //import引入的组件需要注入到对象中才能使用
  components: { VChart },
  data () {
    //这里存放数据
    return {
      params: {},
      stockStyleDate: [new Date(this.$route.query.startDate), new Date(this.$route.query.endDate)],
      pickerOptions: {
        disabledDate: (date) => {
          const startTime = this.moment(this.$route.query.startDate).subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss');
          const endTime = this.moment(this.$route.query.endDate).add(1, 'day').format('YYYY-MM-DD HH:mm:ss');
          const checkTime = this.moment(date, 'YYYY-MM-DD HH:mm:ss');
          return !checkTime.isBetween(startTime, endTime);
        }
      },
      listTable: [],
      radio: '图表',
      areaLoading: false,
      list: [
        {
          name: '大盘',
          data: [0, 0, 0]
        },
        {
          name: '均衡',
          data: [0, 0, 0]
        },
        {
          name: '小盘',
          data: [0, 0, 0]
        }
      ],
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    /**
  * 获取颜色
  */
    getColor (item) {
      if (item > 50) return { backgroundColor: '#BF6C00' };
      if (item > 25 && item <= 50) return { backgroundColor: '#4096ff' };
      if (item > 10 && item <= 50) return { backgroundColor: '#FFBE6A' };
      if (item >= 0 && item <= 10) return { backgroundColor: '#FFECD2' };
    },
    downloadExcel (name) {
      if (name === '报告期前十大集中度') {
        const title = [
          { label: '', value: 'date' },
          { label: this.reportName, value: 'top10_concert', format: 'fix2p' }
        ];
        filter_json_to_excel_inside(title, this.topTenData, ['data'], name);
      } else if (name === '报告期双边换手率') {
        const title1 = [
          { label: '', value: 'date' },
          { label: this.reportName, value: 'turnover', format: 'fix2p' }
        ];
        filter_json_to_excel_inside(title1, this.twoSideData, ['data'], name);
      } else if (name === '持仓风格') {
        const format = ['', 'fixb0', 'fixb0', 'fixb0', 'fixb0'];
        const data = changColumnToRow(this.ccfgColumn, format);
        export_json_to_excel_multiHeader([this.ccfgTitle], null, data, name);
      } else if (name === '当前权益持仓风格（只分析股票）') {
        console.log('listTable', this.listTable);
        const title1 = [
          { label: '持仓风格', value: 'name' },
          { label: '投资比例', value: 'data', format: 'fix2b' }
        ];
        filter_json_to_excel_inside(title1, this.listTable, [], name);
      } else if (name === '整体财务指标') {
        console.log(this.wholeData.list);
        let indexName = '';
        if (this.wholeData.list && this.wholeData.list.length > 0) {
          const temp = this.wholeData.list[0];
          indexName = temp?.data?.index_name;
        }
        const type = this.wholeData.type === 'all' ? '全持仓' : '重仓股';
        const title = [
          { label: '', value: 'date' },
          { label: this.reportName + '(PE)', value: 'pe', format: 'fix2b' },
          { label: this.reportName + '(PB)', value: 'pb', format: 'fix2b' },
          { label: this.reportName + '(ROE)', value: 'roe', format: 'fix2b' },
          { label: this.reportName + '(股息率)', value: 'dividendratiolyr', format: 'fix2b' },
          { label: indexName + '(PE)', value: 'index_pe', format: 'fix2b' },
          { label: indexName + '(PB)', value: 'index_pb', format: 'fix2b' },
          { label: indexName + '(ROE)', value: 'index_roe', format: 'fix2b' }
        ];
        filter_json_to_excel_inside(title, this.wholeData.list, ['data'], name + '(' + type + ')');
      }
    },

    /**
     * 返回number
     */
    getNumber (data) {
      if (data === '大盘' || data === '价值') return 0;
      if (data === '均衡') return 1;
      if (data === '小盘' || data === '成长') return 2;
    },
    /**
  * 获取权益持仓风格
  */
    getEquityStyle (date) {
      // console.log("object");
      this.areaLoading = true;
      let data = {
        ...this.params,
        reportTemplate: 'FOF',
      };
      if (date) {
        data.startFrom = Number(this.stockStyleDate[0]);
        data.endTo = Number(this.stockStyleDate[1]);
      }
      return getObjectStockStyle(data).then((res) => {
        this.areaLoading = false;
        if (res.code === 200) {
          // this.show.styleEmpty = false
          this.listTable = [];
          let number = 0;
          res.data.rows.forEach((item) => {
            number += Number(item.data.weight);
          });
          res.data.rows.forEach((item) => {
            this.list[this.getNumber(item.data.bigsmall)].data[this.getNumber(item.data.valuegrowth)] = handleData(
              item.data.weight / number,
              true
            );
          });
          this.list.forEach((item) => {
            item.data.forEach((citem, cindex) => {
              this.listTable.push({
                name: `${item.name}${cindex === 0 ? '价值' : cindex === 1 ? '均衡' : '成长'}`,
                data: citem
              });
            });
          });
          return this.listTable;
          console.log('list数据 =>', this.list);
        } else {
          // this.show.styleEmpty = true
        }
      });
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () {

  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () {
    this.params = {
      reportID: Number(this.$route.query.id),
      startFrom: Number(this.moment(this.$route.query.startDate).format('YYYYMMDD')),
      endTo: Number(this.moment(this.$route.query.endDate).format('YYYYMMDD')),
      industryStandard: 3,
      selectedCuts: this.$route.query.graininess
    };
  },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { }, //如果页面有keep-alive缓存功能，这个函数会触发
}
</script>
<style scoped lang="scss">
@import '../../../tkdesign';

.border_table_header_search {
	display: flex;
	justify-content: flex-end;
	position: relative;
	margin-bottom: 16px;

	.selector {
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
		line-height: 22px;
		margin: 5px 0 0 25px;
		color: rgba(0, 0, 0, 0.85);
	}

	.search-security {
		width: 250px;
	}
}

.download {
	padding-left: 25px;
	width: 57px;
	height: 32px;
}

.area-chart {
	display: flex;
	justify-content: space-between;
	flex-wrap: wrap;

	.piece {
		width: 100%;
		display: flex;

		.card {
			width: 10px;
			height: 10px;
			margin-right: 4px;
		}
	}

	.piece-list {
		width: 95%;

		.piece-row {
			display: flex;
		}

		.piece-name {
			color: rgba(0, 0, 0, 0.65);
			text-align: center;
			font-size: 12px;
			font-style: normal;
			font-weight: 400;
			line-height: 100px;
			width: 50px;
		}

		.piece-color_row {
			display: flex;
			width: calc(100% - 50px);
			margin-top: 1px;

			.piece-color {
				width: 33%;
				height: 100px;
				align-items: center;
				display: flex;
				justify-content: center;
				line-height: 40px;
				margin-right: 1px;
				text-align: center;
			}
		}
	}

	.chart-card_half {
		width: calc(50% - 8px);
		padding: 0 20px 20px;
		border: 1px solid #d9d9d9;
		border-radius: 4px;
	}

	.chart-card {
		width: 100%;
		padding: 0 20px 0px;
		border: 1px solid #d9d9d9;
		border-radius: 4px;
		margin-top: 16px;
	}

	.chart-card_title {
		display: flex;
		justify-content: space-between;
		color: rgba(0, 0, 0, 0.85);
		font-family: PingFang;
		height: 46px;
		line-height: 46px;
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
		border-bottom: 1px solid #d9d9d9;
	}

	.chart-card_header_bg {
		display: flex;
		flex-direction: row;
		align-items: center;
		border-bottom: 1px solid #d9d9d9;
		justify-content: space-between;
	}

	.chart-card_body {
		display: flex;

		.sidebar {
			width: 120px;
			padding: 20px 0px;
			gap: 40px;
			box-shadow: 19px 0px 20px 0px rgba(0, 0, 0, 0.04);
			background-color: #ffffff;
			height: 484px;

			.card {
				width: 12px;
				height: 8px;
				margin-right: 5px;
			}
		}
	}
}
</style>
