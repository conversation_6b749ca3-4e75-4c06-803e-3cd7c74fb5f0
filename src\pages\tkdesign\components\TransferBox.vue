<template>
	<div class="transfer-box-wrapper">
		<div class="transfer-box-left">
			<div class="transfer-box-left-title">
				<div class="tbl-search-wrapper">
					搜索指标：<el-input v-model="searchInput" @input="handleSearchInput" placeholder="请输入内容"></el-input>
				</div>
			</div>
			<div class="transfer-box-left-list">
				<template v-for="item in originalList">
					<div
						class="list-item"
						:class="orginalSelectStatus[item.id] ? 'item-active' : ''"
						@click="handleOriginalItemClick(item)"
						:key="item.id"
					>
						{{ item.name }}
					</div>
				</template>
			</div>
		</div>
		<div class="transfer-box-center">
			<el-button style="width: 82px; margin-bottom: 8px" @click="addToResult">添加<i class="el-icon-arrow-right"></i></el-button>
			<el-button style="width: 82px; margin-bottom: 8px; margin-left: unset" @click="removeFromResult"
				><i class="el-icon-arrow-left"></i>删除</el-button
			>
			<el-button style="width: 82px; margin-left: unset" @click="handleClear">清空已选</el-button>
		</div>
		<div class="transfet-box-right">
			<div class="transfer-box-right-title">已选指数({{ resultList.length }})</div>
			<div class="transfer-box-right-list">
				<template v-for="item in resultList">
					<div
						class="list-item"
						:class="resultSelectStatus[item.id] ? 'item-active' : ''"
						:key="item.id"
						@click="handleResultItemClick(item)"
					>
						{{ item.name }}
						<svg
							@click.stop="handleRemoveOne(item)"
							xmlns="http://www.w3.org/2000/svg"
							width="14"
							height="14"
							viewBox="0 0 14 14"
							fill="none"
						>
							<path
								d="M4.62476 1.8736H4.49976C4.56851 1.8736 4.62476 1.81735 4.62476 1.7486V1.8736H9.37476V1.7486C9.37476 1.81735 9.43101 1.8736 9.49976 1.8736H9.37476V2.9986H10.4998V1.7486C10.4998 1.19703 10.0513 0.748596 9.49976 0.748596H4.49976C3.94819 0.748596 3.49976 1.19703 3.49976 1.7486V2.9986H4.62476V1.8736ZM12.4998 2.9986H1.49976C1.22319 2.9986 0.999756 3.22203 0.999756 3.4986V3.9986C0.999756 4.06735 1.05601 4.1236 1.12476 4.1236H2.06851L2.45444 12.2955C2.47944 12.8283 2.92007 13.2486 3.45288 13.2486H10.5466C11.081 13.2486 11.5201 12.8298 11.5451 12.2955L11.931 4.1236H12.8748C12.9435 4.1236 12.9998 4.06735 12.9998 3.9986V3.4986C12.9998 3.22203 12.7763 2.9986 12.4998 2.9986ZM10.4263 12.1236H3.57319L3.19507 4.1236H10.8044L10.4263 12.1236Z"
								fill="black"
								fill-opacity="0.25"
							/>
						</svg>
					</div>
				</template>
			</div>
		</div>
	</div>
</template>
<script>
export default {
	props: {
		value: {
			type: Array,
			default: () => {
				return [];
			}
		},
		serverInfo: {
			type: Object,
			default: () => {
				// {
				//     url:''
				// }
				return {};
			}
		}
	},
	data() {
		return {
			searchInput: '',
			originalList: [],
			originalListCopy: [],
			orginalSelectStatus: {},
			resultList: [],
			resultSelectStatus: {}
		};
	},
	created() {
		this.getData();
	},
	watch: {
		resultList: {
			handler(newValue, oldValue) {
				this.$emit('input', newValue);
			},
			deep: true
		}
	},
	methods: {
		getData() {
			this.originalList = [
				{ name: '111', id: '1' },
				{ name: '112', id: '2' },
				{ name: '113', id: '3' }
			];
			this.originalListCopy = this.FUNC.deepClone(this.originalList);
			this.resultList = [];
		},
		handleSearchInput() {
			this.getData();
		},
		handleOriginalItemClick(item) {
			if (!item.id) {
				return;
			}
			this.$set(this.orginalSelectStatus, item.id, !this.orginalSelectStatus[item.id]);
		},
		handleResultItemClick(item) {
			if (!item.id) {
				return;
			}
			this.$set(this.resultSelectStatus, item.id, !this.resultSelectStatus[item.id]);
		},
		addToResult() {
			let select = this.originalList.filter((item) => {
				return this.orginalSelectStatus[item.id];
			});
			//将选中了的列表移除至右边
			this.originalList = this.originalList.filter((item) => {
				return !this.orginalSelectStatus[item.id];
			});
			this.resultList = this.resultList.concat(select);
			this.orginalSelectStatus = {};
		},
		removeFromResult() {
			let select = this.resultList.filter((item) => {
				return this.resultSelectStatus[item.id];
			});
			this.resultList = this.resultList.filter((item) => {
				return !this.resultSelectStatus[item.id];
			});

			this.originalList = this.originalList.concat(select);
			this.resultSelectStatus = {};
		},
		handleRemoveOne(item) {
			let select = this.resultList.filter((resultItem) => {
				return resultItem.id === item.id;
			});
			this.resultList = this.resultList.filter((resultItem) => {
				return resultItem.id !== item.id;
			});

			this.originalList = this.originalList.concat(select);
		},
		handleClear() {
			this.originalList = this.FUNC.deepClone(this.originalListCopy);
			this.resultList = [];
		}
	}
};
</script>
<style lang="scss" scoped>
.transfer-box-wrapper {
	display: flex;
	.list-item {
		padding: 8px 16px;
		color: rgba(0, 0, 0, 0.85);
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
		line-height: 22px; /* 157.143% */
		display: flex;
		justify-content: space-between;
	}
	.transfer-box-left {
		border: 1px solid #e9e9e9;
		border-radius: 4px;
		width: 400px;
		.transfer-box-left-title {
			display: flex;
			padding: 8px 16px;
			align-items: center;
			border-bottom: 1px solid #e9e9e9;
			.tbl-search-wrapper {
				display: flex;
				word-break: keep-all;
				align-items: center;
			}
		}
	}
	.transfet-box-right {
		border: 1px solid #e9e9e9;
		border-radius: 4px;
		width: 400px;
		.transfer-box-right-title {
			border-bottom: 1px solid #e9e9e9;
			padding: 10px 16px;
		}
	}
	.transfer-box-center {
		display: flex;
		flex-direction: column;
		padding: 16px;
	}
	.item-active {
		background: #ecf5ff;
	}
}
</style>
