<template>
    <div class="charts_fill_class" v-loading="loading">
        <el-empty image-size="160" v-if="showEmpty"></el-empty>
			<v-chart
				v-else
            ref="companySizeChange"
            :options="option"
            element-loading-text="暂无数据"
            element-loading-spinner="el-icon-document-delete"
            element-loading-background="rgba(239, 239, 239, 0.5)"
            class="charts_one_class"
            autoresize
        ></v-chart>
    </div>
</template>

<script>
import VChart from 'vue-echarts';

export default {
components: { VChart },
props:{
    chartName:{
        type: String,
        default:'bond'
    }
},
data() {
    return {
        option: {},
        loading: true,
			showEmpty: true,
    };
},
methods: {
    getData(data) {
        this.loading = false;
        if(data.length==0){
            return;
        }else{
            
            this.showEmpty = false;
        }
        const self = this;
        this.option = {

                        legend: {
                            left:'50%',
                            orient: 'vertical',
                            bottom:'center',
                            itemWidth: 7,
                            itemHeight: 7,
                            itemGap: 12,
                            selectedMode:false,
                            type: 'scroll',
                            borderRadius: 7, 
                            textStyle: {
                                rich: {
                                    a: {
                                        color: '#333',
                                        fontSize: 16,
                                        width:150,
                                        padding: [0, 10, 0, 0],
                                    },
                                    b: {
                                        color: '#1492ff',
                                        fontSize: 16,
                                        fontWeight:'bold',
                                        padding: [0, 10, 0, 10],
                                    },
                                }
                            },
                        },
                        series: [
                            {
                                name: 'lwyg',
                                type: 'pie',
                                radius: ['40%', '80%'],
                                center:['30%','50%'],
                                minAngle:30,
                                avoidLabelOverlap: false,
                                hoverAnimation:false,
                                label: {
                                    normal:{
                                        show: false,
                                        position: 'center',
                                        formatter:'{b}\n{c}',
                                        textStyle:{
                                            fontSize:0,
                                            color: '#fff',
                                        }
                                    },
                                    emphasis: {
                                        show:true,
                                        textStyle:{
                                            fontSize:14,
                                            fontWeight:'bold'
                                        },
                                        formatter:function(params){
                                            return `{a| ${params.name}}\n{b|${params.value}}`;
                                        },
                                        rich: {
                                            a: {
                                                color: '#333',
                                                fontSize: 14,
                                                lineHeight:25,
                                            },
                                            b: {
                                                color: '#1492ff',
                                                fontSize: 14,
                                                fontWeight:'bold',
                                                lineHeight:25,
                                            },
                                        }
                                    },
                                },
                                labelLine: {
                                    normal:{
                                        show: false
                                    }
                                },
                                data: data
                            }
                        ]
                        };
    }
}
};
</script>

<style scoped>
.chart_one{
padding: 0;
box-shadow: none;
}
.charts_fill_class{
    border: 1px solid #D9D9D9;
    border-radius: 4px;
    .echarts{
    height: 248px;

    }
}
</style>
