<template>
	<div class="transfer-box-wrapper">
		<div class="transfer-box-content">
			<div>
				<div class="transfer-box-wrapper">
					<div class="transfer-left">
						<div class="transfer-left-title">
							<span class="label">{{ searchInputConfigStruct.label }}</span
							><el-input v-model="searchInput" :placeholder="searchInputConfigStruct.placeholder" @input="handelSearch"></el-input>
						</div>
						<el-tabs v-model="typeTabValue" tab-position="left" style="height: 405px" class="left-nav-wrapper">
							<el-tab-pane disabled v-if="navTitle" name="navTitle">
								<span class="nav-title" slot="label">{{ navTitle }}</span>
							</el-tab-pane>
							<!-- 第一级分类循环 -->
							<el-tab-pane v-for="leftItem in optionListCopy" :name="leftItem[typeIdSource]">
								<span slot="label"
									>{{ leftItem[typeLabelSource] }}<span v-if="showCount">（{{ leftItem.children && leftItem.children.length }}）</span>
								</span>
								<!-- 循环具体子内容 -->
								<div v-for="checkItem in leftItem.children" :key="leftItem[typeIdSource] + checkItem[childrenIdSource]">
									<el-checkbox
										@dblclick.native="handleDbclickAdd(checkItem)"
										v-if="!checkItem.checked && !checkItem.searchHide"
										v-model="checkItem.addStash"
										:label="checkItem.value"
									>
										<div class="left-value-wrapper">
											{{ checkItem[childrenLabelSource] }}<span class="value-tag" v-if="checkItem.tag">{{ checkItem.tag }}</span>
										</div>
									</el-checkbox>
								</div>
							</el-tab-pane>
						</el-tabs>
					</div>
					<div class="transfer-center">
						<el-button style="width: 82px; margin-bottom: 8px" @click="handleAdd">添加<i class="el-icon-arrow-right"></i></el-button>
						<el-button style="width: 82px; margin-bottom: 8px; margin-left: unset" @click="handleRemove"
							><i class="el-icon-arrow-left"></i>删除</el-button
						>
						<el-button style="width: 82px; margin-left: unset" @click="handleClear">清空已选</el-button>
					</div>
					<div class="transfer-right">
						<div class="transfer-right-title">已选列({{ selectedData.length }})</div>
						<el-table
							ref="multipleTable"
							:data="selectedData"
							header-row-class-name="table-header-wrapper"
							max-height="405"
							style="width: 480px"
							:highlight-selection-row="true"
							:row-class-name="handleRowClassName"
							@row-dblclick="handleDbclickRemove"
							@row-click="handleSelectionClick"
							@selection-change="handleSelectionChange"
						>
							<template v-for="item in resultTableHeader">
								<el-table-column show-overflow-tooltip :key="item.prop" :prop="item.prop" :label="item.label" align="gotoleft">
									<template slot-scope="scope">
										{{ scope.row[item.prop] }}
									</template>
								</el-table-column>
							</template>
							<el-table-column align="gotoleft" label="" width="50">
								<template slot-scope="scope">
									<svg
										@click.stop="handleRemoveOne(scope.row)"
										xmlns="http://www.w3.org/2000/svg"
										width="14"
										height="14"
										viewBox="0 0 14 14"
										fill="none"
									>
										<path
											d="M4.62476 1.87402H4.49976C4.56851 1.87402 4.62476 1.81777 4.62476 1.74902V1.87402H9.37476V1.74902C9.37476 1.81777 9.43101 1.87402 9.49976 1.87402H9.37476V2.99902H10.4998V1.74902C10.4998 1.19746 10.0513 0.749023 9.49976 0.749023H4.49976C3.94819 0.749023 3.49976 1.19746 3.49976 1.74902V2.99902H4.62476V1.87402ZM12.4998 2.99902H1.49976C1.22319 2.99902 0.999756 3.22246 0.999756 3.49902V3.99902C0.999756 4.06777 1.05601 4.12402 1.12476 4.12402H2.06851L2.45444 12.2959C2.47944 12.8287 2.92007 13.249 3.45288 13.249H10.5466C11.081 13.249 11.5201 12.8303 11.5451 12.2959L11.931 4.12402H12.8748C12.9435 4.12402 12.9998 4.06777 12.9998 3.99902V3.49902C12.9998 3.22246 12.7763 2.99902 12.4998 2.99902ZM10.4263 12.124H3.57319L3.19507 4.12402H10.8044L10.4263 12.124Z"
											fill="black"
											fill-opacity="0.25"
										/>
									</svg>
								</template>
							</el-table-column>
							<el-empty slot="empty" description="暂无数据"></el-empty>
						</el-table>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
// import { getIndexCode } from '@/api/pages/tkAnalysis/captial-market.js';
export default {
	components: {},
	props: {
		//左边搜索框配置选项 详见计算属性searchInputConfigStruct
		searchInputConfig: {
			type: Object,
			default: () => {}
		},
		searchValue: {
			type: String,
			default: ''
		},
		//类型展示label的取值字段
		typeLabelSource: {
			type: String,
			default: 'typeLabel'
		},
		//类型展示唯一标识的取值字段
		typeIdSource: {
			type: String,
			default: 'typeId'
		},
		//每一子项展示label的取值字段
		childrenLabelSource: {
			type: String,
			default: 'childrenLabel'
		},
		//每一子项唯一标识的取值字段
		childrenIdSource: {
			type: String,
			default: 'childrenId'
		},
		//时候需要展示名称后面的计数器
		showCount: {
			type: Boolean,
			default: false
		},
		//左侧一级分类tab的title
		navTitle: {
			type: String,
			default: ''
		},
		//初始列表数据
		optionList: {
			type: Array,
			required: true,
			default: () => {
				return [
					{
						typeLabel: '一级分类展示文案',
						typeId: '一级分类唯一标识',
						children: [
							{
								childrenLabel: '子节点展示文案',
								childrenId: '子节点唯一标识',
								value: {} //注意：该值将决定右侧列表展示的数据 子节点本身的数据值，用户接口传输
							}
						]
					}
				];
			}
		},
		//结果列表配置头配置
		resultTableHeader: {
			type: Array,
			default: () => {
				return [
					{
						prop: 'typeLabel',
						label: '分类'
					}
				];
			}
		},
		//选中的值
		value: {
			type: Array,
			default: () => {
				return [];
			}
		}
	},
	data() {
		return {
			typeTabValue: '',
			multipleSelection: [],
			searchCopeList: [],
			optionListCopy: [] //内部使用的选择列表数据，搜索时使用
		};
	},
	watch: {
		optionList: {
			handler(newValue, oldValue) {
				//当监听到props optionList发生改变时，需要重新赋值内部正真使用的optionList,根据id取值字段生成内部使用的唯一标识
				this.optionListCopy = this.FUNC.deepClone(newValue);
				this.initData();
			},
			deep: true,
			immediate: true
		}
	},
	computed: {
		searchInput: {
			get() {
				return this.searchValue;
			},
			set(val) {
				// console.log('set', val);
				this.$emit('update:searchValue', val);
			}
		},
		/**
		 * 保证对象内支持单属性不传值能使用上正确的默认值
		 * custom 是否自定义
		 */
		searchInputConfigStruct() {
			let { label = '搜索：', placeholder = '请输入', searchName = 'indexName', custom = false } = this.searchInputConfig;
			return {
				...this.searchInputConfig,
				label,
				placeholder,
				searchName,
				custom
			};
		},
		selectedData: {
			get() {
				return this.value;
			},
			set(val) {
				// console.log('set', val);
				this.$emit('input', val);
			}
		}
	},
	mounted() {
		this.typeTabValue = '';
	},
	methods: {
		//双击添加
		handleDbclickAdd(item) {
			this.handleAddOne(item);
		},
		//双击删除
		handleDbclickRemove(row, column, event) {
			this.handleRemoveOne(row);
		},
		handleAddOne(target) {
			for (const item of this.optionListCopy) {
				//只处理符合typeIdSource childrenIdSource一致的情况，找到符合的值后直接结束循环
				if (item[this.typeIdSource] === target.value[this.typeIdSource]) {
					for (let childrenItem of item?.children) {
						if (childrenItem[this.childrenIdSource] === target[this.childrenIdSource]) {
							//右移
							childrenItem.checked = true;
							childrenItem.addStash = false;
							this.selectedData.push({
								...childrenItem.value
							});
							//用于computed读取set
							this.selectedData = this.selectedData;
							break;
						}
					}
					break;
				}
			}
		},
		handelSearch(value) {
			if (this.searchInputConfigStruct.custom) {
				this.$emit('searchInput', value);
				return;
			}
			//输入为空时，查询全量数据
			if (!value) {
				this.optionListCopy.forEach((item) => {
					item.children.forEach((childrenItem) => {
						childrenItem.searchHide = false;
					});
				});
				return;
			}
			this.optionListCopy.forEach((item) => {
				item.children.forEach((childrenItem) => {
					let str = childrenItem[this.searchInputConfigStruct.searchName];
					if (str && str.indexOf(value) < 0) {
						childrenItem.searchHide = true;
					} else {
						childrenItem.searchHide = false;
					}
				});
			});
		},
		//设置右侧table被选中后的高亮样式
		handleRowClassName({ row, rowIndex }) {
			// typeIdName: Symbol('typeId'), //一级类型的唯一标识属性名称
			// childIdName: Symbol('childId'), //子节点唯一标识属性名称
			// console.log('***', row, this.multipleSelection);
			let index = this.multipleSelection.findIndex((item) => {
				return item[this.typeIdSource] === row[this.typeIdSource] && item[this.childrenIdSource] === row[this.childrenIdSource];
			});
			if (index >= 0) {
				return 'is-active';
			}
			return '';
		},
		initData() {
			//初始化checked removeStash addStash属性值 并使其成为响应式
			this.optionListCopy.forEach((item, index) => {
				item?.children?.forEach((target, targetIndex) => {
					this.$set(this.optionListCopy[index]?.children[targetIndex], 'checked', false);
					this.$set(this.optionListCopy[index]?.children[targetIndex], 'addStash', false);
					this.$set(this.optionListCopy[index]?.children[targetIndex], 'searchHide', false);
				});
			});
			this.searchCopeList = this.FUNC.deepClone(this.optionListCopy);
			this.typeTabValue = this.optionListCopy[0] && this.optionListCopy[0][this.typeIdSource];
		},
		handleAdd() {
			//循环下拉列表 第一层为一级分类
			this.optionListCopy.forEach((item) => {
				//循环一级分类下的孩子节点
				item?.children?.forEach((target) => {
					//如果addStash为ture表示以前在左侧列表中选中过 需要移动到右侧
					if (target?.addStash) {
						target.checked = true;
						target.addStash = false;
						this.selectedData.push({
							...target.value
						});
						//用于computed读取set
						this.selectedData = this.selectedData;
					}
				});
			});
			console.log('***', this.optionListCopy);
		},
		handleSelectionClick(row) {
			this.toggleSelection(row);
		},
		toggleSelection(row) {
			this.$refs.multipleTable.toggleRowSelection(row);
		},
		handleSelectionChange(val) {
			//当前选中的列表
			this.multipleSelection = val;
		},
		handleRemoveOne(item) {
			console.log('item', item);
			//当前项在当前列表中的位置
			let index = this.selectedData.findIndex((selectedItem) => {
				return (
					selectedItem[this.typeIdSource] === item[this.typeIdSource] && selectedItem[this.childrenIdSource] === item[this.childrenIdSource]
				);
			});
			if (index >= 0) {
				//将列表中对应的选中状态设置成否
				this.selectedData.splice(index, 1);
				//主动触发selecteData computed的set方法
				this.selectedData = this.selectedData;
				//循环下拉列表 第一层为一级分类
				this.optionListCopy.forEach((optionItem) => {
					//循环一级分类下的孩子节点
					optionItem?.children?.forEach((target) => {
						if (
							target[this.childrenIdSource] === item[this.childrenIdSource] &&
							optionItem[this.typeIdSource] === item[this.typeIdSource]
						) {
							target.checked = false;
						}
					});
				});
				console.log('****', this.optionListCopy);
			}
		},
		handleRemove() {
			this.multipleSelection.forEach((element) => {
				this.handleRemoveOne(element);
			});
			this.multipleSelection = [];
		},
		handleClear() {
			this.initData();
			this.selectedData = [];
		}
	}
};
</script>
<style lang="scss" scoped>
.transfer-box-wrapper {
	display: flex;
	::v-deep .el-table th .cell:after {
		content: '';
		display: none;
	}
	.first-type-wrapper {
		::v-deep .el-tabs__nav-scroll {
			padding: 0 16px;
			.el-tabs__nav-wrap {
				&::after {
					content: unset;
				}
			}
		}
	}
	.transfer-left {
		width: 480px;
		border: 1px solid #e9e9e9;
		border-radius: 4px;
		.left-nav-wrapper {
			::v-deep .el-tabs__header {
				margin-right: 0;
				#tab-navTitle {
					position: sticky;
					top: 0;
					z-index: 1;
					background: #ffffff;
					border-right: 3px solid #e4e7ed;
				}
				.el-tabs__nav-scroll {
					overflow: scroll;
					&::-webkit-scrollbar {
						width: 0; /* 宽度为0会隐藏滚动条 */
						height: 0;
					}
				}
				.el-tabs__nav-prev {
					display: none;
				}
				.el-tabs__nav-next {
					display: none;
				}
				.is-scrollable {
					padding: 0;
				}
			}

			::v-deep .el-tabs__content {
				height: 100%;
				overflow: scroll;
				.el-tab-pane {
					.el-checkbox {
						display: flex;
						padding: 8px;
						.el-checkbox__input {
							display: none;
						}
						&.is-checked {
							background: #f5f5f5;
						}
					}
				}
			}
			.nav-title {
				color: rgba(0, 0, 0, 0.65);
				font-size: 14px;
				font-style: normal;
				font-weight: 500;
				line-height: 22px; /* 157.143% */
			}
			::v-deep .el-tabs__item {
				&.is-active {
					border-right: #4096ff;
					background: rgba(255, 145, 3, 0.1);
				}
			}
			.left-value-wrapper {
				display: flex;
				align-items: center;
				color: rgba(0, 0, 0, 0.85);
				font-size: 14px;
				font-style: normal;
				font-weight: 400;
				line-height: 22px; /* 157.143% */
				.value-tag {
					display: flex;
					padding: 2px 4px;
					border-radius: 2px;
					background: rgba(255, 145, 3, 0.1);
					justify-content: center;
					align-items: center;
					color: #4096ff;
					font-size: 10px;
					font-style: normal;
					font-weight: 400;
					line-height: normal;
					margin-left: 8px;
				}
			}

			::v-deep .el-tabs__header {
				.el-tabs__item {
					text-align: left;
				}
			}
		}
		.transfer-left-title {
			display: flex;
			padding: 8px 16px;
			align-items: center;
			border-bottom: 1px solid #e9e9e9;
			.label {
				color: rgba(0, 0, 0, 0.85);
				font-size: 14px;
				font-style: normal;
				font-weight: 400;
				line-height: 22px; /* 157.143% */
				word-break: keep-all;
			}
		}
	}
	.transfer-center {
		display: flex;
		flex-direction: column;
		padding: 0 20px;
		justify-content: center;
		align-items: center;
	}
	.transfer-right {
		border: 1px solid #e9e9e9;
		border-radius: 4px;
		.transfer-right-title {
			padding: 10px 16px;
			color: #000;
			font-size: 14px;
			font-style: normal;
			font-weight: 400;
			line-height: 22px; /* 157.143% */
		}
		.right-table-item {
			overflow: hidden;
			word-break: keep-all;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
		::v-deep .table-header-wrapper {
			height: 34px;

			th.el-table__cell > .cell {
				height: unset;
			}
			.el-table__cell {
				padding-top: unset !important;
			}
		}
		::v-deep .el-table {
			.el-table__row {
				height: 34px;
				&.is-active {
					background: #f5f5f5;
				}
			}
			&::before {
				display: none;
			}
		}
	}
}
.transfer-box-wrapper {
	background-color: #ffffff;
	border-radius: 4px;
	// .transfer-box-content {
	// 	padding: 0 24px;
	// }
	.transfer-box-header {
		color: rgba(0, 0, 0, 0.85);
		font-size: 16px;
		font-style: normal;
		font-weight: 500;
		line-height: 24px; /* 150% */
		padding: 16px 0;
	}
}
.transfer-box-footer {
	display: flex;
	justify-content: space-between;
	padding: 8px 0;
	color: rgba(0, 0, 0, 0.45);
	font-size: 12px;
	font-weight: 400;
	line-height: 20px; /* 166.667% */
}
.btn-footer-wrapper {
	padding: 16px 24px;
	border-top: 1px solid #e9e9e9;
}
</style>
