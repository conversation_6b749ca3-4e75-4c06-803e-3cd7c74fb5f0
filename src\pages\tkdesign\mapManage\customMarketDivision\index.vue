<template>
  <div class="box_Board">
    <div class="header_box"><span class="header_inactive">投后&nbsp;/&nbsp;映射管理&nbsp;/&nbsp;</span>
      自定义市场划分
    </div>
    <!-- 表格区域 -->
    <div class="border_table">
      <div class="border_table_header">
        <div class="border_table_header_title">自定义市场划分</div>
        <div>
          <el-button icon="el-icon-plus" type="primary" @click="addRow">
            新增
          </el-button>
          <el-button type="" @click="showRecord">
            修改记录
          </el-button>
        </div>
      </div>
      <!-- 表格 -->
      <el-table
          :data="tableData"
          border
          v-loading="loading.tableLoading"
          stripe>
        <el-table-column align="center" label="序号" type="index" width="50"/>
        <el-table-column align="gotoleft" label="市场名称" prop="marketName">
          <template slot-scope="scope">
            <div v-if="scope.row.addFlag">
              <el-input v-model="scope.row.marketName" placeholder="请输入内容"/>
            </div>
            <div v-else
                 style="color: #4096ff;cursor: pointer"
                 @click="gotoItem(scope.row)"
            >{{ scope.row.marketName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column align="gotoleft" label="市场描述" prop="marketDescription">
          <template slot-scope="scope">
            <div v-if="scope.row.addFlag">
              <el-input v-model="scope.row.marketDescription" placeholder="请输入内容"/>
            </div>
            <div v-else>{{ scope.row.marketDescription }}</div>
          </template>
        </el-table-column>
        <el-table-column align="gotoleft" label="创建时间" prop="createDate">
          <template slot-scope="scope">
            <div>{{ scope.row.createDate }}</div>
          </template>
        </el-table-column>
        <el-table-column align="gotoleft" label="修改时间" prop="modifiedDate">
          <template slot-scope="scope">
            {{ scope.row.modifiedDate }}
          </template>
        </el-table-column>
        <el-table-column align="gotoleft" label="操作">
          <template slot-scope="scope">
            <div v-if="!scope.row.addFlag" class="flex">
              <el-button class="button-color" type="text" @click="edit(scope.row)">编辑
              </el-button>
              <el-button class="button-color" type="text" @click="deleteRow(scope.row.id)">
                删除
              </el-button>
            </div>
            <div v-else class="flex">
              <el-button class="button-color" type="text" @click="save(scope.row)">保存
              </el-button>
              <el-button class="button-color" type="text" @click="cancel(scope.row)">取消
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页器 -->
      <div class="pagination_board">
        <el-pagination :current-page.sync="pagination.pageIndex"
                       :page-size="pagination.pageSize"
                       :total="pagination.total"
                       background
                       layout="total, sizes, prev, pager, next"
                       @size-change="sizeChange"
                       @current-change="currentChange">
        </el-pagination>
      </div>
      <!-- 修改记录弹框 -->
      <el-dialog :visible.sync="showDialogRecord" title="修改记录" width="1200px">
        <!-- 表格 -->
        <el-table
            :data="tableDataRecord"
            v-loading="loading.dialogLoading"
            height="400">
          <el-table-column align="center" label="序号" type="index" width="50"/>
          <el-table-column align="gotoleft" label="修改人 ID" prop="userId"></el-table-column>
          <el-table-column align="gotoleft" label="市场名称" prop="context.marketName"></el-table-column>
          <el-table-column align="gotoleft" label="市场描述" prop="context.marketDescription"></el-table-column>
          <el-table-column align="gotoleft" label="阶段明细" prop="" width="280">
            <template slot-scope="scope">
              <div v-if="scope.row.context.timeInterval instanceof Array"
                   v-for="item in scope.row.context.timeInterval">
                {{ item.desc }}： {{ item.startDate }} {{ item.endDate }}
              </div>
              <div v-else>
                <div v-if="JSON.parse(scope.row.context.timeInterval.value) instanceof Array"
                     v-for="item in JSON.parse(scope.row.context.timeInterval.value)">
                  {{ item.desc }}： {{ item.startDate }} {{ item.endDate }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="gotoleft" label="操作类别" prop="actionType">
            <template slot-scope="scope">
              {{ scope.row.actionType === 1 ? '新增' : scope.row.actionType === 2 ? '删除' : '修改' }}
            </template>
          </el-table-column>
          <el-table-column align="gotoleft" label="修改时间" prop="insertDate" width="250"/>
          <el-empty :image-size="160"/>
        </el-table>
        <!-- 分页器 -->
        <div class="pagination_board">
          <el-pagination :current-page.sync="paginationRecord.pageIndex"
                         :page-size="paginationRecord.pageSize"
                         :total="paginationRecord.total"
                         background
                         layout="total, sizes, prev, pager, next"
                         @size-change="sizeChangeRecord"
                         @current-change="currentChangeRecord">
          </el-pagination>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {getList, saveRow, delRow} from "../../../../api/pages/tkdesign/customMarketDivision";
import {getRecordList} from "../../../../api/pages/tkdesign/historyRecord";

export default {
  data() {
    return {
      tableData: [],// 页面表格数据源
      tableDataRecord: [],// 修改记录表格数据源
      oldData: {},

      pagination: {
        pageIndex: 1,// 当前页码
        pageSize: 10,// 页面显示几条数据
        total: 0
      },
      paginationRecord: {
        pageIndex: 1,// 当前页码
        pageSize: 10,// 页面显示几条数据
        total: 0,
      },

      showDialogRecord: false,// 绑定修改记录的dialog
      loading: {
        tableLoading: false,
        dialogLoading: false
      }
    };
  },
  methods: {
    // 每页条数改变时触发的回调
    sizeChange(value) {
      this.pagination.pageSize = value
      this.getList()
    },
    sizeChangeRecord(value) {
      this.paginationRecord.pageSize = value
      this.getRecordList()
    },

    // 当前页数改变时触发的回调
    currentChange(value) {
      this.pagination.pageIndex = value
      this.getList()
    },
    currentChangeRecord(value) {
      this.paginationRecord.pageIndex = value
      this.getRecordList()
    },
    // 跳转详情
    gotoItem(detail) {
      this.$router.push({
        path: '/customMarketDivision/item', query: {
          detail,
        }
      },)
    },

    /**
     * 获取自定义表格数据
     */
    async getList() {
      await getList({
        current: this.pagination.pageIndex,
        pageSize: this.pagination.pageSize
      }).then((res) => {
        if (res.code === 200) {
          this.tableData = res.data
          this.pagination.total = res.total
        } else {
          this.tableData = []
          this.pagination.total = 0
        }
      })
      this.tableData.forEach(item => {
        item.addFlag = false
      })
    },
    /**
     * 刷新表格addFlag状态
     * @param row
     */
    splice(row, data) {
      const number = this.tableData.indexOf(row)
      this.tableData.splice(number, 1)
      this.tableData.splice(number, 0, data)
    },
    /**
     *新增
     */
    async addRow() {
      //判断是否处于编辑状态 如果正在编辑则不允许点击新增按钮
      for await (let item of this.tableData) {
        if (item.addFlag)
          return
      }
      const row = {
        addFlag: true,
        actionType: 1,
        timeInterval: {
          value: JSON.stringify([])
        }
      };
      this.tableData.unshift(row);
    },
    /**
     * 编辑
     * @param row
     */
    async edit(row) {
      for await (let item of this.tableData) {
        if (item.addFlag)
          return
      }
      this.oldData = JSON.parse(JSON.stringify(row))
      row.addFlag = true
      this.splice(row, row)
    },
    /**
     *取消
     * @param row
     */
    cancel(row) {
      if (row.actionType === 1) {
        this.tableData.shift()
      }
      this.splice(row, this.oldData)
    },
    /**
     * 保存
     * @param row
     */
    save(row) {
      row.addFlag = false
      const data = {
        ...row,
        timeInterval: JSON.parse(row.timeInterval.value)
      }
      saveRow(data).then((res) => {
        if (res.code === 200) {
          this.$message.success('上传成功')
          this.getList()
        } else {
          this.$message.error('上传失败')
        }
      })
    },
    /**
     * 删除
     * @param id
     */
    async deleteRow(id) {
      for await (let item of this.tableData) {
        if (item.addFlag)
          return
      }
      this.$confirm('确定删除么?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delRow(id).then((res) => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.getList()
          } else
            this.$message.error(res.message)
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    /**
     * 获取历史修改记录
     */
    getRecordList() {
      this.loading.dialogLoading = true
      const params = {
        type: 7,
        current: this.paginationRecord.pageIndex,
        pageSize: this.paginationRecord.pageSize
      }
      getRecordList(params).then((res) => {
        this.loading.dialogLoading = false
        if (res.code === 200) {
          this.tableDataRecord = res.data.map(item => {
            return {
              ...item,
              context: JSON.parse(item.context.value),
            }
          })
          this.paginationRecord.total = res.total
        } else {
          this.tableDataRecord = []
          this.paginationRecord.total = 0
        }
      })
    },
    showRecord() {
      this.showDialogRecord = true
      this.getRecordList()
    },
  },
  mounted() {
    this.getList()
  },
}
</script>
<style lang="scss" scoped>
@import "../../tkdesign";
</style>
