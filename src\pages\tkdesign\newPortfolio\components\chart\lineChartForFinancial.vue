<template>
    <div class="charts_fill_class" v-loading="loading">
        <el-empty image-size="160" v-if="showEmpty"></el-empty>
			<v-chart
				v-else
            ref="companySizeChange"
            :options="option"
            element-loading-text="暂无数据"
            element-loading-spinner="el-icon-document-delete"
            element-loading-background="rgba(239, 239, 239, 0.5)"
            class="charts_one_class"
            autoresize
            @legendselectchanged="handleLegendSelectChanged"
				@zr:dblclick="handleDblClick"
        ></v-chart>
    </div>
</template>

<script>
import VChart from 'vue-echarts';
import { combinationFinancialIndex} from '@/api/pages/tkAnalysis/portfolio.js';
import { lineChartOption } from '@/utils/chartStyle.js';
export default {
components: { VChart },
data() {
    return {
        option: {},
        loading: true,
			showEmpty: true,
            doubleClick:false,
			legendChanged:false,
			legendData:{}
    };
},
watch: {
		doubleClick: {
			handler(val) {
				if(val && this.legendChanged){
					const chart = this.$refs.companySizeChange;
					let legendWai = this.legendData.name;
					for (const element in this.legendData.selected) {
						//显示当前legent 关闭非当前legent
						if (legendWai == element) {
							chart.dispatchAction({
								type: 'legendSelect',
								name: element
							});
						} else {
							chart.dispatchAction({
								type: 'legendUnSelect',
								name: element
							});
						}
					}
					this.doubleClick = false;
					this.legendChanged = false;
				}
			},
			immediate: true,
		},
	},
methods: {
    handleDblClick(){
			this.doubleClick = true;
		},
		handleLegendSelectChanged (params)  {
			this.legendChanged = true;
			this.legendData = params;
		
			
		},
    getChartData(data,key){
        let name = '';
        let dateList = [],
            data1 = [],
            data2 = [];
            
        data.forEach(item => {
            dateList.push(item.date);
            let val = '0';
            if(key == 'yield' || key == 'netIncomeYoy' || key == 'incomeYoy'){
                val =parseInt(item[key] * 100)/100;
            }else{
                val = item[key];
            }
            data1.push(val);
            data2.push(parseInt(item.indexReturn * 100)/100);
            name = item.indexName || '沪深'
        });
        return {dateList,data1,data2,name};
    },
   async getData(param) {
        this.loading = true;
        let res =  await combinationFinancialIndex(param);
        this.loading = false;
        if(res.mtycode != 200){
            return;
        }
        if(res.data.length >0){
            this.showEmpty = false;
        }else{
            this.showEmpty = true;
            return;
        }
        const {dateList,data1,data2,name} = this.getChartData(res.data,param.index);
        this.$emit('tableData',{dateList,data1,name,data2});
        this.option = lineChartOption({
            toolbox:'none',
            color:['#4096ff', '#4096ff'],
            tooltip: {
					backgroundColor: '#ffffff',
					formatter: function (obj) {
                        //数据排序
                        let list = obj;
						list.sort((a,b)=>{
							if(a.value-b.value < 0){
								return 1;
							}else{
								return -1;
							}
						})
						var value = `<div style="font-size:14px;">` + list?.[0].axisValue + `</div>`;
						for (let i = 0; i < list.length; i++) {
							value +=
								`<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
								`<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
                                    list?.[i].color +
								`;"></div>` +
								`<div style="font-family: PingFang SC;">` +
                                    list?.[i].seriesName +
								'</div></div>' +
								`<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
								(Number(list?.[i].value) * 1).toFixed(2) +
								'%</div>' +
								`</div>`;
						}
						return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
					}
				},
            grid:{
					bottom:30,
					left:40,
					top:20
			},
            legend: {
                bottom:'0%',
                data: [
                    {
                        name: '财务指标',
                    
                    },
                    {
                        name,
                        
                    }
                ]
            },
            xAxis: [{
                type: 'category',
                boundaryGap: true,
                data: dateList,
                axisLabel: {

                    // interval 可以定义成数字，但这也会有一个弊端，在不确定数据量的时候，间隔数不好定义，不能随心所欲的控制所要展示的内容 
                    interval:2
                }, 
            }],
            yAxis: 
                [{
                    type: 'value',
                    axisLine:{
                        show:false
                    },
                    axisTick:{
                        show:false
                    },
                    splitLine:{
                        lineStyle:{
                            type:'dashed'
                        }
                    },
                    name:'财务指标（%）',
                    nameLocation: 'middle' ,
                    nameGap: 40,
                    nameTextStyle:{
                        fontSize: 12,
                        color: "rgba(0, 0, 0, 0.65)"
                    }  
                },{
                    type: 'value',
                    axisLine:{
                        show:false
                    },
                    axisTick:{
                        show:false
                    },
                    splitLine:{
                        lineStyle:{
                            type:'dashed'
                        }
                    },
                    show:false,
                    nameLocation: 'middle' ,
                    nameGap: 40,
                    nameTextStyle:{
                        fontSize: 12,
                        color: "rgba(0, 0, 0, 0.65)"
                    }  
                }],
         
           
            series: [
                {
                    name: '财务指标',
                    type: 'line',
                    symbol: 'none',
                    data: data1,
                },
                {
                    name,
                    type: 'line',
                    symbol: 'none',
                    data: data2,
                }
            ]
        });
    }
}
};
</script>

<style scoped>
.chart_one{
padding: 0;
box-shadow: none;
}
.charts_fill_class{
    .echarts{
    height: 248px;

    }
}
</style>
