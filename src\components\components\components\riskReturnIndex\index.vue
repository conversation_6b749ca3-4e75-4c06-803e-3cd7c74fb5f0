<template>
	<div>
		<analysis-card-title title="风险收益指标" @downloadExcel="exportExcel"> </analysis-card-title>
		<el-table
			v-loading="loadyeji"
			:data="df_measure"
			style="margin-top: 24px; max-height: 400px"
			class="table"
			:cell-style="elcellstyle"
			ref="multipleTable"
			header-cell-class-name="table-header"
			:default-sort="{ prop: 'dateFlag', order: 'descending' }"
		>
			<el-table-column
				v-for="item in columns"
				:key="item.value"
				align="gotoleft"
				:prop="item.value"
				:label="item.label"
				:sortable="item.sortable"
			>
				<template #header>
					<long-table-popover-chart
						v-if="item.popover"
						:data="foramtPopoverData()"
						date_key="dateFlag"
						:data_key="item.value"
						:show_name="item.label"
					>
						<span>{{ item.label }}</span>
					</long-table-popover-chart>
					<span v-else>{{ item.label }}</span>
				</template>
				<template slot-scope="{ row }">
					<span>{{ item.format ? item.format(row[item.value]) : row[item.value] }}</span>
				</template>
			</el-table-column>
			<template slot="empty">
				<el-empty image-size="160"></el-empty>
			</template>
		</el-table>
	</div>
</template>

<script>
import { exportTitle, exportTable } from '@/utils/exportWord.js';
import { filter_json_to_excel } from '@/utils/exportExcel.js';

// 风险收益指标
import { getRiskFeatureYearly } from '@/api/pages/Analysis.js';
// 风险收益指标
export default {
	name: 'riskReturnIndex',
	data() {
		return {
			description: null,
			df_measure: [],
			loadyeji: true,
			columns: [
				{
					label: '年份',
					value: 'dateFlag',
					sortable: true,
					popover: false
				},
				{
					label: '年累计收益',
					value: 'cumReturn',
					sortable: true,
					popover: true,
					format: this.fix2p
				},
				{
					label: '排名',
					value: 'cumReturnRank',
					sortable: true,
					popover: true,
					format: this.fix2p
				},
				{
					label: '最大回撤',
					value: 'maxdrawdown',
					sortable: true,
					popover: true,
					format: this.fix2p
				},
				{
					label: '排名',
					value: 'maxdrawdownRank',
					sortable: true,
					popover: true,
					format: this.fix2p
				},
				{
					label: '波动率',
					value: 'volatility',
					sortable: true,
					popover: true,
					format: this.fix2p
				},
				{
					label: '排名',
					value: 'volatilityRank',
					sortable: true,
					popover: true,
					format: this.fix2p
				},
				{
					label: '夏普率',
					value: 'sharpe',
					sortable: true,
					popover: true,
					format: this.fix3
				},
				{
					label: '排名',
					value: 'sharpeRank',
					sortable: true,
					popover: true,
					format: this.fix2p
				}
			],
			info: {}
		};
	},
	methods: {
		// 获取风险收益指标
		async getRiskFeatureYearly() {
			this.loadyeji = true;
			let riskFeatureYearlyData = {
				code: this.info.code,
				type: this.info.type,
				flag: this.info.flag,
				start_date: this.info.start_date,
				end_date: this.info.end_date
			};
			let data = await getRiskFeatureYearly(riskFeatureYearlyData);
			this.loadyeji = false;
			if (data?.mtycode == 200) {
				return data?.data;
			} else {
				this.hideLoading();
				return [];
			}
		},
		// 获取数据
		async getData(info) {
			this.info = info;
			this.fundindextype = info?.type;
			let data = await this.getRiskFeatureYearly();
			this.df_measure = data;
			this.loadyeji = false;
		},
		formatColumn() {
			let indexList = [
				{
					label: '跟踪误差',
					value: 'trackingerror',
					sortable: true,
					popover: true,
					format: this.fix2p
				},
				{
					label: '排名',
					value: 'trackingerrorRank',
					sortable: true,
					popover: true,
					format: this.fix2p
				}
			];
			let enhanceList = [
				{
					label: '信息比率',
					value: 'information',
					sortable: true,
					popover: true,
					format: this.fix2p
				},
				{
					label: '排名',
					value: 'informationRank',
					sortable: true,
					popover: true,
					format: this.fix2p
				}
			];
			if ((this.fundindextype = 'equityindex' || this.fundindextype == 'equityenhance')) {
				this.columns.push(...indexList);
			} else if ((this.fundindextype = 'equityenhance')) {
				this.columns.push(...enhanceList);
			}
		},
		// 隐藏loading
		hideLoading() {
			this.loadyeji = false;
		},
		// df_measure
		foramtPopoverData() {
			let data = [];
			this.df_measure.map((item) => {
				let obj = { ...item };
				for (const key in item) {
					let format = this.columns.find((obj) => {
						return obj.value == key;
					})?.format;
					if (format) {
						let val = format(item[key]);
						obj[key] = typeof val == 'string' ? (val.includes('%') ? val?.split('%')?.[0] * 1 : !isNaN(val) ? val * 1 : val) : val;
					}
				}
				data.push(obj);
			});
			return data;
		},
		// 行样式
		elcellstyle({ row, column, rowIndex, columnIndex }) {
			if (columnIndex == 1) {
				if (row['cumReturn'] >= 0) {
					return 'color: #E85D2D;';
				} else return 'color: #18C2A0;';
			}
		},
		fix2p(val) {
			return val == '--' ? val : !isNaN(val) ? Number(val * 100)?.toFixed(2) + '%' : '--';
		},
		fix3(value) {
			return parseInt(value * 1000) / 1000 ? parseInt(value * 1000) / 1000 : '--';
		},
		// 导出excel
		exportExcel() {
			let list = [
				{ label: '年份', value: 'year', fill: 'header' },
				{ label: '年累计收益', value: 'cumReturn', format: 'fix2p', fill: 'red_or_green' },
				{ label: '排名', value: 'cumReturnRank', format: 'fix2p' },
				{ label: '最大回撤', value: 'maxdrawdown', format: 'fix2p' },
				{ label: '排名', value: 'maxdrawdownRank', format: 'fix2p' },
				{ label: '波动率', value: 'volatility', format: 'fix2p' },
				{ label: '排名', value: 'volatilityRank', format: 'fix2p' },
				{ label: '夏普率', value: 'sharpe', format: 'fix2p' },
				{ label: '排名', value: 'sharpeRank', format: 'fix2p' }
			];
			let indexList = [
				{ label: '跟踪误差', value: 'trackingerror', format: 'fix2p' },
				{ label: '排名', value: 'trackingerrorRank', format: 'fix2p' }
			];
			let enhanceList = [
				{ label: '信息比率', value: 'information', format: 'fix2p' },
				{ label: '排名', value: 'informationRank', format: 'fix2p' }
			];
			if ((this.fundindextype = 'equityindex' || this.fundindextype == 'equityenhance')) {
				list.push(...indexList);
			} else if ((this.fundindextype = 'equityenhance')) {
				list.push(...enhanceList);
			}
			filter_json_to_excel(list, this.df_measure, '风险收益指标');
		},
		// 导出
		createPrintWord() {
			let list = [
				{ label: '年份', value: 'year', fill: 'header' },
				{ label: '年累计收益', value: 'cumReturn', format: 'fix2p', fill: 'red_or_green' },
				{ label: '排名', value: 'cumReturnRank', format: 'fix2p' },
				{ label: '最大回撤', value: 'maxdrawdown', format: 'fix2p' },
				{ label: '排名', value: 'maxdrawdownRank', format: 'fix2p' },
				{ label: '波动率', value: 'volatility', format: 'fix2p' },
				{ label: '排名', value: 'volatilityRank', format: 'fix2p' },
				{ label: '夏普率', value: 'sharpe', format: 'fix2p' },
				{ label: '排名', value: 'sharpeRank', format: 'fix2p' }
			];
			let indexList = [
				{ label: '跟踪误差', value: 'trackingerror', format: 'fix2p' },
				{ label: '排名', value: 'trackingerrorRank', format: 'fix2p' }
			];
			let enhanceList = [
				{ label: '信息比率', value: 'information', format: 'fix2p' },
				{ label: '排名', value: 'informationRank', format: 'fix2p' }
			];
			if ((this.fundindextype = 'equityindex' || this.fundindextype == 'equityenhance')) {
				list.push(...indexList);
			} else if ((this.fundindextype = 'equityenhance')) {
				list.push(...enhanceList);
			}
			if (this.df_measure.length) {
				return [...exportTitle('风险收益指标'), ...exportTable(list, this.df_measure.reverse(), '', true)];
			} else {
				return [];
			}
		}
	}
};
</script>

<style></style>
