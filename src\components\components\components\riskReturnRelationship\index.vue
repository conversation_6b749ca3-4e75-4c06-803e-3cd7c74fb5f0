<template>
	<div>
		<analysis-card-title title="风险收益关系" @downloadExcel="exportExcel">
			<div v-show="info.type !== 'equityindex' && info.type !== 'equity-index' && info.type !== 'portfolio'">
				基准选择：<el-select v-model="valuebase" @change="onChangeBenchmark" placeholder="请选择基准概念">
					<el-option v-for="item in optionsbase" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>
			</div>
		</analysis-card-title>
		<div v-loading="fengxianshowe">
			<el-table :data="base1" class="table" :cell-style="elcellstyle2" header-cell-class-name="table-header">
				<el-table-column prop="code" label="累计收益" width="240px" align="gotoleft">
					<template slot-scope="scope">
						<span>{{ scope.row['name'] }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="1week" label="近一周" align="right">
					<template slot-scope="scope"
						><span>{{ scope.row['1week'] | fix2p }}</span></template
					>
				</el-table-column>
				<el-table-column prop="1month" label="近一月" align="right">
					<template slot-scope="scope"
						><span>{{ scope.row['1month'] | fix2p }}</span></template
					>
				</el-table-column>
				<el-table-column prop="1quarter" label="近一季" align="right">
					<template slot-scope="scope"
						><span>{{ scope.row['1quarter'] | fix2p }}</span></template
					>
				</el-table-column>
				<el-table-column prop="1year" label="近一年" align="right">
					<template slot-scope="scope"
						><span>{{ scope.row['1year'] | fix2p }}</span></template
					>
				</el-table-column>
				<el-table-column prop="3year" label="近三年" align="right">
					<template slot-scope="scope"
						><span>{{ scope.row['3year'] | fix2p }}</span></template
					>
				</el-table-column>
				<template slot="empty">
					<el-empty image-size="40"></el-empty>
				</template>
			</el-table>
			<el-table :data="base2" class="table" header-cell-class-name="table-header">
				<el-table-column prop="meter" label="最大回撤" width="240px" align="gotoleft">
					<template slot-scope="scope">
						<span>{{ scope.row['name'] }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="1week" label="近一周" align="right">
					<template slot-scope="scope"
						><span>{{ scope.row['1week'] | fix2p }}</span></template
					>
				</el-table-column>
				<el-table-column prop="1month" label="近一月" align="right">
					<template slot-scope="scope"
						><span>{{ scope.row['1month'] | fix2p }}</span></template
					>
				</el-table-column>
				<el-table-column prop="1quarter" label="近一季" align="right">
					<template slot-scope="scope"
						><span>{{ scope.row['1quarter'] | fix2p }}</span></template
					>
				</el-table-column>
				<el-table-column prop="1year" label="近一年" align="right">
					<template slot-scope="scope"
						><span>{{ scope.row['1year'] | fix2p }}</span></template
					>
				</el-table-column>
				<el-table-column prop="3year" label="近三年" align="right">
					<template slot-scope="scope"
						><span>{{ scope.row['3year'] | fix2p }}</span></template
					>
				</el-table-column>
				<template slot="empty">
					<el-empty image-size="40"></el-empty>
				</template>
			</el-table>
			<el-table :data="base3" class="table" header-cell-class-name="table-header">
				<el-table-column prop="meter" label="年化夏普率" width="240px" align="gotoleft">
					<template slot-scope="scope">
						<span>{{ scope.row['name'] }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="1week" label="近一周" align="right">
					<template slot-scope="scope"
						><span>{{ scope.row['1week'] | fix2 }}</span></template
					>
				</el-table-column>
				<el-table-column prop="1month" label="近一月" align="right">
					<template slot-scope="scope"
						><span>{{ scope.row['1month'] | fix2 }}</span></template
					>
				</el-table-column>
				<el-table-column prop="1quarter" label="近一季" align="right">
					<template slot-scope="scope"
						><span>{{ scope.row['1quarter'] | fix2 }}</span></template
					>
				</el-table-column>
				<el-table-column prop="1year" label="近一年" align="right">
					<template slot-scope="scope"
						><span>{{ scope.row['1year'] | fix2 }}</span></template
					>
				</el-table-column>
				<el-table-column prop="3year" label="近三年" align="right">
					<template slot-scope="scope"
						><span>{{ scope.row['3year'] | fix2 }}</span></template
					>
				</el-table-column>
				<template slot="empty">
					<el-empty image-size="40"></el-empty>
				</template>
			</el-table>
		</div>
	</div>
</template>

<script>
import { exportTitle, exportTable } from '@/utils/exportWord.js';
import { filter_json_to_excel } from '@/utils/exportExcel.js';
import searchConponents from '@/components/components/components/search/index.vue';

// 基准列表
import { getBenchmarkList, getRiskFeatureRecent } from '@/api/pages/Analysis.js';
// 风险收益关系, 基准概念列表
// import { getRiskFeatureRecent } from '@/api/pages/components/riskReturnRelationship.js';
// 风险收益关系
export default {
	components: { searchConponents },
	name: 'riskReturnRelationship',
	data() {
		return {
			optionsbase: [],
			valuebase: '',
			valuebasename: '',
			fengxianshowe: true,
			base1: [],
			base2: [],
			base3: [],
			info: {},
			postData: {},
			show: true
		};
	},
	props: {
		indexInfo: {
			type: {
				type: Object,
				default: {}
			}
		}
	},
	filters: {
		fix3(value) {
			return parseInt(value * 1000) / 1000;
		},

		fix2p(value) {
			return value == '--' || !value ? '--' : (value * 100).toFixed(2) + '%';
		},
		fix2(value) {
			return value == '--' || !value ? '--' : (value * 1).toFixed(2) + '%';
		}
	},
	methods: {
		// 获取风险收益关系基准列表
		async getBestBenchmarks() {
			let data = await getBenchmarkList({
				code: this.info.code,
				type: this.info.type,
				template: 'risk_return',
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			if (data?.mtycode == 200) {
				this.getBenchmarkList(data?.data);
			} else {
				this.getBenchmarkList([]);
			}
		},
		// 获取风险收益关系
		async getRiskFeatureRecent() {
			let data = await getRiskFeatureRecent({
				code: this.info.code,
				type: this.info.type,
				flag: this.info.flag,
				start_date: this.info.start_date,
				end_date: this.info.end_date,
				index_code: this.postData.index
			});
			if (data?.mtycode == 200) {
				this.base1 = this.formatData(data?.data, 'cum_return');
				this.base2 = this.formatData(data?.data, 'maxdrawdown');
				this.base3 = this.formatData(data?.data, 'sharpe0');
			}
			this.fengxianshowe = false;
		},
		// 获取数据
		getData(info) {
			this.info = info;
			if (this.info.end_date !== '' && this.moment(this.info.end_date).isBefore(this.moment())) {
				this.show = false;
				return;
			}
			this.getBestBenchmarks();
		},
		formatData(data, key) {
			let result = [];
			data.map((item) => {
				let index = result.findIndex((v) => v.code == item.code);
				let obj = {};
				obj[item.recent] = item[key];
				if (index == -1) {
					result.push({
						code: item.code,
						...obj
					});
				} else {
					result[index] = { ...result[index], ...obj };
				}
			});
			return result.map((item) => {
				return {
					...item,
					name: item.code.includes('_self')
						? '自身基准'
						: this.info.code == item.code
						? this.info.name
						: this.valuebase == item.code
						? this.optionsbase.filter((obj) => {
								return obj.value == item.code;
						  })[0]?.label
						: item.code
						? item.code
						: '--'
				};
			});
		},
		// 格式化数据
		filterData(data, key) {
			if (this.info.type == 'equityindex' || this.info.type == 'equity-index') {
				return data?.[key]
					?.filter((item) => {
						return this.info.code == item.code;
					})
					.map((item) => {
						return {
							...item,
							name:
								this.info.code == item.code
									? this.info.name
									: this.valuebase == item.code
									? this.optionsbase.filter((obj) => {
											return obj.value == item.code;
									  })[0]?.label
									: item.code
									? item.code
									: '--'
						};
					});
			} else {
				return data?.[key]?.map((item) => {
					return {
						...item,
						name:
							this.info.code == item.code
								? this.info.name
								: this.valuebase == item.code
								? this.optionsbase.filter((obj) => {
										return obj.value == item.code;
								  })[0]?.label
								: item.code
								? item.code
								: '--'
					};
				});
			}
		},
		// 格式化风险收益关系
		formatRiskRecent(data) {
			let self = data?.self;
			let reference = data?.reference;
			let arr = [];
			if (self?.length) {
				self.map((item) => {
					item['code'] = this.info.code;
				});
				arr.push(...self);
			}
			if (reference?.length) {
				reference.map((item) => {
					item['code'] = this.info.type == 'portfolio' ? this.indexInfo.name : this.valuebase;
				});
				arr.push(...reference);
			}
			let filterData = {};
			for (const key in arr?.[0]) {
				if (key !== 'code' && key !== 'recent') {
					filterData[key] = arr.map((item) => {
						let obj = {};
						obj[item.recent] = item[key];
						return {
							code: item.code,
							...obj
						};
					});
				}
			}
			for (const key in filterData) {
				let arr = [];
				filterData[key].map((item) => {
					let index = arr.findIndex((obj) => {
						return obj.code == item.code;
					});
					if (index == -1) {
						arr.push(item);
					} else {
						arr[index] = { ...arr[index], ...item };
					}
				});
				filterData[key] = arr;
			}
			return filterData;
		},
		// 获取基准列表
		getBenchmarkList(data) {
			let optionsbase = [];
			if (data?.length) {
				for (let i = 0; i < data?.length; i++) {
					optionsbase.push({
						label: data?.[i]?.indexName,
						value: data?.[i]?.indexCode
					});
				}
			}
			this.optionsbase = optionsbase;
			this.valuebase =
				optionsbase?.filter((item) => {
					return item.meter !== 0;
				})?.[0]?.value || data?.[0]?.value;
			this.valuebasename =
				optionsbase?.filter((item) => {
					return item.meter !== 0;
				})?.[0]?.label || data?.[0]?.label;
			this.postData.index = this.valuebase;
			this.getRiskFeatureRecent();
		},
		// 监听基准变化
		onChangeBenchmark() {
			this.fengxianshowe = true;
			this.postData.index = this.valuebase;
			this.getRiskFeatureRecent();
		},
		// 表格动态样式
		elcellstyle2({ row, column, rowIndex, columnIndex }) {
			if (columnIndex == 1) {
				if (row['1week'] >= 0) {
					return 'color: #E85D2D;';
				} else return 'color: #18C2A0;';
			}
			if (columnIndex == 2) {
				if (row['1month'] >= 0) {
					return 'color: #E85D2D;';
				} else return 'color: #18C2A0;';
			}
			if (columnIndex == 3) {
				if (row['1quarter'] >= 0) {
					return 'color: #E85D2D;';
				} else return 'color: #18C2A0;';
			}
			if (columnIndex == 4) {
				if (row['1year'] >= 0) {
					return 'color: #E85D2D;';
				} else return 'color: #18C2A0;';
			}
			if (columnIndex == 5) {
				if (row['3year'] >= 0) {
					return 'color: #E85D2D;';
				} else return 'color: #18C2A0;';
			}
		},
		// 导出excel
		exportExcel() {
			let list = [
				{ label: '累计收益', value: 'namer', fill: 'header' },
				{ label: '近一周', value: '1weekr', format: 'fix2p', fill: 'red_or_green' },
				{ label: '近一月', value: '1monthr', format: 'fix2p', fill: 'red_or_green' },
				{ label: '近一季', value: '1quarterr', format: 'fix2p', fill: 'red_or_green' },
				{ label: '近一年', value: '1yearr', format: 'fix2p', fill: 'red_or_green' },
				{ label: '近三年', value: '3yearr', format: 'fix2p', fill: 'red_or_green' },
				{ label: '最大回撤', value: 'namem', fill: 'header' },
				{ label: '近一周', value: '1weekm', format: 'fix2p' },
				{ label: '近一月', value: '1monthm', format: 'fix2p' },
				{ label: '近一季', value: '1quarterm', format: 'fix2p' },
				{ label: '近一年', value: '1yearm', format: 'fix2p' },
				{ label: '近三年', value: '3yearm', format: 'fix2p' },
				{ label: '年化夏普率', value: 'names', fill: 'header' },
				{ label: '近一周', value: '1weeks', format: 'fix2b' },
				{ label: '近一月', value: '1months', format: 'fix2b' },
				{ label: '近一季', value: '1quarters', format: 'fix2b' },
				{ label: '近一年', value: '1years', format: 'fix2b' },
				{ label: '近三年', value: '3years', format: 'fix2b' }
			];
			let data = [];

			let base1 = this.base1.map((item) => {
				return {
					namer: item.name,
					'1weekr': item['1week'],
					'1monthr': item['1month'],
					'1quarterr': item['1quarter'],
					'1yearr': item['1year'],
					'3yearr': item['3year']
				};
			});
			let base2 = this.base2.map((item) => {
				return {
					namem: item.name,
					'1weekm': item['1week'],
					'1monthm': item['1month'],
					'1quarterm': item['1quarter'],
					'1yearm': item['1year'],
					'3yearm': item['3year']
				};
			});
			let base3 = this.base3.map((item) => {
				return {
					names: item.name,
					'1weeks': item['1week'],
					'1months': item['1month'],
					'1quarters': item['1quarter'],
					'1years': item['1year'],
					'3years': item['3year']
				};
			});
			base1.map((item, index) => {
				let obj = { ...item, ...base2?.[index], ...base3?.[index] };
				data.push(obj);
			});
			filter_json_to_excel(list, data, '风险收益关系');
		},
		// 导出
		createPrintWord() {
			let returnList = [
				{ label: '累计收益', value: 'name', fill: 'header' },
				{ label: '近一周', value: '1week', format: 'fix2p', fill: 'red_or_green' },
				{ label: '近一月', value: '1month', format: 'fix2p', fill: 'red_or_green' },
				{ label: '近一季', value: '1quarter', format: 'fix2p', fill: 'red_or_green' },
				{ label: '近一年', value: '1year', format: 'fix2p', fill: 'red_or_green' },
				{ label: '近三年', value: '3year', format: 'fix2p', fill: 'red_or_green' }
			];
			let maxdrawdownList = [
				{ label: '最大回撤', value: 'name', fill: 'header' },
				{ label: '近一周', value: '1week', format: 'fix2p' },
				{ label: '近一月', value: '1month', format: 'fix2p' },
				{ label: '近一季', value: '1quarter', format: 'fix2p' },
				{ label: '近一年', value: '1year', format: 'fix2p' },
				{ label: '近三年', value: '3year', format: 'fix2p' }
			];
			let sharpeList = [
				{ label: '年化夏普率', value: 'name', fill: 'header' },
				{ label: '近一周', value: '1week', format: 'fix2' },
				{ label: '近一月', value: '1month', format: 'fix2' },
				{ label: '近一季', value: '1quarter', format: 'fix2' },
				{ label: '近一年', value: '1year', format: 'fix2' },
				{ label: '近三年', value: '3year', format: 'fix2' }
			];
			if (this.base1?.length && this.base2?.length && this.base3?.length) {
				return [
					...exportTitle('风险收益关系'),
					...exportTable(returnList, this.base1),
					...exportTable(maxdrawdownList, this.base2),
					...exportTable(sharpeList, this.base3)
				];
			} else {
				return [];
			}
		}
	}
};
</script>

<style scoped>
.table_scoll {
	height: 215px;
	overflow-y: scroll;
}
</style>
