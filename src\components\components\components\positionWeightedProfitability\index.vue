<template>
	<div class="chart_one">
		<analysis-card-title title="持仓加权盈利能力" @downloadExcel="exportExcel"></analysis-card-title>
		<div style="width: 100%">
			<el-table
				v-loading="loading"
				:data="finance"
				style="width: 99%"
				height="400px"
				class="table"
				ref="multipleTable"
				:default-sort="{ prop: 'yearqtr', order: 'descending' }"
				header-cell-class-name="table-header"
			>
				<el-table-column v-for="item in column" :key="item.value" align="gotoleft" :prop="item.value" :label="item.label" sortable>
					<template #header>
						<long-table-popover-chart
							v-if="item.popover"
							:data="formatTableData()"
							date_key="yearqtr"
							:data_key="item.value"
							:show_name="item.label"
						>
							<span>{{ item.label }}</span>
						</long-table-popover-chart>
						<span v-else>{{ item.label }}</span>
					</template>
					<template slot-scope="{ row }">
						<span>{{ item.format ? item.format(row[item.value]) : row[item.value] }}</span>
					</template>
				</el-table-column>
				<template slot="empty">
					<el-empty image-size="160"></el-empty>
				</template>
			</el-table>
		</div>
	</div>
</template>

<script>
import { exportTitle, exportTable } from '@/utils/exportWord.js';
import { filter_json_to_excel } from '@/utils/exportExcel.js';
// 持仓加权盈利水平
import { getStockHistoryHold } from '@/api/pages/Analysis.js';
export default {
	name: 'positionWeightedProfitability',
	data() {
		return {
			finance: [],
			loading: true,
			column: [
				{
					label: '季度',
					value: 'yearqtr',
					popover: false
				},
				{
					label: '净资产回报率',
					value: 'roe',
					format: this.fix3,
					popover: true
				},
				{
					label: '收入增速',
					value: 'income_yoy',
					format: this.fix3,
					popover: true
				},
				{
					label: '净利润增速',
					value: 'net_income_yoy',
					format: this.fix3,
					popover: true
				}
			],
			info: {}
		};
	},
	methods: {
		// 获取持股加权水平数据
		async getStockHistoryHold() {
			let data = await getStockHistoryHold({
				flag: this.info.flag,
				code: this.info.code,
				type: this.info.type,
				feature_flag: '2',
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			if (data?.mtycode == 200) {
				this.loading = false;
				this.finance = data?.data;
			}
		},
		getData(info) {
			this.info = info;
			this.getStockHistoryHold();
		},
		formatTableData() {
			let data = [];
			this.finance.map((item) => {
				let obj = { ...item };
				for (const key in item) {
					let format = this.column.find((obj) => {
						return obj.value == key;
					})?.format;
					if (format) {
						let val = format(item[key]);
						obj[key] = typeof val == 'string' ? (val.includes('%') ? val?.split('%')?.[0] * 1 : !isNaN(val) ? val : val) : val;
					}
				}
				data.push(obj);
			});
			return data;
		},
		fix3(value) {
			return parseInt(value * 1000) / 1000;
		},
		exportExcel() {
			let list = [
				{
					label: '季度',
					fill: 'header',
					value: 'yearqtr'
				},
				{
					label: '净资产回报率',
					value: 'roe',
					format: 'fix3'
				},
				{
					label: '收入增速',
					value: 'income_yoy',
					format: 'fix3'
				},
				{
					label: '净利润增速',
					value: 'net_income_yoy',
					format: 'fix3'
				}
			];
			filter_json_to_excel(
				list,
				this.finance.sort((a, b) => {
					return this.moment(this.moment(a.yearqtr, 'YYYY QQ').format()).isAfter(this.moment(b.yearqtr, 'YYYY QQ').format()) ? -1 : 1;
				}),
				'持仓加权盈利能力'
			);
		},
		createPrintWord() {
			let list = [
				{
					label: '季度',
					fill: 'header',
					value: 'yearqtr'
				},
				{
					label: '净资产回报率',
					value: 'roe',
					format: 'fix3'
				},
				{
					label: '收入增速',
					value: 'income_yoy',
					format: 'fix3'
				},
				{
					label: '净利润增速',
					value: 'net_income_yoy',
					format: 'fix3'
				}
			];
			if (this.finance.length) {
				return [
					...exportTitle('持仓加权盈利能力'),
					...exportTable(
						list,
						this.finance
							.sort((a, b) => {
								return this.moment(this.moment(a.yearqtr, 'YYYY QQ').format()).isAfter(this.moment(b.yearqtr, 'YYYY QQ').format()) ? -1 : 1;
							})
							.slice(0, 10),
						'',
						true
					)
				];
			} else {
				return [];
			}
		}
	}
};
</script>

<style></style>
