<template>
	<div class="institute-form">
		<div class="drawer-header">
			<div class="header-title">
				<span>{{ !id ? '新增机构' : isCheck ? '查看机构' : '编辑机构' }}</span>
			</div>
			<div class="close-btn" @click="companyclose">
				<i class="el-icon-close"></i>
			</div>
		</div>
		<el-form ref="companyForm" :model="companyForm" :rules="companyRules" class="edit-company">
			<el-form-item label="机构名称" prop="name">
				<el-input v-model="companyForm.name" :disabled="isCheck" :placeholder="isCheck ? '' : '请输入'" maxlength="200"></el-input>
			</el-form-item>
			<el-form-item label="联系人" prop="contact">
				<el-input v-model="companyForm.contact" :disabled="isCheck" :placeholder="isCheck ? '' : '请输入'"></el-input>
			</el-form-item>
			<el-form-item label="联系电话" prop="mobile">
				<el-input v-model="companyForm.mobile" :disabled="isCheck" :placeholder="isCheck ? '' : '请输入'"></el-input>
			</el-form-item>
			<el-form-item label="备注" prop="notes">
				<el-input v-model="companyForm.notes" :disabled="isCheck" :placeholder="isCheck ? '' : '请输入'"></el-input>
			</el-form-item>
			<el-form-item label="名片" class="business-card-upload">
				<br />
				<div class="upload-card">
					<el-upload
						class="upload-box"
						v-show="!isCheck"
						:action="`${$baseUrl}/institues/${id}`"
						:show-file-list="false"
						:auto-upload="false"
						accept="image/jpg,image/jpeg,image/png"
						:file-list="fileList"
						:on-change="onUploadChange"
					>
						<el-button size="small">点击上传机构名片</el-button>
					</el-upload>
					<div>{{ companyForm.imageName }}</div>
				</div>
			</el-form-item>
			<el-form-item v-if="id" label="机构销售计划对应表" prop="serviceplans">
				<br />
				<div v-if="companyForm.serviceplans.length > 0" style="color: #999">
					<span v-for="(item, index) of companyForm.serviceplans" :key="index">
						{{ item }}{{ index == companyForm.serviceplans.length - 1 ? '' : ', ' }}
					</span>
				</div>
				<div v-else style="color: #999">无</div>
			</el-form-item>
			<el-form-item v-if="id" label="机构员工" prop="users">
				<br />
				<div v-if="companyForm.users.length > 0" style="color: #999">
					<span v-for="(item, index) of companyForm.users" :key="index">
						{{ item }}{{ index == companyForm.users.length - 1 ? '' : ', ' }}
					</span>
				</div>
				<div v-else style="color: #999">无</div>
			</el-form-item>
			<el-form-item class="submit-btn" v-if="!isCheck">
				<el-button type="primary" @click="companySubmitForm()">保存</el-button>
				<el-button @click="companyclose()">取消</el-button>
			</el-form-item>
			<el-form-item class="submit-btn" v-else>
				<el-button @click="companyclose()">关闭</el-button>
			</el-form-item>
		</el-form>
	</div>
</template>

<script>
import { refreshUserGroups, getInstitues, patchInstitues, postInstitues } from '@/api/pages/SystemOther.js';
export default {
	data() {
		return {
			companyForm: {
				// id: '',
				name: '',
				contact: '',
				mobile: '',
				notes: '',
				imageName: '',
				serviceplans: [],
				users: []
			},
			companyRules: {
				name: { required: true, message: '机构名称不能为空', trigger: 'blur' },
				mobile: [
					{ required: true, message: '联系电话不能为空', trigger: 'blur' },
					{ type: 'string', pattern: /^1[3-9]\d{9}$/, message: '请输入长度为11位的手机号码', trigger: 'blur' }
				]
			},
			fileList: []
		};
	},
	props: {
		visible: {
			type: Boolean,
			require: true
		},
		id: {
			require: true
		},
		isCheck: {
			type: Boolean,
			default: false
		}
	},
	created() {},
	mounted() {
		// //console.log('id', this.id);
		if (this.visible && this.id) {
			this.getCompanyDetail();
		}
	},
	watch: {
		visible(val) {
			if (this.visible && this.id) {
				this.getCompanyDetail();
			}
		}
	},
	methods: {
		async getCompanyDetail() {
			let data = await getInstitues(this.id);
			this.companyForm = {
				// id: res.data.id,
				name: data.name,
				contact: data.contact,
				mobile: data.mobile,
				notes: data.notes,
				imageName: data.image ? data.image.split('/').pop() : '',
				serviceplans: data.serviceplans.map((item) => item.title),
				users: data.users
			};
		},
		companySubmitForm() {
			if (!this.id && this.fileList.length == 0) {
				this.$message.error('请上传机构名片');
				return;
			}
			this.$refs.companyForm.validate(async (valid) => {
				if (valid) {
					let params = new FormData();
					params.append('name', this.companyForm.name);
					params.append('contact', this.companyForm.contact);
					params.append('mobile', this.companyForm.mobile);
					params.append('notes', this.companyForm.notes);
					if (this.fileList.length) {
						params.append('image', this.fileList[0].raw);
					}
					let result = {};
					if (!!this.id) {
						result = await patchInstitues(this.id, params);
					} else {
						result = await postInstitues(params);
					}
					if (result.id == 200) {
						this.$message.success('成功');
						this.companyDrawer = false;
						this.$emit('companyclose', true);
						this.resetCompanyForm();
						this.callRefresh();
					}
				}
			});
		},
		companyclose() {
			this.resetCompanyForm();
			this.$emit('companyclose');
		},
		resetCompanyForm() {
			this.companyForm = {
				// id: '',
				name: '',
				contact: '',
				mobile: '',
				notes: '',
				imageName: '',
				serviceplans: [],
				users: []
			};
			this.fileList = [];
			this.$refs.companyForm.resetFields();
		},
		// 上传机构名片
		onUploadChange(file) {
			// console.log('file: ', file, 'fileList: ', this.fileList);
			this.fileList = [file];
			this.companyForm.imageName = file.name;
		},
		async callRefresh() {
			await refreshUserGroups();
		}
	}
};
</script>

<style lang="scss" scoped>
.edit-company {
	.upload-box ::v-deep .el-upload--text {
		display: inline;
		border: none;
	}
	.submit-btn {
		display: flex;
		justify-content: flex-end;
	}
}
.drawer-header {
	margin-bottom: 20px;
	display: flex;
	justify-content: space-between;
	font-size: 22px;
	.header-title {
		border-bottom: 2px solid #f68136;
	}
	.close-btn {
		cursor: pointer;
	}
}
</style>
