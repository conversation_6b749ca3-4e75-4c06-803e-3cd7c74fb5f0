<template>
	<div class="charts_fill_class">
		<!-- <div id="shareDataset" autoresize :class="canvasClass"></div> -->
		<!-- <v-chart :options="option" :class="canvasClass" autoresize /> -->
		<v-chart
			ref="shareDataset"
			v-loading="loading"
			class="charts_one_class"
			style="height: 600px"
			autoresize
			element-loading-text="暂无数据"
			element-loading-spinner="el-icon-document-delete"
			element-loading-background="rgba(239, 239, 239, 0.5)"
			:options="option"
			@click.native="updateAxisPointer"
		/>
	</div>
</template>

<script>
import VChart from 'vue-echarts';
export default {
	props: ['canvasClass'],
	data() {
		return {
			option: {},
			loading: true,
			currentIndex: 0
		};
	},
	components: {
		VChart
	},
	methods: {
		getCharts(data) {
			// data = data.filter((item) => {
			// 	return this.moment(this.moment(item.yearqtr, 'YYYY QQ').format()).isAfter(this.moment('2019 Q3', 'YYYY QQ').format());
			// });
			this.loading = false;
			data = data.filter((item) => {
				return item.weight;
			});
			// let myChart = echarts.init(document.getElementById('shareDataset'));
			let nameList = data.map((item) => {
				return item.industry_name;
			});
			let source = [];
			let yearList = Array.from(
				new Set(
					data.map((item) => {
						return item.yearqtr;
					})
				)
			).sort((a, b) => {
				return this.moment(this.moment(a, 'YYYY QQ').format()).isBefore(this.moment(b, 'YYYY QQ').format()) ? -1 : 1;
			});
			nameList.map((item) => {
				if (
					!source.some((arr) => {
						return arr.indexOf(item) !== -1;
					})
				) {
					source.push([
						item,
						...yearList.map((item) => {
							return 0;
						})
					]);
				}
			});

			data.map((obj) => {
				yearList.map((item, index) => {
					if (obj.yearqtr == item) {
						source.map((list) => {
							if (list[0] == obj.industry_name) {
								list[index + 1] = obj.weight;
							}
						});
					}
				});
			});
			let option = {
				toolbox: {
					feature: {
						saveAsImage: { pixelRatio: 3 }
					}
				},
				color: [
					'#DB7093',
					'#DA70D6',
					'#800080',
					'#9370DB',
					'#6A5ACD',
					'#4169E1',
					'#B0C4DE',
					'#4682B4',
					'#FDDBC7',
					'#F3A483',
					'#D45C4E',
					'#409eff',
					'#f39c12',
					'#ff1744',
					'#d500f9',
					'#2979ff',
					'#00e5ff',
					'#ff5722',
					'#ffea00',
					'#ff3d00',
					'#ff8a80',
					'#ff80ab',
					'#b388ff',
					'#8c9eff',
					'#a7ffeb',
					'#ffff00',
					'#ffab40',
					'#ffebee',
					'#e8eaf6',
					'#e1f5fe',
					'#fffde7',
					'#efebe9'
				],
				legend: {
					show: false
				},
				tooltip: {
					trigger: 'axis',
					showContent: false,
					textStyle: {
						fontSize: '14px'
					}
				},
				dataset: {
					source: [['product', ...yearList], ...source]
				},
				tooltip: {
					trigger: 'axis',
					formatter: (val) => {
						this.currentIndex = val?.[0].dataIndex;
					}
				},

				xAxis: {
					margin: 12,
					type: 'category',
					axisLabel: {
						textStyle: {
							fontSize: '14px',
							color: 'rgba(0,0,0,0.65)'
						}
					},
					axisLine: {
						lineStyle: {
							color: '#e9e9e9'
						}
					},
					axisTick: {
						show: false
					}
				},
				yAxis: {
					margin: 16,
					gridIndex: 0,
					name: '权重(%)',
					nameTextStyle: {
						fontSize: '14px',
						color: 'rgba(0,0,0,0.65)'
					},
					splitLine: {
						lineStyle: {
							type: 'dashed',
							color: '#e9e9e9'
						}
					},
					axisLine: {
						lineStyle: {
							color: '#e9e9e9'
						}
					},
					axisLabel: {
						textStyle: {
							fontSize: '14px',
							color: 'rgba(0,0,0,0.65)'
						}
					}
				},
				grid: { top: '50%', left: 0, right: 0, bottom: '40px', containLabel: true },
				dataZoom: [
					{
						type: 'slider',
						xAxisIndex: 0,
						filterMode: 'none'
					}
				],
				series: [
					...source.map((item) => {
						return {
							type: 'bar',
							smooth: true,
							seriesLayoutBy: 'row',
							emphasis: { focus: 'series' }
						};
					}),
					{
						type: 'pie',
						id: 'pie',
						radius: '30%',
						center: ['50%', '25%'],
						emphasis: {
							focus: 'self'
						},
						label: {
							// show: false,
							// position: 'inside',
							textStyle: {
								fontSize: '14px'
							},
							formatter: function (val) {
								if (val.percent > 0) {
									return val.name + val.percent + '%';
								} else {
									return '';
								}
							}
						}
						// labelLine: { length: 10, length2: 10, smooth: true }
					}
				]
			};
			console.log(option);
			this.option = option;
		},
		updateAxisPointer(event) {
			console.log(this.currentIndex);
			const xAxisInfo = { value: this.currentIndex };
			if (xAxisInfo) {
				const dimension = xAxisInfo.value + 1;
				this.$refs['shareDataset'].mergeOptions({
					series: {
						id: 'pie',
						label: {
							formatter: function (val) {
								if (val.percent > 0) {
									return val.data[0] + val.percent + '%';
								} else {
									return '';
								}
							}
						},
						// labelLine: { show: false, length: 10, length2: 10, smooth: true },
						encode: {
							value: dimension,
							tooltip: dimension
						}
					}
				});
			}
		}
	}
};
</script>

<style scoped>
.canvasBig {
	width: 100% !important;
	height: 500px !important;
}
.canvasSmall {
	width: 100% !important;
	height: 400px !important;
}
</style>
