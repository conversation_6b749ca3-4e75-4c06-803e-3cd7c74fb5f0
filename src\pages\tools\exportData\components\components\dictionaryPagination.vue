<template>
	<div class="pagination-box">
		<el-pagination
			@size-change="handleSizeChange"
			@current-change="handleCurrentChange"
			:current-page.sync="currentPage"
			:page-size="size"
			layout="total, prev, pager, next"
			:total="total"
		>
		</el-pagination>
	</div>
</template>

<script>
export default {
  props:{
    size: Number,
    total: Number
  },
	data() {
		return {
			currentPage: 1
		};
	},
	methods: {
		handleSizeChange(val) {
			console.log(`每页 ${val} 条`);
		},
		handleCurrentChange(val) {
			console.log(`当前页: ${val}`);
      this.$emit('handleCurrentChange', val);
		}
	}
};
</script>

<style scoped>
</style>