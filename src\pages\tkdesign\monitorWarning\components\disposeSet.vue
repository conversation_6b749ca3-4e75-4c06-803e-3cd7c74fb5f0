<script>
export default {
  props: ['detail'],
  data () {
    return {
      formData: {
        notifyMethod: true
      }
    };
  },
  methods: {
    changeData () {
      console.log(this.formData.notifyMethod);
    },
    changeInput () {

    },
  },
  watch: {
    detail: {
      handler (newValue, oldValue) {
        // console.log(newValue, oldValue, 'XXXXXXXXXXXXXXXXXX')
        if (newValue === undefined) {
          this.formData.notifyMethod = []
          this.detail = {
            ...this.detail,
            desiny: '',
          }
          return
        } else
          this.formData.notifyMethod = JSON.parse(newValue.notifyMethod.value)
      },
      deep: true,
    },
    formData: {
      handler (newValue) {
        const obj = {
          notifyMethod: newValue.notifyMethod,
          desiny: this.detail.desiny
        }
        this.$emit('uploadData', obj)
      },
      deep: true
    }
  },
  mounted () {
    console.log(this.detail);
    if (this.detail === undefined) {
      this.formData.notifyMethod = []
      this.detail = {
        ...this.detail,
        desiny: '',
      }
    }
    else {
      this.formData.notifyMethod = JSON.parse((this.detail?.notifyMethod?.value))
    }
  },
};
</script>

<template>
  <div class="body">
    <div>
      <p class="operation">风险监控触发后，会执行以下操作</p>
      <div>
        <el-checkbox-group v-model="formData.notifyMethod"
                           class="checklist"
                           @change="changeData">
          <!-- <el-checkbox v-show="false"
                       :label='1'>浏览器弹窗提示</el-checkbox>
          <el-checkbox v-show="false"
                       :label="2">浏览器声音提示</el-checkbox> -->
          <el-checkbox :label="3">邮件提示</el-checkbox>
        </el-checkbox-group>
      </div>
    </div>
    <div>
      <p class="inform">通知相关收件人</p>
      <el-input type="textarea"
                placeholder="发送给多人时，邮箱地址请用英文分号分开"
                v-model="detail.desiny"
                class="text-field"
                @change="changeInput" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.body {
	height: calc(100vh - 490px);
	overflow: auto;
	padding: 0 24px 96px 24px;
	font-size: 14px;
}

.operation {
	color: rgba(0, 0, 0, 0.85);
	font-family: PingFang SC;
	font-size: 15px;
	font-style: normal;
	font-weight: 600;
	line-height: 22px;
	margin-top: 5px;
}

.checklist {
	display: flex;
	flex-direction: column;
	color: rgba(0, 0, 0, 0.85);
	font-family: PingFang SC;
	font-style: normal;
	font-weight: 600;
	line-height: 40px;
}

.inform {
	color: rgba(0, 0, 0, 0.85);
	font-family: PingFang SC;
	font-size: 15px;
	font-style: normal;
	font-weight: 600;
	line-height: 22px;
	margin-top: 15px;
}
</style>
<style>
.text-field .el-textarea__inner {
	min-height: 200px !important;
}
</style>
