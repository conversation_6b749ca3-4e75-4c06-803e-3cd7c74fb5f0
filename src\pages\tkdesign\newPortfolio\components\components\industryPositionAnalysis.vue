<template>
    <div class="plate-wrapper fund-performance-board-wrapper">
        <combinationComponentHeader title="行业持仓分析" showMoreBtn @download="exportExcel">
            <template slot="right">
                <div style="margin-right: 16px;">
                        <el-radio-group class="radio-group-wrapper" v-model="form.penetrateFlag" size="small" style="margin-left: 0 !important;" @input="radioChange">
                            <el-radio-button :label="true">穿透fof持仓</el-radio-button>
                            <el-radio-button :label="false">不穿透fof持仓</el-radio-button>
                            
                        </el-radio-group>
                    </div>
                <el-select v-model="form.industryStandard" placeholder="选择行业口径" style="margin-right: 16px;"  @change="radioChange">
                    <el-option
                        v-for="item in options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                        </el-option>
                </el-select>
                <div style="margin-right: 16px;">
                    <FormTimePicker v-model="preset_time" @input="handleFormChange"></FormTimePicker>
                </div>
            </template>
        </combinationComponentHeader>
        <div style="display: flex;justify-content: space-between;gap:12px;align-items: center;">
            <PolylineStackDiagramChartForIndustry ref="fund-performance-board-chart-container3" style="flex: 1;" @tableData="getTableData"></PolylineStackDiagramChartForIndustry>
        </div>
    </div>
</template>
<script>
import combinationComponentHeader from './combinationComponentHeader.vue';
import PolylineStackDiagramChartForIndustry from '../chart/PolylineStackDiagramChartForIndustry.vue';
import FormTimePicker from './formTimePicker.vue';
import { filter_to_excel } from "@/utils/exportExcel.js";
const dayjs = require('dayjs');
export default {
    name:'industryPositionAnalysis',
    components:{
        combinationComponentHeader,
        PolylineStackDiagramChartForIndustry,
        FormTimePicker
    },
    data(){
        return {
            form:{
                penetrateFlag:true,
                industryStandard:'sw',
                startDate: dayjs().subtract(1, 'year').format('YYYY-MM-DD'),
                endDate: dayjs().format('YYYY-MM-DD')
            },
            options:[{
                label:'申万一级行业',
                value: 'sw'
            },{
                label:'泰康一级行业',
                value: 'tk'
            }],
            preset_time: {
                radioValue: '1',
                startDate: dayjs().subtract(1, 'year').format('YYYY-MM-DD'),
                endDate: dayjs().format('YYYY-MM-DD')
            },
            param:null,
              tableHeader:[{
                prop: 'name',
                label: '产品名称'
            }],
            tableData:[],
        }
    },
    methods:{
        radioChange(){
            this.getData(this.param);
        },
        handleFormChange(val) {
            this.preset_time = val;
            this.form.startDate = val.startDate;
            this.form.endDate = val.endDate;
            this.getData(this.param);
		},
        getData(param){
            this.param = param;
          
            let chartDom3 = this.$refs['fund-performance-board-chart-container3'];
            chartDom3?.getData({
                ...param,
                ...this.form
            });
        },
          // 获取表格数据的方法
       getTableData(val) {
        // 遍历日期列表，将每个日期作为表头添加到表头数组中
        val.date_list.forEach(item => {
           this.tableHeader.push({
             prop: item, // 使用日期作为表头属性名
             label: item // 使用日期作为表头显示名称
           });
         });
         // 遍历图例列表，为每个图例创建一个表格行
         val.itemList.forEach((item, index) => {
           this.tableData.push({
             name: item.label // 将图例名称作为表格行的 name 属性
           });
           // 遍历日期列表，将每个日期对应的数据填充到表格行中
           val.date_list.forEach((item2, index2) => {
             this.tableData[index][item2] = val.series[index]['data'][index2]; // 将数据填充到表格行中
           });
         });
       },
       exportExcel(){
          // 将表头数据进行遍历，生成新的数组list，每个元素包含原表头数据和format字段
          let list = this.tableHeader.map((item) => {
            return {
              ...item,
              format: ''
            };
          });
          // 调用filter_to_excel函数，传入list、表格数据this.tableData和文件名'基金标签'
          filter_to_excel(list, this.tableData, '行业持仓分析');
        },
    },
}
</script>
<style lang="scss" scoped>
.fund-performance-board-wrapper {
    .select-form-wrapper {
        margin-bottom: 16px;
    }
    .content-table-wrapper {
        margin-bottom: 32px;
    }
}

</style>