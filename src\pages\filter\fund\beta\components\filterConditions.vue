<template>
	<div class="dialogFilerDX">
		<div style="width: 100%">
			<div style="display: flex; width: 100%">
				<div style="width: 200px; border-right: 1px solid #e9e9e9">
					<div style="text-align: left; margin-left: 24px; margin-top: 16px">
						<div style="font-weight: 500; font-size: 16px; line-height: 24px; color: rgba(0, 0, 0, 0.85); margin-bottom: 16px">
							筛选条件
						</div>
						<el-scrollbar style="height: 628px">
							<el-tree :data="data" :props="defaultProps" @node-click="handleNodeClick"></el-tree>
						</el-scrollbar>
					</div>
				</div>
				<div style="flex: 19">
					<div style="text-align: left">
						<el-scrollbar style="min-height: 602px; height: calc(100vh - 682px)">
							<div>
								<!-- 写死的 -->
								<div style="border-bottom: 1px solid #e9e9e9" class="boxItemDetailM">
									<div class="titleContent">计算区间</div>
									<div class="contentBoxFilter2">
										<div style="display: flex; align-items: center; margin-bottom: 8px">
											<el-radio v-model="radio" label="latest">最近一期持仓</el-radio>
											<el-radio v-model="radio" label="3">最近3年</el-radio>
											<el-radio v-model="radio" label="6">最近6年</el-radio>
											<el-radio v-model="radio" label="created">成立以来</el-radio>
											<el-radio v-model="radio" label="radioSelf">
												<span style="margin-right: 8px">自定义</span>
												<el-input v-model="radioSelf" placeholder="年" style="width: 60px"></el-input>
											</el-radio>
										</div>
										<div class="contentDel"></div>
									</div>
								</div>
								<div v-for="(item, index) in activeCondition" :key="index">
									<div class="boxItemDetail">
										<div class="titleContent">{{ item.label }}</div>
										<div v-for="obj in item.children" :key="obj.value" style="display: flex; align-items: center">
											<div v-for="(val, i) in obj.condition" :key="i">
												<div v-if="val.type == 'text'">{{ val.value }}</div>
												<div v-else-if="val.type == 'dropdown'">
													<el-dropdown @command="command3">
														<el-button type="primary">
															<span>{{
																val.value == '' ? val.placeholder : val.list[val.list.findIndex((v) => v.value == val.value)].label
															}}</span>
															<i class="el-icon-arrow-down el-icon--right"></i>
														</el-button>
														<el-dropdown-menu slot="dropdown">
															<el-dropdown-item v-for="(v, v_index) in val.list" :command="v.value" :key="v_index">{{
																v.label
															}}</el-dropdown-item>
														</el-dropdown-menu>
													</el-dropdown>
												</div>
												<div v-else-if="val.type == 'input'">
													<el-input style="width: 200px" v-model="val.value" :placeholder="val.placeholder"></el-input>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</el-scrollbar>

						<div
							style="text-align: right; margin-right: 24px; padding-top: 24px; border-top: 1px solid #e9e9e9"
							class="demo-drawer__footer"
						>
							<el-radio v-model="radioFilterOrOut" :label="false" @click.native.prevent="changeRadio">反选</el-radio>
							<el-button @click="out(1)" type="">重置</el-button>
							<el-button type="primary" @click="out(2)">下一步</el-button>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	data() {
		return {
			radio: 'latest',
			radioSelf: '',
			radioFilterOrOut: false,
			data: [
				{
					label: '基础信息',
					children: [
						{
							label: '基金名称',
							value: 'name',
							condition: [
								{
									type: 'text',
									value: '基金名称'
								},
								{
									type: 'dropdown',
									value: '包含',
									placeholder: '选择条件',
									list: [
										{
											label: '包含',
											value: '包含'
										},
										{
											label: '除外',
											value: '除外'
										}
									]
								},
								{
									type: 'input',
									value: '',
									placeholder: '输入基金名称包含/除外字段'
								}
							],
							typeCate: '9', //1为运算符
							type: 'a'
						}
						// 		{
						// 			label: '基金经理规模',
						// 			typeCate: '1', //1为运算符
						// 			type: 'a'
						// 		},
						// 		{
						// 			label: '基金经理管理经验',
						// 			typeCate: '1',
						// 			type: 'a'
						// 		},
						// 		{
						// 			label: '基金规模',
						// 			typeCate: '1',
						// 			type: 'a'
						// 		},
						// 		{
						// 			label: '可申购金额',
						// 			typeCate: '1',
						// 			type: 'a'
						// 		},
						// 		{
						// 			label: '基金分类',
						// 			typeCate: '2', //2为基金分类特有
						// 			type: 'a'
						// 		},

						// 		{
						// 			label: '是否量化',
						// 			typeCate: '4',
						// 			type: 'a', //3为持有人特有
						// 			conditions: [
						// 				{
						// 					label: '是',
						// 					value: 'true'
						// 				},
						// 				{
						// 					label: '否',
						// 					value: 'false'
						// 				}
						// 			]
						// 		},
						// 		{
						// 			label: '基金持有人特征',
						// 			typeCate: '3',
						// 			type: 'a', //3为持有人特有
						// 			conditions: [
						// 				{
						// 					label: '机构定制',
						// 					value: '机构定制'
						// 				},
						// 				{
						// 					label: '机构为主',
						// 					value: '机构为主'
						// 				},
						// 				{
						// 					label: '散户为主',
						// 					value: '散户为主'
						// 				}
						// 			]
						// 		},
						// 		{
						// 			label: '基金存续时长',
						// 			typeCate: '1',
						// 			type: 'a'
						// 		},
						// 		{
						// 			label: '基金经理任职时长',
						// 			typeCate: '1',
						// 			type: 'a'
						// 		},
						// 		{
						// 			label: '申购类型',
						// 			typeCate: '3',
						// 			type: 'a',
						// 			conditions: [
						// 				{
						// 					label: '开放式基金',
						// 					value: '开放式基金'
						// 				},
						// 				{
						// 					label: '封闭式基金',
						// 					value: '封闭式基金'
						// 				},
						// 				{
						// 					label: '持有期基金',
						// 					value: '持有期基金'
						// 				}
						// 			]
						// 		}
						// 	]
						// },
						// // {
						// // 	label: '费用及限制',
						// // 	children: [
						// // 		{
						// // 			label: '可申购金额',
						// // 			typeCate: '1',
						// // 			type: 'b'
						// // 		},
						// // 		{
						// // 			label: '申购费',
						// // 			typeCate: '1',
						// // 			type: 'b'
						// // 		},
						// // 		{
						// // 			label: '赎回费',
						// // 			typeCate: '1',
						// // 			type: 'b'
						// // 		},
						// // 		{
						// // 			label: '管理费',
						// // 			typeCate: '1',
						// // 			type: 'b'
						// // 		},
						// // 		{
						// // 			label: '托管费',
						// // 			typeCate: '1',
						// // 			type: 'b'
						// // 		},
						// // 		{
						// // 			label: '销售服务费',
						// // 			typeCate: '1',
						// // 			type: 'b'
						// // 		},
						// // 		{
						// // 			label: '综合费率',
						// // 			typeCate: '1',
						// // 			type: 'b'
						// // 		}
						// // 	]
						// // },
						// {
						// 	label: '基金大类资产约束',
						// 	children: [
						// 		{
						// 			label: '股票',
						// 			typeCate: '1', //4 name+运算符
						// 			type: 'c'
						// 		},
						// 		{
						// 			label: '债券',
						// 			typeCate: '1', //4 name+运算符
						// 			type: 'c'
						// 		}
						// 	]
						// },
						// {
						// 	label: '权益债券资产约束',
						// 	children: [
						// 		{
						// 			label: 'A股仓位',
						// 			typeCate: '4', //4 name+运算符
						// 			type: 'n'
						// 		},
						// 		{
						// 			label: 'A股仓位变化',
						// 			typeCate: '1', //4 name+运算符
						// 			type: 'n',
						// 			conditions: [
						// 				{
						// 					label: '稳定',
						// 					value: '稳定'
						// 				},
						// 				{
						// 					label: '温和变化',
						// 					value: '温和变化'
						// 				},
						// 				{
						// 					label: '剧烈变化',
						// 					value: '剧烈变化'
						// 				}
						// 			]
						// 		},
						// 		{
						// 			label: '港股仓位',
						// 			typeCate: '4', //4 name+运算符
						// 			type: 'n'
						// 		},
						// 		{
						// 			label: '港股仓位变化',
						// 			typeCate: '1', //4 name+运算符
						// 			type: 'n',
						// 			conditions: [
						// 				{
						// 					label: '稳定',
						// 					value: '稳定'
						// 				},
						// 				{
						// 					label: '温和变化',
						// 					value: '温和变化'
						// 				},
						// 				{
						// 					label: '剧烈变化',
						// 					value: '剧烈变化'
						// 				}
						// 			]
						// 		}
						// 	]
						// },
						// {
						// 	label: '最新权益债券资产约束',
						// 	children: [
						// 		{
						// 			label: '最新一期A股仓位',
						// 			typeCate: '41', //4 name+运算符
						// 			type: 'n1'
						// 		},
						// 		{
						// 			label: '最新一期港股仓位',
						// 			typeCate: '41', //4 name+运算符
						// 			type: 'n1'
						// 		}
						// 	]
						// },
						// {
						// 	label: '风险特征',
						// 	children: [
						// 		{
						// 			label: '波动率',
						// 			typeCate: '4', //4 name+运算符
						// 			type: 'g'
						// 		},
						// 		{
						// 			label: '最大回撤',
						// 			typeCate: '4', //4 name+运算符
						// 			type: 'g'
						// 		},
						// 		{
						// 			label: '平均下行周期',
						// 			typeCate: '4', //4 name+运算符
						// 			type: 'g'
						// 		},
						// 		{
						// 			label: '平均恢复周期',
						// 			typeCate: '4', //4 name+运算符
						// 			type: 'g'
						// 		},
						// 		{
						// 			label: '最大回撤比',
						// 			typeCate: '4', //4 name+运算符
						// 			type: 'g'
						// 		},
						// 		{
						// 			label: '在险价值',
						// 			typeCate: '4', //4 name+运算符
						// 			type: 'g'
						// 		},
						// 		{
						// 			label: '期望损失',
						// 			typeCate: '4', //4 name+运算符
						// 			type: 'g'
						// 		},
						// 		{
						// 			label: '下行风险',
						// 			typeCate: '4', //4 name+运算符
						// 			type: 'g'
						// 		},
						// 		{
						// 			label: '波动率比',
						// 			typeCate: '4', //4 name+运算符
						// 			type: 'g'
						// 		},
						// 		{
						// 			label: '痛苦指数',
						// 			typeCate: '4', //4 name+运算符
						// 			type: 'g'
						// 		}
						// 	]
						// },
						// {
						// 	label: '风险收益特征',
						// 	children: [
						// 		{
						// 			label: '年化收益率',
						// 			typeCate: '4', //4 name+运算符
						// 			type: 'h'
						// 		},
						// 		{
						// 			label: '累计收益率',
						// 			typeCate: '4', //4 name+运算符
						// 			type: 'h'
						// 		},
						// 		{
						// 			label: '夏普率（rf==0）',
						// 			typeCate: '4', //4 name+运算符
						// 			type: 'h'
						// 		},
						// 		{
						// 			label: '夏普率（rf==4%）',
						// 			typeCate: '4', //4 name+运算符
						// 			type: 'h'
						// 		},
						// 		{
						// 			label: '夏普率（动态rf）',
						// 			typeCate: '4', //4 name+运算符
						// 			type: 'h'
						// 		},
						// 		{
						// 			label: '卡码率',
						// 			typeCate: '4', //4 name+运算符
						// 			type: 'h'
						// 		},
						// 		{
						// 			label: '索提诺系数（rf==0）',
						// 			typeCate: '4', //4 name+运算符
						// 			type: 'h'
						// 		},
						// 		{
						// 			label: '索提诺系数（rf==4%）',
						// 			typeCate: '4', //4 name+运算符
						// 			type: 'h'
						// 		},
						// 		{
						// 			label: '索提诺系数（动态rf）',
						// 			typeCate: '4', //4 name+运算符
						// 			type: 'h'
						// 		},
						// 		{
						// 			label: '稳定系数',
						// 			typeCate: '4', //4 name+运算符
						// 			type: 'h'
						// 		},
						// 		{
						// 			label: '凯利系数',
						// 			typeCate: '4', //4 name+运算符
						// 			type: 'h'
						// 		},
						// 		{
						// 			label: '信息比率',
						// 			typeCate: '4', //4 name+运算符
						// 			type: 'h'
						// 		},
						// 		{
						// 			label: '上攻潜力（周）',
						// 			typeCate: '4', //4 name+运算符
						// 			type: 'h'
						// 		},
						// 		{
						// 			label: '月胜率',
						// 			typeCate: '4', //4 name+运算符
						// 			type: 'h'
						// 		},
						// 		{
						// 			label: '詹森系数',
						// 			typeCate: '4', //4 name+运算符
						// 			type: 'h'
						// 		},
						// 		{
						// 			label: '特诺系数',
						// 			typeCate: '4', //4 name+运算符
						// 			type: 'h'
						// 		},
						// 		{
						// 			label: '上行捕获',
						// 			typeCate: '4', //4 name+运算符
						// 			type: 'h'
						// 		},
						// 		{
						// 			label: '下行捕获',
						// 			typeCate: '4', //4 name+运算符
						// 			type: 'h'
						// 		},
						// 		{
						// 			label: '择时gamma',
						// 			typeCate: '4', //4 name+运算符
						// 			type: 'h'
						// 		},
						// 		{
						// 			label: 'M2',
						// 			typeCate: '4', //4 name+运算符
						// 			type: 'h'
						// 		}
						// 	]
						// },
						// {
						// 	label: '风格偏好',
						// 	children: [
						// 		{
						// 			label: '成长',
						// 			typeCate: '5', //5 特有单选
						// 			type: 'i'
						// 		},
						// 		{
						// 			label: '价值',
						// 			typeCate: '6', //6 特有单选
						// 			type: 'i'
						// 		},
						// 		{
						// 			label: '动量',
						// 			typeCate: '5', //5 特有单选
						// 			type: 'i'
						// 		},
						// 		{
						// 			label: '盈利',
						// 			typeCate: '6', //6 特有单选
						// 			type: 'i'
						// 		},
						// 		{
						// 			label: '贝塔',
						// 			typeCate: '5', //5 特有单选
						// 			type: 'i'
						// 		},
						// 		{
						// 			label: '规模',
						// 			typeCate: '5', //5 特有单选
						// 			type: 'i'
						// 		},
						// 		{
						// 			label: '估值',
						// 			typeCate: '6', //6 特有单选
						// 			type: 'i'
						// 		}
						// 	]
						// },
						// {
						// 	label: '行业判断(全持仓)',
						// 	children: [
						// 		{
						// 			label: '大行业',
						// 			typeCate: '10', //行业
						// 			type: 'j'
						// 		},
						// 		{
						// 			label: '申万一级行业',
						// 			typeCate: '7', //行业
						// 			type: 'j'
						// 		},
						// 		{
						// 			label: '申万二级行业',
						// 			typeCate: '7', //行业
						// 			type: 'j'
						// 		},
						// 		{
						// 			label: '申万三级行业',
						// 			typeCate: '7', //行业
						// 			type: 'j'
						// 		},
						// 		{
						// 			label: '恒生一级行业',
						// 			typeCate: '7', //行业
						// 			type: 'j'
						// 		},
						// 		{
						// 			label: '多行业权重判断',
						// 			typeCate: '7', //行业
						// 			type: 'j'
						// 		}
						// 	]
						// },
						// {
						// 	label: '行业判断(重仓)',
						// 	children: [
						// 		{
						// 			label: '大行业',
						// 			typeCate: '10', //行业
						// 			type: 'j1'
						// 		},
						// 		{
						// 			label: '申万一级行业',
						// 			typeCate: '7', //行业
						// 			type: 'j1'
						// 		},
						// 		{
						// 			label: '申万二级行业',
						// 			typeCate: '7', //行业
						// 			type: 'j1'
						// 		},
						// 		{
						// 			label: '申万三级行业',
						// 			typeCate: '7', //行业
						// 			type: 'j1'
						// 		},
						// 		{
						// 			label: '恒生一级行业',
						// 			typeCate: '7', //行业
						// 			type: 'j1'
						// 		},
						// 		{
						// 			label: '多行业权重判断',
						// 			typeCate: '7', //行业
						// 			type: 'j1'
						// 		}
						// 	]
						// },
						// {
						// 	label: '行业特征',
						// 	children: [
						// 		{
						// 			label: '行业超低配',
						// 			typeCate: '4', //name+运算符
						// 			type: 'f'
						// 		},
						// 		{
						// 			label: '景气行业占比',
						// 			typeCate: '7', //行业
						// 			type: 'f'
						// 		},
						// 		{
						// 			label: '行业轮动',
						// 			typeCate: '7', //行业
						// 			type: 'f'
						// 		}
						// 	]
						// },
						// {
						// 	label: '主题判断(全持仓)',
						// 	typeCate: '7', //行业
						// 	type: 'k'
						// },
						// {
						// 	label: '主题判断(重仓)',
						// 	typeCate: '7', //行业
						// 	type: 'k1'
						// },
						// {
						// 	label: '指数匹配',
						// 	typeCate: '8', //特有
						// 	type: 'l'
						// },
						// {
						// 	label: '其他',
						// 	children: [
						// 		{
						// 			label: '前十大集中度',
						// 			typeCate: '4', //name+运算符
						// 			type: 'm'
						// 		},
						// 		{
						// 			label: 'ROE',
						// 			typeCate: '4', //name+运算符
						// 			type: 'm'
						// 		},
						// 		{
						// 			label: '换手率',
						// 			typeCate: '4', //name+运算符
						// 			type: 'm'
						// 		},
						// 		{
						// 			label: '股票关注期',
						// 			typeCate: '7', //行业
						// 			type: 'm'
						// 		},
						// 		{
						// 			label: '持有股票抱团度',
						// 			typeCate: '7', //行业
						// 			type: 'm'
						// 		},
						// 		{
						// 			label: '胜率',
						// 			typeCate: '7', //行业
						// 			type: 'm'
						// 		},
						// 		{
						// 			label: '赔率',
						// 			typeCate: '7', //行业
						// 			type: 'm'
						// 		},
						// 		{
						// 			label: '买入模式',
						// 			typeCate: '7', //行业
						// 			type: 'm'
						// 		},
						// 		{
						// 			label: '卖出模式',
						// 			typeCate: '7', //行业
						// 			type: 'm'
						// 		}
					]
				}
			],
			activeCondition: [],
			defaultProps: {
				children: 'children',
				label: 'label'
			}
		};
	},
	methods: {
		handleNodeClick(val) {
			let label = this.data.find((item) => {
				return (
					item.children.findIndex((obj) => {
						return obj.value == val.value;
					}) != -1
				);
			})?.label;
			let index = this.activeCondition.findIndex((item) => {
				return item.label == label;
			});
			if (label) {
				if (index == -1) {
					this.activeCondition.push({
						label,
						children: [val]
					});
				} else {
					this.activeCondition[index].children.push(val);
				}
			}

			console.log(this.activeCondition);
		},
		changeRadio() {},
		out() {}
	}
};
</script>

<style></style>
