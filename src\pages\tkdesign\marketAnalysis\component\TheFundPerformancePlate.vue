<template>
  <div class="plate-wrapper fund-performance-wrapper"
       v-loading="loading">
    <VerticalLineHeader title="主动权益基金的分位数业绩"
                        showDownloadBtn
                        @downloadClick="exportExcel">
      <template slot="right">
        <el-form ref="form"
                 :model="form"
                 label-width="80px"
                 class="title-right-form">
          <el-form-item size="small"
                        style="margin-right: 16px"
                        label="截止日期:">
            <el-date-picker value-format="yyyy-MM-dd"
                            type="date"
                            placeholder="选择日期"
                            v-model="deadline"
                            @change="handleSelectHeader"
                            style="width: 100%;"></el-date-picker>
          </el-form-item>
          <div>
            间隔：
            <el-radio-group class="lq-radio-group radio-group-wrapper"
                            @change="handleSelectHeader"
                            v-model="form.interval"
                            size="small">
              <el-radio-button v-for="radioItem in QuantileIntervalOption"
                               :key="radioItem.value"
                               :label="radioItem.value">{{
								radioItem.label
							}}</el-radio-button>
            </el-radio-group>
          </div>
          <!-- <el-radio-group class="lq-radio-group radio-group-wrapper" v-model="form.test1" size="small">
                    <el-radio-button label="left">近期业绩</el-radio-button>
                    <el-radio-button label="right">自然年份业绩</el-radio-button>
                </el-radio-group> -->
          <el-radio-group class="lq-radio-group radio-group-wrapper"
                          v-model="form.dateFlag"
                          @change="handleSelectHeader"
                          size="small">
            <el-radio-button v-for="radioItem in DateTypeOption"
                             :key="radioItem.value"
                             :label="radioItem.value">{{
							radioItem.label
						}}</el-radio-button>
          </el-radio-group>
        </el-form>
      </template>
    </VerticalLineHeader>
    <div class="select-form-wrapper">
      <RadioGroup ref="RadioGroup"
                  class="radio-group-wrapper"
                  :defaultValue="defaultValue"
                  :configList="configList"
                  @change="handleTypeChange"></RadioGroup>
    </div>
    <el-table class="content-table-wrapper"
              style="width: 100%"
              :data="tableData"
              :stripe="true"
              :border="true"
              @sort-change="handeleSortChange">
      <!-- <el-table-column prop="quantile" label="分位数" sortable align="gotoleft" width="180"> </el-table-column> -->
      <template v-for="item in tableHeader">
        <!-- <el-table-column
            :key="item.prop"
            v-if="item.prop==='customTime'"
            prop="customTime"
            label="区间收益"
            sortable="custom"
            align="gotoleft">
            <template slot="header" slot-scope="scope">
                区间收益
            <DatePickerBtn @change="handleDateChange"></DatePickerBtn>
            </template>
            </el-table-column> -->
        <el-table-column :prop="item.prop"
                         min-width="120"
                         :label="item.label"
                         :key="item.prop"
                         :sortable="item.sortable !== false ? 'custom' : false"
                         align="gotoleft">
          <template slot-scope="{ row }">
            <div>{{ item.format ? item.format(row[item.prop]) : row[item.prop] }}</div>
          </template>
          <template slot="header"
                    v-if="item.prop === 'customTime'">
            区间收益
            <DatePickerBtn @click.native.stop
                           @change="handleDateChange"></DatePickerBtn>
          </template>
        </el-table-column>
      </template>
    </el-table>
  </div>
</template>
<script>
import VerticalLineHeader from './VerticalLineHeader.vue';
import RadioGroup from './RadioGroup.vue';
import { getQuantilePerformance, PerformanceType, getFundCode } from '@/api/pages/tkAnalysis/captial-market.js';
import DatePickerBtn from './DatePickerBtn.vue';
import stringTool from '../../components/string.tool';
import { filter_json_to_excel } from '@/utils/exportExcel.js';
export default {
  name: 'TheFundPerformancePlate',
  components: {
    VerticalLineHeader,
    RadioGroup,
    DatePickerBtn
  },
  data () {
    let defaultTypeValue = 'industry';
    return {
      deadline: '',
      loading: false,
      form: {
        interval: '5',
        dateFlag: '0',
        type: defaultTypeValue,
        subType: []
      },
      IndexStyleOption: [],
      tableData: [],
      defaultValue: {
        radioValue: defaultTypeValue
        // selectValue:{name:'动态市盈率',value:'pe'}
      },
      QuantileIntervalOption: [
        { label: '5%', value: '5' },
        { label: '10%', value: '10' },
        { label: '25%', value: '25' }
      ],
      DateTypeOption: [
        { label: '近期业绩', value: '0' },
        { label: '自然年份业绩', value: '1' }
      ],
      tableHeader1: [
        {
          prop: 'yearToDate',
          label: '年初至今',
          format: stringTool.fix2px
        },
        {
          prop: 'lastWeek',
          label: '近一周',
          format: stringTool.fix2px
        },
        {
          prop: 'lastMounth',
          label: '近一月',
          format: stringTool.fix2px
        },
        {
          prop: 'lastSeason',
          label: '近一季',
          format: stringTool.fix2px
        },
        {
          prop: 'lastHalfYears',
          label: '近半年',
          format: stringTool.fix2px
        },
        {
          prop: 'lastYear',
          label: '近一年',
          format: stringTool.fix2px
        },
        {
          prop: 'lastThreeYear',
          label: '近三年',
          format: stringTool.fix2px
        },
        {
          prop: 'lastFiveYear',
          label: '近五年',
          format: stringTool.fix2px
        },
        {
          prop: 'customTime',
          label: '区间收益',
          format: stringTool.fix2px
        }
      ],
      tableHeader3: [],
      configList: [
        { type: 'select', value: '', label: 'type', text: '类型', option: [{ label: '全部类型', value: [] }] },
        { type: 'select', value: '', label: 'industry', text: '行业', option: [{ label: '全部行业', value: [] }] },
        { type: 'select', value: '', label: 'theme', text: '主题', option: [{ label: '全部主题', value: [] }] },
        { type: 'select', value: '', label: 'optionalPool', text: '自选池', option: [{ label: '全部自选池', value: [] }] },
        { type: 'select', value: '', label: 'taikang', text: '泰康分类', option: [{ label: '全部泰康分类', value: [] }] },
        { type: 'select', value: '', label: 'style', text: '风格', option: [{ label: '全部风格', value: [] }] }
      ]
    };
  },
  computed: {
    tableHeader () {
      let result = [];
      let commmon = {
        prop: 'quantile',
        label: '分位数',
        sortable: false
      };
      result.push(commmon);
      //为自然年份业绩
      if (this.form.dateFlag === '1') {
        result.push(...this.tableHeader3);
        return result;
      }
      result.push(...this.tableHeader1);
      return result;
    }
  },
  created () {
    this.getFundCode();
  },
  mounted () {
    // this.$set(this.form, 'deadline', this.moment().subtract(1, 'day').format('YYYY-MM-DD'))
    this.deadline = this.moment().subtract(1, 'day').format('YYYY-MM-DD');
    if (this.localStorage.getItem('TheFundPerformancePlate')) {
      let key_list = ['form', 'defaultValue'];
      for (let key of key_list) {
        this[key] = this.localStorage.getItem('TheFundPerformancePlate')?.[key] || this[key];
      }
      let index = this.configList.findIndex((v) => v.label == this.defaultValue.radioValue);
      this.$set(this.configList, index, { ...this.configList[index], value: this.defaultValue.selectValue });
      this.$refs['RadioGroup'].setValue(this.defaultValue);
    }
    this.getData();
  },

  methods: {
    handeleSortChange ({ column, prop, order }) {
      this.tableData.sort((item1, item2) => {
        const a1 = item1[prop] || 0;
        const a2 = item2[prop] || 0;
        let orderVal = order === 'ascending' ? -(a1 - a2) : a1 - a2;
        return orderVal;
      });
    },
    // 导出excel
    exportExcel () {
      let list = this.tableHeader.map((item) => {
        return {
          ...item,
          value: item.prop,
          format: ''
        };
      });
      filter_json_to_excel(list, this.tableData, '主动权益基金的分位数业绩');
    },
    // 获取列表数据
    async getData () {
      this.loading = true;
      let params = {
        ...this.form,
        deadline: this.deadline,
        // pageSize: this.pageInfo.pageSize,
        // currentPage: this.pageInfo.currentPage,
        marketType: PerformanceType.FundQuantile.value,
      };
      this.localStorage.setItem('TheFundPerformancePlate', { form: this.form, defaultValue: this.defaultValue });
      let req = await getQuantilePerformance(params);
      let { data, code, message } = req || {};
      if (code == 200) {
        // this.pageInfo.total = data?.total || 0
        // this.pageInfo.currentPage = data?.currentPage || 1
        // this.pageInfo.pageSize = data?.pageSize || 20
        this.tableData = data || [];
        let { naturalList = [] } = data[0] || {};
        if (naturalList) {
          this.tableHeader3 = naturalList
            .map((item) => {
              return {
                prop: item.naturalDate,
                label: item.naturalDate,
                format: stringTool.fix2px
              };
            })
            ?.sort((a, b) => {
              if (a?.prop < b?.prop) return 1;
              else return -1;
            });
          this.tableData = this.tableData.map((item) => {
            let obj = { ...item };
            item?.naturalList?.map((v) => {
              obj[v.naturalDate] = v.meter;
            });
            return obj;
          });
        }
      } else {
        this.$message.warning(message);
        this.tableData = [];
        // this.pageInfo={
        //     total:0,
        //     currentPage:1,
        //     pageSize:20
        // }
      }
      this.loading = false;
    },
    handleSelectHeader () {
      // this.pageInfo.currentPage=1;
      //接口调用
      this.getData();
    },
    handleTypeChange (value) {
      //重新设置chart
      this.defaultValue = value;
      this.form.type = value.radioValue;
      this.form.subType = [value?.selectValue?.value || ''];
      this.getData();
    },
    handleDateChange (value) {
      //自定义区间收益，只影响自定义列表
      this.form['startDate'] = value[0];
      this.form['endDate'] = value[1];
      this.getData();
    },
    async getFundCode () {
      let params = {
        deadline: ''
      };
      let req = await getFundCode(params);
      let { data, code, message } = req || {};
      if (code == 200) {
        // {type:'select',value:'',label:'type',text:'类型',option:[{label:'类型'}]},
        // {type:'select',value:'',label:'industry',text:'行业',option:[{label:'行业'}]},
        // {type:'select',value:'',label:'theme',text:'主题',option:[{label:'主题'}]},
        // {type:'select',value:'',label:'pool',text:'自选池',option:[{label:'自选池'}]},
        // {type:'select',value:'',label:'taikang',text:'泰康分类',option:[{label:'泰康分类'}]},
        // {type:'select',value:'',label:'style',text:'风格',option:[{label:'风格'}]}],
        this.configList = this.configList.map((item) => {
          let dataList = data[item.label + 'List'] || [];
          let curOption = [];
          if (item.label == 'optionalPool') {
            curOption = dataList.map((item) => {
              return {
                label: item.name,
                value: { name: item.name, value: item.id }
              };
            });
          } else {
            curOption = this.dulConfigOption(dataList);
          }
          item.option.push(...curOption);
          return item;
        });
      } else {
      }
    },
    dulConfigOption (dataList) {
      // {label:'动态市盈率',value:{name:'动态市盈率',value:'pe'}},
      // {label:'静态市盈率',value:{name:'静态市盈率',value:'staticState_pe'}},
      // {label:'滚动市盈率',value:{name:'滚动市盈率',value:'trends_pe'}},
      return dataList.map((item) => {
        return {
          label: item,
          value: { name: item, value: item }
        };
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.fund-performance-wrapper {
	padding-bottom: 20px;
	.select-form-wrapper {
		margin-bottom: 16px;
	}
}
</style>
