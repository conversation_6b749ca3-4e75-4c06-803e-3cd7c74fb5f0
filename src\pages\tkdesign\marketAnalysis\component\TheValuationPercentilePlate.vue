<template>
	<div class="plate-wrapper valuation-percentile-wrapper" v-loading="loading">
		<VerticalLineHeader title="估值分位" showDownloadBtn @downloadClick="exportExcel">
			<template slot="right">
				<el-form ref="form" :model="filterForm" label-width="auto" class="title-right-form">
					<el-form-item>
						<QuickTimePicker class="radio-group-wrapper" :list="timeList" v-model="dateSelect" @change="handleFormChange"></QuickTimePicker>
					</el-form-item>
					<el-form-item style="margin-left: 12px; width: 160px" size="small" label="滚动窗口:">
						<el-select v-model="filterForm.scrollWindow" placeholder="请选择" @change="handleFormChange">
							<el-option v-for="item in ScrollWindowOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item style="margin-left: 12px; width: 160px" size="small" label="滚动频率:">
						<el-select v-model="filterForm.scrollFrequency" placeholder="请选择" @change="handleFormChange">
							<el-option v-for="item in ScrollFrequencyOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item style="margin-left: 12px" size="small" label="指数风格:">
						<!-- <el-select v-model="filterForm.indexStyle" placeholder="请选择" @change="handleIndexChange">
                    <el-option v-for="item in IndexStyleOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>-->
						<el-cascader v-model="codeList" :options="IndexTypeOption" @change="handleIndexChange" placeholder="请选择"></el-cascader>
					</el-form-item>
					<RadioGroup
						ref="RadioGroup"
						class="radio-group-wrapper"
						:defaultValue="defaultValue"
						:configList="configList"
						@change="handleTypeChange"
					></RadioGroup>
				</el-form>
			</template>
		</VerticalLineHeader>
		<div class="checkbox-wrapper">
			<el-checkbox-group v-model="checkList">
				<el-checkbox v-for="checkItem in CheckBoxOption" @change="checkItem.changeMethod" :key="checkItem.value" :label="checkItem.value">{{
					checkItem.label
				}}</el-checkbox>
			</el-checkbox-group>
			<div class="score-item">z-score：{{ zScore }}</div>
		</div>
		<ValuationPercentileChart
			v-show="!showEmpty"
			@legendselectchanged="legendselectchanged"
			ref="valuation-percentile-chart"
		></ValuationPercentileChart>
		<el-empty v-show="showEmpty" :image-size="160"></el-empty>
	</div>
</template>
<script>
import { filter_json_to_excel } from '@/utils/exportExcel.js';
import TkChart from './TkChart.vue';
import VerticalLineHeader from './VerticalLineHeader.vue';
import QuickTimePicker from './QuickTimePicker.vue';
import RadioGroup from './RadioGroup.vue';
import ValuationPercentileChart from './chart/ValuationPercentileChart.vue';
import { getValuationPercentileList, getIndexCode } from '@/api/pages/tkAnalysis/captial-market.js';
export default {
	name: 'TheValuationPercentilePlate',
	components: {
		VerticalLineHeader,
		QuickTimePicker,
		RadioGroup,
		TkChart,
		ValuationPercentileChart
	},
	data() {
		let TTMOption = [
			{ label: '动态市盈率', value: { name: '动态市盈率', value: 'trends_pe' } },
			{
				label: '静态市盈率',
				value: { name: '静态市盈率', value: 'staticState_pe' }
			},
			{ label: '滚动市盈率', value: { name: '滚动市盈率', value: 'pe' } }
		];
		let PBROption = [
			{ label: '市净率', value: { name: '市净率', value: 'pb' } },
			{
				label: '加权市净率',
				value: { name: '加权市净率', value: 'weighted_pb' }
			}
		];
		let defaultMeasure = {
			radioValue: 'ttm',
			selectValue: { name: '动态市盈率', value: 'trends_pe' }
		};
		return {
			zScore: 0,
			showEmpty: false,
			codeList: [],
			filterForm: {
				scrollFrequency: 'day',
				scrollWindow: '1',
				// show_quantile:'',
				// show_regression_line:'',
				excludeNegativeCul: 'false',
				meanRollingCul: 'false',
				measure: defaultMeasure.selectValue.value
			},
			index_indicators: defaultMeasure.selectValue,
			checkList: [],
			ScrollWindowOption: [
				{ label: '1年', value: '1' },
				{ label: '3年', value: '3' }
			],
			ScrollFrequencyOption: [
				{ label: '天', value: 'day' },
				{ label: '周', value: 'week' },
				{ label: '月', value: 'month' }
			],
			timeList: [
				{ label: '3Y', value: '3' },
				{ label: '5Y', value: '5' },
				{ label: '7Y', value: '7' }
			],
			// IndexStyleOption:[
			//     {label:'规模指数',value:'001'},
			//     {label:'风格指数',value:'002'},
			//     {label:'行业指数',value:'003'},
			//     {label:'泰康自定义行业指数',value:'004'},
			// ],
			IndexTypeOption: null,
			defaultValue: defaultMeasure,
			configList: [
				{
					type: 'select',
					value: TTMOption[0].value,
					label: 'ttm',
					text: '市盈率TTM',
					option: TTMOption
				},
				{
					type: 'select',
					value: PBROption[0].value,
					label: 'pb',
					text: '市净率',
					option: PBROption
				},
				{ label: 'peg', text: 'PEG', value: { name: 'PEG', value: 'peg' } }
			],
			legendName: {
				indicatorPoints: '指标点位',
				ttm: 'TTM',
				dividedIntoPoints: '分位点',
				positiveStandardDeviation: '标准差（+1）',
				negativeStandardDeviation: '标准差（-1）',
				average: '平均值'
			},
			chartOption: {},
			chartData: [],
			legendSelect: '',
			point: {},
			quantile: {},
			deviceValue: false,
			selectedOption: {
				culType: ''
			},
			loading: false,
			dateSelect: { radioValue: '3' }
		};
	},
	created() {},
	mounted() {
		this.$nextTick(() => {
			if (this.localStorage.getItem('TheValuationPercentilePlate')) {
				let key_list = ['filterForm', 'codeList', 'dateSelect', 'defaultValue'];
				for (let key of key_list) {
					this[key] = this.localStorage.getItem('TheValuationPercentilePlate')?.[key] || this[key];
				}
				let index = this.configList.findIndex((v) => v.label == this.defaultValue.radioValue);
				this.$set(this.configList, index, { ...this.configList[index], value: this.defaultValue.selectValue });
				this.$refs['RadioGroup'].setValue(this.defaultValue);
			}
			this.getData();
		});
	},
	computed: {
		CheckBoxOption() {
			return [
				{
					label: '剔除负值计算',
					value: 'excludeNegativeCul',
					changeMethod: this.handleCheckBoxChange
				},
				{
					label: '均值滚动计算',
					value: 'meanRollingCul',
					changeMethod: this.handleCheckBoxChange
				}
			];
		}
	},
	watch: {
		deviceValue() {
			this.updateChart();
		}
	},
	methods: {
		exportExcel() {
			this.$refs['valuation-percentile-chart']?.exportExcel();
		},
		// 格式化数据
		fix2(val) {
			return val * 1 && !isNaN(val) ? (val * 1).toFixed(2) : '--';
		},
		fix2p(val) {
			return val * 1 && !isNaN(val) ? (val * 100).toFixed(2) : '--';
		},
		async getIndexOptions() {
			if (this.IndexTypeOption) {
				return;
			}
			let params = {};
			let reqData = await getIndexCode(params);
			let { data = [], code, message } = reqData || {};
			if (code == 200) {
				this.IndexTypeOption = data.map((item) => {
					let children = item?.dataList?.map((childrenItem) => {
						return {
							...childrenItem,
							label: childrenItem.name,
							value: childrenItem.code
						};
					});
					return {
						...item,
						label: item.indexName,
						value: item.indexType,
						children: children
					};
				});
				let firstList = this.IndexTypeOption[0];
				if (this.codeList.length == 0) {
					this.codeList = [firstList.value, firstList?.children[0]?.value];
				}
			} else {
				this.$message.warning(message);
			}
		},
		handleIndexChange() {
			this.getData();
		},
		handleFormChange() {
			this.getData();
		},
		handleTypeChange(value) {
			this.index_indicators = {
				name: value?.selectValue?.name,
				value: value?.selectValue?.value
			};
			this.defaultValue = value;
			this.filterForm['measure'] = this.index_indicators?.value;
			// this.updateChart()
			this.getData();
		},
		handleCheckBoxChange() {
			this.filterForm['excludeNegativeCul'] = this.checkList.includes('excludeNegativeCul');
			this.filterForm['meanRollingCul'] = this.checkList.includes('meanRollingCul');
			this.getData();
		},
		updateChart() {
			this.updateZScore();
			let chartDataNew = this.chartData.map((item) => {
				let option = this.deviceValue ? item.pointValue : item.quantileValue;
				return {
					...item,
					...option
				};
			});
			this.$nextTick(() => {
				this.$refs['valuation-percentile-chart'].getData(chartDataNew, {
					typeLegendOption: this.index_indicators
				});
			});
		},
		legendselectchanged(value) {
			this.deviceValue = value.selected.分位点;
		},
		// 获取列表数据
		async getData() {
			this.loading = true;
			this.showEmpty = false;
			let year = this.dateSelect?.radioValue === 'custom' ? '' : this.dateSelect?.radioValue;
			let { startDate, endDate } = this.dateSelect || {};
			await this.getIndexOptions();
			let params = {
				...this.filterForm,
				code: this.codeList[this.codeList.length - 1],
				year,
				startDate,
				endDate
			};
			this.localStorage.setItem('TheValuationPercentilePlate', {
				filterForm: this.filterForm,
				codeList: this.codeList,
				dateSelect: this.dateSelect,
				defaultValue: this.defaultValue
			});
			this.chartData = [];
			let { data, code, message } = await getValuationPercentileList(params);
			if (code == 200) {
				let { point, dataList, quantile } = data || {};
				this.point = point || {};
				this.quantile = quantile || {};
				this.chartData = dataList || [];
			} else {
				this.showEmpty = true;
				this.$message.warning(message);
			}
			this.loading = false;
			this.updateChart();
		},
		// 更新z-score
		updateZScore() {
			let key = this.deviceValue ? 'pointValue' : 'quantileValue';
			this.zScore = this.fix2(this.chartData.length > 0 ? this.chartData[this.chartData.length - 1][key].zScore : '--');
		}
	}
};
</script>
<style lang="scss" scoped>
.valuation-percentile-wrapper {
	padding-bottom: 20px;
	.checkbox-wrapper {
		display: flex;
		justify-content: space-between;
		.score-item {
			border-radius: 4px;
			border: 1px solid #d9d9d9;
			background: #f5f5f5;
			padding: 5px 16px;
			line-height: 1;
			color: #000;
			text-align: center;
			font-size: 14px;
			font-style: normal;
			font-weight: 400;
		}
		padding-bottom: 16px;
	}
	.radio-group-wrapper {
		::v-deep .is-active {
			background-color: #4096ff;
			color: #ffffff !important;
			.el-input__inner {
				background-color: #4096ff;
				color: #ffffff !important;
			}
		}
	}
}

.title-right-form {
	display: flex;
}
#chart-container {
	position: relative;
	height: 334px;
	overflow: hidden;
}
</style>
