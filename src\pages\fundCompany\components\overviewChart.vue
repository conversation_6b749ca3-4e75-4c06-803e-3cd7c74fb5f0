<template>
	<div class="overview-chart" v-loading="chartLoading">
		<div class="invest-tab">
			<div
				v-for="(type, i) in typeList"
				:key="i"
				class="tab-btn"
				:class="{ 'active-invest': investType == type.type }"
				@click="changeInvestType(type.type)"
			>
				{{ type.name }}
			</div>
			<!-- <div class="tab-btn" :class="{ 'active-invest': investType == 'bond' }" @click="changeInvestType('bond')">债 券</div>
			<div class="tab-btn" :class="{ 'active-invest': investType == 'moner' }" @click="changeInvestType('money')">货 币</div> -->
		</div>
		<div class="invest-chart">
			<!-- echart -->
			<div v-show="showChart" id="main" class="main"></div>
			<div v-show="!showChart" class="no-data main">
				<el-empty description="暂无数据"></el-empty>
			</div>
			<!-- <div id="main" style="width: 800px; height: 240px"></div> -->
			<!-- 选择list -->
			<!-- (样式与基金页面折线图下方四个比较指标，但目前无该需求) -->
			<!-- <div class="select-list">
				<div class="select-item" @click="changeSelect('fund_ret')">
					<span class="color-line" :style="{ color: colorCard.fund_ret, display: showFlagList.fund_ret ? 'inline' : 'none' }">—— </span>
					<span>{{ name }}</span>
				</div>
				<div class="select-item" @click="changeSelect('avg_ret')">
					<span class="color-line" :style="{ color: colorCard.avg_ret, display: showFlagList.avg_ret ? 'inline' : 'none' }">—— </span>
					<span>同类平均</span>
				</div>
				<div class="select-item" @click="changeSelect('index_flag1')">
					<span class="color-line" :style="{ color: colorCard.index_flag1, display: showFlagList.index_flag1 ? 'inline' : 'none' }"
						>——
					</span>
					<div class="select-box">
						<el-select v-model="index_flag1" placeholder="请选择">
							<el-option
								v-for="item in indexList"
								:key="item.value"
								:label="item.label"
								:value="item.value"
								:disabled="index_flag2 && item.value == index_flag2"
							>
							</el-option>
						</el-select>
					</div>
				</div>
				<div class="select-item" @click="changeSelect('index_flag2')">
					<span class="color-line" :style="{ color: colorCard.index_flag2, display: showFlagList.index_flag2 ? 'inline' : 'none' }"
						>——
					</span>
					<div class="select-box">
						<el-select v-model="index_flag2" placeholder="请选择">
							<el-option
								v-for="item in indexList"
								:key="item.value"
								:label="item.label"
								:value="item.value"
								:disabled="index_flag1 && item.value == index_flag1"
							>
							</el-option>
						</el-select>
					</div>
				</div>
			</div> -->
		</div>
	</div>
</template>

<script>
// 基金收益率
import { getReturnWithAsset } from '@/api/pages/SystemOther.js';
export default {
	data() {
		return {
			// 权益-equity; 债券-bond; 货币-cash;
			investType: 'equity',
			chartLoading: true,
			showChart: true,
			colorCard: {
				bond_ret: '#4096FF',
				avg_ret: '#929694',
				index_flag1: '#40BFDD',
				index_flag2: '#C2B12F'
			},
			chartColor: [],
			typeList: [],
			info: {}
		};
	},
	methods: {
		// 获取类型列表
		getTypeList(info) {
			this.info = info;
			this.typeList = info?.type.map((item) => {
				return {
					type: item,
					name: this.FUNC.textConverter(this.COMMON.fundType_zh_en, item, 'en', 'zh')
				};
			});

			this.investType = this.typeList?.[0]?.type;
			this.getData();
		},
		// 获取收益率数据
		async getData() {
			this.chartLoading = true;
			let data = await getReturnWithAsset({ code: this.info.code, type: this.investType });
			if (data?.mtycode == 200) {
				this.formatChart(data);
				this.chartLoading = false;
			}
		},
		// 选择数据类型
		changeInvestType(type) {
			if (!type) return;
			this.investType = type;
			this.getData();
		},
		// 格式化图数据
		formatChart(data) {
			let resReturn = data.return; // cum_return 累计收益 每日
			let resNetasset = data.netasset; // netasset 规模 季度
			let dateDayList = []; // 每日日期
			let dateQuarterList = []; // 季度日期
			let returnList = []; // 每日值-累计收益
			let netassetList = []; // 季度值-规模

			// 最早&最晚 季度&每日
			let earlyQ =
				resReturn && resNetasset
					? resReturn.yearqtr[0] < resNetasset.yearqtr[0]
						? resReturn.yearqtr[0]
						: resNetasset.yearqtr[0]
					: resReturn
					? resReturn.yearqtr[0]
					: resNetasset.yearqtr[0];
			let lastQ =
				resReturn && resNetasset
					? resReturn.yearqtr[resReturn.yearqtr.length - 1] > resNetasset.yearqtr[resNetasset.yearqtr.length - 1]
						? resReturn.yearqtr[resReturn.yearqtr.length - 1]
						: resNetasset.yearqtr[resNetasset.yearqtr.length - 1]
					: resReturn
					? resReturn.yearqtr[resReturn.yearqtr.length - 1]
					: resNetasset.yearqtr[resNetasset.yearqtr.length - 1];
			let earlyD = this.FUNC.returnQuarter(earlyQ, 'start', earlyQ.indexOf('Q') == -1 ? 'date' : 'quarter');
			let lastD = this.FUNC.returnQuarter(lastQ, 'end', lastQ.indexOf('Q') == -1 ? 'date' : 'quarter');

			// 连续日期 季度&每日
			dateDayList = this.FUNC.generateDateList(earlyD, lastD);
			dateQuarterList = this.FUNC.dateGenerateQuarterList(earlyD, lastD);

			// 生成季度规模值 netassetList
			dateQuarterList.forEach((item) => {
				if (resNetasset) {
					if (resNetasset.yearqtr.includes(item)) {
						let index = resNetasset.yearqtr.indexOf(item);
						let value = resNetasset.sum_netasset[index];
						netassetList.push(value / 10 ** 8); // 元换算成亿
					} else {
						netassetList.push(null);
					}
				}
			});

			// 生成每日累计收益值 cum_return
			let date = resReturn ? resReturn.date.slice() : [];
			let cum_return = resReturn ? resReturn.cum_return.slice() : [];
			let preRet = null;
			let curDate = date.shift();
			let curRet = cum_return.shift();

			dateDayList.forEach((item) => {
				if (resReturn) {
					if (item == curDate) {
						returnList.push((curRet * 100).toFixed(2));
						preRet = curRet;
						curDate = date.shift();
						curRet = cum_return.shift();
					} else if (item < curDate) {
						returnList.push((preRet * 100).toFixed(2));
					} else if (item > resReturn.date[resReturn.date.length - 1]) {
						returnList.push(null);
					} else {
						returnList.push((curRet * 100).toFixed(2));
						preRet = curRet;
						curDate = date.shift();
						curRet = cum_return.shift();
					}
				}
			});

			let myChart = echarts.init(document.getElementById('main'));
			let option = {
				color: ['#409eff', '#4096ff'],
				tooltip: {
					textStyle: {
						fontSize: '14px'
					},
					trigger: 'axis',
					formatter: (params) => {
						let str = `日期: ${params[1] ? params[1].axisValue : ''} 季度: ${params[0] ? params[0].axisValue : ''} <br />`;
						for (let i = params.length - 1; i >= 0; i--) {
							if (params[i].value != undefined && params[i].value != null && params[i].value != '') {
								let dotHtml =
									'<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' +
									params[i].color +
									'"></span>';
								str += dotHtml + `${params[i].seriesName}: `;
								let value;
								if (params[i].seriesName == '规模') {
									str += `${parseFloat(params[i].value.toFixed(2)) + '亿'}<br />`;
								} else {
									str += `${parseFloat(params[i].value).toFixed(2) + '%'}<br />`;
								}
							}
						}
						return str;
					}
				},
				legend: {
					textStyle: {
						fontSize: '14px'
					}
					// data: ['累计收益', '规模']
				},
				grid: {
					left: '60px',
					right: '60px',
					bottom: '30px',
					top: '30px'
				},
				xAxis: [
					{
						data: dateQuarterList,
						silent: false,
						splitLine: {
							show: false
						},
						splitArea: {
							show: false
						},
						axisLabel: {
							fontSize: '14px'
						}
					},

					{
						//每日
						nameTextStyle: {
							fontSize: '14px'
						},
						axisLine: {
							show: false
						},
						axisTick: {
							show: false
						},
						axisLabel: {
							show: false,
							textStyle: {
								fontSize: '10px'
							}
							// interval: 0,
							// rotate: 30
							// formatter: function (value) {
							// 	if (quarterEndList.includes(value)) {
							// 		return value;
							// 	} else {
							// 		return ' ';
							// 	}
							// }
						},
						data: dateDayList,
						type: 'category'
					}
				],
				yAxis: [
					{
						// 该项在相应季度的数值
						nameTextStyle: {
							fontSize: '14px'
						},
						axisLabel: {
							show: true,
							textStyle: {
								fontSize: '14px'
							}
						},
						name: '管理规模(亿)',
						type: 'value',
						// min: 0
						scale: true,
						splitLine: {
							show: true,
							lineStyle: {
								type: 'dashed'
							}
						}
					},
					{
						//该项在相应每日的数值
						nameTextStyle: {
							fontSize: '14px'
						},
						axisLabel: {
							show: true,
							textStyle: {
								fontSize: '14px'
							}
						},
						name: '累计收益(%)',
						type: 'value',
						scale: true,
						splitLine: {
							show: false
						}
					}
				],
				series: [
					{
						name: '规模',
						type: 'bar',
						symbol: 'none',
						// barCategoryGap: '0%',
						barWidth: '60%',
						data: netassetList,
						yAxisIndex: 0,
						xAxisIndex: 0
					},
					{
						name: '累计收益',
						type: 'line',
						symbol: 'none',
						data: returnList,
						yAxisIndex: 1,
						xAxisIndex: 1
					}
				]
			};
			myChart.setOption(option, true);
			console.log(option);
			this.showChart = true;
		},
		generateChart() {
			this.chartLoading = true;
			let params = {
				code: this.code,
				type: this.investType
			};
			let url = this.$baseUrl + '/Company/ReturnWithAsset/?' + this.FUNC.paramsToString(params);
			axios
				.get(url)
				.then((res) => {
					if (res.status == 200) {
						console.log(res.data);
						let resReturn = res.data.return; // cum_return 累计收益 每日
						let resNetasset = res.data.netasset; // netasset 规模 季度

						let dateDayList = []; // 每日日期
						let dateQuarterList = []; // 季度日期
						let returnList = []; // 每日值-累计收益
						let netassetList = []; // 季度值-规模

						// 最早&最晚 季度&每日
						let earlyQ =
							resReturn && resNetasset
								? resReturn.yearqtr[0] < resNetasset.yearqtr[0]
									? resReturn.yearqtr[0]
									: resNetasset.yearqtr[0]
								: resReturn
								? resReturn.yearqtr[0]
								: resNetasset.yearqtr[0];
						let lastQ =
							resReturn && resNetasset
								? resReturn.yearqtr[resReturn.yearqtr.length - 1] > resNetasset.yearqtr[resNetasset.yearqtr.length - 1]
									? resReturn.yearqtr[resReturn.yearqtr.length - 1]
									: resNetasset.yearqtr[resNetasset.yearqtr.length - 1]
								: resReturn
								? resReturn.yearqtr[resReturn.yearqtr.length - 1]
								: resNetasset.yearqtr[resNetasset.yearqtr.length - 1];
						let earlyD = this.FUNC.returnQuarter(earlyQ, 'start', earlyQ.indexOf('Q') == -1 ? 'date' : 'quarter');
						let lastD = this.FUNC.returnQuarter(lastQ, 'end', lastQ.indexOf('Q') == -1 ? 'date' : 'quarter');

						// 连续日期 季度&每日
						dateDayList = this.FUNC.generateDateList(earlyD, lastD);
						dateQuarterList = this.FUNC.dateGenerateQuarterList(earlyD, lastD);

						// 生成季度规模值 netassetList
						dateQuarterList.forEach((item) => {
							if (resNetasset) {
								if (resNetasset.yearqtr.includes(item)) {
									let index = resNetasset.yearqtr.indexOf(item);
									let value = resNetasset.sum_netasset[index];
									netassetList.push(value / 10 ** 8); // 元换算成亿
								} else {
									netassetList.push(null);
								}
							}
						});

						// 生成每日累计收益值 cum_return
						let date = resReturn ? resReturn.date.slice() : [];
						let cum_return = resReturn ? resReturn.cum_return.slice() : [];
						let preRet = null;
						let curDate = date.shift();
						let curRet = cum_return.shift();

						dateDayList.forEach((item) => {
							if (resReturn) {
								if (item == curDate) {
									returnList.push((curRet * 100).toFixed(2));
									preRet = curRet;
									curDate = date.shift();
									curRet = cum_return.shift();
								} else if (item < curDate) {
									returnList.push((preRet * 100).toFixed(2));
								} else if (item > resReturn.date[resReturn.date.length - 1]) {
									returnList.push(null);
								} else {
									returnList.push((curRet * 100).toFixed(2));
									preRet = curRet;
									curDate = date.shift();
									curRet = cum_return.shift();
								}
							}
						});

						let myChart = echarts.init(document.getElementById('main'));
						let option = {
							color: ['#409eff', '#4096ff'],
							tooltip: {
								textStyle: {
									fontSize: '14px'
								},
								trigger: 'axis',
								formatter: (params) => {
									let str = `日期: ${params[1] ? params[1].axisValue : ''} 季度: ${params[0] ? params[0].axisValue : ''} <br />`;
									for (let i = params.length - 1; i >= 0; i--) {
										if (params[i].value != undefined && params[i].value != null && params[i].value != '') {
											let dotHtml =
												'<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' +
												params[i].color +
												'"></span>';
											str += dotHtml + `${params[i].seriesName}: `;
											let value;
											if (params[i].seriesName == '规模') {
												str += `${parseFloat(params[i].value.toFixed(2)) + '亿'}<br />`;
											} else {
												str += `${parseFloat(params[i].value).toFixed(2) + '%'}<br />`;
											}
										}
									}
									return str;
								}
							},
							legend: {
								textStyle: {
									fontSize: '14px'
								}
								// data: ['累计收益', '规模']
							},
							grid: {
								left: '60px',
								right: '60px',
								bottom: '30px',
								top: '30px'
							},
							xAxis: [
								{
									data: dateQuarterList,
									silent: false,
									splitLine: {
										show: false
									},
									splitArea: {
										show: false
									},
									axisLabel: {
										fontSize: '14px'
									}
								},

								{
									//每日
									nameTextStyle: {
										fontSize: '14px'
									},
									axisTick: {
										show: false
									},
									axisLabel: {
										show: false,
										textStyle: {
											fontSize: '10px'
										}
										// interval: 0,
										// rotate: 30
										// formatter: function (value) {
										// 	if (quarterEndList.includes(value)) {
										// 		return value;
										// 	} else {
										// 		return ' ';
										// 	}
										// }
									},
									data: dateDayList,
									type: 'category'
								}
							],
							yAxis: [
								{
									// 该项在相应季度的数值
									nameTextStyle: {
										fontSize: '14px'
									},
									axisLabel: {
										show: true,
										textStyle: {
											fontSize: '14px'
										}
									},
									name: '管理规模(亿)',
									type: 'value',
									// min: 0
									scale: true,
									splitLine: {
										show: true,
										lineStyle: {
											type: 'dashed'
										}
									}
								},
								{
									//该项在相应每日的数值
									nameTextStyle: {
										fontSize: '14px'
									},
									axisLabel: {
										show: true,
										textStyle: {
											fontSize: '14px'
										}
									},
									name: '累计收益(%)',
									type: 'value',
									scale: true,
									splitLine: {
										show: false
									}
								}
							],
							series: [
								{
									name: '规模',
									type: 'bar',
									symbol: 'none',
									// barCategoryGap: '0%',
									barWidth: '60%',
									data: netassetList,
									yAxisIndex: 0,
									xAxisIndex: 0
								},
								{
									name: '累计收益',
									type: 'line',
									symbol: 'none',
									data: returnList,
									yAxisIndex: 1,
									xAxisIndex: 1
								}
							]
						};
						myChart.setOption(option, true);
						this.showChart = true;
					} else {
						console.error('error: ', res);
						this.showChart = false;
					}
					this.chartLoading = false;
				})
				.catch((error) => {
					this.chartLoading = false;
					this.showChart = false;
					console.error('error: ', error);
				});
		},
		generateDynamicChart() {
			let mockDate = this.FUNC.generateDateList('2021-06-01', '2021-07-01');
			let myChart = echarts.init(document.getElementById('main'));
			let seriesList = [];

			if (this.showFlagList.fund_ret) {
				seriesList.push({
					name: this.name,
					type: 'line',
					symbol: 'none',
					data: this.FUNC.mockDataList(30, 10)
				});
				this.chartColor.push(this.colorCard.fund_ret);
			}

			if (this.showFlagList.avg_ret) {
				seriesList.push({
					name: '同类平均',
					type: 'line',
					symbol: 'none',
					data: this.FUNC.mockDataList(30, 10)
				});
				this.chartColor.push(this.colorCard.avg_ret);
			}

			if (this.showFlagList.index_flag1) {
				seriesList.push({
					name: this.index_flag1,
					type: 'line',
					symbol: 'none',
					data: this.FUNC.mockDataList(30, 10)
				});
				this.chartColor.push(this.colorCard.index_flag1);
			}

			if (this.showFlagList.index_flag2) {
				seriesList.push({
					name: this.index_flag2,
					type: 'line',
					symbol: 'none',
					data: this.FUNC.mockDataList(30, 10)
				});
				this.chartColor.push(this.colorCard.index_flag2);
			}

			let option = {
				color: this.chartColor,
				tooltip: {
					trigger: 'axis'
					// formatter: function (item) {
					//   // do something
					//   return value
					// }
				},
				grid: {
					left: '20px',
					right: '20px',
					bottom: '20px',
					top: '20px'
				},
				xAxis: {
					type: 'category',
					boundaryGap: false,
					data: mockDate
				},
				yAxis: {
					axisLine: { show: false },
					axisTick: { show: false },
					splitLine: {
						lineStyle: {
							type: 'dashed'
						}
					},
					type: 'value'
				},
				series: seriesList
			};

			myChart.setOption(option, true);
		}
	}
};
</script>

<style lang="scss" scoped>
.overview-chart {
	padding: 0px 0px 0px 0px;
	background: white;
	.invest-tab {
		background: #f7f9fa;
		display: flex;
		height: 50px;
		.tab-btn {
			width: 100px;
			height: 50px;
			text-align: center;
			line-height: 50px;
			font-size: 16px;
			color: #333333;
			background: #fff;
			cursor: pointer;
		}
		.active-invest {
			font-weight: bold;
			background: #fff;
			border-bottom: 3px solid #40AFFF;
		}
	}
	.invest-chart {
		border-radius: 5px;
		margin-top: 15px;
		height: 250px;
		// .select-list {
		// 	display: flex;
		// 	align-items: center;
		// 	justify-content: flex-end;
		// 	font-size: 12px;
		// 	color: #666;
		// 	.select-item {
		// 		padding: 20px;
		// 		cursor: pointer;
		// 		.select-box {
		// 			display: inline-block;
		// 			width: 150px;
		// 		}
		// 		.color-line {
		// 			font-weight: bolder;
		// 		}
		// 	}
		// }
	}
}
.main {
	height: 244px !important;
}
</style>
