<template>
	<div class="basic_info_capability px-12 py-12" :style="info.flag == 2 ? 'border-radius: 0' : 'height: 74px;'">
		<div :class="index == 0 ? 'flex_start' : 'flex_start mt-8'" v-for="(item, index) in column" :key="index">
			<div class="flex_start mr-20" style="min-width: 180px" v-for="obj in item" :key="obj.value">
				<div
					class="mr-8 basic_info_fund_laabel"
					:style="obj.method ? 'color:#4096ff;cursor: pointer;' : ''"
					@click="obj.method ? obj.method(obj.label) : ''"
				>
					{{ obj.label }}
				</div>
				<div>{{ data[obj.value] }}</div>
			</div>
		</div>
	</div>
</template>

<script>
import { getCapabilityInfo } from '@/api/pages/Analysis.js';
export default {
	data() {
		return {
			column: [
				[
					{ label: '擅长风格', value: 'style', method: this.goDetail },
					{ label: '持仓风格', value: 'hold' }
				],
				[{ label: '擅长行业', value: 'industry', method: this.goDetail }]
			],
			data: {
				style: '价值、小盘',
				hold: '中高权益',
				industry: '有色金属、建筑装饰、房地产'
			},
			info: {}
		};
	},
	methods: {
		getData(info) {
			this.info = info;
			this.formatColumn();
			this.getCapabilityInfo();
		},
		async getCapabilityInfo() {
			let data = await getCapabilityInfo({
				codes: [this.info.code],
				type: this.info.type,
				flag: [this.info.flag],
				start_date: '',
				end_date: '',
				item: ['擅长风格', '持仓风格', '擅长行业']
			});
			if (data?.mtycode == 200) {
				this.data.style = data?.data?.find((v) => v.item == '擅长风格')?.description;
				this.data.hold = data?.data?.find((v) => v.item == '擅长风格')?.description || '暂无数据';
				this.data.industry = data?.data?.find((v) => v.item == '擅长行业')?.description;
			}
		},
		// 根据是基金还是基金经理动态显示不同的item
		formatColumn() {
			/**
			 * flag
			 * 1:基金
			 * 2:基金经理
			 * 3:基金公司
			 */
			if (this.info.flag == 1) {
				if (this.info.type == 'money') {
					this.column = [];
				} else {
					this.column = [
						[
							{ label: '擅长风格', value: 'style', method: this.goDetail },
							{ label: '持仓风格', value: 'hold' }
						],
						[{ label: '擅长行业', value: 'industry', method: this.goDetail }]
					];
				}
			} else if (this.info.flag == 2) {
				this.column = [
					[
						{ label: '相对擅长风格', value: 'style', method: this.goDetail },
						{ label: '擅长选股行业', value: 'industry', method: this.goDetail }
					]
				];
			}
		},
		// 前往详情分析
		goDetail(name) {
			if (name) {
				this.$event.$emit('position-analysis', name);
			}
		},
		createPrintWord() {
			let list = [];
			this.column.map((item) => {
				list.push(...item);
			});
			return [...this.$exportWord.exportDoubleColumnTable(list, this.data)];
		}
	}
};
</script>
<style lang="scss" scoped>
.basic_info_capability {
	border-radius: 0 0 4px 4px;
	background: #ecf5ff;
	font-size: 14px;
	color: rgba(0, 0, 0, 0.85);
	font-family: PingFang SC;
	font-size: 14px;
	font-style: normal;
	font-weight: 400;
	.basic_info_fund_laabel {
		color: rgba(0, 0, 0, 0.45);
		font-family: PingFang SC;
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
	}
}
</style>
