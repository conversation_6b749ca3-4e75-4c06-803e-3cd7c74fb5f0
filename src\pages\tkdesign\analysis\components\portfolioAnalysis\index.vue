<script>
import { getObjectStockFactors, getObjectStockInfo } from '@/api/pages/analysis/report';
import Analyse from './components/analyse.vue';
import Divisor from './components/divisor.vue';
import { handleData, handleDataBillion, handleData1 } from '@/utils/count';
import {
  downloadWord,
  exportTitleWithSubtitle,
  exportTableMergeHeader,
  exportTitle,
  exportFirstTitle,
  exportChart,
  exportTable,
  Format,
  exportSencondTitle
} from '@/utils/exportWord.js';
import { filter_json_to_excel_inside, changColumnToRow, filter_json_to_excel_inside_multiHeader } from '@/utils/exportExcel.js';
import analysisDescription from '@/components/components/components/analysisDescription/index.vue';
export default {
  components: { Analyse, Divisor, analysisDescription },
  computed: {
    description () {
      return `贝塔（Beta）：基金收益率对基准收益率加权线性回归的回归系数；
                    规模（Size）：总市值的自然对数；
                    成长（growth）：综合计算长期净利润预期、短期净利润预期、过去5年盈利增长率、长期历史销售率；
                    动量（Momentum）：长端动量与短端动量之差；
                    流动性（Liquidity）：综合计算月度、季度、年度换手率；
                    账面市值比（Book-to-Price）：市净率倒数；
                    盈利预期（Earning Yield）：净资产除以总市值；
                    残差波动（Residual Volatility）：综合计算超额收益年化波动率、年度超额收益率离差、Beta回归残差项的年化波动率；
                    非线性市值（Non-linear Size）：SIZE因子的立方和SIZE因子正交回归的残差项；
                    杠杆（Leverage）：综合计算市场杠杆、资产总债比、账面杠杆；`;
    }
  },
  data () {
    return {
      sortRule: { prop: null, order: 'nan' },
      tableList: {
        list: []
      },
      tableData: {
        list: [],
        oldList: [],
        pageSize: 10,
        pageIndex: 1,
        loading: false
      }, // 页面表格数据源
      searchSecurity: 3,
      options: [
        {
          value: 2,
          label: '泰康一级行业'
        },
        {
          value: 3,
          label: '申万一级行业'
        }
      ],
      value: '',
      tableColumns: [],
      items: ['beta', 'momentum', 'size', 'growth', 'bp', 'leverage', 'liquidity', 'nonlinearsize', 'earningyield'],
      // 单位列表
      unit: '亿',
      unit_list: [
        { label: '元', value: '元' },
        { label: '万', value: '万' },
        { label: '亿', value: '亿' }
      ]
    };
  },
  methods: {
    handleData,
    handleDataBillion,
    handleData1,
    /**
     * 获取中文
     */
    getZH (data) {
      switch (data) {
        case 'beta':
          return '贝塔因子';
          break;
        case 'momentum':
          return '动量因子';
          break;
        case 'size':
          return '市值因子';
          break;
        case 'growth':
          return '成长因子';
          break;
        case 'bp':
          return '估值因子';
          break;
        case 'leverage':
          return '杠杆因子';
          break;
        case 'liquidity':
          return '流动性因子';
          break;
        case 'nonlinearsize':
          return '非线性市值因子';
          break;
        case 'earningyield':
          return '盈利';
          break;
        default:
          return '--';
          break;
      }
    },

    uploadPage () {
      Promise.all([this.getTableData()]).then((arr) => {
        console.log(arr);
        this.buildData(arr[0][0], arr[0][1], arr[1]);
      });
    },

    /**
     * 获取个股因子（列）
     * @param arr
     */
    getColumns (arr) {
      this.tableColumns = [];
      arr.forEach((item) => {
        let name = '';
        name = this.getZH(item);
        this.tableColumns.push({
          label: name + '期末数值',
          prop: 'data.' + item
        });
      });
    },
    async getTableData () {
      this.tableData.loading = true;
      let data = {
        reportID: Number(this.$route.query.id),
        startFrom: Number(this.moment(this.$route.query.startDate).format('YYYYMMDD')),
        endTo: Number(this.moment(this.$route.query.endDate).format('YYYYMMDD')),
        industryStandard: 3,
        reportTemplate: this.$route.query.reportTemplate,
        selectedCuts: this.$route.query.graininess
      };
      data.factors = [0];
      data.holdType = 3;
      return getObjectStockFactors(data).then((res) => {
        if (res.code === 200) {
          this.tableData.list = res.data.rows
            .map((v) => {
              return {
                data: { ...v.data, swName: v.data.swlevel1, swCode: v.data.swlevel1code, tkName: v.data.tklevel1, tkCode: v.data.tk1code }
              };
            })
            ?.sort((a, b) => {
              if (a.data['avg_weight'] == '--' || a.data['avg_weight'] == 'NaN' || a.data['avg_weight'] == 'NaN%' || a.data['avg_weight'] == '' || a.data['avg_weight'] == 'nan') return 1
              if (b.data['avg_weight'] == '--' || b.data['avg_weight'] == 'NaN' || b.data['avg_weight'] == 'NaN%' || b.data['avg_weight'] == '' || b.data['avg_weight'] == 'nan') return -1

              // console.log(a.data.netasset, b.data.netasset)
              return Number(b?.data.avg_weight) - Number(a?.data.avg_weight);
            });
          this.tableData.oldList = res.data.rows
            .map((v) => {
              return {
                data: { ...v.data, swName: v.data.swlevel1, swCode: v.data.swlevel1code, tkName: v.data.tklevel1, tkCode: v.data.tk1code }
              };
            })
            ?.sort((a, b) => {
              if (a.data['avg_weight'] == '--' || a.data['avg_weight'] == 'NaN' || a.data['avg_weight'] == 'NaN%' || a.data['avg_weight'] == '' || a.data['avg_weight'] == 'nan') return 1
              if (b.data['avg_weight'] == '--' || b.data['avg_weight'] == 'NaN' || b.data['avg_weight'] == 'NaN%' || b.data['avg_weight'] == '' || b.data['avg_weight'] == 'nan') return -1

              // console.log(a.data.netasset, b.data.netasset)
              return Number(b?.data.avg_weight) - Number(a?.data.avg_weight);
            });
          return [this.tableData.list, this.tableData.oldList];
        } else {
          this.tableData.list = [];
          this.tableData.oldList = [];
          return [[], []];
        }
      });
    },
    async getTableData1 () {
      this.tableData.loading = true;
      let data = {
        reportID: Number(this.$route.query.id),
        startFrom: Number(this.moment(this.$route.query.startDate).format('YYYYMMDD')),
        endTo: Number(this.moment(this.$route.query.endDate).format('YYYYMMDD')),
        industryStandard: 3,
        selectedCuts: this.$route.query.graininess
      };
      data.factors = [0];
      data.holdType = 3;
      data.industryStandard = 2;
      return getObjectStockFactors(data).then((res) => {
        return res.data?.rows;
      });
    },

    buildData (list, oldList, datalist) {
      // console.log(datalist);
      // list.forEach((item, index) => {
      //   datalist.forEach((citem, cindex) => {
      //     if (citem.data.name === item.data.name) {
      //       item.data.tkName = citem.data.swlevel1;
      //       item.data.tkCode = citem.data.tklevel1;
      //     }
      //   });
      // });
      this.tableData.list = list;
      // oldList.forEach((item, index) => {
      //   datalist.forEach((citem, cindex) => {
      //     if (citem.data.name === item.data.name) {
      //       item.data.tkName = citem.data.swlevel1;
      //       item.data.tkCode = citem.data.tklevel1;
      //     }
      //   });
      // });
      this.tableData.oldList = oldList;
      this.tableData.loading = false;
    },

    /**
     * 分页切换
     */
    changeSize (size) {
      this.tableData.pageSize = size;
    },
    currentChange (value) {
      this.tableData.pageIndex = value;
    },
    cellStyle ({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        return {
          color: '#4096ff',
          cursor: 'pointer'
        };
      }
    },
    /**
     *给第一列添加点击事件
     * @param obj
     */
    cellClick (obj) {
      this.$refs.analyse.showDialog(obj);
    },

    /**
     *数据处理
     */
    formatter (row, column, cellValue, index) {
      if (cellValue === 'nan' || cellValue === 'NaN' || cellValue === undefined || !cellValue) {
        return '--';
      } else if (column.label === '期末规模（亿）' || column.label === '总市值（亿）') {
        if (Number((Number(cellValue) / 100000000).toFixed(2)) !== 0) {
          return (Number(cellValue) / 100000000).toFixed(2);
        } else {
          return '0.00（' + (Number(cellValue) / 10000).toFixed(2) + '万）';
        }
      } else if (column.label === 'pe(ttm)' || column.label === 'roe') {
        return Number(cellValue).toFixed(2);
      } else if (column.label === '股息率') {
        return Number(cellValue).toFixed(2) + '%';
      } else if (column.label === '市值收益（万）' || column.label === '累计浮盈（万）' || column.label === '财务收益（万）') {
        return (Number(cellValue) / 10000).toFixed(2);
      } else if (column.label === '收益率期末数值') {
        return Number(cellValue).toFixed(2) + '%';
      } else {
        return Number(cellValue).toFixed(2);
      }
    },
    /**
     *数据单位处理
     */
    formatterUnit (row, column, cellValue, index) {
      if (this.unit == '亿') {
        return (Number(cellValue) / 10 ** 8).toFixed(2);
      } else if (this.unit == '万') {
        return (Number(cellValue) / 10 ** 4).toFixed(2);
      } else {
        return Number(cellValue).toFixed(2);
      }
    },
    /**
     * 排序
     */
    sortData ({ column, prop, order }) {
      let that = this;
      let key = prop.split('.')[1];
      let arr = JSON.parse(JSON.stringify(this.tableData.list.filter((v) => v.data[key] !== 'nan' && v.data[key] !== 'NaN')));
      let noArr = this.tableData.list.filter((v) => v.data[key] === 'nan' || v.data[key] === 'NaN');
      if (order === 'ascending') {
        if (prop == 'data.swName' || prop == 'data.tkName' || prop == 'data.flag') {
          that.tableData.list = noArr.concat(
            arr.sort((a, b) => {
              if (a.data[key] == '--' || a.data[key] == 'NaN' || a.data[key] == 'NaN%' || a.data[key] == '' || a.data[key] == 'nan') return 1
              if (b.data[key] == '--' || b.data[key] == 'NaN' || b.data[key] == 'NaN%' || b.data[key] == '' || b.data[key] == 'nan') return -1
              if (a.data[key] > b.data[key]) return 1;
              else return -1;
            })
          );
        } else {
          that.tableData.list = noArr.concat(arr.sort((a, b) => {
            if (a.data[key] == '--' || a.data[key] == 'NaN' || a.data[key] == 'NaN%' || a.data[key] == '' || a.data[key] == 'nan') return 1
            if (b.data[key] == '--' || b.data[key] == 'NaN' || b.data[key] == 'NaN%' || b.data[key] == '' || b.data[key] == 'nan') return -1

            return Number(a.data[key]) - Number(b.data[key])
          }
          ));
        }
      }
      if (order === 'descending') {
        if (prop == 'data.swName' || prop == 'data.tkName' || prop == 'data.flag') {
          that.tableData.list = arr
            .sort((a, b) => {
              if (a.data[key] < b.data[key]) return 1;
              else return -1;
            })
            .concat(noArr);
        } else {
          that.tableData.list = arr.sort((a, b) => Number(b.data[key]) - Number(a.data[key])).concat(noArr);
        }
      }
      if (order === null) {
        that.tableData.list = that.tableData.oldList;
      }
    },

    getData (row, key) {
      let newKey = key.split('.')[1];
      return row.data[newKey].toFixed(2);
    },
    async createPrintWord () {
      return Promise.all([this.getTableData(), this.getTableData1()]).then((arr) => {
        this.buildData(arr[0][0] || [], arr[0][1] || [], arr[1] || []);
        return new Promise((resolve, reject) => {
          const head = exportFirstTitle('四、组合各个股分析');
          const subhead = exportTitle('组合各个股分析');
          let array = this.tableData.list.map((item) => {
            return item.data;
          });
          const title = [
            { label: '个股', value: 'name', format: '' },
            { label: '申万一级行业', value: 'swName', format: '' },
            { label: '泰康内部行业', value: 'tkName', format: '' },
            { label: '总市值（亿)', value: 'totalmv', format: 'fix8fix4ns' },
            { label: 'pe(ttm)', value: 'pe', format: 'fix2' },
            { label: 'roe', value: 'roe', format: 'fix2' },
            { label: '股息率', value: 'dividendratiolyr', format: 'fix2b' },
            { label: '期末规模（亿）', value: 'netasset', format: 'fix8fix4ns' },
            { label: '平均持仓权重', value: 'avg_weight', format: 'fix2p' },
            { label: '财务收益（万)', value: 'financialIncomeChange', format: 'fix4ns' },
            { label: '市值收益（万）', value: 'marketValueGainLossChange', format: 'fix4ns' },
            { label: '累计浮盈（万）', value: 'profitChange', format: 'fix4ns' },
            { label: '区间收益率', value: 'cum_return', format: 'fix2p' }
          ];
          const table = exportTable(title, array, {}, {});

          resolve([...head, ...subhead, ...table]);
        });
      });
    },
    downloadExcel (name) {
      if (name === '组合各个股分析') {
        const title = [
          { label: '个股', value: 'name', format: '' },
          { label: '代码', value: 'code', format: '' },
          { label: '申万一级行业', value: 'swName', format: '' },
          { label: '泰康内部行业', value: 'tkName', format: '' },
          { label: '总市值（亿)', value: 'totalmv', format: 'fix8fix4ns' },
          { label: 'pe(ttm)', value: 'pe', format: 'fix2' },
          { label: 'roe', value: 'roe', format: 'fix2' },
          { label: '股息率', value: 'dividendratiolyr', format: 'fix2b' },
          { label: '期末规模（亿）', value: 'netasset', format: 'fix8fix4ns' },
          { label: '平均持仓权重', value: 'avg_weight', format: 'fix2p' },
          { label: '财务收益（万)', value: 'financialIncomeChange', format: 'fix4ns' },
          { label: '市值收益（万）', value: 'marketValueGainLossChange', format: 'fix4ns' },
          { label: '累计浮盈（万）', value: 'profitChange', format: 'fix4ns' },
          { label: '区间收益率', value: 'cum_return', format: 'fix2p' }
        ];
        console.log(this.tableData.list);
        filter_json_to_excel_inside(title, this.processArray(this.tableData.list), ['data'], name);
      }
    },
    processArray (array) {
      let temp = JSON.parse(JSON.stringify(array));
      temp.forEach(item => {
        // 遍历对象的每个属性
        for (let key in item.data) {
          // 检查属性值是否为'--'
          if (item.data[key] === '--' || item.data[key] === 'nan' || item.data[key] === undefined || item.data[key] === 'NaN') {
            // 将'--'替换为字符串空''
            item.data[key] = "";
          }
        }
      });
      return temp;
    },
    changeUnit (val) {
      this.unit = val;
    }
  }
};
</script>

<template>
  <div class="page-box">
    <div class="flex item-center justify-between">
      <div class="area-title">组合各个股分析</div>
      <div class="border_table_header_search">
        <div class="flex_start">
          <div class="mr-4">单位选择:</div>
          <div>
            <el-select v-model="unit"
                       class="mr-12"
                       style="width: 50px">
              <el-option v-for="item in unit_list"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value"
                         @change="changeUnit">
              </el-option>
            </el-select>
          </div>
        </div>

        <!-- 添加个股因子按钮 -->
        <Divisor @updateColumn="getColumns" />
        <img alt=""
             src="../../../../../assets/img/download.png"
             class="download"
             @click="downloadExcel('组合各个股分析')" />
      </div>
    </div>
    <el-divider></el-divider>
    <div class="area-body">
      <div class="table">
        <el-table v-loading="tableData.loading"
                  :data="tableData.list.slice((tableData.pageIndex - 1) * tableData.pageSize, tableData.pageIndex * tableData.pageSize)"
                  border
                  @sort-change="sortData"
                  :cell-style="cellStyle">
          <el-table-column align="gotoleft"
                           label="个股"
                           min-width="140">
            <template slot-scope="scope">
              <div @click="cellClick(scope.row.data)">
                {{ scope.row.data.name }}
              </div>
            </template>
          </el-table-column>
          <el-table-column align="gotoleft"
                           prop='data.code'
                           label="代码"
                           min-width="140">
          </el-table-column>
          <el-table-column align="gotoleft"
                           label="持有类型"
                           sortable="custom"
                           prop="data.flag"
                           min-width="140">
            <template slot-scope="scope">{{ scope.row.data.flag == 'fof' ? '间接持有' : '直接持有' }}</template>
          </el-table-column>
          <el-table-column align="gotoleft"
                           label="申万一级行业"
                           sortable="custom"
                           prop="data.swName"
                           min-width="140" />
          <el-table-column align="gotoleft"
                           label="泰康内部行业"
                           sortable="custom"
                           prop="data.tkName"
                           min-width="140" />
          <el-table-column align="gotoleft"
                           :label="'总市值（' + unit + '）'"
                           prop="data.totalmv"
                           sortable="custom"
                           min-width="140"
                           :formatter="formatterUnit">
          </el-table-column>
          <el-table-column align="gotoleft"
                           label="pe(ttm)"
                           prop="data.pe"
                           sortable="custom"
                           min-width="140"
                           :formatter="formatter" />
          <el-table-column align="gotoleft"
                           label="roe"
                           prop="data.roe"
                           sortable="custom"
                           min-width="140"
                           :formatter="formatter" />
          <el-table-column align="gotoleft"
                           label="股息率"
                           prop="data.dividendratiolyr"
                           sortable="custom"
                           min-width="140"
                           :formatter="formatter" />
          <el-table-column align="gotoleft"
                           :label="'期末规模（' + unit + '）'"
                           sortable="custom"
                           min-width="140"
                           prop="data.netasset"
                           :formatter="formatterUnit">
          </el-table-column>
          <el-table-column align="gotoleft"
                           label="平均持仓权重"
                           prop="data.avg_weight"
                           sortable="custom"
                           min-width="140">
            <template slot-scope="scope">
              {{ handleData(scope.row.data.avg_weight) }}
            </template>
          </el-table-column>
          <el-table-column align="gotoleft"
                           :label="'财务收益（' + unit + '）'"
                           sortable="custom"
                           prop="data.financialIncomeChange"
                           min-width="140"
                           :formatter="formatterUnit" />
          <el-table-column align="gotoleft"
                           :label="'市值收益（' + unit + '）'"
                           sortable="custom"
                           prop="data.marketValueGainLossChange"
                           min-width="140"
                           :formatter="formatterUnit" />
          <el-table-column align="gotoleft"
                           :label="'累计浮盈（' + unit + '）'"
                           prop="data.profitChange"
                           sortable="custom"
                           min-width="140"
                           :formatter="formatterUnit" />
          <el-table-column align="gotoleft"
                           label="区间收益率"
                           prop="data.cum_return"
                           sortable="custom"
                           min-width="140">
            <template slot-scope="scope">
              {{ handleData(scope.row.data.cum_return) }}
            </template>
          </el-table-column>
          <!-- <el-table-column align="gotoleft"
                           label="pe"
                           prop="data.pe"
                           sortable="custom"
                           min-width="140">
            <template slot-scope="scope">
              {{ handleData1(scope.row.data.pe) }}
            </template>
          </el-table-column>-->
          <el-table-column align="gotoleft"
                           label="pb"
                           prop="data.pb"
                           sortable="custom"
                           min-width="140">
            <template slot-scope="scope">
              {{ handleData1(scope.row.data.pb) }}
            </template>
          </el-table-column>
          <!--  <el-table-column align="gotoleft"
                           label="ROE"
                           prop="data.roe"
                           sortable="custom"
                           min-width="140">
            <template slot-scope="scope">
              {{ handleData(scope.row.data.roe) }}
            </template>
          </el-table-column> -->
          <el-table-column align="gotoleft"
                           label="股息率"
                           prop="data.dividendratiolyr"
                           sortable="custom"
                           min-width="140">
            <template slot-scope="scope">
              {{ handleData(scope.row.data.dividendratiolyr) }}
            </template>
          </el-table-column>
          <el-table-column align="gotoleft"
                           label="净利润增长率"
                           prop="data.net_income_yoy"
                           sortable="custom"
                           min-width="140">
            <template slot-scope="scope">
              {{ handleData(scope.row.data.net_income_yoy) }}
            </template>
          </el-table-column>
          <el-table-column align="gotoleft"
                           label="营业收入同比增长"
                           prop="data.income_yoy"
                           sortable="custom"
                           min-width="140">
            <template slot-scope="scope">
              {{ handleData(scope.row.data.income_yoy) }}
            </template>
          </el-table-column>
          <el-table-column v-for="(item, index) in tableColumns"
                           :key="index"
                           align="gotoleft"
                           sortable="custom"
                           min-width="200"
                           :label="item.label"
                           :prop="item.prop"
                           :formatter="formatter" />
          <!-- <el-table-column align="gotoleft" v-else prop="data.bp" sortable="custom" min-width="200" :formatter="formatter">
						<template slot="header">
							<div>估值因子期末数值</div>
							<span>
								<el-tooltip
									class="item"
									effect="dark"
									content="Barra风格因子模型是MSCI公司推出的多因子模型产品，主要用于进行多因子选股分析和结构化风险因子分析。模型共有十个风格因子，各因子的含义为：SIZE（市值因子），BETA（贝塔因子），MOMENTUM（动量因子），RESIDUAL VOLATILITY（残差波动因子），NON-LINEAR-SIZE（非线性市值因子），BOOK-TO-PRICE（账面市值比因子），LIQUIDITY（流动性因子），EARNING YEILD（盈利预期因子），GROWTH（成长因子），LEVERAGE（杠杆因子）。"
									placement="top"
								>
									<i class="el-icon-question" />
								</el-tooltip>
							</span>
						</template>
					</el-table-column> -->

          <el-empty slot="empty"
                    description="暂无数据"
                    :image-size="300"></el-empty>
        </el-table>
        <div class="pagination_board">
          <el-pagination :current-page.sync="tableData.pageIndex"
                         :page-size="tableData.pageSize"
                         :total="tableData.list.length"
                         background
                         layout="total, sizes, prev, pager, next"
                         @size-change="changeSize"
                         @current-change="currentChange" />
        </div>
        <!-- <div class="mt-12">
          <analysis-description title="组合个股分析"
                                :description="description"></analysis-description>
        </div> -->
      </div>
    </div>
    <Analyse ref="analyse" />
  </div>
</template>

<style scoped lang="scss">
@import '../../../tkdesign';

.area-title {
	margin-bottom: 0 !important;
}

.border_table_header_search {
	display: flex;
	justify-content: flex-end;
	position: relative;

	.selector {
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
		line-height: 22px;
		margin-top: 5px;
		color: rgba(0, 0, 0, 0.85);
	}

	.search-security {
		width: 250px;
		margin-right: 10px;
	}
}

.table {
	margin-top: 16px;
}

.download {
	padding-left: 10px;
}

.pagination_board {
	text-align: right;
	margin-top: 16px;
}
</style>
