import request from '@/api/request';
// 获取基金/基准
// export function getFundOrBase(params) {
// 	return request({
// 		url: '/SearchFundOrIndex/',
// 		method: 'get',
// 		params
// 	});
// }
//获得基准曲线
export function getBasicFundLine(data) {
	return request({
		url: '/FundReturnCompare/',
		method: 'post',
		data
	});
}

//获得下拉框搜索数据
export function getFundOrBase(params) {
	return request({
		url: '/Search/',
		method: 'get',
		params
	});
}

// 收益率分布直方图
export function FundReturnSection(params) {
	return request({
		url: '/FundReturnSection/',
		method: 'get',
		params
	});
} //
//
export function ManagerReturnCompare(data) {
	return request({
		url: '/ManagerReturnCompare/',
		method: 'post',
		data
	});
}
export function ManagerRateStd(params) {
	return request({
		url: '/ManagerRateStd/',
		method: 'get',
		params
	});
} //
