<!--  -->
<template>
	<div class="fundCateOnly">
		<div
			v-show="haveName != ''"
			style="
				font-weight: 400;
				font-size: 14px;
				line-height: 22px;
				color: rgba(0, 0, 0, 0.85);
				margin-left: 0px;
				margin-right: 16px;
				margin-bottom: 4px;
			"
		>
			{{ haveName }}
		</div>
		<div style="display: flex; align-items: center">
			<operator v-if="is_range" ref="operator" @resolveMathRange="resolveMathRange"></operator>
			<el-checkbox-group @change="changeNode" v-model="field103" size="medium">
				<el-checkbox v-for="(item, index) in field103Options" :key="index" :label="item.value" :disabled="item.disabled">{{
					item.label
				}}</el-checkbox>
			</el-checkbox-group>
		</div>
	</div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import operator from '@/pages/filter/fund/beta/componentsFilter/components/operator.vue';

export default {
	props: {
		is_range: {
			type: Boolean,
			default: false
		},
		haveName: {
			type: String,
			default: ''
		},
		dataX: {
			type: Object,
			default: {}
		},
		placeholder: {
			type: String
		},
		indexFlag: {
			type: Number
		},
		baseIndexFlag: {
			type: Number
		},
		fundType: {
			type: String,
			default: 'equity'
		},
		field103Options: {
			type: Array,
			default: []
		}
	},
	//import引入的组件需要注入到对象中才能使用
	components: { operator },
	data() {
		//这里存放数据
		return {
			field103: [],
			mathRange: { mathRange: 'avg' }
			// field103Options: [
			// 	{
			// 		label: '机构定制',
			// 		value: '机构定制'
			// 	},
			// 	{
			// 		label: '机构为主',
			// 		value: '机构为主'
			// 	},
			// 	{
			// 		label: '散户为主',
			// 		value: '散户为主'
			// 	}
			// ]
		};
	},
	//监听属性 类似于data概念
	computed: {},
	//监控data中的数据变化
	watch: {
		dataX(val) {
			if (val.dataResult && val.dataResult.length > 0) {
				this.field103 = val.dataResult[0].value;
				if (this.$refs['operator']) {
					this.$refs['operator'].getFlag(val.dataResult[0].mathRange);
				}
			}
		},
		haveName(n, o) {
			console.log(n, o);
		}
	},
	//方法集合
	methods: {
		resolveMathRange(obj) {
			this.mathRange = obj;
			this.resolveFather();
		},
		changeNode() {
			this.resolveFather();
		},
		resolveFather() {
			this.$emit('fundCateOnlyChange', this.baseIndexFlag, this.indexFlag, this.field103, this.FUNC.isEmpty(this.field103), this.mathRange);
		}
	},
	//生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	//生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		if (JSON.stringify(this.dataX) != '{}') {
			if (this.dataX.dataResult && this.dataX.dataResult.length > 0) {
				this.field103 = this.dataX.dataResult[0].value;
				if (this.$refs['operator']) {
					this.$refs['operator'].getFlag(this.dataX.dataResult[0].mathRange);
				}
			}
		}
	},
	beforeCreate() {}, //生命周期 - 创建之前
	beforeMount() {}, //生命周期 - 挂载之前
	beforeUpdate() {}, //生命周期 - 更新之前
	updated() {}, //生命周期 - 更新之后
	beforeDestroy() {}, //生命周期 - 销毁之前
	destroyed() {}, //生命周期 - 销毁完成
	activated() {} //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
</style>
