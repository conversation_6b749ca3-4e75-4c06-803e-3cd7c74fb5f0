<!-- 基金配置走势 -->
<template>
	<div class="chart_one">
		<div v-loading="loadyejis">
			<div class="card_header">
				<el-menu :default-active="activeIndex" class="el-menu-demo" mode="horizontal" active-text-color="#4096ff" @select="handleSelect">
					<el-menu-item v-for="item in menuList" :key="item.key" :index="item.key">{{ item.label }}</el-menu-item>
				</el-menu>
			</div>
			<div class="charts_fill_class" v-loading="loading">
				<v-chart
					element-loading-text="暂无数据"
					element-loading-spinner="el-icon-document-delete"
					element-loading-background="rgba(239, 239, 239, 0.5)"
					class="charts_one_class"
					ref="allocationTrend"
					style="height: 442px; margin-top: 8px"
					autoresize
					:options="option"
				/>
			</div>
		</div>
	</div>
</template>

<script>
import VChart from 'vue-echarts';
import { barChartOption, lineChartOption } from '@/utils/chartStyle';
export default {
	name: 'allocationTrend',
	components: { VChart },
	data() {
		return {
			activeIndex: 'fund',
			menuList: [
				{ label: '基金配置走势', key: 'fund' },
				{ label: '资产配置走势', key: 'allocation' }
			],
			options: [
				{
					label: '总回报',
					value: 'all'
				}
			],
			model: '',
			option: {},
			loading: true
		};
	},
	methods: {
		getFundData(data) {
			this.loading = false;
			let legend = data?.map((item) => {
				return item.name;
			});
			let xAxis = Array.from(
				new Set(
					data?.map((item) => {
						return item.date;
					})
				)
			).sort();
			let series = [];
			data?.map((item) => {
				let index = series.findIndex((obj) => {
					return obj.name == item.name;
				});
				if (index == -1) {
					series.push({
						name: item.name,
						type: 'bar',
						data: [[item.date, (item.weight * 100).toFixed(2)]],
						symbol: 'none',
						areaStyle: {},
						barWidth: '100%',
						stack: 'Total'
					});
				} else {
					series[index].data.push([item.date, (item.weight * 100).toFixed(2)]);
				}
			});
			this.option = barChartOption({
				toolbox: false,
				legend,
				xAxis: [{ data: xAxis }],
				dataZoom: true,
				tooltip: {
					formatter: function (obj) {
						var value = obj[0].axisValue + `<br />`;
						for (let i = 0; i < obj.length; i++) {
							value +=
								`<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:` +
								obj[i].color +
								`;"></span>` +
								obj[i].seriesName +
								':' +
								Number(obj[i].data[1]).toFixed(2) +
								'%' +
								`<br />`;
						}
						return value;
					}
				},
				yAxis: [
					{
						type: 'value',
						scale: true,
						formatter: function (val) {
							return val + '%';
						},
						max: 100
					}
				],
				series
			});
			console.log(this.option);
		},
		getAllocationData(data) {
			this.loading = false;
			let arr = [];
			let date = [];
			for (const key in data) {
				if (key == 'yearqtr') {
					date = data[key];
				} else if (key != 'all_weight') {
					var name = '';
					switch (key) {
						case 'equity_weight':
							name = '股票';
							break;
						case 'abs_weight':
							name = 'abs';
							break;
						case 'bond_weight':
							name = '债券';
							break;
						case 'cash_weight':
							name = '货币';
							break;
						case 'fund_weight':
							name = '基金';
							break;
						case 'other_weight':
							name = '其他';
							break;
						default:
							name = '其他';
							break;
					}
					arr.push({
						name,
						data: data[key]
					});
				}
			}
			let legend = arr.map((item) => {
				return item.name;
			});
			let xAxis = date.sort();
			let series = arr.map((item) => {
				return {
					name: item.name,
					type: 'line',
					data: item.data,
					symbol: 'none',
					areaStyle: {},
					stack: 'Total'
				};
			});
			this.option = lineChartOption({
				legend,
				xAxis: [{ data: xAxis }],
				dataZoom: true,
				tooltip: {
					formatter: function (obj) {
						var value = obj[0].axisValue + `<br />`;
						for (let i = 0; i < obj.length; i++) {
							value +=
								`<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:` +
								obj[i].color +
								`;"></span>` +
								obj[i].seriesName +
								':' +
								Number(obj[i].data * 100).toFixed(2) +
								'%' +
								`<br />`;
						}
						return value;
					}
				},
				yAxis: [
					{
						type: 'value',
						scale: true,
						formatter: function (val) {
							return val * 100 + '%';
						},
						max: 1,
						min: 0
					}
				],
				series
			});
		},
		handleSelect(val) {
			this.$emit('resolveFather', val);
			this.loading = true;
		},
		createPrintWord() {
			this.$refs['allocationTrend'].mergeOptions({ toolbox: { show: false } });
			let height = this.$refs['allocationTrend']?.$el.clientHeight;
			let width = this.$refs['allocationTrend']?.$el.clientWidth;
			let chart = this.$refs['allocationTrend'].getDataURL({
				type: 'png',
				pixelRatio: 3,
				backgroundColor: '#fff'
			});
			this.$refs['allocationTrend'].mergeOptions({ toolbox: { show: true } });
			return [...this.$exportWord.exportTitle('基金配置走势'), ...this.$exportWord.exportChart(chart, { width, height })];
		}
	}
};
</script>

<style>
.chart_one .el-menu-demo .el-menu-item {
	height: 47px;
	line-height: 47px;
}
.card_header .el-menu.el-menu--horizontal {
	width: 100%;
	border-bottom: 1px solid #e6e6e6;
}
</style>
