<template>
  <div class="border_table">
    <!-- 头部区域 -->
    <div class="border_table_header">
      <!-- 右侧标题区域 -->
      <div class="border_table_header_title">
        <div class="block" />
        <div>重点关注个股收益</div>
        <img alt=""
             src="../../../../assets/img/question.png"
             class="question">
      </div>
      <div class="border_table_header_filter">
        <el-select v-model="graininess.chartModel"
                   class="search-security"
                   :disabled="loading.chartLoading"
                   @change="getChartData(performance.data)"
                   placeholder="请选择颗粒度明细">
          <el-option v-for="item in graininess.option"
                     :key="item.value"
                     :label="item.label"
                     :value="item.value">
          </el-option>
        </el-select>
        <!-- 下载 -->
        <div class="border_table_header_upload">
          <i class="el-icon-download"></i>
        </div>
      </div>
    </div>
    <div class="area-body">
      <div class="chart">
        <v-chart v-loading="loading.chartLoading"
                 autoresize
                 style="height: 340px; width: 100% !important"
                 :options="performance.options" />
      </div>
    </div>
    <div class="area-body">
      <div class="chart">
        <v-chart v-loading="loading.chartLoading"
                 autoresize
                 style="height: 340px; width: 100% !important"
                 :options="performance.options2" />
      </div>
    </div>
    <div class="border_table">
      <div class="border_table_header">
        <div class="border_table_header_title">
          <div class="block" />
          <div>重点关注个股指标</div>
          <img alt=""
               src="../../../../assets/img/question.png"
               class="question">
        </div>
        <div class="border_table_header_filter">
          <el-select v-model="graininess.tableModel"
                     class="search-security"
                     :disabled="loading.tableLoading"
                     @change="changeSelect"
                     placeholder="请选择颗粒度明细">
            <el-option v-for="item in graininess.option"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
          <el-select v-model="date.dateFlag"
                     class="search-security"
                     @change="changeDate"
                     :disabled="loading.tableLoading"
                     placeholder="年末">
            <el-option v-for="item in date.option"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
          <div class="border_table_header_upload">
            <i class="el-icon-download"></i>
          </div>
        </div>
      </div>
      <div class="area-body">
        <div class="table">
          <el-table :data="tableData"
                    border
                    stripe
                    v-loading="loading.tableLoading">
            <el-table-column align="gotoleft"
                             label="名称"
                             prop="data.code" />
            <el-table-column align="gotoleft"
                             label="账面价值(亿)"
                             prop="data.compoentBookValue"
                             sortable>
              <template slot-scope="scope"> {{ scope.row.data.compoentBookValue|fixY }} </template>
            </el-table-column>
            <el-table-column align="gotoleft"
                             label="持有数量(万)"
                             prop="data.num"
                             sortable>
              <template slot-scope="scope"> {{ scope.row.data.num|fixW }} </template>
            </el-table-column>
            <el-table-column align="gotoleft"
                             label="买入规模(亿)"
                             prop="data.BuyAmount"
                             sortable>
              <template slot-scope="scope"> {{ scope.row.data.BuyAmount|fixY }} </template>
            </el-table-column>
            <el-table-column align="gotoleft"
                             label="卖出规模(亿)"
                             prop="data.SellAmount"
                             sortable>
              <template slot-scope="scope"> {{ scope.row.data.SellAmount|fixY }} </template>
            </el-table-column>
            <el-table-column align="gotoleft"
                             label="市值收益(亿)"
                             prop="data.marketValueGainLoss"
                             sortable>
              <template slot-scope="scope"> {{ scope.row.data.marketValueGainLoss|fixY }} </template>
            </el-table-column>
            <el-table-column align="gotoleft"
                             label="财务收益(亿)"
                             prop="data.financialIncome"
                             sortable>
              <template slot-scope="scope"> {{ scope.row.data.financialIncome|fixY }} </template>
            </el-table-column>
            <el-table-column align="gotoleft"
                             label="净买入(亿)"
                             prop="data.NetBuyAmount"
                             sortable>
              <template slot-scope="scope"> {{ scope.row.data.NetBuyAmount|fixY }} </template>
            </el-table-column>
            <el-table-column align="gotoleft"
                             label="日期"
                             prop="data.date_flag"
                             sortable><template slot-scope="scope"> {{ scope.row.data.date_flag|fixX }} </template>
            </el-table-column>
            <el-empty :image-size="180" />
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { lineChartOption } from "@/utils/chartStyle";
import {
  getObjectStocksReturnInfo,
  getObjectStocksMeasureInfo
} from "../../../../api/pages/tkdesign/monitorSpectaculars";

export default {
  data () {
    return {
      tableData: [],// 页面表格数据源
      tempData: [],
      date: {
        dateFlag: 'year',
        option: [
          {
            value: 'year',
            label: '年份'
          },
          {
            value: 'month',
            label: '月份'
          }
        ],
      },
      value: '',
      account: '',
      selectAccount: [
        {
          value: '选项1',
          label: '整体'
        },
        {
          value: '选项2',
          label: '分析对象1'
        },
        {
          value: '选项3',
          label: '分析对象2'
        },
      ],
      promptMessage: false,
      performance: {
        data: [],
        options2: {},
        options: {}, // 业绩表现图表数据源
      },
      graininess: {
        option: [],
        chartModel: '',
        tableModel: ''
      },// 颗粒度明细
      loading: {
        tableLoading: false,
        chartLoading: false
      },
      Tableparams: {}
    }
  },
  filters: {
    fixY (value) {
      return (value / *********).toFixed(4) || '--'
    },
    fixW (value) {
      return (value / 10000).toFixed(2) || '--'
    }, fixX (value) {
      if (value == 'mean') return '均值'
      else {
        return value
      }
    }
  },
  methods: {
    /**
     *重点关注个股收益选择框change事件
     */
    changeSelect () {
      // console.log(this.tempData);
      this.tableData = []
      if (this.graininess.chartModel == 'all') {
        this.tableData = this.tempData;
      }
      else {
        this.tableData = this.tempData.filter(item => item.data.code === this.graininess.tableModel)
      }
      // this.tableData = this.tempData.filter(item => item.data.code === this.graininess.tableModel)
    },
    /**
     * 调取接口获取图表数据
     * @param data
     */
    getObjectStocksReturnInfo (data, option) {
      this.graininess.option = option
      this.graininess.chartModel = this.graininess.tableModel = option[0].value
      getObjectStocksReturnInfo(data).then((res) => {
        this.loading.chartLoading = false
        if (res.code === 200) {
          this.performance.data = res.data.rows
          this.getChartData(this.performance.data)
        }
      })
    },
    /**
     * 调接口获取表格数据
     * @params data
     */
    changeDate (value) {
      this.getTableData({
        ...this.Tableparams,
        dateFlag: value
      })
    },
    getTableData (data) {
      this.Tableparams = {
        ...data,
        dateFlag: this.date.dateFlag,
      }
      getObjectStocksMeasureInfo(this.Tableparams).then((res) => {
        this.loading.tableLoading = false
        if (res.code === 200) {
          this.tempData = res.data.rows
          this.changeSelect()
        } else {
          this.tableData = []
        }
      })
    },
    loadingTableLoading () {
      this.loading.chartLoading = true
    },
    /**
     * 图表生成
     */
    getChartData (data) {
      let rows = []
      if (this.graininess.chartModel == 'all') {
        rows = data;
      }
      else {
        rows = data.filter((item) => item.data['name'] === this.graininess.chartModel);
      }
      // let rows = data.filter(item => item.data['name'] === this.graininess.chartModel)
      let date = []
      let obj = {}
      let arr = ['累计市值收益', '累计财务收益']
      let series = []
      // 时间排序
      rows.sort((a, b) => {
        const dateA = new Date(a.data.accountingDate);
        const dateB = new Date(b.data.accountingDate);
        return dateA - dateB;
      })
      rows.forEach((item) => {
        arr.forEach((v) => {
          if (!obj[v]) {
            obj[v] = []
          }
          if (v === '累计市值收益')
            obj[v].push(item.data['marketValueGainLossChange'] / *********)
          else if (v === '累计财务收益')
            obj[v].push(item.data['financialIncomeChange'] / *********)
        })
        date.push(item.data.accountingDate)
      })
      for (let key in obj) {
        series.push({
          name: key,
          type: 'bar',
          yAxisIndex: 0,
          symbol: 'none',
          stack: 'Total',
          emphasis: {
            focus: 'series'
          },
          lineStyle: {
            width: 2,
          },
          data: obj[key],
        })
      }
      console.log(obj, series, arr)
      this.performance.options = lineChartOption({
        dataZoom: false,
        toolbox: 'none',
        legend: {
          data: [arr?.[0]],
        },
        xAxis: [
          {
            name: '',
            data: date,
            boundaryGap: true
          }
        ],
        yAxis: [
          {
            type: 'value',
            position: 'left',
            axisLabel: {
              formatter: '{value}'
            },
          }
        ],
        series: [series?.[0]],
        tooltip: {
          formatter: function (obj) {
            let value = `<div style="font-size:14px;">` + obj?.[0].axisValue + `</div>`;
            for (let i = 0; i < obj.length; i++) {
              value +=
                `<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
                `<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
                obj?.[i].color +
                `;"></div>` +
                `<div style="font-family: PingFang SC;">` +
                obj?.[i].seriesName +
                '</div></div>' +
                `<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
                (Number(obj?.[i].value).toFixed(4) + '亿') +
                '</div>' +
                `</div>`;
            }
            return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
          }
        },
      });
      this.performance.options2 = lineChartOption({
        dataZoom: false,
        toolbox: 'none',
        legend: {
          data: [arr?.[1]],
        },
        xAxis: [
          {
            name: '',
            data: date,
            boundaryGap: true
          }
        ],
        yAxis: [
          {
            type: 'value',
            position: 'left',
            axisLabel: {
              formatter: '{value}'
            },
          }
        ],
        series: [series?.[1]],
        tooltip: {
          formatter: function (obj) {
            let value = `<div style="font-size:14px;">` + obj?.[0].axisValue + `</div>`;
            for (let i = 0; i < obj.length; i++) {
              value +=
                `<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
                `<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
                obj?.[i].color +
                `;"></div>` +
                `<div style="font-family: PingFang SC;">` +
                obj?.[i].seriesName +
                '</div></div>' +
                `<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
                (Number(obj?.[i].value).toFixed(4) + '亿') +
                '</div>' +
                `</div>`;
            }
            return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
          }
        },
      });
    },
  },
  mounted () {

  },
}
</script>

<style lang="scss" scoped>
@import '../../tkdesign';

.border_table_header {
	padding-bottom: 16px;
	border-bottom: 1px solid #ccc;

	.border_table_header_title {
		display: flex;
		align-items: center;

		.block {
			width: 6px;
			height: 20px;
			border-radius: 35px;
			background-color: #4096ff;
			margin-right: 16px;
		}

		.question {
			margin-left: 3px;
		}
	}

	.border_table_header_filter {
		display: flex;
		align-items: center;
		font-size: 14px;

		.border_table_header_radio {
			display: flex;
			align-items: center;
		}

		.border_table_header_select {
			margin-left: 16px;
		}

		.border_table_header_upload {
			width: 32px;
			line-height: 30px;
			border-radius: 4px;
			border: 1px solid #d9d9d9;
			text-align: center;
		}
	}
}

.search-security {
	width: 210px;
	margin-right: 16px;
}
</style>
