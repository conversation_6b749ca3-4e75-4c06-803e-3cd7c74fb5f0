<template>
	<div id="app">
		<keep-alive>
			<router-view v-if="$route.meta.keepAlive" :key="$route.fullPath"></router-view>
		</keep-alive>
		<router-view v-if="!$route.meta.keepAlive"></router-view>
	</div>
</template>
<script>
import '@/assets/css/main.css';
import '@/assets/css/icons/iconfont.css';
import '@/assets/css/color-dark.css'; /*深色主题*/
import axios from '@/api/index.js';
export default {
	data() {
		return {};
	},
	created() {
		let that = this;
		if (location.search && location.search.indexOf('jgtappkey=') >= 0) {
			localStorage.removeItem('token');
			localStorage.removeItem('retoken');
			let params = { signature: location.search.split('jgtappkey=')[1] };
			axios
				.post(that.$baseUrl + '/VerificationUser/', params)
				.then((res) => {
					//埋点成功
					if (res.data) {
						////console.log(res.data)
						that.$store.dispatch('changetoken', res.data.access);
						that.$store.dispatch('changerefreshtoken', res.data.refresh);
						that.$store.dispatch('changeusername', res.data.username);
						that.$store.dispatch('changeuserid', res.data.user_id);
						that.$store.dispatch('changeuserrole', res.data.roles);
						that.$store.commit('setUserType', res.data.type);
						that.$router.push('/');
					} else {
						that.$message('登陆失败');
					}
				})
				.catch((err) => {
					//  		//console.log(err)
					if (err.data.detail && err.data.detail == '没有提供正确的登录信息') {
						that.$message('没有提供正确的登录信息');
					} else {
						that.$message('账号密码错误');
					}
				});
		}
	}
};
</script>

<style lang="scss">
.flex_between {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.flex_around {
	display: flex;
	justify-content: space-around;
	align-items: center;
}
.flex_start {
	display: flex;
	justify-content: start;
	align-items: center;
}
.flex_center {
	display: flex;
	justify-content: center;
	align-items: center;
}
.flex_end {
	display: flex;
	justify-content: flex-end;
	align-items: center;
}
$margin-sizes: (
	'4': 4px,
	'5': 5px,
	'8': 8px,
	'10': 10px,
	'12': 12px,
	'14': 14px,
	'16': 16px,
	'20': 20px,
	'24': 24px,
	'40': 40px
);
$padding-sizes: (
	'4': 4px,
	'5': 5px,
	'8': 8px,
	'10': 10px,
	'12': 12px,
	'14': 14px,
	'16': 16px,
	'20': 20px,
	'24': 24px,
	'40': 40px
);
$font-sizes: (
	'12': 12px,
	'14': 14px,
	'16': 16px,
	'18': 18px,
	'20': 20px,
	'24': 24px,
	'40': 40px
);

@each $name, $size in $font-sizes {
	.fs-#{$name} {
		font-size: $size !important;
	}
}
// 生成动态边距类
@each $name, $size in $padding-sizes {
	.p-#{$name} {
		padding: $size !important;
	}
	.pt-#{$name} {
		padding-top: $size !important;
	}
	.pr-#{$name} {
		padding-right: $size !important;
	}
	.pb-#{$name} {
		padding-bottom: $size !important;
	}
	.pl-#{$name} {
		padding-left: $size !important;
	}
	.px-#{$name} {
		padding-right: $size !important;
		padding-left: $size !important;
	}
	.py-#{$name} {
		padding-top: $size !important;
		padding-bottom: $size !important;
	}
}
@each $name, $size in $margin-sizes {
	.m-#{$name} {
		margin: $size !important;
	}
	.mt-#{$name} {
		margin-top: $size !important;
	}
	.mr-#{$name} {
		margin-right: $size !important;
	}
	.mb-#{$name} {
		margin-bottom: $size !important;
	}
	.ml-#{$name} {
		margin-left: $size !important;
	}
	.mx-#{$name} {
		margin-right: $size !important;
		margin-left: $size !important;
	}
	.my-#{$name} {
		margin-top: $size !important;
		margin-bottom: $size !important;
	}
}
.flex_card {
	width: 100%;
	display: flex;
	justify-content: space-between;
	align-items: center;
	flex-wrap: wrap;
}
.flex_card .card_header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	position: relative;
	padding: 16px 0;
}
.flex_card > .small_template {
	flex: 1;
	// max-width: 830px;
	min-width: 610px;
	height: 402px;
	// padding: 0 20px 20px 20px;
	// margin: 12px;
	margin: 0 16px 16px 0;
	background: #ffffff;
	border-radius: 4px;
	border: 1px solid #d4d8e5;
	/* 主模块 */
	box-shadow: 0px 1px 2px 0px rgba(0, 0, 18, 0.1);
}
.flex_card > .small_template > div {
	padding: 0 20px 20px 20px;
}
.flex_card > .big_template {
	height: auto;
	width: 100%;
	flex-basis: 100%;
	min-width: 1236px;
	max-width: 1684px;
	/* height: 390px; */
	// padding: 0 20px 20px 20px;
	margin: 0 16px 16px 0;
	// margin: 12px;
	background: #ffffff;
	border-radius: 4px;
	border: 1px solid #d4d8e5;

	/* 主模块 */
	box-shadow: 0px 1px 2px 0px rgba(0, 0, 18, 0.1);
}
.flex_card > .big_template > div {
	padding: 0 20px 20px 20px;
}
.flex_card > .small_template .chart_title {
	position: absolute;
	left: 24px;
	top: 16px;
	font-family: 'PingFang';
	font-style: normal;
	font-weight: 500;
	font-size: 16px;
	line-height: 24px;
	color: rgba(0, 0, 0, 0.85);
}
.flex_card > .small_template .title {
	font-family: 'PingFang SC';
}
.flex_card > div .title {
	font-family: 'PingFang';
	font-style: normal;
	font-weight: 500;
	font-size: 16px;
	line-height: 24px;
	color: rgba(0, 0, 0, 0.85);
}
.manager_info_title {
	font-family: 'PingFang';
	font-style: normal;
	font-weight: 500;
	font-size: 18px;
	line-height: 26px;
	color: rgba(0, 0, 0, 0.85);
	margin-left: 12px;
}
// .chart_one {
// 	min-width: 1684px;
// 	max-width: 1684px;
// 	/* height: 390px; */
// 	padding: 0 20px 20px 20px;
// 	margin: 12px;
// 	background: #ffffff;
// 	border-radius: 4px;
// 	border: 1px solid #d4d8e5;

// 	/* 主模块 */
// 	box-shadow: 0px 1px 2px 0px rgba(0, 0, 18, 0.1);
// }
.combination .chart_one {
	min-width: 1152px;
	/* height: 390px; */
	padding: 0 24px 24px 24px;
	margin: 12px;
	background: #ffffff;
	box-shadow: 0px 5px 12px 4px rgba(0, 0, 18, 0.02);
	border-radius: 4px;
}
.emptyClass .title {
	font-family: 'PingFang';
	font-style: normal;
	font-weight: 500;
	font-size: 16px;
	line-height: 24px;
	color: rgba(0, 0, 0, 0.85);
	background: #ffffff;
}
.chart_one .title {
	font-family: 'PingFang';
	font-style: normal;
	font-weight: 500;
	font-size: 16px;
	line-height: 24px;
	color: rgba(0, 0, 0, 0.85);
	background: #ffffff;
}

div .charts_one_class {
	// height: 282px;
	position: relative;
	page-break-inside: avoid;
}
.charts_fill_class .charts_one_class {
	width: 100%;
}
.charts_fill_class .charts_two_class {
	width: 100%;
}
.charts_fill_class .charts_analysis_class {
	width: 100%;
}
// charts外层div,使用flex将图居中显示
div .charts_center_class {
	width: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
}
div .charts_two_class {
	width: 564px;
	height: 210px;
	position: relative;
	page-break-inside: avoid;
}
div .charts_analysis_class {
	// width: 100%;
	height: 280px;
	position: relative;
	page-break-inside: avoid;
}

.analysis_main {
	background: #f9f5f9;
	padding: 12px;
}
.analysis_main_company {
	background: #f9f5f9;
	padding: 24px;
}
.backbut {
	position: absolute;
	right: 5px;
	bottom: 10px;
	top: 10px;
	margin: auto;
}
.backbuts {
	position: absolute;
	right: 110px;
	bottom: 10px;
	top: 10px;
	margin: auto;
}
div .type_menu {
	position: -webkit-sticky;
	position: sticky !important;
	top: 0px;
	z-index: 100;
	// background: #f9f5f9 !important;
	margin: 12px;
}
div .drag_template_list_type_menu {
	display: flex;
	overflow: scroll;
}
div .drag_template_list_type_menu::-webkit-scrollbar {
	height: 0;
}
.main {
	width: 100%;
	height: 100%;
}
.fixed-height-short {
	height: 250px;
}
.flex_basic_info {
	flex: 1;
	min-width: 564px;
	max-width: 564px;
	background: #ffffff;
	margin: 12px;
}
.flex_return {
	flex: 2;
	width: 100%;
	min-width: 564px;
	padding: 16px 40px 24px 24px;
	background: #ffffff;
	margin: 12px;
}
</style>
