<template>
  <div>
    <div class="chart_one">
      <div
        style="display: flex; align-items: center; justify-content: space-between; position: relative"
      >
        <div class="title" style="margin-bottom: 24px">持仓债券分析</div>
        <el-button icon="el-icon-document-delete" @click="exportExcel">导出Excel</el-button>
      </div>
      <el-table
        class="fund-analysis-table"
        style="width: 99%"
        :data="datatable"
        v-loading="bondTopTenLoading"
        max-height="400px"
        :default-sort="{ prop: 'creditrating', order: 'descending' }"
      >
        <el-table-column
          prop="bond_name"
          label="债券名称"
          :show-overflow-tooltip="true"
          align="gotoleft"
        ></el-table-column>
        <el-table-column prop="bond_code" label="债券代码" align="gotoleft"></el-table-column>
        <el-table-column prop="exchangenature" sortable label="个券类型" align="gotoleft"></el-table-column>
        <el-table-column prop="firstindustryname" sortable label="行业" align="gotoleft"></el-table-column>
        <el-table-column prop="creditrating" sortable label="个券评级" align="gotoleft"></el-table-column>
        <el-table-column prop="ratioinN" sortable label="占净资产比" align="gotoleft">
          <template slot-scope="{ row }">
            <span>{{ (parseFloat(row.ratioinN * 100) ? parseFloat(row.ratioinN * 100).toFixed(2) : '--') + '%' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="holdings" sortable label="持券数量(万)" align="gotoleft">
          <template slot-scope="{ row }">
            <span>{{ parseFloat(row.holdings).toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="value" sortable label="持仓市值(亿)" align="gotoleft">
          <template slot-scope="{ row }">
            <span>{{ parseFloat(row.value).toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="info.type == 'cbond' || info.type == 'bond'"
          prop="ratioinN"
          sortable
          label="转股溢价率"
          align="gotoleft"
        >
          <template slot-scope="{ row }">
            <span>{{ parseFloat(row.ratioinN).toFixed(2) + '%' }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="info.type == 'cbond' || info.type == 'bond'"
          prop="ratioinN"
          sortable
          label="转债溢价率"
          align="gotoleft"
        >
          <template slot-scope="{ row }">
            <span>{{ parseFloat(row.ratioinN).toFixed(2) + '%' }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="chart_one mt-16">
      <div class="flex_card">
        <div
          class="mr-16 mb-16 pt-20"
          style="flex:1;border-radius:5px;border:1px solid #d4d8e5;box-shadow: none"
        >
          <div class="charts_center_class">
            <v-chart
              ref="equityStockPositionAnalysistype"
              v-loading="bondTopTenLoading"
              element-loading-text="暂无数据"
              element-loading-spinner="el-icon-document-delete"
              element-loading-background="rgba(239, 239, 239, 0.5)"
              class="charts_analysis_class"
              autoresize
              :options="optiontype"
            ></v-chart>
          </div>
        </div>
        <div
          class="mr-16 mb-16 pt-20"
          style="flex:1;border-radius:5px;border:1px solid #d4d8e5;box-shadow: none"
        >
          <div class="charts_center_class">
            <v-chart
              ref="equityStockPositionAnalysisindustry"
              v-loading="bondTopTenLoading"
              element-loading-text="暂无数据"
              element-loading-spinner="el-icon-document-delete"
              element-loading-background="rgba(239, 239, 239, 0.5)"
              class="charts_analysis_class"
              autoresize
              :options="optionindustry"
            ></v-chart>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { exportTitle, exportTable, exportChart } from "@/utils/exportWord.js";
import { filter_json_to_excel } from "@/utils/exportExcel.js";
// 持仓债券分析
import VCharts from "vue-echarts";

export default {
  name: "positionBondAnalysis",
  components: { "v-chart": VCharts },
  data() {
    return {
      bondTopTenLoading: true,
      datatable: [],
      info: {},
      industrypeizhi: [],
      optiontype: {},
      optionindustry: {},
      color: [
        "#4096ff",
        "#4096ff",
        "#7388A9",
        "#6F80DD",
        "#6C96F2",
        "#FD6865",
        "#83D6AE",
        "#88C9E9",
        "#ED589D",
        "#FA541C"
      ]
    };
  },
  methods: {
    // 获取数据
    async getData(data, info) {
      this.info = info;
      this.bondTopTenLoading = false;
      this.datatable = data.data.map(item => {
        return {
          ...item,
          holdings: parseFloat(item.holdings / 10 ** 4),
          value: parseFloat(item.value / 10 ** 8),
          ratioinN: item.ratioinN / 100
        };
      });
      this.getIndustryData(data);
    },
    getIndustryData(data) {
      let type = [];
      let industry = [];
      let typeWeight = 0;
      if (data?.bond?.length && typeof data?.bond == "object") {
        data?.bond.map(item => {
          typeWeight = typeWeight + item.ratioinN;
          // 个券类型
          let typeIndex = type.findIndex(obj => {
            return obj.name == item.exchangenature;
          });
          if (typeIndex == -1) {
            type.push({
              name: item.exchangenature,
              value: item.ratioinN
            });
          } else {
            type[typeIndex].value = type[typeIndex].value + item.ratioinN;
          }
        });
      }

      let industryWeight = 0;
      if (data?.industry?.length && typeof data?.industry == "object") {
        data?.industry.map(item => {
          industryWeight = industryWeight + item.ratioinN;
          // 个券行业
          let industryIndex = industry.findIndex(obj => {
            return obj.name == item.firstindustryname;
          });
          if (industryIndex == -1) {
            industry.push({
              name: item.firstindustryname,
              value: item.ratioinN
            });
          } else {
            industry[industryIndex].value =
              industry[industryIndex].value + item.ratioinN;
          }
        });
      }
      //   type.unshift({
      //     name: "未披露个券",
      //     value: 100 - typeWeight,
      //     itemStyle: {
      //       color: "#BFBFBF"
      //     }
      //   });
      //   industry.unshift({
      //     name: "未披露个券",
      //     value: 100 - industryWeight,
      //     itemStyle: {
      //       color: "#BFBFBF"
      //     }
      //   });
      this.optiontype = this.returnPieChart(type);
      this.optionindustry = this.returnPieChart(industry);
    },
    returnPieChart(data) {
      return {
        color: this.color,
        tooltip: {
          trigger: "item",
          formatter: function(val) {
            return (
              val?.marker + " " + val?.name + " " + val?.value?.toFixed(2) + "%"
            );
          }
        },
        legend: {
          orient: "vertical",
          left: "left",
          type: "scroll",
          data: data
            ?.sort((a, b) => {
              return b.value - a.value;
            })
            ?.map(item => {
              return item.name;
            }),
          pageIcons: {
            horizontal: [
              "path://M11.7487 6.92214L6.30673 0.634973C6.15096 0.455009 5.85102 0.455009 5.69359 0.634973L0.251579 6.92214C0.049409 7.15658 0.231693 7.5 0.558148 7.5L11.4422 7.5C11.7686 7.5 11.9509 7.15658 11.7487 6.92214Z",
              "path://M0.251255 1.07786L5.69327 7.36503C5.84904 7.54499 6.14898 7.54499 6.30641 7.36503L11.7484 1.07786C11.9506 0.843416 11.7683 0.499999 11.4419 0.5L0.557824 0.5C0.231369 0.5 0.0490849 0.843417 0.251255 1.07786Z"
            ]
          }
        },
        toolbox: {
          feature: {
            saveAsImage: { pixelRatio: 3 }
          },
          top: -4,
          width: 104
        },
        series: {
          type: "pie",
          radius: ["30%", "70%"],
          center: ["63%", "50%"],
          emphasis: {
            focus: "ancestor",
            label: {
              show: true,
              fontSize: "14",
              fontWeight: "bold"
            }
          },
          data,
          // radius: [0, '90%'],
          label: {
            show: false,
            position: "center"
          }
        }
      };
    },
    exportExcel() {
      let list = [
        {
          label: "债券名称",
          value: "bond_name"
        },
        {
          label: "债券代码",
          value: "bond_code"
        },
        {
          label: "个券类型",
          value: "exchangenature"
        },
        {
          label: "行业",
          value: "firstindustryname"
        },

        {
          label: "个券评级",
          value: "creditrating"
        },
        {
          label: "占总净值比例",
          value: "ratioinN",
          format: "fix2b"
        },
        {
          label: "持券数量(万)",
          value: "holdings",
          format: "fix2b"
        },
        {
          label: "持仓市值(亿)",
          value: "value",
          format: "fix2b"
        }
      ];
      filter_json_to_excel(list, this.datatable, "持仓债券分析");
    },
    createPrintWord() {
      let list = [
        {
          label: "债券名称",
          value: "bond_name"
        },
        {
          label: "债券代码",
          value: "bond_code"
        },
        {
          label: "个券类型",
          value: "exchangenature"
        },
        {
          label: "行业",
          value: "firstindustryname"
        },

        {
          label: "个券评级",
          value: "creditrating"
        },
        {
          label: "占总资产比",
          value: "ratioinN",
          format: "fix2b"
        },
        {
          label: "持券数量(万)",
          value: "holdings",
          format: "fix2b"
        },
        {
          label: "持仓市值(亿)",
          value: "value",
          format: "fix2b"
        }
      ];
      this.$refs["equityStockPositionAnalysistype"].mergeOptions({
        toolbox: { show: false }
      });
      this.$refs["equityStockPositionAnalysisindustry"].mergeOptions({
        toolbox: { show: false }
      });
      let height = this.$refs["equityStockPositionAnalysistype"]?.$el
        .clientHeight;
      let width = this.$refs["equityStockPositionAnalysistype"]?.$el
        .clientWidth;
      let chart1 = this.$refs["equityStockPositionAnalysistype"].getDataURL({
        type: "png",
        pixelRatio: 3,
        backgroundColor: "#fff"
      });
      let chart2 = this.$refs["equityStockPositionAnalysisindustry"].getDataURL(
        {
          type: "png",
          pixelRatio: 3,
          backgroundColor: "#fff"
        }
      );
      this.$refs["equityStockPositionAnalysistype"].mergeOptions({
        toolbox: { show: true }
      });
      this.$refs["equityStockPositionAnalysisindustry"].mergeOptions({
        toolbox: { show: true }
      });
      return [
        ...exportTitle("持仓债券分析"),
        ...exportTable(
          list,
          this.datatable
            .sort((a, b) => {
              return b.ratioinN - a.ratioinN;
            })
            .slice(0, 10)
        ),
        ...exportTitle("持仓债券类型"),
        ...exportChart(chart1, { width, height }),
        ...exportTitle("持仓债券行业"),
        ...exportChart(chart2, { width, height })
      ];
    }
  }
};
</script>

<style></style>
