<template>
  <div class="box_Board">
    <div class="header_box">
      <span class="header_inactive">投后&nbsp;/&nbsp;映射管理&nbsp;/&nbsp;</span>
      产品映射管理
    </div>
    <div class="border_table">
      <el-tabs v-model="activeName"
               @tab-click="changeTab">
        <el-tab-pane label="委托账户"
                     name="1">
          <!-- 头部区域 -->
          <div class="border_table_header">
            <!-- 左侧标题区域 -->
            <div class="border_table_header_title">产品映射管理列表</div>
            <!-- 右侧按钮区域 -->
            <div class="border_table_header_button">
              <!-- 提示是否删除 -->
              <div v-show="promptMessage"
                   class="prompt-message">
                <div class="block" />
                <div><i class="el-icon-warning"></i>确认删除内容吗</div>
                <el-button class="cancel"
                           @click="promptMessage = false">取消</el-button>
                <el-button class="warning"
                           @click="closePromptMessage">确认</el-button>
              </div>
              <el-button icon="el-icon-plus"
                         type="primary"
                         @click="addRow">
                新增
              </el-button>
              <el-button class="ml"
                         type="primary"
                         @click="showDialog = true">
                上传映射表
              </el-button>
              <el-button class="button-del ml"
                         @click="promptMessage = !promptMessage">
                删除原有映射
              </el-button>
              <el-button class="ml"
                         @click="showRecord">修改记录</el-button>
            </div>
          </div>
          <!-- 查询区域 -->
          <el-form class="search-box search-area">
            <el-form-item class="search-box_item">
              GP3组合代码：
              <el-select v-model="formData.gp3"
                         placeholder="请输入"
                         clearable
                         filterable
                         remote
                         :remote-method="getCodeOptions"
                         style="width: 240px">
                <el-option v-for="item in codeOptions"
                           :value="item"
                           :label="item"
                           :key="item"
                           style="width: 240px" />
              </el-select>
            </el-form-item>
            <el-form-item class="search-box_item">
              一级策略分类：
              <el-select v-model="formData.strategy1"
                         allow-create
                         clearable
                         filterable
                         remote
                         @focus="getStrategy1List()"
                         :remote-method="getStrategy1List"
                         placeholder="请选择"
                         style="width: 240px">
                <el-option v-for="item in strategy1Options"
                           :key="item"
                           :label="item"
                           :value="item"
                           style="width: 240px">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item class="search-box_item">
              二级策略分类：
              <el-select v-model="formData.strategy2"
                         allow-create
                         clearable
                         filterable
                         remote
                         @focus="getStrategy2List()"
                         :remote-method="getStrategy2List"
                         placeholder="请选择"
                         style="width: 240px">
                <el-option v-for="item in strategy2Options"
                           :key="item"
                           :label="item"
                           :value="item"
                           style="width: 240px">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item class="search-box_item">
              一级管理人:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              <el-select v-model="formData.manager"
                         allow-create
                         clearable
                         filterable
                         placeholder="请选择"
                         style="width: 240px">
                <el-option v-for="item in manager1Options"
                           :key="item"
                           :label="item"
                           :value="item"
                           style="width: 240px">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item class="search-box_item">
              <el-radio-group v-model="formData.nullType">
                <el-radio :label="false">显示全部</el-radio>
                <el-radio :label="true">仅显示未设置分类的产品</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item class="search-box_item" />
            <el-form-item>
              <el-button type="primary"
                         @click="getProductList(formData)">查询</el-button>
              <el-button type=""
                         @click="reset">重置</el-button>
            </el-form-item>
          </el-form>
          <!-- 表格区域 -->
          <el-table v-loading="loading.tableLoading"
                    :data="tableData"
                    border
                    stripe>
            <el-table-column align="gotoleft"
                             label="GP3组合代码"
                             prop="gp3Code">
              <template slot-scope="scope">
                <div v-if="scope.row.addFlag">
                  <el-select v-model="scope.row.gp3Code"
                             placeholder="请选择"
                             :disabled="scope.row.gp3Code"
                             clearable
                             allow-create
                             filterable
                             remote
                             @focus="getCodeOptions(scope.row.gp3Code)"
                             :remote-method="getCodeOptions">
                    <el-option v-for="item in codeOptions"
                               :value="item"
                               :label="item"
                               :key="item" />
                  </el-select>
                </div>
                <div v-else>
                  {{ scope.row.gp3Code }}
                </div>
              </template>
            </el-table-column>
            <el-table-column align="gotoleft"
                             label="一级策略分类"
                             prop="">
              <template slot-scope="scope">
                <div v-if="scope.row.addFlag">
                  <el-select v-model="scope.row.strategy1"
                             allow-create
                             clearable
                             filterable
                             remote
                             @focus="getStrategy1List()"
                             :remote-method="getStrategy1List"
                             placeholder="请选择">
                    <el-option v-for="item in strategy1Options"
                               :key="item"
                               :label="item"
                               :value="item">
                    </el-option>
                  </el-select>
                </div>
                <div v-else
                     :style="scope.row.strategy1 ? '' : 'color:red;'">
                  {{ scope.row.strategy1 ? scope.row.strategy1 : "未定义" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column align="gotoleft"
                             label="二级策略分类"
                             prop="strategy2">
              <template slot-scope="scope">
                <div v-if="scope.row.addFlag">
                  <el-select @focus="getStrategy2List()"
                             v-model="scope.row.strategy2"
                             allow-create
                             clearable
                             filterable
                             remote
                             :remote-method="getStrategy2List"
                             placeholder="请选择">
                    <el-option v-for="item in strategy2Options"
                               :key="item"
                               :label="item"
                               :value="item">
                    </el-option>
                  </el-select>
                </div>
                <div v-else
                     :style="scope.row.strategy2 ? '' : 'color:red;'">
                  {{ scope.row.strategy2 ? scope.row.strategy2 : "未定义" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column align="gotoleft"
                             label="一级管理人"
                             prop="">
              <template slot-scope="scope">
                <div v-if="scope.row.addFlag">
                  <el-select v-model="scope.row.manager1"
                             allow-create
                             clearable
                             filterable
                             placeholder="请选择">
                    <el-option v-for="item in manager1Options"
                               :key="item"
                               :label="item"
                               :value="item">
                    </el-option>
                  </el-select>
                </div>
                <div v-else
                     :style="scope.row.manager1 ? '' : 'color:red;'">
                  {{ scope.row.manager1 ? scope.row.manager1 : "未定义" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope">
                <div v-if="!scope.row.addFlag"
                     class="flex">
                  <el-button class="button-color"
                             type="text"
                             @click="edit(scope.row, scope.$index)">编辑
                  </el-button>
                  <el-button class="button-color"
                             type="text"
                             @click="deleteProduct(scope.row.gp3Code)">
                    删除
                  </el-button>
                </div>
                <div v-else
                     class="flex">
                  <el-button class="button-color"
                             type="text"
                             @click="saveProduct(scope.row)">保存
                  </el-button>
                  <el-button type="text"
                             @click="cancel(scope.row, scope.$index)">取消
                  </el-button>
                </div>
              </template>
            </el-table-column>
            <template slot="empty">
              <el-empty :image-size="160"></el-empty>
            </template>
          </el-table>
          <!-- 分页器 -->
          <div class="pagination_board">
            <el-pagination :current-page.sync="pagination.pageIndex"
                           :page-size="pagination.pageSize"
                           :total="pagination.total"
                           background
                           layout="total, sizes, prev, pager, next"
                           @size-change="sizeChange"
                           @current-change="currentChange" />
          </div>
        </el-tab-pane>
        <el-tab-pane label="直投账户"
                     name="2">
          <!-- 头部区域 -->
          <div class="border_table_header">
            <!-- 左侧标题区域 -->
            <div class="border_table_header_title">产品映射管理列表</div>
            <!-- 右侧按钮区域 -->
            <div class="border_table_header_button">
              <!-- 提示是否删除 -->
              <div v-show="promptMessage"
                   class="prompt-message">
                <div class="block" />
                <div><i class="el-icon-warning"></i>确认删除内容吗</div>
                <el-button class="cancel"
                           @click="promptMessage = false">取消</el-button>
                <el-button class="warning"
                           @click="closePromptMessage">确认</el-button>
              </div>
              <el-button icon="el-icon-plus"
                         type="primary"
                         @click="addRow">
                新增
              </el-button>
              <el-button class="ml"
                         type="primary"
                         @click="showDialog = true">
                上传映射表
              </el-button>
              <el-button class="button-del ml"
                         @click="promptMessage = !promptMessage">
                删除原有映射
              </el-button>
              <el-button class="ml"
                         @click="showRecord">修改记录</el-button>
            </div>
          </div>
          <!-- 查询区域 -->
          <el-form class="search-box search-area">
            <el-form-item class="search-box_item">
              GP3组合代码：
              <el-select v-model="formData.gp3"
                         placeholder="请输入"
                         clearable
                         filterable
                         remote
                         :remote-method="getCodeOptions">
                <el-option v-for="item in codeOptions"
                           :value="item"
                           :label="item"
                           :key="item" />
              </el-select>
            </el-form-item>
            <el-form-item class="search-box_item">
              一级策略分类：
              <el-select v-model="formData.strategy1"
                         allow-create
                         clearable
                         filterable
                         remote
                         @focus="getStrategy1List()"
                         :remote-method="getStrategy1List"
                         placeholder="请选择">
                <el-option v-for="item in strategy1Options"
                           :key="item"
                           :label="item"
                           :value="item">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item class="search-box_item">
              二级策略分类：
              <el-select v-model="formData.strategy2"
                         allow-create
                         clearable
                         filterable
                         remote
                         @focus="getStrategy2List()"
                         :remote-method="getStrategy2List"
                         placeholder="请选择">
                <el-option v-for="item in strategy2Options"
                           :key="item"
                           :label="item"
                           :value="item">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item class="search-box_item">
              一级管理人:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              <el-select v-model="formData.manager"
                         allow-create
                         clearable
                         filterable
                         placeholder="请选择">
                <el-option v-for="item in manager1Options"
                           :key="item"
                           :label="item"
                           :value="item">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item class="search-box_item">
              <el-radio-group v-model="formData.nullType">
                <el-radio :label="false">显示全部</el-radio>
                <el-radio :label="true">仅显示未设置分类的产品</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item class="search-box_item" />
            <el-form-item>
              <el-button type="primary"
                         @click="getProductList(formData)">查询</el-button>
              <el-button type=""
                         @click="reset">重置</el-button>
            </el-form-item>
          </el-form>
          <!-- 表格区域 -->
          <el-table v-loading="loading.tableLoading"
                    :data="tableData"
                    border
                    stripe>
            <el-table-column align="gotoleft"
                             label="GP3组合代码"
                             prop="gp3Code">
              <template slot-scope="scope">
                <div v-if="scope.row.addFlag">
                  <el-select v-model="scope.row.gp3Code"
                             placeholder="请选择"
                             :disabled="scope.row.gp3Code"
                             clearable
                             allow-create
                             filterable
                             remote
                             @focus="getCodeOptions(scope.row.gp3Code)"
                             :remote-method="getCodeOptions">
                    <el-option v-for="item in codeOptions"
                               :value="item"
                               :label="item"
                               :key="item" />
                  </el-select>
                </div>
                <div v-else>
                  {{ scope.row.gp3Code }}
                </div>
              </template>
            </el-table-column>
            <el-table-column align="gotoleft"
                             label="一级策略分类"
                             prop="">
              <template slot-scope="scope">
                <div v-if="scope.row.addFlag">
                  <el-select v-model="scope.row.strategy1"
                             allow-create
                             clearable
                             filterable
                             remote
                             @focus="getStrategy1List()"
                             :remote-method="getStrategy1List"
                             placeholder="请选择">
                    <el-option v-for="item in strategy1Options"
                               :key="item"
                               :label="item"
                               :value="item">
                    </el-option>
                  </el-select>
                </div>
                <div v-else
                     :style="scope.row.strategy1 ? '' : 'color:red;'">
                  {{ scope.row.strategy1 ? scope.row.strategy1 : "未定义" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column align="gotoleft"
                             label="二级策略分类"
                             prop="strategy2">
              <template slot-scope="scope">
                <div v-if="scope.row.addFlag">
                  <el-select @focus="getStrategy2List()"
                             v-model="scope.row.strategy2"
                             allow-create
                             clearable
                             filterable
                             remote
                             :remote-method="getStrategy2List"
                             placeholder="请选择">
                    <el-option v-for="item in strategy2Options"
                               :key="item"
                               :label="item"
                               :value="item">
                    </el-option>
                  </el-select>
                </div>
                <div v-else
                     :style="scope.row.strategy2 ? '' : 'color:red;'">
                  {{ scope.row.strategy2 ? scope.row.strategy2 : "未定义" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column align="gotoleft"
                             label="一级管理人"
                             prop="">
              <template slot-scope="scope">
                <div v-if="scope.row.addFlag">
                  <el-select v-model="scope.row.manager1"
                             allow-create
                             clearable
                             filterable
                             placeholder="请选择">
                    <el-option v-for="item in manager1Options"
                               :key="item"
                               :label="item"
                               :value="item">
                    </el-option>
                  </el-select>
                </div>
                <div v-else
                     :style="scope.row.manager1 ? '' : 'color:red;'">
                  {{ scope.row.manager1 ? scope.row.manager1 : "未定义" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope">
                <div v-if="!scope.row.addFlag"
                     class="flex">
                  <el-button class="button-color"
                             type="text"
                             @click="edit(scope.row, scope.$index)">编辑
                  </el-button>
                  <el-button class="button-color"
                             type="text"
                             @click="deleteProduct(scope.row.gp3Code)">
                    删除
                  </el-button>
                </div>
                <div v-else
                     class="flex">
                  <el-button class="button-color"
                             type="text"
                             @click="saveProduct(scope.row)">保存
                  </el-button>
                  <el-button type="text"
                             @click="cancel(scope.row, scope.$index)">取消
                  </el-button>
                </div>
              </template>
            </el-table-column>
            <template slot="empty">
              <el-empty :image-size="160"></el-empty>
            </template>
          </el-table>
          <!-- 分页器 -->
          <div class="pagination_board">
            <el-pagination :current-page.sync="pagination.pageIndex"
                           :page-size="pagination.pageSize"
                           :page-sizes="[10, 20, 30, 40, 50, 100, 1000, 10000]"
                           :total="pagination.total"
                           background
                           layout="total, sizes, prev, pager, next"
                           @size-change="sizeChange"
                           @current-change="currentChange" />
          </div>
        </el-tab-pane>
        <el-tab-pane label="投连账户"
                     name="3">
          <!-- 头部区域 -->
          <div class="border_table_header">
            <!-- 左侧标题区域 -->
            <div class="border_table_header_title">产品映射管理列表</div>
            <!-- 右侧按钮区域 -->
            <div class="border_table_header_button">
              <!-- 提示是否删除 -->
              <div v-show="promptMessage"
                   class="prompt-message">
                <div class="block" />
                <div><i class="el-icon-warning"></i>确认删除内容吗</div>
                <el-button class="cancel"
                           @click="promptMessage = false">取消</el-button>
                <el-button class="warning"
                           @click="closePromptMessage">确认</el-button>
              </div>
              <el-button icon="el-icon-plus"
                         type="primary"
                         @click="addRow">
                新增
              </el-button>
              <el-button class="ml"
                         type="primary"
                         @click="showDialog = true">
                上传映射表
              </el-button>
              <el-button class="button-del ml"
                         @click="promptMessage = !promptMessage">
                删除原有映射
              </el-button>
              <el-button class="ml"
                         @click="showRecord">修改记录</el-button>
            </div>
          </div>
          <!-- 查询区域 -->
          <el-form class="search-box search-area">
            <el-form-item class="search-box_item">
              GP3代码：
              <el-select v-model="formData.gp3"
                         placeholder="请输入"
                         clearable
                         filterable
                         remote
                         :remote-method="getCodeOptions">
                <el-option v-for="item in codeOptions"
                           :value="item"
                           :label="item"
                           :key="item" />
              </el-select>
            </el-form-item>
            <el-form-item class="search-box_item">
              产品名称：
              <el-select v-model="formData.manager"
                         allow-create
                         clearable
                         filterable
                         remote
                         @focus="getStrategy1List()"
                         :remote-method="getStrategy1List"
                         placeholder="请选择">
                <el-option v-for="item in manager1Options"
                           :key="item"
                           :label="item"
                           :value="item">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item class="search-box_item">
              产品类型：
              <el-select v-model="formData.strategy1"
                         allow-create
                         clearable
                         filterable
                         remote
                         @focus="getStrategy2List()"
                         :remote-method="getStrategy2List"
                         placeholder="请选择">
                <el-option v-for="item in strategy1Options"
                           :key="item"
                           :label="item"
                           :value="item">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item class="search-box_item">
              组合类型：
              <el-select v-model="formData.strategy2"
                         allow-create
                         clearable
                         filterable
                         placeholder="请选择">
                <el-option v-for="item in strategy2Options"
                           :key="item"
                           :label="item"
                           :value="item">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item class="search-box_item">
              <el-radio-group v-model="formData.nullType">
                <el-radio :label="false">显示全部</el-radio>
                <el-radio :label="true">仅显示未设置分类的产品</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item class="search-box_item" />
            <el-form-item>
              <el-button type="primary"
                         @click="getProductList(formData)">查询</el-button>
              <el-button type=""
                         @click="reset">重置</el-button>
            </el-form-item>
          </el-form>
          <!-- 表格区域 -->
          <el-table v-loading="loading.tableLoading"
                    :data="tableData"
                    border
                    stripe>
            <el-table-column align="gotoleft"
                             label="GP3组合代码"
                             prop="gp3Code">
              <template slot-scope="scope">
                <div v-if="scope.row.addFlag">
                  <el-select v-model="scope.row.gp3Code"
                             placeholder="请选择"
                             :disabled="scope.row.gp3Code"
                             allow-create
                             clearable
                             filterable
                             remote
                             @focus="getCodeOptions(scope.row.gp3Code)"
                             :remote-method="getCodeOptions">
                    <el-option v-for="item in codeOptions"
                               :value="item"
                               :label="item"
                               :key="item" />
                  </el-select>
                </div>
                <div v-else>
                  {{ scope.row.gp3Code }}
                </div>
              </template>
            </el-table-column>
            <el-table-column align="gotoleft"
                             label="产品名称"
                             prop="">
              <template slot-scope="scope">
                <div v-if="scope.row.addFlag">
                  <el-select v-model="scope.row.manager1"
                             allow-create
                             clearable
                             filterable
                             placeholder="请选择">
                    <el-option v-for="item in manager1Options"
                               :key="item"
                               :label="item"
                               :value="item">
                    </el-option>
                  </el-select>
                </div>
                <div v-else
                     :style="scope.row.manager1 ? '' : 'color:red;'">
                  {{ scope.row.manager1 ? scope.row.manager1 : "未定义" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column align="gotoleft"
                             label="产品类型"
                             prop="">
              <template slot-scope="scope">
                <div v-if="scope.row.addFlag">
                  <el-select v-model="scope.row.strategy1"
                             allow-create
                             clearable
                             filterable
                             remote
                             @focus="getStrategy1List()"
                             :remote-method="getStrategy1List"
                             placeholder="请选择">
                    <el-option v-for="item in strategy1Options"
                               :key="item"
                               :label="item"
                               :value="item">
                    </el-option>
                  </el-select>
                </div>
                <div v-else
                     :style="scope.row.strategy1 ? '' : 'color:red;'">
                  {{ scope.row.strategy1 ? scope.row.strategy1 : "未定义" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column align="gotoleft"
                             label="组合类型"
                             prop="strategy2">
              <template slot-scope="scope">
                <div v-if="scope.row.addFlag">
                  <el-select @focus="getStrategy2List()"
                             v-model="scope.row.strategy2"
                             allow-create
                             clearable
                             filterable
                             remote
                             :remote-method="getStrategy2List"
                             placeholder="请选择">
                    <el-option v-for="item in strategy2Options"
                               :key="item"
                               :label="item"
                               :value="item">
                    </el-option>
                  </el-select>
                </div>
                <div v-else
                     :style="scope.row.strategy2 ? '' : 'color:red;'">
                  {{ scope.row.strategy2 ? scope.row.strategy2 : "未定义" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope">
                <div v-if="!scope.row.addFlag"
                     class="flex">
                  <el-button class="button-color"
                             type="text"
                             @click="edit(scope.row, scope.$index)">编辑
                  </el-button>
                  <el-button class="button-color"
                             type="text"
                             @click="deleteProduct(scope.row.gp3Code)">
                    删除
                  </el-button>
                </div>
                <div v-else
                     class="flex">
                  <el-button class="button-color"
                             type="text"
                             @click="saveProduct(scope.row)">保存
                  </el-button>
                  <el-button type="text"
                             @click="cancel(scope.row, scope.$index)">取消
                  </el-button>
                </div>
              </template>
            </el-table-column>
            <template slot="empty">
              <el-empty :image-size="160"></el-empty>
            </template>
          </el-table>
          <!-- 分页器 -->
          <div class="pagination_board">
            <el-pagination :current-page.sync="pagination.pageIndex"
                           :page-size="pagination.pageSize"
                           :total="pagination.total"
                           background
                           layout="total, sizes, prev, pager, next"
                           @size-change="sizeChange"
                           @current-change="currentChange" />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <!-- 上传映射表 -->
    <el-dialog :visible.sync="showDialog"
               title="上传映射表"
               width="900px">
      <ExcelPort :excelUrl="`product.xlsx`"
                 :path="`/cloud/api/taikang/product/upload`"
                 @refrshtable="uploadSuccess"
                 :data="{ type: '' }" />
    </el-dialog>
    <!-- 修改记录 -->
    <el-dialog :visible.sync="showDialogRecord"
               title="修改记录"
               width="1200px">
      <!-- 表格区域 -->
      <el-table v-loading="loading.dialogLoading"
                :data="tableDataRecord"
                height="400">
        <el-table-column align="gotoleft"
                         label="GP3组合代码"
                         prop="context.gp3Code"
                         min-width="100" />
        <el-table-column align="gotoleft"
                         label="修改人 ID"
                         prop="context.userId"
                         min-width="100" />
        <el-table-column align="gotoleft"
                         label="一级策略分类"
                         prop="context.strategy1"
                         min-width="100" />
        <el-table-column align="gotoleft"
                         label="二级策略分类"
                         prop="context.strategy2"
                         min-width="100" />
        <el-table-column align="gotoleft"
                         label="一级管理人"
                         prop="context.manager1"
                         min-width="100" />
        <el-table-column align="gotoleft"
                         label="操作类别"
                         prop="actionType"
                         min-width="100">
          <template slot-scope="scope">
            {{
              scope.row.actionType === 1
                ? "新增"
                : scope.row.actionType === 2
                ? "删除"
                : "修改"
            }}
          </template>
        </el-table-column>
        <el-table-column align="gotoleft"
                         label="修改时间"
                         prop="insertDate"
                         min-width="250" />
        <template slot="empty">
          <el-empty :image-size="160"></el-empty>
        </template>
      </el-table>
      <!-- 分页器 -->
      <div class="pagination_board">
        <el-pagination :current-page.sync="paginationRecord.pageIndex"
                       :page-size="paginationRecord.pageSize"
                       :total="paginationRecord.total"
                       background
                       layout="total, sizes, prev, pager, next"
                       @size-change="sizeChangeRecord"
                       @current-change="currentChangeRecord" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ExcelPort from "./component/map/alphaownpool.vue";
import {
  getManagerList,
  getProductList,
  getStrategy1List,
  getStrategy2List,
  deleteProduct,
  saveProduct,
  deleteAll,
} from "../../../api/pages/tkdesign/productController";
import { getRecordList } from "../../../api/pages/tkdesign/historyRecord";

export default {
  components: {
    ExcelPort,
  },
  data () {
    return {
      // 删除原有映射按钮提示显示与隐藏
      promptMessage: false,
      // el-tabs绑定的标签
      activeName: "2",
      // 搜索区域input绑定的数据
      formData: {
        gp3: "",
        strategy1: "",
        strategy2: "",
        manager: "",
        nullType: false,
      },

      tableData: [], // 页面表格数据源
      oldValue: {},
      tableDataRecord: [], // 修改记录表格数据源

      pagination: {
        pageIndex: 1, // 当前页码
        pageSize: 10, // 页面显示几条数据
        total: 0,
      },

      paginationRecord: {
        pageIndex: 1, // 当前页码
        pageSize: 10, // 页面显示几条数据
        total: 0,
      },

      showDialog: false, // 绑定上传黑名单的dialog
      showDialogRecord: false, // 绑定修改记录的dialog
      loading: {
        tableLoading: false,
        dialogLoading: false,
      },
      // 模糊查询的数据
      manager1Options: [],
      strategy1Options: [],
      strategy2Options: [],
      codeOptions: [],
    };
  },
  methods: {
    /**
     * 切换tab
     */
    changeTab () {
      this.pagination.pageSize = 10;
      this.pagination.pageIndex = 1;
      this.getProductList();
    },

    // 上传成功
    uploadSuccess () {
      this.showDialog = false;
      this.getProductList();
    },

    // 关闭删除提示
    closePromptMessage () {
      this.deleteAll();
      this.promptMessage = false;
    },
    // 每页条数改变时触发的回调
    sizeChange (value) {
      this.pagination.pageSize = value;
      this.getProductList(this.formData);
    },
    sizeChangeRecord (value) {
      this.paginationRecord.pageSize = value;
      this.getRecordList(this.formData);
    },
    // 当前页数改变时触发的回调
    currentChange (value) {
      this.pagination.pageIndex = value;
      this.getProductList(this.formData);
    },
    currentChangeRecord (value) {
      this.paginationRecord.pageIndex = value;
      this.getRecordList(this.formData);
    },

    /**
     * 处在编辑状态时，不允许对其他数据进行操作
     */
    ban () {
      let result = this.tableData.filter((v) => v.addFlag);
      if (result.length > 0) return true;
      else return false;
    },

    /**
     *新增
     */
    addRow () {
      //判断是否处于编辑状态 如果正在编辑则不允许点击新增按钮
      if (this.ban()) return;

      const row = {
        actionType: 1,
        addFlag: true,
      };
      this.tableData.unshift(row);
    },

    /**
     * 编辑
     * @param obj 该行的数据
     * @param number 该行的下标
     */
    edit (obj, number) {
      //判断是否处于编辑状态 如果正在编辑则不允许点击其他编辑按钮
      let result = this.tableData.filter((v) => v.addFlag);
      if (result.length > 0) return;
      //保存传过来的的对象，留着在用户点击取消的时候还原数据
      this.oldValue = JSON.parse(JSON.stringify(obj));
      //这里addFlag为true但是页面没有实现响应式
      obj.addFlag = true;
      //这里是为了解决addFlag不能实现响应式的问题 （数组重写）
      //将tableData里对应的该行数据删除，然后再把addFlag为true的obj添加到删除的位置
      this.tableData.splice(number, 1);
      this.tableData.splice(number, 0, obj);
    },

    /**
     * 取消
     * @param obj
     * @param number
     */
    cancel (obj, number) {
      if (obj.actionType === 1) {
        this.tableData.shift();
      } else {
        this.tableData.splice(number, 1);
        this.tableData.splice(number, 0, this.oldValue);
      }
    },
    /**
     *清除
     */
    clearFormData () {
      this.formData = {
        gp3: "",
        strategy1: "",
        strategy2: "",
        manager: "",
        nullType: false,
      };
    },
    /**
     * 重置
     */
    reset () {
      this.clearFormData();
      this.getProductList();
    },

    showRecord () {
      this.showDialogRecord = true;
      this.getRecordList();
    },

    /**
     * 获取产品映射列表
     */
    getProductList (data) {
      this.loading.tableLoading = true;
      let params = {
        ...data,
        current: this.pagination.pageIndex,
        pageSize: this.pagination.pageSize,
        accountProps: Number(this.activeName),
      };
      getProductList(params).then((res) => {
        this.loading.tableLoading = false;
        if (res.code === 200) {
          this.tableData = res.data;
          this.pagination.total = res.total;
          this.tableData.forEach((item) => {
            item.addFlag = false;
          });
        } else {
          this.tableData = [];
          this.pagination.total = 0;
        }
      });
    },

    /**
     * 获取GP3代码选择项
     */
    getCodeOptions (query) {
      let params = {
        current: 1,
        pageSize: 10,
        gp3: query,
        accountProps: Number(this.activeName),
      };
      getProductList(params).then((res) => {
        if (res.code === 200) {
          let array = [];
          res.data.forEach((item) => {
            if (item.gp3Code) {
              array.push(item.gp3Code);
            }
          });
          this.codeOptions = Array.from(new Set(array));
        } else {
          this.codeOptions = [];
        }
      });
    },

    /**
     * 获取全部管理人列表
     */
    getManagerList () {
      getManagerList().then((res) => {
        let array = [];
        if (res.code === 200) {
          array = res.data.filter((item) => {
            return item !== "";
          });
          this.manager1Options = array;
        } else this.manager1Options = [];
      });
    },

    /**
     * 获取全部一级策略分类列表
     */
    getStrategy1List (query) {
      if (query === undefined) {
        query = "";
      }
      getStrategy1List(query).then((res) => {
        if (res.code === 200) {
          this.strategy1Options = Array.from(
            new Set(res.data.filter((v) => v !== ""))
          );
        } else this.strategy1Options = [];
      });
      console.log(this.strategy1Options);
    },

    /**
     * 获取全部二级策略分类列表
     */
    getStrategy2List (query) {
      if (query === undefined) {
        query = "";
      }
      getStrategy2List(query).then((res) => {
        if (res.code === 200)
          this.strategy2Options = res.data.filter((v) => v !== "");
        else this.strategy2Options = [];
      });
    },

    /**
     * 提交保存数据
     * @param data
     */
    saveProduct (params) {
      const data = {
        ...params,
        accountProps: Number(this.activeName),
      };
      saveProduct(data).then((res) => {
        if (res.code === 200) {
          this.$message.success("上传成功");
          this.getProductList(this.formData);
          this.getManagerList();
          data.addFlag = false;
        } else {
          this.$message.error("上传失败");
        }
      });
    },

    /**
     * 删除数据
     */
    deleteProduct (gp3) {
      if (this.ban()) return;
      this.$confirm("确定删除么?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteProduct(gp3).then((res) => {
            if (res.code === 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getProductList(this.formData);
              this.getManagerList();
            } else {
              this.$message.error("删除失败");
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },

    /**
     *删除全部数据
     */
    deleteAll () {
      this.$confirm("确定删除么?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteAll().then((res) => {
            if (res.code === 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getProductList(this.formData);
              this.getManagerList();
            } else {
              this.$message.error("删除失败");
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },

    /**
     * 获取历史修改记录
     */
    getRecordList () {
      this.loading.dialogLoading = true;
      const params = {
        type: 2,
        current: this.paginationRecord.pageIndex,
        pageSize: this.paginationRecord.pageSize,
      };
      getRecordList(params).then((res) => {
        this.loading.dialogLoading = false;
        if (res.code === 200) {
          this.tableDataRecord = res.data;
          this.tableDataRecord.forEach((item) => {
            item.context = JSON.parse(item.context.value);
          });
          this.paginationRecord.total = res.total;
        } else {
          this.tableDataRecord = [];
          this.paginationRecord.total = 0;
        }
      });
    },
  },
  mounted () {
    this.getProductList();
    this.getManagerList();
    this.getCodeOptions();
    this.getStrategy1List();
    this.getStrategy2List();
  },
};
</script>
<style lang="scss" scoped>
@import '../tkdesign';

.ml {
	margin-left: 16px;
}

.border_table {
	padding: 16px 24px;
	background: white;

	.border_table_header_button {
		position: relative;

		.prompt-message {
			z-index: 99;
			position: absolute;
			bottom: -98px;
			right: 82px;
			padding: 16px;
			border-radius: 4px;
			box-shadow: 0 0 2px #ccc;
			background-color: #fff;

			.el-button {
				padding: 5px;
				margin: 20px 0 0;
			}

			.cancel {
				margin-left: 20px;
			}

			.warning {
				border-color: #4096ff;
				background: #4096ff;
				color: white;
				margin-left: 5px;
			}

			.block {
				position: absolute;
				width: 10px;
				height: 10px;
				box-shadow: 0.5px 0.5px 1px #ccc;
				background-color: #fff;
				top: 0;
				left: 50%;
				transform: translate(-50%, -50%) rotate(225deg);
			}
		}
	}

	.search-box {
		background-color: #fafafa;
		padding: 16px 24px 0;
		margin-bottom: 16px;
		display: flex;
		justify-content: space-between;
		flex-wrap: wrap;

		.search-box_item {
			width: 33%;

			.el-input,
			.el-select {
				width: 60%;
			}
		}
	}
}
</style>

<style lang="scss">
.search-area {
	.el-form-item__content {
		margin-left: 0 !important;
	}
}
</style>
