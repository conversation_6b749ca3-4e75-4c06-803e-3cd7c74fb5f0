<template>
	<div>
		<fund-info v-show="info.flag == 1" ref="fundInfo"></fund-info>
		<manager-info v-show="info.flag == 2" ref="managerInfo"></manager-info>
		<!-- <company-info v-show="info.classType == 'company'" :returnInfo="returnInfo"></company-info> -->
	</div>
</template>

<script>
// 基金基础信息

// import '@/assets/css/fund/homepage/header.css';
import fundInfo from './components/fundInfo.vue';
import managerInfo from './components/managerInfo.vue';
import companyInfo from './components/companyInfo.vue';
export default {
	name: 'fundBasicInformation',
	components: {
		fundInfo,
		managerInfo,
		companyInfo
	},
	data() {
		return {
			info: {}
		};
	},
	methods: {
		// 获取父组件传递数据
		getData(info) {
			this.info = info;
			this.$nextTick(() => {
				if (this.info == 2) {
					this.$refs['managerInfo'].getData(this.info);
				} else {
					this.$refs['fundInfo'].getData(this.info);
				}
			});
		}
	}
};
</script>
