<template>
  <div class="chart_one"
       v-show="show"
       v-loading="loading">
    <div class="flex_between">
      <div class="title">转债股性债性分析</div>
      <div>
        <el-dropdown>
          <span class="el-dropdown-link"
                style>
            <i class="el-icon-more"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <div @click="exportExcel">导出Excel</div>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
    <div class="charts_fill_class"
         style="margin-top: 16px">
      <el-table :data="data"
                style="width: 100%">
        <el-table-column v-for="item in column"
                         :key="item.value"
                         :prop="item.value"
                         :label="item.label"
                         :min-width="item.width"
                         align="gotoleft"
                         :sortable="item.sortable">
          <template #header>
            <long-table-popover-chart v-if="item.popover"
                                      :data="formatTableData()"
                                      date_key="yearqtr"
                                      :data_key="item.value"
                                      :show_name="item.label">
              <div>{{ item.label }}</div>
            </long-table-popover-chart>
            <div v-else>{{ item.label }}</div>
          </template>
          <template slot-scope="{ row }">
            <div v-if="item.value == 'name'"
                 class="overflow_ellipsis flex_start">
              <div v-if="row.flag == 2">
                <div :style="`width:12px;height:12px;background:${row.color}`"
                     class="mr-8"></div>
              </div>
              <div v-else-if="row.flag == 1"
                   class="flex_start">
                <div :style="`width:12px;height:12px;background:#4096ff`"
                     class="mr-8"></div>
                <div class="mr-8 flex_start">
                  <svg width="14"
                       height="14"
                       viewBox="0 0 14 14"
                       fill="none"
                       xmlns="http://www.w3.org/2000/svg">
                    <path d="M12.4156 4.82735L8.94433 4.32286L7.39258 1.17696C7.35019 1.09083 7.28047 1.0211 7.19433 0.978716C6.97832 0.872075 6.71582 0.960943 6.60781 1.17696L5.05605 4.32286L1.58476 4.82735C1.48906 4.84102 1.40156 4.88614 1.33457 4.9545C1.25358 5.03774 1.20895 5.14973 1.21049 5.26586C1.21203 5.38199 1.25961 5.49276 1.34277 5.57383L3.8543 8.02247L3.26094 11.4801C3.24702 11.5605 3.25592 11.6432 3.28663 11.7189C3.31733 11.7945 3.36862 11.86 3.43466 11.908C3.50071 11.9559 3.57887 11.9845 3.66029 11.9903C3.74171 11.9961 3.82313 11.9789 3.89531 11.9408L7.00019 10.3084L10.1051 11.9408C10.1898 11.9859 10.2883 12.001 10.3826 11.9846C10.6205 11.9436 10.7805 11.718 10.7395 11.4801L10.1461 8.02247L12.6576 5.57383C12.726 5.50684 12.7711 5.41934 12.7848 5.32364C12.8217 5.08438 12.6549 4.8629 12.4156 4.82735Z"
                          fill="#FFD600" />
                  </svg>
                </div>
              </div>
              <el-link @click="alphaGo(row.code, row.name)">{{ row[item.value] }}</el-link>
            </div>
            <div v-else
                 v-html="item.format ? item.format(row[item.value]) : row[item.value]"></div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { getCbondConvert } from "@/api/pages/tools/pool.js";
import { alphaGo } from "@/assets/js/alpha_type.js";
import { filter_json_to_excel } from "@/utils/exportExcel.js";
export default {
  props: {

  },
  data () {
    return {
      ismanager: false,
      show: true,
      optione: [],
      model: [],
      loading: true,
      option: {},
      info: {},
      data: [],
      column: [
        {
          label: "名称",
          value: "name"
        },
        {
          label: "季度",
          value: "yearqtr"
        },
        {
          label: "平底溢价率",
          value: "convertparprice",
          format: this.fixp,
          popover: true,
          sortable: true
        },
        {
          label: "转股溢价率",
          value: "convertpremiumrate",
          format: this.fixp,
          popover: true,
          sortable: true
        },
        {
          label: "规模",
          value: "netasset",
          format: this.fix_money,
          popover: true,
          sortable: true
        }
      ],
      cache_data: []
    };
  },
  methods: {
    fix2p (val) {
      return !isNaN(val) && typeof (val * 1) == "number"
        ? (val * 100).toFixed(2) + "%"
        : "--";
    },
    fixp (val) {
      return !isNaN(val) && typeof (val * 1) == "number"
        ? (val * 1).toFixed(2) + "%"
        : "--";
    },
    fix_money (val) {
      if (!isNaN(val) && typeof (val * 1) == "number") {
        // return val >= 10 ** 8 || val <= -1 * 10 ** 8
        // 	? (val / 10 ** 8).toFixed(2) + '亿'
        // 	: val >= 10 ** 4 || val <= -1 * 10 ** 4
        // 	? (val / 10 ** 4).toFixed(2) + '万'
        // 	: (val * 1).toFixed(2) + '元';
        return (val).toFixed(2) + "亿";
      } else {
        return "--";
      }
    },
    // 跳转基金详情
    alphaGo (id, name) {
      alphaGo(id, name, this.$route.path);
    },
    exportExcel () {
      let list = this.column.map(item => {
        return {
          label: item.label,
          value: item.value
        };
      });
      let data = this.data.map(item => {
        let obj = { ...item };
        for (const key in item) {
          let index = this.column.findIndex(v => v.value == key);
          if (index != -1) {
            let format = this.column[index]?.format;
            if (format) {
              obj[key] = format(item[key]);
            }
          }
        }
        return obj;
      });
      filter_json_to_excel(list, data, "转债股性债性分析");
    },
    formatTableData () {
      let data = [];
      this.data.map(item => {
        let obj = { ...item };
        for (const key in item) {
          let format = this.column.find(obj => {
            return obj.value == key;
          })?.format;
          if (format) {
            let val = format(item[key]);
            obj[key] =
              typeof val == "string"
                ? val.includes("%")
                  ? val?.split("%")?.[0] * 1
                  : val.includes("亿")
                    ? val?.split("亿")?.[0] * 1
                    : !isNaN(val)
                      ? val * 1
                      : val
                : val;
          }
        }
        data.push(obj);
      });
      // console.log(data, 'sssss');
      return data;
    },
    refresInfo (info) {
      this.info = info;
      this.formatData(this.cache_data);
    },
    async getData (info) {
      this.loading = true;
      this.info = info;
      let data = await getCbondConvert({
        ids: this.info["code_list"].map(v => {
          return {
            code: v.code,
            type: this.ismanager
              ? "manager"
              : "fund"
          };
        }),
        insert_time: this.info.date,
        yearqtr: this.info["quarter"],
        method: "等权",
        flag: 5,
        ismanager: this.ismanager,
        type: this.info.type
      });
      this.loading = false;
      if (data?.mtycode == 200) {
        this.show = true;
        this.cache_data = data?.data;
        this.formatData(data?.data);
      } else {
        this.show = false;
        return false;
      }
    },
    formatData (data) {
      this.data = data.map(item => {
        let name =
          item.flag == "pool"
            ? this.info.name
            : this.info["code_list"].find(v => v.code == item.fund_pool_id)
              ?.name;
        let color =
          item.flag == "pool"
            ? "#4096ff"
            : this.info["code_list"].find(v => v.code == item.fund_pool_id)
              ?.color;
        let type =
          item.flag == "pool"
            ? 0
            : this.info["code_list"].find(v => v.code == item.fund_pool_id)
              ?.flag;
        return {
          ...item,
          name: item.name,
          color,
          flag: type
        };
      });
      console.log(this.data);
    }
  },
  mounted () {
    this.ismanager = String(this.$route.query.ismanager) == 'true' ? true : false
  },
};
</script>

<style></style>
