import request from '@/utils/request';
// 保存比较模板
export function TemplaterGet(params) {
	return request({
		url: '/Comparison/Templater/',
		method: 'get',
		params
	});
}
export function TemplaterDel(params) {
	return request({
		url: '/Comparison/Templater/',
		method: 'delete',
		params
	});
}
export function Templater(data) {
	return request({
		url: '/Comparison/Templater/',
		method: 'post',
		data
	});
}
// 对比指标
export function Measure(data) {
	return request({
		url: '/Comparison/Measure/',
		method: 'post',
		data
	});
}
// 自定义打分器列表
export function basketFund() {
	return request({
		url: '/pool/basket_fund/',
		method: 'get'
	});
}
export function header_search_all(params) {
	return request({
		url: '/Analysis/Search/',
		method: 'get',
		params
	});
}
// 获取选择的人或者产品基础信息
export function FundCodeBasicMsg(params) {
	return request({
		url: '/Comparison/FundCodeBasicMsg/',
		method: 'get',
		params
	});
}
// TypeMsg
export function TypeMsg(params) {
	return request({
		url: '/Comparison/TypeMsg/',
		method: 'get',
		params
	});
}

// 删除池子中某个数据
export function delBasketFund(params) {
	return request({
		url: '/pool/basket_fund/',
		method: 'delete',
		params
	});
}

// 备注
export function getDescriptionCompare(params) {
	return request({
		url: '/Comparison/DescriptionCompare/',
		method: 'get',
		params
	});
}
// 备注
export function postDescriptionCompare(data) {
	return request({
		url: '/Comparison/DescriptionCompare/',
		method: 'post',
		data
	});
}
// 基金
// 行业能力图manager_industry//基金
export function manager_industry(params) {
	return request({
		url: '/Comparison/manager_industry/?codes=' + params.codes,
		method: 'get'
	});
}
// 基金收益波动排名关系
export function FundRiskMsg(params) {
	return request({
		url: '/Comparison/FundRiskMsg/',
		method: 'get',
		params
	});
}
// 头部表格
export function FundBasicMsg(params) {
	return request({
		url: '/Comparison/FundBasicMsg/',
		method: 'get',
		params
	});
}
// 风格
export function FundStyleClass(params) {
	return request({
		url: '/Comparison/FundStyleClass/',
		method: 'get',
		params
	});
}
// 打分卡
export function FundCapabilityRank(params) {
	return request({
		url: '/Comparison/FundCapabilityRank/',
		method: 'get',
		params
	});
}

// 近一年持仓
export function FundHoldStockRecent1y(params) {
	return request({
		url: '/Comparison/FundHoldStockRecent1y/',
		method: 'get',
		params
	});
}
// 行业能力图compare/ManagerIndustryCapability/?type=equity&flag=2&manager_code=30189741,30189744&yearqtr=2020 Q2&manager_name=hbj,giu
export function FundIndustryCapability(params) {
	return request({
		url: '/Comparison/FundIndustryCapability/',
		method: 'get',
		params
	});
}
// 风格能力图compare/ManagerIndustryCapability/?type=equity&flag=2&manager_code=30189741,30189744&yearqtr=2020 Q2&manager_name=hbj,giu
export function FundMarketCapability(params) {
	return request({
		url: '/Comparison/FundMarketCapability/',
		method: 'get',
		params
	});
}

export function FundBuySell(params) {
	return request({
		url: '/Comparison/FundBuySell/',
		method: 'get',
		params
	});
}
// 基金换手率
export function FundTurnoverWithConcentration(params) {
	return request({
		url: '/Comparison/FundTurnoverWithConcentration/',
		method: 'get',
		params
	});
}

// 因子暴露
export function BondFundIndexFactor(params) {
	return request({
		url: '/Comparison/BondFundIndexFactor/',
		method: 'get',
		params
	});
}
// d大类资产配置FundAllocationDetails
export function FundAllocationDetails(params) {
	return request({
		url: '/Comparison/FundAllocationDetails/',
		method: 'get',
		params
	});
}
// 报告期债券资产配置选项
export function BondFundAllHold(params) {
	return request({
		url: '/Comparison/BondFundAllHold/',
		method: 'get',
		params
	});
}
// 报告期债券资产配置
export function BondHoldAsset(params) {
	return request({
		url: '/Comparison/BondHoldAsset/',
		method: 'get',
		params
	});
}
// 债券近一年配置
export function BondFundRecent1y(params) {
	return request({
		url: '/Comparison/BondFundRecent1y/',
		method: 'get',
		params
	});
}
// 券种配置

export function BondFundHoldBond(params) {
	return request({
		url: '/Comparison/BondFundHoldBond/',
		method: 'get',
		params
	});
}
// 九七拉长

export function BondFundDurationLong(params) {
	return request({
		url: '/Comparison/BondFundDurationLong/',
		method: 'get',
		params
	});
}
// 牛熊市场

export function BondFundMarket(params) {
	return request({
		url: '/Comparison/BondFundMarket/',
		method: 'get',
		params
	});
}
// 信用挖掘

export function BondFundCreditDown(params) {
	return request({
		url: '/Comparison/BondFundCreditDown/',
		method: 'get',
		params
	});
}

// 指标比较

export function FundAlphaWithBeta(params) {
	return request({
		url: '/Comparison/FundAlphaWithBeta/',
		method: 'get',
		params
	});
}

// 行业配置

export function FundStocksIndustry(params) {
	return request({
		url: '/Comparison/FundStocksIndustry/',
		method: 'get',
		params
	});
}
// gegu配置

export function FundHoldStocks(params) {
	return request({
		url: '/Comparison/FundHoldStocks/',
		method: 'get',
		params
	});
}

// 仓位 估算
export function FundAllocationWithIndex(params) {
	return request({
		url: '/Comparison/FundAllocationWithIndex/',
		method: 'get',
		params
	});
}
//共管行业name
export function FundIndustryNameList(params) {
	return request({
		url: '/Comparison/FundIndustryNameList/',
		method: 'get',
		params
	});
}
// 共管行业
export function FundIndustryReturn(params) {
	return request({
		url: '/Comparison/FundIndustryReturn/',
		method: 'get',
		params
	});
}
// 共管行业
export function FundDiffIndustry(params) {
	return request({
		url: '/Comparison/FundDiffIndustry/',
		method: 'get',
		params
	});
}
// 基金经理共管行业name
export function ManagerIndustryNameList(params) {
	return request({
		url: '/Comparison/ManagerIndustryNameList/',
		method: 'get',
		params
	});
}
// 基金经理共管行业
export function ManagerIndustryReturnDetails(params) {
	return request({
		url: '/Comparison/ManagerIndustryReturnDetails/',
		method: 'get',
		params
	});
}

// 基金经理
// 基础信息
export function ManagerBasicMsg(params) {
	return request({
		url: '/Comparison/ManagerBasicMsg/',
		method: 'get',
		params
	});
}
// 收益曲线
export function ManagerReturn(params) {
	return request({
		url: '/Comparison/ManagerReturn/',
		method: 'get',
		params
	});
}

// 收益曲线 接口拆分1   FundorManagerReturn为基金经理收益
export function FundorManagerReturn(params) {
	return request({
		url: '/Comparison/FundorManagerReturn/',
		method: 'get',
		params
	});
}

// 慧捕基打分卡
export function ManagerCapability(params) {
	return request({
		url: '/Comparison/ManagerCapability/',
		method: 'get',
		params
	});
}

// 戴维斯
export function FundLongStockStat(params) {
	return request({
		url: '/Comparison/FundLongStockStat/',
		method: 'get',
		params
	});
}

// 代表基金收益
export function ManagerManagedFund(params) {
	return request({
		url: '/Comparison/ManagerManagedFund/',
		method: 'get',
		params
	});
}
export function ManagedFundReturn(params) {
	return request({
		url: '/Comparison/ManagedFundReturn/',
		method: 'get',
		params
	});
}

// 近一年持仓
export function ManagerHoldRecent1y(params) {
	return request({
		url: '/Comparison/ManagerHoldRecent1y/',
		method: 'get',
		params
	});
}
// 换手率 前三大行业集中度
export function ManagerTurnoverWithCon(params) {
	return request({
		url: '/Comparison/ManagerTurnoverWithCon/',
		method: 'get',
		params
	});
}
// 行业配置/?type=equity&flag=1&manager_code=30189741,30189744
export function ManagerSwindustryHold(params) {
	return request({
		url: '/Comparison/ManagerSwindustryHold/',
		method: 'get',
		params
	});
}
// 持仓?type=equity&yearqtr=2020 Q2&manager_code=30189741,30441407&swname=食品饮料
export function ManagerHoldStocks(params) {
	return request({
		url: '/Comparison/ManagerHoldStocks/',
		method: 'get',
		params
	});
}
// 行业能力图compare/ManagerIndustryCapability/?type=equity&flag=2&manager_code=30189741,30189744&yearqtr=2020 Q2&manager_name=hbj,giu
export function ManagerIndustryCapability(params) {
	return request({
		url: '/Comparison/ManagerIndustryCapability/',
		method: 'get',
		params
	});
}
// 风格能力图compare/ManagerIndustryCapability/?type=equity&flag=2&manager_code=30189741,30189744&yearqtr=2020 Q2&manager_name=hbj,giu
export function ManagerMarketCapability(params) {
	return request({
		url: '/Comparison/ManagerMarketCapability/',
		method: 'get',
		params
	});
}
// 基金经理收益波动排名关系
export function ManagerSinceFeature(params) {
	return request({
		url: '/Comparison/ManagerSinceFeature/',
		method: 'get',
		params
	});
}
// equitytheme
export function equitytheme(params) {
	return request({
		url: '/Analysis/equitytheme/',
		method: 'get',
		params
	});
}
// 风格
export function ManagerStyleClass(params) {
	return request({
		url: '/Comparison/ManagerStyleClass/',
		method: 'get',
		params
	});
}
// 戴维斯
export function ManagerLongHoldStats(params) {
	return request({
		url: '/Comparison/ManagerLongHoldStats/',
		method: 'get',
		params
	});
}
// 买入卖出
export function ManagerBuySellMod(params) {
	return request({
		url: '/Comparison/ManagerBuySellMod/',
		method: 'get',
		params
	});
}
// 个股分析
export function StockMsg(params) {
	return request({
		url: '/Comparison/StockMsg/',
		method: 'get',
		params
	});
}
//
// 指标比较

export function ManagerAlphaWithBeta(params) {
	return request({
		url: '/Comparison/ManagerAlphaWithBeta/',
		method: 'get',
		params
	});
}
// 搜索基金/基金经理

export function search_all(params) {
	return request({
		url: '/Analysis/Search/',
		method: 'get',
		params
	});
}
export function ManagerAllocationWithIndex(params) {
	return request({
		url: '/Comparison/ManagerAllocationWithIndex/',
		method: 'get',
		params
	});
}
// 行业能力宽度-table1-胜率超过50%的行业汇总-基金
export function FundIndustryExcessSummary(params) {
	return request({
		url: '/Comparison/FundIndustryExcessSummary/',
		method: 'get',
		params
	});
}

// 行业能力宽度-table1-胜率超过50%的行业汇总-基金经理
export function ManagerIndustryExcessSummary(params) {
	return request({
		url: '/Comparison/ManagerIndustryExcessSummary/',
		method: 'get',
		params
	});
}

// 行业能力宽度-table2-配置权重超过5%的行业汇总-基金
export function FundIndustryWeightSummary(params) {
	return request({
		url: '/Comparison/FundIndustryWeightSummary/',
		method: 'get',
		params
	});
}
// 行业能力宽度-table2-配置权重超过5%的行业汇总-基金经理
export function ManagerIndustryWeightSummary(params) {
	return request({
		url: '/Comparison/ManagerIndustryWeightSummary/',
		method: 'get',
		params
	});
}

// 行业能力宽度-table3-回报最高的5个行业-基金
export function FundLongHoldIndustryCumReturn(params) {
	return request({
		url: '/Comparison/FundLongHoldIndustryCumReturn/',
		method: 'get',
		params
	});
}
// 行业能力宽度-table3-回报最高的5个行业-基金经理
export function ManagerIndustryCumReturnSummary(params) {
	return request({
		url: '/Comparison/ManagerIndustryCumReturnSummary/',
		method: 'get',
		params
	});
}

// 近一年公布重仓-PB&PE-基金
export function FundPBWithPE(params) {
	return request({
		url: '/Comparison/FundPBWithPE/',
		method: 'get',
		params
	});
}
// 近一年公布重仓-PB&PE-基金经理
export function ManagerPBWithPE(params) {
	return request({
		url: '/Comparison/ManagerPBWithPE/',
		method: 'get',
		params
	});
}

// 基金-共同持股分析-下拉列表
export function FundStockHoldList(params) {
	return request({
		url: '/Comparison/FundStockHoldList/',
		method: 'get',
		params
	});
}
// 基金-共同持股分析
export function FundAllHoldStocks(params) {
	return request({
		url: '/Comparison/FundCommonHoldStocks/',
		method: 'get',
		params
	});
}
// 基金经理-共同持股分析-下拉列表
export function ManagerStockHoldList(params) {
	return request({
		url: '/Comparison/ManagerStockHoldList/',
		method: 'get',
		params
	});
}
// 基金经理-共同持股分析
export function ManagerHoldSameStocksDetail(params) {
	return request({
		url: '/Comparison/ManagerHoldSameStocksDetail/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {报告期持仓统计指数收益} params
 * code
 * @returns
 */

export function getIndexReturn(data) {
	return request({
		url: '/system/other/IndexReturnInfo/',
		method: 'post',
		data
	});
}