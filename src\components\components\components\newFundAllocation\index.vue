<!--  -->
<template>
	<div class="chart_one">
		<analysis-card-title title="最新各类型基金配置情况" @downloadExcel="exportExcel"></analysis-card-title>
		<div style="display: flex" v-loading="loading">
			<el-table
				:data="fourdata1"
				class="table"
				:default-sort="{ prop: 'fof_weight', order: 'descending' }"
				ref="multipleTable"
				header-cell-class-name="table-header"
				max-height="400px"
			>
				<el-table-column v-for="(item, index) in columnList" :key="index" :prop="item.value" :label="item.label" align="gotoleft">
					<template slot-scope="{ row }">
						<div>{{ item.format ? item.format(row[item.value]) : row[item.value] }}</div>
					</template>
				</el-table-column>
			</el-table>
		</div>
	</div>
</template>

<script>
import { exportTitle, exportTable, downloadWord } from '@/utils/exportWord.js';
import { filter_json_to_excel } from '@/utils/exportExcel.js';

// 获取基金基础信息,获取最新各类基金配置情况
import { getBasicInfo, getFofAllocationMsg } from '@/api/pages/Analysis.js';
import { format } from '@/utils/getfulldate';
export default {
	//import引入的组件需要注入到对象中才能使用
	data() {
		//这里存放数据
		return {
			companyCreateDate: '',
			quarterList: [],
			targetQuarter: '',
			datatable: [],
			benchmarkvalue: '',
			benchmarkvaluename: '',
			benchmarkoptions: [],
			fourdata1: [],
			fourdata2: [],
			notesData: {
				fholdfundetail: ''
			},
			info: {},
			loading: true,
			columnList: [
				{
					label: '基金类型',
					value: 'csrctype'
				},
				{
					label: '仓位',
					value: 'weight',
					format: this.fix2p
				},
				{
					label: 'A类仓位',
					value: 'A',
					format: this.fix2p
				},
				{
					label: 'C类仓位',
					value: 'C',
					format: this.fix2p
				},
				{
					label: '内部公司权重',
					value: 'company_weight',
					format: this.fix2p
				},
				{
					label: '基金经理平均管理时间',
					value: 'managed_time'
				}
			]
		};
	},
	filters: {
		fix2p(value) {
			if (value == '--') return value;
			else return (value * 1).toFixed(2) + '%';
		},
		fixY(value) {
			if (value == '--') return value;
			else {
				return (Number(value) / *********).toFixed(2) + '亿';
			}
		},
		fixt(value) {
			if (value == '--') return value;
			else {
				return Number(value).toFixed(2) + '年';
			}
		},
		fixp(value) {
			if (value == '--') return value;
			else {
				return Number(value).toFixed(2);
			}
		}
	},
	//方法集合
	methods: {
		fix2p(value) {
			if (value == '--') return value;
			else return value.toFixed(2) + '%';
		},
		fixt(value) {
			if (value == '--') return value;
			else {
				return Number(value).toFixed(2) + '年';
			}
		},
		// 获取最新各类基金配置情况
		async getFofAllocationMsg() {
			this.loading = true;
			let data = await getFofAllocationMsg({
				// yearqtr: this.targetQuarter.join(' '),
				code: this.info.code,
				type: this.info.type,
				flag: this.info.flag,
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			this.loading = false;
			if (data?.mtycode == 200) {
				this.fourdata1 = data?.data.map((item) => {
					return {
						...item,
						// weight: this.fix2p(item.weight),
						// A: this.fix2p(item.A),
						// C: this.fix2p(item.C),
						// company_weight: this.fix2p(item.company_weight),
						managed_time: this.fixt(item.managed_time)
					};
				});
			} else {
			}
		},
		getData(info) {
			this.info = info;
			if (info.type == 'portfolio') {
				this.columnList.splice(
					this.columnList.findIndex((item) => {
						return item.value == 'company_weight';
					}),
					1
				);
			}
			this.getFofAllocationMsg();
		},
		// 获取基金成立日
		async getFundMessage() {
			let data = await getBasicInfo({
				code: this.info.code,
				flag: this.info.flag,
				type: this.info.type,
				start_date: this.info.start_date,
				end_date: this.info.end_date,
				template: 'newFundAllocation'
			});
			if (data?.mtycode == 200) {
				this.companyCreateDate = data?.data?.founddate || '暂无数据';
				this.generateQuarterList();
			}
		},
		changgedate() {
			this.resolveData();
		},
		resolveData() {
			this.getFofAllocationMsg();
		},
		generateQuarterList() {
			let option = [];
			let qList = ['Q1', 'Q2', 'Q3', 'Q4'];
			let pre = this.companyCreateDate;
			let now = this.FUNC.transformDate(new Date());

			let preYear = pre.slice(0, 4);
			let nowYear = now.slice(0, 4);
			let preQ = this.FUNC.dateToQuarter(pre).slice(5);
			let nowQ = this.FUNC.dateToQuarter(now).slice(5);

			let yList = Array.from({ length: Math.abs(nowYear - preYear + 1) }, (item, index) => (item = parseInt(preYear) + index));

			for (let y of yList) {
				let yobj = {
					value: y,
					label: y,
					children: []
				};
				if (y == preYear) {
					qList.forEach((q) => {
						if (q >= preQ) {
							yobj.children.push({ value: q, label: q });
						}
					});
				} else if (y == nowYear) {
					qList.forEach((q) => {
						if (q <= nowQ) {
							yobj.children.push({ value: q, label: q });
						}
					});
				} else {
					qList.forEach((q) => yobj.children.push({ value: q, label: q }));
				}
				option.push(yobj);
			}
			this.quarterList = option;
			if (option[option.length - 1].children.length == 1) {
				this.targetQuarter = [
					option[option.length - 2].value,
					option[option.length - 2].children[option[option.length - 2].children.length - 1].value
				];
			} else {
				this.targetQuarter = [
					option[option.length - 1].value,
					option[option.length - 1].children[option[option.length - 1].children.length - 2].value
				];
			}
			this.resolveData();
		},

		exportExcel() {
			let list = [
				{
					label: '基金类型',
					value: 'csrctype'
				},
				{
					label: '仓位',
					value: 'weight',
					format: 'fix2p'
				},
				{
					label: 'A类仓位',
					value: 'A',
					format: 'fix2p'
				},
				{
					label: 'C类仓位',
					value: 'C',
					format: 'fix2p'
				},
				{
					label: '内部公司权重',
					value: 'company_weight',
					format: 'fix2p'
				},
				{
					label: '基金经理平均管理时间',
					value: 'managed_time',
					format: 'fix2p'
				}
			];
			filter_json_to_excel(list, this.fourdata1, '最新各类型基金配置情况');
		},
		createPrintWord() {
			if (this.fourdata1.length) {
				return [
					...exportTitle('最新各类型基金配置情况'),
					...exportTable(
						this.columnList.map((item) => {
							return { ...item, format: undefined };
						}),
						this.fourdata1
					)
				];
			} else {
				return [];
			}
		}
	}
};
</script>
