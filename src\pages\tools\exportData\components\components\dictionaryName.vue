<template>
	<div class="dictionary_name">
		<div>
			<el-input v-model="content" placeholder="   请输入搜索内容" style="width: 320px" prefix-icon="el-icon-search" @input="searchItem">
			</el-input>
		</div>
		<div>
			<el-tree
				ref="tree"
				highlight-current
				:data="data"
				:props="defaultProps"
				@node-click="handleNodeClick"
				:filter-node-method="filterNode"
				accordion="true"
			></el-tree>
		</div>
	</div>
</template>

<script>
export default {
	data() {
		return {
			content: '',
			data: [
				{
					name: '基金',
					children: [
						{
							name: '基金规模',
							description: '基金自成立以来历史规模',
							url: 'FundScale',
							condition: [],
							data: [
								{
									label: '基金代码',
									value: 'fund_code',
									description: '"fund_code":"110011"'
								},
								{
									label: '年份',
									value: 'year',
									description: '"year":"2022"'
								},
								{
									label: '季度',
									value: 'quarter',
									description: '"quarter":"1"'
								},
								{
									label: '规模',
									value: 'netasset',
									description: '"netasset":"1"'
								}
							]
						},
						{
							name: '基金类型',
							description: '根据成立以来对其持仓分析划定基金类型',
							url: 'FundType',
							condition: [],
							data: [
								{
									label: '基金代码',
									value: 'fund_code',
									description: '"fund_code":"110011"'
								},
								{
									label: '基金所属类型',
									value: 'type',
									description: '"type":"equitywithhk"'
								},
								{
									label: '基金所属类型名称',
									value: 'type_name',
									description: '"type_name":"主动权益(含港股)"'
								}
							]
						},
						// {
						// 	name: '基金申购赎回状态',
						// 	description: '该基金最新的申购赎回状态',
						// 	url: 'FundIsBigForBuyOrSell',
						// 	data: [
						// 		{
						// 			label: '基金代码',
						// 			value: 'fund_code',
						// 			description: '"fund_code":"110011"'
						// 		},
						// 		{
						// 			label: '申购状态',
						// 			value: 'applyingtype',
						// 			description: '"applyingtype":"可申购"'
						// 		},
						// 		{
						// 			label: '赎回状态',
						// 			value: 'redeemtype',
						// 			description: '"redeemtype":"可赎回"'
						// 		},
						// 		{
						// 			label: '限购金额',
						// 			value: 'largeapplyingmax',
						// 			description: '"largeapplyingmax":"100万"'
						// 		}
						// 	]
						// },
						{
							name: '基金历史管理人',
							description: '该基金历史管理人',
							url: 'FundOfManager',
							condition: [],
							data: [
								{
									label: '基金经理代码',
									value: 'manager_code',
									description: '"manager_code":"30189744"'
								},
								{
									label: '基金经理名称',
									value: 'manager_name',
									description: '"manager_name":"张坤"'
								},
								{
									label: '基金代码',
									value: 'fund_code',
									description: '"fund_code":"110011"'
								},
								{
									label: '基金名称',
									value: 'fund_name',
									description: '"fund_name":"易方达优质精选混合(QDII)"'
								},
								{
									label: '开始管理日期',
									value: 'manage_from',
									description: '"manage_from":"2008-01-01"'
								},
								{
									label: '截止管理日期',
									value: 'manage_to',
									description: '"manage_to":"2022-01-01","--"为仍在管理'
								}
							]
						},
						// {
						// 	name: '基金能力列表',
						// 	description: '获取该基金所计算的全部指标项',
						// 	url: 'FundCapabilityItems',
						// 	data: [
						// 		// {
						// 		// 	label: '基金代码',
						// 		// 	value: 'fund_code',
						// 		// 	description: '"fund_code":"110011""'
						// 		// },
						// 		{
						// 			label: '基金指标与能力名称',
						// 			value: 'item',
						// 			description: '"item":"综合排名"'
						// 		}
						// 	]
						// },
						{
							name: '基金能力分位',
							description: '该基金在此项能力上在全市场同类产品中的排名',
							url: 'FundRiskLevel',
							condition: ['item'],
							data: [
								{
									label: '基金代码',
									value: 'fund_code',
									description: '"fund_code":"110011""'
								},
								{
									label: '基金能力项',
									value: 'item',
									description: '"item":"行业能力"'
								},
								{
									label: '基金能力项描述',
									value: 'description',
									description: '"description":"低"'
								},
								{
									label: '基金能力项市场同类分位',
									value: 'rel_rank',
									description: '"rel_rank":"0"'
								}
							]
						},
						{
							name: '基金持股信息',
							description: '该基金在季报中所披露的股票持仓信息',
							url: 'FundHoldStocks',
							condition: [],
							data: [
								{
									label: '基金代码',
									value: 'fund_code',
									description: '"fund_code":"110011""'
								},
								{
									label: '年份',
									value: 'year',
									description: '"year":"2022"'
								},
								{
									label: '季度',
									value: 'quarter',
									description: '"quarter":"1"'
								},
								{
									label: '股票代码',
									value: 'stock_code',
									description: '"stock_code":"000001.SH"'
								},
								{
									label: '股票名称',
									value: 'stock_name',
									description: '"stock_code":"平安银行"'
								},
								{
									label: '占净资产比',
									value: 'weight',
									description: '"weight":"0.1"'
								}
							]
						},
						{
							name: '基金持有债券信息',
							description: '该基金在季报中所披露的债券持仓信息',
							url: 'FundHoldBonds',
							condition: [],
							data: [
								{
									label: '基金代码',
									value: 'fund_code',
									description: '"fund_code":"110011""'
								},
								{
									label: '年份',
									value: 'year',
									description: '"year":"2022"'
								},
								{
									label: '季度',
									value: 'quarter',
									description: '"quarter":"1"'
								},
								{
									label: '债券代码',
									value: 'bond_code',
									description: '"bond_code":"018018"'
								},
								{
									label: '债券名称',
									value: 'bond_name',
									description: '"bond_name":"国开2101"'
								},
								{
									label: '持有张数',
									value: 'holdings',
									description: '"holdings":"1000"'
								},
								// {
								// 	label: '持仓变动数量',
								// 	value: 'changeofH',
								// 	description: '"changeofH":"1000"'
								// },
								{
									label: '占净资产比',
									value: 'ratioinN',
									description: '"ratioinN":"0.1"'
								},
								{
									label: '占债券资产比',
									value: 'ratioinB',
									description: '"ratioinB":"0.1"'
								},
								{
									label: '占总资产比',
									value: 'ratioinA',
									description: '"ratioinA":"0.1"'
								}
							]
						},
						{
							name: '基金报告披露持有行业',
							description:
								'基金根据季报披露股票情况计算其持有行业的信息(包括行业基准收益, 行业收益, 行业权重, 其中1 3 季报信息采用慧捕基算法估计)',
							url: 'FundHoldIndustry',
							condition: [],
							data: [
								{
									label: '基金代码',
									value: 'fund_code',
									description: '"fund_code":"110011""'
								},
								{
									label: '年份',
									value: 'year',
									description: '"year":"2022"'
								},
								{
									label: '季度',
									value: 'quarter',
									description: '"quarter":"1"'
								},
								{
									label: '行业划分标准',
									value: 'industry_standard',
									description: '"industry_standard":"申万(2021)"'
								},
								{
									label: '行业市值',
									value: 'value',
									description: '"value":"10000"'
								},
								{
									label: '行业代码',
									value: 'industry_code',
									description: '"industry_code":"220000"'
								},
								{
									label: '行业名称',
									value: 'industry_name',
									description: '"industry_name":"基础化工"'
								},
								{
									label: '占净值比',
									value: 'ratioinN',
									description: '""ratioinN": 0.1938474672741787"'
								},
								{
									label: '行业收益',
									value: 'industry_return',
									description: '"industry_return":"0.1"'
								},
								{
									label: '超额收益(相对于行业基准)',
									value: 'excess_return',
									description: '"excess_return":"0.1"'
								}
							]
						},
						{
							name: '基金资产详情',
							description: '根据基金披露报告中个资产占比',
							url: 'FundAssetDetail',
							condition: [],
							data: [
								{
									label: '基金代码',
									value: 'fund_code',
									description: '"fund_code":"110011""'
								},
								{
									label: '年份',
									value: 'year',
									description: '"year":"2022"'
								},
								{
									label: '季度',
									value: 'quarter',
									description: '"quarter":"1"'
								},
								{
									label: '股票占净值比',
									value: 'equity_weight',
									description: '"equity_weight`":"0.1"'
								},
								{
									label: '债券占净值比',
									value: 'bond_weight',
									description: '"bond_weight":"0.1"'
								},
								{
									label: '基金占净值比',
									value: 'fund_weight',
									description: '"fund_weight":"0.1"'
								},
								{
									label: '权证占净值比',
									value: 'option_weight',
									description: '"option_weight":"0.1"'
								},
								{
									label: '买入反手金融资产占净值比',
									value: 'repo_weight',
									description: '"repo_weight":"0.1"'
								},
								{
									label: '现金占净值比',
									value: 'cash_weight',
									description: '"cash_weight":"0.1"'
								},
								{
									label: '其它资产占净值比',
									value: 'other_weight',
									description: '"other_weight":"0.1"'
								}
							]
						},
						{
							name: '基金超额收益归因',
							description: '根据慧捕基算法对其基金超额收益进行拆解为分别在大类资产 行业 股票 交易上的超额收益',
							url: 'FundExcessReturnReason',
							condition: [],
							data: [
								{
									label: '基金代码',
									value: 'fund_code',
									description: '"fund_code":"110011""'
								},
								{
									label: '年份',
									value: 'year',
									description: '"year":"2022"'
								},
								{
									label: '季度',
									value: 'quarter',
									description: '"quarter":"1"'
								},
								{
									label: '在大类资产配置上的超额收益',
									value: 'allocation',
									description: '"allocation`":"0.1"'
								},
								{
									label: '在行业配置上的超额收益',
									value: 'industry',
									description: '"industry":"0.1"'
								},
								{
									label: '在个股选择上的超额收益',
									value: 'stock',
									description: '"stock":"0.1"'
								},
								{
									label: '在交易上的超额收益',
									value: 'trading',
									description: '"trading":"0.1"'
								}
							]
						},
						{
							name: '基金风格变化',
							description: '根据基金持仓用慧捕基算法计算基金风格',
							url: 'FundStyle',
							condition: [],
							data: [
								{
									label: '基金代码',
									value: 'fund_code',
									description: '"fund_code":"110011""'
								},
								{
									label: '计算年份',
									value: 'year',
									description: '"year":"2022"'
								},
								{
									label: '成长价值',
									value: 'valuegrowth',
									description: '"valuegrowth":"成长"'
								},
								{
									label: '大小盘',
									value: 'bigsmall',
									description: '"bigsmall`":"大盘"'
								},
								{
									label: '择时',
									value: 'timing',
									description: '"timing":"个股"'
								},
								{
									label: '大行业分类',
									value: 'industrysector',
									description: '"industrysector":"大消费"'
								}
							]
						},
						{
							name: '基金股票换手率',
							description: '根据基金半年报和年报计算该基金年度换手率',
							url: 'FundTurnoverStocks',
							condition: [],
							data: [
								{
									label: '基金代码',
									value: 'fund_code',
									description: '"fund_code":"110011""'
								},
								{
									label: '年份',
									value: 'year',
									description: '"year":"2022"'
								},
								{
									label: '季度',
									value: 'quarter',
									description: '"quarter":"1"'
								},
								{
									label: '股票换手率',
									value: 'turnover',
									description: '"turnover`":"0.1"'
								},
								{
									label: '是否为真实计算值(0: 估算; 1: 真实计算)',
									value: 'num',
									description: '"num":"1"'
								}
							]
						},
						{
							name: '基金债券换手率',
							description: '根据基金报告披露持仓计算其债券换手率',
							url: 'FundTurnoverBonds',
							condition: [],
							data: [
								{
									label: '基金代码',
									value: 'fund_code',
									description: '"fund_code":"110011""'
								},
								{
									label: '年份',
									value: 'year',
									description: '"year":"2022"'
								},
								{
									label: '季度',
									value: 'quarter',
									description: '"quarter":"1"'
								},
								{
									label: '债券换手率',
									value: 'turnover',
									description: '"turnover`":"0.1"'
								},
								{
									label: '是否为真实计算值(0: 估算; 1: 真实计算)',
									value: 'num',
									description: '"num":"1"'
								}
							]
						},
						{
							name: '基金行业集中度',
							description: '根绝持仓通过慧捕基算法计算其行业集中度',
							url: 'FundConcentration',
							condition: [],
							data: [
								{
									label: '基金代码',
									value: 'fund_code',
									description: '"fund_code":"110011""'
								},
								{
									label: '计算日期',
									value: 'yearqtr',
									description: '"yearqtr":"2022-01-01"'
								},
								{
									label: '前10集中度',
									value: 'top10_concentration',
									description: '"top10_concentration":"0.1"'
								},
								{
									label: '前10 集中度排名(越大集中度越高)',
									value: 'top10_concentration_rank',
									description: '"top10_concentration_rank`":"0.1"'
								},
								{
									label: '前5集中度',
									value: 'top5_concentration',
									description: '"top5_concentration":"1"'
								},
								{
									label: '前5 集中度同类排名',
									value: 'top5_concentration_rank',
									description: '"top5_concentration_rank`":"0.1"'
								},
								{
									label: '前3集中度',
									value: 'top3_concentration',
									description: '"top3_concentration":"1"'
								},
								{
									label: '前3集中度同类排名',
									value: 'top3_concentration_rank',
									description: '"top3_concentration_rank`":"0.1"'
								}
							]
						},
						{
							name: '基金买入模式',
							description: '根据报告披露的基金买入股票通过慧捕基算法对买入行为进行分析',
							url: 'FundBuyMods',
							condition: [],
							data: [
								{
									label: '基金代码',
									value: 'fund_code',
									description: '"fund_code":"110011""'
								},
								{
									label: '基金买入股票的情况',
									value: 'name',
									description: '"name":"急速下跌"'
								},
								{
									label: '基金在该种情形下买入股票的概率',
									value: 'freq',
									description: '"freq":"0.1"'
								}
							]
						},
						{
							name: '基金卖出模式',
							description: '根据报告披露的基金卖出股票通过慧捕基算法对卖出行为进行分析',
							url: 'FundSellMods',
							condition: [],
							data: [
								{
									label: '基金代码',
									value: 'fund_code',
									description: '"fund_code":"110011""'
								},
								{
									label: '基金买入股票的情况',
									value: 'name',
									description: '"name":"急速下跌"'
								},
								{
									label: '基金在该种情形下买入股票的概率',
									value: 'freq',
									description: '"freq":"0.1"'
								}
							]
						},
						{
							name: '基金长期持股统计',
							description: '对基金长期持有股票进行统计',
							url: 'FundLongHoldStocks',
							condition: [],
							data: [
								{
									label: '基金代码',
									value: 'fund_code',
									description: '"fund_code":"110011""'
								},
								{
									label: '股票代码',
									value: 'stock_code',
									description: '"stock_code":"000001.SH"'
								},
								{
									label: '在计算期间内第一次出现在报告中的日期',
									value: 'first_report',
									description: '"first_report":"2020-01-01"'
								},
								{
									label: '在计算期间内最后一次出现在报告中的日期',
									value: 'last_report',
									description: '"last_report":"2020-01-01"'
								},
								{
									label: '平均权重',
									value: 'weight',
									description: '"weight":"0.1"'
								},
								{
									label: '该股票在报告中出现的次数',
									value: 'times',
									description: '"times":"10"'
								},
								{
									label: '股票持有期间最大pb',
									value: 'pb_max',
									description: '"pb_max":"1"'
								},
								{
									label: '计算期间内股票最小pb',
									value: 'pb_min',
									description: '"pb_min":"1"'
								},
								{
									label: '计算期间内最大pe',
									value: 'pe_max',
									description: '"pe_max":"1"'
								},
								{
									label: '计算期间内最小pe',
									value: 'pe_min',
									description: '"pe_min":"1"'
								}
							]
						},
						// {
						// 	name: '基金分别在各个行业上处于前三分之一,中三分之一,后三分之一的统计',
						// 	description: '该基金各个在该行业处于前三分之一 中三分之一 后三分之一时的配置情况',
						// 	url: 'FundHistoryIndustryWeight',
						// 	data: [
						// 		{
						// 			label: '基金代码',
						// 			value: 'fund_code',
						// 			description: '"fund_code":"110011""'
						// 		},
						// 		{
						// 			label: '行业代码',
						// 			value: 'industry_code',
						// 			description: '"industry_code":"220000"'
						// 		},
						// 		{
						// 			label: '行业名称',
						// 			value: 'industry_name',
						// 			description: '"industry_name":"建筑装饰"'
						// 		},
						// 		{
						// 			label: '当行业在前三分之一时,该基金的权重配置',
						// 			value: 'weight_top',
						// 			description: '"weight_top":"0.1""'
						// 		},
						// 		{
						// 			label: '当行业在后三分之一时, 该基金的权重配置',
						// 			value: 'weight_bottom',
						// 			description: '"weight_bottom":"0.1"'
						// 		},
						// 		{
						// 			label: '当行业在中间三分之一时,该基金的权重配置',
						// 			value: 'weight_other',
						// 			description: '"weight_other":"0.1"'
						// 		}
						// 	]
						// },
						// {
						// 	name: '基金分时段业绩表现市场划分标准列表',
						// 	description: '基金通过慧捕基算法计算的市场阶段列表',
						// 	url: 'FundMarketPeriodnameList',
						// 	data: [
						// 		{
						// 			label: '基金代码',
						// 			value: 'fund_code',
						// 			description: '"fund_code":"110011""'
						// 		},
						// 		{
						// 			label: '市场划分标准',
						// 			value: 'periodname',
						// 			description: '"periodname":"牛熊市场"'
						// 		}
						// 	]
						// },
						{
							name: '基金在不同市场阶段的表现',
							description: '通过慧捕基算法计算该基金在该市场阶段下alpha sharpe等指标值',
							url: 'FundMarketShow',
							condition: ['item'],
							data: [
								{
									label: '基金代码',
									value: 'fund_code',
									description: '"fund_code":"110011""'
								},
								{
									label: '计算开始时间',
									value: 'time_start',
									description: '"time_start":"2020-01-01"'
								},
								{
									label: '计算截止时间',
									value: 'time_end',
									description: '"time_end":"2020-01-01""'
								},
								{
									label: '市场划分标准',
									value: 'periodname',
									description: '"periodname":"牛熊市场""'
								},
								{
									label: '市场名称',
									value: 'description',
									description: '"description":"牛市""'
								},
								{
									label: 'alpha',
									value: 'alpha',
									description: '"alpha":"1""'
								},
								{
									label: 'smartbeta',
									value: 'smartbeta',
									description: '"smartbeta":"1""'
								},
								{
									label: '夏普率',
									value: 'sharpe',
									description: '"sharpe":"1""'
								},
								{
									label: '超额收益',
									value: 'excessreturn',
									description: '"excessreturn":"0.1""'
								},
								{
									label: 'beta',
									value: 'beta',
									description: '"beta":"1""'
								},
								{
									label: '信息率',
									value: 'info',
									description: '"info":"1""'
								}
							]
						},
						{
							name: '基金长期持股特征',
							description: '慧捕基算法通过对该基金长期持有个股分析,得出该基金持股的偏好',
							url: 'FundHoldStockStat',
							condition: [],
							data: [
								{
									label: '基金代码',
									value: 'fund_code',
									description: '"fund_code":"110011""'
								},
								{
									label: '基金长期持股特征描述',
									value: 'description',
									description: '"description":"挣规模扩张的钱"'
								},
								{
									label: '基金长持股特征比率',
									value: 'ratio',
									description: '"ratio":"0.1"'
								}
							]
						},
						// {
						// 	name: '基金滚动胜率基准列表',
						// 	description: '慧捕基算法在该基金上计算滚动胜率的基准列表',
						// 	url: 'FundHoldPressuresBenchmarkLists',
						// 	data: [
						// 		{
						// 			label: '基金代码',
						// 			value: 'fund_code',
						// 			description: '"fund_code":"110011""'
						// 		},
						// 		{
						// 			label: '基准',
						// 			value: 'competitor',
						// 			description: '"competitor":"00300.SH"'
						// 		},
						// 		{
						// 			label: '基准描述',
						// 			value: 'name',
						// 			description: '"name":"沪深300"'
						// 		}
						// 	]
						// },
						{
							name: '基金滚动胜率(计算与不同基准的胜率)',
							description: '慧捕基算法通过与不同计算做比较分别计算该基金胜率',
							url: 'FundHoldPressure',
							condition: ['item'],
							data: [
								{
									label: '基金代码',
									value: 'fund_code',
									description: '"fund_code":"110011""'
								},
								{
									label: '比较基准',
									value: 'competitor',
									description: '"competitor":"00300.SH"'
								},
								{
									label: '持有时长',
									value: 'hold_length',
									description: '"hold_lengths":"1"'
								},
								{
									label: '总比较场数',
									value: 'comparison',
									description: '"comparison":"1"'
								},
								{
									label: '基金胜场数',
									value: 'wins',
									description: '"wins":"1"'
								},
								{
									label: '基准胜场数',
									value: 'loses',
									description: '"loses":"1"'
								},
								{
									label: '基金胜率',
									value: 'prob_win',
									description: '"prob_wins":"1"'
								},
								{
									label: '基准胜率',
									value: 'prob_lose',
									description: '"prob_loses":"1"'
								},
								{
									label: '基准胜率',
									value: 'name',
									description: '"name":"中证红利"'
								}
							]
						},
						{
							name: '基金Barra因子',
							description: '通过慧捕基算法计算其Barra因子',
							url: 'FundBarraFactor',
							condition: [],
							data: [
								{
									label: '基金代码',
									value: 'fund_code',
									description: '"fund_code":"110011""'
								},
								{
									label: '年',
									value: 'year',
									description: '"year":"2010""'
								},
								{
									label: '贝塔因子',
									value: 'beta',
									description: '"beta":"1"'
								},
								{
									label: '估值因子',
									value: 'bp',
									description: '"bp":"1"'
								},
								{
									label: '盈利因子',
									value: 'earningyield',
									description: '"earningyield":"1"'
								},
								{
									label: '成长因子',
									value: 'growth',
									description: '"growth":"1"'
								},
								{
									label: '杠杆因子',
									value: 'leverage',
									description: '"leverage":"1"'
								},
								{
									label: '流动性因子',
									value: 'liquidity',
									description: '"liquidity":"1"'
								},
								{
									label: '动量因子',
									value: 'momentum',
									description: '"momentum":"1"'
								},
								{
									label: '残差波动性',
									value: 'residualvolatility',
									description: '"residualvolatility":"1"'
								},
								{
									label: '市值因子',
									value: 'size',
									description: '"size":"1"'
								}
							]
						},
						{
							name: '基金行业能力圈',
							description: '通过计算该基金在各个行业上面的指标在全市场同类基金做排名',
							url: 'FundIndustryCapabilitryRank',
							condition: [],
							data: [
								{
									label: '基金代码',
									value: 'fund_code',
									description: '"fund_code":"110011""'
								},
								{
									label: '行业名称',
									value: 'industry_code',
									description: '"industry_code":"建筑装饰"'
								},
								{
									label: '在同类基金中的该基金行业能力排名',
									value: 'industry_rank',
									description: '"industry_rank":"1"'
								}
							]
						},
						{
							name: '基金市场能力圈',
							description: '通过慧捕基算法对该基金在各个市场维度下进行打分',
							url: 'FundMarketCapabilityRank',
							condition: [],
							data: [
								{
									label: '基金代码',
									value: 'fund_code',
									description: '"fund_code":"110011""'
								},
								{
									label: '市场阶段',
									value: 'description',
									description: '"description":"牛市"'
								},
								{
									label: '该基金在同类基金中的市场排名',
									value: 'window_rank',
									description: '"window_rank":"1"'
								}
							]
						},
						{
							name: '债券类基金信用收益估计',
							description: '通过慧捕基算法分别计算债券基金在各项的收益情况',
							url: 'BondFundCredit',
							condition: [],
							data: [
								{
									label: '基金代码',
									value: 'fund_code',
									description: '"fund_code":"110011""'
								},
								{
									label: '计算日期',
									value: 'yearqtr',
									description: '"yearqtr":"2000-01-01"'
								},
								{
									label: '信用',
									value: 'credit',
									description: '"credit":"1"'
								},
								{
									label: '信用回报',
									value: 'returnofcredit',
									description: '"returnofcredit":"1"'
								},
								{
									label: '利率',
									value: 'interest',
									description: '"interest":"1"'
								},
								{
									label: '利率回报',
									value: 'returnofinterest',
									description: '"returnofinterest":"1"'
								},
								{
									label: '货币',
									value: 'currency',
									description: '"currency":"1"'
								},
								{
									label: '货币回报',
									value: 'returnofcurrency',
									description: '"returnofcurrency":"1"'
								},
								{
									label: '权益',
									value: 'equity',
									description: '"equity":"1"'
								},
								{
									label: '权益回报',
									value: 'returnofequity',
									description: '"returnofequity":"1"'
								}
							]
						},
						{
							name: '债券类基金历年来信用下沉程度',
							description: '债券基金的信用下沉程度',
							url: 'BondFundCreditDown',
							condition: [],
							data: [
								{
									label: '基金代码',
									value: 'fund_code',
									description: '"fund_code":"110011""'
								},
								{
									label: '年份',
									value: 'year',
									description: '"year":"2000"'
								},
								{
									label: '季度',
									value: 'quarter',
									description: '"quarter":"1"'
								},
								{
									label: '信用下沉占信用债比',
									value: 'downratioinC',
									description: '"downratioinC":"0.1"'
								},
								{
									label: '信用下沉占净资产比',
									value: 'downratioinN',
									description: '"downratioinN":"0.1"'
								},
								{
									label: '信用债占净资产比',
									value: 'ratioinN',
									description: '"ratioinN":"0.1"'
								}
							]
						},
						{
							name: '债券类基金久期估计',
							description: '通过慧捕基算法分别对债券基金的久期进行估算',
							url: 'BondFundDuration',
							condition: [],
							data: [
								{
									label: '基金代码',
									value: 'fund_code',
									description: '"fund_code":"110011""'
								},
								{
									label: '日期',
									value: 'date',
									description: '"date":"2000-01-01"'
								},
								{
									label: '估算久期',
									value: 'duration',
									description: '"duration":"1"'
								}
							]
						}
					]
				},
				{
					name: '基金经理',
					children: [
						{
							name: '基金经理管理规模',
							description: '通过对该基金经理管理基金来计算该基金经理管理规模',
							url: 'ManagerScale',
							condition: [],
							data: [
								{ label: '基金经理代码', value: 'manager_code', description: '"manager_code":"30189741"' },
								{ label: '基金经理名称', value: 'manager_name', description: '"manager_code":"萧楠"' },
								{ label: '基金经理管理规模(单位：　亿元)', value: 'netasset', description: '"netasset":"596.36716568"' }
							]
						},
						{
							name: '基金经理管理的基金',
							description: '该基金经理所管理的所有基金',
							url: 'ManagerManagedFunds',
							condition: [],
							data: [
								{ label: '基金经理代码', value: 'manager_code', description: '"manager_code":"30189741"' },
								{ label: '基金代码', value: 'fund_code', description: '"fund_code":"110022"' },
								{ label: '基金名称', value: 'fund_name', description: '"fund_name":"易方达消费"' },
								{ label: '基金经理开始管理日期', value: 'manage_from', description: '"manage_from":"2012-01-01"' },
								{ label: '基金经理截止管理日期', value: 'manage_to', description: '"manage_to":"2022-01-01"' }
							]
						},
						{
							name: '基金经理管理年限',
							description: '该基金经理管理基金年限',
							url: 'ManagerTimes',
							condition: [],
							data: [
								{ label: '基金经理代码', value: 'manager_code', description: '"manager_code":"30189741"' },
								{ label: '基金经理管理年限', value: 'managed_year', description: '"managed_year":"6.7"' }
							]
						},
						{
							name: '基金经理持仓信息',
							description: '根据慧捕基算法计算该基金经理持仓信息',
							url: 'ManagerHoldStocks',
							condition: [],
							data: [
								{ label: '基金经理代码', value: 'manager_code', description: '"manager_code":"30189741"' },
								{ label: '年份', value: 'year', description: '"year":"2020"' },
								{ label: '季度', value: 'quarter', description: '"quarter":"4"' },
								{ label: '股票代码', value: 'stock_code', description: '"stock_code":"000001.sh"' },
								{ label: '股票名称', value: 'stock_name', description: '"stock_name":"中国平安"' },
								{ label: '持有占比', value: 'weight', description: '"weight":"0.0568"' },
								{ label: 'pb', value: 'pb', description: '"pb":"0.89"' },
								{ label: 'pe', value: 'pe', description: '"pe":"1.56"' }
							]
						},
						// {
						// 	name: '基金经理类型',
						// 	description: '根据该基金经理大类资产等数据来分析其类型',
						// 	url: 'ManagerType',
						// 	data: [
						// 		{ label: '基金经理代码', value: 'manager_code', description: '"manager_code":"30189741"' },
						// 		{ label: '基金经理类型', value: 'type', description: '"type":"activeequity"' },
						// 		{ label: '开始管理该类型的起始日期', value: 'start_from', description: '"start_from":"2012-01-01"' },
						// 		{ label: '停止管理该类型的截止日期', value: 'end_to', description: '"end_to":"--"' }
						// 	]
						// },
						// {
						// 	name: '基金经理分时段业绩表现市场划分标准列表',
						// 	description: '根据慧捕基算法划分市场的列表',
						// 	url: 'ManagerMarketPeriodnameList',
						// 	data: [
						// 		{ label: '基金经理代码', value: 'manager_code', description: '"manager_code":"30189741"' },
						// 		{ label: '市场划分标准', value: 'periodname', description: '"periodname":"大小盘"' }
						// 	]
						// },
						{
							name: '基金经理分时段业绩表现统计',
							description: '根据慧捕基算法分别计算其在各市场阶段的收益情况',
							url: 'ManagerMarketShow',
							condition: ['item'],
							data: [
								{ label: '基金经理代码', value: 'manager_code', description: '"manager_code":"30189741"' },
								{ label: '计算开始时间', value: 'time_start', description: '"time_start":"2012-01-01"' },
								{ label: '计算截止时间', value: 'time_end', description: '"time_end":"2013-01-01"' },
								{ label: '市场划分标准', value: 'periodname', description: '"periodname":"大小盘"' },
								{ label: '市场名称', value: 'description', description: '"description":"大盘强势"' },
								{ label: 'alpha', value: 'alpha', description: '"alpha":"0.0568"' },
								{ label: 'smartbeta', value: 'smartbeta', description: '"smartbeta":"0.19"' },
								{ label: '夏普率', value: 'sharpe', description: '"sharpe":"1.56"' },
								{ label: '超额收益', value: 'excessreturn', description: '"excessreturn":"1.56"' },
								{ label: 'beta', value: 'beta', description: '"beta":"0.56"' },
								{ label: '信息率', value: 'info', description: '"info":"1.56"' }
							]
						},
						{
							name: '基金经理超额收益归因',
							description: '根据慧捕基算法对基金经理收益进行拆解',
							url: 'ManagerExcessReturnReason',
							condition: [],
							data: [
								{ label: '基金经理代码', value: 'manager_code', description: '"manager_code":"30189741"' },
								{ label: '年份', value: 'year', description: '"year":"2020"' },
								{ label: '季度', value: 'quarter', description: '"quarter":"4"' },
								{ label: '在大类资产配置上的超额收益', value: 'allocation', description: '"allocation":"0.563687"' },
								{ label: '在行业配置上的超额收益', value: 'industry', description: '"industry":"-0.068954"' },
								{ label: '在个股选择上的超额收益', value: 'stock', description: '"stock":"0.1596325"' },
								{ label: '在交易上的超额收益', value: 'trading', description: '"trading":"0.56322541"' }
							]
						},
						{
							name: '基金经理股票换手率',
							description: '通过慧捕基算法分析基金经理股票变动情况计算其换手率',
							url: 'ManagerTurnoverStocks',
							condition: [],
							data: [
								{ label: '基金经理代码', value: 'manager_code', description: '"manager_code":"30189741"' },
								{ label: '年份', value: 'year', description: '"year":"2020"' },
								{ label: '股票换手率', value: 'turnover', description: '"turnover":"6.1865123"' }
							]
						},
						{
							name: '基金经理债券换手率',
							description: '慧捕基算法通过对基金经理债券变动情况进行分析来计算债券换手率',
							url: 'ManagaerTurnoverBond',
							condition: [],
							data: [
								{ label: '基金经理代码', value: 'manager_code', description: '"manager_code":"30189741"' },
								{ label: '年份', value: 'year', description: '"year":"2020"' },
								{ label: '债券换手率', value: 'turnover', description: '"turnover":"0.1865123"' }
							]
						},
						{
							name: '基金经理集中度',
							description: '通过慧捕基算法计算基金经理任职期间集中度',
							url: 'ManagerConcentration',
							condition: [],
							data: [
								{ label: '基金经理代码', value: 'manager_code', description: '"manager_code":"30189741"' },
								{ label: '日期', value: 'yearqtr', description: '"year":"2020"' },
								{ label: '前10集中度', value: 'top10_concentration', description: '"top10_concentration":"10"' },
								{ label: '前10集中度排名', value: 'top10_concentration_rank', description: '"top10_concentration_rank":"2"' },
								{ label: '前5集中度', value: 'top5_concentration', description: '"top5_concentration":"5"' },
								{ label: '前5 集中度同类排名', value: 'top5_concentration_rank', description: '"top5_concentration_rank":"5"' },
								{ label: '前3集中度', value: 'top3_concentration', description: '"top3_concentration":"30"' },
								{ label: '前3集中度同类排名', value: 'top3_concentration_rank', description: '"top3_concentration_rank":"1"' }
							]
						},
						{
							name: '基金经理整体风格变化',
							description: '通过慧捕基算法划分该基金经理所属风格的变化情况',
							url: 'ManagerStyle',
							condition: [],
							data: [
								{ label: '基金经理代码', value: 'manager_code', description: '"manager_code":"30189741"' },
								{ label: '年份', value: 'year', description: '"year":"2020"' },
								{ label: '成长价值', value: 'valuegrowth', description: '"valuegrowth":"成长"' },
								{ label: '大小盘', value: 'bigsmall', description: '"bigsmall":"大盘"' },
								{ label: '择时', value: 'timing', description: '"timing":"个股"' },
								{ label: '大行业分类', value: 'industrysector', description: '"industrysector":"大消费"' }
							]
						},
						{
							name: '基金经理根据持仓估算风格',
							description: '通过慧捕基算法来估算该基金经理风格占比',
							url: 'ManagerHoldStyle',
							condition: [],
							data: [
								{ label: '基金经理代码', value: 'manager_code', description: '"manager_code":"30189741"' },
								{ label: '计算日期', value: 'date', description: '"date":"2020-01-01"' },
								{ label: '估算风格', value: 'style', description: '"style":"成长"' },
								{ label: '估算风格权重', value: 'weight', description: '"weight":"0.26589554"' }
							]
						},
						{
							name: '基金经理长持股特征',
							description: '慧捕基算法通过对基金经理长期持有个股的分析来分析基金经理的偏好',
							url: 'ManagerLongHoldStockStat',
							condition: [],
							data: [
								{ label: '基金经理代码', value: 'manager_code', description: '"manager_code":"30189741"' },
								{ label: '基金经理长期持股特征描述', value: 'description', description: '"description":"--"' },
								{ label: '基金经理长持股特征比率', value: 'ratio', description: '"ratio":"--"' }
							]
						},
						{
							name: '基金经理买入模式统计',
							description: '通过慧捕基算法对基金经理买入股票的情形分析得出该基金经理的买入模式',
							url: 'ManagerBuyMods',
							condition: [],
							data: [
								{ label: '基金经理代码', value: 'manager_code', description: '"manager_code":"30006400"' },
								{ label: '基金经理买入股票的情况', value: 'name', description: '"name":"急速大幅上升"' },
								{ label: '基金经理在该种情形下买入股票的概率', value: 'freq', description: '"freq":"0.3256684"' }
							]
						},
						{
							name: '基金经理卖出模式统计',
							description: '通过慧捕基算法对基金经理卖出股票的情形分析得出该基金经理的卖出模式',
							url: 'ManagerSellMods',
							condition: [],
							data: [
								{ label: '基金经理代码', value: 'manager_code', description: '"manager_code":"30006400"' },
								{ label: '基金经理卖出股票的情况', value: 'name', description: '"name":"箱式震荡"' },
								{ label: '基金经理在该种情形下卖出股票的概率', value: 'freq', description: '"freq":"0.3256684"' }
							]
						},
						{
							name: '债券类基金经理久期估计',
							description: '通过慧捕基算法对债券类基金经理的久期进行估算',
							url: 'ManagerDurationGuess',
							condition: [],
							data: [
								{ label: '基金经理代码', value: 'manager_code', description: '"manager_code":"30189741"' },
								{ label: '计算日期', value: 'date', description: '"date":"2022-06-30"' },
								{ label: '估算久期', value: 'duration', description: '"duration":"3.6"' }
							]
						},
						{
							name: '基金经理信用下沉比例',
							description: '债券类基金经理的信用是否下沉及下沉程度',
							url: 'ManagerCreditDownRatio',
							condition: [],
							data: [
								{ label: '基金经理代码', value: 'manager_code', description: '"manager_code":"30189741"' },

								{ label: '信用下沉占信用债比', value: 'downratioinC', description: '"downratioinC":"0.154545"' },
								{ label: '信用下沉占净资产比', value: 'downratioinN', description: '"downratioinN":"0.23652546"' },
								{ label: '信用债占净资产比', value: 'ratioinN', description: '"ratioinN":"0.4635454687"' }
							]
						},
						// {
						// 	name: '基金经理牛熊市场表现',
						// 	description: '通过计算分别在股债市该基金经理的表现情况',
						// 	url: 'ManagerBearBullMarketShow',
						// 	data: [
						// 		{ label: '基金经理代码', value: 'manager_code', description: '"manager_code":"30189741"' },
						// 		{ label: '市场开始日期', value: 'start_date', description: '"start_date":"2021-04"' },
						// 		{ label: '市场截止日期', value: 'end_date', description: '"end_date":"2021-09"' },
						// 		{ label: '市场状况', value: 'market', description: '"market":"新能源之夏"' },
						// 		{ label: '累计收益', value: 'cum_return', description: '"cum_return":"0.23652546"' },
						// 		{ label: '年化收益', value: 'ann_return', description: '"ann_return":"0.4635454687"' },
						// 		{ label: '最大回撤', value: 'maxdrawdown', description: '"maxdrawdown":"0.1635454687"' },
						// 		{ label: '年化夏普率', value: 'sharpe', description: '"sharpe":"1.4635454687"' }
						// 	]
						// },
						// {
						// 	name: '基金经理滚动胜率基准列表',
						// 	description: '基金经理滚动胜率的比较列表',
						// 	url: 'HoldPressuresBenchmarkLists',
						// 	data: [
						// 		{ label: ' 基金经理代码', value: 'manager_code', description: '"manager_code":"30189741"' },
						// 		{ label: '基准', value: 'competitor', description: '"competitor":"000300.sh"' },
						// 		{ label: '基准描述', value: 'name', description: '"name":"沪深300"' }
						// 	]
						// },
						{
							name: '基金经理滚动胜率',
							description: '通过慧捕基算法来计算该基金经理对比不同基准的胜率',
							url: 'ManagerHoldPressure',
							condition: [],
							data: [
								{ label: '基金经理代码', value: 'manager_code', description: '"manager_code":"30189741"' },
								{ label: '比较基准', value: 'competitor', description: '"competitor":"000300.sh"' },
								{ label: '持有时长', value: 'hold_length', description: '"hold_length":"3"' },
								{ label: '总比较场数', value: 'comparison', description: '"comparison":"1200"' },
								{ label: '基金胜场数', value: 'wins', description: '"wins":"1200"' },
								{ label: '基准胜场数', value: 'loses', description: '"loses":"0"' },
								{ label: '基金胜率', value: 'prob_win', description: '"prob_win":"1"' },
								{ label: '基准胜率', value: 'prob_lose', description: '"prob_lose":"0"' },
								{ label: '比较基准', value: 'benchmark', description: '"benchmark":"收益率0%"' }
							]
						},
						{
							name: '基金经理各能力项打分',
							description: '通过慧捕基算法对该基金经理各项能力项进行打分',
							url: 'ManagerCapabilityRank',
							condition: [],
							data: [
								{ label: '基金经理代码', value: 'manager_code', description: '"manager_code":"33030320"' },
								{ label: '基金经理能力项名称', value: 'item', description: '"item":"择股能力"' },
								{
									label: '基金经理在全市场同类基金经理中该能力项目的排名(越大能力越强',
									value: 'rel_rank',
									description: '"rel_rank":"0.85631545"'
								},
								{ label: '基金经理在全市场同类基金经理中该能力项排名的描述', value: 'description', description: '"description":"高"' }
							]
						},
						{
							name: '基金经理各能力项',
							description: '慧捕基算法所计算的所有基金经理能力项',
							url: 'ManagerAllCapability',
							condition: [],
							data: [
								{ label: '基金经理代码', value: 'manager_code', description: '"manager_code":"2020201"' },
								{ label: '该基金经理能力项列表', value: 'item', description: '"item":"[择股能力,交易能力]"' }
							]
							// manager_code:['item1', 'item2', ....]
						},
						{
							name: '基金经理资产配置比例',
							description: '通过慧捕基算法估算的基金经理在各个资产上面的配置情况',
							url: 'ManagerAllocationDetails',
							condition: [],
							data: [
								{ label: '基金经理代码', value: 'manager_code', description: '"manager_code":"30189741"' },
								{ label: '年份', value: 'year', description: '"year":"2020"' },
								{ label: '季度', value: 'quarter', description: '"quarter":"1"' },
								{ label: '股票占净值比', value: 'equity_weight', description: '"equity_weight":"85.13195952785914"' },
								{ label: '债券占净值比', value: 'bond_weight', description: '"bond_weight":"0.16884491381168041"' },
								{ label: '基金占净值比', value: 'fund_weight', description: '"fund_weight":"--"' },
								{ label: '权证占净值比', value: 'option_weight', description: '"option_weight":"--"' },
								{ label: '买入反手金融资产占净值比', value: 'repo_weight', description: '"repo_weight":"3.8134092793550654"' },
								{ label: '现金占净值比', value: 'cash_weight', description: '"cash_weight":"10.878846275415398"' },
								{ label: '其它资产占净值比', value: 'other_weight', description: '"other_weight":"0.6483519549793711"' }
							]
						},
						{
							name: '基金经理风格信息',
							description: '基金经理风格信息',
							url: 'ManagerStyleInfo',
							condition: [],
							data: [
								{ label: '基金公司代码', value: 'company_code', description: '"company_code":"41634"' },
								{ label: '基金经理姓名', value: 'name', description: '"name":"萧楠"' },
								{ label: '基金公司名称', value: 'fund_company', description: '"fund_company":"易方达基金管理有限公司"' },
								{ label: '成长价值', value: 'valuegrowth', description: '"valuegrowth":"成长价值"' },
								{ label: '大小盘', value: 'bigsmall', description: '"bigsmall":"大盘"' },
								{ label: '个股择时', value: 'timing', description: '"timing":"趋势"' },
								{ label: '进攻防守', value: 'volatility', description: '"volatility":"防守"' },
								{ label: '大行业', value: 'industrysector', description: '"industrysector":"大消费"' }
							]
						}
					]
				}
			],
			currentKey: '',
			defaultProps: {
				children: 'children',
				label: 'name'
			}
		};
	},
	methods: {
		filterNode(value, data) {
			if (!value) return true;
			return data.name.indexOf(value) !== -1;
		},
		handleNodeClick(data) {
			if (!data?.children) {
				this.currentKey = data?.$treeNodeId;
				this.$emit('handleChoose', data);
			}
		},
		searchItem() {
			this.$refs.tree.filter(this.content);
		}
	}
};
</script>

<style lang="scss">
.dictionary_name {
	.el-input__inner {
		border: none;
		border-bottom: 1px solid #e9e9e9;
	}
	.el-tree-node__label {
		overflow: hidden;
		text-overflow: ellipsis;
	}
	.el-tree-node:focus {
		> .el-tree-node__content {
			overflow: hidden;
			text-overflow: ellipsis;
			background-color: rgba(255, 145, 3, 0.1) !important;
			color: #4096ff !important;
		}
	}
	.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
		background-color: rgba(255, 145, 3, 0.1) !important;
		color: #4096ff !important;
	}
}
</style>
