<!--  -->
<template>
  <div class="filter_new">
    <div class="boxTitleHeader"><span class="headerFontSmall">筛选/<span v-if="!ismanager">基金产品</span><span v-else>基金经理</span>/</span><span class="headerFontBig">打分卡</span></div>
    <div>
      <!-- 头部步骤条 -->
      <div class="step_filter_new">
        <div style="display: flex; align-items: center; justify-content: center; height: 100%">
          <div style="height: 24px">
            <svg v-if="activeID <= 1"
                 width="24"
                 height="24"
                 viewBox="0 0 24 24"
                 fill="none"
                 xmlns="http://www.w3.org/2000/svg">
              <circle cx="12"
                      cy="12"
                      r="11.5"
                      :stroke="activeID == 1 ? '#4096ff' : 'black'"
                      stroke-opacity="0.45" />
              <path d="M8.40269 8.656C8.78669 8.656 9.16002 8.624 9.52269 8.56C9.88535 8.496 10.2107 8.38933 10.4987 8.24C10.7974 8.09067 11.048 7.89867 11.2507 7.664C11.464 7.42933 11.6027 7.14133 11.6667 6.8H13.1547V18H11.1547V10.096H8.40269V8.656Z"
                    :fill="activeID == 1 ? '#4096ff' : 'black'"
                    fill-opacity="0.45" />
            </svg>
            <svg v-else
                 width="24"
                 height="24"
                 viewBox="0 0 24 24"
                 fill="none"
                 xmlns="http://www.w3.org/2000/svg">
              <circle cx="12"
                      cy="12"
                      r="11.5"
                      stroke="#4096ff" />
              <path d="M19.3177 6.22217H18.0695C17.8945 6.22217 17.7285 6.30253 17.6213 6.44003L10.2588 15.7668L6.72846 11.2936C6.67505 11.2258 6.60698 11.171 6.52935 11.1332C6.45173 11.0955 6.36656 11.0758 6.28025 11.0757H5.03203C4.91239 11.0757 4.84632 11.2132 4.91953 11.3061L9.8106 17.5025C10.0392 17.7918 10.4785 17.7918 10.7088 17.5025L19.4302 6.45074C19.5035 6.35967 19.4374 6.22217 19.3177 6.22217Z"
                    fill="#4096ff" />
            </svg>
          </div>
          <div :style="activeID == 1 ? 'color:#4096ff' : ''"
               class="step_filter_font">打分指标</div>
          <div style="margin: 0px 16px; height: 1px; width: 26.67px; background: #4096ff"></div>
          <div style="height: 24px">
            <svg v-if="activeID <= 2"
                 width="24"
                 height="24"
                 viewBox="0 0 24 24"
                 fill="none"
                 xmlns="http://www.w3.org/2000/svg">
              <circle cx="12"
                      cy="12"
                      r="11.5"
                      :stroke="activeID == 2 ? '#4096ff' : 'black'"
                      stroke-opacity="0.45" />
              <path d="M15.8267 18H8.17869C8.18935 17.072 8.41335 16.2613 8.85069 15.568C9.28802 14.8747 9.88535 14.272 10.6427 13.76C11.0054 13.4933 11.384 13.2373 11.7787 12.992C12.1734 12.736 12.536 12.464 12.8667 12.176C13.1974 11.888 13.4694 11.5787 13.6827 11.248C13.896 10.9067 14.008 10.5173 14.0187 10.08C14.0187 9.87733 13.992 9.664 13.9387 9.44C13.896 9.20533 13.8054 8.992 13.6667 8.8C13.528 8.59733 13.336 8.432 13.0907 8.304C12.8454 8.16533 12.5254 8.096 12.1307 8.096C11.768 8.096 11.464 8.17067 11.2187 8.32C10.984 8.45867 10.792 8.656 10.6427 8.912C10.504 9.15733 10.3974 9.45067 10.3227 9.792C10.2587 10.1333 10.2214 10.5013 10.2107 10.896H8.38669C8.38669 10.2773 8.46669 9.70667 8.62669 9.184C8.79735 8.65067 9.04802 8.192 9.37869 7.808C9.70935 7.424 10.1094 7.12533 10.5787 6.912C11.0587 6.688 11.6134 6.576 12.2427 6.576C12.9254 6.576 13.496 6.688 13.9547 6.912C14.4134 7.136 14.7814 7.41867 15.0587 7.76C15.3467 8.10133 15.5494 8.47467 15.6667 8.88C15.784 9.27467 15.8427 9.65333 15.8427 10.016C15.8427 10.464 15.7734 10.8693 15.6347 11.232C15.496 11.5947 15.3094 11.9307 15.0747 12.24C14.84 12.5387 14.5734 12.816 14.2747 13.072C13.976 13.328 13.6667 13.568 13.3467 13.792C13.0267 14.016 12.7067 14.2293 12.3867 14.432C12.0667 14.6347 11.768 14.8373 11.4907 15.04C11.224 15.2427 10.9894 15.456 10.7867 15.68C10.584 15.8933 10.4454 16.1227 10.3707 16.368H15.8267V18Z"
                    :fill="activeID == 2 ? '#4096ff' : 'black'"
                    fill-opacity="0.45" />
            </svg>
            <svg v-else
                 width="24"
                 height="24"
                 viewBox="0 0 24 24"
                 fill="none"
                 xmlns="http://www.w3.org/2000/svg">
              <circle cx="12"
                      cy="12"
                      r="11.5"
                      stroke="#4096ff" />
              <path d="M19.3177 6.22217H18.0695C17.8945 6.22217 17.7285 6.30253 17.6213 6.44003L10.2588 15.7668L6.72846 11.2936C6.67505 11.2258 6.60698 11.171 6.52935 11.1332C6.45173 11.0955 6.36656 11.0758 6.28025 11.0757H5.03203C4.91239 11.0757 4.84632 11.2132 4.91953 11.3061L9.8106 17.5025C10.0392 17.7918 10.4785 17.7918 10.7088 17.5025L19.4302 6.45074C19.5035 6.35967 19.4374 6.22217 19.3177 6.22217Z"
                    fill="#4096ff" />
            </svg>
          </div>
          <div :style="activeID == 2 ? 'color:#4096ff' : ''"
               class="step_filter_font">打分结果</div>
        </div>
      </div>
    </div>
    <div v-show="activeID == 1">
      <scoreC @scoreResult="scoreResult"
              @sameScore="sameScore"
              @back="back"
              ref="scoreC"></scoreC>
    </div>
    <div v-show="activeID == 2">
      <scoreR @changeStep2="changeStep2"
              ref="scoreR"></scoreR>
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import filterC from '../beta/filter';
import scoreC from './score';
import filterR from '../beta/filterResult.vue';
import scoreR from './scoreResult.vue';
export default {
  props: {
    ismanager: Boolean
  },
  //import引入的组件需要注入到对象中才能使用
  components: { filterC, filterR, scoreC, scoreR },
  data () {
    //这里存放数据
    return {
      activeID: 1
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    sameScore () {
      this.activeID = 2;
      this.localStorage.setItem('mty_filterNew_index2', JSON.stringify(this.activeID));
    },
    //过滤结果切换页面
    changeStep (e) {
      if (e == 1) {
        this.$refs.scoreC.Init();
      } else {
      }
      this.localStorage.setItem('mty_filterNew_index2', JSON.stringify(this.activeID));
    },
    changeStep2 (e) {
      if (e == 1) {
        this.activeID = 1;
        this.$refs.scoreC.Init();
      } else {
        this.$refs.filterC.Init();
        this.activeID = 1;
      }
      this.localStorage.setItem('mty_filterNew_index2', JSON.stringify(this.activeID));
    },
    back () {
      this.activeID = 2;
      this.localStorage.setItem('mty_filterNew_index2', JSON.stringify(this.activeID));
    },
    scoreResult (e, e1, e2) {
      this.activeID = 2;
      this.$nextTick(() => {
        this.$refs.scoreR.getData(e, e1, e2);
      });
      this.localStorage.setItem('mty_filterNew_index2', JSON.stringify(this.activeID));
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () {
    this.$refs.scoreC.Init();
    if (
      this.localStorage.getItem('mty_filterNew_index2') != null &&
      this.localStorage.getItem('mty_filterNew_index2') != undefined &&
      this.localStorage.getItem('mty_filterNew_index2') != 'null' &&
      this.localStorage.getItem('mty_filterNew_index2') != 'undefined'
    ) {
      this.$nextTick(() => {
        this.activeID = Number(JSON.parse(this.localStorage.getItem('mty_filterNew_index2')));
        if (this.activeID == '1') {
          this.$refs.scoreC.Init();
        }
      });
    }
  },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
.filter_new {
  padding: 24px;
  // height: calc(100vh - 261px);
  overflow: auto;
}
.step_filter_new {
  height: 72px;
  width: 100%;

  background: #ffffff;
  box-shadow: 0px 1px 0px #e9e9e9;
  border-radius: 4px 4px 0px 0px;
}
.step_filter_font {
  font-family: "PingFang";
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: rgba(0, 0, 0, 0.65);
  margin-left: 16px;
}
</style>
