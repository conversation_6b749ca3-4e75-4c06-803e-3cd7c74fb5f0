<template>
	<div id="brinsonAttribution" class="brinsonAttribution">
		<analysis-card-title title="Brinson归因" image_id="brinsonAttribution">
			<el-select v-model="activeBenchmark">
				<el-option v-for="item in benchmarkList" :key="item.code" :label="item.name" :value="item.code"></el-option>
			</el-select>
		</analysis-card-title>
		<div v-loading="loading">
			<el-table :data="data" style="width: 100%" border stripe>
				<el-table-column
					v-for="item in column"
					:key="item.value"
					:prop="item.value"
					:label="item.label"
					:min-width="item.width"
					:align="item.align ? item.align : 'gotoleft'"
				>
					<template slot-scope="{ row }">
						<div v-if="item.flag == 'chart'">
							<v-chart
								:ref="item.value + row.industry_name"
								:options="formatChart(row[item.value])"
								style="width: 163px; height: 46px"
							></v-chart>
						</div>
						<div v-else-if="item.flag == 'background'">
							<div
								v-if="item.color == 'yellow'"
								class="pr-8"
								:style="`background: linear-gradient(90deg, rgba(255, 145, 3, 0.2) 0%, rgba(255, 145, 3, 0.03) ${item.format(
									row[item.value]
								)}`"
							>
								{{ item.format ? item.format(row[item.value]) : row[item.value] }}
							</div>
							<div
								v-else
								class="pr-8"
								:style="
									row[item.value] > 0
										? `background: linear-gradient(90deg, rgba(207, 19, 34, 0.2) 0%, rgba(207, 19, 34, 0.03) ${item.format(
												row[item.value]
										  )}`
										: `background: linear-gradient(90deg, rgba(56, 158, 13, 0.2)  0%, rgba(56, 158, 13, 0.03) ${item.format(
												row[item.value] * -1
										  )}`
								"
							>
								{{ item.format ? item.format(row[item.value]) : row[item.value] }}
							</div>
						</div>
						<div class="ml-8" v-else>{{ item.format ? item.format(row[item.value]) : row[item.value] }}</div>
					</template>
				</el-table-column>
			</el-table>
		</div>
		<div class="mt-20" v-if="showDescription">
			<analysis-description id="brinsonAttributionDescription" :is_column="true" :description="description"></analysis-description>
		</div>
	</div>
</template>

<script>
// 模型使用说明
import analysisDescription from '@/components/components/components/analysisDescription/index.vue';

import { getBenchmarkList, getMultiBrision } from '@/api/pages/Analysis.js';

export default {
	name: 'brinsonAttribution',
	components: { analysisDescription },
	props: {
		showDescription: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			loading: true,
			data: [],
			activeBenchmark: '',
			benchmarkList: [],
			column: [
				{
					label: '行业',
					value: 'industryName'
				},
				{
					label: '配置比例',
					value: 'weightList',
					flag: 'chart',
					align: 'right',
					type: 'image'
				},
				{
					label: '平均配置比例',
					value: 'meanWeight',
					align: 'right',
					flag: 'background',
					color: 'yellow',
					format: this.fix2p
				},
				{
					label: '超欠配比例',
					value: 'changeWeightList',
					flag: 'chart',
					align: 'right',
					type: 'image'
				},
				{
					label: '平均超欠配比例',
					value: 'changeWeightMean',
					align: 'right',
					flag: 'background',
					color: 'red_or_green',
					format: this.fix2p
				},
				{
					label: '超额收益',
					value: 'excessReturn',
					align: 'right',
					flag: 'background',
					color: 'red_or_green',
					format: this.fix2p
				},
				{
					label: '行业配置收益',
					value: 'allocationReturn',
					align: 'right',
					flag: 'background',
					color: 'red_or_green',
					format: this.fix2p
				},
				{
					label: '证券选择收益',
					value: 'chooseReturn',
					align: 'right',
					flag: 'background',
					color: 'red_or_green',
					format: this.fix2p
				},
				{
					label: '其他收益',
					value: 'otherReturn',
					align: 'right',
					flag: 'background',
					format: this.fix2p
				}
			],
			info: {}
		};
	},
	computed: {
		description() {
			return `Brinson公式是一种用于评估投资组合绩效的常用工具。它通过将投资组合的超额回报分解为主动回报和配置回报，帮助投资者分析和评估投资经理的投资决策能力和配置能力。`;
		}
	},
	methods: {
		async getData(info) {
			this.info = info;
			await this.getBenchmarkList();
			this.getMultiBrision();
			// this.data = [
			// 	{
			// 		industry_name: '食品饮料',
			// 		rank: this.returnMock(),
			// 		avg_rank: '0.2',
			// 		super_rank: this.returnMock(),
			// 		avg_super_rank: '0.3',
			// 		excess_return: '-0.4',
			// 		industry_return: '0.5',
			// 		stock_return: '-0.6',
			// 		other_return: '0.7'
			// 	}
			// ];
		},
		async getMultiBrision() {
			this.loading = true;
			let data = await getMultiBrision({
				code: this.info.code,
				type: this.info.type,
				flag: this.info.flag,
				start_date: this.info.start_date,
				end_date: this.info.end_date,
				index_code: this.activeBenchmark
			});
			this.loading = false;
			if (data.mtycode == 200) {
				this.data = data?.data.filter((v) => v.industryCode !== 'all' && v.industryCode !== '--');
			}
		},
		// 发送请求，获取基准列表
		async getBenchmarkList() {
			let data = await getBenchmarkList({
				code: this.info.code,
				type: this.info.type,
				flag: this.info.flag,
				template: 'brinsonAttribution'
			});
			this.benchmarkList = [];
			this.activeBenchmark = '';
			if (data?.mtycode == 200) {
				this.benchmarkList = data?.data
					.sort((a, b) => {
						return b.isdefault - a.isdefault;
					})
					.map((v) => {
						return { code: v.indexCode, name: v.indexName, flag: v.flag };
					});
				if (this.benchmarkList.findIndex((v) => v.code == '000300.SH') != -1) {
					this.activeBenchmark = '000300.SH';
				} else {
					this.activeBenchmark = this.benchmarkList?.[0]?.code;
				}
			} else {
				this.benchmarkList = this.COMMON.default_index[this.info.type].map((v) => {
					return { code: v.indexCode, name: v.indexName, flag: '6' };
				});
				this.activeBenchmark = this.benchmarkList?.[0]?.code;
			}
		},
		fix2p(val) {
			return val * 1 && !isNaN(val) ? (val * 1).toFixed(2) + '%' : '--';
		},
		formatChart(data) {
			return {
				xAxis: {
					show: false,
					type: 'category',
					boundaryGap: false,
					data: data.map((v, index) => index)
				},
				grid: {
					top: '8px',
					left: '4px',
					right: '4px',
					bottom: '8px'
				},
				yAxis: {
					show: false,
					type: 'value',
					min: Math.min(...data),
					max: Math.max(...data)
				},
				series: [
					{
						data: data.map((v) => v),
						type: 'line',
						symbol: 'none',
						lineStyle: {
							color: '#64A8FF'
						},
						areaStyle: {
							color: new echarts.graphic.LinearGradient(1, 1, 1, 0, [
								{ offset: 0, color: 'rgba(69, 118, 233, 0.00)' },
								{ offset: 1, color: 'rgba(69, 118, 233, 0.25)' }
							])
						}
					}
				]
			};
		},
		async createPrintWord(info) {
			await this.getData(info);
			return await new Promise((resolve, reject) => {
				this.$nextTick(async () => {
					let list = this.column.map((item) => {
						return { label: item.label, value: item.value, type: item?.type };
					});
					let height = 0;
					let width = 0;
					let data = this.data.map((item) => {
						let obj = {};

						for (const key in item) {
							let index = this.column.findIndex((v) => v.value == key);
							if (this.column[index]?.type == 'image') {
								let ref = this.column[index].value + item.industry_name;
								height = this.$refs[ref][0]?.$el.clientHeight;
								width = this.$refs[ref][0]?.$el.clientWidth;
								let chart = this.$refs[ref][0]?.getDataURL({
									type: 'jpg',
									pixelRatio: 3,
									backgroundColor: '#fff'
								});
								obj[key] = chart;
							} else {
								if (this.column[index]?.format) {
									obj[key] = this.column[index]?.format(item[key]);
								} else {
									obj[key] = item[key];
								}
							}
						}
						return obj;
					});
					//   模型使用说明
					let id = 'brinsonAttributionDescription';
					let height_des = document.getElementById(id).clientHeight || 196;
					let width_des = document.getElementById(id).clientWidth || 1642;
					let canvas = await this.html2canvas(document.getElementById(id), {
						scale: 3
					});
					resolve([
						...this.$exportWord.exportTitle('Brinson归因'),
						...this.$exportWord.exportDescripe('基准为：' + this.benchmarkList.find((v) => v.code == this.activeBenchmark)?.name),
						...this.$exportWord.exportTable(list, data, { width, height }),
						...this.$exportWord.exportChart(canvas.toDataURL('image/jpg'), {
							width: width_des,
							height: height_des
						})
					]);
				});
			});
		}
	}
};
</script>
<style lang="scss" scoped>
.brinsonAttribution {
	::v-deep.el-table__body {
		.el-table__row {
			.el-table__cell {
				.cell {
					padding: 0;
					> div {
						height: 46px;
						line-height: 46px;
					}
				}
			}
		}
	}
}
</style>
