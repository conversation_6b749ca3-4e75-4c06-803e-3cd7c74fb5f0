<template>
	<div class="small_template" id="newFund" :style="downloadShow ? '' : ''">
		<div style="display: flex; justify-content: space-between; align-items: center">
			<div class="title">基金发行时间</div>
			<el-button icon="el-icon-document-delete" @click="exportExcel">导出Excel</el-button>
		</div>
		<div v-loading="loading">
			<div class="new_fund_list" v-if="list.length > 0">
				<div class="new_fund_item" v-for="(item, index) in list" :key="item.date">
					<div class="item_date">{{ item.date }}</div>
					<div
						class="new_fund_scatter"
						:style="`${item.date == '募集中' ? 'background: #4096ff;box-shadow: 0px 0px 4px 2px #4096ff;' : ''}`"
					>
						<div v-show="index != list.length - 1" class="new_fund_scatter_line"></div>
					</div>
					<el-tooltip placement="top">
						<div slot="content">{{ item.description }}</div>
						<div class="item_description">{{ item.description }}</div>
					</el-tooltip>
				</div>
			</div>
			<div v-else>
				<el-empty description="暂无数据"></el-empty>
			</div>
		</div>
	</div>
</template>

<script>
// 新发基金
import { getNewDevelopmentFund } from '@/api/pages/SystemOther.js';
import { filter_json_to_excel } from '@/utils/exportExcel.js';

export default {
	data() {
		return {
			loading: true,
			downloadShow: true,
			list: [],
			info: {}
		};
	},
	methods: {
		async getData(info) {
			this.info = info;
			if (this.getCacheData('newDevelopmentFund')) {
				this.list = this.getCacheData('newDevelopmentFund');
			} else {
				let data = await getNewDevelopmentFund({ code: this.info.code });
				if (data?.mtycode == 200) {
					this.list = data?.result

						?.map((item) => {
							return {
								...item,
								date: item.date == '--' ? '募集中' : item.date
							};
						})
						?.sort((a, b) => {
							return this.moment(b.date).isBefore(a.date) ? -1 : 1;
						});
				}
			}
			this.loading = false;
		},
		// 导出图片
		exportImage() {
			this.downloadShow = false;
			setTimeout(() => {
				this.html2canvas(document.getElementById('newFund'), { scale: 3 }).then((canvas) => {
					let base64Str = canvas.toDataURL('image/png');
					let aLink = document.createElement('a');
					aLink.style.display = 'none';
					aLink.href = base64Str;
					aLink.download = this.managername + '简介.jpg';
					// 触发点击-然后移除
					document.body.appendChild(aLink);
					aLink.click();
					document.body.removeChild(aLink);
					this.downloadShow = true;
				});
			}, 100);
		},
		exportExcel() {
			let list = [
				{ label: '时间', value: 'date' },
				{ label: '描述', value: 'description' }
			];
			filter_json_to_excel(list, this.list, this.info.name + '-' + '基金发行时间');
		}
	}
};
</script>

<style lang="scss" scoped>
.new_fund_list {
	height: 375px;
	overflow: auto;
	.new_fund_item {
		display: flex;
		align-items: center;
		margin: 8px 0;
		.item_date {
			width: 73px;
		}
		.item_description {
			overflow: hidden;
			text-overflow: ellipsis; /* 超出部分省略号 */
			word-break: break-all; /* break-all(允许在单词内换行。) */
			display: -webkit-box; /* 对象作为伸缩盒子模型显示 */
			-webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
			-webkit-line-clamp: 1; /* 显示的行数 */
			flex: 1;
		}
		.new_fund_scatter {
			width: 10px;
			height: 10px;
			border-radius: 50%;
			background: #4096ff;
			box-shadow: 0px 0px 4px 2px #4096ff;
			margin: 0 8px;
			position: relative;
			.new_fund_scatter_line {
				position: absolute;
				left: 4px;
				top: 10px;
				width: 2px;
				height: 14px;
				background-color: gray;
			}
		}
	}
}
</style>
