import request from '@/utils/request';
import requestSystem from '@/utils/requestLee';

// 行业
export function alphamsg() {
	return request({
		url: '/system/alpha/alphamsg/',
		method: 'get'
	});
}
// 主题
export function getCharacteristicsBarra() {
	return request({
		url: '/factorexposed/',
		method: 'get'
	});
}
// 异常点
export function abnorlmalPoint(params) {
	return request({
		url: '/Analysis/ReturnAbnormal/',
		method: 'get',
		params
	});
}

// 国债list
export function getcomList() {
	return request({
		url: '/getcomList/',
		method: 'get'
	});
}
