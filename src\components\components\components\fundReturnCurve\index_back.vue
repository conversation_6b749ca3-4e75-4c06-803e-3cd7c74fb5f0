<template>
	<div>
		<div>
			<div id="fundReturnCurve">
				<div
					v-show="showBasic"
					style="display: flex; justify-content: center; align-self: center; width: 100%; position: relative"
					class="height60"
				>
					<div @click="push(1)" class="headbenchmarkbox">
						<div :class="t1 ? 'benchmarkline1' : 'hidebox'"></div>
						<div>
							<span :class="t1 ? 'fonst' : 'fonstgrep'">{{ info.name }}</span>
						</div>
					</div>
					<!-- <div @click="push(2)" class="headbenchmarkbox">
					<div :class="t2 ? 'benchmarkline2' : 'hidebox'"></div>
					<div>
						<div>
							<span v-if="info.type == 'equity'" :class="t2 ? 'fonst' : 'fonstgrep'">慧捕基主动权益同类平均</span>
							<span v-else-if="info.type == 'bond'" :class="t2 ? 'fonst' : 'fonstgrep'">慧捕基固收+同类平均</span>
							<span v-else-if="info.type == 'bill'" :class="t2 ? 'fonst' : 'fonstgrep'">慧捕基中短债同类平均</span>
							<span v-else-if="info.type == 'purebond'" :class="t2 ? 'fonst' : 'fonstgrep'">慧捕基纯债同类平均</span>
							<span v-else-if="info.type == 'cbond'" :class="t2 ? 'fonst' : 'fonstgrep'">慧捕基可转债同类平均</span>
							<span v-else-if="info.type == 'hkequity'" :class="t2 ? 'fonst' : 'fonstgrep'">慧捕基港股同类平均</span>
							<span v-else-if="info.type == 'equityindex'" :class="t2 ? 'fonst' : 'fonstgrep'">慧捕基被动权益同类平均</span> -->
					<!-- <span v-else-if="info.type.indexOf('fof') >= 0" :class="t2 ? 'fonst' : 'fonstgrep'">慧捕基FOF同类平均</span> -->
					<!-- <span v-else :class="t2 ? 'fonst' : 'fonstgrep'">同类平均</span>
						</div>
					</div>
				</div> -->
					<div @click="push(3)" class="headbenchmarkbox">
						<div :class="t3 ? 'benchmarkline3' : 'hidebox'"></div>
						<div class="headwidth160">
							<el-select v-model="bench1" placeholder="请选择" @change="changeBench()">
								<el-option
									v-for="(item, index) in benchoption"
									:key="index"
									:label="item.name"
									:value="item.index_code"
									:disabled="item.index_code == bench2"
								>
								</el-option>
							</el-select>
						</div>
					</div>
					<div @click="push(4)" class="headbenchmarkbox">
						<div :class="t4 ? 'benchmarkline4' : 'hidebox'"></div>
						<div class="headwidth160">
							<el-select v-model="bench2" placeholder="请选择" @change="changeBench()">
								<el-option
									v-for="(item, index) in benchoption"
									:key="index"
									:label="item.name"
									:value="item.index_code"
									:disabled="item.index_code == bench1"
								>
								</el-option>
							</el-select>
						</div>
					</div>
					<div v-show="iconShow" style="line-height: 32px; position: absolute; right: 0" @click="downloadImage">
						<i class="el-icon-download" style="font-size: 20px"></i>
					</div>
				</div>
				<div class="avgbox" style="margin: 10px 0">
					<div class="selfde">
						{{ info.name }}:<span :class="num1 > 0 ? 'colr2' : 'colr1'">{{ (num1 * 100).toFixed(2) }}%</span>
					</div>
					<!-- <div class="avgde">
					同类平均:<span :class="num2 > 0 ? 'colr2' : 'colr1'">{{ (num2 * 100).toFixed(2) }}%</span>
				</div> -->
				</div>
				<div style="page-break-inside: avoid; position: relative" v-loading="yejishowflag">
					<v-chart
						element-loading-text="暂无数据"
						element-loading-spinner="el-icon-document-delete"
						element-loading-background="rgba(239, 239, 239, 0.5)"
						:style="printActive ? 'width: 1000px; height: 400px;' : 'width: 100%; height: 412px; page-break-inside: avoid'"
						autoresize
						:options="yejioptions"
						ref="fundReturnCurveChart"
						@click.native="watchMouseDown"
					/>
				</div>
			</div>

			<div v-show="showBasic">
				<div class="print_show" style="font-size: 14px; margin-top: 16px">
					<div style="display: flex; flex-wrap: wrap; justify-content: space-between; align-items: center">
						<div>
							<el-button
								:class="activeid == 1 ? 'yearbuttonact' : 'yearbutton'"
								@click="changeActive(1)"
								style="font-family: PingFangSC-Regular, PingFang; font-weight: 400; color: #333333"
								type="text"
								>近一月</el-button
							>
						</div>
						<div class="linebox"><span class="linecolum"></span></div>
						<div>
							<el-button
								:class="activeid == 2 ? 'yearbuttonact' : 'yearbutton'"
								@click="changeActive(2)"
								style="font-family: PingFangSC-Regular, PingFang; font-weight: 400; color: #333333"
								type="text"
								>近三月</el-button
							>
						</div>
						<div class="linebox"><span class="linecolum"></span></div>
						<div>
							<el-button
								:class="activeid == 3 ? 'yearbuttonact' : 'yearbutton'"
								@click="changeActive(3)"
								style="font-family: PingFangSC-Regular, PingFang; font-weight: 400; color: #333333"
								type="text"
								>近半年</el-button
							>
						</div>
						<div class="linebox"><span class="linecolum"></span></div>
						<div>
							<el-button
								:class="activeid == 4 ? 'yearbuttonact' : 'yearbutton'"
								@click="changeActive(4)"
								style="font-family: PingFangSC-Regular, PingFang; font-weight: 400; color: #333333"
								type="text"
								>近一年</el-button
							>
						</div>
						<div class="linebox"><span class="linecolum"></span></div>
						<div>
							<el-button
								:class="activeid == 5 ? 'yearbuttonact' : 'yearbutton'"
								@click="changeActive(5)"
								style="font-family: PingFangSC-Regular, PingFang; font-weight: 400; color: #333333"
								type="text"
								>近三年</el-button
							>
						</div>
						<div class="linebox"><span class="linecolum"></span></div>
						<div>
							<el-button
								:class="activeid == 6 ? 'yearbuttonact' : 'yearbutton'"
								@click="changeActive(6)"
								style="font-family: PingFangSC-Regular, PingFang; font-weight: 400; color: #333333"
								type="text"
								>近五年</el-button
							>
						</div>
						<div class="linebox"><span class="linecolum"></span></div>
						<div>
							<el-button
								:class="activeid == 7 ? 'yearbuttonact' : 'yearbutton'"
								@click="changeActive(7)"
								style="font-family: PingFangSC-Regular, PingFang; font-weight: 400; color: #333333"
								type="text"
								>今年以来</el-button
							>
						</div>
						<div class="linebox"><span class="linecolum"></span></div>
						<div>
							<el-button
								:class="activeid == 8 ? 'yearbuttonact' : 'yearbutton'"
								@click="changeActive(8)"
								style="font-family: PingFangSC-Regular, PingFang; font-weight: 400; color: #333333"
								type="text"
								>成立以来</el-button
							>
						</div>
						<div class="linebox"><span class="linecolum"></span></div>
						<div>
							<el-button
								:class="activeid == 9 ? 'yearbuttonact' : 'yearbutton'"
								@click="changeBasicShow"
								style="font-family: PingFangSC-Regular, PingFang; font-weight: 400; color: #333333"
								type="text"
								>自定义</el-button
							>
						</div>
					</div>
				</div>
			</div>
			<div v-show="!showBasic" class="print_show">
				<div style="display: flex; flex-direction: row-reverse">
					<div class="boxslefhead marginright202">
						<el-button @click="changeBasicShow"><i style="color: #4096FF" class="el-icon-back"></i></el-button>
					</div>
					<div style="display: flex; flex-direction: row; width: 100%; align-items: center">
						<div class="boxslefhead">
							<el-date-picker
								v-model="value1"
								type="daterange"
								unlink-panels
								range-separator="-"
								start-placeholder="开始日期"
								end-placeholder="结束日期"
								value-format="yyyy-MM-dd"
								@change="selectDate"
							>
							</el-date-picker>
						</div>
						<div class="boxslefhead" style="display: flex">
							<el-select
								v-model="values"
								:remote-method="searchpeople"
								filterable
								remote
								prefix-icon="el-icon-search"
								:loading="loading"
								placeholder="输入简拼、代码、名称选择基金/基准"
								@change="selectData"
							>
								<el-option-group v-for="groups in havefundmanager" :key="groups.label" :label="groups.label">
									<el-option v-for="group in groups.options" :key="group.code" :label="group.name" :value="group.code"> </el-option>
								</el-option-group>
							</el-select>
						</div>
						<!-- <div class="boxslefhead" v-if="info.type !== 'manager'" style="display: flex; align-items: center">
							<div style="display: flex; align-items: center">
								<el-checkbox v-model="radio">去打新收益</el-checkbox>
							</div>
						</div> -->
					</div>
				</div>
				<div style="display: flex; flex-wrap: wrap" class="margintop10">
					<div v-for="(item, index) in list" :key="index" class="tagselfbox">
						<el-tooltip
							style="cursor: pointer"
							v-if="item.name.length > 6"
							class="item"
							effect="dark"
							:content="item.name"
							placement="right-start"
						>
							<el-tag
								closable
								:style="`background:${color[index + 1]}!important;border:0px !important`"
								@close="handleClose(item.code)"
								:key="item.code"
								class="boxlabels"
								>{{ item.name.slice(0, 6) }}</el-tag
							>
						</el-tooltip>
						<el-tag
							closable
							:style="`background:${color[index + 1]}!important;border:0px !important`"
							@close="handleClose(item.code)"
							:key="item.code"
							class="boxlabels"
							v-else
							>{{ item.name }}</el-tag
						>
					</div>
				</div>
				<div v-show="list.length == 0 || value1 == ''" class="height20border"></div>
				<div v-show="list.length == 0 || value1 == ''" class="height20border"></div>
			</div>

			<div v-show="!showBasic">
				<div class="height20border"></div>
				<div v-show="list.length == 0 || value1 == ''" class="height20border"></div>
			</div>
		</div>
	</div>
</template>
<script>
// 基金收益曲线

import VCharts from 'vue-echarts';
// 引入提示框和title组件
import { Search } from '@/api/pages/Analysis.js';
import { exportChart, exportTitle } from '@/utils/exportWord.js';
import { lineChartOption } from '@/utils/chartStyle.js';
import { abnorlmalPoint } from '@/api/pages/SystemAlpha.js';

export default {
	name: 'fundReturnCurve',
	components: { 'v-chart': VCharts },
	props: {
		info: {
			type: Object
		}
	},
	data() {
		//这里存放数据
		return {
			abnormalP: [],
			showBasic: true,
			yejioptions: {},
			printActive: false,
			yejishowflag: false,
			activeid: 8,
			bench1: null,
			bench1name: '',
			bench2name: '',
			bench2: null,
			benchoption: [],
			num1: null,
			num2: null,
			t1: true,
			t2: true,
			t3: true,
			t4: true,
			dataarr: [],
			color: [
				'#4096FF',
				'#FFB6C1',
				'#DB7093',
				'#DA70D6',
				'#800080',
				'#9370DB',
				'#6A5ACD',
				'#4169E1',
				'#B0C4DE',
				'#4682B4',
				'#5F9EA0',
				'#8FBC8F',
				'#EEE8AA',
				'#FFD700',
				'#FFA500',
				'#FF6347',
				'#CD5C5C',
				'#B22222',
				'#D3D3D3',
				'#A9A9A9',
				'#FA8072',
				'#929694',
				'#40BFDD',
				'#C2B12F',
				'#ffa94d',
				'#fcc419',
				'#94d82d',
				'#94C5DE',
				'#B7A7D7',
				'#FDDBC7',
				'#F3A483',
				'#D45C4E',
				'#409eff',
				'#f39c12',
				'#ff1744',
				'#d500f9',
				'#2979ff',
				'#00e5ff',
				'#ff5722',
				'#ffea00',
				'#ff3d00',
				'#ff8a80',
				'#ff80ab',
				'#b388ff',
				'#8c9eff',
				'#a7ffeb',
				'#ffff00',
				'#ffab40',
				'#ffebee',
				'#e8eaf6',
				'#e1f5fe',
				'#fffde7',
				'#efebe9'
			],
			colorpan: [],
			series: [],
			dateList: [],
			timeInterval: {},
			radio: '1',
			value1: '',
			values: null,
			loading: true,
			// yejioptions2: {},
			havefundmanager: [],
			listfront: [],
			list: [],
			showing: true,
			postData: {},
			echartData: [],
			current: '',
			symbol: {
				itemStyle: {
					color: '#7581BD'
				},
				showSymbol: true,
				symbolSize: [30, 30],
				symbolKeepAspect: true,
				symbol:
					'path://M22.8843 12.2628L17.4953 16.9274C17.2944 17.1006 17 16.9444 17 16.6646V7.33542C17 7.0556 17.2944 6.89936 17.4953 7.07264L22.8843 11.7372C23.0386 11.8722 23.0386 12.1293 22.8843 12.2628ZM6.50469 7.07264L1.11569 11.7372C0.961436 11.8707 0.961436 12.1278 1.11569 12.2628L6.50469 16.9274C6.70564 17.1006 7 16.9444 7 16.6646V7.33542C7 7.0556 6.70564 6.89936 6.50469 7.07264ZM15 12C15 13.6569 13.6569 15 12 15C10.3431 15 9 13.6569 9 12C9 10.3431 10.3431 9 12 9C13.6569 9 15 10.3431 15 12Z'
			},
			timer: null,
			activeCurrent: '',
			currentList: [],
			iconShow: true
		};
	},
	//方法集合
	methods: {
		async createPrintWord() {
			let item = [];
			let height = document.getElementById('fundReturnCurve')?.clientHeight || 400;
			let width = document.getElementById('fundReturnCurve')?.clientWidth || 1200;
			let canvas = await this.html2canvas(document.getElementById('fundReturnCurve'), { scale: 3 });
			let base64Str = canvas.toDataURL('image/png');
			item = [...exportTitle('业绩曲线'), ...exportChart(base64Str, { width, height })];
			return item;
		},
		downloadImage() {
			this.iconShow = false;
			this.$nextTick(async () => {
				let canvas = await this.html2canvas(document.getElementById('fundReturnCurve'), { scale: 3 });
				let base64Str = canvas.toDataURL('image/png');
				let aLink = document.createElement('a');
				aLink.style.display = 'none';
				aLink.href = base64Str;
				aLink.download = this.info.name + '收益曲线.jpg';
				// 触发点击-然后移除
				document.body.appendChild(aLink);
				aLink.click();
				document.body.removeChild(aLink);
				this.iconShow = true;
			});
		},
		// createPrintWord() {
		// 	let height = this.$refs['fundReturnCurveChart'].$el.clientHeight;
		// 	let width = this.$refs['fundReturnCurveChart'].$el.clientWidth;
		// 	let item = [
		// 		...exportTitle('业绩曲线'),
		// 		...exportChart(
		// 			this.$refs['fundReturnCurveChart'].getDataURL({
		// 				type: 'png',
		// 				pixelRatio: 2,
		// 				backgroundColor: '#fff'
		// 			}),
		// 			{ width, height }
		// 		)
		// 	];
		// 	return item;
		// },
		push(type) {
			switch (type) {
				// 监听基金曲线显示隐藏
				case 1:
					this.t1 = !this.t1;
					break;
				// 监听慧捕基同类平均显示隐藏
				case 2:
					this.t2 = !this.t2;
					break;
				// 监听benchmark1显示隐藏
				case 3:
					this.t3 = !this.t3;
					break;
				// 监听benchmark2显示隐藏
				case 4:
					this.t4 = !this.t4;
					break;
			}
			this.series = [];
			this.formatDataShow(this.echartData);
		},
		// 监听时间变化
		changeActive(val) {
			this.activeid = val;
			let date = new Date();
			this.timeInterval.end_date = this.moment(date).format('YYYY-MM-DD');
			switch (val) {
				// 近一月
				case 1:
					this.timeInterval.start_date = this.moment(date).subtract(1, 'months').format('YYYY-MM-DD');
					break;
				// 近三月
				case 2:
					this.timeInterval.start_date = this.moment(date).subtract(3, 'months').format('YYYY-MM-DD');
					break;
				// 近半年
				case 3:
					this.timeInterval.start_date = this.moment(date).subtract(6, 'months').format('YYYY-MM-DD');
					break;
				// 近一年
				case 4:
					this.timeInterval.start_date = this.moment(date).subtract(1, 'years').format('YYYY-MM-DD');
					break;
				// 近三年
				case 5:
					this.timeInterval.start_date = this.moment(date).subtract(3, 'years').format('YYYY-MM-DD');
					break;
				// 近五年
				case 6:
					this.timeInterval.start_date = this.moment(date).subtract(5, 'years').format('YYYY-MM-DD');
					break;
				// 今年以来
				case 7:
					this.timeInterval.start_date = this.moment(date.getFullYear() + '-01-01').format('YYYY-MM-DD');
					break;
				// 成立以来
				case 8:
					this.timeInterval.start_date = '';
					this.timeInterval.end_date = '';
					break;
			}
			this.series = [];
			this.echartData = [];
			this.currentList = [];
			this.$emit('getReturnTime', this.timeInterval);
		},
		// 监听基准选择变化
		changeBench() {
			this.series = this.series.filter((item) => {
				return item.name == this.info.name;
			});
			this.bench1name = this.benchoption.filter((item) => {
				return item.index_code == this.bench1;
			})[0].name;
			this.bench2name = this.benchoption.filter((item) => {
				return item.index_code == this.bench2;
			})[0].name;
			let flag1 = this.benchoption.filter((item) => {
				return item.index_code == this.bench1;
			})[0].flag;
			let flag2 = this.benchoption.filter((item) => {
				return item.index_code == this.bench2;
			})[0].flag;
			// this.currentList = [];
			this.$emit('getIndexCodes', [
				{ index_code: this.bench1, flag: flag1 },
				{ index_code: this.bench2, flag: flag2 }
			]);
		},
		// 监听点击图发散
		watchMouseDown(val) {
			this.yejishowflag = true;
			// if (!this.showBasic) {
			// 	this.yejishowflag = false;
			// 	return;
			// }
			if (this.timer || this.currentList.indexOf(this.current?.[0]) !== -1 || !this.current?.[0]) {
				this.yejishowflag = false;
				this.currentList.splice(this.currentList.indexOf(this.current?.[0]), 1);
				if (this.currentList.length == 0) {
					this.drawAction(this.series);
					return;
				} else {
					this.timer = setTimeout(() => {
						this.componentdMouseDown();
					}, 100);
				}
			} else {
				this.currentList.push(this.current?.[0]);
			}
			this.timer = setTimeout(() => {
				this.componentdMouseDown();
			}, 100);
		},
		// 图发散数据过滤
		filterMouseDown() {
			let currentDate = this.current?.[0];
			let currentData = this.current?.[1];
			let option = this.series.map((obj) => {
				return {
					...obj,
					data: obj.data.map((item) => {
						return { ...item };
					})
				};
			});
			option.map((obj) => {
				let differenceData =
					obj.data[
						obj.data.findIndex((item) => {
							return item[0] == currentDate;
						})
					]?.[1];
				if (differenceData) {
					let difference = (currentData * 1) / (differenceData * 1); // 值相处
					difference = currentData * difference;
					if (obj.name !== this.info.name) {
						obj.data = obj.data.map((item) => {
							return [item[0], item[1] * 1 * difference * 1];
						});
					} else {
						obj.data = obj.data.map((item) => {
							return [item[0], item[1]];
						});
					}
				}
			});
			this.drawAction(option);
		},
		// 图发散数据计算
		componentdMouseDown() {
			this.timer = null;
			clearTimeout(this.timer);
			// let current = this.current?.[0];
			// this.activeCurrent = current;
			let option = [];
			this.currentList.sort().map((current) => {
				if (current) {
					this.series.map((item) => {
						let object = this.echartData.filter((obj) => {
							return obj.name == item.name;
						})[0];
						let index = object.date.indexOf(current);
						// 从当前点开始计算累计收益
						let data = object.date.slice(index).map((val, i) => {
							return [val, this.computedReturn(object.value.slice(index))[i]];
						});
						if (item.name == this.info.name) {
							let oIndex = option.findIndex((obj) => {
								return obj.name == item.name;
							});
							if (oIndex == -1) {
								option.unshift({
									...item,
									data: [...item.data.slice(0, index), ...data],
									markLine: {
										silent: true,
										symbol: 'none',
										data: [
											{
												name: '买入',
												symbol: 'none',
												y: '100%',
												xAxis: current
											}
										]
									}
								});
							} else {
								option[oIndex].data = [...option[oIndex].data.slice(0, index), ...data];
								option[oIndex].markLine.data.push({
									name: '买入',
									symbol: 'none',
									y: '100%',
									xAxis: current
								});
							}
						} else {
							let oIndex = option.findIndex((obj) => {
								return obj.name == item.name;
							});
							if (oIndex == -1) {
								option.push({
									...item,
									data: [...item.data.slice(0, index), ...data]
								});
							} else {
								option[oIndex].data = [...option[oIndex].data.slice(0, index), ...data];
							}
						}
					});
				}
			});
			let currentData = [];
			option.map((item) => {
				this.currentList.map((val, i) => {
					let index = item.data.findIndex((time) => {
						return time[0] == val;
					});
					if (index != -1) {
						if (i == 0) {
							currentData.push({ ...item, data: item.data.slice(0, index) });
						}
						let indexAfter = item.data.findIndex((time) => {
							return time[0] == this.currentList[i + 1];
						});
						if (indexAfter != -1) {
							currentData.push({ ...item, data: item.data.slice(index, indexAfter) });
						} else {
							currentData.push({ ...item, data: item.data.slice(index) });
						}
					}
				});
			});
			this.drawAction(currentData);
		},
		// 累计收益计算
		computedReturn(data) {
			let cum_return = 1;
			let cum = data.map((item) => {
				cum_return = cum_return * (1 + item);
				return cum_return - 1;
			});
			return cum;
		},
		// 接收父组件数据
		async getData(data) {
			console.log(data);
			this.abnormalP = [];
			let data2 = await abnorlmalPoint({ code: this.info.code });
			if (this.FUNC.isEmpty(data2?.data)) {
				this.abnormalP = data2.data;
			}
			let indexList = [];
			this.havefundmanager.map((item) => {
				item.options.map((obj) => {
					indexList.push(obj);
				});
			});
			data.map((item) => {
				// item name==code
				// this.echartData==name
				let index = this.echartData.findIndex((obj) => {
					return obj.code == item.name || obj.name == item.name;
				});
				if (index == -1) {
					if (item.name == this.info.code) {
						this.echartData.push({
							...item,
							name: this.info.name,
							code: this.info.code
						});
					} else {
						let i = indexList.findIndex((obj) => {
							return obj.code == item.name || obj.name == item.name;
						});
						this.echartData.push({
							...item,
							name: i == -1 ? item.name : indexList[i].name,
							code: i == -1 ? item.code : indexList[i].code,
							date: item.date.map((val) => {
								return val.slice(0, 10);
							})
						});
					}
				} else {
					if (item.name == this.info.code || item.name == this.info.name) {
						this.echartData[index] = {
							...item,
							name: this.echartData[index].name,
							code: this.echartData[index].code
						};
					} else {
						this.echartData[index] = {
							...item,
							name: this.echartData[index].name,
							code: this.echartData[index].code,
							date: item.date.map((val) => {
								return val.slice(0, 10);
							})
						};
					}
				}
			});
			this.yejishowflag = true;
			let dateList =
				this.echartData.filter((item) => {
					return item.name == this.info.name || item.name == this.info.code;
				})?.[0]?.date || this.echartData[0]?.date;
			this.dateList = dateList ? dateList : this.dateList;
			if (this.showBasic) {
				this.formatDataShow(data);
			} else {
				this.formatData(data);
			}
			if (this.currentList.length == 0) {
				this.drawAction(this.series);
			} else {
				// this.current = [this.currentList?.[this.currentList.length - 1]];
				this.componentdMouseDown();
			}
		},
		// 重置图表数据
		clearOption() {
			this.series = [];
			this.echartData = [];
		},
		// 格式化数据
		formatDataShow(data) {
			data.map((item) => {
				if (this.t1 == true && (this.info.code == item?.name || this.info.name == item?.name)) {
					this.series.unshift({
						name: this.info.name,
						type: 'line',
						symbol: 'none',
						// markPoint: {
						// 	data: [
						// 		{
						// 			type: 'max',
						// 			symbol: 'pin',
						// 			symbolSize: 50,
						// 			animation: true,
						// 			label: {
						// 				show: true,
						// 				color: '#000'
						// 			},
						// 			itemStyle: { color: '#f00' }
						// 		}
						// 	]
						// },
						lineStyle: {
							color: '#4096FF'
						},
						data: this.computedReturn(item.value).map((obj, index) => {
							return [item.date[index].slice(0, 10), obj];
						})
					});
					this.colorpan.push('#4096FF');
					this.num1 = Number(this.computedReturn(item.value)[item.value.length - 1]);
				}
			});
			if (
				this.t3 == true &&
				this.bench1 != '' &&
				data.findIndex((item) => {
					return item?.name == this.bench1name;
				}) !== -1
			) {
				let bench1Data = data.filter((item) => {
					return item?.name == this.bench1name;
				})?.[0];
				this.series.push({
					name: this.bench1name,
					type: 'line',
					symbol: 'none',
					lineStyle: {
						color: '#40BFDD'
					},
					data: this.computedReturn(bench1Data?.value).map((obj, index) => {
						return [bench1Data.date[index].slice(0, 10), obj];
					})
				});
				this.colorpan.push('#40BFDD');
			}
			if (
				this.t4 == true &&
				this.bench2 != '' &&
				data.findIndex((item) => {
					return item?.name == this.bench2name;
				}) !== -1
			) {
				let bench2Data = data.filter((item) => {
					return item?.name == this.bench2name;
				})?.[0];
				this.series.push({
					name: this.bench2name,
					type: 'line',
					symbol: 'none',
					lineStyle: {
						color: '#C2B12F'
					},
					data: this.computedReturn(bench2Data?.value).map((obj, index) => {
						return [bench2Data.date[index].slice(0, 10), obj];
					})
				});
				this.colorpan.push('#C2B12F');
			}
		},
		// 格式化数据
		formatData(data) {
			data.map((item) => {
				if (this.info.code == item?.name) {
					this.series.unshift({
						name: this.info.name,
						type: 'line',
						symbol: 'none',
						data: this.computedReturn(item.value).map((obj, index) => {
							return [item.date[index].slice(0, 10), obj];
						})
					});
					this.num1 = Number(this.computedReturn(item.value)[item.value.length - 1]);
					return;
				}
			});

			this.list.map((item, index) => {
				let filterData = data.filter((obj) => {
					return obj?.name == item.code || obj?.name == item.name;
				})?.[0];
				if (filterData) {
					this.series.push({
						name: item.name,
						type: 'line',
						symbol: 'none',
						data: this.computedReturn(filterData?.value).map((obj, index) => {
							return [filterData.date[index].slice(0, 10), obj];
						})
					});
				}
			});
		},
		// 接收自定义数据
		getCustomData(data) {
			this.yejishowflag = true;
			this.dateList = data.filter((item) => {
				return item.date;
			})[0].date;
			data.map((item) => {
				this.series.push({
					name: this.list.filter((obj) => item?.name == obj?.name)?.[0]?.name || this.info.name,
					type: 'line',
					symbol: 'none',
					data: item.value.map((obj, index) => {
						return [item.date[index].slice(0, 10), obj];
					})
				});
			});
			this.drawAction(this.series);
		},
		// 接收基准数据
		sendBenchmark(benchmarkList, bench1, bench2) {
			this.benchoption = benchmarkList;
			this.bench1 = bench1?.index_code;
			this.bench1name = bench1?.name;
			this.bench2 = bench2?.index_code;
			this.bench2name = bench2?.name;
		},
		// 接收基金收益数据
		getCurveReturn(data) {
			data.map((item) => {
				this.series.push({
					name: item.name,
					type: 'line',
					symbol: 'none',
					data: item.value.map((obj, index) => {
						return [item.date[index].slice(0, 10), obj];
					})
				});
			});
		},
		// 画图
		drawAction(series2) {
			let series = series2;
			if (this.abnormalP.length > 0) {
				series[series.findIndex((item) => item.name == this.info.name)]['markPoint'] = {
					data: []
				};
				for (let i = 0; i < this.abnormalP.length; i++) {
					if (
						series[series.findIndex((item) => item.name == this.info.name)].data.filter((item) => item[0] == this.abnormalP[i].date)
							.length > 0
					) {
						series[series.findIndex((item) => item.name == this.info.name)]['markPoint'].data.push({
							// value: this.abnormalP[i].reason,
							symbol: 'pin',
							emphasis: {
								disabled: false,
								label: {
									show: true,
									formatter: `异常点:${this.abnormalP[i].reason}`,
									position: 'top',
									backgroundColor: 'rgba(0, 0, 0, 0.45)',
									borderRadius: 4,
									padding: [0, 9],
									width: 164,
									height: 26,
									color: 'white',
									lineHeight: 32,
									align: 'center',
									fontFamily: 'PingFang',
									fontStyle: 'normal',
									fontWeight: 400,
									fontSize: '12px'
								}
							},
							coord: [
								this.abnormalP[i].date,
								series[series.findIndex((item) => item.name == this.info.name)].data.filter(
									(item) => item[0] == this.abnormalP[i].date
								)[0][1]
							],
							symbolSize: 20,
							animation: true,
							label: {
								show: false,
								color: '#000'
							},
							itemStyle: { color: '#4096ff' }
						});
					}
				}
			}
			let that = this;
			let options = lineChartOption({
				toolbox: 'none',
				color: this.showBasic ? this.colorpan : this.color,
				tooltip: {
					formatter: function (obj) {
						that.current = obj?.[0].data;
						var value = obj?.[0].axisValue + `<br />`;
						for (let i = 0; i < obj.length; i++) {
							value +=
								`<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:` +
								obj?.[i].color +
								`;"></span>` +
								obj?.[i].seriesName +
								':' +
								(Number(obj?.[i].value?.[1]) * 100).toFixed(2) +
								'%' +
								`<br />`;
						}
						return value;
					}
				},
				xAxis: [
					{
						data: this.dateList
						// formatter: function (obj) {
						// 	let date = obj?.split('-');
						// 	return date[0] + '-' + date[1];
						// }
					}
				],
				yAxis: [
					{
						formatter: function (obj) {
							return (obj * 100 > 10 || -(obj * 100) > 10 ? (obj * 100).toFixed(0) : (obj * 100).toFixed(2)) + '%';
						}
					}
				],
				series: series.map((item) => {
					return {
						...item,
						data: item.data.map((obj) => {
							return obj?.[0] == this.current?.[0] && item.name == this.info.name
								? {
										// ...this.symbol,
										value: obj
								  }
								: obj;
						}),
						connectNulls: true
					};
				})
			});
			this.yejishowflag = false;
			this.yejioptions = options;
			console.log(options);
		},
		// 查询基金
		async searchpeople(query) {
			this.loading = false;
			let data = await Search({ message: query, flag: '1,2,6' });
			if (data) {
				let temparr =
					this.info.classType == 'manager'
						? [
								{
									label: '基金经理',
									options: []
								},
								{
									label: '基金基准',
									options: []
								}
						  ]
						: [
								{
									label: '基金产品',
									options: []
								},
								{
									label: '基金基准',
									options: []
								}
						  ];
				for (let i = 0; i < data.length; i++) {
					if (data[i].flag == 'fund' && this.info.classType != 'manager') {
						temparr[0].options.push(data[i]);
					} else if (data[i].flag == 'manager' && this.info.classType == 'manager') {
						temparr[0].options.push(data[i]);
					} else if (data[i].flag == 'index') {
						temparr[1].options.push(data[i]);
					}
				}
				this.listfront = data;
				this.havefundmanager = temparr;
			}
		},
		// 选择时间
		selectDate() {
			this.currentList = [];
			this.value1.map((item) => {
				return this.moment(item).format('YYYY-MM-DD');
			});
			let isBefore = this.moment(this.value1[0]).isBefore(this.value1[1]);
			let date = {};
			if (isBefore) {
				date.start_date = this.value1?.[0];
				date.end_date = this.value1?.[1];
			} else {
				date.start_date = this.value1?.[1];
				date.end_date = this.value1?.[1];
			}
			this.postData.start_date = date.start_date;
			this.postData.end_date = date.end_date;
			this.postData.codes = this.postData.codes?.length == 0 || !this.postData.codes ? [this.info.code] : this.postData.codes;
			if (this.postData?.codes.length || this.postData?.index_codes.length) {
				this.series = [];
				this.$emit('getTimeAndList', this.postData);
			}
		},
		// 选择基金/基准
		selectData() {
			this.list.push(
				this.listfront.filter((item) => {
					return item.code == this.values;
				})[0]
			);
			this.sendPostData();
		},
		// 给父组件传递请求数据
		sendPostData() {
			this.postData.codes = [this.info.code];
			this.postData.index_codes = [];
			this.list.map((item) => {
				if (item.flag == 'fund' || item.flag == 'manager') {
					this.postData.codes.push(item.code);
				} else if (item.flag == 'index') {
					this.postData.index_codes.push(item.code);
				}
			});
			if (this.postData?.start_date && this.postData?.end_date) {
				this.series = [];
			} else {
				this.postData['start_date'] = this.series?.[0]?.data?.[0]?.[0];
				this.postData['end_date'] = this.series?.[0]?.data?.[this.series?.[0]?.data?.length - 1]?.[0];
			}
			this.series = [];
			this.$emit('getTimeAndList', this.postData);
		},
		// 关闭标签
		handleClose(val) {
			this.list.splice(
				this.list.findIndex((item) => {
					return item.code == val;
				}),
				1
			);
			this.sendPostData();
		},
		// 切换状态
		changeBasicShow() {
			this.series = [];
			this.options = {};
			this.currentList = [];
			if (this.showBasic) {
				this.sendPostData();
				this.$emit('getRateInfo');
			} else {
				this.$emit('getFundAndIndex');
			}
			this.showBasic = !this.showBasic;
		}
	}
};
</script>
<style scoped>
.hidebox {
	width: 23px;
	height: 2px;
	background: #e9e9e9;
}
.marginright202 {
	margin-right: 20px !important;
}
/* .tagselfbox .el-input__icon {
	
    display: flex !important;
    align-items: center !important;

} */
.boxslefhead ::v-deep .el-date-editor .el-range-editor .el-input__inner .el-date-editor--daterange .el-range-editor--small {
	width: 200px !important;
}
.tagselfbox .el-tag {
	background-color: #4096FF !important;
	border-color: #4096FF !important;
	color: white !important;
	font-size: 14px !important;
}

.boxslefhead {
	margin: 10px;
}
.margintop10 {
	/* margin-top:10px */
}
.tagselfbox {
	margin: 10px;
}
.boxlabels {
}
.height300 {
	height: 310px !important;
}
</style>
<style>
.fonstgrep {
	width: 55px;
	height: 14px;
	font-size: 12px;
	font-family: PingFangSC-Regular, PingFang;
	font-weight: 400;
	color: #e9e9e9;
	line-height: 14px;
}

.height300 {
	height: 310px !important;
}
.colr1 {
	color: #20995b;
	font-weight: 600;
	margin-left: 10px;
}
.colr2 {
	color: #e85d2d;
	font-weight: 600;
	margin-left: 10px;
}
.el-date-editor .el-range-separator {
	padding: 0 20px !important;
}
.el-range-separator {
	justify-content: center !important;
	align-items: center !important;
	display: flex !important;
}
.el-input__icon .el-range__icon .el-icon-date {
	display: flex !important;
	align-items: center !important;
}
.boxslefhead ::v-deep .el-select--small {
	width: 160px !important;
}
.tagselfbox ::v-deep .el-range-editor--small.el-input__inner {
	height: 32px !important;
}
.tagselfbox ::v-deep .el-date-editor--daterange.el-input,
.el-date-editor--daterange.el-input__inner,
.el-date-editor--timerange.el-input,
.el-date-editor--timerange.el-input__inner {
	width: 200px !important;
}
.tagselfbox ::v-deep .el-tag .el-tag__close {
	color: white !important;
}
.tagselfbox ::v-deep .el-range-editor--small .el-range__close-icon,
.el-range-editor--small .el-range__icon {
	line-height: 24px !important;
}
.tagselfbox ::v-deep .el-date-editor .el-range__icon {
	font-size: 14px !important;
	margin-left: -5px !important;
}
.tagselfbox ::v-deep .el-range-editor--small .el-range-input {
	font-size: 13px !important;
}
.tagselfbox ::v-deep .el-range-editor--small .el-range-separator {
	line-height: 24px !important;
	font-size: 13px !important;
}
</style>
