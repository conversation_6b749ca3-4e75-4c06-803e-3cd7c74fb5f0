<template>
	<div id="excessReturnAttribution">
		<analysis-card-title title="分年度类Brinson归因" @downloadExcel="exportExcel"></analysis-card-title>
		<div style="width: 100%">
			<el-table
				v-loading="loading"
				max-height="414px"
				style="width: 99%"
				:data="data"
				:cell-style="elcellstyle"
				:default-sort="{ prop: 'yearqtr', order: 'descending' }"
				header-cell-class-name="table-header"
				@sort-change="sortChange"
				border
				stripe
			>
				<el-table-column
					v-for="item in column"
					:key="item.value"
					:align="item.align ? item.align : 'gotoleft'"
					:prop="item.value"
					:label="item.label"
					sortable="custom"
				>
					<template #header>
						<long-table-popover-chart
							v-if="item.popover"
							:data="formatTableData()"
							date_key="yearqtr"
							:data_key="item.value"
							:show_name="item.label"
						>
							<span>{{ item.label }}</span>
						</long-table-popover-chart>
						<span v-else>{{ item.label }}</span>
					</template>
					<template slot-scope="{ row }">
						<span>{{ item.format ? item.format(row[item.value]) : row[item.value] }}</span>
					</template>
				</el-table-column>
				<template slot="empty">
					<el-empty image-size="160"></el-empty>
				</template>
			</el-table>
		</div>
	</div>
</template>

<script>
import { filter_json_to_excel } from '@/utils/exportExcel.js';

// 超额收益归因
import { getExcessReturnDecomposition } from '@/api/pages/Analysis.js';
// 超额收益归因
export default {
	name: 'excessReturnAttribution',
	data() {
		return {
			data: [],
			column: [
				{
					label: '年份',
					value: 'yearqtr',
					popover: false
				},
				{
					label: '资产配置收益',
					value: 'allocation',
					format: this.fix2p,
					align: 'right',
					popover: true
				},
				{
					label: '排名',
					value: 'allocationRank',
					align: 'right',
					format: this.fix2pfu,
					popover: true
				},
				{
					label: '行业配置收益',
					value: 'industry',
					align: 'right',
					format: this.fix2p,
					popover: true
				},
				{
					label: '排名',
					value: 'industryRank',
					align: 'right',
					format: this.fix2pfu,
					popover: true
				},
				{
					label: '择股收益',
					value: 'stock',
					align: 'right',
					format: this.fix2p,
					popover: true
				},
				{
					label: '排名',
					value: 'stockRank',
					align: 'right',
					format: this.fix2pfu,
					popover: true
				},
				{
					label: '交易收益',
					value: 'trading',
					align: 'right',
					format: this.fix2p,
					popover: true
				},
				{
					label: '排名',
					value: 'tradingRank',
					align: 'right',
					format: this.fix2pfu,
					popover: true
				}
			],
			loading: true,
			info: {}
		};
	},
	methods: {
		openvideo() {
			window.open('https://www.bilibili.com/video/BV1T3411F7ju?share_source=copy_web');
		},
		// 获取超额收益归因数据
		async getExcessReturnDecomposition() {
			this.loading = true;
			let data = await getExcessReturnDecomposition({
				flag: this.info.flag,
				code: this.info.code,
				type: this.info.type,
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			this.loading = false;
			if (data?.mtycode == 200) {
				this.data = data?.data.sort((a, b) => {
					return this.moment(this.moment(a.yearqtr, 'YYYY QQ').format()).isAfter(this.moment(b.yearqtr, 'YYYY QQ').format()) ? -1 : 1;
				});
			}
		},
		// 获取数据
		getData(info) {
			this.info = info;
			this.getExcessReturnDecomposition();
		},
		formatTableData() {
			let data = [];
			this.data.map((item) => {
				let obj = { ...item };
				for (const key in item) {
					let format = this.column.find((obj) => {
						return obj.value == key;
					})?.format;
					if (format) {
						let val = format(item[key]);
						obj[key] = typeof val == 'string' ? (val.includes('%') ? val?.split('%')?.[0] * 1 : !isNaN(val) ? val * 1 : val) : val;
					}
				}
				data.push(obj);
			});
			return data;
		},
		// 无数据
		getNoData() {
			this.loading = false;
		},
		// 表格排序
		sortChange(val) {
			let data = this.data;
			// 降序
			if (val?.order == 'descending') {
				data = data?.sort((a, b) => {
					if (a?.[val?.prop] === '--' && b?.[val?.prop] !== '--') return 1;
					if (a?.[val?.prop] !== '--' && b?.[val?.prop] === '--') return -1;
					if (a?.[val?.prop] === '--' && b?.[val?.prop] === '--') return 0;
					return b?.[val?.prop] * 1 - a?.[val?.prop] * 1;
				});
			} else if (val?.order == 'ascending') {
				// 升序
				data = data?.sort((a, b) => {
					if (a?.[val?.prop] === '--' && b?.[val?.prop] !== '--') return 1;
					if (a?.[val?.prop] !== '--' && b?.[val?.prop] === '--') return -1;
					if (a?.[val?.prop] === '--' && b?.[val?.prop] === '--') return 0;
					return a?.[val?.prop] * 1 - b?.[val?.prop] * 1;
				});
			}
		},
		fix2p(value) {
			if (value == '--' || value == undefined || value == 'nan' || value == null || value == 'NAN') return value;
			else return (value * 100).toFixed(2) + '%';
		},
		fix2pfu(value) {
			if (value == '--' || value == undefined || value == 'nan' || value == null || value == 'NAN') return value;
			else return (100 - Number(value * 100)).toFixed(2) + '%';
		},
		// 表格动态设置样式
		elcellstyle({ row, column, rowIndex, columnIndex }) {
			if (columnIndex == 1) {
				if (row['allocation'] >= 0) {
					return 'color: #E85D2D;';
				} else return 'color: #18C2A0;';
			}
			if (columnIndex == 3) {
				if (row['industry'] >= 0) {
					return 'color: #E85D2D;';
				} else return 'color: #18C2A0;';
			}

			if (columnIndex == 5) {
				if (row['stock'] >= 0) {
					return 'color: #E85D2D;';
				} else return 'color: #18C2A0;';
			}
			if (columnIndex == 7) {
				if (row['trading'] >= 0) {
					return 'color: #E85D2D;';
				} else return 'color: #18C2A0;';
			}
		},
		exportExcel() {
			let list = this.column.map((item) => {
				return { label: item.label, value: item.value };
			});
			filter_json_to_excel(list, this.data, '超额收益归因');
		},
		createPrintWord() {
			let list = this.column.map((item) => {
				return { label: item.label, value: item.value };
			});
			let data = this.data.map((item) => {
				let obj = {};
				for (const key in item) {
					let index = this.column.findIndex((v) => v.value == key);
					if (this.column[index]?.format) {
						obj[key] = this.column[index]?.format(item[key]);
					} else {
						obj[key] = item[key];
					}
				}
				return obj;
			});
			if (this.data.length) {
				return [
					...this.$exportWord.exportTitle('分年度类Brinson归因'),
					...this.$exportWord.exportDescripe(
						'我们将持续的超额表现归因为:大类资产配置能力 + 行业配置能力（股票类） + 择股能力 + 交易能力 ⇒ 超额收益'
					),
					...this.$exportWord.exportTable(
						list,
						data
							.sort((a, b) => {
								return this.moment(this.moment(a.yearqtr, 'YYYY QQ').format()).isAfter(this.moment(b.yearqtr, 'YYYY QQ').format()) ? -1 : 1;
							})
							.slice(0, 10),
						'',
						true
					)
				];
			} else {
				return [];
			}
		}
	}
};
</script>

<style></style>
