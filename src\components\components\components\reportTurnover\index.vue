<template>
	<div id="reportTurnover">
		<analysis-card-title title="报告期换手率" image_id="reportTurnover"></analysis-card-title>
		<div class="charts_fill_class">
			<v-chart
				ref="tradingStyle1"
				v-loading="loading"
				element-loading-text="暂无数据"
				element-loading-spinner="el-icon-document-delete"
				element-loading-background="rgba(239, 239, 239, 0.5)"
				style="width: 100%; height: 400px"
				autoresize
				:options="option"
			/>
		</div>
	</div>
</template>

<script>
import { barChartOption } from '@/utils/chartStyle.js';
import { getTurnoverInfo } from '@/api/pages/Analysis.js';
export default {
	data() {
		return {
			loading: true,
			option: {},
			info: {}
		};
	},
	methods: {
		getData(info) {
			this.info = info;
			this.getConcentrationInfo();
		},
		// 获取交易风格数据
		async getConcentrationInfo() {
			this.loading = true;
			let postData = {
				code: this.info.code,
				flag: this.info.flag,
				type: this.info.type,
				start_date: this.info.start_date,
				end_date: this.info.end_date
			};
			// 换手率
			let data = await getTurnoverInfo(postData);
			this.loading = false;
			if (data?.mtycode == 200) {
				this.getTurnoverData({ ...this.formatTurnover(data.data) });
			} else {
				this.hideLoading();
			}
		},
		getTurnoverData(data) {
			this.$nextTick(() => {
				let tempdata1 = [];
				let tempdata2 = [];
				var date = new Date();
				let year = date.getFullYear();
				if (data.yearqtr[data.yearqtr.length - 1] >= year) {
					for (let i = 0; i < data.turnover.length; i++) {
						if (i == data.turnover.length - 1) {
							tempdata1.push({
								value: data.turnover[i],
								itemStyle: {
									normal: {
										color: '#f5f5f5',
										barBorderColor: '#4096FF',
										barBorderWidth: 1,
										barBorderRadius: 0,
										borderType: 'dotted'
									}
								}
							});
							tempdata2.push({
								value: data.turnover_middle[i],
								itemStyle: {
									normal: {
										color: '#f5f5f5',
										barBorderColor: '#4096ff',
										barBorderWidth: 1,
										barBorderRadius: 0,
										borderType: 'dotted'
									}
								}
							});
						} else {
							tempdata1.push(data.turnover[i]);
							tempdata2.push(data.turnover_middle[i]);
						}
					}
				} else {
					tempdata1 = data.turnover;
					tempdata2 = data.turnover_middle;
				}
				this.option = barChartOption({
					toolbox: 'none',
					color: ['#4096ff', '#4096ff', '#E8684A'],
					grid: {
						top: '8px',
						left: '48px',
						right: '48px',
						bottom: '36px'
					},
					tooltip: {
						trigger: 'axis',
						formatter: function (obj) {
							var value = obj[0].axisValue + `年<br />`;
							for (let i = 0; i < obj.length; i++) {
								if (obj[i].seriesName == '全市场排名分位') {
									value +=
										`<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:` +
										obj[i].color +
										`;"></span>` +
										obj[i].seriesName +
										':' +
										Number(obj[i].data * 100).toFixed(2) +
										'%' +
										`<br />`;
								} else {
									value +=
										`<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:` +
										obj[i].borderColor +
										`;"></span>` +
										obj[i].seriesName +
										':' +
										Number(obj[i].data?.value || obj[i].data).toFixed(2) +
										'倍' +
										`<br />`;
								}
							}
							return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
						}
					},
					legend: {
						bottom: '0',
						data: ['换手率', '换手率中位数', { name: '全市场排名分位', icon: 'line' }]
					},
					xAxis: [{ type: 'category', data: data.yearqtr }],
					yAxis: [
						{
							name: '换手率',
							type: 'value',
							nameLocation: 'middle', // 设置名称居中
							nameGap: 48, // 控制名称距离轴线的距离
							nameTextStyle: {
								align: 'center'
							}
						},
						{
							name: '全市场排名分位',
							type: 'value',
							nameLocation: 'middle', // 设置名称居中
							nameGap: 48, // 控制名称距离轴线的距离
							nameRotate: 270,
							nameTextStyle: {
								align: 'center'
							},
							formatter: function (val) {
								return val * 100 + '%';
							}
						}
					],
					series: [
						{
							name: '换手率',
							type: 'bar',
							data: tempdata1,
							barGap: 0,
							itemStyle: {
								borderColor: '#4096ff',
								color: new echarts.graphic.LinearGradient(
									0,
									0,
									0,
									1, // 渐变方向，这里表示从上到下
									[
										{ offset: 0, color: '#4096ff' }, // 渐变起始颜色
										{ offset: 1, color: '#85AEFF' } // 渐变结束颜色
									]
								)
							}
						},
						// {
						// 	name: '换手率中位数',
						// 	type: 'bar',
						// 	data: tempdata2,
						// 	barGap: 0,
						// 	itemStyle: {
						// 		borderColor: '#FFAB3E',
						// 		color: new echarts.graphic.LinearGradient(
						// 			0,
						// 			0,
						// 			0,
						// 			1, // 渐变方向，这里表示从上到下
						// 			[
						// 				{ offset: 0, color: '#FFAB3E' }, // 渐变起始颜色
						// 				{ offset: 1, color: '#FFC67D' } // 渐变结束颜色
						// 			]
						// 		)
						// 	}
						// },
						{
							name: '全市场排名分位',
							type: 'line',
							symbol: 'none',
							yAxisIndex: 1,
							data: data.turnover_rank,
							itemStyle: {
								color: '#E8684A'
							},
							lineStyle: {
								color: '#E8684A'
							}
						}
					]
				});
				console.log(this.option);
				this.empty1 = false;
			});
		},
		// 格式化换手率数据
		formatTurnover(data) {
			if (data?.length) {
				// 换手率
				let turnover = [];
				// 换手率中位数 -- 苟煜说没有
				let turnover_middle = [];
				// 换手率排名分位
				let turnover_rank = [];
				// 换手率时间
				let yearqtr = [];
				data?.map((item) => {
					turnover.push(item.turnover);
					turnover_rank.push(item.turnoverRank);
					// turnover_middle.push(item.num);
					yearqtr.push(item.yearqtr);
				});
				return { turnover, turnover_rank, turnover_middle, yearqtr };
			}
		},
		async createPrintWord(info) {
			await this.getData(info);
			return await new Promise((resolve, reject) => {
				this.$nextTick(async () => {
					let height = this.$refs['tradingStyle1'].$el.clientHeight;
					let width = this.$refs['tradingStyle1'].$el.clientWidth;
					let chart = this.$refs['tradingStyle1'].getDataURL({
						type: 'jpg',
						pixelRatio: 3,
						backgroundColor: '#fff'
					});
					resolve([...this.$exportWord.exportTitle('报告期换手率'), ...this.$exportWord.exportChart(chart, { width, height })]);
				});
			});
		}
	}
};
</script>

<style></style>
