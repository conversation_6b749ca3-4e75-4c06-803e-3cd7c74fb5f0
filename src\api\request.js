import request from '@/utils/request';
import axios from 'axios';
const CancelToken = axios.CancelToken;
let cancel;
const mockUrl = 'https://console-mock.apipost.cn/mock/095be09f-d916-465e-a700-fd1f3bce1b7c/Analysis';

function analysisRequest({ url, method, params, data ,cancelRequest}) {
	if( cancelRequest && cancel) cancel();
	return request({
		url: '/Analysis' + url,
		method,
		params,
		data,
		cancelToken: new CancelToken(function (c) {
			cancel = c;
		}),
	});
}
export const tkRequest = function ({ url, method, params, data }) {
	return request({
		url: '/api/taikang' + url,
		method,
		params,
		data
	});
};
export const poolRequest = function ({ url, method, params, data }) {
	return request({
		url: '/Pool' + url,
		method,
		params,
		data
	});
};
export const combinationRequest = function ({ url, method, params, data,headers }) {
	return request({
		url: '/api/combination' + url,
		method,
		params,
		data,
		headers
	});
};

export const mockRequest = function ({ url, method, params, data }) {
	return request({
		url: mockUrl + url,
		method,
		params,
		data
	});
};
export default analysisRequest;
