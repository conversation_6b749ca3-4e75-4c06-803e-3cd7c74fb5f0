<template>
    <div class="plate-wrapper fund-performance-board-wrapper" v-loading="showLoading">
        <combinationComponentHeader title="组合个股持仓复盘" @download="exportExcel">
            <template slot="right">
                <div class="block" style="margin-right: 12px;">
                    <span class="demonstration">截止日期：</span>
                    <el-date-picker
                    v-model="deadline"
                    align="right"
                    type="date"
                    placeholder="选择日期"
                    @change="getData(param)"
                    :picker-options="pickerOptions"
                    >
                    </el-date-picker>
                </div>
                <el-radio-group class="radio-group-wrapper" v-model="form.penetrateFlag" size="small" style="margin-left: 0 !important;margin-right: 16px;" @change="radioChange">
                    <el-radio-button :label="true">穿透fof持仓</el-radio-button>
                    <el-radio-button :label="false">不穿透fof持仓</el-radio-button>
                    
                </el-radio-group>
                <el-select v-model="form.industryStandard" placeholder="选择行业口径" style="margin-right: 16px;" @change="radioChange">
                    <el-option
                        v-for="item in options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                        </el-option>
                </el-select>
                <!-- <el-date-picker
                    style="margin-right: 16px;"
                    @change="radioChange"
                    v-model="form.endDate"
                    type="date"
                    placeholder="截止日期">
                </el-date-picker> -->
                <el-button @click="addFactoryVisible=true" style="margin-right: 16px;"> <i class="el-icon-plus"></i>添加个股因子</el-button>
            </template>
        </combinationComponentHeader>
  
        <el-table border stripe :data="tableDataNow">
            <template v-for="(item,index) in tableHeader" >
                <el-table-column :min-width="item.label ==='个股收益贡献' ? '120' :'100'" align="gotoleft" sortable :prop="item.prop" show-overflow-tooltip :label="item.label" :key="index" v-if="item.prop === 'avgWeight' || item.prop === 'cumReturn'">
                    <template   slot-scope="scope" >
							{{ fix2p(scope.row[item.prop])}}
				</template>
                </el-table-column>
                <el-table-column :min-width="item.prop ==='netasset' ? '120' :'100'" align="gotoleft" sortable :prop="item.prop" show-overflow-tooltip :label="item.label" :key="index" v-else-if="item.prop === 'netasset' || item.prop === 'yield'">
     
                <template   slot-scope="scope" >
							{{ fix2(scope.row[item.prop])}}
				</template>

                </el-table-column>
                <el-table-column  :min-width="item.label ==='个股收益贡献' ? '110' :'100'" align="gotoleft"  sortable :prop="item.prop" show-overflow-tooltip :label="item.label" :key="index" v-else>
               
                <template   slot-scope="scope" >
							{{ item.format ? item.format(scope.row[item.prop])  : scope.row[item.prop]}}
				</template>
                </el-table-column>
            </template>
            
            

            <template slot="empty">
                <el-empty image-size="160"></el-empty>
            </template>
        </el-table>
        <el-pagination
            background
            style="display: flex; justify-content: right; padding-top: 16px; padding-bottom: 24px"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="currentPage"
            :page-sizes="[10, 20, 40, 60, 80, 100]"
            :page-size="pageSIze"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
        >
        </el-pagination>
        <el-dialog title="添加个股因子" :visible.sync="addFactoryVisible">
            <individualFactorSelection  @confirm="factorConfirm"></individualFactorSelection>
            
        </el-dialog>
        <!-- <el-dialog title="贵州茅台" :visible.sync="addFactoryVisible2">
            <stockAnalysis  @confirm="addFactoryVisible2=false"></stockAnalysis>
            
        </el-dialog> -->
    </div>
</template>
<script>
import combinationComponentHeader from './combinationComponentHeader.vue';
import individualFactorSelection from './individualFactorSelection.vue';
// import stockAnalysis from './stockAnalysis.vue';
import { combinationPositionReview} from '@/api/pages/tkAnalysis/portfolio.js';
import { filter_to_excel } from "@/utils/exportExcel.js";

export default {
    name:'portfolioStockResumption',
    components:{
        combinationComponentHeader,
        individualFactorSelection,
        // stockAnalysis
    },
    props: {
		endDate:{
			type: String,
			default:""
		}
	},
    data(){
        return {
            form:{
                penetrateFlag:true,
                industryStandard:'sw',
            },
            addFactoryVisible:false,
            addFactoryVisible2:false,
            options:[{
                label:'申万一级行业',
                value: 'sw'
            },{
                label:'泰康一级行业',
                value: 'tk'
            }],
            IndexStyleOption:[],
            tableData:[],
            param:null,
            tableDataNow: [],
            allTableData:[],
            pageSize: 10,
			currentPage: 1,
			total:0,
            showLoading:true,
            deadline:new Date().setTime(new Date().getTime() - 3600 * 1000 * 24),
            tableHeader:[{
                prop:'name',
                label:'个股'
            },{
                prop:'code',
                label:'股票代码'
            },{
                prop:'industryName',
                label:'所属行业'
            },{
                prop:'totalmv',
                label:'总市值(亿)',
                format:this.fix2
            },{
                prop:'peTtm',
                label:'pettem',
                format:this.fix2
            },{
                prop:'roe',
                label:'roe(%)',
                format:this.fix2
            },{
                prop:'yield',
                label:'股息率(%)',
                format:this.fix2
            },{
                prop:'netasset',
                label:'期末规模(万)',
                format:this.fix2
            },{
                prop:'avgWeight',
                label:'持仓权重'
            },{
                prop:'cumReturn',
                label:'个股收益贡献'
            },],
            tableHeader2:[{
                prop:'name',
                label:'个股'
            },{
                prop:'code',
                label:'股票代码'
            },{
                prop:'industryName',
                label:'所属行业'
            },{
                prop:'totalmv',
                label:'总市值(亿)'
            },{
                prop:'peTtm',
                label:'pettem'
            },{
                prop:'roe',
                label:'roe(%)'
            },{
                prop:'yield',
                label:'股息率(%)'
            },{
                prop:'netasset',
                label:'期末规模(万)'
            },{
                prop:'avgWeight',
                label:'持仓权重'
            },{
                prop:'cumReturn',
                label:'个股收益贡献'
            },],
            pickerOptions: {
				disabledDate(time) {
					return false;
				},
			}	
        }
    },
    watch: {
		endDate:{
			handler(val){
				if(val.trim()){
					this.pickerOptions.disabledDate = (time) => {
						return time.getTime() > (new Date(val).getTime());
					}
                    this.deadline = val;
				}
			},
			immediate: true
		},
    }, 
    methods:{
        exportExcel(){
            let list = this.tableHeader.map((item) => {
				return {
					...item,
					format: ''
				};
			});
			filter_to_excel(list, this.allTableData, '组合个股持仓复盘');
        },
        fix2p(value) {
			return value &&
				value != '' &&
				value != '--' &&
				value != '- -' &&
				JSON.stringify(value) != '[]' &&
				JSON.stringify(value) != '{}' &&
				value != 'NAN' &&
				value != 'nan'
				? (Number(value) * 100).toFixed(2) + '%'
				: '--';
		},
        fix2b(value) {
			return value &&
				value != '' &&
				value != '--' &&
				value != '- -' &&
				JSON.stringify(value) != '[]' &&
				JSON.stringify(value) != '{}' &&
				value != 'NAN' &&
				value != 'nan'
				? (Number(value) * 100).toFixed(2) 
				: '--';
		},
		fix2(value) {
			return value &&
				value != '' &&
				value != '--' &&
				value != '- -' &&
				JSON.stringify(value) != '[]' &&
				JSON.stringify(value) != '{}' &&
				value != 'NaN' &&
				value != 'nan'
				? Number(value).toFixed(2)
				: '--';
		},
		fixY(value) {
			return value &&
				value != '' &&
				value != '--' &&
				value != '- -' &&
				JSON.stringify(value) != '[]' &&
				JSON.stringify(value) != '{}' &&
				value != 'NAN' &&
				value != 'nan'
				? Number(value / 100000000).toFixed(2) + '亿'
				: '--';
		},
        handleSizeChange(val) {
			this.pageSize = val;
			this.currentPage = 1;
			this.handleCurrentChange(1);
		},
		handleCurrentChange(val) {
			this.currentPage = val;
            this.tableDataNow = this.allTableData.slice((this.currentPage-1)*this.pageSize,this.currentPage*this.pageSize)
		},
        radioChange(){
            this.getData(this.param)
        },
        factorConfirm(val) {
            if(val){
                let factorArr = [];
                let tableHeaderCopy = JSON.parse(JSON.stringify(this.tableHeader2));
                val.forEach(item=>{
                    factorArr.push(item.measure)
                    tableHeaderCopy.push({
                        prop:item.measure,
                        label:item.name,
                        format:this.fix2b
                    })
                })
                // this.form.factor = val;
                this.tableHeader = tableHeaderCopy;
                // this.getData(this.param)
            }
            this.addFactoryVisible = false;
		},
        async getData(param){
            this.showLoading = true;
            this.param = param;
            let res = await combinationPositionReview({
                ...param,
                ...this.param,
                ...this.form,
                deadline:this.moment(this.deadline).format('YYYY-MM-DD')
            });
            this.showLoading = false;
            if(res.mtycode == 200){
                this.allTableData = res.data;
                this.total = res.data.length;
                this.handleCurrentChange(1);
                // this.tableDataNow = res.data;
                // this.total = res.data.length;
                // this.tableData = res.data.slice((this.currentPage-1)*this.pageSize,this.currentPage*this.pageSize)
            }
        },
    
    },



}
</script>
<style lang="scss" scoped>
.fund-performance-board-wrapper {
    .select-form-wrapper {
        margin-bottom: 16px;
    }
    .content-table-wrapper {
        margin-bottom: 32px;
    }
}

</style>