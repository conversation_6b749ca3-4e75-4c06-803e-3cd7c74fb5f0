export default [
	{
		path: '/exportData',
		component: () => import(/* webpackChunkName: "tools" */ '../../pages/tools/exportData/index.vue'),
		meta: { title: '报告与数据导出', tagShow: false }
	},
	{
		path: '/subscriptionCenter',
		component: () => import(/* webpackChunkName: "tools" */ '../../pages/tools/subscriptionCenter/index.vue'),
		meta: { title: '订阅中心' }
	},
	{
		path: '/addReportTemplate',
		component: () => import(/* webpackChunkName: "tools" */ '../../pages/tools/subscriptionCenter/addReportTemplate.vue'),
		meta: { title: '新增报告模板' }
	},
	{
		path: '/addSubscription',
		component: () => import(/* webpackChunkName: "tools" */ '../../pages/tools/subscriptionCenter/addSubscription.vue'),
		meta: { title: '新增订阅' }
	},{
		path: '/Comparefundormanager',
		component: () => import(/* webpackChunkName: "dashboard" */ '../../pages/tools/compare/index.vue'),
		meta: { title: '比较池子', tagShow: false }
	},
	{
		path: '/fundcompare',
		component: () => import(/* webpackChunkName: "dashboard" */ '../../pages/tools/compare/fund/index.vue'),
		meta: { title: '基金比较', tagShow: true }
	},
	{
		path: '/fundcompareDiff',
		component: () => import(/* webpackChunkName: "dashboard" */ '../../pages/tools/compare/fund/different.vue'),
		meta: { title: '跨类型基金比较', tagShow: true }
	},
	{
		path: '/managercompare',
		component: () => import(/* webpackChunkName: "dashboard" */ '../../pages/tools/compare/manager/index.vue'),
		meta: { title: '基金经理比较', tagShow: true }
	},

];
