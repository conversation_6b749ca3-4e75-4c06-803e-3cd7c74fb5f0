<script>
import { getObjectBarraAnalysis } from '@/api/pages/analysis/report';
import { handleData } from '@/utils/count';
import { filter_json_to_excel_inside, changColumnToRow, filter_json_to_excel_inside_multiHeader } from '@/utils/exportExcel.js';
import analysisDescription from '@/components/components/components/analysisDescription/index.vue';
import { export_json_to_excel_multiHeader } from '@/vendor/Export2Excel.js';
import {
	downloadWord,
	exportTitleWithSubtitle,
	exportTableMergeHeader,
	exportTitle,
	exportFirstTitle,
	exportChart,
	exportTable,
	Format,
	exportSencondTitle
} from '@/utils/exportWord.js';
export default {
	data() {
		return {
			uploadState: false,
			tableData: [], // 页面表格数据源
			allTableData: [],
			otherTableData: [],
			graininess: {
				model: '整体',
				option: [{ value: '整体', label: '整体' }]
			},
			loading: false,
			pageIndex: 1,
			pageSize: 10,
			description: `贝塔（Beta）：基金收益率对基准收益率加权线性回归的回归系数；
                    规模（Size）：总市值的自然对数；
                    成长（growth）：综合计算长期净利润预期、短期净利润预期、过去5年盈利增长率、长期历史销售率；
                    动量（Momentum）：长端动量与短端动量之差；
                    流动性（Liquidity）：综合计算月度、季度、年度换手率；
                    账面市值比（Book-to-Price）：市净率倒数；
                    盈利预期（Earning Yield）：净资产除以总市值；
                    残差波动（Residual Volatility）：综合计算超额收益年化波动率、年度超额收益率离差、Beta回归残差项的年化波动率；
                    非线性市值（Non-linear Size）：SIZE因子的立方和SIZE因子正交回归的残差项；
                    杠杆（Leverage）：综合计算市场杠杆、资产总债比、账面杠杆；`
		};
	},
	components: { analysisDescription },
	methods: {
		handleData,
		/**
		 * 获取表格数据
		 */
		getTableData() {
			this.loading = true;
			let params = {
				reportID: Number(this.$route.query.id),
				startFrom: Number(this.moment(this.$route.query.startDate).format('YYYYMMDD')),
				endTo: Number(this.moment(this.$route.query.endDate).format('YYYYMMDD')),
				holdType: this.$route.query.graininess
			};
			return getObjectBarraAnalysis(params).then((res) => {
				if (res.code === 200) {
					this.otherTableData = res.data.rows.map((itemA) => {
						return {
							...itemA,
							data: {
								...itemA.data,
								factor_return_act: itemA?.data?.factor_return / itemA?.data?.factor || '--'
							}
						};
					});
					res.data.rows.forEach((item) => {
						let number = 0;
						for (let i = 0; i < this.graininess.option.length; i++) {
							if (this.graininess.option[i].label !== item.data.code) continue;
							number++;
						}
						if (number === 0)
							this.graininess.option.push({
								value: item.data.code,
								label: item.data.code
							});
					});
				} else {
					this.tableData = [];
					this.oldTableData = [];
				}
			});
		},
		getTableData1() {
			this.loading = true;
			let params = {
				reportID: Number(this.$route.query.id),
				startFrom: Number(this.moment(this.$route.query.startDate).format('YYYYMMDD')),
				endTo: Number(this.moment(this.$route.query.endDate).format('YYYYMMDD')),
				holdType: this.$route.query.graininess
			};
			params.holdType = '';
			return getObjectBarraAnalysis(params).then((res) => {
				this.loading = false;
				if (res.code === 200) {
					this.tableData = res.data.rows.map((itemA) => {
						return {
							...itemA,
							data: {
								...itemA.data,
								factor_return_act: itemA?.data?.factor_return / itemA?.data?.factor || '--'
							}
						};
					});
					this.allTableData = res.data.rows.map((itemA) => {
						return {
							...itemA,
							data: {
								...itemA.data,
								factor_return_act: itemA?.data?.factor_return / itemA?.data?.factor || '--'
							}
						};
					});
				} else {
					this.tableData = [];
					this.allTableData = [];
				}
			});
		},
		/**
		 *颗粒度明细切换事件
		 */
		changeOption(model) {
			if (model === '整体') {
				this.tableData = this.allTableData;
			} else {
				this.tableData = this.otherTableData.filter((item) => item.data.code === model);
			}
		},
		/**
		 * 获取中文
		 */
		getZH(data) {
			switch (data) {
				case 'beta':
					return '贝塔因子';
					break;
				case 'momentum':
					return '动量因子';
					break;
				case 'size':
					return '市值因子';
					break;
				case 'growth':
					return '成长因子';
					break;
				case 'bp':
					return '估值因子';
					break;
				case 'leverage':
					return '杠杆因子';
					break;
				case 'liquidity':
					return '流动性因子';
					break;
				case 'country':
					return '国家因子';
					break;
				case 'residualvolatility':
					return '残差波动因子';
					break;
				case 'nonlinearsize':
					return '非线性市值因子';
					break;
				case 'earningyield':
					return '盈利因子';
					break;
				default:
					return data;
					break;
			}
		},

		/**
		 * 切换大小
		 */
		changeSize(size) {
			this.pageSize = size;
		},

		/**
		 * 排序
		 */
		sortData({ column, prop, order }) {
			let key = prop.split('.')[1];
			let arr = JSON.parse(JSON.stringify(this.tableData.filter((v) => v.data[key] !== 'nan' && v.data[key] !== 'NaN')));
			let noArr = this.tableData.filter((v) => v.data[key] === 'nan' || v.data[key] === 'NaN');

			if (order === 'ascending') {
				this.tableData = noArr.concat(arr.sort((a, b) => Number(a.data[key]) - Number(b.data[key])));
			}
			if (order === 'descending') {
				this.tableData = arr.sort((a, b) => Number(b.data[key]) - Number(a.data[key])).concat(noArr);
			}
			if (order === null) {
				this.tableData = this.oldTableData;
			}
		},

		/**
		 *数据处理
		 */
		formatter(row, column, cellValue, index) {
			if (cellValue === 'nan' || cellValue === 'NaN' || cellValue === undefined || !row || cellValue === '--') {
				return '--';
			}
			return (Number(cellValue) * 100).toFixed(2) + '%';
		},
		formatterP(row, column, cellValue, index) {
			if (cellValue === 'nan' || cellValue === 'NaN' || cellValue === undefined || !row || cellValue === '--') {
				return '--';
			}
			return (Number(cellValue) * 1).toFixed(4);
		},
		uploadPage() {
			this.getTableData();
			this.getTableData1();
		},
		async createPrintWord() {
			return Promise.all([this.getTableData(), this.getTableData1()]).then((arr) => {
				const head = exportFirstTitle('五、风险因子分析');
				let kldName = '';
				const code = this.graininess.model;
				this.graininess.option.map((item) => {
					if (item.value === code) {
						kldName = item.label;
					}
				});
				const subhead = exportTitle(`风险因子分析-颗粒度${kldName}`);
				const title = [
					{ label: '因子', value: 'textName', format: '' },
					{ label: 'PureAlpha', value: 'PureAlpha', format: 'fixE0' },
					{ label: '因子暴露', value: 'factor', format: 'fixE0' },
					{ label: '因子收益率', value: 'factor_return_act', format: 'fixE0' },
					{ label: '因子收益率贡献', value: 'factor_return', format: 'fixE0' }
				];

				this.tableData.map((item) => {
					item.data.textName = this.getZH(item.data.name);
				});
				let array = this.tableData.map((item) => {
					return item.data;
				});
				const table = exportTable(title, array, {}, {});
				return [...(head || []), ...(subhead || []), ...(table || [])];
			});
		},
		downloadExcel(name) {
			if (name === '风险因子分析') {
				let kldName = '';
				const code = this.graininess.model;
				this.graininess.option.map((item) => {
					if (item.value === code) {
						kldName = item.label;
					}
				});
				const title = [
					{ label: '因子', value: 'textName', format: '' },
					{ label: 'PureAlpha', value: 'PureAlpha', format: 'fixE0' },
					{ label: '因子暴露', value: 'factor', format: 'fixE0' },
					{ label: '因子收益率', value: 'factor_return_act', format: 'fixE0' },
					{ label: '因子收益率贡献', value: 'factor_return', format: 'fixE0' }
				];

				this.tableData.map((item) => {
					item.data.textName = this.getZH(item.data.name);
				});

				filter_json_to_excel_inside(title, this.tableData, ['data'], `${name}-颗粒度${kldName}`);
			}
		}
	}
};
</script>

<template>
	<div class="page-box">
		<div class="flex item-center justify-between">
			<div class="area-title">风险因子分析</div>
			<div class="border_table_header_search">
				<span class="selector">颗粒度：</span>
				<el-select v-model="graininess.model" class="search-security" placeholder="请选择" @change="changeOption" style="width: 240px">
					<el-option v-for="item in graininess.option" :key="item.value" :label="item.label" :value="item.value" style="width: 240px">
					</el-option>
				</el-select>
				<img alt="" src="../../../../../assets/img/download.png" class="download" @click="downloadExcel('风险因子分析')" />
			</div>
		</div>
		<el-divider></el-divider>
		<div class="area-body">
			<div class="table">
				<el-table
					v-loading="loading"
					:data="tableData.slice((pageIndex - 1) * pageSize, pageIndex * pageSize)"
					@sort-change="sortData"
					border
					stripe
				>
					<el-table-column align="gotoleft" label="因子">
						<template slot-scope="scope">
							{{ getZH(scope.row.data.name) }}
						</template>
					</el-table-column>
					<!-- <el-table-column align="gotoleft"
                           label="PureAlpha"
                           prop="data.PureAlpha"
                           sortable="custom"
                           :formatter="formatter" /> -->
					<el-table-column align="gotoleft" label="因子暴露" prop="data.factor" sortable="custom" :formatter="formatterP" />
					<el-table-column align="gotoleft" label="因子收益率" prop="data.factor_return_act" sortable="custom" :formatter="formatter" />
					<el-table-column align="gotoleft" label="因子收益率贡献" prop="data.factor_return" sortable="custom" :formatter="formatter" />
					<el-empty :image-size="180" />
				</el-table>
				<div class="pagination_board">
					<el-pagination
						:current-page.sync="pageIndex"
						:page-size="pageSize"
						:total="tableData.length"
						background
						layout="total, sizes, prev, pager, next"
						@size-change="changeSize"
					/>
				</div>
			</div>
			<div class="mt-12">
				<analysis-description title="风险因子分析" :description="description"></analysis-description>
			</div>
		</div>
	</div>
</template>

<style scoped lang="scss">
@import '../../../tkdesign';

.area-title {
	margin-bottom: 0 !important;
}

.border_table_header_search {
	display: flex;
	justify-content: flex-end;
	position: relative;

	.selector {
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
		line-height: 22px;
		margin-top: 5px;
		color: rgba(0, 0, 0, 0.85);
	}

	.search-security {
		width: 250px;
		margin-right: 10px;
	}
}

.table {
	margin-top: 16px;
}

.pagination_board {
	text-align: right;
	margin-top: 16px;
}
</style>
