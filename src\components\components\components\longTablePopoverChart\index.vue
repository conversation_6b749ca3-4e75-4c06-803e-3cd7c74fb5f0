<template>
	<el-popover placement="right" width="528" trigger="hover" @show="formatBigChart()">
		<v-chart
			style="width: 500px"
			autoresize
			:options="option"
			element-loading-text="暂无数据"
			element-loading-spinner="el-icon-document-delete"
			element-loading-background="rgba(239, 239, 239, 0.5)"
		></v-chart>
		<div slot="reference" style="display: flex; jusitfy-content: start; align-items: center">
			<slot></slot>
			<svg class="ml-8" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path
					d="M14.714 13.0006H2.42829V1.8577C2.42829 1.77913 2.36401 1.71484 2.28544 1.71484H1.28544C1.20686 1.71484 1.14258 1.77913 1.14258 1.8577V14.1434C1.14258 14.222 1.20686 14.2863 1.28544 14.2863H14.714C14.7926 14.2863 14.8569 14.222 14.8569 14.1434V13.1434C14.8569 13.0648 14.7926 13.0006 14.714 13.0006ZM3.99972 11.572H4.99972C5.07829 11.572 5.14258 11.5077 5.14258 11.4291V8.8577C5.14258 8.77913 5.07829 8.71484 4.99972 8.71484H3.99972C3.92115 8.71484 3.85686 8.77913 3.85686 8.8577V11.4291C3.85686 11.5077 3.92115 11.572 3.99972 11.572ZM6.71401 11.572H7.71401C7.79258 11.572 7.85687 11.5077 7.85687 11.4291V5.71484C7.85687 5.63627 7.79258 5.57199 7.71401 5.57199H6.71401C6.63544 5.57199 6.57115 5.63627 6.57115 5.71484V11.4291C6.57115 11.5077 6.63544 11.572 6.71401 11.572ZM9.42829 11.572H10.4283C10.5069 11.572 10.5712 11.5077 10.5712 11.4291V7.1077C10.5712 7.02913 10.5069 6.96484 10.4283 6.96484H9.42829C9.34972 6.96484 9.28544 7.02913 9.28544 7.1077V11.4291C9.28544 11.5077 9.34972 11.572 9.42829 11.572ZM12.1426 11.572H13.1426C13.2212 11.572 13.2854 11.5077 13.2854 11.4291V4.28627C13.2854 4.2077 13.2212 4.14342 13.1426 4.14342H12.1426C12.064 4.14342 11.9997 4.2077 11.9997 4.28627V11.4291C11.9997 11.5077 12.064 11.572 12.1426 11.572Z"
					fill="black"
					fill-opacity="0.45"
				/>
			</svg>
		</div>
	</el-popover>
</template>

<script>
import { barChartOption } from '@/utils/chartStyle.js';
export default {
	props: {
		data: {
			type: Object
		},
		date_key: {
			type: String,
			default: 'yearqtr'
		},
		data_key: {
			type: String,
			default: 'weight'
		},
		active_row: {
			type: Object
		},
		show_name: {
			type: String,
			default: ''
		},
		formatter: {
			type: Function,
			default: function (val) {
				return val * 1;
			}
		}
	},
	data() {
		return {
			show: true,
			option: {}
		};
	},
	methods: {
		formatBigChart() {
			if (this.checkKey()) {
				this.$nextTick(() => {
					var result = ecStat.histogram(
						this.data.map((item) => {
							return item[this.data_key];
						})
					);
					let interval;
					let datalist = [];
					let datalist2 = [];
					let datalist3 = [];
					var min = Infinity;
					var max = -Infinity;
					let data = echarts.util.map(result.data, function (item, index) {
						var x0 = result.bins[index].x0;
						var x1 = result.bins[index].x1;
						interval = x1 - x0;
						min = Math.min(min, x0);
						max = Math.max(max, x1);
						return [x0, x1, item[1]];
					});
					for (let i = 0; i < result.bins.length; i++) {
						datalist.push(String(Number(result.bins[i].x0)) + '~' + String(Number(result.bins[i].x1)));
						datalist2.push(result.bins[i].sample.length);
						datalist3.push(result.bins[i].sample);
					}
					// data

					this.option = barChartOption({
						toolbox: false,
						grid: {
							right: '128px'
						},
						xAxis: [
							{
								name: this.show_name,
								axisLabel: {
									interval
								},
								nameTextStyle: {
									align: 'center'
								}
							}
						],
						tooltip: {
							formatter: (value) => {
								let name = value[0].data[0] + '~' + value[0].data[1];
								let name2 = value[0].data[0].toFixed(2) + '~' + value[0].data[1].toFixed(2) + '';
								let index = datalist.indexOf(name);
								let t1 = 0;
								let t2 = 0;
								let t3 = 0;
								let t4 = 0;
								let t3l = 0;
								let t4l = 0;
								let t5 = 0;

								for (let i = 0; i < datalist.length; i++) {
									if (i <= index) {
										for (let j = 0; j < datalist3[i].length; j++) {
											t3 += Number(datalist3[i][j]);
											t3l += 1;
										}
									}
									if (i >= index) {
										for (let k = 0; k < datalist3[i].length; k++) {
											t4 += Number(datalist3[i][k]);
											t4l += 1;
										}
									}
									for (let l = 0; l < datalist3[i].length; l++) {
										t5 += 1;
									}
								}
								return `${this.show_name}区间${name2}:${value[0].data[2]}次<br />左侧频率(包含此区间)：${((t3l * 100) / t5).toFixed(
									2
								)}%<br />左侧${this.show_name}均值(包含此区间)：${(t3 / t3l).toFixed(2)}<br />右侧频率(包含此区间)：${(
									(t4l * 100) /
									t5
								).toFixed(2)}%<br />右侧${this.show_name}均值(包含此区间)：${(t4 / t4l).toFixed(2)}`;
							}
						},
						yAxis: [
							{
								type: 'value',
								name: '次数',
								min: 0
							}
						],
						series: [
							{
								name: '分布直方图',
								type: 'custom',
								barGap: '-100%',
								renderItem: this.renderItem,
								encode: {
									x: [0, 1],
									y: 2,
									tooltip: 2,
									label: 2
								},
								data: data
							}
						]
					});
					console.log(this.option);
				});
			} else {
				return {};
			}
		},
		// 格式化item
		renderItem(params, api) {
			var yValue = api.value(2);
			var start = api.coord([api.value(0), yValue]);
			var size = api.size([api.value(1) - api.value(0), yValue]);
			var style = api.style();

			return {
				type: 'rect',
				shape: {
					x: start[0] + 1,
					y: start[1],
					width: size[0] - 2,
					height: size[1]
				},
				style: style
			};
		},
		checkKey() {
			if (this.data?.length) {
				if (
					this.data.some((item) => {
						return item[this.date_key] && item[this.data_key];
					})
				) {
					// if (
					// 	this.data.some((item) => {
					// 		return this.active_row[this.date_key] == item[this.date_key];
					// 	})
					// ) {
					return true;
					// } else {
					// 	return false;
					// }
				} else {
					return false;
				}
			} else {
				return false;
			}
		}
	}
};
</script>

<style></style>
