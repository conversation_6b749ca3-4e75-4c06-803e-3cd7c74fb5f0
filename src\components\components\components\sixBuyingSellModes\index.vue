<template>
	<div class="sixBuyAndSellingModes" id="sixBuyingSellModes">
		<div class="mb-20 flex_between six_buying_sell">
			<div class="flex_start">
				<div
					class="flex_center title py-20 px-16 menu_item"
					@click="changeActive('buy')"
					:style="`${active == 'buy' ? 'color:#4096ff;border-color:#4096ff' : ''}`"
				>
					<div class="mr-4">六种买入模式</div>
					<el-tooltip
						class="item"
						effect="dark"
						:content="this.info.type == 'cbond' ? EXPLAIN.abilityPage['六种买入模式(债)'] : EXPLAIN.abilityPage['六种买入模式']"
						placement="right-start"
					>
						<svg width="14" height="14" viewBox="0 0 14 14" fill="none">
							<path
								fill-rule="evenodd"
								clip-rule="evenodd"
								d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
								fill="black"
								fill-opacity="0.45"
							/>
						</svg>
					</el-tooltip>
					<!-- <i class="el-icon-video-camera-solid videoIconDes" @click="openvideo"></i> -->
				</div>
				<div
					class="flex_center title py-20 px-16 menu_item"
					@click="changeActive('sell')"
					:style="`${active == 'sell' ? 'color:#4096ff;border-color:#4096ff' : ''}`"
				>
					<div class="mr-4">六种卖出模式</div>
					<el-tooltip
						class="item"
						effect="dark"
						:content="this.info.type == 'cbond' ? EXPLAIN.abilityPage['六种买入模式(债)'] : EXPLAIN.abilityPage['六种买入模式']"
						placement="right-start"
					>
						<svg width="14" height="14" viewBox="0 0 14 14" fill="none">
							<path
								fill-rule="evenodd"
								clip-rule="evenodd"
								d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
								fill="black"
								fill-opacity="0.45"
							/>
						</svg>
					</el-tooltip>
					<!-- <i class="el-icon-video-camera-solid videoIconDes" @click="openvideo"></i> -->
				</div>
			</div>
			<div>
				<div @click="exportImage" style="height: 32px; cursor: pointer" class="ml-12">
					<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
						<rect x="0.5" y="0.5" width="31" height="31" rx="3.5" fill="white" />
						<path
							d="M15.8874 18.6575C15.9007 18.6746 15.9178 18.6884 15.9373 18.6979C15.9568 18.7074 15.9782 18.7123 15.9999 18.7123C16.0215 18.7123 16.0429 18.7074 16.0624 18.6979C16.0819 18.6884 16.099 18.6746 16.1124 18.6575L18.1124 16.1271C18.1856 16.0343 18.1195 15.8968 17.9999 15.8968H16.6766V9.85392C16.6766 9.77535 16.6124 9.71106 16.5338 9.71106H15.4624C15.3838 9.71106 15.3195 9.77535 15.3195 9.85392V15.895H13.9999C13.8802 15.895 13.8141 16.0325 13.8874 16.1253L15.8874 18.6575ZM22.5356 18.0325H21.4641C21.3856 18.0325 21.3213 18.0968 21.3213 18.1753V20.9253H10.6784V18.1753C10.6784 18.0968 10.6141 18.0325 10.5356 18.0325H9.46415C9.38557 18.0325 9.32129 18.0968 9.32129 18.1753V21.7111C9.32129 22.0271 9.57665 22.2825 9.89272 22.2825H22.107C22.4231 22.2825 22.6784 22.0271 22.6784 21.7111V18.1753C22.6784 18.0968 22.6141 18.0325 22.5356 18.0325Z"
							fill="black"
							fill-opacity="0.45"
						/>
						<rect x="0.5" y="0.5" width="31" height="31" rx="3.5" stroke="#D9D9D9" />
					</svg>
				</div>
			</div>
		</div>
		<div v-show="!empty" v-loading="loading" class="flex_start" style="flex-wrap: wrap; min-height: 776px">
			<div v-for="(item, index) in chartList" :key="index" class="six_item px-20 pb-20 ml-12" :id="'sixBuyingModes' + index">
				<div class="six_item_title py-12 flex_between">
					<div>{{ item.name }}</div>
					<div>{{ item.freq }}</div>
				</div>
				<v-chart
					:ref="'sixBuyingModes' + index"
					class="mt-20"
					autoresize
					style="width: 486px; height: 278px"
					element-loading-text="暂无数据"
					element-loading-spinner="el-icon-document-delete"
					element-loading-background="rgba(239, 239, 239, 0.5)"
					:options="item.option"
				/>
			</div>
		</div>
		<div v-show="empty">
			<el-empty image-size="160"></el-empty>
		</div>
		<div class="mt-20" v-if="showDescription" id="buyingSellModesDexcription">
			<analysis-description :is_column="true" :description="description"></analysis-description>
		</div>
	</div>
</template>

<script>
// 模型使用说明
import analysisDescription from '@/components/components/components/analysisDescription/index.vue';
import { lineChartOption } from '@/utils/chartStyle.js';
import { exportTitle, exportChart, exportTable } from '@/utils/exportWord.js';
// 六种买入模式
import { getBuyOrSellModInfo } from '@/api/pages/Analysis.js';
export default {
	name: 'sixBuyingModes',
	components: { analysisDescription },
	props: {
		showDescription: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			active: 'buy',
			chartList: [],
			empty: false,
			loading: true,
			show: true,
			info: {}
		};
	},
	computed: {
		description() {
			return `分析历史买入所有个股首次买入和末次卖出时的情况，分析其买入/卖出时点的股价走 势（买入为报告期前 80 交易日至后 120 交易日：eventwindow 为 80，obswindow 为 40；卖 出为前 80 交易日和后 90 交易日：eventwindow 为 80，obswindow 为 10），并对所有股价走 势做聚类（带权重的，按个股 0.01、0.04、0.06 权重划分赋予 1，2，3，4 的权重）聚为 6 类。对聚类出来的曲线做拟合 lm(f[,i]~n+I(n^2)-1,data=f)，对拟合结果系数做判断，得到模 式类型。`;
		}
	},
	methods: {
		openvideo() {
			window.open('https://www.bilibili.com/video/BV1ZF411N7WQ?share_source=copy_web');
		},
		// 获取六种买入模式
		async getBuyModInfo() {
			this.loading = true;
			let data = await getBuyOrSellModInfo({
				flag: this.info.flag,
				code: this.info.code,
				type: this.info.type,
				start_date: this.info.start_date,
				end_date: this.info.end_date,
				status: this.active
			});
			this.loading = false;
			if (data?.mtycode == 200) {
				if (this.FUNC.isValidObj(data?.data.obs_data.id) && this.FUNC.isValidObj(data?.data.freq)) {
					this.empty = false;
					let result = data?.data.obs_data;
					this.chartList = [];
					for (const key in result) {
						if (key !== 'id') {
							this.chartList.push(this.drawLine(result.id, result[key], data?.data.freq[key.slice(-1) - 1]));
						}
					}
				} else {
					this.chartList = [1, 2, 3, 4, 5, 6];
					// this.empty = true;
				}
			}
		},
		async getData(info) {
			this.info = info;
			await this.getBuyModInfo();
		},
		drawLine(id, mod, freq) {
			let option = lineChartOption({
				toolbox: 'none',
				color: ['#4096ff'],
				grid: {
					top: '8px',
					bottom: '0px'
				},
				tooltip: {
					formatter: function (obj) {
						var value = ``;
						for (let i = 0; i < obj.length; i++) {
							value +=
								`<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
								`<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
								obj?.[i].color +
								`;"></div>` +
								`<div style="font-family: PingFang SC;">` +
								obj?.[i].seriesName +
								'</div></div>' +
								`<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
								(Number(obj?.[i].value) * 100).toFixed(2) +
								'%</div>' +
								`</div>`;
						}
						return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
					}
				},
				xAxis: [{ data: id, show: false, type: 'category' }],
				yAxis: [
					{
						type: 'value',
						formatter: function (val) {
							return Math.round(val * 100) + '%';
						}
					}
				],
				series: [
					{
						name: freq.name,
						type: 'line',
						data: mod,
						symbol: 'none',
						itemStyle: {
							color: '#4096ff'
						}
					}
				]
			});
			return {
				name: freq.name,
				freq: '频率：' + (freq.freq * 100).toFixed(2) + '%',
				option
			};
		},
		// 监听买入卖出模式切换
		changeActive(flag) {
			this.active = flag;
			this.getBuyModInfo();
		},
		exportImage() {
			this.downloadImage('sixBuyingSellModes', this.active == 'buy' ? '六种买入模式' : '六种卖出模式');
		},
		async createPrintWord(info) {
			this.active = 'buy';
			await this.getData(info);
			let arr = await this.getChartListFile();
			this.active = 'sell';
			await this.getData(info);
			arr.push(...(await this.getChartListFile()));
			let height = document.getElementById('buyingSellModesDexcription').clientHeight;
			let width = document.getElementById('buyingSellModesDexcription').clientWidth;
			let canvas = await this.html2canvas(document.getElementById('buyingSellModesDexcription'), {
				scale: 3
			});
			arr.push(
				...this.$exportWord.exportChart(canvas.toDataURL('image/jpg'), {
					width,
					height
				})
			);
			return arr;
		},
		// 截取买入卖出模式图
		async getChartListFile() {
			return await new Promise((resolve, reject) => {
				this.$nextTick(async () => {
					let arr = [];
					for (let index = 0; index <= this.chartList.length - 1; index++) {
						let canvas = await this.html2canvas(document.getElementById('sixBuyingModes' + index), {
							scale: 3
						});
						arr.push(canvas.toDataURL('image/jpg'));
					}
					let list = [
						{
							label: '',
							value: 'image1',
							type: 'six_image'
						},
						{
							label: '',
							value: 'image2',
							type: 'six_image'
						},
						{
							label: '',
							value: 'image3',
							type: 'six_image'
						}
					];
					let data = [
						{ image1: arr[0], image2: arr[1], image3: arr[2] },
						{ image1: arr[3], image2: arr[4], image3: arr[5] }
					];
					let height = document.getElementById('sixBuyingModes0').clientHeight;
					let width = document.getElementById('sixBuyingModes0').clientWidth;
					resolve([
						...this.$exportWord.exportTitle(this.active == 'buy' ? '六种买入模式' : '六种卖出模式'),
						...this.$exportWord.exportTable(list, data, { width, height })
					]);
				});
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.six_buying_sell {
	border-radius: 4px;
	border-bottom: 2px solid #e9e9e9;
	background: #fff;
	.menu_item {
		border-bottom: 2px solid transparent;
		transition: all 0.5s;
	}
	.menu_item:hover {
		cursor: pointer;
		color: #4096ff;
		// border-bottom: 2px solid #4096ff;
	}
}
.six_item {
	border-radius: 4px;
	border: 1px solid #d9d9d9;
	background: #fff;
	.six_item_title {
		border-bottom: 1px solid #e9e9e9;
		color: rgba(0, 0, 0, 0.85);
		font-family: PingFang SC;
		font-size: 16px;
		font-style: normal;
		font-weight: 500;
	}
}
</style>
