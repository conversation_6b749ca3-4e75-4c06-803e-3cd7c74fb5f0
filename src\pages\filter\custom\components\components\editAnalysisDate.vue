<template>
	<div>
		<el-dialog title="创建自定义时间" :visible.sync="dialogVisible">
			<div>
				<div class="flex_start mb-16">
					<div style="width: 100px">名称</div>
					<div><el-input v-model="name" placeholder="请输入自定义时间名称"></el-input></div>
				</div>
				<div class="flex_start mb-16">
					<div style="width: 100px">开始日期</div>
					<div><el-date-picker v-model="date" type="date" placeholder="请选择开始日期" value-format="yyyy-MM-dd"></el-date-picker></div>
				</div>
				<div class="flex_start mb-16">
					<div style="width: 100px">是否公开</div>
					<div><el-switch v-model="ispubilc" active-color="#4096ff" inactive-color="#e7e7e7"></el-switch></div>
				</div>
			</div>
			<div slot="footer">
				<el-button @click="dialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="submit">确 定</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import { saveModel } from '@/api/pages/Tools.js';
export default {
	data() {
		return {
			dialogVisible: false,
			name: '',
			date: '',
			ispublic: false
		};
	},
	methods: {
		getData() {
			this.dialogVisible = true;
		},
		async submit() {
			let postData = {
				model_name: this.name,
				model_description: '',
				ispublic: this.ispublic,
				ismanager: false,
				source: 'customAnalysisDate',
				type: 'equity',
				title: '--',
				user_permission: [],
				model_args: [{ start_date: this.date, end_date: this.moment().format('YYYY-MM-DD') }]
			};
			let data = await saveModel(postData);
			if (data?.mtycode == 200) {
				this.$message.success('保存成功');
				this.$emit('resolveFather');
				this.dialogVisible = false;
			} else {
				this.$message.warning(data?.mtymessage);
			}
		}
	}
};
</script>

<style></style>
