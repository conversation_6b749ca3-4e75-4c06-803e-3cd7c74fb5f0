<!--  -->
<template>
	<div class="">
		<el-dialog title="保存为组合" :visible.sync="addzuheflag2" width="30%" destroy-on-close>
			<div class="savemodel" style="width: 100%">
				<el-form :model="usermodal">
					<el-form-item label="组合名称">
						<el-input v-model="usermodal.name" autocomplete="off"></el-input>
					</el-form-item>
					<div class="height10border"></div>
					<el-form-item label="组合描述">
						<el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="usermodal.textarea"> </el-input>
					</el-form-item>
					<div class="height10border"></div>

					<div class="height10border"></div>
					<div style="text-align: right" class="demo-drawer__footer">
						<el-button
							type="primary"
							style="background: #d7dbe0 !important; color: balck !important; border: 1px solid #d7dbe0 !important"
							@click="addzuheflag2 = fasle"
							>取消</el-button
						>
						<el-button type="primary" @click="submitmodal">确认提交</el-button>
					</div>
				</el-form>
			</div>
		</el-dialog>
	</div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
//例如：import 《组件名称》 from '《组件路径》';
import axios from '@/api/index';
export default {
	//import引入的组件需要注入到对象中才能使用
	components: {},
	props: {
		addzuheflag: {
			type: Boolean,
			default: false
		},
		fundlist: {
			type: Array,
			default: []
		},
		dataobj: {
			type: Object,
			default: {}
		}
	},
	data() {
		//这里存放数据
		return {
			usermodal: {
				name: '',
				textarea: ''
			},
			temp: {},
			addzuheflag2: false,
			clicktt: false
		};
	},
	//监听属性 类似于data概念
	computed: {},
	//监控data中的数据变化
	watch: {
		dataobj(val) {
			// //console.log(val)

			if (val.clicktype == true) {
				this.clicktt = true;
				if (this.clicktt == true) {
					//    //console.log('???')
					this.addzuheflag2 = true;
				}
				this.clicktt = false;
				//   //console.log('??')
			}
			if (val.ftype == 'equity' || val.ftype == 'hkequity') {
				this.temp = {
					formData: val.formData,
					temp: val.temp,
					temp2: val.temp2,
					temp3: val.temp3,
					temp4: val.temp4,
					temp5: val.temp5,
					themevalue: val.themevalue,
					quanzhongtheme: val.quanzhongtheme,
					swonevalue: val.swonevalue,
					quanzhongswone: val.quanzhongswone,
					insertindex: val.insertindex,
					insertindexswone: val.insertindexswone,
					dpchoose: val.dpchoose,
					czchoose: val.czchoose,
					kcchoose: val.kcchoose,
					hgchoose: val.hgchoose,
					ejchoose: val.ejchoose,
					swchoose: val.swchoose,
					swonevalue2: val.swonevalue2,
					quanzhongswone2: val.quanzhongswone2,
					swonevalue3: val.swonevalue3,
					quanzhongswone3: val.quanzhongswone3,
					swonevalue4: val.swonevalue4,
					quanzhongswone4: val.quanzhongswone4
				};
			} else if (val.ftype == 'purebond' || val.ftype == 'bill') {
				this.temp = {
					formData: val.formData,
					temp1: val.temp1,
					czchoose: val.czchoose,
					dpchoose: val.dpchoose,
					temp4: val.temp4
				};
			} else if (val.ftype == 'cbond') {
				this.temp = {
					formData: val.formData,
					temp1: val.temp1
				};
			} else if (val.ftype == 'bond') {
				this.temp = {
					formData: val.formData,
					temp1: val.temp1,
					swonevalue: val.swonevalue,
					quanzhongswone: val.quanzhongswone,
					insertindex: val.insertindex,
					insertindexswone: val.insertindexswone
				};
			}
		}
	},
	//方法集合
	methods: {
		submitmodal() {
			let that = this;
			if (this.usermodal.name == null || this.usermodal.name == '') {
				that.$message('请输入组合名称');
			} else {
				if (this.fundlist.length > 50) {
					that.$message('选择的基金太多了');
				} else {
					axios
						.post(that.$baseUrl + '/HomePage/savealphacombination/', {
							type: this.ftype,
							ismanager: this.ismanager,
							pool_name: this.usermodal.name,
							pool_description: this.usermodal.textarea,
							model_args: this.temp,
							pool_fund: this.fundlist
						})
						.then((res) => {
							that.$message('保存组合成功');
							that.addzuheflag2 = false;
						})
						.catch((err) => {
							//  that.$message('失败')
							that.addzuheflag2 = false;
							////console.log(err)
							//that.$message('数据缺失')
						});
				}
			}
		}
	},
	//生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	//生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	beforeCreate() {}, //生命周期 - 创建之前
	beforeMount() {}, //生命周期 - 挂载之前
	beforeUpdate() {}, //生命周期 - 更新之前
	updated() {}, //生命周期 - 更新之后
	beforeDestroy() {}, //生命周期 - 销毁之前
	destroyed() {}, //生命周期 - 销毁完成
	activated() {} //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style></style>
