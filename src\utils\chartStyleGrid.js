/**
 *
 * @param {折线图}
 * color: 接收一个颜色数组(不传递使用默认) Array
 * tooltip: 接收一个function(不传递为无) Function
 * dataZoom: 接收一个布尔值(默认为false,不显示) Boolean
 * legend: 接收一个数组(默认为空) Array
 * xAxis: 接收一个对象({name(String,默认不显示),type(String,默认category),formatter(Function,默认为空),boundaryGap(Boolean,默认为false),isAlign(Boolean,默认为false),data(Array,必传)})
 * yAxis: 接收一个对象({name(String,默认不显示),type(String,默认value),formatter(Function,默认为空),data(Array,默认为空)})
 * series: 接收一个数组
 * @returns
 */
export function lineChartOption({ toolbox, color, tooltip, dataZoom, legend, xAxis, yAxis, series, grid }) {
	if (!xAxis || !xAxis?.length || typeof xAxis !== 'object') {
		return;
	}
	if (!series || !series.length || typeof series !== 'object') {
		return;
	}
	let option = {
		toolbox: {
			feature: {
				// dataZoom: {
				// 	yAxisIndex: 'none'
				// },
				// dataView: { readOnly: false },
				magicType: { type: ['line', 'bar'] },
				restore: {},
				saveAsImage: {}
			},
			top: -4,
			width: 104
		}
	};
	option['toolbox'] =
		toolbox == false
			? {
					feature: {
						saveAsImage: { pixelRatio: 3 }
					},
					top: -4,
					width: 104
			  }
			: {
					feature: {
						magicType: { type: ['line', 'bar'] },
						restore: {},
						saveAsImage: { pixelRatio: 3 }
					},
					top: -4,
					width: 104
			  };
	option['color'] = color?.length
		? color
		: ['#191970','#B22222','#008080','#FF8C00','#4B0082','#FF6B6B','#26619C','#CC7722','#FF00FF','#01796F','#9370DB','#BA55D3','#FF1493','#C71585','#4B0082','#9932CC','#8A2BE2','#DA70D6','#FF00FF','#8B008B'];
	option['tooltip'] = {
		trigger: 'axis',
		formatter: tooltip?.formatter ? tooltip?.formatter : undefined,
		axisPointer: {
			type: tooltip?.type ? tooltip?.type : 'line'
		}
	};
	dataZoom
		? (option['dataZoom'] = [
				{
					type: 'slider',
					zoomOnMouseWheel: false,
					preventDefaultMouseMove: false,
					start: 0,
					end: 100
				}
				// {
				// 	start: 0,
				// 	zoomOnMouseWheel: false,
				// 	preventDefaultMouseMove: false,
				// 	end: 100
				// }
		  ])
		: '';
	option['grid'] = {
		left: grid.left,
		right: grid.right,
		bottom: grid.bottom,
		top: grid.top, // 无图例18px
		containLabel: true
	};
	option['legend'] = {
		type: 'scroll',
		pageIcons: {
			horizontal: [
				'path://M11.7487 6.92214L6.30673 0.634973C6.15096 0.455009 5.85102 0.455009 5.69359 0.634973L0.251579 6.92214C0.049409 7.15658 0.231693 7.5 0.558148 7.5L11.4422 7.5C11.7686 7.5 11.9509 7.15658 11.7487 6.92214Z',
				'path://M0.251255 1.07786L5.69327 7.36503C5.84904 7.54499 6.14898 7.54499 6.30641 7.36503L11.7484 1.07786C11.9506 0.843416 11.7683 0.499999 11.4419 0.5L0.557824 0.5C0.231369 0.5 0.0490849 0.843417 0.251255 1.07786Z'
			]
		},
		data: legend ? legend : [],
		icon: 'path://M63.6 489.6h896.7v44.8H63.6z',
		top: '0',
		itemGap: 40,
		right: 128,
		width: '80%',
		// left: 128
		left: 'center',
		pageButtonGap: 16
	};
	option['xAxis'] = xAxis?.map((item) => {
		return {
			show: item?.show == false ? false : true,
			offset: 8,
			nameGap: 8,
			name: item?.name ? item?.name : '',
			nameTextStyle: {
				fontFamily: 'PingFang',
				fontStyle: 'normal',
				fontWeight: 400,
				fontSize: 12,
				color: 'rgba(0, 0, 0, 0.65)'
			},
			boundaryGap: true,
			type: item?.type ? item?.type : 'category',
			axisLine: {
				lineStyle: {
					color: '#e9e9e9'
				}
			},
			axisLabel: {
				fontSize: 12,
				color: 'rgba(0, 0, 0, 0.65)',
				showMinLabel: true,
				showMaxLabel: true,
				hideOverlap: true,
				formatter: item?.formatter ? item?.formatter : undefined
			},
			data: item?.isAlign
				? item?.data
				: item?.data?.map((obj, index) => {
						if (index == 0 || index == item.data?.length - 1) {
							return {
								value: obj,
								textStyle: { align: index == 0 ? 'left' : 'right' }
							};
						} else {
							return obj;
						}
				  })
		};
	});
	option['yAxis'] = yAxis?.map((item) => {
		return {
			show: item?.show == false ? false : true,
			offset: 8,
			nameGap: 20,
			scale: item?.scale == false ? false : true,
			type: item?.type ? item?.type : 'value',
			name: item?.name ? item?.name : '',
			nameTextStyle: {
				align: 'left',
				fontFamily: 'PingFang',
				fontStyle: 'normal',
				fontWeight: 400,
				fontSize: 12,
				color: 'rgba(0, 0, 0, 0.65)'
			},
			max: item?.max ? item.max : null,
			axisLine: {
				show: false
			},
			axisTick: {
				show: false
			},
			splitLine: {
				show: item?.splitLine == false ? false : true,
				lineStyle: {
					color: '#e9e9e9',
					type: 'dashed'
				}
			},
			axisLabel: {
				fontSize: 12,
				color: 'rgba(0, 0, 0, 0.65)',
				formatter: item?.formatter ? item?.formatter : undefined
			},
			data: item?.data ? item?.data : undefined
		};
	});
	option['series'] = series;
	return option;
}
/**
 *
 * @param {柱状图}
 * color: 接收一个颜色数组(不传递使用默认) Array
 * tooltip: 接收一个function(不传递为无) Function
 * dataZoom: 接收一个布尔值(默认为false,不显示) Boolean
 * legend: 接收一个数组(默认为空) Array
 * xAxis: 接收一个对象({name(String,默认不显示),type(String,默认category),formatter(Function,默认为空),boundaryGap(Boolean,默认为false),isAlign(Boolean,默认为false),data(Array,必传)})
 * yAxis: 接收一个对象({name(String,默认不显示),type(String,默认value),formatter(Function,默认为空),data(Array,默认为空)})
 * series: 接收一个数组
 * @returns
 */
export function barChartOption({ toolbox, color, tooltip, dataZoom, visualMap, legend, xAxis, yAxis, series }) {
	if (!xAxis || !xAxis?.length || typeof xAxis !== 'object') {
		return;
	}
	if (!series || !series.length || typeof series !== 'object') {
		return;
	}
	let option = {
		toolbox: {
			feature: {
				// dataZoom: {
				// 	yAxisIndex: 'none'
				// },
				// dataView: { readOnly: false },
				magicType: { type: ['line', 'bar'] },
				restore: {},
				saveAsImage: { pixelRatio: 3 }
			},
			top: -4,
			width: 104
		}
	};
	option['toolbox'] =
		toolbox == false
			? {
					feature: {
						saveAsImage: { pixelRatio: 3 }
					},
					top: -4,
					width: 104
			  }
			: {
					feature: {
						magicType: { type: ['line', 'bar'] },
						restore: {},
						saveAsImage: { pixelRatio: 3 }
					},
					top: -4,
					width: 104
			  };
	option['color'] = color?.length
		? color
		: ['#191970','#B22222','#008080','#FF8C00','#4B0082','#FF6B6B','#26619C','#CC7722','#FF00FF','#01796F','#9370DB','#BA55D3','#FF1493','#C71585','#4B0082','#9932CC','#8A2BE2','#DA70D6','#FF00FF','#8B008B'];
	option['tooltip'] = {
		trigger: 'axis',
		formatter: tooltip?.formatter ? tooltip?.formatter : undefined,
		axisPointer: {
			type: tooltip?.type ? tooltip?.type : 'line'
		}
	};
	dataZoom
		? (option['dataZoom'] = [
				{
					type: 'slider',
					zoomOnMouseWheel: false,
					preventDefaultMouseMove: false,
					start: 0,
					end: 100
				}
		  ])
		: '';
	visualMap ? (option['visualMap'] = visualMap) : '';
	option['grid'] = {
		left: yAxis?.[0]?.show == false ? '0' : '16px',
		right:
			(xAxis?.[0]?.name && xAxis?.[0]?.name !== '') || (yAxis?.length >= 2 ? (yAxis?.[1]?.show == false ? false : true) : false)
				? '32px'
				: '0',
		bottom: dataZoom ? '64px' : '18px',
		top: (legend && legend?.length) || legend?.data?.length ? '38px' : '18px', // 无图例18px
		containLabel: true
	};
	option['legend'] = {
		type: 'scroll',
		pageIcons: {
			horizontal: [
				'path://M11.7487 6.92214L6.30673 0.634973C6.15096 0.455009 5.85102 0.455009 5.69359 0.634973L0.251579 6.92214C0.049409 7.15658 0.231693 7.5 0.558148 7.5L11.4422 7.5C11.7686 7.5 11.9509 7.15658 11.7487 6.92214Z',
				'path://M0.251255 1.07786L5.69327 7.36503C5.84904 7.54499 6.14898 7.54499 6.30641 7.36503L11.7484 1.07786C11.9506 0.843416 11.7683 0.499999 11.4419 0.5L0.557824 0.5C0.231369 0.5 0.0490849 0.843417 0.251255 1.07786Z'
			]
		},
		data: legend?.length
			? legend.map((item) => {
					if (item?.name) {
						return {
							...item,
							icon: item?.icon == 'line' ? 'path://M63.6 489.6h896.7v44.8H63.6z' : ''
						};
					} else {
						return item;
					}
			  })
			: legend?.data?.length
			? legend?.data.map((item) => {
					if (item?.name) {
						return {
							...item,
							icon: item?.icon == 'line' ? 'path://M63.6 489.6h896.7v44.8H63.6z' : ''
						};
					} else {
						return item;
					}
			  })
			: [],
		// icon: 'path://M63.6 489.6h896.7v44.8H63.6z',
		top: legend?.top ? legend?.top : '0',
		itemGap: legend?.itemGap ? legend?.itemGap : 40,
		right: legend?.right ? legend?.right : 128,
		width: legend?.width ? legend?.width : '80%',
		left: legend?.left ? legend?.left : 'center',
		orient: legend?.orient ? legend?.orient : 'horizontal',
		select: legend?.select ? legend?.select : {},
		pageButtonGap: 16
	};
	option['xAxis'] = xAxis?.map((item) => {
		return {
			show: item?.show == false ? false : true,
			offset: 8,
			nameGap: 8,
			name: item?.name ? item?.name : '',
			position: item?.position ? item?.position : 'bottom',
			nameTextStyle: {
				fontFamily: 'PingFang',
				fontStyle: 'normal',
				fontWeight: 400,
				fontSize: 12,
				color: 'rgba(0, 0, 0, 0.65)'
			},
			type: item?.type ? item?.type : 'category',
			// boundaryGap: item?.boundaryGap ? item?.boundaryGap : false,
			axisLine: {
				lineStyle: {
					color: '#e9e9e9'
				}
			},
			splitLine: {
				show: false
			},
			axisTick: {
				show: false
			},
			axisLabel: {
				fontSize: 12,
				color: 'rgba(0, 0, 0, 0.65)',
				showMinLabel: true,
				showMaxLabel: true,
				hideOverlap: true,
				formatter: item?.formatter ? item?.formatter : undefined
			},
			data: item?.isAlign
				? item?.data
				: item?.data?.map((obj, index) => {
						if (index == 0 || index == item.data?.length - 1) {
							return {
								value: obj,
								textStyle: { align: index == 0 ? 'left' : 'right' }
							};
						} else {
							return obj;
						}
				  })
		};
	});
	option['yAxis'] = yAxis?.map((item) => {
		return {
			show: item?.show == false ? false : true,
			offset: 8,
			nameGap: 20,
			type: item?.type ? item?.type : 'value',
			name: item?.name ? item?.name : '',
			nameTextStyle: {
				align: 'left',
				fontFamily: 'PingFang',
				fontStyle: 'normal',
				fontWeight: 400,
				fontSize: 12,
				color: 'rgba(0, 0, 0, 0.65)'
			},
			axisLine: {
				show: false
			},
			axisTick: {
				show: false
			},
			max: item?.max ? item?.max : undefined,
			splitLine: {
				show: item?.splitLine == false ? false : true,
				lineStyle: {
					color: '#e9e9e9',
					type: 'dashed'
				}
			},
			axisLabel: {
				fontSize: 12,
				color: 'rgba(0, 0, 0, 0.65)',
				formatter: item?.formatter ? item?.formatter : undefined
			},
			data: item?.data ? item?.data : undefined
		};
	});
	option['series'] = series;
	return option;
	// return {
	// 	color: [
	// 		'rgba(253, 156, 255, 1)',
	// 		'rgba(254, 208, 238, 1)',
	// 		'rgba(254, 174, 174, 1)',
	// 		'rgba(253, 208, 159, 1)',
	// 		'rgba(251, 227, 142, 1)',
	// 		'rgba(169, 244, 208, 1)',
	// 		'rgba(208, 232, 255, 1)',
	// 		'rgba(159, 212, 253, 1)',
	// 		'rgba(174, 201, 254, 1)',
	// 		'rgba(219, 174, 255, 1)',
	// 		'rgba(154, 137, 255, 1)',
	// 		'#FD9CFF',
	// 		'#FED0EE',
	// 		'#FEAEAE',
	// 		'#FDD09F',
	// 		'#FBE38E',
	// 		'#A9F4D0',
	// 		'#D0E8FF',
	// 		'#9FD4FD',
	// 		'#AEC9FE',
	// 		'#DBAEFF',
	// 		'#9A89FF',
	// 		'#4096ff',
	// 		'#4096ff',
	// 		'#7388A9',
	// 		'#6F80DD',
	// 		'#6C96F2',
	// 		'#FD6865',
	// 		'#83D6AE',
	// 		'#88C9E9',
	// 		'#ED589D',
	// 		'#FA541C',
	// 		'#18C2A0',
	// 		'#E85D2D'
	// 	],
	// 	tooltip: {
	// 		trigger: 'axis'
	// 	},
	// 	legend: {
	// 		type: 'scroll',
	// 		pageIcons: {
	// 			horizontal: [
	// 				'path://M11.7487 6.92214L6.30673 0.634973C6.15096 0.455009 5.85102 0.455009 5.69359 0.634973L0.251579 6.92214C0.049409 7.15658 0.231693 7.5 0.558148 7.5L11.4422 7.5C11.7686 7.5 11.9509 7.15658 11.7487 6.92214Z',
	// 				'path://M0.251255 1.07786L5.69327 7.36503C5.84904 7.54499 6.14898 7.54499 6.30641 7.36503L11.7484 1.07786C11.9506 0.843416 11.7683 0.499999 11.4419 0.5L0.557824 0.5C0.231369 0.5 0.0490849 0.843417 0.251255 1.07786Z'
	// 			]
	// 		},
	// 		data: legend ? legend : [],
	// 		icon: 'path://M63.6 489.6h896.7v44.8H63.6z',
	// 		top: '0',
	// 		itemGap: 40,
	// 		right: 128,
	// 		width: '80%',
	// 		// left: 128
	// 		left: 'center',
	// 		pageButtonGap: 16
	// 	},
	// 	grid: {
	// 		left: '16px',
	// 		right: '32px',
	// 		bottom: '64px',
	// 		top: '38px', // 无图例18px
	// 		containLabel: true
	// 	},
	// 	toolbox: {
	// 		feature: {
	// 			// dataZoom: {
	// 			// 	yAxisIndex: 'none'
	// 			// },
	// 			// dataView: { readOnly: false },
	// 			magicType: { type: ['line', 'bar'] },
	// 			restore: {},
	// 			saveAsImage: {}
	// 		},
	// 		top: -4,
	// 		width: 104
	// 	},
	// 	dataZoom: [
	// 		{
	// 			type: 'inside',
	// 			start: 0,
	// 			end: 100
	// 		},
	// 		{
	// 			start: 0,
	// 			end: 100
	// 		}
	// 	],
	// 	xAxis: {
	// 		offset: 8,
	// 		nameGap: 8,
	// 		name: '日期',
	// 		nameTextStyle: {
	// 			fontFamily: 'PingFang',
	// 			fontStyle: 'normal',
	// 			fontWeight: 400,
	// 			fontSize: 12,
	// 			color: 'rgba(0, 0, 0, 0.65)'
	// 		},
	// 		type: 'category',
	// 		boundaryGap: false,
	// 		axisLine: {
	// 			lineStyle: {
	// 				color: '#e9e9e9'
	// 			}
	// 		},
	// 		axisLabel: {
	// 			fontSize: 12,
	// 			color: 'rgba(0, 0, 0, 0.65)'
	// 		},
	// 		data: xAxis.map((item, index) => {
	// 			if (index == 0 || index == xAxis.length - 1) {
	// 				return {
	// 					value: item,
	// 					textStyle: { align: index == 0 ? 'left' : 'right' }
	// 				};
	// 			} else {
	// 				return item;
	// 			}
	// 		})
	// 	},
	// 	yAxis: {
	// 		offset: 8,
	// 		type: 'value',
	// 		name: '收益率',
	// 		nameTextStyle: {
	// 			align: 'left',
	// 			fontFamily: 'PingFang',
	// 			fontStyle: 'normal',
	// 			fontWeight: 400,
	// 			fontSize: 12,
	// 			color: 'rgba(0, 0, 0, 0.65)'
	// 		},
	// 		nameGap: 20,
	// 		axisLine: {
	// 			show: false
	// 		},
	// 		axisTick: {
	// 			show: false
	// 		},
	// 		splitLine: {
	// 			lineStyle: {
	// 				color: '#e9e9e9',
	// 				type: 'dashed'
	// 			}
	// 		},
	// 		axisLabel: {
	// 			fontSize: 12,
	// 			color: 'rgba(0, 0, 0, 0.65)'
	// 		}
	// 	},
	// 	series: series
	// };
}
