<template>
	<div id="allReturnPerformance">
		<analysis-card-title title="总体业绩表现" @downloadExcel="exportExcel">
			<el-select v-model="activeBenchmark" multiple collapse-tags @change="changeBenchmark">
				<el-option v-for="item in benchmarkList" :key="item.code" :label="item.name" :value="item.code"> </el-option>
			</el-select>
		</analysis-card-title>
		<div v-loading="loading">
			<el-table :data="data" border stripe style="width: 100%" :cell-style="cellStyle">
				<el-table-column
					v-for="item in column"
					:key="item.value"
					:prop="item.value"
					:label="item.label"
					:align="item.align || 'gotoleft'"
					:fill="item.fill"
				>
					<template slot-scope="{ row }">
						<div>{{ item.format ? item.format(row[item.value]) : row[item.value] }}</div>
					</template>
				</el-table-column>
				<template slot="empty">
					<el-empty image-size="160"></el-empty>
				</template>
			</el-table>
		</div>
	</div>
</template>

<script>
import { filter_json_to_excel } from '@/utils/exportExcel.js';

// 获取基准列表
import { getBenchmarkList, getRiskFeatureRecent } from '@/api/pages/Analysis.js';
export default {
	data() {
		return {
			activeBenchmark: [],
			benchmarkList: [],
			data: [],
			column: [
				{
					label: '名称',
					value: 'name'
				},
				{
					label: '年初至今',
					value: 'theyear',
					format: this.fix2p,
					fill: 'red_or_green',
					sortable: true,
					align: 'right'
				},
				{
					label: '近一周',
					value: '1week',
					format: this.fix2p,
					fill: 'red_or_green',
					sortable: true,
					align: 'right'
				},
				{
					label: '近一月',
					value: '1month',
					format: this.fix2p,
					fill: 'red_or_green',
					sortable: true,
					align: 'right'
				},
				{
					label: '近一季',
					value: '1quarter',
					format: this.fix2p,
					fill: 'red_or_green',
					sortable: true,
					align: 'right'
				},
				{
					label: '近一年',
					value: '1year',
					format: this.fix2p,
					fill: 'red_or_green',
					sortable: true,
					align: 'right'
				},
				{
					label: '近两年',
					value: '2year',
					format: this.fix2p,
					fill: 'red_or_green',
					sortable: true,
					align: 'right'
				},
				{
					label: '近三年',
					value: '3year',
					format: this.fix2p,
					fill: 'red_or_green',
					sortable: true,
					align: 'right'
				},
				{
					label: '成立以来',
					value: 'eversince',
					format: this.fix2p,
					fill: 'red_or_green',
					sortable: true,
					align: 'right'
				}
			],
			info: {},
			loading: true
		};
	},
	methods: {
		async getData(info) {
			this.info = info;
			await this.getBenchmarkList();
			this.getRiskFeatureRecent();
		},
		// 监听基准选择切换
		changeBenchmark(value) {
			this.activeBenchmark = value;
			this.getRiskFeatureRecent();
		},
		// 获取基准列表
		async getBenchmarkList() {
			let data = await getBenchmarkList({
				code: this.info.code,
				flag: this.info.flag,
				type: this.info.type
			});
			this.benchmarkList = [];
			this.activeBenchmark = '';
			if (data?.mtycode == 200) {
				this.benchmarkList = data?.data
					.sort((a, b) => {
						return b.isdefault - a.isdefault;
					})
					.map((v) => {
						return { code: v.indexCode, name: v.indexName, flag: v.flag };
					});
				if (this.benchmarkList.findIndex((v) => v.code == '000300.SH') != -1) {
					this.activeBenchmark = ['000300.SH'];
				} else {
					this.activeBenchmark = [this.benchmarkList?.[0]?.code];
				}
			} else {
				this.benchmarkList = this.COMMON.default_index[this.info.type].map((v) => {
					return { code: v.indexCode, name: v.indexName, flag: '6' };
				});
				this.activeBenchmark = [this.benchmarkList?.[0]?.code];
			}
		},
		// 总体业绩表现
		async getRiskFeatureRecent() {
			this.loading = true;
			let data = await getRiskFeatureRecent({
				code: this.info.code,
				type: this.info.type,
				flag: this.info.flag,
				index_code: this.activeBenchmark.join(',')
			});
			this.loading = false;
			if (data?.mtycode == 200) {
				this.data = this.formatData(data?.data);
				console.log(this.data);
			}
		},
		// 格式化数据
		formatData(data) {
			let result = [];
			let rank = { code: 'rank', name: '同类排名' };
			data.map((item) => {
				let index = result.findIndex((v) => v.code == item.code);
				let obj = {};
				obj[item.recent] = item.cum_return;
				if (item.code == this.info.code) {
					let rank_obj = {};
					rank_obj[item.recent] = item.meterDescriptioncum_return;
					rank = { ...rank, ...rank_obj };
				}
				if (index == -1) {
					result.push({ code: item.code, name: this.returnItemName(item.code), ...obj });
				} else {
					result[index] = { ...result[index], ...obj };
				}
			});

			return [...result, rank];
		},
		// 返回数据项名称
		returnItemName(val) {
			if (val == this.info.code) {
				return this.info.name;
			} else if (this.activeBenchmark.includes(val)) {
				return this.benchmarkList.find((v) => v.code == val)?.name;
			} else if (val.includes('self')) {
				return '自身基准';
			} else {
				return val;
			}
		},
		// 动态判断数据，更改颜色
		cellStyle({ column, row }) {
			let val = row[column.property];
			if (val * 1 && !isNaN(val)) {
				if (val * 1 > 0) {
					return { color: '#CF1322' };
				} else if (val * 1 < 0) {
					return { color: '#389E0D' };
				}
			}
		},
		// 百分化数据，保留两位小数
		fix2p(val) {
			return val * 1 && !isNaN(val) ? (val * 100).toFixed(2) + '%' : val?.includes('/') ? val : '--';
		},
		// 导出excel
		exportExcel() {
			let list = this.column.map((item) => {
				return {
					label: item.label,
					value: item.value
				};
			});
			filter_json_to_excel(list, this.data, '业绩表现');
		},
		// 导出
		async createPrintWord(info) {
			let list = this.column.map((item) => {
				return {
					label: item.label,
					value: item.value
				};
			});
			if (this.data.length) {
				return [
					...this.$exportWord.exportTitle('业绩表现'),
					...this.$exportWord.exportDescripe('对比基准：' + this.benchmarkList.find((v) => v.code == this.activeBenchmark)?.name),
					...this.$exportWord.exportTable(list, this.data, '', true)
				];
			} else {
				return [];
			}
		}
	}
};
</script>

<style></style>
