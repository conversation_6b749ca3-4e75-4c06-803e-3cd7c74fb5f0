<template>
  <div class="chart_one">
    <div style="display: flex; align-items: center; justify-content: space-between">
      <div class="title" style="flex: 1; text-align: left">直投股票capm分析</div>
      <div>
        比较基准选择：
        <el-select
          v-show="info.type !== 'portfolio'"
          v-model="benchmarkvalue"
          placeholder="请选择比较基准"
        >
          <el-option
            v-for="item in benchmarkoptions"
            :key="item.index_code"
            :label="item.index_name"
            :value="item.index_code"
          ></el-option>
        </el-select>
        <search-components
          v-show="info.type == 'portfolio'"
          type="index"
          @resolveFather="getIndexInfo"
        ></search-components>
        <el-button
          icon="el-icon-document-delete"
          style="margin-left: 16px"
          @click="exportExcel"
        >导出Excel</el-button>
      </div>
    </div>
    <div class="flex_card" v-loading="loading">
      <el-table
        :data="fourdata1"
        class="table"
        :default-sort="{ prop: 'name', order: 'ascending' }"
        ref="multipleTable"
        header-cell-class-name="table-header"
        height="300px"
      >
        <el-table-column prop="name" label="静态capm" align="gotoleft"></el-table-column>
        <el-table-column :label="'基金to' + benchmarkvaluename" align="gotoleft" prop="meter">
          <template slot-scope="scope">
            <span>{{ scope.row.meter }}</span>
          </template>
        </el-table-column>
      </el-table>
      <el-table
        :data="fourdata2"
        class="table"
        :default-sort="{ prop: 'name', order: 'ascending' }"
        ref="multipleTable"
        header-cell-class-name="table-header"
        height="300px"
      >
        <el-table-column prop="name" label="动态capm" align="gotoleft"></el-table-column>
        <el-table-column :label="'基金to' + benchmarkvaluename" align="gotoleft" prop="meter">
          <template slot-scope="scope">
            <span>{{ scope.row.meter }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { exportTitle, exportTable } from "@/utils/exportWord.js";
import { filter_json_to_excel } from "@/utils/exportExcel.js";
import searchComponents from "@/components/components/components/search/index.vue";

export default {
  components: { searchComponents },
  data() {
    //这里存放数据
    return {
      datatable: [],
      benchmarkvalue: "",
      benchmarkvaluename: "",
      benchmarkoptions: [],
      fourdata1: [],
      fourdata2: [],
      notesData: {
        capm002: ""
      },
      indexInfo: {},
      loading: true,
      info: {}
    };
  },
  filters: {
    fix2p(value) {
      if (value == "--") return value;
      else return (value * 100).toFixed(2) + "%";
    },
    fixY(value) {
      if (value == "--") return value;
      else {
        return (Number(value) / 100000000).toFixed(2) + "亿";
      }
    },
    fixp(value) {
      if (value == "--") return value;
      else {
        return Number(value).toFixed(2);
      }
    }
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {
    benchmarkvalue(value) {
      for (let i = 0; i < this.benchmarkoptions.length; i++) {
        if (this.benchmarkoptions[i].index_code == value)
          this.benchmarkvaluename = this.benchmarkoptions[i].index_name;
      }
      this.$emit("resolveFather", this.benchmarkvalue);
    }
  },
  //方法集合
  methods: {
    getBenchmarkList(data) {
      this.benchmarkoptions = data;
      if (this.benchmarkoptions.length > 0) {
        this.benchmarkvalue = data?.[0]?.index_code;
        this.benchmarkvaluename = data?.[0]?.index_name;
      }
      this.$emit("resolveFather", this.benchmarkvalue);
    },
    getData(data, info) {
      this.info = info;
      this.loading = false;
      if (data?.static_capm) {
        let obj = [];
        for (const key in data.static_capm) {
          obj.push({
            name:
              key == "alpha"
                ? "阿尔法"
                : key == "beta"
                ? "贝塔"
                : key == "beta_bull"
                ? "正向贝塔"
                : key == "beta_bear"
                ? "负向贝塔"
                : key == "r_squared"
                ? "R^2"
                : key == "trackingerror"
                ? "跟踪误差"
                : key == "activepremium"
                ? "主动增益"
                : key == "information"
                ? "信息比率"
                : key == "treynor"
                ? "特雷诺系数"
                : key,
            meter: data.static_capm[key]
          });
        }
        this.fourdata1 = obj;
      }
      if (data?.change_capm) {
        let obj = [];
        for (const key in data.change_capm) {
          obj.push({
            name:
              key == "alpha"
                ? "阿尔法"
                : key == "beta"
                ? "贝塔"
                : key == "beta_bull"
                ? "正向贝塔"
                : key == "beta_bear"
                ? "负向贝塔"
                : key == "r_squared"
                ? "R^2"
                : key == "trackingerror"
                ? "跟踪误差"
                : key == "activepremium"
                ? "主动增益"
                : key == "information"
                ? "信息比率"
                : key == "treynor"
                ? "特雷诺系数"
                : key,
            meter: data.static_capm[key]
          });
        }
        this.fourdata2 = obj;
      }
    },
    getIndexInfo(info) {
      this.indexInfo = info;
      this.loading = true;
      this.$emit("resolveFather", info.id);
    },
    exportExcel() {
      let list1 = [
        {
          label: "静态capm",
          fill: "header",
          value: "name1"
        },
        {
          label: "基金to" + this.benchmarkvaluename,
          value: "meter1"
        },
        {
          label: "动态capm",
          fill: "header",
          value: "name2"
        },
        {
          label: "基金to" + this.benchmarkvaluename,
          value: "meter2"
        }
      ];
      let data = [
        ...this.fourdata1.map(item => {
          return {
            name1: item.name,
            meter1: item.meter
          };
        }),
        ...this.fourdata2.map(item => {
          return {
            name2: item.name,
            meter2: item.meter
          };
        })
      ];
      filter_json_to_excel(list1, data, "直投股票capm分析");
    },
    createPrintWord() {
      let list = [
        {
          label: "静态capm",
          fill: "header",
          value: "name"
        },
        {
          label: "基金to" + this.benchmarkvaluename,
          value: "meter"
        },
        {
          label: "动态capm",
          fill: "header",
          value: "dynamic_name"
        },
        {
          label: "基金to" + this.benchmarkvaluename,
          value: "dynamic_meter"
        }
      ];
      let data = [
        ...this.fourdata1,
        ...this.fourdata2?.map(item => {
          return {
            ...item,
            dynamic_name: item.name,
            dynamic_meter: item.meter
          };
        })
      ];
      if (data.length) {
        return [...exportTitle("直投股票capm分析"), ...exportTable(list, data)];
      } else {
        return [];
      }
    }
  }
};
</script>
