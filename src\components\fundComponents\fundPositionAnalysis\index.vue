<!--  -->
<template>
  <div class="chart_one">
    <div style="display: flex; align-items: center; justify-content: space-between">
      <div class="title" style="flex: 1; text-align: left">基金持仓分析</div>
      <div>
        <!-- <el-cascader v-model="targetQuarter" :options="quarterList" separator=" " @change="changgedate"></el-cascader> -->
        <el-select v-model="targetQuarter" placeholder @change="changgedate">
          <el-option
            v-for="item in quarterList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <el-button
          icon="el-icon-document-delete"
          style="margin-left: 16px"
          @click="exportExcel"
        >导出Excel</el-button>
      </div>
    </div>
    <div>
      <el-table
        v-loading="loading"
        :data="fourdata1"
        style="width: 99% !important"
        class="table"
        :default-sort="{ prop: 'fof_weight', order: 'descending' }"
        ref="multipleTable"
        header-cell-class-name="table-header"
        max-height="400px"
      >
        <el-table-column prop="name" label="名称" align="gotoleft"></el-table-column>
        <el-table-column prop="manager_name" label="基金经理" align="gotoleft"></el-table-column>
        <el-table-column prop="type" label="基金类型" align="gotoleft"></el-table-column>
        <el-table-column prop="netasset" sortable label="规模" align="gotoleft">
          <template slot-scope="scope">{{ scope.row.netasset | fixY }}</template>
        </el-table-column>
        <el-table-column sortable prop="fof_weight" label="持仓权重" align="gotoleft">
          <template slot-scope="scope">{{ scope.row.fof_weight | fix2p }}</template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { exportTitle, exportTable } from "@/utils/exportWord.js";
import { filter_json_to_excel } from "@/utils/exportExcel.js";
// 最新报告持仓分析
import { getDateList } from "@/api/pages/Analysis.js";
// 基金持仓分析
export default {
  name: "fundPositionAnalysis",
  data() {
    //这里存放数据
    return {
      info: {},
      companyCreateDate: "",
      quarterList: [],
      targetQuarter: "",
      datatable: [],
      benchmarkvalue: "",
      benchmarkvaluename: "",
      benchmarkoptions: [],
      fourdata1: [],
      fourdata2: [],
      notesData: {
        fholdfund: ""
      },
      loading: true
    };
  },
  filters: {
    fix2p(value) {
      if (value == "--") return value;
      else return (value * 100).toFixed(2) + "%";
    },
    fixY(value) {
      if (value == "--") return value;
      else {
        return (Number(value) / *********).toFixed(2) + "亿";
      }
    },
    fixp(value) {
      if (value == "--") return value;
      else {
        return Number(value).toFixed(2);
      }
    }
  },
  watch: {
    benchmarkvalue(value) {
      for (let i = 0; i < this.benchmarkoptions.length; i++) {
        if (this.benchmarkoptions[i].index_code == value)
          this.benchmarkvaluename = this.benchmarkoptions[i].index_name;
      }
      this.getlabeldata();
    }
  },
  //方法集合
  methods: {
    getData(data) {
      this.loading = false;
      this.fourdata1 = data;
    },
    // 获取报告持仓季度列表
    async getDateList() {
      let data = await getDateList({
        code: this.info.code,
        type: this.info.type,
        flag: this.info.flag
      });
      if (data?.mtycode == 200) {
        this.getDateListData(data?.data);
      }
    },
    // 获取持仓季度列表
    getDateListData(data) {
      this.quarterList = data
        ?.sort((a, b) => {
          return this.moment(b).isAfter(a) ? 1 : -1;
        })
        ?.map(item => {
          return { label: item, value: item };
        });
      this.targetQuarter = this.quarterList?.[0]?.value;
      this.resolveData();
    },
    changgedate() {
      this.resolveData();
    },
    resolveData() {
      this.loading = true;
      this.$emit("resolveFather", this.targetQuarter);
    },
    generateQuarterList() {
      let option = [];
      let qList = ["Q1", "Q2", "Q3", "Q4"];
      let pre = this.companyCreateDate;
      let now = this.FUNC.transformDate(new Date());
      let preYear = pre.slice(0, 4);
      let nowYear = now.slice(0, 4);
      let preQ = this.FUNC.dateToQuarter(pre).slice(5);
      let nowQ = this.FUNC.dateToQuarter(now).slice(5);
      let yList = Array.from(
        { length: Math.abs(nowYear - preYear + 1) },
        (item, index) => (item = parseInt(preYear) + index)
      );

      for (let y of yList) {
        let yobj = {
          value: y,
          label: y,
          children: []
        };
        if (y == preYear) {
          qList.forEach(q => {
            if (q >= preQ) {
              yobj.children.push({ value: q, label: q });
            }
          });
        } else if (y == nowYear) {
          qList.forEach(q => {
            if (q <= nowQ) {
              yobj.children.push({ value: q, label: q });
            }
          });
        } else {
          qList.forEach(q => yobj.children.push({ value: q, label: q }));
        }
        option.push(yobj);
      }
      this.quarterList = option;
      if (option[option.length - 1].children.length == 1) {
        this.targetQuarter = [
          option[option.length - 2].value,
          option[option.length - 2].children[
            option[option.length - 2].children.length - 1
          ].value
        ];
      } else {
        this.targetQuarter = [
          option[option.length - 1].value,
          option[option.length - 1].children[
            option[option.length - 1].children.length - 2
          ].value
        ];
      }
      this.resolveData();
    },
    getTime(data, info) {
      this.info = info;
      if (data.funds_manager && data.funds_manager.length > 0) {
        this.companyCreateDate = data.fund_message[0]["成立日期"] || "暂无数据";
      } else {
        this.companyCreateDate = "2008-01-01";
      }
      this.getDateList();
    },
    exportExcel() {
      let list = [
        {
          label: "名称",
          value: "name"
        },
        {
          label: "基金经理",
          value: "manager_name"
        },
        {
          label: "基金类型",
          value: "type"
        },
        {
          label: "规模",
          value: "netasset",
          format: "fixY"
        },
        {
          label: "持仓权重",
          value: "fof_weight",
          format: "fix2p"
        },
        {
          label: "占总净值比例",
          value: "ratioinN",
          format: "fix2b"
        },
        {
          label: "持券数量(万)",
          value: "holdings",
          format: "fix2b"
        },
        {
          label: "持仓市值(亿)",
          value: "value",
          format: "fix2b"
        }
      ];
      filter_json_to_excel(list, this.fourdata1, "基金持仓分析");
    },
    createPrintWord() {
      let list = [
        {
          label: "名称",
          value: "name"
        },
        {
          label: "基金经理",
          value: "manager_name"
        },
        {
          label: "基金类型",
          value: "type"
        },
        {
          label: "规模",
          value: "netasset",
          format: "fixY"
        },
        {
          label: "持仓权重",
          value: "fof_weight",
          format: "fix2p"
        },
        {
          label: "占总净值比例",
          value: "ratioinN",
          format: "fix2b"
        },
        {
          label: "持券数量(万)",
          value: "holdings",
          format: "fix2b"
        },
        {
          label: "持仓市值(亿)",
          value: "value",
          format: "fix2b"
        }
      ];
      if (this.fourdata1.length) {
        return [
          ...exportTitle("基金持仓分析"),
          ...exportTable(list, this.fourdata1)
        ];
      } else {
        return [];
      }
    }
  }
};
</script>
