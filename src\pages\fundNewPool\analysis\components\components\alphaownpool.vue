<!--  -->
<template>
  <div class="alphaownpool">
    <el-dialog :title="type == 'fund' ? '批量导入基金' : '导入自定义基金经理组合'"
               :visible.sync="alphaownshow"
               width="400"
               destroy-on-close>
      <div class="remind">
        <span style="margin-left: 10px; font-size: 14px; font-weight: 400; color: #000000d9">上传前请先按Excel模板中的格式编辑内容</span>
        <img style="margin-left: 12px"
             :src="require('../../../../../../public/images/excelImg.svg')"
             alt="Excel" />
        <a href="./samplepool.xlsx"
           download="示例excel模板">下载Excel模板</a>
      </div>
      <!-- :auto-upload="false" -->
      <el-upload style="margin-top: 10px"
                 class="upload-demo"
                 drag
                 :action="action1"
                 :headers="headersss"
                 :on-preview="handlePreview"
                 :on-remove="handleRemove"
                 :on-error="errorok"
                 :on-success="successok"
                 :before-remove="beforeRemove"
                 :multiple="false"
                 :limit="1"
                 :on-change="loadJsonFromFile"
                 :data="{ id: id }"
                 ref="upload"
                 :before-upload="beforeAvatarUpload"
                 :on-exceed="handleExceed"
                 :auto-upload="false"
                 :file-list="formfile">
        <!-- 				accept="xlsx"
        -->
        <div class="upBox">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">
            将文件拖拽至此区域，或
            <em>点击添加</em>
          </div>
          <div class="el-upload__tip"
               slot="tip">支持.xls，.xlsx 格式，限500kb以内</div>
        </div>
      </el-upload>
      <div class="footer"
           style="margin-top: 5px">
        <div style="font-size: 16px; font-weight: 600">导入规则</div>
        <div style="margin-top: 5px">
          <div class="one"
               style="font-size: 14px; font-weight: 500">1.请先下载模板，在模板中按要求填写信息，然后上传该文件。</div>
          <div class="two"
               style="font-size: 14px; font-weight: 500">2.文件大小不超过500KB</div>
        </div>
      </div>
      <div style="display:flex;justify-content:right">
        <div>
          <el-button @click="submitExcel"
                     type="primary">
            上传
          </el-button>
        </div>
        <div style="margin-left:16px">
          <el-button @click="alphaownshow=false"
                     type="">取消</el-button>

        </div>
      </div>
      <!-- <div class="savemodelfile" style="width: 100%">
				<el-form :model="usermodal">
					<div></div>
					<div class="upload-main-style">
						<el-upload
							style="text-align: center"
							class="upload-demo"
							:action="action1"
							:headers="headersss"
							:on-preview="handlePreview"
							:on-remove="handleRemove"
							:on-error="errorok"
							:on-success="successok"
							:before-remove="beforeRemove"
							:multiple="false"
							:limit="1"
							:on-change="loadJsonFromFile"
							:auto-upload="false"
							:data="{ id: id }"
							ref="upload"
							accept="xlsx"
							:before-upload="beforeAvatarUpload"
							:on-exceed="handleExceed"
							:file-list="formfile"
						>
							<el-button size="small" type="primary">点击上传</el-button>
							<div slot="tip" class="el-upload__tip">只能上传excel文件，且不超过500kb</div>
						</el-upload>
					</div>
					<div class="height10border"></div>
					<div>
						<a href="./samplepool.xlsx" download="示例excel模板">示例excel模板下载</a>
						<br />
						excel格式如下表：
						<el-table :data="sampledata" border>
							<el-table-column prop="code" align="gotoleft" :label="type == 'fund' ? '基金代码' : '基金经理代码'"></el-table-column>
							<el-table-column></el-table-column>
							<el-table-column></el-table-column>
						</el-table>
					</div>
					<div class="height10border"></div>
					<div style="text-align: right" class="demo-drawer__footer">
						<el-button
							type="primary"
							style="background: #d7dbe0 !important; color: balck !important; border: 1px solid #d7dbe0 !important"
							@click="alphaownshow = fasle"
							>取消</el-button
						>
						<el-button type="primary" @click="submitnew">确认提交</el-button>
					</div>
				</el-form>
      </div>-->
    </el-dialog>
  </div>
</template>
<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';`
import axios from "@/api/index.js";
import store from "@/store/store";
import { VueEasyJwt } from "vue-easy-jwt";
const jwt = new VueEasyJwt();
export default {
  //import引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    type: { type: String, default: "fund" }
  },
  data () {
    //这里存放数据
    return {
      dataTableModel: [],
      id: "",
      headersss: null,
      alphaownshow: false,
      formfile: null,
      file: null,
      action1: "",
      sampledata: [
        { code: "000001", weight: "50" },
        { code: "000057", weight: "30" },
        { code: "110011", weight: "20" },
        { code: "......", weight: "0" }
      ]
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    closeDialog () {
      this.alphaownshow = false
      this.$refs.upload.clearFiles();
      this.$refs.upload.$el.querySelectorAll('.el-upload-list')[0].innerHTML = ""
    },
    // 手动提交excel
    submitExcel () {
      this.$emit('submitExcel', this.dataTableModel.map(item => { return item.a }))
    },
    beforeAvatarUpload (file) {

      console.log("file", file);
      var testmsg = file.name.substring(file.name.lastIndexOf(".") + 1);
      const extension = testmsg === "xls";
      const extension2 = testmsg === "xlsx";
      const isLt2M = file.size / 1024 / 1024 < 0.5;
      if (!extension && !extension2) {
        this.$message.error("上传文件类型只能是.xls或者.xlsx格式");
        return false;
      }
      if (!isLt2M) {
        this.$message.error("上传文件大小不能超过 500KB!");
      }
      return (extension || extension2) && isLt2M;

    },
    loadJsonFromFile (file, fileList) {
      this.file = file;
      this.formfile = fileList;
      const _this = this;
      _this.dataTableModel = [];
      const fileName = file.name;
      const reader = new FileReader();
      reader.readAsArrayBuffer(file.raw);
      reader.onload = function () {
        const buffer = reader.result;
        const bytes = new Uint8Array(buffer);
        const length = bytes.byteLength;
        let binary = '';
        for (let i = 0; i < length; i++) {
          binary += String.fromCharCode(bytes[i]);
        }
        // const XLSX = require('xlsx');
        const wb = XLSX.read(binary, {
          type: 'binary'
        });
        const outdata = XLSX.utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]]);
        outdata.forEach((i) => {
          let obj = {
            a: i['基金代码'],
          };
          if (i['基金代码'] == '') {
          } else {
            _this.dataTableModel.push(obj); //此处是把数据添加到表格中
          }
        });
      };
      console.log(_this.dataTableModel)
    },
    submitnew () {
      let that = this;
      if (jwt.isExpired(store.state.token)) {
        // try{
        axios
          .post(that.$baseUrl + "/api-token-refresh/", {
            refresh: store.state.retoken
          })
          .then(res => {
            // //console.log('res......')
            // //console.log(res);
            store.dispatch("changetoken", res.data.access);
            // config.headers.authorization = 'Bearer ' + store.state.token;
            that.headersss = {
              authorization: "Bearer " + store.state.token
            };
            // console.log( store.state.token);
            setTimeout(() => {
              that.$refs.upload.submit();
            }, 560);
          })
          .catch(err => {
            store.dispatch("changetoken", "");
            store.dispatch("changeusername", "");
            store.dispatch("changeuserid", "");
            store.dispatch("changerefreshtoken", "");
            localStorage.removeItem("username");
            localStorage.removeItem("token");
            localStorage.removeItem("id");
            localStorage.removeItem("retoken");
            that.$router.replace({
              path: "login"
            });
            if (flag == 0) {
              flag += 1;
              alert("登录状态过期,请重新登录");
            }
            config.headers.authorization = "Bearer " + store.state.token;
            return config;
          });

        // }
        // catch(err){
        // 	//console.log('trycatvh')
        // 	//console.log(err)
        // }
      } else {
        // config.headers.authorization = 'Bearer ' + store.state.token;
        // return config;

        this.$refs.upload.submit();
      }
    },
    errorok () {
      this.$message.error("上传失败");
    },
    showitem (val) {
      this.id = val;
      this.alphaownshow = true;
    },
    successok () {
      this.alphaownshow = false;
      this.$message.success("上传成功");
      this.$emit("refrshtable");
      this.$refs.upload.clearFiles();
    },
    handleRemove (file, fileList) {
      //console.log(file, fileList);
    },
    handlePreview (file) {
      //console.log(file);
    },
    handleExceed (files, fileList) {
      this.$message.warning(
        `当前限制选择 1 个文件，本次选择了 ${files.length
        } 个文件，共选择了 ${files.length + fileList.length} 个文件`
      );
    },
    beforeRemove (file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`);
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () {
    this.headersss = {
      authorization: "Bearer " + store.state.token
    };
    this.action1 = this.$baseUrl + "/system/mixed/Report/getfile/";
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () { },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style scoped>
.leftmargin20 {
	margin-left: 10px !important;
}

.remind {
	width: 100%;
	height: 42px;
	background-color: #f5f5f5;
	display: flex;
	align-items: center;
}

.upload-demo {
	/* width: 100%;
	height: 700px; */
}
.savemodelfile {
	border-top: 1px solid #e5e5e5;
	border-bottom: 1px solid #e5e5e5;
	padding: 20px;
}
</style>
<style>
.alphaownpool .el-dialog__body {
	padding: 10px 20px !important;
}
.el-upload {
	width: 100%;
	height: 350px;
}
.el-upload-dragger {
	width: 100% !important;
	height: 100% !important;
}
.upBox {
	margin-top: 55px;
	/* display: flex;
	justify-content: center;
	align-items: center; */
}
</style>
