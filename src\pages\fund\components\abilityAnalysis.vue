<!-- 能力分析 -->
<template>
	<div>
		<div class="flex_card">
			<div v-for="item in templateList" :key="item.value" v-show="item.isshow" :class="item.type">
				<component :is="item.is" :ref="item.value" @resolveFather="item.methods" v-loading="loading" :showDescription="true"></component>
			</div>
		</div>
	</div>
</template>

<script>
// 已披露股票持仓
import stocksReportPosition from '@/components/components/components/stocksReportPosition/index.vue';
// 个股复盘
import longTermHoldingShares from '@/components/components/fundComponents/longTermHoldingShares/index.vue';
// 前十大相对质量
import topTenAttacks from '@/components/components/fundComponents/topTenAttacks/index.vue';
// 六种买入卖出模式
import sixBuyingSellModes from '@/components/components/components/sixBuyingSellModes/index.vue';

// 股票PLUS
import stockReturnPlus from '@/components/components/components/stockReturnPlus/index.vue';
// 估值分析
import valuationAnalysis from '@/components/components/components/valuationAnalysis/index.vue';
// PB-ROE估值
import PBROEvaluation from '@/components/components/components/PBROEvaluation/index.vue';
// 持股加权估值水平
import shareholdingWeightedValuation from '@/components/components/components/shareholdingWeightedValuation/index.vue';
// 持仓加权盈利能力
import positionWeightedProfitability from '@/components/components/components/positionWeightedProfitability/index.vue';

export default {
	components: {
		stocksReportPosition,
		longTermHoldingShares,
		sixBuyingSellModes,
		topTenAttacks,
		stockReturnPlus,
		valuationAnalysis,
		PBROEvaluation,
		shareholdingWeightedValuation,
		positionWeightedProfitability
	},
	data() {
		return {
			name: '持股分析',
			info: {},
			templateList: [],
			requestOver: [],
			requestAll: [],
			loading: true,
			componentsName: 'abilityAnalysis'
		};
	},
	props: {
		showEditor: {
			type: Boolean,
			default: false
		}
	},
	methods: {
		// 接收/返回组件列表
		getTemplateList(list) {
			if (list) {
				this.templateList = [...list];
			} else {
				return this.templateList;
			}
		},
		// 获取父组件数据
		getData(data) {
			this.info = data;
			this.loading = true;
			this.requestOver = [];
			this.formatTemplatList();
		},
		// 获取打印数据
		async createPrintWord(info) {
			this.info = info;
			let printData = [];
			this.templateList.map((item) => {
				if (item.isshow) {
					if (this.$refs[item.value]?.[0].createPrintWord) {
						let list = this.$refs[item.value]?.[0].createPrintWord(this.info);
						printData.push(list);
					}
				}
			});
			let data = await Promise.all(printData);
			data.unshift(this.$exportWord.exportFirstTitle(this.name));
			return data;
		},
		// 格式化模板列表
		formatTemplatList() {
			this.$nextTick(() => {
				this.templateList.map((item) => {
					if (item.typelist.indexOf(this.info.type) !== -1) {
						this.$refs[item.value]?.[0]?.getData(this.info);
						this.loading = false;
					}
				});
			});
		}
	}
};
</script>

<style scoped>
div .six_buy_sell_chart {
	width: 300px;
	height: 200px;
}
.charts_three_class {
	width: 100%;
	/* flex:1;
	min-width:752px !important; */
	/* height: 200px; */
	position: relative;
	page-break-inside: avoid;
}
.flex_card .small_template {
	position: relative;
	height: 324px;
	overflow-x: hidden;
}
</style>
