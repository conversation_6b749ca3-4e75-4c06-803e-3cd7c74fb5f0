<template>
	<div style="margin-top: 16px">
		<el-table
			ref="multipleTable"
			:data="tableData"
			tooltip-effect="dark"
			border
			:cell-style="cellStyle"
			style="width: 100%"
			:row-key="getRowKey"
		>
			<!-- <el-table-column type="selection" width="60" class-name="" align="center" :reserve-selection="true"></el-table-column> -->
			<el-table-column label="字段" width="150" align="gotoleft">
				<template slot-scope="scope">{{ scope.row.value }}</template>
			</el-table-column>
			<el-table-column prop="label" label="名称" width="250" align="gotoleft"> </el-table-column>
			<el-table-column prop="description" label="备注" show-overflow-tooltip align="gotoleft"> </el-table-column>
		</el-table>
	</div>
</template>

<script>
export default {
	data() {
		return {
			tableData: [],
			multipleSelection: []
		};
	},
	methods: {
		getRowKey(row) {
			// console.log('rrow', row);
			return row.fund_code;
		},
		getData(data) {
			this.tableData = data;
		},
		toggleSelection(rows) {
			if (rows) {
				rows.forEach((row) => {
					this.$refs.multipleTable.toggleRowSelection(row);
				});
			} else {
				this.$refs.multipleTable.clearSelection();
			}
		},
		handleSelectionChange(val) {
			console.log('vv', val);
			this.multipleSelection = val;
			this.$emit('changeSelection', val);
		},
		cellStyle({ row, column, rowIndex, columnIndex }) {
			if (columnIndex != 3) {
				return 'background:#fafafa';
			}
		}
	}
};
</script>
