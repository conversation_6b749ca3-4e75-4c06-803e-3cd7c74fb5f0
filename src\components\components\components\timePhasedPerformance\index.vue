<template>
	<div>
		<analysis-card-title title="分时段业绩表现" @downloadExcel="exportExcel">
			<div class="flex_start">
				<div class="mr-16">
					<el-select v-model="index" @change="changeIndex" placeholder="请选择比较基准">
						<el-option v-for="item in indexList" :key="item.code" :label="item.name" :value="item.code"> </el-option>
					</el-select>
				</div>
				<div>
					<el-select v-model="valuebench" @change="changeValuebench" placeholder="请选择市场区间">
						<el-option v-for="item in optionsbench" :key="item.value" :label="item.label" :value="item.value"> </el-option>
					</el-select>
				</div>
			</div>
		</analysis-card-title>
		<el-table
			v-loading="loading"
			:data="data"
			border
			stripe
			class="table"
			ref="multipleTable"
			header-cell-class-name="table-header"
			:cell-style="cellStyle"
		>
			<el-table-column
				v-for="item in column"
				:key="item.value"
				:align="item.align"
				:prop="item.value"
				:label="item.label"
				:show-overflow-tooltip="true"
				sortable
			>
				<template #header>
					<long-table-popover-chart
						v-if="item.popover"
						:data="formatTableData()"
						date_key="date"
						:data_key="item.value"
						:show_name="item.label"
					>
						<span>{{ item.label }}</span>
					</long-table-popover-chart>
					<span v-else>{{ item.label }}</span>
				</template>
				<template slot-scope="{ row }">
					<span
						v-if="item.value == 'ave_return' || item.value == 'excessreturn'"
						:style="row[item.value] * 1 > 0 ? 'color:red' : 'color:green'"
						>{{ item.format ? item.format(row[item.value]) : row[item.value] }}</span
					>
					<span v-else>{{ item.format ? item.format(row[item.value]) : row[item.value] }}</span>
				</template>
			</el-table-column>
			<template slot="empty">
				<el-empty image-size="160"></el-empty>
			</template>
		</el-table>
	</div>
</template>

<script>
// 分时段业绩表现
import { filter_json_to_excel } from '@/utils/exportExcel.js';

// 分时段业绩表现
import { getMarketWindowReturnV2, getFundPeriod, getBenchmarkList } from '@/api/pages/Analysis.js';

export default {
	name: 'timePhasedPerformance',
	data() {
		return {
			selectParting: [],
			data: [],
			manageStart: '',
			manageEnd: '',
			showicon: false,
			optionsbench: [],
			valuebench: '',
			postData: {},
			loading: true,
			column: [],
			allData: [],
			info: {},
			index: '',
			indexList: []
		};
	},
	methods: {
		openvideo() {
			window.open('https://www.bilibili.com/video/BV12e4y1R7tj?share_source=copy_web');
		},
		// 获取数据
		async getData(info) {
			this.info = info;
			await this.getFundPeriod();
			await this.getBenchmarkList();
			this.setColumn();
			this.getMarketWindowReturn();
		},
		// 获取分时段业绩表现
		async getMarketWindowReturn() {
			let data = await getMarketWindowReturnV2({
				flag: this.info.flag,
				code: this.info.code,
				type: this.info.type,
				start_date: this.info.start_date,
				end_date: this.info.end_date,
				periodname: this.postData.periodname
			});
			this.loading = false;
			if (data?.mtycode == 200) {
				this.data = data?.data;
				this.allData = [...this.data];
			} else {
				this.data = [];
			}
		},
		// 获取基准概念列表
		async getFundPeriod() {
			let postData = {
				code: this.info.code,
				type: this.info.type,
				flag: this.info.flag,
				start_date: this.info.start_date,
				end_date: this.info.end_date
			};
			let data = await getFundPeriod(postData);
			let list = [{ label: '成长价值', value: '成长价值' }];
			if (data?.mtycode == 200) {
				list = data?.data
					.map((v) => {
						if (v.label == 'A股特征市场') {
							return { label: 'A股重大事件', value: 'A股特征市场' };
						} else {
							return v;
						}
					})
					.filter((v) => !v.label.includes('猫头鹰'));
			}

			this.optionsbench = list;
			this.valuebench = list?.[0]?.value;
			this.postData.periodname = this.valuebench;
		},
		// 获取基准列表
		async getBenchmarkList() {
			let data = await getBenchmarkList({
				code: this.info.code,
				flag: this.info.flag,
				type: this.info.type
			});
			this.indexList = [];
			this.index = '';
			if (data?.mtycode == 200) {
				this.indexList = data?.data
					.sort((a, b) => {
						return b.isdefault - a.isdefault;
					})
					.map((v) => {
						return { code: v.indexCode, name: v.indexName, flag: v.flag };
					});
				if (this.indexList.findIndex((v) => v.code == '000300.SH') != -1) {
					this.index = '000300.SH';
				} else {
					this.index = this.indexList?.[0]?.code;
				}
			} else {
				this.indexList = this.COMMON.default_index[this.info.type].map((v) => {
					return { code: v.indexCode, name: v.indexName, flag: '6' };
				});
				this.index = this.indexList?.[0]?.code;
			}
			// this.indexList.unshift({ code: '0', name: '收益率0%', flag: '6' });
			this.getMarketWindowReturn();
		},
		// 监听指数切换
		changeIndex(value) {
			this.index = value;
			let index = this.column.findIndex((v) => v.value == 'index_cumReturn');
			let label = this.indexList.find((v) => v.code == this.index)?.name + '区间收益';
			this.$set(this.column, index, { ...this.column, label });
			this.getMarketWindowReturn();
		},
		// 初始化表头
		setColumn() {
			this.column = [
				{
					label: '日期',
					value: 'date',
					format: this.repalce,
					align: 'gotoleft',
					popover: false
				},
				{
					label: '描述',
					value: 'name',
					align: 'gotoleft',
					popover: false
				},
				{
					label: '区间收益',
					value: 'cumreturn',
					align: 'right',
					format: this.fix2p,
					popover: true,
					fill: 'red_or_green'
				},
				{
					label: '区间年化收益',
					value: 'aveReturn',
					align: 'right',
					format: this.fix2p,
					popover: true,
					fill: 'red_or_green'
				},
				{
					label: '业绩基准区间收益',
					value: 'self_index_cumReturn',
					align: 'right',
					format: this.fix2p,
					popover: true,
					fill: 'red_or_green'
				},
				{
					label: this.indexList.find((v) => v.code == this.index)?.name + '区间收益',
					value: 'index_cumReturn',
					align: 'right',
					format: this.fix2p,
					popover: true,
					fill: 'red_or_green'
				},
				{
					label: '同类区间收益排名',
					value: 'cumreturnDescription',
					align: 'right',
					popover: true
				},
				{
					label: '仓位',
					value: 'equityWeight',
					align: 'right',
					format: this.fixp,
					popover: true
				},
				{
					label: '区间夏普率',
					value: 'sharpe',
					align: 'right',
					format: this.fix3,
					popover: true
				}
			];
			if (this.info.flag == 2) {
				this.column.splice(
					this.column.findIndex((v) => v.label == '业绩基准区间收益'),
					1
				);
			}
		},
		formatTableData() {
			let data = [];
			this.data.map((item) => {
				let obj = { ...item };
				for (const key in item) {
					let format = this.column.find((obj) => {
						return obj.value == key;
					})?.format;
					if (format) {
						let val = format(item[key]);
						obj[key] = typeof val == 'string' ? (val.includes('%') ? val?.split('%')?.[0] * 1 : !isNaN(val) ? val * 1 : val) : val;
					}
				}
				data.push(obj);
			});
			return data;
		},
		// 监听排序
		sortChange(val) {
			if (val.order) {
				this.data.sort((a, b) => {
					if (val.prop) {
						if (a[val.prop].indexOf('%') != -1) {
							return val.order == 'descending'
								? b[val.prop].split('%')[0] * 1 - a[val.prop].split('%')[0] * 1
								: val.order == 'ascending'
									? a[val.prop].split('%')[0] * 1 - b[val.prop].split('%')[0] * 1
									: 1;
						} else {
							return val.order == 'descending'
								? b[val.prop] * 1 - a[val.prop] * 1
								: val.order == 'ascending'
									? a[val.prop] * 1 - b[val.prop] * 1
									: 1;
						}
					}
				});
			} else {
				this.data = this.allData;
			}
		},

		// 监听基准选择
		changeValuebench() {
			this.loading = true;
			this.postData.periodname = this.valuebench;
			this.getMarketWindowReturn();
		},
		// 监听时间选择
		changeParting() {
			this.loading = true;
			this.postData.start_date = this.selectParting?.[0];
			this.postData.end_date = this.selectParting?.[1];
			this.getMarketWindowReturn();
		},
		fix3(value) {
			return value * 1 ? Number(value).toFixed(3) : '--';
		},
		repalce(value) {
			return value ? value.replace('::', '~') : '--';
		},
		fix2p(value) {
			return value * 1 ? (value * 100).toFixed(2) + '%' : '--%';
		},
		fix3_2(value) {
			return value * 1 ? Number(value).toFixed(3) : '--';
		},
		fixp(value) {
			return value * 1 ? value.toFixed(2) + '%' : '--%';
		},
		// 动态判断字体颜色
		cellStyle({ column, row }) {
			let boolean = this.column.find((v) => v.value == column.property)?.fill == 'red_or_green';
			if (boolean) {
				let val = row[column.property];
				if (val * 1 && !isNaN(val)) {
					if (val * 1 > 0) {
						return { color: '#CF1322' };
					} else if (val * 1 < 0) {
						return { color: '#389E0D' };
					}
				}
			}
		},
		// 导出excel
		exportExcel() {
			let list = this.column.map((item) => {
				return {
					...item,
					format: ''
				};
			});
			filter_json_to_excel(list, this.data, '分时段业绩表现');
		},
		// 导出
		async createPrintWord(info) {
			if (this.valuebench) {
				this.info = info;
				await this.getMarketWindowReturn();
			} else {
				await this.getData(info);
			}
			return await new Promise((resolve, reject) => {
				this.$nextTick(async () => {
					let list = this.column.map((item) => {
						return {
							...item,
							format: ''
						};
					});
					let data = this.data.map((item) => {
						let obj = {};
						for (const key in item) {
							let index = this.column.findIndex((v) => v.value == key);
							if (this.column[index]?.format) {
								obj[key] = this.column[index]?.format(item[key]);
							} else {
								obj[key] = item[key];
							}
						}
						return obj;
					});
					if (this.data.length) {
						resolve([
							...this.$exportWord.exportTitle('分时段业绩表现'),
							...this.$exportWord.exportDescripe('市场区间为：' + this.valuebench),
							...this.$exportWord.exportTable(list, data, '', data?.length > 5 ? true : false)
						]);
					} else {
						resolve([]);
					}
				});
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.parting-header {
	display: flex;
	align-items: gotoleft;
	justify-content: space-between;

	.filter-box {
		display: flex;
		align-items: gotoleft;
		.filter-box-item {
			margin-left: 20px;
			display: inline-block;
		}
	}
}
</style>
