<template>
    <div class="plate-wrapper fund-performance-board-wrapper">
        <combinationComponentHeader title="行业操作复盘" showMoreBtn @download="exportExcel">
            <template slot="right">
                <div style="margin-right: 16px;">
                        <el-radio-group class="radio-group-wrapper" v-model="form.penetrateFlag" size="small" style="margin-left: 0 !important;" @input="radioChange">
                            <el-radio-button :label="true">穿透fof持仓</el-radio-button>
                            <el-radio-button :label="false">不穿透fof持仓</el-radio-button>
                            
                        </el-radio-group>
                    </div>
                <el-select v-model="form.industryStandard" placeholder="行业口径" style="margin-right: 16px;" @change="indChange">
                    <el-option
                        v-for="item in options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                        </el-option>
                </el-select>
                <el-select v-model="form.industryCode" placeholder="选择行业" style="margin-right: 16px;" @change="radioChange">
                    <el-option
                        v-for="item in industryOptions"
                        :key="item.measure"
                        :label="item.name"
                        :value="item.measure">
                        </el-option>
                </el-select>
                <!-- <el-select v-model="comparisonValue" placeholder="分析对象" style="margin-right: 12px;">
                    <el-option
                        v-for="item in options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                        </el-option>
                </el-select> -->
                <div style="margin-right: 16px;">
                    <FormTimePicker v-model="preset_time" @input="handleFormChange"></FormTimePicker>
                </div>
            </template>
        </combinationComponentHeader>
        <div style="display: flex;justify-content: space-between;gap:12px;align-items: center;">
            <lineChartForOptionReview ref="fund-performance-board-chart-container3" style="flex: 1;" @tableData="getTableData"></lineChartForOptionReview>
        </div>
    </div>
</template>
<script>
import combinationComponentHeader from './combinationComponentHeader.vue';
import lineChartForOptionReview from '../chart/lineChartForOptionReview.vue';
import { majorAssetPerformance } from '@/api/pages/tkAnalysis/portfolio.js';
import FormTimePicker from './formTimePicker.vue';
import { filter_to_excel } from "@/utils/exportExcel.js";
const dayjs = require('dayjs');
export default {
    name:'industryOperationReview',
    components:{
        combinationComponentHeader,
        lineChartForOptionReview,
        FormTimePicker
    },
    data(){
        return {
            form:{
                penetrateFlag:true,
                industryStandard:'sw',
                startDate: dayjs().subtract(1, 'year').format('YYYY-MM-DD'),
                endDate: dayjs().format('YYYY-MM-DD'),
                industryCode:''

            },
            options:[{
                label:'申万一级行业',
                value: 'sw'
            },{
                label:'泰康一级行业',
                value: 'tk'
            }],
            industryOptions:[],
            param:null,
            preset_time: {
                radioValue: '1',
                startDate: dayjs().subtract(1, 'year').format('YYYY-MM-DD'),
                endDate: dayjs().format('YYYY-MM-DD')
            },
            tableHeader:[{
                prop: 'date',
                label: '时间'
            },{
                prop: '组合累计净值',
                label: '组合累计净值'
            },{
                prop: '沪深300',
                label: '沪深300'
            },{
                prop: '权重',
                label: '权重'
            }],
            tableData:[],
        }
    },
    methods:{
        indChange(){
            this.getData(this.param)
        },
        radioChange(){
            this.getChartData(this.param);
        },
        handleFormChange(val) {
            this.preset_time = val;
            this.form.startDate = val.startDate;
            this.form.endDate = val.endDate;
            this.getChartData(this.param);
		},
        async getData(param){
            let res = await majorAssetPerformance({
                target:this.form.industryStandard
            })
            if(res.mtycode == 200){
                this.industryOptions = res.data;
                this.form.industryCode = res.data[0].measure;
                this.getChartData(param);
            }
        },
        getChartData(param){
            this.param = param;
          
            let chartDom3 = this.$refs['fund-performance-board-chart-container3'];
            chartDom3?.getData({
                ...this.param,
                ...this.form
            });
        },
           // 获取表格数据的方法
       getTableData(val) {
        this.tableHeader[2] = {
            prop: val.name,
                label: val.name
        }
          // 遍历图例列表，为每个图例创建一个表格行
          val.dateList.forEach((item, index) => {
           this.tableData.push({
            date: item, // 将图例名称作为表格行的 name 属性
            '组合累计净值':val.data1[index],
            [val.name]:val.data2[index],
            '权重':val.data3[index]
           });
          
         });
       },
       exportExcel(){
          // 将表头数据进行遍历，生成新的数组list，每个元素包含原表头数据和format字段
          let list = this.tableHeader.map((item) => {
            return {
              ...item,
              format: ''
            };
          });
          // 调用filter_to_excel函数，传入list、表格数据this.tableData和文件名'基金标签'
          filter_to_excel(list, this.tableData, '行业操作复盘');
        },
    },
    async created(){
        
    },
}
</script>
<style lang="scss" scoped>
.fund-performance-board-wrapper {
    .select-form-wrapper {
        margin-bottom: 16px;
    }
    .content-table-wrapper {
        margin-bottom: 32px;
    }
}

</style>