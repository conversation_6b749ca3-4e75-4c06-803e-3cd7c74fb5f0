<template>
	<div v-show="show" v-loading="loadingcredit" class="chart_one">
		<div style="display: flex; justify-content: space-between; align-items: center">
			<div class="title" style="margin-bottom: 24px">
				<span
					>信用分析<el-tooltip
						class="item"
						effect="dark"
						content="根据基金半年度披露的信用风险敞口，观察债券组合的信用分布情况。"
						placement="right-start"
					>
						<svg width="14" height="14" viewBox="0 0 14 14" fill="none">
							<path
								fill-rule="evenodd"
								clip-rule="evenodd"
								d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
								fill="black"
								fill-opacity="0.45"
							/>
						</svg> </el-tooltip
				></span>
			</div>
		</div>

		<div class="charts_fill_class" v-loading="loadingcredit">
			<el-empty v-show="emptyShow" image-size="160"></el-empty>
			<v-chart
				v-show="!emptyShow"
				style="margin-top: 16px"
				:options="bondCreditOption"
				element-loading-text="暂无数据"
				element-loading-spinner="el-icon-document-delete"
				element-loading-background="rgba(239, 239, 239, 0.5)"
				class="charts_one_class"
				autoresize
			/>
		</div>
	</div>
</template>

<script>
import { lineChartOption } from '@/utils/chartStyle.js';
import VChart from 'vue-echarts';
export default {
	components: { VChart },
	data() {
		return {
			loadingcredit: true,
			fundTypeList: [],
			fundType: '',
			bondCreditOption: {},
			show: true,
			emptyShow: true
		};
	},
	methods: {
		// 获取数据
		getData(data) {
			this.show = true;
			this.loadingcredit = false;
			this.emptyShow = false;
			this.bondCreditOption = lineChartOption({
				xAxis: [{ type: 'category', data: data.yearqtr }],
				yAxis: [
					{
						type: 'value'
					}
				],
				series: [
					{
						name: '下沉占信用债比例',
						type: 'line',
						symbol: 'none',
						data: data.downratioinC
					},
					{
						name: '下沉占净值比',
						type: 'line',
						symbol: 'none',
						data: data.downratioinN
					}
				]
			});
		},
		// 隐藏loading
		hideLoading() {
			this.emptyShow = true;
			this.show = false;
			this.loadingcredit = false;
		}
	}
};
</script>

<style></style>
