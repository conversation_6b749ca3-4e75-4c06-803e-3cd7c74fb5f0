<template>
	<div id="barraReturnLine">
		<analysis-card-title title="因子暴露与收益" image_id="barraReturnLine">
			<el-select v-model="value" placeholder="" @change="changeValue">
				<el-option v-for="item in list" :key="item.value" :label="item.label" :value="item.value"> </el-option>
			</el-select>
		</analysis-card-title>
		<div class="charts_center_class" v-loading="loading">
			<v-chart
				style="width: 100%; height: 300px"
				ref="barraReturnLine"
				autoresize
				v-loading="loading"
				element-loading-text="暂无数据"
				element-loading-spinner="el-icon-document-delete"
				element-loading-background="rgba(239, 239, 239, 0.5)"
				:options="option"
			/>
		</div>
	</div>
</template>

<script>
import { getBarraInfo } from '@/api/pages/Analysis.js';

import { lineChartOption } from '@/utils/chartStyle.js';
export default {
	data() {
		return {
			value: 'momentum',
			list: [
				{
					label: '贝塔',
					value: 'beta'
				},
				{
					label: '动量',
					value: 'momentum'
				},
				{
					label: '杠杆',
					value: 'leverage'
				},
				{
					label: '估值',
					value: 'bp'
				},
				{
					label: '盈利',
					value: 'earningyield'
				},
				{
					label: '成长',
					value: 'growth'
				},
				{
					label: '价值',
					value: 'value'
				},
				{
					label: '流动',
					value: 'liquidity'
				},
				{
					label: '非线性市值因子',
					value: 'nolinearsize'
				},
				{
					label: '规模',
					value: 'size'
				}
			],
			loading: true,
			option: {},
			data: [],
			info: {}
		};
	},
	methods: {
		getData(info) {
			this.loading = false;
			this.info = info;
			this.getBarraInfo();
		},
		async getBarraInfo() {
			let data = await getBarraInfo({ code: this.info.code, type: this.info.type, flag: this.info.flag });
			if (data?.mtycode == 200) {
				this.data = data?.data;
				this.setOption();
			}
		},
		// 切换因子
		changeValue(value) {
			this.value = value;
			this.setOption();
		},
		// 画图
		setOption() {
			let data = this.data.sort((a, b) => {
				return this.moment(this.moment(a.yearqtr, 'YYYY QQ').format()).isBefore(this.moment(b.yearqtr, 'YYYY QQ').format()) ? -1 : 1;
			});
			let date = Array.from(new Set(data.map((v) => v.yearqtr)));
			this.option = lineChartOption({
				toolbox: 'none',
				grid: {
					top: '8px',
					left: '48px',
					right: '48px'
				},
				color: ['#4096ff', 'rgba(115, 136, 169, 0.5)'],
				xAxis: [
					{
						data: date
					}
				],
				tooltip: {
					backgroundColor: '#ffffff',
					formatter: function (obj) {
						var value = `<div style="font-size:14px;">` + obj?.[0].axisValue + `</div>`;
						for (let i = 0; i < obj.length; i++) {
							value +=
								`<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
								`<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
								obj?.[i].color +
								`;"></div>` +
								`<div style="font-family: PingFang SC;">` +
								obj?.[i].seriesName +
								'</div></div>' +
								`<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
								Number(obj?.[i].value?.[1]).toFixed(2) +
								'%</div>' +
								`</div>`;
						}
						return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
					}
				},

				yAxis: [
					{
						name: '因子暴露',
						type: 'value',
						nameLocation: 'middle', // 设置名称居中
						nameGap: 48, // 控制名称距离轴线的距离
						nameTextStyle: {
							align: 'center'
						}
					},
					{
						name: '因子收益',
						type: 'value',
						nameLocation: 'middle', // 设置名称居中
						nameGap: 48, // 控制名称距离轴线的距离
						nameRotate: 270,
						nameTextStyle: {
							align: 'center'
						}
					}
				],
				series: [
					{
						name: '因子暴露',
						data: data.map((v) => [v.yearqtr, v[this.value]]),
						type: 'line',
						yAxisIndex: 0,
						connectNulls: true,
						symbol: 'none',
						lineStyle: {
							color: '#4096ff'
						}
					}
					// {
					// 	name: '因子收益',
					// 	data: [13, 13, 82, 93, 90, 93, 12, 13, 82, 93, 90, 93, 12].map((v, i) => [date[i], v]),
					// 	type: 'line',
					// 	yAxisIndex: 1,
					// 	connectNulls: true,
					// 	symbol: 'none',
					// 	lineStyle: {
					// 		width: 0,
					// 		color: 'rgba(115, 136, 169, 0.5)'
					// 	},
					// 	areaStyle: {
					// 		color: 'rgba(115, 136, 169, 0.5)'
					// 	}
					// }
				]
			});
		},
		// word导出配置
		async createPrintWord(info) {
			await this.getData(info);
			return await new Promise((resolve, reject) => {
				this.$nextTick(async () => {
					let key = 'barraReturnLine';
					let height = document.getElementById(key).clientHeight;
					let width = document.getElementById(key).clientWidth;
					let canvas = await this.html2canvas(document.getElementById(key), {
						scale: 3
					});
					let print_word = [
						...this.$exportWord.exportTitle('因子暴露与收益'),
						...this.$exportWord.exportChart(canvas.toDataURL('image/jpg'), {
							width,
							height
						})
					];
					resolve(print_word);
				});
			});
		}
	}
};
</script>

<style></style>
