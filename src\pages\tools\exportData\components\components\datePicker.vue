<template>
	<div style="display: flex; justify-content: space-between; align-items: center">
		<div style="margin-right: 16px">基金计算日期:</div>
		<div style="margin-right: 16px">
			<el-date-picker v-model="value1" value-format="yyyy-MM-dd" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions" @change="deliveryDate">
			</el-date-picker>
		</div>
	</div>
</template>

<script>
export default {
	data() {
		return {
			value1: '',
			pickerOptions: {
				// 添加禁用日期，如当前为2023-02-28无法选择2023-03-01以及之后时间
				disabledDate(time) {
          return time.getTime() > Date.now();
        },
			}
		};
	},
	methods: {
		// 传递选择完成的时间
		deliveryDate(data) {
			this.$emit('getDate', data);
		}
	}
};
</script>

<style></style>
