<template>
	<div>
		<div class="chart_one" style="height: calc(100vh - 351px); padding: 0; margin-top: 0">
			<div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px">
				<el-button type="primary" @click="addSubscription">新增订阅</el-button>
				<el-button type="primary" @click="getData">刷新</el-button>

				<!-- <el-input v-model="value" placeholder="请输入订阅名称" style="width: 240px"></el-input> -->
			</div>
			<el-table v-loading="loading" :data="data" style="width: 100%; min-height: calc(100vh - 293px)" @expand-change="expandChange">
				<el-table-column type="expand">
					<template slot-scope="props">
						<div class="expand_main">
							<div class="expand_list" v-for="(item, index) in props.row.list" :key="item.name + index">
								<div class="name">
									{{ item.pool_name ? item.pool_name + '-' + item.name : item.name }}
								</div>
								<div style="display: flex; align-items: center" class="mr-16">
									<svg width="7" height="6" viewBox="0 0 7 6" fill="none" xmlns="http://www.w3.org/2000/svg">
										<circle
											cx="3.66675"
											cy="3"
											r="3"
											:fill="item.is_file_over == 0 ? '#e85d2d' : item.is_file_over == 1 ? '#389E0D' : '#CF1322'"
										/>
									</svg>
									<span :style="item.is_file_over == 0 ? 'color: #e85d2d' : item.is_file_over == 1 ? 'color:#389E0D' : 'color:#CF1322'"
										>&nbsp;{{ item.is_file_over == 0 ? '生成中' : item.is_file_over == 1 ? '已生成' : '失败' }}</span
									>
								</div>
								<div>
									<el-popover placement="right" width="400" trigger="click" v-model="item.show">
										<div v-show="item.is_file_over == 1" class="report_popover">
											<div class="title_header">
												<div>报告二维码</div>
												<div class="close_icon" @click="item.show = false">x</div>
											</div>
											<div class="report_main">
												<div
													v-loading="item.is_file_over == 0"
													v-if="item.show"
													:id="item.show ? 'qrcode' : ''"
													style="width: 200px; height: 200px"
													ref="qrcode"
												></div>
												<div>扫描二维码下载报告</div>
											</div>
										</div>
										<el-link @click="getReportUrl(item.id, item.name)" :disabled="item.is_file_over !== 1 ? true : false" slot="reference"
											>报告二维码</el-link
										>
									</el-popover>
								</div>
								<div style="margin-left: 16px">
									<el-link @click="goPrint(item, props.row)">手动下载</el-link>
								</div>
							</div>
						</div>
					</template>
				</el-table-column>
				<el-table-column
					v-for="(item, index) in column"
					:key="index"
					:prop="item.value"
					:label="item.label"
					:min-width="item.width ? item.width : ''"
					show-overflow-tooltip
					align="gotoleft"
				>
					<template slot-scope="{ row }">
						<div
							v-show="item.label !== '操作' && item.label !== '报告二维码' && item.label !== '发送频率' && item.label !== '定时发送'"
							style="overflow: hidden; text-overflow: ellipsis"
						>
							{{ row[item.value] }}
						</div>
						<div v-show="item.label == '发送频率'">
							<div style="display: flex; align-items: center">
								<svg width="7" height="6" viewBox="0 0 7 6" fill="none" xmlns="http://www.w3.org/2000/svg">
									<circle cx="3.66675" cy="3" r="3" fill="#E85D2D" />
								</svg>
								<span style="color: #e85d2d">&nbsp;{{ row[item.value] }}</span>
							</div>
						</div>
						<div v-if="item.value == 'is_timing_send'">
							<el-switch v-model="row[item.value]" @change="changeTimingSend(row)"></el-switch>
						</div>
						<div v-show="item.label == '操作'">
							<el-link
								style="margin-right: 16px"
								:type="row.is_file_over == 1 ? '' : 'info'"
								:disabled="row.is_file_over == 1 ? false : true"
								@click="getSendReport(row)"
								>手动发送</el-link
							>
							<el-link style="margin-right: 16px" @click="goToUpdate(row)">订阅配置</el-link>
							<el-popconfirm title="确定删除内容吗？" placement="top" @confirm="deleteItem(row)">
								<el-link slot="reference">删除</el-link>
							</el-popconfirm>
						</div>
					</template>
				</el-table-column>
				<template slot="empty">
					<el-empty image-size="160"></el-empty>
				</template>
			</el-table>
			<div style="display: flex; justify-content: space-between; align-items: center; margin-top: 16px">
				<div style="font-family: 'PingFang'; font-style: normal; font-weight: 400; font-size: 14px; color: rgba(0, 0, 0, 0.65)">
					共{{ total }}条数据
				</div>
				<!-- <el-pagination
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
					:current-page="currentPage"
					:page-sizes="[10, 20, 30, 40]"
					:page-size="pageSize"
					layout="sizes, prev, pager, next, jumper"
				>
				</el-pagination> -->
			</div>
		</div>
	</div>
</template>

<script>
import { getSubscriptionList, deleteSubscriptionList, getReportUrl, putTaskStatus, getSendReport } from '@/api/pages/NodeServer.js';
// 获取池子详情
import { getPoolDetail } from '@/api/pages/SystemMixed.js';

import QRCode from 'qrcodejs2';
import { alphaGo } from '@/assets/js/alpha_type.js';

export default {
	data() {
		return {
			data: [],
			loading: true,
			column: [
				{
					label: '名称',
					value: 'name',
					width: '200px'
				},
				{
					label: '简介',
					value: 'description',
					width: '200px'
				},
				{
					label: '修改日期',
					value: 'time',
					sortable: true,
					width: '200px'
				},
				{
					label: '相关名称',
					value: 'relevant_name',
					filter: true,
					width: '200px'
				},
				{
					label: '发送频率',
					value: 'frequency',
					width: '200px'
				},
				{
					label: '定时发送',
					value: 'is_timing_send',
					width: '100px'
				},
				{
					label: '发送邮箱',
					value: 'email',
					width: '200px'
				},
				{
					label: '操作',
					value: 'setting',
					width: '200px'
				}
			],
			total: 0,
			currentPage: 1,
			pageSize: 10,
			user_id: localStorage.getItem('id'),
			popoverShow: false,
			qrcode: null
		};
	},
	methods: {
		// 获取订阅列表数据
		async getData() {
			this.loading = true;
			let data = await getSubscriptionList({
				user_id: this.user_id,
				page: this.currentPage,
				size: this.pageSize,
				is_quick: false
			});
			this.loading = false;
			if (data?.mtycode == 200) {
				this.total = data?.data?.total;
				this.data = data?.data?.data
					?.map((item) => {
						let frequency = '';
						if (item.frequency?.split(',')?.length == 3) {
							let arr = item.frequency?.split(',');
							let newA = arr?.map((item, index) => {
								if (index == 0) {
									return item == 'day' ? '每天' : item == 'week' ? '每周' : item == 'month' ? '每月' : '';
								} else if (index == 1) {
									if (arr[0] == 'day') {
										return '';
									} else if (arr[0] == 'week') {
										return item == '1'
											? '一'
											: item == '2'
											? '二'
											: item == '3'
											? '三'
											: item == '4'
											? '四'
											: item == '5'
											? '五'
											: item == '6'
											? '六'
											: '日';
									} else {
										return item + '号';
									}
								} else {
									return item + '点';
								}
							});
							frequency = newA.join('');
						}
						return {
							...item,
							email: item.email
								.map((val) => {
									return val.email_name + `(${val.email})`;
								})
								.join(','),
							relevant_name: item.list
								?.map((obj) => {
									return obj.name;
								})
								.join(','),
							show: false,
							frequency,
							is_file_over: item.list?.every((obj) => {
								return obj.is_file_over == 1;
							})
						};
					})
					.sort((a, b) => {
						return b.time - a.time;
					});
				console.log(this.data);
			} else {
				this.data = [];
			}
		},
		// 前往修改订阅
		goToUpdate(item) {
			this.$router.push('/addSubscription?id=' + item.id + '&longSubscription=true');
		},
		// 获取报告地址
		async getReportUrl(id, name) {
			let data = await getReportUrl({
				id,
				name,
				user_id: this.user_id
				// type: "word",
			});
			if (data?.mtycode == 200) {
				let url = data?.data?.url;
				if (url) {
					if (this.qrcode) {
						this.qrcode.clear();
					}
					this.$nextTick(() => {
						this.qrcode = new QRCode('qrcode', {
							width: 200, // 设置宽度，单位像素
							height: 200, // 设置高度，单位像素
							text: url // 设置二维码内容或跳转地址
						});
					});
				}
			} else {
				this.$message.warning(data?.mtymessage || '报告正在生成,请稍后重试...');
			}
		},
		// 监听分页页码
		handleCurrentChange(val) {
			this.currentPage = val;
			this.getData();
		},
		// 监听分页页数
		handleSizeChange(val) {
			this.pageSize = val;
			this.getData();
		},
		// 新增订阅
		addSubscription() {
			this.$router.push('/addSubscription?longSubscription=true');
		},
		// 删除订阅
		async deleteItem(item) {
			let data = await deleteSubscriptionList({ id: item.id });
			if (data?.mtycode == 200) {
				this.$message.success(data?.mtymessage || '删除成功');
				this.getData();
			} else {
				this.$message.warning(data?.mtymessage || '删除失败');
			}
		},
		// 监听定时发送任务是否开启
		async changeTimingSend(item) {
			let data = await putTaskStatus({
				id: item?.id,
				status: item?.is_timing_send
			});
			if (data?.mtycode == 200) {
				this.$message.success(data?.mtymessage || '定时发送任务状态修改成功');
			} else {
				this.$message.warning(data?.mtymessage || '定时发送任务状态修改失败');
			}
		},
		// 手动发送报告
		async getSendReport(item) {
			let data = await getSendReport({ id: item.id });
			if (data?.mtycode == 200) {
				this.$message.success(data?.mtymessage || '发送成功');
			} else {
				this.$message.warning(data?.mtymessage || '发送失败');
			}
		},
		// 手动下载
		goPrint(info, row) {
			alphaGo(info.code, info.name, this.$route.path);
			//   this.$router.push(
			//     `/reportCenter?subcriptionId=${row.id}&code=${info.code}`
			//   );
		},
		// 监听展开行
		expandChange(row) {
			let list = row.list;
			if (
				list.some((item) => {
					return item.flag == 'pool';
				})
			) {
				let fund_or_manager = [];
				list.map(async (item) => {
					if (item.flag == 'pool') {
						let data = await this.lookDetail(row, item);
						fund_or_manager.push(...data);
					} else {
						fund_or_manager.push(item);
					}
				});
				this.$nextTick(() => {
					let index = this.data.findIndex((item) => {
						return item.id == row.id;
					});
					this.data[index].list = fund_or_manager;
				});
			}
		},
		// 查看池子列表
		async lookDetail(row, info) {
			let data = await getPoolDetail({ id: info.code, have: '' });
			if (data?.mtycode == 200) {
				let list = data?.data.table.map((item) => {
					return {
						pool_name: info.name,
						name: item.name,
						type: item.type,
						code: item.code,
						flag: 'fund',
						is_file_over: 1
					};
				});
				return list;
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.expand_main {
	overflow: auto;
	max-height: calc(100vh - 591px);
}
.expand_list {
	display: flex;
	align-items: center;
	margin: 12px 24px 12px 62px;
	font-family: 'PingFang';
	font-style: normal;
	font-weight: 400;
	font-size: 14px;
	color: rgba(0, 0, 0, 0.65);
	.name {
		padding: 9px 0;
		width: 240px;
		margin-right: 16px;
	}
}
.report_popover {
	width: 100%;
	.title_header {
		padding: 4px 12px 16px 12px;
		border-bottom: 1px solid #e9e9e9;
		border-radius: 4px 4px 0px 0px;
		font-family: 'PingFang';
		font-style: normal;
		font-weight: 500;
		font-size: 16px;
		color: rgba(0, 0, 0, 0.85);
		display: flex;
		justify-content: space-between;
		align-items: center;
		.close_icon {
			cursor: pointer;
			color: rgba(0, 0, 0, 0.45);
		}
	}
	.report_main {
		margin: 16px auto;
		display: flex;
		flex-direction: column;
		// justify-content: center;
		align-items: center;
		font-size: 14px;
		#qrcode {
			margin-bottom: 16px;
		}
	}
}
</style>
