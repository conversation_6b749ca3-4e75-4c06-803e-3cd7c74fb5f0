<template>
	<div class="analysis_main dictionary">
		<div class="chart_one">
			<div style="display: flex; position: relative">
				<div
					style="
						min-width: 280px;
						height: calc(100vh - 287px);
						overflow-y: auto;
						overflow-x: hidden;
						/* height: 660px; */
						/* overflow-y: auto; */
						/* min-width: 287px; */
						/* min-width: 468px; */
						/* overflow-x: hidden; */
						/* overflow-x: auto; */
						/* top: 150px; */
						/* overflow: auto; */

						min-width: 496px;

						border-right: 1px solid #e9e9e9;
						background: #ffffff;
						border-radius: 4px 0px 0px 4px;
						position: fixed;
					"
				>
					<dictionary-name @handleChoose="handleChoose"></dictionary-name>
				</div>
				<div style="padding: 16px 24px; width: 100%; max-width: 2081px; overflow: hidden; margin-left: 302px">
					<div style="display: flex; justify-content: space-between; align-items: center">
						<div class="title">{{ title }}</div>
						<div style="display: flex; align-items: center">
							<div v-if="title !== ''">
								<date-picker ref="datePicker" @getDate="assignDate"></date-picker>
							</div>
							<div class="mr-16" v-show="isShowItem">
								<el-select v-model="item">
									<el-option v-for="item in itemList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
								</el-select>
							</div>
							<el-button type="primary" @click="exportExcel" v-if="title !== ''" v-loading.fullscreen.lock="downLoadExcel"
								>导出Excel</el-button
							>
						</div>
					</div>
					<div id="dictionaryDescription">
						<dictionary-description ref="dictionaryDescription"></dictionary-description>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import dictionaryName from './components/dictionaryName.vue';
import dictionaryDescription from './components/dictionaryDescription.vue';
import dropDown from './components/dropDown.vue';
import datePicker from './components/datePicker.vue';

import { mtyExportToExcel, getMtyPeriodnameList } from '@/api/pages/ApiTaikang.js';

export default {
	components: {
		dictionaryName,
		dictionaryDescription,
		datePicker,
		dropDown
	},
	data() {
		return {
			title: '',
			description: '',
			date: ['', ''],
			downLoadExcel: false,
			activeTable: {},
			loading: false,
			itemList: [],
			item: ''
		};
	},
	computed: {
		isShowItem() {
			return this.activeTable.condition?.includes('item');
		}
	},
	methods: {
		getData() {},
		// 监听数据列表切换
		handleChoose(data) {
			this.item = '';
			this.itemList = [];
			// 请求数据
			this.activeTable = data;
			if (this.isShowItem) {
				this.getItemList();
			}
			this.title = data?.name;
			this.description = data?.description;
			this.$refs['dictionaryDescription'].getData(data?.data);
		},
		// 获取可选择基准列表
		async getItemList() {
			let data = await getMtyPeriodnameList({ templateType: this.activeTable?.url });
			if (data.code == 200) {
				this.itemList = data.data.map((item) => {
					return { label: item.name, value: item.code };
				});
				this.item = this.itemList[0].value;
			}
		},
		// 将datePicker传递的date数据赋值给date变量
		assignDate(data) {
			this.date = data;
			console.log(this.date);
		},
		// 请求接口返回Excel地址下载
		async mtyExportToExcel() {
			this.loading = this.$loading({
				lock: true,
				text: '正在生成数据,请稍等...',
				spinner: 'el-icon-loading',
				background: 'rgba(0, 0, 0, 0.7)'
			});
			let postData = { templateType: this.activeTable?.url, startDate: this.date[0], endDate: this.date[1], item: this.item };
			let data = await mtyExportToExcel(postData);
			this.loading.close();
			if (data.code == 200) {
				let aLink = document.createElement('a');
				aLink.style.display = 'none';
				aLink.href = data.data;
				// 触发点击-然后移除
				document.body.appendChild(aLink);
				aLink.click();
				document.body.removeChild(aLink);
			} else {
				this.$message.warning(data.message);
			}
		},
		// 导出excel
		async exportExcel() {
			this.mtyExportToExcel();
		}
	}
};
</script>

<style lang="scss" scoped>
// .dictionary {
// 	.el-input__inner {
// 	}
// }

.chart_one {
	min-height: 1052px;
}
::v-deep .el-checkbox:last-of-type {
	z-index: 0;
}
</style>
