<template>
<div class="asset-selection-wrapper">
    <div class="asset-selection-content">
        <div>
            <div class="transfer-box-wrapper">
                <div class="transfer-left">
                      <el-tabs v-model="activeName1" tab-position="left" style="height: 405px;" class="left-nav-wrapper">
                        <el-tab-pane v-for="leftItem in options" :label="leftItem.label" :name="leftItem.value" :key="leftItem.value">
                          <el-checkbox-group v-model="checkList">
                          <div v-for="checkItem in leftItem.children" :key="checkItem.value" style="height: 30px;line-height: 30px;">
                            <el-checkbox v-model="checkItem.checked" :label="checkItem">{{ checkItem.name }}  </el-checkbox>
                          </div>
                        </el-checkbox-group>
                        </el-tab-pane>
                      </el-tabs>
                </div>
        </div>
        </div>
   
    </div>
    <div class="btn-footer-wrapper">
      <el-button type="plain" style="margin-top: 12px;margin-right: 5px;" @click="next(false)">取消</el-button>
        <el-button type="primary" style="margin-top: 12px;" @click="next(true)">确定</el-button>
    </div>
</div>
</template>
<script>
// import TheAssetAnalysis from './TheAssetAnalysis.vue'
import { majorAssetPerformance} from '@/api/pages/tkAnalysis/portfolio.js';

  export default {
    components:{
        // TheAssetAnalysis
    },
    data() {
      return {
        searchInput:'',
        activeName1:'默认分类',
        checkedCities:[],
        checkList:[],
        options: [
        
          {
            value: '默认分类',
            label: '默认分类',
            children:[
            
            ]
          }
        ],
        selectedData:[],
        multipleSelection: []
      };
    },
    created(){
      majorAssetPerformance({
        target:'factor'
      }).then(res=>{
        if(res.mtycode == 200){
            this.options[0].children = res.data;
        }
      })
    },
    methods: {
      initData(){
        //需要将数据返回处理成想要的结构
      },
      next(val){
        this.$emit('confirm',val ? this.checkList :null)
      },
    }
  };
</script>
<style lang="scss" scoped>
.transfer-box-wrapper {
    display: flex;
    .first-type-wrapper {
      ::v-deep .el-tabs__nav-scroll {
        .el-tabs__nav-wrap {
          &::after {
            content: unset;
          } 
        }
      } 
    }
    .transfer-left {
        width: 480px;
        border: 1px solid #E9E9E9;
        border-radius: 4px;
        .left-nav-wrapper {
          ::v-deep .el-tabs__header {
           .el-tabs__item {
            text-align: left;
           }
          }
        }
        .transfer-left-title {
            display: flex;
            padding: 8px 16px;
            align-items: center;
            border-bottom: 1px solid #E9E9E9;
            .label {
              color: rgba(0, 0, 0, 0.85);
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 22px; /* 157.143% */
              word-break: keep-all;
            }
        }
    }
    .transfer-center {
        display: flex;
        flex-direction: column;
        padding: 0 20px;
        justify-content: center;
        align-items: center;
    }
    .transfer-right {
      min-width: 200px;
        border: 1px solid #E9E9E9;
        border-radius: 4px;
        .transfer-right-title {
            padding: 10px 16px;
            color: #000;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
            text-align: center;
        }
        .transfer-right-item{
          height:30px;
          line-height:30px;
          display: flex;
          justify-content: space-between;
          padding: 0 15px;
          .el-icon-delete{
            line-height: 30px;
          }
        }
    }
}
.asset-selection-wrapper {
    background-color: #ffffff;
    border-radius:  4px;
    border-top: 1px solid #E9E9E9;
    padding-top: 20px;
    .asset-selection-content {
        padding: 0 24px;
        padding-bottom: 20px;
    }
    .asset-selection-header {
        color: rgba(0, 0, 0, 0.85);
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 24px; /* 150% */
        padding: 16px 0;
    }
}
.asset-selection-footer {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    color: rgba(0, 0, 0, 0.45);
    font-size: 12px;
    font-weight: 400;
    line-height: 20px; /* 166.667% */
}
.btn-footer-wrapper {
    padding: 16px 24px;
    border-top: 1px solid #E9E9E9;
    display: flex;
    justify-content:flex-end ;
}
</style>