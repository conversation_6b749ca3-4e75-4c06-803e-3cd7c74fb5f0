<!--  -->
<template>
  <div v-loading="loading"
       style="page-break-inside: avoid"
       class="industryscore">
    <div style="display: flex; align-items: center; width: 100%; position: relative; justify-content: space-between">
      <div style="display: flex; align-items: center">
        <div class="TitltCompare">行业能力圈</div>
      </div>
      <div>
        <el-button @click="show = !show"
                   type="">{{ show ? '合并' : '拆分' }}</el-button>
      </div>
    </div>
    <div style="min-width: 400px"
         v-if="!show">
      <v-chart ref="industryscoreAll"
               v-loading="empty2"
               autoresize
               element-loading-text="暂无数据"
               element-loading-spinner="el-icon-document-delete"
               element-loading-background="rgba(239, 239, 239, 0.5)"
               style="width: 100%; height: 500px; margin-top: 16px; page-break-inside: avoid"
               :options="options2" />
    </div>
    <div v-if="show"
         style="display: flex; flex-wrap: wrap; width: 100%">
      <div :style="inwidth > 1743 ? ' flex: 1; min-width: 300px; margin-right: 24px' : 'min-width:450px;flex:1; margin-right: 24px'"
           v-if="JSON.stringify(options2_1) != '{}'">
        <v-chart v-loading="empty2"
                 autoresize
                 element-loading-text="暂无数据"
                 element-loading-spinner="el-icon-document-delete"
                 element-loading-background="rgba(239, 239, 239, 0.5)"
                 style="width: 100%; height: 500px; margin-top: 16px; page-break-inside: avoid"
                 :options="options2_1" />
      </div>
      <div :style="inwidth > 1743 ? ' flex: 1; min-width: 300px; margin-right: 24px' : 'min-width:450px;flex:1; margin-right: 24px'"
           v-if="JSON.stringify(options2_2) != '{}'">
        <v-chart v-loading="empty2"
                 autoresize
                 element-loading-text="暂无数据"
                 element-loading-spinner="el-icon-document-delete"
                 element-loading-background="rgba(239, 239, 239, 0.5)"
                 style="width: 100%; height: 500px; margin-top: 16px; page-break-inside: avoid"
                 :options="options2_2" />
      </div>
      <div :style="inwidth > 1743 ? ' flex: 1; min-width: 300px; margin-right: 24px' : 'min-width:450px;flex:1; margin-right: 24px'"
           v-if="JSON.stringify(options2_3) != '{}'">
        <v-chart v-loading="empty2"
                 autoresize
                 element-loading-text="暂无数据"
                 element-loading-spinner="el-icon-document-delete"
                 element-loading-background="rgba(239, 239, 239, 0.5)"
                 style="width: 100%; height: 500px; margin-top: 16px; page-break-inside: avoid"
                 :options="options2_3" />
      </div>
      <div :style="inwidth > 1743 ? ' flex: 1; min-width: 300px;' : 'min-width:450px;flex:1; '"
           v-if="JSON.stringify(options2_4) != '{}'">
        <v-chart v-loading="empty2"
                 autoresize
                 element-loading-text="暂无数据"
                 element-loading-spinner="el-icon-document-delete"
                 element-loading-background="rgba(239, 239, 239, 0.5)"
                 style="width: 100%; height: 500px; margin-top: 16px; page-break-inside: avoid"
                 :options="options2_4" />
      </div>
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { ManagerIndustryCapability, FundIndustryCapability } from '@/api/pages/tools/compare.js';
import VCharts from 'vue-echarts';
export default {
  //import引入的组件需要注入到对象中才能使用
  components: { 'v-chart': VCharts },
  props: {
    comparetype: {
      type: String,
      default: 'manager' //fund
    },
    id: {
      type: String,
      default: '30189741,30441407'
    },
    type: {
      type: String,
      default: 'equity'
    },
    name: {
      type: String,
      default: '萧楠,胡昕炜'
    }
  },
  data () {
    //这里存放数据
    return {
      show: false,
      inwidth: window.innerWidth,
      loading: false,
      options2: {},
      options2_1: {},
      options2_2: {},
      options2_3: {},
      options2_4: {},
      empty2: false,
      industryClassify: {
        // 板块分类-逆顺序
        finance: ['非银金融', '银行', '房地产'], // 金融地产-3
        cycle: ['基础化工', '公用事业', '交通运输', '石油石化', '建筑材料', '煤炭', '钢铁', '环保', '建筑装饰', '有色金属'], // 周期-10
        manufacture: ['国防军工', '汽车', '机械设备', '综合', '电力设备'], // 制造-5
        TMT: ['传媒', '通信', '计算机', '电子'], // TMT-4
        medicine: ['医药生物'], // 医药-1
        consume: ['农林牧渔', '家用电器', '商贸零售', '社会服务', '轻工制造', '纺织服饰', '食品饮料', '美容护理'] // 消费-8
      },
      classifyTranseName: {
        finance: '金融地产',
        cycle: '周期',
        manufacture: '制造',
        TMT: 'TMT',
        medicine: '医药',
        consume: '消费'
      }
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    getdata () {
      Object.assign(this.$data, this.$options.data());
      this.loading = true;
      this.generateChart();
    },
    async generateChart () {
      // let color = ['#4096FF', '#4096ff', '#afb2b1', '#862e9c', '#fa5252'];
      let color = ['#4096ff', '#4096ff', '#7388A9', '#6F80DD', '#4096FF', '#e040fb', '#ff3d00'];
      let data = null;
      if (this.comparetype == 'manager') {
        data = await ManagerIndustryCapability({
          manager_code: this.id,
          type: this.type,
          manager_name: this.name
        });
      } else {
        data = await FundIndustryCapability({
          fund_code: this.id,
          type: this.type,
          fund_name: this.name
        });
      }
      this.loading = false;

      if (data) {
        // //console.log('data data: ', data.data);

        let nameList = [];
        let dataList = {};
        let keyList = [];
        let backgroundList = {};
        let industry = data.data.industry_name;
        let backIndicator = Array.from({ length: industry.length }, (item) => (item = { name: '', max: 1 }));
        // 获取基金/基金经理名-keyList; 初始化dataList;
        for (let key in data.data) {
          if (key !== 'industry_name') {
            keyList.push(key);
          }
        }
        for (let key of keyList) {
          dataList[key] = [];
        }

        // 处理nameList、dataList、backgroundList
        for (let classify_name in this.industryClassify) {
          let theClassNum = 0;
          let classify = this.industryClassify[classify_name];
          backgroundList[classify_name] = [];
          classify.forEach((item) => {
            if (data.data.industry_name.includes(item)) {
              theClassNum++;
              let index = industry.indexOf(item);
              nameList.push({ name: item, max: 1 });
              backgroundList[classify_name].push(item);
              for (let key of keyList.sort((a, b) => {
                if (this.$route.query.name.split(',').indexOf(a) > this.$route.query.name.split(',').indexOf(b)) return 1;
                else return -1;
              })) {
                this.isValidData(data.data[key][index]) ? dataList[key].push(data.data[key][index]) : dataList[key].push(0);
              }
              // dataList.push(industry.rank[index]);
            }
          });
          let zhName = this.classifyTranseName[classify_name];
          let zhIndex = Math.ceil(nameList.length - 1 - theClassNum / 2);
          if (theClassNum > 0) {
            backIndicator[zhIndex].name = zhName;
          }
        }

        let option = {
          color: color,
          legend: {
            show: true,
            orient: 'vertical',
            right: '10%'
          },
          radar: [
            {
              // 底色
              startAngle: 90 + (360 / nameList.length) * 1.5,
              indicator: backIndicator,
              nameGap: 60,
              name: {
                fontSize: 15,
                color: '#4096ff'
              },
              axisLine: {
                show: false
              },
              radius: ['0%', '60%']
            },
            {
              startAngle: 90 + 360 / nameList.length,
              splitLine: {
                // (这里是指所有圆环)坐标轴在 grid 区域中的分隔线。
                lineStyle: {
                  color: '#CFE5F3',
                  opacity: 0.3,
                  // 分隔线颜色
                  width: 2
                  // 分隔线线宽
                }
              },
              name: {
                fontSize: 12,
                color: '#4096FF'
              },
              splitArea: {
                // 坐标轴在 grid 区域中的分隔区域，默认不显示。
                show: true,
                areaStyle: {
                  // 分隔区域的样式设置。
                  color: ['rgb(246,250,255)', 'rgb(246,250,255)']
                  // 分隔区域颜色。分隔区域会按数组中颜色的顺序依次循环设置颜色。默认是一个深浅的间隔色。
                }
              },
              axisLine: {
                // (圆内的几条直线)坐标轴轴线相关设置
                lineStyle: {
                  color: '#666',
                  // 坐标轴线线的颜色。
                  width: 0.5,
                  // 坐标轴线线宽。
                  type: 'solid'
                  // 坐标轴线线的类型。
                }
              },

              // shape: 'circle',
              indicator: nameList,
              radius: ['0%', '60%']
            }
          ],
          // tooltip: {
          // 	trigger: 'axis',
          // 	// axisPointer: {
          // 	//     type: 'cross'
          // 	// },
          // 	formatter(params) {
          // 		// ////console.log(params)
          // 		return '行业：' + params[0].axisValue + ' ,' + params[0].data.toFixed(3);
          // 	}
          // },
          series: [
            {
              // 底色
              type: 'radar',
              data: [],
              radarIndex: 0
            },
            {
              itemStyle: {
                // 单个拐点标志的样式设置。
                normal: {
                  borderColor: '#4096FF',
                  // 拐点的描边颜色。[ default: '#000' ]
                  borderWidth: 0
                  // 拐点的描边宽度，默认不描边。[ default: 0 ]
                }
              },
              lineStyle: {
                // 单项线条样式。
                normal: {
                  color: '#4096FF',
                  opacity: 0.5 // 图形透明度
                }
              },
              areaStyle: {
                // 单项区域填充样式
                normal: {
                  color: 'rgb(223,236,255)', // 填充的颜色。[ default: "#000" ]
                  opacity: 0.7
                }
              },
              name: '基金经理能力',
              type: 'radar',
              // data: arrdata
              data: [],
              radarIndex: 1
            }
          ]
        };

        // 向option.series[1]填入数据
        for (let key of keyList) {
          let i = keyList.indexOf(key);
          option.series[1].data.push({
            value: dataList[key],
            name: key,
            itemStyle: {
              // 单个拐点标志的样式设置。
              normal: {
                borderColor: color[i],
                // 拐点的描边颜色。[ default: '#000' ]
                borderWidth: 3
                // 拐点的描边宽度，默认不描边。[ default: 0 ]
              }
            },
            lineStyle: {
              // 单项线条样式。
              normal: {
                color: color[i],
                opacity: 0.5 // 图形透明度
              }
            },
            areaStyle: {
              // 单项区域填充样式
              normal: {
                color: color[i], // 填充的颜色。[ default: "#000" ]
                opacity: 0.7
              }
            }
          });
        }

        // 向option.series[0]填入背景数据
        let onlyNameList = nameList.map((item) => item.name);
        let backColorList = ['#CFE5F3', '#FFD59B', '#F0D3C8', '#C3E4DE', '#DED2EA', '#C6D6F8'];

        for (let classify_name in this.industryClassify) {
          let classify = this.industryClassify[classify_name];
          let arr = Array.from({ length: nameList.length }, (item) => (item = ''));
          classify.forEach((item) => {
            if (onlyNameList.includes(item)) {
              let index = onlyNameList.indexOf(item);
              arr[index] = 1;
            }
          });

          // 需要每个色块往前加一位与前面的色块连上
          let preIndex = arr.indexOf(1);
          preIndex === 0 ? (arr[arr.length - 1] = 1) : (arr[preIndex - 1] = 1);

          let aColor = backColorList.shift();
          option.series[0].data.push({
            // name: classify_name,
            symbol: 'none',
            value: arr,
            areaStyle: { color: aColor },
            itemStyle: { color: aColor }
          });
        }
        this.options2 = option;
        this.options2.series[1].data.sort((a, b) => {
          if (this.$route.query.name.split(',').indexOf(a.name) > this.$route.query.name.split(',').indexOf(b.name)) return 1;
          else return -1;
        });
        for (let i = 0; i < this.$route.query.name.split(',').length; i++) {
          let temp = [];
          if (this.options2.series[1].data.findIndex((item) => item.name == this.$route.query.name.split(',')[i]) >= 0) {
            temp = [
              this.options2.series[1].data[
              this.options2.series[1].data.findIndex((item) => item.name == this.$route.query.name.split(',')[i])
              ]
            ];
          }
          this['options2_' + (i + 1)] = JSON.parse(
            JSON.stringify({
              color: option.color,
              radar: option.radar,
              series: option.series,
              title: {
                text:
                  this.$route.query.name.split(',')[i].length > 10
                    ? this.$route.query.name.split(',')[i].slice(0, 8) + '...'
                    : this.$route.query.name.split(',')[i],
                fontSize: 16
              }
            })
          );

          this['options2_' + (i + 1)].series[1].data = temp;
        }
      } else {
        this.empty2 == true;
      }
    },
    async getmanagerdata () {
      let color = ['#ff9003', '#4096FF', '#e040fb', '#ff3d00'];
      let data = await ManagerIndustryCapability({
        manager_code: this.id,
        type: this.type,
        manager_name: this.name
      });
      if (data) {
        // //console.log(data)
        // //console.log('ability')
        let tempindustry = [];
        for (let i = 0; i < data.data.industry_name.length; i++) {
          tempindustry.push({ name: data.data.industry_name[i], max: 1 });
        }
        let arrdata = [];
        let names = this.name.split(',');
        for (let i = 0; i < names.length; i++) {
          if (data.data[names[i]]) {
            arrdata.push({
              value: data.data[names[i]],
              name: names[i] + '行业能力',
              itemStyle: {
                // 单个拐点标志的样式设置。
                normal: {
                  borderColor: color[i],
                  // 拐点的描边颜色。[ default: '#000' ]
                  borderWidth: 3
                  // 拐点的描边宽度，默认不描边。[ default: 0 ]
                }
              },
              lineStyle: {
                // 单项线条样式。
                normal: {
                  color: color[i],
                  opacity: 0.5 // 图形透明度
                }
              },
              areaStyle: {
                // 单项区域填充样式
                normal: {
                  color: color[i], // 填充的颜色。[ default: "#000" ]
                  opacity: 0.7
                }
              }
            });
          }
        }
        this.options2 = {
          color: color,
          legend: {},
          radar: {
            splitLine: {
              // (这里是指所有圆环)坐标轴在 grid 区域中的分隔线。
              lineStyle: {
                color: '#CFE5F3',
                opacity: 0.3,
                // 分隔线颜色
                width: 2
                // 分隔线线宽
              }
            },
            splitArea: {
              // 坐标轴在 grid 区域中的分隔区域，默认不显示。
              show: true,
              areaStyle: {
                // 分隔区域的样式设置。
                color: ['rgb(246,250,255)', 'rgb(246,250,255)']
                // 分隔区域颜色。分隔区域会按数组中颜色的顺序依次循环设置颜色。默认是一个深浅的间隔色。
              }
            },
            axisLine: {
              // (圆内的几条直线)坐标轴轴线相关设置
              lineStyle: {
                color: '#7ab1ff',
                // 坐标轴线线的颜色。
                width: 0.3,
                // 坐标轴线线宽。
                type: 'solid'
                // 坐标轴线线的类型。
              }
            },

            // shape: 'circle',
            indicator: tempindustry
          },
          // tooltip: {
          // 	trigger: 'axis',
          // 	// axisPointer: {
          // 	//     type: 'cross'
          // 	// },
          // 	formatter(params) {
          // 		// ////console.log(params)
          // 		return '行业：' + params[0].axisValue + ' ,' + params[0].data.toFixed(3);
          // 	}
          // },
          series: [
            {
              itemStyle: {
                // 单个拐点标志的样式设置。
                normal: {
                  borderColor: '#4096FF',
                  // 拐点的描边颜色。[ default: '#000' ]
                  borderWidth: 3
                  // 拐点的描边宽度，默认不描边。[ default: 0 ]
                }
              },
              lineStyle: {
                // 单项线条样式。
                normal: {
                  color: '#4096FF',
                  opacity: 0.5 // 图形透明度
                }
              },
              areaStyle: {
                // 单项区域填充样式
                normal: {
                  color: 'rgb(223,236,255)', // 填充的颜色。[ default: "#000" ]
                  opacity: 0.7
                }
              },
              name: '基金经理能力',
              type: 'radar',
              data: arrdata
            }
          ]
        };
      } else {
        this.empty2 == true;
      }
    },
    async gefunddata () {
      let color = ['#4096FF', '#4096ff', '#afb2b1', '#862e9c', '#fa5252'];
      let data = await FundIndustryCapability({
        fund_code: this.id,
        type: this.type,
        fund_name: this.name
      });
      if (data) {
        // //console.log(data)
        // //console.log('ability')
        let tempindustry = [];
        for (let i = 0; i < data.data.industry_name.length; i++) {
          tempindustry.push({ name: data.data.industry_name[i], max: 1 });
        }
        let arrdata = [];
        let names = this.name.split(',');
        for (let i = 0; i < names.length; i++) {
          if (data.data[names[i]]) {
            arrdata.push({
              value: data.data[names[i]],
              name: names[i] + '行业能力',
              itemStyle: {
                // 单个拐点标志的样式设置。
                normal: {
                  borderColor: color[i],
                  // 拐点的描边颜色。[ default: '#000' ]
                  borderWidth: 3
                  // 拐点的描边宽度，默认不描边。[ default: 0 ]
                }
              },
              lineStyle: {
                // 单项线条样式。
                normal: {
                  color: color[i],
                  opacity: 0.5 // 图形透明度
                }
              },
              areaStyle: {
                // 单项区域填充样式
                normal: {
                  color: color[i], // 填充的颜色。[ default: "#000" ]
                  opacity: 0.7
                }
              }
            });
          }
        }
        this.options2 = {
          color: color,
          legend: {},
          radar: {
            splitLine: {
              // (这里是指所有圆环)坐标轴在 grid 区域中的分隔线。
              lineStyle: {
                color: '#CFE5F3',
                opacity: 0.3,
                // 分隔线颜色
                width: 2
                // 分隔线线宽
              }
            },
            splitArea: {
              // 坐标轴在 grid 区域中的分隔区域，默认不显示。
              show: true,
              areaStyle: {
                // 分隔区域的样式设置。
                color: ['rgb(246,250,255)', 'rgb(246,250,255)']
                // 分隔区域颜色。分隔区域会按数组中颜色的顺序依次循环设置颜色。默认是一个深浅的间隔色。
              }
            },
            axisLine: {
              // (圆内的几条直线)坐标轴轴线相关设置
              lineStyle: {
                color: '#7ab1ff',
                // 坐标轴线线的颜色。
                width: 0.3,
                // 坐标轴线线宽。
                type: 'solid'
                // 坐标轴线线的类型。
              }
            },

            // shape: 'circle',
            indicator: tempindustry
          },
          // tooltip: {
          // 	trigger: 'axis',
          // 	// axisPointer: {
          // 	//     type: 'cross'
          // 	// },
          // 	formatter(params) {
          // 		// ////console.log(params)
          // 		return '行业：' + params[0].axisValue + ' ,' + params[0].data.toFixed(3);
          // 	}
          // },
          series: [
            {
              itemStyle: {
                // 单个拐点标志的样式设置。
                normal: {
                  borderColor: '#4096FF',
                  // 拐点的描边颜色。[ default: '#000' ]
                  borderWidth: 3
                  // 拐点的描边宽度，默认不描边。[ default: 0 ]
                }
              },
              lineStyle: {
                // 单项线条样式。
                normal: {
                  color: '#4096FF',
                  opacity: 0.5 // 图形透明度
                }
              },
              areaStyle: {
                // 单项区域填充样式
                normal: {
                  color: 'rgb(223,236,255)', // 填充的颜色。[ default: "#000" ]
                  opacity: 0.7
                }
              },
              name: '基金经理能力',
              type: 'radar',
              data: arrdata
            }
          ]
        };
      } else {
        this.empty2 == true;
      }
    },
    isValidData: function (val, type = 'number') {
      if (type === 'number') {
        if (val === undefined || val === null || val === NaN || val === 'nan') {
          return false;
        } else if (Number.isNaN(parseFloat(val))) {
          return false;
        }
      }
      return true;
    },
    async createPrintWord () {
      this.show = false;
      let data = [];
      await this.$nextTick(() => {
        let height = this.$refs['industryscoreAll']?.$el.clientHeight;
        let width = this.$refs['industryscoreAll']?.$el.clientWidth;
        let chart = this.$refs['industryscoreAll'].getDataURL({
          type: 'png',
          pixelRatio: 2,
          backgroundColor: '#fff'
        });
        data = [...this.$exportWord.exportTitle('行业能力圈'), ...this.$exportWord.exportChart(chart, { width, height })];
      });
      return data;
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () { },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
</style>
