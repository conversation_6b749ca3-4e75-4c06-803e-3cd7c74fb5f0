<template>
	<div class="add_email">
		<div class="form_item">
			<div>已绑定邮箱:&nbsp;</div>
			<div><EMAIL></div>
		</div>
		<div class="form_item">
			<div>邮箱:&nbsp;</div>
			<el-input v-model="form.email" placeholder="请输入邮箱" style="width: 390px"></el-input>
		</div>
		<div class="form_item">
			<div>姓名:&nbsp;</div>
			<el-input v-model="form.name" placeholder="请输入姓名" style="width: 390px"></el-input>
		</div>
		<div class="form_item">
			<div>分组:&nbsp;</div>
			<el-select v-model="model" multiple placeholder="请选择" style="width: 366px" @change="changeSelect">
				<el-option v-for="item in list" :key="item.group_id" :label="item.group_name" :value="item.group_id">
					<div>
						<el-checkbox v-model="item.checked" @change="changeCheckbox">{{ item.group_name }}</el-checkbox>
					</div>
				</el-option>
			</el-select>
			<div style="margin-left: 8px">
				<el-popover placement="top" width="272" trigger="click" v-model="popoverShow">
					<div style="padding: 4px">
						<el-input v-model="groupName" placeholder="请输入新建分组名称" style="width: 240px"></el-input>
						<div style="margin-top: 16px; float: right">
							<el-button style="padding: 4px 7px" @click="changePopoverShow">取消</el-button>
							<el-button style="padding: 4px 7px" type="primary" @click="postEmailGroup">确认</el-button>
						</div>
					</div>
					<div slot="reference">
						<el-link>
							<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
								<g clip-path="url(#clip0_3453_73312)">
									<path
										d="M4.71416 8.57225H7.42844V11.2865C7.42844 11.3651 7.49273 11.4294 7.5713 11.4294H8.42844C8.50702 11.4294 8.5713 11.3651 8.5713 11.2865V8.57225H11.2856C11.3642 8.57225 11.4284 8.50796 11.4284 8.42939V7.57225C11.4284 7.49368 11.3642 7.42939 11.2856 7.42939H8.5713V4.71511C8.5713 4.63654 8.50702 4.57225 8.42844 4.57225H7.5713C7.49273 4.57225 7.42844 4.63654 7.42844 4.71511V7.42939H4.71416C4.63559 7.42939 4.5713 7.49368 4.5713 7.57225V8.42939C4.5713 8.50796 4.63559 8.57225 4.71416 8.57225Z"
										fill="black"
										fill-opacity="0.45"
									/>
									<path
										d="M14.5717 0.857422H1.42885C1.11278 0.857422 0.857422 1.11278 0.857422 1.42885V14.5717C0.857422 14.8878 1.11278 15.1431 1.42885 15.1431H14.5717C14.8878 15.1431 15.1431 14.8878 15.1431 14.5717V1.42885C15.1431 1.11278 14.8878 0.857422 14.5717 0.857422ZM13.8574 13.8574H2.14314V2.14314H13.8574V13.8574Z"
										fill="black"
										fill-opacity="0.45"
									/>
								</g>
								<defs>
									<clipPath id="clip0_3453_73312">
										<rect width="16" height="16" fill="white" />
									</clipPath>
								</defs>
							</svg>
						</el-link>
					</div>
				</el-popover>
			</div>
		</div>
	</div>
</template>

<script>
import { postEmailGroup } from '@/api/pages/NodeServer.js';

export default {
	data() {
		return {
			form: {},
			model: [],
			checked: false,
			groupName: '',
			list: [],
			popoverShow: false
		};
	},
	methods: {
		// 获取父组件传递数据
		getData(list) {
			this.list = [];
			list?.map((item) => {
				let index = this.list.findIndex((obj) => {
					return item.group_id == obj.group_id;
				});
				if (index == -1) {
					this.list.push({
						group_id: item.group_id,
						group_name: item.group_name,
						checked: false
					});
				}
			});
		},
		// 新增分组
		async postEmailGroup() {
			let data = await postEmailGroup({
				user_id: localStorage.getItem('id'),
				name: this.groupName
			});
			if (data?.mtycode == 200) {
				this.$message.success(data?.mtymessage);
				this.$emit('refreshList');
				this.changePopoverShow();
			} else {
				this.$message.warning(data?.mtymessage || '新增邮箱失败');
			}
		},
		// 父组件获取表单
		getSubmitForm() {
			return {
				...this.form,
				group_id: this.model
			};
		},
		// 隐藏弹出框
		changePopoverShow() {
			this.popoverShow = !this.popoverShow;
		},
		changeCheckbox() {
			this.model = this.list
				.filter((item) => {
					return item.checked;
				})
				.map((item) => {
					return item.group_id;
				});
		}
	}
};
</script>

<style lang="scss" scoped>
.add_email {
	font-family: 'PingFang SC';
	font-style: normal;
	font-weight: 400;
	font-size: 14px;
	line-height: 11px;
	color: rgba(0, 0, 0, 0.85);
	.form_item {
		display: flex;
		align-items: center;
		margin-bottom: 16px;

		.add_group {
			cursor: pointer;
		}
		.form_item_select {
			width: 390px;
			height: 120px;
			background: #ffffff;
			box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);
			border-radius: 4px;
			.btn_flex {
				display: flex;
			}
		}
	}
}
.el-checkbox {
	width: 100%;
}
</style>
