<!--  -->
<template>
  <div v-loading="loading" class="FTB">
    <div style="margin-bottom: 16px">
      <span class="headerFontSmall">组合管理/</span>
      <span class="headerFontBig">{{ title }}</span>
    </div>
    <div class="boxMain">
      <div v-show="title == '我的组合'" style="margin-bottom: 8px">
        <el-button @click="createPortfolio" type="primary">+&nbsp;新建组合</el-button>
      </div>
      <div>
        <el-table style="min-height: 612px" id="tablePortfolio1" :data="tableDataNow">
          <el-table-column
            min-width="233px"
            align="gotoleft"
            prop="name"
            show-overflow-tooltip
            label="组合名称"
          >
            <template slot-scope="scope">
              <el-link @click="goDetail(scope.row)">
                {{
                scope.row.name
                }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column
            min-width="160px"
            align="gotoleft"
            prop="description"
            show-overflow-tooltip
            label="组合说明"
          ></el-table-column>
          <el-table-column min-width="136px" align="gotoleft" prop="date" sortable label="成立时间"></el-table-column>
          <el-table-column min-width="112px" align="gotoleft" prop="rate" sortable label="当日涨跌幅">
            <templete slot-scope="scope">{{ scope.row.rate | fix2p }}</templete>
          </el-table-column>
          <el-table-column
            min-width="112px"
            align="gotoleft"
            sortable
            prop="cum_return"
            label="积累涨跌幅"
          >
            <templete slot-scope="scope">
              {{
              scope.row.cum_return | fix2p
              }}
            </templete>
          </el-table-column>
          <el-table-column min-width="88px" align="gotoleft" sortable prop="nav" label="净值">
            <templete slot-scope="scope">{{ scope.row.nav | fix2 }}</templete>
          </el-table-column>
          <el-table-column min-width="88px" align="gotoleft" prop="totalmv" sortable label="市值">
            <templete slot-scope="scope">
              {{
              scope.row.totalmv | fixY
              }}
            </templete>
          </el-table-column>
          <el-table-column min-width="150px" align="gotoleft" label="操作">
            <templete slot-scope="scope">
              <div v-show="userId == scope.row.user_id">
                <el-button
                  type="text"
                  @click="getComdetail(scope.row.combination_id)"
                  style="color: #4096ff"
                >编辑</el-button>
                <el-button
                  type="text"
                  @click="changeFund(scope.row.combination_id, scope.row.date)"
                  style="color: #4096ff"
                >调仓</el-button>
                <el-button
                  type="text"
                  @click="delCom(scope.row.combination_id)"
                  style="color: #4096ff"
                >删除</el-button>
              </div>
            </templete>
          </el-table-column>
          <template slot="empty">
            <el-empty image-size="160"></el-empty>
          </template>
        </el-table>
        <el-pagination
          background
          style="
            display: flex;
            justify-content: right;
            padding-top: 16px;
            padding-bottom: 24px;
          "
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="currentPage"
          :page-sizes="[10, 20, 40, 60, 80, 100]"
          :page-size="pageSIze"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.length"
        ></el-pagination>
      </div>
    </div>
    <el-dialog class="FTBdialog" width="686px" height="444px" :visible.sync="showAdd">
      <div slot="title">
        <span
          style="
            font-family: 'PingFang';
            font-style: normal;
            font-weight: 500;
            font-size: 16px;
            line-height: 24px;
            color: rgba(0, 0, 0, 0.85);
            width: 100%;
          "
        >{{ nowName }}</span>
      </div>
      <div
        style="
          width: 100%;
          height: 1px;
          background: rgba(0, 0, 0, 0.06);
          margin-bottom: 16px;
        "
      ></div>
      <div style="display: flex; margin-bottom: 16px">
        <div style="flex: 1">
          组合名称：
          <el-input v-model="name" style="width: 216px" placeholder="请输入"></el-input>
        </div>
        <div style="flex: 1">
          <span style="margin-left: 50px">创建人：</span>
          <el-input disabled v-model="creater" style="width: 216px" placeholder="请输入"></el-input>
        </div>
      </div>
      <div style="display: flex; margin-bottom: 16px">
        <div style="flex: 1">
          创建日期：
          <el-input disabled v-model="createdate" style="width: 216px" placeholder="请输入"></el-input>
        </div>
        <div style="flex: 1">
          <span style="margin-left: 36px">成立日期：</span>
          <el-date-picker
            v-model="founddate"
            type="date"
            value-format="yyyy-MM-dd"
            style="width: 216px"
            placeholder="请选择"
          ></el-date-picker>
        </div>
      </div>
      <div style="display: flex; margin-bottom: 16px">
        <div style="flex: 1">
          结束日期：
          <el-date-picker
            v-model="enddate"
            type="date"
            value-format="yyyy-MM-dd"
            style="width: 216px"
            placeholder="请选择"
          ></el-date-picker>
        </div>
        <div style="flex: 1">
          <span style="margin-left: 36px">分红处理：</span>
          <el-radio-group v-model="moneyDo">
            <el-radio :label="true">提取现金</el-radio>
            <el-radio :label="false">分红再投资</el-radio>
          </el-radio-group>
        </div>
      </div>
      <div style="display: flex; margin-bottom: 24px">
        <div style="flex: 1">
          组合分类：
          <el-radio-group v-model="categray">
            <el-radio :label="1">个人</el-radio>
            <el-radio :label="2">公司</el-radio>
            <el-radio
              v-if="
                $store.state.userType == 'superuser' ||
                $store.state.userType == 'staff'
              "
              :label="3"
            >公开</el-radio>
          </el-radio-group>
        </div>
        <div style="flex: 1">
          <span style="margin-left: 36px">是否置顶：</span>
          <el-radio-group v-model="istop">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </div>
      </div>
      <div style="margin-bottom: 16px">
        <div style="margin-bottom: 8px">组合说明:</div>
        <el-input
          v-model="description"
          :autosize="{ minRows: 4, maxRows: 8 }"
          placeholder="请输入"
          type="textarea"
        ></el-input>
      </div>
      <div style="text-align: right">
        <el-button type @click="showAdd = false">取消</el-button>
        <el-button type="primary" @click="nowName == '新建组合' ? addCom() : upCom()">确认</el-button>
      </div>
    </el-dialog>
    <assetDetails :show="showDetails" ref="assetDetails" :comId="showDetailsID"></assetDetails>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import {
  getList,
  createCom,
  delCom,
  upCom,
  getListAll
} from "@/api/pages/SystemMixed.js";
import assetDetails from "./assetDetails";
export default {
  props: {
    title: {}
  },
  //import引入的组件需要注入到对象中才能使用
  components: { assetDetails },
  filters: {
    fix2p(value) {
      return value &&
        value != "" &&
        value != "--" &&
        value != "- -" &&
        JSON.stringify(value) != "[]" &&
        JSON.stringify(value) != "{}" &&
        value != "NAN" &&
        value != "nan"
        ? (Number(value) * 100).toFixed(2) + "%"
        : "--";
    },
    fix2(value) {
      return value &&
        value != "" &&
        value != "--" &&
        value != "- -" &&
        JSON.stringify(value) != "[]" &&
        JSON.stringify(value) != "{}" &&
        value != "NAN" &&
        value != "nan"
        ? Number(value).toFixed(2)
        : "--";
    },
    fixY(value) {
      return value &&
        value != "" &&
        value != "--" &&
        value != "- -" &&
        JSON.stringify(value) != "[]" &&
        JSON.stringify(value) != "{}" &&
        value != "NAN" &&
        value != "nan"
        ? Number(value / 100000000).toFixed(2) + "亿"
        : "--";
    }
  },
  data() {
    //这里存放数据
    return {
      tableData: [],
      tableDataNow: [],
      showAdd: false,
      name: "",
      creater: "",
      createdate: "",
      founddate: "",
      enddate: "",
      moneyDo: false,
      categray: "",
      istop: false,
      description: "",
      nowName: "新建组合",
      updateId: "",
      showDetails: false,
      showDetailsID: "",
      userId: "",
      loading: false,
      pageSIze: 10,
      currentPage: 1
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    handleSizeChange(val) {
      this.pageSIze = val;
      this.currentPage = 1;
      this.handleCurrentChange(1);
    },
    handleCurrentChange(val) {
      this.tableDataNow = this.tableData.slice(
        (val - 1) * this.pageSIze,
        val * this.pageSIze
      );
    },
    // 前往组合分析
    goDetail(val) {
      this.$router.push({
        path: "/portfolioAnalysis/" + val.combination_id,
        hash: "",
        query: { id: val.combination_id, name: val.name }
      });
    },
    // 获取组合列表
    async getData(val) {
      this.loading = true;
      let flag =
        val == "我的组合"
          ? 1
          : val == "公司组合"
          ? 2
          : val == "公开组合"
          ? 3
          : 0;
      let { data, message, mtycode } = await getListAll({ ispublic: flag });
      this.loading = false;
      if (mtycode == 200) {
        this.tableData = data;
        this.handleCurrentChange(1);
      } else {
        this.tableData = [];
        // this.$message.warning('暂无数据');
      }
    },
    // 获取组合详情
    async getComdetail(id) {
      let { data, message, mtycode } = await getList({ combination_id: id });
      if (mtycode == 200) {
        this.updateId = id;
        this.nowName = "编辑组合";
        this.creater = this.$store.state.username;
        this.name = data[0].name;
        this.createdate = data[0].create_date;
        this.founddate = data[0].date;
        this.enddate = data[0].end_date;
        this.moneyDo = data[0].flag;
        this.categray = data[0].ispublic;
        this.description = data[0].description;
        this.istop = data[0].isshow;
        this.showAdd = true;
      } else {
        this.$message.warning("暂无数据");
      }
    },
    // 点击创建组合
    createPortfolio() {
      this.nowName = "新建组合";
      this.showAdd = true;
      this.createdate = this.moment(new Date()).format("YYYY-MM-DD");
      this.creater = this.$store.state.username;
    },
    // 确认创建组合
    async addCom() {
      let { data, message, mtycode } = await createCom({
        name: this.name,
        create_date: this.createdate,
        date: this.founddate,
        end_date: this.enddate,
        flag: this.moneyDo,
        ispublic: this.FUNC.isEmpty(this.categray) ? this.categray : 1,
        isshow: this.istop,
        description: this.description
      });
      if (mtycode == 200) {
        // try {
        // 	window.localStorage.setItem('mty_portfolio_founddate', JSON.stringify(this.founddate));
        // } catch (e) {
        // 	// this.$message.warning('缓存已满，无法存入');
        // 	// window.localStorage.setItem('mty_portfolio_founddate', JSON.stringify(''));
        // }
        this.$message.success("创建成功");
        this.showAdd = false;
        // this.getData(this.title);
        let flag =
          this.title == "我的组合"
            ? 1
            : this.title == "公司组合"
            ? 2
            : this.title == "公开组合"
            ? 3
            : 0;
        let { data, message, mtycode } = await getListAll({ ispublic: flag });
        if (mtycode == 200) {
          this.tableData = data;
          this.handleCurrentChange(1);
        } else {
          this.tableData = [];
          // this.$message.warning('暂无数据');
        }
        // this.changeFund(this.tableData[this.tableData.length - 1].combination_id, this.tableData[this.tableData.length - 1].date);//由于苟煜无法确定新建的位置故无法自动打开新建的组合
      } else {
        this.$message.warning("创建失败");
      }
    },
    // 删除组合
    async delCom(id) {
      let { data, message, mtycode } = await delCom({ combination_id: id });
      if (mtycode == 200) {
        this.getData(this.title);
        this.$message.success("删除成功");
      } else {
        this.$message.warning("删除失败");
      }
    },
    // 确认修改组合
    async upCom() {
      let { data, message, mtycode } = await upCom({
        combination_id: this.updateId,
        name: this.name,
        create_date: this.createdate,
        date: this.founddate,
        end_date: this.enddate,
        flag: this.moneyDo,
        ispublic: this.FUNC.isEmpty(this.categray) ? this.categray : 1,
        isshow: this.istop,
        description: this.description
      });
      if (mtycode == 200) {
        this.$message.success("修改成功");
        this.showAdd = false;
        this.getData(this.title);
      } else {
        this.$message.warning("修改失败");
      }
    },
    // 组合内资产详情open
    changeFund(id, date) {
      this.$refs.assetDetails.showdialog(id, date);
      this.showDetailsID = id;
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    if (window.innerHeight > 1600) {
      this.pageSIze = 20;
      document.getElementById("tablePortfolio1").style = "min-height:1152px";
    } else {
      this.pageSIze = 10;
      document.getElementById("tablePortfolio1").style = "min-height:612px";
    }
    this.userId = localStorage.getItem("id");
  },
  beforeCreate() {}, //生命周期 - 创建之前
  beforeMount() {}, //生命周期 - 挂载之前
  beforeUpdate() {}, //生命周期 - 更新之前
  updated() {}, //生命周期 - 更新之后
  beforeDestroy() {}, //生命周期 - 销毁之前
  destroyed() {}, //生命周期 - 销毁完成
  activated() {} //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style>
.FTBdialog .el-dialog__body {
  padding-top: 0 !important;
}
</style>
<style lang="scss" scoped>
.FTB {
  padding: 24px;
}

//@import url(); 引入公共css类
</style>
