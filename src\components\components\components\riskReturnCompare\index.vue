<template>
	<div>
		<!-- <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px">
			<div class="title">风险收益对比</div>
			<div>
				<el-date-picker
					v-model="value"
					type="daterange"
					align="right"
					unlink-panels
					range-separator="至"
					start-placeholder="开始日期"
					end-placeholder="结束日期"
					:picker-options="pickerOptions"
				>
				</el-date-picker>
			</div>
		</div> -->
		<div class="charts_fill_class" v-loading="loading">
			<v-chart
				ref="riskReturnCompare"
				:options="option"
				element-loading-text="暂无数据"
				element-loading-spinner="el-icon-document-delete"
				element-loading-background="rgba(239, 239, 239, 0.5)"
				class="charts_one_class"
				autoresize
			></v-chart>
		</div>
	</div>
</template>

<script>
import VChart from 'vue-echarts';
export default {
	components: { VChart },
	data() {
		return {
			pickerOptions: {
				shortcuts: [
					{
						text: '最近一周',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
							picker.$emit('pick', [start, end]);
						}
					},
					{
						text: '最近一个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
							picker.$emit('pick', [start, end]);
						}
					},
					{
						text: '最近三个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
							picker.$emit('pick', [start, end]);
						}
					}
				]
			},
			value: '',
			loading: true,
			option: {}
		};
	},
	methods: {
		// 获取数据
		getData(data) {
			let series = data?.map((item) => {
				return [
					item.cum_return * 100 ? (item.cum_return * 100).toFixed(2) : '--',
					item.maxdrawdown * 100 ? (item.maxdrawdown * 100).toFixed(2) : '--',
					this.FUNC.textConverter(this.COMMON.fundType_zh_en, item.type, 'en', 'zh')
				];
			});
			this.loading = false;
			this.option = {
				xAxis: {},
				yAxis: {},
				grid: {
					left: '16px',
					right: '0',
					bottom: '18px',
					top: '18px', // 无图例18px
					containLabel: true
				},
				series: [
					{
						symbolSize: 20,
						label: {
							show: true,
							formatter: function (param) {
								return param.data[2];
							},
							minMargin: 10,
							position: 'top'
						},
						data: series,
						type: 'scatter'
					}
				]
			};
		}
	}
};
</script>

<style></style>
