<template>
	<div class="drop-down-menu-box">
		<el-select :loading="loading" clearable filterable v-model="chooseName" :placeholder="placeholderName" @change="deliverFundManagerType">
			<el-option v-for="item in dropDownData" :key="item.value" :label="item.label" :value="item.value"> </el-option>
		</el-select>
	</div>
</template>

<script>
export default {
	data() {
		return {
			dropDownData: [],
			loading: true,
			chooseName: '基金经理类型'
		};
	},
	methods: {
		getData(data, loading = true) {
			console.log('childrendata', data); //空
			this.loading = loading;
			this.dropDownData = data;
		},
		deliverFundManagerType(data) {
			this.chooseName = data;
			this.$emit('deliverFundManagerType', data);
		}
	}
};
</script>
