import request from '@/api/request';

/**
 * 
 * @param {收益曲线}} params 
 *  code: str (基金 基金经理 基金公司代码)
    flag: int (是否为基金 基金经理基金公司)
        1: fund
        2: manager
        3: company
    type: str(类型)
    start_date: date (起始时间)
    end_date: date (截止时间)
    benchmark: list (基准, 特殊基准见备注)
 * @returns 
 */

// 获取基金/基金经理/基金公司收益曲线
export function getRateInfo(data) {
	return request({
		url: '/RateInfo/',
		method: 'post',
		data
	});
}
// 获取基金收益曲线基准列表
export function getBenchmarkList(params) {
	return request({
		url: '/BenchmarkList/',
		method: 'get',
		params
	});
}
// 基金经理收益曲线基准列表 code type
export function getManagerBenchmark(params) {
	return request({
		url: '/ManagerBenchmark/',
		method: 'get',
		params
	});
}
// 获取基准收益曲线
export function getIndexReturnInfo(data) {
	return request({
		url: '/IndexReturnInfo/',
		method: 'post',
		data
	});
}
