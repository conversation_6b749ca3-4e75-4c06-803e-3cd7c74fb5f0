<template>
  <el-dialog class="FTBdialog" width="686px" height="444px" :visible.sync="visible">
    <div v-loading="loading">
      <div slot="title">
        <span
          style="
            font-family: 'PingFang';
            font-style: normal;
            font-weight: 500;
            font-size: 16px;
            line-height: 24px;
            color: rgba(0, 0, 0, 0.85);
            width: 100%;
          "
        >快速创建组合</span>
      </div>
      <div
        style="
          width: 100%;
          height: 1px;
          background: rgba(0, 0, 0, 0.06);
          margin-bottom: 16px;
        "
      ></div>
      <div style="display: flex; margin-bottom: 16px">
        <div style="flex: 1">
          组合名称：
          <el-input v-model="form.name" style="width: 216px" placeholder="请输入"></el-input>
        </div>
        <div style="flex: 1">
          <span style="margin-left: 50px">创建人：</span>
          <el-input disabled v-model="user_name" style="width: 216px" placeholder="请输入"></el-input>
        </div>
      </div>
      <div style="display: flex; margin-bottom: 16px">
        <div style="flex: 1">
          创建日期：
          <el-input disabled v-model="form.create_date" style="width: 216px" placeholder="请输入"></el-input>
        </div>
        <div style="flex: 1">
          <span style="margin-left: 36px">成立日期：</span>
          <el-date-picker
            v-model="form.date"
            type="date"
            value-format="yyyy-MM-dd"
            style="width: 216px"
            placeholder="请选择"
          ></el-date-picker>
        </div>
      </div>
      <div style="display: flex; margin-bottom: 16px">
        <div style="flex: 1">
          结束日期：
          <el-date-picker
            v-model="form.end_date"
            type="date"
            value-format="yyyy-MM-dd"
            style="width: 216px"
            placeholder="请选择"
          ></el-date-picker>
        </div>

        <div style="flex: 1">
          <span style="margin-left: 36px">调仓日期：</span>
          <el-date-picker
            v-model="form.date"
            type="date"
            disabled
            value-format="yyyy-MM-dd"
            style="width: 216px"
            placeholder="请选择"
          ></el-date-picker>
        </div>
      </div>
      <div style="display: flex; margin-bottom: 24px">
        <div style="flex: 1">
          <span>分红处理：</span>
          <el-radio-group v-model="form.flag">
            <el-radio :label="true">提取现金</el-radio>
            <el-radio :label="false">分红再投资</el-radio>
          </el-radio-group>
        </div>
        <div style="flex: 1">
          <span style="margin-left: 36px">是否置顶：</span>
          <el-radio-group v-model="form.isshow">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </div>
      </div>
      <div style="margin-bottom: 16px">
        <div style="margin-bottom: 8px">组合说明:</div>
        <el-input
          v-model="form.description"
          :autosize="{ minRows: 4, maxRows: 8 }"
          placeholder="请输入"
          type="textarea"
        ></el-input>
      </div>
      <div style="text-align: right">
        <el-button type @click="visible = false">取消</el-button>
        <el-button type="primary" @click="submit">确认</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { createCom2 } from "@/api/pages/SystemMixed.js";
export default {
  props: {
    ismanager: {
      type: Boolean
    }
  },
  data() {
    return {
      loading: false,
      user_name: this.$store.state.username,
      visible: false,
      form: {
        flag: true,
        isshow: false
      }
    };
  },
  methods: {
    getData(val) {
      this.form = {
        ...this.form,
        create_date: this.moment().format("YYYY-MM-DD"),
        idList: val.map(v => v.id)
      };
      this.visible = true;
    },
    async submit() {
      if (this.FUNC.isEmpty(this.form.date)) {
      } else {
        this.$message.error("请选择组合成立日期");
        return false;
      }
      if (this.FUNC.isEmpty(this.form.name)) {
      } else {
        this.$message.error("请输入组合名称");
        return false;
      }
      this.loading = true;
      let data = await createCom2({
        name: this.form.name,
        create_date: this.form.create_date,
        date: this.form.date,
        end_date: this.form.end_date,
        flag: this.form.flag,
        ispublic: 1,
        isshow: this.form.isshow,
        description: this.form.description,
        idList: this.form.idList
      });
      if (data?.mtycode == 200) {
        this.$message.success("创建成功");
        this.visible = false;
        this.$router.push("/portfolioSelf");
      } else {
        this.$message.warning("创建失败，" + data?.mtymessage);
      }
      this.loading = false;
    }
  }
};
</script>

<style></style>
