<!--  -->
<template>
  <div v-if="empty1"
       class="returns">
    <div style="display: flex; align-items: center; width: 100%; position: relative; justify-content: space-between">
      <div style="display: flex; align-items: center">
        <div class="TitltCompare">相同行业分析</div>
      </div>
      <div style="text-align: right; margin-top: 16px">
        <el-select v-model="value"
                   @change="searchpeople"
                   prefix-icon="el-icon-search"
                   :loading="loading"
                   placeholder="选择基准">
          <el-option v-for="group in options"
                     :key="group.industry_code"
                     :label="group.industry_name"
                     :value="group.industry_code">
          </el-option>
        </el-select>
      </div>
    </div>

    <div v-loading="loading"
         style="page-break-inside: avoid; text-align: left; margin-top: 16px">
      <v-chart ref="sameholdindustry"
               autoresize
               element-loading-text="暂无数据"
               element-loading-spinner="el-icon-document-delete"
               element-loading-background="rgba(239, 239, 239, 0.5)"
               style="page-break-inside: avoid; width: 100%; height: 400px"
               :options="optionpbroe"></v-chart>
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { FundIndustryNameList, FundIndustryReturn, ManagerIndustryNameList, ManagerIndustryReturnDetails } from '@/api/pages/tools/compare.js';
import { getFundOrBase } from '@/api/pages/components/yejiheader.js';
import { fontSize } from '../../../../../assets/js/echartsrpxtorem'; //注意路径
import VCharts from 'vue-echarts';
export default {
  props: {
    comparetype: {
      type: String,
      default: 'manager' //fund
    },
    id: {
      type: String,
      default: '30189741,30441407'
    },
    type: {
      type: String,
      default: 'equity'
    },
    name: {
      type: String,
      default: '萧楠,胡昕炜'
    }
  },
  //import引入的组件需要注入到对象中才能使用
  components: { 'v-chart': VCharts },
  data () {
    //这里存放数据
    return {
      showdetailchoose: false,
      fund_hold: [],
      empty1: true,
      value: '',
      options: '',
      loading: false,
      optionpbroe: {},
      baseperson: '',
      namebench: '',
      baseindexlist: [],
      basepersonfund: [],
      loading: false
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {
    value (val) { }
  },
  //方法集合
  methods: {
    // 获取基准
    async searchpeople (value) {
      for (let i = 0; i < this.options.length; i++) {
        if (value == this.options[i].industry_code) {
          this.namebench = this.options[i].industry_name;
        }
      }
      if (this.comparetype == 'manager') {
        this.getmanagerdata();
      } else {
        this.gefunddata();
      }
    },
    async getdata () {
      Object.assign(this.$data, this.$options.data());
      this.loading = true;
      if (this.comparetype == 'manager') {
        let data = await ManagerIndustryNameList({ manager_code: this.id, type: this.type, manager_name: this.name });
        if (data) {
          this.options = data.data.same;
          if (this.options.length > 0) {
            this.value = this.options[0].industry_code;
            this.namebench = this.options[0].industry_name;
            this.getmanagerdata();
          } else {
            this.empty1 = false;
          }
        } else {
          this.empty1 = false;
        }
      } else {
        let data = await FundIndustryNameList({ fund_code: this.id, type: this.type, index_code: this.value, fund_name: this.name });
        if (data) {
          this.options = data.data.same;
          if (this.options.length > 0) {
            this.value = this.options[0].industry_code;
            this.namebench = this.options[0].industry_name;
            this.gefunddata();
          } else {
            this.empty1 = false;
          }
        } else {
          this.empty1 = false;
        }
      }
    },
    async getmanagerdata () {
      this.loading = true;
      let { data } = await ManagerIndustryReturnDetails({
        manager_code: this.id,
        type: this.type,
        industry_code: this.value,
        manager_name: this.name
      });
      this.loading = false;

      if (data && data.length) {
        this.createEcharts('manager', data);
      } else {
        this.empty1 = false;
      }
    },
    async gefunddata () {
      this.loading = true;
      let { data } = await FundIndustryReturn({ fund_code: this.id, type: this.type, fund_name: this.name, industry_code: this.value });
      this.loading = false;

      if (data && data.length) {
        this.createEcharts('fund', data);
      } else {
        this.empty1 = false;
      }
    },
    createEcharts (type, data) {
      this.loading = false;
      let xAxis = [];
      let serise = [];
      let yAxis = [];
      let maxp = 0;
      data.map((item, i) => {
        if (type == 'fund') {
          yAxis.push(`${this.name.split(',')[this.id.split(',').findIndex((fund) => fund == item[0].fund_code)]}`);
          item.map((obj) => {
            if (maxp < obj.weight && obj.weight !== 'nan') {
              maxp = obj.weight;
            }
            serise.push([obj.yearqtr, i, obj.excess_return, obj.weight, obj.excess_return > 0 ? 'True' : 'False']);
            xAxis.indexOf(obj.yearqtr) == -1 ? xAxis.push(obj.yearqtr) : '';
          });
        } else {
          yAxis.push(`${this.name.split(',')[this.id.split(',').findIndex((code) => code == item[0].code)]}`);
          item.map((obj) => {
            if (maxp < obj.weight && obj.weight !== 'nan') {
              maxp = Number(obj.weight).toFixed(2);
            }
            serise.push([obj.yearqtr, i, obj.excess, obj.weight, obj.excess > 0 ? 'True' : 'False']);
            xAxis.indexOf(obj.yearqtr) == -1 ? xAxis.push(obj.yearqtr) : '';
          });
        }
      });
      xAxis = xAxis.sort();
      this.optionpbroe = {
        title: {
          text: this.namebench + '的配置与表现', //主标题
          textStyle: {
            color: '#000000', //颜色
            fontStyle: 'normal', //风格
            fontWeight: 'bolder', //粗细
            fontFamily: 'Microsoft yahei', //字体
            fontSize: fontSize(14), //大小
            align: 'center' //水平对齐
            // margin: 10
          },
          x: 'center',
          y: 'top',
          textAlign: 'center',
          left: '50%',
          itemGap: 7
        },
        tooltip: {
          textStyle: {
            fontSize: 14
          },
          trigger: 'item',
          axisPointer: {
            type: 'cross'
          },
          position: function (point, params, dom, rect, size) {
            return [0, point[1]];
          },
          formatter (params) {
            return (
              '季度：' +
              params.data[0] +
              '，权重：' +
              parseFloat(params.data[3]).toFixed(2) +
              '%，行业收益：' +
              (Number(params.data[2]) * 100).toFixed(2) +
              '%'
            );
          }
        },
        visualMap: [
          {
            show: false,
            type: 'piecewise', // 定义为分段型 visualMap
            right: '0',
            bottom: '20px',
            dimension: 4,
            splitNumber: 2,
            precision: 1,
            itemWidth: 10,
            itemHeight: 10,
            textGap: 5,
            textStyle: {
              color: 'black'
            },
            // categories 定义了 visualMap-piecewise 组件显示出来的项。
            categories: ['True', 'False'],
            // 表示 目标系列 的视觉样式 和 visualMap-piecewise 共有的视觉样式。
            inRange: {
              symbol: ['triangle', 'circle']
            },
            // 表示 visualMap-piecewise 本身的视觉样式。
            controller: {
              inRange: {
                color: ['black', 'black'],
                symbol: ['triangle', 'circle']
              }
            }
          },
          {
            show: false,
            right: '0px',
            top: '150px',
            dimension: 3,
            min: 0,
            max: maxp,
            itemWidth: 30,
            itemHeight: 120,
            precision: 0,
            text: ['配置权重%'],
            textGap: 5,
            textStyle: {
              color: 'black',
              fontSize: 10
            },
            inRange: {
              symbolSize: [0, 20]
            },
            controller: {
              inRange: {
                color: ['black']
              }
            }
          },
          {
            show: false,
            right: '5px',
            top: '5%',
            dimension: 2,
            min: -1.0,
            max: 1.0,
            itemHeight: 100,
            precision: 1,
            text: ['1', '-1.0'],
            textGap: 0,
            textStyle: {
              color: 'black',
              fontSize: 10
            },
            inRange: {
              color: ['green', '#E3E3E3', 'red']
            },
            outOfRange: {
              color: ['rgba(255,255,255,.5)']
            },
            controller: {
              inRange: {
                color: ['green', '#E3E3E3', 'red']
              }
            }
          }
        ],
        xAxis: {
          nameTextStyle: {
            fontSize: 14
          },
          axisLabel: {
            show: true,
            textStyle: {
              fontSize: 14
            }
          },
          type: 'category',
          boundaryGap: false,
          splitLine: {
            show: true,
            lineStyle: {
              color: 'white',
              type: 'solid',
              width: 2
            }
          },
          data: xAxis,
          interval: 0.25,
          length: xAxis.length
        },
        yAxis: {
          type: 'value',
          axisLine: {
            show: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: 'white',
              type: 'solid',
              width: 2
            }
          },
          splitArea: {
            show: true,
            areaStyle: {
              color: '#f5f7fa'
            }
          },
          // data: yAxis,
          interval: 1,
          axisLabel: {
            show: true,
            textStyle: {
              fontSize: 14
            },
            formatter: function (value, index) {
              return yAxis[index];
            }
          }
        },
        grid: {
          left: '80px',
          right: '30px',
          bottom: '50px',
          top: '50px',
          show: true,
          containLabel: true,
          backgroundColor: '#fafafa'
        },
        series: [
          {
            name: '相同行业',
            type: 'scatter',
            data: serise
          }
        ]
      };
    },
    createPrintWord () {
      if (this.empty1) {
        let height = this.$refs['sameholdindustry']?.$el.clientHeight;
        let width = this.$refs['sameholdindustry']?.$el.clientWidth;
        let chart = this.$refs['sameholdindustry'].getDataURL({
          type: 'png',
          pixelRatio: 2,
          backgroundColor: '#fff'
        });
        return [...this.$exportWord.exportTitle('相同行业分析'), ...this.$exportWord.exportChart(chart, { width, height })];
      } else {
        return [];
      }
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () { },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
</style>
