import request from '@/utils/request';

const server = '/Analysis';

// 搜索基金/基金经理

export function Search(params) {
	return request({
		url: server + '/Search/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {直投股票capm分析-基准列表} params
 * @returns
 */
export function getCapmBenchmark(params) {
	return request({
		url: server + '/CapmBenchmark/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {直投股票capm分析} params
 * @returns
 */
export function getCapmAnalysis(params) {
	return request({
		url: server + '/CapmAnalysis/',
		method: 'get',
		params
	});
}

/**
 *
 * @param {同类排名比较} params
 * @returns
 */
export function getFofMeasureSinceRank(params) {
	return request({
		url: server + '/FofMeasureSinceRank/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {fof相关系数}} params
 *  FofReturnRelevat/?code=006991
 * @returns
 */
export function getFofReturnRelevat(params) {
	return request({
		url: server + '/FofReturnRelevat/',
		method: 'get',
		params
	});
}
// 收益率分布直方图
export function getFundReturnSection(data) {
	return request({
		url: server + '/ReturnSummary/',
		method: 'post',
		data
	});
}
/**
 *
 * @param {动态4因子统计}} params
 *  /FactorAnalysis/?flag=1&type=equity&code=000001&start_date=&end_date=&status=summary
 * @returns
 */
export function getDynamicStatistics(params) {
	return request({
		url: server + '/FactorAnalysis/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {权益基金alpha/beta/smartbeta分解} params
 * @returns
 *  fund_code
 */
export function getFofAlphaRank(params) {
	return request({
		url: server + '/FofAlphaRank/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {权益基金标签分析}} params
 * @returns
 */
export function getFoFEquityTag(params) {
	return request({
		url: server + '/FoFEquityTag/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {权益基金股票分析}} params
 * @returns
 * fund_code
 */
export function getFoFHoldingNewest(params) {
	return request({
		url: server + '/FoFHoldingNewest/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {超额收益归因} params
 * /ExcessReturnDecomposition/?flag=1&code=110022&type=equity
 * @returns
 */
export function getExcessReturnDecomposition(params) {
	return request({
		url: server + '/ExcessReturnDecomposition/',
		method: 'get',
		params
	});
}

/**
 *
 * @param {报告期持仓统计指数列表} params
 * type
 * @returns
 */
export function getIndexList(params) {
	return request({
		url: server + '/BenchmarkList/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {报告期持仓统计指数收益} params
 * code
 * @returns
 */

export function getIndexReturn(data) {
	return request({
		url: server + '/RateInfo/',
		method: 'post',
		data
	});
}

export function getAllocationDetails(params) {
	return request({
		url: server + '/AllocationDetails/',
		method: 'get',
		params
	});
}
export function getFofAllocationDetails(params) {
	return request({
		url: server + '/FofAllocationDetails/',
		method: 'get',
		params
	});
}
// 基金公司大类资产配置
export function getCompanyAllocationDetails(params) {
	return request({
		url: server + '/Company/AllocationDetails/',
		method: 'get',
		params
	});
}
// 债券配置
export function getBondClassDetails(params) {
	return request({
		url: server + '/BondClassDetails/',
		method: 'get',
		params
	});
}

/**
 * 
 * @param {基础信息} params 
 *  基金,基金经理:
      code: str (基金 基金经理code)
      flag: int(是否为基金)
          1: fund
          2:manager
      type: str基金(基金经理类型)
    基金公司:
      code:str (基金公司)
 * @returns 
 */

// 获取基金/基金经理基础信息
export function getReturnInfo(params) {
	return request({
		url: server + '/ReturnInfo/',
		method: 'get',
		params
	});
}
// 获取基金基础信息
export function getBasicInfo(params) {
	return request({
		url: server + '/BasicInfo/',
		method: 'get',
		params
	});
}
// 基金/基金经理能力项
export function getCapabilityInfo(data) {
	return request({
		url: server + '/CapabilityInfo/',
		method: 'post',
		data
	});
}
// 基金经理基础信息
// code
export function getTypeInfo(params) {
	return request({
		url: server + '/TypeInfo/',
		method: 'get',
		params
	});
}

/**
 * 
 * @param {收益曲线}} params 
 *  code: str (基金 基金经理 基金公司代码)
    flag: int (是否为基金 基金经理基金公司)
        1: fund
        2: manager
        3: company
    type: str(类型)
    start_date: date (起始时间)
    end_date: date (截止时间)
    benchmark: list (基准, 特殊基准见备注)
 * @returns 
 */

// 获取基金/基金经理/基金公司收益曲线
export function getRateInfo(data) {
	return request({
		url: server + '/RateInfo/',
		method: 'post',
		data
	});
}
// 获取基金收益曲线基准列表
export function getBenchmarkList(params) {
	return request({
		url: server + '/BenchmarkList/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {因子暴露与收益} params
 * @returns
 * code type flag start_date end_date
 */
export function getBarraInfo(params) {
	return request({
		url: server + '/BarraInfo/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {基金持仓分析} params
 * @returns
 * yearqtr fund_code
 */
export function getFofHoldFund(params) {
	return request({
		url: server + '/FofHoldFund/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {产品分析} params
 * @returns
 * fund_code,type,index_code,start_date,end_date
 */
export function getFofImmediateAsset(params) {
	return request({
		url: server + '/FofImmediateAsset/',
		method: 'get',
		params
	});
}

/**
 *
 * @param {行业配置表现} params
 *  { flag: 2, code: this.manager_code, type: 'activeequity', industry_section: '申万(2021)' }
 * @returns
 */

// 获取基金/基金经理 行业评价数据
export function getIndustryInfo(params) {
	return request({
		url: server + '/IndustryInfo/',
		method: 'get',
		params
	});
}
export function getIndustryInfoCompany(params) {
	return request({
		url: server + '/Company/IndustryDetails/',
		method: 'get',
		params
	});
}

/**
 *
 * @param {行业评价数据} params
 *  /IndustryAppraise/?flag=1&code=110022&type=equity&feature_flag=1
 * @returns
 */

// 获取基金/基金经理 行业评价数据
export function getIndustryDetlaCapability(params) {
	return request({
		url: server + '/IndustryAppraise/',
		method: 'get',
		params
	});
}

/**
 *
 * @param {最新报告持仓} params
 *  /HoldStocks/?flag=2&code=30189741&type=equity&start_date&end_date
 * @returns
 */

// 获取基金/基金经理 行业评价数据
export function getHoldStocks(params) {
	return request({
		url: server + '/HoldStocks/',
		method: 'get',
		params
	});
}

/**
 *
 * @param {短期流动性管理} params
 *  fund_code
 * @returns
 */
export function getFofLiquidity(params) {
	return request({
		url: server + '/FofLiquidity/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {最新各类型基金配置情况}} params
 * @returns
 */
export function getFofAllocationMsg(params) {
	return request({
		url: server + '/FofAllocationMsg/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {PB_ROE特征}} params
 * @returns
 */
export function getCharacteristics(params) {
	return request({
		url: server + '/LongHoldStocksStat/',
		method: 'get',
		params
	});
}

/**
 *
 * @param {基金/基金经理表现风格} params
 * @returns
 *  code: str (基金 基金经理代码)
    flag : int (是否为基金)
        1: fund
        2: manager
    type: str (类型)
 */

// 获取基金/基金经理表现风格
export function getPerformanceStyle(params) {
	return request({
		url: server + '/EquityRecentStyle/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {持仓债券分析} params
 *
 * @returns
 */
export function getBondAnalysise(params) {
	return request({
		url: server + '/BondHoldingMsg/',
		method: 'get',
		params
	});
}

/**
 *
 * @param {获取持仓季度列表} params
 *  /DateList/
 * @returns
 */
export function getDateList(params) {
	return request({
		url: server + '/DateList/',
		method: 'get',
		params
	});
}

/**
 *
 * @param {买入卖出模式} params
 * /BuyOrSellModInfo/?flag=1&type=equity&code=000001&start_date=&end_date=&status=buy
 * status = buy (买入模式)
 * status = sell (卖出模式)
 * @returns
 */

// 获取基金/基金经理 行业评价数据
export function getBuyOrSellModInfo(params) {
	return request({
		url: server + '/BuyOrSellModInfo/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {股票风格特征}} params
 * /CreditDownRation/?flag=1&code=000045&type=bond
 * @returns
 */
export function getCharacteristicsBarra(params) {
	return request({
		url: server + '/factorexposed/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {风格择时能力}} params
 * /TimmingStyle/?flag=1&type=bond&code=000045
 * @returns
 */
export function getStyleTiming(params) {
	return request({
		url: server + '/TimmingStyle/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {分时段业绩表现} params
 * lag=1&code=110022&type=equity&start_date&end_date&periodname=股票牛熊市场
 * @returns
 */
export function getMarketWindowReturnV2(params) {
	return request({
		url: server + '/MarketWindowReturnV2/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {分时段业绩表现} params
 * lag=1&code=110022&type=equity&start_date&end_date&periodname=股票牛熊市场
 * @returns
 */
export function getMarketWindowReturn(params) {
	return request({
		url: server + '/MarketWindowReturn/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {基准概念列表} params
 * @returns
 */
export function getFundPeriod(params) {
	return request({
		url: server + '/MacroPeriod/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {交易风格-集中度} params
 * /ConcentrationInfo/?flag=2&code=30189741&type=equity&start_date&end_date
 * @returns
 */
export function getConcentrationInfo(params) {
	return request({
		url: server + '/ConcentrationInfo/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {交易风格-换手率} params
 * /ConcentrationInfo/?flag=2&code=30189741&type=equity&start_date&end_date
 * @returns
 */
export function getTurnoverInfo(params) {
	return request({
		url: server + '/TurnoverInfo/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {What-if 假想验证：调仓时的资产配置作对了吗} params
 * @returns
 */
export function getWhatIf(params) {
	return request({
		url: server + '/imaginarynav/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {调仓节奏} params
 * @returns
 */
export function getImaginaryNav(params) {
	return request({
		url: server + '/ImaginaryNav/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {持有压力} params
 *  flag=1&code=110022&type=equity&start_date&end_date&benchmark=0
 * @returns
 */

export function getHoldPressureInfo(params) {
	return request({
		url: server + '/HoldPressureInfo/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {持有压力基准列表} params
 *  code
 * @returns
 */
export function getAnalysisIndex(params) {
	return request({
		url: server + '/analysisindex/',
		method: 'get',
		params
	});
}

/**
 *
 * @param {长期持有个股} params
 * /StocksLongHold/?flag=1&code=110022&type=equity
 * @returns
 */

export function getStocksLongHold(params) {
	return request({
		url: server + '/StocksLongHold/',
		method: 'get',
		params
	});
}

/**
 *
 * @param {长期持有个股-基金个股详情} params
 * stock_code='  '&code='   '&type
 * @returns
 */

export function getStocksDetail(params) {
	return request({
		url: server + '/StockInfo/',
		method: 'get',
		params
	});
}

/**
 *
 * @param {持仓风格} params
 * /HoldStyle/?flag=1&code=110022&type=equity&method=A股价值成长大小盘
 * type = [bond, purebond, bill, cbond] -- method = '多券种组合'
 * @returns
 */

export function getHoldStyle(params) {
	return request({
		url: server + '/HoldStyle/',
		method: 'get',
		params
	});
}

/**
 *
 * @param {风险收益指标} params
 *  code: str(基金代码)
 *  type: str(基金类型)
 *  flag: str(基金/基金经理)
 * @returns
 */
export function getRiskFeatureYearly(params) {
	return request({
		url: server + '/RiskFeatureYearly/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {风险收益关系} params
 *  code: str(基金代码)
 *  type: str(基金类型)
 *  flag: str(基金/基金经理)
 * @returns
 */
export function getRiskFeatureRecent(params) {
	return request({
		url: server + '/RiskFeatureRecent/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {规模及持有人结构} params
 *  code: str (基金 基金经理代码)
    flag : int (是否为基金)
        1: fund
        2: manager
 * @returns
 * {
    "mtycode": 200,
    "mtymessage": "success",
    "data":{
         industry_name: [],
         industry_alpha: [],
         industry_top_weight: [],
         industry_bottom_weight: [],
         industry_middle_weight: []
        }
    }
 */

// 获取基金 规模及持有人结构
export function getHoldInfo(params) {
	return request({
		url: server + '/HoldInfo/',
		method: 'get',
		params
	});
}

/**
 *
 * @param {风格标签} params
 * /StyleInfo/?flag=1&code=110022&type=equity&start_date
 * @returns
 */

export function getStyleInfo(params) {
	return request({
		url: server + '/StyleInfo/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {TM模型分析}} params
 * @returns
 */
export function getTMStatistics(params) {
	return request({
		url: server + '/TMCapability/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {前十大的进击}} params
 * @returns
 */
export function getTopTenAttacks(params) {
	return request({
		url: server + '/ImaginaryExcess/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {前十大的进击}} params
 * @returns
 */
export function getTopTenAlpha(params) {
	return request({
		url: server + '/alphatre/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {行业能力} params
 * IndustryRankInfo/?flag=1&code=110022&type=equity&industry_section
 * @returns
 */
export function getIndustryRankInfo(params) {
	return request({
		url: server + '/IndustryRankInfo/',
		method: 'get',
		params
	});
}
// 风格能力图compare/ManagerIndustryCapability/?type=equity&flag=2&manager_code=30189741,30189744&yearqtr=2020 Q2&manager_name=hbj,giu
export function getManagerMarketCapability(params) {
	return request({
		url: server + '/MarketRankInfo/',
		method: 'get',
		params
	});
}
// FundMarketCapability
export function getFundMarketCapability(params) {
	return request({
		url: server + '/MarketRankInfo/',
		method: 'get',
		params
	});
}
// 权益持仓风格
export function getHoldStocksStyle(params) {
	return request({
		url: server + '/HoldStocksStyle/',
		method: 'get',
		params
	});
}
// Brinson归因
export function getMultiBrision(params) {
	return request({
		url: server + '/MultiBrision/',
		method: 'get',
		params
	});
}
// 组合整体财务指标
export function getStockFinanceInfo(params) {
	return request({
		url: server + '/StockFinanceInfo/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {债基风格-纯债}} params
 * @returns
 */
export function getPurebondStyleYearlyInfo(params) {
	return request({
		url: server + '/PurebondStyleYearlyInfo/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {债基风格-固收+} params
 * @returns
 */
export function getBondStyleYearlyInfo(params) {
	return request({
		url: server + '/BondStyleYearlyInfo/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {近期风格}} params
 * @returns
 */

export function getRecentStyleInfo(params) {
	return request({
		url: server + '/RecentStyleInfo/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {获取固收+加法描述} params
 * @returns
 */

export function getBondDes(params) {
	return request({
		url: server + '/BondStyle/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {市场风格表现}} params
 * @returns
 */

export function getMarketStyleBYlan(params) {
	return request({
		url: server + '/MacroPeriodMeasure/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {基金经理评价} params
 * @returns
 */
export function getEvaluateManagers(params) {
	return request({
		url: server + '/ManagedFundsVolReturnInfo/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {基金经理管理产品业绩表现} params
 * @returns
 */
export function getManagedFundsAllReturn(params) {
	return request({
		url: server + '/ManagedFundsAllReturn/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {基金经理管理产品列表} params
 * @returns
 */
export function getManagedFunds(params) {
	return request({
		url: server + '/ManagedFunds/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {估值分析} params
 *  /StocksMarketValuation/?flag=1&code=110022&type=equity
 * @returns
 */

export function getValuationAnalysis(params) {
	return request({
		url: server + '/StocksMarketValuation/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {PB-ROE估值} params
 *  /StocksValuationInfo/?flag=1&code=110022&type=equity
 * @returns
 */

export function getStocksValuationInfo(params) {
	return request({
		url: server + '/StocksValuationInfo/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {持股加权估值水平} params
 * /StockHistoryHold/?flag=1&code=110022&type=equity&feature_flag=4
 * @returns
 */
export function getStockHistoryHold(params) {
	return request({
		url: server + '/StockHistoryHold/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {久期拉长} params
 * /DurationAnalysisInfo/?flag=1&type=equity&code=000045&start_date=&end_date=
 * @returns
 */

export function getDurationAnalysisInfo(params) {
	return request({
		url: server + '/DurationAnalysisInfo/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {信用挖掘图}} params
 * /CreditDownRatio/?flag=1&code=000045&type=bond
 * @returns
 */
export function getCreditDownRation(params) {
	return request({
		url: server + '/CreditDownRatio/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {信用挖掘表}} params
 * /CreditLevel/?flag=1&code=000045&type=bond
 * @returns
 */
export function getCreditLevel(params) {
	return request({
		url: server + '/CreditLevel/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {转债季度信息} params
 * @returns
 */
export function getBondQuarterInfo(params) {
	return request({
		url: server + '/BondQuarterInfo/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {固收PLUS} params
 * @returns
 */
export function getCbondReturns(params) {
	return request({
		url: server + '/CbondReturns/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {转债正股风格} params
 * @returns
 */
export function getcbondStyleData(params) {
	return request({
		url: server + '/getcbondStyleData/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {转债股性债性}} params
 * /TimmingStyle/?flag=1&type=bond&code=000045
 * @returns
 */
export function getcbondHoldEquityBond(params) {
	return request({
		url: server + '/ConvertpremiumrateInfo/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {基金收益来源分析} params
 * fund_code
 * @returns
 */
export function getProfitAnalysis(params) {
	return request({
		url: server + '/ProfitAnalysis/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {杠杆水平} params
 * @returns
 */
export function getLever(params) {
	return request({
		url: server + '/lever/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {平均剩余期限} params
 * @returns
 */
export function getTerm(params) {
	return request({
		url: server + '/term/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {非交易日变动} params
 * @returns
 */
export function getUntradday(params) {
	return request({
		url: server + '/untradday/',
		method: 'get',
		params
	});
}
