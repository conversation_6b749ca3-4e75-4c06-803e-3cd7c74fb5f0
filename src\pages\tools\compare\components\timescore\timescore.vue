<!--  -->
<template>
  <div v-loading="loading">
    <div style="display: flex; align-items: center; width: 100%; position: relative; justify-content: space-between">
      <div style="display: flex; align-items: center">
        <div class="TitltCompare">风格与能力</div>
      </div>
      <div>
        <el-button @click="show = !show"
                   type="">{{ show ? '合并' : '拆分' }}</el-button>
      </div>
    </div>
    <div style="margin-top: 16px">
      <sTable :data="fund_hold"
              typeFlag="1"></sTable>
    </div>
    <div style="min-width: 300px"
         v-if="!show">
      <v-chart ref="marketScoreAll"
               v-loading="empty2"
               autoresize
               element-loading-text="暂无数据"
               element-loading-spinner="el-icon-document-delete"
               element-loading-background="rgba(239, 239, 239, 0.5)"
               style="width: 100%; height: 500px; margin-top: 16px; page-break-inside: avoid"
               :options="options2" />
    </div>
    <div v-if="show"
         style="display: flex; flex-wrap: wrap">
      <div :style="inwidth > 1743 ? ' flex: 1; min-width: 300px; margin-right: 24px' : 'min-width:450px;flex:1; margin-right: 24px'"
           v-if="JSON.stringify(options2_1) != '{}'">
        <v-chart v-loading="empty2"
                 autoresize
                 element-loading-text="暂无数据"
                 element-loading-spinner="el-icon-document-delete"
                 element-loading-background="rgba(239, 239, 239, 0.5)"
                 style="width: 100%; height: 500px; margin-top: 16px; page-break-inside: avoid"
                 :options="options2_1" />
      </div>
      <div :style="inwidth > 1743 ? ' flex: 1; min-width: 300px; margin-right: 24px' : 'min-width:450px;flex:1; margin-right: 24px'"
           v-if="JSON.stringify(options2_2) != '{}'">
        <v-chart v-loading="empty2"
                 autoresize
                 element-loading-text="暂无数据"
                 element-loading-spinner="el-icon-document-delete"
                 element-loading-background="rgba(239, 239, 239, 0.5)"
                 style="width: 100%; height: 500px; margin-top: 16px; page-break-inside: avoid"
                 :options="options2_2" />
      </div>
      <div :style="inwidth > 1743 ? ' flex: 1; min-width: 300px; margin-right: 24px' : 'min-width:450px;flex:1; margin-right: 24px'"
           v-if="JSON.stringify(options2_3) != '{}'">
        <v-chart v-loading="empty2"
                 autoresize
                 element-loading-text="暂无数据"
                 element-loading-spinner="el-icon-document-delete"
                 element-loading-background="rgba(239, 239, 239, 0.5)"
                 style="width: 100%; height: 500px; margin-top: 16px; page-break-inside: avoid"
                 :options="options2_3" />
      </div>
      <div :style="inwidth > 1743 ? ' flex: 1; min-width: 300px' : 'min-width:450px;flex:1'"
           v-if="JSON.stringify(options2_4) != '{}'">
        <v-chart v-loading="empty2"
                 autoresize
                 element-loading-text="暂无数据"
                 element-loading-spinner="el-icon-document-delete"
                 element-loading-background="rgba(239, 239, 239, 0.5)"
                 style="width: 100%; height: 500px; margin-top: 16px; page-break-inside: avoid"
                 :options="options2_4" />
      </div>
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { ManagerMarketCapability, ManagerStyleClass, FundStyleClass, FundMarketCapability } from '@/api/pages/tools/compare.js';
import sTable from '../SelfTable.vue';
import VCharts from 'vue-echarts';
export default {
  //import引入的组件需要注入到对象中才能使用
  components: { 'v-chart': VCharts, sTable },
  props: {
    comparetype: {
      type: String,
      default: 'manager' //fund
    },
    id: {
      type: String,
      default: '30189741,30441407'
    },
    type: {
      type: String,
      default: 'equity'
    },
    name: {
      type: String,
      default: '萧楠,胡昕炜'
    }
  },
  data () {
    //这里存放数据
    return {
      show: false,
      loading: false,
      options2: {},
      empty2: false,
      inwidth: window.innerWidth,
      options2_1: {},
      options2_2: {},
      options2_3: {},
      options2_4: {},
      managerholdcolumns: [
        { dataIndex: 'manager_name', key: 'manager_name', title: '基金经理' },
        { dataIndex: 'bigsmall', key: 'bigsmall', title: '大小盘' },
        { dataIndex: 'valuegrowth', key: 'valuegrowth', title: '成长价值' },
        { dataIndex: 'industrysector', key: 'industrysector', title: '大行业' }
      ],
      fund_hold: [],
      fundholdcolumns: [
        { dataIndex: 'code', key: 'code', title: '基金', scopedSlots: { customRender: 'code' } },
        { dataIndex: 'bigsmall', key: 'bigsmall', title: '大小盘' },
        { dataIndex: 'valuegrowth', key: 'valuegrowth', title: '成长价值' },
        { dataIndex: 'industrysector', key: 'industrysector', title: '大行业' }
      ]
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    getdata () {
      Object.assign(this.$data, this.$options.data());
      this.loading = true;
      this.empty2 = false;
      if (this.comparetype == 'manager') {
        this.getmanagerdata();
        this.gettable();
      } else {
        this.gefunddata();
        this.getfundtable();
      }
    },
    // 基金大小成长
    async getfundtable () {
      this.fund_hold = [];
      let data = await FundStyleClass({
        fund_code: this.id,
        type: this.type,
        fund_name: this.name
      });
      if (data) {
        data.data.sort((a, b) => {
          if (this.$route.query.id.split(',').indexOf(a.code) > this.$route.query.id.split(',').indexOf(b.code)) return 1;
          else return -1;
        });
        this.fund_hold = [['大小盘'], ['成长价值'], ['大行业']];
        for (let i = 0; i < data.data.length; i++) {
          this.fund_hold[0].push(data.data[i].bigsmall);
          this.fund_hold[1].push(data.data[i].valuegrowth);
          this.fund_hold[2].push(data.data[i].industrysector);
        }
      }
    },
    // 基金经理大小成长
    async gettable () {
      this.fund_hold = [];
      let data = await ManagerStyleClass({
        manager_code: this.id,
        type: this.type,
        manager_name: this.name
      });
      if (data) {
        data.data.sort((a, b) => {
          if (this.$route.query.id.split(',').indexOf(a.code) > this.$route.query.id.split(',').indexOf(b.code)) return 1;
          else return -1;
        });
        this.fund_hold = [['大小盘'], ['成长价值'], ['大行业']];
        for (let i = 0; i < data.data.length; i++) {
          this.fund_hold[0].push(data.data[i].bigsmall);
          this.fund_hold[1].push(data.data[i].valuegrowth);
          this.fund_hold[2].push(data.data[i].industrysector);
        }
      }
    },
    async getmanagerdata () {
      let color = ['#4096ff', '#4096ff', '#7388A9', '#6F80DD', '#4096FF', '#e040fb', '#ff3d00'];
      let data = await ManagerMarketCapability({
        manager_code: this.id,
        type: this.type,
        manager_name: this.name
      });
      this.loading = false;

      if (data) {
        if (JSON.stringify(data.data) == '{}' || JSON.stringify(data.data) == '[]' || JSON.stringify(data.data) == '') {
          this.empty2 = true;
        } else {
          // //console.log(data)
          // //console.log('ability')
          let tempindustry = [];
          for (let i = 0; i < data.data.description.length; i++) {
            tempindustry.push({ name: data.data.description[i], max: 1 });
          }
          let arrdata = [];
          let names = this.name.split(',');
          for (let i = 0; i < names.length; i++) {
            if (data.data[names[i]]) {
              arrdata.push({
                value: data.data[names[i]],
                name: names[i],
                itemStyle: {
                  // 单个拐点标志的样式设置。
                  normal: {
                    borderColor: color[i],
                    // 拐点的描边颜色。[ default: '#000' ]
                    borderWidth: 3
                    // 拐点的描边宽度，默认不描边。[ default: 0 ]
                  }
                },
                lineStyle: {
                  // 单项线条样式。
                  normal: {
                    color: color[i],
                    opacity: 0.5 // 图形透明度
                  }
                },
                areaStyle: {
                  // 单项区域填充样式
                  normal: {
                    color: color[i], // 填充的颜色。[ default: "#000" ]
                    opacity: 0.7
                  }
                }
              });
            }
          }
          this.options2 = {
            color: color,
            legend: {},
            radar: {
              splitLine: {
                // (这里是指所有圆环)坐标轴在 grid 区域中的分隔线。
                lineStyle: {
                  color: '#CFE5F3',
                  opacity: 0.3,
                  // 分隔线颜色
                  width: 2
                  // 分隔线线宽
                }
              },
              splitArea: {
                // 坐标轴在 grid 区域中的分隔区域，默认不显示。
                show: true,
                areaStyle: {
                  // 分隔区域的样式设置。
                  color: ['rgb(246,250,255)', 'rgb(246,250,255)']
                  // 分隔区域颜色。分隔区域会按数组中颜色的顺序依次循环设置颜色。默认是一个深浅的间隔色。
                }
              },
              axisLine: {
                // (圆内的几条直线)坐标轴轴线相关设置
                lineStyle: {
                  color: '#7ab1ff',
                  // 坐标轴线线的颜色。
                  width: 0.3,
                  // 坐标轴线线宽。
                  type: 'solid'
                  // 坐标轴线线的类型。
                }
              },

              // shape: 'circle',
              indicator: tempindustry
            },
            // tooltip: {
            // 	trigger: 'axis',
            // 	// axisPointer: {
            // 	//     type: 'cross'
            // 	// },
            // 	formatter(params) {
            // 		// ////console.log(params)
            // 		return '风格：' + params[0].axisValue + ' ,' + params[0].data.toFixed(3);
            // 	}
            // },
            series: [
              {
                itemStyle: {
                  // 单个拐点标志的样式设置。
                  normal: {
                    borderColor: '#4096FF',
                    // 拐点的描边颜色。[ default: '#000' ]
                    borderWidth: 3
                    // 拐点的描边宽度，默认不描边。[ default: 0 ]
                  }
                },
                lineStyle: {
                  // 单项线条样式。
                  normal: {
                    color: '#4096FF',
                    opacity: 0.5 // 图形透明度
                  }
                },
                areaStyle: {
                  // 单项区域填充样式
                  normal: {
                    color: 'rgb(223,236,255)', // 填充的颜色。[ default: "#000" ]
                    opacity: 0.7
                  }
                },
                name: '基金经理能力',
                type: 'radar',
                data: arrdata
              }
            ]
          };
          for (let i = 0; i < this.$route.query.name.split(',').length; i++) {
            let temp = [];
            if (this.options2.series[0].data.findIndex((item) => item.name == this.$route.query.name.split(',')[i]) >= 0) {
              temp = [
                this.options2.series[0].data[
                this.options2.series[0].data.findIndex((item) => item.name == this.$route.query.name.split(',')[i])
                ]
              ];
            }
            this['options2_' + (i + 1)] = JSON.parse(
              JSON.stringify({
                color: this.options2.color,
                radar: this.options2.radar,
                series: this.options2.series,
                title: {
                  text:
                    this.$route.query.name.split(',')[i].length > 10
                      ? this.$route.query.name.split(',')[i].slice(0, 8) + '...'
                      : this.$route.query.name.split(',')[i],
                  fontSize: 16
                }
              })
            );

            this['options2_' + (i + 1)].series[0].data = temp;
          }
        }
      } else {
        this.empty2 == true;
      }
    },
    async gefunddata () {
      let color = ['#4096ff', '#4096ff', '#7388A9', '#6F80DD', '#4096FF', '#e040fb', '#ff3d00'];
      let data = await FundMarketCapability({
        fund_code: this.id,
        type: this.type,
        fund_name: this.name
      });
      this.loading = false;

      if (data) {
        if (JSON.stringify(data.data) == '{}' || JSON.stringify(data.data) == '[]' || JSON.stringify(data.data) == '') {
          this.empty2 = true;
        } else {
          // //console.log(data)
          // //console.log('ability')
          let tempindustry = [];
          for (let i = 0; i < data.data.description.length; i++) {
            tempindustry.push({ name: data.data.description[i], max: 1 });
          }
          let arrdata = [];
          let names = this.name.split(',');
          for (let i = 0; i < names.length; i++) {
            if (data.data[names[i]]) {
              arrdata.push({
                value: data.data[names[i]],
                name: names[i],
                itemStyle: {
                  // 单个拐点标志的样式设置。
                  normal: {
                    borderColor: color[i],
                    // 拐点的描边颜色。[ default: '#000' ]
                    borderWidth: 3
                    // 拐点的描边宽度，默认不描边。[ default: 0 ]
                  }
                },
                lineStyle: {
                  // 单项线条样式。
                  normal: {
                    color: color[i],
                    opacity: 0.5 // 图形透明度
                  }
                },
                areaStyle: {
                  // 单项区域填充样式
                  normal: {
                    color: color[i], // 填充的颜色。[ default: "#000" ]
                    opacity: 0.7
                  }
                }
              });
            }
          }
          this.options2 = {
            color: color,
            legend: {},
            radar: {
              splitLine: {
                // (这里是指所有圆环)坐标轴在 grid 区域中的分隔线。
                lineStyle: {
                  color: '#CFE5F3',
                  opacity: 0.3,
                  // 分隔线颜色
                  width: 2
                  // 分隔线线宽
                }
              },
              splitArea: {
                // 坐标轴在 grid 区域中的分隔区域，默认不显示。
                show: true,
                areaStyle: {
                  // 分隔区域的样式设置。
                  color: ['rgb(246,250,255)', 'rgb(246,250,255)']
                  // 分隔区域颜色。分隔区域会按数组中颜色的顺序依次循环设置颜色。默认是一个深浅的间隔色。
                }
              },
              axisLine: {
                // (圆内的几条直线)坐标轴轴线相关设置
                lineStyle: {
                  color: '#7ab1ff',
                  // 坐标轴线线的颜色。
                  width: 0.3,
                  // 坐标轴线线宽。
                  type: 'solid'
                  // 坐标轴线线的类型。
                }
              },

              // shape: 'circle',
              indicator: tempindustry
            },
            // tooltip: {
            // 	trigger: 'axis',
            // 	// axisPointer: {
            // 	//     type: 'cross'
            // 	// },
            // 	formatter(params) {
            // 		// ////console.log(params)
            // 		return '风格：' + params[0].axisValue + ' ,' + params[0].data.toFixed(3);
            // 	}
            // },
            series: [
              {
                itemStyle: {
                  // 单个拐点标志的样式设置。
                  normal: {
                    borderColor: '#4096FF',
                    // 拐点的描边颜色。[ default: '#000' ]
                    borderWidth: 3
                    // 拐点的描边宽度，默认不描边。[ default: 0 ]
                  }
                },
                lineStyle: {
                  // 单项线条样式。
                  normal: {
                    color: '#4096FF',
                    opacity: 0.5 // 图形透明度
                  }
                },
                areaStyle: {
                  // 单项区域填充样式
                  normal: {
                    color: 'rgb(223,236,255)', // 填充的颜色。[ default: "#000" ]
                    opacity: 0.7
                  }
                },
                name: '基金经理能力',
                type: 'radar',
                data: arrdata
              }
            ]
          };
          for (let i = 0; i < this.$route.query.name.split(',').length; i++) {
            let temp = [];
            if (this.options2.series[0].data.findIndex((item) => item.name == this.$route.query.name.split(',')[i]) >= 0) {
              temp = [
                this.options2.series[0].data[
                this.options2.series[0].data.findIndex((item) => item.name == this.$route.query.name.split(',')[i])
                ]
              ];
            }
            this['options2_' + (i + 1)] = JSON.parse(
              JSON.stringify({
                color: this.options2.color,
                radar: this.options2.radar,
                series: this.options2.series,
                title: {
                  text:
                    this.$route.query.name.split(',')[i].length > 10
                      ? this.$route.query.name.split(',')[i].slice(0, 8) + '...'
                      : this.$route.query.name.split(',')[i],
                  fontSize: 16
                }
              })
            );

            this['options2_' + (i + 1)].series[0].data = temp;
          }
        }
      } else {
        this.empty2 == true;
      }
    },
    createPrintWord () {
      return [...this.$exportWord.exportTitle('卖出模式'), ...this.$exportWord.exportCompareTable(data2, [], true)];
    },
    async createPrintWord () {
      this.show = false;
      let name = this.name.split(',');
      let table = [['', ...name], ...this.fund_hold];
      let data = [];
      await this.$nextTick(() => {
        let height = this.$refs['marketScoreAll']?.$el.clientHeight;
        let width = this.$refs['marketScoreAll']?.$el.clientWidth;
        let chart = this.$refs['marketScoreAll'].getDataURL({
          type: 'png',
          pixelRatio: 2,
          backgroundColor: '#fff'
        });
        data = [...this.$exportWord.exportTitle('市场适应性'), ...this.$exportWord.exportChart(chart, { width, height })];
      });
      return [...this.$exportWord.exportTitle('风格与能力'), ...this.$exportWord.exportCompareTable(table, [], true), ...data];
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () { },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
</style>
