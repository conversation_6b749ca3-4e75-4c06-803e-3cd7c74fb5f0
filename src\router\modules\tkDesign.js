export default [
	{
		path: '/managedInvest',
		component: () => import(/* webpackChunkName: "tkDesign" */ '../../pages/tkdesign/postinvestmentManagement/indexBoard.vue'),
		meta: { title: '分析对象管理', tagShow: false }
	},
	{
		path: '/industryMap',
		component: () => import(/* webpackChunkName: "tkDesign" */ '../../pages/tkdesign/postinvestmentManagement/indexBoardIndustryMap.vue'),
		meta: { title: '行业映射管理列表', tagShow: false }
	},
	{
		path: '/productMap',
		component: () => import(/* webpackChunkName: "tkDesign" */ '../../pages/tkdesign/postinvestmentManagement/indexBoardProductMap.vue'),
		meta: { title: '产品映射管理列表', tagShow: false }
	},
	{
		path: '/captial-market-conditions',
		component: () =>
			import(/* webpackChunkName: "captialMarketConditions" */ '../../pages/tkdesign/marketAnalysis/capitalMarketConditions.vue'),
		meta: { title: '资本市场分析', tagShow: false }
	},
	{
		path: '/fund-market-performance',
		component: () =>
			import(/* webpackChunkName: "fundMarketPerformance" */ '../../pages/tkdesign/marketAnalysis/fundMarketPerformance.vue'),
		meta: { title: '基金市场业绩', tagShow: false }
	},
	{
		path: '/fund-market-allocation',
		component: () => import(/* webpackChunkName: "fundMarketAllocation" */ '../../pages/tkdesign/marketAnalysis/fundMarketAllocation.vue'),
		meta: { title: '基金市场配置', tagShow: false }
	},
	{
		path: '/portfolioList',
		component: () => import(/* webpackChunkName: "portfolioList" */ '../../pages/tkdesign/newPortfolio/index.vue'),
		meta: { title: '模拟组合管理', tagShow: false }
	},
	{
		path: '/portfolioDetail',
		component: () => import(/* webpackChunkName: "portfolioDetail" */ '../../pages/tkdesign/newPortfolio/portfolioDetail.vue'),
		meta: { title: '组合详情', tagShow: false }
	},
	{
		path: '/configurationStrategySteps',
		component: () => import(/* webpackChunkName: "portfolioOptimize" */ '../../pages/tkdesign/newPortfolio/portfolioOptimize.vue'),
		meta: { title: '配置策略研究', tagShow: false }
	},
	{
		path: '/combinationStrategySteps',
		component: () => import(/* webpackChunkName: "portfolioOptimize" */ '../../pages/tkdesign/newPortfolio/portfolioOptimize.vue'),
		meta: { title: '组合策略研究', tagShow: false }
	},
	{
		path: '/configurationStrategyIndex',
		component: () => import(/* webpackChunkName: "portfolioOptimize" */ '../../pages/tkdesign/newPortfolio/optimizeIndex.vue'),
		meta: { title: '配置策略研究', tagShow: false }
	},
	{
		path: '/portfolioStudyIndex',
		component: () => import(/* webpackChunkName: "portfolioOptimize" */ '../../pages/tkdesign/newPortfolio/portfolioStudyIndex.vue'),
		meta: { title: '组合策略研究', tagShow: false }
	}
];
