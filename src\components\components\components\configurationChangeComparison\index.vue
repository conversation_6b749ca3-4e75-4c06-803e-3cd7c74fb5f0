<template>
	<div class="chart_one" v-show="show">
		<div style="display: flex; justify-content: space-between; align-items: center">
			<div class="title">配置变化对比</div>
			<div class="select-list">
				<span class="select-label" style="margin-left: 24px">目标季度: </span>
				<el-cascader v-model="targetQuarter" :options="quarterList" separator=" " @change="getIndustryChange"></el-cascader>
				<span class="select-label" style="margin-left: 24px">对比季度: </span>
				<el-cascader v-model="contrastQuarter" :options="quarterList" separator=" " @change="getIndustryChange"></el-cascader>
				<span class="select-label" style="margin-left: 24px">比较类型: </span>
				<el-cascader v-model="contrastType" :options="contrastTypeList" @change="getIndustryChange"></el-cascader>
			</div>
		</div>

		<div style="margin-top: 24px" class="config-change-table-list">
			<el-table
				v-loading="compareQuarterLoading"
				:data="compareQuarterData"
				class="table-box"
				max-height="400px"
				row-key="stock_code"
				:tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
			>
				<el-table-column :label="contrastType == 'stock' ? '个股名称' : '行业名称'" prop="name" align="gotoleft"></el-table-column>
				<el-table-column :label="contrastType == 'stock' ? '个股配置' : '行业配置'" prop="weight">
					<template slot-scope="{ row }">
						<span class="amount-font-color">{{ row.weight == '--' || !row.weight ? '--' : Number(row.weight).toFixed(4) }}%</span>
					</template>
				</el-table-column>
				<el-table-column label="较上期变化" prop="change_weight">
					<template slot-scope="{ row }">
						<span class="amount-font-color"
							>{{ row.change_weight == '--' || !row.change_weight ? '--' : Number(row.change_weight).toFixed(4) }}%</span
						>
					</template>
				</el-table-column>
			</el-table>
		</div>
	</div>
</template>

<script>
// 配置变化对比
export default {
	name: 'configurationChangeComparison',
	data() {
		return {
			targetQuarter: '',
			contrastQuarter: '',
			contrastType: ['stock'],
			compareQuarterLoading: true,
			compareQuarterData: [],
			quarterList: [],
			contrastTypeList: [
				{ label: '个股', value: 'stock' },
				{ label: '行业', value: 'industry' }
			],
			show: true
		};
	},
	methods: {
		// 获取时间
		getDateList(val) {
			if (!val.length) {
				this.generateQuarterList();
			}
			this.resolveFather();
		},
		// 向父组件传递数据
		resolveFather() {
			console.log({
				flag: this.contrastType,
				now_yearqtr: this.contrastQuarter,
				target_yearqtr: this.targetQuarter
			});
			this.$emit('resolveFather', {
				flag: this.contrastType[0],
				now_yearqtr: this.contrastQuarter.join(' '),
				target_yearqtr: this.targetQuarter.join(' ')
			});
		},
		// 在给定时间区间内生成连续季度
		generateQuarterList() {
			let option = [];
			let qList = ['Q1', 'Q2', 'Q3', 'Q4'];
			let pre = '2006-09-10';
			let now = this.FUNC.transformDate(new Date());
			let preYear = pre.slice(0, 4);
			let nowYear = now.slice(0, 4);
			let preQ = this.FUNC.dateToQuarter(pre).slice(5);
			let nowQ = this.FUNC.dateToQuarter(now).slice(5);
			let yList = Array.from({ length: Math.abs(nowYear - preYear + 1) }, (item, index) => (item = parseInt(preYear) + index));
			for (let y of yList) {
				let yobj = {
					value: y,
					label: y,
					children: []
				};
				if (y == preYear) {
					qList.forEach((q) => {
						if (q >= preQ) {
							yobj.children.push({ value: q, label: q });
						}
					});
				} else if (y == nowYear) {
					qList.forEach((q) => {
						if (q <= nowQ) {
							yobj.children.push({ value: q, label: q });
						}
					});
				} else {
					qList.forEach((q) => yobj.children.push({ value: q, label: q }));
				}
				option.push(yobj);
			}
			this.quarterList = option;
			if (option[option.length - 1].children.length == 1) {
				this.targetQuarter = [option[option.length - 3].value, option[option.length - 3].children[0].value];
				this.contrastQuarter = [
					option[option.length - 2].value,
					option[option.length - 2].children[option[option.length - 2].children.length - 1].value
				];
			} else {
				this.targetQuarter = [option[option.length - 2].value, option[option.length - 2].children[0].value];
				this.contrastQuarter = [
					option[option.length - 1].value,
					option[option.length - 1].children[option[option.length - 1].children.length - 2].value
				];
			}
		},
		// 获取数据
		getData(data) {
			this.compareQuarterLoading = false;
		},
		// 隐藏
		hideLoading() {
			this.show = false;
		},
		// 配置变化对比
		getIndustryChange() {
			this.compareQuarterLoading = true;
			let url = this.$baseUrl + '/Company/IndustryChange/';
			let params = {
				code: this.code,
				flag: this.contrastType[0],
				now_yearqtr: this.contrastQuarter.join(' '),
				target_yearqtr: this.targetQuarter.join(' ')
			};
			axios
				.get(url, { params })
				.then((res) => {
					if (res.data.mtycode == 200) {
						if (this.contrastType[0] == 'industry') {
							this.formatIndustryChange(res.data.data, res.data.industry_section);
						} else {
							this.compareQuarterData = res.data.data;
						}
						this.compareQuarterLoading = false;
					} else {
						console.error('error: ', res);
						this.compareQuarterData = [];
						this.compareQuarterLoading = false;
					}
				})
				.catch((err) => {
					console.error('error: ', err);
					this.compareQuarterData = [];
					this.compareQuarterLoading = false;
				});
		}
	}
};
</script>

<style></style>
