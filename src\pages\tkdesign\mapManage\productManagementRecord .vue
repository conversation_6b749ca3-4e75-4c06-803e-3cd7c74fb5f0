<template>
  <div class="box_Board">
    <div class="header_box">
      <span class="header_inactive">投后&nbsp;/&nbsp;映射管理&nbsp;/&nbsp;</span>
      产品管理记录
    </div>
    <div class="border_table">
      <!-- 头部区域 -->
      <div class="border_table_header">
        <!-- 左侧标题区域 -->
        <div class="border_table_header_title">
          产品管理记录
        </div>
        <!-- 右侧按钮区域 -->
        <div class="border_table_header_button">
          <!-- 提示是否删除 -->
          <div v-show="promptMessage"
               class="prompt-message">
            <div class="block" />
            <div><i class="el-icon-warning"></i>确认删除内容吗</div>
            <el-button class="cancel"
                       @click="closePromptMessage">取消</el-button>
            <el-button class="warning"
                       @click="deleteAll">确认</el-button>
          </div>
          <el-button icon="el-icon-plus"
                     type="primary"
                     @click="addRow">
            新增
          </el-button>
          <el-button class="ml"
                     type="primary"
                     @click="showDialog = true">
            上传映射表
          </el-button>
          <el-button class="button-del ml"
                     @click="promptMessage =!promptMessage">
            删除原有映射
          </el-button>
          <el-button class="ml"
                     @click="showRecord">修改记录</el-button>
        </div>
      </div>
      <!-- 查询区域 -->
      <el-form class="search-box search-area">
        <el-form-item class="search-box_item">
          GP3组合代码：
          <el-select @change="changeOptions"
                     v-model="formData.gp3"
                     filterable
                     remote
                     :remote-method="getCodeOptions"
                     clearable
                     @clear="formData.gp3Name = ''"
                     style="width: 240px">
            <el-option v-for="item in gp3CodeOptions"
                       :key="item.gp3Code"
                       :label="item.gp3Code"
                       :value="item.gp3Code"
                       style="width: 240px">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="search-box_item">
          管理起始日期：
          <el-date-picker v-model="formData.startDate"
                          value-format="yyyyMMdd"
                          placeholder="请选择日期"
                          clearable
                          type="date"
                          style="width: 240px" />
        </el-form-item>
        <el-form-item class="search-box_item">
          管理结束日期：
          <el-date-picker v-model="formData.endDate"
                          value-format="yyyyMMdd"
                          clearable
                          placeholder="请选择日期"
                          type="date"
                          style="width: 240px" />
        </el-form-item>
        <el-form-item class="search-box_item">
          投资经理:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          <el-select v-model="formData.manager"
                     clearable
                     filterable
                     style="width: 240px">
            <el-option v-for="item in managerOptions"
                       :key="item"
                       :label="item"
                       :value="item"
                       style="width: 240px">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="search-box_item">
          GP3组合名称：
          <el-input v-model="formData.gp3Name"
                    :placeholder="formData.gp3Name?formData.gp3Name:'请输入'"
                    style="width: 240px">
          </el-input>
        </el-form-item>
        <el-form-item class="search-box_item">
          <el-radio-group v-model="formData.showModel"
                          @input="input()">
            <el-radio label="1">仅显示未分配投资经理的产品</el-radio>
            <el-radio label="2">仅显示未设置组合名称的产品</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-button type="primary"
                     @click="getProductManager()">
            查询
          </el-button>
          <el-button @click="reset">
            重置
          </el-button>
        </el-form-item>
      </el-form>
      <!-- 表格区域 -->
      <el-table v-loading="loading.tableLoading"
                :data="tableData"
                border
                stripe>
        <el-table-column align="gotoleft"
                         label="GP3组合代码"
                         prop="gp3Code">
          <template slot-scope="scope">
            <!-- <div v-if="scope.row.addFlag">
              <el-select @change="change(scope.row)"
                         v-model="scope.row.gp3Code"
                         filterable
                         clearable
                         remote
                         :remote-method="getCodeOptions">
                <el-option v-for="item in gp3CodeOptions"
                           :key="item.gp3Code"
                           :label="item.gp3Code"
                           :value="item.gp3Code">
                </el-option>
              </el-select>
            </div> -->
            <div :style="scope.row.gp3Code ?'':'color:red;'">
              {{ scope.row.gp3Code ? scope.row.gp3Code : '未定义' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column align="gotoleft"
                         label="管理起始日期"
                         prop="startTime"
                         width="240">
          <template slot-scope="scope">
            <div v-if="scope.row.addFlag">
              <el-date-picker v-model="scope.row.managerFrom"
                              value-format="yyyy-MM-dd"
                              type="date"
                              placeholder="选择日期时间">
              </el-date-picker>
            </div>
            <div v-else>{{ scope.row.managerFrom ? scope.row.managerFrom : '—— ——' }}</div>
          </template>
        </el-table-column>
        <el-table-column align="gotoleft"
                         label="管理结束日期"
                         prop="endTime"
                         width="240">
          <template slot-scope="scope">
            <div v-if="scope.row.addFlag">
              <el-date-picker v-model="scope.row.managerTo"
                              value-format="yyyy-MM-dd"
                              type="date"
                              placeholder="选择日期时间">
              </el-date-picker>
            </div>
            <div v-else>{{ scope.row.managerTo ? scope.row.managerTo : '—— ——' }}</div>
          </template>
        </el-table-column>
        <el-table-column align="gotoleft"
                         label="投资经理"
                         prop="managerName">
          <template slot-scope="scope">
            <div v-if="scope.row.addFlag">
              <el-select v-model="scope.row.managerName"
                         filterable
                         allow-create
                         clearable>
                <!-- allow-create -->
                <el-option v-for="item in managerOptions"
                           :key="item"
                           :label="item"
                           :value="item">
                </el-option>
              </el-select>
            </div>
            <div v-else>{{ scope.row.managerName ? scope.row.managerName : '未分配' }}</div>
          </template>
        </el-table-column>
        <el-table-column align="gotoleft"
                         label="GP3组合名称"
                         prop="gp3Name">
          <template slot-scope="scope">
            <div v-if="scope.row.addFlag">
              <el-input v-model="scope.row.gp3Name"
                        clearable
                        :placeholder="scope.row.gp3Name?scope.row.gp3Name:'未匹配'">
              </el-input>
            </div>
            <div v-else
                 :style="scope.row.gp3Name ?'':'color:red;'">
              {{ scope.row.gp3Name ? scope.row.gp3Name : '未定义' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <div v-if="!scope.row.addFlag"
                 class="flex">
              <el-button class="button-color"
                         type="text"
                         @click="edit(scope.row,scope.$index)">编辑
              </el-button>
              <el-button class="button-color"
                         type="text"
                         @click="deleteManager(scope.row.id)">
                删除
              </el-button>
            </div>
            <div v-else
                 class="flex">
              <el-button class="button-color"
                         type="text"
                         @click="onsubmit(scope.row)">保存
              </el-button>
              <el-button class="button-color"
                         type="text"
                         @click="cancel(scope.row,scope.$index)">取消
              </el-button>
            </div>
          </template>
        </el-table-column>
        <template slot="empty">
          <el-empty :image-size="160" />
        </template>
      </el-table>
      <!-- 分页器 -->
      <div class="pagination_board">
        <el-pagination :current-page.sync="pagination.pageIndex"
                       :page-size="pagination.pageSize"
                       :total="pagination.total"
                       background
                       layout="total, sizes, prev, pager, next"
                       @size-change="sizeChange"
                       @current-change="currentChange" />
      </div>
    </div>
    <!-- 上传映射表弹框 -->
    <el-dialog :visible.sync="showDialog"
               title="修改记录"
               width="900px">
      <ExcelPort :excelUrl="`manager.xlsx`"
                 :path="`/api/taikang/product/manager/upload`"
                 @refrshtable="uploadSuccess"
                 :data="{type:''}" />
    </el-dialog>
    <!-- 修改记录弹框 -->
    <el-dialog :visible.sync="showDialogRecord"
               title="修改记录"
               width="1200px">
      <!-- 表格区域 -->
      <el-table v-loading="loading.dialogLoading"
                :data="tableDataRecord"
                height="400">
        <el-table-column align="gotoleft"
                         label="投资经理"
                         prop="context.managerName" />
        <el-table-column align="gotoleft"
                         label="GP3组合代码"
                         prop="context.gp3Code" />
        <el-table-column align="gotoleft"
                         label="GP3组合名称"
                         prop="context.gp3Name" />
        <el-table-column align="gotoleft"
                         label="管理起始日期"
                         prop="context.managerFrom" />
        <el-table-column align="gotoleft"
                         label="修改人 ID"
                         prop="context.userId" />
        <el-table-column align="gotoleft"
                         label="操作类别"
                         prop="actionType">
          <template slot-scope="scope">
            {{ scope.row.actionType === 1 ? '新增' : scope.row.actionType === 2 ? '删除' : '修改' }}
          </template>
        </el-table-column>
        <el-table-column align="gotoleft"
                         label="修改时间"
                         prop="insertDate" />
        <template slot="empty">
          <el-empty :image-size="160" />
        </template>
      </el-table>
      <div class="pagination_board">
        <el-pagination :current-page.sync="paginationRecord.pageIndex"
                       :page-size="paginationRecord.pageSize"
                       :total="paginationRecord.total"
                       background
                       layout="total, sizes, prev, pager, next"
                       @size-change="sizeChangeRecord"
                       @current-change="currentChangeRecord" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ExcelPort from './component/map/alphaownpool.vue'
import {
  getProductManager,
  getManager,
  saveManager,
  deleteManager,
  deleteAll
} from "../../../api/pages/tkdesign/productManager";
import { getRecordList } from "../../../api/pages/tkdesign/historyRecord";
import { getProductList } from "../../../api/pages/tkdesign/productController";

export default {
  components: {
    ExcelPort,
  },
  data () {
    return {
      // 删除原有映射按钮提示显示与隐藏
      promptMessage: false,

      showDialog: false,
      showDialogRecord: false,

      tableData: [],// 页面表格数据源
      oldValue: {},
      tableDataRecord: [],// 修改记录表格数据源


      pagination: {
        pageIndex: 1,// 当前页码
        pageSize: 10,// 页面显示几条数据
        total: 0,
      },

      paginationRecord: {
        pageIndex: 1,// 当前页码
        pageSize: 10,// 页面显示几条数据
        total: 0,
      },

      // 搜索框对应的数据
      formData: {
        gp3: '',
        gp3Name: '',
        manager: '',
        startDate: '',
        endDate: '',
        showModel: ''
      },

      loading: {
        tableLoading: false,
        dialogLoading: false,
      },
      // 模糊查询的数据
      gp3CodeOptions: [],
      managerOptions: [],
    };
  },

  methods: {
    // 上传成功
    uploadSuccess () {
      this.showDialog = false
      this.getProductManager()
    },

    // 关闭删除提示
    closePromptMessage () {
      this.promptMessage = false
    },
    // 每页条数改变时触发的回调
    sizeChange (value) {
      this.pagination.pageSize = value
      this.getProductManager()
    },
    sizeChangeRecord (value) {
      this.paginationRecord.pageSize = value
      this.getRecordList()
    },

    // 当前页数改变时触发的回调
    currentChange (value) {
      this.pagination.pageIndex = value
      this.getProductManager()
    },
    currentChangeRecord (value) {
      this.paginationRecord.pageIndex = value
      this.getRecordList()
    },
    /**
     * 查询GP3组合代码改变选择事件
     * @param gp3Code
     */
    changeOptions (gp3Code) {
      this.gp3CodeOptions.forEach((item) => {
        if (item.gp3Code === gp3Code) {
          this.formData.gp3Name = item.gp3Name
        }
      })
    },
    /**
     * 表格GP3组合代码改变选择事件
     * @param gp3Code
     */
    change (row) {
      this.gp3CodeOptions.forEach((item) => {
        if (item.gp3Code === row.gp3Code) {
          row.gp3Name = item.gp3Name
        }
      })
    },
    /**
     * 处在编辑状态时，不允许对其他数据进行操作
     */
    ban () {
      let result = this.tableData.filter(v => v.addFlag)
      if (result.length > 0)
        return true
      else
        return false
    },
    /**
     *新增
     */
    addRow () {
      //判断是否处于编辑状态 如果正在编辑则不允许点击新增按钮
      if (this.ban())
        return

      const row = {
        addFlag: true,
        actionType: 1,
      };
      this.tableData.unshift(row);
    },
    /**
     * 编辑
     * @param obj 该行的数据
     * @param number 该行的下标
     */
    edit (obj, number) {
      //判断是否处于编辑状态 如果正在编辑则不允许点击其他编辑按钮
      let result = this.tableData.filter(v => v.addFlag)
      if (result.length > 0)
        return
      //保存传过来的的对象，留着在用户点击取消的时候还原数据
      this.oldValue = JSON.parse(JSON.stringify(obj))
      //这里addFlag为true但是页面没有实现响应式
      obj.addFlag = true
      //这里是为了解决addFlag不能实现响应式的问题 （数组重写）
      //将tableData里对应的该行数据删除，然后再把addFlag为true的obj添加到删除的位置
      this.tableData.splice(number, 1)
      this.tableData.splice(number, 0, obj)
    },

    /**
     * 取消
     * @param obj
     * @param number
     */
    cancel (obj, number) {
      if (obj.actionType === 1) {
        this.tableData.shift()
      } else {
        this.tableData.splice(number, 1)
        this.tableData.splice(number, 0, this.oldValue)
      }
    },
    /**
     * 重置数据
     */
    reset () {
      this.formData = {
        gp3: '',
        gp3Name: '',
        manager: '',
        startDate: '',
        endDate: '',
        showModel: ''
      }
      this.pagination = {
        pageIndex: 1,
        pageSize: 10,
      }
      this.getProductManager()
    },
    /**
     * 单选按钮事件
     */
    input () {
      this.pagination = {
        pageIndex: 1,
        pageSize: 10,
      }
      this.getProductManager()
    },
    /**
     * 显示修改记录
     */
    showRecord () {
      this.showDialogRecord = true
      this.getRecordList()
    },
    /**
     * 获取产品管理记录列表
     */
    getProductManager () {
      this.loading.tableLoading = true
      let params = {
        ...this.formData,
        current: this.pagination.pageIndex,
        pageSize: this.pagination.pageSize,
      }
      getProductManager(params).then((res) => {
        this.loading.tableLoading = false
        if (res.code === 200) {
          this.tableData = res.data
          this.pagination.total = res.total
          this.tableData.forEach((item) => {
            item.addFlag = false
          })
        } else {
          this.tableData = []
          this.pagination.total = 0
        }
      })
    },
    /**
     * 获取管理人
     */
    getManagerOptions () {
      getManager().then((res) => {
        if (res.code === 200) {
          this.managerOptions = new Set(res.data)
        } else {
          this.managerOptions = []
        }
      })
    },
    /**
     * 获取代码选择项
     */
    getCodeOptions (query) {
      let params = {
        current: 1,
        pageSize: 10,
        gp3: query,
      }
      getProductList(params).then((res) => {
        if (res.code === 200) {
          this.gp3CodeOptions = []
          res.data.forEach((item) => {
            if (item.gp3Code) {
              this.gp3CodeOptions.push({
                gp3Code: item.gp3Code,
                gp3Name: item.gp3Name
              })
              this.gp3CodeOptions = Array.from(new Set(this.gp3CodeOptions))
            } else
              this.gp3CodeOptions = []
          })
        }
      })
    },
    /**
     * 提交保存
     * @param params
     */
    onsubmit (params) {
      const data = {
        ...params,
        addFlag: false,
      }
      saveManager(data).then((res) => {
        if (res.code === 200) {
          this.$message.success('上传成功')
          this.getProductManager()
          this.getManagerOptions()
        } else {
          this.$message.error('上传失败')
        }
      })
    },
    /**
     *删除某一行数据
     * @param id
     */
    deleteManager (id) {
      if (this.ban())
        return
      this.$confirm('确定删除么?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteManager(id).then((res) => {
          if (res.code === 200) {
            this.$message({
              type: 'success',
              message: '删除成功!',
            });
            this.getProductManager()
            this.getManagerOptions()
          } else {
            this.$message.error('删除失败')
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    /**
     * 删除全部数据
     */
    deleteAll () {
      this.$confirm('确定删除么?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteAll().then((res) => {
          if (res.code === 200) {
            this.$message({
              type: 'success',
              message: '删除成功!',
            });
            this.getProductManager()
            this.getManagerOptions()
          } else {
            this.$message.error('删除失败')
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
      this.closePromptMessage()
    },
    /**
     * 获取历史修改记录
     */
    getRecordList () {
      this.loading.dialogLoading = true
      const params = {
        type: 3,
        current: this.paginationRecord.pageIndex,
        pageSize: this.paginationRecord.pageSize
      }
      getRecordList(params).then((res) => {
        this.loading.dialogLoading = false
        if (res.code === 200) {
          res.data.forEach((item) => {
            if (item.context)
              item.context = JSON.parse(item.context.value)
          })
          console.log(res.data)
          this.tableDataRecord = res.data

          this.paginationRecord.total = res.total
        } else {
          this.tableDataRecord = []
          this.paginationRecord.total = 0
        }
      })
    }
  },
  mounted () {
    this.getProductManager()
    this.getCodeOptions()
    this.getManagerOptions()
  },
}
</script>
<style lang="scss" scoped>
@import '../tkdesign';

.ml {
	margin-left: 16px;
}

.border_table {
	padding: 16px 24px;
	background: white;

	.border_table_header_button {
		position: relative;

		.prompt-message {
			z-index: 99;
			position: absolute;
			bottom: -98px;
			right: 82px;
			padding: 16px;
			border-radius: 4px;
			box-shadow: 0 0 2px #ccc;
			background-color: #fff;

			.el-button {
				padding: 5px;
				margin: 20px 0 0;
			}

			.cancel {
				margin-left: 20px;
			}

			.warning {
				border-color: #4096ff;
				background: #4096ff;
				color: white;
				margin-left: 5px;
			}

			.block {
				position: absolute;
				width: 10px;
				height: 10px;
				box-shadow: 0.5px 0.5px 1px #ccc;
				background-color: #fff;
				top: 0;
				left: 50%;
				transform: translate(-50%, -50%) rotate(225deg);
			}
		}
	}

	.search-box {
		background-color: #fafafa;
		padding: 16px 24px 0;
		margin-bottom: 16px;
		display: flex;
		justify-content: space-between;
		flex-wrap: wrap;

		.search-box_item {
			width: 33%;

			.el-input,
			.el-select,
			.el-date-picker {
				width: 60%;
			}
		}
	}
}
</style>

<style lang="scss">
.search-area {
	.el-form-item__content {
		margin-left: 0 !important;
	}
}
</style>
