import request from '@/utils/request';

/**
 * 获取自定义表格数据
 * @param params
 * @returns {*}
 */
export function getList(params) {
    return request({
        url: '/api/taikang/market/segment/list',
        method: 'get',
        params
    });
}

/**
 * 删除一行数据
 * @param id
 * @returns {*}
 */
export function delRow(id) {
    return request({
        url: `/api/taikang/market/segment/del?id=${id}`,
        method: 'post',
    });
}

/**
 * 保存
 * @param data
 * @returns {*}
 */
export function saveRow(data) {
    return request({
        url: '/api/taikang/market/segment/save',
        method: 'post',
        data
    });
}


export function getById(id) {
    return request({
        url: `/api/taikang/market/segment/getById?id=${id}`,
        method: 'get',
    });
}
