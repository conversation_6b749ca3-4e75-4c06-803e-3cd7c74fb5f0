<template>
  <div v-show="show"
       v-loading="loading">
    <div style="display: flex; justify-content: space-between; align-items: center">
      <div style="padding-top:16px;font-style: normal; font-weight: 500; font-size: 16px; line-height: 24px; color: rgba(0, 0, 0, 0.85)"
           class="title">相关矩阵</div>
      <div style="padding-top:16px;">
        <el-popover placement="top"
                    width="170"
                    v-model="visible">
          <div>
            <div style="display: flex; align-items: center">
              <div>行业：</div>
              <el-input style="width: 100px"
                        v-model="industryP"
                        placeholder="配置权重"></el-input>
            </div>
            <div style="display: flex; align-items: center; margin-top: 16px">
              <div>债券：</div>
              <el-input style="width: 100px"
                        v-model="bondP"
                        placeholder="配置权重"></el-input>
            </div>
            <div style="display: flex; align-items: center; margin-top: 16px">
              <div>收益：</div>
              <el-input style="width: 100px"
                        v-model="returnP"
                        placeholder="配置权重"></el-input>
            </div>
          </div>
          <div style="text-align: right; margin: 0; margin-top: 16px">
            <el-button size="mini"
                       type="text"
                       @click="visible = false">取消</el-button>
            <el-button type="primary"
                       size="mini"
                       @click="showChange()">确定</el-button>
          </div>
          <el-button type="text"
                     style="margin-right: 8px"
                     slot="reference">
            <i class="el-icon-s-tools"></i>
          </el-button>
        </el-popover>

        <el-button type="primary"
                   @click="setOption('add')">+</el-button>
        <el-button type="primary"
                   :disabled="zoom == 1 ? true : false"
                   @click="setOption('subtract')">-</el-button>
      </div>
    </div>
    <div style="margin-top: 16px">
      <v-chart ref="correlationMatrix"
               class="charts_two_class"
               style="width: 100%; height: 400px"
               v-loading="loading"
               autoresize
               :options="option"
               element-loading-text="暂无数据"
               element-loading-spinner="el-icon-document-delete"
               element-loading-background="rgba(239, 239, 239, 0.5)"
               @click="goFundDetail"></v-chart>
    </div>
  </div>
</template>

<script>
import { getCombinationSimilar } from "@/api/pages/tools/pool.js";

import { alphaGo } from "@/assets/js/alpha_type.js";

export default {
  props: {
    dateC: {},
  },
  data () {
    return {
      ismanager: false,
      industryP: 0.6,
      bondP: 0,
      returnP: 0.4,
      loading: true,
      option: {},
      zoom: 1,
      show: true,
      visible: false,
      arrData: "",
      flagData: ""
    };
  },
  methods: {
    showChange (data) {
      this.visible = false;
      this.getData(this.arrData, this.flagData, {
        industry: this.industryP,
        rate: this.returnP,
        bond: this.bondP
      });
    },
    async getData (arr, flag, items) {
      this.arrData = arr;
      this.flagData = flag;
      this.loading = true;
      this.zoom = 1;
      let color = {
        equity: "#4096ff",
        equityhk: "#7388A9",
        bond: "#4096ff",
        equitywithhk: "#E85D2D",
        cbond: "#7388A9",
        purebond: "#9A89FF",
        bill: "#6C96F2"
      };
      this.show = true;
      let requestList = {};
      if (flag == 5) {
        requestList = {
          id: arr,
          flag: flag,
          datePool: this.dateC,
          item: items,
          type: this.$route.query?.type

        };
      } else {
        requestList = {
          ismanager: this.ismanager,
          code_list: arr?.map(item => {
            return this.ismanager
              ? item.manager_code
              : item.fund_code; //TODP
          }),
          datePool: this.dateC,
          item: items,
          type: this.$route.query?.type
        };
      }
      let result = await getCombinationSimilar(requestList);
      if (result?.mtycode != 200) {
        this.show = false;
        this.$emit("hideComponents", "correlationMatrix");
        return;
      }
      this.loading = false;
      // let categories = result?.map((item) => {
      // 	return { name: item.name, keyword: {}, base: item.name };
      // });
      let edges = result?.data?.edges?.map(item => {
        return {
          source: item?.[0],
          target: item?.[1],
          value: item?.[2]
        };
      });
      let data = [];
      result?.data?.nodes?.map(item => {
        data.push({
          id: item.name,
          code: item.code,
          name: item.name,
          x: item.x,
          y: item.y,
          symbolSize: Math.log(item.netasset) * 5,
          itemStyle: {
            color: color[item.type]
          },
          label: {
            color: "#e9e9e9",
            fontSize: 10
          }
        });
      });
      this.option = {
        animationDurationUpdate: 1000,
        animationEasingUpdate: "quinticInOut",
        series: [
          {
            type: "graph",
            layout: "none",
            // progressiveThreshold: 700,
            data,
            edges,
            room: true,
            label: {
              show: true,
              position: "right",
              formatter: "{b}"
              // color: function (val) {
              // 	console.log(val);
              // }
            },
            // itemStyle: {
            // 	opacity: 0.2
            // },
            labelLayout: {
              hideOverlap: true
            },
            emphasis: {
              focus: "adjacency",
              label: {
                // opacity: 1
                color: "000000",
                fontSize: 14
              },
              lineStyle: {
                width: 5
              }
            },
            roam: "move",
            zoom: this.zoom,
            lineStyle: {
              width: 0.5,
              curveness: 0.3,
              opacity: 0.7
            }
          }
        ]
      };
    },
    // 放大/缩小
    setOption (type) {
      if (type == "add") {
        this.zoom = this.zoom + 1;
      } else {
        this.zoom = this.zoom - 1;
      }
      this.option.series[0].zoom = this.zoom;
    },
    // 前往详情页
    goFundDetail (val) {
      alphaGo(val.data.code, val.data.name, this.$route.path);
    },
    createPrintWord () {
      let height = this.$refs["correlationMatrix"]?.$el.clientHeight;
      let width = this.$refs["correlationMatrix"]?.$el.clientWidth;
      let chart = this.$refs["correlationMatrix"].getDataURL({
        type: "png",
        pixelRatio: 3,
        backgroundColor: "#fff"
      });
      return [
        ...this.$exportWord.exportTitle("相关矩阵"),
        ...this.$exportWord.exportChart(chart, { width, height })
      ];
    }
  },
  mounted () {
    this.ismanager = String(this.$route.query.ismanager) == 'true' ? true : false
  },
};
</script>

<style></style>
