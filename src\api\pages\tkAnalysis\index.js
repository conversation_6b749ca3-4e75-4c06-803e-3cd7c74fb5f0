import request from "@/api/request";
// 分析兑现管理列表
export function getList() {
  return request({
    url: "/analyse/object/getList/",
    method: "get",
  });
}
// 新建分析兑现
export function insertObj(data) {
  return request({
    url: "/analyse/object/insert/",
    method: "post",
    data,
  });
}
// 删除分析兑现
export function delObj(params) {
  return request({
    url: "/analyse/object/delete/",
    method: "delete",
    params,
  });
}
// 修改分析兑现
export function upObj(data) {
  return request({
    url: "/analyse/object/update/",
    method: "put",
    data,
  });
}
// 行业映射表格 TODO没找到接口
export function getListMap() {
  return request({
    url: "/analyse/object/getListMap/",
    method: "get",
  });
}
// 行业印射查询
export function searchMap() {
  return request({
    url: "/mapping/query/",
    method: "get",
  });
}
// 新增行业印射查询
export function insertMap(data) {
  return request({
    url: "/mapping/insert/",
    method: "post",
    data,
  });
}
// 删除行业印射查询
export function delMap(params) {
  return request({
    url: "/mapping/deleteById/",
    method: "delete",
    params,
  });
}
// 修改行业印射查询
export function upMap(data) {
  return request({
    url: "/mapping/history/",
    method: "put",
    data,
  });
}
