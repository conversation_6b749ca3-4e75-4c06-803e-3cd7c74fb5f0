<!--  -->
<template>
  <div class="dialogFilerDX">
    <div style="width: 100%">
      <div style="display: flex; width: 100%">
        <div style="width: 200px; border-right: 1px solid #e9e9e9">
          <div style="text-align: left; margin-left: 24px; margin-top: 16px">
            <div
              style="font-weight: 500; font-size: 16px; line-height: 24px; color: rgba(0, 0, 0, 0.85); margin-bottom: 16px"
            >打分条件</div>
            <el-scrollbar style="height: 628px">
              <el-tree :data="data" :props="defaultProps" @node-click="handleNodeClick"></el-tree>
            </el-scrollbar>
          </div>
        </div>
        <div style="flex: 19">
          <div style="text-align: left; height: 100%">
            <div style="min-height: 602px; height: calc(100% - 75px);border: 1px solid rgba(217, 217, 217, 0.5);">
              <div>
                <!-- 动态数据部分 -->
                <div v-for="(item, index) in listSelect" :key="index">
                  <div v-if="item.labelIndex == 'g'">
                    <div class="boxItemDetail">
                      <div class="titleContent">风险特征</div>
                      <div
                        v-for="(items, index1) in item.data"
                        :key="index1"
                        class="contentBoxFilter"
                      >
                        <div style="display: flex; align-items: center" class="contentItem">
                          <!-- {{ optionsselect[items.labelName] }} -->
                          <boxNameYSF
                            :haveName="items.labelName"
                            @boxOnlyYSFNameChange="boxOnlyYSFNameChange"
                            placeholder="前百分之,如10,表示TOP10%"
                            :fundType="fundType"
                            :optionsselect="optionsselect[items.labelName]"
                            :ref="'boxNameYSF' + index + '_' + index1"
                            :indexFlag="index1"
                            :baseIndexFlag="index"
                            :dataX="items"
                          ></boxNameYSF>
                        </div>
                        <div class="contentDel" @click="del(index, index1)">
                          <svg
                            width="12"
                            height="14"
                            viewBox="0 0 12 14"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M3.62476 1.87354H3.49976C3.56851 1.87354 3.62476 1.81729 3.62476 1.74854V1.87354H8.37476V1.74854C8.37476 1.81729 8.43101 1.87354 8.49976 1.87354H8.37476V2.99854H9.49976V1.74854C9.49976 1.19697 9.05132 0.748535 8.49976 0.748535H3.49976C2.94819 0.748535 2.49976 1.19697 2.49976 1.74854V2.99854H3.62476V1.87354ZM11.4998 2.99854H0.499756C0.223193 2.99854 -0.********* 3.22197 -0.********* 3.49854V3.99854C-0.********* 4.06729 0.0560059 4.12354 0.124756 4.12354H1.06851L1.45444 12.2954C1.47944 12.8282 1.92007 13.2485 2.45288 13.2485H9.54663C10.081 13.2485 10.5201 12.8298 10.5451 12.2954L10.931 4.12354H11.8748C11.9435 4.12354 11.9998 4.06729 11.9998 3.99854V3.49854C11.9998 3.22197 11.7763 2.99854 11.4998 2.99854ZM9.42632 12.1235H2.57319L2.19507 4.12354H9.80444L9.42632 12.1235Z"
                              fill="black"
                              fill-opacity="0.45"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-else-if="item.labelIndex == 'h'">
                    <div class="boxItemDetail">
                      <div class="titleContent">风险收益特征</div>
                      <div
                        v-for="(items, index1) in item.data"
                        :key="index1"
                        class="contentBoxFilter"
                      >
                        <div style="display: flex; align-items: center" class="contentItem">
                          <boxNameYSF
                            :haveName="items.labelName"
                            @boxOnlyYSFNameChange="boxOnlyYSFNameChange"
                            placeholder="前百分之,如10,表示TOP10%"
                            :fundType="fundType"
                            :optionsselect="optionsselect[items.labelName]"
                            :ref="'boxNameYSF' + index + '_' + index1"
                            :indexFlag="index1"
                            :baseIndexFlag="index"
                            :dataX="items"
                          ></boxNameYSF>
                        </div>
                        <div class="contentDel" @click="del(index, index1)">
                          <svg
                            width="12"
                            height="14"
                            viewBox="0 0 12 14"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M3.62476 1.87354H3.49976C3.56851 1.87354 3.62476 1.81729 3.62476 1.74854V1.87354H8.37476V1.74854C8.37476 1.81729 8.43101 1.87354 8.49976 1.87354H8.37476V2.99854H9.49976V1.74854C9.49976 1.19697 9.05132 0.748535 8.49976 0.748535H3.49976C2.94819 0.748535 2.49976 1.19697 2.49976 1.74854V2.99854H3.62476V1.87354ZM11.4998 2.99854H0.499756C0.223193 2.99854 -0.********* 3.22197 -0.********* 3.49854V3.99854C-0.********* 4.06729 0.0560059 4.12354 0.124756 4.12354H1.06851L1.45444 12.2954C1.47944 12.8282 1.92007 13.2485 2.45288 13.2485H9.54663C10.081 13.2485 10.5201 12.8298 10.5451 12.2954L10.931 4.12354H11.8748C11.9435 4.12354 11.9998 4.06729 11.9998 3.99854V3.49854C11.9998 3.22197 11.7763 2.99854 11.4998 2.99854ZM9.42632 12.1235H2.57319L2.19507 4.12354H9.80444L9.42632 12.1235Z"
                              fill="black"
                              fill-opacity="0.45"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-else-if="item.labelIndex == 'a'">
                    <div class="boxItemDetail">
                      <div class="titleContent">慧捕基指标</div>
                      <div
                        v-for="(items, index1) in item.data"
                        :key="index1"
                        class="contentBoxFilter"
                      >
                        <div
                          v-if="items.labelName != '特定行业能力' && items.labelName != '特定主题能力'"
                          style="display: flex; align-items: center"
                          class="contentItem"
                        >
                          <boxNameYSF
                            :showTime="false"
                            :haveName="items.labelName"
                            @boxOnlyYSFNameChange="boxOnlyYSFNameChange"
                            placeholder="前百分之,如10,表示TOP10%"
                            :fundType="fundType"
                            :ref="'boxNameYSF' + index + '_' + index1"
                            :indexFlag="index1"
                            :baseIndexFlag="index"
                            :dataX="items"
                          ></boxNameYSF>
                        </div>
                        <div v-else style="display: flex; align-items: center" class="contentItem">
                          <boxNameYSF2
                            :showTime="false"
                            :dataIndustry="dataIndustry"
                            :haveName="items.labelName"
                            @boxOnlyYSFNameChange="boxOnlyYSFNameChange"
                            placeholder="前百分之,如10,表示TOP10%"
                            :fundType="fundType"
                            :ref="'boxNameYSF2' + index + '_' + index1"
                            :indexFlag="index1"
                            :baseIndexFlag="index"
                            :dataX="items"
                          ></boxNameYSF2>
                        </div>
                        <div class="contentDel" @click="del(index, index1)">
                          <svg
                            width="12"
                            height="14"
                            viewBox="0 0 12 14"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M3.62476 1.87354H3.49976C3.56851 1.87354 3.62476 1.81729 3.62476 1.74854V1.87354H8.37476V1.74854C8.37476 1.81729 8.43101 1.87354 8.49976 1.87354H8.37476V2.99854H9.49976V1.74854C9.49976 1.19697 9.05132 0.748535 8.49976 0.748535H3.49976C2.94819 0.748535 2.49976 1.19697 2.49976 1.74854V2.99854H3.62476V1.87354ZM11.4998 2.99854H0.499756C0.223193 2.99854 -0.********* 3.22197 -0.********* 3.49854V3.99854C-0.********* 4.06729 0.0560059 4.12354 0.124756 4.12354H1.06851L1.45444 12.2954C1.47944 12.8282 1.92007 13.2485 2.45288 13.2485H9.54663C10.081 13.2485 10.5201 12.8298 10.5451 12.2954L10.931 4.12354H11.8748C11.9435 4.12354 11.9998 4.06729 11.9998 3.99854V3.49854C11.9998 3.22197 11.7763 2.99854 11.4998 2.99854ZM9.42632 12.1235H2.57319L2.19507 4.12354H9.80444L9.42632 12.1235Z"
                              fill="black"
                              fill-opacity="0.45"
                            />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              style="text-align: right; margin-right: 24px; padding-top: 24px; border-top: 1px solid #e9e9e9;display:flex;align-items: center;justify-content: end"
              class="demo-drawer__footer"
            >
              <div>
                打分结果数量：
                <el-select style="width:100px" v-model="selecNumber" placeholder="请选择打分结果数量">
                  <el-option
                    v-for="(item,index) in numberList"
                    :label="item.label"
                    :value="item.value"
                    :key="index"
                  ></el-option>
                </el-select>
              </div>
              <div style="margin-left:16px">
                <el-button @click="out(1)" type>重置</el-button>
              </div>
              <div style="margin-left:16px">
                <el-button type="primary" @click="out(2)">下一步</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { scoreCardList } from "@/api/pages/Filter.js";
// import Holdperson from '@/components/tools/alphafof/analysis/components/holdperson.vue';
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
// import boxOnlyYSF from './boxOnlyYSF';
// import fundCateOnly from './fundCateOnly';
// import holdPersonOnly from './holdPersonOnly';
import axios from "@/api/index";
import boxNameYSF from "./boxNameYSF";
import boxNameYSF2 from "./boxNameYSF2";
import { alphamsg } from "@/api/pages/SystemAlpha.js";
// import industryTheme from './industryTheme';
// import indexOnly from './indexOnly';
// import boxCzJzOnly from './boxCzJzOnly';
// import boxCzJzOnly2 from './boxCzJzOnly2';
// import industryBig from './industryBig';
export default {
  props: {
    // dataIndustry: {
    // 	type: Object
    // },
    listSelectX: {
      type: Array
    },
    radioType2: {
      type: String,
      default: "latest"
    },
    radioInput2: {
      type: String,
      default: ""
    },
    isSame2: {
      type: Boolean,
      default: true
    }
  },
  //import引入的组件需要注入到对象中才能使用
  components: {
    // Holdperson,
    // boxOnlyYSF,
    // fundCateOnly,
    // holdPersonOnly,
    boxNameYSF,
    boxNameYSF2
    // industryTheme,
    // indexOnly,
    // boxCzJzOnly,
    // boxCzJzOnly2,
    // industryBig
  },
  data() {
    //这里存放数据
    return {
      selecNumber: "all",
      numberList: [
        {
          value: 10,
          label: "10条"
        },
        {
          value: 20,
          label: "20条"
        },
        {
          value: 50,
          label: "50条"
        },
        {
          value: 100,
          label: "100条"
        },
        {
          value: 200,
          label: "200条"
        },
        {
          value: 500,
          label: "500条"
        },
        {
          value: "all",
          label: "全部"
        }
      ],
      dataIndustry: "",
      fundType: "equity",
      listSelect: [],
      filterData: {},
      optionsselect: [],
      checkedMerge: false,
      radioFilterOrOut: true,
      radioSelf: null,
      radio: "latest",
      data: [
        {
          label: "风险特征",
          children: []
        },
        {
          label: "风险收益特征",
          children: []
        },
        {
          label: "慧捕基指标",
          children: []
        }
      ],
      defaultProps: {
        children: "children",
        label: "label"
      },
      field104Options: [
        {
          label: "波动率", //波动率：区间数据（默认下届为0）
          value: "volatility"
        },
        {
          label: "最大回撤", //区间数据（默认下届为0）
          value: "maxdrawdown"
        },
        {
          label: "平均下行周期", //区间数据（默认下届为0）
          value: "averagelength"
        },
        {
          label: "平均恢复周期", //区间数据（默认下届为0）
          value: "averagerecovery"
        },
        {
          label: "最大回撤比", //区间数据（默认下届为0.5，上届为1）
          value: "maxdrawdown_ratio"
        },
        {
          label: "在险价值", //区间数据
          value: "VaR05"
        },
        {
          label: "期望损失", //区间数据
          value: "ES05"
        },
        {
          label: "下行风险", //区间数据
          value: "downsidevolatility"
        },
        {
          label: "波动率比", //区间数据（默认下届为0.5，上届为1）
          value: "volatilityratio"
        },
        {
          label: "痛苦指数",
          value: "painindex"
        }
      ],
      field105Options: [
        {
          label: "年化收益率",
          value: "ave_return"
        },
        {
          label: "累计收益率",
          value: "cum_return"
        },
        {
          label: "夏普率（rf==0）",
          value: "sharpe0"
        },
        {
          label: "夏普率（rf==4%）",
          value: "sharpe04"
        },
        {
          label: "夏普率（动态rf）",
          value: "sharpe"
        },
        {
          label: "卡码率",
          value: "calmar"
        },
        {
          label: "索提诺系数（rf==0）",
          value: "sortino0"
        },
        {
          label: "索提诺系数（rf==4%）",
          value: "sortino04"
        },
        {
          label: "索提诺系数（动态rf）",
          value: "sortino"
        },
        {
          label: "稳定系数",
          value: "hurstindex"
        },
        {
          label: "凯利系数",
          value: "kelly"
        },
        {
          label: "信息⽐率",
          value: "information"
        },
        {
          label: "上攻潜力（周）",
          value: "upsidepotential"
        },
        {
          label: "月胜率",
          value: "monthly_win_ratio"
        },
        {
          label: "詹森系数",
          value: "jensen"
        },
        {
          label: "特诺系数",
          value: "treynor"
        },
        {
          label: "上行捕获",
          value: "bullreturn"
        },
        {
          label: "下行捕获",
          value: "bearreturn"
        },
        {
          label: "择时gamma",
          value: "gamma"
        },
        {
          label: "M2",
          value: "msquared"
        }
      ]
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {
    listSelectX(val) {
      // //console.log("0000");
      this.listSelect = this.listSelectX;
    }
  },
  //方法集合
  methods: {
    changeMerge() {
      this.$emit("changeMerge", this.checkedMerge);
    },
    boxOnlyYSFNameChange(e1, e2, e3, e4, e5, e6, e7, e8) {
      this.listSelect[e1].data[e2].dataResult = [
        {
          flag: e4,
          value: e3,
          date: e5,
          option: e6,
          havedata: e7,
          operation: e8
        }
      ];
      // console.log(this.listSelect, '2222');
    },
    industryThemeChange(e1, e2, e3, e4, e5, e6) {
      this.listSelect[e1].data[e2].dataResult = [
        { flag: e4, value: e3, industryValue: e5, industryName: e6 }
      ];
    },
    bigOnlyChange(e1, e2, e3) {
      this.listSelect[e1].data[e2].dataResult = [{ value: e3 }];
    },
    indexOnlyChange(e1, e2, e3, e4, e5, e6) {
      this.listSelect[e1].data[e2].dataResult = [
        { flag: e4, value: e3, index_code: e5, index_code_options: e6 }
      ];
      //console.log(this.listSelect);
    },
    boxOnlyYSFChange(e1, e2, e3, e4) {
      this.listSelect[e1].data[e2].dataResult = [{ flag: e4, value: e3 }];
    },
    boxCzJzOnlyChange(e1, e2, e3, e4, e5, e6, e7) {
      this.listSelect[e1].data[e2].dataResult = [
        { flag: e4, value: e3, label: e5, yearqtr: e6, rank_value: e7 }
      ];
    },
    fundCateOnlyChange(e1, e2, e3) {
      this.listSelect[e1].data[e2].dataResult = [{ value: e3 }];
    },
    del(item, index) {
      for (let k = 0; k < this.listSelect.length; k++) {
        let temp1 = this.listSelect[k];
        let obj = [];
        for (let i = 0; i < temp1.data.length; i++) {
          if (JSON.stringify(temp1.data[i].dataResult) != "[]") {
            for (let key in temp1.data[i].dataResult[0]) {
              obj.push(key);
            }
          }
        }
        for (let i = 0; i < temp1.data.length; i++) {
          if (JSON.stringify(temp1.data[i].dataResult) == "[]") {
            temp1.data[i].dataResult[0] = {};
            for (let j = 0; j < obj.length; j++) {
              temp1.data[i].dataResult[0][obj[j]] = "";
            }
          }
        }
      }
      if (this.listSelect[item].data.length > 1) {
        // console.log("object");
        // let temp1 = this.FUNC.deepClone(this.listSelect[item])
        // let obj = []
        // for(let i = 0;i<temp1.data.length;i++){
        //     if(JSON.stringify(temp1.data[i].dataResult)!='[]'){
        //         for(let key in temp1.data[i].dataResult[0]){
        //             obj.push(key)
        //         }
        //     }
        // }
        // for(let i = 0;i<temp1.data.length;i++){
        //     if(JSON.stringify(temp1.data[i].dataResult)=='[]'){
        //          temp1.data[i].dataResult[0]={}
        //         for(let j = 0;j<obj.length;j++){
        //              temp1.data[i].dataResult[0][obj[j]] = ''
        //         }
        //     }
        // }
        // temp1.data.splice(index,1)
        // debugger

        // this.$set(this.listSelect,item,temp1)
        // this.listSelect[item].data = temp1
        this.listSelect[item].data.splice(index, 1);
      } else {
        this.listSelect.splice(item, 1);
      }
      // console.log( this.listSelect);
      // let temp = this.FUNC.deepClone(this.listSelect)
      // this.listSelect = []
      // this.$nextTick(() => {
      //     this.listSelect = temp
      // })
    },
    out(flag) {
      //console.log(this.listSelect);
      if (flag == 1) {
        this.$emit("closeDialog", "--");
      } else if (flag == 3) {
        this.$emit("closeDialog", "back");
      } else {
        let flagX = 0;
        // console.log(this.listSelect);
        // for (let i = 0; i < this.listSelect.length; i++) {

        // }
        if (flagX == 0) {
          this.$emit(
            "closeDialog",
            this.listSelect,
            this.radio,
            this.radioSelf,
            this.radioFilterOrOut,
            this.selecNumber
          );
        }
      }
    },
    isEqual(objA, objB) {
      //相等
      if (objA === objB) return objA !== 0 || 1 / objA === 1 / objB;
      //空判断
      if (objA == null || objB == null) return objA === objB;
      //类型判断
      if (
        Object.prototype.toString.call(objA) !==
        Object.prototype.toString.call(objB)
      )
        return false;

      switch (Object.prototype.toString.call(objA)) {
        case "[object RegExp]":
        case "[object String]":
          //字符串转换比较
          return "" + objA === "" + objB;
        case "[object Number]":
          //数字转换比较,判断是否为NaN
          if (+objA !== +objA) {
            return +objB !== +objB;
          }

          return +objA === 0 ? 1 / +objA === 1 / objB : +objA === +objB;
        case "[object Date]":
        case "[object Boolean]":
          return +objA === +objB;
        case "[object Array]":
          //判断数组
          for (let i = 0; i < objA.length; i++) {
            if (!this.isEqual(objA[i], objB[i])) return false;
          }
          return true;
        case "[object Object]":
          //判断对象
          let keys = Object.keys(objA);
          for (let i = 0; i < keys.length; i++) {
            if (!this.isEqual(objA[keys[i]], objB[keys[i]])) return false;
          }

          keys = Object.keys(objB);
          for (let i = 0; i < keys.length; i++) {
            if (!this.isEqual(objA[keys[i]], objB[keys[i]])) return false;
          }

          return true;
        default:
          return false;
      }
    },
    handleNodeClick(e) {
      if (
        this.listSelect.length == 0 &&
        e.type &&
        e.type != null &&
        e.type != undefined
      ) {
        this.listSelect.push({
          labelIndex: e.type,
          data: [
            {
              labelName: e.label,
              typeCate: e.typeCate,
              dataResult: []
            }
          ]
        });
      } else {
        if (e.type && e.type != null && e.type != undefined) {
          if (this.listSelect == "null") {
            this.listSelect = [];
          }
          if (
            this.listSelect?.findIndex(item => item.labelIndex == e.type) >= 0
          ) {
            this.listSelect[
              this.listSelect.findIndex(item => item.labelIndex == e.type)
            ].data.push({
              labelName: e.label,
              typeCate: e.typeCate,
              dataResult: []
            });
          } else {
            this.listSelect.push({
              labelIndex: e.type,
              data: [
                {
                  labelName: e.label,
                  typeCate: e.typeCate,
                  dataResult: []
                }
              ]
            });
          }
        }
      }

      // 分类 push for循环 if判断类型
    },
    async getList() {
      // console.log('????????????');
      let data = await scoreCardList({
        type: this.type || "equity",
        flag: "2"
      });
      // //console.log(data);
      if (data) {
        if (data.data?.table2) {
          // data.data?.table2.push({
          // 	date_recent: false,
          // 	description: '--',
          // 	ename: '--',
          // 	id: '22',
          // 	isTrue: false,
          // 	isopen: false,
          // 	lbound: false,
          // 	name: '转债能力',
          // 	other_args: true,
          // 	rank: false,
          // 	ubound: false
          // });
          // data.data?.table2.push({
          // 	date_recent: false,
          // 	description: '--',
          // 	ename: '--',
          // 	id: '22',
          // 	isTrue: false,
          // 	isopen: false,
          // 	lbound: false,
          // 	name: '信用能力',
          // 	other_args: true,
          // 	rank: false,
          // 	ubound: false
          // });
          // data.data?.table2.push({
          // 	date_recent: false,
          // 	description: '--',
          // 	ename: '--',
          // 	id: '22',
          // 	isTrue: false,
          // 	isopen: false,
          // 	lbound: false,
          // 	name: '久期能力',
          // 	other_args: true,
          // 	rank: false,
          // 	ubound: false
          // });
          this.$emit("t1t2", data.data.table1, data.data.table2);
          this.data = [
            {
              label: "风险特征",
              children: []
            },
            {
              label: "风险收益特征",
              children: []
            },
            {
              label: "慧捕基指标",
              children: []
            }
          ];
          this.data[2].children = [];
          for (let i = 0; i < data.data.table2.length; i++) {
            if (
              data.data.table2[i].name == "特定行业能力" ||
              data.data.table2[i].name == "特定主题能力"
            ) {
              this.data[2].children.push({
                label: data.data.table2[i].name,
                typeCate: "2", //4 name+运算符
                type: "a"
              });
            } else {
              this.data[2].children.push({
                label: data.data.table2[i].name,
                typeCate: "1", //4 name+运算符
                type: "a"
              });
            }
          }
          for (let i = 0; i < data.data.table1.length; i++) {
            if (
              this.field104Options.findIndex(
                item => item.value == data.data.table1[i].ename
              ) >= 0
            ) {
              this.data[0].children.push({
                label: data.data.table1[i].name,
                typeCate: "4", //4 name+运算符
                type: "g"
              });
            } else if (
              this.field105Options.findIndex(
                item => item.value == data.data.table1[i].ename
              ) >= 0
            ) {
              this.data[1].children.push({
                label: data.data.table1[i].name,
                typeCate: "4", //4 name+运算符
                type: "h"
              });
            } else {
              this.data[1].children.push({
                label: data.data.table1[i].name,
                typeCate: "4", //4 name+运算符
                type: "h"
              });
            }
          }
        }
      }
      this.getDate();
    },
    async getDate() {
      this.optionsselect = [];
      let data = await alphamsg({ type: "score" });
      if (data.mtycode == 200) {
        this.dataIndustry = data.data;
      }

      // console.log('xxxxxxx');
      let option = {};
      let that = this;

      axios
        .get(that.$baseUrl + "/system/alpha/filter_risk_future/?type=score")
        .then(result => {
          let res = result.data;
          this.optionsselect["最大回撤比"] = [
            { value: "now", label: "近期表现", children: [] }
          ];
          for (let i = 0; i < res.data.maxdrawdown_ratio.length; i++) {
            if (res.data.maxdrawdown_ratio[i] == "1w")
              this.optionsselect["最大回撤比"][0].children.push({
                value: "1w",
                label: "一周"
              });
            if (res.data.maxdrawdown_ratio[i] == "2w")
              this.optionsselect["最大回撤比"][0].children.push({
                value: "2w",
                label: "两周"
              });
            if (res.data.maxdrawdown_ratio[i] == "1m")
              this.optionsselect["最大回撤比"][0].children.push({
                value: "1m",
                label: "一月"
              });
            if (res.data.maxdrawdown_ratio[i] == "2m")
              this.optionsselect["最大回撤比"][0].children.push({
                value: "2m",
                label: "两月"
              });
            if (res.data.maxdrawdown_ratio[i] == "1q")
              this.optionsselect["最大回撤比"][0].children.push({
                value: "1q",
                label: "一季"
              });
            if (res.data.maxdrawdown_ratio[i] == "2q")
              this.optionsselect["最大回撤比"][0].children.push({
                value: "2q",
                label: "两季"
              });
            if (res.data.maxdrawdown_ratio[i] == "1y")
              this.optionsselect["最大回撤比"][0].children.push({
                value: "1y",
                label: "一年"
              });
            if (res.data.maxdrawdown_ratio[i] == "2y")
              this.optionsselect["最大回撤比"][0].children.push({
                value: "2y",
                label: "两年"
              });
            if (res.data.maxdrawdown_ratio[i] == "3y")
              this.optionsselect["最大回撤比"][0].children.push({
                value: "3y",
                label: "三年"
              });
            if (res.data.maxdrawdown_ratio[i] == "5y")
              this.optionsselect["最大回撤比"][0].children.push({
                value: "5y",
                label: "五年"
              });
          }
          this.optionsselect["波动率比"] = [
            { value: "now", label: "近期表现", children: [] }
          ];

          for (let i = 0; i < res.data.volatilityratio.length; i++) {
            if (res.data.volatilityratio[i] == "1w")
              this.optionsselect["波动率比"][0].children.push({
                value: "1w",
                label: "一周"
              });
            if (res.data.volatilityratio[i] == "2w")
              this.optionsselect["波动率比"][0].children.push({
                value: "2w",
                label: "两周"
              });
            if (res.data.volatilityratio[i] == "1m")
              this.optionsselect["波动率比"][0].children.push({
                value: "1m",
                label: "一月"
              });
            if (res.data.volatilityratio[i] == "2m")
              this.optionsselect["波动率比"][0].children.push({
                value: "2m",
                label: "两月"
              });
            if (res.data.volatilityratio[i] == "1q")
              this.optionsselect["波动率比"][0].children.push({
                value: "1q",
                label: "一季"
              });
            if (res.data.volatilityratio[i] == "2q")
              this.optionsselect["波动率比"][0].children.push({
                value: "2q",
                label: "两季"
              });
            if (res.data.volatilityratio[i] == "1y")
              this.optionsselect["波动率比"][0].children.push({
                value: "1y",
                label: "一年"
              });
            if (res.data.volatilityratio[i] == "2y")
              this.optionsselect["波动率比"][0].children.push({
                value: "2y",
                label: "两年"
              });
            if (res.data.volatilityratio[i] == "3y")
              this.optionsselect["波动率比"][0].children.push({
                value: "3y",
                label: "三年"
              });
            if (res.data.volatilityratio[i] == "5y")
              this.optionsselect["波动率比"][0].children.push({
                value: "5y",
                label: "五年"
              });
          }
          this.optionsselect["年化波动率"] = [
            { value: "now", label: "近期表现", children: [] }
          ];
          // if (this.haveName == '年化波动率') {
          for (let i = 0; i < res.data.volatility.length; i++) {
            if (res.data.volatility[i] == "1w")
              this.optionsselect["年化波动率"][0].children.push({
                value: "1w",
                label: "一周"
              });
            if (res.data.volatility[i] == "2w")
              this.optionsselect["年化波动率"][0].children.push({
                value: "2w",
                label: "两周"
              });
            if (res.data.volatility[i] == "1m")
              this.optionsselect["年化波动率"][0].children.push({
                value: "1m",
                label: "一月"
              });
            if (res.data.volatility[i] == "2m")
              this.optionsselect["年化波动率"][0].children.push({
                value: "2m",
                label: "两月"
              });
            if (res.data.volatility[i] == "1q")
              this.optionsselect["年化波动率"][0].children.push({
                value: "1q",
                label: "一季"
              });
            if (res.data.volatility[i] == "2q")
              this.optionsselect["年化波动率"][0].children.push({
                value: "2q",
                label: "两季"
              });
            if (res.data.volatility[i] == "1y")
              this.optionsselect["年化波动率"][0].children.push({
                value: "1y",
                label: "一年"
              });
            if (res.data.volatility[i] == "2y")
              this.optionsselect["年化波动率"][0].children.push({
                value: "2y",
                label: "两年"
              });
            if (res.data.volatility[i] == "3y")
              this.optionsselect["年化波动率"][0].children.push({
                value: "3y",
                label: "三年"
              });
            if (res.data.volatility[i] == "5y")
              this.optionsselect["年化波动率"][0].children.push({
                value: "5y",
                label: "五年"
              });
          }
          // }
          this.optionsselect["最大回撤"] = [
            { value: "now", label: "近期表现", children: [] }
          ];
          // if (this.haveName == '最大回撤') {
          for (let i = 0; i < res.data.maxdrawdown.length; i++) {
            if (res.data.maxdrawdown[i] == "1w")
              this.optionsselect["最大回撤"][0].children.push({
                value: "1w",
                label: "一周"
              });
            if (res.data.maxdrawdown[i] == "2w")
              this.optionsselect["最大回撤"][0].children.push({
                value: "2w",
                label: "两周"
              });
            if (res.data.maxdrawdown[i] == "1m")
              this.optionsselect["最大回撤"][0].children.push({
                value: "1m",
                label: "一月"
              });
            if (res.data.maxdrawdown[i] == "2m")
              this.optionsselect["最大回撤"][0].children.push({
                value: "2m",
                label: "两月"
              });
            if (res.data.maxdrawdown[i] == "1q")
              this.optionsselect["最大回撤"][0].children.push({
                value: "1q",
                label: "一季"
              });
            if (res.data.maxdrawdown[i] == "2q")
              this.optionsselect["最大回撤"][0].children.push({
                value: "2q",
                label: "两季"
              });
            if (res.data.maxdrawdown[i] == "1y")
              this.optionsselect["最大回撤"][0].children.push({
                value: "1y",
                label: "一年"
              });
            if (res.data.maxdrawdown[i] == "2y")
              this.optionsselect["最大回撤"][0].children.push({
                value: "2y",
                label: "两年"
              });
            if (res.data.maxdrawdown[i] == "3y")
              this.optionsselect["最大回撤"][0].children.push({
                value: "3y",
                label: "三年"
              });
            if (res.data.maxdrawdown[i] == "5y")
              this.optionsselect["最大回撤"][0].children.push({
                value: "5y",
                label: "五年"
              });
          }
          // }
          this.optionsselect["平均下行周期"] = [
            { value: "now", label: "近期表现", children: [] }
          ];
          // if (this.haveName == '平均下行周期') {
          for (let i = 0; i < res.data.averagelength.length; i++) {
            if (res.data.averagelength[i] == "1w")
              this.optionsselect["平均下行周期"][0].children.push({
                value: "1w",
                label: "一周"
              });
            if (res.data.averagelength[i] == "2w")
              this.optionsselect["平均下行周期"][0].children.push({
                value: "2w",
                label: "两周"
              });
            if (res.data.averagelength[i] == "1m")
              this.optionsselect["平均下行周期"][0].children.push({
                value: "1m",
                label: "一月"
              });
            if (res.data.averagelength[i] == "2m")
              this.optionsselect["平均下行周期"][0].children.push({
                value: "2m",
                label: "两月"
              });
            if (res.data.averagelength[i] == "1q")
              this.optionsselect["平均下行周期"][0].children.push({
                value: "1q",
                label: "一季"
              });
            if (res.data.averagelength[i] == "2q")
              this.optionsselect["平均下行周期"][0].children.push({
                value: "2q",
                label: "两季"
              });
            if (res.data.averagelength[i] == "1y")
              this.optionsselect["平均下行周期"][0].children.push({
                value: "1y",
                label: "一年"
              });
            if (res.data.averagelength[i] == "2y")
              this.optionsselect["平均下行周期"][0].children.push({
                value: "2y",
                label: "两年"
              });
            if (res.data.averagelength[i] == "3y")
              this.optionsselect["平均下行周期"][0].children.push({
                value: "3y",
                label: "三年"
              });
            if (res.data.averagelength[i] == "5y")
              this.optionsselect["平均下行周期"][0].children.push({
                value: "5y",
                label: "五年"
              });
          }
          this.optionsselect["平均恢复周期"] = [
            { value: "now", label: "近期表现", children: [] }
          ];
          // }
          // if (this.haveName == '平均恢复周期') {
          for (let i = 0; i < res.data.averagerecovery.length; i++) {
            if (res.data.averagerecovery[i] == "1w")
              this.optionsselect["平均恢复周期"][0].children.push({
                value: "1w",
                label: "一周"
              });
            if (res.data.averagerecovery[i] == "2w")
              this.optionsselect["平均恢复周期"][0].children.push({
                value: "2w",
                label: "两周"
              });
            if (res.data.averagerecovery[i] == "1m")
              this.optionsselect["平均恢复周期"][0].children.push({
                value: "1m",
                label: "一月"
              });
            if (res.data.averagerecovery[i] == "2m")
              this.optionsselect["平均恢复周期"][0].children.push({
                value: "2m",
                label: "两月"
              });
            if (res.data.averagerecovery[i] == "1q")
              this.optionsselect["平均恢复周期"][0].children.push({
                value: "1q",
                label: "一季"
              });
            if (res.data.averagerecovery[i] == "2q")
              this.optionsselect["平均恢复周期"][0].children.push({
                value: "2q",
                label: "两季"
              });
            if (res.data.averagerecovery[i] == "1y")
              this.optionsselect["平均恢复周期"][0].children.push({
                value: "1y",
                label: "一年"
              });
            if (res.data.averagerecovery[i] == "2y")
              this.optionsselect["平均恢复周期"][0].children.push({
                value: "2y",
                label: "两年"
              });
            if (res.data.averagerecovery[i] == "3y")
              this.optionsselect["平均恢复周期"][0].children.push({
                value: "3y",
                label: "三年"
              });
            if (res.data.averagerecovery[i] == "5y")
              this.optionsselect["平均恢复周期"][0].children.push({
                value: "5y",
                label: "五年"
              });
          }
          // }
          this.optionsselect["在险价值"] = [
            { value: "now", label: "近期表现", children: [] }
          ];
          // if (this.haveName == '在险价值') {
          for (let i = 0; i < res.data.volatility.length; i++) {
            if (res.data.VaR05[i] == "1w")
              this.optionsselect["在险价值"][0].children.push({
                value: "1w",
                label: "一周"
              });
            if (res.data.VaR05[i] == "2w")
              this.optionsselect["在险价值"][0].children.push({
                value: "2w",
                label: "两周"
              });
            if (res.data.VaR05[i] == "1m")
              this.optionsselect["在险价值"][0].children.push({
                value: "1m",
                label: "一月"
              });
            if (res.data.VaR05[i] == "2m")
              this.optionsselect["在险价值"][0].children.push({
                value: "2m",
                label: "两月"
              });
            if (res.data.VaR05[i] == "1q")
              this.optionsselect["在险价值"][0].children.push({
                value: "1q",
                label: "一季"
              });
            if (res.data.VaR05[i] == "2q")
              this.optionsselect["在险价值"][0].children.push({
                value: "2q",
                label: "两季"
              });
            if (res.data.VaR05[i] == "1y")
              this.optionsselect["在险价值"][0].children.push({
                value: "1y",
                label: "一年"
              });
            if (res.data.VaR05[i] == "2y")
              this.optionsselect["在险价值"][0].children.push({
                value: "2y",
                label: "两年"
              });
            if (res.data.VaR05[i] == "3y")
              this.optionsselect["在险价值"][0].children.push({
                value: "3y",
                label: "三年"
              });
            if (res.data.VaR05[i] == "5y")
              this.optionsselect["在险价值"][0].children.push({
                value: "5y",
                label: "五年"
              });
            // }
          }
          this.optionsselect["期望损失"] = [
            { value: "now", label: "近期表现", children: [] }
          ];
          // if (this.haveName == '期望损失') {
          for (let i = 0; i < res.data.ES05.length; i++) {
            if (res.data.ES05[i] == "1w")
              this.optionsselect["期望损失"][0].children.push({
                value: "1w",
                label: "一周"
              });
            if (res.data.ES05[i] == "2w")
              this.optionsselect["期望损失"][0].children.push({
                value: "2w",
                label: "两周"
              });
            if (res.data.ES05[i] == "1m")
              this.optionsselect["期望损失"][0].children.push({
                value: "1m",
                label: "一月"
              });
            if (res.data.ES05[i] == "2m")
              this.optionsselect["期望损失"][0].children.push({
                value: "2m",
                label: "两月"
              });
            if (res.data.ES05[i] == "1q")
              this.optionsselect["期望损失"][0].children.push({
                value: "1q",
                label: "一季"
              });
            if (res.data.ES05[i] == "2q")
              this.optionsselect["期望损失"][0].children.push({
                value: "2q",
                label: "两季"
              });
            if (res.data.ES05[i] == "1y")
              this.optionsselect["期望损失"][0].children.push({
                value: "1y",
                label: "一年"
              });
            if (res.data.ES05[i] == "2y")
              this.optionsselect["期望损失"][0].children.push({
                value: "2y",
                label: "两年"
              });
            if (res.data.ES05[i] == "3y")
              this.optionsselect["期望损失"][0].children.push({
                value: "3y",
                label: "三年"
              });
            if (res.data.ES05[i] == "5y")
              this.optionsselect["期望损失"][0].children.push({
                value: "5y",
                label: "五年"
              });
          }
          // }
          this.optionsselect["下行风险"] = [
            { value: "now", label: "近期表现", children: [] }
          ];
          // if (this.haveName == '下行风险') {
          for (let i = 0; i < res.data.downsidevolatility.length; i++) {
            if (res.data.downsidevolatility[i] == "1w")
              this.optionsselect["下行风险"][0].children.push({
                value: "1w",
                label: "一周"
              });
            if (res.data.downsidevolatility[i] == "2w")
              this.optionsselect["下行风险"][0].children.push({
                value: "2w",
                label: "两周"
              });
            if (res.data.downsidevolatility[i] == "1m")
              this.optionsselect["下行风险"][0].children.push({
                value: "1m",
                label: "一月"
              });
            if (res.data.downsidevolatility[i] == "2m")
              this.optionsselect["下行风险"][0].children.push({
                value: "2m",
                label: "两月"
              });
            if (res.data.downsidevolatility[i] == "1q")
              this.optionsselect["下行风险"][0].children.push({
                value: "1q",
                label: "一季"
              });
            if (res.data.downsidevolatility[i] == "2q")
              this.optionsselect["下行风险"][0].children.push({
                value: "2q",
                label: "两季"
              });
            if (res.data.downsidevolatility[i] == "1y")
              this.optionsselect["下行风险"][0].children.push({
                value: "1y",
                label: "一年"
              });
            if (res.data.downsidevolatility[i] == "2y")
              this.optionsselect["下行风险"][0].children.push({
                value: "2y",
                label: "两年"
              });
            if (res.data.downsidevolatility[i] == "3y")
              this.optionsselect["下行风险"][0].children.push({
                value: "3y",
                label: "三年"
              });
            if (res.data.downsidevolatility[i] == "5y")
              this.optionsselect["下行风险"][0].children.push({
                value: "5y",
                label: "五年"
              });
          }
          this.optionsselect["痛苦指数"] = [
            { value: "now", label: "近期表现", children: [] }
          ];
          // }
          // if (this.haveName == '痛苦指数') {
          for (let i = 0; i < res.data.painindex.length; i++) {
            if (res.data.painindex[i] == "1w")
              this.optionsselect["痛苦指数"][0].children.push({
                value: "1w",
                label: "一周"
              });
            if (res.data.painindex[i] == "2w")
              this.optionsselect["痛苦指数"][0].children.push({
                value: "2w",
                label: "两周"
              });
            if (res.data.painindex[i] == "1m")
              this.optionsselect["痛苦指数"][0].children.push({
                value: "1m",
                label: "一月"
              });
            if (res.data.painindex[i] == "2m")
              this.optionsselect["痛苦指数"][0].children.push({
                value: "2m",
                label: "两月"
              });
            if (res.data.painindex[i] == "1q")
              this.optionsselect["痛苦指数"][0].children.push({
                value: "1q",
                label: "一季"
              });
            if (res.data.painindex[i] == "2q")
              this.optionsselect["痛苦指数"][0].children.push({
                value: "2q",
                label: "两季"
              });
            if (res.data.painindex[i] == "1y")
              this.optionsselect["痛苦指数"][0].children.push({
                value: "1y",
                label: "一年"
              });
            if (res.data.painindex[i] == "2y")
              this.optionsselect["痛苦指数"][0].children.push({
                value: "2y",
                label: "两年"
              });
            if (res.data.painindex[i] == "3y")
              this.optionsselect["痛苦指数"][0].children.push({
                value: "3y",
                label: "三年"
              });
            if (res.data.painindex[i] == "5y")
              this.optionsselect["痛苦指数"][0].children.push({
                value: "5y",
                label: "五年"
              });
          }
          // }
          // console.log('????????????????????????????????????');
          // ////console.log(res.data)
          this.optionsselect["日频择时能力"] = [
            {
              value: "now",
              label: "近期表现",
              children: [
                { value: "1w", label: "一周" },
                { value: "2w", label: "两周" },
                { value: "1m", label: "一月" },
                { value: "2m", label: "两月" },
                { value: "1q", label: "一季" },
                { value: "2q", label: "两季" },
                { value: "1y", label: "一年" },
                { value: "2y", label: "两年" },
                { value: "3y", label: "三年" },
                { value: "5y", label: "五年" }
              ]
            }
          ];

          this.optionsselect["年化收益率"] = [
            { value: "now", label: "近期表现", children: [] }
          ];
          // if (this.haveName == '年化收益率') {
          for (let i = 0; i < res.data.ave_return.length; i++) {
            if (res.data.ave_return[i] == "1w")
              this.optionsselect["年化收益率"][0].children.push({
                value: "1w",
                label: "一周"
              });
            if (res.data.ave_return[i] == "2w")
              this.optionsselect["年化收益率"][0].children.push({
                value: "2w",
                label: "两周"
              });
            if (res.data.ave_return[i] == "1m")
              this.optionsselect["年化收益率"][0].children.push({
                value: "1m",
                label: "一月"
              });
            if (res.data.ave_return[i] == "2m")
              this.optionsselect["年化收益率"][0].children.push({
                value: "2m",
                label: "两月"
              });
            if (res.data.ave_return[i] == "1q")
              this.optionsselect["年化收益率"][0].children.push({
                value: "1q",
                label: "一季"
              });
            if (res.data.ave_return[i] == "2q")
              this.optionsselect["年化收益率"][0].children.push({
                value: "2q",
                label: "两季"
              });
            if (res.data.ave_return[i] == "1y")
              this.optionsselect["年化收益率"][0].children.push({
                value: "1y",
                label: "一年"
              });
            if (res.data.ave_return[i] == "2y")
              this.optionsselect["年化收益率"][0].children.push({
                value: "2y",
                label: "两年"
              });
            if (res.data.ave_return[i] == "3y")
              this.optionsselect["年化收益率"][0].children.push({
                value: "3y",
                label: "三年"
              });
            if (res.data.ave_return[i] == "5y")
              this.optionsselect["年化收益率"][0].children.push({
                value: "5y",
                label: "五年"
              });
          }
          // }
          this.optionsselect["累计收益率"] = [
            { value: "now", label: "近期表现", children: [] }
          ];
          // if (this.haveName == '累计收益率') {
          for (let i = 0; i < res.data.cum_return.length; i++) {
            if (res.data.cum_return[i] == "1w")
              this.optionsselect["累计收益率"][0].children.push({
                value: "1w",
                label: "一周"
              });
            if (res.data.cum_return[i] == "2w")
              this.optionsselect["累计收益率"][0].children.push({
                value: "2w",
                label: "两周"
              });
            if (res.data.cum_return[i] == "1m")
              this.optionsselect["累计收益率"][0].children.push({
                value: "1m",
                label: "一月"
              });
            if (res.data.cum_return[i] == "2m")
              this.optionsselect["累计收益率"][0].children.push({
                value: "2m",
                label: "两月"
              });
            if (res.data.cum_return[i] == "1q")
              this.optionsselect["累计收益率"][0].children.push({
                value: "1q",
                label: "一季"
              });
            if (res.data.cum_return[i] == "2q")
              this.optionsselect["累计收益率"][0].children.push({
                value: "2q",
                label: "两季"
              });
            if (res.data.cum_return[i] == "1y")
              this.optionsselect["累计收益率"][0].children.push({
                value: "1y",
                label: "一年"
              });
            if (res.data.cum_return[i] == "2y")
              this.optionsselect["累计收益率"][0].children.push({
                value: "2y",
                label: "两年"
              });
            if (res.data.cum_return[i] == "3y")
              this.optionsselect["累计收益率"][0].children.push({
                value: "3y",
                label: "三年"
              });
            if (res.data.cum_return[i] == "5y")
              this.optionsselect["累计收益率"][0].children.push({
                value: "5y",
                label: "五年"
              });
            // }
          }
          this.optionsselect["夏普率（rf=0）"] = [
            { value: "now", label: "近期表现", children: [] }
          ];
          // if (this.haveName == '夏普率（rf=0）') {
          for (let i = 0; i < res.data.sharpe0.length; i++) {
            if (res.data.sharpe0[i] == "1w")
              this.optionsselect["夏普率（rf=0）"][0].children.push({
                value: "1w",
                label: "一周"
              });
            if (res.data.sharpe0[i] == "2w")
              this.optionsselect["夏普率（rf=0）"][0].children.push({
                value: "2w",
                label: "两周"
              });
            if (res.data.sharpe0[i] == "1m")
              this.optionsselect["夏普率（rf=0）"][0].children.push({
                value: "1m",
                label: "一月"
              });
            if (res.data.sharpe0[i] == "2m")
              this.optionsselect["夏普率（rf=0）"][0].children.push({
                value: "2m",
                label: "两月"
              });
            if (res.data.sharpe0[i] == "1q")
              this.optionsselect["夏普率（rf=0）"][0].children.push({
                value: "1q",
                label: "一季"
              });
            if (res.data.sharpe0[i] == "2q")
              this.optionsselect["夏普率（rf=0）"][0].children.push({
                value: "2q",
                label: "两季"
              });
            if (res.data.sharpe0[i] == "1y")
              this.optionsselect["夏普率（rf=0）"][0].children.push({
                value: "1y",
                label: "一年"
              });
            if (res.data.sharpe0[i] == "2y")
              this.optionsselect["夏普率（rf=0）"][0].children.push({
                value: "2y",
                label: "两年"
              });
            if (res.data.sharpe0[i] == "3y")
              this.optionsselect["夏普率（rf=0）"][0].children.push({
                value: "3y",
                label: "三年"
              });
            if (res.data.sharpe0[i] == "5y")
              this.optionsselect["夏普率（rf=0）"][0].children.push({
                value: "5y",
                label: "五年"
              });
          }
          this.optionsselect["夏普率"] = [
            { value: "now", label: "近期表现", children: [] }
          ];
          // if (this.haveName == '夏普率（rf=0）') {
          for (let i = 0; i < res.data.sharpe0.length; i++) {
            if (res.data.sharpe0[i] == "1w")
              this.optionsselect["夏普率"][0].children.push({
                value: "1w",
                label: "一周"
              });
            if (res.data.sharpe0[i] == "2w")
              this.optionsselect["夏普率"][0].children.push({
                value: "2w",
                label: "两周"
              });
            if (res.data.sharpe0[i] == "1m")
              this.optionsselect["夏普率"][0].children.push({
                value: "1m",
                label: "一月"
              });
            if (res.data.sharpe0[i] == "2m")
              this.optionsselect["夏普率"][0].children.push({
                value: "2m",
                label: "两月"
              });
            if (res.data.sharpe0[i] == "1q")
              this.optionsselect["夏普率"][0].children.push({
                value: "1q",
                label: "一季"
              });
            if (res.data.sharpe0[i] == "2q")
              this.optionsselect["夏普率"][0].children.push({
                value: "2q",
                label: "两季"
              });
            if (res.data.sharpe0[i] == "1y")
              this.optionsselect["夏普率"][0].children.push({
                value: "1y",
                label: "一年"
              });
            if (res.data.sharpe0[i] == "2y")
              this.optionsselect["夏普率"][0].children.push({
                value: "2y",
                label: "两年"
              });
            if (res.data.sharpe0[i] == "3y")
              this.optionsselect["夏普率"][0].children.push({
                value: "3y",
                label: "三年"
              });
            if (res.data.sharpe0[i] == "5y")
              this.optionsselect["夏普率"][0].children.push({
                value: "5y",
                label: "五年"
              });
          }
          // }
          this.optionsselect["夏普率（rf=0.04）"] = [
            { value: "now", label: "近期表现", children: [] }
          ];
          // if (this.haveName == '夏普率（rf=4%）') {
          // console.log("夏普率（rf==4%");
          for (let i = 0; i < res.data.sharpe04.length; i++) {
            if (res.data.sharpe04[i] == "1w")
              this.optionsselect["夏普率（rf=0.04）"][0].children.push({
                value: "1w",
                label: "一周"
              });
            if (res.data.sharpe04[i] == "2w")
              this.optionsselect["夏普率（rf=0.04）"][0].children.push({
                value: "2w",
                label: "两周"
              });
            if (res.data.sharpe04[i] == "1m")
              this.optionsselect["夏普率（rf=0.04）"][0].children.push({
                value: "1m",
                label: "一月"
              });
            if (res.data.sharpe04[i] == "2m")
              this.optionsselect["夏普率（rf=0.04）"][0].children.push({
                value: "2m",
                label: "两月"
              });
            if (res.data.sharpe04[i] == "1q")
              this.optionsselect["夏普率（rf=0.04）"][0].children.push({
                value: "1q",
                label: "一季"
              });
            if (res.data.sharpe04[i] == "2q")
              this.optionsselect["夏普率（rf=0.04）"][0].children.push({
                value: "2q",
                label: "两季"
              });
            if (res.data.sharpe04[i] == "1y")
              this.optionsselect["夏普率（rf=0.04）"][0].children.push({
                value: "1y",
                label: "一年"
              });
            if (res.data.sharpe04[i] == "2y")
              this.optionsselect["夏普率（rf=0.04）"][0].children.push({
                value: "2y",
                label: "两年"
              });
            if (res.data.sharpe04[i] == "3y")
              this.optionsselect["夏普率（rf=0.04）"][0].children.push({
                value: "3y",
                label: "三年"
              });
            if (res.data.sharpe04[i] == "5y")
              this.optionsselect["夏普率（rf=0.04）"][0].children.push({
                value: "5y",
                label: "五年"
              });
            // }
            // console.log(object);
          }
          this.optionsselect["夏普率（动态rf）"] = [
            { value: "now", label: "近期表现", children: [] }
          ];
          // if (this.haveName == '夏普率（动态rf）' || this.haveName == '夏普率') {
          for (let i = 0; i < res.data.sharpe.length; i++) {
            if (res.data.sharpe[i] == "1w")
              this.optionsselect["夏普率（动态rf）"][0].children.push({
                value: "1w",
                label: "一周"
              });
            if (res.data.sharpe[i] == "2w")
              this.optionsselect["夏普率（动态rf）"][0].children.push({
                value: "2w",
                label: "两周"
              });
            if (res.data.sharpe[i] == "1m")
              this.optionsselect["夏普率（动态rf）"][0].children.push({
                value: "1m",
                label: "一月"
              });
            if (res.data.sharpe[i] == "2m")
              this.optionsselect["夏普率（动态rf）"][0].children.push({
                value: "2m",
                label: "两月"
              });
            if (res.data.sharpe[i] == "1q")
              this.optionsselect["夏普率（动态rf）"][0].children.push({
                value: "1q",
                label: "一季"
              });
            if (res.data.sharpe[i] == "2q")
              this.optionsselect["夏普率（动态rf）"][0].children.push({
                value: "2q",
                label: "两季"
              });
            if (res.data.sharpe[i] == "1y")
              this.optionsselect["夏普率（动态rf）"][0].children.push({
                value: "1y",
                label: "一年"
              });
            if (res.data.sharpe[i] == "2y")
              this.optionsselect["夏普率（动态rf）"][0].children.push({
                value: "2y",
                label: "两年"
              });
            if (res.data.sharpe[i] == "3y")
              this.optionsselect["夏普率（动态rf）"][0].children.push({
                value: "3y",
                label: "三年"
              });
            if (res.data.sharpe[i] == "5y")
              this.optionsselect["夏普率（动态rf）"][0].children.push({
                value: "5y",
                label: "五年"
              });
            // }
          }
          this.optionsselect["卡码率"] = [
            { value: "now", label: "近期表现", children: [] }
          ];
          // if (this.haveName == '卡码率') {
          for (let i = 0; i < res.data.calmar.length; i++) {
            if (res.data.calmar[i] == "1w")
              this.optionsselect["卡码率"][0].children.push({
                value: "1w",
                label: "一周"
              });
            if (res.data.calmar[i] == "2w")
              this.optionsselect["卡码率"][0].children.push({
                value: "2w",
                label: "两周"
              });
            if (res.data.calmar[i] == "1m")
              this.optionsselect["卡码率"][0].children.push({
                value: "1m",
                label: "一月"
              });
            if (res.data.calmar[i] == "2m")
              this.optionsselect["卡码率"][0].children.push({
                value: "2m",
                label: "两月"
              });
            if (res.data.calmar[i] == "1q")
              this.optionsselect["卡码率"][0].children.push({
                value: "1q",
                label: "一季"
              });
            if (res.data.calmar[i] == "2q")
              this.optionsselect["卡码率"][0].children.push({
                value: "2q",
                label: "两季"
              });
            if (res.data.calmar[i] == "1y")
              this.optionsselect["卡码率"][0].children.push({
                value: "1y",
                label: "一年"
              });
            if (res.data.calmar[i] == "2y")
              this.optionsselect["卡码率"][0].children.push({
                value: "2y",
                label: "两年"
              });
            if (res.data.calmar[i] == "3y")
              this.optionsselect["卡码率"][0].children.push({
                value: "3y",
                label: "三年"
              });
            if (res.data.calmar[i] == "5y")
              this.optionsselect["卡码率"][0].children.push({
                value: "5y",
                label: "五年"
              });
            // }
          }
          this.optionsselect["索提诺系数（rf=0）"] = [
            { value: "now", label: "近期表现", children: [] }
          ];
          // if (this.haveName == '索提诺系数（rf=0）') {
          for (let i = 0; i < res.data.sortino0.length; i++) {
            if (res.data.sortino0[i] == "1w")
              this.optionsselect["索提诺系数（rf=0）"][0].children.push({
                value: "1w",
                label: "一周"
              });
            if (res.data.sortino0[i] == "2w")
              this.optionsselect["索提诺系数（rf=0）"][0].children.push({
                value: "2w",
                label: "两周"
              });
            if (res.data.sortino0[i] == "1m")
              this.optionsselect["索提诺系数（rf=0）"][0].children.push({
                value: "1m",
                label: "一月"
              });
            if (res.data.sortino0[i] == "2m")
              this.optionsselect["索提诺系数（rf=0）"][0].children.push({
                value: "2m",
                label: "两月"
              });
            if (res.data.sortino0[i] == "1q")
              this.optionsselect["索提诺系数（rf=0）"][0].children.push({
                value: "1q",
                label: "一季"
              });
            if (res.data.sortino0[i] == "2q")
              this.optionsselect["索提诺系数（rf=0）"][0].children.push({
                value: "2q",
                label: "两季"
              });
            if (res.data.sortino0[i] == "1y")
              this.optionsselect["索提诺系数（rf=0）"][0].children.push({
                value: "1y",
                label: "一年"
              });
            if (res.data.sortino0[i] == "2y")
              this.optionsselect["索提诺系数（rf=0）"][0].children.push({
                value: "2y",
                label: "两年"
              });
            if (res.data.sortino0[i] == "3y")
              this.optionsselect["索提诺系数（rf=0）"][0].children.push({
                value: "3y",
                label: "三年"
              });
            if (res.data.sortino0[i] == "5y")
              this.optionsselect["索提诺系数（rf=0）"][0].children.push({
                value: "5y",
                label: "五年"
              });
          }
          // }
          this.optionsselect["索提诺系数（rf=0.04）"] = [
            { value: "now", label: "近期表现", children: [] }
          ];
          // if (this.haveName == '索提诺系数（rf=0.04）') {
          for (let i = 0; i < res.data.sortino04.length; i++) {
            if (res.data.sortino04[i] == "1w")
              this.optionsselect["索提诺系数（rf=0.04）"][0].children.push({
                value: "1w",
                label: "一周"
              });
            if (res.data.sortino04[i] == "2w")
              this.optionsselect["索提诺系数（rf=0.04）"][0].children.push({
                value: "2w",
                label: "两周"
              });
            if (res.data.sortino04[i] == "1m")
              this.optionsselect["索提诺系数（rf=0.04）"][0].children.push({
                value: "1m",
                label: "一月"
              });
            if (res.data.sortino04[i] == "2m")
              this.optionsselect["索提诺系数（rf=0.04）"][0].children.push({
                value: "2m",
                label: "两月"
              });
            if (res.data.sortino04[i] == "1q")
              this.optionsselect["索提诺系数（rf=0.04）"][0].children.push({
                value: "1q",
                label: "一季"
              });
            if (res.data.sortino04[i] == "2q")
              this.optionsselect["索提诺系数（rf=0.04）"][0].children.push({
                value: "2q",
                label: "两季"
              });
            if (res.data.sortino04[i] == "1y")
              this.optionsselect["索提诺系数（rf=0.04）"][0].children.push({
                value: "1y",
                label: "一年"
              });
            if (res.data.sortino04[i] == "2y")
              this.optionsselect["索提诺系数（rf=0.04）"][0].children.push({
                value: "2y",
                label: "两年"
              });
            if (res.data.sortino04[i] == "3y")
              this.optionsselect["索提诺系数（rf=0.04）"][0].children.push({
                value: "3y",
                label: "三年"
              });
            if (res.data.sortino04[i] == "5y")
              this.optionsselect["索提诺系数（rf=0.04）"][0].children.push({
                value: "5y",
                label: "五年"
              });
          }
          // }
          this.optionsselect["索提诺系数（动态rf）"] = [
            { value: "now", label: "近期表现", children: [] }
          ];
          // if (this.haveName == '索提诺系数（动态rf）') {
          for (let i = 0; i < res.data.sortino.length; i++) {
            if (res.data.sortino[i] == "1w")
              this.optionsselect["索提诺系数（动态rf）"][0].children.push({
                value: "1w",
                label: "一周"
              });
            if (res.data.sortino[i] == "2w")
              this.optionsselect["索提诺系数（动态rf）"][0].children.push({
                value: "2w",
                label: "两周"
              });
            if (res.data.sortino[i] == "1m")
              this.optionsselect["索提诺系数（动态rf）"][0].children.push({
                value: "1m",
                label: "一月"
              });
            if (res.data.sortino[i] == "2m")
              this.optionsselect["索提诺系数（动态rf）"][0].children.push({
                value: "2m",
                label: "两月"
              });
            if (res.data.sortino[i] == "1q")
              this.optionsselect["索提诺系数（动态rf）"][0].children.push({
                value: "1q",
                label: "一季"
              });
            if (res.data.sortino[i] == "2q")
              this.optionsselect["索提诺系数（动态rf）"][0].children.push({
                value: "2q",
                label: "两季"
              });
            if (res.data.sortino[i] == "1y")
              this.optionsselect["索提诺系数（动态rf）"][0].children.push({
                value: "1y",
                label: "一年"
              });
            if (res.data.sortino[i] == "2y")
              this.optionsselect["索提诺系数（动态rf）"][0].children.push({
                value: "2y",
                label: "两年"
              });
            if (res.data.sortino[i] == "3y")
              this.optionsselect["索提诺系数（动态rf）"][0].children.push({
                value: "3y",
                label: "三年"
              });
            if (res.data.sortino[i] == "5y")
              this.optionsselect["索提诺系数（动态rf）"][0].children.push({
                value: "5y",
                label: "五年"
              });
          }
          // }
          this.optionsselect["稳定系数"] = [
            { value: "now", label: "近期表现", children: [] }
          ];
          // if (this.haveName == '稳定系数') {
          for (let i = 0; i < res.data.hurstindex.length; i++) {
            if (res.data.hurstindex[i] == "1w")
              this.optionsselect["稳定系数"][0].children.push({
                value: "1w",
                label: "一周"
              });
            if (res.data.hurstindex[i] == "2w")
              this.optionsselect["稳定系数"][0].children.push({
                value: "2w",
                label: "两周"
              });
            if (res.data.hurstindex[i] == "1m")
              this.optionsselect["稳定系数"][0].children.push({
                value: "1m",
                label: "一月"
              });
            if (res.data.hurstindex[i] == "2m")
              this.optionsselect["稳定系数"][0].children.push({
                value: "2m",
                label: "两月"
              });
            if (res.data.hurstindex[i] == "1q")
              this.optionsselect["稳定系数"][0].children.push({
                value: "1q",
                label: "一季"
              });
            if (res.data.hurstindex[i] == "2q")
              this.optionsselect["稳定系数"][0].children.push({
                value: "2q",
                label: "两季"
              });
            if (res.data.hurstindex[i] == "1y")
              this.optionsselect["稳定系数"][0].children.push({
                value: "1y",
                label: "一年"
              });
            if (res.data.hurstindex[i] == "2y")
              this.optionsselect["稳定系数"][0].children.push({
                value: "2y",
                label: "两年"
              });
            if (res.data.hurstindex[i] == "3y")
              this.optionsselect["稳定系数"][0].children.push({
                value: "3y",
                label: "三年"
              });
            if (res.data.hurstindex[i] == "5y")
              this.optionsselect["稳定系数"][0].children.push({
                value: "5y",
                label: "五年"
              });
            // }
          }
          this.optionsselect["凯利系数"] = [
            { value: "now", label: "近期表现", children: [] }
          ];
          // if (this.haveName == '凯利系数') {
          for (let i = 0; i < res.data.kelly.length; i++) {
            if (res.data.kelly[i] == "1w")
              this.optionsselect["凯利系数"][0].children.push({
                value: "1w",
                label: "一周"
              });
            if (res.data.kelly[i] == "2w")
              this.optionsselect["凯利系数"][0].children.push({
                value: "2w",
                label: "两周"
              });
            if (res.data.kelly[i] == "1m")
              this.optionsselect["凯利系数"][0].children.push({
                value: "1m",
                label: "一月"
              });
            if (res.data.kelly[i] == "2m")
              this.optionsselect["凯利系数"][0].children.push({
                value: "2m",
                label: "两月"
              });
            if (res.data.kelly[i] == "1q")
              this.optionsselect["凯利系数"][0].children.push({
                value: "1q",
                label: "一季"
              });
            if (res.data.kelly[i] == "2q")
              this.optionsselect["凯利系数"][0].children.push({
                value: "2q",
                label: "两季"
              });
            if (res.data.kelly[i] == "1y")
              this.optionsselect["凯利系数"][0].children.push({
                value: "1y",
                label: "一年"
              });
            if (res.data.kelly[i] == "2y")
              this.optionsselect["凯利系数"][0].children.push({
                value: "2y",
                label: "两年"
              });
            if (res.data.kelly[i] == "3y")
              this.optionsselect["凯利系数"][0].children.push({
                value: "3y",
                label: "三年"
              });
            if (res.data.kelly[i] == "5y")
              this.optionsselect["凯利系数"][0].children.push({
                value: "5y",
                label: "五年"
              });
            // }
          }
          this.optionsselect["信息比率"] = [
            { value: "now", label: "近期表现", children: [] }
          ];
          // if (this.haveName == '信息比率') {
          for (let i = 0; i < res.data.information.length; i++) {
            if (res.data.information[i] == "1w")
              this.optionsselect["信息比率"][0].children.push({
                value: "1w",
                label: "一周"
              });
            if (res.data.information[i] == "2w")
              this.optionsselect["信息比率"][0].children.push({
                value: "2w",
                label: "两周"
              });
            if (res.data.information[i] == "1m")
              this.optionsselect["信息比率"][0].children.push({
                value: "1m",
                label: "一月"
              });
            if (res.data.information[i] == "2m")
              this.optionsselect["信息比率"][0].children.push({
                value: "2m",
                label: "两月"
              });
            if (res.data.information[i] == "1q")
              this.optionsselect["信息比率"][0].children.push({
                value: "1q",
                label: "一季"
              });
            if (res.data.information[i] == "2q")
              this.optionsselect["信息比率"][0].children.push({
                value: "2q",
                label: "两季"
              });
            if (res.data.information[i] == "1y")
              this.optionsselect["信息比率"][0].children.push({
                value: "1y",
                label: "一年"
              });
            if (res.data.information[i] == "2y")
              this.optionsselect["信息比率"][0].children.push({
                value: "2y",
                label: "两年"
              });
            if (res.data.information[i] == "3y")
              this.optionsselect["信息比率"][0].children.push({
                value: "3y",
                label: "三年"
              });
            if (res.data.information[i] == "5y")
              this.optionsselect["信息比率"][0].children.push({
                value: "5y",
                label: "五年"
              });
            // }
          }
          this.optionsselect["上攻潜力"] = [
            { value: "now", label: "近期表现", children: [] }
          ];
          // if (this.haveName == '上攻潜力') {
          for (let i = 0; i < res.data.upsidepotential.length; i++) {
            if (res.data.upsidepotential[i] == "1w")
              this.optionsselect["上攻潜力"][0].children.push({
                value: "1w",
                label: "一周"
              });
            if (res.data.upsidepotential[i] == "2w")
              this.optionsselect["上攻潜力"][0].children.push({
                value: "2w",
                label: "两周"
              });
            if (res.data.upsidepotential[i] == "1m")
              this.optionsselect["上攻潜力"][0].children.push({
                value: "1m",
                label: "一月"
              });
            if (res.data.upsidepotential[i] == "2m")
              this.optionsselect["上攻潜力"][0].children.push({
                value: "2m",
                label: "两月"
              });
            if (res.data.upsidepotential[i] == "1q")
              this.optionsselect["上攻潜力"][0].children.push({
                value: "1q",
                label: "一季"
              });
            if (res.data.upsidepotential[i] == "2q")
              this.optionsselect["上攻潜力"][0].children.push({
                value: "2q",
                label: "两季"
              });
            if (res.data.upsidepotential[i] == "1y")
              this.optionsselect["上攻潜力"][0].children.push({
                value: "1y",
                label: "一年"
              });
            if (res.data.upsidepotential[i] == "2y")
              this.optionsselect["上攻潜力"][0].children.push({
                value: "2y",
                label: "两年"
              });
            if (res.data.upsidepotential[i] == "3y")
              this.optionsselect["上攻潜力"][0].children.push({
                value: "3y",
                label: "三年"
              });
            if (res.data.upsidepotential[i] == "5y")
              this.optionsselect["上攻潜力"][0].children.push({
                value: "5y",
                label: "五年"
              });
            // }
          }
          this.optionsselect["月胜率"] = [
            { value: "now", label: "近期表现", children: [] }
          ];
          // if (this.haveName == '月胜率') {
          for (let i = 0; i < res.data.monthly_win_ratio.length; i++) {
            if (res.data.monthly_win_ratio[i] == "1w")
              this.optionsselect["月胜率"][0].children.push({
                value: "1w",
                label: "一周"
              });
            if (res.data.monthly_win_ratio[i] == "2w")
              this.optionsselect["月胜率"][0].children.push({
                value: "2w",
                label: "两周"
              });
            if (res.data.monthly_win_ratio[i] == "1m")
              this.optionsselect["月胜率"][0].children.push({
                value: "1m",
                label: "一月"
              });
            if (res.data.monthly_win_ratio[i] == "2m")
              this.optionsselect["月胜率"][0].children.push({
                value: "2m",
                label: "两月"
              });
            if (res.data.monthly_win_ratio[i] == "1q")
              this.optionsselect["月胜率"][0].children.push({
                value: "1q",
                label: "一季"
              });
            if (res.data.monthly_win_ratio[i] == "2q")
              this.optionsselect["月胜率"][0].children.push({
                value: "2q",
                label: "两季"
              });
            if (res.data.monthly_win_ratio[i] == "1y")
              this.optionsselect["月胜率"][0].children.push({
                value: "1y",
                label: "一年"
              });
            if (res.data.monthly_win_ratio[i] == "2y")
              this.optionsselect["月胜率"][0].children.push({
                value: "2y",
                label: "两年"
              });
            if (res.data.monthly_win_ratio[i] == "3y")
              this.optionsselect["月胜率"][0].children.push({
                value: "3y",
                label: "三年"
              });
            if (res.data.monthly_win_ratio[i] == "5y")
              this.optionsselect["月胜率"][0].children.push({
                value: "5y",
                label: "五年"
              });
            // }
          }
          this.optionsselect["季度胜率"] = this.optionsselect["月胜率"];
          this.optionsselect["周胜率"] = this.optionsselect["月胜率"];
          this.optionsselect["詹森系数"] = [
            { value: "now", label: "近期表现", children: [] }
          ];
          // if (this.haveName == '詹森系数') {
          for (let i = 0; i < res.data.jensen.length; i++) {
            if (res.data.jensen[i] == "1w")
              this.optionsselect["詹森系数"][0].children.push({
                value: "1w",
                label: "一周"
              });
            if (res.data.jensen[i] == "2w")
              this.optionsselect["詹森系数"][0].children.push({
                value: "2w",
                label: "两周"
              });
            if (res.data.jensen[i] == "1m")
              this.optionsselect["詹森系数"][0].children.push({
                value: "1m",
                label: "一月"
              });
            if (res.data.jensen[i] == "2m")
              this.optionsselect["詹森系数"][0].children.push({
                value: "2m",
                label: "两月"
              });
            if (res.data.jensen[i] == "1q")
              this.optionsselect["詹森系数"][0].children.push({
                value: "1q",
                label: "一季"
              });
            if (res.data.jensen[i] == "2q")
              this.optionsselect["詹森系数"][0].children.push({
                value: "2q",
                label: "两季"
              });
            if (res.data.jensen[i] == "1y")
              this.optionsselect["詹森系数"][0].children.push({
                value: "1y",
                label: "一年"
              });
            if (res.data.jensen[i] == "2y")
              this.optionsselect["詹森系数"][0].children.push({
                value: "2y",
                label: "两年"
              });
            if (res.data.jensen[i] == "3y")
              this.optionsselect["詹森系数"][0].children.push({
                value: "3y",
                label: "三年"
              });
            if (res.data.jensen[i] == "5y")
              this.optionsselect["詹森系数"][0].children.push({
                value: "5y",
                label: "五年"
              });
            // }
          }
          this.optionsselect["特诺系数"] = [
            { value: "now", label: "近期表现", children: [] }
          ];
          // if (this.haveName == '特诺系数') {
          for (let i = 0; i < res.data.treynor.length; i++) {
            if (res.data.treynor[i] == "1w")
              this.optionsselect["特诺系数"][0].children.push({
                value: "1w",
                label: "一周"
              });
            if (res.data.treynor[i] == "2w")
              this.optionsselect["特诺系数"][0].children.push({
                value: "2w",
                label: "两周"
              });
            if (res.data.treynor[i] == "1m")
              this.optionsselect["特诺系数"][0].children.push({
                value: "1m",
                label: "一月"
              });
            if (res.data.treynor[i] == "2m")
              this.optionsselect["特诺系数"][0].children.push({
                value: "2m",
                label: "两月"
              });
            if (res.data.treynor[i] == "1q")
              this.optionsselect["特诺系数"][0].children.push({
                value: "1q",
                label: "一季"
              });
            if (res.data.treynor[i] == "2q")
              this.optionsselect["特诺系数"][0].children.push({
                value: "2q",
                label: "两季"
              });
            if (res.data.treynor[i] == "1y")
              this.optionsselect["特诺系数"][0].children.push({
                value: "1y",
                label: "一年"
              });
            if (res.data.treynor[i] == "2y")
              this.optionsselect["特诺系数"][0].children.push({
                value: "2y",
                label: "两年"
              });
            if (res.data.treynor[i] == "3y")
              this.optionsselect["特诺系数"][0].children.push({
                value: "3y",
                label: "三年"
              });
            if (res.data.treynor[i] == "5y")
              this.optionsselect["特诺系数"][0].children.push({
                value: "5y",
                label: "五年"
              });
            // }
          }
          this.optionsselect["上行捕获"] = [
            { value: "now", label: "近期表现", children: [] }
          ];
          // if (this.haveName == '上行捕获') {
          for (let i = 0; i < res.data.bullreturn.length; i++) {
            if (res.data.bullreturn[i] == "1w")
              this.optionsselect["上行捕获"][0].children.push({
                value: "1w",
                label: "一周"
              });
            if (res.data.bullreturn[i] == "2w")
              this.optionsselect["上行捕获"][0].children.push({
                value: "2w",
                label: "两周"
              });
            if (res.data.bullreturn[i] == "1m")
              this.optionsselect["上行捕获"][0].children.push({
                value: "1m",
                label: "一月"
              });
            if (res.data.bullreturn[i] == "2m")
              this.optionsselect["上行捕获"][0].children.push({
                value: "2m",
                label: "两月"
              });
            if (res.data.bullreturn[i] == "1q")
              this.optionsselect["上行捕获"][0].children.push({
                value: "1q",
                label: "一季"
              });
            if (res.data.bullreturn[i] == "2q")
              this.optionsselect["上行捕获"][0].children.push({
                value: "2q",
                label: "两季"
              });
            if (res.data.bullreturn[i] == "1y")
              this.optionsselect["上行捕获"][0].children.push({
                value: "1y",
                label: "一年"
              });
            if (res.data.bullreturn[i] == "2y")
              this.optionsselect["上行捕获"][0].children.push({
                value: "2y",
                label: "两年"
              });
            if (res.data.bullreturn[i] == "3y")
              this.optionsselect["上行捕获"][0].children.push({
                value: "3y",
                label: "三年"
              });
            if (res.data.bullreturn[i] == "5y")
              this.optionsselect["上行捕获"][0].children.push({
                value: "5y",
                label: "五年"
              });
            // }
          }
          this.optionsselect["下行捕获"] = [
            { value: "now", label: "近期表现", children: [] }
          ];
          // if (this.haveName == '下行捕获') {
          for (let i = 0; i < res.data.bearreturn.length; i++) {
            if (res.data.bearreturn[i] == "1w")
              this.optionsselect["下行捕获"][0].children.push({
                value: "1w",
                label: "一周"
              });
            if (res.data.bearreturn[i] == "2w")
              this.optionsselect["下行捕获"][0].children.push({
                value: "2w",
                label: "两周"
              });
            if (res.data.bearreturn[i] == "1m")
              this.optionsselect["下行捕获"][0].children.push({
                value: "1m",
                label: "一月"
              });
            if (res.data.bearreturn[i] == "2m")
              this.optionsselect["下行捕获"][0].children.push({
                value: "2m",
                label: "两月"
              });
            if (res.data.bearreturn[i] == "1q")
              this.optionsselect["下行捕获"][0].children.push({
                value: "1q",
                label: "一季"
              });
            if (res.data.bearreturn[i] == "2q")
              this.optionsselect["下行捕获"][0].children.push({
                value: "2q",
                label: "两季"
              });
            if (res.data.bearreturn[i] == "1y")
              this.optionsselect["下行捕获"][0].children.push({
                value: "1y",
                label: "一年"
              });
            if (res.data.bearreturn[i] == "2y")
              this.optionsselect["下行捕获"][0].children.push({
                value: "2y",
                label: "两年"
              });
            if (res.data.bearreturn[i] == "3y")
              this.optionsselect["下行捕获"][0].children.push({
                value: "3y",
                label: "三年"
              });
            if (res.data.bearreturn[i] == "5y")
              this.optionsselect["下行捕获"][0].children.push({
                value: "5y",
                label: "五年"
              });
            // }
          }
          this.optionsselect["择时gamma"] = [
            { value: "now", label: "近期表现", children: [] }
          ];
          // if (this.haveName == '择时gamma') {
          for (let i = 0; i < res.data.gamma.length; i++) {
            if (res.data.gamma[i] == "1w")
              this.optionsselect["择时gamma"][0].children.push({
                value: "1w",
                label: "一周"
              });
            if (res.data.gamma[i] == "2w")
              this.optionsselect["择时gamma"][0].children.push({
                value: "2w",
                label: "两周"
              });
            if (res.data.gamma[i] == "1m")
              this.optionsselect["择时gamma"][0].children.push({
                value: "1m",
                label: "一月"
              });
            if (res.data.gamma[i] == "2m")
              this.optionsselect["择时gamma"][0].children.push({
                value: "2m",
                label: "两月"
              });
            if (res.data.gamma[i] == "1q")
              this.optionsselect["择时gamma"][0].children.push({
                value: "1q",
                label: "一季"
              });
            if (res.data.gamma[i] == "2q")
              this.optionsselect["择时gamma"][0].children.push({
                value: "2q",
                label: "两季"
              });
            if (res.data.gamma[i] == "1y")
              this.optionsselect["择时gamma"][0].children.push({
                value: "1y",
                label: "一年"
              });
            if (res.data.gamma[i] == "2y")
              this.optionsselect["择时gamma"][0].children.push({
                value: "2y",
                label: "两年"
              });
            if (res.data.gamma[i] == "3y")
              this.optionsselect["择时gamma"][0].children.push({
                value: "3y",
                label: "三年"
              });
            if (res.data.gamma[i] == "5y")
              this.optionsselect["择时gamma"][0].children.push({
                value: "5y",
                label: "五年"
              });
            // }
          }
          this.optionsselect["M2"] = [
            { value: "now", label: "近期表现", children: [] }
          ];
          // if (this.haveName == 'M2') {
          for (let i = 0; i < res.data.msquared.length; i++) {
            if (res.data.msquared[i] == "1w")
              this.optionsselect["M2"][0].children.push({
                value: "1w",
                label: "一周"
              });
            if (res.data.msquared[i] == "2w")
              this.optionsselect["M2"][0].children.push({
                value: "2w",
                label: "两周"
              });
            if (res.data.msquared[i] == "1m")
              this.optionsselect["M2"][0].children.push({
                value: "1m",
                label: "一月"
              });
            if (res.data.msquared[i] == "2m")
              this.optionsselect["M2"][0].children.push({
                value: "2m",
                label: "两月"
              });
            if (res.data.msquared[i] == "1q")
              this.optionsselect["M2"][0].children.push({
                value: "1q",
                label: "一季"
              });
            if (res.data.msquared[i] == "2q")
              this.optionsselect["M2"][0].children.push({
                value: "2q",
                label: "两季"
              });
            if (res.data.msquared[i] == "1y")
              this.optionsselect["M2"][0].children.push({
                value: "1y",
                label: "一年"
              });
            if (res.data.msquared[i] == "2y")
              this.optionsselect["M2"][0].children.push({
                value: "2y",
                label: "两年"
              });
            if (res.data.msquared[i] == "3y")
              this.optionsselect["M2"][0].children.push({
                value: "3y",
                label: "三年"
              });
            if (res.data.msquared[i] == "5y")
              this.optionsselect["M2"][0].children.push({
                value: "5y",
                label: "五年"
              });
            // }
          }
          // console.log(this.optionsselect);
        })
        .catch(error => {
          //that.$message('数据缺失');
          console.log(error);
        });

      // this.optionsselect = this.optionsselect;

      // console.log(this.optionsselect, '?');
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.listSelect = this.listSelectX;
    this.radioSelf = this.radioInput2;
    this.radio = this.radioType2;
    this.radioFilterOrOut = this.isSame2;
  },
  beforeCreate() {}, //生命周期 - 创建之前
  beforeMount() {}, //生命周期 - 挂载之前
  beforeUpdate() {}, //生命周期 - 更新之前
  updated() {}, //生命周期 - 更新之后
  beforeDestroy() {}, //生命周期 - 销毁之前
  destroyed() {}, //生命周期 - 销毁完成
  activated() {} //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
.dialogFilerDX {
  .boxItemDetailM {
    padding-left: 24px;
    // margin-bottom: 16px;
    margin-top: 16px;
  }
  .boxItemDetail {
    padding-left: 24px;
    // margin-bottom: 16px;
    // margin-top: 16px;
    padding: 8px 24px;
  }
  .boxItemDetail:hover {
    // background: #e9e9e9;
    // .contentDel {
    // 	margin-right: 16px;
    // 	justify-content: end;
    // 	// display: flex;
    // 	display: flex;
    // }
  }
  ::v-deep .el-dialog__header {
    padding: 0px !important ;
  }
  .titleContent {
    font-size: 16px;
    margin-bottom: 8px;
    line-height: 24px;
    color: rgba(0, 0, 0, 0.85);
  }
  .contentBoxFilter {
    padding-top: 8px;
    padding-bottom: 8px;
    margin-left: -24px;
    padding-left: 24px;
    // margin-top: 16px;
    display: flex;
    width: 100%;
    // height:40px;
    align-items: center;
    justify-content: start;
    .contentItem {
      padding-right: 16px;
    }

    .contentDel {
      margin-right: 16px;
      justify-content: end;
      // display: flex;
      display: none;
    }
  }
  .contentBoxFilter:hover {
    background: rgba(0, 0, 0, 0.04);
    .contentDel {
      margin-right: 16px;
      justify-content: end;
      // display: flex;
      display: flex;
    }
  }
}
</style>
