<template>
  <div class="chart_one"
       v-show="show"
       v-loading="loading">
    <div class="flex_between">
      <div class="title">表现风格</div>
      <div>
        <el-dropdown>
          <span class="el-dropdown-link"
                style>
            <i class="el-icon-more"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <div @click="exportExcel">导出Excel</div>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
    <div class="mt-16">
      <el-table :data="data"
                class="expressionStyle-table"
                style="width: 100%"
                border
                :row-class-name="tableRowClassName">
        <el-table-column v-for="item in column"
                         :key="item.label"
                         :prop="item.value"
                         :label="item.label"
                         :align="item.children ? 'center' : 'gotoleft'">
          <template slot-scope="{ row }">
            <div v-if="item.value == 'pool_name' && row['flag'] != 0"
                 class="overflow_ellipsis flex_start">
              <div v-if="row.color">
                <div :style="`width:12px;height:12px;background:${row.color}`"
                     class="mr-8"></div>
              </div>
              <div v-else
                   class="flex_start">
                <div :style="`width:12px;height:12px;background:#4096ff`"
                     class="mr-8"></div>
                <div class="mr-8 flex_start">
                  <svg width="14"
                       height="14"
                       viewBox="0 0 14 14"
                       fill="none"
                       xmlns="http://www.w3.org/2000/svg">
                    <path d="M12.4156 4.82735L8.94433 4.32286L7.39258 1.17696C7.35019 1.09083 7.28047 1.0211 7.19433 0.978716C6.97832 0.872075 6.71582 0.960943 6.60781 1.17696L5.05605 4.32286L1.58476 4.82735C1.48906 4.84102 1.40156 4.88614 1.33457 4.9545C1.25358 5.03774 1.20895 5.14973 1.21049 5.26586C1.21203 5.38199 1.25961 5.49276 1.34277 5.57383L3.8543 8.02247L3.26094 11.4801C3.24702 11.5605 3.25592 11.6432 3.28663 11.7189C3.31733 11.7945 3.36862 11.86 3.43466 11.908C3.50071 11.9559 3.57887 11.9845 3.66029 11.9903C3.74171 11.9961 3.82313 11.9789 3.89531 11.9408L7.00019 10.3084L10.1051 11.9408C10.1898 11.9859 10.2883 12.001 10.3826 11.9846C10.6205 11.9436 10.7805 11.718 10.7395 11.4801L10.1461 8.02247L12.6576 5.57383C12.726 5.50684 12.7711 5.41934 12.7848 5.32364C12.8217 5.08438 12.6549 4.8629 12.4156 4.82735Z"
                          fill="#FFD600" />
                  </svg>
                </div>
              </div>
              <el-link @click="alphaGo(row['code'], row['pool_name'])">{{ row[item.value] }}</el-link>
            </div>
            <div v-else>{{ row[item.value] }}</div>
          </template>
          <el-table-column v-show="item.children"
                           v-for="obj in item.children"
                           :key="obj.value"
                           align="gotoleft"
                           :prop="obj.value"
                           :label="obj.label"></el-table-column>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { getStyleInfo } from "@/api/pages/tools/pool.js";
import { filter_json_to_excel } from "@/utils/exportExcel.js";
import { alphaGo } from "@/assets/js/alpha_type.js";
export default {
  props: {

  },
  data () {
    return {
      ismanager: false,
      data: [],
      column: [
        {
          label: "名称",
          value: "pool_name"
        }
      ],
      show: true,
      info: {},
      loading: true
    };
  },
  methods: {
    getData (info) {
      this.info = info;
      this.getStyleInfo();
      this.formatYearqtr();
      this.data = [
        {
          pool_name: "平均"
        },
        {
          pool_name: "加权平均"
        },
        {
          pool_name: "中位数"
        }
      ];
    },
    // 获取风格数据
    async getStyleInfo () {
      this.loading = true;
      let data = await getStyleInfo({
        ids: [
          { code: this.info.code, type: "pool" },
          ...this.info["code_list"]
            .filter(v => v.flag != 0)
            .map(item => {
              return {
                code: item.code,
                type: this.ismanager
                  ? "manager"
                  : "fund"
              };
            })
        ],
        yearqtr: this.info.quarter,
        insert_time: this.info.date,
        flag: 5,
        ismanager: this.ismanager,
        type: this.info.type,
      });
      if (data?.mtycode == 200) {
        this.show = true;
        this.data = [];
        let key_list = ["yearqtr", "flag", "poolId", "status"];
        data?.data
          .filter(v =>
            v.flag == this.ismanager
              ? "manager"
              : "fund"
          )
          .map(item => {
            let index = this.data.findIndex(v => v.code == item.poolId);
            let name =
              item.flag == "pool"
                ? this.info.name
                : this.info["code_list"].find(v => v.code == item.poolId)
                  ?.name;
            let color =
              item.flag == "pool"
                ? "#4096ff"
                : this.info["code_list"].find(v => v.code == item.poolId)
                  ?.color;
            let type =
              item.flag == "pool"
                ? 0
                : this.info["code_list"].find(v => v.code == item.poolId)
                  ?.flag;
            let obj = {
              pool_name: name,
              color,
              flag: type,
              code: item.poolId
            };
            if (index == -1) {
              for (const key in item) {
                if (!key_list.some(v => v == key)) {
                  obj[item.yearqtr + "_" + key] = item[key];
                }
              }
              this.data.push(obj);
            } else {
              for (const key in item) {
                if (!key_list.some(v => v == key)) {
                  obj[item.yearqtr + "_" + key] = item[key];
                }
              }
              this.data[index] = { ...this.data[index], ...obj };
            }
          });
        data?.data
          .filter(v => v.flag == "pool")
          .map(item => {
            let name =
              item.status == "等权"
                ? "平均"
                : item.status == "规模加权"
                  ? "加权平均"
                  : "中位数";
            let index = this.data.findIndex(
              v => v.pool_name == name && v.code == item.poolId
            );
            let color =
              item.flag == "pool"
                ? "#4096ff"
                : this.info["code_list"].find(v => v.code == item.poolId)
                  ?.color;
            let type =
              item.flag == "pool"
                ? 0
                : this.info["code_list"].find(v => v.code == item.poolId)
                  ?.flag;
            let obj = {
              pool_name: name,
              color,
              flag: type,
              code: item.poolId
            };
            if (index == -1) {
              for (const key in item) {
                if (!key_list.some(v => v == key)) {
                  obj[item.yearqtr + "_" + key] = item[key];
                }
              }
              this.data.push(obj);
            } else {
              for (const key in item) {
                if (!key_list.some(v => v == key)) {
                  obj[item.yearqtr + "_" + key] = item[key];
                }
              }
              this.data[index] = { ...this.data[index], ...obj };
            }
          });
        this.loading = false;
      } else {
        this.show = false;
      }
    },
    tableRowClassName ({ row, rowIndex }) {
      // rowIndex === 0 ||
      if (
        rowIndex === this.data.length - 1 ||
        rowIndex === this.data.length - 2 ||
        rowIndex === this.data.length - 3
      ) {
        return "default-row";
      }
      return "";
    },
    formatYearqtr () {
      this.column = [
        {
          label: "名称",
          value: "pool_name"
        }
      ];
      let yearqtr = this.info["quarter"].sort((a, b) => {
        return this.moment(this.moment(a, "YYYY QQ").format()).isAfter(
          this.moment(b, "YYYY QQ").format()
        )
          ? -1
          : 1;
      });
      yearqtr.map(item => {
        this.column.push({
          label: item,
          children: [
            {
              label: "成长价值",
              value: item + "_valuegrowth"
            },
            {
              label: "大小盘",
              value: item + "_bigsmall"
            },
            {
              label: "大行业",
              value: item + "_industry_section"
            }
          ]
        });
      });
    },
    alphaGo (code, name) {
      alphaGo(code, name, this.$route.path);
    },
    // 导出Excel
    exportExcel () {
      let list = [];
      this.column.map(item => {
        if (item.children) {
          item.children.map(obj => {
            list.push({
              label: item.label + obj.label,
              value: obj.value
            });
          });
        } else {
          list.push({
            ...item
          });
        }
      });
      let data = this.data;
      filter_json_to_excel(list, data, this.info.name + "表现风格");
    }
  },
  mounted () {
    this.ismanager = String(this.$route.query.ismanager) == 'true' ? true : false
  },
};
</script>

<style lang="scss" scoped>
.expressionStyle-table {
	::v-deep .default-row {
		background-color: #fafafa;
	}
}
.overflow_ellipsis {
	a {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	.el-link {
		justify-content: start;
	}
	::v-deep.el-link--inner {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
}
</style>
