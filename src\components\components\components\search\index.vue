<template>
  <el-select :style="selectStyle"
             class="search_index"
             v-model="values"
             :remote-method="searchpeople"
             @change="changeResult"
             filterable
             remote
             :multiple="multiple"
             prefix-icon="el-icon-search"
             :loading="loading"
             :disabled="disabled"
             :placeholder="
			placeholder
				? placeholder
				: type == 'all'
				? '输入简拼、代码、名称查询基金/经理'
				: type == 'fund'
				? '输入简拼、代码、名称查询基金'
				: type == 'manager'
				? '输入简拼、代码、名称查询经理'
				: type == 'index'
				? '输入简拼、代码、名称查询指数'
				: type == 'stock'
				? '输入简拼、代码、名称查询股票'
				: '输入简拼、代码、名称查询'
		">
    <template slot="prefix">
      <div style="width: 24px; height: 100%; display: flex; justify-content: center; align-items: center; matgin-left: 13.1px">
        <i class="el-icon-search"
           style="color: #00000073"></i>
      </div>
    </template>
    <el-option-group v-for="groups in havefundmanager"
                     :key="groups.label"
                     :label="groups.label">
      <el-option v-for="group in groups.options"
                 :key="group.code"
                 :label="
					group.flag == 'fund'
						? `${group.code}-${group.name}-${group.fundCo.split('基金')[0]}`
						: group.flag == 'manager'
						? `${group.name}-${group.fundCo.split('基金')[0]}`
						: group.flag == 'company'
						? group.name
						: `${group.name}-${group.code}`
				"
                 :value="group.code + '|' + group.name + '|' + group.flag">
      </el-option>
    </el-option-group>
  </el-select>
</template>

<script>
import axios from '@/api/index';

export default {
  data () {
    return {
      values: '',
      havefundmanager: [],
      loading: false
    };
  },
  props: {
    defaultCode: { type: String },
    selectStyle: { type: String, default: 'margin-right: 12px' },
    type: { type: String, default: 'all' },
    info: { type: Object, default: {} },
    fundType: { type: String, default: 'all' },
    multiple: { type: Boolean, default: false },
    placeholder: { type: String, default: '' },
    disabled: { type: Boolean, default: false }
  },
  methods: {
    setDefaultValue (val) {
      this.values = val;
    },
    searchpeople (query) {
      this.havefundmanager = null;
      clearTimeout(this.timeout);
      this.timeout = setTimeout(() => {
        this.getResultsList(query);
      }, 500);
    },
    getResultsList (query) {
      let flag =
        this.type == 'all'
          ? '1,2,3'
          : this.type == 'fund'
            ? '1'
            : this.type == 'manager'
              ? '2'
              : this.type == 'index'
                ? '6'
                : this.type == 'stock'
                  ? ''
                  : '1,2,3';
      axios
        .get(this.$baseUrl + '/Analysis/Search/?message=' + query + '&flag=' + flag)
        .then((res) => {
          let temparr = [];
          let data = res?.data?.data || [];
          if (this.type.indexOf('all') != -1) {
            temparr = [
              {
                label: '基金产品',
                options: []
              },
              {
                label: '基金经理',
                options: []
              },
              {
                label: '基金公司',
                options: []
              },
              {
                label: '基准',
                options: []
              }
            ];
            for (let i = 0; i < data.length; i++) {
              if (data[i].flag === 'fund') {
                temparr[0].options.push(data[i]);
              } else if (data[i].flag == 'manager') {
                temparr[1].options.push(data[i]);
              } else if (data[i].flag == 'company') {
                temparr[2].options.push(data[i]);
              } else if (data[i].flag == 'index') {
                temparr[3].options.push(data[i]);
              }
            }
          } else {
            if (this.type.indexOf('fund') != -1) {
              temparr.push({
                label: '基金产品',
                options: []
              });
              for (let i = 0; i < data.length; i++) {
                if (data[i].flag === 'fund') {
                  if (this.fundType == 'equity') {
                    if (data[i].type == 'equity' || data[i].type == 'equitywithhk') {
                      temparr[
                        temparr.findIndex((val) => {
                          return val.label == '基金产品';
                        })
                      ].options.push(data[i]);
                    }
                  } else if (this.fundType == data[i].type || this.fundType == 'all') {
                    temparr[
                      temparr.findIndex((val) => {
                        return val.label == '基金产品';
                      })
                    ].options.push(data[i]);
                  }
                }
              }
            }
            if (this.type.indexOf('manager') != -1) {
              temparr.push({
                label: '基金经理',
                options: []
              });
              for (let i = 0; i < data.length; i++) {
                if (data[i].flag === 'manager') {
                  if (this.fundType) {
                    if (data[i].type?.indexOf(this.fundType) != -1 || this.fundType == 'all') {
                      temparr[
                        temparr.findIndex((val) => {
                          return val.label == '基金经理';
                        })
                      ].options.push(data[i]);
                    }
                  } else {
                    temparr[
                      temparr.findIndex((val) => {
                        return val.label == '基金经理';
                      })
                    ].options.push(data[i]);
                  }
                }
              }
            }
            if (this.type.indexOf('company') != -1) {
              temparr.push({
                label: '基金公司',
                options: []
              });
              for (let i = 0; i < data.length; i++) {
                if (data[i].flag === 'company') {
                  temparr[
                    temparr.findIndex((val) => {
                      return val.label == '基金公司';
                    })
                  ].options.push(data[i]);
                }
              }
            }
            if (this.type.indexOf('index') != -1) {
              temparr.push({
                label: '基准',
                options: []
              });
              for (let i = 0; i < data.length; i++) {
                if (data[i].flag === 'index') {
                  temparr[
                    temparr.findIndex((val) => {
                      return val.label == '基准';
                    })
                  ].options.push(data[i]);
                }
              }
            }
            if (this.type.indexOf('stock') != -1) {
              temparr.push({
                label: '股票',
                options: []
              });

              for (let i = 0; i < data.length; i++) {
                if (data[i].flag === 'stock') {
                  temparr[
                    temparr.findIndex((val) => {
                      return val.label == '股票';
                    })
                  ].options.push(data[i]);
                }
              }
            }
          }
          this.havefundmanager = temparr;
          this.loading = false;
        })
        .catch((error) => { });
    },
    changeResult (val) {
      let type = '';
      this.havefundmanager?.map((obj) => {
        obj.options?.map((item) => {
          if (item.code == val.split('|')[0]) {
            type = item.type;
          }
        });
      });
      this.havefundmanager = null;
      if (this.multiple) {
        let arr = [];
        val?.map((item) => {
          let id = item.split('|')[0];
          let name = item.split('|')[1];
          let flag = item.split('|')[2];
          arr.push({ id, name, flag });
        });
        this.$emit('resolveFather', arr);
      } else {
        if (val == '') {
        } else {
          let id = val.split('|')[0];
          let name = val.split('|')[1];
          let flag = val.split('|')[2];
          this.$emit('resolveFather', { id, name, flag, type });
          this.values = name;
        }
      }
    },
    clearValue () {
      this.values = '';
    },
    setindexCode (val) {
      this.values = val
    }
  },
  mounted () {

  },
};
</script>

<style>
.search_index .el-input__inner {
	padding: 0 24px !important;
	/* margin-left: 24px; */
}
.search_index .el-select__input {
	margin-left: 0;
}
.search_index .el-select__tags {
	margin-left: 24px;
}
</style>
