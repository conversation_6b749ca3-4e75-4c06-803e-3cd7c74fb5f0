<template>
  <div class="homebodyfontsize">
    <tempbasket ref="tempbasketfund"
                type="fund"></tempbasket>
    <div v-loading="showmsg"
         style="padding-bottom: 20px; background: white">
      <div class="title"
           style="display: flex">
        <div class="pointssearch"></div>
        QDII商品-黄金
      </div>
      <div v-loading="showmsgsloading">
        <el-table :default-sort="{ prop: 'code' }"
                  :data="tableData"
                  class="table"
                  :cell-style="elcellstyle"
                  ref="multipleTable"
                  header-cell-class-name="table-header">
          <el-table-column :width="getfontSize(320)"
                           prop="name"
                           align="gotoleft"
                           label="基金名称">
            <template slot-scope="scope"><a style="border-bottom: 1px solid #4096ff"
                 @click="godetail(scope.row.code, scope.row.name)">{{
								scope.row.name | isDefault
							}}</a></template>
          </el-table-column>
          <el-table-column sortable
                           prop="code"
                           label="基金代码"
                           align="gotoleft"> </el-table-column>
          <el-table-column sortable
                           prop="found_date"
                           label="起始时间"
                           align="gotoleft"> </el-table-column>
          <el-table-column sortable
                           prop="manager"
                           label="基金经理姓名"
                           align="gotoleft"> </el-table-column>
          <!-- <el-table-column sortable prop="type" label="管理类别" align="gotoleft" >             
                </el-table-column>
                <el-table-column label="近一个月收益" align="gotoleft">
                    <template slot-scope="scope">{{scope.row['1m']|fix2p}}</template>
                </el-table-column>
                <el-table-column  label="近一年收益" align="gotoleft"> 
                   <template slot-scope="scope">{{scope.row['1y']|fix2p}}</template>
                </el-table-column> -->
          <el-table-column prop="1w"
                           sortable
                           label="近一周收益"
                           align="gotoleft">
            <template slot-scope="scope">{{ scope.row['1w'] | fix2p }}</template>
          </el-table-column>
          <el-table-column prop="1m"
                           sortable
                           label="近一月收益"
                           align="gotoleft">
            <template slot-scope="scope">{{ scope.row['1m'] | fix2p }}</template>
          </el-table-column>
          <el-table-column prop="1q"
                           sortable
                           label="近一季收益"
                           align="gotoleft">
            <template slot-scope="scope">{{ scope.row['1q'] | fix2p }}</template>
          </el-table-column>
          <el-table-column prop="1y"
                           sortable
                           label="近一年收益"
                           align="gotoleft">
            <template slot-scope="scope">{{ scope.row['1y'] | fix2p }}</template>
          </el-table-column>
          <el-table-column label="关注"
                           width="100"
                           align="gotoleft">
            <template slot-scope="scope">
              <div @click="addpool(scope.row.code, scope.row.name)"><i class="el-icon-circle-plus icon_color"></i></div>
            </template>
          </el-table-column>
          <!-- <el-table-column label="查看详情" width="100" align="gotoleft">
                    <template slot-scope="scope"><div @click="godetail(scope.row.code,scope.row.name)"><i  class="el-icon-tickets icon_color"></i></div></template>  
                </el-table-column> -->
        </el-table>
      </div>
      <div class="title"
           style="display: flex">
        <div class="pointssearch"></div>
        QDII商品-原油
      </div>
      <div v-loading="showmsgsloading">
        <el-table :default-sort="{ prop: 'code' }"
                  :data="tableData2"
                  class="table"
                  :cell-style="elcellstyle"
                  ref="multipleTable"
                  header-cell-class-name="table-header">
          <el-table-column prop="name"
                           :width="getfontSize(320)"
                           align="gotoleft"
                           label="基金名称">
            <template slot-scope="scope"><a style="border-bottom: 1px solid #4096ff"
                 @click="godetail2(scope.row.code, scope.row.name)">{{
								scope.row.name | isDefault
							}}</a></template>
          </el-table-column>
          <el-table-column sortable
                           prop="code"
                           label="基金代码"
                           align="gotoleft"> </el-table-column>
          <el-table-column sortable
                           prop="found_date"
                           label="起始时间"
                           align="gotoleft"> </el-table-column>
          <el-table-column sortable
                           prop="manager"
                           label="基金经理姓名"
                           align="gotoleft"> </el-table-column>
          <!-- <el-table-column sortable prop="type" label="管理类别" align="gotoleft" >             
                </el-table-column>
                <el-table-column label="近一个月收益" align="gotoleft">
                    <template slot-scope="scope">{{scope.row['1m']|fix2p}}</template>
                </el-table-column>
                <el-table-column  label="近一年收益" align="gotoleft"> 
                   <template slot-scope="scope">{{scope.row['1y']|fix2p}}</template>
                </el-table-column> -->
          <el-table-column prop="1w"
                           sortable
                           label="近一周收益"
                           align="gotoleft">
            <template slot-scope="scope">{{ scope.row['1w'] | fix2p }}</template>
          </el-table-column>
          <el-table-column prop="1m"
                           sortable
                           label="近一月收益"
                           align="gotoleft">
            <template slot-scope="scope">{{ scope.row['1m'] | fix2p }}</template>
          </el-table-column>
          <el-table-column prop="1q"
                           sortable
                           label="近一季收益"
                           align="gotoleft">
            <template slot-scope="scope">{{ scope.row['1q'] | fix2p }}</template>
          </el-table-column>
          <el-table-column prop="1y"
                           sortable
                           label="近一年收益"
                           align="gotoleft">
            <template slot-scope="scope">{{ scope.row['1y'] | fix2p }}</template>
          </el-table-column>
          <el-table-column label="关注"
                           width="100"
                           align="gotoleft">
            <template slot-scope="scope">
              <div @click="addpool(scope.row.code, scope.row.name)"><i class="el-icon-circle-plus icon_color"></i></div>
            </template>
          </el-table-column>
          <!-- <el-table-column label="查看详情" width="100" align="gotoleft">
                    <template slot-scope="scope"><div @click="godetail2(scope.row.code,scope.row.name)"><i  class="el-icon-tickets icon_color"></i></div></template>  
                </el-table-column> -->
        </el-table>
      </div>
      <div class="title"
           style="display: flex">
        <div class="pointssearch"></div>
        QDII商品-reits
      </div>
      <div v-loading="showmsgsloading">
        <el-table :default-sort="{ prop: 'code' }"
                  :data="tableData3"
                  class="table"
                  :cell-style="elcellstyle"
                  ref="multipleTable"
                  header-cell-class-name="table-header">
          <el-table-column prop="name"
                           :width="getfontSize(320)"
                           align="gotoleft"
                           label="基金名称">
            <template slot-scope="scope"><a style="border-bottom: 1px solid #4096ff"
                 @click="godetail3(scope.row.code, scope.row.name)">{{
								scope.row.name | isDefault
							}}</a></template>
          </el-table-column>
          <el-table-column sortable
                           prop="code"
                           label="基金代码"
                           align="gotoleft"> </el-table-column>
          <el-table-column sortable
                           prop="found_date"
                           label="起始时间"
                           align="gotoleft"> </el-table-column>
          <el-table-column sortable
                           prop="manager"
                           label="基金经理姓名"
                           align="gotoleft"> </el-table-column>
          <!-- <el-table-column sortable prop="type" label="管理类别" align="gotoleft" >             
                </el-table-column>
                <el-table-column label="近一个月收益" align="gotoleft">
                    <template slot-scope="scope">{{scope.row['1m']|fix2p}}</template>
                </el-table-column>
                <el-table-column  label="近一年收益" align="gotoleft"> 
                   <template slot-scope="scope">{{scope.row['1y']|fix2p}}</template>
                </el-table-column> -->
          <el-table-column prop="1w"
                           sortable
                           label="近一周收益"
                           align="gotoleft">
            <template slot-scope="scope">{{ scope.row['1w'] | fix2p }}</template>
          </el-table-column>
          <el-table-column prop="1m"
                           sortable
                           label="近一月收益"
                           align="gotoleft">
            <template slot-scope="scope">{{ scope.row['1m'] | fix2p }}</template>
          </el-table-column>
          <el-table-column prop="1q"
                           sortable
                           label="近一季收益"
                           align="gotoleft">
            <template slot-scope="scope">{{ scope.row['1q'] | fix2p }}</template>
          </el-table-column>
          <el-table-column prop="1y"
                           sortable
                           label="近一年收益"
                           align="gotoleft">
            <template slot-scope="scope">{{ scope.row['1y'] | fix2p }}</template>
          </el-table-column>
          <el-table-column label="关注"
                           width="100"
                           align="gotoleft">
            <template slot-scope="scope">
              <div @click="addpool(scope.row.code, scope.row.name)"><i class="el-icon-circle-plus icon_color"></i></div>
            </template>
          </el-table-column>
          <!-- <el-table-column label="查看详情" width="100" align="gotoleft">
                    <template slot-scope="scope"><div @click="godetail3(scope.row.code,scope.row.name)"><i  class="el-icon-tickets icon_color"></i></div></template>  
                </el-table-column> -->
        </el-table>
      </div>
      <div class="title"
           style="display: flex">
        <div class="pointssearch"></div>
        QDII商品-其他大宗商品
      </div>
      <div v-loading="showmsgsloading">
        <el-table :default-sort="{ prop: 'code' }"
                  :data="tableData4"
                  :cell-style="elcellstyle"
                  class="table"
                  ref="multipleTable"
                  header-cell-class-name="table-header">
          <el-table-column prop="name"
                           :width="getfontSize(320)"
                           align="gotoleft"
                           label="基金名称">
            <template slot-scope="scope"><a style="border-bottom: 1px solid #4096ff"
                 @click="godetail4(scope.row.code, scope.row.name)">{{
								scope.row.name | isDefault
							}}</a></template>
          </el-table-column>
          <el-table-column sortable
                           prop="code"
                           label="基金代码"
                           align="gotoleft"> </el-table-column>
          <el-table-column sortable
                           prop="found_date"
                           label="起始时间"
                           align="gotoleft"> </el-table-column>
          <el-table-column sortable
                           prop="manager"
                           label="基金经理姓名"
                           align="gotoleft"> </el-table-column>
          <!-- <el-table-column sortable prop="type" label="管理类别" align="gotoleft" >             
                </el-table-column>
                <el-table-column label="近一个月收益" align="gotoleft">
                    <template slot-scope="scope">{{scope.row['1m']|fix2p}}</template>
                </el-table-column>
                <el-table-column  label="近一年收益" align="gotoleft"> 
                   <template slot-scope="scope">{{scope.row['1y']|fix2p}}</template>
                </el-table-column> -->
          <el-table-column prop="1w"
                           sortable
                           label="近一周收益"
                           align="gotoleft">
            <template slot-scope="scope">{{ scope.row['1w'] | fix2p }}</template>
          </el-table-column>
          <el-table-column prop="1m"
                           sortable
                           label="近一月收益"
                           align="gotoleft">
            <template slot-scope="scope">{{ scope.row['1m'] | fix2p }}</template>
          </el-table-column>
          <el-table-column prop="1q"
                           sortable
                           label="近一季收益"
                           align="gotoleft">
            <template slot-scope="scope">{{ scope.row['1q'] | fix2p }}</template>
          </el-table-column>
          <el-table-column prop="1y"
                           sortable
                           label="近一年收益"
                           align="gotoleft">
            <template slot-scope="scope">{{ scope.row['1y'] | fix2p }}</template>
          </el-table-column>
          <el-table-column label="关注"
                           width="100"
                           align="gotoleft">
            <template slot-scope="scope">
              <div @click="addpool(scope.row.code, scope.row.name)"><i class="el-icon-circle-plus icon_color"></i></div>
            </template>
          </el-table-column>
          <!-- <el-table-column label="查看详情" width="100" align="gotoleft">
                    <template slot-scope="scope"><div @click="godetail4(scope.row.code,scope.row.name)"><i  class="el-icon-tickets icon_color"></i></div></template>  
                </el-table-column> -->
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
import { alphaGo } from '@/assets/js/alpha_type.js';
import tempbasket from './components/tempbasket';
import axios from '@/api/index';
import { fontSize } from '@/assets/js/echartsrpxtorem'; //注意路径
export default {
  components: { tempbasket },
  props: [],
  filters: {
    fix6 (value) {
      return value.substring(0, 10);
    },
    fix3 (value) {
      return parseInt(value * 1000) / 1000;
    },
    fix2p (value) {
      return (value * 100).toFixed(2) + '%';
    },
    isDefault (value) {
      return value == '--' ? '' : value;
    }
  },
  data () {
    return {
      showmsg: true,
      choosereason: null,
      choosefundid: null,
      choosefundname: null,
      choosedpool: null,
      alldata: [],
      tableData: [],
      pageTotal: null,
      pageIndex: 1,
      pageSize: 20,
      showmsgsloading: false,
      options: [
        {
          value: '黄金',
          label: '黄金'
        },
        {
          value: '原油',
          label: '原油'
        },
        {
          value: '其他大宗',
          label: '其他大宗'
        },
        {
          value: '不动产',
          label: '不动产'
        }
      ],

      showej: false,

      addfundvis: false,
      tableData: [],
      tableData2: [],
      tableData3: [],
      tableData4: [],
      fundname: null,
      formData: {
        choose: null
      },
      is_request_active: false
    };
  },
  computed: {},
  watch: {},
  created () {
    this.getmsg();
  },
  mounted () { },
  methods: {
    getfontSize (val) {
      return fontSize(val);
    },
    elcellstyle ({ row, column, rowIndex, columnIndex }) {
      // ////console.log(row[0])
      if (columnIndex == 4) {
        if (row['1w'] >= 0) {
          return 'color: #E85D2D;';
        } else return 'color: #20995B;';
      }
      if (columnIndex == 5) {
        if (row['1m'] >= 0) {
          return 'color: #E85D2D;';
        } else return 'color: #20995B;';
      }
      if (columnIndex == 6) {
        if (row['1q'] >= 0) {
          return 'color: #E85D2D;';
        } else return 'color: #20995B;';
      }
      if (columnIndex == 7) {
        if (row['1y'] >= 0) {
          return 'color: #E85D2D;';
        } else return 'color: #20995B;';
      }
    },
    getmsg () {
      let that = this;
      if (that.is_request_active) return;
      that.is_request_active = true;
      axios
        .get(this.$baseUrl + '/system/alpha/funds_qdii/')
        .then((res) => {
          that.is_request_active = false;
          this.$emit('overRequest');
          that.showmsg = false;
          ////console.log(res.data)
          that.tableData = res.data.data.gold;
          that.tableData2 = res.data.data.crud;
          that.tableData3 = res.data.data.reit;
          that.tableData4 = res.data.data.other;
        })
        .catch((err) => {
          that.showmsg = false;
          //that.$message('数据缺失')
        });
    },
    cancelchoose (event) {
      //取消单线
      if (event.target.tagName === 'INPUT')
        // console. log(this radio,000"event. target);
        // this.radio= this.radio ? '' :'1'
        this.formData.sink = this.formData.sink ? '' : '1';
    },
    godetail (id, name) {
      //带参进去
      alphaGo(id, name, this.$route.path);
    },
    godetail2 (id, name) {
      //带参进去
      this.$router.push({ path: '/alphaqdiioil', query: { id: id, name: name } });
    },
    godetail3 (id, name) {
      //带参进去
      this.$router.push({ path: '/alphaqdiireits', query: { id: id, name: name } });
    },
    godetail4 (id, name) {
      //带参进去
      this.$router.push({ path: '/alphaqdiiother', query: { id: id, name: name } });
    },
    addpool (id, name) {
      let that = this;
      axios
        .post(that.$baseUrl + '/system/alpha/pool/basket_fund/', { fund_code: id })
        .then((res) => {
          that.$message('新增成功' + '  ' + id + ' ' + name);
        })
        .catch((err) => {
          //  that.$message('失败')
          ////console.log(err)
          //that.$message('数据缺失')
        });
    }
  }
};
</script>
<style>
.choosebox {
	display: flex;

	background: #f1f1f5;
	margin: 20px;
}
.tiptablebox {
	display: flex;
}
.inputbox {
	border: 0px;
	width: 80px !important;
	outline: medium;
	text-align: center;

	padding: 0;
	-webkit-appearance: none;
	appearance: none;
	margin: 0;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
	-webkit-appearance: none !important;
	-moz-appearance: none !important;
	-o-appearance: none !important;
	-ms-appearance: none !important;
	appearance: none !important;
	margin: 0;
}
input[type='number'] {
	-webkit-appearance: textfield;
	-moz-appearance: textfield;
	-o-appearance: textfield;
	-ms-appearance: textfield;
	appearance: textfield;
}
.el-checkbox-group {
	margin-left: 0 !important;
}
/* input[readonly]{
background-color: #f1f1f5
} */
.el-form-item {
	margin-bottom: 0;
}
.homebodyfontsize .el-form-item__label {
	width: 200px !important;
	text-align: left;
}
.homebodyfontsize .el-form-item__content {
	margin-left: 200px !important;
}
</style>
<style lang="scss" scoped>
.managerDetailPage {
	margin-left: 2%;
	width: 96%;
	background: #d0d7df;
	padding: 20px;
}

.row {
	margin: -10px;
	display: flex;
}

.left {
	width: 155px;
	flex: 0 0 auto;
	margin-right: 10px;
}

.right {
	position: relative;
	flex: 1 1 100px;
}
</style>
<style lang="scss">
.comment-section {
	padding: 5px 15px 0 15px;
}

.comment {
	background: linear-gradient(90deg, #3b64f2, #1b8eff);
	color: white;
	font-size: 12px;
	padding: 12px 24px;
}

.comment.center {
	text-align: center;
}

.section {
	padding: 15px 15px 0 15px;
}

.double-table {
	display: flex;
	flex-basis: 10px;
	justify-content: space-between;

	.single-table {
		flex: 1;
	}

	.cell {
		font-size: 14px !important;
		font-weight: 400 !important;
		text-align: center !important;
		padding: 0 !important;
	}

	th {
		padding: 5px 0 !important;
	}
}

.split-cell {
	display: flex;
	align-items: center;
	justify-content: center;

	div {
		width: 40px;
	}
}
</style>
<style lang="scss" scoped>
.title {
	width: 99%;
	font-weight: 600;
	padding: 10px 15px;
}
.sub-title {
	font-size: 14px;
	font-weight: 600;
	border-left: 2px solid dodgerblue;
	margin-bottom: 6px;
	padding-left: 3px;
	line-height: 22px;
	height: 22px;
	flex: 1 1 auto;
}
.title-change-fund {
	display: flex;
	align-items: center;
	margin-bottom: 4px;
	label {
		font-size: 14px;
		margin-right: 10px;
	}
}
</style>
