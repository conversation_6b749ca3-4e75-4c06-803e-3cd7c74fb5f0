<template>
  <el-dialog :visible.sync="dialogVisible"
             width="55%"
             @close="closeDialog"
             :title="title">
    <div class="border_table main">
      <div>
        <!-- 右侧标题区域 -->
        <div class="border_table_header">
          <div class="border_table_header_title">
            <div class="block" />
            <div>{{ title }}</div>
          </div>
        </div>
        <div class="area-body">
          <div class="table">
            <el-table v-loading="tableLoading"
                      :data="tableData"
                      border
                      stripe
                      @sort-change="sortData"
                      highlight-current-row
                      @current-change="handleCurrentChange">
              <el-table-column align="gotoleft"
                               label="颗粒度"
                               prop="security">
                <template slot-scope="scope">
                  <div @click="cellClick(scope.row.data)"
                       style="color: #4096ff; cursor: pointer">
                    {{ scope.row.data.name }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column align="gotoleft"
                               label="期末规模（亿）"
                               prop="data.assets"
                               sortable="custom"
                               :formatter="formatter" />
              <el-table-column align="gotoleft"
                               label="持仓权重"
                               prop="data.weight"
                               sortable="custom"
                               :formatter="formatter" />
              <el-table-column align="gotoleft"
                               label="市值收益（万）"
                               prop="data.marketValueGainLoss"
                               sortable="custom"
                               :formatter="formatter" />
              <el-table-column align="gotoleft"
                               label="累计浮盈（万）"
                               prop="data.profit_lose"
                               sortable="custom"
                               :formatter="formatter" />
              <el-empty :image-size="180" />
            </el-table>
          </div>
        </div>
      </div>

      <div>
        <div class="border_table_header">
          <div class="border_table_header_title">
            <div class="block" />
            <div>股价走势特征</div>
          </div>
          <div class="border_table_header_filter">
            <!-- <div class="border_table_header_upload">
              <i class="el-icon-download"></i>
            </div> -->
          </div>
        </div>
        <div class="area-body">
          <div class="chart"
               v-loading="tableLoading">
            <v-chart autoresize
                     id="1"
                     element-loading-text="暂无数据"
                     element-loading-spinner="el-icon-document-delete"
                     element-loading-background="rgba(239, 239, 239, 0.5)"
                     style="height: 340px; width: 100% !important"
                     :options="performance.options" />
          </div>
        </div>
      </div>
      <div>
        <div class="border_table_header">
          <div class="border_table_header_title">
            <div class="block" />
            <div>盈利与估值</div>
          </div>
          <div class="border_table_header_filter">
            <!-- <div class="border_table_header_upload">
              <i class="el-icon-download"></i>
            </div> -->
          </div>
        </div>
        <div class="area-body">
          <div class="chart"
               v-loading="tableLoading">
            <v-chart autoresize
                     id="1"
                     element-loading-text="暂无数据"
                     element-loading-spinner="el-icon-document-delete"
                     element-loading-background="rgba(239, 239, 239, 0.5)"
                     style="height: 340px; width: 100% !important"
                     :options="particle.options" />
          </div>
        </div>
        <Person ref="person" />
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { lineChartOption, barChartOption } from '@/utils/chartStyle';
import { getObjectStockInfo } from '@/api/pages/analysis/report';
import Person from './person.vue';
import fa from 'element-ui/src/locale/lang/fa';

export default {
  components: { Person },
  data () {
    return {
      date: '',
      params: {},
      dialogVisible: false,
      title: '',
      tableData: [], // 页面表格数据源
      oldTableData: [],
      select: [
        {
          value: '选项1',
          label: '整体'
        },
        {
          value: '选项2',
          label: '分析对象1'
        },
        {
          value: '选项3',
          label: '分析对象2'
        }
      ],
      value: '',
      account: '',
      selectAccount: [
        {
          value: '选项1',
          label: '整体'
        },
        {
          value: '选项2',
          label: '分析对象1'
        },
        {
          value: '选项3',
          label: '分析对象2'
        }
      ],
      flags: 'MOM',
      promptMessage: false,
      year: '',
      performance: {
        options: {} // 业绩表现图表数据源
      },
      particle: {
        options: {} // 业绩表现图表数据源
      },
      tableLoading: false
    };
  },
  mounted () {
    this.oldTableData = this.tableData;
  },
  methods: {
    /**
     * 排序
     */
    sortData ({ column, prop, order }) {
      let arr = JSON.parse(JSON.stringify(this.tableData.filter((v) => v[prop] !== 'nan' && v[prop] !== 'NaN')));
      let noArr = this.tableData.filter((v) => v[prop] === 'nan' || v[prop] === 'NaN');
      if (order === 'ascending') {
        this.tableData = noArr.concat(arr.sort((a, b) => Number(a[prop]) - Number(b[prop])));
      }
      if (order === 'descending') {
        this.tableData = arr.sort((a, b) => Number(b[prop]) - Number(a[prop])).concat(noArr);
      }
      if (order === null) {
        this.tableData = this.oldTableData;
      }
    },

    /**
     * 数据格式
     */
    formatter (row, column, cellValue, index) {
      // 期末规模
      if (column.label === '期末规模（亿）') {
        if (cellValue === 'nan' || cellValue === 'NaN' || cellValue === undefined || !cellValue) {
          return '--';
        }
        if (Number((Number(cellValue) / 100000000).toFixed(2)) !== 0) {
          return (Number(cellValue) / 100000000).toFixed(2);
        } else {
          return '0.00（' + (Number(cellValue) / 10000).toFixed(2) + '万）';
        }
      }
      // 净买入
      if (column.label === '市值收益（万）' || column.label === '累计浮盈（万）') {
        if (cellValue === 'nan' || cellValue === 'NaN' || cellValue === undefined || !cellValue) {
          return '--';
        }
        return (Number(cellValue) / 10000).toFixed(2);
      }

      if (column.label === '持仓权重') {
        if (cellValue === 'nan' || cellValue === 'NaN' || cellValue === undefined || !cellValue) {
          return '--';
        }
        return (Number(cellValue) * 100).toFixed(2) + '%';
      }
    },

    showDialog (obj) {
      console.log(obj);
      this.particle.options = null;
      this.performance.options = null;
      this.dialogVisible = true;
      this.title = obj.name;
      this.params = obj;
      this.flags = obj?.flag || 'MOM';
      this.getObjectStockInfo();
    },

    /**
     * 选中行
     */
    handleCurrentChange (event) {
      this.getChartData(event.rowId);
    },

    /**
     * 数据整理
     */
    arrangeData (arr, key) {
      arr.forEach((item) => {
        item.data.dataArr = [[], []];
        // console.log(item);
        eval(item.data.rate_date)?.forEach((citem, cindex) => {
          // console.log(cindex);
          // console.log(item.data?.[key].replace('nan', '--'));
          item.data.dataArr[0].push({
            date: citem,
            基准指数: eval(item.data?.['基准指数'])?.[cindex] || [],
            行业指数: eval(item.data?.['行业指数'])?.[cindex] || [],
            股价走势特征: eval(item.data?.[key]?.replace('nan', '0'))?.[cindex],
            pe: eval(item.data?.['pe'])?.[cindex] || [],
            roe: eval(item.data?.['roe'])?.[cindex] || [],
            rate_date: eval(item.data?.['rate_date'])?.[cindex] || [],
            weightDate: eval(item.data?.['weightDate'])?.[cindex] || [],
            股票披露权重: eval(item.data?.['weightInNetasset'])?.[cindex] || []
          });

        }) || [];
        console.log(eval(item.data));
        console.log(typeof (eval(item.data.date)), 'ssssss');
        if (typeof (eval(item.data.date)) == 'string') {
          item.data.dataArr[1] = []
          console.log("object");
        }
        else {
          eval(item.data.date)?.forEach((citem, cindex) => {
            // console.log(item.data?.[key].replace('nan', '--'));
            item.data.dataArr[1].push({
              date: citem,
              基准指数: eval(item.data['基准指数'])?.[cindex] || [],
              行业指数: eval(item.data['行业指数'])?.[cindex] || [],
              股价走势特征: eval(item.data?.[key]?.replace('nan', '0'))?.[cindex],
              pe: eval(item.data['pe'])?.[cindex] || [],
              roe: eval(item.data['roe'])?.[cindex] || [],
              rate_date: eval(item.data['rate_date'])?.[cindex] || [],
              weightDate: eval(item.data['weightDate'])?.[cindex] || [],
              股票披露权重: eval(item.data['weightInNetasset'])?.[cindex] || []
            });
          }) || [];
        }
      });
      // arr.forEach((item) => {
      //   item.data.dataArr.sort((a, b) => a.date.localeCompare(b.date));
      // });

      return arr;
    },

    /**
     * 获取弹窗内信息
     */
    getObjectStockInfo () {
      this.tableLoading = true;
      const params = {
        reportId: Number(this.$route.query.id),
        codes: [this.params.code],
        type: this.$route.query.graininess,
        industryCode: this.params.swCode,
        flag: this.flags,
        industryStandard: 3,
        startFrom: Number(this.moment(this.$route.query.startDate).format('YYYYMMDD')),
        endTo: Number(this.moment(this.$route.query.endDate).format('YYYYMMDD'))
      };
      getObjectStockInfo(params).then(async (res) => {
        this.tableLoading = false;
        if (res.code === 200) {
          // console.log(res.data);
          let key = res.data.columns.filter((v) => v.name.includes(this.params.code))[0].name;
          console.log(key);
          this.tableData = await this.arrangeData(res.data.rows, key);
          await this.getChartData(0);
        }
      });
      // params.industryStandard = 2
      // params.industryCode = this.params.tkCode
      // getObjectStockInfo(params).then( async res => {
      //   this.tableLoading = false
      //   if(res.code === 200) {
      //     let key = res.data.columns.filter(v => v.name.includes(this.params.code))[0].name
      //     this.tableData = await this.arrangeData(res.data.rows, key)
      //     await this.getChartData(0)
      //   }
      // })
    },

    /**
     * 表格数据生成
     */
    getChartData (number) {
      // console.log(this.tableData[number].data.dataArr.map((v) => Number((v['行业指数'] * 100).toFixed(2))), this.tableData[number].data.dataArr.map((v) => Number((v['基准指数'] * 100).toFixed(2))),
      //   this.tableData[number].data.dataArr.map((v) => Number((v['股价走势特征'] * 100).toFixed(2))), this.tableData[number].data.dataArr.map((v) => Number((v['股票披露权重'] * 100).toFixed(2))),
      //   this.tableData[number].data.dataArr.map((v) => v['date'])
      // );
      console.log(this.tableData[number]);
      const line1 = [
        {
          yAxisIndex: 0,
          name: '行业指数',
          type: 'line',
          symbol: 'none',
          lineStyle: {
            width: 3
          },
          data: this.tableData[number].data.dataArr[0].map((v) => Number((v['行业指数'] * 100).toFixed(2)))
        },
        {
          yAxisIndex: 0,
          name: '沪深300',
          type: 'line',
          symbol: 'none',
          lineStyle: {
            width: 3
          },
          data: this.tableData[number].data.dataArr[0].map((v) => Number((v['基准指数'] * 100).toFixed(2)))
        },
        {
          yAxisIndex: 0,
          name: '股价走势特征',
          type: 'line',
          symbol: 'none',
          lineStyle: {
            width: 3
          },
          data: this.tableData[number].data.dataArr[0].map((v) => Number((v['股价走势特征'] * 100).toFixed(2)))
        },
        {
          yAxisIndex: 1,
          name: '股票披露权重',
          type: 'bar',
          areaStyle: {
            color: '#a1abdd'
          },
          barWidth: 15,
          data: this.tableData[number].data.dataArr[0].map((v) => [v.date, Number((v['股票披露权重'] * 100).toFixed(2))])
        }
      ];
      // console.log(this.tableData[number].data.dataArr[0].map((v) => Number((v['股票披露权重'] * 100).toFixed(2))));
      // console.log(this.tableData[number].data.dataArr[0].map((v) => v['rate_date']).sort());
      this.performance.options = barChartOption({
        color: [
          '#4096ff',
          '#4096ff',
          '#7388A9',
          '#a1abdd',
          '#9A89FF',
          '#6C96F2',
          '#FD6865',
          'rgba(253, 156, 255, 1)',
          '#83D6AE',
          'rgba(174, 201, 254, 1)',
          '#88C9E9'],
        grid: { top: '36px', bottom: '36px' }, // 位置
        toolbox: false,
        tooltip: {
          formatter: function (obj) {
            let value = `<div style="font-size:14px;">` + obj?.[0].axisValue + `</div>`;
            for (let i = 0; i < obj.length; i++) {
              value +=
                `<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
                `<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
                obj?.[i].color +
                `;"></div>` +
                `<div style="font-family: PingFang SC;">` +
                obj?.[i].seriesName +
                '</div></div>' +
                `<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
                (obj?.[i].value[1] ? Number(obj?.[i].value[1]) : Number(obj?.[i].value) || '--') +
                '%</div>' +
                `</div>`;
            }
            return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
          }
        },
        legend: {
          data: ['行业指数', '沪深300', '股价走势特征', '股票披露权重']
        },
        xAxis: [
          {
            name: '日期',
            data: this.tableData[number].data.dataArr[0].map((v) => v['rate_date']).sort()
          }
        ],
        series: line1,
        yAxis: [
          {
            name: '收益率',
            formatter: function (value) {
              return value + '%';
            }
          },

          {
            name: '权重',
            formatter: function (value) {
              return value + '%';
            }
          }
        ]
      });
      const line2 = [
        {
          name: 'pe',
          type: 'line',
          symbol: 'none',
          yAxisIndex: 0,
          lineStyle: {
            width: 3
          },
          data: this.tableData[number].data.dataArr[1].map((v) => Number(v['pe']).toFixed(2))
        },
        {
          name: 'roe',
          type: 'line',
          yAxisIndex: 1,
          symbol: 'none',
          lineStyle: {
            width: 3
          },
          data: this.tableData[number].data.dataArr[1].map((v) => Number(v['roe']).toFixed(2))
        }
      ];
      console.log(this.tableData);
      console.log(line2);
      this.particle.options = lineChartOption({
        grid: { left: '24px', right: '48px', top: '24px', bottom: '36px' }, // 位置
        dataZoom: false,
        toolbox: false,
        tooltip: {
          formatter: function (obj) {
            console.log(obj);
            let value = `<div style="font-size:14px;">` + obj?.[0].axisValue + `</div>`;
            for (let i = 0; i < obj.length; i++) {
              value +=
                `<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
                `<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
                obj?.[i].color +
                `;"></div>` +
                `<div style="font-family: PingFang SC;">` +
                obj?.[i].seriesName +
                '</div></div>' +
                `<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
                Number(obj?.[i].value) + `` + (obj?.[i].seriesName == 'roe' ? '%' : ' ') +
                '</div>' +
                `</div>`;
            }
            return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
          }
        },
        legend: {
          data: ['pe', 'roe']
        },
        xAxis: [
          {
            name: '日期',
            data: this.tableData[number].data.dataArr[1].map((v) => v['date'])
          }
        ],
        series: line2,
        yAxis: [
          {
            name: 'pe',
            type: 'value',
            formatter: function (value, index) {
              //Y轴的自定义刻度值，对应上图
              return `${value}`;
            }
          },
          {
            name: 'roe',
            type: 'value',
            formatter: function (value, index) {
              //Y轴的自定义刻度值，对应上图
              return `${value}%`;
            }
          }
        ]
      });
    },

    /**
     *给第一列添加点击事件
     * @param value
     */
    cellClick (obj) {
      this.$refs.person.showDialog(obj);
    },

    /**
     * 关闭弹窗
     */
    closeDialog () {
      this.dialogVisible = false;
      this.particle.options = null;
      this.performance.options = null;
      this.tableData = [];
    }
  }
};
</script>

<style lang="scss" scoped>
@import '../../../../tkdesign';

.main {
	height: 70vh;
	overflow-y: auto;
}

.border_table_header {
	padding-bottom: 16px;
	border-bottom: 1px solid #ccc;

	.border_table_header_title {
		display: flex;
		align-items: center;

		.block {
			width: 6px;
			height: 20px;
			border-radius: 35px;
			background-color: #4096ff;
			margin-right: 16px;
		}

		i {
			margin-left: 3px;
		}
	}

	.border_table_header_filter {
		display: flex;
		align-items: center;
		font-size: 14px;

		.border_table_header_radio {
			display: flex;
			align-items: center;
		}

		.border_table_header_select {
			margin-left: 16px;
		}

		.border_table_header_upload {
			width: 32px;
			line-height: 30px;
			border-radius: 4px;
			border: 1px solid #d9d9d9;
			text-align: center;
			margin-left: 16px;
		}
	}
}

.search-security {
	width: 210px;
	margin-right: 16px;
}
</style>
