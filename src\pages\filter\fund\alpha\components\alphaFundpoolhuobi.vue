<template>
  <div class="homebodyfontsize">
    <tempbasket ref="tempbasketfund"
                type="fund"></tempbasket>
    <div>
      <div style="margin-top: 10px; padding-bottom: 20px; background: white">
        <div class="title"
             style="display: flex; background: white !important">
          <div class="pointssearch"></div>
          筛选条件
        </div>
        <div style="margin-top: 10px">
          <div style="margin-left: 30px; margin-top: 10px">
            <el-form ref="elForm"
                     :model="formData"
                     size="medium"
                     label-width="100px">
              <el-form-item>
                <template slot="label">
                  基金规模<el-tooltip class="item"
                              effect="dark"
                              content="请输入数字，如0-2，表示0亿-2亿范围"
                              placement="right-start">
                    <svg width="14"
                         height="14"
                         viewBox="0 0 14 14"
                         fill="none">
                      <path fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
                            fill="black"
                            fill-opacity="0.45" />
                    </svg>
                  </el-tooltip>
                </template>
                <div class="tiptablebox">
                  <div>
                    <el-input type="number"
                              v-model="formData.scale.from"
                              placeholder="e.g. 30"
                              class="inputbox">
                      <i slot="suffix"><i class="yifont">亿</i></i></el-input>
                  </div>
                  <div>&nbsp; ~ &nbsp;</div>
                  <div>
                    <el-input type="number"
                              v-model="formData.scale.end"
                              placeholder="e.g. 300"
                              class="inputbox">
                      <i slot="suffix"><i class="yifont">亿</i></i></el-input>
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="基金类型一">
                <el-radio-group v-model="formData.type1"
                                size="medium">
                  <el-radio v-for="(item, index) in field102Options"
                            :key="index"
                            :label="item.value"
                            @click.native="cancelchoose">{{
										item.label
									}}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="基金类型二">
                <el-radio-group v-model="formData.type2"
                                size="medium">
                  <el-radio v-for="(item, index) in field1022Options"
                            :key="index"
                            :label="item.value"
                            @click.native="cancelchoose">{{
										item.label
									}}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item>
                <template slot="label">
                  基金最大可申购金额<el-tooltip class="item"
                              effect="dark"
                              content="请输入数字单位为万元，如5000，表示5000万元及以上"
                              placement="right-start">
                    <svg width="14"
                         height="14"
                         viewBox="0 0 14 14"
                         fill="none">
                      <path fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
                            fill="black"
                            fill-opacity="0.45" />
                    </svg>
                  </el-tooltip>
                </template>
                <div class="tiptablebox">
                  <div>
                    <el-input type="number"
                              v-model="formData.maxmoney"
                              placeholder="e.g. 30"
                              class="inputbox">
                      <i slot="suffix"><i class="yifont">万</i></i></el-input>
                  </div>
                </div>
              </el-form-item>
              <div style="height: 5px"></div>
              <el-form-item>
                <template slot="label"> 收益稳定性 </template>
                <div class="tiptablebox">
                  <el-select v-model="formData.returnwd"
                             placeholder="请选择">
                    <el-option v-for="item in optionsreturn"
                               :key="item.value"
                               :label="item.label"
                               :value="item.value"> </el-option>
                  </el-select>
                </div>
              </el-form-item>
              <el-form-item label="⻛险收益特征">
                <template slot="label">
                  风险收益特征<el-tooltip class="item"
                              effect="dark"
                              content="请输入风险特征。例如年化收益率，输入0.1-0.6，表示其范围为10%-60%"
                              placement="right-start">
                    <svg width="14"
                         height="14"
                         viewBox="0 0 14 14"
                         fill="none">
                      <path fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
                            fill="black"
                            fill-opacity="0.45" />
                    </svg>
                  </el-tooltip>
                </template>
                <el-checkbox-group v-model="temp1"
                                   size="medium">
                  <el-checkbox v-for="(item, index) in field104Options"
                               :key="index"
                               :label="item.value">{{ item.label }}</el-checkbox>
                </el-checkbox-group>
                <div>
                  <div style="display: flex; margin: 5px; background: #f1f1f5; max-width: 75vw; flex-wrap: wrap">
                    <div v-if="showfengxian"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="年化收益"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择年化"
                                     class="width160"
                                     v-model="formData.insuranceincome[0].date"
                                     :options="option1">
                        </el-cascader>
                      </div>
                      <div>
                        <el-select v-model="formData.insuranceincome[0].rank"
                                   placeholder="请选择">
                          <el-option v-for="item in optionsreturn"
                                     :key="item.value"
                                     :label="item.label"
                                     :value="item.value"> </el-option>
                        </el-select>
                      </div>
                      <div style="display: flex">
                        <div class="tiptablebox">
                          <div><el-input type="number"
                                      v-model="formData.insuranceincome[0].value1"
                                      class="inputbox"></el-input></div>
                          &nbsp;~&nbsp;
                          <div><el-input type="number"
                                      v-model="formData.insuranceincome[0].value2"
                                      class="inputbox"></el-input></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-form-item>
              <el-form-item>
                <template slot="label">
                  7日年化与7日同业拆借利差<el-tooltip class="item"
                              effect="dark"
                              content="请输入数字，如0.0002，即2bp"
                              placement="right-start">
                    <svg width="14"
                         height="14"
                         viewBox="0 0 14 14"
                         fill="none">
                      <path fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
                            fill="black"
                            fill-opacity="0.45" />
                    </svg>
                  </el-tooltip>
                </template>
                <div class="tiptablebox">
                  <div><el-input type="number"
                              placeholder="e.g. 0.0005"
                              v-model="formData.senvenmin"
                              class="inputbox"></el-input></div>
                </div>
                <div style="height: 5px"></div>
              </el-form-item>
              <!-- <el-form-item size="medium">
        <el-button type="" @click="submitForm">提交</el-button>
        <el-button @click="redoForm">重置</el-button>
        <el-button @click="resetForm">保存为模板</el-button>
         <el-button @click="printconsole">导出筛选结果</el-button>
      </el-form-item> -->
            </el-form>
            <div style="display: flex; margin-top: 20px">
              <el-button style="background: #4096FF; color: white"
                         type=""
                         size="medium"
                         @click="submitForm">提交</el-button>
              <el-button style="background: #40AFFF; color: white"
                         size="medium"
                         @click="redoForm">清除筛选条件</el-button>
              <!-- <el-button size="medium" @click="resetForm">存为模板</el-button> -->
              <!-- <el-button size="medium" @click="vismodel = true">选择模板</el-button> -->
            </div>
          </div>
        </div>
      </div>
      <div class="line2"
           style="margin-top: 5px; margin-bottom: 5px"></div>
      <div style="padding-bottom: 20px; background: white">
        <div style="display: flex">
          <div class="title"
               style="display: flex; background: white !important">
            <div class="pointssearch"></div>
            筛选结果
          </div>
          <div style="justify-content: flex-end; display: flex"
               class="marginight20px">
            <el-button size="medium"
                       style="background: #4096FF; color: white"
                       @click="printconsole">导出筛选结果</el-button>
          </div>
        </div>
        <div v-loading="showmsgsloading"
             class="tablemargin">
          <el-table :cell-style="elcellstyle"
                    :default-sort="{ prop: 'code' }"
                    :data="tableData"
                    @sort-change="sort_change"
                    class="table"
                    ref="multipleTable"
                    header-cell-class-name="table-header">
            <el-table-column prop="name"
                             align="gotoleft"
                             label="基金名称">
              <template slot-scope="scope"><a style="border-bottom: 1px solid #4096ff"
                   @click="godetail(scope.row.code, scope.row.name)">{{
									scope.row.name | isDefault
								}}</a></template>
            </el-table-column>
            <el-table-column sortable="custom"
                             prop="code"
                             label="基金代码"
                             align="gotoleft"> </el-table-column>
            <el-table-column sortable="custom"
                             prop="manager_name"
                             label="基金经理"
                             align="gotoleft">
              <template slot-scope="scope"><a style="border-bottom: 1px solid #4096ff"
                   @click="godetailP(scope.row.manager_code.split(',')[0], scope.row.manager_name.split(',')[0])">{{ scope.row.manager_name.split(',')[0] | isDefault }}</a><span v-if="scope.row.manager_code.split(',').length >= 2">,<a style="border-bottom: 1px solid #4096ff"
                     @click="godetailP(scope.row.manager_code.split(',')[1], scope.row.manager_name.split(',')[1])">{{ scope.row.manager_name.split(',')[1] | isDefault }}</a></span></template>
            </el-table-column>
            <el-table-column prop="1w"
                             sortable="custom"
                             label="近一周收益"
                             align="gotoleft">
              <template slot-scope="scope">{{ scope.row['1w'] | fix2p }}</template>
            </el-table-column>
            <el-table-column prop="1m"
                             sortable="custom"
                             label="近一月收益"
                             align="gotoleft">
              <template slot-scope="scope">{{ scope.row['1m'] | fix2p }}</template>
            </el-table-column>
            <el-table-column prop="1q"
                             sortable="custom"
                             label="近一季收益"
                             align="gotoleft">
              <template slot-scope="scope">{{ scope.row['1q'] | fix2p }}</template>
            </el-table-column>
            <el-table-column prop="1y"
                             sortable="custom"
                             label="近一年收益"
                             align="gotoleft">
              <template slot-scope="scope">{{ scope.row['1y'] | fix2p }}</template>
            </el-table-column>
            <!-- <el-table-column label="查看详情" width="100" align="gotoleft">
                    <template slot-scope="scope"><div @click="godetail(scope.row.code,scope.row.name)"><i  class="el-icon-tickets icon_color"></i></div></template>  
                </el-table-column> -->
            <el-table-column label="关注"
                             width="100"
                             align="gotoleft">
              <template slot-scope="scope">
                <div @click="addpool(scope.row.code, scope.row.name)"><i class="el-icon-circle-plus icon_color"></i></div>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <el-pagination background
                           layout="total, sizes, prev, pager, next"
                           :current-page.sync="pageIndex"
                           :page-size="pageSize"
                           :total="pageTotal"
                           @size-change="handleSizeChange"
                           @current-change="handlePageChange"></el-pagination>
          </div>
        </div>
      </div>
    </div>
    <el-dialog title="选择添加的基金池"
               :visible.sync="addfundvis"
               width="20%"
               destroy-on-close>
      基金代码:<br /><el-input type="text"
                :disabled="true"
                :value="choosefundid"
                label="基金代码"></el-input> 基金名称:<br /><el-input type="text"
                :disabled="true"
                :value="choosefundname"
                label="基金名称"></el-input>
      基金池：<br /><el-select style="width: 100%"
                 v-model="choosedpool"
                 placeholder="请选择您的基金池">
        <el-option v-for="item in options"
                   :key="item.value"
                   :label="item.label"
                   :value="item.value"> </el-option>
      </el-select>
      <br />理由:<br /><el-input type="textarea"
                v-model="choosereason"
                label="选择的理由"></el-input>
      <span slot="footer"
            class="dialog-footer">
        <el-button type="primary"
                   @click="saveEdit(form)">提 交</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { alphaGo } from '@/assets/js/alpha_type.js';
import tempbasket from './components/tempbasket';
import axios from '@/api/index';
export default {
  components: { tempbasket },
  props: [],
  data () {
    return {
      alldata: [],
      tableData: [],
      pageTotal: null,
      pageIndex: 1,
      pageSize: 20,
      showmsgsloading: false,
      options: [
        {
          value: '选项1',
          label: '池子1'
        },
        {
          value: '选项2',
          label: '池子2'
        },
        {
          value: '选项3',
          label: '池子3'
        },
        {
          value: '选项4',
          label: '池子4'
        },
        {
          value: '选项5',
          label: '池子5'
        }
      ],
      showfengxian: false,
      showfengxian1: false,
      showfengxian2: false,
      showfengxian3: false,
      showfengxian4: false,
      showej: false,
      temp1: [],
      temp3: [],
      temp4: [],
      temp5: [],
      hgchoose: [],
      czchoose: [],
      dpchoose: [],
      swchoose: [],
      czshow: false,
      dpshow: false,
      swshow: false,
      hangyeshow: false,
      fenggeshow: false,
      kuanjishow: true,
      choosedpool: null,
      choosereason: null,
      choosefundid: null,
      choosefundname: null,
      optionsreturn: [
        {
          value: '100',
          label: '全部'
        },
        {
          value: '1',
          label: 'TOP 1%'
        },
        {
          value: '5',
          label: 'TOP 5%'
        },
        {
          value: '10',
          label: 'TOP 10%'
        },
        {
          value: '25',
          label: 'TOP 25%'
        },
        {
          value: '50',
          label: 'TOP 50%'
        },
        {
          value: '70',
          label: 'TOP 70%'
        },
        {
          value: '80',
          label: 'TOP 80%'
        }
      ],
      option: [
        {
          value: 'now',
          label: '近期表现',
          children: [
            {
              value: '1m',
              label: '一月'
            },
            {
              value: '3m',
              label: '三月'
            },
            {
              value: '6m',
              label: '六月'
            },
            {
              value: '1y',
              label: '一年'
            },
            {
              value: '2y',
              label: '两年'
            }
          ]
        }
      ],
      addfundvis: false,
      tableData: [],
      fundname: null,
      option1: [
        { value: '7日年化', label: '7日年化' },
        {
          value: '14日年化',
          label: '14日年化'
        },
        {
          value: '28日年化',
          label: '28日年化'
        },
        {
          value: '三月年化',
          label: '三月年化'
        },
        {
          value: '六月年化',
          label: '六月年化'
        },
        {
          value: '一年年化',
          label: '一年年化'
        },
        {
          value: '两年年化',
          label: '两年年化'
        }
      ],
      option2: [
        { value: 'now', label: '近期表现', children: [] },
        {
          value: 'from',
          label: '从那时起',
          children: [
            {
              value: '2015-08-26',
              label: '2015-08-26'
            }
          ]
        }
      ],
      option3: [
        { value: 'now', label: '近期表现', children: [] },
        {
          value: 'from',
          label: '从那时起',
          children: [
            {
              value: '2015-08-26',
              label: '2015-08-26'
            }
          ]
        }
      ],
      option4: [
        { value: 'now', label: '近期表现', children: [] },
        {
          value: 'from',
          label: '从那时起',
          children: [
            {
              value: '2015-08-26',
              label: '2015-08-26'
            }
          ]
        }
      ],
      option5: [
        { value: 'now', label: '近期表现', children: [] },
        {
          value: 'from',
          label: '从那时起',
          children: [
            {
              value: '2015-08-26',
              label: '2015-08-26'
            }
          ]
        }
      ],
      formData: {
        scale: {
          from: null,
          end: null
        },
        type1: null,
        type2: null,
        returnwd: '100',
        maxmoney: null,
        insuranceincome: [
          {
            id: 1,
            rank: '100',
            date: '7日年化',
            value1: null,
            value2: null
          }
        ],
        senvenmin: null
      },
      field102Options: [
        {
          label: '场内',
          value: '场内'
        },
        {
          label: '场外',
          value: '场外'
        }
      ],
      field1022Options: [
        {
          label: '摊余成本法',
          value: '摊余成本法'
        },
        {
          label: '市场法估值',
          value: '市场法估值'
        }
      ],
      field104Options: [
        {
          label: '区间收益', //波动率：区间数据（默认下届为0）
          value: 1
        }
      ],
      field105Options: [
        {
          label: '二级市场周期',
          value: 1
        }
      ],
      field107Options: [
        {
          label: '成长vs价值',
          value: 1
        },
        {
          label: '大盘vs小盘',
          value: 2
        }
      ],
      field108Options: [
        {
          label: '申万一级行业',
          value: 1
        }
      ],
      is_request_active: false
    };
  },
  computed: {},
  watch: {
    temp1 (val) {
      this.showfengxian = false;
      //  this.showfengxian1 =false
      //  this.showfengxian2 =false
      //  this.showfengxian3 =false
      //  this.showfengxian4 =false
      ////console.log(val)
      if (val.indexOf(1) > -1) {
        this.showfengxian = true;
        //////console.log('波动率')
      } else {
        this.$set(this.formData.insuranceincome[0], 'date', null);
        this.$set(this.formData.insuranceincome[0], 'value1', null);
        this.$set(this.formData.insuranceincome[0], 'value2', null);
        this.$set(this.formData.insuranceincome[0], 'rank', '100');
      }
      // if(val.indexOf(2)>-1){
      //   this.showfengxian1 = true
      //   //////console.log('波动率')
      // }else{
      //   this.$set(this.formData.insuranceincome[1],'date',[])
      //   this.$set(this.formData.insuranceincome[1],'value1',null)
      // }
      // if(val.indexOf(3)>-1){
      //   this.showfengxian2 = true
      //   //////console.log('波动率')
      // }else{
      //   this.$set(this.formData.insuranceincome[2],'date',[])
      //   this.$set(this.formData.insuranceincome[2],'value1',null)
      // }
      //  if(val.indexOf(4)>-1){
      //   this.showfengxian3 = true
      //   //////console.log('波动率')
      // }else{
      //   this.$set(this.formData.insuranceincome[3],'date',[])
      //   this.$set(this.formData.insuranceincome[3],'value1',null)
      // }
      // if(val.indexOf(5)>-1){
      //   this.showfengxian4 = true
      //   //////console.log('波动率')
      // }else{
      //   this.$set(this.formData.insuranceincome[4],'date',[])
      //   this.$set(this.formData.insuranceincome[4],'value1',null)
      // }
    }
  },
  created () {
    if (this.localStorage.getItem('formDatamoney') != null && this.localStorage.getItem('formDatamoney') != 'null') {
      this.formData = JSON.parse(this.localStorage.getItem('formDatamoney'));
    }
    if (this.localStorage.getItem('temp1money') != null && this.localStorage.getItem('temp1money') != 'null') {
      this.temp1 = JSON.parse(this.localStorage.getItem('temp1money'));
    }
    let that = this;
    if (this.localStorage.getItem('alldatamoney') != null && this.localStorage.getItem('alldatamoney') != 'null') {
      this.alldata = JSON.parse(this.localStorage.getItem('alldatamoney'));
      that.tableData = this.alldata.slice(0, 19);
      that.pageTotal = this.alldata.length;
    } else {
      that.showmsgsloading = true;
      if (that.is_request_active) return;
      that.is_request_active = true;
      axios
        .post(this.$baseUrl + '/system/alpha/money_filter/', { fund_type: 'money' })
        .then((res) => {
          that.is_request_active = false;
          that.showmsgsloading = false;
          that.alldata = res.data;

          that.tableData = res.data.data.slice(0, 19);
          that.pageTotal = res.data.data.length;
          this.localStorage.setItem('formDatamoney', JSON.stringify(that.formData));
          this.localStorage.setItem('temp1money', JSON.stringify(that.temp1));
          this.localStorage.setItem('alldatamoney', JSON.stringify(that.alldata));
        })
        .catch((err) => {
          that.showmsgsloading = false;
          that.$message('筛选失败');
        });
    }
  },
  mounted () { },
  filters: {
    fix6 (value) {
      return value.substring(0, 10);
    },
    fix3 (value) {
      return parseInt(value * 1000) / 1000;
    },
    fix2p (value) {
      return (value * 100).toFixed(2) + '%';
    },
    isDefault (value) {
      return value == '--' ? '' : value;
    }
  },
  methods: {
    my_desc_sort (name) {
      //  ////console.log(name)
      return function (a, b) {
        if (a[name] > b[name]) {
          return -1;
        } else if (a[name] < b[name]) {
          return 1;
        } else {
          return 0;
        }
      };
    },
    my_asc_sort (name) {
      return function (a, b) {
        if (a[name] < b[name]) {
          return -1;
        } else if (a[name] > b[name]) {
          return 1;
        } else {
          return 0;
        }
      };
    },

    sort_change (column) {
      // ////console.log(column)
      // ////console.log('colum')
      this.pageIndex = 1; // return to the first page after sorting
      if (column.prop === 'code') {
        if (column.order === 'descending') {
          this.alldata = this.alldata.sort(this.my_desc_sort('code'));
        } else if (column.order === 'ascending') {
          this.alldata = this.alldata.sort(this.my_asc_sort('code'));
        }
      } else if (column.prop === '1y') {
        if (column.order === 'descending') {
          this.alldata = this.alldata.sort(this.my_desc_sort('1y'));
        } else if (column.order === 'ascending') {
          this.alldata = this.alldata.sort(this.my_asc_sort('1y'));
        }
      } else if (column.prop === '1m') {
        if (column.order === 'descending') {
          this.alldata = this.alldata.sort(this.my_desc_sort('1m'));
        } else if (column.order === 'ascending') {
          this.alldata = this.alldata.sort(this.my_asc_sort('1m'));
        }
      } else if (column.prop === '1q') {
        if (column.order === 'descending') {
          this.alldata = this.alldata.sort(this.my_desc_sort('1q'));
        } else if (column.order === 'ascending') {
          this.alldata = this.alldata.sort(this.my_asc_sort('1q'));
        }
      } else if (column.prop === '1w') {
        if (column.order === 'descending') {
          this.alldata = this.alldata.sort(this.my_desc_sort('1w'));
        } else if (column.order === 'ascending') {
          this.alldata = this.alldata.sort(this.my_asc_sort('1w'));
        }
      }
      this.tableData = this.alldata.slice(0, this.pageSize); // show only one page
    },
    godetailP (id, name) {
      //带参进去
      this.$router.push({ path: '/fundmanagerdetail/' + id, hash: '', query: { id: id, name: name } });
    },
    elcellstyle ({ row, column, rowIndex, columnIndex }) {
      // ////console.log(row[0])

      if (columnIndex == 3) {
        if (row['1w'] >= 0) {
          return 'color: #E85D2D;';
        } else return 'color: #20995B';
      }
      if (columnIndex == 4) {
        if (row['1m'] >= 0) {
          return 'color: #E85D2D;';
        } else return 'color: #20995B;';
      }
      if (columnIndex == 5) {
        if (row['1q'] >= 0) {
          return 'color: #E85D2D;';
        } else return 'color: #20995B;';
      }
      if (columnIndex == 6) {
        if (row['1y'] >= 0) {
          return 'color: #E85D2D;';
        } else return 'color: #20995B;';
      }
    },
    cancelchoose (event) {
      //取消单线
      if (event.target.tagName === 'INPUT')
        // console. log(this radio,000"event. target);
        // this.radio= this.radio ? '' :'1'
        this.formData.sink = this.formData.sink ? '' : '1';
    },
    godetail (id, name) {
      //带参进去
      alphaGo(id, name, this.$route.path);
    },
    addpool (id, name) {
      let that = this;
      axios
        .post(that.$baseUrl + '/system/alpha/pool/basket_fund/', { fund_code: id })
        .then((res) => {
          that.$message('新增成功' + '  ' + id + ' ' + name);
        })
        .catch((err) => {
          //  that.$message('失败')
          ////console.log(err)
          //that.$message('数据缺失')
        });
    },
    submitForm () {
      // ////console.log(this.form)
      this.showmsgsloading = true;
      this.submitflag = true;
      if (this.formData.scale.from != null) {
        if (this.formData.scale.from < 0) {
          this.submitflag = false;
          this.$message('基金当前规模前置范围不能小于0');
        }
      }
      if (this.formData.scale.end != null) {
        if (this.formData.scale.end < 0) {
          this.submitflag = false;
          this.$message('基金当前规模后置范围不能小于0');
        }
      }
      if (
        this.formData.scale.from != null &&
        this.formData.scale.end != null &&
        this.formData.scale.from != '' &&
        this.formData.scale.end != ''
      ) {
        if (Number(this.formData.scale.from) > Number(this.formData.scale.end)) {
          this.submitflag = false;
          this.$message('基金当前规模的前置范围大于后置范围');
        }
      }
      if (this.formData.maxmoney != null) {
        if (this.formData.maxmoney < 0) {
          this.$message('申购额度不能小于0');
          this.submitflag = false;
        }
      }
      if (
        this.formData.insuranceincome[0].value1 != null &&
        this.formData.insuranceincome[0].value2 != null &&
        this.formData.insuranceincome[0].value1 != '' &&
        this.formData.insuranceincome[0].value2 != ''
      ) {
        if (this.formData.insuranceincome[0].value1 > this.formData.insuranceincome[0].value2) {
          this.submitflag = false;
          this.$message('风险收益特征中，区间收益的前置范围不能大于后置范围');
        }
      }
      if (this.formData.insuranceincome[0].value1 != null && this.formData.insuranceincome[0].value2 != null) {
        if (this.formData.insuranceincome[0].date == null) {
          this.submitflag = false;
          this.$message('风险收益特征中，区间收益的时间范围未选择');
        }
      }

      if (this.submitflag == true) {
        if (this.formData.maxmoney == null || this.formData.maxmoney == '') {
          this.formData.maxmoney = '0';
        }
        ////console.log("发送请求")
        ////console.log(this.formData)
        let that = this;
        if (that.is_request_active) return;
        that.is_request_active = true;
        axios
          .post(this.$baseUrl + '/system/alpha/money_filter/', { fund_type: 'money', filter_condition: that.formData })
          .then((res) => {
            that.is_request_active = false;
            that.showmsgsloading = false;
            that.alldata = res.data.data;
            that.tableData = res.data.data.slice(0, 19);
            that.pageTotal = res.data.data.length;
            that.pageIndex = 1;
            this.localStorage.setItem('formDatamoney', JSON.stringify(that.formData));
            this.localStorage.setItem('temp1money', JSON.stringify(that.temp1));
            this.localStorage.setItem('alldatamoney', JSON.stringify(that.alldata));
          })
          .catch((err) => {
            that.showmsgsloading = false;
            that.$message('筛选失败');
            that.alldata = [];
            that.tableData = [];
            that.pageTotal = 0;
            that.pageIndex = 1;
          });
      }
    },
    handlePageChange () {
      this.tableData = this.alldata.slice((this.pageIndex - 1) * this.pageSize, this.pageIndex * this.pageSize);
    },
    handleSizeChange (val) {
      this.pageSize = val;
      this.tableData = this.alldata.slice((this.pageIndex - 1) * this.pageSize, this.pageIndex * this.pageSize);
    },
    resetForm () {
      this.$refs['elForm'].resetFields();
    },
    printconsole () {
      const { export_json_to_excel } = require('@/vendor/Export2Excel');
      var list = [];
      let tHeader = [];
      let filterVal = [];

      tHeader = ['基金名称', '基金经理姓名', '基金代码', '近一周收益', '近一月收益', '近一季收益', '近一年收益'];
      filterVal = ['name', 'manager', 'code', '1w', '1m', '1q', '1y'];
      ////console.log(this.alldata)
      for (let i = 0; i < this.alldata.length; i++) {
        list[i] = [];
        list[i][0] = this.alldata[i].name;
        list[i][1] = this.alldata[i].manager_name;
        list[i][2] = this.alldata[i].code;
        list[i][3] = this.alldata[i]['1w'];
        list[i][4] = this.alldata[i]['1m'];
        list[i][5] = this.alldata[i]['1q'];
        list[i][6] = this.alldata[i]['1y'];
      }

      export_json_to_excel(tHeader, list, '货币筛选结果');
    },
    redoForm () {
      this.formData = {
        scale: {
          from: null,
          end: null
        },
        type1: null,
        type2: null,
        maxmoney: null,
        returnwd: '100',
        insuranceincome: [
          {
            id: 1,
            rank: '100',
            date: null,
            value1: null,
            value2: null
          }
        ],
        senvenmin: null
      };
      this.temp1 = [];
    }
  }
};
</script>
<style>
.choosebox {
	display: flex;
	align-items: center;
	background: #f1f1f5;
	margin: 20px;
}
.tiptablebox {
	display: flex;
}
.inputbox {
	border: 0px;
	width: 80px !important;
	outline: medium;
	text-align: center;

	padding: 0;
	-webkit-appearance: none;
	appearance: none;
	margin: 0;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
	-webkit-appearance: none !important;
	-moz-appearance: none !important;
	-o-appearance: none !important;
	-ms-appearance: none !important;
	appearance: none !important;
	margin: 0;
}
input[type='number'] {
	-webkit-appearance: textfield;
	-moz-appearance: textfield;
	-o-appearance: textfield;
	-ms-appearance: textfield;
	appearance: textfield;
}
.el-checkbox-group {
	margin-left: 0 !important;
}
/* input[readonly]{
background-color: #f1f1f5
} */
.el-form-item {
	margin-bottom: 0;
}
.homebodyfontsize .el-form-item__label {
	width: 200px !important;
	text-align: left;
}
.homebodyfontsize .el-form-item__content {
	margin-left: 200px !important;
}
</style>
<style lang="scss" scoped>
.managerDetailPage {
	margin-left: 2%;
	width: 96%;
	background: #d0d7df;
	padding: 20px;
}

.row {
	margin: -10px;
	display: flex;
}

.left {
	width: 155px;
	flex: 0 0 auto;
	margin-right: 10px;
}

.right {
	position: relative;
	flex: 1 1 100px;
}
</style>
<style lang="scss">
.comment-section {
	padding: 5px 15px 0 15px;
}

.comment {
	background: linear-gradient(90deg, #3b64f2, #1b8eff);
	color: white;
	font-size: 12px;
	padding: 12px 24px;
}

.comment.center {
	text-align: center;
}

.section {
	padding: 15px 15px 0 15px;
}

.double-table {
	display: flex;
	flex-basis: 10px;
	justify-content: space-between;

	.single-table {
		flex: 1;
	}

	.cell {
		font-size: 14px !important;
		font-weight: 400 !important;
		text-align: center !important;
		padding: 0 !important;
	}

	th {
		padding: 5px 0 !important;
	}
}

.split-cell {
	display: flex;
	align-items: center;
	justify-content: center;

	div {
		width: 40px;
	}
}
</style>
<style lang="scss" scoped>
.title {
	width: 99%;
	font-weight: 600;
	padding: 10px 15px;
}
.sub-title {
	font-size: 14px;
	font-weight: 600;
	border-left: 2px solid dodgerblue;
	margin-bottom: 6px;
	padding-left: 3px;
	line-height: 22px;
	height: 22px;
	flex: 1 1 auto;
}
.title-change-fund {
	display: flex;
	align-items: center;
	margin-bottom: 4px;
	label {
		font-size: 14px;
		margin-right: 10px;
	}
}
</style>
