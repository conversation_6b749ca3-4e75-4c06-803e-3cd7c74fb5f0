<!--  -->
<template>
  <div class="assetDetails">
    <!-- 调整持仓主页 -->
    <el-dialog :modal-append-to-body="false"
               v-loading="loadingx"
               :append-to-body="true"
               :close-on-click-modal="false"
               class="ADdialog"
               width="1000px"
               :visible.sync="show"
               title="调整持仓">
      <div slot="title">
        <span style="
						font-family: 'PingFang';
						font-style: normal;
						font-weight: 500;
						font-size: 16px;
						line-height: 24px;
						color: rgba(0, 0, 0, 0.85);
						width: 100%;
					">
          调整持仓（{{ combinationName }}）</span>
      </div>
      <div style="width: 100%; height: 1px; background: rgba(0, 0, 0, 0.06); margin-bottom: 16px"></div>
      <div style="display: flex; align-items: center; margin-bottom: 16px">
        <div>调仓日期：</div>
        <div id="step1"
             style="margin-right: 24px">
          <el-date-picker v-model="updateDate"
                          @change="changeDate"
                          type="date"
                          value-format="yyyy-MM-dd"
                          style="width: 144px"
                          placeholder="请选择"
                          :picker-options="pickerOptions">
          </el-date-picker>
        </div>
        <el-button id="step2"
                   @click="showMoney = true"
                   :disabled="Time != TimeOrigin"
                   type="">现金存取</el-button>
        <el-button @click="showAddTrad = true"
                   :disabled="Time != TimeOrigin"
                   type="">交易录入</el-button>
        <el-button @click="showAddTradFile = true"
                   :disabled="Time != TimeOrigin"
                   type="">交易流水文件导入</el-button>
        <el-button @click="showAddTradFileHold = true"
                   :disabled="Time != TimeOrigin"
                   type="">持仓文件导入</el-button>
      </div>
      <div style="display: flex; margin-bottom: 16px;width: 100%;"
           v-loading="loadingtable">
        <adjustDateList :dateList="dateList"
                        :currentDate="currentDate"
                        @click="handleDateClick"
                        @deleteDate="deleteDate"></adjustDateList>
        <div style="flex:1;">
          <el-table height="460px"
                    :data="tableData">
            <el-table-column prop="code"
                             align="gotoleft"
                             show-overflow-tooltip
                             label="代码"></el-table-column>
            <el-table-column prop="name"
                             align="gotoleft"
                             show-overflow-tooltip
                             label="简称"></el-table-column>
            <el-table-column prop="nav"
                             align="gotoleft"
                             width="120px"
                             sortable
                             label="最新收盘价">
              <templete slot-scope="scope">{{ scope.row.nav | fix2 }}</templete>
            </el-table-column>
            <el-table-column prop="holdings"
                             align="gotoleft"
                             sortable
                             label="持仓数量">
              <template slot-scope="scope">{{ scope.row.holdings | fixW }}</template>
            </el-table-column>
            <el-table-column prop="totalmv"
                             align="gotoleft"
                             sortable
                             label="持仓市值">
              <templete slot-scope="scope">{{ scope.row.totalmv | fixW }}</templete>
            </el-table-column>
            <el-table-column prop="weight"
                             align="gotoleft"
                             sortable
                             label="权重">
              <templete slot-scope="scope">{{ scope.row.weight | fix2p }}</templete>
            </el-table-column>
            <el-table-column prop="begin_nav"
                             align="gotoleft"
                             sortable
                             label="成本价">
              <templete slot-scope="scope">{{ scope.row.beginNav | fix2 }}</templete>
            </el-table-column>
            <el-table-column prop="date"
                             align="gotoleft"
                             width="120px"
                             sortable
                             label="收盘日期"> </el-table-column>
            <!-- <el-table-column v-if="Time == TimeOrigin" sortable align="gotoleft" label="操作">
							<template slot-scope="scope"
								><span
									style="cursor: pointer; color: #4096ff"
									@click="updateFund(scope.row.code, scope.row.name, scope.row.holdings, scope.row.begin_nav)"
									>修改</span
								></template
							>
						</el-table-column> -->
          </el-table>
        </div>
      </div>

      <div style="display: flex; justify-content: space-between; align-items: center">
        <div>
          <!-- <el-button :disabled="Time != TimeOrigin" @click="resetUpdate" type="">重置修改</el-button> -->
        </div>
        <div>
          <el-button @click="show = false"
                     type="">取消</el-button>
          <el-button :disabled="Time != TimeOrigin"
                     @click="submitChange()"
                     class="step3"
                     type="primary">确认</el-button>
        </div>
      </div>
    </el-dialog>
    <!-- 现金存取 -->
    <el-dialog :close-on-click-modal="false"
               class="ADdialog"
               :visible.sync="showMoney"
               width="334px"
               title="现金存取"
               v-loading="moneyLoading">
      <div slot="title">
        <span style="
						font-family: 'PingFang';
						font-style: normal;
						font-weight: 500;
						font-size: 16px;
						line-height: 24px;
						color: rgba(0, 0, 0, 0.85);
						width: 100%;
					">现金存取</span>
      </div>
      <div style="width: 100%; height: 1px; background: rgba(0, 0, 0, 0.06); margin-bottom: 16px"></div>
      <div style="margin-bottom: 16px">调仓日期：{{ currentDate }}</div>
      <div style="margin-bottom: 16px">
        <span style="margin-left: 14px"></span>调整前：<el-input style="width: 214px"
                  v-model="money"
                  disabled></el-input>
      </div>
      <div style="margin-bottom: 16px">
        <span style="margin-left: 42px"></span>存：<el-input v-model="addMoney.in"
                  @input="changein"
                  style="width: 214px"
                  placeholder=""></el-input>
      </div>
      <div style="margin-bottom: 16px">
        <span style="margin-left: 42px"></span>取：<el-input v-model="addMoney.out"
                  @input="changeout"
                  style="width: 214px"
                  placeholder=""></el-input>
      </div>
      <div style="margin-bottom: 16px">
        <span style="margin-left: 14px"></span>调整后：<el-input style="width: 214px"
                  disabled
                  v-model="addMoney.result"></el-input>
      </div>
      <div style="text-align: right">
        <el-button @click="showMoney = false"
                   type="">取消</el-button>
        <el-button @click="moneyInOut()"
                   type="primary">确认</el-button>
      </div>
    </el-dialog>
    <!-- 交易录入 -->
    <el-dialog :close-on-click-modal="false"
               class="ADdialog"
               width="360px"
               :visible.sync="showAddTrad"
               title="交易录入"
               v-loading="adLoading">
      <div slot="title">
        <span style="
						font-family: 'PingFang';
						font-style: normal;
						font-weight: 500;
						font-size: 16px;
						line-height: 24px;
						color: rgba(0, 0, 0, 0.85);
						width: 100%;
					">交易录入</span>
      </div>
      <div style="width: 100%; height: 1px; background: rgba(0, 0, 0, 0.06); margin-bottom: 16px"></div>
      <div style="margin-bottom: 16px">调仓日期：{{ currentDate }}</div>
      <div style="margin-bottom: 16px; display: flex; align-items: center">
        证券代码：
        <el-select style="width: 214px"
                   v-model="trad.code"
                   :remote-method="searchpeople"
                   @change="changeCode"
                   filterable
                   remote
                   prefix-icon="el-icon-search"
                   :loading="loading"
                   placeholder="输入查询基金/股票">
          <el-option-group :label="havefundmanager[0].label">
            <template v-for="(group,i) in havefundmanager[0].options">
              <el-option v-if="defer(i)"
                         :key="group.code"
                         :label="
								group.flag == 'fund' || group.flag == 'stock'
									? `${group.code}-${group.name}-${group.fundCo.split('基金')[0]}`
									: `${group.name}-${group.code}`
							"
                         :value="group.code + '|' + group.name + '|' + group.flag">
              </el-option>
            </template>

          </el-option-group>
          <el-option-group :label="havefundmanager[1].label">
            <template v-for="(group,i) in havefundmanager[1].options">
              <el-option v-if="defer(i)"
                         :key="group.code"
                         :label="
								group.flag == 'fund' || group.flag == 'stock'
									? `${group.code}-${group.name}-${group.fundCo.split('基金')[0]}`
									: `${group.name}-${group.code}`
							"
                         :value="group.code + '|' + group.name + '|' + group.flag">
              </el-option>
            </template>
          </el-option-group>
        </el-select>
      </div>
      <div style="margin-bottom: 16px">
        交易数量：<el-radio-group v-model="trad.action">
          <el-radio :label="true">买入</el-radio>
          <el-radio :label="false">卖出</el-radio>
        </el-radio-group>
        <div style="margin-left: 5em; margin-top: 8px">
          <el-input v-model="trad.number"
                    style="width: 214px"
                    placeholder=""
                    @input="tradNumberChange">
            <i style="display: flex; align-items: center; height: 100%"
               slot="suffix">份</i></el-input>
        </div>
      </div>

      <div v-loading="loadingfalg"
           style="margin-bottom: 8px">
        交易价格：<el-radio-group @change="changeTradPrice"
                        v-model="trad.PriceFlag">
          <el-radio :label="false"
                    style="margin-right: 8px">前收盘价</el-radio>
          <!-- <el-radio :label="false" style="margin-right: 8px">最新价</el-radio>
					<el-radio :label="'custom'" style="margin-right: 8px">自定义</el-radio> -->
        </el-radio-group>
      </div>
      <div style="margin-bottom: 16px">
        <el-input v-model="trad.price"
                  :disabled="!trad.PriceFlag"
                  style="width: 214px; margin-left: 70px"
                  placeholder=""></el-input>
      </div>
      <div style="display: flex; align-items: top; margin-bottom: 16px; width: 100%">
        <div>交易总额：</div>
        <div>
          <el-input v-model="allmoney"
                    style="width: 214px"
                    placeholder=""
                    disabled>
            <i style="display: flex; align-items: center; height: 100%"
               slot="suffix">元</i></el-input>
        </div>
      </div>
      <div style="text-align: right">
        <el-button @click="showAddTrad = false"
                   type="">取消</el-button>
        <el-button type="primary"
                   @click="submitTrad">确认</el-button>
      </div>
    </el-dialog>

    <!-- 交易流水文件导入 -->
    <el-dialog v-loading="loadingTradInput"
               :close-on-click-modal="false"
               class="ADdialog"
               width="800px"
               :visible.sync="showAddTradFile"
               title="交易流水文件导入">
      <div slot="title">
        <span style="
						font-family: 'PingFang';
						font-style: normal;
						font-weight: 500;
						font-size: 16px;
						line-height: 24px;
						color: rgba(0, 0, 0, 0.85);
						width: 100%;
					">交易流水文件导入</span>
      </div>
      <div style="width: 100%; height: 1px; background: rgba(0, 0, 0, 0.06); margin-bottom: 16px"></div>
      <div style="height:107px;margin-bottom: 8px; display: flex; justify-content: space-between; align-items: center">
        <div>
          <div>
            请点击下载<span style="color: #4096ff; cursor: pointer"
                  @click="exportExcel">“交易流水模板”</span>，按此模板整理数据后在通过按钮导入
          </div>
          <div>仅支持上传xls,xlsx格式文件</div>
          <el-upload :auto-upload="false"
                     :on-remove="handleRemove"
                     :show-file-list="true"
                     :multiple="false"
                     accept=".xls,.xlsx"
                     :limit="1"
                     :file-list="fileList"
                     :disabled="false">
          </el-upload>
        </div>
        <div>
          <!-- <el-button type="primary">上传文件导入</el-button> -->
          <el-upload :auto-upload="false"
                     :on-change="changeAppendix"
                     :show-file-list="false"
                     :multiple="false"
                     accept=".xls,.xlsx">
            <el-button type="primary">上传文件导入</el-button>
          </el-upload>
        </div>
      </div>
      <div style="margin-bottom: 16px">
        <el-table height="400px"
                  :data="dataTableModel">
          <el-table-column align="gotoleft"
                           prop="f"
                           label="买卖日期"></el-table-column>
          <el-table-column align="gotoleft"
                           prop="a"
                           label="证券代码"></el-table-column>
          <el-table-column align="gotoleft"
                           prop="c"
                           label="买卖数量"></el-table-column>
          <el-table-column align="gotoleft"
                           prop="e"
                           label="买卖方向"></el-table-column>

        </el-table>
      </div>
      <div style="text-align: right">
        <el-button @click="showAddTradFile = false"
                   type="">取消</el-button>
        <el-button type="primary"
                   @click="addExcel(fileList)">确认</el-button>
      </div>
    </el-dialog>
    <!-- 持仓文件导入 -->
    <el-dialog v-loading="loadingTradInput"
               :close-on-click-modal="false"
               class="ADdialog"
               width="800px"
               :visible.sync="showAddTradFileHold"
               title="交易流水文件导入">
      <div slot="title">
        <span style="
						font-family: 'PingFang';
						font-style: normal;
						font-weight: 500;
						font-size: 16px;
						line-height: 24px;
						color: rgba(0, 0, 0, 0.85);
						width: 100%;
					">
          持仓文件导入</span>
      </div>
      <div style="width: 100%; height: 1px; background: rgba(0, 0, 0, 0.06); margin-bottom: 16px"></div>
      <div style="height:107px;margin-bottom: 8px; display: flex; justify-content: space-between; align-items: center">
        <div>
          <div>
            请点击下载<span style="color: #4096ff; cursor: pointer"
                  @click="exportExcel2">“持仓模板”</span>，按此模板整理数据后在通过按钮导入
          </div>
          <div>仅支持上传xls,xlsx格式文件</div>
          <el-upload :auto-upload="false"
                     :on-remove="handleRemove2"
                     :show-file-list="true"
                     :multiple="false"
                     accept=".xls,.xlsx"
                     :limit="1"
                     :file-list="fileList2"
                     :disabled="false">
          </el-upload>
        </div>
        <div>
          <!-- <el-button type="primary">上传文件导入</el-button> -->
          <el-upload :auto-upload="false"
                     :on-change="changeAppendix2"
                     :show-file-list="false"
                     :multiple="false"
                     accept=".xls,.xlsx">
            <el-button type="primary">上传文件导入</el-button>
          </el-upload>
        </div>
      </div>
      <div style="margin-bottom: 16px">
        <el-table height="400px"
                  :data="dataTableModel2">
          <el-table-column align="gotoleft"
                           prop="e"
                           label="调整日期"></el-table-column>
          <el-table-column align="gotoleft"
                           prop="a"
                           label="证券代码"></el-table-column>
          <el-table-column align="gotoleft"
                           prop="c"
                           label="持仓权重"></el-table-column>
        </el-table>
      </div>
      <div style="text-align: right">
        <el-button @click="showAddTradFileHold = false"
                   type="">取消</el-button>
        <el-button type="primary"
                   @click="addExcel2(fileList2)">确认</el-button>
      </div>
    </el-dialog>
    <!-- 等权重调仓 -->
    <el-dialog v-loading="loadingxS"
               :close-on-click-modal="false"
               class="ADdialog"
               width="334px"
               :visible.sync="showequial"
               title="等权重调仓">
      <div slot="title">
        <span style="
						font-family: 'PingFang';
						font-style: normal;
						font-weight: 500;
						font-size: 16px;
						line-height: 24px;
						color: rgba(0, 0, 0, 0.85);
						width: 100%;
					">等权重调仓</span>
      </div>
      <div style="width: 100%; height: 1px; background: rgba(0, 0, 0, 0.06); margin-bottom: 16px"></div>
      <div style="margin-bottom: 16px"><span style="margin-left: 8px"></span>总资产：{{ moneyall }}</div>
      <div style="margin-bottom: 16px; margin-left: 8px">
        <div>目标调仓比例(%)：</div>
        <div>
          <el-input style="width: 220px"
                    v-model="equial.edit"
                    placeholder=""></el-input>
        </div>
      </div>
      <div style="margin-bottom: 16px; margin-left: 8px">
        目标市值：<el-input style="width: 220px"
                  v-model="equial.all"
                  placeholder=""></el-input>
      </div>
      <div style="text-align: right">
        <el-button @click="showequial = false"
                   type="">取消</el-button>
        <el-button @click="DoComEquityWeight"
                   type="primary">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import adjustDateList from './components/adjustDateList.vue';
import { getRecodeList, getRecodeDateList, getSearchList, adjustHolding, combinationHoldNav, adjustPositionConfirm, deleteAdjustPositionRecord, uploadTemplate } from '@/api/pages/tkAnalysis/portfolio.js';
export default {
  props: {
    showDetails: {
      type: Boolean,
      default: false
    },
    combinationName: {
      type: String,
      default: ''
    },
    comId: {
      type: String,
      default: ''
    },
    endDate: {
      type: String,
      default: ""
    }
  },
  model: {
    // 建议显示把这个写上
    event: 'update:showDetails',
    prop: 'showDetails'
  },
  //import引入的组件需要注入到对象中才能使用
  components: { adjustDateList },
  filters: {
    fix2p (value) {
      return value &&
        value != '' &&
        value != '--' &&
        value != '- -' &&
        JSON.stringify(value) != '[]' &&
        JSON.stringify(value) != '{}' &&
        value != 'NAN' &&
        value != 'nan'
        ? (Number(value) * 100).toFixed(2) + '%'
        : '0';
    },
    fix2 (value) {
      return value &&
        value != '' &&
        value != '--' &&
        value != '- -' &&
        JSON.stringify(value) != '[]' &&
        JSON.stringify(value) != '{}' &&
        value != 'NAN' &&
        value != 'nan'
        ? Number(value).toFixed(2)
        : '--';
    },
    fixY (value) {
      return value &&
        value != '' &&
        value != '--' &&
        value != '- -' &&
        JSON.stringify(value) != '[]' &&
        JSON.stringify(value) != '{}' &&
        value != 'NAN' &&
        value != 'nan'
        ? Number(value / 100000000).toFixed(2) + '亿'
        : '--';
    },
    fixW (value) {
      return value &&
        value != '' &&
        value != '--' &&
        value != '- -' &&
        JSON.stringify(value) != '[]' &&
        JSON.stringify(value) != '{}' &&
        value != 'NAN' &&
        value != 'nan'
        ? Number(value / 10000).toFixed(2) + '万'
        : '0';
    }
  },
  data () {
    //这里存放数据
    return {
      moneyMin: 0,
      loadingxS: false,
      loadingx: false,
      fileList: [],
      fileList2: [],
      fileName: '',
      fileName2: '',
      loadingTradInput: false,
      currentDate: '',
      appendixList2: [],
      appendixList: [],
      loadingfalg: false,
      showMoney: false,
      show: false,
      comid: '',
      updateDate: '',
      dateList: [],
      options: [],
      Time: '',
      TimeOrigin: '',
      description: '',
      money: 0,
      moneyall: 0,
      allmoney: 0,
      tableData: [],
      showAddTrad: false,
      showAddTradFile: false,
      showAddTradFileHold: false,
      showequial: false,
      adLoading: false,
      moneyLoading: false,
      // 现金存取
      addMoney: {
        in: '0.00',
        out: '0.00',
        result: '0.00',
        result2: 0
      },
      moneyChange: 0,
      // 交易录入
      trad: {
        code: '',
        action: true,
        number: '0.00',
        price: '0.00',
        PriceFlag: false,
        description: ''
      },
      // 等权重调仓
      equial: {
        edit: '0.00',
        all: '0.00'
      },
      loadingtable: false,
      dataTable: [],
      tableDataDetail: [],
      havefundmanager: [
        {
          label: '基金产品',
          options: []
        },
        {
          label: '股票',
          options: []
        }
      ],
      dataTableModel: [
        { a: '000001.OF', c: '1000000', e: '划入', f: '2018-02-02' },
        { a: '000001.OF', c: '1000', e: '买入', f: '2018-02-02' },
        { a: '000001.OF', c: '100000', e: '买入', f: '2018-02-02' },
        { a: '000001.OF', c: '100000', e: '卖出', f: '2021-04-06' },
      ],
      dataTableModel2: [
        { a: 'CNY', c: '20.00%', e: '2021-02-02' },
        { a: '600519.SH', c: '20.00%', e: '2021-02-02' },
        { a: '000002.SZ', c: '20.00%', e: '2021-02-02' },
        { a: '0700.HK', c: '20.00%', e: '2021-02-02' },
        { a: '512000.SH', c: '20.00%', e: '2021-04-06' },
        { a: '600519.SH', c: '30.00%', e: '2021-04-06' },
        { a: '000001.OF', c: '30.00%', e: '2021-04-06' },
        { a: '000001.OF', c: '-10.00%', e: '2021-04-06' },
      ],
      showUpdate: false,
      pickerOptions: {
        disabledDate (time) {
          return false;
        },
      }
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {
    show () {
      this.$emit('update:showDetails', this.show);
    },
    endDate: {
      handler (val) {
        if (val.trim()) {
          this.pickerOptions.disabledDate = (time) => {
            return time.getTime() > (new Date(val).getTime());
          }
        }
      },
      immediate: true
    },
    showDetails () {
      this.show = this.showDetails;
      if (this.showDetails) {
        this.showdialog(this.comId)
        this.getRecodeDateList();
      }
    },
    showAddTrad (val) {
      if (this.trad.code && this.trad.code.split('|')[0]) {
        this.trade.price = '0.00';
        this.changeTradPrice()

      }
    },
    'trad.action' (val) {
      if (val) {
        if (this.trad.number < 0) {
          this.trad.number = this.trad.number * -1;
        }
      } else {
        if (this.trad.number >= 0) {
          this.trad.number = this.trad.number * -1;
        }
      }
    },

  },
  //方法集合
  methods: {
    handleRemove () {
      this.fileList = [];
    },
    handleRemove2 () {
      this.fileList2 = [];
    },
    showEquityAll () {
      this.showequial = true;
      this.equial.all = this.moneyall;
      this.equial.edit = this.tableData?.length == 0 ? 0 : (100 / this.tableData?.length).toFixed(2);
    },
    changeCode () {
      this.trad.PriceFlag = false;
      this.changeTradPrice();
    },
    changeDate () {
      if (this.dateList.find(date => date === this.updateDate)) {
        this.$message.warning('调仓日期已经存在');
        this.updateDate = '';
      } else {
        this.dateList.push(this.updateDate);
        this.dateList.sort();
        this.currentDate = this.updateDate;
        this.getRecodeList(this.currentDate);
        this.updateDate = '';
      }
    },
    async deleteDate (date) {
      this.loadingtable = true;

      let { mtycode, mtymessage } = await deleteAdjustPositionRecord({
        combinationId: this.comid,
        date,
      })
      if (mtycode == 200) {
        this.dateList = this.dateList.filter(item => item !== date);
        this.getRecodeDateList();

      } else {
        this.$message.error(mtymessage);
      }
      this.loadingtable = false;

    },

    showdialog (id, date) {
      // 清空状态
      this.Time = this.TimeOrigin;
      this.tableData = [];
      this.moneyChange = 0;
      this.loadingTradInput = false;
      this.fileList = [];
      this.fileList2 = [];
      this.fileName = '';
      this.fileName2 = '';
      this.dataTableModel = [
        { a: 'CNY', c: '1000000', e: '买入', f: '2018-02-02' },
        { a: '000700.HK', c: '1000', e: '买入', f: '2018-02-02' },
        { a: '000001.OF', c: '100000', e: '买入', f: '2018-02-02' },
        { a: '000001.OF', c: '100000', e: '卖出', f: '2021-04-06' },
      ];
      this.dataTableModel2 = [
        { a: 'CNY', c: '20.00%', e: '2021-02-02' },
        { a: '600519.SH', c: '20.00%', e: '2021-02-02' },
        { a: '000002.SZ', c: '20.00%', e: '2021-02-02' },
        { a: '0700.HK', c: '20.00%', e: '2021-02-02' },
        { a: 'IF.CFE', c: '-10%', e: '2021-04-06' },


      ];
      this.tableDataDetail = [];
      this.appendixList2 = [];
      this.appendixList = [];
      this.showAddTradFile = false;
      this.showAddTradFileHold = false;
      // end
      this.comid = id;
      this.show = true;
    },
    // 现金存取
    async moneyInOut () {
      if (Number(this.addMoney.result) < 0) this.$message.error('不支持借款');
      else {
        this.moneyChange += this.addMoney.result - this.money;
        this.money = this.addMoney.result;
        this.showMoney = false;
        this.addMoney.in = '0.00';
        this.addMoney.out = '0.00';
        this.moneyLoading = true;
        await this.adjustHold({ code: 'CNY', value: this.addMoney.result2, flag: 'share', status: 'trading' })
        this.getRecodeList(this.currentDate);
        this.showMoney = false;
      }


    },
    // 存
    changein () {
      this.addMoney.result = Number(this.money) + Number(this.addMoney.in) - Number(this.addMoney.out);
      this.addMoney.result2 = Number(this.addMoney.in) - Number(this.addMoney.out);
    },
    // 取
    changeout () {
      this.addMoney.result = Number(this.money) + Number(this.addMoney.in) - Number(this.addMoney.out);
      this.addMoney.result2 = Number(this.addMoney.in) - Number(this.addMoney.out);
    },
    // 交易录入
    // 搜索股票基金
    async searchpeople (query) {
      ////console.log(query)
      ////console.log(this.values)
      let that = this;
      this.loading = true;

      that.havefundmanager = [
        {
          label: '基金产品',
          options: []
        },
        {
          label: '股票',
          options: []
        }
      ];
      let { data } = await getSearchList({ message: query, flag: '1,9' });
      that.loading = false;

      if (this.FUNC.isEmpty(data)) {
        let temparr = [
          {
            label: '基金产品',
            options: []
          },
          {
            label: '股票',
            options: []
          }
        ];
        for (let i = 0; i < data.length; i++) {
          if (data[i].flag === 'fund') {
            temparr[0].options.push(data[i]);
          } else if (data[i].flag == 'stock') {
            temparr[1].options.push(data[i]);
          }
        }
        that.havefundmanager = temparr;
      }

    },
    defer (maxFrameCount = 500) {
      const state = Vue.observable({ frameCount: 0 })
      const reflashFrameCount = () => {
        requestAnimationFrame(() => {
          state.frameCount++
          if (state.frameCount <= maxFrameCount) {
            reflashFrameCount()
          }
        })
      }
      reflashFrameCount()
      return showFrameCount => {
        return state.frameCount >= showFrameCount
      }
    },
    async submitTrad () {
      if (String(this.trad.price) == '0.00') {
        this.$message.error('成交价格不能为0');
      } else if (String(this.trad.number) == '0.00' || String(this.trad.number) == '0') {
        this.$message.error('交易权重不能为0');
      } else if (!this.FUNC.isEmpty(this.trad.code)) {
        this.$message.error('基金代码不能为空');
      } else {
        this.adLoading = true;
        await this.adjustHold({ code: this.trad.code.split('|')[0], value: String(this.trad.number), flag: 'share', status: 'trading' });

        this.getRecodeList(this.currentDate);
        this.showAddTrad = false;
      }
    },
    //调整持仓
    async adjustHold ({ code, value, flag, status, holdInfos, fileName = '' }) {
      let { mtycode, mtymessage } = await adjustHolding({ holdInfos: holdInfos ? holdInfos : [{ code, value, flag, date: this.currentDate }], combinationId: this.comid, status, fileName });
      if (mtycode == '200') {
        this.$message.success(mtymessage);
      } else {
        this.$message.error(mtymessage);
      }
      this.adLoading = false;
      this.moneyLoading = false;
      return { mtycode, mtymessage }
    },
    // 切换签收价和自定义
    // TODO
    async changeTradPrice () {
      if (this.trad.PriceFlag) {
      } else {
        // 请求牵手价格
        this.loadingfalg = true;
        let { data, mtymessage, mtycode } = await combinationHoldNav({
          code: this.trad.code.split('|')[0],
          date: this.currentDate,
          flag: this.trad.code.split('|')[2]
        });
        if (mtycode == 200) {
          this.trad.price = Number(data.price).toFixed(2);
        } else {
          this.$message.error('前收价格获取失败');
          this.trad.price = Number('0').toFixed(2);
        }
        this.allmoney = this.trad.price * this.trad.number;
        this.loadingfalg = false;
      }
    },
    tradNumberChange () {
      if (this.trad.action) {
        if (this.trad.number < 0) {
          this.trad.number = this.trad.number * -1;
        }
      } else {
        if (this.trad.number >= 0) {
          this.trad.number = this.trad.number * -1;
        }
      }
      this.allmoney = this.trad.price * this.trad.number;
    },
    // 交易模板导入导出

    exportExcel () {
      const { export_json_to_excel } = require('@/vendor/Export2Excel');
      var list = [];
      let tHeader = [];

      tHeader = ['买卖日期', '证券代码', '买卖数量', '买卖方向',];
      for (let i = 0; i < this.dataTableModel.length; i++) {
        list[i] = [];
        list[i][1] = this.dataTableModel[i].a;
        list[i][2] = this.dataTableModel[i].c;
        list[i][3] = this.dataTableModel[i].e;
        list[i][0] = this.dataTableModel[i].f;
      }

      export_json_to_excel(tHeader, list, '交易流水模板');
    },
    // 持仓模板导出
    exportExcel2 () {
      const { export_json_to_excel } = require('@/vendor/Export2Excel');
      var list = [];
      let tHeader = [];

      tHeader = ['调整日期', '证券代码', '持仓权重',];
      // ////console.log(this.colums)
      for (let i = 0; i < this.dataTableModel2.length; i++) {
        list[i] = [];
        list[i][1] = this.dataTableModel2[i].a;
        list[i][2] = this.dataTableModel2[i].c;
        list[i][0] = this.dataTableModel2[i].e;
      }

      export_json_to_excel(tHeader, list, '持仓模板');
    },
    // 手动提交change
    async submitChange () {
      // if (this.moneyMin == 0 && this.options.length == 0) {
      // 	this.$message.warning('请存入现金，无剩余金额无法调仓');
      // 	return false;
      // }
      this.loadingx = true;
      let { data, mtymessage, mtycode } = await adjustPositionConfirm({
        combinationId: this.comid,
      });
      if (mtycode == 200) {
        this.$emit('updatePort');
        this.show = false;

        this.loadingx = false;
        this.$message.success('成功');
      } else {
        this.$message.error(mtymessage);
        this.loadingx = false;
      }
    },
    // 等权重调仓
    // TODO
    async DoComEquityWeight () {
      this.loadingxS = true;
      /* let { data, mtymessage, mtycode } = await ComEquityWeight({
        combination_id: this.comid,
        weight: this.equial.edit,
        totalmv: this.equial.all,
        date: this.updateDate
      });
      if (mtycode == 200) {
        this.$message.success('成功调仓，生成新一期记录');
        this.showequial = false;
        this.loadingxS = false;
        this.$emit('updatePort');
        if (this.$route.path.indexOf('portfolioAnalysis') >= 0) {
          window.reload();
          this.show = false;
        } else {
          this.show = true;
        }
        this.getData();
      } else {
        this.loadingxS = false;
        this.$message.error(mtymessage);
      } */
    },
    // excel导入
    async changeAppendix (file, fileList) {
      this.loadingTradInput = true;
      const _this = this;
      if (file.status === 'ready') {
        let forms = new FormData()

        forms.append('combinationId', this.comid)
        forms.append('type', 'trading')
        forms.append('file', file.raw)
        let res = await uploadTemplate(forms)
        if (res.mtycode == 200) {
          this.$message.success('文件上传成功，点击确定提交上传内容')
          this.fileList = [{ name: file.name }]
          this.fileName = res.data.fileName;
          if (res.data.tradingTemplates?.length > 0) {
            this.dataTableModel = res.data.tradingTemplates.map((item) => {
              return {
                a: item.code,
                c: item.value,
                f: item.date,
                e: item.direction
              }
            })
          } else {
            this.dataTableModel = [
              { a: 'CNY', c: '1000000', e: '买入', f: '2018-02-02' },
              { a: '000700.HK', c: '1000', e: '买入', f: '2018-02-02' },
              { a: '000001.OF', c: '100000', e: '买入', f: '2018-02-02' },
              { a: '000001.OF', c: '100000', e: '卖出', f: '2021-04-06' },
            ];
          }
        } else {
          this.$message.error(res.mtymessage)
        }
        this.loadingTradInput = false;
      }
    },
    // excel持仓导入
    async changeAppendix2 (file, fileList) {
      this.loadingTradInput = true;
      const _this = this;
      if (file.status === 'ready') {
        let forms = new FormData()

        forms.append('combinationId', this.comid)
        forms.append('type', 'holding')
        forms.append('file', file.raw)
        console.log('forms:::', forms)
        let res = await uploadTemplate(forms)
        console.log(res)
        if (res.mtycode == 200) {
          this.$message.success('文件上传成功，点击确定提交上传内容')
          this.fileList2 = [{ name: file.name }]
          this.fileName2 = res.data.fileName;
          if (res.data.holdingTemplates?.length > 0) {
            this.dataTableModel2 = res.data.holdingTemplates.map((item) => {
              return {
                a: item.code,
                c: item.value,
                e: item.date
              }
            })
          } else {
            this.dataTableModel2 = [
              { a: 'CNY', c: '20.00%', e: '2021-02-02' },
              { a: '600519.SH', c: '20.00%', e: '2021-02-02' },
              { a: '000002.SZ', c: '20.00%', e: '2021-02-02' },
              { a: '0700.HK', c: '20.00%', e: '2021-02-02' },
              { a: 'IF.CFE', c: '-10%', e: '2021-04-06' },
            ]
          }
        } else {
          this.$message.error(res.mtymessage)
        }
        this.loadingTradInput = false;

      }
      // const reader = new FileReader();
      // reader.readAsArrayBuffer(file.raw);
      // reader.onload = function () {
      // 	const buffer = reader.result;
      // 	const bytes = new Uint8Array(buffer);
      // 	const length = bytes.byteLength;
      // 	let binary = '';
      // 	for (let i = 0; i < length; i++) {
      // 		binary += String.fromCharCode(bytes[i]);
      // 	}
      // 	// const XLSX = require('xlsx');
      // 	const wb = XLSX.read(binary, {
      // 		type: 'binary'
      // 	});
      // 	const outdata = XLSX.utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]]);
      // 	// console.log(outdata);
      // 	outdata.forEach((i) => {
      // 		console.log(i);
      // 		let obj = {
      // 			a: i['证券代码'],
      // 			c: i['持仓权重'],
      // 			e: i['调整日期']
      // 		};
      // 		if (i['证券代码'] == '' || i['持仓权重'] == '') {
      // 		} else {
      // 			_this.dataTableModel2.push(obj); //此处是把数据添加到表格中
      // 		}
      // 	});
      // };
    },
    // 交易流水导入
    async addExcel (fileList) {
      let fileName = '';
      if (fileList.length > 0) {
        fileName = this.fileName;
      }
      // if (this.moneyMin == 0 && this.options.length == 0) {
      // 	this.$message.warning('请存入现金，无剩余金额无法调仓');
      // 	return false;
      // }
      this.loadingTradInput = true;
      try {
        let falgs = false;
        // for (let i = 0; i < this.dataTableModel.length; i++) {
        // 	if (this.dataTableModel[i].a == '000001.OF') {
        // 		falgs = true;
        // 	}
        // }
        if (!falgs) {
          let portfolio_holding = [];
          for (let i = 0; i < this.dataTableModel.length; i++) {
            portfolio_holding.push({
              code: this.dataTableModel[i].a,
              value: this.dataTableModel[i].c.replace(/\,/g, ""),
              flag: 'share',
              date: this.dataTableModel[i].f
            });
            if (
              !this.FUNC.isEmpty(this.dataTableModel[i].a) ||
              !this.FUNC.isEmpty(this.dataTableModel[i].c) ||
              !this.FUNC.isEmpty(this.dataTableModel[i].e)
            ) {
              this.$message.error('excel有未填项,无法写入数据');
              this.loadingTradInput = false;
              return false;
            }
          }
          let { mtycode } = await this.adjustHold({ holdInfos: portfolio_holding, status: 'trading', fileName });
          if (mtycode == 200) {
            // for (let i = 0; i < this.dataTableModel.length; i++) {
            // 	this.dateList.push(this.dataTableModel[i].f);
            // }
            // this.dateList = [...new Set(this.dateList)];
            // this.dateList.sort();
          }
          this.getRecodeDateList(this.currentDate);
          this.showAddTradFile = false;
        } else {
          this.loadingTradInput = false;
          this.$message.warning('请勿使用演示模板数据');
        }
      } catch (err) {
        this.$message.error('异常原因导致中断');
        console.log(err);
      }
    },
    // 持仓文件导入
    async addExcel2 (fileList) {
      let fileName = '';
      if (fileList.length > 0) {
        fileName = this.fileName2;
      }
      // if (this.moneyMin == 0 && this.options.length == 0) {
      // 	this.$message.warning('请存入现金，无剩余金额无法调仓');
      // 	return false;
      // }
      this.loadingTradInput = true;
      try {
        let falgs = false;
        // for (let i = 0; i < this.dataTableModel2.length; i++) {
        // 	if (this.dataTableModel2[i].a == '000002.SZ') {
        // 		falgs = true;
        // 	}
        // }

        if (!falgs) {
          let portfolio_holding = [];
          let temparrX = [];
          for (let i = 0; i < this.dataTableModel2.length; i++) {
            if (!this.FUNC.isEmpty(this.dataTableModel2[i].a) || !this.FUNC.isEmpty(this.dataTableModel2[i].c)) {
              this.loadingTradInput = false;
              this.$message.error('excel有未填项,无法写入数据');
              return false;
            }
            // if (temparrX.indexOf(this.dataTableModel2[i].a) < 0) {
            // 	temparrX.push(this.dataTableModel2[i].a);
            // } else { 	
            // 	this.loadingTradInput = false;
            // 	this.$message.error('导入失败，导入持仓中同一只产品出现了两次');
            // 	return false;
            // }
            portfolio_holding.push({
              code: this.dataTableModel2[i].a,
              value: parseFloat(this.dataTableModel2[i].c) || 0,
              flag: 'weight',
              date: this.dataTableModel2[i].e
            });
          }
          let { mtycode } = await this.adjustHold({ holdInfos: portfolio_holding, status: 'holding', fileName });
          if (mtycode == 200) {
            // for (let i = 0; i < this.dataTableModel2.length; i++) {
            // 	this.dateList.push(this.dataTableModel2[i].e);
            // }
            // this.dateList = [...new Set(this.dateList)];
            // this.dateList.sort();
          }
          this.getRecodeDateList(this.currentDate);
          this.showAddTradFileHold = false;
        } else {
          this.$message.warning('请勿使用演示模板数据');
        }
      } catch (err) {
        this.$message.error('异常原因导致中断');
        console.log(err);
      }
      this.loadingTradInput = false;
    },
    async getRecodeDateList () {
      let res = await getRecodeDateList({
        combinationId: this.comid
      })
      if (res.mtycode == 200) {
        this.dateList = res.data.dateList || [];
        if (this.dateList.length > 0) {
          this.currentDate = this.dateList[0];
          this.getRecodeList(this.dateList[0]);
        } else {
          this.currentDate = '';
          this.tableData = [];
        }
      }
    },
    async getAssetDetails () {
      let res = await getAssetDetails({
        combinationId: this.comid
      })
      if (res.mtycode == 200) {
        this.assetDetails = res.data || {};
      }
    },
    async getAssetDetails2 () {
      let res = await getAssetDetails2({
        combinationId: this.comid
      })
      if (res.mtycode == 200) {
        this.assetDetails2 = res.data || {
        }
      }
    },
    async getRecodeList (date) {
      this.loadingtable = true;

      let { mtycode, data } = await getRecodeList({
        combinationId: this.comid,
        date,
      });
      if (mtycode == 200) {
        this.tableData = data;
        let currency = this.tableData.find((item) => {
          return item.code === 'CNY'
        });
        if (currency) {
          this.money = Number(currency.totalmv) || 0;
          this.addMoney = {
            in: '0.00',
            out: '0.00',
            result: '0.00',
            result2: 0
          }
        } else {
          this.money = 0;
          this.addMoney = {
            in: '0.00',
            out: '0.00',
            result: '0.00',
            result2: 0
          }
        }
      }
      this.loadingtable = false;

    },
    handleDateClick (date) {
      this.currentDate = date;
      this.getRecodeList(date)
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () {
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () {

  },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style>
.ADdialog .el-dialog__body {
	padding-top: 0 !important;
}
</style>
<style lang="scss" scoped>
//@import url(); 引入公共css类
</style>
