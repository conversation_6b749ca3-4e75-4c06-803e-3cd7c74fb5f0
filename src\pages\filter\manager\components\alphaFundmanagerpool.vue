<template>
  <div class="homebodyfontsize managerequity">
    <tempbasket type="manager"></tempbasket>
    <div>
      <div style="margin-top: 10px; padding-bottom: 20px; background: white">
        <div style="display: flex">
          <div class="title"
               style="display: flex; background: white !important">
            <div class="pointssearch"></div>筛选条件
          </div>
          <div style="justify-content: flex-end; display: flex"></div>
        </div>
        <div style="margin-top: 10px">
          <div style="margin-left: 30px; margin-top: 20px">
            <alphachoosepool ismanagertype="manager"
                             @changepool="changepool"></alphachoosepool>
            <el-form ref="elForm"
                     autosize
                     :model="formData"
                     size="medium"
                     label-width="100px">
              <div style="display: flex">
                <div>
                  <el-form-item label="基金当前规模">
                    <template slot="label">
                      基金经理当前规模
                      <el-tooltip class="item"
                                  effect="dark"
                                  content="请输入数字，如0-2，表示0亿-2亿范围"
                                  placement="right-start">
                        <svg width="14"
                             height="14"
                             viewBox="0 0 14 14"
                             fill="none">
                          <path fill-rule="evenodd"
                                clip-rule="evenodd"
                                d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
                                fill="black"
                                fill-opacity="0.45" />
                        </svg>
                      </el-tooltip>
                    </template>
                    <div class="tiptablebox">
                      <div>
                        <el-input type="number"
                                  placeholder="e.g. 0"
                                  v-model="formData.field101"
                                  class="inputbox">
                          <i slot="suffix">
                            <i class="yifont">亿</i>
                          </i>
                        </el-input>
                      </div>
                      <div>&nbsp; ~ &nbsp;</div>
                      <div>
                        <el-input type="number"
                                  placeholder="e.g. 1000"
                                  v-model="formData.field1011"
                                  class="inputbox">
                          <i slot="suffix">
                            <i class="yifont">亿</i>
                          </i>
                        </el-input>
                      </div>
                    </div>
                  </el-form-item>
                </div>
                <div style="margin-left: 10vw">
                  <el-form-item label="管理者管理经验"
                                prop="field102">
                    <template slot="label">
                      管理者管理经验
                      <el-tooltip class="item"
                                  effect="dark"
                                  content="请输入数字，如3，表示管理经验大于3年"
                                  placement="right-start">
                        <svg width="14"
                             height="14"
                             viewBox="0 0 14 14"
                             fill="none">
                          <path fill-rule="evenodd"
                                clip-rule="evenodd"
                                d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
                                fill="black"
                                fill-opacity="0.45" />
                        </svg>
                      </el-tooltip>
                    </template>
                    <div class="tiptablebox">
                      <div>
                        <el-input type="number"
                                  placeholder="e.g. 3"
                                  v-model="formData.field102"
                                  class="inputbox">
                          <i slot="suffix">
                            <i class="yifont">年</i>
                          </i>
                        </el-input>
                      </div>
                      <div>&nbsp; ~ &nbsp;</div>
                      <div>
                        <el-input type="number"
                                  placeholder="e.g. 5"
                                  v-model="formData.field1021"
                                  class="inputbox">
                          <i slot="suffix">
                            <i class="yifont">年</i>
                          </i>
                        </el-input>
                      </div>
                    </div>
                  </el-form-item>
                </div>
              </div>

              <!-- <el-form-item label="能力择优" prop="field103">
        <template slot='label'>
            能力择优<el-tooltip class="item" effect="dark" content="极少有人或产品在以下能力都占优" placement="right-start">
                          <svg width="14" height="14" viewBox="0 0 14 14" fill="none">
									<path
										fill-rule="evenodd"
										clip-rule="evenodd"
										d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
										fill="black"
										fill-opacity="0.45"
									/>
								</svg>
                        </el-tooltip>
         </template>
        <el-checkbox-group v-model="formData.haveability" size="medium">
          <el-checkbox v-for="(item, index) in haveabliltyOptions" :key="index" :label="item.value"
            :disabled="item.disabled">{{item.label}}</el-checkbox>
        </el-checkbox-group>
              </el-form-item>-->
              <!-- <el-form-item label="大行业特征">
								<template slot="label"> 大行业特征 </template>
								<el-checkbox-group v-model="formData.bigindustry" size="medium">
									<el-checkbox v-for="(item, index) in bigindustryOptions" :key="index" :label="item.value" :disabled="item.disabled">{{
										item.label
									}}</el-checkbox>
								</el-checkbox-group>
              </el-form-item>-->
              <el-form-item label="风险特征"
                            prop="field104">
                <template slot="label">
                  风险特征
                  <el-tooltip class="item"
                              effect="dark"
                              content="请输入风险特征。例如波动率，输入0.1-0.6，表示其范围为10%-60%"
                              placement="right-start">
                    <svg width="14"
                         height="14"
                         viewBox="0 0 14 14"
                         fill="none">
                      <path fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
                            fill="black"
                            fill-opacity="0.45" />
                    </svg>
                  </el-tooltip>
                </template>
                <div style="display: flex">
                  <el-checkbox-group style="display: flex; align-items: center; flex-wrap: wrap"
                                     v-model="temp"
                                     size="medium">
                    <el-checkbox v-for="(item, index) in field104Options"
                                 v-show="showmore1 ? index < 5 : true"
                                 :key="index"
                                 :label="item.value"
                                 :disabled="item.disabled">{{ item.label }}</el-checkbox>
                  </el-checkbox-group>
                  <div v-show="showmore1"
                       @click="showmore1 = !showmore1"
                       style="display: flex; align-items: center; flex-wrap: wrap">
                    <i style="font-size: 16px"
                       class="el-icon-caret-right"></i>
                  </div>
                  <div v-show="!showmore1"
                       @click="showmore1 = !showmore1"
                       style="display: flex; align-items: center; flex-wrap: wrap">
                    <i style="font-size: 16px"
                       class="el-icon-caret-left"></i>
                  </div>
                </div>
                <div>
                  <div style="display: flex; margin: 5px; background: #f1f1f5; max-width: 75vw; flex-wrap: wrap">
                    <div v-if="showbodong"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="波动率"></el-input>
                        <!-- <el-tooltip class="item" effect="dark" placement="top-start" :content="content1">
                            
                        </el-tooltip>-->
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     class="width160"
                                     v-model="formData.field104[0].date"
                                     :options="option"></el-cascader>
                      </div>
                      <selecterinput name="波动率"
                                     @setvalue1="setvalue1"
                                     @setvalue2="setvalue2"
                                     indexid="1.1"
                                     @setrank="setrank"
                                     :rankvalue="formData.field104[0].rank"
                                     :valuef="formData.field104[0].value1"
                                     :valueb="formData.field104[0].value2"
                                     :buttonarray="selectvalue1"></selecterinput>
                      <!-- <div style="display:flex"> -->
                      <!-- <div class="tiptablebox"> 
                        <div style='margin-left:10px;'><el-button  type="primary">0-0.3</el-button></div>
                        <div style='margin-left:10px;'><el-button  >0.3-0.6</el-button></div>
                        <div style='margin-left:10px;'> 
                         <el-popover
                             placement="right"
                            >
                            <i slot="reference" class="el-icon-edit"></i>
                      <div style='display:flex'>-->
                      <!-- <div><el-input type="number" placeholder='e.g. 0.1' v-model="formData.field104[0].value1" class="inputbox"></el-input></div>
                        <div style="display:flex;align-self:center">&nbsp; ~ &nbsp;</div>
                      <div><el-input type="number" placeholder='e.g. 0.3' v-model="formData.field104[0].value2" class="inputbox"></el-input></div>-->
                      <!-- </div>
                          </el-popover>
                          </div>


                      </div>-->
                      <!-- </div> -->
                    </div>

                    <div v-if="showbodong1"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="最大回撤"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     class="width160"
                                     v-model="formData.field104[1].date"
                                     :options="option2"></el-cascader>
                      </div>
                      <selecterinput name="最大回撤"
                                     @setvalue1="setvalue1"
                                     @setvalue2="setvalue2"
                                     indexid="1.2"
                                     @setrank="setrank"
                                     :rankvalue="formData.field104[1].rank"
                                     :valuef="formData.field104[1].value1"
                                     :valueb="formData.field104[1].value2"
                                     :buttonarray="selectvalue2"></selecterinput>
                      <!-- <div>
                       <div class="tiptablebox">&nbsp;区间&nbsp;
                         <div><el-input type="number" placeholder='e.g. 0.1' v-model="formData.field104[1].value1" class="inputbox"></el-input></div>
                        <div>&nbsp; ~ &nbsp;</div>
                         <div><el-input type="number" placeholder='e.g. 0.3' v-model="formData.field104[1].value2" class="inputbox"></el-input></div>
                         </div>
                      </div>-->
                    </div>

                    <div v-if="showbodong2"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="平均下行周期"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     class="width160"
                                     v-model="formData.field104[2].date"
                                     :options="option3"></el-cascader>
                      </div>
                      <selecterinput name="平均下行周期"
                                     @setvalue1="setvalue1"
                                     @setvalue2="setvalue2"
                                     indexid="1.3"
                                     @setrank="setrank"
                                     :rankvalue="formData.field104[2].rank"
                                     :valuef="formData.field104[2].value1"
                                     :valueb="formData.field104[2].value2"
                                     :buttonarray="selectvalue3"></selecterinput>
                      <!-- <div>
                       <div class="tiptablebox">&nbsp;区间&nbsp;
                         <div><el-input type="number" placeholder='e.g. 0' v-model="formData.field104[2].value1" class="inputbox"></el-input>天</div>
                        <div>&nbsp; ~ &nbsp;</div>
                         <div><el-input type="number" placeholder='e.g. 10' v-model="formData.field104[2].value2" class="inputbox"></el-input>天</div>
                         </div>
                      </div>-->
                    </div>

                    <div v-if="showbodong3"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="平均恢复周期"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     class="width160"
                                     v-model="formData.field104[3].date"
                                     :options="option4"></el-cascader>
                      </div>
                      <selecterinput name="平均恢复周期"
                                     @setvalue1="setvalue1"
                                     @setvalue2="setvalue2"
                                     indexid="1.4"
                                     @setrank="setrank"
                                     :rankvalue="formData.field104[3].rank"
                                     :valuef="formData.field104[3].value1"
                                     :valueb="formData.field104[3].value2"
                                     :buttonarray="selectvalue4"></selecterinput>
                      <!-- <div>
                       <div class="tiptablebox">&nbsp;区间&nbsp;
                         <div><el-input type="number" placeholder='e.g. 0' v-model="formData.field104[3].value1" class="inputbox"></el-input>天</div>
                        <div>&nbsp; ~ &nbsp;</div>
                         <div><el-input type="number" placeholder='e.g. 10' v-model="formData.field104[3].value2" class="inputbox"></el-input>天</div>
                         </div>
                      </div>-->
                    </div>

                    <div v-if="showbodong4"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="最大回撤比"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     class="width160"
                                     v-model="formData.field104[4].date"
                                     :options="option5"></el-cascader>
                      </div>
                      <selecterinput name="最大回撤比"
                                     @setvalue1="setvalue1"
                                     @setvalue2="setvalue2"
                                     indexid="1.5"
                                     @setrank="setrank"
                                     :rankvalue="formData.field104[4].rank"
                                     :valuef="formData.field104[4].value1"
                                     :valueb="formData.field104[4].value2"
                                     :buttonarray="selectvalue5"></selecterinput>

                      <!-- <div>
                       <div class="tiptablebox">&nbsp;区间&nbsp;
                         <div><el-input type="number" placeholder='e.g. 0.1' v-model="formData.field104[4].value1" class="inputbox"></el-input></div>
                        <div>&nbsp; ~ &nbsp;</div>
                         <div><el-input type="number" placeholder='e.g. 0.3'  v-model="formData.field104[4].value2" class="inputbox"></el-input></div>
                         </div>
                      </div>-->
                    </div>
                    <div v-if="showbodong5"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="在险价值"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     class="width160"
                                     v-model="formData.field104[5].date"
                                     :options="option6"></el-cascader>
                      </div>
                      <selecterinput name="在险价值"
                                     @setvalue1="setvalue1"
                                     @setvalue2="setvalue2"
                                     indexid="1.6"
                                     @setrank="setrank"
                                     :rankvalue="formData.field104[5].rank"
                                     :valuef="formData.field104[5].value1"
                                     :valueb="formData.field104[5].value2"
                                     :buttonarray="selectvalue6"></selecterinput>
                      <!-- <div>
                       <div class="tiptablebox">&nbsp;区间&nbsp;
                         <div><el-input type="number" v-model="formData.field104[5].value1" placeholder='e.g.-0.08' class="inputbox"></el-input></div>
                        <div>&nbsp; ~ &nbsp;</div>
                         <div><el-input type="number" v-model="formData.field104[5].value2" placeholder='e.g. 0.2' class="inputbox"></el-input></div>
                         </div>
                      </div>-->
                    </div>
                    <div v-if="showbodong6"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="期望损失"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     class="width160"
                                     v-model="formData.field104[6].date"
                                     :options="option7"></el-cascader>
                      </div>
                      <selecterinput name="期望损失"
                                     @setvalue1="setvalue1"
                                     @setvalue2="setvalue2"
                                     indexid="1.7"
                                     @setrank="setrank"
                                     :rankvalue="formData.field104[6].rank"
                                     :valuef="formData.field104[6].value1"
                                     :valueb="formData.field104[6].value2"
                                     :buttonarray="selectvalue7"></selecterinput>

                      <!-- <div>
                       <div class="tiptablebox">&nbsp;区间&nbsp;
                         <div><el-input type="number" v-model="formData.field104[6].value1" placeholder='e.g.-0.08' class="inputbox"></el-input></div>
                        <div>&nbsp; ~ &nbsp;</div>
                         <div><el-input type="number" v-model="formData.field104[6].value2" placeholder='e.g. 0.2' class="inputbox"></el-input></div>
                         </div>
                      </div>-->
                    </div>
                    <div v-if="showbodong7"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="下行风险"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     class="width160"
                                     v-model="formData.field104[7].date"
                                     :options="option8"></el-cascader>
                      </div>
                      <selecterinput name="下行风险"
                                     @setvalue1="setvalue1"
                                     @setvalue2="setvalue2"
                                     indexid="1.8"
                                     @setrank="setrank"
                                     :rankvalue="formData.field104[7].rank"
                                     :valuef="formData.field104[7].value1"
                                     :valueb="formData.field104[7].value2"
                                     :buttonarray="selectvalue8"></selecterinput>

                      <!-- <div>
                       <div class="tiptablebox">&nbsp;区间&nbsp;
                         <div><el-input type="number" v-model="formData.field104[7].value1" placeholder='e.g. 0.1' class="inputbox"></el-input></div>
                        <div>&nbsp; ~ &nbsp;</div>
                         <div><el-input type="number" v-model="formData.field104[7].value2" placeholder='e.g. 0.3' class="inputbox"></el-input></div>
                         </div>
                      </div>-->
                    </div>
                    <div v-if="showbodong8"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="波动率比"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     class="width160"
                                     v-model="formData.field104[8].date"
                                     :options="option9"></el-cascader>
                      </div>
                      <selecterinput name="波动率比"
                                     @setvalue1="setvalue1"
                                     @setvalue2="setvalue2"
                                     indexid="1.9"
                                     @setrank="setrank"
                                     :rankvalue="formData.field104[8].rank"
                                     :valuef="formData.field104[8].value1"
                                     :valueb="formData.field104[8].value2"
                                     :buttonarray="selectvalue9"></selecterinput>

                      <!-- <div>
                       <div class="tiptablebox">&nbsp;区间&nbsp;
                         <div><el-input type="number" v-model="formData.field104[8].value1" placeholder='e.g. 0.1' class="inputbox"></el-input></div>
                        <div>&nbsp; ~ &nbsp;</div>
                         <div><el-input type="number" v-model="formData.field104[8].value2" placeholder='e.g. 0.3' class="inputbox"></el-input></div>
                         </div>
                      </div>-->
                    </div>
                    <div v-if="showbodong9"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="痛苦指数"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     class="width160"
                                     v-model="formData.field104[9].date"
                                     :options="option10"></el-cascader>
                      </div>
                      <selecterinputboxsignal name="痛苦指数"
                                              @setvalue1="setvalue1"
                                              indexid="1.101"
                                              @setrank="setrank"
                                              :smallorbig="false"
                                              :rankvalue="formData.field104[9].rank"
                                              :valuef="formData.field104[9].value1"
                                              :buttonarray="selectvalue10"></selecterinputboxsignal>

                      <div>
                        <!-- <div class="tiptablebox">&nbsp;小于&nbsp;
                         <div><el-input type="number" v-model="formData.field104[9].value1" placeholder='e.g. 0.3' class="inputbox"></el-input></div>
                        </div>-->
                      </div>
                    </div>
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="⻛险收益特征"
                            prop="field105">
                <template slot="label">
                  风险收益特征
                  <el-tooltip class="item"
                              effect="dark"
                              content="请输入风险特征。例如年化收益率，输入0.1-0.6，表示其范围为10%-60%"
                              placement="right-start">
                    <svg width="14"
                         height="14"
                         viewBox="0 0 14 14"
                         fill="none">
                      <path fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
                            fill="black"
                            fill-opacity="0.45" />
                    </svg>
                  </el-tooltip>
                </template>
                <div style="display: flex">
                  <el-checkbox-group v-model="temp2"
                                     size="medium">
                    <el-checkbox v-for="(item, index) in field105Options"
                                 v-show="showmore2 ? index == 1 || index == 0 || index == 2 || index == 5 || index == 11 : true"
                                 :key="index"
                                 :label="item.value"
                                 :disabled="item.disabled">{{ item.label }}</el-checkbox>
                  </el-checkbox-group>
                  <div v-show="showmore2"
                       @click="showmore2 = !showmore2"
                       style="display: flex; align-items: center; flex-wrap: wrap">
                    <i style="font-size: 16px"
                       class="el-icon-caret-right"></i>
                  </div>
                  <div v-show="!showmore2"
                       @click="showmore2 = !showmore2"
                       style="display: flex; align-items: center; flex-wrap: wrap">
                    <i style="font-size: 16px"
                       class="el-icon-caret-left"></i>
                  </div>
                </div>
                <div>
                  <div style="display: flex; margin: 5px; background: #f1f1f5; max-width: 75vw; flex-wrap: wrap">
                    <div v-if="showfengxian"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="年化收益率"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     class="width160"
                                     v-model="formData.field105[0].date"
                                     :options="option11"></el-cascader>
                      </div>
                      <selecterinputboxsignal name="波动率比"
                                              @setvalue1="setvalue1"
                                              indexid="2.1"
                                              @setrank="setrank"
                                              :rankvalue="formData.field105[0].rank"
                                              :smallorbig="true"
                                              :valuef="formData.field105[0].value1"
                                              :buttonarray="selectvalue11"></selecterinputboxsignal>
                      <!-- <div style="display:flex">
                      <div class="tiptablebox"> &nbsp;大于&nbsp;
                         <div><el-input type="number" placeholder='e.g. 0.2' v-model="formData.field105[0].value1" class="inputbox"></el-input></div>
                         </div>
                      </div>-->
                    </div>
                    <div v-if="showfengxian1"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="累计收益率"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     class="width160"
                                     v-model="formData.field105[1].date"
                                     :options="option12"></el-cascader>
                      </div>
                      <selecterinputboxsignal name="波动率比"
                                              @setvalue1="setvalue1"
                                              indexid="2.2"
                                              @setrank="setrank"
                                              :rankvalue="formData.field105[1].rank"
                                              :smallorbig="true"
                                              :valuef="formData.field105[1].value1"
                                              :buttonarray="selectvalue12"></selecterinputboxsignal>
                      <!-- <div style="display:flex">
                      <div class="tiptablebox"> &nbsp;大于&nbsp;
                         <div><el-input type="number" placeholder='e.g. 0.5' v-model="formData.field105[1].value1" class="inputbox"></el-input></div>
                         </div>
                      </div>-->
                    </div>
                    <div v-if="showfengxian2"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="夏普率（rf=0）"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     class="width160"
                                     v-model="formData.field105[2].date"
                                     :options="option13"></el-cascader>
                      </div>
                      <selecterinputboxsignal name="波动率比"
                                              @setvalue1="setvalue1"
                                              indexid="2.3"
                                              @setrank="setrank"
                                              :rankvalue="formData.field105[2].rank"
                                              :smallorbig="true"
                                              :valuef="formData.field105[2].value1"
                                              :buttonarray="selectvalue13"></selecterinputboxsignal>
                      <!-- <div style="display:flex">
                      <div class="tiptablebox"> &nbsp;大于&nbsp;
                         <div><el-input type="number" placeholder='e.g. 1.2' v-model="formData.field105[2].value1" class="inputbox"></el-input></div>
                         </div>
                      </div>-->
                    </div>
                    <div v-if="showfengxian3"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="夏普率（rf=0.04）"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     class="width160"
                                     v-model="formData.field105[3].date"
                                     :options="option14"></el-cascader>
                      </div>
                      <selecterinputboxsignal name="波动率比"
                                              @setvalue1="setvalue1"
                                              indexid="2.4"
                                              @setrank="setrank"
                                              :rankvalue="formData.field105[3].rank"
                                              :smallorbig="true"
                                              :valuef="formData.field105[3].value1"
                                              :buttonarray="selectvalue14"></selecterinputboxsignal>
                      <!-- <div style="display:flex">
                      <div class="tiptablebox"> &nbsp;大于&nbsp;
                         <div><el-input type="number" placeholder='e.g. 1.2' v-model="formData.field105[3].value1" class="inputbox"></el-input></div>
                         </div>
                      </div>-->
                    </div>
                    <div v-if="showfengxian4"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="夏普率（动态rf）"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     class="width160"
                                     v-model="formData.field105[4].date"
                                     :options="option15"></el-cascader>
                      </div>
                      <selecterinputboxsignal name="波动率比"
                                              @setvalue1="setvalue1"
                                              indexid="2.5"
                                              @setrank="setrank"
                                              :smallorbig="true"
                                              :rankvalue="formData.field105[4].rank"
                                              :valuef="formData.field105[4].value1"
                                              :buttonarray="selectvalue15"></selecterinputboxsignal>
                      <!-- <div style="display:flex">
                      <div class="tiptablebox"> &nbsp;大于&nbsp;
                         <div><el-input type="number" placeholder='e.g. 1.2' v-model="formData.field105[4].value1" class="inputbox"></el-input></div>
                         </div>
                      </div>-->
                    </div>
                    <div v-if="showfengxian5"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="卡码率"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     class="width160"
                                     v-model="formData.field105[5].date"
                                     :options="option16"></el-cascader>
                      </div>
                      <selecterinputboxsignal name="波动率比"
                                              @setvalue1="setvalue1"
                                              indexid="2.6"
                                              @setrank="setrank"
                                              :rankvalue="formData.field105[5].rank"
                                              :smallorbig="true"
                                              :valuef="formData.field105[5].value1"
                                              :buttonarray="selectvalue16"></selecterinputboxsignal>
                      <!-- <div style="display:flex">
                      <div class="tiptablebox"> &nbsp;大于&nbsp;
                         <div><el-input type="number" placeholder='e.g. 30' v-model="formData.field105[5].value1" class="inputbox"></el-input></div>
                         </div>
                      </div>-->
                    </div>
                    <div v-if="showfengxian6"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="索提诺系数（rf=0）"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     class="width160"
                                     v-model="formData.field105[6].date"
                                     :options="option17"></el-cascader>
                      </div>
                      <selecterinputboxsignal name="波动率比"
                                              @setvalue1="setvalue1"
                                              indexid="2.7"
                                              @setrank="setrank"
                                              :rankvalue="formData.field105[6].rank"
                                              :smallorbig="true"
                                              :valuef="formData.field105[6].value1"
                                              :buttonarray="selectvalue17"></selecterinputboxsignal>
                      <!-- <div style="display:flex">
                      <div class="tiptablebox"> &nbsp;大于&nbsp;
                         <div><el-input type="number" placeholder='e.g. 1' v-model="formData.field105[6].value1" class="inputbox"></el-input></div>
                         </div>
                      </div>-->
                    </div>
                    <div v-if="showfengxian7"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="索提诺系数（rf=0.04）"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     class="width160"
                                     v-model="formData.field105[7].date"
                                     :options="option18"></el-cascader>
                      </div>
                      <selecterinputboxsignal name="波动率比"
                                              @setvalue1="setvalue1"
                                              indexid="2.8"
                                              :smallorbig="true"
                                              @setrank="setrank"
                                              :rankvalue="formData.field105[7].rank"
                                              :valuef="formData.field105[7].value1"
                                              :buttonarray="selectvalue18"></selecterinputboxsignal>
                      <!-- <div style="display:flex">
                      <div class="tiptablebox"> &nbsp;大于&nbsp;
                         <div><el-input type="number" placeholder='e.g. 1' v-model="formData.field105[7].value1" class="inputbox"></el-input></div>
                         </div>
                      </div>-->
                    </div>
                    <div v-if="showfengxian8"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="索提诺系数（动态rf）"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     class="width160"
                                     v-model="formData.field105[8].date"
                                     :options="option19"></el-cascader>
                      </div>
                      <selecterinputboxsignal name="波动率比"
                                              @setvalue1="setvalue1"
                                              indexid="2.9"
                                              :smallorbig="true"
                                              @setrank="setrank"
                                              :rankvalue="formData.field105[8].rank"
                                              :valuef="formData.field105[8].value1"
                                              :buttonarray="selectvalue19"></selecterinputboxsignal>

                      <!-- <div style="display:flex">
                      <div class="tiptablebox"> &nbsp;大于&nbsp;
                         <div><el-input type="number" placeholder='e.g. 1' v-model="formData.field105[8].value1" class="inputbox"></el-input></div>
                         </div>
                      </div>-->
                    </div>
                    <div v-if="showfengxian9"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="稳定系数"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     class="width160"
                                     v-model="formData.field105[9].date"
                                     :options="option20"></el-cascader>
                      </div>
                      <selecterinputboxsignal name="波动率比"
                                              @setvalue1="setvalue1"
                                              indexid="2.101"
                                              :smallorbig="true"
                                              @setrank="setrank"
                                              :rankvalue="formData.field105[9].rank"
                                              :valuef="formData.field105[9].value1"
                                              :buttonarray="selectvalue20"></selecterinputboxsignal>

                      <!-- <div style="display:flex">
                      <div class="tiptablebox"> &nbsp;大于&nbsp;
                         <div><el-input type="number" placeholder='e.g. 0.2' v-model="formData.field105[9].value1" class="inputbox"></el-input></div>
                         </div>
                      </div>-->
                    </div>
                    <div v-if="showfengxian10"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="凯利系数"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     class="width160"
                                     v-model="formData.field105[10].date"
                                     :options="option21"></el-cascader>
                      </div>
                      <selecterinputboxsignal name="波动率比"
                                              @setvalue1="setvalue1"
                                              indexid="2.11"
                                              :smallorbig="true"
                                              @setrank="setrank"
                                              :rankvalue="formData.field105[10].rank"
                                              :valuef="formData.field105[10].value1"
                                              :buttonarray="selectvalue21"></selecterinputboxsignal>

                      <!-- <div style="display:flex">
                      <div class="tiptablebox"> &nbsp;大于&nbsp;
                         <div><el-input type="number" placeholder='e.g. 0.2' v-model="formData.field105[10].value1" class="inputbox"></el-input></div>
                         </div>
                      </div>-->
                    </div>
                    <div v-if="showfengxian11"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="信息⽐率"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     class="width160"
                                     v-model="formData.field105[11].date"
                                     :options="option22"></el-cascader>
                      </div>
                      <selecterinputboxsignal name="波动率比"
                                              @setvalue1="setvalue1"
                                              indexid="2.12"
                                              :smallorbig="true"
                                              @setrank="setrank"
                                              :rankvalue="formData.field105[11].rank"
                                              :valuef="formData.field105[11].value1"
                                              :buttonarray="selectvalue22"></selecterinputboxsignal>

                      <!-- <div style="display:flex">
                      <div class="tiptablebox"> &nbsp;大于&nbsp;
                         <div><el-input type="number" placeholder='e.g. 0.2' v-model="formData.field105[11].value1" class="inputbox"></el-input></div>
                         </div>
                      </div>-->
                    </div>
                    <div v-if="showfengxian12"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="上攻潜⼒（周）"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     class="width160"
                                     v-model="formData.field105[12].date"
                                     :options="option23"></el-cascader>
                      </div>
                      <selecterinputboxsignal name="波动率比"
                                              @setvalue1="setvalue1"
                                              indexid="2.13"
                                              :smallorbig="true"
                                              @setrank="setrank"
                                              :rankvalue="formData.field105[12].rank"
                                              :valuef="formData.field105[12].value1"
                                              :buttonarray="selectvalue23"></selecterinputboxsignal>

                      <!-- <div style="display:flex">
                      <div class="tiptablebox"> &nbsp;大于&nbsp;
                         <div><el-input type="number" placeholder='e.g. 20' v-model="formData.field105[12].value1" class="inputbox"></el-input></div>
                         </div>
                      </div>-->
                    </div>
                    <div v-if="showfengxian13"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="⽉胜率"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     class="width160"
                                     v-model="formData.field105[13].date"
                                     :options="option24"></el-cascader>
                      </div>
                      <selecterinputboxsignal name="波动率比"
                                              @setvalue1="setvalue1"
                                              indexid="2.14"
                                              :smallorbig="true"
                                              @setrank="setrank"
                                              :rankvalue="formData.field105[13].rank"
                                              :valuef="formData.field105[13].value1"
                                              :buttonarray="selectvalue24"></selecterinputboxsignal>

                      <!-- <div style="display:flex">
                      <div class="tiptablebox"> &nbsp;大于&nbsp;
                         <div><el-input type="number" placeholder='e.g. 0.6' v-model="formData.field105[13].value1" class="inputbox"></el-input></div>
                         </div>
                      </div>-->
                    </div>
                    <div v-if="showfengxian14"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="詹森系数"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     class="width160"
                                     v-model="formData.field105[14].date"
                                     :options="option25"></el-cascader>
                      </div>
                      <selecterinputboxsignal name="波动率比"
                                              @setvalue1="setvalue1"
                                              indexid="2.15"
                                              :smallorbig="true"
                                              @setrank="setrank"
                                              :rankvalue="formData.field105[14].rank"
                                              :valuef="formData.field105[14].value1"
                                              :buttonarray="selectvalue25"></selecterinputboxsignal>

                      <!-- <div style="display:flex">
                      <div class="tiptablebox"> &nbsp;大于&nbsp;
                         <div><el-input type="number" placeholder='e.g. 0.2' v-model="formData.field105[14].value1" class="inputbox"></el-input></div>
                         </div>
                      </div>-->
                    </div>
                    <div v-if="showfengxian15"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  placeholder="e.g. 0.2"
                                  disabled
                                  value="特诺系数"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     class="width160"
                                     v-model="formData.field105[15].date"
                                     :options="option26"></el-cascader>
                      </div>
                      <selecterinputboxsignal name="波动率比"
                                              @setvalue1="setvalue1"
                                              indexid="2.16"
                                              :smallorbig="true"
                                              @setrank="setrank"
                                              :rankvalue="formData.field105[15].rank"
                                              :valuef="formData.field105[15].value1"
                                              :buttonarray="selectvalue26"></selecterinputboxsignal>

                      <!-- <div style="display:flex">
                      <div class="tiptablebox"> &nbsp;大于&nbsp;
                         <div><el-input type="number" placeholder='e.g. 0.2' v-model="formData.field105[15].value1" class="inputbox"></el-input></div>
                         </div>
                      </div>-->
                    </div>
                    <div v-if="showfengxian16"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="上⾏捕获"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     class="width160"
                                     v-model="formData.field105[16].date"
                                     :options="option27"></el-cascader>
                      </div>
                      <selecterinputboxsignal name="波动率比"
                                              @setvalue1="setvalue1"
                                              indexid="2.17"
                                              :smallorbig="true"
                                              @setrank="setrank"
                                              :rankvalue="formData.field105[16].rank"
                                              :valuef="formData.field105[16].value1"
                                              :buttonarray="selectvalue27"></selecterinputboxsignal>

                      <!-- <div style="display:flex">
                      <div class="tiptablebox"> &nbsp;大于&nbsp;
                         <div><el-input type="number" placeholder='e.g. 0.5' v-model="formData.field105[16].value1" class="inputbox"></el-input></div>
                         </div>
                      </div>-->
                    </div>
                    <div v-if="showfengxian17"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="下⾏捕获"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     class="width160"
                                     v-model="formData.field105[17].date"
                                     :options="option28"></el-cascader>
                      </div>
                      <selecterinputboxsignal name="波动率比"
                                              @setvalue1="setvalue1"
                                              indexid="2.18"
                                              :smallorbig="true"
                                              @setrank="setrank"
                                              :rankvalue="formData.field105[17].rank"
                                              :valuef="formData.field105[17].value1"
                                              :buttonarray="selectvalue28"></selecterinputboxsignal>

                      <!-- <div style="display:flex">
                      <div class="tiptablebox"> &nbsp;大于&nbsp;
                         <div><el-input type="number" placeholder='e.g. -0.5' v-model="formData.field105[17].value1" class="inputbox"></el-input></div>
                         </div>
                      </div>-->
                    </div>
                    <div v-if="showfengxian18"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="择时gamma"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     style="width: 160px"
                                     v-model="formData.field105[18].date"
                                     :options="option29"></el-cascader>
                      </div>
                      <selecterinputboxsignal name="波动率比"
                                              @setvalue1="setvalue1"
                                              indexid="2.19"
                                              :smallorbig="true"
                                              @setrank="setrank"
                                              :rankvalue="formData.field105[18].rank"
                                              :valuef="formData.field105[18].value1"
                                              :buttonarray="selectvalue29"></selecterinputboxsignal>

                      <!-- <div style="display:flex">
                      <div class="tiptablebox"> &nbsp;大于&nbsp;
                         <div><el-input type="number" placeholder='e.g. -10' v-model="formData.field105[18].value1" class="inputbox"></el-input></div>
                         </div>
                      </div>-->
                    </div>
                    <div v-if="showfengxian19"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="M2"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     class="width160"
                                     v-model="formData.field105[19].date"
                                     :options="option30"></el-cascader>
                      </div>
                      <selecterinputboxsignal name="波动率比"
                                              @setvalue1="setvalue1"
                                              indexid="2.201"
                                              @setrank="setrank"
                                              :smallorbig="true"
                                              :rankvalue="formData.field105[19].rank"
                                              :valuef="formData.field105[19].value1"
                                              :buttonarray="selectvalue30"></selecterinputboxsignal>

                      <!-- <div style="display:flex">
                      <div class="tiptablebox"> &nbsp;大于&nbsp;
                         <div><el-input type="number" placeholder='e.g. 0.2' v-model="formData.field105[19].value1" class="inputbox"></el-input></div>
                         </div>
                      </div>-->
                    </div>
                  </div>
                </div>
              </el-form-item>
              <div>
                <el-divider content-position="left">市场判断</el-divider>
              </div>
              <el-form-item label="alpha筛选器-周期判断"
                            prop="field106">
                <template slot="label">
                  周期判断
                  <el-tooltip class="item"
                              effect="dark"
                              content="请在输入框中输入数字单位为%，请确保选择的元素加起来为100"
                              placement="right-start">
                    <svg width="14"
                         height="14"
                         viewBox="0 0 14 14"
                         fill="none">
                      <path fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
                            fill="black"
                            fill-opacity="0.45" />
                    </svg>
                  </el-tooltip>
                </template>
                <el-checkbox-group v-model="temp3"
                                   size="medium">
                  <el-checkbox v-for="(item, index) in field106Options"
                               :key="index"
                               :label="item.value"
                               :disabled="item.disabled">
                    {{
                    item.label
                    }}
                  </el-checkbox>
                </el-checkbox-group>
                <div>
                  <div style="display: flex; margin: 5px; background: #f1f1f5; max-width: 75vw; flex-wrap: wrap">
                    <div v-if="showhg"
                         class="choosebox">
                      <el-checkbox-group v-model="hgchoose">
                        <el-checkbox style="margin-right: 0"
                                     label="复苏"></el-checkbox>
                        <el-input @mousewheel.native.prevent
                                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
                                  :disabled="hgchoose.indexOf('复苏') < 0"
                                  style="margin-right: 20px"
                                  type="number"
                                  v-model="formData.field106.value1"
                                  class="inputbox"></el-input>
                        <el-checkbox style="margin-right: 0"
                                     label="繁荣"></el-checkbox>
                        <el-input @mousewheel.native.prevent
                                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
                                  :disabled="hgchoose.indexOf('繁荣') < 0"
                                  style="margin-right: 20px"
                                  type="number"
                                  v-model="formData.field106.value2"
                                  class="inputbox"></el-input>
                        <el-checkbox style="margin-right: 0"
                                     label="滞涨"></el-checkbox>
                        <el-input @mousewheel.native.prevent
                                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
                                  :disabled="hgchoose.indexOf('滞涨') < 0"
                                  style="margin-right: 20px"
                                  type="number"
                                  v-model="formData.field106.value3"
                                  class="inputbox"></el-input>
                        <el-checkbox style="margin-right: 0"
                                     label="衰退"></el-checkbox>
                        <el-input @mousewheel.native.prevent
                                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
                                  :disabled="hgchoose.indexOf('衰退') < 0"
                                  style="margin-right: 20px"
                                  type="number"
                                  v-model="formData.field106.value4"
                                  class="inputbox"></el-input>
                        <!-- <el-checkbox  style="margin-right:0" label="衰退初期" ></el-checkbox> -->
                        <!-- <el-input :disabled="hgchoose.indexOf('衰退初期')<0" style="margin-right:20px" type="number" v-model="formData.field106.value5" class="inputbox"></el-input>
                      <el-checkbox  style="margin-right:0" label="衰退后期" ></el-checkbox>
                        <el-input :disabled="hgchoose.indexOf('衰退后期')<0" style="margin-right:20px" type="number" v-model="formData.field106.value6" class="inputbox"></el-input>-->
                      </el-checkbox-group>
                    </div>

                    <div v-if="showkc"
                         class="choosebox">
                      <el-checkbox-group v-model="kcchoose">
                        <el-checkbox style="margin-right: 0"
                                     label="被动去库存"></el-checkbox>
                        <el-input @mousewheel.native.prevent
                                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
                                  :disabled="kcchoose.indexOf('被动去库存') < 0"
                                  style="margin-right: 20px"
                                  type="number"
                                  v-model="formData.field1061.value1"
                                  class="inputbox"></el-input>
                        <el-checkbox style="margin-right: 0"
                                     label="主动补库存"></el-checkbox>
                        <el-input @mousewheel.native.prevent
                                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
                                  :disabled="kcchoose.indexOf('主动补库存') < 0"
                                  style="margin-right: 20px"
                                  type="number"
                                  v-model="formData.field1061.value2"
                                  class="inputbox"></el-input>
                        <el-checkbox style="margin-right: 0"
                                     label="被动补库存"></el-checkbox>
                        <el-input @mousewheel.native.prevent
                                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
                                  :disabled="kcchoose.indexOf('被动补库存') < 0"
                                  style="margin-right: 20px"
                                  type="number"
                                  v-model="formData.field1061.value3"
                                  class="inputbox"></el-input>
                        <el-checkbox style="margin-right: 0"
                                     label="主动去库存"></el-checkbox>
                        <el-input @mousewheel.native.prevent
                                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
                                  :disabled="kcchoose.indexOf('主动去库存') < 0"
                                  style="margin-right: 20px"
                                  type="number"
                                  v-model="formData.field1061.value4"
                                  class="inputbox"></el-input>
                      </el-checkbox-group>
                    </div>

                    <div v-if="showej"
                         class="choosebox">
                      <el-checkbox-group v-model="ejchoose">
                        <el-checkbox style="margin-right: 0"
                                     label="上升"></el-checkbox>
                        <el-input @mousewheel.native.prevent
                                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
                                  :disabled="ejchoose.indexOf('上升') < 0"
                                  style="margin-right: 20px"
                                  type="number"
                                  v-model="formData.field1062.value1"
                                  class="inputbox"></el-input>
                        <el-checkbox style="margin-right: 0"
                                     label="大牛市"></el-checkbox>
                        <el-input @mousewheel.native.prevent
                                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
                                  :disabled="ejchoose.indexOf('大牛市') < 0"
                                  style="margin-right: 20px"
                                  type="number"
                                  v-model="formData.field1062.value2"
                                  class="inputbox"></el-input>
                        <el-checkbox style="margin-right: 0"
                                     label="震荡"></el-checkbox>
                        <el-input @mousewheel.native.prevent
                                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
                                  :disabled="ejchoose.indexOf('震荡') < 0"
                                  style="margin-right: 20px"
                                  type="number"
                                  v-model="formData.field1062.value3"
                                  class="inputbox"></el-input>
                        <el-checkbox style="margin-right: 0"
                                     label="熊市"></el-checkbox>
                        <el-input @mousewheel.native.prevent
                                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
                                  :disabled="ejchoose.indexOf('熊市') < 0"
                                  style="margin-right: 20px"
                                  type="number"
                                  v-model="formData.field1062.value4"
                                  class="inputbox"></el-input>
                        <el-checkbox style="margin-right: 0"
                                     label="反弹"></el-checkbox>
                        <el-input @mousewheel.native.prevent
                                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
                                  :disabled="ejchoose.indexOf('反弹') < 0"
                                  style="margin-right: 20px"
                                  type="number"
                                  v-model="formData.field1062.value5"
                                  class="inputbox"></el-input>
                        <el-checkbox style="margin-right: 0"
                                     label="大跌"></el-checkbox>
                        <el-input @mousewheel.native.prevent
                                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
                                  :disabled="ejchoose.indexOf('大跌') < 0"
                                  style="margin-right: 20px"
                                  type="number"
                                  v-model="formData.field1062.value6"
                                  class="inputbox"></el-input>
                      </el-checkbox-group>
                    </div>
                  </div>
                </div>
              </el-form-item>

              <el-form-item label="市场偏好"
                            prop="field106">
                <el-radio-group v-model="formData.field110"
                                size="medium">
                  <el-radio v-for="(item, index) in field110Options"
                            :key="index"
                            :label="item.value"
                            @click.native="cancelchoose"
                            :disabled="item.disabled">{{ item.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="alpha筛选器-风格判断"
                            prop="field107">
                <template slot="label">
                  风格判断
                  <el-tooltip class="item"
                              effect="dark"
                              content="请在输入框中输入数字单位为%，请确保选择的元素加起来为100"
                              placement="right-start">
                    <svg width="14"
                         height="14"
                         viewBox="0 0 14 14"
                         fill="none">
                      <path fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
                            fill="black"
                            fill-opacity="0.45" />
                    </svg>
                  </el-tooltip>
                </template>
                <el-checkbox-group v-model="temp4"
                                   size="medium">
                  <el-checkbox v-for="(item, index) in field107Options"
                               :key="index"
                               :label="item.value"
                               :disabled="item.disabled">
                    {{
                    item.label
                    }}
                  </el-checkbox>
                </el-checkbox-group>
                <div>
                  <div style="display: flex; margin: 5px; background: #f1f1f5; max-width: 75vw; flex-wrap: wrap">
                    <div v-if="czshow"
                         class="choosebox">
                      <el-checkbox-group v-model="czchoose">
                        <el-checkbox style="margin-right: 0"
                                     label="成长"></el-checkbox>
                        <el-input @mousewheel.native.prevent
                                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
                                  :disabled="czchoose.indexOf('成长') < 0"
                                  style="margin-right: 20px"
                                  type="number"
                                  v-model="formData.field107.value1"
                                  class="inputbox"></el-input>
                        <el-checkbox style="margin-right: 0"
                                     label="价值"></el-checkbox>
                        <el-input @mousewheel.native.prevent
                                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
                                  :disabled="czchoose.indexOf('价值') < 0"
                                  style="margin-right: 20px"
                                  type="number"
                                  v-model="formData.field107.value2"
                                  class="inputbox"></el-input>
                      </el-checkbox-group>
                    </div>

                    <div v-if="dpshow"
                         class="choosebox">
                      <el-checkbox-group v-model="dpchoose">
                        <el-checkbox style="margin-right: 0"
                                     label="大盘"></el-checkbox>
                        <el-input @mousewheel.native.prevent
                                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
                                  :disabled="dpchoose.indexOf('大盘') < 0"
                                  style="margin-right: 20px"
                                  type="number"
                                  v-model="formData.field1071.value1"
                                  class="inputbox"></el-input>

                        <el-checkbox v-show="false"
                                     style="margin-right: 0"
                                     label="中盘"></el-checkbox>
                        <el-input v-show="false"
                                  @mousewheel.native.prevent
                                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
                                  :disabled="dpchoose.indexOf('中盘') < 0"
                                  style="margin-right: 20px"
                                  type="number"
                                  v-model="formData.field1071.value2"
                                  class="inputbox"></el-input>
                        <el-checkbox style="margin-right: 0"
                                     label="小盘"></el-checkbox>
                        <el-input @mousewheel.native.prevent
                                  onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
                                  :disabled="dpchoose.indexOf('小盘') < 0"
                                  style="margin-right: 20px"
                                  type="number"
                                  v-model="formData.field1071.value3"
                                  class="inputbox"></el-input>
                      </el-checkbox-group>
                    </div>
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="alpha筛选器-行业判断"
                            prop="field108">
                <template slot="label">
                  行业判断
                  <el-tooltip class="item"
                              effect="dark"
                              content="请在输入框中输入数字单位为%，请确保选择的元素加起来为100"
                              placement="right-start">
                    <svg width="14"
                         height="14"
                         viewBox="0 0 14 14"
                         fill="none">
                      <path fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
                            fill="black"
                            fill-opacity="0.45" />
                    </svg>
                  </el-tooltip>
                </template>
                <el-checkbox-group v-model="temp5"
                                   size="medium">
                  <el-checkbox v-for="(item, index) in type == 'activeequity' ? field108Options : field108Options2"
                               :key="index"
                               :label="item.value"
                               :disabled="item.disabled">{{ item.label }}</el-checkbox>
                </el-checkbox-group>
                <div>
                  <div style="display: flex; margin: 5px; background: #f1f1f5; max-width: 75vw; flex-wrap: wrap">
                    <div v-if="swshow"
                         class="choosebox">
                      <div style="display: flex; flex-wrap: wrap">
                        <div v-for="(item, index) in insertindexswone"
                             :key="index"
                             style="display: flex; flex-wrap: wrap">
                          <el-select v-model="swonevalue[item - 1]"
                                     filterable
                                     placeholder="请选择申万一级行业"
                                     @change="forbidswone()">
                            <el-option v-for="item in swoneoptions"
                                       :key="item.value"
                                       :label="item.label"
                                       :value="item.value"
                                       :disabled="item.forbidstate"></el-option>
                          </el-select>
                          <div style="width: 5px"></div>
                          <el-input class="inputbox"
                                    @mousewheel.native.prevent
                                    onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
                                    type="number"
                                    v-model="quanzhongswone[item - 1]"></el-input>
                          <div style="width: 5px"></div>
                          <div class="marginright20">
                            <i @click="deldomswone(item - 1)"
                               class="el-icon-remove icon_color"></i>
                          </div>
                        </div>
                      </div>
                      <!-- <el-checkbox-group v-model="swchoose">
                      <el-checkbox  style="margin-right:0" label="非银金融"></el-checkbox>
                      <el-input  @mousewheel.native.prevent onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"  :disabled="swchoose.indexOf('非银金融')<0" style="margin-right:20px" type="number" v-model="formData.field108.value1" class="inputbox"></el-input>
                      <el-checkbox   style="margin-right:0" label="农林牧渔"></el-checkbox>
                      <el-input  @mousewheel.native.prevent onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"  :disabled="swchoose.indexOf('农林牧渔')<0" style="margin-right:20px" type="number" v-model="formData.field108.value2" class="inputbox"></el-input>
                      <el-checkbox  style="margin-right:0" label="国防军工"></el-checkbox>
                      <el-input  @mousewheel.native.prevent onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"  :disabled="swchoose.indexOf('国防军工')<0" style="margin-right:20px" type="number" v-model="formData.field108.value3" class="inputbox"></el-input>
                      <el-checkbox   style="margin-right:0" label="采掘"></el-checkbox>
                      <el-input  @mousewheel.native.prevent onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"  :disabled="swchoose.indexOf('采掘')<0" style="margin-right:20px" type="number" v-model="formData.field108.value4" class="inputbox"></el-input>
                      <el-checkbox  style="margin-right:0" label="传媒"></el-checkbox>
                      <el-input  @mousewheel.native.prevent onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"  :disabled="swchoose.indexOf('传媒')<0" style="margin-right:20px" type="number" v-model="formData.field108.value5" class="inputbox"></el-input>
                      <el-checkbox   style="margin-right:0" label="电子"></el-checkbox>
                      <el-input  @mousewheel.native.prevent onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"  :disabled="swchoose.indexOf('电子')<0" style="margin-right:20px" type="number" v-model="formData.field108.value6" class="inputbox"></el-input>
                      <el-checkbox  style="margin-right:0" label="休闲服务"></el-checkbox>
                      <el-input  @mousewheel.native.prevent onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"  :disabled="swchoose.indexOf('休闲服务')<0" style="margin-right:20px" type="number" v-model="formData.field108.value7" class="inputbox"></el-input>
                      <br>
                      <el-checkbox   style="margin-right:0" label="房地产"></el-checkbox>
                      <el-input  @mousewheel.native.prevent onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"  :disabled="swchoose.indexOf('房地产')<0" style="margin-right:20px" type="number" v-model="formData.field108.value8" class="inputbox"></el-input>
                      <el-checkbox  style="margin-right:0" label="电子设备"></el-checkbox>
                      <el-input  @mousewheel.native.prevent onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"  :disabled="swchoose.indexOf('电子设备')<0" style="margin-right:20px" type="number" v-model="formData.field108.value9" class="inputbox"></el-input>
                      <el-checkbox   style="margin-right:0" label="钢铁"></el-checkbox>
                      <el-input  @mousewheel.native.prevent onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"  :disabled="swchoose.indexOf('钢铁')<0" style="margin-right:20px" type="number" v-model="formData.field108.value10" class="inputbox"></el-input>
                      <el-checkbox  style="margin-right:0" label="通信"></el-checkbox>
                      <el-input  @mousewheel.native.prevent onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"  :disabled="swchoose.indexOf('通信')<0" style="margin-right:20px" type="number" v-model="formData.field108.value11" class="inputbox"></el-input>
                      <el-checkbox   style="margin-right:0" label="建筑材料"></el-checkbox>
                      <el-input  @mousewheel.native.prevent onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"  :disabled="swchoose.indexOf('建筑材料')<0" style="margin-right:20px" type="number" v-model="formData.field108.value12" class="inputbox"></el-input>
                      <el-checkbox  style="margin-right:0" label="综合"></el-checkbox>
                      <el-input  @mousewheel.native.prevent onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"  :disabled="swchoose.indexOf('综合')<0" style="margin-right:20px" type="number" v-model="formData.field108.value13" class="inputbox"></el-input>
                      <el-checkbox   style="margin-right:0" label="公用事业"></el-checkbox>
                      <el-input  @mousewheel.native.prevent onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"  :disabled="swchoose.indexOf('公用事业')<0" style="margin-right:20px" type="number" v-model="formData.field108.value14" class="inputbox"></el-input>
                      <br>
                      <el-checkbox  style="margin-right:0" label="机械设备"></el-checkbox>
                      <el-input  @mousewheel.native.prevent onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"  :disabled="swchoose.indexOf('机械设备')<0" style="margin-right:20px" type="number" v-model="formData.field108.value15" class="inputbox"></el-input>
                      <el-checkbox   style="margin-right:0" label="商业贸易"></el-checkbox>
                      <el-input  @mousewheel.native.prevent onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"  :disabled="swchoose.indexOf('商业贸易')<0" style="margin-right:20px" type="number" v-model="formData.field108.value16" class="inputbox"></el-input>
                      <el-checkbox  style="margin-right:0" label="纺织服装"></el-checkbox>
                      <el-input  @mousewheel.native.prevent onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"  :disabled="swchoose.indexOf('纺织服装')<0" style="margin-right:20px" type="number" v-model="formData.field108.value17" class="inputbox"></el-input>
                      <el-checkbox   style="margin-right:0" label="化工"></el-checkbox>
                      <el-input  @mousewheel.native.prevent onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"  :disabled="swchoose.indexOf('化工')<0" style="margin-right:20px" type="number" v-model="formData.field108.value18" class="inputbox"></el-input>
                      <el-checkbox  style="margin-right:0" label="建筑装饰"></el-checkbox>
                      <el-input  @mousewheel.native.prevent onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"  :disabled="swchoose.indexOf('建筑装饰')<0" style="margin-right:20px" type="number" v-model="formData.field108.value19" class="inputbox"></el-input>
                      <el-checkbox   style="margin-right:0" label="银行"></el-checkbox>
                      <el-input  @mousewheel.native.prevent onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"  :disabled="swchoose.indexOf('银行')<0" style="margin-right:20px" type="number" v-model="formData.field108.value20" class="inputbox"></el-input>
                      <el-checkbox  style="margin-right:0" label="食品饮料"></el-checkbox>
                      <el-input  @mousewheel.native.prevent onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"  :disabled="swchoose.indexOf('食品饮料')<0" style="margin-right:20px" type="number" v-model="formData.field108.value21" class="inputbox"></el-input>
                      <br>
                      <el-checkbox   style="margin-right:0" label="交通运输"></el-checkbox>
                      <el-input  @mousewheel.native.prevent onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"  :disabled="swchoose.indexOf('交通运输')<0" style="margin-right:20px" type="number" v-model="formData.field108.value22" class="inputbox"></el-input>
                      <el-checkbox  style="margin-right:0" label="汽车"></el-checkbox>
                      <el-input  @mousewheel.native.prevent onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"  :disabled="swchoose.indexOf('汽车')<0" style="margin-right:20px" type="number" v-model="formData.field108.value23" class="inputbox"></el-input>
                      <el-checkbox   style="margin-right:0" label="轻工制造"></el-checkbox>
                      <el-input  @mousewheel.native.prevent onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"  :disabled="swchoose.indexOf('轻工制造')<0" style="margin-right:20px" type="number" v-model="formData.field108.value24" class="inputbox"></el-input>
                      <el-checkbox  style="margin-right:0" label="医药生物"></el-checkbox>
                      <el-input  @mousewheel.native.prevent onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"  :disabled="swchoose.indexOf('医药生物')<0" style="margin-right:20px" type="number" v-model="formData.field108.value25" class="inputbox"></el-input>
                      <el-checkbox   style="margin-right:0" label="有色金属"></el-checkbox>
                      <el-input  @mousewheel.native.prevent onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"  :disabled="swchoose.indexOf('有色金属')<0" style="margin-right:20px" type="number" v-model="formData.field108.value26" class="inputbox"></el-input>
                      <el-checkbox  style="margin-right:0" label="计算机"></el-checkbox>
                      <el-input  @mousewheel.native.prevent onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"  :disabled="swchoose.indexOf('计算机')<0" style="margin-right:20px" type="number" v-model="formData.field108.value27" class="inputbox"></el-input>
                      <el-checkbox   style="margin-right:0" label="家用电器"></el-checkbox>
                      <el-input  @mousewheel.native.prevent onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"  :disabled="swchoose.indexOf('家用电器')<0" style="margin-right:20px" type="number" v-model="formData.field108.value28" class="inputbox"></el-input>
                      </el-checkbox-group>-->
                    </div>
                  </div>
                  <div style="display: flex; margin: 5px; background: #f1f1f5; max-width: 75vw; flex-wrap: wrap">
                    <div v-if="swshow2"
                         class="choosebox">
                      <div style="display: flex; flex-wrap: wrap">
                        <div v-for="(item, index) in insertindexswone2"
                             :key="index"
                             style="display: flex; flex-wrap: wrap">
                          <el-select v-model="swonevalue2[item - 1]"
                                     filterable
                                     placeholder="请选择申万二级行业"
                                     @change="forbidswone2()">
                            <el-option v-for="item in swoneoptions2"
                                       :key="item.value"
                                       :label="item.label"
                                       :value="item.value"
                                       :disabled="item.forbidstate"></el-option>
                          </el-select>
                          <div style="width: 5px"></div>
                          <el-input class="inputbox"
                                    @mousewheel.native.prevent
                                    onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
                                    type="number"
                                    v-model="quanzhongswone2[item - 1]"></el-input>
                          <div style="width: 5px"></div>
                          <div class="marginright20">
                            <i @click="deldomswone2(item - 1)"
                               class="el-icon-remove icon_color"></i>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div style="display: flex; margin: 5px; background: #f1f1f5; max-width: 75vw; flex-wrap: wrap">
                    <div v-if="swshow3"
                         class="choosebox">
                      <div style="display: flex; flex-wrap: wrap">
                        <div v-for="(item, index) in insertindexswone3"
                             :key="index"
                             style="display: flex; flex-wrap: wrap">
                          <el-select v-model="swonevalue3[item - 1]"
                                     filterable
                                     placeholder="请选择申万三级行业"
                                     @change="forbidswone3()">
                            <el-option v-for="item in swoneoptions3"
                                       :key="item.value"
                                       :label="item.label"
                                       :value="item.value"
                                       :disabled="item.forbidstate"></el-option>
                          </el-select>
                          <div style="width: 5px"></div>
                          <el-input class="inputbox"
                                    @mousewheel.native.prevent
                                    onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
                                    type="number"
                                    v-model="quanzhongswone3[item - 1]"></el-input>
                          <div style="width: 5px"></div>
                          <div class="marginright20">
                            <i @click="deldomswone3(item - 1)"
                               class="el-icon-remove icon_color"></i>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div style="display: flex; margin: 5px; background: #f1f1f5; max-width: 75vw; flex-wrap: wrap">
                    <div v-if="swshow4"
                         class="choosebox">
                      <div style="display: flex; flex-wrap: wrap">
                        <div v-for="(item, index) in insertindexswone4"
                             :key="index"
                             style="display: flex; flex-wrap: wrap">
                          <el-select v-model="swonevalue4[item - 1]"
                                     filterable
                                     placeholder="请选择恒生一级行业"
                                     @change="forbidswone4()">
                            <el-option v-for="item in swoneoptions4"
                                       :key="item.value"
                                       :label="item.label"
                                       :value="item.value"
                                       :disabled="item.forbidstate"></el-option>
                          </el-select>
                          <div style="width: 5px"></div>
                          <el-input class="inputbox"
                                    @mousewheel.native.prevent
                                    onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
                                    type="number"
                                    v-model="quanzhongswone4[item - 1]"></el-input>
                          <div style="width: 5px"></div>
                          <div class="marginright20">
                            <i @click="deldomswone4(item - 1)"
                               class="el-icon-remove icon_color"></i>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-form-item>
              <el-form-item v-if="type == 'activeequity'"
                            label="alpha筛选器-主题判断">
                <template slot="label">
                  主题判断
                  <el-tooltip class="item"
                              effect="dark"
                              content="最多选择五个主题"
                              placement="right-start">
                    <svg width="14"
                         height="14"
                         viewBox="0 0 14 14"
                         fill="none">
                      <path fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
                            fill="black"
                            fill-opacity="0.45" />
                    </svg>
                  </el-tooltip>
                </template>
                <div style="display: flex; flex-wrap: wrap">
                  <div v-for="(item, index) in insertindex"
                       :key="index"
                       style="display: flex; flex-wrap: wrap">
                    <el-select v-model="themevalue[item - 1]"
                               filterable
                               placeholder="请选择主题"
                               @change="forbidtheme()">
                      <el-option v-for="item in themeoptions"
                                 :key="item.value"
                                 :label="item.label"
                                 :value="item.value"
                                 :disabled="item.forbidstate"></el-option>
                    </el-select>
                    <div style="width: 5px"></div>
                    <el-input class="inputbox"
                              @mousewheel.native.prevent
                              onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
                              type="number"
                              v-model="quanzhongtheme[item - 1]"></el-input>
                    <div style="width: 5px"></div>
                    <div class="marginright20">
                      <i @click="deldom(item - 1)"
                         class="el-icon-remove icon_color"></i>
                    </div>
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="alpha筛选器-指数持仓匹配">
                <template slot="label">
                  指数持仓匹配
                  <!-- <el-tooltip class="item" effect="dark" content="最多选择五个指数" placement="right-start">
										 <svg width="14" height="14" viewBox="0 0 14 14" fill="none">
									<path
										fill-rule="evenodd"
										clip-rule="evenodd"
										d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
										fill="black"
										fill-opacity="0.45"
									/>
								</svg>
                  </el-tooltip>-->
                </template>
                <div class="tagForFilter"
                     style="display: flex; flex-wrap: wrap; align-items: center">
                  <el-tag :key="tag"
                          v-for="tag in formData.index_code"
                          closable
                          :disable-transitions="false"
                          @close="handleClose2(tag)">{{ tag.label }}</el-tag>
                  <el-autocomplete ref="saveTagInput2"
                                   v-if="inputVisible2"
                                   popper-class="autoFilterPop"
                                   style="width: 240px"
                                   class="input-new-tag"
                                   v-model="inputValue2"
                                   :fetch-suggestions="querySearch2"
                                   placeholder="请输入内容"
                                   size="small"
                                   @select="handleInputConfirm2"></el-autocomplete>
                  <el-button v-else
                             class="button-new-tag"
                             size="small"
                             style="width: 100px"
                             @click="showInput2">+ New Index</el-button>
                </div>
              </el-form-item>
              <el-form-item label="是否持仓匹配"
                            prop="field106">
                <el-radio-group v-model="formData.field109"
                                size="medium">
                  <el-radio v-for="(item, index) in field109Options"
                            :key="index"
                            :label="item.value"
                            @click.native="cancelchoose1"
                            :disabled="item.disabled">{{ item.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <!-- <el-form-item size="medium">
        <el-button type="" @click="submitForm">提交</el-button>
        <el-button @click="redoForm">重置</el-button>
        <el-button @click="resetForm">保存为模板</el-button>
        <el-button @click="vismodel = true">选择模板查询</el-button>
       
        <el-button @click="printconsole">导出筛选结果</el-button>
              </el-form-item>-->
            </el-form>
            <div style="display: flex; margin-top: 20px">
              <el-button style="background: #4096FF; color: white"
                         type
                         size="medium"
                         @click="FUNC.throttleFunc(submitForm, 3000)()">提交</el-button>
              <el-button style="background: #40AFFF; color: white"
                         size="medium"
                         @click="redoForm">清除筛选条件</el-button>
              <!-- <el-button size="medium" @click="resetForm">存为模板</el-button> -->
              <!-- <el-button size="medium" @click="vismodel = true">选择模板</el-button> -->
              <el-button size="medium"
                         @click="gotoshowpdf">查看筛选器流程</el-button>
            </div>
          </div>
        </div>
      </div>
      <div class="line2"
           style="padding-top: 5px; padding-bottom: 5px"></div>
      <div style="padding-bottom: 20px; background: white; padding-top: 16px">
        <div style="display: flex">
          <div class="title"
               style="display: flex; background: white !important">
            <div class="pointssearch"></div>筛选结果
          </div>
          <div style="justify-content: flex-end; display: flex"
               class="marginight20px">
            <!-- <el-button size="medium" :disabled="disabledfalg" @click="cretezuhequ">创建组合</el-button> -->
            <div class="marginleft20"></div>
            <el-popover placement="left"
                        width="140"
                        trigger="click">
              <el-checkbox-group v-model="checkList">
                <el-checkbox label="选择能力"></el-checkbox>
                <div style="height: 5px"></div>
                <el-checkbox label="组合能力"></el-checkbox>
                <div style="height: 5px"></div>
                <el-checkbox label="盈利能力"></el-checkbox>
                <div style="height: 5px"></div>
                <el-checkbox label="匹配度"></el-checkbox>
              </el-checkbox-group>
              <div style="height: 10px"></div>
              <!-- <div style="display:flex;justify-content:flex-start"> -->
              <!-- <el-button size="small">取消</el-button> -->
              <!-- <el-button size="small" @click='chooseThis' type="primary">确定</el-button> -->
              <!-- </div> -->
              <!-- <el-button slot="reference">click 激活</el-button> -->
              <el-button slot="reference"
                         size="medium">编辑指标</el-button>
            </el-popover>
            <div class="marginleft20"></div>
            <el-button size="medium"
                       style="background: #4096FF; color: white"
                       @click="printconsole">导出筛选结果</el-button>
          </div>
        </div>
        <div v-loading="showmsgloading"
             class="tablemargin">
          <el-table :default-sort="{ prop: 'final_rank', order: 'descending' }"
                    :data="tableData"
                    @sort-change="sort_change"
                    :cell-style="elcellstyle"
                    class="table"
                    ref="multipleTable"
                    @selection-change="handleSelectzuhe"
                    :row-key="getRowKey"
                    header-cell-class-name="table-header">
            <el-table-column type="selection"
                             align="gotoleft"
                             :reserve-selection="true"
                             :width="getfontSize(55)"></el-table-column>
            <el-table-column :show-overflow-tooltip="true"
                             prop="name"
                             :min-width="getfontSize(160)"
                             align="gotoleft"
                             label="姓名">
              <template slot-scope="scope">
                <a style="border-bottom: 1px solid #4096FF"
                   @click="godetail(scope.row.code, scope.row.name)">
                  {{
                  scope.row.name | isDefault
                  }}
                </a>
              </template>
            </el-table-column>
            <el-table-column prop="fund_co"
                             :min-width="getfontSize(160)"
                             :show-overflow-tooltip="true"
                             label="基金公司"
                             align="gotoleft"></el-table-column>
            <el-table-column v-if="showsearch"
                             prop="final_rank"
                             sortable="custom"
                             label="总分"
                             :min-width="getfontSize(100)"
                             align="gotoleft">
              <template slot="header">
                总分
                <el-tooltip class="item"
                            effect="dark"
                            :content="dataexplain[6]"
                            placement="right-start">
                  <svg width="14"
                       height="14"
                       viewBox="0 0 14 14"
                       fill="none">
                    <path fill-rule="evenodd"
                          clip-rule="evenodd"
                          d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
                          fill="black"
                          fill-opacity="0.45" />
                  </svg>
                </el-tooltip>
              </template>
              <template slot-scope="scope">{{ scope.row['final_rank'] | fix2p }}</template>
            </el-table-column>
            <el-table-column align="gotoleft"
                             v-for="(item, index) in checkList"
                             :min-width="getfontSize(170)"
                             :key="index"
                             :label="item">
              <template slot="header">
                {{ item
                }}
                <el-tooltip class="item"
                            effect="dark"
                            :content="ListFilters[ListFilters.findIndex((items) => items.label == item)].description"
                            placement="right-start">
                  <svg width="14"
                       height="14"
                       viewBox="0 0 14 14"
                       fill="none">
                    <path fill-rule="evenodd"
                          clip-rule="evenodd"
                          d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
                          fill="black"
                          fill-opacity="0.45" />
                  </svg>
                </el-tooltip>
              </template>
              <template slot-scope="scope">
                {{
                scope.row[ListFilters[ListFilters.findIndex((items) => items.label == item)].value] | fix2
                }}
              </template>
            </el-table-column>
            <!-- <el-table-column
							v-if="showsearch"
							prop="long_stock_pick"
							:min-width="getfontSize(148)"
							sortable="custom"
							label="择股能力"
							align="gotoleft"
						>
							<template slot="header">
								择股能力<el-tooltip class="item" effect="dark" :content="dataexplain[2]" placement="right-start">
									 <svg width="14" height="14" viewBox="0 0 14 14" fill="none">
									<path
										fill-rule="evenodd"
										clip-rule="evenodd"
										d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
										fill="black"
										fill-opacity="0.45"
									/>
								</svg> </el-tooltip
							></template>
							<template slot-scope="scope">{{ scope.row['long_stock_pick'] | fix2 }}</template>
						</el-table-column>
						<el-table-column
							v-if="showsearch"
							prop="short_trade"
							:min-width="getfontSize(170)"
							sortable="custom"
							label="胜率"
							align="gotoleft"
						>
							<template slot="header">
								短期交易能力<el-tooltip class="item" effect="dark" :content="dataexplain[3]" placement="right-start">
									 <svg width="14" height="14" viewBox="0 0 14 14" fill="none">
									<path
										fill-rule="evenodd"
										clip-rule="evenodd"
										d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
										fill="black"
										fill-opacity="0.45"
									/>
								</svg> </el-tooltip
							></template>
							<template slot-scope="scope">{{ scope.row['short_trade'] | fix2 }}</template>
						</el-table-column>
						<el-table-column
							v-if="showsearch"
							prop="long_adaptive"
							sortable="custom"
							label="稳定性"
							:min-width="getfontSize(158)"
							align="gotoleft"
						>
							<template slot="header">
								风格适应性<el-tooltip class="item" effect="dark" :content="dataexplain[4]" placement="right-start">
									 <svg width="14" height="14" viewBox="0 0 14 14" fill="none">
									<path
										fill-rule="evenodd"
										clip-rule="evenodd"
										d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
										fill="black"
										fill-opacity="0.45"
									/>
								</svg> </el-tooltip
							></template>
							<template slot-scope="scope">{{ scope.row['long_adaptive'] | fix2 }}</template>
						</el-table-column>
						<el-table-column
							v-if="showsearch"
							prop="long_industry_cap"
							sortable="custom"
							label="行业综合能力"
							:min-width="getfontSize(178)"
							align="gotoleft"
						>
							<template slot="header">
								行业综合能力<el-tooltip class="item" effect="dark" :content="dataexplain[5]" placement="right-start">
									 <svg width="14" height="14" viewBox="0 0 14 14" fill="none">
									<path
										fill-rule="evenodd"
										clip-rule="evenodd"
										d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
										fill="black"
										fill-opacity="0.45"
									/>
								</svg> </el-tooltip
							></template>
							<template slot-scope="scope">{{ scope.row['long_industry_cap'] | fix2 }}</template>
						</el-table-column>
						<el-table-column
							v-if="showsearch"
							prop="long_stockclass_cap"
							sortable="custom"
							label="重仓股能力"
							:min-width="getfontSize(170)"
							align="gotoleft"
						>
							<template slot="header">
								主题综合能力<el-tooltip class="item" effect="dark" :content="dataexplain[6]" placement="right-start">
									 <svg width="14" height="14" viewBox="0 0 14 14" fill="none">
									<path
										fill-rule="evenodd"
										clip-rule="evenodd"
										d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
										fill="black"
										fill-opacity="0.45"
									/>
								</svg> </el-tooltip
							></template>
							<template slot-scope="scope">{{ scope.row['long_stockclass_cap'] | fix2 }}</template>
						</el-table-column>
						<el-table-column
							v-if="showsearch"
							prop="window_score"
							sortable="custom"
							label="市场适应能力"
							:min-width="getfontSize(168)"
							align="gotoleft"
						>
							<template slot="header">
								市场适应能力<el-tooltip class="item" effect="dark" :content="dataexplain[7]" placement="right-start">
									 <svg width="14" height="14" viewBox="0 0 14 14" fill="none">
									<path
										fill-rule="evenodd"
										clip-rule="evenodd"
										d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
										fill="black"
										fill-opacity="0.45"
									/>
								</svg> </el-tooltip
							></template>
							<template slot-scope="scope">{{ scope.row['window_score'] | fix2 }}</template>
            </el-table-column>-->

            <el-table-column prop="1w"
                             :min-width="getfontSize(128)"
                             sortable="custom"
                             label="近一周收益"
                             align="gotoleft">
              <template slot-scope="scope">{{ scope.row['1w'] | fix2p }}</template>
            </el-table-column>
            <el-table-column prop="1m"
                             :min-width="getfontSize(128)"
                             sortable="custom"
                             label="近一月收益"
                             align="gotoleft">
              <template slot-scope="scope">{{ scope.row['1m'] | fix2p }}</template>
            </el-table-column>
            <el-table-column prop="1q"
                             :min-width="getfontSize(128)"
                             sortable="custom"
                             label="近一季收益"
                             align="gotoleft">
              <template slot-scope="scope">{{ scope.row['1q'] | fix2p }}</template>
            </el-table-column>
            <el-table-column prop="1y"
                             :min-width="getfontSize(128)"
                             sortable="custom"
                             label="近一年收益"
                             align="gotoleft">
              <template slot-scope="scope">{{ scope.row['1y'] | fix2p }}</template>
            </el-table-column>
            <el-table-column v-if="showsearch"
                             prop="scale"
                             sortable="custom"
                             label="规模"
                             :min-width="getfontSize(128)"
                             align="gotoleft">
              <template slot-scope="scope">{{ scope.row['scale'] | fixp }}</template>
            </el-table-column>

            <!-- <el-table-column  prop="bigs" :show-overflow-tooltip="true"  sortable='custom' label="重仓股" align="gotoleft"> 
                   
            </el-table-column>-->
            <!-- 
               <el-table-column   label="查看详情" width="100" align="gotoleft">
                    <template slot-scope="scope"><div @click="godetail(scope.row.code,scope.row.name)"><i  class="el-icon-tickets icon_color"></i></div></template>  
            </el-table-column>-->
            <el-table-column label="关注"
                             :min-width="getfontSize(128)"
                             align="gotoleft">
              <template slot-scope="scope">
                <div @click="addpool(scope.row.code, scope.row.name)">
                  <i class="el-icon-circle-plus icon_color"></i>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <el-pagination background
                           layout="total, prev, pager, next"
                           :current-page.sync="pageIndex"
                           :page-size="pageSize"
                           :total="pageTotal"
                           @current-change="handlePageChange"></el-pagination>
          </div>
        </div>
      </div>
    </div>
    <createdcomb :addzuheflag="addzuheflag"
                 :fundlist="fundlisttemp"
                 :dataobj="dataobj"></createdcomb>

    <el-drawer style="overflow-x: hidden"
               :visible.sync="vismodel"
               direction="rtl"
               :show-close="false"
               size="600px">
      <div style="text-align: left"
           class="marginleft20">
        <span class="recommodfont borderbottom2px">
          推荐组合&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          <el-button style="color: #b4bed0 !important; border: 1px solid #b4bed0; border-radius: 2px; background: #white !important"
                     @click="changepos"
                     icon="el-icon-refresh">换一换</el-button>
        </span>
      </div>
      <div style="height: 20px"></div>
      <div v-if="showmodel"
           style="width: 600px; display: flex; flex-wrap: wrap">
        <div v-for="(items, index) in listar"
             :key="index"
             style="cursor: pointer"
             @click="setshuju(items.model_args)"
             class="modelbox">
          <el-tooltip class="item"
                      effect="dark"
                      :content="items.model_description"
                      placement="right-start">
            <div style="text-align: center">
              <span class="modelnamebox">{{ items.model_name }}</span>
            </div>
          </el-tooltip>
        </div>
        <!--  -->
      </div>
      <div style="height: 50px"></div>
      <div style="text-align: left"
           class="marginleft20">
        <span class="recommodfont borderbottom2px">我的组合</span>
      </div>
      <div style="height: 20px"></div>
      <div v-for="(item1, index) in gridData"
           :key="index">
        <div class="selfmodelbox">
          <div class="margin20px"></div>
          <div class="selfmodelde1">
            模型名称：
            <span class="selfmodelde2">{{ item1.model_name }}</span>
          </div>
          <div class="selfmodelde1">
            创建日期：
            <span class="selfmodelde2">{{ item1.model_time }}</span>
          </div>
          <div class="selfmodelde1">
            编辑操作：
            <span class="selfmodelde2">
              <el-button @click="deletemodel(item1.model_name)"
                         type="danger">删除</el-button>
            </span>
          </div>
          <div class="selfmodelde1">
            模型描述：
            <span class="selfmodelde2">{{ item1.model_description }}</span>
          </div>
          <div class="margin20px"></div>
        </div>
      </div>
      <!-- <el-table :data="gridData" >
      <el-table-column property="model_name" label="模型名称" >
        <templete slot-scope="scope"><el-button @click="setshuju(scope.row.model_args)">{{scope.row.model_name}}</el-button></templete>
      </el-table-column>
      <el-table-column property="model_description" show-overflow-tooltip label="描述" ></el-table-column>
       <el-table-column property="model_time" label="创建日期" > <templete slot-scope="scope">{{scope.row.model_time|fix6}}</templete></el-table-column>
      <el-table-column property="address" label="编辑操作">  <templete slot-scope="scope"><el-button @click='deletemodel(scope.row.model_name)' type="danger">删除</el-button></templete></el-table-column>
      </el-table>-->
    </el-drawer>
    <el-dialog title="保存为模板"
               :visible.sync="useraddmodalshow"
               width="30%"
               destroy-on-close>
      <div class="savemodel"
           style="width: 100%">
        <el-form :model="usermodal">
          <el-form-item label="模板名称">
            <el-input v-model="usermodal.name"
                      autocomplete="off"></el-input>
          </el-form-item>
          <div class="height10border"></div>
          <el-form-item label="模板描述">
            <el-input type="textarea"
                      :rows="2"
                      placeholder="请输入内容"
                      v-model="usermodal.textarea"></el-input>
          </el-form-item>
          <div class="height10border"></div>
          <el-form-item label="是否公开模板">
            <template>
              <el-radio v-model="usermodal.ispublic"
                        label="true">是</el-radio>
              <el-radio v-model="usermodal.ispublic"
                        label="false">否</el-radio>
            </template>
          </el-form-item>
          <div class="height10border"></div>
          <div style="text-align: right"
               class="demo-drawer__footer">
            <el-button type="primary"
                       style="background: #d7dbe0 !important; color: balck !important; border: 1px solid #d7dbe0 !important"
                       @click="useraddmodalshow = fasle">取消</el-button>
            <el-button type="primary"
                       @click="submitmodal">确认提交</el-button>
          </div>
        </el-form>
      </div>
    </el-dialog>

    <el-dialog title="选择添加的基金池"
               :visible.sync="addfundvis"
               width="20%"
               destroy-on-close>
      基金代码:
      <br />
      <el-input type="text"
                :disabled="true"
                :value="choosefundid"
                label="基金代码"></el-input>基金名称:
      <br />
      <el-input type="text"
                :disabled="true"
                :value="choosefundname"
                label="基金名称"></el-input>基金池：
      <br />
      <el-select style="width: 100%"
                 v-model="choosedpool"
                 placeholder="请选择您的基金池">
        <el-option v-for="item in options"
                   :key="item.value"
                   :label="item.label"
                   :value="item.value"></el-option>
      </el-select>
      <br />理由:
      <br />
      <el-input type="textarea"
                v-model="choosereason"
                label="选择的理由"></el-input>
      <span slot="footer"
            class="dialog-footer">
        <el-button type="primary"
                   @click="saveEdit(form)">提 交</el-button>
      </span>
    </el-dialog>
    <riskexplain ref="visdescriobe"></riskexplain>
    <pdfshow ref="equitypdf"></pdfshow>
  </div>
</template>
<script>
import pdfshow from "../../fund/alpha/components/components/showalphafilterpdf.vue";
import alphachoosepool from "../../fund/alpha/components/components/alphachoosepool.vue";
import riskexplain from "../../fund/alpha/components/components/riskexplain.vue";
import selecterinput from "../../fund/alpha/components/components/selecterinpubox";
import selecterinputboxsignal from "../../fund/alpha/components/components/selecterinpuboxsignal";
import { fontSize } from "@/assets/js/echartsrpxtorem"; //注意路径
import tempbasket from "./components/tempbasketmanager";
import axios from "@/api/index";
import createdcomb from "../../fund/alpha/components/components/createzuhealpha";
import { Search } from "@/api/pages/Analysis.js";

export default {
  components: {
    tempbasket,
    selecterinput,
    selecterinputboxsignal,
    createdcomb,
    riskexplain,
    alphachoosepool,
    pdfshow
  },
  props: {
    type: {
      type: String,
      default: "activeequity"
    }
  },
  data () {
    return {
      inputValue2: [],
      inputVisible2: false,
      showIndexSelect: false,
      showIndustrySelect: false,
      showThemeSelect: false,

      ListFilters: [
        {
          label: "选择能力",
          value: "picking_score",
          description: "择股择行业能力。"
        },
        {
          label: "组合能力",
          value: "portfolio_score",
          description: "组合构造短期交易等形成的能力"
        },
        {
          label: "盈利能力",
          value: "profitability_score",
          description: "风格适应叠加收益创造能力"
        },
        {
          label: "匹配度",
          value: "selected_weight",
          description: "筛选行业或主题持仓匹配的程度。"
        },
        {
          label: "总分",
          value: "final_rank",
          description: "能力得分叠加（选择持仓匹配的情况）匹配度。"
        }
      ],
      checkList: [],
      dataexplain: [
        "",
        "",
        "择股择行业能力。",
        "组合构造短期交易等形成的能力",
        "风格适应叠加收益创造能力",
        "筛选行业或主题持仓匹配的程度。",
        "能力得分叠加（选择持仓匹配的情况）匹配度。",
        "",
        "",
        "",
        "",
        "",
        "",
        ""
      ],
      addzuheflag: false,
      fundlist: [],
      dataobj: [],
      fundlisttemp: [],
      pageIndex: 1,
      pageSize: 20,
      showmodel: true,
      gridData: [],
      poollist: [],
      vismodel: false,
      pageTotal: null,
      searchdetail: null,
      filtersearch1: true,
      filtersearch2: true,
      filtersearch3: true,
      filtersearch4: true,
      filtersearch5: true,
      filtersearch6: true,
      filtersearch7: true,
      filtersearch8: true,
      filtersearch9: true,
      filtersearch10: true,
      filtersearch11: true,
      filtersearch12: true,
      filtersearch13: true,
      filtersearch14: true,
      filtersearch15: true,
      filtersearch16: true,
      filtersearch17: true,
      filtersearch18: true,
      filtersearch19: true,
      filtersearch20: true,
      filtersearch21: true,
      filtersearch22: true,
      filtersearch23: true,
      filtersearch24: true,
      filtersearch25: true,
      filtersearch26: true,
      filtersearch27: true,
      filtersearch28: true,
      filtersearch29: true,
      filtersearch30: true,
      msg1: {},
      msg2: {},
      msg3: {},
      msg4: {},
      msg5: {},
      msg6: {},
      msg7: {},
      msg8: {},
      msg9: {},
      msg10: {},
      msg11: {},
      msg12: {},
      msg13: {},
      msg14: {},
      msg15: {},
      msg16: {},
      msg17: {},
      msg18: {},
      msg19: {},
      msg20: {},
      msg21: {},
      msg22: {},
      msg23: {},
      msg24: {},
      msg25: {},
      msg26: {},
      msg27: {},
      msg28: {},
      msg29: {},
      msg30: {},
      search: null,
      tableDatas: [],
      visdescriobe: false,
      modellist: [{ a1: 8, a2: 2, a3: 8, a4: 8 }],
      vismodel: false,
      submitflag: true,
      showsearch: false,
      showmsgloading: false,
      swshow: false,
      swshow2: false,
      swshow3: false,
      swshow4: false, //恒生一级
      czshow: false,
      dpshow: false,
      dpchoose: [],
      czchoose: [],
      kcchoose: [],
      hgchoose: [],
      ejchoose: [],
      swchoose: [],
      listar: [],
      value: [],
      option: [
        { value: "now", label: "近期表现", children: [] },
        {
          value: "from",
          label: "从那时起",
          children: [
            {
              value: "2015-08-26",
              label: "2015-08-26"
            }
          ]
        }
      ],
      option2: [
        { value: "now", label: "近期表现", children: [] },
        {
          value: "from",
          label: "从那时起",
          children: [
            {
              value: "2015-08-26",
              label: "2015-08-26"
            }
          ]
        }
      ],
      option3: [
        { value: "now", label: "近期表现", children: [] },
        {
          value: "from",
          label: "从那时起",
          children: [
            {
              value: "2015-08-26",
              label: "2015-08-26"
            }
          ]
        }
      ],
      option4: [
        { value: "now", label: "近期表现", children: [] },
        {
          value: "from",
          label: "从那时起",
          children: [
            {
              value: "2015-08-26",
              label: "2015-08-26"
            }
          ]
        }
      ],
      option5: [
        { value: "now", label: "近期表现", children: [] },
        {
          value: "from",
          label: "从那时起",
          children: [
            {
              value: "2015-08-26",
              label: "2015-08-26"
            }
          ]
        }
      ],
      option6: [
        { value: "now", label: "近期表现", children: [] },
        {
          value: "from",
          label: "从那时起",
          children: [
            {
              value: "2015-08-26",
              label: "2015-08-26"
            }
          ]
        }
      ],
      option7: [
        { value: "now", label: "近期表现", children: [] },
        {
          value: "from",
          label: "从那时起",
          children: [
            {
              value: "2015-08-26",
              label: "2015-08-26"
            }
          ]
        }
      ],
      option8: [
        { value: "now", label: "近期表现", children: [] },
        {
          value: "from",
          label: "从那时起",
          children: [
            {
              value: "2015-08-26",
              label: "2015-08-26"
            }
          ]
        }
      ],
      option9: [
        { value: "now", label: "近期表现", children: [] },
        {
          value: "from",
          label: "从那时起",
          children: [
            {
              value: "2015-08-26",
              label: "2015-08-26"
            }
          ]
        }
      ],
      option10: [
        { value: "now", label: "近期表现", children: [] },
        {
          value: "from",
          label: "从那时起",
          children: [
            {
              value: "2015-08-26",
              label: "2015-08-26"
            }
          ]
        }
      ],
      option11: [
        { value: "now", label: "近期表现", children: [] },
        {
          value: "from",
          label: "从那时起",
          children: [
            {
              value: "2015-08-26",
              label: "2015-08-26"
            }
          ]
        }
      ],
      option12: [
        { value: "now", label: "近期表现", children: [] },
        {
          value: "from",
          label: "从那时起",
          children: [
            {
              value: "2015-08-26",
              label: "2015-08-26"
            }
          ]
        }
      ],
      option13: [
        { value: "now", label: "近期表现", children: [] },
        {
          value: "from",
          label: "从那时起",
          children: [
            {
              value: "2015-08-26",
              label: "2015-08-26"
            }
          ]
        }
      ],
      option14: [
        { value: "now", label: "近期表现", children: [] },
        {
          value: "from",
          label: "从那时起",
          children: [
            {
              value: "2015-08-26",
              label: "2015-08-26"
            }
          ]
        }
      ],
      option15: [
        { value: "now", label: "近期表现", children: [] },
        {
          value: "from",
          label: "从那时起",
          children: [
            {
              value: "2015-08-26",
              label: "2015-08-26"
            }
          ]
        }
      ],
      option16: [
        { value: "now", label: "近期表现", children: [] },
        {
          value: "from",
          label: "从那时起",
          children: [
            {
              value: "2015-08-26",
              label: "2015-08-26"
            }
          ]
        }
      ],
      option17: [
        { value: "now", label: "近期表现", children: [] },
        {
          value: "from",
          label: "从那时起",
          children: [
            {
              value: "2015-08-26",
              label: "2015-08-26"
            }
          ]
        }
      ],
      option18: [
        { value: "now", label: "近期表现", children: [] },
        {
          value: "from",
          label: "从那时起",
          children: [
            {
              value: "2015-08-26",
              label: "2015-08-26"
            }
          ]
        }
      ],
      option19: [
        { value: "now", label: "近期表现", children: [] },
        {
          value: "from",
          label: "从那时起",
          children: [
            {
              value: "2015-08-26",
              label: "2015-08-26"
            }
          ]
        }
      ],
      option20: [
        { value: "now", label: "近期表现", children: [] },
        {
          value: "from",
          label: "从那时起",
          children: [
            {
              value: "2015-08-26",
              label: "2015-08-26"
            }
          ]
        }
      ],
      option21: [
        { value: "now", label: "近期表现", children: [] },
        {
          value: "from",
          label: "从那时起",
          children: [
            {
              value: "2015-08-26",
              label: "2015-08-26"
            }
          ]
        }
      ],
      option22: [
        { value: "now", label: "近期表现", children: [] },
        {
          value: "from",
          label: "从那时起",
          children: [
            {
              value: "2015-08-26",
              label: "2015-08-26"
            }
          ]
        }
      ],
      option23: [
        { value: "now", label: "近期表现", children: [] },
        {
          value: "from",
          label: "从那时起",
          children: [
            {
              value: "2015-08-26",
              label: "2015-08-26"
            }
          ]
        }
      ],
      option24: [
        { value: "now", label: "近期表现", children: [] },
        {
          value: "from",
          label: "从那时起",
          children: [
            {
              value: "2015-08-26",
              label: "2015-08-26"
            }
          ]
        }
      ],
      option25: [
        { value: "now", label: "近期表现", children: [] },
        {
          value: "from",
          label: "从那时起",
          children: [
            {
              value: "2015-08-26",
              label: "2015-08-26"
            }
          ]
        }
      ],
      option26: [
        { value: "now", label: "近期表现", children: [] },
        {
          value: "from",
          label: "从那时起",
          children: [
            {
              value: "2015-08-26",
              label: "2015-08-26"
            }
          ]
        }
      ],
      option27: [
        { value: "now", label: "近期表现", children: [] },
        {
          value: "from",
          label: "从那时起",
          children: [
            {
              value: "2015-08-26",
              label: "2015-08-26"
            }
          ]
        }
      ],
      option28: [
        { value: "now", label: "近期表现", children: [] },
        {
          value: "from",
          label: "从那时起",
          children: [
            {
              value: "2015-08-26",
              label: "2015-08-26"
            }
          ]
        }
      ],
      option29: [
        { value: "now", label: "近期表现", children: [] },
        {
          value: "from",
          label: "从那时起",
          children: [
            {
              value: "2015-08-26",
              label: "2015-08-26"
            }
          ]
        }
      ],
      option30: [
        { value: "now", label: "近期表现", children: [] },
        {
          value: "from",
          label: "从那时起",
          children: [
            {
              value: "2015-08-26",
              label: "2015-08-26"
            }
          ]
        }
      ],
      usermodal: {
        name: "",
        textarea: "",
        ispublic: "false"
      },
      showbodong: false,
      showbodong1: false,
      showbodong2: false,
      showbodong3: false,
      showbodong4: false,
      showbodong5: false,
      showbodong6: false,
      showbodong7: false,
      showbodong8: false,
      showbodong9: false,
      showfengxian: false,
      showfengxian1: false,
      showfengxian2: false,
      showfengxian3: false,
      showfengxian4: false,
      showfengxian5: false,
      showfengxian6: false,
      showfengxian7: false,
      showfengxian8: false,
      showfengxian9: false,
      showfengxian10: false,
      showfengxian11: false,
      showfengxian12: false,
      showfengxian13: false,
      showfengxian14: false,
      showfengxian15: false,
      showfengxian16: false,
      showfengxian17: false,
      showfengxian18: false,
      showfengxian19: false,
      showhg: false,
      showkc: false,
      showej: false,
      temp5: [],
      temp4: [],
      temp3: [],
      temp2: [],
      temp: [],
      choosedpool: null,
      choosereason: null,
      choosefundid: null,
      choosefundname: null,
      options: [
        {
          value: "选项1",
          label: "池子1"
        },
        {
          value: "选项2",
          label: "池子2"
        },
        {
          value: "选项3",
          label: "池子3"
        },
        {
          value: "选项4",
          label: "池子4"
        },
        {
          value: "选项5",
          label: "池子5"
        }
      ],
      showmore2: true,
      showmore1: true,
      insertindex: 1,
      useraddmodalshow: false,
      selectvalue1: [],
      selectvalue2: [],
      selectvalue3: [],
      selectvalue4: [],
      selectvalue5: [],
      selectvalue6: [],
      selectvalue7: [],
      selectvalue8: [],
      selectvalue9: [],
      selectvalue10: [],
      selectvalue11: [],
      selectvalue12: [],
      selectvalue13: [],
      selectvalue14: [],
      selectvalue15: [],
      selectvalue16: [],
      selectvalue17: [],
      selectvalue18: [],
      selectvalue19: [],
      selectvalue20: [],
      selectvalue21: [],
      selectvalue22: [],
      selectvalue23: [],
      selectvalue24: [],
      selectvalue25: [],
      selectvalue26: [],
      selectvalue27: [],
      selectvalue28: [],
      selectvalue29: [],
      selectvalue30: [],
      addfundvis: false,
      alldata: [],
      tableData: [],
      fundname: null,
      formData: {
        index_code: [], //指标列表

        haveability: [],
        field101: null,
        field1011: null,
        field102: null,
        field1021: null,
        bigindustry: [],
        field104: [
          {
            id: 1,
            date: ["now", "3y"],
            value1: null,
            value2: null,
            rank: "100"
          },
          {
            id: 2,
            date: ["now", "3y"],
            value1: null,
            value2: null,
            rank: "100"
          },
          {
            id: 3,
            date: ["now", "3y"],
            value1: null,
            value2: null,
            rank: "100"
          },
          {
            id: 4,
            date: ["now", "3y"],
            value1: null,
            value2: null,
            rank: "100"
          },
          {
            id: 5,
            date: ["now", "3y"],
            value1: null,
            value2: null,
            rank: "100"
          },
          {
            id: 6,
            date: ["now", "3y"],
            value1: null,
            value2: null,
            rank: "100"
          },
          {
            id: 7,
            date: ["now", "3y"],
            value1: null,
            value2: null,
            rank: "100"
          },
          {
            id: 8,
            date: ["now", "3y"],
            value1: null,
            value2: null,
            rank: "100"
          },
          {
            id: 9,
            date: ["now", "3y"],
            value1: null,
            value2: null,
            rank: "100"
          },
          { id: 10, date: ["now", "3y"], value1: null, rank: "100" }
        ],
        field105: [
          { id: 1, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 2, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 3, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 4, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 5, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 6, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 7, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 8, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 9, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 10, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 11, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 12, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 13, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 14, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 15, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 16, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 17, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 18, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 19, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 20, date: ["now", "3y"], value1: null, rank: "100" }
        ],
        field106: {
          value1: 0,
          value2: 0,
          value3: 0,
          value4: 0
        },
        field1061: {
          value1: 0,
          value2: 0,
          value3: 0,
          value4: 0
        },
        field1062: {
          value1: 0,
          value2: 0,
          value3: 0,
          value4: 0,
          value5: 0,
          value6: 0
        },
        field107: {
          value1: 0,
          value2: 0
        },
        field1071: {
          value1: 0,
          value2: 0,
          value3: 0
        },
        field108: {
          value1: 0,
          value2: 0,
          value3: 0,
          value4: 0,
          value5: 0,
          value6: 0,
          value7: 0,
          value8: 0,
          value9: 0,
          value10: 0,
          value11: 0,
          value12: 0,
          value13: 0,
          value14: 0,
          value15: 0,
          value16: 0,
          value17: 0,
          value18: 0,
          value19: 0,
          value20: 0,
          value21: 0,
          value22: 0,
          value23: 0,
          value24: 0,
          value25: 0,
          value26: 0,
          value27: 0,
          value28: 0
        },
        field109: 1,
        field110: 1,
        theme: [],
        swindustry1: [],
        swindustry2: [],
        swindustry3: [],
        hsindustry1: []
      },
      disabledfalg: true,
      quanzhongtheme: [],
      themeoptions: [],
      themevalue: [],
      swonevalue: [],
      swonevalue2: [],
      swonevalue3: [],
      swonevalue4: [],
      insertindexswone: 1,
      insertindexswone2: 1,
      insertindexswone3: 1,
      insertindexswone4: 1,
      quanzhongswone: [],
      quanzhongswone2: [],
      quanzhongswone3: [],
      quanzhongswone4: [],
      swoneoptions2: [],
      swoneoptions3: [],
      swoneoptions4: [],
      swoneoptions: [],
      field103Options: [
        {
          label: "机构定制",
          value: "机构定制"
        },
        {
          label: "机构为主",
          value: "机构为主"
        },
        {
          label: "散户为主",
          value: "散户为主"
        }
      ],
      field110Options: [
        {
          label: "全体",
          value: 1
        },
        {
          label: "价值",
          value: 2
        },
        {
          label: "成长",
          value: 3
        }
      ],
      bigindustryOptions: [
        {
          label: "中周期",
          value: "中周期"
        },
        {
          label: "大金融",
          value: "大金融"
        },
        {
          label: "大科技",
          value: "大科技"
        },
        {
          label: "大周期",
          value: "大周期"
        },
        {
          label: "大消费",
          value: "大消费"
        },
        {
          label: "未突出",
          value: "未突出"
        }
      ],
      haveabliltyOptions: [
        {
          label: "选股能力",
          value: "选股能力"
        },
        {
          label: "行业配置能力",
          value: "行业配置能力"
        },
        {
          label: "股债配置能力",
          value: "股债配置能力"
        },
        {
          label: "交易能力",
          value: "交易能力"
        }
      ],
      field104Options: [
        {
          label: "波动率", //波动率：区间数据（默认下届为0）
          value: 1
        },
        {
          label: "最⼤回撤", //区间数据（默认下届为0）
          value: 2
        },
        {
          label: "平均下⾏周期", //区间数据（默认下届为0）
          value: 3
        },
        {
          label: "平均恢复周期", //区间数据（默认下届为0）
          value: 4
        },
        {
          label: "最⼤回撤⽐", //区间数据（默认下届为0.5，上届为1）
          value: 5
        },
        {
          label: "在险价值", //区间数据
          value: 6
        },
        {
          label: "期望损失", //区间数据
          value: 7
        },
        {
          label: "下⾏⻛险", //区间数据
          value: 8
        },
        {
          label: "波动率⽐", //区间数据（默认下届为0.5，上届为1）
          value: 9
        },
        {
          label: "痛苦指数",
          value: 10
        }
      ],
      field105Options: [
        {
          label: "年化收益率",
          value: 1
        },
        {
          label: "累计收益率",
          value: 2
        },
        {
          label: "夏普率(rf=0)",
          value: 3
        },
        {
          label: "夏普率（rf=0.04）",
          value: 4
        },
        {
          label: " 夏普率（动态rf）",
          value: 5
        },
        {
          label: "卡码率",
          value: 6
        },
        {
          label: "索提诺系数（rf=0）",
          value: 7
        },
        {
          label: " 索提诺系数（rf=0.04）",
          value: 8
        },
        {
          label: "索提诺系数（动态rf）",
          value: 9
        },
        {
          label: "稳定系数",
          value: 10
        },
        {
          label: "凯利系数",
          value: 11
        },
        {
          label: "信息⽐率",
          value: 12
        },
        {
          label: "上攻潜⼒（周）",
          value: 13
        },
        {
          label: "⽉胜率",
          value: 14
        },
        {
          label: "詹森系数",
          value: 15
        },
        {
          label: "特诺系数",
          value: 16
        },
        {
          label: "上⾏捕获",
          value: 17
        },
        {
          label: "下⾏捕获",
          value: 18
        },
        {
          label: "择时gamma",
          value: 19
        },
        {
          label: "M2",
          value: 20
        }
      ],
      field106Options: [
        {
          label: "宏观周期",
          value: 1
        },
        {
          label: "库存周期",
          value: 2
        },
        {
          label: "二级市场周期",
          value: 3
        }
      ],
      field107Options: [
        {
          label: "成长vs价值",
          value: 1
        },
        {
          label: "大盘vs小盘",
          value: 2
        }
      ],
      field108Options: [
        {
          label: "申万一级行业",
          value: 1
        },
        {
          label: "申万二级行业",
          value: 2
        },
        {
          label: "申万三级行业",
          value: 3
        },
        {
          label: "恒生一级行业",
          value: 4
        }
      ],
      field108Options2: [
        {
          label: "恒生一级行业",
          value: 4
        }
      ],
      field109Options: [
        {
          label: "是",
          value: 1
        },
        {
          label: "否",
          value: 2
        }
      ]
    };
  },
  computed: {},
  watch: {
    // 申万一级
    swonevalue (val) {
      // //console.log(this.insertindexswone)
      //  //console.log(this.quanzhongswone)
      //  //console.log(val)
      let sum = 0;
      for (let i = 0; i < val.length - 1; i++) {
        sum += Number(this.quanzhongswone[i]);
      }
      this.quanzhongswone[val.length - 1] = 100 - sum;

      if (
        this.swonevalue.length == this.quanzhongswone.length &&
        this.insertindexswone == val.length
      ) {
        // //console.log('213')
        this.adddomswone();
      }

      // //console.log(val)
      // //console.log('swcalue权重val')
      // if(this.insertindexswone==val.length){
      //   if(val[val.length-1]!=null&&val[val.length-1]!=''){
      //     if(this.quanzhongswone[val.length-1]!=null&&this.quanzhongswone[val.length-1]!=''){

      //         this.adddomswone()
      //     }
      // }
      // }
    },
    quanzhongswone (val) {
      if (this.insertindexswone == val.length) {
        if (val[val.length - 1] != null && val[val.length - 1] != "") {
          if (
            this.swonevalue[val.length - 1] != null &&
            this.swonevalue[val.length - 1] != ""
          ) {
            this.adddomswone();
          }
        }
      }
    },
    // 申万二级
    swonevalue2 (val) {
      // ////console.log(this.insertindex)
      // if(this.insertindexswone2==val.length){
      //   if(val[val.length-1]!=null&&val[val.length-1]!=''){
      //     if(this.quanzhongswone2[val.length-1]!=null&&this.quanzhongswone2[val.length-1]!=''){
      //         this.adddomswone2()
      //     }
      // }
      // }
      let sum = 0;
      for (let i = 0; i < val.length - 1; i++) {
        sum += Number(this.quanzhongswone2[i]);
      }
      this.quanzhongswone2[val.length - 1] = 100 - sum;

      if (
        this.swonevalue2.length == this.quanzhongswone2.length &&
        this.insertindexswone2 == val.length
      ) {
        // //console.log('213')
        this.adddomswone2();
      }
    },
    quanzhongswone2 (val) {
      if (this.insertindexswone2 == val.length) {
        if (val[val.length - 1] != null && val[val.length - 1] != "") {
          if (
            this.swonevalue2[val.length - 1] != null &&
            this.swonevalue2[val.length - 1] != ""
          ) {
            this.adddomswone2();
          }
        }
      }
    },
    // 申万三级
    swonevalue3 (val) {
      // ////console.log(this.insertindex)
      // if(this.insertindexswone3==val.length){
      //   if(val[val.length-1]!=null&&val[val.length-1]!=''){
      //     if(this.quanzhongswone3[val.length-1]!=null&&this.quanzhongswone3[val.length-1]!=''){
      //         this.adddomswone3()
      //     }
      // }
      // }
      let sum = 0;
      for (let i = 0; i < val.length - 1; i++) {
        sum += Number(this.quanzhongswone3[i]);
      }
      this.quanzhongswone3[val.length - 1] = 100 - sum;

      if (
        this.swonevalue3.length == this.quanzhongswone3.length &&
        this.insertindexswone3 == val.length
      ) {
        // //console.log('213')
        this.adddomswone3();
      }
    },
    quanzhongswone3 (val) {
      if (this.insertindexswone3 == val.length) {
        if (val[val.length - 1] != null && val[val.length - 1] != "") {
          if (
            this.swonevalue3[val.length - 1] != null &&
            this.swonevalue3[val.length - 1] != ""
          ) {
            this.adddomswone3();
          }
        }
      }
    },
    // 恒生一级
    swonevalue4 (val) {
      // ////console.log(this.insertindex)
      // if(this.insertindexswone4==val.length){
      //   if(val[val.length-1]!=null&&val[val.length-1]!=''){
      //     if(this.quanzhongswone4[val.length-1]!=null&&this.quanzhongswone4[val.length-1]!=''){
      //         this.adddomswone4()
      //     }
      // }
      // }
      let sum = 0;
      for (let i = 0; i < val.length - 1; i++) {
        sum += Number(this.quanzhongswone4[i]);
      }
      this.quanzhongswone4[val.length - 1] = 100 - sum;

      if (
        this.swonevalue4.length == this.quanzhongswone4.length &&
        this.insertindexswone4 == val.length
      ) {
        // //console.log('213')
        this.adddomswone4();
      }
    },
    quanzhongswone4 (val) {
      if (this.insertindexswone4 == val.length) {
        if (val[val.length - 1] != null && val[val.length - 1] != "") {
          if (
            this.swonevalue4[val.length - 1] != null &&
            this.swonevalue4[val.length - 1] != ""
          ) {
            this.adddomswone4();
          }
        }
      }
    },
    themevalue (val) {
      // ////console.log(this.insertindex)
      // if(this.insertindex==val.length){
      //   if(val[val.length-1]!=null&&val[val.length-1]!=''){
      //     if(this.quanzhongtheme[val.length-1]!=null&&this.quanzhongtheme[val.length-1]!=''){
      //         this.adddom()
      //     }
      // }
      // }
      let sum = 0;
      for (let i = 0; i < val.length - 1; i++) {
        sum += Number(this.quanzhongtheme[i]);
      }
      this.quanzhongtheme[val.length - 1] = 100 - sum;

      if (
        this.themevalue.length == this.quanzhongtheme.length &&
        this.insertindex == val.length
      ) {
        // //console.log('213')
        this.adddom();
      }
    },
    quanzhongtheme (val) {
      if (this.insertindex == val.length) {
        if (val[val.length - 1] != null && val[val.length - 1] != "") {
          if (
            this.themevalue[val.length - 1] != null &&
            this.themevalue[val.length - 1] != ""
          ) {
            this.adddom();
          }
        }
      }
    },
    // insertindex(val){
    //     this.quanzhongtheme.length = val
    // },
    searchdetail (val) {
      ////console.log(val)
      this.filtersearch1 = false;
      this.filtersearch2 = false;
      this.filtersearch3 = false;
      this.filtersearch4 = false;
      this.filtersearch5 = false;
      this.filtersearch6 = false;
      this.filtersearch7 = false;
      this.filtersearch8 = false;
      this.filtersearch9 = false;
      this.filtersearch10 = false;
      this.filtersearch11 = false;
      this.filtersearch12 = false;
      this.filtersearch13 = false;
      this.filtersearch14 = false;
      this.filtersearch15 = false;
      this.filtersearch16 = false;
      this.filtersearch17 = false;
      this.filtersearch18 = false;
      this.filtersearch19 = false;
      this.filtersearch20 = false;
      this.filtersearch21 = false;
      this.filtersearch22 = false;
      this.filtersearch23 = false;
      this.filtersearch24 = false;
      this.filtersearch25 = false;
      this.filtersearch26 = false;
      this.filtersearch27 = false;
      this.filtersearch28 = false;
      this.filtersearch29 = false;
      this.filtersearch30 = false;
      if (val == "") {
        this.filtersearch1 = true;
        this.filtersearch2 = true;
        this.filtersearch3 = true;
        this.filtersearch4 = true;
        this.filtersearch5 = true;
        this.filtersearch6 = true;
        this.filtersearch7 = true;
        this.filtersearch8 = true;
        this.filtersearch9 = true;
        this.filtersearch10 = true;
        this.filtersearch11 = true;
        this.filtersearch12 = true;
        this.filtersearch13 = true;
        this.filtersearch14 = true;
        this.filtersearch15 = true;
        this.filtersearch16 = true;
        this.filtersearch17 = true;
        this.filtersearch18 = true;
        this.filtersearch19 = true;
        this.filtersearch20 = true;
        this.filtersearch21 = true;
        this.filtersearch22 = true;
        this.filtersearch23 = true;
        this.filtersearch24 = true;
        this.filtersearch25 = true;
        this.filtersearch26 = true;
        this.filtersearch27 = true;
        this.filtersearch28 = true;
        this.filtersearch29 = true;
        this.filtersearch30 = true;
      } else {
        if ("波动率".indexOf(val) >= 0) this.filtersearch1 = true;
        if ("最大回撤".indexOf(val) >= 0) this.filtersearch2 = true;
        if ("平均下行周期".indexOf(val) >= 0) this.filtersearch3 = true;
        if ("平均恢复周期".indexOf(val) >= 0) this.filtersearch4 = true;
        if ("最大回撤比".indexOf(val) >= 0) this.filtersearch5 = true;
        if ("在险价值".indexOf(val) >= 0) this.filtersearch6 = true;
        if ("期望损失".indexOf(val) >= 0) this.filtersearch7 = true;
        if ("下行风险".indexOf(val) >= 0) this.filtersearch8 = true;
        if ("波动率比".indexOf(val) >= 0) this.filtersearch9 = true;
        if ("痛苦指数".indexOf(val) >= 0) this.filtersearch10 = true;
        if ("年化收益率".indexOf(val) >= 0) this.filtersearch11 = true;
        if ("累计收益率".indexOf(val) >= 0) this.filtersearch12 = true;
        if ("夏普率（rf=0）".indexOf(val) >= 0) this.filtersearch13 = true;
        if ("夏普率（rf=0.04）".indexOf(val) >= 0) this.filtersearch14 = true;
        if ("夏普率（动态rf）".indexOf(val) >= 0) this.filtersearch15 = true;
        if ("卡码率".indexOf(val) >= 0) this.filtersearch16 = true;
        if ("索提诺系数（rf=0）".indexOf(val) >= 0) this.filtersearch17 = true;
        if ("索提诺系数（rf=0.04）".indexOf(val) >= 0)
          this.filtersearch18 = true;
        if ("索提诺系数（动态rf）".indexOf(val) >= 0)
          this.filtersearch19 = true;
        if ("稳定系数".indexOf(val) >= 0) this.filtersearch20 = true;
        if ("凯利系数".indexOf(val) >= 0) this.filtersearch21 = true;
        if ("信息比率".indexOf(val) >= 0) this.filtersearch22 = true;
        if ("上攻潜力（周）".indexOf(val) >= 0) this.filtersearch23 = true;
        if ("月胜率".indexOf(val) >= 0) this.filtersearch24 = true;
        if ("詹森系数".indexOf(val) >= 0) this.filtersearch25 = true;
        if ("特诺系数".indexOf(val) >= 0) this.filtersearch26 = true;
        if ("上行捕获".indexOf(val) >= 0) this.filtersearch27 = true;
        if ("下行捕获".indexOf(val) >= 0) this.filtersearch28 = true;
        if ("择时gamma".indexOf(val) >= 0) this.filtersearch29 = true;
        if ("M2".indexOf(val) >= 0) this.filtersearch30 = true;
      }
    },

    temp (val) {
      // ////console.log(this.value)
      this.showbodong = false;
      this.showbodong1 = false;
      this.showbodong2 = false;
      this.showbodong3 = false;
      this.showbodong4 = false;
      this.showbodong5 = false;
      this.showbodong6 = false;
      this.showbodong7 = false;
      this.showbodong8 = false;
      this.showbodong9 = false;
      ////console.log(val)
      if (val.indexOf(1) > -1) {
        this.showbodong = true;
        //////console.log('波动率')
      } else {
        this.$set(this.formData.field104[0], "date", ["now", "3y"]);
        this.$set(this.formData.field104[0], "value1", 0);
        this.$set(this.formData.field104[0], "value2", null);
        this.$set(this.formData.field104[0], "rank", "100");
      }
      if (val.indexOf(2) > -1) {
        this.showbodong1 = true;
        //////console.log('波动率')
      } else {
        this.$set(this.formData.field104[1], "date", ["now", "3y"]);
        this.$set(this.formData.field104[1], "value1", 0);
        this.$set(this.formData.field104[1], "value2", null);
        this.$set(this.formData.field104[1], "rank", "100");
      }
      if (val.indexOf(3) > -1) {
        this.showbodong2 = true;
        //////console.log('波动率')
      } else {
        this.$set(this.formData.field104[2], "date", ["now", "3y"]);
        this.$set(this.formData.field104[2], "value1", 0);
        this.$set(this.formData.field104[2], "value2", null);
        this.$set(this.formData.field104[2], "rank", "100");
      }
      if (val.indexOf(4) > -1) {
        this.showbodong3 = true;
        //////console.log('波动率')
      } else {
        this.$set(this.formData.field104[3], "date", ["now", "3y"]);
        this.$set(this.formData.field104[3], "value1", 0);
        this.$set(this.formData.field104[3], "value2", null);
        this.$set(this.formData.field104[3], "rank", "100");
      }
      if (val.indexOf(5) > -1) {
        this.showbodong4 = true;
        //////console.log('波动率')
      } else {
        this.$set(this.formData.field104[4], "date", ["now", "3y"]);
        this.$set(this.formData.field104[4], "value1", 0);
        this.$set(this.formData.field104[4], "value2", null);
        this.$set(this.formData.field104[4], "rank", "100");
      }
      if (val.indexOf(6) > -1) {
        this.showbodong5 = true;
        //////console.log('波动率')
      } else {
        this.$set(this.formData.field104[5], "date", ["now", "3y"]);
        this.$set(this.formData.field104[5], "value1", null);
        this.$set(this.formData.field104[5], "value2", null);
        this.$set(this.formData.field104[5], "rank", "100");
      }
      if (val.indexOf(7) > -1) {
        this.showbodong6 = true;
        //////console.log('波动率')
      } else {
        this.$set(this.formData.field104[6], "date", ["now", "3y"]);
        this.$set(this.formData.field104[6], "value1", null);
        this.$set(this.formData.field104[6], "value2", null);
        this.$set(this.formData.field104[6], "rank", "100");
      }
      if (val.indexOf(8) > -1) {
        this.showbodong7 = true;
        //////console.log('波动率')
      } else {
        this.$set(this.formData.field104[7], "date", ["now", "3y"]);
        this.$set(this.formData.field104[7], "value1", null);
        this.$set(this.formData.field104[7], "value2", null);
        this.$set(this.formData.field104[7], "rank", "100");
      }
      if (val.indexOf(9) > -1) {
        this.showbodong8 = true;
        //////console.log('波动率')
      } else {
        this.$set(this.formData.field104[8], "date", ["now", "3y"]);
        this.$set(this.formData.field104[8], "value1", null);
        this.$set(this.formData.field104[8], "value2", null);
        this.$set(this.formData.field104[8], "rank", "100");
      }
      if (val.indexOf(10) > -1) {
        this.showbodong9 = true;
        // ////console.log('波动率')
      } else {
        this.$set(this.formData.field104[9], "date", ["now", "3y"]);
        this.$set(this.formData.field104[9], "value1", null);
        this.$set(this.formData.field104[9], "rank", "100");
      }
    },
    temp2 (val) {
      this.showfengxian = false;
      this.showfengxian1 = false;
      this.showfengxian2 = false;
      this.showfengxian3 = false;
      this.showfengxian4 = false;
      this.showfengxian5 = false;
      this.showfengxian6 = false;
      this.showfengxian7 = false;
      this.showfengxian8 = false;
      this.showfengxian9 = false;
      this.showfengxian10 = false;
      this.showfengxian11 = false;
      this.showfengxian12 = false;
      this.showfengxian13 = false;
      this.showfengxian14 = false;
      this.showfengxian15 = false;
      this.showfengxian16 = false;
      this.showfengxian17 = false;
      this.showfengxian18 = false;
      this.showfengxian19 = false;
      ////console.log(val)
      if (val.indexOf(1) > -1) {
        this.showfengxian = true;
        //////console.log('波动率')
      } else {
        this.$set(this.formData.field105[0], "date", ["now", "3y"]);
        this.$set(this.formData.field105[0], "value1", null);
        this.$set(this.formData.field105[0], "rank", "100");
      }
      if (val.indexOf(2) > -1) {
        this.showfengxian1 = true;
        //////console.log('波动率')
      } else {
        this.$set(this.formData.field105[1], "date", ["now", "3y"]);
        this.$set(this.formData.field105[1], "value1", null);
        this.$set(this.formData.field105[1], "rank", "100");
      }
      if (val.indexOf(3) > -1) {
        this.showfengxian2 = true;
        //////console.log('波动率')
      } else {
        this.$set(this.formData.field105[2], "date", ["now", "3y"]);
        this.$set(this.formData.field105[2], "value1", null);
        this.$set(this.formData.field105[2], "rank", "100");
      }
      if (val.indexOf(4) > -1) {
        this.showfengxian3 = true;
        //////console.log('波动率')
      } else {
        this.$set(this.formData.field105[3], "date", ["now", "3y"]);
        this.$set(this.formData.field105[3], "value1", null);
        this.$set(this.formData.field105[3], "rank", "100");
      }
      if (val.indexOf(5) > -1) {
        this.showfengxian4 = true;
        //////console.log('波动率')
      } else {
        this.$set(this.formData.field105[4], "date", ["now", "3y"]);
        this.$set(this.formData.field105[4], "value1", null);
        this.$set(this.formData.field105[4], "rank", "100");
      }
      if (val.indexOf(6) > -1) {
        this.showfengxian5 = true;
        //////console.log('波动率')
      } else {
        this.$set(this.formData.field105[5], "date", ["now", "3y"]);
        this.$set(this.formData.field105[5], "value1", null);
        this.$set(this.formData.field105[5], "rank", "100");
      }
      if (val.indexOf(7) > -1) {
        this.showfengxian6 = true;
        //////console.log('波动率')
      } else {
        this.$set(this.formData.field105[6], "date", ["now", "3y"]);
        this.$set(this.formData.field105[6], "value1", null);
        this.$set(this.formData.field105[6], "rank", "100");
      }
      if (val.indexOf(8) > -1) {
        this.showfengxian7 = true;
        //////console.log('波动率')
      } else {
        this.$set(this.formData.field105[7], "date", ["now", "3y"]);
        this.$set(this.formData.field105[7], "value1", null);
        this.$set(this.formData.field105[7], "rank", "100");
      }
      if (val.indexOf(9) > -1) {
        this.showfengxian8 = true;
        //////console.log('波动率')
      } else {
        this.$set(this.formData.field105[8], "date", ["now", "3y"]);
        this.$set(this.formData.field105[8], "value1", null);
        this.$set(this.formData.field105[8], "rank", "100");
      }
      if (val.indexOf(10) > -1) {
        this.showfengxian9 = true;
        // ////console.log('波动率')
      } else {
        this.$set(this.formData.field105[9], "date", ["now", "3y"]);
        this.$set(this.formData.field105[9], "value1", null);
        this.$set(this.formData.field105[9], "rank", "100");
      }

      if (val.indexOf(11) > -1) {
        this.showfengxian10 = true;
        //////console.log('波动率')
      } else {
        this.$set(this.formData.field105[10], "date", ["now", "3y"]);
        this.$set(this.formData.field105[10], "value1", null);
        this.$set(this.formData.field105[10], "rank", "100");
      }
      if (val.indexOf(12) > -1) {
        this.showfengxian11 = true;
        //////console.log('波动率')
      } else {
        this.$set(this.formData.field105[11], "date", ["now", "3y"]);
        this.$set(this.formData.field105[11], "value1", null);
        this.$set(this.formData.field105[11], "rank", "100");
      }
      if (val.indexOf(13) > -1) {
        this.showfengxian12 = true;
        //////console.log('波动率')
      } else {
        this.$set(this.formData.field105[12], "date", ["now", "3y"]);
        this.$set(this.formData.field105[12], "value1", null);
        this.$set(this.formData.field105[12], "rank", "100");
      }
      if (val.indexOf(14) > -1) {
        this.showfengxian13 = true;
        //////console.log('波动率')
      } else {
        this.$set(this.formData.field105[13], "date", ["now", "3y"]);
        this.$set(this.formData.field105[13], "value1", null);
        this.$set(this.formData.field105[13], "rank", "100");
      }
      if (val.indexOf(15) > -1) {
        this.showfengxian14 = true;
        //////console.log('波动率')
      } else {
        this.$set(this.formData.field105[14], "date", ["now", "3y"]);
        this.$set(this.formData.field105[14], "value1", null);
        this.$set(this.formData.field105[14], "rank", "100");
      }
      if (val.indexOf(16) > -1) {
        this.showfengxian15 = true;
        //////console.log('波动率')
      } else {
        this.$set(this.formData.field105[15], "date", ["now", "3y"]);
        this.$set(this.formData.field105[15], "value1", null);
        this.$set(this.formData.field105[15], "rank", "100");
      }
      if (val.indexOf(17) > -1) {
        this.showfengxian16 = true;
        //////console.log('波动率')
      } else {
        this.$set(this.formData.field105[16], "date", ["now", "3y"]);
        this.$set(this.formData.field105[16], "value1", null);
        this.$set(this.formData.field105[16], "rank", "100");
      }
      if (val.indexOf(18) > -1) {
        this.showfengxian17 = true;
        //////console.log('波动率')
      } else {
        this.$set(this.formData.field105[17], "date", ["now", "3y"]);
        this.$set(this.formData.field105[17], "value1", null);
        this.$set(this.formData.field105[17], "rank", "100");
      }
      if (val.indexOf(19) > -1) {
        this.showfengxian18 = true;
        // ////console.log('波动率')
      } else {
        this.$set(this.formData.field105[18], "date", ["now", "3y"]);
        this.$set(this.formData.field105[18], "value1", null);
        this.$set(this.formData.field105[18], "rank", "100");
      }
      if (val.indexOf(20) > -1) {
        this.showfengxian19 = true;
        //////console.log('波动率')
      } else {
        this.$set(this.formData.field105[19], "date", ["now", "3y"]);
        this.$set(this.formData.field105[19], "value1", null);
        this.$set(this.formData.field105[19], "rank", "100");
      }
    },
    temp3 (val) {
      this.showhg = false;
      this.showkc = false;
      this.showej = false;
      if (val.indexOf(1) > -1) {
        this.showhg = true;
      }
      if (val.indexOf(2) > -1) {
        this.showkc = true;
      }
      if (val.indexOf(3) > -1) {
        this.showej = true;
      }
    },
    temp4 (val) {
      this.czshow = false;
      this.dpshow = false;
      if (val.indexOf(1) > -1) {
        this.czshow = true;
      }
      if (val.indexOf(2) > -1) {
        this.dpshow = true;
      }
    },
    temp5 (val) {
      this.swshow = false;
      this.swshow2 = false;
      this.swshow3 = false;
      this.swshow4 = false;
      if (val.indexOf(1) > -1) {
        this.swshow = true;
      }
      if (val.indexOf(2) > -1) {
        this.swshow2 = true;
      }
      if (val.indexOf(3) > -1) {
        this.swshow3 = true;
      }
      if (val.indexOf(4) > -1) {
        this.swshow4 = true;
      }
    }
  },
  filters: {
    fix10Y (value) {
      return (Number(value) / 100000000).toFixed(2);
    },
    fix2pp (value) {
      if (value == "nan" || value == "--" || value == "- -") return "--";
      else return Number(value).toFixed(2) + "%";
    },
    fix6 (value) {
      return value.substring(0, 10);
    },
    fix3 (value) {
      return parseInt(value * 1000) / 1000;
    },
    fix2 (value) {
      if (
        value == "" ||
        value == null ||
        value == "nan" ||
        value == "--" ||
        value == "- -"
      ) {
        return "—";
      } else {
        return (value * 100).toFixed(2);
      }
    },
    fix2p (value) {
      if (
        value == "" ||
        value == null ||
        value == "nan" ||
        value == "--" ||
        value == "- -"
      ) {
        return "—";
      } else {
        return (value * 100).toFixed(2) + "%";
      }
    },
    fixp (value) {
      if (
        value == "" ||
        value == null ||
        value == "nan" ||
        value == "--" ||
        value == "- -"
      ) {
        return "—";
      } else {
        return Number(value).toFixed(2) + "亿";
      }
    },
    isDefault (value) {
      return value == "--" ? "" : value;
    }
  },
  created () {
    this.getmodals();
    this.getdownuplist();
    this.getselectoptions();
    let that = this;
    if (JSON.stringify(this.$route.query) != "{}") {
      if (this.$route.query.args != null) {
        axios
          .get(
            that.$baseUrl + "/HomePage/modelquery/?id=" + this.$route.query.args
          )
          .then(res => {
            // //console.log(res.data)
            // that.flagargs = 1

            that.setshuju(res.data.model_query.model_args);
          })
          .catch(error => {
            //that.$message('数据缺失');
          });
      }
    } else {
      // ////console.log('session')
      // ////console.log(localStorage.getItem('formDataequity'))
      // 获取theme

      if (
        localStorage.getItem("formDatathemevaluemanager" + this.type) != null
      ) {
        this.themevalue = JSON.parse(
          window.localStorage.getItem("formDatathemevaluemanager" + this.type)
        );
      }
      if (
        localStorage.getItem("formDatainsertindexmanager" + this.type) != null
      ) {
        this.insertindex = JSON.parse(
          window.localStorage.getItem("formDatainsertindexmanager" + this.type)
        );
      }
      if (
        localStorage.getItem("formDataquanzhongthememanager" + this.type) !=
        null
      ) {
        this.quanzhongtheme = JSON.parse(
          window.localStorage.getItem(
            "formDataquanzhongthememanager" + this.type
          )
        );
      }
      // endtheme
      // 获取theme
      if (
        localStorage.getItem("formDataswonevaluemanager" + this.type) != null
      ) {
        this.swonevalue = JSON.parse(
          window.localStorage.getItem("formDataswonevaluemanager" + this.type)
        );
      }
      if (
        localStorage.getItem("formDatainsertindexswonemanager" + this.type) !=
        null
      ) {
        this.insertindexswone = JSON.parse(
          window.localStorage.getItem(
            "formDatainsertindexswonemanager" + this.type
          )
        );
      }
      if (
        localStorage.getItem("formDataquanzhongswonemanager" + this.type) !=
        null
      ) {
        this.quanzhongswone = JSON.parse(
          window.localStorage.getItem(
            "formDataquanzhongswonemanager" + this.type
          )
        );
      }
      // endtheme
      // 获取theme
      if (
        localStorage.getItem("formDataswonevalue2manager" + this.type) != null
      ) {
        this.swonevalue2 = JSON.parse(
          window.localStorage.getItem("formDataswonevalue2manager" + this.type)
        );
      }
      if (
        localStorage.getItem("formDatainsertindexswone2manager" + this.type) !=
        null
      ) {
        this.insertindexswone2 = JSON.parse(
          window.localStorage.getItem(
            "formDatainsertindexswone2manager" + this.type
          )
        );
      }
      if (
        localStorage.getItem("formDataquanzhongswone2manager" + this.type) !=
        null
      ) {
        this.quanzhongswone2 = JSON.parse(
          window.localStorage.getItem(
            "formDataquanzhongswone2manager" + this.type
          )
        );
      }
      // endtheme
      // 获取theme
      if (
        localStorage.getItem("formDataswonevalue3manager" + this.type) != null
      ) {
        this.swonevalue3 = JSON.parse(
          window.localStorage.getItem("formDataswonevalue3manager" + this.type)
        );
      }
      if (
        localStorage.getItem("formDatainsertindexswone3manager" + this.type) !=
        null
      ) {
        this.insertindexswone3 = JSON.parse(
          window.localStorage.getItem(
            "formDatainsertindexswone3manager" + this.type
          )
        );
      }
      if (
        localStorage.getItem("formDataquanzhongswone3manager" + this.type) !=
        null
      ) {
        this.quanzhongswone3 = JSON.parse(
          window.localStorage.getItem(
            "formDataquanzhongswone3manager" + this.type
          )
        );
      }
      // endtheme
      // 获取theme
      if (
        localStorage.getItem("formDataswonevalue4manager" + this.type) != null
      ) {
        this.swonevalue4 = JSON.parse(
          window.localStorage.getItem("formDataswonevalue4manager" + this.type)
        );
      }
      if (
        localStorage.getItem("formDatainsertindexswone4manager" + this.type) !=
        null
      ) {
        this.insertindexswone4 = JSON.parse(
          window.localStorage.getItem(
            "formDatainsertindexswone4manager" + this.type
          )
        );
      }
      if (
        localStorage.getItem("formDataquanzhongswone4manager" + this.type) !=
        null
      ) {
        this.quanzhongswone4 = JSON.parse(
          window.localStorage.getItem(
            "formDataquanzhongswone4manager" + this.type
          )
        );
      }
      // endtheme

      if (localStorage.getItem("formDataequitymanager" + this.type) != null) {
        this.formData = JSON.parse(
          window.localStorage.getItem("formDataequitymanager" + this.type)
        );
      }
      if (localStorage.getItem("tempequity1manager" + this.type) != null) {
        this.temp = JSON.parse(
          window.localStorage.getItem("tempequity1manager" + this.type)
        );
      }
      if (localStorage.getItem("tempequity2manager" + this.type) != null) {
        this.temp2 = JSON.parse(
          window.localStorage.getItem("tempequity2manager" + this.type)
        );
      }
      if (localStorage.getItem("tempequity3manager" + this.type) != null) {
        this.temp3 = JSON.parse(
          window.localStorage.getItem("tempequity3manager" + this.type)
        );
      }
      if (localStorage.getItem("tempequity4manager" + this.type) != null) {
        this.temp4 = JSON.parse(
          window.localStorage.getItem("tempequity4manager" + this.type)
        );
      }
      if (localStorage.getItem("tempequity5manager" + this.type) != null) {
        this.temp5 = JSON.parse(
          window.localStorage.getItem("tempequity5manager" + this.type)
        );
      }
      if (localStorage.getItem("hgchooseequitymanager" + this.type) != null) {
        this.hgchoose = JSON.parse(
          window.localStorage.getItem("hgchooseequitymanager" + this.type)
        );
      }
      if (localStorage.getItem("kcchooseequitymanager" + this.type) != null) {
        this.kcchoose = JSON.parse(
          window.localStorage.getItem("kcchooseequitymanager" + this.type)
        );
      }
      if (localStorage.getItem("ejchooseequitymanager" + this.type) != null) {
        this.ejchoose = JSON.parse(
          window.localStorage.getItem("ejchooseequitymanager" + this.type)
        );
      }
      if (localStorage.getItem("czchooseequitymanager" + this.type) != null) {
        this.czchoose = JSON.parse(
          window.localStorage.getItem("czchooseequitymanager" + this.type)
        );
      }
      if (localStorage.getItem("dpchooseequitymanager" + this.type) != null) {
        this.dpchoose = JSON.parse(
          window.localStorage.getItem("dpchooseequitymanager" + this.type)
        );
      }
      if (localStorage.getItem("swchooseequitymanager" + this.type) != null) {
        this.swchoose = JSON.parse(
          window.localStorage.getItem("swchooseequitymanager" + this.type)
        );
      }
      if (localStorage.getItem("alldataequitymanager" + this.type) != null) {
        this.alldata = JSON.parse(
          window.localStorage.getItem("alldataequitymanager" + this.type)
        );
        that.tableData = this.alldata.slice(0, 19);
        that.pageTotal = this.alldata.length;
        that.showsearch = true;
      } else {
        that.showmsgloading = true;
        axios
          .post(that.$baseUrl + "/manageralphascore/", {
            alphachooseflag: false,
            type: this.type
          })
          .then(res => {
            if (res.data.data.length == 0) {
              // this.$message('未筛选出结果');
              that.showmsgloading = false;

              return;
            }
            res.data.data.sort((a, b) => {
              return b.final_rank - a.final_rank;
            });

            that.showmsgloading = false;
            that.alldata = res.data;
            that.tableData = res.data.slice(0, 19);
            that.pageTotal = res.data.length;
          })
          .catch(error => {
            //that.$message('数据缺失');
            that.showmsgloading = false;
          });
      }
    }
    axios
      .get(that.$baseUrl + "/system/alpha/filter_risk_future/")
      .then(res => {
        for (let i = 0; i < res.data.maxdrawdown_ratio.length; i++) {
          if (res.data.maxdrawdown_ratio[i] == "1w")
            that.option5[0].children.push({ value: "1w", label: "一周" });
          if (res.data.maxdrawdown_ratio[i] == "2w")
            that.option5[0].children.push({ value: "2w", label: "两周" });
          if (res.data.maxdrawdown_ratio[i] == "1m")
            that.option5[0].children.push({ value: "1m", label: "一月" });
          if (res.data.maxdrawdown_ratio[i] == "2m")
            that.option5[0].children.push({ value: "2m", label: "两月" });
          if (res.data.maxdrawdown_ratio[i] == "1q")
            that.option5[0].children.push({ value: "1q", label: "一季" });
          if (res.data.maxdrawdown_ratio[i] == "2q")
            that.option5[0].children.push({ value: "2q", label: "两季" });
          if (res.data.maxdrawdown_ratio[i] == "1y")
            that.option5[0].children.push({ value: "1y", label: "一年" });
          if (res.data.maxdrawdown_ratio[i] == "2y")
            that.option5[0].children.push({ value: "2y", label: "两年" });
          if (res.data.maxdrawdown_ratio[i] == "3y")
            that.option5[0].children.push({ value: "3y", label: "三年" });
          if (res.data.maxdrawdown_ratio[i] == "5y")
            that.option5[0].children.push({ value: "5y", label: "五年" });
        }
        for (let i = 0; i < res.data.volatilityratio.length; i++) {
          if (res.data.volatilityratio[i] == "1w")
            that.option9[0].children.push({ value: "1w", label: "一周" });
          if (res.data.volatilityratio[i] == "2w")
            that.option9[0].children.push({ value: "2w", label: "两周" });
          if (res.data.volatilityratio[i] == "1m")
            that.option9[0].children.push({ value: "1m", label: "一月" });
          if (res.data.volatilityratio[i] == "2m")
            that.option9[0].children.push({ value: "2m", label: "两月" });
          if (res.data.volatilityratio[i] == "1q")
            that.option9[0].children.push({ value: "1q", label: "一季" });
          if (res.data.volatilityratio[i] == "2q")
            that.option9[0].children.push({ value: "2q", label: "两季" });
          if (res.data.volatilityratio[i] == "1y")
            that.option9[0].children.push({ value: "1y", label: "一年" });
          if (res.data.volatilityratio[i] == "2y")
            that.option9[0].children.push({ value: "2y", label: "两年" });
          if (res.data.volatilityratio[i] == "3y")
            that.option9[0].children.push({ value: "3y", label: "三年" });
          if (res.data.volatilityratio[i] == "5y")
            that.option9[0].children.push({ value: "5y", label: "五年" });
        }
        for (let i = 0; i < res.data.volatility.length; i++) {
          if (res.data.volatility[i] == "1w")
            that.option[0].children.push({ value: "1w", label: "一周" });
          if (res.data.volatility[i] == "2w")
            that.option[0].children.push({ value: "2w", label: "两周" });
          if (res.data.volatility[i] == "1m")
            that.option[0].children.push({ value: "1m", label: "一月" });
          if (res.data.volatility[i] == "2m")
            that.option[0].children.push({ value: "2m", label: "两月" });
          if (res.data.volatility[i] == "1q")
            that.option[0].children.push({ value: "1q", label: "一季" });
          if (res.data.volatility[i] == "2q")
            that.option[0].children.push({ value: "2q", label: "两季" });
          if (res.data.volatility[i] == "1y")
            that.option[0].children.push({ value: "1y", label: "一年" });
          if (res.data.volatility[i] == "2y")
            that.option[0].children.push({ value: "2y", label: "两年" });
          if (res.data.volatility[i] == "3y")
            that.option[0].children.push({ value: "3y", label: "三年" });
          if (res.data.volatility[i] == "5y")
            that.option[0].children.push({ value: "5y", label: "五年" });
        }
        for (let i = 0; i < res.data.maxdrawdown.length; i++) {
          if (res.data.maxdrawdown[i] == "1w")
            that.option2[0].children.push({ value: "1w", label: "一周" });
          if (res.data.maxdrawdown[i] == "2w")
            that.option2[0].children.push({ value: "2w", label: "两周" });
          if (res.data.maxdrawdown[i] == "1m")
            that.option2[0].children.push({ value: "1m", label: "一月" });
          if (res.data.maxdrawdown[i] == "2m")
            that.option2[0].children.push({ value: "2m", label: "两月" });
          if (res.data.maxdrawdown[i] == "1q")
            that.option2[0].children.push({ value: "1q", label: "一季" });
          if (res.data.maxdrawdown[i] == "2q")
            that.option2[0].children.push({ value: "2q", label: "两季" });
          if (res.data.maxdrawdown[i] == "1y")
            that.option2[0].children.push({ value: "1y", label: "一年" });
          if (res.data.maxdrawdown[i] == "2y")
            that.option2[0].children.push({ value: "2y", label: "两年" });
          if (res.data.maxdrawdown[i] == "3y")
            that.option2[0].children.push({ value: "3y", label: "三年" });
          if (res.data.maxdrawdown[i] == "5y")
            that.option2[0].children.push({ value: "5y", label: "五年" });
        }
        for (let i = 0; i < res.data.averagelength.length; i++) {
          if (res.data.averagelength[i] == "1w")
            that.option3[0].children.push({ value: "1w", label: "一周" });
          if (res.data.averagelength[i] == "2w")
            that.option3[0].children.push({ value: "2w", label: "两周" });
          if (res.data.averagelength[i] == "1m")
            that.option3[0].children.push({ value: "1m", label: "一月" });
          if (res.data.averagelength[i] == "2m")
            that.option3[0].children.push({ value: "2m", label: "两月" });
          if (res.data.averagelength[i] == "1q")
            that.option3[0].children.push({ value: "1q", label: "一季" });
          if (res.data.averagelength[i] == "2q")
            that.option3[0].children.push({ value: "2q", label: "两季" });
          if (res.data.averagelength[i] == "1y")
            that.option3[0].children.push({ value: "1y", label: "一年" });
          if (res.data.averagelength[i] == "2y")
            that.option3[0].children.push({ value: "2y", label: "两年" });
          if (res.data.averagelength[i] == "3y")
            that.option3[0].children.push({ value: "3y", label: "三年" });
          if (res.data.averagelength[i] == "5y")
            that.option3[0].children.push({ value: "5y", label: "五年" });
        }
        for (let i = 0; i < res.data.averagerecovery.length; i++) {
          if (res.data.averagerecovery[i] == "1w")
            that.option4[0].children.push({ value: "1w", label: "一周" });
          if (res.data.averagerecovery[i] == "2w")
            that.option4[0].children.push({ value: "2w", label: "两周" });
          if (res.data.averagerecovery[i] == "1m")
            that.option4[0].children.push({ value: "1m", label: "一月" });
          if (res.data.averagerecovery[i] == "2m")
            that.option4[0].children.push({ value: "2m", label: "两月" });
          if (res.data.averagerecovery[i] == "1q")
            that.option4[0].children.push({ value: "1q", label: "一季" });
          if (res.data.averagerecovery[i] == "2q")
            that.option4[0].children.push({ value: "2q", label: "两季" });
          if (res.data.averagerecovery[i] == "1y")
            that.option4[0].children.push({ value: "1y", label: "一年" });
          if (res.data.averagerecovery[i] == "2y")
            that.option4[0].children.push({ value: "2y", label: "两年" });
          if (res.data.averagerecovery[i] == "3y")
            that.option4[0].children.push({ value: "3y", label: "三年" });
          if (res.data.averagerecovery[i] == "5y")
            that.option4[0].children.push({ value: "5y", label: "五年" });
        }

        for (let i = 0; i < res.data.volatility.length; i++) {
          if (res.data.VaR05[i] == "1w")
            that.option6[0].children.push({ value: "1w", label: "一周" });
          if (res.data.VaR05[i] == "2w")
            that.option6[0].children.push({ value: "2w", label: "两周" });
          if (res.data.VaR05[i] == "1m")
            that.option6[0].children.push({ value: "1m", label: "一月" });
          if (res.data.VaR05[i] == "2m")
            that.option6[0].children.push({ value: "2m", label: "两月" });
          if (res.data.VaR05[i] == "1q")
            that.option6[0].children.push({ value: "1q", label: "一季" });
          if (res.data.VaR05[i] == "2q")
            that.option6[0].children.push({ value: "2q", label: "两季" });
          if (res.data.VaR05[i] == "1y")
            that.option6[0].children.push({ value: "1y", label: "一年" });
          if (res.data.VaR05[i] == "2y")
            that.option6[0].children.push({ value: "2y", label: "两年" });
          if (res.data.VaR05[i] == "3y")
            that.option6[0].children.push({ value: "3y", label: "三年" });
          if (res.data.VaR05[i] == "5y")
            that.option6[0].children.push({ value: "5y", label: "五年" });
        }
        for (let i = 0; i < res.data.ES05.length; i++) {
          if (res.data.ES05[i] == "1w")
            that.option7[0].children.push({ value: "1w", label: "一周" });
          if (res.data.ES05[i] == "2w")
            that.option7[0].children.push({ value: "2w", label: "两周" });
          if (res.data.ES05[i] == "1m")
            that.option7[0].children.push({ value: "1m", label: "一月" });
          if (res.data.ES05[i] == "2m")
            that.option7[0].children.push({ value: "2m", label: "两月" });
          if (res.data.ES05[i] == "1q")
            that.option7[0].children.push({ value: "1q", label: "一季" });
          if (res.data.ES05[i] == "2q")
            that.option7[0].children.push({ value: "2q", label: "两季" });
          if (res.data.ES05[i] == "1y")
            that.option7[0].children.push({ value: "1y", label: "一年" });
          if (res.data.ES05[i] == "2y")
            that.option7[0].children.push({ value: "2y", label: "两年" });
          if (res.data.ES05[i] == "3y")
            that.option7[0].children.push({ value: "3y", label: "三年" });
          if (res.data.ES05[i] == "5y")
            that.option7[0].children.push({ value: "5y", label: "五年" });
        }
        for (let i = 0; i < res.data.downsidevolatility.length; i++) {
          if (res.data.downsidevolatility[i] == "1w")
            that.option8[0].children.push({ value: "1w", label: "一周" });
          if (res.data.downsidevolatility[i] == "2w")
            that.option8[0].children.push({ value: "2w", label: "两周" });
          if (res.data.downsidevolatility[i] == "1m")
            that.option8[0].children.push({ value: "1m", label: "一月" });
          if (res.data.downsidevolatility[i] == "2m")
            that.option8[0].children.push({ value: "2m", label: "两月" });
          if (res.data.downsidevolatility[i] == "1q")
            that.option8[0].children.push({ value: "1q", label: "一季" });
          if (res.data.downsidevolatility[i] == "2q")
            that.option8[0].children.push({ value: "2q", label: "两季" });
          if (res.data.downsidevolatility[i] == "1y")
            that.option8[0].children.push({ value: "1y", label: "一年" });
          if (res.data.downsidevolatility[i] == "2y")
            that.option8[0].children.push({ value: "2y", label: "两年" });
          if (res.data.downsidevolatility[i] == "3y")
            that.option8[0].children.push({ value: "3y", label: "三年" });
          if (res.data.downsidevolatility[i] == "5y")
            that.option8[0].children.push({ value: "5y", label: "五年" });
        }

        for (let i = 0; i < res.data.painindex.length; i++) {
          if (res.data.painindex[i] == "1w")
            that.option10[0].children.push({ value: "1w", label: "一周" });
          if (res.data.painindex[i] == "2w")
            that.option10[0].children.push({ value: "2w", label: "两周" });
          if (res.data.painindex[i] == "1m")
            that.option10[0].children.push({ value: "1m", label: "一月" });
          if (res.data.painindex[i] == "2m")
            that.option10[0].children.push({ value: "2m", label: "两月" });
          if (res.data.painindex[i] == "1q")
            that.option10[0].children.push({ value: "1q", label: "一季" });
          if (res.data.painindex[i] == "2q")
            that.option10[0].children.push({ value: "2q", label: "两季" });
          if (res.data.painindex[i] == "1y")
            that.option10[0].children.push({ value: "1y", label: "一年" });
          if (res.data.painindex[i] == "2y")
            that.option10[0].children.push({ value: "2y", label: "两年" });
          if (res.data.painindex[i] == "3y")
            that.option10[0].children.push({ value: "3y", label: "三年" });
          if (res.data.painindex[i] == "5y")
            that.option10[0].children.push({ value: "5y", label: "五年" });
        }
        for (let i = 0; i < res.data.ave_return.length; i++) {
          if (res.data.ave_return[i] == "1w")
            that.option11[0].children.push({ value: "1w", label: "一周" });
          if (res.data.ave_return[i] == "2w")
            that.option11[0].children.push({ value: "2w", label: "两周" });
          if (res.data.ave_return[i] == "1m")
            that.option11[0].children.push({ value: "1m", label: "一月" });
          if (res.data.ave_return[i] == "2m")
            that.option11[0].children.push({ value: "2m", label: "两月" });
          if (res.data.ave_return[i] == "1q")
            that.option11[0].children.push({ value: "1q", label: "一季" });
          if (res.data.ave_return[i] == "2q")
            that.option11[0].children.push({ value: "2q", label: "两季" });
          if (res.data.ave_return[i] == "1y")
            that.option11[0].children.push({ value: "1y", label: "一年" });
          if (res.data.ave_return[i] == "2y")
            that.option11[0].children.push({ value: "2y", label: "两年" });
          if (res.data.ave_return[i] == "3y")
            that.option11[0].children.push({ value: "3y", label: "三年" });
          if (res.data.ave_return[i] == "5y")
            that.option11[0].children.push({ value: "5y", label: "五年" });
        }
        for (let i = 0; i < res.data.cum_return.length; i++) {
          if (res.data.cum_return[i] == "1w")
            that.option12[0].children.push({ value: "1w", label: "一周" });
          if (res.data.cum_return[i] == "2w")
            that.option12[0].children.push({ value: "2w", label: "两周" });
          if (res.data.cum_return[i] == "1m")
            that.option12[0].children.push({ value: "1m", label: "一月" });
          if (res.data.cum_return[i] == "2m")
            that.option12[0].children.push({ value: "2m", label: "两月" });
          if (res.data.cum_return[i] == "1q")
            that.option12[0].children.push({ value: "1q", label: "一季" });
          if (res.data.cum_return[i] == "2q")
            that.option12[0].children.push({ value: "2q", label: "两季" });
          if (res.data.cum_return[i] == "1y")
            that.option12[0].children.push({ value: "1y", label: "一年" });
          if (res.data.cum_return[i] == "2y")
            that.option12[0].children.push({ value: "2y", label: "两年" });
          if (res.data.cum_return[i] == "3y")
            that.option12[0].children.push({ value: "3y", label: "三年" });
          if (res.data.cum_return[i] == "5y")
            that.option12[0].children.push({ value: "5y", label: "五年" });
        }
        for (let i = 0; i < res.data.sharpe0.length; i++) {
          if (res.data.sharpe0[i] == "1w")
            that.option13[0].children.push({ value: "1w", label: "一周" });
          if (res.data.sharpe0[i] == "2w")
            that.option13[0].children.push({ value: "2w", label: "两周" });
          if (res.data.sharpe0[i] == "1m")
            that.option13[0].children.push({ value: "1m", label: "一月" });
          if (res.data.sharpe0[i] == "2m")
            that.option13[0].children.push({ value: "2m", label: "两月" });
          if (res.data.sharpe0[i] == "1q")
            that.option13[0].children.push({ value: "1q", label: "一季" });
          if (res.data.sharpe0[i] == "2q")
            that.option13[0].children.push({ value: "2q", label: "两季" });
          if (res.data.sharpe0[i] == "1y")
            that.option13[0].children.push({ value: "1y", label: "一年" });
          if (res.data.sharpe0[i] == "2y")
            that.option13[0].children.push({ value: "2y", label: "两年" });
          if (res.data.sharpe0[i] == "3y")
            that.option13[0].children.push({ value: "3y", label: "三年" });
          if (res.data.sharpe0[i] == "5y")
            that.option13[0].children.push({ value: "5y", label: "五年" });
        }
        for (let i = 0; i < res.data.sharpe04.length; i++) {
          if (res.data.sharpe04[i] == "1w")
            that.option14[0].children.push({ value: "1w", label: "一周" });
          if (res.data.sharpe04[i] == "2w")
            that.option14[0].children.push({ value: "2w", label: "两周" });
          if (res.data.sharpe04[i] == "1m")
            that.option14[0].children.push({ value: "1m", label: "一月" });
          if (res.data.sharpe04[i] == "2m")
            that.option14[0].children.push({ value: "2m", label: "两月" });
          if (res.data.sharpe04[i] == "1q")
            that.option14[0].children.push({ value: "1q", label: "一季" });
          if (res.data.sharpe04[i] == "2q")
            that.option14[0].children.push({ value: "2q", label: "两季" });
          if (res.data.sharpe04[i] == "1y")
            that.option14[0].children.push({ value: "1y", label: "一年" });
          if (res.data.sharpe04[i] == "2y")
            that.option14[0].children.push({ value: "2y", label: "两年" });
          if (res.data.sharpe04[i] == "3y")
            that.option14[0].children.push({ value: "3y", label: "三年" });
          if (res.data.sharpe04[i] == "5y")
            that.option14[0].children.push({ value: "5y", label: "五年" });
        }
        for (let i = 0; i < res.data.sharpe.length; i++) {
          if (res.data.sharpe[i] == "1w")
            that.option15[0].children.push({ value: "1w", label: "一周" });
          if (res.data.sharpe[i] == "2w")
            that.option15[0].children.push({ value: "2w", label: "两周" });
          if (res.data.sharpe[i] == "1m")
            that.option15[0].children.push({ value: "1m", label: "一月" });
          if (res.data.sharpe[i] == "2m")
            that.option15[0].children.push({ value: "2m", label: "两月" });
          if (res.data.sharpe[i] == "1q")
            that.option15[0].children.push({ value: "1q", label: "一季" });
          if (res.data.sharpe[i] == "2q")
            that.option15[0].children.push({ value: "2q", label: "两季" });
          if (res.data.sharpe[i] == "1y")
            that.option15[0].children.push({ value: "1y", label: "一年" });
          if (res.data.sharpe[i] == "2y")
            that.option15[0].children.push({ value: "2y", label: "两年" });
          if (res.data.sharpe[i] == "3y")
            that.option15[0].children.push({ value: "3y", label: "三年" });
          if (res.data.sharpe[i] == "5y")
            that.option15[0].children.push({ value: "5y", label: "五年" });
        }
        for (let i = 0; i < res.data.calmar.length; i++) {
          if (res.data.calmar[i] == "1w")
            that.option16[0].children.push({ value: "1w", label: "一周" });
          if (res.data.calmar[i] == "2w")
            that.option16[0].children.push({ value: "2w", label: "两周" });
          if (res.data.calmar[i] == "1m")
            that.option16[0].children.push({ value: "1m", label: "一月" });
          if (res.data.calmar[i] == "2m")
            that.option16[0].children.push({ value: "2m", label: "两月" });
          if (res.data.calmar[i] == "1q")
            that.option16[0].children.push({ value: "1q", label: "一季" });
          if (res.data.calmar[i] == "2q")
            that.option16[0].children.push({ value: "2q", label: "两季" });
          if (res.data.calmar[i] == "1y")
            that.option16[0].children.push({ value: "1y", label: "一年" });
          if (res.data.calmar[i] == "2y")
            that.option16[0].children.push({ value: "2y", label: "两年" });
          if (res.data.calmar[i] == "3y")
            that.option16[0].children.push({ value: "3y", label: "三年" });
          if (res.data.calmar[i] == "5y")
            that.option16[0].children.push({ value: "5y", label: "五年" });
        }
        for (let i = 0; i < res.data.sortino0.length; i++) {
          if (res.data.sortino0[i] == "1w")
            that.option17[0].children.push({ value: "1w", label: "一周" });
          if (res.data.sortino0[i] == "2w")
            that.option17[0].children.push({ value: "2w", label: "两周" });
          if (res.data.sortino0[i] == "1m")
            that.option17[0].children.push({ value: "1m", label: "一月" });
          if (res.data.sortino0[i] == "2m")
            that.option17[0].children.push({ value: "2m", label: "两月" });
          if (res.data.sortino0[i] == "1q")
            that.option17[0].children.push({ value: "1q", label: "一季" });
          if (res.data.sortino0[i] == "2q")
            that.option17[0].children.push({ value: "2q", label: "两季" });
          if (res.data.sortino0[i] == "1y")
            that.option17[0].children.push({ value: "1y", label: "一年" });
          if (res.data.sortino0[i] == "2y")
            that.option17[0].children.push({ value: "2y", label: "两年" });
          if (res.data.sortino0[i] == "3y")
            that.option17[0].children.push({ value: "3y", label: "三年" });
          if (res.data.sortino0[i] == "5y")
            that.option17[0].children.push({ value: "5y", label: "五年" });
        }
        for (let i = 0; i < res.data.sortino04.length; i++) {
          if (res.data.sortino04[i] == "1w")
            that.option18[0].children.push({ value: "1w", label: "一周" });
          if (res.data.sortino04[i] == "2w")
            that.option18[0].children.push({ value: "2w", label: "两周" });
          if (res.data.sortino04[i] == "1m")
            that.option18[0].children.push({ value: "1m", label: "一月" });
          if (res.data.sortino04[i] == "2m")
            that.option18[0].children.push({ value: "2m", label: "两月" });
          if (res.data.sortino04[i] == "1q")
            that.option18[0].children.push({ value: "1q", label: "一季" });
          if (res.data.sortino04[i] == "2q")
            that.option18[0].children.push({ value: "2q", label: "两季" });
          if (res.data.sortino04[i] == "1y")
            that.option18[0].children.push({ value: "1y", label: "一年" });
          if (res.data.sortino04[i] == "2y")
            that.option18[0].children.push({ value: "2y", label: "两年" });
          if (res.data.sortino04[i] == "3y")
            that.option18[0].children.push({ value: "3y", label: "三年" });
          if (res.data.sortino04[i] == "5y")
            that.option18[0].children.push({ value: "5y", label: "五年" });
        }
        for (let i = 0; i < res.data.sortino.length; i++) {
          if (res.data.sortino[i] == "1w")
            that.option19[0].children.push({ value: "1w", label: "一周" });
          if (res.data.sortino[i] == "2w")
            that.option19[0].children.push({ value: "2w", label: "两周" });
          if (res.data.sortino[i] == "1m")
            that.option19[0].children.push({ value: "1m", label: "一月" });
          if (res.data.sortino[i] == "2m")
            that.option19[0].children.push({ value: "2m", label: "两月" });
          if (res.data.sortino[i] == "1q")
            that.option19[0].children.push({ value: "1q", label: "一季" });
          if (res.data.sortino[i] == "2q")
            that.option19[0].children.push({ value: "2q", label: "两季" });
          if (res.data.sortino[i] == "1y")
            that.option19[0].children.push({ value: "1y", label: "一年" });
          if (res.data.sortino[i] == "2y")
            that.option19[0].children.push({ value: "2y", label: "两年" });
          if (res.data.sortino[i] == "3y")
            that.option19[0].children.push({ value: "3y", label: "三年" });
          if (res.data.sortino[i] == "5y")
            that.option19[0].children.push({ value: "5y", label: "五年" });
        }
        for (let i = 0; i < res.data.hurstindex.length; i++) {
          if (res.data.hurstindex[i] == "1w")
            that.option20[0].children.push({ value: "1w", label: "一周" });
          if (res.data.hurstindex[i] == "2w")
            that.option20[0].children.push({ value: "2w", label: "两周" });
          if (res.data.hurstindex[i] == "1m")
            that.option20[0].children.push({ value: "1m", label: "一月" });
          if (res.data.hurstindex[i] == "2m")
            that.option20[0].children.push({ value: "2m", label: "两月" });
          if (res.data.hurstindex[i] == "1q")
            that.option20[0].children.push({ value: "1q", label: "一季" });
          if (res.data.hurstindex[i] == "2q")
            that.option20[0].children.push({ value: "2q", label: "两季" });
          if (res.data.hurstindex[i] == "1y")
            that.option20[0].children.push({ value: "1y", label: "一年" });
          if (res.data.hurstindex[i] == "2y")
            that.option20[0].children.push({ value: "2y", label: "两年" });
          if (res.data.hurstindex[i] == "3y")
            that.option20[0].children.push({ value: "3y", label: "三年" });
          if (res.data.hurstindex[i] == "5y")
            that.option20[0].children.push({ value: "5y", label: "五年" });
        }
        for (let i = 0; i < res.data.kelly.length; i++) {
          if (res.data.kelly[i] == "1w")
            that.option21[0].children.push({ value: "1w", label: "一周" });
          if (res.data.kelly[i] == "2w")
            that.option21[0].children.push({ value: "2w", label: "两周" });
          if (res.data.kelly[i] == "1m")
            that.option21[0].children.push({ value: "1m", label: "一月" });
          if (res.data.kelly[i] == "2m")
            that.option21[0].children.push({ value: "2m", label: "两月" });
          if (res.data.kelly[i] == "1q")
            that.option21[0].children.push({ value: "1q", label: "一季" });
          if (res.data.kelly[i] == "2q")
            that.option21[0].children.push({ value: "2q", label: "两季" });
          if (res.data.kelly[i] == "1y")
            that.option21[0].children.push({ value: "1y", label: "一年" });
          if (res.data.kelly[i] == "2y")
            that.option21[0].children.push({ value: "2y", label: "两年" });
          if (res.data.kelly[i] == "3y")
            that.option21[0].children.push({ value: "3y", label: "三年" });
          if (res.data.kelly[i] == "5y")
            that.option21[0].children.push({ value: "5y", label: "五年" });
        }
        for (let i = 0; i < res.data.information.length; i++) {
          if (res.data.information[i] == "1w")
            that.option22[0].children.push({ value: "1w", label: "一周" });
          if (res.data.information[i] == "2w")
            that.option22[0].children.push({ value: "2w", label: "两周" });
          if (res.data.information[i] == "1m")
            that.option22[0].children.push({ value: "1m", label: "一月" });
          if (res.data.information[i] == "2m")
            that.option22[0].children.push({ value: "2m", label: "两月" });
          if (res.data.information[i] == "1q")
            that.option22[0].children.push({ value: "1q", label: "一季" });
          if (res.data.information[i] == "2q")
            that.option22[0].children.push({ value: "2q", label: "两季" });
          if (res.data.information[i] == "1y")
            that.option22[0].children.push({ value: "1y", label: "一年" });
          if (res.data.information[i] == "2y")
            that.option22[0].children.push({ value: "2y", label: "两年" });
          if (res.data.information[i] == "3y")
            that.option22[0].children.push({ value: "3y", label: "三年" });
          if (res.data.information[i] == "5y")
            that.option22[0].children.push({ value: "5y", label: "五年" });
        }
        for (let i = 0; i < res.data.upsidepotential.length; i++) {
          if (res.data.upsidepotential[i] == "1w")
            that.option23[0].children.push({ value: "1w", label: "一周" });
          if (res.data.upsidepotential[i] == "2w")
            that.option23[0].children.push({ value: "2w", label: "两周" });
          if (res.data.upsidepotential[i] == "1m")
            that.option23[0].children.push({ value: "1m", label: "一月" });
          if (res.data.upsidepotential[i] == "2m")
            that.option23[0].children.push({ value: "2m", label: "两月" });
          if (res.data.upsidepotential[i] == "1q")
            that.option23[0].children.push({ value: "1q", label: "一季" });
          if (res.data.upsidepotential[i] == "2q")
            that.option23[0].children.push({ value: "2q", label: "两季" });
          if (res.data.upsidepotential[i] == "1y")
            that.option23[0].children.push({ value: "1y", label: "一年" });
          if (res.data.upsidepotential[i] == "2y")
            that.option23[0].children.push({ value: "2y", label: "两年" });
          if (res.data.upsidepotential[i] == "3y")
            that.option23[0].children.push({ value: "3y", label: "三年" });
          if (res.data.upsidepotential[i] == "5y")
            that.option23[0].children.push({ value: "5y", label: "五年" });
        }
        for (let i = 0; i < res.data.monthly_win_ratio.length; i++) {
          if (res.data.monthly_win_ratio[i] == "1w")
            that.option24[0].children.push({ value: "1w", label: "一周" });
          if (res.data.monthly_win_ratio[i] == "2w")
            that.option24[0].children.push({ value: "2w", label: "两周" });
          if (res.data.monthly_win_ratio[i] == "1m")
            that.option24[0].children.push({ value: "1m", label: "一月" });
          if (res.data.monthly_win_ratio[i] == "2m")
            that.option24[0].children.push({ value: "2m", label: "两月" });
          if (res.data.monthly_win_ratio[i] == "1q")
            that.option24[0].children.push({ value: "1q", label: "一季" });
          if (res.data.monthly_win_ratio[i] == "2q")
            that.option24[0].children.push({ value: "2q", label: "两季" });
          if (res.data.monthly_win_ratio[i] == "1y")
            that.option24[0].children.push({ value: "1y", label: "一年" });
          if (res.data.monthly_win_ratio[i] == "2y")
            that.option24[0].children.push({ value: "2y", label: "两年" });
          if (res.data.monthly_win_ratio[i] == "3y")
            that.option24[0].children.push({ value: "3y", label: "三年" });
          if (res.data.monthly_win_ratio[i] == "5y")
            that.option24[0].children.push({ value: "5y", label: "五年" });
        }
        for (let i = 0; i < res.data.jensen.length; i++) {
          if (res.data.jensen[i] == "1w")
            that.option25[0].children.push({ value: "1w", label: "一周" });
          if (res.data.jensen[i] == "2w")
            that.option25[0].children.push({ value: "2w", label: "两周" });
          if (res.data.jensen[i] == "1m")
            that.option25[0].children.push({ value: "1m", label: "一月" });
          if (res.data.jensen[i] == "2m")
            that.option25[0].children.push({ value: "2m", label: "两月" });
          if (res.data.jensen[i] == "1q")
            that.option25[0].children.push({ value: "1q", label: "一季" });
          if (res.data.jensen[i] == "2q")
            that.option25[0].children.push({ value: "2q", label: "两季" });
          if (res.data.jensen[i] == "1y")
            that.option25[0].children.push({ value: "1y", label: "一年" });
          if (res.data.jensen[i] == "2y")
            that.option25[0].children.push({ value: "2y", label: "两年" });
          if (res.data.jensen[i] == "3y")
            that.option25[0].children.push({ value: "3y", label: "三年" });
          if (res.data.jensen[i] == "5y")
            that.option25[0].children.push({ value: "5y", label: "五年" });
        }
        for (let i = 0; i < res.data.treynor.length; i++) {
          if (res.data.treynor[i] == "1w")
            that.option26[0].children.push({ value: "1w", label: "一周" });
          if (res.data.treynor[i] == "2w")
            that.option26[0].children.push({ value: "2w", label: "两周" });
          if (res.data.treynor[i] == "1m")
            that.option26[0].children.push({ value: "1m", label: "一月" });
          if (res.data.treynor[i] == "2m")
            that.option26[0].children.push({ value: "2m", label: "两月" });
          if (res.data.treynor[i] == "1q")
            that.option26[0].children.push({ value: "1q", label: "一季" });
          if (res.data.treynor[i] == "2q")
            that.option26[0].children.push({ value: "2q", label: "两季" });
          if (res.data.treynor[i] == "1y")
            that.option26[0].children.push({ value: "1y", label: "一年" });
          if (res.data.treynor[i] == "2y")
            that.option26[0].children.push({ value: "2y", label: "两年" });
          if (res.data.treynor[i] == "3y")
            that.option26[0].children.push({ value: "3y", label: "三年" });
          if (res.data.treynor[i] == "5y")
            that.option26[0].children.push({ value: "5y", label: "五年" });
        }
        for (let i = 0; i < res.data.bullreturn.length; i++) {
          if (res.data.bullreturn[i] == "1w")
            that.option27[0].children.push({ value: "1w", label: "一周" });
          if (res.data.bullreturn[i] == "2w")
            that.option27[0].children.push({ value: "2w", label: "两周" });
          if (res.data.bullreturn[i] == "1m")
            that.option27[0].children.push({ value: "1m", label: "一月" });
          if (res.data.bullreturn[i] == "2m")
            that.option27[0].children.push({ value: "2m", label: "两月" });
          if (res.data.bullreturn[i] == "1q")
            that.option27[0].children.push({ value: "1q", label: "一季" });
          if (res.data.bullreturn[i] == "2q")
            that.option27[0].children.push({ value: "2q", label: "两季" });
          if (res.data.bullreturn[i] == "1y")
            that.option27[0].children.push({ value: "1y", label: "一年" });
          if (res.data.bullreturn[i] == "2y")
            that.option27[0].children.push({ value: "2y", label: "两年" });
          if (res.data.bullreturn[i] == "3y")
            that.option27[0].children.push({ value: "3y", label: "三年" });
          if (res.data.bullreturn[i] == "5y")
            that.option27[0].children.push({ value: "5y", label: "五年" });
        }
        for (let i = 0; i < res.data.bearreturn.length; i++) {
          if (res.data.bearreturn[i] == "1w")
            that.option28[0].children.push({ value: "1w", label: "一周" });
          if (res.data.bearreturn[i] == "2w")
            that.option28[0].children.push({ value: "2w", label: "两周" });
          if (res.data.bearreturn[i] == "1m")
            that.option28[0].children.push({ value: "1m", label: "一月" });
          if (res.data.bearreturn[i] == "2m")
            that.option28[0].children.push({ value: "2m", label: "两月" });
          if (res.data.bearreturn[i] == "1q")
            that.option28[0].children.push({ value: "1q", label: "一季" });
          if (res.data.bearreturn[i] == "2q")
            that.option28[0].children.push({ value: "2q", label: "两季" });
          if (res.data.bearreturn[i] == "1y")
            that.option28[0].children.push({ value: "1y", label: "一年" });
          if (res.data.bearreturn[i] == "2y")
            that.option28[0].children.push({ value: "2y", label: "两年" });
          if (res.data.bearreturn[i] == "3y")
            that.option28[0].children.push({ value: "3y", label: "三年" });
          if (res.data.bearreturn[i] == "5y")
            that.option28[0].children.push({ value: "5y", label: "五年" });
        }
        for (let i = 0; i < res.data.gamma.length; i++) {
          if (res.data.gamma[i] == "1w")
            that.option29[0].children.push({ value: "1w", label: "一周" });
          if (res.data.gamma[i] == "2w")
            that.option29[0].children.push({ value: "2w", label: "两周" });
          if (res.data.gamma[i] == "1m")
            that.option29[0].children.push({ value: "1m", label: "一月" });
          if (res.data.gamma[i] == "2m")
            that.option29[0].children.push({ value: "2m", label: "两月" });
          if (res.data.gamma[i] == "1q")
            that.option29[0].children.push({ value: "1q", label: "一季" });
          if (res.data.gamma[i] == "2q")
            that.option29[0].children.push({ value: "2q", label: "两季" });
          if (res.data.gamma[i] == "1y")
            that.option29[0].children.push({ value: "1y", label: "一年" });
          if (res.data.gamma[i] == "2y")
            that.option29[0].children.push({ value: "2y", label: "两年" });
          if (res.data.gamma[i] == "3y")
            that.option29[0].children.push({ value: "3y", label: "三年" });
          if (res.data.gamma[i] == "5y")
            that.option29[0].children.push({ value: "5y", label: "五年" });
        }
        for (let i = 0; i < res.data.msquared.length; i++) {
          if (res.data.msquared[i] == "1w")
            that.option30[0].children.push({ value: "1w", label: "一周" });
          if (res.data.msquared[i] == "2w")
            that.option30[0].children.push({ value: "2w", label: "两周" });
          if (res.data.msquared[i] == "1m")
            that.option30[0].children.push({ value: "1m", label: "一月" });
          if (res.data.msquared[i] == "2m")
            that.option30[0].children.push({ value: "2m", label: "两月" });
          if (res.data.msquared[i] == "1q")
            that.option30[0].children.push({ value: "1q", label: "一季" });
          if (res.data.msquared[i] == "2q")
            that.option30[0].children.push({ value: "2q", label: "两季" });
          if (res.data.msquared[i] == "1y")
            that.option30[0].children.push({ value: "1y", label: "一年" });
          if (res.data.msquared[i] == "2y")
            that.option30[0].children.push({ value: "2y", label: "两年" });
          if (res.data.msquared[i] == "3y")
            that.option30[0].children.push({ value: "3y", label: "三年" });
          if (res.data.msquared[i] == "5y")
            that.option30[0].children.push({ value: "5y", label: "五年" });
        }
      })
      .catch(err => { });
  },

  mounted () { },
  methods: {
    changeTab (type) {
      this.tableData = [];
      this.alldata = [];
      this.pageTotal = 0;
    },
    handleClose2 (tag) {
      this.formData.index_code.splice(this.formData.index_code.indexOf(tag), 1);
    },
    showInput2 () {
      this.inputVisible2 = true;
      this.$nextTick(() => {
        this.$refs.saveTagInput2.$refs.input.focus();
      });
    },
    async querySearch2 (queryString, cb) {
      let data = await Search({
        message: queryString,
        flag: "6"
      });
      let temparr = [];
      if (data) {
        for (let i = 0; i < data.length; i++) {
          if (data[i].flag == "index") {
            temparr.push(data[i]);
            let temp = "";
            temp = temp + data[i].code + "_" + data[i].name;
            if (data[i].start_from != "--") {
              temp = temp + "_" + data[i].start_from;
            }
            temparr[temparr.length - 1]["value"] = temp;
          }
        }
      }

      var restaurants = temparr;
      // var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
      // console.log(restaurants);
      // 调用 callback 返回建议列表的数据
      cb(restaurants);
    },
    handleInputConfirm2 (e) {
      // console.log(e);
      let inputValue = this.inputValue2;
      if (inputValue) {
        this.formData.index_code.push({ value: e.code, label: e.name });
      }
      this.inputVisible2 = false;
      this.inputValue2 = "";
    },
    gotoshowpdf () {
      this.$refs.equitypdf.showpdfs();
    },
    changepool (val) {
      this.poollist = val;
    },
    getRowKey: function (row) {
      return row.code;
    },
    handleSelectzuhe (val) {
      if (val.length > 0) {
        this.disabledfalg = false;
      } else {
        this.disabledfalg = true;
      }

      this.fundlisttemp = val;
    },
    cretezuhequ () {
      // //console.log('22321')
      this.addzuheflag = true;
      this.fundlist = this.tablelistzuhe;
      this.dataobj = {
        clicktype: true,
        ftype: "equity",
        ismanager: true,
        formData: this.formData,
        temp: this.temp,
        temp2: this.temp2,
        temp3: this.temp3,
        temp4: this.temp4,
        temp5: this.temp5,
        themevalue: this.themevalue,
        quanzhongtheme: this.quanzhongtheme,
        swonevalue: this.swonevalue,
        quanzhongswone: this.quanzhongswone,
        insertindex: this.insertindex,
        insertindexswone: this.insertindexswone,
        dpchoose: this.dpchoose,
        czchoose: this.czchoose,
        kcchoose: this.kcchoose,
        hgchoose: this.hgchoose,
        ejchoose: this.ejchoose,
        swchoose: this.swchoose,
        swonevalue2: this.swonevalue2,
        quanzhongswone2: this.quanzhongswone2,
        swonevalue3: this.swonevalue3,
        quanzhongswone3: this.quanzhongswone3,
        swonevalue4: this.swonevalue4,
        quanzhongswone4: this.quanzhongswone4
      };
    },
    deletemodel (val) {
      let that = this;
      axios
        .delete(
          that.$baseUrl +
          "/delete_model/?type=equity&ismanager=true&model_name=" +
          val
        )
        .then(res => {
          that.getmodals();
          that.$message("删除成功");
          // that.useraddmodalshow =false
        })
        .catch(err => {
          //  that.$message('失败')
          ////console.log(err)
          //that.$message('数据缺失')
        });
    },
    changepos () {
      // //console.log('herein')
      let temparr = this.listar;
      let i = temparr.length;
      while (i) {
        let j = Math.floor(Math.random() * i--);
        [temparr[j], temparr[i]] = [temparr[i], temparr[j]];
      }
      this.listar = temparr;

      this.showmodel = false;
      this.showmodel = true;
      // //console.log(this.listar)
    },
    getmodals () {
      //获取模板
      let that = this;
      axios
        .get(that.$baseUrl + "/query_model/?type=equity&ismanager=true")
        .then(res => {
          //  //console.log('模板2')
          //  //console.log(res.data)
          if (res.data.self_data) {
            that.gridData = res.data.self_data;
          }
          if (res.data.owl_data) {
            that.listar = res.data.owl_data;
          }
          if (that.listar.length > 6) {
            that.listar = that.listar.slice(0, 6);
          }
          //  that.gridData =  res.data.owl_data

          // that.useraddmodalshow =false
          // let listar = []
          // for(let i = 0 ; i <6 ; i++){
          //     let m  = parseInt(Math.random()*(res.data.owl_data.length-0+1) + 6);
          //     while(listar.indexOf(m)>=0){
          //         m  = parseInt(Math.random()*(res.data.owl_data.length-0+1) + 6);
          //     }
          //     listar.push(m)
          // }
          // //console.log(listar)
        })
        .catch(err => {
          //  that.$message('失败')
          ////console.log(err)
          //that.$message('数据缺失')
        });
    },
    setshuju (val) {
      // //console.log('GOGOGO')
      this.formData = val.formData;
      this.temp = val.temp;
      this.temp2 = val.temp2;
      this.temp3 = val.temp3;
      this.temp4 = val.temp4;
      this.temp5 = val.temp5;
      this.themevalue = val.themevalue;
      this.quanzhongtheme = val.quanzhongtheme;
      this.swonevalue = val.swonevalue;
      this.quanzhongswone = val.quanzhongswone;
      this.insertindex = val.insertindex;
      this.insertindexswone = val.insertindexswone;
      this.dpchoose = val.dpchoose;
      this.czchoose = val.czchoose;
      this.kcchoose = val.kcchoose;
      this.hgchoose = val.hgchoose;
      this.ejchoose = val.ejchoose;
      this.swchoose = val.swchoose;
      this.swonevalue2 = val.swonevalue2;
      this.quanzhongswone2 = val.quanzhongswone2;
      this.swonevalue3 = val.swonevalue3;
      this.quanzhongswone3 = val.quanzhongswone3;
      this.swonevalue4 = val.swonevalue4;
      this.quanzhongswone4 = val.quanzhongswone4;
      this.vismodel = false;
      this.submitForm();
    },
    submitmodal () {
      if (this.usermodal.name == null || this.usermodal.name == "") {
        that.$message("请输入模板名称");
      } else {
        let temp = null;
        let that = this;
        temp = {
          formData: this.formData,
          temp: this.temp,
          temp2: this.temp2,
          temp3: this.temp3,
          temp4: this.temp4,
          temp5: this.temp5,
          themevalue: this.themevalue,
          quanzhongtheme: this.quanzhongtheme,
          swonevalue: this.swonevalue,
          quanzhongswone: this.quanzhongswone,
          insertindex: this.insertindex,
          insertindexswone: this.insertindexswone,
          dpchoose: this.dpchoose,
          czchoose: this.czchoose,
          kcchoose: this.kcchoose,
          hgchoose: this.hgchoose,
          ejchoose: this.ejchoose,
          swchoose: this.swchoose,
          swonevalue2: this.swonevalue2,
          quanzhongswone2: this.quanzhongswone2,
          swonevalue3: this.swonevalue3,
          quanzhongswone3: this.quanzhongswone3,
          swonevalue4: this.swonevalue4,
          quanzhongswone4: this.quanzhongswone4
        };
        axios
          .post(that.$baseUrl + "/save_model/", {
            type: "equity",
            ismanager: "true",
            model_name: this.usermodal.name,
            model_description: this.usermodal.textarea,
            ispublic: this.usermodal.ispublic,
            model_args: temp
          })
          .then(res => {
            that.$message("保存模板成功");
            that.getmodals();
            that.useraddmodalshow = false;
          })
          .catch(err => {
            //  that.$message('失败')
            ////console.log(err)
            //that.$message('数据缺失')
          });
      }
    },
    getfontSize (val) {
      return fontSize(val);
    },
    getdownuplist () {
      let that = this;
      axios
        .get(that.$baseUrl + "/system/alpha/alphamsg/?type=1")
        .then(res => {
          ////console.log(res.data)
          res.data.industry_tree.map(industry1 => {
            //  一级行业
            that.swoneoptions.push({
              label: industry1.industry_name,
              value: industry1.industry_code
            });
            industry1.children.map(industry2 => {
              //  二级行业
              that.swoneoptions2.push({
                label: industry2.industry_name,
                value: industry2.industry_code
              });
              industry2.children.map(industry3 => {
                //  二级行业
                that.swoneoptions3.push({
                  label: industry3.industry_name,
                  value: industry3.industry_code
                });
              });
            });
          });
          // 恒生一级
          that.swoneoptions4 = res.data.hkindustry.map(item => {
            return { label: item.industry_name, value: item.industry_code };
          });

          // 主题判断
          that.themeoptions = res.data.stockclass.map(item => {
            return { label: item, value: item };
          });
        })
        .catch(err => {
          //  that.$message('失败')
          ////console.log(err)
          //that.$message('数据缺失')
        });
    },
    // 选中后禁止再选 申万1/2/3行业
    forbidswone () {
      for (let k = 0; k < this.swoneoptions.length; k++) {
        this.swoneoptions[k].forbidstate = false;
      }
      for (let i = 0; i < this.swonevalue.length; i++) {
        for (let j = 0; j < this.swoneoptions.length; j++) {
          if (this.swonevalue[i] == this.swoneoptions[j].value) {
            this.swoneoptions[j].forbidstate = true;
          }
        }
      }
    },
    forbidswone2 () {
      for (let k = 0; k < this.swoneoptions2.length; k++) {
        this.swoneoptions2[k].forbidstate = false;
      }
      for (let i = 0; i < this.swonevalue2.length; i++) {
        for (let j = 0; j < this.swoneoptions2.length; j++) {
          if (this.swonevalue2[i] == this.swoneoptions2[j].value) {
            this.swoneoptions2[j].forbidstate = true;
          }
        }
      }
    },
    forbidswone3 () {
      for (let k = 0; k < this.swoneoptions3.length; k++) {
        this.swoneoptions3[k].forbidstate = false;
      }
      for (let i = 0; i < this.swonevalue3.length; i++) {
        for (let j = 0; j < this.swoneoptions3.length; j++) {
          if (this.swonevalue3[i] == this.swoneoptions3[j].value) {
            this.swoneoptions3[j].forbidstate = true;
          }
        }
      }
    },
    forbidswone4 () {
      for (let k = 0; k < this.swoneoptions4.length; k++) {
        this.swoneoptions4[k].forbidstate = false;
      }
      for (let i = 0; i < this.swonevalue4.length; i++) {
        for (let j = 0; j < this.swoneoptions4.length; j++) {
          if (this.swonevalue4[i] == this.swoneoptions4[j].value) {
            this.swoneoptions4[j].forbidstate = true;
          }
        }
      }
    },
    getselectoptions () {
      let that = this;
      axios
        .get(that.$baseUrl + "/measure_section_range/?type=equity")
        .then(res => {
          //console.log(res.data);
          //console.log('listdown');
          that.selectvalue1 = [];
          that.selectvalue2 = [];
          that.selectvalue3 = [];
          that.selectvalue4 = [];
          that.selectvalue5 = [];
          that.selectvalue6 = [];
          that.selectvalue7 = [];
          that.selectvalue8 = [];
          that.selectvalue9 = [];
          that.selectvalue10 = [];
          that.selectvalue11 = [];
          that.selectvalue12 = [];
          that.selectvalue13 = [];
          that.selectvalue14 = [];
          that.selectvalue15 = [];
          that.selectvalue16 = [];
          that.selectvalue17 = [];
          that.selectvalue18 = [];
          that.selectvalue19 = [];
          that.selectvalue20 = [];
          that.selectvalue21 = [];
          that.selectvalue22 = [];
          that.selectvalue23 = [];
          that.selectvalue24 = [];
          that.selectvalue25 = [];
          that.selectvalue26 = [];
          that.selectvalue27 = [];
          that.selectvalue28 = [];
          that.selectvalue29 = [];
          that.selectvalue30 = [];
          for (let i = 0; i < res.data.length; i++) {
            if (res.data[i].measure_id == "31") {
              that.selectvalue1.push(res.data[i]);
            } else if (res.data[i].measure_id == "34") {
              that.selectvalue2.push(res.data[i]);
            } else if (res.data[i].measure_id == "42") {
              that.selectvalue3.push(res.data[i]);
            } else if (res.data[i].measure_id == "43") {
              that.selectvalue4.push(res.data[i]);
            } else if (res.data[i].measure_id == "55") {
              that.selectvalue5.push(res.data[i]);
            } else if (res.data[i].measure_id == "32") {
              that.selectvalue6.push(res.data[i]);
            } else if (res.data[i].measure_id == "33") {
              that.selectvalue7.push(res.data[i]);
            } else if (res.data[i].measure_id == "41") {
              that.selectvalue8.push(res.data[i]);
            } else if (res.data[i].measure_id == "65") {
              that.selectvalue9.push(res.data[i]);
            } else if (res.data[i].measure_id == "46") {
              that.selectvalue10.push(res.data[i]);
            } else if (res.data[i].measure_id == "29") {
              that.selectvalue11.push(res.data[i]);
            } else if (res.data[i].measure_id == "30") {
              that.selectvalue12.push(res.data[i]);
            } else if (res.data[i].measure_id == "35") {
              that.selectvalue13.push(res.data[i]);
            } else if (res.data[i].measure_id == "36") {
              that.selectvalue14.push(res.data[i]);
            } else if (res.data[i].measure_id == "56") {
              that.selectvalue15.push(res.data[i]);
            } else if (res.data[i].measure_id == "37") {
              that.selectvalue16.push(res.data[i]);
            } else if (res.data[i].measure_id == "48") {
              that.selectvalue17.push(res.data[i]);
            } else if (res.data[i].measure_id == "49") {
              that.selectvalue18.push(res.data[i]);
            } else if (res.data[i].measure_id == "104") {
              that.selectvalue19.push(res.data[i]);
            } else if (res.data[i].measure_id == "44") {
              that.selectvalue20.push(res.data[i]);
            } else if (res.data[i].measure_id == "45") {
              that.selectvalue21.push(res.data[i]);
            } else if (res.data[i].measure_id == "57") {
              that.selectvalue22.push(res.data[i]);
            } else if (res.data[i].measure_id == "58") {
              that.selectvalue23.push(res.data[i]);
            } else if (res.data[i].measure_id == "59") {
              that.selectvalue24.push(res.data[i]);
            } else if (res.data[i].measure_id == "60") {
              that.selectvalue25.push(res.data[i]);
            } else if (res.data[i].measure_id == "61") {
              that.selectvalue26.push(res.data[i]);
            } else if (res.data[i].measure_id == "63") {
              that.selectvalue27.push(res.data[i]);
            } else if (res.data[i].measure_id == "64") {
              that.selectvalue28.push(res.data[i]);
            } else if (res.data[i].measure_id == "68") {
              that.selectvalue29.push(res.data[i]);
            } else if (res.data[i].measure_id == "69") {
              that.selectvalue30.push(res.data[i]);
            }
          }
        })
        .catch(err => {
          //  that.$message('失败')
          ////console.log(err)
          //that.$message('数据缺失')
        });
    },
    setrank (val, index) {
      if (index == 1.1) {
        this.$set(this.formData.field104[0], "rank", val);
      } else if (index == 1.2) {
        this.$set(this.formData.field104[1], "rank", val);
      } else if (index == 1.3) {
        this.$set(this.formData.field104[2], "rank", val);
      } else if (index == 1.4) {
        this.$set(this.formData.field104[3], "rank", val);
      } else if (index == 1.5) {
        this.$set(this.formData.field104[4], "rank", val);
      } else if (index == 1.6) {
        this.$set(this.formData.field104[5], "rank", val);
      } else if (index == 1.7) {
        this.$set(this.formData.field104[6], "rank", val);
      } else if (index == 1.8) {
        this.$set(this.formData.field104[7], "rank", val);
      } else if (index == 1.9) {
        this.$set(this.formData.field104[8], "rank", val);
      } else if (index == 1.101) {
        this.$set(this.formData.field104[9], "rank", val);
      } else if (index == 2.1) {
        this.$set(this.formData.field105[0], "rank", val);
      } else if (index == 2.2) {
        this.$set(this.formData.field105[1], "rank", val);
      } else if (index == 2.3) {
        this.$set(this.formData.field105[2], "rank", val);
      } else if (index == 2.4) {
        this.$set(this.formData.field105[3], "rank", val);
      } else if (index == 2.5) {
        this.$set(this.formData.field105[4], "rank", val);
      } else if (index == 2.6) {
        this.$set(this.formData.field105[5], "rank", val);
      } else if (index == 2.7) {
        this.$set(this.formData.field105[6], "rank", val);
      } else if (index == 2.8) {
        this.$set(this.formData.field105[7], "rank", val);
      } else if (index == 2.9) {
        this.$set(this.formData.field105[8], "rank", val);
      } else if (index == 2.101) {
        this.$set(this.formData.field105[9], "rank", val);
      } else if (index == 2.11) {
        this.$set(this.formData.field105[10], "rank", val);
      } else if (index == 2.12) {
        this.$set(this.formData.field105[11], "rank", val);
      } else if (index == 2.13) {
        this.$set(this.formData.field105[12], "rank", val);
      } else if (index == 2.14) {
        this.$set(this.formData.field105[13], "rank", val);
      } else if (index == 2.15) {
        this.$set(this.formData.field105[14], "rank", val);
      } else if (index == 2.16) {
        this.$set(this.formData.field105[15], "rank", val);
      } else if (index == 2.17) {
        this.$set(this.formData.field105[16], "rank", val);
      } else if (index == 2.18) {
        this.$set(this.formData.field105[17], "rank", val);
      } else if (index == 2.19) {
        this.$set(this.formData.field105[18], "rank", val);
      } else if (index == 2.201) {
        this.$set(this.formData.field105[19], "rank", val);
      }
    },
    setvalue1 (val, index) {
      if (index == 1.1) {
        this.$set(this.formData.field104[0], "value1", val);
      } else if (index == 1.2) {
        this.$set(this.formData.field104[1], "value1", val);
      } else if (index == 1.3) {
        this.$set(this.formData.field104[2], "value1", val);
      } else if (index == 1.4) {
        this.$set(this.formData.field104[3], "value1", val);
      } else if (index == 1.5) {
        this.$set(this.formData.field104[4], "value1", val);
      } else if (index == 1.6) {
        this.$set(this.formData.field104[5], "value1", val);
      } else if (index == 1.7) {
        this.$set(this.formData.field104[6], "value1", val);
      } else if (index == 1.8) {
        this.$set(this.formData.field104[7], "value1", val);
      } else if (index == 1.9) {
        this.$set(this.formData.field104[8], "value1", val);
      } else if (index == 1.101) {
        this.$set(this.formData.field104[9], "value1", val);
      } else if (index == 2.1) {
        this.$set(this.formData.field105[0], "value1", val);
      } else if (index == 2.2) {
        this.$set(this.formData.field105[1], "value1", val);
      } else if (index == 2.3) {
        this.$set(this.formData.field105[2], "value1", val);
      } else if (index == 2.4) {
        this.$set(this.formData.field105[3], "value1", val);
      } else if (index == 2.5) {
        this.$set(this.formData.field105[4], "value1", val);
      } else if (index == 2.6) {
        this.$set(this.formData.field105[5], "value1", val);
      } else if (index == 2.7) {
        this.$set(this.formData.field105[6], "value1", val);
      } else if (index == 2.8) {
        this.$set(this.formData.field105[7], "value1", val);
      } else if (index == 2.9) {
        this.$set(this.formData.field105[8], "value1", val);
      } else if (index == 2.101) {
        this.$set(this.formData.field105[9], "value1", val);
      } else if (index == 2.11) {
        this.$set(this.formData.field105[10], "value1", val);
      } else if (index == 2.12) {
        this.$set(this.formData.field105[11], "value1", val);
      } else if (index == 2.13) {
        this.$set(this.formData.field105[12], "value1", val);
      } else if (index == 2.14) {
        this.$set(this.formData.field105[13], "value1", val);
      } else if (index == 2.15) {
        this.$set(this.formData.field105[14], "value1", val);
      } else if (index == 2.16) {
        this.$set(this.formData.field105[15], "value1", val);
      } else if (index == 2.17) {
        this.$set(this.formData.field105[16], "value1", val);
      } else if (index == 2.18) {
        this.$set(this.formData.field105[17], "value1", val);
      } else if (index == 2.19) {
        this.$set(this.formData.field105[18], "value1", val);
      } else if (index == 2.201) {
        this.$set(this.formData.field105[19], "value1", val);
      }
    },
    setvalue2 (val, index) {
      // //console.log(val)
      // //console.log(index)
      if (index == 1.1) {
        this.$set(this.formData.field104[0], "value2", val);
      } else if (index == 1.2) {
        this.$set(this.formData.field104[1], "value2", val);
      } else if (index == 1.3) {
        this.$set(this.formData.field104[2], "value2", val);
      } else if (index == 1.4) {
        this.$set(this.formData.field104[3], "value2", val);
      } else if (index == 1.5) {
        this.$set(this.formData.field104[4], "value2", val);
      } else if (index == 1.6) {
        this.$set(this.formData.field104[5], "value2", val);
      } else if (index == 1.7) {
        this.$set(this.formData.field104[6], "value2", val);
      } else if (index == 1.8) {
        this.$set(this.formData.field104[7], "value2", val);
      } else if (index == 1.9) {
        this.$set(this.formData.field104[8], "value2", val);
      }
    },
    forbidtheme () {
      for (let k = 0; k < this.themeoptions.length; k++) {
        this.themeoptions[k].forbidstate = false;
      }
      for (let i = 0; i < this.themevalue.length; i++) {
        for (let j = 0; j < this.themeoptions.length; j++) {
          if (this.themevalue[i] == this.themeoptions[j].value) {
            this.themeoptions[j].forbidstate = true;
          }
        }
      }
    },
    my_desc_sort (name) {
      //  ////console.log(name)
      return function (a, b) {
        if (
          a[name] === "--" ||
          a[name] === "nan" ||
          a[name] === "- -" ||
          b[name] === "--" ||
          b[name] === "nan" ||
          b[name] === "- -"
        ) {
          if (a[name] === "--" || a[name] === "nan" || a[name] === "- -") {
            return 1;
          } else if (
            b[name] === "--" ||
            b[name] === "nan" ||
            b[name] === "- -"
          ) {
            return -1;
          }
        } else if (Number(a[name]) > Number(b[name])) {
          return -1;
        } else if (Number(a[name]) < Number(b[name])) {
          return 1;
        } else {
          return 0;
        }
      };
    },
    my_asc_sort (name) {
      return function (a, b) {
        if (
          a[name] === "--" ||
          a[name] === "nan" ||
          a[name] === "- -" ||
          b[name] === "--" ||
          b[name] === "nan" ||
          b[name] === "- -"
        ) {
          if (a[name] === "--" || a[name] === "nan" || a[name] === "- -") {
            return 1;
          } else if (
            b[name] === "--" ||
            b[name] === "nan" ||
            b[name] === "- -"
          ) {
            return -1;
          }
        } else if (Number(a[name]) < Number(b[name])) {
          return -1;
        } else if (Number(a[name]) > Number(b[name])) {
          return 1;
        } else {
          return 0;
        }
      };
    },

    sort_change (column) {
      // ////console.log(column)
      // ////console.log('colum')
      this.pageIndex = 1; // return to the first page after sorting
      if (column.prop === "code") {
        if (column.order === "descending") {
          this.alldata = this.alldata.sort(this.my_desc_sort("code"));
        } else if (column.order === "ascending") {
          this.alldata = this.alldata.sort(this.my_asc_sort("code"));
        }
      } else if (column.prop === "1y") {
        if (column.order === "descending") {
          this.alldata = this.alldata.sort(this.my_desc_sort("1y"));
        } else if (column.order === "ascending") {
          this.alldata = this.alldata.sort(this.my_asc_sort("1y"));
        }
      } else if (column.prop === "1m") {
        if (column.order === "descending") {
          this.alldata = this.alldata.sort(this.my_desc_sort("1m"));
        } else if (column.order === "ascending") {
          this.alldata = this.alldata.sort(this.my_asc_sort("1m"));
        }
      } else if (column.prop === "1q") {
        if (column.order === "descending") {
          this.alldata = this.alldata.sort(this.my_desc_sort("1q"));
        } else if (column.order === "ascending") {
          this.alldata = this.alldata.sort(this.my_asc_sort("1q"));
        }
      } else if (column.prop === "1w") {
        if (column.order === "descending") {
          this.alldata = this.alldata.sort(this.my_desc_sort("1w"));
        } else if (column.order === "ascending") {
          this.alldata = this.alldata.sort(this.my_asc_sort("1w"));
        }
      } else if (column.prop === "scale") {
        if (column.order === "descending") {
          this.alldata = this.alldata.sort(this.my_desc_sort("scale"));
        } else if (column.order === "ascending") {
          this.alldata = this.alldata.sort(this.my_asc_sort("scale"));
        }
      } else if (column.prop === "long_stock_pick") {
        if (column.order === "descending") {
          this.alldata = this.alldata.sort(
            this.my_desc_sort("long_stock_pick")
          );
        } else if (column.order === "ascending") {
          this.alldata = this.alldata.sort(this.my_asc_sort("long_stock_pick"));
        }
      } else if (column.prop === "short_trade") {
        if (column.order === "descending") {
          this.alldata = this.alldata.sort(this.my_desc_sort("short_trade"));
        } else if (column.order === "ascending") {
          this.alldata = this.alldata.sort(this.my_asc_sort("short_trade"));
        }
      } else if (column.prop === "long_adaptive") {
        if (column.order === "descending") {
          this.alldata = this.alldata.sort(this.my_desc_sort("long_adaptive"));
        } else if (column.order === "ascending") {
          this.alldata = this.alldata.sort(this.my_asc_sort("long_adaptive"));
        }
      } else if (column.prop === "long_industry_cap") {
        if (column.order === "descending") {
          this.alldata = this.alldata.sort(
            this.my_desc_sort("long_industry_cap")
          );
        } else if (column.order === "ascending") {
          this.alldata = this.alldata.sort(
            this.my_asc_sort("long_industry_cap")
          );
        }
      } else if (column.prop === "long_stockclass_cap") {
        if (column.order === "descending") {
          this.alldata = this.alldata.sort(
            this.my_desc_sort("long_stockclass_cap")
          );
        } else if (column.order === "ascending") {
          this.alldata = this.alldata.sort(
            this.my_asc_sort("long_stockclass_cap")
          );
        }
      } else if (column.prop === "window_score") {
        if (column.order === "descending") {
          this.alldata = this.alldata.sort(this.my_desc_sort("window_score"));
        } else if (column.order === "ascending") {
          this.alldata = this.alldata.sort(this.my_asc_sort("window_score"));
        }
      } else if (column.prop === "final_rank") {
        if (column.order === "descending") {
          this.alldata = this.alldata.sort(this.my_desc_sort("final_rank"));
        } else if (column.order === "ascending") {
          this.alldata = this.alldata.sort(this.my_asc_sort("final_rank"));
        }
      } else if (column.prop === "stockclass_hold_weight") {
        if (column.order === "descending") {
          this.alldata = this.alldata.sort(
            this.my_desc_sort("stockclass_hold_weight")
          );
        } else if (column.order === "ascending") {
          this.alldata = this.alldata.sort(
            this.my_asc_sort("stockclass_hold_weight")
          );
        }
      } else if (column.prop === "industry_weight") {
        if (column.order === "descending") {
          this.alldata = this.alldata.sort(
            this.my_desc_sort("industry_weight")
          );
        } else if (column.order === "ascending") {
          this.alldata = this.alldata.sort(this.my_asc_sort("industry_weight"));
        }
      } else if (column.prop === "index_weight") {
        if (column.order === "descending") {
          this.alldata = this.alldata.sort(this.my_desc_sort("index_weight"));
        } else if (column.order === "ascending") {
          this.alldata = this.alldata.sort(this.my_asc_sort("index_weight"));
        }
      }
      this.tableData = this.alldata.slice(0, this.pageSize); // show only one page
    },
    elcellstyle ({ row, column, rowIndex, columnIndex }) {
      // ////console.log(row[0])
      let t = this.checkList.length;
      if (this.showIndustrySelect) t = t + 1;
      if (this.showThemeSelect) t = t + 1;

      if (columnIndex == 4 + t) {
        if (row["1w"] >= 0) {
          return "color: #E85D2D;";
        } else return "color: #20995B;";
      }
      if (columnIndex == 5 + t) {
        if (row["1m"] >= 0) {
          return "color: #E85D2D;";
        } else return "color: #20995B;";
      }
      if (columnIndex == 6 + t) {
        if (row["1q"] >= 0) {
          return "color: #E85D2D;";
        } else return "color: #20995B;";
      }
      if (columnIndex == 7 + t) {
        if (row["1y"] >= 0) {
          return "color: #E85D2D;";
        } else return "color: #20995B;";
      }
    },
    godetail (id, name) {
      //带参进去
      this.$router.push({
        path: "/fundmanagerdetail/" + id,
        hash: "",
        query: { id: id, name: name }
      });
    },
    addpool (id, name) {
      // this.addfundvis = true
      // this.choosefundid = id
      // this.choosefundname=name
      let that = this;
      axios
        .post(that.$baseUrl + "/pool/basket_fund/", { fund_code: id })
        .then(res => {
          that.$message("新增成功" + "  " + id + " " + name);
        })
        .catch(err => {
          //  that.$message('失败')
          ////console.log(err)
          //that.$message('数据缺失')
        });
    },
    bindnumber (e) {
      ////console.log(e)
    },
    submitForm () {
      this.submitflag = true;
      ////console.log(this.formData)
      if (this.temp5.indexOf(1) > -1) {
      } else {
        this.swonevalue = [];
        this.quanzhongswone = [];
        this.insertindexswone = 1;
      }
      if (this.temp5.indexOf(2) > -1) {
      } else {
        this.swonevalue2 = [];
        this.quanzhongswone2 = [];
        this.insertindexswone2 = 1;
      }
      if (this.temp5.indexOf(3) > -1) {
      } else {
        this.swonevalue3 = [];
        this.quanzhongswone3 = [];
        this.insertindexswone3 = 1;
      }
      if (this.temp5.indexOf(4) > -1) {
      } else {
        this.swonevalue4 = [];
        this.quanzhongswone4 = [];
        this.insertindexswone4 = 1;
      }
      if (this.swonevalue.length == 0 && this.quanzhongswone.length == 1) {
        this.quanzhongswone = [];
      }
      if (this.themevalue.length == 0 && this.quanzhongtheme.length == 1) {
        this.quanzhongtheme = [];
      }
      if (this.swonevalue2.length == 0 && this.quanzhongswone2.length == 1) {
        this.quanzhongswone2 = [];
      }

      if (this.swonevalue3.length == 0 && this.quanzhongswone3.length == 1) {
        this.quanzhongswone3 = [];
      }

      if (this.swonevalue4.length == 0 && this.quanzhongswone4.length == 1) {
        this.quanzhongswone4 = [];
      }

      for (let the = 0; the < this.themevalue.length; the++) {
        if (this.themevalue[the] == "" || this.themevalue[the] == null) {
          this.submitflag = false;
          this.$message("主题判断有未选择条件");
        }
      }
      for (let thesw = 0; thesw < this.swonevalue.length; thesw++) {
        if (this.swonevalue[thesw] == "" || this.swonevalue[thesw] == null) {
          this.submitflag = false;
          this.$message("申万一级行业判断有未选择条件");
        }
      }
      for (let thesw2 = 0; thesw2 < this.swonevalue2.length; thesw2++) {
        if (
          this.swonevalue2[thesw2] == "" ||
          this.swonevalue2[thesw2] == null
        ) {
          this.submitflag = false;
          this.$message("申万二级行业判断有未选择条件");
        }
      }
      for (let thesw3 = 0; thesw3 < this.swonevalue3.length; thesw3++) {
        if (
          this.swonevalue3[thesw3] == "" ||
          this.swonevalue3[thesw3] == null
        ) {
          this.submitflag = false;
          this.$message("申万三级行业判断有未选择条件");
        }
      }
      for (let thesw4 = 0; thesw4 < this.swonevalue4.length; thesw4++) {
        if (
          this.swonevalue4[thesw4] == "" ||
          this.swonevalue4[thesw4] == null
        ) {
          this.submitflag = false;
          this.$message("恒生一级行业判断有未选择条件");
        }
      }
      let quanzhongsum = 0;
      for (let the1 = 0; the1 < this.quanzhongtheme.length; the1++) {
        quanzhongsum += Number(this.quanzhongtheme[the1]);
      }
      ////console.log(this.themevalue)
      ////console.log(this.quanzhongtheme)
      if (this.quanzhongtheme.length > 0) {
        if (this.themevalue.length == 0 && this.quanzhongtheme.length == 1) {
        } else {
          if (quanzhongsum != 100) {
            this.submitflag = false;
            this.$message("主题判断权重相加不为100");
          }
        }
      }

      let quanzhongsumswone = 0;
      for (let the1sw = 0; the1sw < this.quanzhongswone.length; the1sw++) {
        quanzhongsumswone += Number(this.quanzhongswone[the1sw]);
      }
      if (this.quanzhongswone.length > 0) {
        if (this.swonevalue.length == 0 && this.quanzhongswone.length == 1) {
        } else {
          if (quanzhongsumswone != 100) {
            this.submitflag = false;
            this.$message("申万一级行业判断权重相加不为100");
          }
        }
      }
      let quanzhongsumswone2 = 0;
      for (let the1sw2 = 0; the1sw2 < this.quanzhongswone2.length; the1sw2++) {
        quanzhongsumswone2 += Number(this.quanzhongswone2[the1sw2]);
      }
      if (this.quanzhongswone2.length > 0) {
        if (this.swonevalue2.length == 0 && this.quanzhongswone2.length == 1) {
        } else {
          if (quanzhongsumswone2 != 100) {
            this.submitflag = false;
            this.$message("申万二级行业判断权重相加不为100");
          }
        }
      }
      let quanzhongsumswone3 = 0;
      for (let the1sw3 = 0; the1sw3 < this.quanzhongswone3.length; the1sw3++) {
        quanzhongsumswone3 += Number(this.quanzhongswone3[the1sw3]);
      }
      if (this.quanzhongswone3.length > 0) {
        if (this.swonevalue3.length == 0 && this.quanzhongswone3.length == 1) {
        } else {
          if (quanzhongsumswone3 != 100) {
            this.submitflag = false;
            this.$message("申万三级行业判断权重相加不为100");
          }
        }
      }
      let quanzhongsumswone4 = 0;
      for (let the1sw4 = 0; the1sw4 < this.quanzhongswone4.length; the1sw4++) {
        quanzhongsumswone4 += Number(this.quanzhongswone4[the1sw4]);
      }
      if (this.quanzhongswone4.length > 0) {
        if (this.swonevalue4.length == 0 && this.quanzhongswone4.length == 1) {
        } else {
          if (quanzhongsumswone4 != 100) {
            this.submitflag = false;
            this.$message("恒生一级行业判断权重相加不为100");
          }
        }
      }
      let tempthemearr = [];

      for (let q = 0; q < this.themevalue.length; q++) {
        tempthemearr.push({
          value: this.themevalue[q],
          weight: this.quanzhongtheme[q]
        });
      }
      this.formData.theme = tempthemearr;

      if (this.themevalue.length == 0 && this.quanzhongtheme.length == 1) {
      } else {
        if (this.themevalue.length != this.quanzhongtheme.length) {
          this.submitflag = false;
          this.$message("主题中有未输入的权重或主题选项");
        }
      }
      if (this.swonevalue.length == 0 && this.quanzhongswone.length == 1) {
      } else {
        if (this.swonevalue.length != this.quanzhongswone.length) {
          this.submitflag = false;
          this.$message("申万一级行业中有未输入的权重或申万一级行业选项");
        }
      }
      if (this.swonevalue2.length == 0 && this.quanzhongswone2.length == 1) {
      } else {
        if (this.swonevalue2.length != this.quanzhongswone2.length) {
          this.submitflag = false;
          this.$message("申万二级行业中有未输入的权重或申万二级行业选项");
        }
      }
      if (this.swonevalue3.length == 0 && this.quanzhongswone3.length == 1) {
      } else {
        if (this.swonevalue3.length != this.quanzhongswone3.length) {
          this.submitflag = false;
          this.$message("申万三级行业中有未输入的权重或申万三级行业选项");
        }
      }
      if (this.swonevalue4.length == 0 && this.quanzhongswone4.length == 1) {
      } else {
        if (this.swonevalue4.length != this.quanzhongswone4.length) {
          this.submitflag = false;
          this.$message("恒生一级行业中有未输入的权重或申万三级行业选项");
        }
      }
      //挂到formdata里 sw 恒生 theme
      this.formData.swindustry1 = [];
      for (let p = 0; p < this.swonevalue.length; p++) {
        this.formData.swindustry1.push({
          industrycode: this.swonevalue[p],
          weight: this.quanzhongswone[p]
        });
      }
      this.formData.swindustry2 = [];
      for (let p = 0; p < this.swonevalue2.length; p++) {
        this.formData.swindustry2.push({
          industrycode: this.swonevalue2[p],
          weight: this.quanzhongswone2[p]
        });
      }
      this.formData.swindustry3 = [];
      for (let p = 0; p < this.swonevalue3.length; p++) {
        this.formData.swindustry3.push({
          industrycode: this.swonevalue3[p],
          weight: this.quanzhongswone3[p]
        });
      }
      this.formData.hsindustry1 = [];
      for (let p = 0; p < this.swonevalue4.length; p++) {
        this.formData.hsindustry1.push({
          industrycode: this.swonevalue4[p],
          weight: this.quanzhongswone4[p]
        });
      }
      this.formData.theme = [];
      for (let p = 0; p < this.themevalue.length; p++) {
        this.formData.theme.push({
          industrycode: this.themevalue[p],
          weight: this.quanzhongtheme[p]
        });
      }
      if (this.formData.theme.length > 0) {
        this.showThemeSelect = true;
      } else {
        this.showThemeSelect = false;
      }
      if (
        this.formData.swindustry1.length > 0 ||
        this.formData.swindustry2.length > 0 ||
        this.formData.swindustry3.length > 0 ||
        this.formData.hsindustry1.length > 0
      ) {
        this.showIndustrySelect = true;
      } else {
        this.showIndustrySelect = false;
      }
      if (this.formData.index_code.length > 0) {
        this.showIndexSelect = true;
      } else {
        this.showIndexSelect = false;
      }
      if (this.formData.field101 != null) {
        if (this.formData.field101 < 0) {
          this.submitflag = false;
          this.$message("基金当前规模前置范围不能小于0");
        }
      }
      if (this.formData.field1011 != null) {
        if (this.formData.field1011 < 0) {
          this.submitflag = false;
          this.$message("基金当前规模后置范围不能小于0");
        }
      }
      // ////console.log(this.formData.field101)  //3
      //     ////console.log(typeof(this.formData.field102))  //1
      //      ////console.log(this.formData.field1011)//1000
      //      ////console.log(this.formData.field101 > this.formData.field1011)
      if (
        this.formData.field101 != null &&
        this.formData.field1011 != null &&
        this.formData.field101 != "" &&
        this.formData.field1011 != ""
      ) {
        if (Number(this.formData.field101) > Number(this.formData.field1011)) {
          this.submitflag = false;
          this.$message("基金当前规模的前置范围大于后置范围");
        }
      }
      if (this.formData.field102 < 0 || this.formData.field1021 < 0) {
        this.$message("管理者经验不能小于0年");
        this.submitflag = false;
      }
      if (this.temp3.indexOf(1) > -1) {
        let hgsum = 0;
        if (this.hgchoose.indexOf("复苏") < 0) {
          this.$set(this.formData.field106, "value1", 0);
        } else if (this.hgchoose.indexOf("复苏") > -1) {
          hgsum += Number(this.formData.field106.value1);
        }

        if (this.hgchoose.indexOf("繁荣") < 0) {
          this.$set(this.formData.field106, "value2", 0);
        } else if (this.hgchoose.indexOf("繁荣") > -1) {
          hgsum += Number(this.formData.field106.value2);
        }

        if (this.hgchoose.indexOf("滞涨") < 0)
          this.$set(this.formData.field106, "value3", 0);
        else if (this.hgchoose.indexOf("滞涨") > -1) {
          hgsum += Number(this.formData.field106.value3);
        }

        if (this.hgchoose.indexOf("衰退") < 0)
          this.$set(this.formData.field106, "value4", 0);
        else if (this.hgchoose.indexOf("衰退") > -1) {
          hgsum += Number(this.formData.field106.value4);
        }

        if (hgsum != 100) {
          if (
            this.hgchoose.indexOf("复苏") < 0 &&
            this.hgchoose.indexOf("繁荣") < 0 &&
            this.hgchoose.indexOf("滞涨") < 0 &&
            this.hgchoose.indexOf("衰退") < 0
          ) {
          } else {
            ////console.log(100==hgsum)
            ////console.log('宏观周期'+hgsum)
            this.$message(
              "周期判断的宏观周期筛选条件值加起来不为100，无法提交"
            );
            this.submitflag = false;
          }
        }
      } else {
        this.$set(this.formData.field106, "value1", 0);
        this.$set(this.formData.field106, "value2", 0);
        this.$set(this.formData.field106, "value3", 0);
        this.$set(this.formData.field106, "value4", 0);
      }

      if (this.temp3.indexOf(2) > -1) {
        let kcsum = 0;
        if (this.kcchoose.indexOf("被动去库存") < 0) {
          this.$set(this.formData.field1061, "value1", 0);
        } else if (this.kcchoose.indexOf("被动去库存") > -1) {
          kcsum += Number(this.formData.field1061.value1);
        }

        if (this.kcchoose.indexOf("主动补库存") < 0) {
          this.$set(this.formData.field1061, "value2", 0);
        } else if (this.kcchoose.indexOf("主动补库存") > -1) {
          kcsum += Number(this.formData.field1061.value2);
        }

        if (this.kcchoose.indexOf("被动补库存") < 0)
          this.$set(this.formData.field1061, "value3", 0);
        else if (this.kcchoose.indexOf("被动补库存") > -1) {
          kcsum += Number(this.formData.field1061.value3);
        }

        if (this.kcchoose.indexOf("主动去库存") < 0)
          this.$set(this.formData.field1061, "value4", 0);
        else if (this.kcchoose.indexOf("主动去库存") > -1) {
          kcsum += Number(this.formData.field1061.value4);
        }

        if (kcsum != 100) {
          if (
            this.kcchoose.indexOf("被动去库存") < 0 &&
            this.kcchoose.indexOf("主动补库存") < 0 &&
            this.kcchoose.indexOf("被动补库存") < 0 &&
            this.kcchoose.indexOf("主动去库存") < 0
          ) {
          } else {
            this.$message(
              "周期判断的库存周期筛选条件值加起来不为100，无法提交"
            );
            this.submitflag = false;
            ////console.log('库存周期'+kcsum)
          }
        }
      } else {
        this.$set(this.formData.field1061, "value1", 0);
        this.$set(this.formData.field1061, "value2", 0);
        this.$set(this.formData.field1061, "value3", 0);
        this.$set(this.formData.field1061, "value4", 0);
      }

      if (this.temp3.indexOf(3) > -1) {
        let ejsum = 0;
        if (this.ejchoose.indexOf("上升") < 0) {
          this.$set(this.formData.field1062, "value1", 0);
        } else if (this.ejchoose.indexOf("上升") > -1) {
          ejsum += Number(this.formData.field1062.value1);
        }

        if (this.ejchoose.indexOf("大牛市") < 0) {
          this.$set(this.formData.field1062, "value2", 0);
        } else if (this.ejchoose.indexOf("大牛市") > -1) {
          ejsum += Number(this.formData.field1062.value2);
        }

        if (this.ejchoose.indexOf("震荡") < 0)
          this.$set(this.formData.field1062, "value3", 0);
        else if (this.ejchoose.indexOf("震荡") > -1) {
          ejsum += Number(this.formData.field1062.value3);
        }

        if (this.ejchoose.indexOf("熊市") < 0)
          this.$set(this.formData.field1062, "value4", 0);
        else if (this.ejchoose.indexOf("熊市") > -1) {
          ejsum += Number(this.formData.field1062.value4);
        }

        if (this.ejchoose.indexOf("反弹") < 0)
          this.$set(this.formData.field1062, "value5", 0);
        else if (this.ejchoose.indexOf("反弹") > -1) {
          ejsum += Number(this.formData.field1062.value5);
        }

        if (this.ejchoose.indexOf("大跌") < 0)
          this.$set(this.formData.field1062, "value6", 0);
        else if (this.ejchoose.indexOf("大跌") > -1) {
          ejsum += Number(this.formData.field1062.value6);
        }
        if (ejsum != 100) {
          if (
            this.ejchoose.indexOf("上升") < 0 &&
            this.ejchoose.indexOf("大牛市") < 0 &&
            this.ejchoose.indexOf("震荡") < 0 &&
            this.ejchoose.indexOf("熊市") < 0 &&
            this.ejchoose.indexOf("反弹") < 0 &&
            this.ejchoose.indexOf("大跌") < 0
          ) {
          } else {
            this.$message(
              "周期判断的二级市场周期筛选条件值加起来不为100，无法提交"
            );
            this.submitflag = false;
            ////console.log('二级周期'+ejsum)
          }
        }
      } else {
        this.$set(this.formData.field1062, "value1", 0);
        this.$set(this.formData.field1062, "value2", 0);
        this.$set(this.formData.field1062, "value3", 0);
        this.$set(this.formData.field1062, "value4", 0);
        this.$set(this.formData.field1062, "value5", 0);
        this.$set(this.formData.field1062, "value6", 0);
      }

      if (this.temp4.indexOf(1) > -1) {
        let czsum = 0;
        if (this.czchoose.indexOf("成长") < 0) {
          this.$set(this.formData.field107, "value1", 0);
        } else if (this.czchoose.indexOf("成长") > -1) {
          czsum += Number(this.formData.field107.value1);
        }

        if (this.czchoose.indexOf("价值") < 0) {
          this.$set(this.formData.field107, "value2", 0);
        } else if (this.czchoose.indexOf("价值") > -1) {
          czsum += Number(this.formData.field107.value2);
        }

        if (czsum != 100) {
          if (
            this.czchoose.indexOf("成长") < 0 &&
            this.czchoose.indexOf("价值") < 0
          ) {
          } else {
            this.$message(
              "风格判断的成长价值筛选条件值加起来不为100，无法提交"
            );
            this.submitflag = false;
            ////console.log('成长周期'+czsum)
          }
        }
      } else {
        this.$set(this.formData.field107, "value1", 0);
        this.$set(this.formData.field107, "value2", 0);
      }

      if (this.temp4.indexOf(2) > -1) {
        let dpsum = 0;
        if (this.dpchoose.indexOf("大盘") < 0) {
          this.$set(this.formData.field1071, "value1", 0);
        } else if (this.dpchoose.indexOf("大盘") > -1) {
          dpsum += Number(this.formData.field1071.value1);
        }

        if (this.dpchoose.indexOf("中盘") < 0) {
          this.$set(this.formData.field1071, "value2", 0);
        } else if (this.dpchoose.indexOf("中盘") > -1) {
          dpsum += Number(this.formData.field1071.value2);
        }

        if (this.dpchoose.indexOf("小盘") < 0) {
          this.$set(this.formData.field1071, "value3", 0);
        } else if (this.dpchoose.indexOf("小盘") > -1) {
          dpsum += Number(this.formData.field1071.value3);
        }

        if (dpsum != 100) {
          if (
            this.dpchoose.indexOf("大盘") < 0 &&
            this.dpchoose.indexOf("小盘") < 0 &&
            this.dpchoose.indexOf("中盘") < 0
          ) {
          } else {
            this.$message(
              "风格判断的大盘小盘筛选条件值加起来不为100，无法提交"
            );
            this.submitflag = false;
            ////console.log('库存周期'+dpsum)
          }
        }
      } else {
        this.$set(this.formData.field1071, "value1", 0);
        this.$set(this.formData.field1071, "value2", 0);
        this.$set(this.formData.field1071, "value3", 0);
      }

      // if(this.temp5.indexOf(1)>-1){
      //     let swsum = 0
      //     if(this.swchoose.indexOf('非银金融')<0)  {this.$set(this.formData.field108,'value1',0)}
      //     else if((this.swchoose.indexOf('非银金融')>-1)){ swsum +=Number(this.formData.field108.value1)}

      //     if(this.swchoose.indexOf('农林牧渔')<0) {this.$set(this.formData.field108,'value2',0)}
      //     else if(this.swchoose.indexOf('农林牧渔')>-1){ swsum +=Number(this.formData.field108.value2)}

      //     if(this.swchoose.indexOf('国防军工')<0)  {this.$set(this.formData.field108,'value3',0)}
      //     else if((this.swchoose.indexOf('国防军工')>-1)){ swsum +=Number(this.formData.field108.value3)}

      //     if(this.swchoose.indexOf('采掘')<0) {this.$set(this.formData.field108,'value4',0)}
      //     else if(this.swchoose.indexOf('采掘')>-1){ swsum +=Number(this.formData.field108.value4)}

      //     if(this.swchoose.indexOf('传媒')<0)  {this.$set(this.formData.field108,'value5',0)}
      //     else if((this.swchoose.indexOf('传媒')>-1)){ swsum +=Number(this.formData.field108.value5)}

      //     if(this.swchoose.indexOf('电子')<0) {this.$set(this.formData.field108,'value6',0)}
      //     else if(this.swchoose.indexOf('电子')>-1){ swsum +=Number(this.formData.field108.value6)}

      //     if(this.swchoose.indexOf('休闲服务')<0)  {this.$set(this.formData.field108,'value7',0)}
      //     else if((this.swchoose.indexOf('休闲服务')>-1)){ swsum +=Number(this.formData.field108.value7)}

      //     if(this.swchoose.indexOf('房地产')<0) {this.$set(this.formData.field108,'value8',0)}
      //     else if(this.swchoose.indexOf('房地产')>-1){ swsum +=Number(this.formData.field108.value8)}

      //     if(this.swchoose.indexOf('电子设备')<0)  {this.$set(this.formData.field108,'value9',0)}
      //     else if((this.swchoose.indexOf('电子设备')>-1)){ swsum +=Number(this.formData.field108.value9)}

      //     if(this.swchoose.indexOf('钢铁')<0) {this.$set(this.formData.field108,'value10',0)}
      //     else if(this.swchoose.indexOf('钢铁')>-1){ swsum +=Number(this.formData.field108.value10)}

      //     if(this.swchoose.indexOf('通信')<0)  {this.$set(this.formData.field108,'value11',0)}
      //     else if((this.swchoose.indexOf('通信')>-1)){ swsum +=Number(this.formData.field108.value11)}

      //     if(this.swchoose.indexOf('建筑材料')<0) {this.$set(this.formData.field108,'value12',0)}
      //     else if(this.swchoose.indexOf('建筑材料')>-1){ swsum +=Number(this.formData.field108.value12)}

      //     if(this.swchoose.indexOf('综合')<0)  {this.$set(this.formData.field108,'value13',0)}
      //     else if((this.swchoose.indexOf('综合')>-1)){ swsum +=Number(this.formData.field108.value13)}

      //     if(this.swchoose.indexOf('公用事业')<0) {this.$set(this.formData.field108,'value14',0)}
      //     else if(this.swchoose.indexOf('公用事业')>-1){ swsum +=Number(this.formData.field108.value14)}

      //     if(this.swchoose.indexOf('机械设备')<0)  {this.$set(this.formData.field108,'value15',0)}
      //     else if((this.swchoose.indexOf('机械设备')>-1)){ swsum +=Number(this.formData.field108.value15)}

      //     if(this.swchoose.indexOf('商业贸易')<0) {this.$set(this.formData.field108,'value16',0)}
      //     else if(this.swchoose.indexOf('商业贸易')>-1){ swsum +=Number(this.formData.field108.value16)}

      //     if(this.swchoose.indexOf('纺织服装')<0)  {this.$set(this.formData.field108,'value17',0)}
      //     else if((this.swchoose.indexOf('纺织服装')>-1)){ swsum +=Number(this.formData.field108.value17)}

      //     if(this.swchoose.indexOf('化工')<0) {this.$set(this.formData.field108,'value18',0)}
      //     else if(this.swchoose.indexOf('化工')>-1){ swsum +=Number(this.formData.field108.value18)}

      //     if(this.swchoose.indexOf('建筑装饰')<0)  {this.$set(this.formData.field108,'value19',0)}
      //     else if((this.swchoose.indexOf('建筑装饰')>-1)){ swsum +=Number(this.formData.field108.value19)}

      //     if(this.swchoose.indexOf('银行')<0) {this.$set(this.formData.field108,'value20',0)}
      //     else if(this.swchoose.indexOf('银行')>-1){ swsum +=Number(this.formData.field108.value20)}

      //     if(this.swchoose.indexOf('食品饮料')<0)  {this.$set(this.formData.field108,'value21',0)}
      //     else if((this.swchoose.indexOf('食品饮料')>-1)){ swsum +=Number(this.formData.field108.value21)}

      //     if(this.swchoose.indexOf('交通运输')<0) {this.$set(this.formData.field108,'value22',0)}
      //     else if(this.swchoose.indexOf('交通运输')>-1){ swsum +=Number(this.formData.field108.value22)}

      //     if(this.swchoose.indexOf('汽车')<0)  {this.$set(this.formData.field108,'value23',0)}
      //     else if((this.swchoose.indexOf('汽车')>-1)){ swsum +=Number(this.formData.field108.value23)}

      //     if(this.swchoose.indexOf('轻工制造')<0) {this.$set(this.formData.field108,'value24',0)}
      //     else if(this.swchoose.indexOf('轻工制造')>-1){ swsum +=Number(this.formData.field108.value24)}

      //     if(this.swchoose.indexOf('医药生物')<0)  {this.$set(this.formData.field108,'value25',0)}
      //     else if((this.swchoose.indexOf('医药生物')>-1)){ swsum +=Number(this.formData.field108.value25)}

      //     if(this.swchoose.indexOf('有色金属')<0) {this.$set(this.formData.field108,'value26',0)}
      //     else if(this.swchoose.indexOf('有色金属')>-1){ swsum +=Number(this.formData.field108.value26)}

      //     if(this.swchoose.indexOf('计算机')<0)  {this.$set(this.formData.field108,'value27',0)}
      //     else if((this.swchoose.indexOf('计算机')>-1)){ swsum +=Number(this.formData.field108.value27)}

      //     if(this.swchoose.indexOf('家用电器')<0) {this.$set(this.formData.field108,'value28',0)}
      //     else if(this.swchoose.indexOf('家用电器')>-1){ swsum +=Number(this.formData.field108.value28)}

      //     if(swsum!=100){
      //          if(this.swchoose.indexOf('非银金融')<0&&this.swchoose.indexOf('农林牧渔')<0&&this.swchoose.indexOf('国防军工')<0&&this.swchoose.indexOf('采掘')<0&&this.swchoose.indexOf('传媒')<0&&this.swchoose.indexOf('电子')<0&&this.swchoose.indexOf('休闲服务')<0&&this.swchoose.indexOf('房地产')<0&&this.swchoose.indexOf('电子设备')<0&&this.swchoose.indexOf('钢铁')<0&&this.swchoose.indexOf('通信')<0&&this.swchoose.indexOf('建筑材料')<0&&this.swchoose.indexOf('综合')<0&&this.swchoose.indexOf('公用事业')<0&&this.swchoose.indexOf('机械设备')<0&&this.swchoose.indexOf('商业贸易')<0&&this.swchoose.indexOf('纺织服装')<0&&this.swchoose.indexOf('化工')<0&&this.swchoose.indexOf('建筑装饰')<0&&this.swchoose.indexOf('银行')<0&&this.swchoose.indexOf('食品饮料')<0&&this.swchoose.indexOf('交通运输')<0&&this.swchoose.indexOf('汽车')<0&&this.swchoose.indexOf('轻工制造')<0&&this.swchoose.indexOf('医药生物')<0&&this.swchoose.indexOf('有色金属')<0&&this.swchoose.indexOf('计算机')<0&&this.swchoose.indexOf('家用电器')<0){

      //       }
      //       else{
      //         this.$message('行业判断的申万一级筛选条件值加起来不为100，无法提交')
      //         this.submitflag = false
      //         ////console.log('申万一级'+swsum)
      //       }
      //     }
      // }else{

      //     this.$set(this.formData.field108,'value1',0)
      //     this.$set(this.formData.field108,'value2',0)
      //     this.$set(this.formData.field108,'value3',0)
      //     this.$set(this.formData.field108,'value4',0)
      //     this.$set(this.formData.field108,'value5',0)
      //     this.$set(this.formData.field108,'value6',0)
      //     this.$set(this.formData.field108,'value7',0)
      //     this.$set(this.formData.field108,'value8',0)
      //     this.$set(this.formData.field108,'value9',0)
      //     this.$set(this.formData.field108,'value10',0)
      //     this.$set(this.formData.field108,'value11',0)
      //     this.$set(this.formData.field108,'value12',0)
      //     this.$set(this.formData.field108,'value13',0)
      //     this.$set(this.formData.field108,'value14',0)
      //     this.$set(this.formData.field108,'value15',0)
      //     this.$set(this.formData.field108,'value16',0)
      //     this.$set(this.formData.field108,'value17',0)
      //     this.$set(this.formData.field108,'value18',0)
      //     this.$set(this.formData.field108,'value19',0)
      //     this.$set(this.formData.field108,'value20',0)
      //     this.$set(this.formData.field108,'value21',0)
      //     this.$set(this.formData.field108,'value22',0)
      //     this.$set(this.formData.field108,'value23',0)
      //     this.$set(this.formData.field108,'value24',0)
      //     this.$set(this.formData.field108,'value25',0)
      //     this.$set(this.formData.field108,'value26',0)
      //     this.$set(this.formData.field108,'value27',0)
      //     this.$set(this.formData.field108,'value28',0)
      // }

      if (this.submitflag == true) {
        let val = this.temp;
        //发送请求前置准备 ， 未选过滤数据数据清空
        if (val.indexOf(1) > -1) {
          // this.showbodong = true
          //////console.log('波动率')
        } else {
          this.$set(this.formData.field104[0], "date", []);
          this.$set(this.formData.field104[0], "value1", null);
          this.$set(this.formData.field104[0], "value2", null);
          this.$set(this.formData.field104[0], "rank", "100");
        }
        if (val.indexOf(2) > -1) {
          // this.showbodong1 = true
          //////console.log('波动率')
        } else {
          this.$set(this.formData.field104[1], "date", []);
          this.$set(this.formData.field104[1], "value1", null);
          this.$set(this.formData.field104[1], "value2", null);
          this.$set(this.formData.field104[1], "rank", "100");
        }
        if (val.indexOf(3) > -1) {
          // this.showbodong2 = true
          //////console.log('波动率')
        } else {
          this.$set(this.formData.field104[2], "date", []);
          this.$set(this.formData.field104[2], "value1", null);
          this.$set(this.formData.field104[2], "value2", null);
          this.$set(this.formData.field104[2], "rank", "100");
        }
        if (val.indexOf(4) > -1) {
          // this.showbodong3 = true
          //////console.log('波动率')
        } else {
          this.$set(this.formData.field104[3], "date", []);
          this.$set(this.formData.field104[3], "value1", null);
          this.$set(this.formData.field104[3], "value2", null);
          this.$set(this.formData.field104[3], "rank", "100");
        }
        if (val.indexOf(5) > -1) {
          // this.showbodong4 = true
          //////console.log('波动率')
        } else {
          this.$set(this.formData.field104[4], "date", []);
          this.$set(this.formData.field104[4], "value1", null);
          this.$set(this.formData.field104[4], "value2", null);
          this.$set(this.formData.field104[4], "rank", "100");
        }
        if (val.indexOf(6) > -1) {
          // this.showbodong5 = true
          //////console.log('波动率')
        } else {
          this.$set(this.formData.field104[5], "date", []);
          this.$set(this.formData.field104[5], "value1", null);
          this.$set(this.formData.field104[5], "value2", null);
          this.$set(this.formData.field104[5], "rank", "100");
        }
        if (val.indexOf(7) > -1) {
          // this.showbodong6 = true
          //////console.log('波动率')
        } else {
          this.$set(this.formData.field104[6], "date", []);
          this.$set(this.formData.field104[6], "value1", null);
          this.$set(this.formData.field104[6], "value2", null);
          this.$set(this.formData.field104[6], "rank", "100");
        }
        if (val.indexOf(8) > -1) {
          // this.showbodong7 = true
          //////console.log('波动率')
        } else {
          this.$set(this.formData.field104[7], "date", []);
          this.$set(this.formData.field104[7], "value1", null);
          this.$set(this.formData.field104[7], "value2", null);
          this.$set(this.formData.field104[7], "rank", "100");
        }
        if (val.indexOf(9) > -1) {
          // this.showbodong8 = true
          //////console.log('波动率')
        } else {
          this.$set(this.formData.field104[8], "date", []);
          this.$set(this.formData.field104[8], "value1", null);
          this.$set(this.formData.field104[8], "value2", null);
          this.$set(this.formData.field104[8], "rank", "100");
        }
        if (val.indexOf(10) > -1) {
          // this.showbodong9 = true
          // ////console.log('波动率')
        } else {
          this.$set(this.formData.field104[9], "date", []);
          this.$set(this.formData.field104[9], "value1", null);
          this.$set(this.formData.field104[9], "rank", "100");
        }
        //发送请求
        //权威空 职位【】  返回所有结果
        if (
          this.formData.field101 == null &&
          this.formData.field1011 == null &&
          this.formData.field102 == null &&
          this.temp == [] &&
          this.temp2 == [] &&
          this.temp3 == [] &&
          this.temp4 == [] &&
          this.temp5 == []
        ) {
          this.formData = [];
        }
        if (this.formData.field102 == null || this.formData.field102 == "") {
          this.formData.field102 = "0";
        }
        this.showmsgloading = true;
        let formtemp = true;
        if (
          this.temp3.length <= 0 &&
          this.temp4.length <= 0 &&
          this.temp5.length <= 0 &&
          this.formData.field109 == "" &&
          this.formData.field110 == ""
        ) {
          formtemp = false;
          this.showsearch = false;
        }
        ////console.log("发送请求")
        let tempformdata = this.formData;
        if (tempformdata.field101 == "") tempformdata.field101 = null;
        if (tempformdata.field1011 == "") tempformdata.field1011 = null;
        if (tempformdata.field102 == "") tempformdata.field102 = null;
        if (tempformdata.field1021 == "") tempformdata.field1021 = null;
        for (let i = 0; i < tempformdata.field104.length; i++) {
          if (tempformdata.field104[i].value1 == "")
            tempformdata.field104[i].value1 = null;
          if (tempformdata.field104[i].value2) {
            if (tempformdata.field104[i].value2 == "") {
              tempformdata.field104[i].value2 = null;
            }
          }
        }
        for (let i = 0; i < tempformdata.field105.length; i++) {
          if (tempformdata.field105[i].value1 == "")
            tempformdata.field105[i].value1 = null;
          if (tempformdata.field105[i].value2) {
            if (tempformdata.field105[i].value2 == "") {
              tempformdata.field105[i].value2 = null;
            }
          }
        }
        let that = this;
        axios
          .post(that.$baseUrl + "/manageralphascore/", {
            filter: tempformdata,
            alphachooseflag: formtemp,
            poollist: this.poollist,
            type: this.type
          })
          .then(res => {
            if (formtemp != false) {
              that.showsearch = true;
            }
            if (res.data.data.length == 0) {
              this.$message("未筛选出结果");
              return;
            }
            res.data.data.sort((a, b) => {
              return b.final_rank - a.final_rank;
            });

            that.pageIndex = 1;
            that.showmsgloading = false;
            that.alldata = res.data.data.sort(that.my_desc_sort("final_rank"));
            that.tableData = that.alldata.slice(0, 19);
            that.pageTotal = res.data.data.length;
            try {
              // 存放主题
              window.localStorage.setItem(
                "formDatathemevaluemanager" + this.type,
                JSON.stringify(that.themevalue)
              );
              window.localStorage.setItem(
                "formDatainsertindexmanager" + this.type,
                JSON.stringify(that.insertindex)
              );
              window.localStorage.setItem(
                "formDataquanzhongthememanager" + this.type,
                JSON.stringify(that.quanzhongtheme)
              );
              // end主题
              // 存放sw1
              window.localStorage.setItem(
                "formDataswonevaluemanager" + this.type,
                JSON.stringify(that.swonevalue)
              );
              window.localStorage.setItem(
                "formDatainsertindexswonemanager" + this.type,
                JSON.stringify(that.insertindexswone)
              );
              window.localStorage.setItem(
                "formDataquanzhongswonemanager" + this.type,
                JSON.stringify(that.quanzhongswone)
              );
              // endsw1
              // 存放sw2
              window.localStorage.setItem(
                "formDataswonevalue2manager" + this.type,
                JSON.stringify(that.swonevalue2)
              );
              window.localStorage.setItem(
                "formDatainsertindexswone2manager" + this.type,
                JSON.stringify(that.insertindexswone2)
              );
              window.localStorage.setItem(
                "formDataquanzhongswone2manager" + this.type,
                JSON.stringify(that.quanzhongswone2)
              );
              // endsw1
              // 存放sw3
              window.localStorage.setItem(
                "formDataswonevalue3manager" + this.type,
                JSON.stringify(that.swonevalue3)
              );
              window.localStorage.setItem(
                "formDatainsertindexswone3manager" + this.type,
                JSON.stringify(that.insertindexswone3)
              );
              window.localStorage.setItem(
                "formDataquanzhongswone3manager" + this.type,
                JSON.stringify(that.quanzhongswone3)
              );
              // endsw1
              // 存放sw4
              window.localStorage.setItem(
                "formDataswonevalue4manager" + this.type,
                JSON.stringify(that.swonevalue4)
              );
              window.localStorage.setItem(
                "formDatainsertindexswone4manager" + this.type,
                JSON.stringify(that.insertindexswone4)
              );
              window.localStorage.setItem(
                "formDataquanzhongswone4manager" + this.type,
                JSON.stringify(that.quanzhongswone4)
              );
              // endsw1
              window.localStorage.setItem(
                "formDataequitymanager" + this.type,
                JSON.stringify(that.formData)
              );
              window.localStorage.setItem(
                "tempequity2manager" + this.type,
                JSON.stringify(that.temp2)
              );
              window.localStorage.setItem(
                "tempequity3manager" + this.type,
                JSON.stringify(that.temp3)
              );
              window.localStorage.setItem(
                "tempequity4manager" + this.type,
                JSON.stringify(that.temp4)
              );
              window.localStorage.setItem(
                "tempequity5manager" + this.type,
                JSON.stringify(that.temp5)
              );
              window.localStorage.setItem(
                "tempequity1manager" + this.type,
                JSON.stringify(that.temp)
              );
              window.localStorage.setItem(
                "hgchooseequitymanager" + this.type,
                JSON.stringify(that.hgchoose)
              );
              window.localStorage.setItem(
                "kcchooseequitymanager" + this.type,
                JSON.stringify(that.kcchoose)
              );
              window.localStorage.setItem(
                "ejchooseequitymanager" + this.type,
                JSON.stringify(that.ejchoose)
              );
              window.localStorage.setItem(
                "czchooseequitymanager" + this.type,
                JSON.stringify(that.czchoose)
              );
              window.localStorage.setItem(
                "dpchooseequitymanager" + this.type,
                JSON.stringify(that.dpchoose)
              );
              window.localStorage.setItem(
                "swchooseequitymanager" + this.type,
                JSON.stringify(that.swchoose)
              );
              window.localStorage.setItem(
                "alldataequitymanager" + this.type,
                JSON.stringify(that.alldata)
              );
            } catch (err) { }
          })
          .catch(error => {
            that.$message("error,筛选失败请联系我们，联系电话：18717782280 ");
            ////console.log(error)
            that.showmsgloading = false;
            that.pageIndex = 1;
            that.alldata = [];
            that.tableData = [];
            that.pageTotal = 0;
          });
        axios
          .post(that.$baseUrl + "/buy_point_alpha/", {
            filter: that.formData,
            type: "equity",
            ismanager: true
          })
          .then(res => {
            //埋点成功
          });
      }
    },
    resetForm () {
      this.useraddmodalshow = true;
    },
    handleSearch () {
      //搜索基金
    },
    cancelchoose1 (event) {
      //取消单线
      if (event.target.tagName === "INPUT")
        // console. log(this radio,000"event. target);
        // this.radio= this.radio ? '' :'1'
        this.formData.field109 = this.formData.field109 ? "" : "1";
    },
    cancelchoose (event) {
      //取消单线
      if (event.target.tagName === "INPUT")
        // console. log(this radio,000"event. target);
        // this.radio= this.radio ? '' :'1'
        this.formData.field110 = this.formData.field110 ? "" : "1";
    },
    deldom (index) {
      // ////console.log('del')
      // ////console.log(index)

      if (this.insertindex > 1) {
        this.themevalue.splice(index, 1);
        this.quanzhongtheme.splice(index, 1);
        this.insertindex -= 1;
        this.forbidtheme();
      } else if (this.insertindex == 1) {
        this.themevalue = [];
        this.quanzhongtheme = [];
      }
    },
    // 删除sw行业
    deldomswone (index) {
      ////console.log('del')
      ////console.log(index)

      if (this.insertindexswone > 1) {
        this.swonevalue.splice(index, 1);
        this.quanzhongswone.splice(index, 1);
        this.insertindexswone -= 1;
        this.forbidswone();
      } else if (this.insertindexswone == 1) {
        this.swonevalue = [];
        this.quanzhongswone = [];
      }
    },
    deldomswone2 (index) {
      // ////console.log('del')
      // ////console.log(index)

      if (this.insertindexswone2 > 1) {
        this.swonevalue2.splice(index, 1);
        this.quanzhongswone2.splice(index, 1);
        this.insertindexswone2 -= 1;
        this.forbidswone2();
      } else if (this.insertindexswone2 == 1) {
        this.swonevalue2 = [];
        this.quanzhongswone2 = [];
      }
    },
    deldomswone3 (index) {
      // ////console.log('del')
      // ////console.log(index)

      if (this.insertindexswone3 > 1) {
        this.swonevalue3.splice(index, 1);
        this.quanzhongswone3.splice(index, 1);
        this.insertindexswone3 -= 1;
        this.forbidswone3();
      } else if (this.insertindexswone3 == 1) {
        this.swonevalue3 = [];
        this.quanzhongswone3 = [];
      }
    },
    deldomswone4 (index) {
      // ////console.log('del')
      // ////console.log(index)

      if (this.insertindexswone4 > 1) {
        this.swonevalue4.splice(index, 1);
        this.quanzhongswone4.splice(index, 1);
        this.insertindexswone4 -= 1;
        this.forbidswone4();
      } else if (this.insertindexswone4 == 1) {
        this.swonevalue4 = [];
        this.quanzhongswone4 = [];
      }
    },
    // 增加申万行业节点
    adddomswone () {
      this.insertindexswone += 1;
    },
    adddomswone2 () {
      this.insertindexswone2 += 1;
    },
    adddomswone3 () {
      this.insertindexswone3 += 1;
    },
    adddomswone4 () {
      this.insertindexswone4 += 1;
    },
    adddom () {
      this.insertindex += 1;
      // document.getElementById("insertdom").innerHTML = `
      //    <div style="width:5px">12314</div>
      //      <el-input class='marginright20 inputbox'  @mousewheel.native.prevent onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"   type="number" v-model="formData.field108.value27"></el-input>
      //           `
    },
    showshuoming () {
      this.$refs["visdescriobe"].showshuoming();
    },
    handlePageChange () {
      this.tableData = this.alldata.slice(
        (this.pageIndex - 1) * 20,
        this.pageIndex * 20 - 1
      );
    },
    godetailP (id, name) {
      //带参进去
      this.$router.push({
        path: "/fundmanagerdetail/" + id,
        hash: "",
        query: { id: id, name: name }
      });
    },
    printconsole () {
      const { export_json_to_excel } = require("@/vendor/Export2Excel");
      var list = [];
      list.push(this.dataexplain);
      let tHeader = [];
      let filterVal = [];

      tHeader = [
        "基金经理姓名",
        "基金公司",
        "选择能力",
        "组合能力",
        "盈利能力",
        "匹配度",
        "总分",
        "近一周收益",
        "近一月收益",
        "近一季收益",
        "近一年收益",
        "规模"
        // '重仓股'
      ];
      filterVal = [
        "name",
        "fund_co",
        "picking_score",
        "portfolio_score",
        "profitability_score",
        "selected_weight",
        "final_rank",
        "1w",
        "1m",
        "1q",
        "1y",
        "scale"
        // 'big10'
      ];
      // ////console.log(this.colums)
      for (let i = 0; i < this.alldata.length; i++) {
        list[i + 1] = [];
        list[i + 1][0] = this.alldata[i].name;
        list[i + 1][1] = this.alldata[i].fund_co;

        if (this.showsearch == true) {
          list[i + 1][2] = this.alldata[i].picking_score;
          list[i + 1][3] = this.alldata[i].portfolio_score;
          list[i + 1][4] = this.alldata[i].profitability_score;
          list[i + 1][5] = this.alldata[i].selected_weight;
          list[i + 1][6] = this.alldata[i].final_rank;

          list[i + 1][11] = Number(this.alldata[i]["scale"]).toFixed(2) + "亿";
        }
        list[i + 1][7] = this.alldata[i]["1w"];
        list[i + 1][8] = this.alldata[i]["1m"];
        list[i + 1][9] = this.alldata[i]["1q"];
        list[i + 1][10] = this.alldata[i]["1y"];
        // list[i + 1][13] = this.alldata[i].bigs;
      }
      console.log(tHeader, list, "主动权益基金经理筛选结果");
      export_json_to_excel(tHeader, list, "主动权益基金经理筛选结果");
    },
    redoForm () {
      this.formData = {
        index_code: [], //指标列表

        haveability: [],
        field101: null,
        field1011: null,
        field102: null,
        field1021: null,
        bigindustry: [],
        field104: [
          {
            id: 1,
            date: ["now", "3y"],
            value1: null,
            value2: null,
            rank: "100"
          },
          {
            id: 2,
            date: ["now", "3y"],
            value1: null,
            value2: null,
            rank: "100"
          },
          {
            id: 3,
            date: ["now", "3y"],
            value1: null,
            value2: null,
            rank: "100"
          },
          {
            id: 4,
            date: ["now", "3y"],
            value1: null,
            value2: null,
            rank: "100"
          },
          {
            id: 5,
            date: ["now", "3y"],
            value1: null,
            value2: null,
            rank: "100"
          },
          {
            id: 6,
            date: ["now", "3y"],
            value1: null,
            value2: null,
            rank: "100"
          },
          {
            id: 7,
            date: ["now", "3y"],
            value1: null,
            value2: null,
            rank: "100"
          },
          {
            id: 8,
            date: ["now", "3y"],
            value1: null,
            value2: null,
            rank: "100"
          },
          {
            id: 9,
            date: ["now", "3y"],
            value1: null,
            value2: null,
            rank: "100"
          },
          { id: 10, date: ["now", "3y"], value1: null, rank: "100" }
        ],
        field105: [
          { id: 1, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 2, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 3, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 4, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 5, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 6, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 7, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 8, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 9, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 10, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 11, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 12, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 13, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 14, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 15, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 16, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 17, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 18, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 19, date: ["now", "3y"], value1: null, rank: "100" },
          { id: 20, date: ["now", "3y"], value1: null, rank: "100" }
        ],
        field106: {
          value1: 0,
          value2: 0,
          value3: 0,
          value4: 0
        },
        field1061: {
          value1: 0,
          value2: 0,
          value3: 0,
          value4: 0
        },
        field1062: {
          value1: 0,
          value2: 0,
          value3: 0,
          value4: 0,
          value5: 0,
          value6: 0
        },
        field107: {
          value1: 0,
          value2: 0
        },
        field1071: {
          value1: 0,
          value2: 0,
          value3: 0
        },
        field108: {
          value1: 0,
          value2: 0,
          value3: 0,
          value4: 0,
          value5: 0,
          value6: 0,
          value7: 0,
          value8: 0,
          value9: 0,
          value10: 0,
          value11: 0,
          value12: 0,
          value13: 0,
          value14: 0,
          value15: 0,
          value16: 0,
          value17: 0,
          value18: 0,
          value19: 0,
          value20: 0,
          value21: 0,
          value22: 0,
          value23: 0,
          value24: 0,
          value25: 0,
          value26: 0,
          value27: 0,
          value28: 0
        },
        field109: 1,
        field110: 1,
        theme: [],
        swindustry1: [],
        swindustry2: [],
        swindustry3: [],
        hsindustry1: []
      };
      this.temp = [];
      this.temp2 = [];
      this.temp3 = [];
      this.temp4 = [];
      this.temp5 = [];
      this.hgchoose = [];
      this.kcchoose = [];
      this.ejchoose = [];
      this.themevalue = [];
      this.quanzhongtheme = [];
      this.swonevalue = [];
      this.quanzhongswone = [];
      this.insertindex = 1;
      this.insertindexswone = 1;
      for (let k = 0; k < this.themeoptions.length; k++) {
        this.themeoptions[k].forbidstate = false;
      }
      for (let p = 0; p < this.swoneoptions.length; p++) {
        this.swoneoptions[p].forbidstate = false;
      }
    }
  }
};
</script>
<style>
.autoFilterPop .el-autocomplete-suggestion__list li {
	white-space: inherit !important;
}
.managerequity .el-form-item__label {
	text-align: left;
}
.sceollbox .el-scrollbar__wrap {
	overflow-x: hidden !important;
}
.choosebox {
	display: flex;
	align-items: center;
	background: #f1f1f5;
	margin: 20px;
}
.width15x {
	width: 240px !important;
}
/* input[readonly]{
background-color: #f1f1f5
} */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
	-webkit-appearance: none !important;
	-moz-appearance: none !important;
	-o-appearance: none !important;
	-ms-appearance: none !important;
	appearance: none !important;
	margin: 0;
}
input[type='number'] {
	-webkit-appearance: textfield;
	-moz-appearance: textfield;
	-o-appearance: textfield;
	-ms-appearance: textfield;
	appearance: textfield;
}
.el-input__inner {
	/* border: 0;
    border-bottom: #000 solid 1px; */
}
.inputbox {
	border: 0px;
	width: 80px !important;
	outline: medium;
	text-align: center;

	padding: 0;
	-webkit-appearance: none;
	appearance: none;
}
.el-form-item {
	margin-bottom: 0;
}
.managerequity .el-form-item__label {
	width: 200px !important;
	text-align: left;
}
.managerequity .el-form-item__content {
	margin-left: 200px !important;
}
</style>
<style lang="scss" scoped>
.managerDetailPage {
	margin-left: 2%;
	width: 96%;
	background: #d0d7df;
	padding: 20px;
}

.row {
	margin: -10px;
	display: flex;
}

.left {
	width: 155px;
	flex: 0 0 auto;
	margin-right: 10px;
}

.right {
	position: relative;
	flex: 1 1 100px;
}
</style>
<style lang="scss">
.comment-section {
	padding: 5px 15px 0 15px;
}

.comment {
	background: linear-gradient(90deg, #3b64f2, #1b8eff);
	color: white;
	font-size: 12px;
	padding: 12px 24px;
}

.comment.center {
	text-align: center;
}

.section {
	padding: 15px 15px 0 15px;
}

.double-table {
	display: flex;
	flex-basis: 10px;
	justify-content: space-between;

	.single-table {
		flex: 1;
	}

	.cell {
		font-size: 14px !important;
		font-weight: 400 !important;
		text-align: center !important;
		padding: 0 !important;
	}

	th {
		padding: 5px 0 !important;
	}
}

.split-cell {
	display: flex;
	align-items: center;
	justify-content: center;

	div {
		width: 40px;
	}
}
</style>
<style lang="scss" scoped>
.managerequity {
	.tagForFilter {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		.el-tag {
			margin-right: 10px !important;
			// margin-left:10px !important;
			margin-top: 5px;
			line-height: 32px;
			height: 32px;
		}
		.button-new-tag {
			// margin-left: 10px;
			height: 32px;
			// height: 24px;
			// line-height: 25px;
			display: flex;
			justify-content: center;
			align-items: center;
			margin-top: 5px;
			padding-top: 0;
			padding-bottom: 0;
		}
		.input-new-tag {
			width: 90px;
			height: 25px;
			line-height: 25px;
			// margin-left: 10px;
			vertical-align: bottom;
		}
	}
}
.title {
	font-weight: 600;
	padding: 10px 15px;
	width: 99%;
}
.sub-title {
	font-size: 14px;
	font-weight: 600;
	border-left: 2px solid dodgerblue;
	margin-bottom: 6px;
	padding-left: 3px;
	line-height: 22px;
	height: 22px;
	flex: 1 1 auto;
}
.title-change-fund {
	display: flex;
	align-items: center;
	margin-bottom: 4px;
	label {
		font-size: 14px;
		margin-right: 10px;
	}
}
</style>
