<!--  -->
<template>
  <div class=" chart_one"
       v-if="datalist.length">
    <div class="title">子池类</div>
    <div class="pool_categorydetail mt-16"
         v-loading="loading">
      <div v-for="(item, index) in datalist"
           :key="index"
           style="
          cursor: pointer;
          padding: 16px 24px 24px 24px;
          background: white;
          width: calc(25% - 16px);
          margin: 0 8px;
          margin-bottom: 16px;
          border: 1px solid #e9e9e9;
          border-radius: 4px;
        ">
        <div class="flex_between">
          <el-tooltip effect="dark"
                      :content="item.name"
                      placement="top">
            <div style="
                font-style: normal;
                font-weight: 500;
                font-size: 20px;
                line-height: 28px;
                color: rgba(0, 0, 0, 0.85);
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                flex: 1;
              "
                 @click="goToAnother(item.poolId, item.name,item.type)">{{ item.name }}</div>
          </el-tooltip>
          <div v-if="is_self_manage">
            <el-popconfirm title="确定删除吗？"
                           @confirm="deletePool(item.id)">
              <el-link slot="reference"
                       class="mr-12"
                       style="margin-top: -1px">
                <i class="el-icon-delete-solid"></i>
              </el-link>
            </el-popconfirm>
          </div>
        </div>

        <div>
          <div style="
              display: flex;
              justify-content: space-between;
              align-items: center;
            ">
            <div>
              <span style="
                  font-style: normal;
                  font-weight: 500;
                  font-size: 20px;
                  line-height: 28px;
                  color: rgba(0, 0, 0, 0.85);
                ">{{ item.netasset | fix10Y }}</span>
              <span class="pool_14size">&nbsp;亿元</span>
            </div>
            <div>
              <span class="pool_14size">占比：{{ item.netasset_weight | fix2p }}</span>
            </div>
          </div>
          <div style="margin-top: 12px">
            <el-progress class="progressbar"
                         :show-text="false"
                         :percentage="item.netasset_weight * 100"
                         color="#4096FF"></el-progress>
          </div>
          <div style="
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-top: 16px;
            ">
            <div>
              <span style="
                  font-style: normal;
                  font-weight: 500;
                  font-size: 20px;
                  line-height: 28px;
                  color: rgba(0, 0, 0, 0.85);
                ">{{ item.number }}</span>
              <span class="pool_14size">支</span>
            </div>
            <div>
              <span class="pool_14size">占比：{{ item.number_weight | fix2p }}</span>
            </div>
          </div>
          <div style="margin-top: 12px">
            <el-progress class="progressbar"
                         :show-text="false"
                         :percentage="item.number_weight * 100"
                         color="#4096ff"></el-progress>
          </div>
        </div>
        <div>
          <v-chart :options="item.option"
                   :lazy-update="true"
                   class="charts_one_class"
                   style="width: 100%; height: 200px"
                   autoresize />
        </div>
      </div>
    </div>
    <categray-compare v-show="show"
                      ref="categrayCompare"></categray-compare>
    <!-- <el-divider>
      <el-link @click="showCompare">
        {{
        show ? "收起子池对比" : "查看子池对比"
        }}
      </el-link>
    </el-divider> -->
  </div>
</template>

<script>
import { lineChartOption } from "@/utils/chartStyle.js";
import categrayCompare from "@/pages/fundNewPool/analysis/components/components/categrayCompare";
// import { deletePool } from "@/api/pages/SystemMixed.js";
import {
  deletePool,
} from "@/api/pages/tools/pool.js";
import { getChildrenCategory } from "@/api/pages/tools/pool.js";
export default {
  props: {
    user_id: {},
    ismanager: {
      type: Boolean
    }
  },
  components: { categrayCompare },
  data () {
    return {
      datalist: [],
      info: {},
      loading: true,
      show: false,
      active_look_status: false,
      is_self_manage: false
    };
  },
  filters: {
    fix2p (value) {
      if (value == "" || value == null || value == "nan" || value == "--") {
        return "—";
      } else {
        return (value * 100).toFixed(2) + "%";
      }
    },
    fix10Y (value) {
      return (Number(value)).toFixed(2);
    }
  },
  methods: {
    getData (info) {
      this.active_look_status = false;
      this.show = false;
      this.info = info;
      this.is_self_manage = this.info.user_id == localStorage.getItem("id");
      this.getChildrenCategory();
    },
    // 获取数据
    async getChildrenCategory () {
      this.loading = true;
      let data = await getChildrenCategory({
        id: this.info.code,
        ismanager: this.ismanager,
        type: this.info.type
      });
      if (data?.mtycode == 200) {
        this.datalist = data?.data.map(item => {
          return {
            ...item,
            option: lineChartOption({
              tooltip: {
                formatter: function (obj) {
                  var value = obj?.[0].axisValue + `<br />`;
                  for (let i = 0; i < obj.length; i++) {
                    value +=
                      `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:` +
                      obj?.[i].color +
                      `;"></span>` +
                      obj?.[i].seriesName +
                      ":" +
                      Number(obj?.[i].value).toFixed(2) +
                      "%" +
                      `<br />`;
                  }
                  return value;
                }
              },
              toolbox: "none",
              xAxis: [
                {
                  type: "category",
                  data: item.date
                }
              ],
              yAxis: [
                {
                  type: "value"
                }
              ],
              series: [
                {
                  name: "累计收益",
                  type: "line",
                  data: item?.cum_return?.map(v =>
                    v * 1 && !isNaN(v) ? (v * 100).toFixed(2) : "--"
                  )
                }
              ]
            })
          };
        });
        this.info["children_pool_list"] = this.datalist.map(item => {
          return {
            ...item,
            code: item.id
          };
        });
      } else {

        this.datalist = [];
        console.log('object', this.datalist.length);
      }
      this.loading = false;
    },
    // 删除基金池
    async deletePool (id) {
      this.datalist.splice(
        this.datalist.findIndex(v => v.id == id),
        1
      );
      let data = await deletePool({ id, ismanager: this.ismanager });
      this.$message.success("删除成功");
    },
    // 查看对比
    showCompare () {
      this.show = !this.show;
      this.$nextTick(() => {
        if (this.show) {
          if (this.active_look_status) {
            this.$refs["categrayCompare"]?.refresInfo(this.info);
          } else {
            this.active_look_status = true;
            this.$refs["categrayCompare"]?.getData(this.info);
          }
        } else {
          this.$refs["categrayCompare"]?.hideAnalysis();
        }
      });
    },
    goToAnother (id, name, type) {
      this.$router.push({
        path: "/poolDetail/" + id,
        hash: "",
        query: {
          id: id,
          name: name,
          user_id: this.info.user_id,
          isdb: 0,
          isChildren: 1,
          type: type
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.pool_categorydetail {
	margin: 0px 16px;
	display: flex;
	flex-wrap: wrap;
	.pool_14size {
		font-style: normal;
		font-weight: 400;
		font-size: 14px;
		line-height: 22px;
		text-align: right;
		color: rgba(0, 0, 0, 0.65);
	}
	.progressbar ::v-deep .el-progress-bar {
		padding-right: 0px;
	}
}
</style>
