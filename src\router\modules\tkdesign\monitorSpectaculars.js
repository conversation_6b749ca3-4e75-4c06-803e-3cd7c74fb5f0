export default [
	{
		path: '/performance',
		component: () => import(/* webpackChunkName: "tkdesignAfter" */ '../../../pages/tkdesign/monitorSpectaculars/performance/index.vue'),
		meta: { title: '业绩看板', tagShow: false }
	},
	{
		path: '/structure',
		component: () =>
			import(/* webpackChunkName: "tkdesignAfter" */ '../../../pages/tkdesign/monitorSpectaculars/structuralSignage/structuralSignage.vue'),
		meta: { title: '结构看板', tagShow: false }
	},
	{
		path: '/pivotTable',
		component: () => import(/* webpackChunkName: "tkdesignAfter" */ '../../../pages/tkdesign/monitorSpectaculars/pivotTable.vue'),
		meta: { title: '数据透视表', tagShow: false }
	},
	{
		path: '/information',
		component: () => import(/* webpackChunkName: "tkdesignAfter" */ '../../../pages/tkdesign/monitorSpectaculars/information.vue'),
		meta: { title: '重点关注个股信息', tagShow: false }
	}
];
