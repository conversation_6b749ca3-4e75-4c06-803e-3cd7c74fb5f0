<!--  -->
<template>
  <div v-loading="loading"
       class="holdindustry">
    <div style="display: flex; align-items: center; width: 100%; position: relative">
      <div style="display: flex; align-items: center">
        <div class="TitltCompare">久期拉长</div>
      </div>
      <div style="right: 0px; position: absolute; display: flex">
        <el-button @click="outexcel()"
                   icon="el-icon-download"></el-button>
        <el-button @click="tablepic = !tablepic"
                   style="margin-left: 10px"
                   :icon="tablepic ? 'el-icon-picture' : 'el-icon-tickets'"></el-button>
      </div>
    </div>
    <div v-show="showdetailchoose"
         style="text-align: right">
      <div class="block">
        <el-date-picker v-model="value2"
                        type="month"
                        @change="changgedate"
                        placeholder="选择季度"> </el-date-picker>
      </div>
    </div>
    <div v-show="tablepic">
      <div style="margin-top: 16px"
           v-for="(item, index) in alldataList"
           :key="index">
        <div style="font-weight: 500; font-size: 16px; line-height: 24px; color: rgba(0, 0, 0, 0.65); opacity: 0.45">
          {{ item.value }}
        </div>
        <sTable :data="item.list"
                typeFlag="1"></sTable>
      </div>
    </div>
    <div style="margin-top: 16px"
         v-show="!tablepic">
      <div style="display: flex; align-items: center; flex-wrap: wrap; justify-content: center">
        <!-- <div style='display: flex;align-items: center;justify-content: center;margin-left:25px'><div style='width:30px;height:16px;background:#4096FF;border-radius:5px;'></div><div style='font-size:16px;font-weight:400'>1年内占比</div></div>
            <div style='display: flex;align-items: center;justify-content: center;margin-left:10px'><div style='width:30px;height:16px;background:#ff9003;border-radius:5px;'></div><div style='font-size:16px;font-weight:400'>1-5年占比</div></div>
            <div style='display: flex;align-items: center;justify-content: center;margin-left:10px'><div style='width:30px;height:16px;background:#9e9d9c;border-radius:5px;'></div><div style='font-size:16px;font-weight:400'>5年外占比</div></div>
            <div style='display: flex;align-items: center;justify-content: center;margin-left:10px'><div style='width:30px;height:16px;background:#fff358;border-radius:5px;'></div><div style='font-size:16px;font-weight:400'>不计息占比</div></div>   -->
        <div v-for="(item, index) in name.split(',')"
             :key="index"
             style="display: flex; align-items: center; justify-content: center; margin-left: 25px">
          <div style="font-size: 16px; font-weight: 400">{{ list[index] }}:{{ item }}</div>
        </div>
      </div>
      <div style="page-break-inside: avoid; text-align: left">
        <v-chart ref="longdurations"
                 v-loading="empty2"
                 autoresize
                 element-loading-text="暂无数据"
                 element-loading-spinner="el-icon-document-delete"
                 element-loading-background="rgba(239, 239, 239, 0.5)"
                 style="page-break-inside: avoid; width: 100%; height: 400px"
                 :options="optionpbroe2"></v-chart>
      </div>
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { BondFundDurationLong } from '@/api/pages/tools/compare.js';
import VCharts from 'vue-echarts';
import sTable from '../SelfTable.vue';
export default {
  //import引入的组件需要注入到对象中才能使用
  components: { 'v-chart': VCharts, sTable },
  props: {
    comparetype: {
      type: String,
      default: 'manager' //fund
    },
    id: {
      type: String,
      default: '30189741,30441407'
    },
    type: {
      type: String,
      default: 'equity'
    },
    name: {
      type: String,
      default: '萧楠,胡昕炜'
    }
  },
  filters: {
    fix3 (value) {
      if (value == '--' || value == null || value == '') {
        return value;
      } else {
        return (value * 100).toFixed(2) + '%';
      }
    },
    fix2 (value) {
      return Number(value).toFixed(2) + '亿';
    },
    fixY (value) {
      if (value == '--' || value == null || value == '') {
        return '--';
      } else {
        return (Number(value) * 100).toFixed(2) + '%';
      }
    }
  },
  data () {
    //这里存放数据
    return {
      alldataList: [],
      loading: false,
      list: ['a', 'b', 'c', 'd', 'e'],
      showdetailchoose: false,
      radio: '2',
      value2: '',
      tablepic: false,
      arrlist: [],
      optionpbroe2: {},
      tablecolumns: [
        {
          dataIndex: 'fund_name',
          key: 'fund_name',
          title: '基金名称'
        },
        {
          dataIndex: 'yearqtr',
          key: 'Yearqtr',
          defaultSortOrder: 'ascend',
          sorter: (a, b) => {
            if (a.yearqtr > b.yearqtr) return 1;
            else {
              return -1;
            }
          },
          title: '季度'
        },
        {
          dataIndex: '1年以内',
          key: '1年以内',
          title: '1年以内',
          sorter: (a, b) => a['1年以内'] - b['1年以内'],
          scopedSlots: {
            customRender: 'xx'
          }
        },
        {
          dataIndex: '1-5年',
          key: '1-5年',
          title: '1-5年',
          sorter: (a, b) => a['1-5年'] - b['1-5年'],
          scopedSlots: {
            customRender: 'xx'
          }
        },
        {
          dataIndex: '5年以上',
          key: '5年以上',
          title: '5年以上',
          sorter: (a, b) => a['5年以上'] - b['5年以上'],
          scopedSlots: {
            customRender: 'xx'
          }
        },
        {
          dataIndex: '不计息',
          key: '不计息',
          title: '不计息',
          sorter: (a, b) => a['不计息'] - b['不计息'],
          scopedSlots: {
            customRender: 'xx'
          }
        },
        {
          dataIndex: '合计',
          key: '合计',
          sorter: (a, b) => a['合计'] - b['合计'],
          title: '合计',
          scopedSlots: {
            customRender: 'xx'
          }
        }
      ],
      alldata: []
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {
    tablepic () {
      if (this.tablepic == false) {
        this.drawpic();
      }
    }
  },
  //方法集合
  methods: {
    NumAscSort (a, b) {
      if (a > b) {
        return 1;
      } else {
        return -1;
      }
    },
    outexcel () {
      const { export_json_to_excel } = require('@/vendor/Export2Excel');
      var list = [];
      list.push(this.dataexplain);
      let tHeader = [];
      let filterVal = [];

      tHeader = ['名称', '季度', '1年以内', '1-5年', '5年以上', '不计息'];
      filterVal = ['manager_name', 'name', 'weight', 'rank', 'yearqtr', 'yearqtr'];
      // //console.log(this.datatable)

      // //console.log(temparr)
      for (let i = 0; i < this.arrlist.length; i++) {
        list[i] = [];
        list[i][0] = this.arrlist[i].fund_name;
        list[i][1] = this.arrlist[i].yearqtr;
        list[i][2] = this.arrlist[i]['1年以内'];
        list[i][3] = this.arrlist[i]['1-5年'];
        list[i][4] = this.arrlist[i]['5年以上'];
        list[i][5] = this.arrlist[i]['不计息'];
      }

      export_json_to_excel(tHeader, list, '久期拉长');
    },
    getdata () {
      Object.assign(this.$data, this.$options.data());
      this.loading = true;
      if (this.comparetype == 'manager') {
        this.getmanager();
      } else {
        this.gefunddata();
      }
    },
    async getmanager (val) {
      this.arrlist = [];
      let data = await ManagerSwindustryHold({
        manager_code: this.id,
        type: this.type,
        manager_name: this.name,
        flag: this.radio,
        yearqtr: val
      });
      this.loading = false;

      if (data) {
        //  //console.log(data)
        //  //console.log('hereindustry')
        //  let temp
        for (let i = 0; i < data.data.length; i++) {
          //  //console.log('2312312')
          this.arrlist = this.arrlist.concat(data.data[i]);
        }
      }
      //  //console.log(this.arrlist)
    },
    async gefunddata () {
      this.arrlist = [];
      let data = await BondFundDurationLong({
        fund_code: this.id,
        type: this.type,
        fund_name: this.name
      });
      this.loading = false;

      if (data) {
        data.data.sort((a, b) => {
          if (this.$route.query.id.split(',').indexOf(a[0].code) > this.$route.query.id.split(',').indexOf(b[0].code)) return 1;
          else return -1;
        });
        // //console.log(data)
        // //console.log('hereindustry222')
        for (let i = 0; i < data.data.length; i++) {
          //  //console.log('2312312')
          this.arrlist = this.arrlist.concat(data.data[i]);
        }
        this.alldata = data.data;
        this.alldataList = [];
        let maxindex = 0;
        let max = 0;
        let quterlist = [];
        for (let i = 0; i < this.alldata.length; i++) {
          if (max < this.alldata[i].length) {
            max = this.alldata[i].length;
            maxindex = i;
          }
          for (let j = 0; j < this.alldata[i].length; j++) {
            if (quterlist.indexOf(this.alldata[i][j].yearqtr) < 0) {
              quterlist.push(this.alldata[i][j].yearqtr);
            }
          }
        }
        quterlist.sort((a, b) => {
          if (a > b) return 1;
          else return -1;
        });
        quterlist.reverse();
        for (let j = 0; j < quterlist.length; j++) {
          this.alldataList[j] = {};
          this.alldataList[j]['list'] = [['1年以内'], ['1-5年'], ['5年以上'], ['不计息'], ['合计']];

          for (let i = 0; i < this.alldata.length; i++) {
            if (this.alldata[i].findIndex((item) => item.yearqtr == quterlist[j]) < 0) {
              this.alldataList[j]['list'][0].push('--');
              this.alldataList[j]['list'][1].push('--');
              this.alldataList[j]['list'][2].push('--');
              this.alldataList[j]['list'][3].push('--');
              this.alldataList[j]['list'][4].push('--');
            } else {
              this.alldataList[j]['value'] = this.alldata[i][this.alldata[i].findIndex((item) => item.yearqtr == quterlist[j])].yearqtr;
              this.alldataList[j]['list'][0].push(
                this.FUNC.isEmpty(this.alldata[i][this.alldata[i].findIndex((item) => item.yearqtr == quterlist[j])]['1年以内'])
                  ? (Number(this.alldata[i][this.alldata[i].findIndex((item) => item.yearqtr == quterlist[j])]['1年以内']) * 100).toFixed(
                    2
                  ) + '%'
                  : '--'
              );
              this.alldataList[j]['list'][1].push(
                this.FUNC.isEmpty(this.alldata[i][this.alldata[i].findIndex((item) => item.yearqtr == quterlist[j])]['1-5年'])
                  ? (Number(this.alldata[i][this.alldata[i].findIndex((item) => item.yearqtr == quterlist[j])]['1-5年']) * 100).toFixed(2) +
                  '%'
                  : '--'
              );
              this.alldataList[j]['list'][2].push(
                this.FUNC.isEmpty(this.alldata[i][this.alldata[i].findIndex((item) => item.yearqtr == quterlist[j])]['5年以上'])
                  ? (Number(this.alldata[i][this.alldata[i].findIndex((item) => item.yearqtr == quterlist[j])]['5年以上']) * 100).toFixed(
                    2
                  ) + '%'
                  : '--'
              );
              this.alldataList[j]['list'][3].push(
                this.FUNC.isEmpty(this.alldata[i][this.alldata[i].findIndex((item) => item.yearqtr == quterlist[j])]['不计息'])
                  ? (Number(this.alldata[i][this.alldata[i].findIndex((item) => item.yearqtr == quterlist[j])]['不计息']) * 100).toFixed(
                    2
                  ) + '%'
                  : '--'
              );
              this.alldataList[j]['list'][4].push(
                this.FUNC.isEmpty(this.alldata[i][this.alldata[i].findIndex((item) => item.yearqtr == quterlist[j])]['合计'])
                  ? (Number(this.alldata[i][this.alldata[i].findIndex((item) => item.yearqtr == quterlist[j])]['合计']) * 100).toFixed(2) +
                  '%'
                  : '--'
              );
            }
          }
        }
        //  let temp
        this.drawpic();
      }
    },
    drawpic () {
      let that = this;
      let x1 = [];
      let x15 = [];
      let x5 = [];
      let bjx = [];
      let seriessdata = [];
      let datelist = [];
      if (this.alldata.length > 0) {
        for (let i = 0; i < this.alldata.length; i++) {
          for (let j = 0; j < this.alldata[i].length; j++) {
            if (datelist.indexOf(this.alldata[i][j].yearqtr) < 0) {
              datelist.push(this.alldata[i][j].yearqtr);
            }
          }
        }
      }
      datelist.sort(this.NumAscSort);
      // //console.log('check')
      // //console.log(this.alldata)
      for (let i = 0; i < this.alldata.length; i++) {
        let temp = {
          name: this.alldata[i][0].fund_name,
          value: []
        };
        let temp2 = {
          name: this.alldata[i][0].fund_name,
          value: []
        };
        let temp3 = {
          name: this.alldata[i][0].fund_name,
          value: []
        };
        let temp4 = {
          name: this.alldata[i][0].fund_name,
          value: []
        };
        for (let j = 0; j < this.alldata[i].length; j++) {
          temp.value.push([this.alldata[i][j].yearqtr, Number(this.alldata[i][j]['1年以内']).toFixed(2)]);
          temp2.value.push([this.alldata[i][j].yearqtr, Number(this.alldata[i][j]['1-5年']).toFixed(2)]);
          temp3.value.push([this.alldata[i][j].yearqtr, Number(this.alldata[i][j]['5年以上']).toFixed(2)]);
          temp4.value.push([this.alldata[i][j].yearqtr, Number(this.alldata[i][j]['不计息']).toFixed(2)]);
        }
        x1.push(temp);
        x15.push(temp2);
        x5.push(temp3);
        bjx.push(temp4);
      }
      for (let i = 0; i < x1.length; i++) {
        seriessdata.push({
          name: '1年内占比',
          type: 'bar',
          label: {
            show: true,
            formatter: function (params) {
              //标签内容

              return that.list[that.name.split(',').indexOf(x1[i].name)];
            },
            position: 'top'
          },
          itemStyle: {
            barBorderColor: '#4096FF',
            color: '#4096FF'
          },
          data: x1[i].value
        });
      }
      for (let i = 0; i < x15.length; i++) {
        seriessdata.push({
          name: '1-5年内占比',
          type: 'bar',
          itemStyle: {
            barBorderColor: '#ff9003',
            color: '#ff9003'
          },
          label: {
            show: true,
            formatter: function (params) {
              //标签内容
              return that.list[that.name.split(',').indexOf(x1[i].name)];
            },
            position: 'top'
          },
          data: x15[i].value
        });
      }
      for (let i = 0; i < x5.length; i++) {
        seriessdata.push({
          name: '5年外占比',
          type: 'bar',
          itemStyle: {
            barBorderColor: '#9e9d9c',
            color: '#9e9d9c'
          },
          label: {
            show: true,
            formatter: function (params) {
              //标签内容
              return that.list[that.name.split(',').indexOf(x1[i].name)];
            },
            position: 'top'
          },
          data: x5[i].value
        });
      }
      for (let i = 0; i < bjx.length; i++) {
        seriessdata.push({
          name: '不计息占比',
          type: 'bar',
          label: {
            show: true,
            formatter: function (params) {
              //标签内容
              return that.list[that.name.split(',').indexOf(x1[i].name)];
            },
            position: 'top'
          },
          itemStyle: {
            barBorderColor: '#e65c00',
            color: '#e65c00'
          },
          data: bjx[i].value
        });
      }

      this.optionpbroe2 = {
        color: ['#4096FF', '#ff9003', '#9e9d9c', 'fff358'],
        legend: {},
        dataZoom: [
          {
            type: 'slider',
            show: true,
            height: 14,
            bottom: 10,
            borderColor: 'transparent',
            backgroundColor: '#fafafa',
            // 拖拽手柄样式 svg 路径
            handleIcon:
              'M512 512m-208 0a6.5 6.5 0 1 0 416 0 6.5 6.5 0 1 0-416 0Z M512 192C335.264 192 192 335.264 192 512c0 176.736 143.264 320 320 320s320-143.264 320-320C832 335.264 688.736 192 512 192zM512 800c-159.072 0-288-128.928-288-288 0-159.072 128.928-288 288-288s288 128.928 288 288C800 671.072 671.072 800 512 800z',
            handleColor: '#aab6c6',
            handleSize: 20,
            handleStyle: {
              borderColor: '#aab6c6',
              shadowBlur: 4,
              shadowOffsetX: 1,
              shadowOffsetY: 1,
              shadowColor: '#e5e5e5'
            },
            start: 0,
            end: 100
          }
        ],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          },
          textStyle: {
            fontSize: 14
          },
          formatter: (params) => {
            //  //console.log(params)
            let str = `时间: ${params[0].axisValue} <br />`;
            for (let i = params.length - 1; i >= 0; i--) {
              let dotHtml =
                '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' +
                params[i].color +
                '"></span>';
              str += dotHtml + `${params[i].seriesName}: ${(Number(params[i].value[1]) * 100).toFixed(2) + '%'}<br />`;
            }
            return str;
          }
        },
        grid: {
          top: '10%',
          left: '3%',
          right: '4%',
          bottom: '10%',
          containLabel: true
        },

        xAxis: [
          {
            type: 'category',
            data: datelist,
            axisLabel: {
              show: true,
              textStyle: {
                fontSize: 14
              },
              interval: 0,
              rotate: 40
            }
          }
        ],
        yAxis: [
          {
            type: 'value',

            axisTick: {
              show: false
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed'
              }
            },
            axisLabel: {
              show: true,
              textStyle: {
                fontSize: 14
              },
              formatter (value) {
                return value * 100 + '%';
              }
            }
          }
        ],
        series: seriessdata
      };
    },
    createPrintWord () {
      let name = this.name
        .split(',')
        .map((item, index) => {
          return this.list[index] + ':' + item;
        })
        .join('  ');
      let height = this.$refs['longdurations']?.$el.clientHeight;
      let width = this.$refs['longdurations']?.$el.clientWidth;
      let chart = this.$refs['longdurations'].getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      });
      return [
        ...this.$exportWord.exportTitle('久期拉长'),
        ...this.$exportWord.exportDescripe(name),
        ...this.$exportWord.exportChart(chart, { width, height })
      ];
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () { },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
</style>
<style>
.holdindustry .el-input__inner {
	/* padding-left: 30px !important; */
}
</style>
