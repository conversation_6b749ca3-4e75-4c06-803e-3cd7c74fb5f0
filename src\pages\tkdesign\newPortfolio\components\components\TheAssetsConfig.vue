<template>
  <div>
    <el-tabs v-model="typeValue"
             @change="handleTypeChange"
             tab-position="left"
             style="height: 405px"
             class="left-nav-wrapper the-assets-config-wrapper">
      <!-- 第一级分类循环 -->
      <el-tab-pane label="大类资产指数"
                   disabled
                   name="assetNavTitle">
        <span class="type-list-title"
              slot="label">大类资产指数</span>
      </el-tab-pane>
      <el-tab-pane v-for="(leftItem, index) in optionListCopy"
                   :key="leftItem[typeName]"
                   :name="leftItem[typeName]">
        <span slot="label"
              class="type-list-label">
          <el-input ref="nameInput"
                    v-if="leftItem[typeName] === typeValue && openEdit"
                    style="width: 120px"
                    v-model="typeEditValue"
                    placeholder="请输入"
                    @blur="handleEditCheck(index)"
                    autofocus></el-input>

          <span v-else>{{ leftItem[typeName] }}（{{ leftItem.children.length }}）</span>
          <span v-if="leftItem[typeName] === typeValue && !openEdit">
            <span class="edit-icon"
                  @click="handleTypeEdit(leftItem[typeName])"
                  style="margin-left: 12px">
              <svg xmlns="http://www.w3.org/2000/svg"
                   width="14"
                   height="14"
                   viewBox="0 0 14 14"
                   fill="none">
                <g clip-path="url(#clip0_10407_8093)">
                  <path d="M3.02632 10.7485C3.05757 10.7485 3.08882 10.7454 3.12007 10.7407L5.74819 10.2798C5.77944 10.2735 5.80913 10.2595 5.83101 10.236L12.4544 3.6126C12.4689 3.59814 12.4804 3.58097 12.4883 3.56207C12.4961 3.54317 12.5001 3.52291 12.5001 3.50244C12.5001 3.48198 12.4961 3.46171 12.4883 3.44281C12.4804 3.42391 12.4689 3.40674 12.4544 3.39229L9.85757 0.793848C9.82788 0.76416 9.78882 0.748535 9.74663 0.748535C9.70444 0.748535 9.66538 0.76416 9.63569 0.793848L3.01226 7.41729C2.98882 7.44072 2.97476 7.46885 2.96851 7.5001L2.50757 10.1282C2.49237 10.2119 2.4978 10.2981 2.52339 10.3792C2.54898 10.4603 2.59397 10.534 2.65444 10.5938C2.75757 10.6938 2.88726 10.7485 3.02632 10.7485ZM4.07944 8.02354L9.74663 2.35791L10.8919 3.50322L5.22476 9.16885L3.83569 9.41416L4.07944 8.02354ZM12.7498 12.061H1.24976C0.973193 12.061 0.749756 12.2845 0.749756 12.561V13.1235C0.749756 13.1923 0.806006 13.2485 0.874756 13.2485H13.1248C13.1935 13.2485 13.2498 13.1923 13.2498 13.1235V12.561C13.2498 12.2845 13.0263 12.061 12.7498 12.061Z"
                        fill="#4096ff" />
                </g>
                <defs>
                  <clipPath id="clip0_10407_8093">
                    <rect width="14"
                          height="14"
                          fill="white" />
                  </clipPath>
                </defs>
              </svg>
            </span>
            <span class="del-icon"
                  @click="handelTypeDel(leftItem[typeName])"
                  style="margin-left: 8px">
              <svg xmlns="http://www.w3.org/2000/svg"
                   width="14"
                   height="14"
                   viewBox="0 0 14 14"
                   fill="none">
                <path d="M4.62476 1.87402H4.49976C4.56851 1.87402 4.62476 1.81777 4.62476 1.74902V1.87402H9.37476V1.74902C9.37476 1.81777 9.43101 1.87402 9.49976 1.87402H9.37476V2.99902H10.4998V1.74902C10.4998 1.19746 10.0513 0.749023 9.49976 0.749023H4.49976C3.94819 0.749023 3.49976 1.19746 3.49976 1.74902V2.99902H4.62476V1.87402ZM12.4998 2.99902H1.49976C1.22319 2.99902 0.999756 3.22246 0.999756 3.49902V3.99902C0.999756 4.06777 1.05601 4.12402 1.12476 4.12402H2.06851L2.45444 12.2959C2.47944 12.8287 2.92007 13.249 3.45288 13.249H10.5466C11.081 13.249 11.5201 12.8303 11.5451 12.2959L11.931 4.12402H12.8748C12.9435 4.12402 12.9998 4.06777 12.9998 3.99902V3.49902C12.9998 3.22246 12.7763 2.99902 12.4998 2.99902ZM10.4263 12.124H3.57319L3.19507 4.12402H10.8044L10.4263 12.124Z"
                      fill="#4096ff" />
              </svg>
            </span>
          </span>
        </span>
        <div class="content-wrapper">
          <div class="content-title-wrapper">指数列表</div>
          <div class="content-list-wrapper">
            <div class="content-list-item"
                 v-for="(leftItemChildren, childIndex) in leftItem.children"
                 :key="leftItemChildren[codeName]">
              <el-autocomplete v-model="leftItemChildren[nameName]"
                               :fetch-suggestions="handleGetIdnex"
                               highlight-first-item
                               placeholder="请输入内容"
                               @change="
									(value) => {
										return handleInputChange(value, index, childIndex);
									}
								"
                               @select="
									(value) => {
										return handelItemSelect(leftItem[typeName], leftItemChildren[codeName], value);
									}
								">
                <template slot-scope="{ item }">
                  <!-- <span>{{ item.code }}</span> -->
                  <span>{{ item.name }}</span>
                </template>
              </el-autocomplete>
              <svg class="item-remove-btn"
                   @click="handleItemRemove(leftItem[typeName], leftItemChildren[codeName])"
                   xmlns="http://www.w3.org/2000/svg"
                   width="14"
                   height="14"
                   viewBox="0 0 14 14"
                   fill="none">
                <path d="M4.62476 1.87402H4.49976C4.56851 1.87402 4.62476 1.81777 4.62476 1.74902V1.87402H9.37476V1.74902C9.37476 1.81777 9.43101 1.87402 9.49976 1.87402H9.37476V2.99902H10.4998V1.74902C10.4998 1.19746 10.0513 0.749023 9.49976 0.749023H4.49976C3.94819 0.749023 3.49976 1.19746 3.49976 1.74902V2.99902H4.62476V1.87402ZM12.4998 2.99902H1.49976C1.22319 2.99902 0.999756 3.22246 0.999756 3.49902V3.99902C0.999756 4.06777 1.05601 4.12402 1.12476 4.12402H2.06851L2.45444 12.2959C2.47944 12.8287 2.92007 13.249 3.45288 13.249H10.5466C11.081 13.249 11.5201 12.8303 11.5451 12.2959L11.931 4.12402H12.8748C12.9435 4.12402 12.9998 4.06777 12.9998 3.99902V3.49902C12.9998 3.22246 12.7763 2.99902 12.4998 2.99902ZM10.4263 12.124H3.57319L3.19507 4.12402H10.8044L10.4263 12.124Z"
                      fill="black"
                      fill-opacity="0.25" />
              </svg>
            </div>
          </div>
          <div class="content-add-btn"
               @click="handleItemAdd(leftItem[typeName])"><i class="el-icon-circle-plus"></i>新增</div>
        </div>
      </el-tab-pane>
      <el-tab-pane disabled
                   name="assetFooterBtn">
        <div class="type-add-btn"
             slot="label">
          <span @click="handleTypeAdd"><i class="el-icon-plus"></i> 新建</span>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import { getIndexSearchList } from '@/api/pages/tkAnalysis/portfolio.js';
export default {
  props: {
    //初始列表数据
    optionList: {
      type: Array,
      required: true,
      default: () => {
        return [
          {
            name: '一级分类展示文案,唯一标识',
            children: [
              {
                label: '子节点展示文案',
                id: '子节点唯一标识',
                value: {
                  id: '0001'
                } //子节点本身的数据值，用户接口传输
              }
            ]
          }
        ];
      }
    }
  },
  computed: {
    optionListCopy: {
      get () {
        return this.optionList;
      },
      set (val) {
        console.log(val);
        this.$emit('update:optionList', val);
      }
    }
  },
  data () {
    return {
      typeName: 'type',
      codeName: 'code',
      nameName: 'name',
      ruleForm: {
        pass: '',
        checkPass: '',
        age: ''
      },
      count: 1,
      typeEditValue: '', //编辑时表达输入的值
      typeValue: '', //当前选中的类型
      openEdit: false, //是否展示输入编辑框
      //指数搜索列表列表
      options: [],
      //搜索指数时增加的loading
      loading: false
    };
  },
  watch: {
    typeValue (newValue, oldValue) {
      this.handleTypeChange();
    }
  },
  mounted () {
    this.typeValue = this.optionListCopy[0] && this.optionListCopy[0][this.typeName];
  },
  methods: {
    //注： 用户选择时 会先触发该事件 再触发select事件
    handleInputChange (value, typeIndex, childIndex) {
      // 当用户手动输入或修改值时，检查它是否在建议值列表中
      if (!this.options.some((suggestion) => suggestion.name.toLowerCase() === value.toLowerCase())) {
        // 你可以选择重置输入值
        this.optionListCopy[typeIndex].children[childIndex][this.nameName] = '';
      }
    },
    handleGetIdnex (query, callBack) {
      this.FUNC.debounceFunc(() => {
        return this.getIndexList(query, callBack);
      }, 1000)();
    },
    async getIndexList (query, callBack) {
      this.loading = true;
      this.options = [];
      let params = {
        flag: '6', //指数
        message: query || ''
      };
      let reqData = await getIndexSearchList(params);
      let { data = [], mtycode, mtymessage } = reqData || {};
      if (mtycode == 200) {
        this.options = data || [];
      } else {
        this.$message.warning(mtymessage);
        this.options = [];
      }
      callBack(this.options);
      this.loading = false;
    },
    //当切换类型时 关闭编辑状态 清空编辑文案
    handleTypeChange () {
      this.typeEditValue = '';
      this.openEdit = false;
    },
    /**
     * 将对应项组装成想要的数据结构
     * [{
     * name:'指数类型',
     * children:[{
     *  	indexName:'',
     *  	indexCode:'',
     * 	    value:{
     *         name:'',
     * 	       indexName:'',
     *         indexCode:''
     *         }
     *     }]
     * }]
     */
    //当修改单一项时 修改指数列表
    handelItemSelect (parentId, childId, value) {
      //将选项列表同步更新 只需要更新结构所需要的 label和id即可

      let index = this.optionListCopy.findIndex((item) => {
        return item[this.typeName] === parentId;
      });
      console.log(parentId, childId, value, index);
      if (index >= 0) {
        //如果选中项在当前分类的列表下已经存在了则不能再进行选择了
        let indexSame = this.optionListCopy[index]?.children.findIndex((item1) => {
          return item1[this.codeName] === value[this.codeName];
        });
        //找到与子id一致的值
        let index1 = this.optionListCopy[index]?.children.findIndex((item1) => {
          return item1[this.codeName] === childId;
        });
        if (indexSame >= 0 && indexSame !== index1) {
          this.$message.warning('请勿选择重复指数');
          return;
        }
        if (index1 >= 0) {
          //修改列表配置项中对应的label 和 id
          this.optionListCopy[index].children[index1][this.codeName] = value[this.codeName];
          this.optionListCopy[index].children[index1][this.nameName] = value[this.nameName];
          this.optionListCopy[index].children[index1].value = {};
          this.optionListCopy[index].children[index1].value[this.codeName] = value[this.codeName];
          this.optionListCopy[index].children[index1].value[this.nameName] = value[this.nameName];
          this.optionListCopy[index].children[index1].value[this.typeName] = parentId;
        }
      }
      //下拉值变更后需要同步将列表更新
      //为了触发set操作
      this.optionListCopy = this.optionListCopy;
    },
    //移除分类下的某一项
    handleItemRemove (parentId, childId) {
      console.log(parentId, this.optionListCopy);
      //将选项列表同步更新 只需要更新结构所需要需要的 label和id即可
      let index = this.optionListCopy.findIndex((item) => {
        return item[this.typeName] === parentId;
      });
      if (index >= 0) {
        //找到与子id一致的值
        let index1 = this.optionListCopy[index]?.children.findIndex((item1) => {
          return item1[this.codeName] === childId;
        });
        if (index1 >= 0) {
          //修改列表配置项中对应的label 和 id
          this.optionListCopy[index].children.splice(index1, 1);
        }
      }
      //为了触发set操作
      this.optionListCopy = this.optionListCopy;
    },
    //增加分类下的某一项
    handleItemAdd (parentId) {
      let index = this.optionListCopy.findIndex((item) => {
        return item[this.typeName] === parentId;
      });
      if (index >= 0) {
        //找到与子id一致的值
        this.optionListCopy[index].children.push({});
      }
    },
    //新增类型
    handleTypeAdd () {
      let name = this.generateFileName('指数名称');
      this.optionListCopy.push({
        [this.typeName]: name,
        children: [{}]
      });
      //将选中项切换至新增项
      this.typeValue = name;
      // //直接将编辑名称框打开，以便修改默认名称
      // this.handleTypeEdit(name);
    },
    //删除类型
    handelTypeDel (typeId) {
      const index = this.optionListCopy.findIndex((item) => {
        return item[this.typeName] === typeId;
      });
      if (index >= 0) {
        this.optionListCopy.splice(index, 1);
      }
    },
    //编辑类型名称
    handleTypeEdit (source) {
      //将原name传入给编辑输入框
      this.typeEditValue = source;
      this.openEdit = true;
    },
    handleEditCheck (typeIndex) {
      //当输入名称不唯一时校验不通过
      this.openEdit = false;
      if (!this.typeEditValue) {
        this.$message({
          message: '输入名称不能为空，请重新输入',
          type: 'warning'
        });
        return;
      }
      let index = this.optionListCopy.findIndex((item, itemIndex) => {
        return item[this.typeName] === this.typeEditValue && itemIndex !== typeIndex;
      });
      if (index >= 0) {
        this.$message({
          message: '输入名称不能重复，请重新输入',
          type: 'warning'
        });
        return;
      }
      this.optionListCopy[typeIndex][this.typeName] = this.typeEditValue;
      //重新选中修改后的名称 对应的tab
      this.typeValue = this.typeEditValue;
      this.typeEditValue = '';
    },
    //如发现有同名名称，自动在其后面加上数
    generateFileName (baseName) {
      let fileName = baseName;
      let index = this.optionListCopy.findIndex((item) => {
        return item[this.typeName] === fileName;
      });
      if (index >= 0) {
        fileName = `${fileName}${this.count}`;
        this.count++;
        return this.generateFileName(fileName);
      } else {
        return fileName;
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.left-nav-wrapper {
	::v-deep .el-tabs__header {
		margin-right: 0;
		#tab-assetNavTitle {
			position: sticky;
			top: 0;
			z-index: 1;
			background: #ffffff;
			border-right: 3px solid #e4e7ed;
		}
		// #tab-assetFooterBtn {
		// 	position: sticky;
		// 	bottom: 0;
		// 	z-index: 1;
		// 	background: #ffffff;
		// 	border-right: 3px solid #e4e7ed;
		// }
		.el-tabs__nav-scroll {
			overflow: scroll;
			&::-webkit-scrollbar {
				width: 0; /* 宽度为0会隐藏滚动条 */
				height: 0;
			}
		}
		.el-tabs__nav-prev {
			display: none;
		}
		.el-tabs__nav-next {
			display: none;
		}
		.is-scrollable {
			padding: 0 !important;
		}
	}

	::v-deep .el-tabs__content {
		height: 100%;
		overflow: scroll;
	}
}
.the-assets-config-wrapper {
	border-top: 1px solid #e9e9e9;
	border-bottom: 1px solid #e9e9e9;
	.type-list-label {
		min-width: 160px;
		display: inline-flex;
		justify-content: space-between;
	}
	::v-deep .el-tabs__item.is-left {
		text-align: left;
	}

	.type-list-title {
		color: rgba(0, 0, 0, 0.65);
		font-size: 14px;
		font-style: normal;
		font-weight: 500;
		line-height: 22px; /* 157.143% */
	}
	.type-add-btn {
		color: #4096ff;
	}

	.content-wrapper {
		padding: 16px;
		.content-list-wrapper {
			.content-list-item {
				margin-bottom: 8px;
				.item-remove-btn {
					margin-left: 12px;
				}
			}
		}
		.content-add-btn {
			.el-icon-circle-plus {
				color: #4096ff;
				margin-right: 8px;
			}
		}
	}
	.content-title-wrapper {
		display: flex;
		justify-content: space-between;
		margin-bottom: 16px;
		.content-title-btn {
			color: #cf1322;
		}
	}
	::v-deep .el-tabs__content {
		height: 100%;
		.el-tab-pane {
			height: 100%;
			.content-wrapper {
				display: flex;
				flex-direction: column;
				height: 100%;
				overflow: hidden;
			}
			.content-list-wrapper {
				flex: 1;
				overflow: scroll;
			}
		}
	}
}
</style>
