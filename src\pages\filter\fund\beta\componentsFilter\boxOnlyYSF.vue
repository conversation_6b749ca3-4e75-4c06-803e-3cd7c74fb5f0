<!--  -->
<template>
	<div>
		<div
			v-show="haveName != ''"
			style="
				font-weight: 400;
				font-size: 14px;
				line-height: 22px;
				color: rgba(0, 0, 0, 0.85);
				margin-left: 0px;
				margin-right: 16px;
				margin-bottom: 4px;
			"
		>
			{{ haveName }}
		</div>
		<div class="boxOnlyYSF">
			<operator v-if="is_range" ref="operator" @resolveMathRange="resolveMathRange"></operator>
			<el-dropdown @command="command">
				<el-button type="primary">
					{{ iconFlag != '' ? (iconFlag == 'all' ? '所有' : iconFlag) : '运算符' }}<i class="el-icon-arrow-down el-icon--right"></i>
				</el-button>
				<el-dropdown-menu slot="dropdown">
					<el-dropdown-item command="all">所有</el-dropdown-item>
					<el-dropdown-item command="<">&lt;</el-dropdown-item>
					<el-dropdown-item command="=">=</el-dropdown-item>
					<el-dropdown-item command=">">&gt;</el-dropdown-item>
					<el-dropdown-item command="<=">&lt;=</el-dropdown-item>
					<el-dropdown-item command=">=">&gt;=</el-dropdown-item>
				</el-dropdown-menu>
			</el-dropdown>
			<div v-show="showBox" style="margin-left: 0px; display: flex; align-items: center">
				<!-- <div style="padding:5px;background:#ecf5ff;border:1px #f8f8f8;">
            {{iconFlag=='all'?'所有':iconFlag}}
        </div> -->
				<div style="margin-left: 16px">
					<el-input
						type="number"
						@input="inputChange"
						:placeholder="placeholder.indexOf('前百分之') >= 0 ? placeholder : '单位为：' + placeholder"
						v-model="input"
					></el-input>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import operator from '@/pages/filter/fund/beta/componentsFilter/components/operator.vue';

export default {
	props: {
		is_range: {
			type: Boolean,
			default: false
		},
		haveName: {
			type: String,
			default: ''
		},
		dataX: {
			type: Object,
			default: {}
		},
		placeholder: {
			type: String
		},
		indexFlag: {
			type: Number
		},
		baseIndexFlag: {
			type: Number
		}
	},
	//import引入的组件需要注入到对象中才能使用
	components: { operator },
	data() {
		//这里存放数据
		return {
			iconFlag: '',
			showBox: false,
			input: '',
			mathRange: { mathRange: 'avg' }
		};
	},
	//监听属性 类似于data概念
	computed: {},
	//监控data中的数据变化
	watch: {
		dataX(val) {
			if (val.dataResult && val.dataResult.length > 0) {
				this.showBox = true;
				this.iconFlag = val.dataResult[0].flag;
				this.input = val.dataResult[0].value;
				if (this.$refs['operator']) {
					this.$refs['operator'].getFlag(val.dataResult[0].mathRange);
				}
			}
		}
	},
	//方法集合
	methods: {
		resolveMathRange(obj) {
			this.mathRange = obj;
			this.resolveFather();
		},
		command(e) {
			this.iconFlag = e;
			this.showBox = true;
			this.resolveFather();
		},
		inputChange() {
			this.resolveFather();
		},
		resolveFather() {
			this.$emit(
				'boxOnlyYSFChange',
				this.baseIndexFlag,
				this.indexFlag,
				this.input,
				this.iconFlag,
				this.FUNC.isEmpty(this.input) && this.FUNC.isEmpty(this.iconFlag),
				this.mathRange
			);
		}
	},
	//生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	//生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		if (JSON.stringify(this.dataX) != '{}') {
			if (this.dataX.dataResult && this.dataX.dataResult.length > 0) {
				this.showBox = true;
				this.iconFlag = this.dataX.dataResult[0].flag;
				this.input = this.dataX.dataResult[0].value;
				if (this.$refs['operator']) {
					this.$refs['operator'].getFlag(this.dataX.dataResult[0].mathRange);
				}
			}
		}
	},
	beforeCreate() {}, //生命周期 - 创建之前
	beforeMount() {}, //生命周期 - 挂载之前
	beforeUpdate() {}, //生命周期 - 更新之前
	updated() {}, //生命周期 - 更新之后
	beforeDestroy() {}, //生命周期 - 销毁之前
	destroyed() {}, //生命周期 - 销毁完成
	activated() {} //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
.boxOnlyYSF {
	display: flex;
	align-items: center;
}
</style>
