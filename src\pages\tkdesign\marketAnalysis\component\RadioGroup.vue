<template>
	<el-radio-group class="lq-radio-group" :value="radioValue" size="small">
		<template v-for="configItem in configList">
			<el-select
				:key="configItem.value + configItem.type"
				v-if="configItem.type === 'select'"
				size="small"
				class="lq-radio-button"
				:style="{
					width: configItem.width || '100px'
				}"
				:label="configItem.label"
				v-model="configItem.value"
				:value="configItem.value"
				@input="
					(value) => {
						return handleSelectInput(value, configItem);
					}
				"
				:placeholder="configItem.text"
				:class="radioValue === configItem.label ? 'is-active' : ''"
			>
				<el-option v-for="item in configItem.option" :key="item.value" :label="item.label" :value="item.value"></el-option>
			</el-select>
			<!-- <el-radio-button class="lq-radio-button"  v-else :label="configItem.value">{{configItem.text}}</el-radio-button> -->
			<el-button
				:key="configItem.value"
				class="lq-radio-button"
				:class="radioValue === configItem.label ? 'is-active' : ''"
				v-else
				@click.native="handleSelectChange(configItem.value, configItem)"
				>{{ configItem.text }}</el-button
			>
		</template>
	</el-radio-group>
</template>
<script>
export default {
	name: 'RadioGroup',
	components: {},
	props: {
		value: {
			type: String,
			default: ''
		},
		defaultValue: {
			type: Object,
			default: () => {
				return {
					radioValue: '',
					selectValue: ''
				};
			}
		},
		configList: {
			type: Array,
			default: () => {
				/**
				 * configList每一项，字段type类型可传select（下拉选择）,空（表示按钮类型）
				 * type为select时的数据结构
				 * {
				 *   type:'select',
				 *   label:'W',
				 *	 text: '市净率',
				 *   option:[{ label: '动态市盈率', value: { name: '动态市盈率', value: 'trends_pe' } },]
				 *  
				 * }
				 * type为空时的数据结构
				 * { 
				 *    label:'DW',
				 *    text:'233',
				 *    value:{name:'PEG',value:'peg'}
				 * },
				 * 
				*/
				/**
				 * 其中每一项的label表示选中tab的值，如切到第一项则radioValue为第一项的label
				 * value：type为空时表示的是选中后向外传递的selectValue，
				 * value：在type为select的时候用于展示当前选中项给select组件双向绑定用，
				 * 
				 * option: 在type为select的时候选中下拉选项后selectValue会取option内相应的value值，option.label为下拉选中的文本展示
				 * 
				 * text: 用于展示文本,用于默认文本的展示，如果类型为select将被后续选中值给替代
				 * 
				*/
				return [];
			}
		}
	},
	data() {
		return {
			radioValue: '',
			selectValue: ''
		};
	},
	created() {
		//外部是用过监听change事件其他事务的，由于组件内是通过主动调用handleChange方法告知的外部，
		//初始化时并不会主动触发相应的handleSelectInput,handleSelectChange事件所以需要手动设置默认值
		this.radioValue = this.defaultValue.radioValue;
		this.selectValue = this.defaultValue.selectValue;
		// this.handleChange()
	},
	methods: {
		// 手动设置radioValue&selectValue
		setValue(defaultValue) {
			console.log(this.configList, defaultValue);
			this.radioValue = defaultValue.radioValue;
			this.selectValue = defaultValue.selectValue;
		},
		//el-radio-button 单选item触发的radiochange
		handleInput(value) {
			this.radioValue = value.key;
			this.selectValue = {
				...value,
				value: value.key
			};
			console.log('handleInput');
			this.handleChange();
		},
		handleSelectClick(value, configItem) {
			this.$emit('click', value);
		},
		handleSelectInput(value, configItem) {
			this.radioValue = configItem.label;
			this.selectValue = value;
			console.log('handleSelectInput');
			this.handleChange();
		},
		//下拉主动触发的radiochange
		handleSelectChange(value, configItem) {
			this.radioValue = configItem.label;
			this.selectValue = value;
			console.log('handleSelectChange');
			this.handleChange();
		},
		handleChange() {
			this.$emit('change', { radioValue: this.radioValue, selectValue: this.selectValue });
		}
	}
};
</script>
<style lang="scss" scoped>
.lq-radio-group {
	display: flex;
}
::v-deep .el-button {
	&:focus,
	&:hover {
		background-color: unset;
	}
}
::v-deep .el-button + .el-button {
	margin-left: unset;
}
</style>
