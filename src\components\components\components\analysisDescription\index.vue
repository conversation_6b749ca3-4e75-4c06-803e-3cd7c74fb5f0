<template>
	<div class="analysis_description px-20 py-20">
		<div class="title mb-8">{{ title }}模型使用说明</div>
		<!-- 横向排列说明 -->
		<div v-if="is_column" class="flex_start is_column">
			<div class="left_top_circle"></div>
			<div class="right_top_circle"></div>
			<div class="mb-8 description_border" style="width: 1624px; height: 114px">
				<div>计算指标说明:</div>
				<div style="height: 65px; overflow-y: auto; text-indent: 2em">
					{{ description }}
				</div>
			</div>
			<!-- <div class="mb-8 description_border flex_start" style="width: 792px; height: 114px">
				<div class="mr-12" style="width: 50%">
					<div>计算过程:</div>
					<div>xxxxxxxxxxxxxxxxxxx</div>
				</div>
				<div style="flex: 1; background: #e1e1e1; height: 88px"></div>
			</div> -->
		</div>
		<!-- 纵向排列说明 -->
		<div v-else>
			<div class="mb-8 description_border">
				<div>计算指标说明:</div>
				<div style="text-indent: 2em">
					{{ description }}
				</div>
			</div>
			<!-- <div class="mb-8 description_border">
				<div>计算过程:</div>
				<div>
					{{xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx}}
				</div>
			</div>
			<div class="description_border">
				<div>比较范围:</div>
				<div>
					xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
				</div>
			</div> -->
		</div>
	</div>
</template>

<script>
export default {
	props: {
		title: {
			type: String,
			default: ''
		},
		description: {
			type: String,
			default: ''
		},
		is_column: {
			type: Boolean,
			default: false
		}
	}
};
</script>

<style lang="scss" scoped>
.analysis_description {
	width: 100%;
	position: relative;
	border-radius: 4px;
	border: 1px solid #d9d9d9;
	background: linear-gradient(180deg, rgba(255, 145, 3, 0.1) 0%, rgba(255, 145, 3, 0) 100%);
	overflow: hidden;
	.left_top_circle {
		width: 162px;
		height: 162px;
		border-radius: 50%;
		background: linear-gradient(315deg, rgba(255, 145, 3, 0.1) 18.6%, rgba(255, 145, 3, 0) 55.04%);
	}
	.right_top_circle {
		width: 72px;
		height: 72px;
		border-radius: 50%;
		background: linear-gradient(135deg, #ecf5ff 18.6%, rgba(255, 145, 3, 0) 55.04%);
	}
	.is_column {
		.left_top_circle {
			position: absolute;
			left: -65px;
			top: -69px;
		}
		.right_top_circle {
			position: absolute;
			right: 20px;
			top: 28px;
		}
	}
	.is_row {
		.left_top_circle {
			position: absolute;
			left: -65px;
			top: -69px;
		}
		.right_top_circle {
			position: absolute;
			right: -27px;
			top: 28px;
		}
	}
	.title {
		color: rgba(0, 0, 0, 0.85);
		font-family: PingFang SC;
		font-size: 16px;
		font-style: normal;
		font-weight: 500;
	}
	.description_border {
		border: 1px solid #d9d9d9;
		padding: 12px 20px;
		color: #000;
		font-family: PingFang SC;
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
		word-wrap: break-word;
	}
}
</style>
