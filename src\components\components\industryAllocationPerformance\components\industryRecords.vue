<template>
	<!-- <div id="industryRecords" class="canvasBig"></div> -->
	<div class="charts_fill_class">
		<v-chart
			v-loading="loading"
			class="charts_one_class"
			autoresize
			element-loading-text="暂无数据"
			element-loading-spinner="el-icon-document-delete"
			element-loading-background="rgba(239, 239, 239, 0.5)"
			:options="option"
		/>
	</div>
</template>

<script>
import VChart from 'vue-echarts';
export default {
	data() {
		return {
			option: {},
			loading: true
		};
	},
	components: {
		VChart
	},
	methods: {
		initEcharts(data) {
			this.loading = false;
			const colors = ['#FFAE57', '#FF7853', '#EA5151', '#CC3F57', '#9A2555'];
			const bgColor = '#2E2733';
			let option = {
				backgroundColor: bgColor,
				color: colors,
				series: [
					{
						type: 'sunburst',
						center: ['50%', '48%'],
						data: data,
						sort: function (a, b) {
							if (a.depth === 1) {
								return b.getValue() - a.getValue();
							} else {
								return a.dataIndex - b.dataIndex;
							}
						},
						label: {
							rotate: 'radial',
							color: bgColor
						},
						itemStyle: {
							borderColor: bgColor,
							borderWidth: 2
						},
						levels: [
							{},
							{
								r0: 0,
								r: 40,
								label: {
									rotate: 0
								}
							},
							{
								r0: 40,
								r: 105
							},
							{
								r0: 115,
								r: 140,
								itemStyle: {
									shadowBlur: 2,
									shadowColor: colors[2],
									color: 'transparent'
								},
								label: {
									rotate: 'tangential',
									fontSize: 10,
									color: colors[0]
								}
							},
							{
								r0: 140,
								r: 145,
								itemStyle: {
									shadowBlur: 80,
									shadowColor: colors[0]
								},
								label: {
									position: 'outside',
									textShadowBlur: 5,
									textShadowColor: '#333'
								},
								downplay: {
									label: {
										opacity: 0.5
									}
								}
							}
						]
					}
				]
			};
			this.option = option;
		}
	}
};
</script>

<style scoped>
.canvasBig {
	width: 100% !important;
	height: 400px !important;
	margin: 0 auto;
}
</style>
