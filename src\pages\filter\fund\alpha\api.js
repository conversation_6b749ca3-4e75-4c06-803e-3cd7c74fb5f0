import request from "@/api/request";
import requestTemp from "@/utils/request";
import requestSystem from "@/api/requestSystem";
// 基金池列表
export function getPoolList(params) {
  return request({
    url: "/pool/fund_pool/",
    method: "get",
    params,
  });
}
// 基金经理吃列表
export function getManagerPoolList() {
  return request({
    url: "/pool/manager_pool/",
    method: "get",
  });
}

// alphascore
export function alphaScore(data) {
  return requestTemp({
    url: "/Filter/AlphaScore/",
    method: "post",
    data,
  });
}
export function getalphatype(params) {
  return request({
    url: "/TypeInfo/",
    method: "get",
    params,
  });
}
// 基金经理纯债信用
export function getBondManagerCapability(params) {
  return request({
    url: "/BondManagerCapability/",
    method: "get",
    params,
  });
}

// 管理时间
export function getStartDate(params) {
  return request({
    url: "/fundhome/fundfounddate/",
    method: "get",
    params,
  });
}

// 画像批注
export function getAnnotation(params) {
  return request({
    url: "/annotation/fund_annotation_latest",
    method: "get",
    params,
  });
}
export function postAnnotation(data) {
  return request({
    url: "/annotation/fund_annotation",
    method: "post",
    data,
  });
}
export function getAnnotationmanager(params) {
  return request({
    url: "/annotation/manager_annotation_latest",
    method: "get",
    params,
  });
}
export function postAnnotationmanager(data) {
  return request({
    url: "/annotation/manager_annotation",
    method: "post",
    data,
  });
}
export function fundAnnotationHistory(params) {
  return request({
    url: "/annotation/fund_annotation_history",
    method: "get",
    params,
  });
}
export function fundAnnotationParticular(params) {
  return request({
    url: "/annotation/fund_annotation_particular",
    method: "get",
    params,
  });
}
export function managerAnnotationHistory(params) {
  return request({
    url: "/annotation/manager_annotation_history",
    method: "get",
    params,
  });
}
export function managerAnnotationParticular(params) {
  return request({
    url: "/annotation/manager_annotation_particular",
    method: "get",
    params,
  });
}

export function AlphaFilterV2(data) {
  return requestTemp({
    url: "/alpha/AlphaFilterV2/",
    method: "post",
    data,
  });
}
export function filter_risk_future(params) {
  return requestSystem({
    url: "/alpha/filter_risk_future/",
    method: "get",
    params,
  });
}

export function fliter_recent(params) {
  return requestSystem({
    url: "/alpha/fliter_recent/",
    method: "get",
    params,
  });
}

export function fliter_return_recent(params) {
  return requestSystem({
    url: "/alpha/fliter_return_recent/",
    method: "get",
    params,
  });
}
export function getStyleList(params) {
  return requestSystem({
    url: "/filter/styleList/",
    method: "get",
    params,
  });
}
export function getTKandJYcategory(params) {
  return requestSystem({
    url: "/filter/TKandJYcategory/",
    method: "get",
    params,
  });
}
