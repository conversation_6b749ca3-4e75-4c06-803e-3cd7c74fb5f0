import request from '@/utils/request';

const server = '/system/other';

// 基金公司
/**
 *
 * @param {基金公司大类资产配置} params
 * @returns
 */
export function getCompanyAllocationDetails(params) {
	return request({
		url: server + '/Company/AllocationDetails/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {所属基金概况} params
 * @returns
 */
export function getAllTypeFundBasicInfo(params) {
	return request({
		url: server + '/Company/NetassetAndNumber/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {各类型基金收益} params
 * @returns
 */
export function getAllTypeFundCumReturn(params) {
	return request({
		url: server + '/Company/Since1yReturn/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {获取行业配置变化比例} params
 * @returns
 */
export function getIndustryChange(params) {
	return request({
		url: server + '/Company/IndustryChange/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {信用分析} params
 * @returns
 */
export function getBondCreditAnalysis(params) {
	return request({
		url: server + '/Company/BondCreditAnalysis/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {基金公司存在基金类型} params
 * @returns
 */
export function getHoldType(params) {
	return request({
		url: server + '/Company/HoldType/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {基金公司基础信息} params
 * @returns
 */
export function getBasicMsg(params) {
	return request({
		url: server + '/Company/BasicMsg/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {基金公司收益曲线} params
 * @returns
 */
export function getReturnWithAsset(params) {
	return request({
		url: server + '/Company/ReturnWithAsset/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {新发基金} params
 * @returns
 */
export function getNewFund(params) {
	return request({
		url: server + '/Company/NewFund/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {全部基金} params
 * @returns
 */
export function getHoldFundMsg(params) {
	return request({
		url: server + '/Company/HoldFundMsg/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {全部基金经理} params
 * @returns
 */
export function getHoldManagerMsg(params) {
	return request({
		url: server + '/Company/HoldManagerMsg/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {新发基金} params
 * @returns
 */
export function getNewDevelopmentFund(params) {
	return request({
		// url: '/Company/NewFund/',
		url: server + '/Company/Event/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {基金业绩排名} params
 * @returns
 */
export function getFundRecentReturn(params) {
	return request({
		url: server + '/Company/FundRecentReturn/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {获取持仓债券分析} params
 * @returns
 */
export function getBondAnalysis(params) {
	return request({
		url: server + '/Company/BondAnalysis/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {持仓股票分析} params
 * @returns
 */
export function getHoldStockMsg(params) {
	return request({
		url: server + '/Company/HoldStockMsg/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {股票Barra分析} params
 * @returns
 */
export function getStockBarraStyle(params) {
	return request({
		url: server + '/Company/StockBarraStyle/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {公司行业配置表现} params
 * @returns
 */
export function getIndustryInfoCompany(params) {
	return request({
		url: server + '/Company/IndustryDetails/',
		method: 'get',
		params
	});
}
export function getAllocationDetails(params) {
	return request({
		url: server + '/AllocationDetails/',
		method: 'get',
		params
	});
}

/**
 *
 * @param {获取机构列表} params
 * @returns
 */
export function getInstituesList(params) {
	return request({
		url: server + '/institues',
		method: 'get',
		params
	});
}
export function getUsersSalesman(params) {
	return request({
		url: server + '/users/salesman',
		method: 'get',
		params
	});
}
export function getUsersGroups(params) {
	return request({
		url: server + '/groups',
		method: 'get',
		params
	});
}
export function refreshUserGroups() {
	return request({
		url: server + '/refresh-user-groups/',
		method: 'post'
	});
}
export function getInstitues(id) {
	return request({
		url: server + '/institues/' + id,
		method: 'get'
	});
}
export function patchInstitues(id, data) {
	return request({
		url: server + '/institues/' + id,
		method: 'patch',
		data
	});
}
export function postInstitues(data) {
	return request({
		url: server + '/institues/',
		method: 'post',
		data
	});
}

// 删除机构
export function deleteInstitues(id) {
	return request({
		url: server + '/institues/' + id,
		method: 'delete'
	});
}
export function getServiceplans(id) {
	return request({
		url: server + '/serviceplans/' + id,
		method: 'get'
	});
}
export function patchServiceplans(id, data) {
	return request({
		url: server + '/serviceplans/' + id,
		method: 'patch',
		data
	});
}
export function postServiceplans(data) {
	return request({
		url: server + '/serviceplans/',
		method: 'post',
		data
	});
}
export function getUsers() {
	return request({
		url: server + '/users',
		method: 'get'
	});
}
export function getUsersRoles() {
	return request({
		url: server + '/users/roles',
		method: 'get'
	});
}
export function patchUsers(id, data) {
	return request({
		url: server + '/users/' + id,
		method: 'patch',
		data
	});
}
export function postUsers(data) {
	return request({
		url: server + '/users',
		method: 'post',
		data
	});
}
export function deleteUsers(id) {
	return request({
		url: server + '/users/' + id,
		method: 'delete'
	});
}
