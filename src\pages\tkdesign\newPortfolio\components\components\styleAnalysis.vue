<template>
    <div class="plate-wrapper fund-performance-board-wrapper">
        <combinationComponentHeader title="风格分析" showMoreBtn @download="exportExcelAll">
            <template slot="right">
                <!-- <el-select v-model="comparisonValue" placeholder="选择比较基准" style="margin-right: 16px;">
                    <el-option
                        v-for="item in options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                        </el-option>
                </el-select> -->
                <div style="margin-right: 16px;">
                    <FormTimePicker v-model="preset_time" @input="handleFormChange"></FormTimePicker>
                </div>
            </template>
        </combinationComponentHeader>
  
        <div style="display: flex;justify-content: space-between;gap:12px;align-items: center;margin-top: 20px;">
            <div class="right" style="flex: 1;border:1px solid rgba(217, 217, 217, 1);border-radius: 4px;overflow: hidden;">
                <div class="item-title">
                    <span>
                        报告期前十大集中度
                    </span>
                    <img src="@/assets/img/tkdesign/download.png" alt="" srcset="" @click="exportExcel">
                </div>
                <lineChartForStyleAnalysis ref="fund-performance-board-chart-container2" :name="'concentrationInfo'" @tableData="getTableData" :key="'concentrationInfo'"></lineChartForStyleAnalysis>
            </div>
            <div class="right" style="flex: 1;border:1px solid rgba(217, 217, 217, 1);border-radius: 4px;overflow: hidden;">
                <div class="item-title">
                    <span>报告期换手率</span>
                    <img src="@/assets/img/tkdesign/download.png" alt="" srcset="" @click="exportExcel2"> 
                </div>
                <lineChartForStyleAnalysis ref="fund-performance-board-chart-container" @tableData="getTableData2" :key="'turnoverInfo'"></lineChartForStyleAnalysis>
            </div>
        </div>
        <div style="border:1px solid rgba(217, 217, 217, 1);border-radius: 4px;margin-top: 16px;overflow: hidden;">
            <div class="item-title">
                <span>持仓风格</span>
                <img src="@/assets/img/tkdesign/download.png" alt="" srcset="" @click="exportExcel3">
            </div>
            <PolylineStackDiagramChart ref="fund-performance-board-chart-container3" style="flex: 1;" @tableData="getTableData3"></PolylineStackDiagramChart>
        </div>
    </div>
</template>
<script>
import combinationComponentHeader from './combinationComponentHeader.vue';
import lineChartForStyleAnalysis from '../chart/lineChartForStyleAnalysis.vue';
import PolylineStackDiagramChart from '../chart/PolylineStackDiagramChart.vue';
import FormTimePicker from './formTimePicker.vue';
import { filter_to_excel } from "@/utils/exportExcel.js";
export default {
    name:'styleAnalysis',
    components:{
        combinationComponentHeader,
        lineChartForStyleAnalysis,
        PolylineStackDiagramChart,
        FormTimePicker
    },
    data(){
        return {
            form:{},
            comparisonValue:'默认指标',
            options:[{
                label:'默认指标',
                value: '默认指标'
            }],
            preset_time: {
                radioValue: '1',
                startDate: this.moment().subtract(1, 'year').format('YYYY-MM-DD'),
                endDate: this.moment().format('YYYY-MM-DD')
            },
            param:null,
            //报告期前十大集中度表头
            tableHeader:[{
                prop: 'date',
                label: '日期'
            },{
                prop: 'concentrationData',
                label: '前十大集中度'
            },
            // {
            //     prop: 'middleConcentrationRank',
            //     label: '前十大集中度中位数'
            // },{
            //     prop: 'top10ConcentrationRank',
            //     label: '全市场排名分位'
            // }
            ],
            //报告期前十大集中度数据
            tableData:[],
            //报告期换手率表头
            tableHeader2:[{
                prop: 'date',
                label: '日期'
            },{
                prop: 'turnover',
                label: '换手率'
            },
            // {
            //     prop: 'turnoverMiddle',
            //     label: '换手率中位数'
            // },{
            //     prop: 'percentRank',
            //     label: '全市场排名分位'
            // }
            ],
            //报告期换手率数据
            tableData2:[],
            //持仓风格表头
            tableHeader3:[{
                prop: 'name',
                label: '产品名称'
            }],
            //持仓风格数据
            tableData3:[],
            
        }
    },
    methods:{
        radioChange(){
            this.$refs['valuation-chart2']?.getData()
        },
        handleFormChange(val) {
            this.preset_time = val;
            this.getData(this.param);
		},
        getData(param){
            this.param = param;
            
            let chartDom = this.$refs['fund-performance-board-chart-container'];
            chartDom?.getData({
                ...param,
                startDate: this.preset_time.startDate,
                endDate: this.preset_time.endDate,
            });
            let chartDom2 = this.$refs['fund-performance-board-chart-container2'];
            chartDom2?.getData({
                ...param,
                startDate: this.preset_time.startDate,
                endDate: this.preset_time.endDate,
            });
            let chartDom3 = this.$refs['fund-performance-board-chart-container3'];
            chartDom3?.getData({
                ...param,
                startDate: this.preset_time.startDate,
                endDate: this.preset_time.endDate,
            });
        },
         // 获取表格数据的方法
       getTableData(val) {
         
         // 遍历图例列表，为每个图例创建一个表格行
         val.dateList.forEach((item, index) => {
           this.tableData.push({
            date: item, // 将图例名称作为表格行的 name 属性
            concentrationData:val.data1[index],
            // middleConcentrationRank:val.data2[index],
            // top10ConcentrationRank:val.data3[index]
           });
          
         });
       },
       // 获取表格数据的方法
       getTableData2(val) {
         // 遍历图例列表，为每个图例创建一个表格行
         val.dateList.forEach((item, index) => {
           this.tableData2.push({
            date: item, // 将图例名称作为表格行的 name 属性
            turnover:val.data1[index],
            // turnoverMiddle:val.data2[index],
            // percentRank:val.data3[index]
           });
          
         });
       },
              // 获取表格数据的方法
              getTableData3(val) {
         // 遍历日期列表，将每个日期作为表头添加到表头数组中
         val.date_list.forEach(item => {
           this.tableHeader3.push({
             prop: item, // 使用日期作为表头属性名
             label: item // 使用日期作为表头显示名称
           });
         });
         // 遍历图例列表，为每个图例创建一个表格行
         val.itemList.forEach((item, index) => {
           this.tableData3.push({
             name: item.label // 将图例名称作为表格行的 name 属性
           });
           // 遍历日期列表，将每个日期对应的数据填充到表格行中
           val.date_list.forEach((item2, index2) => {
             this.tableData3[index][item2] = val.series[index]['data'][index2]; // 将数据填充到表格行中
           });
         });
       },
      exportExcel(){
          // 将表头数据进行遍历，生成新的数组list，每个元素包含原表头数据和format字段
          let list = this.tableHeader.map((item) => {
            return {
              ...item,
              format: ''
            };
          });
          // 调用filter_to_excel函数，传入list、表格数据this.tableData和文件名'基金标签'
          filter_to_excel(list, this.tableData, '报告期前十大集中度');
        },
        exportExcel2(){
          // 将表头数据进行遍历，生成新的数组list，每个元素包含原表头数据和format字段
          let list = this.tableHeader2.map((item) => {
            return {
              ...item,
              format: ''
            };
          });
          // 调用filter_to_excel函数，传入list、表格数据this.tableData和文件名'基金标签'
          filter_to_excel(list, this.tableData2, '报告期换手率');
        },
        exportExcel3(){
          // 将表头数据进行遍历，生成新的数组list，每个元素包含原表头数据和format字段
          let list = this.tableHeader3.map((item) => {
            return {
              ...item,
              format: ''
            };
          });
          // 调用filter_to_excel函数，传入list、表格数据this.tableData和文件名'基金标签'
          filter_to_excel(list, this.tableData3, '持仓风格');
        },
        exportExcelAll(){
          this.exportExcel();
          this.exportExcel2();
          this.exportExcel3();
        }
    },
    
}
</script>
<style lang="scss" scoped>
.fund-performance-board-wrapper{
    .select-form-wrapper {
        margin-bottom: 16px;
    }
    .content-table-wrapper {
        margin-bottom: 32px;
    }
    .item-title{
        height: 46px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid rgba(233, 233, 233, 1);
        margin: 0 20px;
        span{
            color: rgba(0, 0, 0, 0.85);
            font-size: 14px;
            display: block;
        }
        img{
            width: 16px;
            height: 16px;
            display: block;
        }
    }
}

</style>