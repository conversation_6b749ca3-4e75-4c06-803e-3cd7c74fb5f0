<template>
	<div class="chart_one">
		<div>
			<div class="title">产品类型饼图</div>
		</div>
		<div class="charts_fill_class" v-loading="loading">
			<v-chart
				ref="fundPieChart"
				:options="option"
				element-loading-text="暂无数据"
				element-loading-spinner="el-icon-document-delete"
				element-loading-background="rgba(239, 239, 239, 0.5)"
				class="charts_one_class"
				autoresize
			></v-chart>
		</div>
	</div>
</template>

<script>
import VChart from 'vue-echarts';
export default {
	components: { VChart },
	data() {
		return {
			option: {},
			loading: true
		};
	},
	methods: {
		// 获取数据
		getData() {
			this.loading = false;
			this.option = {
				tooltip: {
					trigger: 'item'
				},
				legend: {
					top: '5%',
					left: 'center'
				},
				series: [
					{
						name: 'Access From',
						type: 'pie',
						radius: ['40%', '70%'],
						avoidLabelOverlap: false,
						label: {
							show: false,
							position: 'center'
						},
						emphasis: {
							label: {
								show: true,
								fontSize: '40',
								fontWeight: 'bold'
							}
						},
						labelLine: {
							show: false
						},
						data: [
							{ value: 1048, name: 'Search Engine' },
							{ value: 735, name: 'Direct' },
							{ value: 580, name: 'Email' },
							{ value: 484, name: 'Union Ads' },
							{ value: 300, name: 'Video Ads' }
						]
					}
				]
			};
		}
	}
};
</script>

<style></style>
