<!--  -->
<template>
  <div v-loading="loading"
       class="buyselldws">
    <div style="display: flex; align-items: center">
      <div class="TitltCompare">PBROE特征</div>
    </div>
    <sTable :data="stock_hold"
            typeFlag="1"></sTable>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import sTable from '../SelfTable.vue';
import { ManagerLongHoldStats, FundLongStockStat } from '@/api/pages/tools/compare.js';
export default {
  //import引入的组件需要注入到对象中才能使用
  components: { sTable },
  props: {
    comparetype: {
      type: String,
      default: 'manager' //fund
    },
    id: {
      type: String,
      default: '30189741,30441407'
    },
    type: {
      type: String,
      default: 'equity'
    },
    name: {
      type: String,
      default: '萧楠,胡昕炜'
    }
  },
  data () {
    //这里存放数据
    return {
      stock_holdcolumns: [],
      stock_holdcolumnsfund: [],
      stock_hold: [],
      loading: false
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  filters: {
    fix3 (value) {
      if (value == '--' || value == null || value == '' || value == 'nan') {
        return '--';
      } else {
        return (value * 100).toFixed(2) + '%';
      }
    },
    fix2 (value) {
      return Number(value).toFixed(2) + '亿';
    }
  },
  //方法集合
  methods: {
    getdata () {
      Object.assign(this.$data, this.$options.data());
      this.loading = true;
      if (this.comparetype == 'manager') {
        this.getmanager();
      } else {
        this.gefunddata();
      }
    },
    async getmanager () {
      this.stock_hold = [];
      this.stock_holdcolumns = [
        {
          dataIndex: 'description',
          key: 'description',
          title: 'PB-ROE特征'
        }
      ];
      let data = await ManagerLongHoldStats({ manager_code: this.id, type: this.type, manager_name: this.name });
      this.loading = false;

      if (data) {
        this.stock_hold = [];
        for (let i = 0; i < data.data.length; i++) {
          this.stock_hold[i] = [];
          this.stock_hold[i].push(data.data[i].description);
          for (let j = 0; j < this.$route.query.name.split(',').length; j++) {
            this.FUNC.isEmpty(data.data[i][this.$route.query.name.split(',')[j]])
              ? this.stock_hold[i].push((Number(data.data[i][this.$route.query.name.split(',')[j]]) * 100).toFixed(2) + '%')
              : this.stock_hold[i].push('--');
          }
        }
      }
      //  //console.log(this.arrlist)
    },
    async gefunddata () {
      this.stock_hold = [];
      this.stock_holdcolumnsfund = [
        {
          dataIndex: 'description',
          key: 'description',
          title: 'PB-ROE特征'
        }
      ];
      let data = await FundLongStockStat({ fund_code: this.id, type: this.type, fund_name: this.name });
      this.loading = false;

      if (data) {
        this.stock_hold = [];
        for (let i = 0; i < data.data.length; i++) {
          this.stock_hold[i] = [];
          this.stock_hold[i].push(data.data[i].description);
          for (let j = 0; j < this.$route.query.name.split(',').length; j++) {
            this.FUNC.isEmpty(data.data[i][this.$route.query.name.split(',')[j]])
              ? this.stock_hold[i].push((Number(data.data[i][this.$route.query.name.split(',')[j]]) * 100).toFixed(2) + '%')
              : this.stock_hold[i].push('--');
          }
        }
      }
    },
    createPrintWord () {
      let name = this.name.split(',');
      let data = [['', ...name], ...this.stock_hold];
      return [...this.$exportWord.exportTitle('PB-ROE特征'), ...this.$exportWord.exportCompareTable(data, [], true)];
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () { },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
</style>
