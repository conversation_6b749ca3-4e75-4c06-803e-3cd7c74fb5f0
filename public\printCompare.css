@page {
	size: auto;
	margin: 3mm;
}

@media print {
	.compareBox {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: flex-start;
		padding: 0px;
		gap: 26px;
		background: #fafafa;
		border-radius: 4px;
		flex: none;
		order: 0;
		flex-grow: 0;
		flex: 1;
		margin-left: 10px;
		
	}
	.compareBox .name {
		font-style: normal;
		font-weight: 500;
		font-size: 16px;
		line-height: 24px;
		color: rgba(0, 0, 0, 0.85);
		flex: none;
		order: 0;
		flex-grow: 0;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 1;
	}
	.compareBox	.name :hover {
		font-style: normal;
		font-weight: 500;
		font-size: 16px;
		line-height: 24px;
		color: rgba(0, 0, 0, 0.85);
		flex: none;
		order: 0;
		flex-grow: 0;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 1;
	}
	.compareBox .company {
		font-style: normal;
		font-weight: 400;
		font-size: 14px;
		line-height: 22px;
		color: rgba(0, 0, 0, 0.65);
		flex: none;
		order: 1;
		flex-grow: 0;
	}
	.compareBox .date {
		font-style: normal;
		font-weight: 400;
		font-size: 14px;
		line-height: 22px;
		color: rgba(0, 0, 0, 0.45);
	}
	.compareBox .nav {
		font-style: normal;
		font-weight: 400;
		font-size: 14px;
		line-height: 22px;
		color: rgba(0, 0, 0, 0.45);
	}
	.contentCompare {
		margin-top: 16px;
		background: #fff;
		padding: 16px 24px;
	}
	.contentCompare2 {
		margin-top: -8px;
		margin-left: -24px;
	}
	.coloriconFlag {
		width: 4px;
		height: 24px;
		position: absolute;
		border-radius: 15px;
		flex: none;
		order: 0;
		flex-grow: 0;
		top: 20px;
	}
	
	.compareFixHeader {
		width: 100%;
		height: 178px;
		background: #fff;
		box-shadow: 0px 5px 12px 4px rgba(0, 0, 18, 0.02);
		position: -webkit-sticky;
		position: sticky !important;
		top: 0px;
		z-index: 100;
		margin-bottom: 16px;
		display: flex;
		padding: 16px 24px 16px 24px;
	}
	.rowdetail {
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 1;
	}
	.rowdetail:hover {
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2000;
	}
	.selfTable {
		font-size: 14px;
		width: 100%;
		font-weight: 500;
		color: rgba(0, 0, 0, 0.85);
	}
	.coloriconFlag {
		width: 4px;
		height: 24px;
		position: absolute;
		border-radius: 15px;
		flex: none;
		order: 0;
		flex-grow: 0;
		top: 16px;
	}
	.table_content {
		width: 100%;
		min-height: 56px;
		display: flex;
		flex-direction: column;
		justify-content: center;
		text-align: center;
	}
	.tableHover:hover {
		background: #fafafa;
	}
	
	.contentCompare {
		margin-top: 16px;
		background: #fff;
		padding: 16px 24px;
	}
	.contentCompare2 {
		margin-top: -8px;
		margin-left: -24px;
	}
	.coloriconFlag {
		width: 4px;
		height: 24px;
		position: absolute;
		border-radius: 15px;
		flex: none;
		order: 0;
		flex-grow: 0;
		top: 20px;
	}
	
	.compareFixHeader {
		width: 100%;
		height: 178px;
		background: #fff;
		box-shadow: 0px 5px 12px 4px rgba(0, 0, 18, 0.02);
		position: -webkit-sticky;
		position: sticky !important;
		top: 0px;
		z-index: 100;
		margin-bottom: 16px;
		display: flex;
		padding: 16px 24px;
	}
	.el-progress--circle .el-progress__text, .el-progress--dashboard .el-progress__text {
		position: absolute;
		top: 50%;
		left: 0;
		width: 100%;
		text-align: center;
		margin: 0;
		-webkit-transform: translate(0,-50%);
		transform: translate(0,-50%);
	}
	.equityrankprintUni{
		flex: 1 !important; min-width:  560px !important; background: #fff !important; padding: 24px 24px 12px 24px !important; margin-left: 24px !important; margin-top: 24px !important;page-break-inside: avoid !important;
	}
	.pbai{
		page-break-inside: avoid 
	}
}
