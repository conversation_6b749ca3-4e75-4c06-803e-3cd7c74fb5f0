<template>
    <div class="charts_fill_class" v-loading="loading">
        <el-table
			class="content-table-wrapper"
			style="width: 100%"
			:data="tableDataNow"
			v-loading="loading"
            border stripe
		>
			<template v-for="(item, index) in tableHeader">
			
				<el-table-column
					:prop="item.prop"
					:label="item.label"
					:sortable="index === 0 ? false :true"
					align="gotoleft"
					:key="index"
				>
				<template   slot-scope="scope" >
							{{ item.prop === 'name' ? scope.row[item.prop] :fix2p(scope.row[item.prop])}}
				</template>
				</el-table-column>
			</template>
			<template slot="empty">
                <el-empty image-size="160"></el-empty>
            </template>
		</el-table>
    </div>
</template>

<script>

import { equityHoldStyle} from '@/api/pages/tkAnalysis/portfolio.js';
export default {
data() {
    return {
        option: {},
        loading: true,
        tableHeader: [
            {
                prop: 'name',
                label: '持仓风格'
            }
        ],
        tableDataNow:[]
    };
},
methods: {
	showLoading() {
		this.loading = true;
	},
	hideLoading() {
		this.loading = false;
	},
    async getData(data) {
        this.loading = false;
        if(data.length === 0){
            return;
        }
        const {tableHeader,tableDataNow} = this.filterData(data);
		this.$emit('tableData',{tableHeader,tableDataNow});
        this.tableHeader = tableHeader;
        this.tableDataNow  = tableDataNow;
       
    },
    fix2p(value) {
			return value &&
				value != '' &&
				value != '--' &&
				value != '- -' &&
				JSON.stringify(value) != '[]' &&
				JSON.stringify(value) != '{}' &&
				value != 'NAN' &&
				value != 'nan'
				? (Number(value) * 100).toFixed(2) + '%'
				: '--';
		},
    filterData(data){

		let dateList = [];
       
        let tableDataNow = [];
		let tableHeader = [
            {
                prop: 'name',
                label: '持仓风格'
            },
        ];
        data.forEach((element,index) => {
            tableDataNow.push({
                name:element.name
            })
            element.date.forEach((date,key)=>{
				dateList.push(date)
            })
        });
		
		dateList = [...new Set(dateList)].sort();
		dateList.forEach((item,key)=>{
			tableHeader.push({
				prop:item,
				label:item
			})
		})
	
		data.forEach((element,index) => {
			dateList.forEach((item,key)=>{
				let dateIndex = element.date.indexOf(item)
				if(dateIndex === -1){
					tableDataNow[index][item] = '--';
				}else{
					tableDataNow[index][item] = element.position[dateIndex]
				}

			})
		
           
        });
        return {tableHeader,tableDataNow}
    }
}
};
</script>

<style scoped>
.chart_one{
padding: 0;
box-shadow: none;
}
</style>
