<template>
	<div class="fundCompanyNetasset">
		<div class="flex_card">
			<div class="small_template" style="flex: 2">
				<div class="title">基金公司规模</div>
				<company-netasset @legendselectchanged="legendselectchanged" ref="companyNetasset"></company-netasset>
			</div>
			<div class="small_template">
				<div style="display: flex; justify-content: space-between; align-items: center">
					<div class="title">公募基金规模</div>
					<div style="display: flex; align-items: center">
						<el-select style="width: 100px; margin-right: 16px" v-model="model" @change="getFundCompanyNetasset">
							<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
						</el-select>
						<el-select style="width: 100px; margin-right: 16px" v-model="date" @change="getFundCompanyNetasset">
							<el-option v-for="item in dateList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
						</el-select>
						<div>
							<el-button icon="el-icon-document-delete" @click="exportExcel">导出Excel</el-button>
						</div>
					</div>
				</div>
				<div style="margin-top: 16px">
					<el-table v-loading="loading" :data="data" style="width: 100%" height="375px" :row-class-name="tableRowClassName">
						<el-table-column
							v-for="item in column"
							:key="item.value"
							:prop="item.value"
							:label="item.label"
							:sortable="item.sortable ? item.sortable : false"
							:default-sort="{ prop: 'netasset', order: 'descending' }"
							align="gotoleft"
						>
							<template #header>
								<long-table-popover-chart
									v-if="item.popover"
									:data="data"
									date_key="name"
									:data_key="item.value"
									:show_name="item.label"
									:formatter="
										function (val) {
											return val;
										}
									"
								>
									<span>{{ item.label }}</span>
								</long-table-popover-chart>
								<span v-else>{{ item.label }}</span>
							</template>
							<template slot-scope="scope">
								<div>
									<span v-if="item.value == 'rank'">{{ scope.row[item.value] }}</span>
									<el-link v-else-if="item.value == 'name'" @click="goDetail(scope.row)">{{ scope.row[item.value] }}</el-link>
									<span v-else>{{ scope.row[item.value] }}</span>
								</div>
							</template>
						</el-table-column>
						<template slot="empty">
							<el-empty image-size="160"></el-empty>
						</template>
					</el-table>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import companyNetasset from '@/components/components/components/companyNetasset/index.vue';
import { filter_json_to_excel } from '@/utils/exportExcel.js';
// 所属基金概况/公募基金规模
import { getAllTypeFundBasicInfo } from '@/api/pages/SystemOther.js';
import { getDateList } from '@/api/pages/Analysis.js';
export default {
	components: { companyNetasset },
	data() {
		return {
			info: {},
			loading: true,
			selectedList: [],
			model: '',
			options: [],
			data: [],
			date: '',
			dateList: [],
			column: [
				{
					label: '排名',
					value: 'rank',
					sortable: true,
					popover: false
				},
				{
					label: '基金公司',
					value: 'name',
					popover: false
				},
				{
					label: '数量(只)',
					value: 'number',
					sortable: true,
					popover: true
				},
				{
					label: '规模(亿元)',
					value: 'netasset',
					sortable: true,
					popover: true
				}
			]
		};
	},
	methods: {
		// 获取基金公司数据
		getData(data) {
			let series = [];
			let legend = [];
			let xAxis = [];
			for (const key in data) {
				if (key != 'code' && key != 'number' && key != 'yearqtr') {
					legend.push(this.FUNC.textConverter(this.COMMON.fundType_zh_en, key, 'en', 'zh'));
					series.push({
						name: this.FUNC.textConverter(this.COMMON.fundType_zh_en, key, 'en', 'zh'),
						type: 'bar',
						stack: '总量',
						tooltip: function (value) {
							return value + ' 亿元';
						},
						data: data[key].map((item) => {
							return (item / 10 ** 8).toFixed(2);
						})
					});
				} else if (key == 'yearqtr') {
					xAxis = data[key];
				}
			}
			legend.push('数量');
			series.push({
				name: '数量',
				type: 'line',
				yAxisIndex: 1,
				tooltip: function (value) {
					return value;
				},
				data: data['number']
			});

			this.$refs['companyNetasset'].getData({ series, legend, xAxis, xAxis });
		},
		// 获取基金类型列表
		getTypeList(info) {
			this.info = info;
			this.options = info.type?.map((item) => {
				return {
					label: this.FUNC.textConverter(this.COMMON.fundType_zh_en, item, 'en', 'zh'),
					value: item
				};
			});
			this.model = this.options?.[0]?.value;
			this.getDateList();
		},
		// 获取基金公司季度列表
		async getDateList() {
			let data = await getDateList({ code: this.info.code, type: this.info.type, flag: this.info.flag });
			if (data?.mtycode == 200) {
				this.dateList = data?.data
					.sort((a, b) => {
						return this.moment(this.moment(a, 'YYYY QQ').format()).isAfter(this.moment(b, 'YYYY QQ').format()) ? -1 : 1;
					})
					.map((item) => {
						return {
							label: item,
							value: item
						};
					});
				this.date = this.dateList?.[0].value;
			} else {
				this.dateList = [];
			}
			this.getFundCompanyNetasset();
		},
		// 获取公募基金数据
		getTable(data) {
			this.loading = false;
			this.data = data?.[0]?.data
				.sort((a, b) => {
					return b.netasset - a.netasset;
				})
				.map((item, index) => {
					return {
						...item,
						rank: index + 1,
						netasset: item.netasset * 1 ? (item.netasset / 10 ** 8).toFixed(2) * 1 : '--'
					};
				});
			let selfData = this.data.filter((item) => {
				return item.code == this.info.code;
			})?.[0];
			if (
				data?.[0]?.data.findIndex((item) => {
					return item.code == this.info.code;
				}) != -1
			) {
				this.data.splice(
					data?.[0]?.data.findIndex((item) => {
						return item.code == this.info.code;
					}),
					1
				);
			}
			if (selfData) {
				this.data.unshift(selfData);
			}
		},
		// 获取公募基金规模
		async getFundCompanyNetasset() {
			this.loading = true;
			let data = await getAllTypeFundBasicInfo({ type: this.model ? this.model : this.info.type?.[0], yearqtr: this.date });
			if (data?.mtycode == 200) {
				this.getTable(data?.data?.company_rank);
			}
		},
		// 前往公司详情
		goDetail(val) {
			this.$router.push({
				path: '/fundCompany/' + val.code,
				hash: '',
				query: {
					id: val.code,
					name: val.name
				}
			});
		},
		// 切换基金类型
		changeType(val) {
			this.model = val;
			this.getFundCompanyNetasset();
		},
		// 监听图例点击
		legendselectchanged(val) {
			this.selectedList = val;
		},
		// 为表格行添加class
		tableRowClassName({ row, rowIndex }) {
			if (row.code == this.info.code) {
				return 'table-even-line';
			}
		},
		exportExcel() {
			let list = this.column;
			let data = this.data;
			filter_json_to_excel(list, data, '公募基金规模');
		}
	}
};
</script>

<style lang="scss" scoped>
.fundCompanyNetasset {
	.small_template {
		height: 455px;
	}
	::v-deep .table-even-line {
		background: rgba(55, 120, 246, 0.5);
		// position: sticky;
		// top: 0px;
		// z-index: 10000;
	}
}
</style>
