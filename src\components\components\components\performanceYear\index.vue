<template>
	<div>
		<analysis-card-title title="业绩表现" @downloadExcel="exportExcel">
			<el-select v-model="activeBenchmark" @change="changeBenchmark">
				<el-option v-for="item in benchmarkList" :key="item.code" :label="item.name" :value="item.code"> </el-option>
			</el-select>
		</analysis-card-title>
		<el-table
			:data="data"
			v-loading="loading"
			height="300px"
			:cell-style="cellstyle"
			border
			stripe
			ref="multipleTable"
			header-cell-class-name="table-header"
		>
			<el-table-column
				v-for="item in columns"
				:key="item.value"
				:align="item.align || 'gotoleft'"
				:prop="item.value"
				:label="item.label"
				:min-width="item.width"
				:sortable="item.sortable"
			>
				<template #header>
					<long-table-popover-chart
						v-if="item.popover"
						:data="foramtPopoverData()"
						date_key="dateFlag"
						:data_key="item.value"
						:show_name="item.label"
					>
						<span>{{ item.label }}</span>
					</long-table-popover-chart>
					<span v-else>{{ item.label }}</span>
				</template>
				<template slot-scope="{ row }">
					<span>{{ item.format ? item.format(row[item.value]) : row[item.value] }}</span>
				</template>
			</el-table-column>
			<template slot="empty">
				<el-empty image-size="160"></el-empty>
			</template>
		</el-table>
	</div>
</template>

<script>
import { filter_json_to_excel } from '@/utils/exportExcel.js';

import { getBenchmarkList, getRiskFeatureYearly } from '@/api/pages/Analysis.js';
// 风险收益指标
export default {
	data() {
		return {
			description: null,
			data: [],
			columns: [],
			activeBenchmark: '',
			benchmarkList: [],
			info: {},
			loading: true
		};
	},
	methods: {
		// 获取风险收益指标
		async getRiskFeatureYearly() {
			this.loading = true;
			let riskFeatureYearlyData = {
				code: this.info.code,
				type: this.info.type,
				flag: this.info.flag,
				start_date: this.info.start_date,
				end_date: this.info.end_date,
				index_code: this.activeBenchmark
			};
			let data = await getRiskFeatureYearly(riskFeatureYearlyData);
			this.loading = false;
			if (data?.mtycode == 200) {
				this.data = data?.data.sort((a, b) => b.dateFlag - a.dateFlag);
			} else {
				this.data = [];
			}
		},
		// 获取数据
		async getData(info) {
			this.info = info;
			this.fundindextype = info?.type;
			await this.getBenchmarkList();
			this.setColumn();
			this.getRiskFeatureYearly();
		},
		// 监听基准切换
		changeBenchmark(val) {
			this.activeBenchmark = val;
			let index = this.columns.findIndex((v) => v.value == 'index_cum_return');
			let label = this.benchmarkList.find((v) => v.code == this.activeBenchmark)?.name + '收益';
			this.$set(this.columns, index, { ...this.columns[index], label });
			this.getRiskFeatureYearly();
		},
		// 初始化表头
		setColumn() {
			this.columns = [
				{
					label: '年份',
					value: 'dateFlag',
					width: '100px',
					sortable: true,
					popover: false
				},
				{
					label: '年累计收益',
					value: 'cumReturn',
					width: '150px',
					sortable: true,
					popover: true,
					format: this.fix2p
				},

				{
					label: '同类排名',
					value: 'cumReturnDescription',
					width: '120px',
					align: 'right',
					sortable: false,
					popover: false
					// format: this.fix2p
				},
				{
					label: this.benchmarkList.find((v) => v.code == this.activeBenchmark)?.name + '收益',
					value: 'index_cum_return',
					width: '150px',
					sortable: true,
					popover: true,
					format: this.fix2p
				},
				{
					label: '自身基准收益',
					// label: this.info.name,
					value: 'self_index',
					width: '150px',
					sortable: true,
					popover: true,
					format: this.fix2p
				},
				{
					label: '最大回撤',
					value: 'maxdrawdown',
					width: '150px',
					sortable: true,
					popover: true,
					format: this.fix2p
				},
				{
					label: '夏普率',
					value: 'sharpe',
					width: '150px',
					sortable: true,
					popover: true,
					format: this.fix3
				},
				{
					label: '波动率',
					value: 'volatility',
					width: '150px',
					sortable: true,
					popover: true,
					format: this.fix2p
				}
			];
			if (this.info.flag == 2) {
				this.columns.splice(
					this.columns.findIndex((v) => this.info.name == v.label),
					1
				);
			}
		},
		// 发送请求，获取基准列表
		async getBenchmarkList() {
			let data = await getBenchmarkList({
				code: this.info.code,
				flag: this.info.flag,
				type: this.info.type
			});
			this.benchmarkList = [];
			this.activeBenchmark = '';
			if (data?.mtycode == 200) {
				this.benchmarkList = data?.data
					.sort((a, b) => {
						return b.isdefault - a.isdefault;
					})
					.map((v) => {
						return { code: v.indexCode, name: v.indexName, flag: v.flag };
					});
				if (this.benchmarkList.findIndex((v) => v.code == '000300.SH') != -1) {
					this.activeBenchmark = '000300.SH';
				} else {
					this.activeBenchmark = this.benchmarkList?.[0]?.code;
				}
			} else {
				this.benchmarkList = this.COMMON.default_index[this.info.type].map((v) => {
					return { code: v.indexCode, name: v.indexName, flag: '6' };
				});
				this.activeBenchmark = this.benchmarkList?.[0]?.code;
			}
		},
		formatColumn() {
			let indexList = [
				{
					label: '跟踪误差',
					value: 'trackingerror',
					sortable: true,
					popover: true,
					format: this.fix2p
				},
				{
					label: '排名',
					value: 'trackingerrorRank',
					sortable: true,
					popover: true,
					format: this.fix2p
				}
			];
			let enhanceList = [
				{
					label: '信息比率',
					value: 'information',
					sortable: true,
					popover: true,
					format: this.fix2p
				},
				{
					label: '排名',
					value: 'informationRank',
					sortable: true,
					popover: true,
					format: this.fix2p
				}
			];
			if ((this.fundindextype = 'equityindex' || this.fundindextype == 'equityenhance')) {
				this.columns.push(...indexList);
			} else if ((this.fundindextype = 'equityenhance')) {
				this.columns.push(...enhanceList);
			}
		},
		// data
		foramtPopoverData() {
			let data = [];
			this.data.map((item) => {
				let obj = { ...item };
				for (const key in item) {
					let format = this.columns.find((obj) => {
						return obj.value == key;
					})?.format;
					if (format) {
						let val = format(item[key]);
						obj[key] = typeof val == 'string' ? (val.includes('%') ? val?.split('%')?.[0] * 1 : !isNaN(val) ? val * 1 : val) : val;
					}
				}
				data.push(obj);
			});
			return data;
		},
		// 行样式
		cellstyle({ row, column, rowIndex, columnIndex }) {
			if (columnIndex == 1) {
				if (row['cumReturn'] >= 0) {
					return 'color: #CF1322;';
				} else return 'color: #389E0D;';
			}
			if (columnIndex == 3) {
				if (row['index_cum_return'] >= 0) {
					return 'color: #CF1322;';
				} else return 'color: #389E0D;';
			}
		},
		fix2p(val) {
			return val == '--' ? val : !isNaN(val) ? Number(val * 100)?.toFixed(2) + '%' : '--';
		},
		fix3(value) {
			return parseInt(value * 1000) / 1000 ? parseInt(value * 1000) / 1000 : '--';
		},
		// 导出excel
		exportExcel() {
			let list = this.columns.map((item) => {
				return {
					label: item.label,
					value: item.value
				};
			});
			let data = this.data.map((obj) => {
				let object = { ...obj };
				for (const key in obj) {
					let format = this.columns.find((v) => v.value == key)?.format;
					if (format) {
						object[key] = format(obj[key]);
					}
				}
				return object;
			});
			filter_json_to_excel(list, data, '业绩表现');
		},
		// 导出
		async createPrintWord(info) {
			this.info = info;
			this.fundindextype = info?.type;
			this.activeBenchmark == '' ? await this.getBenchmarkList() : '';
			await this.getRiskFeatureYearly();
			return await new Promise((resolve, reject) => {
				this.$nextTick(async () => {
					let list = this.columns.map((item) => {
						return {
							label: item.label,
							value: item.value
						};
					});
					let data = this.data.map((obj) => {
						let object = { ...obj };
						for (const key in obj) {
							let format = this.columns.find((v) => v.value == key)?.format;
							if (format) {
								object[key] = format(obj[key]);
							}
						}
						return object;
					});
					if (data.length) {
						resolve([...this.$exportWord.exportTitle('业绩表现'), ...this.$exportWord.exportTable(list, data, '', true)]);
					} else {
						resolve([]);
					}
				});
			});
		}
	}
};
</script>

<style></style>
