<template>
	<div class="allocationAnalysis">
		<div class="flex_card">
			<div class="small_template" style="flex: 2">
				<div class="title">基金资产配置</div>
				<div class="charts_fill_class" v-loading="loading">
					<v-chart
						ref="barLineChartComponent"
						class="charts_one_class"
						autoresize
						element-loading-text="暂无数据"
						element-loading-spinner="el-icon-document-delete"
						element-loading-background="rgba(239, 239, 239, 0.5)"
						:options="option"
					/>
				</div>
			</div>
			<div class="small_template">
				<div style="display: flex; justify-content: space-between; align-items: center">
					<div class="title">相对上期资产配置变动({{ yearqtr }})</div>
				</div>
				<div class="charts_fill_class" v-loading="loadingChange">
					<v-chart
						ref="barLineChartComponent"
						class="charts_one_class"
						autoresize
						element-loading-text="暂无数据"
						element-loading-spinner="el-icon-document-delete"
						element-loading-background="rgba(239, 239, 239, 0.5)"
						:options="optionChange"
					/>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import VChart from 'vue-echarts';

import { barChartOption } from '@/utils/chartStyle.js';
export default {
	components: { VChart },
	data() {
		return {
			loading: true,
			loadingChange: true,
			option: {},
			optionChange: {},
			styleColor: [
				'#4096ff',
				'#4096ff',
				'#7388A9',
				'#6F80DD',
				'#6C96F2',
				'#FD6865',
				'#83D6AE',
				'#88C9E9',
				'#ED589D',
				'#FA541C',
				'#18C2A0',
				'#E85D2D'
			],
			yearqtr: ''
		};
	},
	methods: {
		getData(data) {
			this.loading = false;
			let list = [
				{
					label: 'ABS',
					value: 'abs_'
				},
				{
					label: '债券',
					value: 'bond'
				},
				{
					label: '货币',
					value: 'cash'
				},
				{
					label: '股票',
					value: 'equity'
				},
				{
					label: '基金',
					value: 'fund'
				},
				{
					label: '权证',
					value: 'option'
				},
				{
					label: '买入返售金融资产',
					value: 'repo'
				},
				{
					label: '其他',
					value: 'other'
				}
			];
			let series = [];
			for (const key in data) {
				let index = list.findIndex((item) => {
					return item.value == key;
				});
				if (index != -1) {
					series.push({
						name: list[index].label,
						type: 'bar',
						stack: '总量',
						barCategoryGap: '30%',
						itemStyle: {
							color: this.styleColor[index]
						},
						data: data[key].map((item) => {
							return item * 1 ? (item * 100).toFixed(2) : 0;
						})
					});
				}
			}
			this.option = barChartOption({
				toolbox: false,
				legend: {
					data: list.map((item) => {
						return item.label;
					})
				},
				xAxis: [{ data: data?.['yearqtr'] }],
				yAxis: [{ type: 'value', name: '占净值比(%)', max: 100, offset: 1 }],
				series
			});
			this.yearqtr = data?.['yearqtr']?.[data?.['yearqtr']?.length - 1];
			this.getChangeData(series);
		},
		// 获取较上期变化图
		getChangeData(data) {
			this.loadingChange = false;
			let series = [
				{
					name: '较上次变动',
					type: 'bar',
					data: []
				}
			];
			let yAxis = [];
			data.map((item) => {
				yAxis.push(item.name);
				series?.[0]?.data.push({
					value: (item?.data[item?.data?.length - 1] * 1 - item?.data[item?.data?.length - 2] * 1).toFixed(2),
					itemStyle: {
						color: item.itemStyle.color
					}
				});
			});
			this.optionChange = barChartOption({
				xAxis: [
					{
						type: 'category',
						data: data.map((item) => {
							return item.name;
						})
					}
				],
				yAxis: [
					{
						type: 'value',
						formatter: function (val) {
							return val + '%';
						}
					}
				],
				series
			});
			console.log(this.optionChange);
		}
	}
};
</script>
<style lang="scss" scoped>
.allocationAnalysis {
	.small_template {
		height: 455px;
	}
}
</style>
