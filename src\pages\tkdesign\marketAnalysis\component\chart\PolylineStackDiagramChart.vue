<template>
  <div class="position_style">
    <div class="flex_start">
      <div class="left_legend py-20">
        <el-button @click="value2Change"
                   type="text"
                   inactive-text=""> 清空图例</el-button>
        <div v-for="item in itemList"
             :key="item.label"
             class="mb-4 flex_start legend_item"
             @click="changeShow(item.label)">
          <div class="item_icon mr-8"
               :style="item.show ? `background-color:${item.color}` : `background-color:#D9D9D9`"></div>
          <div :style="item.show ? `color:rgba(0, 0, 0, 0.85);` : `color:rgba(0, 0, 0, 0.45);`">{{ item.label }}</div>
        </div>
      </div>
      <div class="charts_fill_class ml-20"
           style="flex: 1"
           v-loading="loading">
        <div style="position: absolute; top: 0; left: 0; z-index: 999">
          <el-switch @change="value1Change"
                     v-model="value1"
                     inactive-text="打开持仓估计"> </el-switch>
        </div>
        <el-empty v-show="!show"
                  :image-size="160"></el-empty>
        <v-chart v-show="show"
                 ref="positionStyle"
                 v-loading="loading"
                 style="width: 100%; height: 668px"
                 autoresize
                 @legendselected="handleLegendSelectChanged"
                 element-loading-text="暂无数据"
                 element-loading-spinner="el-icon-document-delete"
                 element-loading-background="rgba(239, 239, 239, 0.5)"
                 :options="optionex" />
      </div>
    </div>
  </div>
</template>

<script>
import { exportTitle, exportChart } from '@/utils/exportWord.js';
import { lineChartOption } from '@/utils/chartStyle.js';

// 行业配置表现
import { getIndustryConfigurationChange } from '@/api/pages/tkAnalysis/captial-market.js';

// 持仓风格
export default {
  name: 'industryPositionChange',
  data () {
    return {
      optionex: {},
      loading: true,
      show: true,
      info: {},
      value1: true,
      value2: false,
      postData: {},
      color: [
        '#4096ff',
        '#89B3FA',
        '#D0DFF9',
        '#4096ff',
        '#FFB462',
        '#F8D3AB',
        '#83BE57',
        '#A5D084',
        '#D0ECBA',
        '#904371',
        '#BF5694',
        '#DCA1C4',
        '#E8684A',
        '#EF907A',
        '#F9C5B9',
        '#F6BD16',
        '#CADAF2',
        '#EB2F96',
        '#F66DB8',
        '#FFBAE0',
        '#13C2C2',
        '#68E4DD',
        '#B5F5EC',
        '#B43438',
        '#D16063',
        '#EE9799',
        '#722ED1',
        '#B37FEB',
        '#A5D084',
        '#D0ECBA',
        '#904371',
        '#BF5694',
        '#DCA1C4',
        '#E8684A',
        '#EF907A',
        '#F9C5B9',
        '#F6BD16',
        '#CADAF2',
        '#EB2F96',
        '#F66DB8',
        '#FFBAE0',
        '#13C2C2',
        '#68E4DD',
        '#B5F5EC',
        '#B43438',
        '#D16063',
        '#EE9799',
        '#722ED1',
        '#B37FEB'
      ],
      data: [],
      itemList: [],
      weight: 'equivalency'
    };
  },
  methods: {
    // 处理双击只展示单个行业
    handleLegendSelectChanged (params) {
      // console.log(params);
      // console.log("object");
      // if (params.batch.length > 0 && params.batch[0].name) {
      //   const clickedLegend = params.name;

      //   // 更新 legend 选中状态，只保留双击的项
      //   const newSelected = {
      //     [clickedLegend]: true
      //   };

      //   this.optionex.value.legend.selected = newSelected;
      // }
    },
    value2Change () {
      // console.log(this.value2, this.itemList);
      // if (this.value2) {
      for (let i = 0; i < this.itemList.length; i++) {
        if (this.itemList[i].show == true) {
          // this.itemList[i].show = false
          this.changeShow(this.itemList[i].label)
        }
      }

      // } else {
      //   for (let i = 0; i < this.itemList.length; i++) {
      //     if (this.itemList[i].show == false) {
      //       // this.itemList[i].show = false
      //       this.changeShow(this.itemList[i].label)
      //     }
      //   }
      // }
    },
    value1Change () {
      this.getIndustryInfo();
    },
    // 获取父组件传递数据
    getData (postData) {
      this.postData = postData;
      this.getIndustryInfo();
    },
    // 获取行业配置数据
    async getIndustryInfo () {
      this.loading = true;
      /**
       * 预期data数据
       * [{
       * code: "010709",
       * excess_return: -0.0043709392051636264,
       * industryStandard: "申万(2021)",
       * industry_code: "110000",
       * industry_name: "农林牧渔",
       * industry_return: 0.44926761479007526,
       * weight: 0.0053923605273020515,
       * yearqtr: "2021 Q1"
       * }...]
       */
      let data = await getIndustryConfigurationChange({
        ...this.postData,
        positionFlag: this.value1
      });
      this.loading = false;
      this.show = true;

      if (data?.code === 200) {
        let list = data?.data.dataList;
        // 	.filter((item)=>{
        // 		item.proportionList = item.proportionList.filter((res)=>{
        // 			if(res.proportion >= 1){
        // 				return true;
        // 			}
        // 		})
        // 		if(item.proportionList.length >0) {
        // 			return item
        // 		}

        // 	})
        // 	list.sort((a,b)=>{
        // 	if(a['proportionList'][a.proportionList.length -1].proportion < b['proportionList'][b.proportionList.length -1].proportion){
        // 		return 1
        // 	}else{
        // 		return -1;
        // 	}
        // })
        this.filterItemList(list);
        this.drawLine(list);
      } else {
        this.hideLoading();
      }
    },

    // 切换具体图例的显隐
    changeShow (name) {
      let selected = {};
      // 获取当前点击的索引
      let index = this.itemList.findIndex((v) => v.label == name);
      // 修改数组中当前图例的显示状态
      this.$set(this.itemList, index, { ...this.itemList[index], show: !this.itemList[index].show });
      // 将数组转化成echart接收格式
      this.itemList.map((item) => {
        selected[item.label] = item.show;
      });
      this.optionex = { ...this.optionex, legend: { ...this.optionex.legend, selected } };
      // console.log(this.optionex);
    },
    // 画图
    drawLine (data) {
      let { series, date_list } = this.filterData(data);
      this.optionex = lineChartOption({
        toolbox: 'none',
        tooltip: {
          formatter: (params) => {
            let list = [];
            if (params.length >= 8) {
              list = params.filter((item) => {
                if (item.value >= 1) {
                  return item;
                }
              });
            } else {
              list = params;
            }

            list.sort((a, b) => {
              if (a.value - b.value < 0) {
                return 1;
              } else {
                return -1;
              }
            });
            let other_weight = 0;
            if (params.length >= 8) {
              params
                .filter((v) => v.value < 1)
                .map((v) => {
                  other_weight = other_weight + v.value;
                });
              list.push({ value: other_weight, seriesName: '其他<1%合计' });
            }

            let str = `<div style="display:flex;align-items:center;maring-bottom:8px;"><div style="margin-right:4px">时间:</div><div>${list[0].axisValue}</div></div>`;
            for (let i = 0; i < list.length; i++) {
              let value = list[i].value * 1 && !isNaN(list[i].value) ? (list[i].value * 1).toFixed(2) + '%' : '--';
              let dotHtml = `<div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:${this.itemList.find((v) => v.label == list[i].seriesName)?.color
                }"></div>`;
              str += `<div style="margin-bottom:8px;display:flex;align-items:center;justify-content:space-between;"><div style="display:flex;align-items:center;">${dotHtml}<div>${list[i].seriesName}:</div></div><div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">${value}</div></div>`;
            }
            return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${str}</div>`;
          }
        },
        dataZoom: true,
        xAxis: [{ type: 'category', data: date_list }],
        yAxis: [
          {
            type: 'value',
            // max: 100,
            formatter: function (val) {
              return val + '%';
            }
          }
        ],
        series,
        grid: {
          top: 62
        }
      });
    },
    // 过滤图例
    filterItemList (data) {
      let list = Array.from(new Set(data.map((v) => v.name)));
      this.itemList = list.map((v, i) => {
        return { label: v, color: this.color[i], show: true };
      });
    },
    // 隐藏模块
    hideLoading () {
      this.show = false;
    },
    exportImage () {
      let chart = this.$refs['positionStyle'].getDataURL({
        type: 'png',
        pixelRatio: 3,
        backgroundColor: '#fff',
        excludeComponents: ['dataZoom']
      });
      let aLink = document.createElement('a');
      aLink.style.display = 'none';
      aLink.href = chart;
      aLink.download = '持仓风格.jpg';
      // 触发点击-然后移除
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink);
    },
    // 过滤接收数据
    filterData (data) {
      let result = data;
      console.log(data);
      let date_list = []
      for (let i = 0; i < result.length; i++) {
        for (let j = 0; j < result[i].proportionList.length; j++) {
          date_list.push(result[i].proportionList[j].yearqtr)
        }
      }
      date_list = [...new Set(date_list)].sort()
      result.forEach(item => {
        // 已有的日期列表
        const existingDates = item.proportionList.map(proportionItem => proportionItem.yearqtr);
        // 遍历完整的日期列表，检查是否存在于已有的日期列表中，如果不存在，则向数组中插入相应的对象，并将其值设置为 0
        date_list.forEach(date => {
          if (!existingDates.includes(date)) {
            item.proportionList.push({ "yearqtr": date, "proportion": 0 });
          }
        });
        // 按日期排序
        // item.proportionList.sort((a, b) => {
        //   return new Date(a.yearqtr.replace(' Q', '-')) - new Date(b.yearqtr.replace(' Q', '-'));
        // });
      });
      // console.log(result)
      // let date_list = (Array.from(new Set(result.map((k) => k?.proportionList || [])).map((v) => v.yearqtr)));
      // let date_list = [];
      let series = [];
      this.itemList.map((item) => {
        let arr = result.filter((v) => v.name == item.label);
        series.push({
          name: item.label,
          type: 'line',
          stack: '总量',
          barWidth: '100%',
          symbol: 'none',
          lineStyle: {
            color: item.color
          },
          areaStyle: {
            color: item.color,
            opacity: 0.25
          },
          data: arr[0].proportionList.map((v) => (v.proportion ? v.proportion : 0))
        });
      });

      this.$emit('getChartData', { date_list, result });
      // console.log(series);
      return { date_list, series };
    },

    createPrintWord () {
      let height = this.$refs['positionStyle'].$el.clientHeight;
      let width = this.$refs['positionStyle'].$el.clientWidth;
      let chart = this.$refs['positionStyle'].getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff',
        excludeComponents: ['dataZoom']
      });
      return this.show ? [...exportTitle('持仓风格'), ...exportChart(chart, { width, height })] : [];
    }
  }
};
</script>

<style lang="scss" scoped>
.position_style {
	height: 728px;
	.left_legend {
		width: 120px;
		height: 748px;
		margin-top: -20px;
		margin-bottom: -20px;
		background: #fff;
		box-shadow: 8px 0px 20px 0px rgba(0, 0, 0, 0.08);
		.legend_item {
			cursor: pointer;
			color: rgba(0, 0, 0, 0.65);
			font-family: PingFang SC;
			font-size: 12px;
			font-style: normal;
			font-weight: 400;
			.item_icon {
				width: 12px;
				height: 8px;
			}
		}
	}
	.charts_fill_class {
		position: relative;
	}
}
</style>
