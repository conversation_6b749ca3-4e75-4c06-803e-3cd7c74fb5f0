<script>
import Custodian from "./components/custodian.vue"
import SubAccount from "./components/subAccount.vue"
import MOM from "./components/MOM.vue"
import FOF from "./components/FOF.vue"
import store from '@/store/store';
import UnitLinked from "./components/unitLinked.vue";
import { getImportYearList, getImportMsciLastDate } from '@/api/pages/tkdesign/performance'
export default {
  components: {
    Custodian,
    SubAccount,
    MOM,
    FOF,
    UnitLinked
  },
  data () {
    return {
      nowPost: '1',
      tableData: [{
        date: "2022年",
        fileUrl: "https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg",
        name: "投后数据V3",
        type: "xlsx",
      }, {
        date: "2023年",
        fileUrl: "https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg",
        name: "投后数据V3",
        type: "xlsx",
      }, {
        date: "2024年",
        fileUrl: "https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg",
        name: "投后数据V3",
        type: "xlsx",
      }],
      tableData2: [{
        date: "全量数据",
        fileUrl: "https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg",
        name: "投后数据V3",
        type: "xlsx",
      }],
      time: [],
      loadingPort: false,
      showDialog: false,
      showDialog2: false,
      activeName: 'first'
    }
  },
  methods: {
    // 分年度导出投后数据V3
    handleDownload (row) {
      // console.log(row);
      window.open(row.fileUrl, '_blank');
    },
    // 分年度上传投后数据V3
    handleInsert (row, flag) {
      this.nowPost = flag
      this.showDialog2 = true
      this.nowUploadDate = row.date
    },
    handleSuccess (response) {
      // 处理上传成功的逻辑
      this.loadingPort = false
      this.$message.success('上传成功');
      this.showDialog2 = false;
      this.changeBase()
    },
    handleError (error) {
      // 处理上传失败的逻辑
      this.$message.error('上传失败');
      this.loadingPort = false
      console.error(error);
    },
    beforeUpload (file) {
      this.loadingPort = true
      // 在上传之前执行的操作，例如限制文件类型
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
        file.type === 'application/vnd.ms-excel';
      if (!isExcel) {
        this.loadingPort = false
        this.$message.error('只能上传 Excel 文件');
      }
      return isExcel;
    },
    // 修改数据后触发
    changeBase () {
      this.$refs.custodian.init()
      this.$refs.subAccount.getPerformanceBoardAccount()
      this.getV3()
    },
    async getV3 () {
      let { data, code, message } = await getImportYearList()
      if (code == 200 && data?.date?.length != 0) {
        const currentYear = new Date().getFullYear();  // 获取当前年份
        // 找到给定数据中最小的年份
        const minYear = Math.min(...data?.date);

        // 创建一个数组，包含了从最小年份到当前年份的所有年份，并初始化为 0
        const result = Array.from({ length: currentYear - minYear + 1 }, (_, index) => {
          const year = minYear + index;
          return { 'date': year, 'flag': data?.date?.includes(year) ? '已导入' : '未导入', dateUpdate: data?.dateUpdate || '--' };
        });
        // console.log(result);
        this.tableData = result
      }
      else {
        const currentYear = new Date().getFullYear();  // 获取当前年份
        // 找到给定数据中最小的年份
        const minYear = Math.min('2020');
        // 创建一个数组，包含了从最小年份到当前年份的所有年份，并初始化为 0
        const result = Array.from({ length: currentYear - minYear + 1 }, (_, index) => {
          const year = minYear + index;
          return { 'date': year, 'flag': [].includes(year) ? '已导入' : '未导入', dateUpdate: data?.dateUpdate || '--' };
        });
        // console.log(result);
        this.tableData = result
      }
    },
    async getmsci () {
      let { data, code, message } = await getImportMsciLastDate()
      if (code == 200) {
        this.tableData2 = [{
          date: "全量数据",
          flag: data,
          fileUrl: "https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg",
          name: "投后数据V3",
          type: "xlsx",
        }]
      } else {

        this.tableData2 = [{
          date: "全量数据",
          flag: '无',
          fileUrl: "https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg",
          name: "投后数据V3",
          type: "xlsx",
        }]
      }
    }
  },
  computed: {
    token () {
      return 'Bearer ' + store.state.token;
    }
  },
  mounted () {
    // var today = new Date(); // 获取当前日期时间
    // var lastMonth = new Date(today); // 创建一个与今天相同的日期对象
    // lastMonth.setMonth(lastMonth.getMonth() - 1); // 将日期设置为上个月

    // var startDate = lastMonth; // 区间开始日期为上个月的今天
    // var endDate = today; // 区间结束日期为今天
    const currentYear = new Date().getFullYear();
    const startDate = new Date(currentYear, 0, 1);
    const now = new Date();
    const yesterday = new Date(now);
    yesterday.setDate(now.getDate() - 1);
    // const endDate = (new Date()).setDate(now.getDate() - 1);
    // const end = new Date();
    // const start = new Date();
    // start.setFullYear(end.getFullYear() - 1);
    // // const start = new Date(new Date().getFullYear(), 0, 1);
    this.time = [startDate, yesterday]
    this.getV3()
    this.getmsci()
  }
}
</script>
<template>
  <div class="box_Board">
    <div class="header_box">
      <span>
        <span class="header_inactive">投后&nbsp;/&nbsp;投后监控看板&nbsp;/&nbsp;</span>
        业绩看板
      </span>
      <div style="display:flex">
        <div>
          <el-button size="mini"
                     @click="showDialog = true"
                     type="primary">导入数据</el-button>
        </div>
        <el-date-picker style="margin-left:16px"
                        v-model="time"
                        size="mini"
                        type="daterange"
                        range-separator="-"
                        unlink-panels
                        start-placeholder="开始日期"
                        end-placeholder="结束日期" />
      </div>
    </div>
    <!-- 分管理人业绩 -->
    <div class="mb">
      <Custodian ref="custodian"
                 :time="time" />
    </div>
    <!-- 分账户业绩 -->
    <div class="mb">
      <SubAccount ref="subAccount"
                  :time="time" />
    </div>
    <!-- 直投MOM业绩看板 -->
    <div class="mb">
      <MOM :time="time" />
    </div>
    <!-- 直投FOF业绩看板 -->
    <div class="mb">
      <FOF :time="time" />
    </div>
    <!-- 投连业绩看板 -->
    <div class="mb">
      <UnitLinked :time="time" />
    </div>
    <el-dialog :visible.sync="showDialog"
               width="500px"
               title="数据导入"
               @close="showDialog = false">
      <el-tabs v-model="activeName"
               @tab-click="handleClick">
        <el-tab-pane label="投后数据"
                     name="first"><el-table :data="tableData"
                    style="width: 100%;max-height: 300px;overflow-y: auto">
            <el-table-column align="gotoleft"
                             label="日期"
                             prop="date"></el-table-column>
            <el-table-column align="gotoleft"
                             label="数据更新日期"
                             prop="dateUpdate"></el-table-column>
            <el-table-column align="gotoleft"
                             label="是否导入"
                             prop="flag"></el-table-column>
            <!-- <el-table-column label="文件">
          <template slot-scope="scope">
            <div @click="handleDownload(scope.row)">{{scope.row.filename}}</div>
          </template>
        </el-table-column> -->
            <el-table-column align="gotoleft"
                             label="操作">
              <template slot-scope="scope">
                <!-- <el-button @click="handleDownload(scope.row)"
                           type="text">导出</el-button> -->
                <el-button @click="handleInsert(scope.row,'1')"
                           type="text">覆盖性导入</el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- <div @click="()=>{window.open('./投后数据2023.xlsx')}"
               style="margin-top:16px;cursor: pointer;color: #4096ff;">数据示例：投后数据2023.xlsx下载</div> -->
        </el-tab-pane>
        <el-tab-pane label="MSCI数据"
                     name="second"><el-table :data="tableData2"
                    style="width: 100%;max-height: 300px;overflow-y: auto">
            <el-table-column align="gotoleft"
                             label="数据"
                             prop="date"></el-table-column>
            <el-table-column align="gotoleft"
                             label="最近更新日期"
                             prop="flag"></el-table-column>
            <!-- <el-table-column label="文件">
          <template slot-scope="scope">
            <div @click="handleDownload(scope.row)">{{scope.row.filename}}</div>
          </template>
        </el-table-column> -->
            <el-table-column align="gotoleft"
                             label="操作">
              <template slot-scope="scope">
                <!-- <el-button @click="handleDownload(scope.row)"
                           type="text">导出</el-button> -->
                <el-button @click="handleInsert(scope.row,'2')"
                           type="text">覆盖性导入</el-button>
              </template>
            </el-table-column>
          </el-table></el-tab-pane>
      </el-tabs>

    </el-dialog>
    <el-dialog :visible.sync="showDialog2"
               width="300px"
               title="上传投后数据">
      <div v-loading="loadingPort">
        <el-upload class="upload-demo"
                   :action="$baseUrl + (nowPost=='1'?'/api/taikang/board/dataBoard/importBaseData':'/api/taikang/board/dataBoard/importMsciBaseData')"
                   :show-file-list="false"
                   :on-success="handleSuccess"
                   ref="uploadRef"
                   :headers="{ Authorization: token }"
                   :on-error="handleError"
                   :before-upload="beforeUpload">
          <el-button type="primary">点击上传 Excel 文件</el-button>
        </el-upload>
      </div>
    </el-dialog>
  </div>
</template>
<style lang="scss" scoped>
@import '../../tkdesign';

.mb {
	margin-bottom: 16px;
}
.header_box {
	display: flex;
	align-items: center;
	justify-content: space-between;
}
</style>
