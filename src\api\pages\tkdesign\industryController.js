import request from '@/utils/request';

/**
 * 获取行业映射管理列表
 * @param params
 * @returns {*}
 */
export function getIndustryList(params) {
    return request({
        url: '/api/taikang/industry/getIndustry',
        method: 'get',
        params
    });
}

/**
 * 获取全部中信行业
 * @returns {*}
 */
export function getCenterIndustry() {
    return request({
        url: '/api/taikang/industry/option/industry',
        method: 'get',
    });
}

/**
 * 获取全部产业归属
 * @returns {*}
 */
export function getOwnership() {
    return request({
        url: '/api/taikang/industry/option/ownership',
        method: 'get',
    });
}

/**
 * 保存上传
 * @param data
 * @returns {*}
 */
export function saveIndustry(data) {
    return request({
        url: '/api/taikang/industry/save',
        method: 'post',
        data
    });
}

/**
 * 删除
 * @param id
 * @returns {*}
 */
export function deleteIndustry(id) {
    return request({
        url: `/api/taikang/industry/del?id=${id}`,
        method: 'post',
    });
}

/**
 * 删除
 */
export function deleteAll() {
    return request({
        url: `/api/taikang/industry/delAll`,
        method: 'post',
    });
}
// --------------------------------
/**
 * 下拉选项
 */
export function getListTL4(params) {
  return request({
      url: `/api/taikang/tl4/option/getList`,
      method: 'get',
      params
  });
}
// --------------------------------
/**
 * 表格列表
 */
export function getListTL4Table(params) {
  return request({
      url: `/api/taikang/tl4/getList`,
      method: 'get',
      params
  });
}
/**
 * 删除
 */
export function delListTL4Table(params) {
  return request({
      url: `/api/taikang/tl4/del`,
      method: 'post',
      params
  });
}
/**
 * 保存
 */
export function saveListTL4Table(data) {
  return request({
      url: `/api/taikang/tl4/save`,
      method: 'post',
      data
  });
}
/**
 * 数据刷新
 */
export function refreshData(params) {
  return request({
      url: `/api/taikang/tl4/refresh`,
      method: 'get',
      params
  });
}
/**
 * 数据刷新日期
 */
export function refreshDateTime(params) {
  return request({
      url: `/api/taikang/tl4/refresh/status`,
      method: 'get',
      params
  });
}

/**
 * 增加
 */
export function UPDescription(data) {
  return request({
      url: `/api/taikang/tl4/updateTl4Description`,
      method: 'post',
      data
  });
}
/**
 * 查询
 */
export function GetDescription(params) {
  return request({
      url: `/api/taikang/tl4/getTl4Description`,
      method: 'get',
      params
  });
}