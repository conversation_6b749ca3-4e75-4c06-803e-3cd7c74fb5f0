<template>
	<div class="chart_one" v-loading="loading">
		<div>
			<div style="display: flex; align-items: center; justify-content: space-between; position: relative; height: 56px">
				<div class="title">转债调性</div>
				<div>
					<el-date-picker
						@change="changeDate"
						value-format="yyyy-MM-dd"
						:unlink-panels="true"
						v-model="value1"
						type="daterange"
						range-separator="至"
						start-placeholder="开始日期"
						end-placeholder="结束日期"
					>
					</el-date-picker>
				</div>
			</div>
		</div>
		<v-chart
			class="charts_one_class"
			autoresize
			style="height: 150px"
			v-loading="showempty"
			element-loading-text="暂无数据"
			element-loading-spinner="el-icon-document-delete"
			element-loading-background="rgba(239, 239, 239, 0.5)"
			:options="niuxiongoption2"
		/>
		<v-chart
			class="charts_one_class"
			autoresize
			style="height: 250px"
			v-loading="showempty"
			element-loading-text="暂无数据"
			element-loading-spinner="el-icon-document-delete"
			element-loading-background="rgba(239, 239, 239, 0.5)"
			:options="niuxiongoption"
		/>
		<!-- <div> 蛛网图维度'alpha', '利率水平','利率斜率','利率凸性','信用利差','信用挖掘','可转债配置','A 股配置'</div> -->
	</div>
</template>
<script>
// 风格择时能力

import VCharts from 'vue-echarts';
export default {
	name: 'styleTimingAbility',
	filters: {
		fix6(value) {
			return value.substring(0, 10);
		},
		fix3(value) {
			return parseInt(value * 1000) / 1000;
		},
		fix4bf(value) {
			return (value * 100).toFixed(2) + '%';
		}
	},
	data() {
		return {
			styleColor: ['#4096ff', '#4096ff', '#7388A9', '#6F80DD', '#6C96F2', '#88C9E9', '#FD6865'],
			loading: true,
			niuxiongoption: {},
			niuxiongoption2: {},
			showempty: true,
			sflag: 0,
			value1: [],
			loadingtm: true
		};
	},
	methods: {
		// 获取父组件传递数据
		getData(data, info) {
			this.loading = false;
			this.loadingtm = false;
			if (data) {
				this.showempty = false;
				let bondlike = [];
				let equitylike = [];
				let mixlike = [];
				let returnList = [];
				let xdata = [];
				let xdata2 = [];
				let linS = [];
				for (let i = 0; i < data.length; i++) {
					bondlike.push(data[i].bond_like * 100);
					equitylike.push(data[i].stock_like * 100);
					mixlike.push(data[i].mix_like * 100);
					xdata.push(data[i].date);
					linS.push({
						type: 'line',
						symbol: 'none',
						data: [],
						name: '分季度累计收益',
						areaStyle: {
							color: Number(data[i].index_cum[data[i].index_cum.length - 1]) > 0 ? '#ffc9cc' : '#d4ffcc'
						},
						itemStyle: {
							normal: {
								color: Number(data[i].index_cum[data[i].index_cum.length - 1]) > 0 ? '#ff616a' : '#84ff6e'
							}
						}
					});
					for (let k = 0; k < data[i].index_cum.length; k++) {
						linS[i].data.push([returnList.length + k, data[i].index_cum[k] * 100]);
					}

					returnList = returnList.concat(data[i].index_cum);
				}
				for (let j = 0; j < returnList.length; j++) {
					xdata2.push(j + 1);
					returnList[j] = returnList[j] * 100;
				}
				this.niuxiongoption = {
					grid: {
						left: '42px',
						right: '32px',
						bottom: '56px',
						top: '10px'
						// containLabel: true
					},
					legend: {
						orient: 'vertical',
						show: true,
						top: 0,
						right: '50px',
						itemGap: 10
					},
					tooltip: {
						textStyle: {
							fontSize: '14px'
						},
						trigger: 'axis',
						axisPointer: {
							// 坐标轴指示器，坐标轴触发有效
							type: 'line' // 默认为直线，可选为：'line' | 'shadow'
						},
						formatter: (params) => {
							let str = '';
							if (params.length > 0 && params[0].seriesName !== '指数') {
								str += `季度: ${params[0].axisValue} <br />`;
							}
							for (let i = params.length - 1; i >= 0; i--) {
								if (params[i].seriesName !== '指数') {
									let dotHtml =
										'<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' +
										params[i].color +
										'"></span>';
									str +=
										dotHtml + `${params[i].seriesName}: ${!isNaN(params[i].value) ? Number(params[i].value).toFixed(2) + '%' : '--'}<br />`;
								}
							}
							return str;
						}
					},
					xAxis: {
						margin: 12,
						// 季度
						type: 'category',
						nameTextStyle: {
							fontSize: '14px',
							color: 'rgba(0,0,0,0.65)'
						},
						axisLabel: {
							show: true,
							textStyle: {
								fontSize: '14px',
								color: 'rgba(0,0,0,0.65)'
							}
						},
						axisLine: {
							lineStyle: {
								color: '#e9e9e9'
							}
						},
						axisTick: {
							show: false
						},
						data: xdata
					},
					yAxis: {
						margin: 16,
						// 持仓
						nameTextStyle: {
							fontSize: '14px',
							color: 'rgba(0,0,0,0.65)'
						},
						axisLabel: {
							show: true,
							textStyle: {
								fontSize: '14px',
								color: 'rgba(0,0,0,0.65)'
							},
							formatter: function (value) {
								return value + '%';
							}
						},
						axisLine: {
							lineStyle: {
								color: '#e9e9e9'
							}
						},
						axisTick: {
							show: false
						},
						// max: 100.0,
						type: 'value',
						splitLine: {
							show: true,
							lineStyle: {
								type: 'dashed',
								color: '#e9e9e9'
							}
						}
					},
					color: this.styleColor,
					series: [
						{
							type: 'bar',
							data: bondlike,
							name: '债性'
						},
						{
							type: 'bar',
							data: equitylike,
							name: '股性'
						},
						{
							type: 'bar',
							data: mixlike,
							name: '均衡'
						}
					]
				};
				this.niuxiongoption2 = {
					grid: {
						left: '42px',
						top: '34px',
						right: '32px',
						bottom: '10px'
						// containLabel: true
					},
					legend: {
						orient: 'vertical',
						show: true,
						top: 0,
						right: '50px',
						itemGap: 10
					},
					xAxis: {
						show: false,
						margin: 12,
						// 季度
						type: 'category',
						nameTextStyle: {
							fontSize: '14px',
							color: 'rgba(0,0,0,0.65)'
						},
						axisLabel: {
							show: true,
							textStyle: {
								fontSize: '14px',
								color: 'rgba(0,0,0,0.65)'
							}
						},
						axisLine: {
							lineStyle: {
								color: '#e9e9e9'
							}
						},
						axisTick: {
							show: false
						},
						data: xdata2
					},
					yAxis: {
						scale: true,
						margin: 16,
						// 持仓
						nameTextStyle: {
							fontSize: '14px',
							color: 'rgba(0,0,0,0.65)'
						},
						min: function (value) {
							return Math.floor(value.min);
						},
						max: function (value) {
							return Math.ceil(value.max);
						},
						axisLabel: {
							show: true,
							textStyle: {
								fontSize: '14px',
								color: 'rgba(0,0,0,0.65)'
							},
							formatter: function (value) {
								return value + '%';
							}
						},
						axisLine: {
							lineStyle: {
								color: '#e9e9e9'
							}
						},
						axisTick: {
							show: false
						},
						type: 'value',
						splitLine: {
							show: true,
							lineStyle: {
								type: 'dashed',
								color: '#e9e9e9'
							}
						}
					},
					color: this.styleColor,
					series: linS
				};
				console.log(this.niuxiongoption, this.niuxiongoption2);
			}
		},
		changeDate() {
			this.$emit('changeDate', this.value1);
			this.loading = true;
		},
		hideLoading() {
			this.loading = false;

			this.loadingtm = false;
		}
	},
	components: { 'v-chart': VCharts }
};
</script>
