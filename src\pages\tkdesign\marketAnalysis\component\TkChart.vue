<template>
    <div ref="chart" :style="{ width: '100%', height: '100%' }"></div>
</template>
  
<script>
// import echarts from 'echarts'
  export default {
    props: {
      options: {
        type: Object,
        required: true
      }
    },
    data(){
      return {
        chart:null
      }
    },
    mounted() {
      this.initData()
    },
    watch: {
      options: {
        handler() {
          // 当 options 发生改变时，更新图表
          this.initData()
        },
        deep: true
      }
    },
    methods:{
      initData(){
        if(!this.options){
          return
        }
        console.log('init')
        // 初始化图表
        this.chart = echarts.init(this.$refs.chart)
        // 渲染图表
        this.chart.setOption(this.options)
        this.chart.on('legendselectchanged', this.handleLegendChange);
      },
      handleLegendChange(e){
        this.$emit('legendSelectChanged',e)
      }
    },
  }
  </script>