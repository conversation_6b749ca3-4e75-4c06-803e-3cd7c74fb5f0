<template>
	<div class="basic_return_info mt-12">
		<div class="flex_between">
			<!-- 涨跌幅 -->
			<div
				v-if="info.flag == 1"
				:class="data.rate > 0 ? 'return_four_card px-12 py-8 mr-8 win_card_bg' : 'return_four_card px-12 py-8 mr-8 lose_card_bg'"
			>
				<div class="svg_bg">
					<return-svg-win v-if="data.rate > 0"></return-svg-win>
					<return-svg-lose v-else></return-svg-lose>
				</div>
				<div class="return_absolute">
					<div class="return_title">涨跌幅({{ data.date.slice(0, 5) }})</div>
					<div class="return_value" :style="data.rate > 0 ? 'color: #cf1322' : 'color: #389e0d'">{{ data.rate }}%</div>
				</div>
			</div>
			<!-- 近一年涨跌幅 -->
			<div
				:class="
					data.recent1yr_cumreturn > 0 ? 'return_four_card px-12 py-8 mr-8 win_card_bg' : 'return_four_card px-12 py-8 mr-8 lose_card_bg'
				"
			>
				<div class="svg_bg">
					<return-svg-win v-if="data.recent1yr_cumreturn > 0"></return-svg-win>
					<return-svg-lose v-else></return-svg-lose>
				</div>
				<div class="return_absolute">
					<div class="return_title">涨跌幅(1yr)</div>
					<div class="return_value" :style="data.recent1yr_cumreturn > 0 ? 'color: #cf1322' : 'color: #389e0d'">
						{{ data.recent1yr_cumreturn }}%
					</div>
				</div>
			</div>
			<!-- YTD涨跌幅 -->
			<div
				:class="
					data.theyear_cum_return > 0 ? 'return_four_card px-12 py-8 mr-8 win_card_bg' : 'return_four_card px-12 py-8 mr-8 lose_card_bg'
				"
			>
				<div class="svg_bg">
					<return-svg-win v-if="data.theyear_cum_return > 0"></return-svg-win>
					<return-svg-lose v-else></return-svg-lose>
				</div>
				<div class="return_absolute">
					<div class="return_title">YTD涨跌幅</div>
					<div class="return_value" :style="data.theyear_cum_return > 0 ? 'color: #cf1322' : 'color: #389e0d'">
						{{ data.theyear_cum_return }}%
					</div>
				</div>
			</div>
			<!-- 成立以来涨跌幅 -->
			<div :class="data.cum_return > 0 ? 'return_four_card px-12 py-8 mr-8 win_card_bg' : 'return_four_card px-12 py-8 mr-8 lose_card_bg'">
				<div class="svg_bg">
					<return-svg-win v-if="data.cum_return > 0"></return-svg-win>
					<return-svg-lose v-else></return-svg-lose>
				</div>
				<div class="return_absolute">
					<div class="return_title">成立以来涨跌幅</div>
					<div class="return_value" :style="data.cum_return > 0 ? 'color: #cf1322' : 'color: #389e0d'">{{ data.cum_return }}%</div>
				</div>
			</div>
		</div>
		<div class="flex_between mt-8">
			<!-- 成立以来超业绩基准 -->
			<div
				v-if="info.flag == 1"
				:class="
					data.since_excess_index > 0 ? 'return_three_card px-12 py-8 mr-8 win_card_bg' : 'return_three_card px-12 py-8 mr-8 lose_card_bg'
				"
			>
				<div class="svg_bg">
					<return-svg-win v-if="data.since_excess_index > 0"></return-svg-win>
					<return-svg-lose v-else></return-svg-lose>
				</div>
				<div class="return_absolute">
					<div class="return_title">成立以来超业绩基准</div>
					<div class="return_value" :style="data.since_excess_index > 0 ? 'color: #cf1322' : 'color: #389e0d'">
						{{ data.since_excess_index }}%
					</div>
				</div>
			</div>
			<!-- 成立以来年化收益 -->
			<div
				:class="
					data.since_ave_return > 0 ? 'return_three_card px-12 py-8 mr-8 win_card_bg' : 'return_three_card px-12 py-8 mr-8 lose_card_bg'
				"
			>
				<div class="svg_bg">
					<return-svg-win v-if="data.since_ave_return > 0"></return-svg-win>
					<return-svg-lose v-else></return-svg-lose>
				</div>
				<div class="return_absolute">
					<div class="return_title">成立以来年化收益</div>
					<div class="return_value" :style="data.since_ave_return > 0 ? 'color: #cf1322' : 'color: #389e0d'">
						{{ data.since_ave_return }}%
					</div>
				</div>
			</div>
			<!-- 成立以来年化波动 -->
			<div class="return_three_card px-12 py-8" style="background: #ecf5ff">
				<div class="return_absolute">
					<div class="return_title">成立以来年化波动</div>
					<div class="return_value">{{ data.since_volatility }}%</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import returnSvgWin from './components/returnSvgWin.vue';
import returnSvgLose from './components/returnSvgLose.vue';
import { getReturnInfo } from '@/api/pages/Analysis.js';
export default {
	components: { returnSvgWin, returnSvgLose },
	data() {
		return {
			info: {},
			data: {}
		};
	},
	methods: {
		async getData(info) {
			this.info = info;
			let data = await getReturnInfo({
				code: this.info.code,
				flag: this.info.flag,
				type: this.info.type
			});
			if (data?.mtycode == 200) {
				this.data = {
					// 年化收益
					ave_return: this.fix2p(data.data?.ave_return),
					// 计算截止时间
					date: data?.data?.date.slice(5),
					// 超业绩基准收益
					excess_index: this.fix2p(data.data?.excess_index),
					// 上一交易日涨跌幅
					rate: this.fix2p(data.data?.rate),
					// 近一年累计收益
					recent1yr_cumreturn: this.fix2p(data.data?.recent1yr_cumreturn),
					// 近一年波动率
					recent1yr_vol: this.fix2p(data.data?.recent1yr_vol),
					// TYD涨跌幅
					theyear_cum_return: this.fix2p(data.data?.theyear_cum_return),
					// 成立以来涨跌幅
					cum_return: this.fix2p(data.data?.cum_return),
					// 成立以来年化波动
					since_volatility: this.fix2p(data.data?.since_volatility),
					// 成立以来年化收益
					since_ave_return: this.fix2p(data.data?.since_ave_return),
					// 成立以来超业绩基准
					since_excess_index: this.fix2p(data.data?.since_excess_index)
				};
			}
		},
		// 格式化收益数据
		fix2p(val) {
			return !isNaN(val * 1) ? (val * 100).toFixed(2) : '--';
		},
		createPrintWord() {
			let list = [];
			if (this.info.flag == 1) {
				list = [
					{ label: `涨跌幅(${this.data.date.slice(0, 5)})`, value: 'rate' },
					{ label: '涨跌幅(1yr)', value: 'recent1yr_cumreturn' },
					{ label: 'YTD涨跌幅', value: 'YTD_cumreturn' },
					{ label: '成立以来涨跌幅', value: 'cumreturn' },
					{ label: '成立以来超业绩基准', value: 'excess_return' },
					{ label: '成立以来年化收益', value: 'ave_return' },
					{ label: '成立以来年化波动', value: 'volatility' }
				];
			} else if (this.info.flag == 2) {
				list = [
					{ label: '涨跌幅(1yr)', value: 'recent1yr_cumreturn' },
					{ label: 'YTD涨跌幅', value: 'YTD_cumreturn' },
					{ label: '成立以来涨跌幅', value: 'cumreturn' },
					{ label: '成立以来年化收益', value: 'ave_return' },
					{ label: '成立以来年化波动', value: 'volatility' }
				];
			}
			return [...this.$exportWord.exportDoubleColumnTable(list, this.data)];
		}
	}
};
</script>

<style lang="scss" scoped>
.basic_return_info {
	.return_four_card {
		position: relative;
		flex: 1;
		min-width: 146.5px;
		height: 66px;
		border-radius: 2px;
	}
	.return_three_card {
		position: relative;
		flex: 1;
		min-width: 198px;
		height: 66px;
		border-radius: 2px;
		background: red;
	}
	.win_card_bg {
		background: linear-gradient(270deg, #ffece7 0%, rgba(255, 236, 231, 0.25) 100%);
	}
	.lose_card_bg {
		background: linear-gradient(270deg, #e2f7e7 0%, rgba(226, 247, 231, 0.25) 100%);
	}
	.svg_bg {
		position: absolute;
		right: 8px;
	}
	.return_absolute {
		position: absolute;
		.return_title {
			color: rgba(0, 0, 0, 0.45);
			font-family: PingFang SC;
			font-size: 14px;
			font-style: normal;
			font-weight: 400;
		}
		.return_value {
			font-family: Helvetica Neue;
			font-size: 20px;
			font-style: normal;
			font-weight: 500;
			line-height: 28px; /* 140% */
		}
	}
}
</style>
