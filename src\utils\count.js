/**
 * 获取百分比（小数点后保留两位）
 */
export function handleData(row, need) {
    if (row === 'nan' || row === undefined || !row) {
        return '--'
    }
    return (Number(row) * 100).toFixed(2) + (need ? '' : '%')
}

export function handleData1(row, need) {
  if (row === 'nan' || row === undefined || !row) {
      return '--'
  }
  return (Number(row) * 1).toFixed(2) 
}

/**
 * 将单位改为亿（保留两位小数）
 */
export function handleDataBillion(row, need) {
    if (row === 'nan' || row === undefined || !row) {
        return '--'
    }
    return (Number(row) / 100000000).toFixed(8) + (need ? '' : '亿')
}

/**
 * 单位改为万 (保留两位小数)
 */
export function handleDataTenThousand(row) {
    if (row === 'nan' || row === undefined || !row) {
        return '--'
    }
    return (Number(row) / 10000).toFixed(2) + '万'
}
/**
 * 单位改为万 (保留两位小数)
 */
export function handleDataTenThousand2(row) {
  if (row === 'nan' || row === undefined || !row) {
      return '--'
  }
  return (Number(row) / 10000).toFixed(2) 
}
/**
 * 基金类型分类
 */
export function typeMatching(row) {
    switch (row) {
        case 'equity':
            return '主动权益'
            break;
        case 'equityhk':
            return '港股基金'
            break;
        case 'equityindex':
            return '被动权益指数'
            break;
        case 'equitywithhk':
            return '含港股主动权益'
            break;
        case 'equityenhance':
            return '指数增强'
            break;
        case 'equityhk-index':
            return '港股指数'
            break;
        case 'bond':
            return '固收+'
            break;
        case 'cbond':
            return '可转债'
            break;
        case 'purebond':
            return '纯债'
            break;
        case 'bill':
            return '中短债'
            break;
        case 'money':
            return ' 货币'
            break;
        case 'obond':
            return '其他债券'
            break;
        case 'bondindex':
            return '二级债基指数'
            break;
        case 'fof':
            return 'FOF'
            break;
        case 'fof:target-date':
            return '目标日期型fof'
            break;
        case 'fof:target-risk':
            return '目标风险型fof'
            break;
        case 'QDII':
            return 'QDII'
            break;
        default:
            return '其他'
            break;
    }
}

/**
 * 获取满足时间区间内的数据
 */
export function matchedData(dataArr, timeArr, moment) {
    let starTime = timeArr[0]
    let endTime = timeArr[1]
    const arr = []
    dataArr.forEach((v) => {
        if (moment(starTime).diff(moment(v.data.date), 'days') <= 0 && moment(endTime).diff(moment(v.data.date), 'days') >= 0)
            arr.push(v)
    })
    return arr
}

/**
 * 颜色列表
 */
export const colorList = [
    '#4096ff',
    '#4096ff',
    '#7388A9',
    '#E85D2D',
    '#9A89FF',
    '#6C96F2',
    '#FD6865',
    'rgba(253, 156, 255, 1)',
    '#83D6AE',
    'rgba(174, 201, 254, 1)',
    '#88C9E9',
    'rgba(169, 244, 208, 1)',
    '#6F80DD',
    'rgba(154, 137, 255, 1)',
    '#FD9CFF',
    'rgba(219, 174, 255, 1)',
    '#FED0EE',
    'rgba(159, 212, 253, 1)',
    '#ED589D',
    '#FEAEAE',
    'rgba(208, 232, 255, 1)',
    '#FDD09F',
    'rgba(251, 227, 142, 1)',
    '#FBE38E',
    '#A9F4D0',
    'rgba(253, 208, 159, 1)',
    '#D0E8FF',
    '#9FD4FD',
    'rgba(254, 174, 174, 1)',
    '#AEC9FE',
    '#DBAEFF',
    'rgba(254, 208, 238, 1)',
    '#FA541C',
]
