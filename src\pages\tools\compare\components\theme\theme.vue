<!--  -->
<template>
  <div class="themebox">
    <div style="display: flex; align-items: center">
      <div class="TitltCompare">主题与收益</div>
    </div>
    <div style="text-align: left; font-size: 14px; margin-top: 16px">
      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;图中三角形代表跑赢主题指数，圆形代表跑输主题指数，图形大小代表相对权重，颜色越贴近红色表示赚的越多，颜色越贴近绿色表示亏得越多
    </div>
    <div style="height: 16px"></div>
    <div v-show="id.split(',').length > 0"
         style="width: 100%; page-break-inside: avoid">
      <v-chart ref="theme0"
               v-loading="empty7"
               autoresize
               element-loading-text="暂无数据"
               element-loading-spinner="el-icon-document-delete"
               element-loading-background="rgba(239, 239, 239, 0.5)"
               style="width: 100%; height: 400px"
               :options="option0" />
      <div style="height: 20px"></div>
    </div>
    <div v-show="id.split(',').length > 1"
         style="width: 100%; page-break-inside: avoid">
      <v-chart ref="theme1"
               v-loading="empty7"
               autoresize
               element-loading-text="暂无数据"
               element-loading-spinner="el-icon-document-delete"
               element-loading-background="rgba(239, 239, 239, 0.5)"
               style="width: 100%; height: 400px"
               :options="option1" />
      <div style="height: 20px"></div>
    </div>
    <div v-show="id.split(',').length > 2"
         style="width: 100%; page-break-inside: avoid">
      <v-chart ref="theme2"
               v-loading="empty7"
               autoresize
               element-loading-text="暂无数据"
               element-loading-spinner="el-icon-document-delete"
               element-loading-background="rgba(239, 239, 239, 0.5)"
               style="width: 100%; height: 400px"
               :options="option2" />
      <div style="height: 20px"></div>
    </div>
    <div v-show="id.split(',').length > 3"
         style="width: 100%; page-break-inside: avoid">
      <v-chart ref="theme3"
               v-loading="empty7"
               autoresize
               element-loading-text="暂无数据"
               element-loading-spinner="el-icon-document-delete"
               element-loading-background="rgba(239, 239, 239, 0.5)"
               style="width: 100%; height: 400px"
               :options="option3" />
      <div style="height: 20px"></div>
    </div>
    <div v-show="id.split(',').length > 4"
         style="width: 100%; page-break-inside: avoid">
      <v-chart v-loading="empty7"
               autoresize
               element-loading-text="暂无数据"
               element-loading-spinner="el-icon-document-delete"
               element-loading-background="rgba(239, 239, 239, 0.5)"
               style="width: 100%; height: 400px"
               :options="option4" />
      <div style="height: 20px"></div>
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { equitytheme } from '@/api/pages/tools/compare.js';
import VCharts from 'vue-echarts';
export default {
  //import引入的组件需要注入到对象中才能使用
  components: { 'v-chart': VCharts },
  props: {
    comparetype: {
      type: String,
      default: 'manager' //fund
    },
    id: {
      type: String,
      default: '30189741,30441407'
    },
    type: {
      type: String,
      default: 'equity'
    },
    name: {
      type: String,
      default: '萧楠,胡昕炜'
    }
  },
  data () {
    //这里存放数据
    return {
      empty7: false,
      option0: {},
      option1: {},
      option2: {},
      option3: {},
      option4: {}
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    async gettheme (code, i) {
      let that = this;
      let data = await equitytheme({
        manager_code: code
      });
      if (data) {
        // //console.log(data)
        // //console.log(data)
        that.drawtrad(data, i);
      }
      //axios.get(this.$StyleUrl+"/fundmsg/code="+this.id+'&manager_code='+this.manager)
      // axios
      // 	.get(this.$StyleUrl + '/equitytheme/?manager_code=' + this.managercode)
      // 	.then(function (res) {
      // 		that.loadingtheme = false;
      // 		that.drawtrad(res.data.industry_theme); //持仓风格
      // 	})
      // 	.catch(function (error) {
      // 		//console.log(error);
      // 		that.loadingtheme = false;
      // 		//that.$message('数据缺失')
      // 	});
    },
    getdata () {
      if (this.comparetype == 'manager') {
        this.getmanagerdata();
      } else {
        this.gefunddata();
      }
    },
    async getmanagerdata () {
      for (let i = 0; i < this.id.split(',').length; i++) {
        this.gettheme(this.id.split(',')[i], i);
      }
    },
    async gefunddata () { },
    drawtrad (traddata, i) {
      console.log(traddata);
      if (
        traddata.result == [] ||
        traddata.result == '' ||
        traddata.result == {} ||
        traddata.result == '数据缺失' ||
        traddata.result.length <= 0
      ) {
        // this.empty7 = true;
      } else {
        //console.log('三角方块')
        //console.log(traddata)
        let whereend = null;
        let data = [];
        let date = [];
        let industries = traddata.industry_name;
        for (let i = 0; i < traddata.result.length; i++) {
          let temp = industries.indexOf(traddata.result[i].industry_name);
          data.push([
            traddata.result[i].yearqtr,
            temp,
            traddata.result[i].industry_return,
            traddata.result[i].weight,
            traddata.result[i].excess == 'true' ? 'True' : 'False'
          ]);
          if (i >= 1) {
            if (
              traddata.result[i].yearqtr ==
              traddata.result[i - 1].yearqtr
            )
              continue;
            else date.push(traddata.result[i].yearqtr);
          } else {
            date.push(traddata.result[i].yearqtr);
          }
        }
        date = date.sort();
        if (date.length > 25) {
          whereend = Math.trunc(100 - (25 / date.length) * 100);
        } else if (date.length == 2) {
          whereend = 50;
        } else {
          whereend = 0;
        }
        let maxp = 0;
        for (let c = 0; c < traddata.result.length; c++) {
          if (maxp < traddata.result[c].weight) {
            maxp = traddata.result[c].weight;
          }
        }
        this['option' + i] = {
          title: {
            text: this.name.split(',')[i] + '主题特征'
          },
          xAxis: {
            nameTextStyle: {
              fontSize: 14
            },
            axisLabel: {
              show: true,
              textStyle: {
                fontSize: 14
              }
            },
            type: 'category',
            boundaryGap: false,
            splitLine: {
              show: true,
              lineStyle: {
                color: 'white',
                type: 'solid',
                width: 1
              }
            },
            data: traddata.timelist,
            interval: 0.25,
            length: traddata.timelist.length
          },
          dataZoom: [
            {
              // 这个dataZoom组件，默认控制x轴。
              type: 'slider', // 这个 dataZoom 组件是 slider 型 dataZoom 组件
              start: 0, // 左边在 10% 的位置。
              end: 100, // 右边在 60% 的位置。

              show: true
            },
            {
              type: 'slider', // 这个 dataZoom 组件是 slider 型 dataZoom 组件
              start: 50, // 左边在 10% 的位置。
              end: 100, // 右边在 60% 的位置。

              show: true
            }
          ],
          yAxis: {
            nameTextStyle: {
              fontSize: 14
            },
            axisLabel: {
              show: true,
              textStyle: {
                fontSize: 14
              }
            },
            type: 'value',
            axisLine: {
              show: false
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: 'white',
                type: 'solid',
                width: 1
              }
            },

            splitArea: {
              show: true,
              areaStyle: {
                color: '#f5f7fa'
              }
            },
            min: 0,
            max: traddata.industry_name.length,
            interval: 1,
            axisLabel: {
              formatter: function (value, index) {
                return industries[value];
              }
            }
          },
          tooltip: {
            textStyle: {
              fontSize: 14
            },
            trigger: 'item',
            axisPointer: {
              type: 'cross'
            },
            position: function (point, params, dom, rect, size) {
              return [0, point[1]];
            },
            formatter (params) {
              return (
                '日期：' +
                params.data[0] +
                '季度，行业：' +
                industries[params.data[1]] +
                '，权重：' +
                params.data[3].toFixed(2) +
                '%，主题收益率：' +
                parseInt(params.data[2] * 100000) / 100000
              );
            }
          },
          grid: {
            left: '0',
            right: '10px',
            bottom: '50px',
            top: '40px',
            containLabel: true,
            backgroundColor: '#f9f9f9'
          },
          visualMap: [
            {
              show: false,
              type: 'piecewise',
              right: '0',
              bottom: '20px',
              dimension: 4,
              splitNumber: 2,
              precision: 1,
              itemWidth: 10,
              itemHeight: 10,
              textGap: 5,
              textStyle: {
                color: 'black'
              },
              categories: ['True', 'False'],
              inRange: {
                symbol: ['triangle', 'circle']
              },
              controller: {
                inRange: {
                  color: ['black', 'black'],
                  symbol: ['triangle', 'circle']
                }
              }
            },

            {
              show: false,
              right: '0px',
              top: '150px',
              dimension: 3,
              min: 0,
              max: maxp,
              itemWidth: 30,
              itemHeight: 120,
              precision: 0,
              text: ['配置权重%'],
              textGap: 5,
              textStyle: {
                color: 'black',
                fontSize: 10
              },
              inRange: {
                symbolSize: [0, 20]
              },
              controller: {
                inRange: {
                  color: ['black']
                }
              }
            },
            {
              show: false,
              right: '5px',
              top: '5%',
              dimension: 2,
              min: -1.0,
              max: 1.0,
              itemHeight: 100,
              precision: 1,
              text: ['1', '-1.0'],
              textGap: 0,
              textStyle: {
                color: 'black',
                fontSize: 10
              },
              inRange: {
                color: ['#7462b6', 'white', '#7a0811']
              },
              outOfRange: {
                color: ['rgba(255,255,255,.2)']
              },
              controller: {
                inRange: {
                  color: ['#7462b6', 'white', '#7a0811']
                }
              }
            }
          ],
          series: [
            {
              name: '主题特征',
              type: 'scatter',
              //itemStyle: itemStyle,
              data: data,
              symbolSize: 5
              // itemStyle:{
              //   opacity:1
              // }
            }
          ]
        };
      }
    },
    async createPrintWord () {
      let data = [];
      await this.$nextTick(() => {
        for (let index = 0; index < this.id.split(',').length; index++) {
          let height = this.$refs['theme' + index]?.$el.clientHeight;
          let width = this.$refs['theme' + index]?.$el.clientWidth;
          let chart = this.$refs['theme' + index].getDataURL({
            type: 'png',
            pixelRatio: 2,
            backgroundColor: '#fff'
          });
          data.push(...this.$exportWord.exportChart(chart, { width, height }));
        }
      });
      return [
        ...this.$exportWord.exportTitle('主题与收益'),
        ...this.$exportWord.exportDescripe(
          '图中三角形代表跑赢主题指数，圆形代表跑输主题指数，图形大小代表相对权重，颜色越贴近红色表示赚的越多，颜色越贴近绿色表示亏得越多'
        ),
        ...data
      ];
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () { },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
</style>
