<template>
  <div class="plate-wrapper fund-performance-board-wrapper"
       v-loading="loading">
    <VerticalLineHeader title="主动权益基金整体拟合业绩图表"
                        showDownloadBtn
                        @downloadClick="exportExcel">
      <template slot="right">
        <el-form ref="form"
                 :model="form"
                 label-width="80px"
                 class="title-right-form">
          <el-form-item size="small"
                        label="截止日期:">
            <el-date-picker value-format="yyyy-MM-dd"
                            type="date"
                            placeholder="选择日期"
                            v-model="form.deadline"
                            @change="handleSelectHeader"
                            style="width: 100%"></el-date-picker>
          </el-form-item>
          <el-radio-group class="lq-radio-group radio-group-wrapper"
                          v-model="form.dateFlag"
                          @change="handleSelectHeader"
                          size="small">
            <el-radio-button v-for="radioItem in DateTypeOption"
                             :key="radioItem.value"
                             :label="radioItem.value">{{
							radioItem.label
						}}</el-radio-button>
          </el-radio-group>
          <el-radio-group class="lq-radio-group radio-group-wrapper"
                          v-model="form.weight"
                          @change="handleSelectHeader"
                          size="small">
            <el-radio-button v-for="radioItem in CulTypeOption"
                             :key="radioItem.value"
                             :label="radioItem.value">{{
							radioItem.label
						}}</el-radio-button>
          </el-radio-group>
        </el-form>
      </template>
    </VerticalLineHeader>
    <div class="select-form-wrapper">
      <el-radio-group @change="handleSelectHeader"
                      class="lq-radio-group radio-group-wrapper"
                      v-model="form.type"
                      size="small">
        <el-radio-button v-for="optionItem in DisplayDimensionOption"
                         :key="optionItem.value"
                         :label="optionItem.value">{{
					optionItem.name
				}}</el-radio-button>
      </el-radio-group>
    </div>
    <el-table class="content-table-wrapper"
              style="width: 100%"
              :data="tableDataNow"
              :stripe="true"
              :border="true"
              :row-style="rowStyle"
              :cell-style="cellStyle"
              @row-click="submitGetChart"
              @sort-change="handeleSortChange">
      <!-- <el-table-column prop="name" label="名称" sortable align="gotoleft" width="180"> </el-table-column> -->
      <template v-for="item in tableHeader">
        <el-table-column :key="item.prop"
                         v-if="item.prop === 'customTime'"
                         prop="customTime"
                         label="区间收益"
                         width="120"
                         sortable="custom"
                         align="gotoleft">
          <template slot="header">
            区间收益
            <DatePickerBtn trigger="click"
                           @change="handleDateChange"
                           @click.native.stop="() => {}"></DatePickerBtn>
          </template>
          <template slot-scope="{ row }">
            <div>{{ item.format ? item.format(row[item.prop]) : row[item.prop] }}</div>
          </template>
        </el-table-column>
        <el-table-column v-else
                         :prop="item.prop"
                         min-width="120"
                         :label="item.label"
                         :key="item.prop"
                         :sortable="item.sortable !== false ? 'custom' : false"
                         align="gotoleft">
          <template slot-scope="{ row }">
            <div>{{ item.format ? item.format(row[item.prop]) : row[item.prop] }}</div>
          </template>
        </el-table-column>
      </template>
    </el-table>
    <el-pagination style="display: flex; justify-content: right; padding-top: 16px; padding-bottom: 16px"
                   class="pagination-footer-wrapper"
                   @size-change="handleSizeChange"
                   @current-change="handleCurrentChange"
                   :current-page.sync="pageInfo.currentPage"
                   :page-sizes="[100, 200, 300, 400]"
                   :page-size="pageInfo.pageSize"
                   layout="total, sizes, prev, pager, next, jumper"
                   :total="pageInfo.total">
    </el-pagination>
    <div v-loading="chartLoading">
      <div style="display: flex; justify-content: end">
        <el-button style="margin-left: 16px; display: flex; width: 32px; height: 32px; align-items: center; justify-content: center"
                   @click="handleChartDownload">
          <svg xmlns="http://www.w3.org/2000/svg"
               width="16"
               height="16"
               viewBox="0 0 16 16"
               fill="none">
            <path d="M7.88736 10.6575C7.90072 10.6745 7.9178 10.6883 7.93729 10.6978C7.95678 10.7073 7.97818 10.7123 7.99986 10.7123C8.02154 10.7123 8.04294 10.7073 8.06243 10.6978C8.08192 10.6883 8.099 10.6745 8.11236 10.6575L10.1124 8.1271C10.1856 8.03424 10.1195 7.89674 9.99986 7.89674H8.67665V1.85389C8.67665 1.77531 8.61236 1.71103 8.53379 1.71103H7.46236C7.38379 1.71103 7.3195 1.77531 7.3195 1.85389V7.89496H5.99986C5.88022 7.89496 5.81415 8.03246 5.88736 8.12532L7.88736 10.6575ZM14.5356 10.0325H13.4641C13.3856 10.0325 13.3213 10.0967 13.3213 10.1753V12.9253H2.67843V10.1753C2.67843 10.0967 2.61415 10.0325 2.53557 10.0325H1.46415C1.38557 10.0325 1.32129 10.0967 1.32129 10.1753V13.711C1.32129 14.0271 1.57665 14.2825 1.89272 14.2825H14.107C14.4231 14.2825 14.6784 14.0271 14.6784 13.711V10.1753C14.6784 10.0967 14.6141 10.0325 14.5356 10.0325Z"
                  fill="black"
                  fill-opacity="0.45" />
          </svg>
        </el-button>
      </div>
      <FundPerformanceBoard ref="fund-performance-chart"
                            @resolveFather="getIndexCode"></FundPerformanceBoard>
    </div>
  </div>
</template>
<script>
import { filter_json_to_excel } from '@/utils/exportExcel.js';
import VerticalLineHeader from './VerticalLineHeader.vue';
import FundPerformanceBoard from './chart/FundPerformanceBoard.vue';
import {
  getSubFitperformance,
  getFitPerformance,
  PerformanceType,
  PaginationFlag,
  getIndexCode
} from '@/api/pages/tkAnalysis/captial-market.js';
import DatePickerBtn from './DatePickerBtn.vue';
import { DisplayDimensionOption } from '../../components/option.data.js';
import stringTool from '../../components/string.tool';
export default {
  name: 'TheFundPerformanceBoardPlate',
  components: {
    VerticalLineHeader,
    FundPerformanceBoard,
    DatePickerBtn
  },
  data () {
    return {
      DisplayDimensionOption: DisplayDimensionOption,
      tableHeader1: [
        {
          prop: 'yearToDate',
          label: '年初至今',
          format: stringTool.fix2px
        },
        {
          prop: 'lastWeek',
          label: '近一周',
          format: stringTool.fix2px
        },
        {
          prop: 'lastMounth',
          label: '近一月',
          format: stringTool.fix2px
        },
        {
          prop: 'lastSeason',
          label: '近一季',
          format: stringTool.fix2px
        },
        {
          prop: 'lastHalfYears',
          label: '近半年',
          format: stringTool.fix2px
        },
        {
          prop: 'lastYear',
          label: '近一年',
          format: stringTool.fix2px
        },
        {
          prop: 'lastThreeYear',
          label: '近三年',
          format: stringTool.fix2px
        },
        {
          prop: 'lastFiveYear',
          label: '近五年',
          format: stringTool.fix2px
        },
        {
          prop: 'customTime',
          label: '自定义区间',
          format: stringTool.fix2px
        }
      ],
      tableHeader3: [],
      form: {
        deadline: '',
        dateFlag: '0',
        weight: 'scaleWeighted',
        type: 'industry'
      },
      chartForm: {
        subType: '',
        indexCode: ''
      },
      IndexStyleOption: [],
      tableData: [],
      chartData: [],
      pageInfo: {
        pageSize: 100,
        currentPage: 0,
        total: 0
      },
      defaultRadioValue: 'ttm',
      DateTypeOption: [
        { label: '近期业绩', value: '0' },
        { label: '自然年份业绩', value: '1' }
      ],
      CulTypeOption: [
        { label: '规模加权', value: 'scaleWeighted' },
        { label: '等权', value: 'equivalency' }
      ],
      // configList:[
      //     {type:'select',value:'',label:'windType',text:'类型',option:[]},
      //     {type:'select',value:'',label:'industry',text:'行业',option:[]},
      //     {type:'select',value:'',label:'theme',text:'主题',option:[]},
      //     {type:'select',value:'',label:'optionalPool',text:'自选池',option:[]},
      //     {type:'select',value:'',label:'taikang',text:'泰康分类',option:[]},
      //     {type:'select',value:'',label:'style',text:'风格',option:[]}],
      chartOption: {},
      IndexTypeOption: [],
      industryrequestflag: true,
      loading: false,
      options: [],
      chartLoading: false,
      tableDataNow: []
    };
  },
  computed: {
    tableHeader () {
      let result = [];
      let commmon = {
        prop: 'name',
        label: '名称',
        sortable: false
      };
      result.push(commmon);
      //为自然年份业绩
      if (this.form.dateFlag === '1') {
        result.push(...this.tableHeader3);
        return result;
      }
      result.push(...this.tableHeader1);
      return result;
    }
  },
  async mounted () {
    this.form.deadline = this.moment().subtract(1, 'day').format('YYYY-MM-DD');
    await this.getIndexOptions();
    if (this.localStorage.getItem('TheFundPerformanceBoardPlate')?.form) {
      let key_list = ['form', 'chartForm'];
      for (let key of key_list) {
        this[key] = this.localStorage.getItem('TheFundPerformanceBoardPlate')?.[key];
      }
    }
    this.getData();
  },
  methods: {
    handeleSortChange ({ column, prop, order }) {
      this.tableData.sort((item1, item2) => {
        const a1 = item1[prop] || 0;
        const a2 = item2[prop] || 0;
        let orderVal = order === 'ascending' ? -(a1 - a2) : a1 - a2;
        return orderVal;
      });
      this.dulData();
    },
    handleChartDownload () {
      this.$refs['fund-performance-chart']?.exportExcel();
    },
    // 导出excel
    exportExcel () {
      let list = this.tableHeader.map((item) => {
        return {
          ...item,
          value: item.prop,
          format: ''
        };
      });
      filter_json_to_excel(list, this.tableData, '主动权益基金整体拟合业绩图表');
    },
    // handleSortChange(value){
    // //接口调用
    //     let { prop,order }=value
    //     this.form.sort_order=order
    //     this.form.sort_prop=prop
    //     this.getData()
    // },

    handleSelectHeader () {
      this.pageInfo.currentPage = 1;
      //接口调用
      this.getData();
    },
    // // 获取列表数据
    // async getData () {
    //     let params={
    //         marketType:PerformanceType.FundGlobal.value,
    //         PaginationFlag:PaginationFlag.Y.value,
    //         pageSize: this.pageInfo.pageSize,
    //         currentPage: this.pageInfo.currentPage,
    //         ...this.form
    //     }
    //     let req = await getPerformanceListByType(params)
    //     let { data, mtycode, mtymessage } = req||{}
    //     if (mtycode == 200) {
    //         this.pageInfo.total = data?.total || 0
    //         this.pageInfo.currentPage = data?.currentPage || 1
    //         this.pageInfo.pageSize = data?.pageSize || 20
    //         this.tableData = data?.dataList
    //     }
    //     else {
    //         this.tableData = []
    //         this.pageInfo={
    //             total:0,
    //             currentPage:1,
    //             pageSize:20
    //         }
    //     }
    //     this.initChart(this.tableData)

    // },
    getData () {
      this.getListData();
    },
    // 获取列表数据
    async getListData () {
      this.loading = true;
      let params = {
        marketType: PerformanceType.FundGlobal.value,
        ...this.form
      };
      let req = await getFitPerformance(params);
      let { data, code, message } = req || {};
      if (code == 200) {
        this.tableData = data || [];
        let { naturalList = [] } = this.tableData[0] || {};
        this.pageInfo.total = this.tableData.length;

        this.getFundOptions(this.tableData);
        if (naturalList) {
          this.tableHeader3 = naturalList.map((item) => {
            return {
              prop: item.naturalDate,
              label: item.naturalDate,
              format: stringTool.fix2px
            };
          })?.sort((a, b) => {
            if (a?.prop < b?.prop) return 1
            else return -1
          });
          this.tableData = this.tableData.map((item) => {
            let obj = { ...item };
            item.naturalList.map((v) => {
              obj[v.naturalDate] = v.meter;
            });
            return obj;
          });
        }
      } else {
        this.$message.warning(message);
        this.tableDataNow = [];
        this.tableData = [];
      }
      this.dulData();
      console.log(this.chartForm);
      if (!this.chartForm.subType) {
        this.chartForm.subType = this.tableDataNow[0]?.code;
      }
      this.getChartData();
      this.loading = false;
    },
    submitGetChart (val) {
      this.$set(this.chartForm, 'subType', val.code);
      // this.$refs.chartForm.validate((valid) => {
      // 	if (valid) {
      this.getChartData();
      // 	}
      // });
    },
    // 获取指数code
    getIndexCode (code) {
      this.chartForm.indexCode = code;
      this.getChartData();
    },
    // 获取列表数据
    async getChartData () {
      this.chartLoading = true;
      this.$refs['indexType']?.getCheckedNodes();
      let params = {
        marketType: PerformanceType.FundGlobal.value,
        ...this.form,
        indexCode: this.chartForm['indexCode'][1],
        subType: this.chartForm['subType'],
        deadline: this.form.deadline
      };
      this.localStorage.setItem('TheFundPerformanceBoardPlate', { form: this.form, chartForm: this.chartForm });
      let req = await getSubFitperformance(params);
      let { data, code, message } = req || {};
      if (code == 200) {
        this.chartData = data?.dateList || [];
      } else {
        this.chartData = [];
      }
      this.initChart(this.chartData);
      this.chartLoading = false;
    },
    dulData () {
      let { currentPage, pageSize } = this.pageInfo;
      this.tableDataNow = this.tableData.slice((currentPage - 1) * pageSize, currentPage * pageSize);
    },
    initChart (data) {
      this.$nextTick(() => {
        let indexName = this.IndexTypeOption.find((v) => v.value == this.chartForm.indexCode[0])?.children?.find(
          (v) => v.code == this.chartForm.indexCode[1]
        )?.name;
        this.$refs['fund-performance-chart'].getData(data, {
          contrastReturnName: indexName,
          IndexTypeOption: this.IndexTypeOption,
          indexCode: this.chartForm.indexCode
        });
      });
    },
    handleSizeChange (value) {
      this.pageInfo.currentPage = 1;
      this.pageInfo.pageSize = value;
      this.dulData();
    },
    handleCurrentChange (value) {
      this.pageInfo.currentPage = value;
      this.dulData();
    },
    handleDateChange (value) {
      this.form['startDate'] = value[0];
      this.form['endDate'] = value[1];
      this.getData();
    },
    getFundOptions (data) {
      this.options = data.map((item) => {
        return {
          label: item.name,
          value: item.name
        };
      });
    },
    async getIndexOptions () {
      let params = {};
      let reqData = await getIndexCode(params);
      let { data = [], code, message } = reqData || {};
      if (code == 200) {
        this.IndexTypeOption = data.map((item) => {
          let children = item?.dataList?.map((childrenItem) => {
            return {
              ...childrenItem,
              label: childrenItem.name,
              value: childrenItem.code
            };
          });
          return { ...item, label: item.indexName, value: item.indexType, children: children };
        });
      } else {
        this.$message.warning(message);
      }
      if (!this.chartForm.indexCode) {
        this.chartForm.indexCode = [this.IndexTypeOption[0]?.value, this.IndexTypeOption[0]?.children?.[0]?.code];
      }
    },
    // 表格选中状态背景色
    rowStyle ({ row }) {
      if (row.code == this.chartForm.subType) {
        return { backgroundColor: '#4096ff0F !important' };
      } else {
        return { cursor: 'pointer' };
      }
    },
    cellStyle ({ row }) {
      if (row.code == this.chartForm.subType) {
        return { backgroundColor: '#4096ff0F !important' };
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.fund-performance-board-wrapper {
	padding-bottom: 20px;
	.select-form-wrapper {
		margin-bottom: 16px;
	}
	// .content-table-wrapper {
	//     margin-bottom: 32px;
	// }
}
</style>
