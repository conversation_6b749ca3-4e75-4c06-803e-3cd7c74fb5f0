<template>
	<div class="marketAnalysis box_Board">
		<div class="header_box"><span class="header_unactive">市场分析&nbsp;/&nbsp;</span>资本市场行情<span></span></div>
		<TheIndicatorBoard></TheIndicatorBoard>
		<TheValuationPercentilePlate></TheValuationPercentilePlate>
		<TheFinancialIndicatorPlate></TheFinancialIndicatorPlate>
		<TheCompositeIndicesOperation></TheCompositeIndicesOperation>
	</div>
</template>
<script>
import '@/pages/assets/css/page-container.scss';
import TheIndicatorBoard from './component/TheIndicatorBoardPlate.vue';
import TheValuationPercentilePlate from './component/TheValuationPercentilePlate.vue';
import TheCompositeIndicesOperation from './component/TheCompositeIndicesOperation.vue';
import TheFinancialIndicatorPlate from './component/TheFinancialIndicatorPlate.vue';

export default {
	components: {
		TheIndicatorBoard,
		TheValuationPercentilePlate,
		TheCompositeIndicesOperation,
		TheFinancialIndicatorPlate
	}
};
</script>
<style scoped>
/* .marketAnalysis ::v-deep .el-form-item__content {
	margin-left: 0 !important;
} */
</style>
