import request from '@/utils/request';

const server = '/Tools';

// 获取用户list
export function getUserList(params) {
	return request({
		url: server + '/userlist/',
		method: 'get',
		params
	});
}
// 获取模板
export function getModelList(params) {
	return request({
		url: server + '/query_model/',
		method: 'get',
		params
	});
}
// 新增模板
export function saveModel(data) {
	return request({
		url: server + '/save_model/',
		method: 'post',
		data
	});
}
// 编辑模板
export function editModel(data) {
	return request({
		url: server + '/save_model/',
		method: 'put',
		data
	});
}
// 修改模版
export function updateModel(data) {
	return requestTools({
		url: server + '/update_model/',
		method: 'put',
		data
	});
}
// 删除模板
export function deleteModel(params) {
	return request({
		url: server + '/delete_model/',
		method: 'delete',
		params
	});
}
// 获取自定义指数列表

/**
@/api/pages/filter/custom.js
 */

export function getIndexList(params) {
	return request({
		url: server + '/indexList/',
		method: 'get',
		params
	});
}
// 删除自定义指数
export function deleteIndexList(data) {
	return request({
		url: server + '/indexList/',
		method: 'delete',
		data
	});
}
// 新增自定义指数
export function postIndexList(data) {
	return request({
		url: server + '/indexListSub/',
		method: 'post',
		data
	});
}
// 获取自定义基准成分
export function getIndexListBase(params) {
	return request({
		url: server + '/indexListBase/',
		method: 'get',
		params
	});
}
// 修改定义指数
export function putIndexList(data) {
	return request({
		url: server + '/indexListSub/',
		method: 'put',
		data
	});
}
