<!--  -->
<template>
	<div>
		<div
			v-show="haveName != ''"
			style="
				font-weight: 400;
				font-size: 14px;
				line-height: 22px;
				color: rgba(0, 0, 0, 0.85);
				margin-left: 0px;
				margin-right: 16px;
				margin-bottom: 4px;
			"
		>
			{{ haveName }}
		</div>
		<div class="boxOnlyYSF">
			<el-radio-group v-model="model" @change="changeRadio">
				<el-radio :label="true">是</el-radio>
				<el-radio :label="false">否</el-radio>
			</el-radio-group>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		haveName: {
			type: String,
			default: ''
		},
		dataX: {
			type: Object,
			default: {}
		},
		indexFlag: {
			type: Number
		},
		baseIndexFlag: {
			type: Number
		}
	},
	data() {
		//这里存放数据
		return {
			model: true
		};
	},
	mounted() {
		if (JSON.stringify(this.dataX) != '{}') {
			if (this.dataX.dataResult && this.dataX.dataResult.length > 0) {
				this.model = this.dataX.dataResult[0].value;
			}
		}
	},
	watch: {
		dataX(val) {
			if (val.dataResult && val.dataResult.length > 0) {
				this.model = this.equitytype = val.dataResult[0].value;
			}
		}
	},
	//方法集合
	methods: {
		changeRadio() {}
	}
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
.boxOnlyYSF {
	display: flex;
	align-items: center;
}
</style>
