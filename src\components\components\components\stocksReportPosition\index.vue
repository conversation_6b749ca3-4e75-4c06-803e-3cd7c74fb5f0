<template>
  <div id="stocksReportPosition">
    <analysis-card-title title="已披露股票持仓"
                         @downloadExcel="exportExcel">
      <el-select v-model="quarter"
                 placeholder="选择报告期"
                 @change="changeQuarter">
        <el-option v-for="item in quarterList"
                   :key="item.value"
                   :label="item.label"
                   :value="item.value"></el-option>
      </el-select>
    </analysis-card-title>
    <el-table v-loading="loading"
              :data="data"
              :default-sort="{ prop: 'value', order: 'descending' }"
              class="table"
              stripe
              height="600"
              ref="multipleTable"
              header-cell-class-name="table-header">
      <el-table-column v-for="item in column"
                       :key="item.value"
                       :prop="item.value"
                       :align="item.align ? item.align : 'gotoleft'"
                       :label="item.label"
                       border
                       stripe
                       sortable>
        <template #header>
          <long-table-popover-chart v-if="item.popover"
                                    :data="formatTableData()"
                                    date_key="name"
                                    :data_key="item.value"
                                    :show_name="item.label">
            <span>{{ item.label }}</span>
          </long-table-popover-chart>
          <span v-else>{{ item.label }}</span>
        </template>
        <template slot-scope="{ row }">
          <span>
            {{ item.format ? item.format(row[item.value]) : row[item.value] }}
          </span>
        </template>
      </el-table-column>
      <template slot="empty">
        <el-empty image-size="160"></el-empty>
      </template>
    </el-table>
  </div>
</template>

<script>
import { filter_json_to_excel } from '@/utils/exportExcel.js';

// 最新报告持仓分析
import { getHoldStocks, getDateList } from '@/api/pages/Analysis.js';
export default {
  name: 'latestReportedPosition',
  data () {
    return {
      loading: true,
      data: [],
      newtime: '',
      name: '',
      show: true,
      quarterList: [],
      quarter: '',
      optionbaogao: {},
      column: [
        {
          label: '股票代码',
          value: 'stock_code',
          popover: false,
          sortable: false
        },
        {
          label: '股票简称',
          value: 'name',
          popover: false,
          sortable: false
        },
        {
          label: '持仓市值(万元)',
          value: 'value',
          align: 'right',
          format: this.fix_money,
          popover: true,
          sortable: true
        },
        {
          label: '持仓数量',
          value: 'holdings',
          align: 'right',
          popover: true,
          sortable: true
        },
        {
          label: '占净值比',
          value: 'weight',
          align: 'right',
          format: this.fix3p,
          popover: true,
          sortable: true
        },
        {
          label: '相对上季度增减',
          value: 'changeofH',
          align: 'right',
          popover: true,
          sortable: true
        },
        {
          label: '区间收益',
          value: 'nav',
          align: 'right',
          format: this.fix2p,
          popover: true,
          sortable: true
        },
        {
          label: '净值贡献',
          value: 'nav_weight',
          align: 'right',
          format: this.fix4p,
          popover: true,
          sortable: true
        },
        {
          label: '抱团数',
          value: 'number',
          popover: true,
          sortable: true
        },
        // {
        // 	label: 'PB',
        // 	value: 'pb',
        // 	format: this.fix3,
        // 	popover: true
        // },
        // {
        // 	label: 'PB 分位',
        // 	value: 'pb_rank',
        // 	format: this.fix3,
        // 	popover: true
        // },
        // {
        // 	label: 'PE',
        // 	value: 'pe',
        // 	format: this.fix3,
        // 	popover: true
        // },
        // {
        // 	label: 'PE 分位',
        // 	value: 'rank',
        // 	format: this.fix3,
        // 	popover: true
        // },
        // {
        // 	label: '抱团数',
        // 	value: 'number',
        // 	popover: true
        // },
        {
          label: '行业',
          value: 'swname',
          popover: false
        }
      ],
      info: {}
    };
  },
  methods: {
    // 获取报告持仓季度列表
    async getDateList () {
      let data = await getDateList({
        code: this.info.code,
        type: this.info.type,
        flag: this.info.flag,
        start_date: this.info.start_date,
        end_date: this.info.end_date
      });
      if (data?.mtycode == 200) {
        this.quarterList = data?.data
          ?.sort((a, b) => {
            return this.moment(this.moment(a, 'YYYY QQ').format()).isAfter(this.moment(b, 'YYYY QQ').format()) ? -1 : 1;
          })
          ?.map((item) => {
            return { label: item, value: item };
          });
        this.quarter = this.quarterList?.[0]?.value;
        this.getHoldStocks();
      }
    },
    // 切换季度
    changeQuarter () {
      this.loading = true;
      this.getHoldStocks();
    },
    // 获取最新报告持仓数据
    async getHoldStocks () {
      let data = await getHoldStocks({
        flag: this.info.flag,
        code: this.info.code,
        type: this.info.type,
        yearqtr: this.quarter,
        start_date: this.info.start_date,
        end_date: this.info.end_date
      });
      this.loading = false;
      if (data?.mtycode == 200) {
        this.data = data?.data.map((item) => {
          return {
            ...item,
            swname: item.swlevel1 + '-' + item.swlevel2 + '-' + item.swlevel3
          };
        });
        this.newtime = data?.data?.[0]?.yearqtr;
      }
    },
    async getData (info) {
      this.info = info;
      await this.getDateList();
    },
    hideLoading () {
      this.show = false;
    },
    formatTableData () {
      let data = [];
      this.data.map((item) => {
        let obj = { ...item };
        for (const key in item) {
          let format = this.column.find((obj) => {
            return obj.value == key;
          })?.format;
          if (format) {
            let val = format(item[key]);
            obj[key] = typeof val == 'string' ? (val.includes('%') ? val?.split('%')?.[0] * 1 : !isNaN(val) ? val * 1 : val) : val;
          }
        }
        data.push(obj);
      });
      return data;
    },
    fix_money (val) {
      return val * 1 && !isNaN(val) ? (val / 10 ** 4).toFixed(3) : '--';
    },
    fix2p (val) {
      return val * 1 && !isNaN(val) ? (val * 100).toFixed(2) + '%' : '--';
    },
    fix4p (val) {
      return val * 1 && !isNaN(val) ? (val * 1).toFixed(2) + '%' : '--';
    },
    fix6p (val) {
      return val * 1 && !isNaN(val) ? (val * 1000000).toFixed(2) + '%' : '--';
    },
    fix3 (val) {
      return val * 1 && !isNaN(val) ? (val * 1).toFixed(3) : '--';
    },
    fix3p (val) {
      return val * 1 && !isNaN(val) ? (val * 1).toFixed(3) + '%' : '--';
    },
    exportExcel () {
      let list = this.column.map((item) => {
        return { label: item.label, value: item.value };
      });
      filter_json_to_excel(
        list,
        this.data.sort((a, b) => {
          return b.weight - a.weight;
        }),
        '已披露股票持仓'
      );
    },
    async createPrintWord (info) {
      if (this.quarter) {
        this.info = info;
        await this.getHoldStocks();
      } else {
        await this.getData(info);
      }
      return await new Promise((resolve, reject) => {
        this.$nextTick(async () => {
          let list = this.column.map((item) => {
            return { label: item.label, value: item.value };
          });
          let data = this.data.map((object) => {
            let obj = { ...object };
            for (const key in object) {
              let format = this.column.find((v) => v.value == key)?.format;
              if (format) {
                obj[key] = format(object[key]);
              }
            }
            return obj;
          });
          // 已披露股票持仓
          if (data.length) {
            resolve([
              ...this.$exportWord.exportTitle('已披露股票持仓'),
              ...this.$exportWord.exportDescripe('报告期：' + this.quarter),
              ...this.$exportWord.exportTable(
                list,
                data.sort((a, b) => {
                  return b.weight - a.weight;
                }),
                '',
                true
              )
            ]);
          } else {
            resolve([]);
          }
        });
      });
    }
  }
};
</script>
