<template>
	<div>
		<div class="subscription_form_item">
			<div style="margin-bottom: 16px">选择打印内容</div>
			<div
				v-for="(obj, index) in subscriptionList"
				:key="index"
				@mouseenter="mouseenterItem(index)"
				@mouseleave="hoverIndex = null"
				:style="hoverIndex == index ? 'background: rgba(0, 0, 0, 0.04)' : ''"
			>
				<div class="subscription_title subscription_content">
					<el-select v-model="obj.type" placeholder="" style="width: 104px" @change="changeType">
						<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
					</el-select>
					<search-component
						v-show="obj.type != 'pool'"
						:type="obj.type"
						:placeholder="obj.name"
						select-style="width: 328px;margin-left: 8px"
						@resolveFather="getFundInfo"
					></search-component>
					<el-select
						v-show="obj.type == 'pool'"
						v-model="obj.code"
						placeholder="选择池子"
						style="width: 328px; margin-left: 8px"
						@change="changePool"
					>
						<el-option v-for="item in poolList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
					</el-select>
					<div class="delete_icon" v-show="index == hoverIndex && subscriptionList.length > 1" @click="deleteItem(index)">
						<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path
								d="M4.62476 1.8736H4.49976C4.56851 1.8736 4.62476 1.81735 4.62476 1.7486V1.8736H9.37476V1.7486C9.37476 1.81735 9.43101 1.8736 9.49976 1.8736H9.37476V2.9986H10.4998V1.7486C10.4998 1.19703 10.0513 0.748596 9.49976 0.748596H4.49976C3.94819 0.748596 3.49976 1.19703 3.49976 1.7486V2.9986H4.62476V1.8736ZM12.4998 2.9986H1.49976C1.22319 2.9986 0.999756 3.22203 0.999756 3.4986V3.9986C0.999756 4.06735 1.05601 4.1236 1.12476 4.1236H2.06851L2.45444 12.2955C2.47944 12.8283 2.92007 13.2486 3.45288 13.2486H10.5466C11.081 13.2486 11.5201 12.8298 11.5451 12.2955L11.931 4.1236H12.8748C12.9435 4.1236 12.9998 4.06735 12.9998 3.9986V3.4986C12.9998 3.22203 12.7763 2.9986 12.4998 2.9986ZM10.4263 12.1236H3.57319L3.19507 4.1236H10.8044L10.4263 12.1236Z"
								fill="black"
								fill-opacity="0.45"
							/>
						</svg>
					</div>
				</div>
			</div>
			<div class="add_subscription" @click="addSubscription">
				<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
					<path
						fill-rule="evenodd"
						clip-rule="evenodd"
						d="M7.46875 1.375C7.55208 1.375 7.59375 1.41667 7.59375 1.5L7.593 6.406L12.25 6.40625C12.3333 6.40625 12.375 6.44792 12.375 6.53125V7.46875C12.375 7.55208 12.3333 7.59375 12.25 7.59375L7.593 7.593L7.59375 12.5C7.59375 12.5833 7.55208 12.625 7.46875 12.625H6.53125C6.44792 12.625 6.40625 12.5833 6.40625 12.5L6.406 7.593L1.75 7.59375C1.66667 7.59375 1.625 7.55208 1.625 7.46875V6.53125C1.625 6.44792 1.66667 6.40625 1.75 6.40625L6.406 6.406L6.40625 1.5C6.40625 1.41667 6.44792 1.375 6.53125 1.375H7.46875Z"
						fill="black"
						fill-opacity="0.65"
					/>
				</svg>
				<span>添加订阅</span>
			</div>
		</div>
	</div>
</template>

<script>
import searchComponent from '@/components/components/components/search/index.vue';

import { getPoolList, getPoolDetail } from '@/api/pages/SystemMixed.js';

export default {
	components: { searchComponent },
	data() {
		return {
			hoverIndex: null,
			current: null,
			userConfigList: [],
			subscriptionList: [
				{
					type: 'fund',
					name: '',
					model: '',
					modelList: []
				}
			],
			options: [
				{
					label: '基金',
					value: 'fund'
				},
				{
					label: '基金经理',
					value: 'manager'
				},
				{
					label: '基金池',
					value: 'pool'
				}
			],
			info: {},
			poolList: []
		};
	},
	methods: {
		// 获取池子列表
		async getPoolList() {
			let data = await getPoolList();
			this.poolList = data;
		},
		// 鼠标移入item
		mouseenterItem(index) {
			this.hoverIndex = index;
			this.current = index;
		},
		// 获取选择基金基本信息
		getFundInfo(val) {
			this.info = {
				...val,
				code: val.id
			};
			this.subscriptionList[this.current].info = this.info;
			// this.getUserConfig();
			this.filterUserConfig();
		},
		// 过滤模版数据
		filterUserConfig() {
			let data = this.userConfigList;
			if (this.info.flag == 'fund') {
				this.subscriptionList[this.current].modelList = data.filter((item) => {
					return !item.ismanager && item.type == this.info.type;
				});
			} else {
				this.subscriptionList[this.current].modelList = data.filter((item) => {
					return item.ismanager;
				});
			}
			this.subscriptionList[this.current].model = this.subscriptionList[this.current].modelList?.[0]?.model_id;
		},
		// 获取池子信息
		changePool(val) {
			this.subscriptionList[this.current]['info'] = {
				code: val,
				name: this.poolList.filter((item) => {
					return item.id == val;
				})?.[0]?.name,
				flag: 'pool',
				type: '*'
			};
		},
		// 删除条目
		deleteItem(index) {
			this.subscriptionList.splice(index, 1);
		},
		// 添加订阅
		addSubscription() {
			this.subscriptionList.push({
				type: 'fund',
				name: '',
				model: ''
			});
		},
		// 父组件接收值
		getItemInfos() {
			return this.subscriptionList;
		},
		// 监听打印类型选择
		changeType(val) {
			if (val == 'pool') {
				if (this.poolList.length == 0) {
					this.getPoolList();
				}
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.subscription_form_item {
	margin-bottom: 24px;
	.avatar-uploader-icon {
		font-size: 28px;
		color: #8c939d;
		width: 100px;
		height: 100px;
		line-height: 100px;
		text-align: center;
	}
	.avatar {
		width: 100px;
		height: 100px;
		display: block;
		object-fit: cover;
	}
	.subscription_title {
		margin-bottom: 8px;
	}
	.email_list_tags {
		width: 668px;
		border: 1px solid #d9d9d9;
		border-radius: 4px;
	}
	.subscription_content {
		display: flex;
		align-items: center;
		.delete_icon {
			margin-left: 16px;
			cursor: pointer;
		}
		.delete_icon:hover {
			svg {
				path {
					fill: #4096ff;
				}
			}
		}
	}
	.delete_icon {
		margin-top: 8px;
		margin-left: 16px;
		cursor: pointer;
	}
	.delete_icon:hover {
		svg {
			path {
				fill: #4096ff;
			}
		}
	}
	.add_subscription {
		display: flex;
		justify-content: center;
		align-items: center;
		cursor: pointer;
		width: 440px;
		height: 32px;
		background: #ffffff;
		border: 1px dashed #d9d9d9;
		box-shadow: 0px 2px 0px rgba(0, 0, 0, 0.016);
		border-radius: 4px;
	}
	.add_subscription:hover {
		border-color: #4096ff;
	}
}
</style>
