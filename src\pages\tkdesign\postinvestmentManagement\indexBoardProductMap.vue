<template>
  <div class="box_Board">
    <div class="header_box"><span class="header_unactive">投后&nbsp;/&nbsp;映射管理&nbsp;/&nbsp;</span>产品映射管理<span></span></div>
    <div class="border_table">
      <el-tabs v-model="activeName"
               @tab-click="handleClick">
        <el-tab-pane label="委托账户"
                     name="first"></el-tab-pane>
        <el-tab-pane label="直投账户"
                     name="second"></el-tab-pane>
      </el-tabs>
      <div class="border_table_header"
           style="display:flex;justify-content: space-between;align-items: center;">
        <div class="border_table_header_title">产品映射管理</div>
        <div><el-button style="border-color:#4096ff;background: #4096ff;color:white"
                     type=""
                     @click="addMapTable">新增</el-button><el-button @click="showDialog=true"
                     style="border-color:#4096ff;background: #4096ff;color:white;margin-left:16px"
                     type="">上传映射表</el-button><el-button @click="delALL"
                     type=""
                     style="margin-left:16px">删除原有映射</el-button><el-button type=""
                     style="margin-left:16px"
                     @click="lookDate">修改记录</el-button></div>
      </div>
      <div>
        <div style="display:flex">
          <div style="flex:1">GP3代码：<el-input style="width:260px"
                      v-model="codeSearch"
                      placeholder="输入代码"></el-input></div>
          <div style="flex:1">一级策略分类：<el-input style="width:260px"
                      v-model="oneSearch"
                      placeholder="一级策略分类"></el-input></div>
          <div style="flex:1">二级策略分类：<el-input style="width:260px"
                      v-model="twoSearch"
                      placeholder="二级策略分类"></el-input></div>
          <div style="flex:1">一级管理人：<el-input style="width:260px"
                      v-model="oneManagerSearch"
                      placeholder="一级管理人"></el-input></div>
        </div>
        <div style="margin-top:16px;margin-bottom: 16px;">
          <el-button @click="searchMap"
                     type="primary">查询</el-button>
          <el-button @click="reset"
                     type="">重置</el-button>
        </div>
      </div>
      <div>
        <el-table :data="tableData">
          <el-table-column align="gotoleft"
                           prop="objectName"
                           label="GP3代码">
            <template slot-scope="scope">
              <div v-if="scope.row.addflag"><el-input v-model="scope.row.code"
                          placeholder="输入代码"></el-input></div>
              <div v-else>{{ scope.row.code }}</div>
            </template>
          </el-table-column>
          <el-table-column align="gotoleft"
                           prop="objectName"
                           label="一级策略分类">
            <template slot-scope="scope">
              <div v-if="scope.row.addflag"><el-input v-model="scope.row.name"
                          placeholder="一级策略分类"></el-input></div>
              <div v-else>{{ scope.row.name }}</div>
            </template></el-table-column>
          <el-table-column align="gotoleft"
                           prop="objectName"
                           label="二级策略分类"> <template slot-scope="scope">
              <div v-if="scope.row.addflag"><el-input v-model="scope.row.attribution"
                          placeholder="二级策略分类"></el-input></div>
              <div v-else>{{ scope.row.attribution }}</div>
            </template></el-table-column>
          <el-table-column align="gotoleft"
                           prop="date"
                           label="一级管理人"> <template slot-scope="scope">
              <div v-if="scope.row.addflag"><el-input v-model="scope.row.industry"
                          placeholder="一级管理人"></el-input></div>
              <div v-else>{{ scope.row.industry }}</div>
            </template></el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <div v-if="!scope.row.addflag"
                   style="display:flex">
                <el-button @click="editCellTable(scope.row,scope.row.code)"
                           style="color:#4096ff"
                           type="text">编辑</el-button>
                <el-button @click="deleteCell(scope.row.code)"
                           style="color:#4096ff"
                           type="text">删除</el-button>
              </div>
              <div v-else
                   style="display:flex">
                <el-button @click="saveEdit()"
                           style="color:#4096ff"
                           type="text">保存</el-button>
                <el-button @click="init(1)"
                           style="color:#4096ff"
                           type="text">取消</el-button>
              </div>
            </template>
          </el-table-column>
          <template slot="empty">
            <el-empty image-size="160"></el-empty>
          </template>
        </el-table>
        <div class="pagination_board">
          <el-pagination background
                         layout="total, sizes, prev, pager, next"
                         :current-page.sync="pageIndex"
                         :page-size="pageSize"
                         :total="totalSize"
                         @size-change="handleSizeChange"
                         @current-change="handlePageChange"></el-pagination>
        </div>
      </div>
    </div>
    <el-dialog width="900"
               :visible.sync="showDialog"
               title="修改记录">
      <excelPort @submitDialog="submitDialog"
                 @cancelDialog="cancelDialog"></excelPort>
    </el-dialog>
    <el-dialog width="900"
               :visible.sync="showDialog2"
               title="上传映射表">
      <el-table :data="tableData2">
        <el-table-column align="gotoleft"
                         prop="objectName"
                         label="证券代码">
          <template slot-scope="scope">

            <div>{{ scope.row.code }}</div>
          </template>
        </el-table-column>
        <el-table-column align="gotoleft"
                         prop="objectName"
                         label="证券简称">
          <template slot-scope="scope">

            <div>{{ scope.row.name }}</div>
          </template></el-table-column>
        <el-table-column align="gotoleft"
                         prop="objectName"
                         label="产业归属"> <template slot-scope="scope">

            <div>{{ scope.row.attribution }}</div>
          </template></el-table-column>
        <el-table-column align="gotoleft"
                         prop="industry"
                         label="中心行业"> <template slot-scope="scope">

            <div>{{ scope.row.industry }}</div>
          </template></el-table-column>
        <el-table-column align="gotoleft"
                         prop="date"
                         label="修改时间"> <template slot-scope="scope">

            <div>{{ scope.row.date }}</div>
          </template></el-table-column>
        <template slot="empty">
          <el-empty image-size="160"></el-empty>
        </template>
      </el-table>
      <div class="pagination_board">
        <el-pagination background
                       layout="total, sizes, prev, pager, next"
                       :current-page.sync="pageIndex2"
                       :page-size="pageSize2"
                       :total="totalSize2"
                       @size-change="handleSizeChange2"
                       @current-change="handlePageChange2"></el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getListMap, getListMap2, searchMap, insertMap, delMap, upMap } from '@/api/pages/tkAnalysis/index.js'
import excelPort from './component/mapProduct/alphaownpool.vue'
export default {
  components: {
    excelPort,

  },
  data () {
    return {
      activeName: 'first',
      oneManagerSearch: '',
      oneSearch: '',
      twoSearch: '',
      showDialog2: false,
      pageIndex: 1,
      pageSize: 20,
      tableData: [],
      totalSize: 0,
      pageIndex2: 1,
      pageSize2: 20,
      tableData2: [],
      totalSize2: 0,
      showDialog: false,
      selectData: [],
      createdName: '',
      codeSearch: '',
      industrySearch: '',
      inSearch: '',
      flag: 0,

    };
  },
  mounted () {
    this.init(1);
  },
  methods: {
    reset () {
      this.codeSearch = '';
      this.industrySearch = '';
      this.inSearch = ''
    },
    lookDate () {
      this.showDialog2 = true
      this.init2()
    },
    // 保存新增的单条映射或修改记录
    async saveEdit () {
      if (flag == 1) {
        let { data, mtycode, mtymessage } = await insertMap({
          code: this.codeSearch,
          name: 1,
          industry: this.industrySearch,
          attribution: this.inSearch,
        })
        if (mtycode == 200) {
          this.$message.success('新增成功')
          this.init(1)
        }
        else { this.$message.error(mtymessage) }
      }
      else {
        let { data, mtycode, mtymessage } = await upMap({
          code: this.codeSearch,
          name: 1,
          industry: this.industrySearch,
          attribution: this.inSearch,
        })
        if (mtycode == 200) {
          this.$message.success('修改成功')
          this.init(1)
        }
        else { this.$message.error(mtymessage) }
      }
    },
    // 增加一条映射
    addMapTable () {
      this.flag = 1

      this.tableData.unshift({
        code: '',
        name: '',
        industry: '',
        attribution: '',
        addflag: true
      })
    },
    // 查询
    async searchMap () {
      this.pageIndex = 1
      let { data, mtycode, mtymessage } = await searchMap({
        code: this.codeSearch,
        type: 2,
        industry: this.industrySearch,
        attribution: this.inSearch,
        pageSize: this.pageSize,
        currentPage: this.pageIndex
      })
      if (mtycode == 200) {
        this.totalSize = data?.pageSize * data?.totalPage || 0
        this.pageIndex = data?.currentPage || 1
        this.pageSize = data?.pageSize || 20
        this.tableData = data?.dataList.map(item => { return { ...item, addflag: false } })
      }
      else {
        this.tableData = []
        this.totalSize = 0
        this.pageIndex = 1
        this.pageSize = 20
      }

    },
    cancelDialog () {
      this.showDialog = false;
    },
    submitDialog (e, e1) {
      this.showDialog = false;
      this.selectData = e
      this.createdName = e1
      this.submitObject()
    },

    async deleteCell (id) {
      let { data, mtycode, mtymessage } = await delMap({
        id: id,
        type: 2
      })
      if (mtycode == 200) {
        this.$message.success('删除成功')
        this.init(1)
      }
      else { this.$message.error(mtymessage) }
    },
    async delALL () {
      let { data, mtycode, mtymessage } = await delMap({
        type: 2
      })
      if (mtycode == 200) {
        this.$message.success('删除全部成功')
        this.init(1)
      }
      else { this.$message.error(mtymessage) }
    },
    handlePageChange () {
      this.init(this.pageIndex)
    },
    handleSizeChange (val) {
      this.pageSize = val;
      this.init(this.pageIndex)
    },
    handlePageChange2 () {
      this.init2(this.pageIndex2)
    },
    handleSizeChange2 (val) {
      this.pageSize2 = val;
      this.init2(this.pageIndex2)
    },

    // 获取分析对象列表
    async init (pageIndex) {
      this.tableData = [{
        "id": "id",
        "code": "股票代码",
        "company1": "一级管理人",
        "strategy1": "一级策略",
        "strategy2": "二级策略",
        "industry": "中心行业",
        "attribution": "产品归属",
        'addflag': false
      }]
      return;//TODO接口有问题按照我的来
      let { data, mtycode, mtymessage } = await getListMap({ currentPage: pageIndex, pageSize: this.pageSize })
      if (mtycode == 200) {
        this.totalSize = data?.pageSize * data?.totalPage || 0
        this.pageIndex = data?.currentPage || 1
        this.pageSize = data?.pageSize || 20
        this.tableData = data?.dataList.map(item => { return { ...item, addflag: false } })
      }
      else {
        this.tableData = []
        this.totalSize = 0
        this.pageIndex = 1
        this.pageSize = 20
      }
    },
    // 获取修改记录
    async init2 (pageIndex) {
      this.tableData2 = [{
        "id": "id",
        "code": "股票代码",
        "company1": "一级管理人",
        "strategy1": "一级策略",
        "strategy2": "二级策略",
        "industry": "中心行业",
        "attribution": "产品归属",
        'date': '20221009'
      }]
      return;//TODO接口有问题按照我的来
      let { data, mtycode, mtymessage } = await getListMap2({ currentPage: pageIndex, pageSize: this.pageSize2 })
      if (mtycode == 200) {
        this.totalSize2 = data?.pageSize * data?.totalPage || 0
        this.pageIndex2 = data?.currentPage || 1
        this.pageSize2 = data?.pageSize || 20
        this.tableData2 = data?.dataList.map(item => { return { ...item, addflag: false } })
      }
      else {
        this.tableData2 = []
        this.totalSize2 = 0
        this.pageIndex2 = 1
        this.pageSize2 = 20
      }
    },
    editCellTable (row, id) {
      this.flag = 0
      row.addflag = true
    },
    // 修改分析对象
    async editCell (id) {
      let { data, mtycode, mtymessage } = await delMap({
        type: '2',
        // complete: '1',
        id: id,
      })
      if (mtycode == 200) {
        this.$message.success('修改成功')
        this.init(1)
      }
      else { this.$message.error(mtymessage) }
    }
  },
}
</script>
<style lang="scss">
.box_Board {
	padding: 0px 24px 16px 24px;
}
.border_table {
	padding-top: 16px;
	padding-bottom: 16px;
	padding-left: 24px;
	padding-right: 24px;
	background: white;
	.border_table_header {
		margin-bottom: 20px;
		.border_table_header_title {
			color: rgba(0, 0, 0, 0.85);
			text-align: center;
			font-size: 16px;
			font-style: normal;
			font-weight: 500;
			line-height: 24px; /* 150% */
		}
	}
}
.pagination_board {
	text-align: right;
	margin-top: 16px;
}
.header_box {
	margin-top: 16px;
	margin-bottom: 16px;
}
.header_unactive {
	font-size: 14px;
	font-weight: 400;
	line-height: 22px;
	text-align: left;
	color: rgba(0, 0, 0, 0.45);
}
.header_active {
	font-size: 14px;
	font-weight: 400;
	line-height: 22px;
	text-align: left;
	color: rgba(0, 0, 0, 0.85);
}
</style>
