class StringTool {
	fix10Y(value) {
		return (Number(value) / 100000000).toFixed(2);
	}
	fix6(value) {
		return value.substring(0, 10);
	}
	fix3(value) {
		return parseInt(value * 1000) / 1000;
	}
	fix4(value) {
		return (parseInt(value * 10000) / 10000).toFixed(4);
	}
	fix2pxx(value) {
		if (value == '' || value == null || value == 'nan' || value == '--') {
			return '—';
		} else {
			return Number(value).toFixed(2) + '%';
		}
	}
	fix2p(value, flagB) {
		if (value == '' || value == null || value == 'nan' || value == '--') {
			return '--';
		} else if (value == '正无穷' || value == '负无穷') {
			return value;
		} else {
			if (flagB) return Number(value).toFixed(2);
			else return '前' + (100 - value * 100).toFixed(2) + '%';
		}
	}
	fixp(value) {
		return value.toFixed(2) + '%';
	}
	isDefault(value) {
		return value == '--' ? '' : value;
	}
	//0.023 to  2.33%
	fix2px(value) {
		if (value == '' || value == null || value == 'nan' || value == '--') {
			return '--';
		} else {
			return (value * 100).toFixed(2) + '%';
		}
	}
	//没搞懂命名啥意思
	fix2pxxx(value) {
		if (value == '' || value == null || value == 'nan' || value == '--' || value == 'NaN' || value == 'null') {
			return '--';
		} else {
			return Number(value).toFixed(2) + '%';
		}
	}
    fix2(value) {
        if (value == '' || value == null || value == 'nan' || value == '--' || value == 'NaN' || value == 'null') {
			return '--';
		} else {
			return Number(value).toFixed(2);
		}
    }
}
export default new StringTool();
