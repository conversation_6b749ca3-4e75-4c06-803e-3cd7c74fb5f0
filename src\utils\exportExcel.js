const { Format } = require('./exportWord.js');
var format = new Format();
const { export_json_to_excel,export_json_to_excel_multiHeader } = require('@/vendor/Export2Excel.js');

// 过滤单表数据
export async function filter_json_to_excel(column, data, defaultTitle) {
	let list = [];
	let th = column.map((item) => {
		return item.label;
	});
	data.map((obj) => {
		let arr = [];
		column.map((item) => {
			arr.push(item.format ? format[item.format](obj[item.value]) : obj[item.value]);
		});
		list.push(arr);
	});
	export_json_to_excel(th, list, defaultTitle);
}

// 过滤单表数据
export async function filter_to_excel(column, data, defaultTitle) {
	let list = [];
	let th = column.map((item) => {
		return item.label;
	});
	data.map((obj) => {
		let arr = [];
		column.map((item) => {
			arr.push(item.format ? format[item.format](obj[item.prop]) : obj[item.prop]);
		});
		list.push(arr);
	});
	export_json_to_excel(th, list, defaultTitle);
}
// valuekey=c  insidekeys=['a','b'], 则数据=obj.a.b.c 
function getInsideValue(obj, valuekey, insidekeys) {
  if (obj) {
    if (insidekeys && insidekeys.length > 0) {
      let insideObj = obj;
      insidekeys.map(key => {
        if (insideObj[key]) {
          insideObj = insideObj[key]
        } else {
          insideObj = null;
        }
      })
      if (insideObj) {
        return insideObj[valuekey];
      }
    } else {
      return obj[valuekey];
    }
  }
  return null;
}

// 过滤单表数据 
export function filter_json_to_excel_inside(column, data,insidekeys, defaultTitle) {
let list = [];
let th = column.map((item) => {
  return item.label;
});

data.map((obj) => {
  let arr = [];
  column.map((item) => {
    const value = getInsideValue(obj,item.value,insidekeys);
    arr.push(item.format ? format[item.format](value) : value);
  });
  list.push(arr);
});
export_json_to_excel_multiHeader([th],[], list, defaultTitle);
}


export function filter_json_to_excel_inside_multiHeader(column, data,insidekeys, defaultTitle,mergeArray,formatArray) {
let list = [];
data.map((obj) => {
  let arr = [];
  formatArray.map((item) => {
    const value = getInsideValue(obj,item.value,insidekeys);
    arr.push(item.format ? format[item.format](value) : value);
  });
  list.push(arr);
});

export_json_to_excel_multiHeader(column,mergeArray, list, defaultTitle);
}




/**
* [[1,2,3],[a,b,c]] => [[1,a],[2,b],[3,c]]
* @param {[[]]} array 
* @returns [[]]
*/
export function changColumnToRow(array,arrayFormat) {
if(!array || array.length===0) return null;
let maxLength = 0;
array.map(column=>{
  if(column && column.length> maxLength) {
    maxLength = column.length;
  }
})

const newArray = [];
for (let index = 0; index < maxLength; index++) {
  const temp = []
  array.map((column,arrayIndex)=>{
    let value = ''
    if(column && column.length> index) {
      value = column[index];
    }
    let f;
    if(arrayFormat && arrayFormat.length> arrayIndex) {
      f = arrayFormat[arrayIndex];
    
    }
    temp.push(f ? format[f](value) : value);

  })
  newArray.push(temp);
}
return newArray;
}
