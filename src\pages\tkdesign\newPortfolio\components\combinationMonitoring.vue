<template>
	<div >
		<div v-for="item in template" :key="item.value" v-show="item.isshow" :class="item.type" >
			<component :is="item.is" :ref="item.value" :indexInfo="indexInfo" v-loading="loading" style="padding-bottom: 20px;" :endDate="endDate" ></component>
		</div>
	</div>
</template>

<script>
import combinationMonitoringTable from './components/combinationMonitoringTable.vue';
import combinationConfiguration from './components/combinationConfiguration.vue';
import holdNavTrend from './components/holdNavTrend.vue';
import combinationRiskEarning from './components/combinationRiskEarning.vue';

export default {
	components: {
		combinationMonitoringTable,
		combinationConfiguration,
		holdNavTrend,
		combinationRiskEarning,
	},
	data() {
		return {
			info: {},
			distributionPostData: {},
			templateList: [],
			requestOver: 0,
			requestAll: 0,
			loading: false,
			correlationCoefficient: null,
			dynamicPullback: null,
			combinationHoldReturn: null,
			combinationHoldReturnData: {},
			combinationPartReturn: null,
			combinationPartReturnData: {},
			codes: [],
			start_date: '',
			end_date: '',
			fundOrManagerReturn: null,
			basicInfo: null,
			indexReturnInfo: null,
			activeIndex: 'fund',
			combinationFundWeight: null,
			fofAllocationDetails: null,
			returnInfoIndex: [],
			fofDrawdown: {}
		};
	},
	props: {
		indexInfo: {
			type: Object,
			default: {}
		},
		active: {
			type: String,
			default: ''
		},
		template:{
			type: Array,
			dafault: []
		},
		endDate:{
			type: String,
			default:""
		}
	},

	methods: {
		// 获取打印数据
		createPrintWord() {
			let printData = [];
			this.templateList.map((item) => {
				if (item.isshow) {
					if (this.$refs[item.value]?.[0].createPrintWord) {
						printData.push(...this.$refs[item.value]?.[0].createPrintWord());
					}
				}
			});
			return printData;
		},
		// 触发各子组件获取数据
		getData() {
			this.template.forEach((item) => {
				this.$refs[item.value]?.[0].getData({
					combinationId:this.$route.query.id,
                	indexCode:this.indexInfo.id,
				});
			});
		},




	}
};
</script>

<style>
.big_template{
	margin-bottom: 20px;
}
</style>
