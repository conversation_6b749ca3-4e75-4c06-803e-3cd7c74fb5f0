export default [
	{
		path: '/objectManagement',
		component: () => import(/* webpackChunkName: "tkdesignAfter" */ '../../../pages/tkdesign/analysis/objectManagement.vue'),
		meta: { title: '分析对象管理', tagShow: false }
	},
	{
		path: '/reportManagement',
		component: () => import(/* webpackChunkName: "tkdesignAfter" */ '../../../pages/tkdesign/analysis/reportManagement.vue'),
		meta: { title: '分析报告管理', tagShow: false }
	},
	{
		path: '/report',
		component: () => import(/* webpackChunkName: "tkdesignAfter" */ '../../../pages/tkdesign/analysis/report.vue'),
		meta: { title: '生成报告', tagShow: false }
	}
];
