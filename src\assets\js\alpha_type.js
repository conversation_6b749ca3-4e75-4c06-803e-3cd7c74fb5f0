import { getTypeInfo } from '@/api/pages/Analysis.js';
import router from '@/router';
import { Message } from 'element-ui';
async function alphaGo(id, name, ROUTE) {
	let result = await getTypeInfo({ code: id, flag: id.length == 8 || id.indexOf('mty') >= 0 ? 2 : 1 });
	if (result?.mtycode == 200) {
		let data = { ...result?.data?.[0], isactive: true };
		let that = this;
		// //console.log('进入外置组件')
		// //console.log(data)
		if (data.code.length == 8 || data.code.indexOf('mty') >= 0) {
			if (data.type == null || (Array.isArray(data.type) && data.type.length == 0) || data.type == '') {
				Message('此人/产品暂无数据');
			} else {
				if (filterManagerType(data)) {
					if (ROUTE == '/fundmanagerdetail') {
						router.push({
							path: '/fundmanagerdetail/' + id,
							hash: '',
							query: {
								id: id,
								name: name,
								hold: data.type
							}
						});
						//   router.go(0); //routego(0)
					} else {
						router.push({
							path: '/fundmanagerdetail/' + id,
							hash: '',
							query: {
								id: id,
								name: name,
								hold: data.type
							}
						});
					}
				} else {
					Message('暂无此类型基金经理分析模板');
				}
			}
		} else if (data.code.length == 6) {
			if (data.type.indexOf('QDII') == -1) {
				router.push({
					path: '/funddetail/' + id,
					hash: '',
					query: {
						id: id,
						name: name,
						type: data.type,
						type1: data.type1,
						type3: data.type3,
						isactive: data.isactive
					}
				});
			} else {
				Message('找不到此类型模板');
			}
		}
	}
}
function filterManagerType(data) {
	let haveType = data.type.split(',');
	if (
		haveType.indexOf('activeequity') !== -1 ||
		haveType.indexOf('hkequity') !== -1 ||
		haveType.indexOf('bond') !== -1 ||
		haveType.indexOf('purebond') !== -1 ||
		haveType.indexOf('cbond') !== -1 ||
		// 通用模板
		haveType.indexOf('equityindex') !== -1 ||
		haveType.indexOf('fof') !== -1 ||
		haveType.indexOf('QDII') !== -1 ||
		haveType.indexOf('money') !== -1
		// end
	) {
		return true;
	} else {
		return false;
	}
}
export { alphaGo };
