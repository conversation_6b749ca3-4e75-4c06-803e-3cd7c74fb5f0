<template>
<div class="asset-selection-wrapper">

        <el-table border stripe :data="tableDataNow">
            <el-table-column  align="gotoleft" prop="individualName" show-overflow-tooltip label="姓名">
                <template slot-scope="scope">
                    <el-link @click="addFactoryVisible2 = true">{{ scope.row.individualName  }}</el-link>
                </template>
            </el-table-column>
            <el-table-column  align="gotoleft" prop="industry" show-overflow-tooltip label="期末规模"></el-table-column>
            <el-table-column  align="gotoleft" prop="totalMarketValue" sortable label="持仓权重"></el-table-column>
            <el-table-column  align="gotoleft" prop="pettm" sortable label="市值收益">
            </el-table-column>
            <el-table-column align="gotoleft" sortable prop="roe" label="累计浮盈">
                
            </el-table-column>

            <template slot="empty">
                <el-empty image-size="160"></el-empty>
            </template>
        </el-table>
        <div class="section">
          <div class="left">股价走势特征</div>
          <div class="right">
            <el-radio-group class="radio-group-wrapper" v-model="form.test1" size="small" style="margin-left: 0 !important;margin-right: 12px;">
                    <el-radio-button label="left">穿透fof持仓</el-radio-button>
                    <el-radio-button label="right">不穿透fof持仓</el-radio-button>
                    
                </el-radio-group>
          </div>
        </div>
        <lineBarChart ref="fund-performance-board-chart-container3" ></lineBarChart>
        <div class="section">
          <div class="left">盈利与估值</div>
        </div>
        <lineChart ref="fund-performance-board-chart-container" ></lineChart>

</div>
</template>
<script>
import lineBarChart from '../chart/lineBarChart.vue'
import lineChart from '../chart/lineChart.vue'
  export default {
    components:{
        lineBarChart,
        lineChart
    },
    data() {
      return {
        form:{},
        searchInput:'',
        activeName:'',
        activeName1:'',
        checkedCities:[],
        options: [
        
          {
            value: 'shejiyuanze',
            label: '因子分类1',
            children:[
              {
                value: 'shejiyuanze1',
            label: '因子1',
              },
              {
                value: 'shejiyuanze2',
            label: '因子2',
              },
              {
                value: 'shejiyuanze3',
            label: '因子3',
              },
              {
                value: 'shejiyuanze4',
            label: '因子4',
              }
            ]
          }, 
          {
            value: 'daohang',
            label: '因子分类2',
            children:[
              {
                value: 'shejiyuanze21',
            label: '因子1',
              },
              {
                value: 'shejiyuanze22',
            label: '因子2',
              },
              {
                value: 'shejiyuanze23',
            label: '因子3',
              },
              {
                value: 'shejiyuanze24',
            label: '因子4',
              }
            ]
          }
        ],
        selectedData:[],
        multipleSelection: [],
        tableDataNow: [{
                industry: '2016-05-02',
            individualName: '王小虎',
            totalMarketValue: '上海市普陀区金沙江路 1518 弄',
            pettm:'df',
			roe:'上海市普陀区金沙江路 1518 弄',
			dividendYield:'100%',
			endingSize:'100%',
			positionWeight:'1.0000',
			stockEarningsContribution:'1.2000',
            endingFactor1:'100%',
            endingFactor2:'100%',
            }, {
                industry: '2016-05-02',
            individualName: '王小虎',
            totalMarketValue: '上海市普陀区金沙江路 1518 弄',
            pettm:'df',
			roe:'上海市普陀区金沙江路 1518 弄',
			dividendYield:'100%',
			endingSize:'100%',
			positionWeight:'1.0000',
			stockEarningsContribution:'1.2000',
            endingFactor1:'100%',
            endingFactor2:'100%',
            }, {
                industry: '2016-05-02',
            individualName: '王小虎',
            totalMarketValue: '上海市普陀区金沙江路 1518 弄',
            pettm:'df',
			roe:'上海市普陀区金沙江路 1518 弄',
			dividendYield:'100%',
			endingSize:'100%',
			positionWeight:'1.0000',
			stockEarningsContribution:'1.2000',
            endingFactor1:'100%',
            endingFactor2:'100%',
            }, {
                industry: '2016-05-02',
            individualName: '王小虎',
            totalMarketValue: '上海市普陀区金沙江路 1518 弄',
            pettm:'df',
			roe:'上海市普陀区金沙江路 1518 弄',
			dividendYield:'100%',
			endingSize:'100%',
			positionWeight:'1.0000',
			stockEarningsContribution:'1.2000',
            endingFactor1:'100%',
            endingFactor2:'100%',
            }],
      };
    },
    methods: {
      initData(){
        //需要将数据返回处理成想要的结构
      },
      handleAdd(){
        //第一分类
        for(let key in this.options){
          let item=key&&this.options[key]  
          if(item&&item.children){
            //第二分类
            for(let key1 in item.children){
              let item1 = item.children[key1]
              if(item1&&item1.children){
                for(let key2 in item1.children){
                  //最后选择值
                  let item2 =  item1.children[key2]
                  //将item2内值为true的转移到右侧
                  if(item2.checked){
                    this.selectedData.push({
                      firstType:key,
                      secondType: key1,
                      ...item2
                    })
                    this.options[key].children[key1].children[key2].hide=true;
                    this.options[key].children[key1].children[key2].checked=false;
                  }
                }
              }
            }
          }
        }
      },
      handleClick(tab, event) {
        console.log(tab, event);
      },
      handleSelectionClick(row){
        this.toggleSelection(row)
      },
      toggleSelection(row) {
            this.$refs.multipleTable.toggleRowSelection(row);
      },
      handleSelectionChange(val) {
        console.log(val)
        this.multipleSelection = val;
      },
      handleRemoveOne(item){
        let index=this.selectedData.findIndex(selectedItem=>{
          return selectedItem.fund_code===item.fund_code
        })
        if(index>=0){
          this.selectedData.splice(index,1)
          this.options[item.firstType].children[item.secondType].children[item.value].hide=false;
        }
      },
      handleRemove(){
        this.multipleSelection.forEach(element => {
          this.handleRemoveOne(element)
        });
        this.multipleSelection=[]
      },
      handleClear(){
        for(let key in this.options){
          let item=key&&this.options[key]  
          if(item&&item.children){
            //第二分类
            for(let key1 in item.children){
              let item1 = item.children[key1]
              if(item1&&item1.children){
                for(let key2 in item1.children){
                  //最后选择值
                  let item2 =  item1.children[key2]
                  //将item2内值为true的转移到右侧
                    this.options[key].children[key1].children[key2].hide=false;
                    this.options[key].children[key1].children[key2].checked=false;
                }
              }
            }
          }
        }
        this.selectedData=[]
      },
      next(value){
        this.$emit('confirm',value)
      },
    },
    mounted(){
        let chartDom = this.$refs['fund-performance-board-chart-container3'];
        chartDom?.getData();
        let chartDom2 = this.$refs['fund-performance-board-chart-container'];
        chartDom2?.getData();
    }
  };
</script>
<style lang="scss" scoped>
.transfer-box-wrapper {
    display: flex;
    .first-type-wrapper {
      ::v-deep .el-tabs__nav-scroll {
        .el-tabs__nav-wrap {
          &::after {
            content: unset;
          } 
        }
      } 
    }
    .transfer-left {
        width: 480px;
        border: 1px solid #E9E9E9;
        border-radius: 4px;
        .left-nav-wrapper {
          ::v-deep .el-tabs__header {
           .el-tabs__item {
            text-align: left;
           }
          }
        }
        .transfer-left-title {
            display: flex;
            padding: 8px 16px;
            align-items: center;
            border-bottom: 1px solid #E9E9E9;
            .label {
              color: rgba(0, 0, 0, 0.85);
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 22px; /* 157.143% */
              word-break: keep-all;
            }
        }
    }
    .transfer-center {
        display: flex;
        flex-direction: column;
        padding: 0 20px;
        justify-content: center;
        align-items: center;
    }
    .transfer-right {
      min-width: 200px;
        border: 1px solid #E9E9E9;
        border-radius: 4px;
        .transfer-right-title {
            padding: 10px 16px;
            color: #000;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
            text-align: center;
        }
        .transfer-right-item{
          height:30px;
          line-height:30px;
          display: flex;
          justify-content: space-between;
          padding: 0 15px;
          .el-icon-delete{
            line-height: 30px;
          }
        }
    }
}
.asset-selection-wrapper {
    background-color: #ffffff;
    border-radius:  4px;
    border-top: 1px solid #E9E9E9;
    padding-top: 20px;
    .asset-selection-content {
        padding: 0 24px;
        padding-bottom: 20px;
    }
    .asset-selection-header {
        color: rgba(0, 0, 0, 0.85);
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 24px; /* 150% */
        padding: 16px 0;
    }
    .section{
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 0;
    }
}
.asset-selection-footer {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    color: rgba(0, 0, 0, 0.45);
    font-size: 12px;
    font-weight: 400;
    line-height: 20px; /* 166.667% */
}
</style>