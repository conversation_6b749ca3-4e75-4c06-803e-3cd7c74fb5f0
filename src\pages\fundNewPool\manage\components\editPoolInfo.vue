<template>
	<div>
		<el-dialog title="编辑池信息" :visible.sync="visible" width="400px" destroy-on-close>
			<div style="margin-bottom: 8px" class="dialogfontsize15">池名称:</div>
			<el-input style="margin-bottom: 16px" type="text" v-model="form.name" label="代码"></el-input>
			<div style="margin-bottom: 8px" class="dialogfontsize15">池说明:</div>
			<el-input style="margin-bottom: 16px" type="textarea" v-model="form.description" label="名称"></el-input>

			<div style="display: flex">
				<div style="flex: 1">
					<div style="margin-bottom: 8px" class="dialogfontsize15">是否公开:</div>
					<el-radio-group style="margin-top: 8px" v-model="form.ispublic">
						<el-radio :label="1">是</el-radio>
						<el-radio :label="0">否</el-radio>
					</el-radio-group>
				</div>

				<div style="flex: 1">
					<div><span>定时更新频率</span><span>/天</span></div>
					<el-select style="margin-top: 8px" v-model="form.flags" placeholder="请选择">
						<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" :disabled="item.disabled">
						</el-option>
					</el-select>
				</div>
			</div>
			<div>
				<div style="margin-bottom: 8px" class="dialogfontsize15">基准选择:</div>
				<div>
					<search-component
						ref="searchComponentIndex"
						type="index"
						:defaultCode="indexCode"
						select-style="width: 100%"
						placeholder="输入简拼、代码、名称查询指数"
						@resolveFather="getFundInfo"
					></search-component>
				</div>
			</div>
			<!-- <div>
        <div style="margin-bottom: 8px"
             class="dialogfontsize15">{{ '选择转发人员' }}:</div>
        <div>
          <el-cascader style="width: 100%"
                       placeholder="选择可查看人员"
                       :options="userList"
                       v-model="form.user_ids"
                       :props="{ multiple: true, emitPath: false }"
                       filterable></el-cascader>
        </div>
      </div> -->

			<span slot="footer" class="dialog-footer">
				<el-button type="primary" @click="submit">确 定</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
import { putPoolInfo } from '@/api/pages/tools/pool.js';
import searchComponent from '@/components/components/components/search/index.vue';

export default {
	components: { searchComponent },
	data() {
		return {
			visible: false,
			options: [
				{
					label: '1日',
					value: '1'
				},
				{
					label: '7日',
					value: '7'
				},
				{
					label: '30日',
					value: '30'
				},
				{
					label: '60日',
					value: '60'
				},
				{
					label: '180日',
					value: '180'
				},
				{
					label: '365日',
					value: '365'
				}
			],
			form: {},
			indexCode: ''
		};
	},
	props: {
		userList: {
			type: Object,
			default: []
		},
		ismanager: {
			type: Boolean
		}
	},
	methods: {
		getData(val) {
			this.visible = true;
			this.form = { ...val, ispublic: val.ispublic ? 1 : 0, user_ids: val.user_list };
			this.indexCode = this.from?.indexCode || '';
			console.log(this.form);
			// if (this.form.indexCode) {
			//   this.$refs['searchComponentIndex'].setindexCode(this.form.indexCode)
			// }
			this.$nextTick(() => {
				this.$refs['searchComponentIndex'].setDefaultValue(this.form.indexCode);
			});
		},
		getFundInfo(val) {
			this.form.indexCode = val.id;
		},
		async submit() {
			let data = await putPoolInfo({
				id: String(this.form.id),
				name: this.form.name,
				description: this.form.description,
				status: this.form.status == '--' ? 0 : this.form.status,
				index_code: this.form.indexCode,
				status3: this.form.status3,
				userlist: this.form.user_ids,
				ispublic: this.form.ispublic,
				ismanager: this.ismanager,
				user_permission: []
			});
			this.$message.success('转发成功');
			this.visible = false;
			this.$emit('resolveFather');
		}
	}
};
</script>

<style></style>
