<!--  -->
<template>
	<div class="">
		<div style="display: flex">
			<div class="widbnox">
				<el-select v-model="rank" placeholder="请选择">
					<el-option v-for="item in optionsreturn" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>
			</div>
			<div class="tiptablebox">
				<!-- <div style='margin-left:10px;' v-for='(item,i) in buttonarray'>
                    <el-button :class="[nowindex == i?'backstyle':'']" @click="setit(item.lbound,item.ubound,i)">{{item.lbound}}-{{item.ubound}}</el-button>
                </div> -->
				<div style="margin-left: 10px">
					<el-popover placement="right">
						<i slot="reference" class="el-icon-edit"></i>
						<div style="display: flex">
							<div>
								<el-input type="number" placeholder="e.g. 0.1" v-model="value1" class="inputbox"></el-input>
							</div>
							<div style="display: flex; align-self: center">&nbsp; ~ &nbsp;</div>
							<div>
								<el-input type="number" placeholder="e.g. 0.3" v-model="value2" class="inputbox"></el-input>
							</div>
						</div>
					</el-popover>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';

export default {
	props: {
		rankvalue: {
			type: String,
			default: '100'
		},
		name: {
			type: String,
			default: ''
		},
		indexid: {
			type: Number,
			default: 1.1
		},
		buttonarray: {
			type: Array,
			default: []
		},
		valuef: {
			type: Number,
			default: 0
		},
		valueb: {
			type: Number,
			default: 0
		}
	},
	//import引入的组件需要注入到对象中才能使用
	components: {},
	data() {
		//这里存放数据
		return {
			optionsreturn: [
				{
					value: '100',
					label: '全部'
				},
				{
					value: '1',
					label: 'TOP 1%'
				},
				{
					value: '5',
					label: 'TOP 5%'
				},
				{
					value: '10',
					label: 'TOP 10%'
				},
				{
					value: '25',
					label: 'TOP 25%'
				},
				{
					value: '50',
					label: 'TOP 50%'
				},
				{
					value: '70',
					label: 'TOP 70%'
				},
				{
					value: '80',
					label: 'TOP 80%'
				}
			],
			value1: null,
			value2: null,
			rank: '100',
			nowindex: null
		};
	},
	//监听属性 类似于data概念
	computed: {},
	//监控data中的数据变化
	watch: {
		value1() {
			if (Number(this.value1) == Number(this.valuef)) {
			} else {
				this.$emit('setvalue1', this.value1, this.indexid);
			}
		},
		value2() {
			if (Number(this.value2) == Number(this.valueb)) {
			} else {
				this.$emit('setvalue2', this.value2, this.indexid);
			}
		},
		rank() {
			this.$emit('setrank', this.rank, this.indexid);
		}
	},
	//方法集合
	methods: {
		setit(start, end, index) {
			this.value1 = start;
			this.value2 = end;
			this.nowindex = index;
			// //console.log(';rer')
			// //console.log(this.value1)
		}
		// setstart(){
		//     //console.log(this.value1)

		// },
		// setend(){
		//     //console.log(this.value2)
		//     this.$emit('setvalue2', this.value2,this.indexid);
		// },
	},
	//生命周期 - 创建完成（可以访问当前this实例）
	created() {
		//console.log('dsad');
		//console.log(this.buttonarray);
		this.value1 = this.valuef;
		this.value2 = this.valueb;
		this.rank = this.rankvalue;
		//   //console.log(this.buttonarray)
	},
	//生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	beforeCreate() {}, //生命周期 - 创建之前
	beforeMount() {}, //生命周期 - 挂载之前
	beforeUpdate() {}, //生命周期 - 更新之前
	updated() {}, //生命周期 - 更新之后
	beforeDestroy() {}, //生命周期 - 销毁之前
	destroyed() {}, //生命周期 - 销毁完成
	activated() {} //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style>
.widbnox {
	width: 130px;
}
.backstyle {
	background: #409eff;
	color: white;
}
</style>
