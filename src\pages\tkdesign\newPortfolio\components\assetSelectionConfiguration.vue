<template>
	<div class="asset-selection1-wrapper" v-loading="loading">
		<div class="asset-selection-content">
			<div class="asset-selection-header">
				选择大类资产指数 <el-button class="header-btn" @click="showOptionConfigDialog">配置大类资产指数</el-button>
			</div>
			<div>
				<TheTransferBox
					:optionList="optionList"
					:searchValue.sync="searchInput"
					showCount
					v-model="checkoutList"
					:searchInputConfig="searchInputConfig"
					:resultTableHeader="resultTableHeader"
					navTitle="大类资产指数"
				></TheTransferBox>
			</div>
			<div class="asset-selection-footer">
				<div>
					<!-- *最多选择5个大类资产指数 -->
				</div>
				<el-button type="text" @click="handleAssetAnalysisVisible">大类资产指数分析预览<i class="el-icon-arrow-right"></i></el-button>
			</div>
			<el-dialog title="大类资产指数" width="1200px" :visible.sync="dialogAssetAnalysisVisible">
				<TheAssetAnalysis v-if="dialogAssetAnalysisVisible" marketType="index" flag="6" :codeList="codeList"></TheAssetAnalysis>
			</el-dialog>
		</div>
		<div class="btn-footer-wrapper">
			<el-button style="margin-top: 12px" @click="handleBack">取消</el-button>
			<el-button type="primary" style="margin-top: 12px" @click="handleNext">下一步</el-button>
		</div>
		<el-dialog title="配置大类资产指数" :visible.sync="optionConfigDialogVisiable">
			<div>
				<TheAssetsConfig :optionList.sync="optionEditList"></TheAssetsConfig>
			</div>
			<div slot="footer">
				<el-button @click="optionConfigDialogVisiable = !optionConfigDialogVisiable">取消</el-button>
				<el-button type="primary" @click="handleSaveConfig">保存应用</el-button>
			</div>
		</el-dialog>
	</div>
</template>
<script>
import TheAssetAnalysis from './TheAssetAnalysis.vue';
import TheTransferBox from './components/TheTransferBox.vue';
import TheAssetsConfig from './components/TheAssetsConfig.vue';
import { getMajorAssetIndexlist, updateMajorAssetType } from '@/api/pages/tkAnalysis/portfolio.js';
export default {
	name: 'assetSelectionConfiguration',
	components: {
		TheAssetAnalysis,
		TheTransferBox,
		TheAssetsConfig
	},
	data() {
		return {
			dialogAssetAnalysisVisible: false,
			searchInput: '',
			checkedCities: [],
			optionConfigDialogVisiable: false, //配置大类资产指数弹窗是否展示
			//搜索时需要备份的数据
			optionList: [],
			//配置大类资产指数时使用的列表
			optionEditList: [],
			//右侧列表展示头部和属性
			resultTableHeader: [
				{ prop: 'typeLabel', label: '指数类别' },
				{ prop: 'name', label: '指数名称' }
			],
			checkoutList: [],
			searchInputConfig: {
				label: '搜索指数：',
				placeholder: '请输入名称搜索指数',
				searchName: 'name'
			},
			loading: false
		};
	},
	// created() {
	// 	this.getDataList();
	// },
	computed: {
		codeList() {
			let list =
				this.checkoutList.map((item) => {
					return item.code;
				}) || [];
			//去重处理
			list = Array.from(new Set(list));
			return list;
		}
	},
	methods: {
		//替代created时机 由外部触发
		initData() {
			this.getDataList();
		},
		//大类资产指数分析预览弹窗展示
		handleAssetAnalysisVisible() {
			if (!(this.checkoutList && this.checkoutList.length > 0)) {
				this.$message.warning('请选择指数');
				return;
			}
			this.dialogAssetAnalysisVisible = true;
		},
		//搜索指数
		// handelSearch(value) {
		// 	this.optionList = this.optionList.filter((item) => {
		// 		item.children.forEach((childrenItem) => {
		// 			if (childrenItem.indexName === value) {
		// 				return true;
		// 			}
		// 			return false;
		// 		});
		// 	});
		// },
		//展示配置大类资产指数弹窗
		showOptionConfigDialog() {
			this.optionEditList = this.FUNC.deepClone(this.optionList);
			this.optionConfigDialogVisiable = true;
		},
		async getDataList() {
			this.loading = true;
			let params = {};
			let reqData = await getMajorAssetIndexlist(params);
			let { data = [], mtycode, mtymessage } = reqData || {};
			if (mtycode == 200) {
				this.optionList = data.map((item) => {
					item = {
						...item,
						typeId: item.type, //内部结构需要
						typeLabel: item.type //内部结构需要
					};
					item.children = item.codeList || [];
					item.children?.forEach((childrenItem) => {
						childrenItem.childrenId = childrenItem.code;
						childrenItem.childrenLabel = childrenItem.name;
						childrenItem.value = {
							//将外层字段放进选中结果的value中
							typeId: item.type, //内部结构需要
							typeLabel: item.type, //内部结构需要
							...childrenItem
						};
					});
					return item;
				});
			} else {
				this.$message.warning(mtymessage);
			}
			this.loading = false;
		},
		//保存资产大类配置
		async handleSaveConfig() {
			this.searchInput = '';
			this.optionConfigDialogVisiable = false;
			this.optionList = this.FUNC.deepClone(this.optionEditList);
			this.optionList = this.optionList.map((item) => {
				item = {
					...item,
					typeId: item.type, //内部结构需要
					typeLabel: item.type //内部结构需要
				};
				item.children?.forEach((childrenItem) => {
					childrenItem.childrenId = childrenItem.code;
					childrenItem.childrenLabel = childrenItem.name;
					childrenItem.value = {
						//将外层字段放进选中结果的value中
						typeId: item.type, //内部结构需要
						typeLabel: item.type, //内部结构需要
						...childrenItem
					};
				});
				//移除名称的异常数据
				item.children =
					item.children?.filter((childrenItem) => {
						return !!childrenItem.childrenLabel;
					}) || [];
				return item;
			});
			//清空右侧已选指数
			this.checkoutList = [];
			//修改大类资产指数模板
			let objectModel = this.optionList.map((item) => {
				let codeList = item?.children?.map((children) => {
					return {
						code: children.code,
						name: children.name
					};
				});
				return {
					type: item.type,
					codeList
				};
			});
			let params = { objectModel };
			let reqData = await updateMajorAssetType(params);
			let { mtycode, mtymessage } = reqData || {};
			if (mtycode == 200) {
			} else {
				this.$message.warning(mtymessage);
			}
		},
		handleNext() {
			if (!(this.checkoutList && this.checkoutList.length > 0)) {
				this.$message.warning('请选择指数');
				return;
			}
			// console.log('***', this.checkoutList);
			const list = this.checkoutList.map((item) => {
				return {
					code: item.code,
					name: item.name
				};
			});
			this.$emit('nextStep', {
				selectList: list
			});
		},
		handleBack() {
			this.$emit('backStep');
		}
	}
};
</script>
<style lang="scss" scoped>
.transfer-box-wrapper {
	display: flex;
	.first-type-wrapper {
		::v-deep .el-tabs__nav-scroll {
			padding: 0 16px;
			.el-tabs__nav-wrap {
				&::after {
					content: unset;
				}
			}
		}
	}
	.transfer-left {
		width: 480px;
		border: 1px solid #e9e9e9;
		border-radius: 4px;
		.left-nav-wrapper {
			::v-deep .el-tabs__header {
				.el-tabs__item {
					text-align: left;
				}
			}
		}
		.transfer-left-title {
			display: flex;
			padding: 8px 16px;
			align-items: center;
			border-bottom: 1px solid #e9e9e9;
			.label {
				color: rgba(0, 0, 0, 0.85);
				font-size: 14px;
				font-style: normal;
				font-weight: 400;
				line-height: 22px; /* 157.143% */
				word-break: keep-all;
			}
		}
	}
	.transfer-center {
		display: flex;
		flex-direction: column;
		padding: 0 20px;
		justify-content: center;
		align-items: center;
	}
	.transfer-right {
		border: 1px solid #e9e9e9;
		border-radius: 4px;
		.transfer-right-title {
			padding: 10px 16px;
			color: #000;
			font-size: 14px;
			font-style: normal;
			font-weight: 400;
			line-height: 22px; /* 157.143% */
		}
	}
}
.asset-selection1-wrapper {
	background-color: #ffffff;
	border-radius: 4px;
	.asset-selection-content {
		padding: 0 24px;
	}
	.asset-selection-header {
		color: rgba(0, 0, 0, 0.85);
		font-size: 16px;
		font-style: normal;
		font-weight: 500;
		line-height: 24px; /* 150% */
		padding: 16px 0;
		.header-btn {
			margin-left: 20px;
		}
	}
}
.asset-selection-footer {
	display: flex;
	justify-content: space-between;
	padding: 8px 0;
	color: rgba(0, 0, 0, 0.45);
	font-size: 12px;
	font-weight: 400;
	line-height: 20px; /* 166.667% */
}
.btn-footer-wrapper {
	padding: 16px 24px;
	border-top: 1px solid #e9e9e9;
}
</style>
