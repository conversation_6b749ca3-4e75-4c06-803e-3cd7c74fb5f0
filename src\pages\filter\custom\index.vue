<template>
	<div class="chart_one">
		<el-tabs v-model="activeName" @tab-click="handleClick">
			<el-tab-pane v-for="item in active_list" :key="item.value" :label="item.label" :name="item.value">
				<component :is="item.value" :ref="item.value"></component>
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script>
import customTag from './components/customTag.vue';
import customIndex from './components/customIndex.vue';
import customScene from './components/customScene.vue';
import customAnalysisDate from './components/customAnalysisDate.vue';
import customEvaluate from './components/customEvaluate.vue';
export default {
	components: { customTag, customIndex, customScene, customAnalysisDate, customEvaluate },
	data() {
		return {
			activeName: 'customEvaluate',
			active_list: [
				{
					label: '自定义标签',
					value: 'customTag'
				},
				{
					label: '自定义基准',
					value: 'customIndex'
				},
				{
					label: '自定义情景',
					value: 'customScene'
				},
				{
					label: '自定义时间段分析',
					value: 'customAnalysisDate'
				},
				{
					label: '自定义评价',
					value: 'customEvaluate'
				}
			]
		};
	},
	mounted() {
		this.handleClick();
	},
	methods: {
		handleClick() {
			this.$refs[this.activeName]?.[0].getData();
		}
	}
};
</script>

<style></style>
