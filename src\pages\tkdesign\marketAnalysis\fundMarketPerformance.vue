<template>
	<div class="box_Board">
		<div class="header_box"><span class="header_unactive">市场分析&nbsp;/&nbsp;</span>基金市场业绩<span></span></div>
		<TheFundPerformanceBoardPlate></TheFundPerformanceBoardPlate>
		<TheFundPerformancePlate></TheFundPerformancePlate>
		<TheFundPerformanceQuantilePlate></TheFundPerformanceQuantilePlate>
		<!-- 2024-01-17 bug文档对应181行 隐藏 -->
		<!-- <TheFundManagersPerformanceBoardPlate></TheFundManagersPerformanceBoardPlate> -->
		<TheFundManagersPerformancePlate></TheFundManagersPerformancePlate>
		<TheFundManagersPerformanceQuantilePlate></TheFundManagersPerformanceQuantilePlate>
		<TheFundCompanyPerformanceQuantilePlate></TheFundCompanyPerformanceQuantilePlate>
	</div>
</template>
<script>
import '@/pages/assets/css/page-container.scss';
import TheFundPerformanceBoardPlate from './component/TheFundPerformanceBoardPlate.vue';
import TheFundPerformancePlate from './component/TheFundPerformancePlate.vue';
import TheFundPerformanceQuantilePlate from './component/TheFundPerformanceQuantilePlate.vue';
import TheFundManagersPerformanceBoardPlate from './component/TheFundManagersPerformanceBoardPlate.vue';
import TheFundManagersPerformancePlate from './component/TheFundManagersPerformancePlate.vue';
import TheFundManagersPerformanceQuantilePlate from './component/TheFundManagersPerformanceQuantilePlate.vue';
import TheFundCompanyPerformanceQuantilePlate from './component/TheFundCompanyPerformanceQuantilePlate.vue';
export default {
	components: {
		TheFundPerformanceBoardPlate,
		TheFundPerformancePlate,
		TheFundPerformanceQuantilePlate,
		TheFundManagersPerformanceBoardPlate,
		TheFundManagersPerformancePlate,
		TheFundManagersPerformanceQuantilePlate,
		TheFundCompanyPerformanceQuantilePlate
	}
};
</script>
<style scoped></style>
