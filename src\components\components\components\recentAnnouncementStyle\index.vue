<template>
	<div id="recentAnnouncementStyle">
		<div v-loading="loading">
			<analysis-card-title title="近期公告风格" image_id="recentAnnouncementStyle"></analysis-card-title>
			<div class="flex_start">
				<div style="flex: 1">
					<v-chart
						style="width: 100%; height: 303px"
						ref="recentAnnouncementStyle"
						autoresize
						v-loading="loading"
						element-loading-text="暂无数据"
						element-loading-spinner="el-icon-document-delete"
						element-loading-background="rgba(239, 239, 239, 0.5)"
						:options="jincioption"
					/>
				</div>
				<div style="flex: 1; text-align: center; display: flex; flex-direction: column; justify-content: center">
					<div style="position: relative">
						<el-progress
							type="circle"
							:percentage="pilu_gupiao"
							width="258"
							color="#4096ff"
							:stroke-width="20"
							:show-text="false"
						></el-progress>
						<div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%" class="flex_center">
							<div>
								<div class="stock_hold_weight_name">股票仓位</div>
								<div class="flex_start">
									<div class="mr-4 stock_hold_weight_value">
										{{ pilu_gupiao ? pilu_gupiao : '暂无' }}
									</div>
									<div class="stock_hold_weight_rank">
										{{ pilu_gupiao ? '%' : '' }}
									</div>
								</div>
								<div
									v-if="!isNaN(parseFloat(pilu_gupiao_change))"
									class="flex_start stock_hold_weight_change"
									:style="`color: ${pilu_gupiao_change > 0 ? 'red' : '#389e0d'}`"
								>
									<div class="mr-4">{{ pilu_gupiao_change + '%' }}</div>
									<div v-show="pilu_gupiao_change != 0" :style="`color: ${pilu_gupiao_change > 0 ? 'red' : '#389e0d'}`">
										<!-- {{ pilu_gupiao_change > 0 ? '↑' : '↓' }} -->
										<div v-if="pilu_gupiao_change > 0">
											<i class="el-icon-caret-top"></i>
										</div>
										<div v-else>
											<i class="el-icon-caret-bottom"></i>
										</div>
									</div>
								</div>
								<div v-else>暂无数据</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { exportTitle, exportChart, exportDescripe } from '@/utils/exportWord.js';
import { getCapabilityInfo, getAllocationDetails } from '@/api/pages/Analysis.js';
// 近期公告风格
export default {
	name: 'recentAnnouncementStyle',
	data() {
		return {
			jincioption: {},
			pilu_gupiao: null,
			pilu_gupiao_change: null,
			loading: false,
			info: {}
		};
	},
	methods: {
		// 获取能力
		async getCapabilityInfo() {
			let data = await getCapabilityInfo({
				codes: [this.info.code],
				type: this.info.type,
				flag: [this.info.flag],
				start_date: this.info.start_date,
				end_date: this.info.end_date,
				item: ['残差波动率']
			});
			console.log('getCapabilityInfo', data);
			if (data?.mtycode == 200) {
				let recentAnnouncement = {};
				recentAnnouncement.pilu_beta = data?.data?.find((v) => v.item == '贝塔')?.description || '暂无';
				recentAnnouncement.pilu_guimo = data?.data?.find((v) => v.item == '规模')?.description || '暂无';
				recentAnnouncement.pilu_jiazhi = data?.data?.find((v) => v.item == '价值')?.description || '暂无';
				recentAnnouncement.pilu_chengzhang = data?.data?.find((v) => v.item == '成长')?.description || '暂无';
				recentAnnouncement.pilu_yingli = data?.data?.find((v) => v.item == '盈利')?.description || '暂无';
				recentAnnouncement.pilu_cancha = data?.data?.find((v) => v.item == '残差波动率')?.description || '暂无';
				recentAnnouncement.pilu_ganggan = data?.data?.find((v) => v.item == '杠杆')?.description || '暂无';
				recentAnnouncement.pilu_liudongxing = data?.data?.find((v) => v.item == '流动性')?.description || '暂无';
				recentAnnouncement.pilu_guzhi = data?.data?.find((v) => v.item == '估值')?.description || '暂无';
				recentAnnouncement.pilu_dongliang = data?.data?.find((v) => v.item == '动量')?.description || '暂无';
				this.getChartData(recentAnnouncement);
			}
		},
		async getAllocationDetails() {
			let data = await getAllocationDetails({
				code: this.info.code,
				type: this.info.type,
				flag: this.info.flag,
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			if (data?.mtycode == 200) {
				console.log('getAllocationDetails', data);
				this.getStock(data?.data);
			}
		},
		async getData(info) {
			this.info = info;
			await this.getCapabilityInfo();
			await this.getAllocationDetails();
		},
		getChartData(data) {
			this.loading = false;
			let tempjinci = [];
			let {
				pilu_beta,
				pilu_guimo,
				pilu_jiazhi,
				pilu_chengzhang,
				pilu_yingli,
				pilu_cancha,
				pilu_guzhi,
				pilu_ganggan,
				pilu_dongliang,
				pilu_liudongxing
			} = data;
			let temparrjinci = [
				pilu_beta,
				pilu_guimo,
				pilu_jiazhi,
				pilu_chengzhang,
				pilu_yingli,
				pilu_cancha,
				pilu_guzhi,
				pilu_ganggan,
				pilu_dongliang,
				pilu_liudongxing
			];
			if (temparrjinci.find((item) => item !== '暂无')) {
				for (let k = 0; k < temparrjinci.length; k++) {
					if (temparrjinci[k] == '低' || temparrjinci[k] == '低') {
						tempjinci.push(25);
					} else if (temparrjinci[k] == '差' || temparrjinci[k] == '偏低') {
						tempjinci.push(40);
					} else if (temparrjinci[k] == '中') {
						tempjinci.push(60);
					} else if (temparrjinci[k] == '良' || temparrjinci[k] == '偏高') {
						tempjinci.push(80);
					} else if (temparrjinci[k] == '优' || temparrjinci[k] == '高' || temparrjinci[k] == '极高') {
						tempjinci.push(100);
					} else {
						tempjinci.push(0);
					}
				}
				this.jincioption = {
					grid: {},
					radar: {
						splitLine: {
							// (这里是指所有圆环)坐标轴在 grid 区域中的分隔线。
							lineStyle: {
								color: '#A1A1A1',
								// 分隔线颜色
								width: 1,
								// 分隔线线宽
								type: [5]
							}
						},
						splitArea: {
							// 坐标轴在 grid 区域中的分隔区域，默认不显示。
							show: true,
							areaStyle: {
								// 分隔区域的样式设置。
								color: ['rgb(255,255,255)']
								// color: ['rgb(246,250,255)', 'rgb(246,250,255)']
								// 分隔区域颜色。分隔区域会按数组中颜色的顺序依次循环设置颜色。默认是一个深浅的间隔色。
							}
						},
						axisLine: {
							// (圆内的几条直线)坐标轴轴线相关设置
							lineStyle: {
								color: '#A1A1A1',
								// 坐标轴线线的颜色。
								width: 1,
								// 坐标轴线线宽。
								type: 'solid'
								// 坐标轴线线的类型。
							}
						},
						center: ['center', 'center'],
						// radius: '70px',
						name: {
							textStyle: {
								fontSize: '12px',
								color: 'rgba(0,0,0,0.65)',
								fontWeight: 400
							}
						},
						// shape: 'circle',
						indicator: [
							{
								name: '贝塔',
								max: 100
							},
							{
								name: '规模要求',
								max: 100
							},
							{
								name: '价值',
								max: 100
							},
							{
								name: '成长',
								max: 100
							},
							{
								name: '盈利',
								max: 100
							},
							{
								name: '残差波动',
								max: 100
							},
							{
								name: '估值',
								max: 100
							},
							{
								name: '杠杆',
								max: 100
							},
							{
								name: '动量',
								max: 100
							},
							{
								name: '流动性',
								max: 100
							}
						]
					},
					series: [
						{
							itemStyle: {
								// 单个拐点标志的样式设置。
								normal: {
									color: '#ffffff',
									borderColor: '#4096ff',
									// 拐点的描边颜色。[ default: '#000' ]
									borderWidth: 2
									// 拐点的描边宽度，默认不描边。[ default: 0 ]
								}
							},
							lineStyle: {
								// 单项线条样式。
								normal: {
									color: '#4096ff'
								}
							},
							areaStyle: {
								// 单项区域填充样式
								normal: {
									color: '#ecf5ff' // 填充的颜色。[ default: "#000" ]
								}
							},
							name: '基金经理能力',
							type: 'radar',
							data: [
								{
									value: tempjinci,
									name: '基金经理能力'
								}
							]
						}
					]
				};
			}
			this.loading = false;
		},
		getStock(data) {
			let new_equity = data.sort((a, b) => {
				return this.moment(this.moment(a.yearqtr, 'YYYY QQ').format()).isAfter(this.moment(b.yearqtr, 'YYYY QQ').format()) ? -1 : 1;
			})?.[0]?.equityWeight;
			let last_equity = data.sort((a, b) => {
				return this.moment(this.moment(a.yearqtr, 'YYYY QQ').format()).isAfter(this.moment(b.yearqtr, 'YYYY QQ').format()) ? -1 : 1;
			})?.[1]?.equityWeight;
			if (new_equity) {
				this.pilu_gupiao = Number(new_equity).toFixed(2);
			}
			if (last_equity && new_equity) {
				this.pilu_gupiao_change = Number(last_equity - new_equity).toFixed(2);
			}
		},
		exportImage() {
			this.html2canvas(document.getElementById('recentAnnouncementStyle'), {
				scale: 3
			}).then(function (canvas) {
				let base64Str = canvas.toDataURL('image/png');
				let aLink = document.createElement('a');
				aLink.style.display = 'none';
				aLink.href = base64Str;
				aLink.download = '近期公告风格.jpg';
				// 触发点击-然后移除
				document.body.appendChild(aLink);
				aLink.click();
				document.body.removeChild(aLink);
			});
		},
		async createPrintWord(info) {
			await this.getData(info);
			return await new Promise((resolve, reject) => {
				this.$nextTick(async () => {
					let height = this.$refs['recentAnnouncementStyle'].$el.clientHeight || 405;
					let width = this.$refs['recentAnnouncementStyle'].$el.clientWidth || 828;
					let chart = this.$refs['recentAnnouncementStyle'].getDataURL({
						type: 'jpg',
						pixelRatio: 3,
						backgroundColor: '#fff'
					});
					let text =
						`当前季度股票仓位${this.pilu_gupiao ? this.pilu_gupiao + '%' : '--'},较上季度变化` +
						(this.pilu_gupiao_change > 0 ? '上涨' : '下降') +
						this.pilu_gupiao_change +
						'%';
					resolve([...exportTitle('近期公告风格'), ...exportChart(chart, { width, height }), ...exportDescripe(text)]);
				});
			});
		}
	}
};
</script>

<style lang="scss" scoped>
#recentAnnouncementStyle {
	.stock_hold_weight_name {
		color: rgba(0, 0, 0, 0.45);
		font-family: PingFang SC;
		font-size: 26px;
		font-style: normal;
		font-weight: 400;
	}
	.stock_hold_weight_value {
		color: #4096ff;
		font-family: Helvetica Neue;
		font-size: 40px;
		font-style: normal;
		font-weight: 500;
		line-height: normal;
	}
	.stock_hold_weight_rank {
		color: rgba(0, 0, 0, 0.45);
		font-family: Helvetica Neue;
		font-size: 24px;
		font-style: normal;
		font-weight: 500;
		line-height: normal;
	}
	.stock_hold_weight_change {
		color: #389e0d;
		font-family: Helvetica Neue;
		font-size: 16px;
		font-style: normal;
		font-weight: 500;
	}
	::v-deep .echarts {
		div {
			width: 100% !important;
			max-width: 394px;
			min-width: 285px;
		}
	}
}
</style>
