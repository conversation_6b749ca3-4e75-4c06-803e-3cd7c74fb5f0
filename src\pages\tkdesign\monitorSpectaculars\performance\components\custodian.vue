<template>
  <div v-loading="loadingTable"
       class="border_table">
    <!-- 头部区域 -->
    <div class="border_table_header">
      <!-- 右侧标题区域 -->
      <div class="border_table_header_title">
        <div class="block" />
        <div>分管理人业绩</div>
      </div>
      <!-- 左侧筛选区域 -->
      <div class="border_table_header_filter">

        <!-- 单选框 -->
        <div class="border_table_header_radio">
          <el-radio v-model="oldChecked"
                    label="true"
                    @click.native="changeRadioOld($event)">剔除税延及专属商业养老</el-radio>
        </div>
        <!-- <div class="border_table_header_radio">
          <el-radio v-model="checked"
                    label="true"
                    @click.native="changeRadio($event)">税费后</el-radio>
        </div> -->
        <!-- 选择框 -->
        <!-- <div class="border_table_header_select">
          账户选择：
          <el-select v-model="accountSearch"
                     placeholder="请选择"
                     @change="getSpectacularsManager">
            <el-option v-for="(item, index) in accountOptions"
                       :key="index"
                       :value="item.value"
                       :label="item.name">
            </el-option>
          </el-select>
        </div> -->
        <!-- 下载 -->
        <div class="border_table_header_upload"
             @click="downloadExcel()">
          <i class="el-icon-download"></i>
        </div>
      </div>
    </div>
    <!-- 表格区域 -->
    <el-table :data="tableData"
              class="kanbantable"
              border
              stripe>
      <el-table-column :show-overflow-tooltip="true"
                       align="gotoleft"
                       width="200"
                       label=""
                       fixed="left"
                       prop="name" />
      <el-table-column v-for="(item,index) in column"
                       :key="index"
                       width="150"
                       align="center"
                       :label="item.name"
                       :prop="item.key" />
      <template slot="empty">
        <el-empty :image-size="160"></el-empty>
      </template>
    </el-table>

  </div>
</template>

<script>
import { getDataBoardQueryConfig, getSpectacularsManager } from '@/api/pages/tkdesign/performance'
import { filter_json_to_excel_inside, changColumnToRow, filter_json_to_excel_inside_multiHeader } from '@/utils/exportExcel.js';

export default {
  props: {
    time: {
      type: Array,
    }
  },
  data () {
    return {
      // 搜索区域input绑定的数据
      accountSearch: '',
      accountOptions: [],
      checked: false,
      oldChecked: false,
      tableData: [], //表格数据源
      column: [],
      nowUploadDate: '',
      loadingTable: false,
    };
  },
  watch: {
    time () {
      this.getSpectacularsManager()
    }
  },
  mounted () {
    this.getDataBoardQueryConfig()
    // this.init()
  },
  methods: {
    init () {
      this.getSpectacularsManager()
    },
    downloadExcel () {
      const title = [
        { label: '', value: 'name', format: '' },
      ];
      this.column && this.column.map(col => {
        const temp = {
          label: col.name,
          value: col.key,
        }
        title.push(temp);
      })
      filter_json_to_excel_inside(title, this.tableData, [], '分管理人业绩');
    },
    /**
     * 给某一单元格添加样式
     * @param row
     * @param column
     * @param rowIndex
     * @param columnIndex
     */
    cellStyle ({ row, column, rowIndex, columnIndex }) {
      if (rowIndex === 0 && columnIndex === 1)
        return {
          backgroundImage: 'linear-gradient(90deg, rgba(242, 208, 211,1) 0%, rgba(0, 0, 0, 0) 100%)',
        }
      if (rowIndex === 0 && columnIndex === 2)
        return {
          backgroundImage: 'linear-gradient(90deg, rgba(218, 236, 207,1) 0%, rgba(218, 236, 207,0) 100%)',
        }
    },

    /**
     * 单选框选中处理
     */
    changeRadio (event) {
      if (event.target.tagName === 'INPUT') {
        this.checked = !this.checked
        this.getSpectacularsManager()
      }
    },
    changeRadioOld (event) {
      if (event.target.tagName === 'INPUT') {
        this.oldChecked = !this.oldChecked
        this.getSpectacularsManager()
      }
    },

    /**
     * 获取账户配置
     */
    getDataBoardQueryConfig () {
      getDataBoardQueryConfig({}).then(res => {
        if (res && (res.mtycode == 200 || res.code == 200)) {
          res.data?.accountList?.forEach(v => {
            this.accountOptions.push(v)
          })
        }
      })
    },
    handDataForm (data, flag) {
      let result = ''
      if (data === '' || data === 'NaN' || data === undefined) {
        return '--'
      }
      if (flag == '2p') {
        result = (data * 1).toFixed(2) + '%'
      }
      else if (flag == '2') {
        result = (data * 1).toFixed(2)
      }
      else if (flag == 'Y') {
        result = (data * 1).toFixed(2)
      }
      return result
    },
    /**
     * 数据临时处理
     */
    handData (arr) {
      // console.log(arr, 'sss');
      let result = []
      // 遍历数组中的每个对象
      for (let i = 0; i < arr.length; i++) {
        const obj = arr[i];
        // 遍历当前对象的键值对
        for (let key in obj) {
          // 构建新对象的键
          let newKey = `key${i}`;
          // 如果新对象中没有该键，则添加该键并设置初始值为空字符串
          if (!result[key]) {
            result[key] = {};
          }
          var space = '\xa0\xa0\xa0\xa0\xa0\xa0'
          if (key === 'financeCwrr') {
            if (!result[key].name) {
              result[key].name = '财务CWRR值'
              result[key].sort = 1
            }
            result[key][newKey] = this.handDataForm(obj[key], '2p');
          }
          if (key === 'marketCwrrValue') {
            if (!result[key].name) {
              result[key].name = '市值CWRR值'
              result[key].sort = 2
            }
            result[key][newKey] = this.handDataForm(obj[key], '2p');
          }
          if (key === 'marketCwrrValueBenchmark') {
            if (!result[key].name) {
              result[key].name = `市值CWRR业绩比较基准`
              result[key].sort = 3
            }
            result[key][newKey] = this.handDataForm(obj[key], '2p');
          }
          if (key === 'marketCwrrValueExcess') {
            if (!result[key].name) {
              result[key].name = `市值CWRR超额`
              result[key].sort = 4
            }
            result[key][newKey] = this.handDataForm(obj[key], '2p');
          }
          if (key === 'marketTwrrValue') {
            if (!result[key].name) {
              result[key].name = '市值TWRR'
              result[key].sort = 5
            }
            result[key][newKey] = this.handDataForm(obj[key], '2p');
          }
          if (key === 'marketTwrrValueBenchmark') {
            if (!result[key].name) {
              result[key].name = `市值TWRR业绩比较基准`
              result[key].sort = 6
            }
            result[key][newKey] = this.handDataForm(obj[key], '2p');
          }
          if (key === 'marketTwrrValueExcess') {
            if (!result[key].name) {
              result[key].name = `市值TWRR超额`
              result[key].sort = 7
            }
            result[key][newKey] = this.handDataForm(obj[key], '2p');
          }
          if (key === 'financialGain') {
            if (!result[key].name) {
              result[key].name = '财务收益(亿)'
              result[key].sort = 8
            }
            result[key][newKey] = this.handDataForm(obj[key], 'Y');
          }
          if (key === 'financialGainBefore') {
            if (!result[key].name) {
              result[key].name = `财务收益（扣除融资成本前）(亿)`
              result[key].sort = 8.5
            }
            result[key][newKey] = this.handDataForm(obj[key], 'Y');
          }
          if (key === 'marketGain') {
            if (!result[key].name) {
              result[key].name = '市值收益(亿)'
              result[key].sort = 9
            }
            result[key][newKey] = this.handDataForm(obj[key], 'Y');
          }

          if (key === 'marketGainBefore') {
            if (!result[key].name) {
              result[key].name = `市值收益（扣除融资成本前）(亿)`
              result[key].sort = 9.5
            }
            result[key][newKey] = this.handDataForm(obj[key], 'Y');
          }
          if (key === 'gainOut') {
            if (!result[key].name) {
              result[key].name = `融资支出`
              result[key].sort = 12
            }
            result[key][newKey] = this.handDataForm(obj[key], '2');
          }
          if (key === 'totalAfsUnrealized') {
            if (!result[key].name) {
              result[key].name = '累计AFS浮盈(亿)'
              result[key].sort = 13
            }
            result[key][newKey] = this.handDataForm(obj[key], 'Y');
          }

          if (key === 'actualAllocation') {
            if (!result[key].name) {
              result[key].name = '实际配置'
              result[key].sort = 14
            }
            result[key][newKey] = this.handDataForm(obj[key], '2');
          }
          if (key === 'averageOverUnderAllocation') {
            if (!result[key].name) {
              result[key].name = '平均超欠配'
              result[key].sort = 15
            }
            result[key][newKey] = this.handDataForm(obj[key], '2');
          }
          if (key === 'averageFundsEmployed') {
            if (!result[key].name) {
              result[key].name = `平均资金占用`
              result[key].sort = 16
            }
            result[key][newKey] = this.handDataForm(obj[key], '2');
          }
          if (key === 'averageOverAllocationRatio') {
            if (!result[key].name) {
              result[key].name = `平均超配比例`
              result[key].sort = 17
            }
            result[key][newKey] = this.handDataForm(obj[key], '2p');
          }
          if (key === 'marketGainAfsUnrealized') {
            if (!result[key].name) {
              result[key].name = 'AFS浮盈变动(亿)'
              result[key].sort = 13.5
            }
            result[key][newKey] = this.handDataForm(obj[key], 'Y');
          }
          // if (key === 'gainOut') {
          //   if (!result[key].name) {
          //     result[key].name = `融资支出`
          //     result[key].sort = 17
          //   }
          //   result[key][newKey] = obj[key];
          // }
        }
      }
      return result
    },

    /**
     * 获取分管理人-业绩看板
     * @param commission 是否是税费后。ture；false
     * @param accountType 账户类型。在接口‘配置查询’中获取
     * @param startDate 时间区间的开始时间：2023-02-02
     * @param endDate 时间区间的结束时间：2023-02-02
     * @param exclude 是否剔除税延及专属商业养老。true；false
     */
    getSpectacularsManager () {
      this.loadingTable = true
      this.tableData = []
      this.column = []
      let params = {
        commission: this.checked,
        accountType: this.accountSearch,
        startDate: this.moment(this.time[0]).format('YYYY-MM-DD'),
        endDate: this.moment(this.time[1]).format('YYYY-MM-DD'),
        exclude: this.oldChecked,
      }
      getSpectacularsManager(params).then(res => {
        this.loadingTable = false
        if (res && (res.mtycode == 200 || res.code == 200)) {
          const arr = res.data;
          arr.forEach((item, index) => {
            this.column.push({
              name: item.columnName,
              key: `key${index}`
            })
          })
          let result = this.handData(arr)

          for (let key in result) {
            // console.log(key, result[key]);
            if (key !== 'columnName' && key !== 'marketGainOut' && key !== 'financialGainOut') {
              this.tableData.push(result[key])
            }
          }
          this.tableData.sort((n, m) => (n.sort - m.sort)) // 排序
          // console.log(this.tableData);
        }
      })
    }
  },
}
</script>
<style>
.kanbantable .el-table__fixed .cell {
	line-height: 47px !important;
}
</style>
<style lang="scss" scoped>
@import '../../../tkdesign';

.border_table_header_title {
	display: flex;
	align-items: center;

	.block {
		width: 6px;
		height: 20px;
		border-radius: 35px;
		background-color: #4096ff;
		margin-right: 16px;
	}

	.icon-question {
		margin-left: 3px;
	}
}

.border_table_header_filter {
	display: flex;
	align-items: center;
	font-size: 14px;

	.border_table_header_radio {
		display: flex;
		align-items: center;
		margin-left: 20px !important;
	}

	.border_table_header_select {
		margin-left: 16px;
	}

	.border_table_header_upload {
		width: 32px;
		line-height: 30px;
		border-radius: 4px;
		border: 1px solid #d9d9d9;
		text-align: center;
		margin-left: 16px;
	}
}
</style>

 