<template>
	<div class="chart_one" v-show="show">
		<div class="flex_card">
			<div v-loading="showempty2" class="small_template">
				<div class="title">
					What-if 假想验证: 调仓时的资产配置作对了吗<el-tooltip
						class="item"
						effect="dark"
						:content="EXPLAIN.assetPage['调仓时的资产配置作对了吗']"
						placement="right-start"
					>
						<svg width="14" height="14" viewBox="0 0 14 14" fill="none">
							<path
								fill-rule="evenodd"
								clip-rule="evenodd"
								d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
								fill="black"
								fill-opacity="0.45"
							/>
						</svg>
					</el-tooltip>
				</div>
				<div class="charts_fill_class">
					<v-chart
						ref="whatIfAssetAllocation1"
						class="charts_analysis_class"
						autoresize
						v-loading="showempty1"
						element-loading-text="暂无数据"
						element-loading-spinner="el-icon-document-delete"
						element-loading-background="rgba(239, 239, 239, 0.5)"
						:options="peizhibar"
					></v-chart>
				</div>
				<div>{{ dec1 }}</div>
			</div>
			<div v-loading="showempty2" class="small_template">
				<div class="title">What-if 假想验证: 调仓时的资产配置作对了吗(2)</div>
				<div class="charts_fill_class">
					<v-chart
						ref="whatIfAssetAllocation2"
						class="charts_analysis_class"
						autoresize
						v-loading="showempty2"
						element-loading-text="暂无数据"
						element-loading-spinner="el-icon-document-delete"
						element-loading-background="rgba(239, 239, 239, 0.5)"
						:options="peizhibar2"
					></v-chart>
				</div>
				<div>{{ dec1 }}</div>
			</div>
		</div>
	</div>
</template>

<script>
import { exportTitle, exportChart } from '@/utils/exportWord.js';
import { barChartOption, lineChartOption } from '@/utils/chartStyle.js';
// What-if 假想验证: 调仓时的资产配置作对了吗
import VChart from 'vue-echarts';
export default {
	name: 'whatIfAssetAllocation',
	components: {
		VChart
	},
	data() {
		return {
			showempty1: true,
			showempty2: true,
			dec1: '',
			peizhibar: {},
			peizhibar2: {},
			show: true
		};
	},
	methods: {
		// 获取数据
		getData(data) {
			this.show = true;
			this.dec1 = data.imaginary_nav.description;
			if (
				data.imaginary_nav.all_date == null ||
				data.imaginary_nav.all_date == [] ||
				data.imaginary_nav.all_date == '' ||
				data.imaginary_nav.all_date == {} ||
				data.imaginary_nav.yearlist == null ||
				data.imaginary_nav.yearlist == [] ||
				data.imaginary_nav.yearlist == '' ||
				data.imaginary_nav.yearlist == {}
			) {
				this.showempty1 = true;
				this.show = false;
			} else {
				this.showempty1 = false;

				let seriessss = [];
				let day_nav = data.imaginary_nav.yearlist.map((item, index) => {
					return [item, data.imaginary_nav.day_nav[index]];
				});

				seriessss.push({
					name: '管理人实际净值',
					type: 'line',
					data: day_nav.sort((a, b) => {
						return this.moment(this.moment(a[0], 'YYYY-MM-DD').format()).isAfter(this.moment(b[0], 'YYYY-MM-DD').format()) ? 1 : -1;
					})
				});
				for (let a = 0; a < data.imaginary_nav.all_date.length; a++) {
					seriessss.push({
						name: '管理人在' + data.imaginary_nav.all_date[a] + '保持持仓假想净值',
						type: 'line',
						data: data.imaginary_nav.value[a]
					});
				}

				this.peizhibar = lineChartOption({
					legend: {},
					tooltip: {
						type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
					},
					xAxis: [
						{
							data: data.imaginary_nav.yearlist.sort((a, b) => {
								return this.moment(this.moment(a, 'YYYY-MM-DD').format()).isAfter(this.moment(b, 'YYYY-MM-DD').format()) ? 1 : -1;
							})
						}
					],
					yAxis: [{ type: 'value', scale: true }],
					series: seriessss
				});
				console.log(this.peizhibar);
			}
			if (
				data.imaginary_ret.result == null ||
				data.imaginary_ret.result == [] ||
				data.imaginary_ret.result == '' ||
				data.imaginary_ret.result == {}
			) {
				this.showempty2 = true;
			} else {
				this.showempty2 = false;
				let datearr = [];
				let dataarr = [];
				for (let i = 0; i < data.imaginary_ret.result.length; i++) {
					datearr.push(data.imaginary_ret.result[i].name);
					dataarr.push(data.imaginary_ret.result[i].excess_ret.toFixed(4));
				}

				this.peizhibar2 = barChartOption({
					tooltip: {
						type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
					},
					// visualMap: {
					// 	show: false,
					// 	orient: 'horizontal',
					// 	left: 'center',
					// 	min: data.imaginary_ret.maxret,
					// 	max: data.imaginary_ret.minret,
					// 	text: ['High Score', 'Low Score'],
					// 	// Map the score column to color
					// 	dimension: 0,
					// 	inRange: {
					// 		color: ['#4096ff', '#4096ff']
					// 	}
					// },
					yAxis: [
						{
							type: 'value'
							// position: 'top'
						}
					],
					xAxis: [
						{
							type: 'category',
							data: datearr
						}
					],
					series: [
						{
							name: '因子暴露',
							type: 'bar',
							stack: '总量',
							label: {
								show: false,
								textStyle: {
									fontSize: '14px'
								},
								formatter: '{b}'
							},
							data: dataarr.map((item) => {
								return {
									value: item * 1,
									itemStyle: {
										color: item * 1 > 0 ? '#4096ff' : '#4096ff'
									}
								};
							})
						}
					]
				});
			}
		},
		createPrintWord() {
			this.$refs['whatIfAssetAllocation1'].mergeOptions({ toolbox: { show: false } });
			this.$refs['whatIfAssetAllocation2'].mergeOptions({ toolbox: { show: false } });
			let chart1 = this.$refs['whatIfAssetAllocation1'].getDataURL({
				type: 'png',
				pixelRatio: 3,
				backgroundColor: '#fff'
			});
			let chart2 = this.$refs['whatIfAssetAllocation2'].getDataURL({
				type: 'png',
				pixelRatio: 3,
				backgroundColor: '#fff'
			});
			this.$refs['whatIfAssetAllocation1'].mergeOptions({ toolbox: { show: true } });
			this.$refs['whatIfAssetAllocation2'].mergeOptions({ toolbox: { show: true } });
			return [...exportTitle('What-if 假想验证: 调仓时的资产配置作对了吗'), ...exportChart(chart1), ...exportChart(chart2)];
		}
	}
};
</script>

<style scoped>
.flex_card > div {
	height: 350px;
}
</style>
