<template>
	<div class="industry-table">
		<el-button icon="el-icon-document-delete" style="float: right; margin: 8px 0" @click="exportExcel">导出Excel</el-button>
		<el-table
			height="60vh"
			:data="industryList"
			ref="filterTables"
			style="width: 99%"
			@sort-change="sortCahnge"
			@filter-change="filterChange"
			:default-sort="{ prop: 'yearqtr', order: 'descending' }"
		>
			<el-table-column
				v-for="item in column"
				:key="item.value"
				:label="item.label"
				:prop="item.value"
				:filters="item.filter ? industryNameList : ''"
				:filtered-value="item.filter ? industryNameListFilter : ''"
				:filter-method="item.filter ? filterIndustryName : ''"
				align="gotoleft"
				:sortable="item.sortable"
			>
				<template #header>
					<long-table-popover-chart
						v-if="item.popover"
						:data="formatIndustryData()"
						date_key="yearqtr"
						:data_key="item.value"
						:show_name="item.label"
						:formatter="
							function (val) {
								return val;
							}
						"
					>
						<span>{{ item.label }}</span>
					</long-table-popover-chart>
					<span v-else>{{ item.label }}</span>
				</template>
				<template slot-scope="{ row }">
					<span v-if="item.value == 'weight' || item.value == 'yearqtr' || item.value == 'industry_name'">{{
						item.format ? item.format(row[item.value]) : row[item.value]
					}}</span>
					<span
						v-else
						:style="{
							color: row[item.value] > 0 ? '#e85d2d' : row[item.value] < 0 ? '#18c2a0' : 'initial'
						}"
						>{{ item.format ? item.format(row[item.value]) : row[item.value] }}</span
					>
				</template>
			</el-table-column>
		</el-table>
	</div>
</template>

<script>
import { filter_json_to_excel } from '@/utils/exportExcel.js';
export default {
	data() {
		return {
			industryList: [],
			industryNameList: [],
			industryNameListFilter: [],
			column: [
				{
					label: '时间',
					value: 'yearqtr',
					sortable: true,
					popover: false
				},
				{
					label: '行业',
					value: 'industry_name',
					filter: true,
					popover: false
				},
				{
					label: '行业估算收益率',
					value: 'industry_return',
					sortable: true,
					popover: true,
					format: this.fix2p
				},
				{
					label: '行业基准收益率',
					value: 'industry_index_return',
					sortable: true,
					popover: true,
					format: this.fix2p
				},
				{
					label: '行业超额收益率',
					value: 'industry_excess_return',
					sortable: true,
					popover: true,
					format: this.fix2p
				},
				{
					label: '权重',
					value: 'weight',
					sortable: true,
					popover: true,
					format: this.fixp
				}
			]
		};
	},
	methods: {
		getData(data) {
			this.industryNameList = data?.industryNameList;
			this.industryList = data?.industryList;
			this.setDefaultFilter(1, data?.industryNameList?.[0].text);
			this.industryNameListFilter = [data?.industryNameList?.[0].text];
		},
		setDefaultFilter(col, val) {
			const column = this.$refs.filterTables.columns[col];
			column.filteredValue = [val];
			this.$refs.filterTables.store.commit('filterChange', {
				column,
				values: column.filteredValue,
				silent: true
			});
		},
		formatIndustryData() {
			return this.industryList
				.filter((item) => {
					return this.industryNameListFilter.includes(item.industry_name);
				})
				.sort((a, b) => {
					return this.moment(this.moment(a.yearqtr, 'YYYY QQ').format()).isAfter(this.moment(b.yearqtr, 'YYYY QQ').format()) ? 1 : -1;
				})
				.map((item) => {
					let obj = { ...item };
					for (const key in item) {
						let format = this.column.find((val) => {
							return val.value == key;
						})?.format;
						obj[key] = format ? format(item[key]) : obj[key];
					}
					return obj;
				})
				.map((item) => {
					let obj = { ...item };
					for (const key in item) {
						if (typeof item[key] == 'string') {
							if (item[key].includes('%')) {
								obj[key] = item[key]?.split('%')?.[0] * 1;
							} else {
								obj[key] = !isNaN(item[key]) ? item[key] * 1 : item[key];
							}
						}
					}
					return obj;
				});
		},
		exportExcel() {
			let list = this.column.map((item) => {
				return {
					label: item.label,
					value: item.value
				};
			});
			let data = this.industryList.map((item) => {
				let obj = { ...item };
				for (const key in item) {
					const element = this.column.find((v) => v.value == key);
					if (element?.format) {
						obj[key] = element.format(item[key]);
					}
				}
				return obj;
			});
			filter_json_to_excel(list, data, '行业超额收益表');
		},
		// 行业配置表现-筛选方法
		filterIndustryName(value, row) {
			return row.industry_name === value;
		},
		filterChange(val) {
			this.industryNameListFilter = [];
			for (const key in val) {
				this.industryNameListFilter = val[key];
			}
		},
		sortCahnge({ column, prop, order }) {
			return this.industryList.sort((a, b) => {
				if (order == 'descending') {
					return b[prop] - a[prop];
				} else if (order == 'ascending') {
					return a[prop] - b[prop];
				} else {
					return true;
				}
			});
		},
		fixp(val) {
			return val == '--' ? val : !isNaN(val) ? Number(val)?.toFixed(2) + '%' : '--';
		},
		fix2p(val) {
			return val == '--' ? val : !isNaN(val) ? Number(val * 100)?.toFixed(2) + '%' : '--';
		}
	}
};
</script>

<style></style>
