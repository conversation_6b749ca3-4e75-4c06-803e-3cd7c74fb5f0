<template>
  <div class="plate-wrapper the-indicator-board-wrapper"
       v-loading="loading">
    <VerticalLineHeader title="业绩与评价指标看板"
                        showDownloadBtn
                        @downloadClick="exportExcel">
      <template slot="right">
        <el-form ref="form"
                 :model="headerForm"
                 label-width="80px"
                 class="title-right-form">
          <el-form-item size="small"
                        label="指数类型:">
            <el-select v-model="headerForm.indexType"
                       style="width: 150px"
                       placeholder="请选择"
                       @change="handleSelectHeader">
              <el-option v-for="item in IndexTypeOption"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item size="small"
                        label="展示维度:">
            <el-select v-model="headerForm.measure"
                       placeholder="请选择"
                       style="width: 150px"
                       @change="handleSelectHeader">
              <el-option v-for="item in DisplayDimensionOption"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item size="small"
                        label-width="10px"
                        label=" ">
            <el-date-picker value-format="yyyy-MM-dd"
                            type="date"
                            placeholder="请选择截止日期"
                            v-model="headerForm.deadline"
                            style="width: 150px"
                            @change="handleSelectHeader"></el-date-picker>
          </el-form-item>
          <el-radio-group class="lq-radio-group radio-group-wrapper"
                          v-model="headerForm.dateFlag"
                          @change="handleSelectHeader"
                          size="small">
            <el-radio-button v-for="radioItem in DateTypeOption"
                             :key="radioItem.value"
                             :label="radioItem.value">{{
							radioItem.label
						}}</el-radio-button>
          </el-radio-group>
        </el-form>
      </template>
    </VerticalLineHeader>
    <div>
      <el-table style="width: 100%"
                :data="tableDataNow"
                :stripe="true"
                :border="true"
                @sort-change="handeleSortChange">
        <!-- <el-table-column prop="indexName" label="指数名称" align="gotoleft" width="180"> </el-table-column> -->
        <template v-for="item in tableHeader">
          <el-table-column v-if="item.prop === 'customTime'"
                           :key="item.prop + customHeaderName"
                           prop="customTime"
                           :label="customHeaderName"
                           sortable="custom"
                           width="120"
                           :formatter="dataFormatter"
                           align="gotoleft">
            <template slot="header">
              {{ customHeaderName }}
              <DatePickerBtn trigger="click"
                             @change="handleDateChange"
                             @click.native.stop="() => {}"></DatePickerBtn>
            </template>
          </el-table-column>
          <el-table-column v-else
                           :formatter="dataFormatter"
                           min-width="120"
                           :key="item.prop"
                           :prop="item.prop"
                           :label="item.label"
                           :sortable="item.sortable !== false ? 'custom' : false"
                           align="gotoleft"></el-table-column>
        </template>
      </el-table>
      <el-pagination style="display: flex; justify-content: right; padding-top: 16px; padding-bottom: 16px"
                     class="pagination-footer-wrapper"
                     @size-change="handleSizeChange"
                     @current-change="handleCurrentChange"
                     :current-page.sync="pageInfo.currentPage"
                     :page-sizes="[10, 50, 100, 200, 300, 400]"
                     :page-size="pageInfo.pageSize"
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="pageInfo.total">
      </el-pagination>
    </div>
  </div>
</template>
<script>
import { filter_json_to_excel } from '@/utils/exportExcel.js';
import VerticalLineHeader from './VerticalLineHeader.vue';
import DatePickerBtn from './DatePickerBtn.vue';
import { getPerformanceEvaluationList, getIndexCode } from '@/api/pages/tkAnalysis/captial-market.js';
import stringTool from '../../components/string.tool';

export default {
  name: 'TheIndicatorBoard',
  components: {
    VerticalLineHeader,
    DatePickerBtn
  },
  data () {
    return {
      tableHeader1: [
        {
          prop: 'yearToDate',
          label: '年初至今'
        },
        {
          prop: 'lastWeek',
          label: '近一周'
        },
        {
          prop: 'lastMounth',
          label: '近一月'
        },
        {
          prop: 'lastSeason',
          label: '近一季'
        },
        {
          prop: 'lastHalfYears',
          label: '近半年'
        },
        {
          prop: 'lastYear',
          label: '近一年'
        },
        {
          prop: 'lastThreeYear',
          label: '近三年'
        },
        {
          prop: 'lastFiveYear',
          label: '近五年'
        },
        {
          prop: 'customTime',
          label: '自定义区间'
        }
      ],
      tableHeader2: [
        {
          prop: 'yearToDate',
          label: '年初至今'
        },
        {
          prop: 'lastMounth',
          label: '近一月'
        },
        {
          prop: 'lastSeason',
          label: '近一季'
        },
        {
          prop: 'lastHalfYears',
          label: '近半年'
        },
        {
          prop: 'lastYear',
          label: '近一年'
        },
        {
          prop: 'lastThreeYear',
          label: '近三年'
        },
        {
          prop: 'lastFiveYear',
          label: '近五年'
        },
        {
          prop: 'customTime',
          label: '自定义区间'
        }
      ],
      tableHeader3: [],
      tabeleHeader4: [
        {
          prop: 'highestPointMaximumDrawdown',
          label: '自上次最高点的最大回撤'
        },
        {
          prop: 'highestPointDrawdown',
          label: '自上次最高点的回撤'
        },
        {
          prop: 'highestPointDate',
          label: '上次最高点的日期'
        }
      ],
      headerForm: {
        indexType: 'scale', //规模指数
        measure: 'cum_return', //风格指数
        dateFlag: '0', //近期
        deadline: '',
        startDate: '',
        endDate: '',
        sort_prop: '',
        sort_order: ''
      },
      IndexTypeOption: [
        { label: '规模指数', value: 'scale' },
        { label: '风格指数', value: 'style' },
        { label: '行业指数', value: 'industry' },
        { label: '泰康自定义行业指数', value: 'taikang' }
      ],
      DateTypeOption: [
        { label: '近期业绩', value: '0' },
        { label: '自然年份业绩', value: '1' }
      ],
      DisplayDimensionOption: [
        { label: '累计收益率', value: 'cum_return' },
        { label: '夏普', value: 'sharpe' },
        { label: '波动率', value: 'volatility' },
        { label: '年化收益', value: 'ave_return' },
        { label: '最大回撤', value: 'maxdrawdown' }
      ],
      tableData: [],
      tableDataNow: [],
      pageInfo: {
        pageSize: 100,
        currentPage: 1,
        total: 0
      },
      customHeaderName: '区间累计收益率',
      loading: false
    };
  },
  computed: {
    tableHeader () {
      let result = [];
      let commmon = {
        prop: 'indexName',
        label: '指数名称',
        sortable: false
      };
      result.push(commmon);
      //只在展示维度为累计收益率时展示昨日列
      if (this.headerForm.measure === 'cum_return') {
        result.push({
          prop: 'today',
          label: '当日'
        });
      }
      //为自然年份业绩
      if (this.headerForm.dateFlag === '1') {
        result.push(...this.tableHeader3);
        return result;
      }
      if (this.headerForm.measure === 'maxdrawdown') {
        result.push(...this.tableHeader2);
        result.push(...this.tabeleHeader4);
        return result;
      } else {
        result.push(...this.tableHeader1);
        return result;
      }
    }
  },
  created () {
    this.getIndexCode();
    this.headerForm.deadline = this.moment().subtract(1, 'day').format('YYYY-MM-DD');
    if (this.localStorage.getItem('TheIndicatorBoardPlate')?.headerForm) {
      let key_list = ['headerForm'];
      for (let key of key_list) {
        this[key] = this.localStorage.getItem('TheIndicatorBoardPlate')?.[key] || this[key];
      }
    }
    this.getData();
  },
  methods: {
    // 获取指数类型列表
    async getIndexCode () {
      let data = await getIndexCode();
      if (data?.code == 200) {
        this.IndexTypeOption = data.data?.map((v) => {
          return {
            label: v.indexName,
            value: v.indexType
          };
        });
      }
    },
    handeleSortChange ({ column, prop, order }) {
      this.tableData.sort((item1, item2) => {
        const a1 = item1[prop] || 0;
        const a2 = item2[prop] || 0;
        let orderVal = order === 'ascending' ? -(a1 - a2) : a1 - a2;
        return orderVal;
      });
      this.dulData();
    },
    // 导出excel
    exportExcel () {
      let list = this.tableHeader.map((item) => {
        return {
          ...item,
          value: item.prop,
          format: ''
        };
      });
      filter_json_to_excel(list, this.tableData, '业绩与评价指标看板');
    },
    dataFormatter (row, column, cellValue, index) {
      if (column.property === 'highestPointDate' || column.property === 'indexName') {
        return cellValue;
      }
      if (this.headerForm.measure == 'sharpe') {
        return stringTool.fix2(cellValue);
      }
      return stringTool.fix2px(cellValue);
    },
    handleSelectHeader (val) {
      // this.headerForm.measure = val;
      this.customHeaderName = '区间' + this.DisplayDimensionOption.find((v) => v.value == this.headerForm.measure)?.label;
      console.log(this.customHeaderName);
      this.pageInfo.currentPage = 1;
      //接口调用
      this.getData();
    },
    handleSizeChange (value) {
      this.pageInfo.currentPage = 1;
      this.pageInfo.pageSize = value;
      this.dulData();
    },
    handleCurrentChange (value) {
      this.pageInfo.currentPage = value;
      this.dulData();
    },
    dulData () {
      let { currentPage, pageSize } = this.pageInfo;
      this.tableDataNow = this.tableData.slice((currentPage - 1) * pageSize, currentPage * pageSize);
    },
    // 获取列表数据
    async getData () {
      let params = {
        pageSize: this.pageInfo.pageSize,
        currentPage: this.pageInfo.currentPage,
        ...this.headerForm
      };
      this.localStorage.setItem('TheIndicatorBoardPlate', { headerForm: this.headerForm });
      this.loading = true;
      let req = await getPerformanceEvaluationList(params);
      this.loading = false;
      let { data, code, message } = req || {};
      if (code == 200) {
        // this.pageInfo.total = data?.total || 0
        // this.pageInfo.currentPage = data?.currentPage || 1
        // this.pageInfo.pageSize = data?.pageSize || 20
        this.tableData = data || [];
        let { naturalList = [] } = data[0] || {};
        this.pageInfo.total = this.tableData.length;
        if (naturalList && naturalList.length > 0) {
          this.tableHeader3 = naturalList.map((item) => {
            return {
              prop: item.naturalDate,
              label: item.naturalDate
            };
          })?.sort((a, b) => {
            if (a.prop < b.prop) return 1
            else return -1
          });
          this.tableData = this.tableData.map((item) => {
            let obj = { ...item };
            item?.naturalList?.map((v) => {
              obj[v.naturalDate] = v.meter;
            });
            return obj;
          });
        }
      } else {
        this.tableData = [];
      }
      this.dulData();
    },
    handleDateChange (value) {
      //自定义区间收益，只影响自定义列表
      this.headerForm['startDate'] = value[0];
      this.headerForm['endDate'] = value[1];
      this.getData();
    }
  }
};
</script>
<style lang="scss" scoped></style>
