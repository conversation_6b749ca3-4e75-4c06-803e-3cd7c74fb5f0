<template>
	<div id="industryAllocationPerformance">
		<analysis-card-title title="行业配置表现" image_id="industryAllocationPerformance">
			<el-button
				v-for="(item, i) in ['行业收益图', '行业超额收益表']"
				:key="i"
				@click="changeShowCharts(item)"
				plain
				:class="showCharts == item ? 'btn_active' : 'btn_info'"
			>
				{{ item }}
			</el-button>
		</analysis-card-title>
		<div v-loading="loading">
			<div v-show="showCharts == '行业收益图'" id="industryAllocationPerformanceChart">
				<industry-return-chart ref="industryReturnChart"></industry-return-chart>
			</div>
			<div v-show="showCharts == '行业超额收益表'">
				<industry-excess-table ref="industryExcessTable"></industry-excess-table>
			</div>
		</div>
		<div class="mt-20">
			<analysis-description :is_column="true" :description="description"></analysis-description>
		</div>
	</div>
</template>

<script>
// 行业配置表现
import { getIndustryInfo } from '@/api/pages/Analysis.js';
// 模型使用说明
import analysisDescription from '@/components/components/components/analysisDescription/index.vue';

import industryReturnChart from './components/industryReturnChart.vue';
import industryExcessTable from './components/industryExcessTable';
export default {
	components: {
		industryReturnChart,
		industryExcessTable,
		analysisDescription
	},
	data() {
		return {
			title: '行业配置表现',
			visible_hypz: true,
			show: true,
			allocation: {},
			allocationBar: {},
			loadingallo: true,
			equityWeght: [],
			tempvalueselect: '采掘',
			showCharts: '行业收益图',
			industryList: [], // 行业配置表现数据
			industryNameList: [], // 行业配置表现-筛选可选项
			choosemsg: '',
			industries: [
				'非银金融',
				'银行',
				'房地产',
				'基础化工',
				'公用事业',
				'交通运输',
				'石油石化',
				'建筑材料',
				'煤炭',
				'钢铁',
				'环保',
				'建筑装饰',
				'有色金属',
				'国防军工',
				'汽车',
				'机械设备',
				'综合',
				'电力设备',
				'传媒',
				'通信',
				'计算机',
				'电子',
				'医药生物',
				'农林牧渔',
				'家用电器',
				'商贸零售',
				'社会服务',
				'轻工制造',
				'纺织服饰',
				'食品饮料',
				'美容护理'
			],
			noteData: '',
			serIndex: 0,
			loading: true,
			loading: true,
			info: {},
			echartBar: null,
			requestData: null,
			data: []
		};
	},
	computed: {
		description() {
			return `对一、三季度持仓进行插值补全，计算基金/基金经理在各个股上的收益，在此基础上 计算基金在申万一级行业上的行业估算收益、行业超额收益、行业配置权重。`;
		}
	},
	methods: {
		openvideo() {
			window.open('https://www.bilibili.com/video/BV1iW4y1m7R1?share_source=copy_web');
		},
		hideLoading() {
			this.show = false;
			this.loading = false;
		},
		async getData(info) {
			let dateTime = new Date().getTime();
			info.dateTime = dateTime;
			this.info = info;
			await this.getIndustryInfo();
		},
		// 获取行业配置表现数据
		async getIndustryInfo() {
			this.loading = true;
			let data = await getIndustryInfo({
				flag: this.info.flag,
				code: this.info.code,
				type: this.info.type,
				start_date: this.info.start_date,
				end_date: this.info.end_date,
				industry_section: this.info.type == 'equityhk' ? '恒生一级' : '申万(2021)',
				industry_code: '',
				mtyhints: ''
			});
			this.loading = false;
			if (data?.mtycode == 200) {
				this.data = data?.data;
			} else {
				this.hideLoading();
				this.data = [];
			}
			this.$refs['industryReturnChart']?.getData(this.data, this.info);
		},

		changeShowCharts(item) {
			this.showCharts = item;
			if (item == '行业配置比例图') {
				this.$refs['shareDataset'].getCharts(this.data);
			} else if (item == '行业超额收益表') {
				this.$refs['industryExcessTable']?.getData(this.data);
			}
		},
		async createPrintWord(info) {
			await this.getData(info);
			let data = await Promise.all([
				this.$refs['industryReturnChart']?.createPrintWord(this.data, this.info)
				// this.$refs['industryExcessTable']?.createPrintWord(this.data)
			]);
			let result = [];
			data.map((item) => {
				return result.push(...item);
			});
			return result;
		}
	}
};
</script>

<style scoped lang="scss">
.card-header {
	display: flex;
	align-items: center;
}
.heightx600 {
	height: 600px !important;
}
.heightx100 {
	height: 100px !important;
}
.heightx50 {
	height: 50px !important;
}
.header-btn .btn_info {
	color: rgba(0, 0, 0, 0.65);
	border-color: #d9d9d9;
	background-color: #ffffff;
}
.header-btn .btn_info:hover {
	color: #4096ff;
	border-color: #4096ff;
	background-color: #ffffff;
}
.header-btn .btn_active:hover {
	color: #4096ff;
	border-color: #4096ff;
	background-color: #ffffff;
}
.header-btn .btn_active {
	color: #4096ff;
	border-color: #4096ff;
	background-color: #ffffff;
}
.charts_one_class {
	height: 680px;
}
</style>
