<!--  -->
<template>
  <div :class="typeFlag == '1' ? 'selfTable' : 'selfTable'">
    <div style="display: flex; border-bottom: 1px solid rgba(0, 0, 0, 0.06); page-break-inside: avoid"
         class="tableHover"
         v-for="(item, index) in data"
         :key="index">
      <div v-for="(items, indexs) in item"
           :style="indexs == 0 ? 'width:218px;position: relative' : 'flex: 1;margin-left:8px;position: relative'"
           :key="indexs">
        <div v-show="indexs != 0"
             class="coloriconFlag"
             :style="'background:' + colorList[indexs - 1]"></div>
        <div :style="
						indexs == 0
							? 'display: flex; align-items: center; justify-content: left;width:100%'
							: 'display: flex; align-items: center; justify-content: center;width:100%'
					">
          <div class="table_content"
               :style="indexs == 0 ? 'margin-left:24px;text-align:left' : ''">
            <div style="display: flex; width: 100%; padding-left: 8px; align-items: center"
                 v-if="typeFlag == 'splice1' && indexs != 0">
              <div class="rowdetail"
                   style="flex: 1; text-align: left; padding-left: 8px"
                   v-for="(itemx, indexx) in items.split('/')"
                   :key="indexx">
                {{ itemx }}
              </div>
            </div>
            <div v-else-if="typeFlag == 'splice2' && indexs != 0">
              <div>{{ items.split('/')[0] }}</div>
              <div style="display: flex; width: 100%; align-items: center; margin-top: 4px">
                <div class="rowdetail"
                     style="flex: 1; text-align: center"
                     v-for="(itemx, indexx) in items.split('/').slice(1, items.split('/').length)"
                     :key="indexx">
                  <div>{{ itemx }}</div>
                </div>
              </div>
            </div>
            <div v-else>{{ items }}</div>
            <div style="color: rgba(0, 0, 0, 0.65); font-size: 12px"
                 v-if="typeFlag == '2' && indexs == 0 && index == 0">
              {{ measure[0] }}&nbsp;&nbsp;{{ measure[1] }}
            </div>
            <div style="color: rgba(0, 0, 0, 0.65); font-size: 12px"
                 v-if="typeFlag == '2' && indexs == 0 && index == 1">
              {{ measure[0] }}&nbsp;&nbsp;{{ measure[2] }}
            </div>
            <div style="color: rgba(0, 0, 0, 0.65); font-size: 12px"
                 v-if="typeFlag == '2' && indexs == 0 && index == 2">
              {{ measure[0] }}&nbsp;&nbsp;{{ measure[3] }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';

export default {
  props: {
    data: {
      type: Array
    },
    typeFlag: {
      type: String
    },
    measure: {
      type: Array
    }
  },
  //import引入的组件需要注入到对象中才能使用
  components: {},
  data () {
    //这里存放数据
    return {
      colorList: ['#4096ff', '#4096ff', '#7388A9', '#6F80DD']
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {},
  //生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () { },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
.rowdetail {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 1;
}
.rowdetail:hover {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2000;
}
.selfTable {
	font-size: 14px;
	width: 100%;
	font-weight: 500;
	color: rgba(0, 0, 0, 0.85);
}
.coloriconFlag {
	width: 4px;
	height: 24px;
	position: absolute;
	border-radius: 15px;
	flex: none;
	order: 0;
	flex-grow: 0;
	top: 16px;
}
.table_content {
	width: 100%;
	min-height: 56px;
	display: flex;
	flex-direction: column;
	// align-items: center;
	justify-content: center;
	text-align: center;
}
.tableHover:hover {
	background: #fafafa;
}
//@import url(); 引入公共css类
</style>
