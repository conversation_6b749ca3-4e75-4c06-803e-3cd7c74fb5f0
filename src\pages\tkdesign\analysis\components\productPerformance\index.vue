<script>
import VChart from 'vue-echarts';
import { lineChartOption } from '@/utils/chartStyle';
import { handleData, handleDataBillion, typeMatching, handleDataTenThousand2 } from '@/utils/count';
import {
  getObjectPerformance,
  getObjectPerformanceStat,
  getObjectAssetAllocation,
  getObjectCutAllocation,
  getObjectCutPerformance,
  getObjectFundStat,
  getObjectFundStat2
} from '@/api/pages/analysis/report.js';
import { filter_json_to_excel_inside, changColumnToRow, filter_json_to_excel_inside_multiHeader } from '@/utils/exportExcel.js';
import { export_json_to_excel_multiHeader } from '@/vendor/Export2Excel.js';
import {
  downloadWord,
  exportTitleWithSubtitle,
  exportTableMergeHeader,
  exportTitle,
  exportFirstTitle,
  exportChart,
  exportTable,
  Format,
  exportSencondTitle
} from '@/utils/exportWord.js';
export default {
  components: { VChart },
  data () {
    return {
      uploadState: false,
      // 业绩表现
      performance: {
        options: {}, // 业绩表现图表数据源
        tableData: [], // 业绩表现表格数据源
        loading: false, // 加载
        showEmpty: false
      },
      // 颗粒度
      particle: {
        options: {}, // 颗粒度贡献图表数据源
        tableData: [], // 颗粒度比较表格数据源
        loading: false, // 加载
        oldTableData: [],
        showEmpty: false
      },
      summary: {
        options: {}, // 产品业绩汇总图表数据源
        showEmpty: false,
        tableData: [], // 大类资产业绩汇总表格数据源
        oldTableData: [],
        loading: false // 大类资产业绩汇总表格加载
      },

      fundData: {
        tableData: [], // 基金持仓分析表格数据源
        oldTableData: [],
        loading: false, // 基金持仓分析表格加载
        pageIndex: 1,
        pageSize: 10,
        tableData2: [], // 基金持仓分析表格数据源
        oldTableData2: [],
        loading2: false, // 基金持仓分析表格加载
        pageIndex2: 1,
        pageSize2: 10
      },
      granularityPagination: {
        pageIndex: 1,
        pageSize: 10
      },
      params: {},
      // excel data
      // 产品业绩表现
      cpyjbxTitle: [],
      cpyjbxColumn: [],
      // 基本颗粒度业绩贡献比较
      jbkldyjgxbjTitle: [],
      jbkldyjgxbjColumn: []
    };
  },

  methods: {
    handleData,
    handleDataBillion,
    handleDataTenThousand2,
    typeMatching,

    /**
     * 排序方法
     */
    sortSummary ({ column, prop, order }) {
      let key = prop.split('.')[1];
      let arr = JSON.parse(JSON.stringify(this.summary.tableData.filter((v) => v.data[key] !== 'nan' && v.data[key] !== 'NaN')));
      let noArr = this.summary.tableData.filter((v) => v.data[key] === 'nan' || v.data[key] === 'NaN');
      if (order === 'ascending') {
        this.summary.tableData = noArr.concat(arr.sort((a, b) => Number(a.data[key]) - Number(b.data[key])));
      }
      if (order === 'descending') {
        this.summary.tableData = arr.sort((a, b) => Number(b.data[key]) - Number(a.data[key])).concat(noArr);
      }
      if (order === null) {
        this.summary.tableData = this.summary.oldTableData;
      }
    },
    sortParticle ({ column, prop, order }) {
      let key = prop.split('.')[1];
      let arr = JSON.parse(JSON.stringify(this.particle.tableData.filter((v) => v.data[key] !== 'nan' && v.data[key] !== 'NaN')));
      let noArr = this.particle.tableData.filter((v) => v.data[key] === 'nan' || v.data[key] === 'NaN');
      if (order === 'ascending') {
        this.particle.tableData = noArr.concat(arr.sort((a, b) => Number(a.data[key]) - Number(b.data[key])));
      }
      if (order === 'descending') {
        this.particle.tableData = arr.sort((a, b) => Number(b.data[key]) - Number(a.data[key])).concat(noArr);
      }
      if (order === null) {
        this.particle.tableData = this.particle.oldTableData;
      }
    },

    sortFundData ({ column, prop, order }) {
      let key = prop.split('.')[1];
      let arr = JSON.parse(JSON.stringify(this.fundData.tableData.filter((v) => v.data[key] !== 'nan' && v.data[key] !== 'NaN')));
      let noArr = this.fundData.tableData.filter((v) => v.data[key] === 'nan' || v.data[key] === 'NaN');
      if (order === 'ascending') {
        this.fundData.tableData = noArr.concat(arr.sort((a, b) => Number(a.data[key]) - Number(b.data[key])));
      }
      if (order === 'descending') {
        this.fundData.tableData = arr.sort((a, b) => Number(b.data[key]) - Number(a.data[key])).concat(noArr);
      }
      if (order === null) {
        this.fundData.tableData = this.fundData.oldTableData;
      }
    },

    sortFundData2 ({ column, prop, order }) {
      let key = prop.split('.')[1];
      let arr = JSON.parse(JSON.stringify(this.fundData.tableData2.filter((v) => v.data[key] !== 'nan' && v.data[key] !== 'NaN')));
      let noArr = this.fundData.tableData2.filter((v) => v.data[key] === 'nan' || v.data[key] === 'NaN');
      if (order === 'ascending') {
        this.fundData.tableData2 = noArr.concat(arr.sort((a, b) => Number(a.data[key]) - Number(b.data[key])));
      }
      if (order === 'descending') {
        this.fundData.tableData2 = arr.sort((a, b) => Number(b.data[key]) - Number(a.data[key])).concat(noArr);
      }
      if (order === null) {
        this.fundData.tableData2 = this.fundData.oldTableData2;
      }
    },
    getBetweenDates (startDate, endDate) {
      let dates = [];
      let currentDate = new Date(startDate);

      while (currentDate <= endDate) {
        dates.push(currentDate.toISOString().slice(0, 10));
        currentDate.setDate(currentDate.getDate() + 1);
      }

      return dates;
    },

    /**
     * 对数据进行补全
     */
    handData (data) {
      // 获取最小的日期
      let minIndex = 0;
      let maxIndex = 0;
      for (let i = 1; i < data.length; i++) {
        const currentDate = new Date(data[i].data.date);
        const minDate = new Date(data[minIndex].data.date);
        if (currentDate < minDate) {
          minIndex = i;
        }
        if (currentDate > minDate) {
          maxIndex = i;
        }
      }
      let minDate = data[minIndex].data.date;
      let maxDate = data[maxIndex].data.date;

      let arr = data.filter((v) => v.data.code === this.$route.query.analyticalBasis).sort((a, b) => a.data.date.localeCompare(b.data.date)); // 按照日期排序
      let otherArr = data.filter((v) => v.data.code !== this.$route.query.analyticalBasis).sort((a, b) => a.data.date.localeCompare(b.data.date)); // 按照日期排序
      // console.log(arr, otherArr);
      let dateArr = this.getBetweenDates(new Date(minDate), new Date(maxDate));
      let newArr = [];
      let newOtherArr = [];

      dateArr.forEach((item) => {
        newArr.push({
          data: {
            date: item
          }
        });
        newOtherArr.push({
          data: {
            date: item
          }
        });
      });
      newArr.forEach((item) => {
        arr.forEach((citem) => {
          if (item.data.date === citem.data.date) {
            item.data = citem.data;
          }
        });
      });

      newOtherArr.forEach((item) => {
        otherArr.forEach((citem) => {
          if (item.data.date === citem.data.date) {
            item.data = citem.data;
          }
        });
      });
      for (let i = 0; i <= newArr.length - 1; i++) {
        if (i === 0 && !newArr[0].data.cum_return) {
          newArr[i].data = {
            date: newArr[i].data.date,
            cum_return: 0,
            drawdown: 0,
            index_name: otherArr[0].data.index_name,
            code: this.$route.query.analyticalBasis
          };
        }
        if (i !== 0 && !newArr[i].data.code) {
          newArr[i].data = {
            date: newArr[i].data.date,
            cum_return: newArr[i - 1].data.cum_return,
            drawdown: newArr[i - 1].data.cum_return,
            index_name: otherArr[0].data.index_name,
            code: this.$route.query.analyticalBasis
          };
        }
      }
      for (let i = 0; i <= newOtherArr.length - 1; i++) {
        if (i === 0 && !newOtherArr[0].data.code) {
          newOtherArr[i].data = {
            date: newOtherArr[i].data.date,
            cum_return: 0,
            drawdown: 0,
            index_name: otherArr[0].data.index_name,
            code: 'all'
          };
          // console.log(newOtherArr);
          // continue;
        }
        if (i !== 0 && !newOtherArr[i].data.code) {
          newOtherArr[i].data = {
            date: newOtherArr[i].data.date,
            cum_return: newOtherArr[i - 1].data.cum_return,
            drawdown: newOtherArr[i - 1].data.cum_return,
            index_name: otherArr[0].data.index_name,
            code: 'all'
          };
        }
      }
      return newArr.concat(newOtherArr);
    },

    /**
     * 获取产品业绩表现图表数据
     */
    getPerformanceChart () {
      let obj = {};
      let hsData = [];
      let allData = [];
      let withdrawal = [];
      let excess = [];
      let date = [];
      let indexName = '参考基准';
      return getObjectPerformance(this.params).then((res) => {
        console.log(this.$route.query.analyticalBasis);
        if (res.code === 200) {
          console.log(res.data.rows[0]);
          res.data.rows = this.handData(res.data.rows);
          indexName = res.data.rows?.[0]?.data?.index_name || '参考基准';
          res.data.rows.forEach((item) => {
            let key = item.data.date.slice(0, 10);
            if (!obj[key]) {
              obj[key] = [
                { code: this.$route.query.analyticalBasis, data: 0 },
                { code: 'all', data: 0 }
              ];
            }
            if (item.data.code === this.$route.query.analyticalBasis) {
              obj[key][0] = {
                code: item.data.code,
                data: item.data.cum_return === 0 ? 0 : handleData(item.data.cum_return, true)
              };
            } else {
              withdrawal.push(Number(handleData(item.data.drawdown, true)));
              obj[key][1] = {
                code: item.data.code,
                data: item.data.cum_return === 0 ? 0 : handleData(item.data.cum_return, true)
              };
              allData.push(handleData(item.data.cum_return, true));
            }
          });

          let handObj = Object.entries(obj)
            .sort()
            .reduce(function (obj, item) {
              const key = item[0];
              obj[key] = item[1];
              return obj;
            }, {});
          for (let key in handObj) {
            date.push(key);
            hsData.push(obj[key][0].data);
            excess.push((obj[key][1].data - obj[key][0].data).toFixed(2));
          }
          const chartOption = this.getPerformanceChartData(hsData, allData, withdrawal, excess, date, indexName);
          return chartOption;
        }
      });
    },

    /**
     * 获取产品业绩表现表格数据
     */
    getPerformanceTable (hsData, allData) {
      this.performance.loading = true;
      return getObjectPerformanceStat(this.params).then((res) => {
        this.performance.loading = false;
        if (res.code === 200) {
          this.performance.showEmpty = false;
          let allData = {};
          let data = {};
          let arr = [];
          res.data.rows.forEach((item, index) => {
            if (item.data.code === 'all') {
              allData = item.data;
            } else {
              data = item.data;
            }
          });

          res.data.columns.forEach((item, index) => {
            if (!index) return;
            arr.push({
              key: this.getChinese(item.name),
              allValue: allData[item.name],
              value: data[item.name],
              format: !(
                item.name === 'lose_days' ||
                item.name === 'recovrery_length' ||
                item.name === 'rate_high' ||
                item.name === 'win_days'
              )
            });
          });
          let sortData = [
            { key: '区间收益' },
            { key: '年化收益' },
            { key: '年化波动' },
            { key: '夏普率' },
            { key: '信息率' }, //没返回
            { key: '最大回撤' },
            { key: '正收益率占比' },
            { key: '负收益率占比' },
            { key: '最大连续上涨天数' },
            { key: '最大连续下跌天数' }
          ];
          sortData.forEach((item, index) => {
            arr.forEach((citem, cindex) => {
              if (item.key === citem.key) {
                this.performance.tableData.push(citem);
              }
            });
          });
          return this.performance.tableData;
        } else {
          this.performance.tableData = [];
          this.performance.showEmpty = true;
          return this.performance.tableData;
        }
      });
    },

    /**
     * 获取区间指标中文
     */
    getChinese (data) {
      switch (data) {
        case 'volatility':
          return '年化波动';
        case 'recovrery_length':
          return '年化波动';
        case 'lose_days':
          return '最大连续下跌天数';
        case 'cum_return':
          return '区间收益';
        case 'ave_return':
          return '年化收益';
        case 'win_days':
          return '最大连续上涨天数';
        case 'maxdrawdown':
          return '最大回撤';
        case 'win_ratio':
          return '正收益率占比';
        case 'lose_ratio':
          return '负收益率占比';
        case 'rate_high':
          return '创新高次数';
        case 'sortino':
          return '索提诺比率';
        case 'sharpe0':
          return '夏普率';
        case 'information':
          return '信息率';
        default:
          return '';
      }
    },

    /**
     * 获取资产大类汇总表格数据
     */
    getSummaryData () {
      this.summary.loading = true;
      return getObjectAssetAllocation(this.params).then((res) => {
        this.summary.loading = false;
        if (res.code === 200) {
          this.summary.showEmpty = false;
          this.summary.oldTableData = this.summary.tableData = res.data.rows?.sort((a, b) => {
            // console.log(a.data.netasset, b.data.netasset)
            return Number(b?.data.netasset) - Number(a?.data.netasset)
          });
          this.setOptionsDl(res.data.rows)
          return res.data.rows;
        } else {
          this.summary.oldTableData = this.summary.tableData = [];
          this.summary.showEmpty = true;
          this.summary.options = {};
          return [];
        }
      });
    },

    fillMissingDates (fullDates, dataDates, data) {
      // 创建一个结果对象，key为日期，value为数据
      const result = {};

      // 将fullDates中的所有日期初始化为0
      fullDates.forEach(date => {
        result[date] = 0;
      });

      // 遍历dataDates，将已有数据填入result中
      dataDates.forEach((date, index) => {
        if (result.hasOwnProperty(date)) {
          result[date] = data[index];  // 填充原始数据
        }
      });

      // 将结果转换为数组返回（如果需要）
      return Object.keys(result).map(date => ([
        date,
        result[date]
      ]));
    },
    setOptionsDl (data) {
      this.summary.showEmpty = false;
      let line = []
      let color = ['#4096ff',
        '#4096ff',
        '#7388A9',
        '#E85D2D',
        '#9A89FF',
        '#6C96F2',
        '#FD6865',
        'rgba(253, 156, 255, 1)',
        '#83D6AE',
        'rgba(174, 201, 254, 1)',
        '#88C9E9',
        'rgba(169, 244, 208, 1)',
        '#6F80DD',
        'rgba(154, 137, 255, 1)',
        '#FD9CFF',
        'rgba(219, 174, 255, 1)',
        '#FED0EE',
        'rgba(159, 212, 253, 1)',
        '#ED589D',
        '#FEAEAE',
        'rgba(208, 232, 255, 1)',
        '#FDD09F',
        'rgba(251, 227, 142, 1)',
        '#FBE38E',
        '#A9F4D0',
        'rgba(253, 208, 159, 1)',
        '#D0E8FF',
        '#9FD4FD',
        'rgba(254, 174, 174, 1)',
        '#AEC9FE',
        '#DBAEFF',
        'rgba(254, 208, 238, 1)',
        '#FA541C'];
      let t1 = 0
      let t2 = 0
      let t3 = []
      let list = data.map((item, index) => {
        // console.log(item.data.date_list);
        t3 = t3.concat(eval(item.data.date_list))
        if (eval(item.data.date_list).length > t1) {
          t1 = eval(item.data.date_list).length
          t2 = index
        }
        if (item.data.name == '股票') {
          line.push({
            name: '股票占比',
            symbol: 'none',
            type: 'line',
            yAxisIndex: 1,
            lineStyle: {
              opacity: 2
            },
            color: color[data.length],
            data: eval(item.data.date_list).map((items, indexs) => {
              return [items, eval(item.data.weight_list)[indexs]]
            })
          })
        }
        line.push({
          name: item.data.name,
          symbol: 'none',
          type: 'line',
          stack: '总量',
          yAxisIndex: 0,
          lineStyle: {
            opacity: 0
          },
          areaStyle: {
            color: color[index],
            opacity: 1
          },
          color: color[index],
          data: eval(item.data.date_list).map((items, indexs) => {
            return [items, eval(item.data.netasset_list)[indexs]]
          })

        })
        return {
          ...item
        }
      })
      let dateUseList = [...new Set(t3)].sort((a, b) => {
        if (a > b) return 1
        else return -1
      })
      console.log(dateUseList);
      line = line.map((item, index) => {
        return {
          ...item,
          data: this.fillMissingDates(dateUseList, (item.data.map(item => item[0])), (item.data.map(item => item[1])))
        }

      })
      this.summary.options = lineChartOption({
        grid: { left: '24px', right: '24px', top: '48px', bottom: '0px' }, // 位置
        dataZoom: false,
        toolbox: false,
        legend: {
          data: data.map((item) => {
            return item.data.name
          }),
        },
        tooltip: {
          formatter: function (obj) {
            // console.log(obj);
            let value = `<div style="font-size:14px;">` + obj?.[0].axisValue + `</div>`;
            for (let i = 0; i < obj.length; i++) {
              if (obj?.[i].seriesName === '股票占比') {

                value +=
                  `<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
                  `<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
                  obj?.[i].color +
                  `;"></div>` +
                  `<div style="font-family: PingFang SC;">` +
                  obj?.[i].seriesName +
                  '</div></div>' +
                  `<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
                  Number(obj?.[i].value[1] * 100).toFixed(2) +
                  '%</div>' +
                  `</div>`;
              }
              if (obj?.[i].seriesName != '股票占比') {

                value +=
                  `<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
                  `<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
                  obj?.[i].color +
                  `;"></div>` +
                  `<div style="font-family: PingFang SC;">` +
                  obj?.[i].seriesName +
                  '</div></div>' +
                  `<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
                  Number(obj?.[i].value[1] / 100000000).toFixed(2) +
                  '亿</div>' +
                  `</div>`;

              }
            }
            return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;

          }
        },
        xAxis: [
          {
            type: 'category',
            name: '',
            data: dateUseList,
          }
        ],
        series: line,
        yAxis: [
          {
            type: 'value',
            name: '规模',
            min: 0,
            nameTextStyle: {
              align: 'right'
            },
            formatter: function (value, index) {
              //Y轴的自定义刻度值，对应上图
              return `${(value / 100000000).toFixed(1)}亿`;
            }
          },
          {
            type: 'value',
            name: '股票占比',
            nameTextStyle: {
              align: 'right'
            },
            formatter: function (value, index) {
              //Y轴的自定义刻度值，对应上图
              return `${(value * 100).toFixed(0)}%`;
            }
          }
        ]
      });
      console.log(this.summary.options);
    },
    /**
     * 基本颗粒度比较
     */
    getParticleTable () {
      this.particle.loading = true;
      return getObjectCutAllocation(this.params).then((res) => {
        this.particle.loading = false;
        if (res.code === 200) {
          this.particle.oldTableData = res.data.rows?.sort((a, b) => {
            // console.log(a.data.netasset, b.data.netasset)
            return Number(b?.data.netasset) > Number(a?.data.netasset) ? 1 : -1;
          });
          this.particle.tableData = res.data.rows?.sort((a, b) => {
            // console.log(a.data.netasset, b.data.netasset)
            return Number(b?.data.netasset) > Number(a?.data.netasset) ? 1 : -1;
          }).slice(0, this.granularityPagination.pageSize);
          return res.data.rows;
        } else {
          this.particle.oldTableData = this.particle.tableData = [];
          return [];
        }
      });
    },

    /**
     * 基本颗粒度贡献
     */
    getParticleChart () {
      getObjectCutPerformance(this.params).then((res) => {
        if (res.code === 200) {
          this.particle.showEmpty = false;
          let obj = {};
          let data = []; // 基准指数
          let date = []; // 时间
          let dataName = ''; // 基准指数名称
          let arr = ['index_rate']; // 基准数据的key
          let legendArray = [];

          res.data.columns.forEach((item) => {
            if (item.name !== 'date' && item.name !== 'index_name' && item.name !== 'index_rate') {
              arr.push(item.name);
              legendArray.push(item.name);
            }
          });

          // 时间排序
          res.data.rows.sort((a, b) => {
            const dateA = new Date(a.data.date);
            const dateB = new Date(b.data.date);
            return dateA - dateB;
          });
          res.data.rows.forEach((item, index) => {
            arr.forEach((v) => {
              if (!obj[v]) {
                obj[v] = [];
              }
              if (item.data[v] === 'nan' || item.data[v] === 'NaN' || !item.data[v]) {
                let index = obj[v].length - 1;
                if (index !== 0) {
                  obj[v].push(obj[v][index]);
                } else {
                  obj[v].push(0);
                }
              } else {
                obj[v].push(handleData(item.data[v], true));
              }
            });
            date.push(item.data.date);
            dataName = item.data.index_name;
          });
          data = obj['index_rate'];
          legendArray.push(dataName);
          delete obj['index_rate'];
          // 貌似data是基准的图表数据，
          this.getParticleChartData(data, dataName, obj, legendArray, date);
        } else {
          this.particle.showEmpty = true;
        }
      });
    },

    /**
     * 基金持仓分析
     */
    getFundPosition () {
      this.fundData.loading = true;
      return getObjectFundStat(this.params).then((res) => {
        this.fundData.loading = false;
        if (res.code === 200) {
          const array = res.data.rows.sort((a, b) => b.data.assets - a.data.assets);
          this.fundData.tableData = this.fundData.oldTableData = array;
          return array;
        } else {
          this.fundData.tableData = [];
          this.fundData.oldTableData = [];
          return [];
        }
      });
    },
    getFundPosition2 () {
      this.fundData.loading2 = true;
      return getObjectFundStat2(this.params).then((res) => {
        this.fundData.loading2 = false;
        if (res.code === 200) {
          const array = res.data.rows.sort((a, b) => b.data.assets - a.data.assets);
          this.fundData.tableData2 = this.fundData.oldTableData2 = array;
          return array;
        } else {
          this.fundData.tableData2 = [];
          this.fundData.oldTableData2 = [];
          return [];
        }
      });
    },
    /**
     * 表格数据生成
     */
    getPerformanceChartData (hsData, allData, withdrawal, excess, date, indexName) {
      // console.log(hsData, allData, withdrawal, excess, date);
      // 产品业绩表现表
      const line = [
        {
          name: '回撤',
          symbol: 'none',
          type: 'line',
          yAxisIndex: 1,
          lineStyle: {
            opacity: 0
          },
          areaStyle: {
            color: '#E9E6ED',
            opacity: 1
          },
          color: '#E9E6ED',
          data: withdrawal
        },
        {
          name: indexName,
          type: 'line',
          symbol: 'none',
          lineStyle: {
            width: 2
          },
          data: hsData
        },
        {
          name: '分析对象',
          type: 'line',
          symbol: 'none',
          lineStyle: {
            width: 2
          },
          data: allData
        },
        {
          name: '超额收益',
          type: 'line',
          symbol: 'none',
          lineStyle: {
            opacity: 0
          },
          areaStyle: {
            color: '#d0bce5',
            opacity: 0.5
          },
          color: '#d0bce5',
          data: excess
        }
      ];
      this.cpyjbxTitle = ['', '回撤(%)', indexName + '(%)', '分析对象(%)', '超额收益(%)'];
      this.cpyjbxColumn = [date, withdrawal, hsData, allData, excess];
      this.performance.options = lineChartOption({
        grid: { left: '48px', right: '48px', top: '48px', bottom: '48px' }, // 位置
        dataZoom: false,
        toolbox: false,
        legend: {
          data: ['分析对象', indexName, '超额收益', '回撤']
        },
        tooltip: {
          formatter: function (obj) {
            let value = `<div style="font-size:14px;">` + obj?.[0].axisValue + `</div>`;
            for (let i = 0; i < obj.length; i++) {
              value +=
                `<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
                `<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
                obj?.[i].color +
                `;"></div>` +
                `<div style="font-family: PingFang SC;">` +
                obj?.[i].seriesName +
                '</div></div>' +
                `<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
                Number(obj?.[i].value) +
                '%</div>' +
                `</div>`;
            }
            return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
          }
        },
        xAxis: [
          {
            name: '',
            data: date
          }
        ],
        series: line,
        yAxis: [
          {
            type: 'value',
            name: '收益率',
            nameTextStyle: {
              align: 'right'
            },
            formatter: function (value, index) {
              //Y轴的自定义刻度值，对应上图
              return `${value}%`;
            }
          },
          {
            type: 'value',
            name: '回撤',
            max: 0,
            nameTextStyle: {
              align: 'left'
            },
            formatter: function (value, index) {
              //Y轴的自定义刻度值，对应上图
              return `${value}%`;
            }
          }
        ]
      });
      return this.performance.options;
    },

    /**
     * 颗粒度表格生成
     */
    getParticleChartData (data, dataName, obj, arr, date) {
      const line = [
        {
          name: dataName,
          type: 'line',
          symbol: 'none',
          lineStyle: {
            width: 2
          },
          data
        }
      ];
      this.jbkldyjgxbjTitle = ['', ...arr];
      const dataArray = [date];
      arr.map((name) => {
        if (name === dataName) {
          // 基准数据特殊处理
          dataArray.push(data);
        } else if (obj[name]) {
          dataArray.push(obj[name]);
        }
      });
      this.jbkldyjgxbjColumn = dataArray;
      for (let key in obj) {
        if (key !== line[0].name) {
          line.push({
            name: key,
            type: 'bar',
            symbol: 'none',
            stack: 'Total',
            emphasis: {
              focus: 'series'
            },
            lineStyle: {
              width: 2
            },
            areaStyle: {},
            data: obj[key]
          });
        }
      }
      this.particle.options = lineChartOption({
        grid: { left: '24px', right: '48px', top: '24px', bottom: '36px' }, // 位置
        dataZoom: false,
        toolbox: false,
        tooltip: {
          formatter: function (obj) {
            let value = `<div style="font-size:14px;">` + obj?.[0].axisValue + `</div>`;
            for (let i = 0; i < obj.length; i++) {
              value +=
                `<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
                `<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
                obj?.[i].color +
                `;"></div>` +
                `<div style="font-family: PingFang SC;">` +
                obj?.[i].seriesName +
                '</div></div>' +
                `<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
                Number(obj?.[i].value) +
                '%</div>' +
                `</div>`;
            }
            return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
          }
        },
        legend: {
          data: arr
        },
        xAxis: [
          {
            name: '日期',
            data: date
          }
        ],
        series: line,
        yAxis: [
          {
            type: 'value',
            formatter: function (value, index) {
              //Y轴的自定义刻度值，对应上图
              return `${value}%`;
            }
          }
        ]
      });
    },

    /**
     * 分页切换
     */
    fundSizeChange (size) {
      this.fundData.pageSize = size;
    },
    fundSizeChange2 (size) {
      this.fundData.pageSize2 = size;
    },
    granularitySizeChange (size) {
      this.granularityPagination.pageSize = size;
      this.particle.tableData = this.particle.oldTableData.slice(0, this.granularityPagination.pageSize)
    },
    fundCurrentChange (size) {
      this.fundData.pageIndex = size;
    },
    fundCurrentChange2 (size) {
      this.fundData.pageIndex2 = size;
    },
    granularityCurrentChange (size) {
      this.particle.tableData = this.particle.oldTableData.slice((size - 1) * this.granularityPagination.pageSize, size * this.granularityPagination.pageSize)
      // this.granularityPagination.pageSize = size;
    },

    /**
     * 数据格式
     */
    formatter (row, column, cellValue, index) {
      // 期末规模
      if (column.label === '期末规模（亿）') {
        if (cellValue === 'nan' || cellValue === 'NaN' || cellValue === undefined || !cellValue) {
          return cellValue;
        }
        if (Number((Number(cellValue) / 100000000).toFixed(2)) !== 0) {
          return (Number(cellValue) / 100000000).toFixed(2);
        } else {
          return '0.00（' + (Number(cellValue) / 10000).toFixed(2) + '万）';
        }
      }
      // 净买入
      if (column.label === '净买入（万）' || column.label === '财务收益（万）' || column.label === '市值收益（万）') {
        if (cellValue === 'nan' || cellValue === 'NaN' || cellValue === undefined || !cellValue) {
          return '--';
        }
        return (Number(cellValue) / 10000).toFixed(2);
      }
      // 基金经理平均管理时间
      if (column.label === '基金经理平均管理时间（年）') {
        return Number(cellValue).toFixed(2);
      }
      // 前十大集中度(最近一期) || 换手率(最近一期)
      if (column.label === '前十大集中度(最近一期)') {
        if (cellValue === 'nan' || cellValue === 'NaN' || cellValue === undefined || !cellValue) {
          return '--';
        }
        return Number(cellValue).toFixed(2) + '%';
      }
      // 前十大集中度(最近一期) || 换手率(最近一期)
      if (column.label === '换手率(最近一期)') {
        if (cellValue === 'nan' || cellValue === 'NaN' || cellValue === undefined || !cellValue) {
          return '--';
        }
        return (Number(cellValue) * 100).toFixed(2) + '%';
      }
    },
    async waitDom () {
      return new Promise((resovel) => {
        setTimeout(() => {
          resovel('等待dom加载完成');
        }, 1000);
      });
    },
    async createPrintWord () {
      const getImg = (ref) => {
        const chart1 = this.$refs[ref];
        if (!chart1) return null;
        chart1.mergeOptions({ toolbox: { show: false } });
        const height = chart1.$el.clientHeight;
        const width = chart1.$el.clientWidth;
        const img = chart1.getConnectedDataURL({
          type: 'jpg',
          pixelRatio: 2,
          backgroundColor: '#fff',
          excludeComponents: ['dataZoom']
        });
        const chartImg = exportChart(img, { width, height });
        chart1.mergeOptions({ toolbox: { show: true } });
        return chartImg;
      };
      return Promise.all([
        this.getPerformanceChart(),
        this.getPerformanceTable(),
        this.getSummaryData(),
        this.getParticleTable(),
        this.getParticleChart(),
        this.getFundPosition(),
        this.getFundPosition2(),
        this.waitDom()
      ]).then((arr) => {
        const head = exportFirstTitle('一、产品业绩表现');
        const subhead1 = exportTitle('产品业绩表现');
        var wordFormat = new Format();
        // 图表1
        const chartimg1 = getImg('barLineChartComponent');

        // 图表1下面表格
        const title1 = [
          { label: '区间指数', value: 'key' },
          { label: '产品', value: 'allValue' },
          { label: '基准', value: 'value' }
        ];
        let array1 = arr[1] && arr[1].length > 10 ? arr[1].slice(0, 10) : arr[1];
        array1 = array1.map((obj) => {
          let item = JSON.parse(JSON.stringify(obj));
          const set1 = new Set(['夏普率', '信息率']);
          const set2 = new Set(['最大连续上涨天数', '最大连续下跌天数']);
          if (set1.has(item.key)) {
            item.allValue = wordFormat['fix2b'](item.allValue);
            item.value = wordFormat['fix2b'](item.value);
          } else if (set2.has(item.key)) {
            // 无
          } else {
            item.allValue = wordFormat['fix2p'](item.allValue);
            item.value = wordFormat['fix2p'](item.value);
          }
          return item;
        });
        const table1 = exportTable(title1, array1, {}, {});
        // 表格2
        const subhead2 = exportTitle('大类资产业绩汇总');
        let array2 = arr[2].map((item) => {
          return item.data;
        });
        const title2 = [
          { label: '资产类别', value: 'name' },
          { label: '期末规模(亿)', value: 'netasset', format: 'fix8ns' },
          { label: '净买入(万)', value: 'total_buy', format: 'fix4ns' },
          { label: '财务收益(万)', value: 'financialIncomeChange', format: 'fix4ns' },
          { label: '市值收益(万)', value: 'marketValueGainLossChange', format: 'fix4ns' },
          { label: '市值收益率', value: 'eversincecum_return', format: 'fix2p' },
          { label: '基准收益率', value: 'index_cum_return', format: 'fix2p' },
          { label: '超额收益率', value: 'eversinceexcess_cum_return', format: 'fix2p' },
          { label: '最大回撤', value: 'eversincemaxdrawdown', format: 'fix2p' },
          { label: '年化波动率', value: 'eversincevolatility', format: 'fix2p' },
          { label: '超额收益最大回撤', value: 'eversinceexcess_maxdrawdown', format: 'fix2p' }
        ];
        const table2 = exportTable(title2, array2, {}, {});
        // 表格3
        const subhead3 = exportTitle('基本颗粒度对比');
        let array3 = arr[3].map((item) => {
          return item.data;
        });
        const table3 = exportTable(title2, array3, {}, {});
        // 图表4
        const subhead4 = exportTitle('基本颗粒度业绩贡献比较');
        const chartimg4 = getImg('barLineChartComponent_jbkldyjgxbj');
        // 表格5；
        let array5 = arr[5].map((item) => {
          return item.data;
        });
        // const subhead5 = exportSencondTitle('基金持仓分析')
        const subhead5 = exportTitle('基金持仓分析');
        const title51 = [
          { label: '基金名称', rowSpan: 2 },
          { label: '基金信息', columnSpan: 5 },
          { label: '持仓及损益', columnSpan: 5 },
          { label: '市场行情', columnSpan: 3 }
        ];
        const title52 = [
          { label: '基金类型' },
          { label: '基金代码' },
          { label: '基金经理' },
          { label: '基金经理平均管理时间（年）' },
          { label: '前十大集中度(最近一期)' },
          { label: '换手率(最近一期)' },
          { label: '期末规模（亿）' },
          { label: '持仓权重' },
          { label: '财务收益（万）' },
          { label: '市值收益（万）' },
          { label: '累计浮盈（万）' },
          { label: '年初至今' },
          { label: '近一周' },
          { label: '近一月' }
        ];
        const format5 = [
          { value: 'assetsName', format: '' },
          { value: 'type', format: '' },
          { value: 'assetsCode', format: '' },
          { value: 'managerName', format: '' },
          { value: 'managedTime', format: 'fix2' },
          { value: 'top10Concentration', format: 'fix2b' },

          { value: 'turnover', format: 'fix2b' },
          { value: 'assets', format: 'fix8ns' },
          { value: 'weight', format: 'fix2p' },
          { value: 'financialIncomeChange', format: 'fix4ns' },
          { value: 'marketValueGainLossChange', format: 'fix4ns' },

          { value: 'profitChange', format: 'fix4ns' },
          { value: 'cum_returntheyear', format: 'fix2p' },
          { value: 'cum_return1week', format: 'fix2p' },
          { value: 'cum_return1month', format: 'fix2p' }
        ];
        const table5 = exportTableMergeHeader([title51, title52], array5, format5, {}, {});

        return [
          ...(head || []),
          ...(subhead1 || []),
          ...(chartimg1 || []),
          ...(table1 || []),
          ...(subhead2 || []),
          ...(table2 || []),
          ...(subhead3 || []),
          ...(table3 || []),
          ...(subhead4 || []),
          ...(chartimg4 || []),
          ...(subhead5 || []),
          ...(table5 || [])
        ];
      });
    },
    downloadExcel (name) {
      if ('产品业绩表现' === name) {
        const format = ['', 'fixb0', 'fixb0', 'fixb0', 'fixb0'];
        const data = changColumnToRow(this.cpyjbxColumn, format);
        export_json_to_excel_multiHeader([this.cpyjbxTitle], null, data, name);
        return;
      } else if ('大类资产业绩汇总' === name) {
        const title = [
          { label: '资产类别', value: 'name' },
          { label: '期末规模(亿)', value: 'netasset', format: 'fix8ns' },
          { label: '净买入(万)', value: 'total_buy', format: 'fix4ns' },
          { label: '财务收益(万)', value: 'financialIncomeChange', format: 'fix4ns' },
          { label: '市值收益(万)', value: 'marketValueGainLossChange', format: 'fix4ns' },
          { label: '市值收益率', value: 'eversincecum_return', format: 'fix2p' },
          { label: '基准收益率', value: 'index_cum_return', format: 'fix2p' },
          { label: '超额收益率', value: 'eversinceexcess_cum_return', format: 'fix2p' },
          { label: '最大回撤', value: 'eversincemaxdrawdown', format: 'fix2p' },
          { label: '年化波动率', value: 'eversincevolatility', format: 'fix2p' },
          { label: '超额收益最大回撤', value: 'eversinceexcess_maxdrawdown', format: 'fix2p' }
        ];
        var data = this.summary.tableData;
        filter_json_to_excel_inside(title, data, ['data'], name);
        return;
      } else if ('基本颗粒度对比' === name) {
        const title = [
          { label: '颗粒度', value: 'name' },
          { label: '期末规模(亿)', value: 'netasset', format: 'fix8ns' },
          { label: '净买入(万)', value: 'total_buy', format: 'fix4ns' },
          { label: '财务收益(万)', value: 'financialIncomeChange', format: 'fix4ns' },
          { label: '市值收益(万)', value: 'marketValueGainLossChange', format: 'fix4ns' },
          { label: '市值收益率', value: 'eversincecum_return', format: 'fix2p' },
          { label: '基准收益率', value: 'index_cum_return', format: 'fix2p' },
          { label: '超额收益率', value: 'eversinceexcess_cum_return', format: 'fix2p' },
          { label: '最大回撤', value: 'eversincemaxdrawdown', format: 'fix2p' },
          { label: '年化波动率', value: 'eversincevolatility', format: 'fix2p' },
          { label: '超额收益最大回撤', value: 'eversinceexcess_maxdrawdown', format: 'fix2p' }
        ];
        const data = this.particle.tableData;
        filter_json_to_excel_inside(title, data, ['data'], name);
        return;
      } else if ('大类资产配置结构' === name) {
        console.log(this.summary.tableData);
        const format = ['', 'fixb0', 'fixb0', 'fixb0'];
        const data = this.summary.options.map()

        export_json_to_excel_multiHeader(this.summary.options.map(item => {
          return item.name
        }), null, data, name);
        return;
      }
      else if ('基本颗粒度业绩贡献比较' === name) {
        const format = ['', 'fixb0', 'fixb0', 'fixb0'];
        const data = changColumnToRow(this.jbkldyjgxbjColumn, format);
        export_json_to_excel_multiHeader([this.jbkldyjgxbjTitle], null, data, name);
        return;
      } else if ('基金持仓分析' === name) {
        const title1 = ['基金名称', '基金信息', '', '', '', '', '', '', '持仓及损益', '', '', '', '市场行情', '', ''];
        const title2 = [
          '',
          '基金类型',
          '基金代码',
          '基金经理',
          '基金经理平均管理时间（年）',
          '前十大集中度(最近一期)',
          '换手率(最近一期)',
          '期末规模（亿）',
          '持仓权重',
          '财务收益（万）',
          '市值收益（万）',
          '累计浮盈（万）',
          '年初至今',
          '近一周',
          '近一月'
        ];
        const merge = ['A1:A2', 'B1:K1', 'L1:M1'];
        const data = this.fundData.tableData;
        const format = [
          { value: 'assetsName', format: '' },
          { value: 'type', format: '' },
          { value: 'assetsCode', format: '' },
          { value: 'managerName', format: '' },
          { value: 'managedTime', format: 'fix2' },
          { value: 'top10Concentration', format: 'fix2b' },

          { value: 'turnover', format: 'fix2b' },
          { value: 'assets', format: 'fix8ns' },
          { value: 'weight', format: 'fix2p' },
          { value: 'financialIncomeChange', format: 'fix4ns' },
          { value: 'marketValueGainLossChange', format: 'fix4ns' },

          { value: 'profitChange', format: 'fix4ns' },
          { value: 'cum_returntheyear', format: 'fix2p' },
          { value: 'cum_return1week', format: 'fix2p' },
          { value: 'cum_return1month', format: 'fix2p' }
        ];
        filter_json_to_excel_inside_multiHeader([title1, title2], data, ['data'], name, merge, format);
      } else if ('单一资产管理计划和保险资管产品持仓分析' === name) {
        const title1 = ['名称', '信息', '持仓及损益', '', '', '', ''];
        const title2 = [
          '',
          '代码',
          '期末规模（亿）',
          '持仓权重',
          '财务收益（万）',
          '市值收益（万）',
          '累计浮盈（万）',
        ];
        const merge = ['A1:A2', 'B1:K1', 'L1:M1'];
        const data = this.fundData.tableData2;
        const format = [
          { value: 'name', format: '' },
          { value: 'code', format: '' },

          { value: 'assets', format: 'fix8ns' },
          { value: 'weight', format: 'fix2p' },
          { value: 'financialIncomeChange', format: 'fix4ns' },
          { value: 'marketValueGainLossChange', format: 'fix4ns' },

          { value: 'profitChange', format: 'fix4ns' },

        ];
        filter_json_to_excel_inside_multiHeader([title1, title2], data, ['data'], name, merge, format);
      }
      // let list = [['a1','b1','c1'],['','b2','']];
      // export_json_to_excel_multiHeader(list,['A1:A2','B2:C2'],[['1a','1b','1c'],['2a','2b','2c'],['3a','3b','3c']],'12313')
    },
    uploadPage () {
      this.params = {
        reportID: Number(this.$route.query.id),
        startFrom: Number(this.moment(this.$route.query.startDate).format('YYYYMMDD')),
        endTo: Number(this.moment(this.$route.query.endDate).format('YYYYMMDD')),
        industryStandard: 3,
        selectedCuts: this.$route.query.graininess
      };
      this.getPerformanceChart();
      setTimeout(() => {
        this.getPerformanceTable();
        setTimeout(() => {
          this.getSummaryData();
          setTimeout(() => {
            this.getParticleTable();
            setTimeout(() => {
              this.getParticleChart();
              setTimeout(() => {
                this.getFundPosition();
                this.getFundPosition2();
              }, 500);
            }, 500);
          }, 500);
        }, 500);
      }, 500);
    }
  },
  mounted () { }
};
</script>

<template>
  <div>
    <div class="page-box">
      <div class="flex item-center justify-between">
        <div class="area-title">产品业绩表现</div>
        <img alt=""
             src="../../../../../assets/img/download.png"
             class="download"
             @click="downloadExcel('产品业绩表现')" />
      </div>
      <el-divider></el-divider>
      <div class="area-body">
        <div class="chart">
          <v-chart ref="barLineChartComponent"
                   autoresize
                   v-show="!performance.showEmpty"
                   id="1"
                   element-loading-text="暂无数据"
                   element-loading-spinner="el-icon-document-delete"
                   element-loading-background="rgba(239, 239, 239, 0.5)"
                   style="height: 340px; width: 100% !important"
                   :options="performance.options" />
          <el-empty v-show="performance.showEmpty"
                    :image-size="200"></el-empty>
        </div>
        <div class="table">
          <div class="flex">
            <el-table :data="performance.tableData.slice(0, 5)"
                      border
                      stripe
                      v-loading="performance.loading">
              <el-table-column align="gotoleft"
                               label="区间指数"
                               prop="key" />
              <el-table-column align="right"
                               label="产品">
                <template slot-scope="scope">
                  <div>
                    {{
											scope.row.format
												? (scope.row.key === '夏普率' || scope.row.key === '信息率') && scope.row.allValue
													? `${Number(scope.row.allValue).toFixed(2)}`
													: handleData(scope.row.allValue)
												: scope.row.allValue
										}}
                  </div>
                </template>
              </el-table-column>
              <el-table-column align="right"
                               label="基准">
                <template slot-scope="scope">
                  <div>
                    {{
											scope.row.format
												? (scope.row.key === '夏普率' || scope.row.key === '信息率') && scope.row.value
													? `${Number(scope.row.value).toFixed(2)}`
													: handleData(scope.row.value)
												: scope.row.value
										}}
                  </div>
                </template>
              </el-table-column>
              <el-empty :image-size="180" />
            </el-table>
            <el-table :data="performance.tableData.slice(5, 10)"
                      border
                      stripe
                      v-loading="performance.loading">
              <el-table-column align="gotoleft"
                               label="区间指数"
                               prop="key" />
              <el-table-column align="right"
                               label="产品">
                <template slot-scope="scope">
                  <div>
                    {{
											scope.row.format
												? (scope.row.key === '夏普率' || scope.row.key === '信息率') && scope.row.allValue
													? `${Number(scope.row.allValue).toFixed(2)}%`
													: handleData(scope.row.allValue)
												: scope.row.allValue
										}}
                  </div>
                </template>
              </el-table-column>
              <el-table-column align="right"
                               label="基准">
                <template slot-scope="scope">
                  <div>
                    {{
											scope.row.format
												? (scope.row.key === '夏普率' || scope.row.key === '信息率') && scope.row.value
													? `${Number(scope.row.value).toFixed(2)}%`
													: handleData(scope.row.value)
												: scope.row.value
										}}
                  </div>
                </template>
              </el-table-column>
              <el-empty :image-size="180" />
            </el-table>
          </div>
        </div>
      </div>
    </div>
    <div class="page-box">
      <div class="flex item-center justify-between">
        <div class="area-title">大类资产业绩汇总</div>
        <img alt=""
             src="../../../../../assets/img/download.png"
             class="download"
             @click="downloadExcel('大类资产业绩汇总')" />
      </div>
      <el-divider></el-divider>
      <div class="area-body">
        <div class="table">
          <el-table v-loading="summary.loading"
                    :data="summary.tableData"
                    border
                    stripe
                    @sort-change="sortSummary">
            <el-table-column align="gotoleft"
                             label="资产类别"
                             prop="data.name" />
            <el-table-column align="right"
                             label="期末规模（亿）"
                             prop="data.netasset"
                             sortable="custom"
                             :formatter="formatter" />
            <el-table-column align="right"
                             label="净买入（万）"
                             prop="data.total_buy"
                             sortable="custom"
                             :formatter="formatter" />
            <el-table-column align="right"
                             label="财务收益（万）"
                             prop="data.financialIncomeChange"
                             sortable="custom"
                             :formatter="formatter" />
            <el-table-column align="right"
                             label="市值收益（万）"
                             prop="data.marketValueGainLossChange"
                             sortable="custom"
                             :formatter="formatter" />
            <el-table-column align="right"
                             label="市值收益率"
                             sortable="custom"
                             prop="data.eversincecum_return">
              <template slot-scope="scope">
                <div>
                  {{ handleData(scope.row.data.eversincecum_return) }}
                </div>
              </template>
            </el-table-column>
            <el-table-column align="right"
                             label="基准收益率"
                             prop="data.index_cum_return)"
                             sortable="custom">
              <template slot-scope="scope">
                <div>{{ handleData(scope.row.data.index_cum_return) }}</div>
              </template>
            </el-table-column>
            <el-table-column align="right"
                             label="超额收益率"
                             prop="data.eversinceexcess_cum_return"
                             sortable="custom">
              <template slot-scope="scope">
                <div>{{ handleData(scope.row.data.eversinceexcess_cum_return) }}</div>
              </template>
            </el-table-column>
            <el-table-column align="right"
                             label="最大回撤"
                             prop="data.eversincemaxdrawdown"
                             sortable="custom">
              <template slot-scope="scope">
                <div>{{ handleData(scope.row.data.eversincemaxdrawdown) }}</div>
              </template>
            </el-table-column>
            <el-table-column align="right"
                             label="年化波动率"
                             prop="data.eversincevolatility"
                             sortable="custom">
              <template slot-scope="scope">
                <div>{{ handleData(scope.row.data.eversincevolatility) }}</div>
              </template>
            </el-table-column>
            <el-table-column align="right"
                             label="超额收益最大回撤"
                             prop="data.eversinceexcess_maxdrawdown"
                             sortable="custom">
              <template slot-scope="scope">
                <div>{{ handleData(scope.row.data.eversinceexcess_maxdrawdown) }}</div>
              </template>
            </el-table-column>
            <el-empty :image-size="180" />

          </el-table>

        </div>
      </div>
    </div>

    <!--大类资产业绩汇总的时序图 TODO 新需求 -->
    <div class="page-box">
      <div class="flex item-center justify-between">
        <div class="area-title">大类资产配置结构</div>
        <img alt=""
             src="../../../../../assets/img/download.png"
             class="download"
             @click="downloadExcel('大类资产配置结构')" />
      </div>
      <el-divider></el-divider>
      <div class="area-body">
        <div class="table">
          <v-chart ref="barLineChartComponent_dlzcyjhztp"
                   autoresize
                   v-show="!summary.showEmpty"
                   id="1"
                   element-loading-text="暂无数据"
                   element-loading-spinner="el-icon-document-delete"
                   element-loading-background="rgba(239, 239, 239, 0.5)"
                   style="height: 340px; width: 100% !important"
                   :options="summary.options" />
        </div>
      </div>
    </div>
    <div class="page-box">
      <div class="flex item-center justify-between">
        <div class="area-title">基本颗粒度对比</div>
        <img alt=""
             src="../../../../../assets/img/download.png"
             class="download"
             @click="downloadExcel('基本颗粒度对比')" />
      </div>
      <el-divider></el-divider>
      <div class="area-body">
        <div class="table">
          <el-table v-loading="particle.loading"
                    :data="particle.tableData"
                    border
                    :default-sort="{ prop: 'data.netasset', order: 'descending' }"
                    stripe
                    @sort-change="sortParticle">
            <el-table-column align="gotoleft"
                             label="颗粒度"
                             prop="data.name" />
            <el-table-column align="right"
                             label="期末规模（亿）"
                             prop="data.netasset"
                             sortable="custom"
                             :formatter="formatter" />
            <el-table-column align="right"
                             label="净买入（万）"
                             prop="data.total_buy"
                             sortable="custom"
                             :formatter="formatter" />
            <el-table-column align="right"
                             label="财务收益（万）"
                             prop="data.financialIncomeChange"
                             sortable="custom"
                             :formatter="formatter" />
            <el-table-column align="right"
                             label="市值收益（万）"
                             prop="data.marketValueGainLossChange"
                             sortable="custom"
                             :formatter="formatter" />
            <el-table-column align="gotoleft"
                             label="市值收益率"
                             prop="data.eversincecum_return"
                             sortable="custom">
              <template slot-scope="scope">
                <div>{{ handleData(scope.row.data.eversincecum_return) }}</div>
              </template>
            </el-table-column>
            <el-table-column align="gotoleft"
                             label="基准收益率"
                             prop="data.index_cum_return"
                             sortable="custom">
              <template slot-scope="scope">
                <div>{{ handleData(scope.row.data.index_cum_return) }}</div>
              </template>
            </el-table-column>
            <el-table-column align="gotoleft"
                             label="超额收益率"
                             prop="data.eversinceexcess_cum_return"
                             sortable="custom">
              <template slot-scope="scope">
                <div>{{ handleData(scope.row.data.eversinceexcess_cum_return) }}</div>
              </template>
            </el-table-column>
            <el-table-column align="gotoleft"
                             label="最大回撤"
                             prop="data.eversincemaxdrawdown"
                             sortable="custom">
              <template slot-scope="scope">
                <div>{{ handleData(scope.row.data.eversincemaxdrawdown) }}</div>
              </template>
            </el-table-column>
            <el-table-column align="gotoleft"
                             label="年化波动率"
                             prop="data.eversincevolatility"
                             sortable="custom">
              <template slot-scope="scope">
                <div>{{ handleData(scope.row.data.eversincevolatility) }}</div>
              </template>
            </el-table-column>
            <el-table-column align="gotoleft"
                             label="超额收益最大回撤"
                             prop="data.eversinceexcess_maxdrawdown"
                             sortable="custom">
              <template slot-scope="scope">
                <div>{{ handleData(scope.row.data.eversinceexcess_maxdrawdown) }}</div>
              </template>
            </el-table-column>
            <el-empty :image-size="180" />
          </el-table>
          <div class="pagination_board">
            <el-pagination :current-page.sync="granularityPagination.pageIndex"
                           :page-size="granularityPagination.pageSize"
                           :total="particle.oldTableData.length"
                           background
                           layout="total, sizes, prev, pager, next"
                           @size-change="granularitySizeChange"
                           @current-change="granularityCurrentChange" />
          </div>
        </div>
      </div>
    </div>
    <div class="page-box">
      <div class="flex item-center justify-between">
        <div class="area-title">基本颗粒度业绩贡献比较</div>
        <img alt=""
             src="../../../../../assets/img/download.png"
             class="download"
             @click="downloadExcel('基本颗粒度业绩贡献比较')" />
      </div>
      <el-divider></el-divider>
      <div class="area-body">
        <div class="chart">
          <v-chart ref="barLineChartComponent_jbkldyjgxbj"
                   autoresize
                   v-show="!particle.showEmpty"
                   id="1"
                   element-loading-text="暂无数据"
                   element-loading-spinner="el-icon-document-delete"
                   element-loading-background="rgba(239, 239, 239, 0.5)"
                   style="height: 340px; width: 100% !important"
                   :options="particle.options" />
          <el-empty v-show="particle.showEmpty"
                    :image-size="200"></el-empty>
        </div>
      </div>
    </div>
    <div class="page-box">
      <div class="flex item-center justify-between">
        <div class="area-title">基金持仓分析</div>
        <img alt=""
             src="../../../../../assets/img/download.png"
             class="download"
             @click="downloadExcel('基金持仓分析')" />
      </div>
      <el-divider></el-divider>
      <div class="area-body">
        <div class="table">
          <el-table v-loading="fundData.loading"
                    :data="fundData.tableData.slice((fundData.pageIndex - 1) * fundData.pageSize, fundData.pageIndex * fundData.pageSize)"
                    border
                    :default-sort="{ prop: 'data.assets', order: 'descending' }"
                    stripe
                    @sort-change="sortFundData">
            <el-table-column align="gotoleft"
                             label="基金名称"
                             prop="data.assetsName"
                             min-width="200" />
            <el-table-column align="center"
                             label="基金信息">
              <el-table-column label="基金类型"
                               align="gotoleft"
                               min-width="140">
                <template slot-scope="scope">
                  {{ scope.row.data.type }}
                </template>
              </el-table-column>
              <el-table-column label="基金代码"
                               align="gotoleft"
                               min-width="120"
                               prop="data.assetsCode"></el-table-column>
              <el-table-column label="基金经理"
                               align="gotoleft"
                               min-width="120"
                               prop="data.managerName"></el-table-column>
              <el-table-column label="基金经理平均管理时间（年）"
                               prop="data.managedTime"
                               align="gotoleft"
                               min-width="140"
                               sortable="custom"
                               :formatter="formatter" />
              <el-table-column label="前十大集中度(最近一期)"
                               prop="data.top10Concentration"
                               sortable="custom"
                               align="gotoleft"
                               min-width="140"
                               :formatter="formatter" />
              <el-table-column label="换手率(最近一期)"
                               prop="data.turnover"
                               align="gotoleft"
                               min-width="140"
                               sortable="custom"
                               :formatter="formatter" />
            </el-table-column>
            <el-table-column align="center"
                             label="持仓及损益">
              <el-table-column label="期末规模（亿）"
                               prop="data.assets"
                               sortable="custom"
                               align="gotoleft"
                               min-width="100"
                               :formatter="formatter" />
              <el-table-column label="持仓权重"
                               prop="data.weight"
                               align="gotoleft"
                               min-width="100"
                               sortable="custom">
                <template slot-scope="scope">
                  {{ handleData(scope.row.data.weight) }}
                </template>
              </el-table-column>
              <el-table-column label="财务收益（万）"
                               sortable="custom"
                               min-width="100"
                               prop="data.financialIncomeChange"
                               :formatter="formatter" />
              <el-table-column label="市值收益（万）"
                               min-width="100"
                               sortable="custom"
                               prop="data.marketValueGainLossChange"
                               :formatter="formatter" />
              <el-table-column sortable="custom"
                               min-width="100"
                               prop="data.profitChange"
                               label="累计浮盈（万）">
                <template slot-scope="scope">
                  {{ handleDataTenThousand2(scope.row.data.profitChange) }}
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column align="center"
                             label="市场表现">
              <el-table-column sortable="custom"
                               min-width="100"
                               prop="data.cum_returntheyear"
                               label="年初至今">
                <template slot-scope="scope">
                  {{ handleData(scope.row.data.cum_returntheyear) }}
                </template>
              </el-table-column>
              <el-table-column sortable="custom"
                               min-width="100"
                               prop="data.cum_return1week"
                               label="近一周">
                <template slot-scope="scope">
                  {{ handleData(scope.row.data.cum_return1week) }}
                </template>
              </el-table-column>
              <el-table-column sortable="custom"
                               min-width="100"
                               prop="data.cum_return1month"
                               label="近一月">
                <template slot-scope="scope">
                  {{ handleData(scope.row.data.cum_return1month) }}
                </template>
              </el-table-column>
            </el-table-column>
            <el-empty :image-size="180" />
          </el-table>
          <div class="pagination_board">
            <el-pagination :current-page.sync="fundData.pageIndex"
                           :page-size="fundData.pageSize"
                           :total="fundData.tableData.length"
                           background
                           layout="total, sizes, prev, pager, next"
                           @size-change="fundSizeChange"
                           @current-change="fundCurrentChange" />
          </div>
        </div>
      </div>
    </div>
    <div class="page-box">
      <div class="flex item-center justify-between">
        <div class="area-title">单一资产管理计划和保险资管产品持仓分析</div>
        <img alt=""
             src="../../../../../assets/img/download.png"
             class="download"
             @click="downloadExcel('单一资产管理计划和保险资管产品持仓分析')" />
      </div>
      <el-divider></el-divider>
      <div class="area-body">
        <div class="table">
          <el-table v-loading="fundData.loading2"
                    :data="fundData.tableData2.slice((fundData.pageIndex2 - 1) * fundData.pageSize2, fundData.pageIndex2 * fundData.pageSize2)"
                    border
                    :default-sort="{ prop: 'data.assets', order: 'descending' }"
                    stripe
                    @sort-change="sortFundData2">
            <el-table-column align="gotoleft"
                             label="名称"
                             prop="data.name"
                             min-width="200" />
            <el-table-column align="center"
                             label="基础信息">
              <el-table-column label="代码"
                               align="gotoleft"
                               min-width="120"
                               prop="data.code"></el-table-column>

            </el-table-column>
            <el-table-column align="center"
                             label="持仓及损益">
              <el-table-column label="期末规模（亿）"
                               prop="data.assets"
                               sortable="custom"
                               align="gotoleft"
                               min-width="100"
                               :formatter="formatter" />
              <el-table-column label="持仓权重"
                               prop="data.weight"
                               align="gotoleft"
                               min-width="100"
                               sortable="custom">
                <template slot-scope="scope">
                  {{ handleData(scope.row.data.weight) }}
                </template>
              </el-table-column>
              <el-table-column label="财务收益（万）"
                               sortable="custom"
                               min-width="100"
                               prop="data.financialIncomeChange"
                               :formatter="formatter" />
              <el-table-column label="市值收益（万）"
                               min-width="100"
                               sortable="custom"
                               prop="data.marketValueGainLossChange"
                               :formatter="formatter" />
              <el-table-column sortable="custom"
                               min-width="100"
                               prop="data.profitChange"
                               label="累计浮盈（万）">
                <template slot-scope="scope">
                  {{ handleDataTenThousand2(scope.row.data.profitChange) }}
                </template>
              </el-table-column>
            </el-table-column>
            <el-empty :image-size="180" />
          </el-table>
          <div class="pagination_board">
            <el-pagination :current-page.sync="fundData.pageIndex2"
                           :page-size="fundData.pageSize2"
                           :total="fundData.tableData2.length"
                           background
                           layout="total, sizes, prev, pager, next"
                           @size-change="fundSizeChange2"
                           @current-change="fundCurrentChange2" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
@import '../../../tkdesign';

.border_table_header_search {
	display: flex;
	justify-content: flex-end;
	position: relative;

	.selector {
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
		line-height: 22px;
		margin-top: 5px;
		color: rgba(0, 0, 0, 0.85);
	}

	.search-security {
		width: 250px;
		margin-right: 10px;
	}
}

.pagination_board {
	text-align: right;
	margin-top: 16px;
}
</style>
