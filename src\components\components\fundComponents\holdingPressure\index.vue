<template>
	<div class="chart_one holdingPressure">
		<analysis-card-title title="持有压力" @downloadExcel="exportExcel">
			<el-select v-model="activeBenchmark" @change="changeBenchmarkValue" placeholder="请选择比较基准">
				<el-option v-for="item in benchmarkList" :key="item.code" :label="item.name" :value="item.code"> </el-option>
			</el-select>
		</analysis-card-title>
		<el-table v-loading="loading" :data="data" class="table" border stripe ref="multipleTable" header-cell-class-name="table-header">
			<el-table-column v-for="item in column" :key="item.value" :align="item.align" :prop="item.value" :label="item.label">
				<template #header>
					<long-table-popover-chart
						v-if="item.popover"
						:data="formatTableData()"
						date_key="hold_length"
						:data_key="item.value"
						:show_name="item.label"
					>
						<span>{{ item.label }}</span>
					</long-table-popover-chart>
					<span v-else>{{ item.label }}</span>
				</template>
				<template slot-scope="{ row }">
					<span>{{ item.format ? item.format(row[item.value]) : row[item.value] }}</span>
				</template>
			</el-table-column>
			<template slot="empty">
				<el-empty image-size="160"></el-empty>
			</template>
		</el-table>
	</div>
</template>

<script>
import { filter_json_to_excel } from '@/utils/exportExcel.js';

import { getBenchmarkList, getHoldPressureInfo } from '@/api/pages/Analysis.js';
// 持有压力
export default {
	name: 'holdingPressure',
	data() {
		return {
			benchmarkList: [],
			benchmarkvalue: '',
			loading: true,
			column: [
				{
					label: '持有期',
					value: 'window',
					popover: false,
					format: this.fixWindows,
					align: 'gotoleft'
				},
				{ label: '基金胜', value: 'winNum', popover: true, align: 'right' },
				{ label: '基准胜', value: 'loseNum', popover: true, align: 'right' },
				{ label: '总比较数', value: 'totalNum', align: 'gotoright' },
				{
					label: '基金胜率',
					value: 'winRatio',
					popover: true,
					format: this.fix2p,
					align: 'right'
				},
				{
					label: '基准胜率',
					value: 'loseRatio',
					popover: true,
					format: this.fix2p,
					align: 'right'
				}
			],
			postData: {},
			data: [],
			info: {}
		};
	},
	methods: {
		// 获取基准列表
		async getBenchmarkList() {
			let data = await getBenchmarkList({
				code: this.info.code,
				flag: this.info.flag,
				type: this.info.type
			});
			this.benchmarkList = [];
			this.activeBenchmark = '';
			if (data?.mtycode == 200) {
				this.benchmarkList = data?.data
					.sort((a, b) => {
						return b.isdefault - a.isdefault;
					})
					.map((v) => {
						return { code: v.indexCode, name: v.indexName, flag: v.flag };
					});
				if (this.benchmarkList.findIndex((v) => v.code == '000300.SH') != -1) {
					this.activeBenchmark = '000300.SH';
				} else {
					this.activeBenchmark = this.benchmarkList?.[0]?.code;
				}
			} else {
				this.benchmarkList = this.COMMON.default_index[this.info.type].map((v) => {
					return { code: v.indexCode, name: v.indexName, flag: '6' };
				});
				this.activeBenchmark = this.benchmarkList?.[0]?.code;
			}
			// this.benchmarkList.unshift({ code: '0', name: '收益率0%', flag: '6' });
			this.postData.index_code = this.activeBenchmark;
			this.getHoldPressureInfo();
		},
		// 获取持有压力
		async getHoldPressureInfo() {
			this.loading = true;
			let data = await getHoldPressureInfo({
				flag: this.info.flag + ',' + this.benchmarkList.find((v) => v.code == this.activeBenchmark)?.flag,
				code: this.info.code,
				type: this.info.type,
				start_date: this.info.start_date,
				end_date: this.info.end_date,
				index_code: this.postData.index_code
			});
			this.loading = false;
			if (data?.mtycode == 200) {
				this.data = data?.data;
			} else {
				this.data = [];
			}
		},
		// 获取数据
		getData(info) {
			this.info = info;
			this.getBenchmarkList();
		},
		// 监听基准变化
		changeBenchmarkValue() {
			this.loading = true;
			this.postData.index_code = this.activeBenchmark;
			this.getHoldPressureInfo();
		},
		formatTableData() {
			let data = [];
			this.data.map((item) => {
				let obj = { ...item };
				for (const key in item) {
					let format = this.column.find((obj) => {
						return obj.value == key;
					})?.format;
					if (format) {
						let val = format(item[key]);
						obj[key] = typeof val == 'string' ? (val.includes('%') ? val?.split('%')?.[0] * 1 : !isNaN(val) ? val * 1 : val) : val;
					}
				}
				data.push(obj);
			});
			return data;
		},
		fix2p(val) {
			return val == '--' ? val : !isNaN(val) ? Number(val * 100)?.toFixed(2) + '%' : '--';
		},
		fixWindows(val) {
			return val + '天';
		},
		// 导出为Excel
		exportExcel() {
			let list = this.column.map((item) => {
				return { label: item.label, value: item.value };
			});
			filter_json_to_excel(list, this.data, '持有压力');
		},
		// 导出
		async createPrintWord(info) {
			if (this.activeBenchmark) {
				this.info = info;
				await this.getHoldPressureInfo();
			} else {
				await this.getData(info);
			}
			let list = this.column.map((item) => {
				return { label: item.label, value: item.value };
			});
			let data = this.data.map((item) => {
				let obj = {};
				for (const key in item) {
					let index = this.column.findIndex((v) => v.value == key);
					if (this.column[index]?.format) {
						obj[key] = this.column[index]?.format(item[key]);
					} else {
						obj[key] = item[key];
					}
				}
				return obj;
			});
			let benchmarkName = this.benchmarkList?.filter((item) => {
				return item.code == this.activeBenchmark;
			})?.[0]?.name;
			let text = '对比基准为：' + benchmarkName;
			if (data.length) {
				return [
					...this.$exportWord.exportTitle('持有压力'),
					...this.$exportWord.exportDescripe(text),
					...this.$exportWord.exportTable(list, data, '', true)
				];
			} else {
				return [];
			}
		}
	}
};
</script>
<style lang="scss" scoped>
.holdingPressure {
	::v-deep.has-gutter {
		.is-gotoright {
			text-align: right;
		}
		.is-right {
			border-top: 2px solid #4096ff;
			// border-top: 2px solid #e9e9e9;
			border-right: 1px solid #e9e9e9;
			border-bottom: 1px solid #e9e9e9;
			background: linear-gradient(180deg, #ecf5ff 0%, rgba(255, 145, 3, 0) 100%), #f5f5f5 !important;
			.cell {
				background-color: transparent;
			}
		}
	}
}
</style>
