<template>
  <div class=" pool_analysis ml-12">
    <div>
      <div class="big_template"
           style="background:white; border-radius: 4px;border: 1px solid #d4d8e5;box-shadow: 0px 1px 2px 0px rgba(0, 0, 18, 0.1);margin-right: 16px;margin-bottom: 24px;padding: 16px 24px;margin-top: 24px;">
        <info-table ref="infoTable"
                    @resolveFather="changeQuarter"
                    @getCodeList="getCodeList"
                    @getInsertTime="getInsertTime"
                    @poolCreated="poolCreated"
                    @changeCareFund="changeCareFund"
                    @changeIndexCode="changeIndexCode"></info-table>
      </div>
      <!-- <el-menu v-show="info['code_list'].length > 0"
               :default-active="activeIndex"
               class="mx-12"
               mode="horizontal"
               @select="menuSelect">
        <el-menu-item v-for="item in componentsList"
                      :key="item.key"
                      :index="item.key">{{ item.label }}</el-menu-item>
      </el-menu> -->
      <div v-show="info['code_list'].length > 0"
           class="main">
        <div v-for="item in componentsList"
             :key="item.key"
             v-show="activeIndex == item.key || printActive == true">
          <component :is="item.key"
                     :ref="item.key"
                     :ismanager="isMangerPool"
                     @overRequest="overRequest"></component>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
// 头部表格
import infoTable from "@/pages/fundNewPool/analysis/components/infoTable";
// 数据中心
import dataCenter from "@/pages/fundNewPool/analysis/components/dataCenter";
// 信息中心
import infoCenter from "@/pages/fundNewPool/analysis/components/infoCenter";
// 获取季度选择列表
import { getDateList } from "@/api/pages/Analysis.js";
import { putPoolInfo } from "@/api/pages/tools/pool.js";
export default {
  components: { infoTable, dataCenter, infoCenter },
  data () {
    return {
      activeIndex: "dataCenter",
      info: {},
      isMangerPool: false,
      blank: "",
      componentsList: [
        {
          label: "数据中心",
          key: "dataCenter"
        }
        // {
        // 	label: '信息中心',
        // 	key: 'infoCenter'
        // }
      ]
    };
  },
  mounted () {
    let path = String(this.$route.path);
    if (path.indexOf("manage") < 0) {
      this.isMangerPool = false;
      this.blank = "";
    } else {
      this.isMangerPool = true;
      this.blank = "经理";
    }
    this.info.id = this.$route.params.id;
    this.init();
  },
  methods: {
    init () {
      let info = {};
      info["code"] = this.$route.query.id;
      info["type"] = this.$route.query.type;
      info["isdb"] = this.$route.query.isdb;
      info["name"] = this.$route.query.name;
      info["user_id"] = this.$route.query.ListLength;
      info["index_code"] = this.$route.query.index_code || "000300.SH";
      info["quarter"] = ["2022 Q1", "2022 Q2", "2022 Q3"];
      info["quarter_list"] = [
        "2020 Q3",
        "2020 Q4",
        "2021 Q1",
        "2021 Q2",
        "2021 Q3",
        "2021 Q4",
        "2022 Q1",
        "2022 Q2",
        "2022 Q3"
      ];
      info["code_list"] = [];
      info["db_fund"] = {};
      info["flag"] = 5;
      this.info = info;
      this.getData();
    },
    // 获取数据
    getData () {
      this.getDateList();
    },
    // 获取持仓季度
    async getDateList () {
      let data = await getDateList({ flag: 5, code: this.info.code });
      if (data?.mtycode == 200) {
        this.info.quarter_list = data.data;
        if (
          typeof this.localStorage.getItem("fund_pool_quarter") == "object" &&
          this.localStorage.getItem("fund_pool_quarter")?.length != 0
        ) {
          this.info.quarter = this.localStorage.getItem("fund_pool_quarter");
        } else {
          this.info.quarter = data.data.slice(-2);
        }
        this.$refs["infoTable"].getData(this.info);
      }
    },
    // 接收池子当前更新时间
    getInsertTime (time) {
      this.info = { ...this.info, date: time };
    },
    // 接收基准
    async changeIndexCode (index_code) {
      // localStorage.setItem(JSON.stringify(row), 'NowPoolMSG')
      let tempInfo = JSON.parse(this.localStorage.getItem('NowPoolMSG'));

      console.log(tempInfo);
      this.info["index_code"] = index_code;
      console.log(this.info);
      let data = await putPoolInfo({
        id: this.info.code,
        index_code: this.info.index_code,
        name: this.info.name,
        description: tempInfo?.datas?.description || '',
        status: tempInfo?.datas?.status || 0,
        status3: tempInfo?.datas?.status3 || 0,
        userlist: [],
        ispublic: tempInfo?.datas?.ispublic || 0,
        ismanager: this.ismanager,
        user_permission: []
        // user_permission: [],
      });
      this.$refs["dataCenter"]?.[0].getAlphabeta(this.info);
    },
    // 接收基金codeList
    getCodeList (list) {
      /**
       * flag:0 普通
       * flag:1 对标基金
       * flag:2 关注基金
       */
      this.info["code_list"] = list.map(item => {
        if (item.flag == 2) {
          return {
            code: item.code,
            name: item.name,
            color: item.color,
            flag: item.flag
          };
        } else {
          return { code: item.code, name: item.name, flag: item.flag };
        }
      });
      this.$refs["dataCenter"]?.[0].getData(this.info);
    },
    // 接收更改关注基金后列表
    changeCareFund (list) {
      this.info["code_list"] = list.map(item => {
        if (item.flag == 2) {
          return {
            code: item.code,
            name: item.name,
            color: item.color,
            flag: item.flag
          };
        } else {
          return { code: item.code, name: item.name, flag: item.flag };
        }
      });
      this.$refs["dataCenter"]?.[0].changeCareList(this.info);
    },
    // 接收子组件传递参数
    changeQuarter (val) {
      this.localStorage.setItem("fund_pool_quarter", val.quarter);
      this.info = { ...this.info, ...val };
      this.$refs["dataCenter"]?.[0].getData(this.info);
    },
    // 监听子池生成成功
    poolCreated () {
      this.$refs["dataCenter"]?.[0].getCategraydetail();
    },
    // 监听menu切换
    menuSelect (val) {
      this.activeIndex = val;
    }
  }
};
</script>
<style lang="scss" scope>
// .flex_card > .big_template {
//   height: auto;
//   width: 100%;
//   flex-basis: 100%;
//   min-width: 1684px;
//   max-width: 1684px;
//   /* height: 390px; */
//   // padding: 0 20px 20px 20px;
//   margin: 0 16px 16px 0;
//   // margin: 12px;
//   background: #ffffff;
//   border-radius: 4px;
//   border: 1px solid #d4d8e5;

//   /* 主模块 */
//   box-shadow: 0px 1px 2px 0px rgba(0, 0, 18, 0.1);
// }
// .flex_card > .big_template > div {
//   padding: 0 20px 20px 20px;
// }
// .flex_card > .big_template {
//  padding-top:16px;
// }
.chart_one {
	padding-top: 16px !important;
	// box-shadow: 0px 1px 2px 0px rgba(0, 0, 18, 0.1);
}
</style>
<style lang="scss">
.pool_analysis {
	.flex_between {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	.flex_start {
		display: flex;
		justify-content: start;
		align-items: center;
	}
	.flex_center {
		display: flex;
		justify-content: center;
		align-items: center;
	}
}
</style>
<style lang='scss' scoped>
.box_Board {
	padding: 0 24px 16px 24px;
	overflow: auto;
	// 面包屑

	// 表格区域
	.border_table {
		margin-top: 16px;
		padding: 16px 24px;
		background: white;
	}
}
</style>