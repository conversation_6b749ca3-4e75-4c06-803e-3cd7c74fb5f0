<template>
	<div class="correlation-coeffucuent-cahrt-wrapper" v-if="show">
		<div style="width: 100%; max-height: 600px; overflow: auto">
			<table>
				<tr class="correlationCoefficient_tr" style="position: sticky; top: 0; left: 0; background: #ffffff; z-index: 2; height: 80px">
					<th
						v-for="(item, index) in columnList"
						:key="'th' + index"
						:class="index > 0 ? 'header_text' : ''"
						:style="index == 0 ? 'position: sticky; top: 0; left: 0;background:#ffffff;width: 11em;z-index:1' : ''"
					>
						{{ item.label }}
					</th>
				</tr>
				<tr class="correlationCoefficient_tr" v-for="(item, index) in dataList" :key="'tr' + index">
					<td
						v-for="(obj, i) in columnList"
						:key="'td' + i"
						:style="`${cellStyle(item[obj.value])};${
							i == 0 ? 'position: sticky;top: 0; left: 0;background:#ffffff;text-align:right;width: 11em;z-index:1' : ''
						}`"
					>
						<span v-if="i != 0">{{ item[obj.value] | fix2 }}</span>
						<template v-else>
							<el-tooltip
								effect="dark"
								:content="item[obj.value]"
								placement="top-start"
								:disabled="item[obj.value] && item[obj.value].length <= 10"
							>
								<span class="cloum_header_text">{{ item[obj.value] }}</span>
							</el-tooltip>
						</template>
					</td>
				</tr>
			</table>
		</div>
		<div class="scaleplate-wrapper">
			<div class="scaleplate-colors"></div>
			<div class="scaleplate-values">
				<span class="values-item">-1</span>
				<span class="values-item">-0.5</span>
				<span class="values-item">0</span>
				<span class="values-item">0.5</span>
				<span class="values-item">1</span>
			</div>
		</div>
	</div>
</template>

<script>
//收益相关系数矩阵
export default {
	name: 'ValuationPercentileChart',
	data() {
		return {
			dataList: [],
			columnList: [],
			show: false
		};
	},
	filters: {
		fix2(val) {
			//表头文字不需要处理
			if (isNaN(val - 0)) {
				return val;
			}
			//数字处理成两位小数
			return (val - 0).toFixed(2) || '--';
		}
	},
	methods: {
		indexMethod(index) {
			// console.log('***', this.columnList, index);
			return this.columnList[index];
		},
		// 获取父组件传递数据
		getData(columnList, dataList) {
			this.show = true;
			this.columnList = columnList;
			this.dataList = dataList;
		},
		cellStyle(value) {
			let valueNum = value - 0;
			if (isNaN(valueNum)) {
				return '';
			}
			//大于零的数值取值范围
			//小于零的数值取值范围
			// console.log(valueNum);
			if (valueNum > 0) {
				let option = valueNum.toFixed(2);
				console.log(option - 0.2);
				if (option - 0.2 > 0) {
					return `background:rgba(255, 145, 3, ${option});color:#ffffff`;
				}
				return `background:rgba(255, 145, 3, ${option});`;
			} else {
				let option = -valueNum.toFixed(2);
				console.log(option - 0.2);
				if (option - 0.2 > 0) {
					return `background:rgba(55, 120, 246, ${option});color:#ffffff`;
				}
				return `background:rgba(55, 120, 246, ${option});`;
			}

			// rgba(255, 255, 255, 1);
			// if (value <= 1 && value > 0.75) {
			// 	return 'background:rgba(247, 101, 96, 1)';
			// } else if (value <= 0.75 && value > 0.5) {
			// 	return 'background:rgba(247, 101, 96, 0.8)';
			// } else if (value <= 0.5 && value > 0.25) {
			// 	return 'background:rgba(247, 101, 96, 0.6)';
			// } else if (value <= 0.25 && value > 0) {
			// 	return 'background:rgba(247, 101, 96, 0.4)';
			// } else if (value <= 0 && value > -0.25) {
			// 	return 'background:rgba(35, 195, 67, 0.4)';
			// } else if (value <= -0.25 && value > -0.5) {
			// 	return 'background:rgba(35, 195, 67, 0.6)';
			// } else if (value <= -0.5 && value > -0.75) {
			// 	return 'background:rgba(35, 195, 67, 0.8)';
			// } else if (value <= -0.75 && value > -1) {
			// 	return 'background:rgba(35, 195, 67, 1)';
			// } else {
			// 	return '';
			// }
		},
		handleSortChange() {}
	}
};
</script>

<style lang="scss" scoped>
.correlation-coeffucuent-cahrt-wrapper {
	display: flex;
	.correlationCoefficient_tr {
		width: 100%;
		display: flex;
		justify-content: space-around;
		height: 46px;
		align-items: center;
		.header_text {
			display: inline-block;
			transform-origin: 0 100%;
			/* 旋转45度 */
			transform: rotate(-12deg);
			text-align: left;
			line-height: 10;
		}
		// .cloum_header_text {
		// 	display: inline-block;
		// 	transform-origin: 100% 100%;
		// 	transform: rotate(-18deg);
		// 	width: 100%;
		// }
		th {
			width: 136px;
			height: 100%;
			text-align: center;
			font-family: 'PingFang';
			font-style: normal;
			font-weight: 400;
			font-size: 14px;
			line-height: 46px;
			color: rgba(0, 0, 0, 0.65);
			// overflow: hidden;
			// text-overflow: ellipsis;
			white-space: nowrap;
		}
		td {
			border: 1px solid #fff;
			width: 136px;
			height: 100%;
			text-align: center;
			font-style: normal;
			font-weight: 400;
			font-size: 14px;
			line-height: 46px;
			color: rgba(0, 0, 0, 0.65);
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
	}
	.scaleplate-wrapper {
		display: flex;
		height: 172px;
		margin-top: 46px;
		margin-left: 16px;
		.scaleplate-colors {
			width: 24px;

			align-self: stretch;
			background: linear-gradient(180deg, #4096ff 0%, #fff 50%, #4096ff 100%);
		}
		.scaleplate-values {
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			word-break: keep-all;
			margin-left: 8px;
		}
	}
}
</style>
