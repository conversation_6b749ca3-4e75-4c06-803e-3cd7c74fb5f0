<template>
	<div class="chart_one">
		<div class="flex_between mb-16">
			<div></div>
			<div><el-button type="primary" @click="openDialog">新建模版</el-button></div>
		</div>
		<div v-loading="loading">
			<el-table :data="data" style="width: 100%">
				<el-table-column v-for="item in column" :key="item.value" :prop="item.value" :label="item.label" align="gotoleft">
					<template slot-scope="{ row }">
						<div v-if="item.value.includes('is')">
							<el-switch
								v-model="row[item.value]"
								@change="changeData(row, item)"
								active-color="#4096ff"
								inactive-color="#E7E7E7"
							></el-switch>
						</div>
						<div v-else>
							{{ row[item.value] }}
						</div>
					</template>
				</el-table-column>
				<el-table-column align="gotoleft" label="操作">
					<template slot-scope="{ row }">
						<div class="flex_start">
							<el-link class="mr-8" @click="openDialog(row)">编辑</el-link>
							<el-link @click="deleteItem(row.model_id)">删除</el-link>
						</div>
					</template>
				</el-table-column>
				<template slot="empty">
					<el-empty image-size="160"></el-empty>
				</template>
			</el-table>
		</div>
		<edit-evaluate ref="editEvaluate" @resolveFather="getData"></edit-evaluate>
	</div>
</template>

<script>
import editEvaluate from '@/pages/filter/custom/components/components/editEvaluate';
import { getModelList, updateModel, deleteModel } from '@/api/pages/Tools.js';
export default {
	components: { editEvaluate },
	data() {
		return {
			loading: false,
			column: [
				{
					label: '模版名称',
					value: 'model_name'
				},
				{
					label: '筛选范围',
					value: 'args'
				},
				{
					label: '自定义标签',
					value: 'is_tag'
				},
				{
					label: '自定义评分',
					value: 'is_evaluate'
				},
				{
					label: '自定义能力',
					value: 'capability'
				},
				{
					label: '量化评分',
					value: 'rate'
				},
				{
					label: '是否默认',
					value: 'is_default'
				},
				{
					label: '是否公开',
					value: 'is_public'
				}
			],
			data: [],
			model: []
		};
	},
	methods: {
		async getData() {
			this.loading = true;
			let data = await getModelList({ source: 'evaluate', type: 'equity', ismanager: false });
			this.loading = false;
			if (data?.mtycode == 200) {
				this.model = data?.data?.self_data.map((item) => {
					return {
						...item,
						model_args: JSON.parse(item.model_args)?.[0]
					};
				});
				this.data = this.model.map((item) => {
					let rate = '';
					if (item?.model_args?.rate?.item) {
						item?.model_args?.rate?.item.map((v) => {
							rate = v.name + ':' + v.value + ',' + rate;
						});
					}
					return {
						...item,
						...item.model_args,
						capability: item?.model_args?.item ? item.model_args?.item.map((v) => v.name).join(',') : '',
						rate,
						is_public: item.ispublic == 'True' ? true : false,
						is_default: item?.model_description == 'default' ? true : false
					};
				});
			} else {
				this.data = [];
			}
		},
		changeData(row, item) {
			if (item.label == '是否默认') {
				let obj = this.model.find((v) => v.model_id == row.model_id);
				if (row[item.value]) {
					let index = this.model.find((v) => v.model_description == 'default');
					if (index?.model_id) {
						index.model_description = '';
						index.model_args = [index.model_args];
						index.ismanager = false;
						index.user_permission = [];
						index.title = '--';
						this.updateModel(index);
					}
					obj.model_description = 'default';
				} else {
					obj.model_description = '';
				}
				obj.model_args = [obj.model_args];
				obj.ismanager = false;
				obj.user_permission = [];
				obj.title = '--';
				this.updateModel(obj);
			}
		},
		// 修改模版
		async updateModel(postData) {
			let data = await updateModel(postData);
			if (data?.mtycode == 200) {
				this.$message.success('修改成功');
				this.getData();
			}
		},
		openDialog() {
			this.$refs['editEvaluate'].getData();
		},
		async deleteItem(model_id) {
			let data = await deleteModel({ model_id });
			if (data?.mtycode == 200) {
				this.$message.success('删除成功');
				this.getData();
			} else {
				this.$message.warning('删除失败');
			}
		}
	}
};
</script>

<style></style>
