<!--  -->
<template>
	<div class="alphafilter">
		<el-menu :default-active="activeIndex" background-color="#ffffff" active-text-color="#4096ff" mode="horizontal" @select="handleSelect">
			<!-- <el-menu-item index="1">全部基金</el-menu-item> -->
			<el-menu-item index="2">主动权益</el-menu-item>
			<!-- <el-menu-item index="3">被动权益</el-menu-item> -->
			<el-menu-item index="4">港股</el-menu-item>
			<el-menu-item index="5">固收+</el-menu-item>
			<el-menu-item index="6">可转债</el-menu-item>
			<el-menu-item index="7">纯债</el-menu-item>
			<el-menu-item index="8">中短债</el-menu-item>
			<el-menu-item index="9">货币</el-menu-item>
			<!-- <el-menu-item index="10" :disabled="active_request == '11' || active_request == '12'">QDII债券</el-menu-item>
			<el-menu-item index="11" :disabled="active_request == '10' || active_request == '12'">QDII商品</el-menu-item> -->
			<el-menu-item index="12" :disabled="active_request == '10' || active_request == '11'">FOF</el-menu-item>
		</el-menu>
		<div class="line2X"></div>
		<allFund v-if="allFundShow"></allFund>
		<zhudong v-if="zhudong"></zhudong>
		<beidong v-if="beidong"></beidong>
		<ganggu v-if="ganggu"></ganggu>
		<erjizhai v-if="erjizhai"></erjizhai>
		<chunzhai v-if="chunzhai"></chunzhai>
		<kezhuanzhai v-if="kezhuanzhai"></kezhuanzhai>
		<zhongduanzhai v-if="zhongduanzhai"></zhongduanzhai>
		<huobi v-if="huobi"></huobi>
		<qdiizhai v-if="qdiizhai" @overRequest="overRequest"></qdiizhai>
		<qdiicom v-if="qdiicom" @overRequest="overRequest"></qdiicom>
		<fof v-if="fof" @overRequest="overRequest"></fof>
	</div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import tempbasket from './components/components/tempbasket.vue';
import zhudong from './components/alphaFundpool.vue';
import beidong from './components/alphaFundpoolbeidong.vue';
import erjizhai from './components/alphaFundpoolerijizhai';
import chunzhai from './components/alphaFundpoolchunzhai';
import kezhuanzhai from './components/alphaFundpoolkezhuanzhai';
import zhongduanzhai from './components/alphaFundpoolzhongduanzhai';
import huobi from './components/alphaFundpoolhuobi';
import ganggu from './components/alphaFundpoolganggu.vue';
import qdiizhai from './components/alphaFundpoolqdiizhai';
import qdiicom from './components/alphaFundpoolqdiicommodity';
import fof from './components/alphaFundpoolfof';
export default {
	//import引入的组件需要注入到对象中才能使用
	components: {
		zhudong,
		beidong,
		erjizhai,
		chunzhai,
		kezhuanzhai,
		zhongduanzhai,
		huobi,
		ganggu,
		tempbasket,
		qdiizhai,
		qdiicom,
		fof
	},
	data() {
		//这里存放数据
		return {
			activeIndex: '2',
			zhudong: false,
			allFundShow: true,
			beidong: false,
			erjizhai: false,
			chunzhai: false,
			kezhuanzhai: false,
			zhongduanzhai: false,
			huobi: false,
			ganggu: false,
			qdiizhai: false,
			nowflag: null,
			navselected: '1',
			qdiicom: false,
			fof: false,
			active_request: ''
		};
	},
	//监听属性 类似于data概念
	computed: {},
	//监控data中的数据变化
	watch: {},
	//方法集合
	methods: {
		overRequest() {
			this.active_request = '';
		},
		handleSelect(key, keyPath) {
			if (key == '1') {
				this.allFundShow = true;
				this.zhudong = false;
				this.beidong = false;
				this.erjizhai = false;
				this.chunzhai = false;
				this.kezhuanzhai = false;
				this.zhongduanzhai = false;
				this.huobi = false;
				this.ganggu = false;
				this.qdiizhai = false;
				this.qdiicom = false;
				this.fof = false;
				try {
					this.localStorage.setItem('keepactiveIndex', JSON.stringify(key));
				} catch (err) {}
			}
			if (key == '2') {
				this.allFundShow = false;
				this.zhudong = true;
				this.beidong = false;
				this.erjizhai = false;
				this.chunzhai = false;
				this.kezhuanzhai = false;
				this.zhongduanzhai = false;
				this.huobi = false;
				this.ganggu = false;
				this.qdiizhai = false;
				this.qdiicom = false;
				this.fof = false;
				try {
					this.localStorage.setItem('keepactiveIndex', JSON.stringify(key));
				} catch (err) {}
			}
			if (key == '3') {
				this.allFundShow = false;
				this.zhudong = false;
				this.beidong = true;
				this.erjizhai = false;
				this.chunzhai = false;
				this.kezhuanzhai = false;
				this.zhongduanzhai = false;
				this.huobi = false;
				this.ganggu = false;
				this.qdiizhai = false;
				this.qdiicom = false;
				this.fof = false;
				try {
					this.localStorage.setItem('keepactiveIndex', JSON.stringify(key));
				} catch (err) {}
			}
			if (key == '4') {
				this.allFundShow = false;
				this.zhudong = false;
				this.beidong = false;
				this.erjizhai = false;
				this.chunzhai = false;
				this.kezhuanzhai = false;
				this.zhongduanzhai = false;
				this.huobi = false;
				this.ganggu = true;
				this.qdiizhai = false;
				this.qdiicom = false;
				this.fof = false;
				try {
					this.localStorage.setItem('keepactiveIndex', JSON.stringify(key));
				} catch (err) {}
			}
			if (key == '5') {
				this.allFundShow = false;
				this.zhudong = false;
				this.beidong = false;
				this.erjizhai = true;
				this.chunzhai = false;
				this.kezhuanzhai = false;
				this.zhongduanzhai = false;
				this.huobi = false;
				this.ganggu = false;
				this.qdiizhai = false;
				this.qdiicom = false;
				this.fof = false;
				try {
					this.localStorage.setItem('keepactiveIndex', JSON.stringify(key));
				} catch (err) {}
			}
			if (key == '6') {
				this.allFundShow = false;
				this.zhudong = false;
				this.beidong = false;
				this.erjizhai = false;
				this.chunzhai = false;
				this.kezhuanzhai = true;
				this.zhongduanzhai = false;
				this.huobi = false;
				this.ganggu = false;
				this.qdiizhai = false;
				this.qdiicom = false;
				this.fof = false;
				try {
					this.localStorage.setItem('keepactiveIndex', JSON.stringify(key));
				} catch (err) {}
			}
			if (key == '7') {
				this.allFundShow = false;
				this.zhudong = false;
				this.beidong = false;
				this.erjizhai = false;
				this.chunzhai = true;
				this.kezhuanzhai = false;
				this.zhongduanzhai = false;
				this.huobi = false;
				this.ganggu = false;
				this.qdiizhai = false;
				this.qdiicom = false;
				this.fof = false;
				try {
					this.localStorage.setItem('keepactiveIndex', JSON.stringify(key));
				} catch (err) {}
			}
			if (key == '8') {
				this.allFundShow = false;
				this.zhudong = false;
				this.beidong = false;
				this.erjizhai = false;
				this.chunzhai = false;
				this.kezhuanzhai = false;
				this.zhongduanzhai = true;
				this.huobi = false;
				this.ganggu = false;
				this.qdiizhai = false;
				this.qdiicom = false;
				this.fof = false;

				try {
					//  //console.log('gotosave')
					this.localStorage.setItem('keepactiveIndex', JSON.stringify(key));
				} catch (err) {}
			}
			if (key == '9') {
				this.allFundShow = false;
				this.zhudong = false;
				this.beidong = false;
				this.erjizhai = false;
				this.chunzhai = false;
				this.kezhuanzhai = false;
				this.zhongduanzhai = false;
				this.huobi = true;
				this.ganggu = false;
				this.qdiizhai = false;
				this.qdiicom = false;
				this.fof = false;
				try {
					this.localStorage.setItem('keepactiveIndex', JSON.stringify(key));
				} catch (err) {}
			}
			if (key == '10') {
				this.allFundShow = false;
				this.zhudong = false;
				this.beidong = false;
				this.erjizhai = false;
				this.chunzhai = false;
				this.kezhuanzhai = false;
				this.zhongduanzhai = false;
				this.huobi = false;
				this.ganggu = false;
				this.qdiizhai = true;
				this.qdiicom = false;
				this.fof = false;
				this.active_request = key;
				try {
					this.localStorage.setItem('keepactiveIndex', JSON.stringify(key));
				} catch (err) {}
			}
			if (key == '11') {
				this.allFundShow = false;
				this.zhudong = false;
				this.beidong = false;
				this.erjizhai = false;
				this.chunzhai = false;
				this.kezhuanzhai = false;
				this.zhongduanzhai = false;
				this.huobi = false;
				this.ganggu = false;
				this.qdiizhai = false;
				this.qdiicom = true;
				this.fof = false;
				this.active_request = key;
				try {
					this.localStorage.setItem('keepactiveIndex', JSON.stringify(key));
				} catch (err) {}
			}
			if (key == '12') {
				this.allFundShow = false;
				this.zhudong = false;
				this.beidong = false;
				this.erjizhai = false;
				this.chunzhai = false;
				this.kezhuanzhai = false;
				this.zhongduanzhai = false;
				this.huobi = false;
				this.ganggu = false;
				this.qdiizhai = false;
				this.qdiicom = false;
				this.fof = true;
				this.active_request = key;
				try {
					this.localStorage.setItem('keepactiveIndex', JSON.stringify(key));
				} catch (err) {}
			}
		}
	},
	//生命周期 - 创建完成（可以访问当前this实例）
	created() {
		// //console.log('cereater')
		if (
			this.localStorage.getItem('keepactiveIndex') != null &&
			this.localStorage.getItem('keepactiveIndex') != undefined &&
			this.localStorage.getItem('keepactiveIndex') != 'null' &&
			this.localStorage.getItem('keepactiveIndex') != 'undefined'
		) {
			//  //console.log(localStorage.getItem('keepactiveIndex'))
			this.activeIndex = JSON.parse(this.localStorage.getItem('keepactiveIndex'));
		}
		// //console.log(this.activeIndex)
		if (this.activeIndex != '1') {
			this.handleSelect(this.activeIndex);
		}
		if (this.FUNC.isEmpty(this.$route.query.code)) {
			this.localStorage.setItem('keepactiveIndex', JSON.stringify(2));
			this.activeIndex = JSON.parse(this.localStorage.getItem('keepactiveIndex'));

			this.handleSelect(2);
			// this.$nextTick(() => {
			// 	this.tabName = '0';
			// });
		}
	},
	//生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {}
};
</script>
<style>
.alphafilter .line2X {
	width: 100%;
	height: 1px;
	background: #e9e9e9;
}
.alphafilter .el-menu {
	background: #ffffff !important;
	background-color: #ffffff !important;
}
.alphafilter .el-menu.el-menu--horizontal {
	border-bottom: 0px !important;
}
.alphafilter .el-menu-item .is-active {
	background: #ffffff !important;
}
.alphafilter .el-menu-item:hover {
	background: #ffffff !important;
}
</style>
