<template>
    <div class="plate-wrapper fund-performance-wrapper" v-loading="loading">
        <combinationComponentHeader title="分时段收益" showMoreBtn @download="exportExcel">
            <template slot="right">
                <el-form  ref="form" :model="form" label-width="80px" class="title-right-form" style="margin-right:16px">
                    <el-select v-model="form.cutFlag" placeholder="请选择月份" @change="handleChange">
						<el-option
                            v-for="item in options"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                            </el-option>
					</el-select>
                </el-form>
            </template>
        </combinationComponentHeader>
        <barChart ref="fund-performance-board-chart-container2" style="margin-bottom: 20px;"></barChart>
        <el-table border stripe :data="tableData">
            <el-table-column v-for="(item,index) in tableHeader" :key="index" align="gotoleft" :prop="item.prop" show-overflow-tooltip :label="item.label" >
                <template   slot-scope="scope" >
							{{ scope.row[item.prop] || '--'}}
						</template>
            </el-table-column>

            <template slot="empty">
                <el-empty image-size="160"></el-empty>
            </template>
        </el-table>
    </div>
</template>
<script>
import combinationComponentHeader from './combinationComponentHeader.vue';
import barChart from '../chart/barChart.vue';
import { getProductPerformance } from '@/api/pages/tkAnalysis/portfolio.js';
import { filter_to_excel } from "@/utils/exportExcel.js";
export default {
    name:'monthIncome',
    components:{
        combinationComponentHeader,
        barChart
    },
    data(){
        return {
            form:{
                cutFlag:'monthly',
                flag:'combination',
                measure:['cum_return'],
                scene:'yearqtr',
                startDate:'',
                endDate:''
            },
            IndexStyleOption:[],
            tableData:[],
            tableHeader:[{
                prop:'year',
                label:'年份'

            }],
            options: [
                {
                    label: '月度',
                    value: 'monthly'
                },
                {
                    label: '季度',
                    value: 'quarterly'
                },
                {
                    label: '半年度',
                    value: 'halfyearly'
                },
                {
                    label: '年度',
                    value: 'yearly'
                }
            ],
            myChart:null,
            legendName : {
                'indicatorPoints':'组合累计净值',
                'ttm':'沪深300',
                'dividedIntoPoints':'超额收益累计',
                'positiveStandardDeviation':'标准差（+1）',
                'negativeStandardDeviation':'标准差（-1）',
                'average':"平均值"
            },
            param:null,
            loading:true,
        }
    },
    methods:{
        exportExcel(){
            let list = this.tableHeader.map((item) => {
				return {
					...item,
					format: ''
				};
			});
			filter_to_excel(list, this.tableData, '月度收益');
        },
        handleChange(){
            this.getData(this.param);
        },
        async getData(param){
            this.loading = true;
            this.param = param;
            let res = await getProductPerformance({
                ...param,
                codes:[this.param.combinationId],
                ...this.form
            })
            this.loading = false;
            if(res.mtycode == 200){
                let {tableData,tableHeader,chartDate,chartData} = this.dealResult(res.data.periodList);
                this.tableData = tableData;
                this.tableHeader = [{
                    prop:'year',
                    label:'年份'

                },
                ...tableHeader
                ]
                let chartDom = this.$refs['fund-performance-board-chart-container2'];
                if(res.data.periodList?.length > 0){
                    chartDom?.hideempty()
                }else{
                    chartDom?.showempty()
                }
                chartDom?.getData({
                    chartDate,
                    chartData,
                })
            }
           
        },
        //处理接口返回数据
        dealResult(data){
            let tableData = [];
            let tableHeader = [];
            let chartDate = [];
            let chartData = [];
            let yearSet = new Set();
            let flagSet = new Set()
            data?.forEach((item)=>{
                yearSet.add(item.year);
                flagSet.add(item.flag);
                if(this.form.cutFlag !== 'yearly'){
                    if(item.date){
                        chartDate.push(item.date || (item.year + item.flag));
                        chartData.push(item.cumReturn * 10000 /100 );
                    }
                }
                else{
                    chartDate.push(item.year);
                    chartData.push(item.cumReturn * 10000 /100 );
                }
                
            })
            if(this.form.cutFlag !== 'yearly'){
                yearSet.forEach((key)=>{
                    let item = {
                        year:key
                    };

                    data.forEach(val=>{
                        if(val.year === key){
                            if(this.form.cutFlag === 'halfyearly'){
                                if(val.flag === (key+'上半年')){
                                    item['preHalfYear'] = this.fix2p(val.cumReturn);
                                }
                                if(val.flag === (key+'下半年')){
                                    item['postHalfYear'] = this.fix2p(val.cumReturn);
                                }
                            
                            }else{
                                item[val.flag] = this.fix2p(val.cumReturn);

                            }

                        }
                    })
                    tableData.push(item);
                })
            }else{
                tableData.push({
                    year:'组合收益'
                });
                data.forEach(val=>{
                    tableData[0][val.flag] = this.fix2p(val.cumReturn);
                })
            }
            
            flagSet.forEach((item)=>{
                tableHeader.push({
                    prop:item,
                    label:item
                })
            })
            if(this.form.cutFlag === 'halfyearly'){
                tableHeader = [{
                    prop:'preHalfYear',
                    label:'上半年'
                },
                {
                    prop:'postHalfYear',
                    label:'下半年'
                }]
            }
            if(this.form.cutFlag !== 'halfyearly'){
                tableHeader.sort((a,b)=>{
                    let c = parseInt(a.prop);
                    let d = parseInt(b.prop);
                    if(a.prop === '全年' && b.prop !== '全年'){
                        return 1;
                    }
                    if(a.prop !== '全年' && b.prop === '全年'){
                        return -1;
                    }
                    
                    if(a.prop > b.prop){
                        return 1
                    }
                    return -1;
                });
            }
            //     tableHeader.sort()
            // }
            
            return {tableData,tableHeader,chartDate,chartData}
        },
        fix2p(value) {
			return value &&
				value != '' &&
				value != '--' &&
				value != '- -' &&
				JSON.stringify(value) != '[]' &&
				JSON.stringify(value) != '{}' &&
				value != 'NAN' &&
				value != 'nan'
				? (Number(value) * 100).toFixed(2) + '%'
				: '--';
		},
    }
    
}
</script>
<style lang="scss" scoped>
.fund-performance-wrapper {
    .select-form-wrapper {
        margin-bottom: 16px;
    }
    .content-table-wrapper {
        margin-bottom: 32px;
    }
}
</style>