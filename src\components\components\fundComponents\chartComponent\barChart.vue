<template>
	<div class="charts_fill_class">
		<v-chart
			ref="barChartComponent"
			v-loading="loading"
			class="charts_one_class"
			autoresize
			element-loading-text="暂无数据"
			element-loading-spinner="el-icon-document-delete"
			element-loading-background="rgba(239, 239, 239, 0.5)"
			:options="option"
		/>
	</div>
</template>

<script>
import { barChartOption } from '@/utils/chartStyle.js';

export default {
	data() {
		return {
			option: {},
			loading: true
		};
	},
	methods: {
		getData({ series, legend, xAxis }) {
			this.loading = false;
			this.$nextTick(() => {
				this.option = barChartOption({
					color: ['#F8931B','#FEC70B','#FDED00','#B5EC30','#08C47C','#00D7E9','#4096ff', '#7388A9', '#6F80DD', '#6C96F2', '#FD6865', '#83D6AE', '#83D6AE'],
					tooltip: {
						formatter: (params) => {
							//  console.log(params)
							let str = `时间: ${params[0].axisValue} <br />`;
							for (let i = params.length - 1; i >= 0; i--) {
								let dotHtml =
									'<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' +
									params[i].color +
									'"></span>';
								str += dotHtml + `${params[i].seriesName}: ${(params[i].value * 100).toFixed(3) + '%'}<br />`;
							}
							return str;
						}
					},
					legend,
					dataZoom: true,
					xAxis: [{ data: xAxis }],
					yAxis: [{ type: 'value' }],
					series
				});
			});
		},
		createPrintWord() {
			this.$refs['barChartComponent'].mergeOptions({ toolbox: { show: false } });
			let chart = this.$refs['barChartComponent'].getDataURL({
				type: 'png',
				pixelRatio: 2,
				backgroundColor: '#fff'
			});
			this.$refs['barChartComponent'].mergeOptions({ toolbox: { show: true } });
			return chart;
		}
	}
};
</script>

<style></style>
