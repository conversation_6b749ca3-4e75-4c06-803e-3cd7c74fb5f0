<template>
	<el-dialog title="邮箱配置" :visible.sync="dialogVisible">
		<div>
			<el-menu :default-active="activeIndex" mode="horizontal" @select="menuSelect">
				<el-menu-item index="addEmail">添加邮箱</el-menu-item>
				<el-menu-item index="chooseEmail">选择邮箱</el-menu-item>
			</el-menu>
		</div>
		<div style="padding: 16px 0">
			<add-email ref="addEmail" v-show="activeIndex == 'addEmail'" @refreshList="getEmailList"></add-email>
			<choose-email ref="chooseEmail" v-show="activeIndex == 'chooseEmail'" @refreshList="getEmailList"></choose-email>
		</div>
		<div slot="footer">
			<el-button @click="dialogVisible = false">取 消</el-button>
			<el-button type="primary" @click="submit">确 定</el-button>
		</div>
	</el-dialog>
</template>

<script>
import addEmail from './components/addEmail.vue';
import chooseEmail from './components/chooseEmail.vue';

import { postEmailItem, getEmailList } from '@/api/pages/NodeServer.js';

export default {
	components: { addEmail, chooseEmail },
	data() {
		return {
			dialogVisible: false,
			activeIndex: 'chooseEmail',
			list: [],
			user_id: localStorage.getItem('id')
		};
	},
	methods: {
		// 获取邮箱列表
		async getEmailList() {
			let data = await getEmailList({ user_id: this.user_id });
			if (data?.mtycode == 200) {
				this.list = data?.data;
			} else {
				this.list = [];
			}
			this.sendList();
		},
		// 监听用户点击确定
		submit() {
			if (this.activeIndex == 'addEmail') {
				this.postEmailItem();
			} else {
				let ids = this.$refs[this.activeIndex].getCurrentKey();
				ids = ids.map((item) => {
					return this.list.filter((obj) => {
						return obj.email_id == item;
					})?.[0];
				});
				this.$emit('resolveEmailIds', ids);
			}
		},
		// 关闭邮箱弹唱
		closeDialog() {
			this.dialogVisible = false;
		},
		// 新增客户邮箱
		async postEmailItem() {
			let postData = this.$refs['addEmail']?.getSubmitForm();
			let data = await postEmailItem({ ...postData, user_id: this.user_id });
			if (data?.mtycode == 200) {
				this.$message.success('新增成功');
				this.getEmailList();
			} else {
				this.$message.warning(data?.mtymessage || '新增失败');
			}
		},
		getData(list) {
			this.getEmailList();
			this.$refs[this.activeIndex]?.setCurrentKey(
				list.map((item) => {
					return item.email_id;
				})
			);
			this.dialogVisible = true;
		},
		menuSelect(val) {
			this.activeIndex = val;
			this.sendList();
		},
		// 向子组件传递列表
		sendList() {
			this.$refs[this.activeIndex]?.getData(this.list);
		}
	}
};
</script>

<style></style>
