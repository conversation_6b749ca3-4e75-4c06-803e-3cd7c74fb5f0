<template>
  <div class="sidebar"
       :style="
			collapse
				? 'width:48px;height: calc(100vh - 56px);overflow:hidden'
				: 'height: calc(100vh - 56px);overflow:hidden;position: relative;margin-bottom:40px'
		">
    <div style="width: 100%">
      <el-menu class="sidebar-el-menu"
               :collapse="collapse"
               style="height: calc(100vh - 96px); overflow: auto"
               :default-active="onRoutes"
               background-color="#ffffff"
               text-color="#000000A6"
               active-text-color="#4096ff"
               unique-opened>
        <template v-for="item in items">
          <template v-if="item.subs && item.subs.length">
            <el-submenu :index="item.index"
                        @click="navigateToSubItem(item.index)"
                        :key="item.index">
              <template slot="title">
                <!-- <i :class="item.icon"></i> -->
                <svg width="16"
                     height="16"
                     viewBox="0 0 16 16"
                     fill="none"
                     xmlns="http://www.w3.org/2000/svg">
                  <path v-for="(d, index) in item.icon"
                        :key="index"
                        :d="d"
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        fill="black"
                        fill-opacity="0.65" />
                </svg>
                <span slot="title"
                      style="margin-left: 10px">
                  {{ item.title }}
                </span>

                <!-- <span style="margin-left: 10px">{{ item.title }}</span> -->
              </template>
              <template v-for="subItem in item.subs">
                <el-submenu v-if="subItem.subs"
                            :index="subItem.index"
                            @click="navigateToSubItem(subItem.index)"
                            :key="subItem.index">
                  <template slot="title">{{ subItem.title }}</template>
                  <el-menu-item v-for="(threeItem, i) in subItem.subs"
                                :key="i"
                                :index="threeItem.index"
                                @click="navigateToSubItem(threeItem.index)">{{ threeItem.title }}</el-menu-item>
                </el-submenu>
                <el-menu-item v-else
                              :index="subItem.index"
                              @click="navigateToSubItem(subItem.index)"
                              :key="subItem.index + '1'">
                  <div style="padding-left: 16px">{{ subItem.title }}</div>
                </el-menu-item>
              </template>
            </el-submenu>
          </template>
          <template v-else>
            <el-menu-item :index="item.index"
                          @click="navigateToSubItem(item.index)"
                          :key="item.index"
                          class="pl-16">
              <!-- <i :class="item.icon"></i> -->
              <svg width="16"
                   height="16"
                   viewBox="0 0 16 16"
                   fill="none"
                   xmlns="http://www.w3.org/2000/svg">
                <path v-for="(d, index) in item.icon"
                      :key="index"
                      :d="d"
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      fill="black"
                      fill-opacity="0.65" />
              </svg>
              <span slot="title"
                    style="margin-left: 10px">
                {{ item.title }}
              </span>
            </el-menu-item>
          </template>
        </template>
      </el-menu>
      <div style="
					width: 100%;
					background: #ffffff;
					border-top: 1px solid #e9e9e9;
					padding-left: 16px;
					height: 40px;
					position: absolute;
					bottom: 0;
					line-height: 40px;
					cursor: pointer;
				"
           @click="collapse = !collapse">
        <i v-show="collapse"
           class="el-icon-s-unfold"
           style="font-size: 20px; color: rgba(0, 0, 0, 0.65)"></i>
        <i v-show="!collapse"
           class="el-icon-s-fold"
           style="font-size: 20px; color: rgba(0, 0, 0, 0.65)"></i>
      </div>
    </div>
  </div>
</template>

<script>
// import { showMenu } from '@/api/common.js';

export default {
  data () {
    return {
      collapse: false,
      items: [],
      pathList: []
    };
  },
  computed: {
    onRoutes () {
      return this.$route.path.replace('/', '');
    }
  },
  created () {
    this.getmenu();
    // 通过 Event Bus 进行组件间通信，来折叠侧边栏
    this.$event.$on('collapse', (msg) => {
      this.collapse = msg;
      this.$event.$emit('collapse-content', msg);
    });
    //隐藏自动折叠
    this.collapse = window.innerWidth < 1920 ? true : false;
    window.addEventListener('resize', (e) => {
      this.collapse = e.currentTarget.innerWidth < 1920 ? true : false;
    });
    // this.collapse = true



    // 		let pathList = `M8.01562 0.984375C7.70496 0.984375 7.45312 1.23621 7.45312 1.54687V1.98438H1.54688C1.23621 1.98438 0.984375 2.23621 0.984375 2.54687V13.4531C0.984375 13.7638 1.23621 14.0156 1.54688 14.0156H7.45312V14.4531C7.45312 14.7638 7.70496 15.0156 8.01562 15.0156C8.32629 15.0156 8.57812 14.7638 8.57812 14.4531V14.0178H9.97546C10.2479 14.0178 10.4688 13.797 10.4688 13.5245C10.4688 13.2521 10.2479 13.0312 9.97546 13.0312H8.57812V2.97095H9.97546C10.2479 2.97095 10.4688 2.7501 10.4688 2.47766C10.4688 2.20523 10.2479 1.98438 9.97546 1.98438H8.57812V1.54688C8.57812 1.23621 8.32629 0.984375 8.01562 0.984375ZM7.45312 12.8906V3.10938H2.10938V12.8906H7.45312Z&&M10.9688 13.5245C10.9688 13.2521 11.1896 13.0312 11.462 13.0312H12.8973C13.1698 13.0312 13.3906 13.2521 13.3906 13.5245C13.3906 13.797 13.1698 14.0178 12.8973 14.0178H11.462C11.1896 14.0178 10.9688 13.797 10.9688 13.5245Z&&M10.9688 2.47766C10.9688 2.20523 11.1896 1.98438 11.462 1.98438H12.8973C13.1698 1.98438 13.3906 2.20523 13.3906 2.47766C13.3906 2.7501 13.1698 2.97095 12.8973 2.97095H11.462C11.1896 2.97095 10.9688 2.7501 10.9688 2.47766Z&&M15.0156 2.54687C15.0156 2.23621 14.7638 1.98438 14.4531 1.98438C14.1425 1.98437 13.8906 2.23621 13.8906 2.54688V4.05469C13.8906 4.36535 14.1425 4.61719 14.4531 4.61719C14.7638 4.61719 15.0156 4.36535 15.0156 4.05469V2.54687Z&&M14.4531 8.25C14.7638 8.25 15.0156 8.50184 15.0156 8.8125V10.3203C15.0156 10.631 14.7638 10.8828 14.4531 10.8828C14.1425 10.8828 13.8906 10.631 13.8906 10.3203V8.8125C13.8906 8.50184 14.1425 8.25 14.4531 8.25Z&&M15.0156 11.9297C15.0156 11.619 14.7638 11.3672 14.4531 11.3672C14.1425 11.3672 13.8906 11.619 13.8906 11.9297V13.4375C13.8906 13.7482 14.1425 14 14.4531 14C14.7638 14 15.0156 13.7482 15.0156 13.4375V11.9297Z&&M14.4531 5.11719C14.7638 5.11719 15.0156 5.36903 15.0156 5.67969V7.1875C15.0156 7.49816 14.7638 7.75 14.4531 7.75C14.1425 7.75 13.8906 7.49816 13.8906 7.1875V5.67969C13.8906 5.36903 14.1425 5.11719 14.4531 5.11719Z
    // `;
    // 		this.pathList = pathList.split('&&');
  },
  watch: {
    $route (to, from) {
      // console.log('object');
      if (to.query.flaglogin == 'login' || to.query.flaglogin == 'logins') {
        console.log('menu');
        this.getmenu();
      }
    }
  },
  methods: {
    navigateToSubItem (hash) {
      this.$router.push({ path: '/' + hash, hash: '' });
    },
    async getmenu () {
      // let data = await showMenu({ userId: this.$store.state.id });
      // if (data?.mtycode == 200) {
      // 	let temp = data.data?.menuBoList.map((item) => {
      // 		return {
      // 			...item,
      // 			icon: item.icon.split('&&')
      // 		};
      // 	});
      // 	this.items = temp;
      // } else {
      this.items = [
        {
          index: 'marketAnalysys',
          title: '市场分析',
          icon: 'M14.714 13.0006H2.42829V1.8577C2.42829 1.77913 2.36401 1.71484 2.28544 1.71484H1.28544C1.20686 1.71484 1.14258 1.77913 1.14258 1.8577V14.1434C1.14258 14.222 1.20686 14.2863 1.28544 14.2863H14.714C14.7926 14.2863 14.8569 14.222 14.8569 14.1434V13.1434C14.8569 13.0648 14.7926 13.0006 14.714 13.0006ZM3.99972 11.572H4.99972C5.07829 11.572 5.14258 11.5077 5.14258 11.4291V8.8577C5.14258 8.77913 5.07829 8.71484 4.99972 8.71484H3.99972C3.92115 8.71484 3.85686 8.77913 3.85686 8.8577V11.4291C3.85686 11.5077 3.92115 11.572 3.99972 11.572ZM6.71401 11.572H7.71401C7.79258 11.572 7.85687 11.5077 7.85687 11.4291V5.71484C7.85687 5.63627 7.79258 5.57199 7.71401 5.57199H6.71401C6.63544 5.57199 6.57115 5.63627 6.57115 5.71484V11.4291C6.57115 11.5077 6.63544 11.572 6.71401 11.572ZM9.42829 11.572H10.4283C10.5069 11.572 10.5712 11.5077 10.5712 11.4291V7.1077C10.5712 7.02913 10.5069 6.96484 10.4283 6.96484H9.42829C9.34972 6.96484 9.28544 7.02913 9.28544 7.1077V11.4291C9.28544 11.5077 9.34972 11.572 9.42829 11.572ZM12.1426 11.572H13.1426C13.2212 11.572 13.2854 11.5077 13.2854 11.4291V4.28627C13.2854 4.2077 13.2212 4.14342 13.1426 4.14342H12.1426C12.064 4.14342 11.9997 4.2077 11.9997 4.28627V11.4291C11.9997 11.5077 12.064 11.572 12.1426 11.572Z',
          subs: [
            {
              index: 'captial-market-conditions',
              title: '资本市场分析',
              subs: null
            },
            {
              index: 'fund-market-performance',
              title: '基金市场业绩',
              subs: null
            },
            {
              index: 'fund-market-allocation',
              title: '基金市场配置',
              subs: null
            }
          ]
        },
        {
          index: 'dashboard',
          title: '投前画像',
          icon: 'M14.7247 7.88569L8.74446 1.90243L8.34361 1.50117C8.25228 1.41035 8.12877 1.35938 8.00003 1.35938C7.87129 1.35938 7.74778 1.41035 7.65645 1.50117L1.2754 7.88569C1.18182 7.97901 1.10785 8.09015 1.05788 8.21255C1.00791 8.33495 0.982935 8.46614 0.984439 8.59836C0.99063 9.1437 1.4441 9.57904 1.98888 9.57904H2.64664V14.625H13.3534V9.57904H14.0251C14.2898 9.57904 14.5389 9.47524 14.7262 9.28778C14.8184 9.19577 14.8915 9.08637 14.9412 8.9659C14.9908 8.84543 15.0161 8.71629 15.0156 8.58596C15.0156 8.32259 14.9119 8.07316 14.7247 7.88569ZM8.86672 13.5095H7.13333V10.349H8.86672V13.5095ZM12.2391 8.46357V13.5095H9.85723V9.9772C9.85723 9.63482 9.5802 9.3575 9.23816 9.3575H6.76189C6.41986 9.3575 6.14282 9.63482 6.14282 9.9772V13.5095H3.76096V8.46357H2.2752L8.00158 2.73594L8.35909 3.09382L13.7264 8.46357H12.2391Z',
          subs: null
        },
        {
          index: 'alphaHeader',
          title: '投前捕获',
          icon: 'M0.574741 2.87207L5.36571 10.1406L5.36572 13.4531C5.36572 13.7638 5.61756 14.0156 5.92822 14.0156H10.0845C10.3951 14.0156 10.647 13.7638 10.647 13.4531L10.647 10.1406L15.4268 2.87155C15.6728 2.49751 15.4045 2 14.9568 2H1.04439C0.59646 2 0.328225 2.49807 0.574741 2.87207ZM13.9137 3.125H2.08886L5.97159 9.01562H10.0403L13.9137 3.125ZM9.52197 10.1406H6.49072V12.8906H9.52197V10.1406Z',
          subs: null
        },
        {
          index: 'Comparefundormanager',
          title: '比较',
          icon: "M1.4375 1.03125V12.0156H2.5625V6.57812H6.66406C8.19579 6.57812 9.4375 5.33641 9.4375 3.80469C9.4375 2.27296 8.19579 1.03125 6.66406 1.03125H1.4375ZM6.66406 2.15625H2.5625V5.45312H6.66406C7.57447 5.45312 8.3125 4.71509 8.3125 3.80469C8.3125 2.89428 7.57447 2.15625 6.66406 2.15625Z&&M7.29688 7.20312H6.17188V14.9844H7.29688V10.9559L8.11928 10.1335L12.9701 14.9844H14.5611L8.91477 9.33803L13.3636 4.88925L11.7868 4.875L7.29688 9.36494V7.20312Z"
          , subs: null
        },
        {
          index: 'fundPool',
          title: '投资池',
          icon: 'M14.6408 6.92037L14.6372 6.90723L12.6402 2.22413C12.5512 1.95967 12.2859 1.77734 11.9851 1.77734H3.88997C3.58738 1.77734 3.31861 1.96296 3.23317 2.23071L1.36602 6.86616L1.36068 6.87766L1.35712 6.8908C1.33398 6.97129 1.32686 7.05342 1.33932 7.13391C1.33754 7.16019 1.33576 7.18647 1.33576 7.21275V13.2231C1.33623 13.4878 1.4504 13.7416 1.65325 13.9288C1.8561 14.116 2.13109 14.2214 2.41796 14.2218H13.5817C14.178 14.2218 14.6639 13.7734 14.6657 13.2231V7.21275C14.6657 7.1914 14.6657 7.17005 14.6639 7.15198C14.6711 7.07149 14.6639 6.99429 14.6408 6.92037ZM9.37574 6.21404L9.3704 6.47193C9.35616 7.20947 8.80438 7.70554 7.99807 7.70554C7.6047 7.70554 7.26652 7.58891 7.02266 7.36716C6.77881 7.14541 6.64532 6.8366 6.6382 6.47193L6.63286 6.21404H2.96974L4.38479 3.03887H11.4903L12.9445 6.21404H9.37574ZM2.70097 7.47557H5.50082C5.93334 8.41351 6.85357 8.96707 7.99985 8.96707C8.59969 8.96707 9.15681 8.81266 9.60714 8.52027C10.0023 8.26403 10.3102 7.90594 10.5096 7.47557H13.2952V12.9603H2.70097V7.47557Z',
          subs: [
            {
              index: 'poolnormal',
              title: '基金池',
              subs: null
            },
            {
              index: 'poolcompare',
              title: '基金对标池',
              subs: null
            },
            {
              index: 'managerpoolnormal',
              title: '基金经理池',
              subs: null
            },
            {
              index: 'managerpoolcompare',
              title: '基金经理对标池',
              subs: null
            }
          ]
        },


        {
          index: 'board',
          title: '投后',
          icon: 'M4.43279 10.6779C4.48259 10.6281 4.48754 10.549 4.44434 10.4934C3.65771 9.48062 3.18785 8.20857 3.18785 6.82614C3.18785 3.52463 5.86405 0.848427 9.16556 0.848427C12.4671 0.848427 15.1433 3.52463 15.1433 6.82614C15.1433 10.1276 12.4671 12.8039 9.16556 12.8039C7.78312 12.8039 6.51108 12.334 5.4983 11.5474C5.44267 11.5042 5.36358 11.5091 5.31378 11.5589L1.75182 15.1209C1.74824 15.1245 1.74315 15.1268 1.73693 15.1268C1.73071 15.1268 1.72562 15.1245 1.72204 15.1209L0.871316 14.2702C0.867349 14.2661 0.86499 14.2605 0.86499 14.2548C0.86499 14.2491 0.867218 14.2436 0.871193 14.2396L4.43279 10.6779ZM9.16556 11.554C10.4279 11.554 11.6165 11.0624 12.5081 10.1689C13.4018 9.27732 13.8934 8.08863 13.8934 6.82614C13.8934 5.5637 13.4018 4.37506 12.5082 3.48348C11.6166 2.58988 10.428 2.09832 9.16556 2.09832C7.90312 2.09832 6.71448 2.58988 5.8229 3.48348C4.9293 4.37506 4.43774 5.5637 4.43774 6.82614C4.43774 8.08853 4.92926 9.27711 5.82278 10.1687C6.71438 11.0624 7.90306 11.554 9.16556 11.554Z',
          subs: [
            {
              index: 'monitorWarning',
              title: '监控预警',
              subs: null
            },
            {
              index: 'monitorBoard',
              title: '投后监控看板',
              subs: [
                {
                  index: 'performance',
                  title: '业绩看板',
                  subs: null
                },
                {
                  index: 'structure',
                  title: '结构看板',
                  subs: null
                },
                {
                  index: 'pivotTable',
                  title: '数据透视表',
                  subs: null
                },
                {
                  index: 'information',
                  title: '重点个股信息',
                  subs: null
                }
              ]
            },
            {
              index: 'management',
              title: '投后分析',
              subs: [
                {
                  index: 'objectManagement',
                  title: '分析对象管理',
                  subs: null
                },
                {
                  index: 'reportManagement',
                  title: '分析报告管理',
                  subs: null
                }
              ]
            },
            {
              index: 'mapping',
              title: '映射管理',
              subs: [
                {
                  index: 'industryMap',
                  title: '行业映射管理列表',
                  subs: null
                },
                {
                  index: 'productMap',
                  title: '产品映射管理列表',
                  subs: null
                },
                {
                  index: 'Tl4Map',
                  title: 'TL4映射列表',
                  subs: null
                },
                {
                  index: 'productRecord',
                  title: '产品管理记录',
                  subs: null
                },
                {
                  index: 'customMarketDivision',
                  title: '自定义市场划分',
                  subs: null
                },
                {
                  index: 'whitelist',
                  title: '白名单管理列表',
                  subs: null
                },
                {
                  index: 'blacklist',
                  title: '黑名单管理列表',
                  subs: null
                }
              ]
            }
          ]
        },
        {
          index: 'portfolio',
          title: '组合管理',
          icon: 'M5.125 7.85938C5.125 8.17004 4.87316 8.42188 4.5625 8.42188C4.25184 8.42188 4 8.17004 4 7.85938C4 7.54871 4.25184 7.29688 4.5625 7.29688C4.87316 7.29688 5.125 7.54871 5.125 7.85938Z&&M6.57812 7.29688C6.26746 7.29688 6.01562 7.54871 6.01562 7.85938C6.01562 8.17004 6.26747 8.42188 6.57812 8.42188H11.4375C11.7482 8.42188 12 8.17004 12 7.85938C12 7.54871 11.7482 7.29688 11.4375 7.29688H6.57812Z&&M5.125 10.1094C5.125 10.42 4.87316 10.6719 4.5625 10.6719C4.25184 10.6719 4 10.42 4 10.1094C4 9.79871 4.25184 9.54688 4.5625 9.54688C4.87316 9.54688 5.125 9.79871 5.125 10.1094Z&&M6.57812 9.54688C6.26746 9.54688 6.01562 9.79871 6.01562 10.1094C6.01562 10.42 6.26747 10.6719 6.57812 10.6719H11.4375C11.7482 10.6719 12 10.42 12 10.1094C12 9.79871 11.7482 9.54688 11.4375 9.54688H6.57812Z&&M0.984375 2.5625C0.984375 2.25184 1.23621 2 1.54688 2H6.59877C6.84263 2 7.05872 2.15714 7.13388 2.38913L7.63552 3.9375H14.4531C14.7638 3.9375 15.0156 4.18934 15.0156 4.5V13.4531C15.0156 13.7638 14.7638 14.0156 14.4531 14.0156H1.54687C1.23621 14.0156 0.984375 13.7638 0.984375 13.4531V2.5625ZM2.10938 3.9375V3.125H6.18972L6.45295 3.9375H2.10938ZM2.10938 5.0625V12.8906H13.8906V5.0625H2.10938Z',
          subs: [
            {
              index: 'configurationStrategyIndex',
              title: '配置策略研究',
              subs: null
            },
            {
              index: 'portfolioStudyIndex',
              title: '组合策略研究',
              subs: null
            },
            {
              index: 'portfolioList',
              title: '模拟组合管理',
              subs: null
            }
          ]
        },
        {
          index: 'dataAndReport',
          title: '数据与报告',
          icon: 'M2 13.3333C2 13.5101 2.07024 13.6797 2.19526 13.8047C2.32029 13.9298 2.48986 14 2.66667 14H13.3333C13.5101 14 13.6797 13.9298 13.8047 13.8047C13.9298 13.6797 14 13.5101 14 13.3333V8.33333H12.6667V12.6667H3.33333V3.33333H7.66667V2H2.66667C2.48986 2 2.32029 2.07024 2.19526 2.19526C2.07024 2.32029 2 2.48986 2 2.66667V13.3333Z&&M10.3333 1.66663H9.66667V2.99996H12.1143L7.25234 7.86196L6.78101 8.33329L7.72401 9.27629L8.19534 8.80463L13 3.99996V6.33329H14.3333V2.33329C14.3333 2.15648 14.2631 1.98691 14.1381 1.86189C14.0131 1.73686 13.8435 1.66663 13.6667 1.66663H10.3333Z',
          subs: [
            {
              index: 'subscriptionCenter',
              title: '订阅中心',
              subs: null
            },
            {
              index: 'exportData',
              title: '数据导出',
              sub: null
            }
          ]
        },

        {
          index: 'system',
          title: '系统管理',
          icon: 'M15.0078 13.5078C15.0078 13.1972 14.756 12.9453 14.4453 12.9453L13.0391 12.9453L13.0391 11.0078L13.0388 11.0078C13.039 11.0026 13.0391 10.9974 13.0391 10.9922C13.0391 10.6815 12.7872 10.4297 12.4766 10.4297L8.57031 10.4297L8.57031 9.03906L9.97656 9.03906C10.2872 9.03906 10.5391 8.78722 10.5391 8.47656L10.5391 4.53906C10.5391 4.2284 10.2872 3.97656 9.97656 3.97656L6.03906 3.97656C5.7284 3.97656 5.47656 4.2284 5.47656 4.53906L5.47656 8.47656C5.47656 8.78722 5.7284 9.03906 6.03906 9.03906L7.44531 9.03906L7.44531 10.4297L3.52344 10.4297C3.21278 10.4297 2.96094 10.6815 2.96094 10.9922C2.96094 10.9974 2.96101 11.0026 2.96115 11.0078L2.96094 11.0078L2.96094 12.9453L1.55469 12.9453C1.24403 12.9453 0.992187 13.1972 0.992187 13.5078L0.992187 17.4453C0.992187 17.756 1.24403 18.0078 1.55469 18.0078L5.49219 18.0078C5.80285 18.0078 6.05469 17.756 6.05469 17.4453L6.05469 13.5078C6.05469 13.1972 5.80285 12.9453 5.49219 12.9453L4.08594 12.9453L4.08594 11.5547L11.9141 11.5547L11.9141 12.9453L10.5078 12.9453C10.1972 12.9453 9.94531 13.1972 9.94531 13.5078L9.94531 17.4453C9.94531 17.756 10.1972 18.0078 10.5078 18.0078L14.4453 18.0078C14.756 18.0078 15.0078 17.756 15.0078 17.4453L15.0078 13.5078ZM13.8828 16.8828L13.8828 14.0703L11.0703 14.0703L11.0703 16.8828L13.8828 16.8828ZM9.41406 5.10156L6.60156 5.10156L6.60156 7.91406L9.41406 7.91406L9.41406 5.10156ZM2.11719 16.8828L2.11719 14.0703L4.92969 14.0703L4.92969 16.8828L2.11719 16.8828Z&&M30.482 17.358H28.914L28.69 16.364C29.194 16.406 29.67 16.434 30.132 16.434C30.468 16.434 30.65 16.252 30.65 15.916V12.486C29.306 12.57 27.85 12.654 26.268 12.724L25.988 11.828C26.31 11.8 26.604 11.73 26.87 11.646C28.018 11.142 29.306 10.358 30.734 9.294C29.516 9.392 28.2 9.476 26.8 9.56L26.59 8.664C26.842 8.636 27.066 8.566 27.276 8.482C27.976 8.132 28.802 7.446 29.754 6.41C28.564 6.466 27.304 6.494 25.96 6.494L25.666 5.612C29.684 5.612 33.03 5.318 35.69 4.73L36.18 5.584C34.724 5.92 33.016 6.172 31.028 6.326C30.16 7.334 29.278 8.118 28.382 8.706C29.502 8.65 30.636 8.566 31.784 8.468C32.414 7.964 33.058 7.404 33.73 6.802L34.654 7.264C32.288 9.35 30.23 10.848 28.466 11.772C30.748 11.646 32.708 11.492 34.332 11.296C33.982 10.848 33.618 10.386 33.24 9.938L33.996 9.476C35.144 10.82 36.026 11.982 36.642 12.976L35.83 13.55C35.564 13.074 35.256 12.598 34.92 12.108C33.954 12.22 32.876 12.318 31.672 12.416V16.168C31.672 16.952 31.266 17.358 30.482 17.358ZM33.59 13.27C35.004 14.292 36.18 15.314 37.132 16.35L36.418 17.064C35.564 16.084 34.402 15.034 32.918 13.914L33.59 13.27ZM28.256 13.368L28.97 13.998C28.018 15.146 26.842 16.14 25.442 16.994L24.812 16.182C26.184 15.37 27.332 14.432 28.256 13.368ZM43.348 14.208V15.174C42.06 15.818 40.604 16.35 38.98 16.742L38.854 15.734C40.562 15.356 42.06 14.852 43.348 14.208ZM41.108 4.534L42.074 4.926C41.374 6.718 40.66 8.146 39.932 9.21C40.548 9.126 41.164 9.014 41.78 8.902C42.032 8.426 42.284 7.936 42.55 7.404L43.46 7.754C42.228 10.078 41.206 11.744 40.394 12.752C41.374 12.5 42.354 12.164 43.348 11.73V12.668C41.878 13.284 40.464 13.69 39.12 13.914L38.84 12.99C39.022 12.906 39.176 12.794 39.302 12.682C39.834 12.122 40.492 11.184 41.262 9.84C40.506 9.98 39.736 10.12 38.966 10.232L38.7 9.308C38.868 9.238 39.008 9.112 39.148 8.916C39.904 7.642 40.548 6.186 41.108 4.534ZM43.824 10.68L43.6 9.784C43.684 9.756 43.74 9.728 43.796 9.714C44.272 9.364 44.874 8.468 45.63 7.012H43.236V6.046H46.806C46.61 5.514 46.4 5.038 46.176 4.604L47.198 4.436C47.408 4.912 47.618 5.458 47.814 6.046H50.964V7.012H46.68C46.064 8.23 45.518 9.112 45.042 9.672C46.344 9.56 47.646 9.406 48.934 9.196C48.626 8.748 48.29 8.286 47.94 7.838L48.71 7.362C49.662 8.58 50.404 9.644 50.922 10.568L50.138 11.114C49.942 10.75 49.732 10.4 49.494 10.036C47.758 10.316 45.868 10.526 43.824 10.68ZM44.958 11.254H45.938C45.868 13.018 45.644 14.32 45.28 15.146C44.874 16.014 44.202 16.756 43.25 17.372L42.606 16.574C43.432 16.042 44.02 15.426 44.37 14.712C44.706 13.984 44.902 12.836 44.958 11.254ZM49.942 17.148H48.962C48.122 17.148 47.702 16.742 47.702 15.93V11.128H48.668V15.762C48.668 16.056 48.766 16.21 48.99 16.238H49.9C50.208 16.21 50.39 16.042 50.446 15.734C50.502 15.342 50.544 14.74 50.544 13.914L51.412 14.222C51.412 15.538 51.314 16.364 51.132 16.672C50.95 16.952 50.558 17.12 49.942 17.148ZM63.004 10.092V12.836H56.018V13.802H63.648V17.4H62.654V16.812H56.018V17.386H55.01V10.092H63.004ZM56.018 15.916H62.654V14.614H56.018V15.916ZM56.018 12.024H62.024V10.904H56.018V12.024ZM64.936 8.398V10.932H63.956V9.266H54.044V10.932H53.064V8.398H58.608C58.468 8.062 58.328 7.768 58.174 7.488L59.154 7.32L59.07 7.264C59.742 6.424 60.218 5.514 60.526 4.506L61.492 4.716C61.394 5.01 61.296 5.29 61.184 5.57H65.23V6.452H62.738C63.032 6.844 63.27 7.208 63.438 7.544L62.542 7.88C62.29 7.376 61.996 6.9 61.66 6.452H60.778C60.526 6.928 60.232 7.376 59.924 7.796L59.168 7.334C59.322 7.656 59.476 8.006 59.616 8.398H64.936ZM54.744 6.452C54.408 6.998 54.044 7.502 53.624 7.964L52.742 7.404C53.582 6.536 54.198 5.556 54.59 4.492L55.556 4.702C55.444 5.01 55.318 5.29 55.206 5.57H58.958V6.452H56.606C56.914 6.872 57.18 7.278 57.376 7.656L56.452 8.006C56.2 7.474 55.892 6.956 55.542 6.452H54.744ZM71.824 5.122H78.67V11.59H75.758V13.242H78.908V14.194H75.758V16.014H79.426V16.98H70.998V16.014H74.75V14.194H71.586V13.242H74.75V11.59H71.824V5.122ZM77.704 10.694V8.804H75.758V10.694H77.704ZM74.75 10.694V8.804H72.79V10.694H74.75ZM72.79 7.922H74.75V6.018H72.79V7.922ZM75.758 6.018V7.922H77.704V6.018H75.758ZM66.882 9.756H68.408V6.438H66.686V5.486H71.152V6.438H69.444V9.756H70.984V10.722H69.444V13.9C69.976 13.718 70.48 13.522 70.984 13.298V14.278C69.696 14.81 68.31 15.258 66.826 15.622L66.574 14.656C67.19 14.544 67.806 14.404 68.408 14.236V10.722H66.882V9.756Z',
          subs: [
            // {
            // 	index: 'CompanyManager',
            // 	title: '服务机构',
            // 	subs: null
            // },
            {
              index: 'userrolecontrol',
              title: '用户权限管理',
              subs: null
            }
          ]
        }
      ].map((item) => {
        return {
          ...item,
          icon: item.icon.split('&&')
        };
      });
      // }
    }
  }
};
</script>

<style lang="scss" scoped>
.sidebar {
	font-family: 'PingFang';
	width: 200px;
	// margin-top: -3px;
	display: block;
	/* position: absolute;
  left: 0;
  top: 56px;
  bottom: 0; */
	overflow-y: scroll;
	overflow-x: hidden;
	transition: all 0.5s;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

	.el-menu--collapse {
		width: 48px;
	}
	.el-menu--inline {
		> .el-menu-item {
			padding-left: 16px !important;
			padding-right: 0 !important;
		}
		.el-menu {
			> .el-menu-item {
				padding-left: 30px !important;
				padding-right: 0 !important;
			}
		}
	}
}

.sidebar .el-menu {
	background: #ffffff !important;
	background-color: #ffffff !important;
	color: white !important;
	border-right: none;

	li {
		margin-bottom: 8px;

		::v-deep .el-submenu__title {
			height: 40px !important;
			line-height: 40px !important;
			// margin-bottom: 8px;
		}
	}
}

.sidebar .el-menu .el-menu-item {
	background: #ffffff;
	background-color: #ffffff;
	color: white;
	//   padding-left: 16px !important;
	height: 40px !important;
	line-height: 40px !important;
	margin-top: 4px;

	::v-deep div {
		padding: 0 0 0 16px !important;
	}

	// text-align: center;
}

// ::v-deep .el-submenu__title {
//   height: 40px !important;
// }

.sidebar .el-menu .el-menu-item:hover {
	background: #ffffff !important;
	/* background: #4096ff1a !important;*/
	i {
		color: #40AFFF !important;
	}

	svg path {
		fill: #40AFFF;
	}
}

.el-menu .el-menu-item:hover {
	background: #ffffff !important;
	// background-color: #ffffff !important;
	color: #40AFFF !important;

	i {
		color: #40AFFF !important;
	}

	svg path {
		fill: #40AFFF !important;
	}
}
.sidebar .el-submenu ::v-deep .el-submenu__title:hover {
	background: #ffffff !important;
	background-color: #ffffff !important;
	color: #40AFFF !important;

	i {
		color: #40AFFF !important;
	}

	svg path {
		fill: #40AFFF;
	}
}

// .sidebar .el-submenu__title:hover {
// background-color: #ffffff !important;
// i {
// 	color: #40AFFF !important;
// }
// }
.sidebar .el-submenu {
	background: #ffffff;
	background-color: #ffffff;
	color: white;

	::v-deep div {
		padding: 0 0 0 16px !important;
		// padding: 0 !important;
	}

	::v-deep .el-menu .el-submenu {
		margin-left: 16px !important;
	}

	// text-align: center;
}

.sidebar-el-menu > .el-menu-item.is-active {
	background: #4096ff1a !important;
	background-color: #4096ff1a !important;
	border-right: 2px solid #4096ff;
	color: #40AFFF !important;
	font-weight: 600;

	svg path {
		fill: #40AFFF;
	}
}

.sidebar .el-menu .is-active {
	svg path {
		fill: #40AFFF;
	}
}

.sidebar .el-submenu .is-active {
	// border-right: 2px solid #4096ff;
}

.sidebar::-webkit-scrollbar {
	width: 0;
}

.sidebar > ul {
	height: 100%;
}
</style>
<style lang="scss">
.sidebar {
	.el-menu {
		.el-submenu {
			.el-submenu__title {
				line-height: 40px;
				height: 40px !important;
			}
		}
	}
}
</style>
