<template>
	<div id="industryPositionChange" class="position_style">
		<analysis-card-title title="行业配置变化年度堆叠图" @downloadExcel="exportExcel"> </analysis-card-title>
		<div class="flex_start" id="industryPositionChangeMain">
			<div class="left_legend py-20">
				<div v-for="item in itemList" :key="item.label" class="mb-4 flex_start legend_item" @click="changeShow(item.label)">
					<div class="item_icon mr-8" :style="item.show ? `background-color:${item.color}` : `background-color:#D9D9D9`"></div>
					<div :style="item.show ? `color:rgba(0, 0, 0, 0.85);` : `color:rgba(0, 0, 0, 0.45);`">
						{{ item.label }}
					</div>
				</div>
			</div>
			<div class="charts_fill_class ml-20" style="flex: 1" v-loading="loading">
				<el-empty v-show="!show" :image-size="160"></el-empty>
				<v-chart
					v-show="show"
					ref="positionStyle"
					v-loading="loading"
					style="width: 100%; height: 668px"
					autoresize
					element-loading-text="暂无数据"
					element-loading-spinner="el-icon-document-delete"
					element-loading-background="rgba(239, 239, 239, 0.5)"
					:options="optionex"
				/>
			</div>
		</div>
	</div>
</template>

<script>
import { lineChartOption } from '@/utils/chartStyle.js';

// 行业配置表现
import { getIndustryInfo } from '@/api/pages/Analysis.js';
import { filter_json_to_excel } from '@/utils/exportExcel.js';

// 持仓风格
export default {
	name: 'industryPositionChange',
	data() {
		return {
			optionex: {},
			loading: true,
			show: true,
			info: {},
			color: [
				'#4096ff',
				'#89B3FA',
				'#D0DFF9',
				'#4096ff',
				'#FFB462',
				'#F8D3AB',
				'#83BE57',
				'#A5D084',
				'#D0ECBA',
				'#904371',
				'#BF5694',
				'#DCA1C4',
				'#E8684A',
				'#EF907A',
				'#F9C5B9',
				'#F6BD16',
				'#FFDD79',
				'#FFEDB9',
				'#7388A9',
				'#9EB2D2',
				'#CADAF2',
				'#EB2F96',
				'#F66DB8',
				'#FFBAE0',
				'#13C2C2',
				'#68E4DD',
				'#B5F5EC',
				'#B43438',
				'#D16063',
				'#EE9799',
				'#722ED1',
				'#B37FEB'
			],
			data: [],
			itemList: []
		};
	},
	methods: {
		// 获取父组件传递数据
		async getData(info) {
			this.info = info;
			await this.getIndustryInfo();
		},
		// 获取行业配置数据
		async getIndustryInfo() {
			this.loading = true;
			/**
			 * 预期data数据
			 * [{
			 * code: "010709",
			 * excess_return: -0.0043709392051636264,
			 * industryStandard: "申万(2021)",
			 * industry_code: "110000",
			 * industry_name: "农林牧渔",
			 * industry_return: 0.44926761479007526,
			 * weight: 0.0053923605273020515,
			 * yearqtr: "2021 Q1"
			 * }...]
			 */
			let data = await getIndustryInfo({
				flag: this.info.flag,
				code: this.info.code,
				type: this.info.type,
				start_date: this.info.start_date,
				end_date: this.info.end_date,
				industry_section: this.info.type == 'equityhk' ? '恒生一级' : '申万(2021)',
				industry_code: '',
				mtyhints: ''
			});
			this.loading = false;
			if (data?.mtycode == 200) {
				this.data = data.data;
				this.filterItemList(data?.data);
				this.drawLine(data?.data);
			} else {
				this.hideLoading();
			}
		},

		// 切换具体图例的显隐
		changeShow(name) {
			let selected = {};
			// 获取当前点击的索引
			let index = this.itemList.findIndex((v) => v.label == name);
			// 修改数组中当前图例的显示状态
			this.$set(this.itemList, index, {
				...this.itemList[index],
				show: !this.itemList[index].show
			});
			// 将数组转化成echart接收格式
			this.itemList.map((item) => {
				selected[item.label] = item.show;
			});
			this.optionex = {
				...this.optionex,
				legend: { ...this.optionex.legend, selected }
			};
		},
		// 画图
		drawLine(data) {
			let { series, date_list } = this.filterData(data);
			this.optionex = lineChartOption({
				toolbox: 'none',
				tooltip: {
					formatter: (params) => {
						let str = `<div style="display:flex;align-items:center;maring-bottom:8px;"><div style="margin-right:4px">时间:</div><div>${params[0].axisValue}</div></div>`;
						params = params.sort((a, b) => {
							return b.value[1] - a.value[1];
						});
						for (let i = 0; i < params.length; i++) {
							let value = !isNaN(params[i].value[1]) ? (params[i].value[1] * 1).toFixed(3) + '%' : 0;
							let dotHtml = `<div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:${
								this.itemList.find((v) => v.label == params[i].seriesName)?.color
							}"></div>`;
							if (value != 0 && params[i].value[1] * 1 != 0) {
								str += `<div style="margin-bottom:8px;display:flex;align-items:center;justify-content:space-between;"><div style="display:flex;align-items:center;">${dotHtml}<div>${params[i].seriesName}:</div></div><div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">${value}</div></div>`;
							}
						}
						return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${str}</div>`;
					}
				},
				dataZoom: true,
				xAxis: [{ type: 'category', data: date_list }],
				yAxis: [
					{
						type: 'value',
						max: 100,
						formatter: function (val) {
							return val + '%';
						}
					}
				],
				series
			});
		},
		// 过滤图例
		filterItemList(data) {
			let list = Array.from(new Set(data.map((v) => v.industry_name)));
			this.itemList = list.map((v, i) => {
				return { label: v, color: this.color[i], show: true };
			});
		},
		// 隐藏模块
		hideLoading() {
			this.show = false;
		},
		exportImage() {
			let chart = this.$refs['positionStyle'].getDataURL({
				type: 'png',
				pixelRatio: 3,
				backgroundColor: '#fff',
				excludeComponents: ['dataZoom']
			});
			let aLink = document.createElement('a');
			aLink.style.display = 'none';
			aLink.href = chart;
			aLink.download = '持仓风格.jpg';
			// 触发点击-然后移除
			document.body.appendChild(aLink);
			aLink.click();
			document.body.removeChild(aLink);
		},
		exportExcel() {
			let list = [
				{ label: '基金代码', value: 'code' },
				{ label: '行业类型', value: 'industryStandard' },
				{ label: '行业代码', value: 'industry_code' },
				{ label: '行业名称', value: 'industry_name' },
				{ label: '报告期', value: 'yearqtr' },
				{ label: '持仓权重', value: 'weight' },
				{ label: '行业收益', value: 'industry_return' },
				{ label: '行业超额收益', value: 'excess_return' },
				{ label: '对比基准', value: 'indexCode' }
			];
			filter_json_to_excel(list, this.data, '行业配置变化年度堆叠图');
		},
		// 过滤接收数据
		filterData(data) {
			let result = data.sort((a, b) => {
				return this.moment(this.moment(a.yearqtr, 'YYYY QQ').format()).isBefore(this.moment(b.yearqtr, 'YYYY QQ').format()) ? -1 : 1;
			});
			let date_list = Array.from(new Set(result.map((v) => v.yearqtr)));
			let series = [];
			this.itemList.map((item) => {
				let arr = result.filter((v) => v.industry_name == item.label);
				series.push({
					name: item.label,
					type: 'line',
					stack: '总量',
					barWidth: '100%',
					symbol: 'none',
					lineStyle: {
						color: item.color
					},
					areaStyle: {
						color: item.color,
						opacity: 0.25
					},
					data: date_list.map((item) => {
						let index = arr.findIndex((v) => v.yearqtr == item);
						if (index == -1) {
							return [item, 0];
						} else {
							return [item, !isNaN(arr[index].weight) ? arr[index].weight : 0];
						}
					})
				});
			});
			return { date_list, series };
		},

		async createPrintWord(info) {
			await this.getData(info);
			let key = 'industryPositionChangeMain';
			let height = document.getElementById(key).clientHeight;
			let width = document.getElementById(key).clientWidth;
			let canvas = await this.html2canvas(document.getElementById(key), {
				scale: 3
			});
			return this.show
				? [
						...this.$exportWord.exportTitle('行业配置变化年度堆叠图'),
						...this.$exportWord.exportChart(canvas.toDataURL('image/jpg'), {
							width,
							height
						})
				  ]
				: [];
		}
	}
};
</script>

<style lang="scss" scoped>
.position_style {
	.left_legend {
		width: 140px;
		height: 708px;
		padding-left: 20px;
		margin-left: -20px;
		margin-top: -20px;
		margin-bottom: -20px;
		background: #fff;
		box-shadow: 8px 0px 20px 0px rgba(0, 0, 0, 0.08);
		.legend_item {
			cursor: pointer;
			color: rgba(0, 0, 0, 0.65);
			font-family: PingFang SC;
			font-size: 12px;
			font-style: normal;
			font-weight: 400;
			.item_icon {
				width: 12px;
				height: 8px;
			}
		}
	}
}
</style>
