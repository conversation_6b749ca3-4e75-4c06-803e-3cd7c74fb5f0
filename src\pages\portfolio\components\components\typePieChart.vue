<template>
  <div class="flex_card">
    <div
      class="small_template"
      style="min-width: calc(50% - 24px)"
      v-for="item in data"
      :key="item.type"
    >
      <div
        style="
          display: flex;
          justify-content: space-between;
          align-items: center;
        "
      >
        <div class="title">{{ item.name }}</div>
        <!-- <el-button type="primary" @click="exportExcel(item.name)">导出excel</el-button> -->
      </div>
      <div>
        <v-chart
          :ref="'typePieChart' + item.type"
          class="charts_two_class"
          style="width: 100%"
          autoresize
          :options="item.option"
          v-loading="loading"
          element-loading-text="暂无数据"
          element-loading-spinner="el-icon-document-delete"
          element-loading-background="rgba(239, 239, 239, 0.5)"
        ></v-chart>
      </div>
    </div>
  </div>
</template>

<script>
import vChart from "vue-echarts";
import { getCombinationType } from "@/api/pages/SystemMixed.js";
import { filter_json_to_excel } from "@/utils/exportExcel.js";
export default {
  components: { vChart },
  data() {
    return {
      loading: true,
      option: {},
      styleColor: [
        "#4096ff",
        "#4096ff",
        "#7388A9",
        "#6F80DD",
        "#6C96F2",
        "#FD6865",
        "#83D6AE",
        "#88C9E9",
        "#ED589D",
        "#FA541C",
      ],
      list: [
        {
          type: "type",
          name: "基金类型穿透",
        },
        {
          type: "行业穿透",
          name: "行业穿透",
        },
        {
          type: "成长价值",
          name: "成长价值穿透",
        },
        {
          type: "大小盘",
          name: "大小盘穿透",
        },
        {
          type: "久期",
          name: "久期穿透",
        },
        {
          type: "信用",
          name: "信用穿透",
        },
        {
          type: "风险等级",
          name: "风险等级",
        },
      ],
      data: [],
      excelData: [],
    };
  },
  methods: {
    async getData(code_list) {
      this.loading = false;
      this.data = [];
      this.list.map(async (item) => {
        let data = await getCombinationType({ code_list, type: item.type });
        if (data?.mtycode == 200) {
          this.data.push({
            name: item.name,
            type: item.type,
            option: this.formatPieChart(data?.data),
          });
          this.excelData.push({
            name: item.name,
            type: item.type,
            data: data?.table_data,
          });
        }
      });
      // let data2 = await getCombinationRisk({ code_list });
      // if (data2?.mtycode == 200) {
      // 	this.data.push({
      // 		name: item.name,
      // 		type: item.type,
      // 		option: this.formatPieChart(data?.data)
      // 	});
      // }
    },
    exportExcel(name) {
      let data = this.excelData.filter((item) => {
        return item.name == name;
      })?.[0]?.data;
      let column = [{ label: "基金代码", value: "code" }];
      filter_json_to_excel();
    },
    formatPieChart(data) {
      let series = [];
      data?.forEach((item, index) => {
        series.push({ value: item.weight, name: item.type });
      });
      let option = {
        color: this.styleColor,
        tooltip: {
          trigger: "item",
          formatter: (item) => {
            return `${item.data.name} ${
              item.data.name.indexOf("R") >= 0
                ? ":" + item.data.value + "个"
                : (parseFloat(item.data.value) * 100).toFixed(2) + "%"
            }`;
          },
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
          top: -4,
          width: 104,
        },
        legend: {
          show: false,
        },
        series: [
          {
            type: "pie",
            radius: "70%",
            data: series,
            label: {
              fontSize: "14px",
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.2)",
              },
            },
          },
        ],
      };
      return option;
    },
    createPrintWord() {
      let list = [];
      this.data.map((item) => {
        this.$refs["typePieChart" + item.type]?.[0].mergeOptions({
          toolbox: { show: false },
        });
        let height =
          this.$refs["typePieChart" + item.type]?.[0]?.$el.clientHeight;
        let width =
          this.$refs["typePieChart" + item.type]?.[0]?.$el.clientWidth;
        let chart = this.$refs["typePieChart" + item.type]?.[0].getDataURL({
          type: "png",
          pixelRatio: 3,
          backgroundColor: "#fff",
        });
        this.$refs["typePieChart" + item.type]?.[0].mergeOptions({
          toolbox: { show: true },
        });
        list.push(
          ...this.$exportWord.exportTitle(item.name),
          ...this.$exportWord.exportChart(chart, { width, height })
        );
      });
      return list;
    },
  },
};
</script>

<style></style>
