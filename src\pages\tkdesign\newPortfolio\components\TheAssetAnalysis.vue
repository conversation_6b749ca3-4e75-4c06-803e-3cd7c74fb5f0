<template>
	<div class="the-asset-analysis-wrapper">
		<div class="data-form-wrapper" v-loading="loading">
			<el-date-picker
				:unlink-panels="true"
				class="data-picker"
				v-model="datePicker"
				type="daterange"
				start-placeholder="开始日期"
				end-placeholder="结束日期"
			>
			</el-date-picker>
			<el-button
				v-for="item in radioList"
				class="date-btn"
				:class="timeRadio === item.value && showDefault ? 'active' : ''"
				type="text"
				@click="handleQuickClick(item.value)"
			>
				{{ item.label }}
			</el-button>
		</div>
		<div>
			<div v-loading="incomeLoading">
				<VerticalLineHeader title="收益走势" showDownloadBtn @downloadClick="exportIncomeExcel"> </VerticalLineHeader>
				<IncomeTrendChart ref="income-trend-chart"></IncomeTrendChart>
			</div>
			<div v-loading="performanceLoading">
				<VerticalLineHeader
					:title="marketType === 'index' ? '大类资产表现' : '基金产品表现'"
					showDownloadBtn
					@downloadClick="exportPerformanceExcel"
				>
					<div slot="right" class="title-right-form">
						选择指标：
						<el-select v-model="formData.measure" placeholder="选择指标" @change="handleMeasureChange">
							<el-option v-for="item in measureOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
						</el-select>
						<RadioGroup
							class="radio-group-wrapper"
							:defaultValue="defaultValue"
							:configList="configList"
							@change="handleTypeChange"
						></RadioGroup>
					</div>
				</VerticalLineHeader>
				<el-table
					:cell-class-name="hanldeCellClassName"
					class="fund-analysis-table"
					:data="stagePerformanceData"
					stripe
					border
					max-height="400px"
					@sort-change="sortStagePerformanceData"
				>
					<el-table-column
						v-for="item in columnList"
						:key="item.value"
						align="gotoleft"
						:prop="item.value"
						:sortable="item.sortable ? item.sortable : false"
						:label="item.label"
						:width="item.width"
						min-width="128px"
					>
						<template slot-scope="scope">
							<span
								v-if="item.color && colorType === 'red_or_green'"
								:style="`${scope.row[item.value] > 0 ? 'color:red' : scope.row[item.value] < 0 ? 'color:green' : ''}`"
								>{{ item.format ? formatFn(scope.row[item.value]) : scope.row[item.value] }}</span
							>
							<span v-else>{{ item.format ? formatFn(scope.row[item.value]) : scope.row[item.value] }}</span>
						</template>
					</el-table-column>
					<template slot="empty">
						<el-empty image-size="160"></el-empty>
					</template>
				</el-table>
			</div>
			<div v-loading="correlationLoading">
				<VerticalLineHeader title="收益相关系数矩阵" showDownloadBtn @downloadClick="exportCorrelationEXcel"></VerticalLineHeader>
				<CorrelationCoefficientChart ref="correlation-coefficient-chart"></CorrelationCoefficientChart>
			</div>
		</div>
	</div>
</template>
<script>
import VerticalLineHeader from '../../components/VerticalLineHeader.vue';
import IncomeTrendChart from './chart/IncomeTrendChart.vue';
import CorrelationCoefficientChart from './chart/CorrelationCoefficientChart.vue';
import { getProductTrend, getProductPerformance, getReturnsCorrelationMatrix } from '@/api/pages/tkAnalysis/portfolio.js';
import RadioGroup from '../../components/RadioGroup.vue';
import { filter_json_to_excel } from '@/utils/exportExcel.js';
import { color } from 'echarts/lib/export';

export default {
	name: 'TheAssetAnalysis',
	components: {
		VerticalLineHeader,
		IncomeTrendChart,
		CorrelationCoefficientChart,
		RadioGroup
	},
	props: {
		// "1": "fund", "2": "manager", "3": "company", "4": "combination", "5": "pool", "6": "index", "7": "user_index", "8": "self", "9": "stock"
		flag: {
			type: String,
			default: ''
		},
		//类型  指数：index；基金：fund
		marketType: {
			type: String,
			default: ''
		},
		//code列表
		codeList: {
			type: Array,
			default: () => {
				return [];
			}
		}
	},
	data() {
		return {
			showDefault: true, //是否为默认选中日期
			//快捷时间选择器选项
			radioList: [
				{
					label: '近6月',
					value: '6m'
				},
				{
					label: '近1年',
					value: '1y'
				},
				{
					label: '近2年',
					value: '2y'
				},
				{
					label: '近3年',
					value: '3y'
				}
			],
			//当前选中的快捷时间
			timeRadio: '',
			defaultValue: {
				radioValue: 'since',
				selectValue: 'since'
			},
			// 近期：since; monthly:月度；weekly：周度；quarterly;季度；halfyearly：半年；yearly；年度
			configList: [
				{ label: 'since', text: '近期业绩', value: 'since' },
				{
					type: 'select',
					label: '',
					text: '自然年份业绩',
					width: '112px',
					option: [
						{
							label: '月度',
							value: 'monthly'
						},
						{
							label: '季度',
							value: 'quarterly'
						},
						{
							label: '半年度',
							value: 'halfyearly'
						},
						{
							label: '年',
							value: 'yearly'
						}
					]
				}
			],
			//指标选项列表
			// ave_return：累计收益，maxdrawdown：最大回撤；volatility：波动率；
			// sharpe：夏普率
			measureOptions: [
				{
					label: '累计收益',
					value: 'cum_return'
				},
				{
					label: '最大回撤',
					value: 'maxdrawdown'
				},
				{
					label: '波动率',
					value: 'volatility'
				},
				{
					label: '夏普率',
					value: 'sharpe'
				}
			],
			datePicker: [],
			//表现数据列表
			stagePerformanceData: [],
			//收益走势数据
			incomeData: [],
			correlationColumnList: [],
			//相关系数矩阵
			correlationData: [],
			//表现列表表头
			columnList: [],
			// column: [
			// 	{
			// 		label: '资产名称',
			// 		value: 'name_x'
			// 	}
			// ],
			formData: {
				measure: 'cum_return', //指标选择
				cutFlag: 'since' // 近期：since; monthly:月度；weekly：周度；quarterly;季度；halfyearly：半年；yearly；年度
			},
			incomeLoading: false,
			correlationLoading: false,
			performanceLoading: false
		};
	},
	watch: {
		//当日期选择改变时，需要重新调用接口
		datePicker(newVal, oldVal) {
			this.getAllData();
		}
	},
	computed: {
		loading() {
			return this.incomeLoading || this.correlationLoading || this.performanceLoading;
		},
		startDate() {
			if (this.datePicker[0]) {
				return this.moment(this.datePicker[0]).format('YYYY-MM-DD');
			}
			return '';
		},
		endDate() {
			if (this.datePicker[1]) {
				return this.moment(this.datePicker[1]).format('YYYY-MM-DD');
			}
			return '';
		},
		formatFn() {
			if (this.formData.measure === 'sharpe') {
				return this.fix2;
			}
			return this.fix2p;
		},
		//使用的表格文字颜色模式 colorType默认黑色 red_or_green
		colorType() {
			//夏普和累计收益展示彩色
			if (this.formData.measure === 'sharpe' || this.formData.measure === 'cum_return') {
				return 'red_or_green';
			}
			return '';
		}
	},
	created() {
		this.setQuickDate('1y');
	},
	mounted() {},
	methods: {
		//表现表格 单元格样式定义
		hanldeCellClassName({ row, column, rowIndex, columnIndex }) {
			return 'yangshi';
		},
		// 百分化
		fix2p(val) {
			let fixValue = val * 1 ? (val * 100).toFixed(2) * 1 : '--';
			//加正号
			if (fixValue > 0) {
				fixValue = '+' + fixValue;
			}
			//加百分号
			if (!isNaN(fixValue)) {
				fixValue = fixValue + '%';
			}
			return fixValue;
		},
		//保留两位小数
		fix2(val) {
			let fixValue = val * 1 ? (val * 1).toFixed(2) : '--';
			return fixValue;
		},
		//导出收益走势表格
		exportIncomeExcel() {
			let title = '收益走势';
			let list = [
				{
					value: 'name',
					format: '',
					label: '资产名称'
				},
				{
					value: 'date',
					format: '',
					label: '日期'
				},
				{
					value: 'rate',
					format: '',
					label: '值'
				}
			];
			filter_json_to_excel(list, this.incomeData, title);
		},
		//导出大类资产表现表格
		exportPerformanceExcel() {
			let title = this.marketType === 'index' ? '大类资产表现' : '基金产品表现';
			let list = this.columnList.map((item) => {
				return {
					...item,
					value: item.value,
					format: ''
				};
			});
			filter_json_to_excel(list, this.stagePerformanceData, title);
		},
		//导出收益相关矩阵表格
		exportCorrelationEXcel() {
			let title = '收益相关系数矩阵';
			filter_json_to_excel(this.correlationColumnList, this.correlationData, title);
		},

		getAllData() {
			this.getIncomeData();
			this.getCorrelationData();
			this.getPerformanceData();
		},
		handleQuickClick(type) {
			this.showDefault = false;
			this.setQuickDate(type);
		},
		setQuickDate(type) {
			this.timeRadio = type;
			const dataNow = this.moment(this.moment()).format('YYYY-MM-DD');
			let startTime = this.getQuickDate(type, dataNow);
			this.datePicker = [startTime, dataNow];
		},
		//时间快捷选择器
		getQuickDate(type, time) {
			const date = time;
			let result;
			switch (type) {
				case '6m':
					result = this.moment(date).subtract(6, 'months').format('YYYY-MM-DD');
					break;
				case '1y':
					result = this.moment(date).subtract(1, 'years').format('YYYY-MM-DD');
					break;
				case '2y':
					result = this.moment(date).subtract(2, 'years').format('YYYY-MM-DD');
					break;
				case '3y':
					result = this.moment(date).subtract(3, 'years').format('YYYY-MM-DD');
					break;
				default:
					break;
			}
			return result;
		},
		//获取表现列表
		async getPerformanceData() {
			this.performanceLoading = true;
			this.stagePerformanceData = [];
			let params = {
				codes: this.codeList,
				startDate: this.startDate,
				endDate: this.endDate,
				flag: this.marketType,
				cutFlag: this.formData.cutFlag,
				scene: 'analysis',
				measure: [this.formData.measure]
			};
			let req = await getProductPerformance(params);
			let { data, mtycode, mtymessage } = req || {};
			if (mtycode == 200) {
				this.dulPerFormanceData(data);
			} else {
				this.$message.warning(mtymessage);
				this.stagePerformanceData = [];
			}
			this.performanceLoading = false;
		},
		dulPerFormanceData(data) {
			this.columnList = [
				{
					value: 'name',
					label: '资产名称'
				}
			];
			//夏普保留两位 其他的乘以100加100%号保留两位
			let format = true;
			//是否需要根据判断展示不同的字体样式
			const colorFlag = true;
			if (this.formData.cutFlag === 'since') {
				this.stagePerformanceData = data?.recentList || [];
				const dateList = [
					{
						value: 'yearToDate',
						label: '年初至今',
						color: colorFlag,
						format: format,
						sortable: true
					},
					{
						value: 'lastWeek',
						label: '近一周',
						color: colorFlag,
						format: format,
						sortable: true
					},
					{
						value: 'lastMounth',
						label: '近一月',
						color: colorFlag,
						format: format,
						sortable: true
					},
					{
						value: 'lastSeason',
						label: '近一季度',
						color: colorFlag,
						format: format,
						sortable: true
					},
					{
						value: 'lastHalfYears',
						label: '近半年',
						color: colorFlag,
						format: format,
						sortable: true
					},
					{
						value: 'lastYear',
						label: '近一年',
						color: colorFlag,
						format: format,
						sortable: true
					},
					{
						value: 'lastThreeYear',
						label: '近三年',
						color: colorFlag,
						format: format,
						sortable: true
					},
					{
						value: 'lastFiveYear',
						label: '近五年',
						color: colorFlag,
						format: format,
						sortable: true
					},

					{
						value: 'since',
						label: '全时段',
						color: colorFlag,
						format: format,
						sortable: true
					},
					{
						value: 'customTime',
						label: '区间',
						color: colorFlag,
						format: format,
						sortable: true
					}
				];
				this.columnList = this.columnList.concat(dateList);
				console.log(this.columnList);
			} else {
				let relData = [];
				//非近期的取非近期数组
				relData = data?.periodList || [];
				let flag_list = [];

				// {
				// 	label: '累计收益',
				// 	value: 'cum_return'
				// },
				// {
				// 	label: '最大回撤',
				// 	value: 'maxdrawdown'
				// },
				// {
				// 	label: '波动率',
				// 	value: 'volatility'
				// },
				// {
				// 	label: '夏普率',
				// 	value: 'sharpe'
				// }
				//对齐入参出参枚举
				const option = {
					['cum_return']: 'cumReturn',
					['maxdrawdown']: 'maxdrawdown',
					['volatility']: 'volatility',
					['sharpe']: 'sharpe'
				};
				let valueKey = option[this.formData.measure];
				for (let i = 0; i < relData.length; i++) {
					if (flag_list.indexOf(relData[i].flag) < 0) {
						flag_list.push(relData[i].flag);
						this.columnList.push({
							value: relData[i].flag,
							label: relData[i].flag,
							color: colorFlag,
							format: format,
							sortable: true
						});
					}
					let value = relData[i][valueKey];
					this.stagePerformanceData.push({ ...relData[i], [relData[i].flag]: value });
				}
				//将相同code的数组合并在一起
				const mergedArray = this.stagePerformanceData.reduce((accumulator, currentValue) => {
					const existingIndex = accumulator.findIndex((item) => item.code === currentValue.code);

					if (existingIndex !== -1) {
						// 如果已存在具有相同id的对象，则合并它们
						Object.assign(accumulator[existingIndex], currentValue);
					} else {
						// 否则，将当前对象添加到累加器中
						accumulator.push(currentValue);
					}

					return accumulator;
				}, []);
				this.stagePerformanceData = mergedArray;
			}
		},
		//相关系数矩阵
		async getCorrelationData() {
			this.correlationLoading = true;
			let params = {
				codes: this.codeList,
				flag: this.flag
				// startDate: this.startDate,
				// endDate: this.endDate,
				// marketType: this.marketType
			};
			let req = await getReturnsCorrelationMatrix(params);
			let { data, mtycode, mtymessage } = req || {};
			let list = [];
			if (mtycode == 200) {
				list = data || [];
			} else {
				this.$message.warning(mtymessage);
				list = [];
			}
			this.initCorrelationChart(list);
			this.correlationLoading = false;
		},
		//获取收益走势数据
		async getIncomeData() {
			this.incomeLoading = true;
			let params = {
				codeList: this.codeList,
				startDate: this.startDate,
				endDate: this.endDate,
				marketType: this.marketType
			};
			let req = await getProductTrend(params);
			let { data, mtycode, mtymessage } = req || {};
			if (mtycode == 200) {
				this.incomeData = data;
			} else {
				this.$message.warning(mtymessage);
				this.incomeData = [];
			}
			this.initIncomeChart(this.incomeData);
			this.incomeLoading = false;
		},
		// 所有基金排序
		sortStagePerformanceData(sortVal) {
			this.stagePerformanceData =
				sortVal.order && sortVal.prop ? this.sortChange(sortVal, this.stagePerformanceData.slice()) : this.stagePerformanceData.slice();
		},
		// 排序
		sortChange(sortVal, list) {
			let order = sortVal.order;
			let key = sortVal.prop;
			if (order == 'ascending') {
				let haveValList = list.filter((item) => !isNaN(parseFloat(item[key])));
				let noValList = list.filter((item) => isNaN(parseFloat(item[key])));
				haveValList.sort((a, b) => a[key] - b[key]);
				return [...haveValList, ...noValList];
			} else if (order == 'descending') {
				let haveValList = list.filter((item) => !isNaN(parseFloat(item[key])));
				let noValList = list.filter((item) => isNaN(parseFloat(item[key])));
				haveValList.sort((a, b) => b[key] - a[key]);
				return [...haveValList, ...noValList];
			}
		},
		initIncomeChart(data) {
			this.$refs['income-trend-chart'].getData(data, {});
		},
		initCorrelationChart(data) {
			let headerLst = [];
			if (!data?.length) {
				return;
			}
			if (data?.[0]) {
				let obj = data?.[0];
				for (const key in obj) {
					headerLst.push({ label: key, value: key });
				}
				headerLst.unshift({
					label: '',
					value: 'name'
				});
			}
			this.correlationColumnList = headerLst;
			this.correlationData = data.map((item, index) => {
				return {
					...item,
					name: headerLst[index + 1]?.label
				};
			});
			this.$nextTick(() => {
				this.$refs['correlation-coefficient-chart']?.getData(this.correlationColumnList, this.correlationData);
			});
		},
		handleTypeChange(value) {
			this.formData.cutFlag = value?.selectValue || '';
			this.getPerformanceData();
		},
		//指标选择修改
		handleMeasureChange() {
			this.getPerformanceData();
		}
	}
};
</script>
<style lang="scss" scoped>
.fontRed {
	color: #cf1322;
}
.fontGreen {
	color: #389e0d;
}
.the-asset-analysis-wrapper {
	.data-form-wrapper {
		.data-picker {
			width: 320px;
		}
		.date-btn {
			margin-left: 12px;
			&.active {
				::v-deep span {
					padding-bottom: 4px;
					border-bottom: 2px solid;
				}
			}
		}
	}
	.title-right-form {
		display: flex;
		align-items: center;
	}
}
</style>
