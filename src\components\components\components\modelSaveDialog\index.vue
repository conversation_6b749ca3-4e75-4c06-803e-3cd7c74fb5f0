<template>
	<el-dialog title="模板保存" :visible.sync="dialogFormVisible">
		<el-form :model="templateForm">
			<el-form-item label="模板名称">
				<el-input v-model="templateForm.name" autocomplete="off" placeholder="请输入模板名称"></el-input>
			</el-form-item>
			<el-form-item label="是否公开">
				<el-radio-group v-model="templateForm.ispublic">
					<el-radio :label="true">是</el-radio>
					<el-radio :label="false">否</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="指定人查看">
				<el-cascader
					style="width: 100%"
					placeholder="选择可查看人员"
					:options="allUserList"
					v-model="templateForm.user_list"
					:props="{ multiple: true, emitPath: false }"
					filterable
				></el-cascader>
			</el-form-item>
		</el-form>
		<div slot="footer" class="dialog-footer">
			<el-button @click="dialogFormVisible = false">取 消</el-button>
			<el-button type="primary" @click="submitAddTemplate">确 定</el-button>
		</div>
	</el-dialog>
</template>

<script>
import { getUserList } from '@/api/pages/SystemMixed.js';
export default {
	data() {
		return {
			dialogFormVisible: false,
			templateForm: { ispublic: false },
			allUserList: []
		};
	},
	methods: {
		// 父组件调用
		getData(val) {
			this.templateForm = val ? { ...this.templateForm, ...val } : this.templateForm;
			this.dialogFormVisible = true;
			this.getUserList();
		},
		// 获取用户列表
		async getUserList() {
			let data = await getUserList();
			if (data?.mtycode == 200) {
				this.formatUserList(data?.data);
			}
		},
		// 格式化用户列表
		formatUserList(data) {
			let userList = [];
			data?.map((item) => {
				let index = userList.findIndex((obj) => {
					return obj.value == item.institute_id;
				});
				if (index == -1) {
					userList.push({
						value: item.institute_id,
						label: item.name,
						children: [
							{
								value: item.id,
								label: item.username
							}
						]
					});
				} else {
					let i = userList[index].children.findIndex((obj) => {
						return obj.value == item.id;
					});
					if (i == -1) {
						userList[index].children.push({
							value: item.id,
							label: item.username
						});
					}
				}
			});
			this.allUserList = userList;
		},
		// 提交
		submitAddTemplate() {
			this.$emit('resolveTemplateInfo', this.templateForm);
		}
	}
};
</script>

<style></style>
