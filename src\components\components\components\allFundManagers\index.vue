<template>
	<div class="chart_one">
		<div style="display: flex; justify-content: space-between; align-items: center">
			<div class="title">全部基金经理</div>
			<div>
				<el-select style="margin-right: 16px" v-model="fundType" placeholder="请选择" @change="changeType">
					<el-option v-for="item in fundTypeList" :key="item.type" :label="item.label" :value="item.type"> </el-option>
				</el-select>
				<el-button icon="el-icon-document-delete" @click="exportExcel">导出Excel</el-button>
			</div>
		</div>
		<div style="margin-top: 24px" v-loading="loading">
			<el-table class="fund-analysis-table" :data="allFundManagerData" max-height="400px" @row-click="goDetail" @sort-change="sort_change">
				<el-table-column
					v-for="item in column"
					:key="item.value"
					align="gotoleft"
					:prop="item.value"
					:label="item.label"
					:width="item.width"
					:sortable="item.sortable ? item.sortable : false"
					:show-overflow-tooltip="true"
					:min-width="item.width ? item.width : ''"
					:default-sort="{ prop: 'managed_year', order: 'descending' }"
				>
					<template #header>
						<long-table-popover-chart
							v-if="item.popover"
							:data="allFundManagerData"
							date_key="name_y"
							:data_key="item.value"
							:active_row="row"
							:show_name="item.label"
							:formatter="
								function (val) {
									return val;
								}
							"
						>
							<span>{{ item.label }}</span>
						</long-table-popover-chart>
						<span v-else>{{ item.label }}</span>
					</template>
					<template slot-scope="{ row }">
						<a v-if="item.value == 'name_y'" style="border-bottom: 1px solid #4096FF" @click="godetail(row.code, row.name_y)">{{
							row[item.value]
						}}</a>
						<span
							v-else-if="item.value == 'cum_return'"
							:style="`${row[item.value] > 0 ? 'color:red' : row[item.value] < 0 ? 'color:green' : 'color:black'}`"
							>{{ row[item.value] }}%</span
						>
						<span v-else>
							{{ row[item.value] }}
						</span>
					</template>
				</el-table-column>
				<template slot="empty">
					<el-empty image-size="160"></el-empty>
				</template>
			</el-table>
		</div>
	</div>
</template>

<script>
// 全部基金经理
import { getHoldManagerMsg } from '@/api/pages/SystemOther.js';
import { alphaGo } from '@/assets/js/alpha_type.js';
import { filter_json_to_excel } from '@/utils/exportExcel.js';
// 所属全部基金经理
export default {
	name: 'allFundManagers',
	data() {
		return {
			allFundManagerData: [],
			allFundManagerDataSort: [],
			fundType: '',
			fundTypeList: [
				{ value: 'equity', label: '主动权益' },
				{ value: 'bond', label: '固收+' },
				{ value: 'purebond', label: '纯债' },
				{ value: 'cbond', label: '可转债' },
				{ value: 'bill', label: '中短债' },
				{ value: 'money', label: '货币' }
			],
			allManagerLoading: false,
			sortRule: { prop: null, order: null },
			column: [
				// {
				// 	label: '基金经理代码',
				// 	value: 'manager_code'
				// },
				{
					label: '基金经理名称',
					width: '120px',
					value: 'name_y',
					popover: false
				},
				{
					label: '从业年限',
					width: '120px',
					value: 'managed_year',
					sortable: true,
					popover: true
				},
				{
					label: '基金经理介绍',
					value: 'bio',
					popover: false
				},
				{
					label: '职业期间收益率',
					width: '170px',
					value: 'cum_return',
					sortable: true,
					popover: true
				},
				{
					label: '管理规模(亿元)',
					width: '170px',
					value: 'sum_netasset',
					sortable: true,
					popover: true
				},
				{
					label: '代表基金',
					width: '150px',
					value: 'name_x',
					popover: false
				},
				{
					label: '代表基金标签',
					width: '150px',
					value: 'tags',
					popover: false
				}
			],
			info: {},
			loading: true
		};
	},
	methods: {
		// 获取基金经理
		async getAllFundManagers(type) {
			let data = await getHoldManagerMsg({ code: this.info.code, type });
			if (data?.mtycode == 200) {
				this.getData(data?.data);
			}
		},
		// 获取数据
		getData(data) {
			this.loading = false;
			this.allFundManagerData = data?.map((item) => {
				return {
					...item,
					cum_return: item.cum_return * 1 ? (item.cum_return * 100).toFixed(2) * 1 : '--',
					managed_year: !isNaN(parseFloat(item.managed_year)) ? parseFloat(item.managed_year).toFixed(1) * 1 : item.managed_year * 1,
					sum_netasset: parseFloat(item.sum_netasset) ? parseFloat(item.sum_netasset).toFixed(2) * 1 : item.sum_netasset * 1
				};
			});
			// this.allFundManagerDataSort = res.data.data.slice();
		},
		// 获取类型
		getTypeList(info) {
			this.info = info;
			this.fundTypeList = info.type
				.filter((item) => {
					return (
						item == 'equity' ||
						item == 'equityhk' ||
						item == 'bond' ||
						// item.type == 'cbond' ||
						item == 'purebond' ||
						// item.type == 'bill' ||
						item == 'money'
					);
				})
				.map((item) => {
					let sort = null;
					switch (item) {
						case 'equity':
							sort = 0;
							break;
						case 'equityhk':
							sort = 1;
							break;
						case 'bond':
							sort = 2;
							break;
						case 'cbond':
							sort = 3;
							break;
						case 'purebond':
							sort = 4;
							break;
						case 'bill':
							sort = 5;
							break;
						case 'money':
							sort = 6;
							break;
					}
					return { type: item, label: this.FUNC.textConverter(this.COMMON.fundType_zh_en, item, 'en', 'zh'), sort };
				})
				.sort((a, b) => {
					return a.sort - b.sort;
				});
			if (this.fundTypeList.length > 0) {
				this.fundType = this.fundTypeList[0].type;
			}
			this.resolveFather();
		},
		// 切换类型
		changeType(val) {
			console.log(val);
			this.loading = true;
			this.fundType = val;
			this.resolveFather();
		},
		// 向父组件传递数据
		resolveFather() {
			this.getAllFundManagers(this.fundType);
		},
		sort_change(sortVal) {
			let order = sortVal.order;
			let key = sortVal.prop;
			if (!order) {
				this.allFundManagerDataSort = this.allFundManagerData.slice();
			} else if (order == 'ascending' && key) {
				let haveValList = this.allFundManagerDataSort.filter((item) => !isNaN(parseFloat(item[key])));
				let noValList = this.allFundManagerDataSort.filter((item) => isNaN(parseFloat(item[key])));
				haveValList.sort((a, b) => a[key] - b[key]);
				this.allFundManagerDataSort = [...haveValList, ...noValList];
			} else if (order == 'descending' && key) {
				let haveValList = this.allFundManagerDataSort.filter((item) => !isNaN(parseFloat(item[key])));
				let noValList = this.allFundManagerDataSort.filter((item) => isNaN(parseFloat(item[key])));
				haveValList.sort((a, b) => b[key] - a[key]);
				this.allFundManagerDataSort = [...haveValList, ...noValList];
			}
		},
		// 跳转
		godetail(code, name) {
			//带参进去
			alphaGo(code, name, this.$route.path);
		},
		// 导出Excel
		exportExcel() {
			let list = this.column;
			let data = this.allFundManagerData;
			let name = this.fundTypeList.find((item) => {
				return item.type == this.fundType;
			})?.label;
			filter_json_to_excel(list, data, this.info.name + '-' + name + '全部基金经理');
		}
	}
};
</script>

<style>
.el-tooltip__popper.is-dark {
	width: 400px;
}
</style>
