<!--  -->
<template>
  <div v-loading="loading"
       class="holdstock">
    <div style="display: flex; align-items: center; width: 100%; position: relative">
      <div style="display: flex; align-items: center">
        <div class="TitltCompare">近一年公布重仓</div>
      </div>
    </div>
    <div>
      <div style="width: 100%; text-align: right">
        <div style="display: flex; justify-content: flex-end">
          <el-button @click="outexcel()"
                     icon="el-icon-download"></el-button><el-button :style="nowindex == item ? 'color:white;background:#4096FF' : ''"
                     v-for="(item, index) in yearqtrlist"
                     :key="index"
                     @click="changgeyearqtr(item)">{{ item }}</el-button>
        </div>
      </div>
      <div class="f12"
           style="margin-top: 16px">
        <sTable :data="hold_stock_msg"
                typeFlag="splice1"></sTable>
        <sTable :data="PbTable"
                typeFlag="1"></sTable>
      </div>
    </div>

    <div style="display: flex; align-items: center; width: 100%; position: relativel; margin-top: 24px">
      <div style="display: flex; align-items: center">
        <div class="TitltCompare">换手率及集中度</div>
      </div>
    </div>
    <div style="page-break-inside: avoid">
      <v-chart ref="hlodstcokTurnover"
               v-loading="empty1"
               autoresize
               element-loading-text="暂无数据"
               element-loading-spinner="el-icon-document-delete"
               element-loading-background="rgba(239, 239, 239, 0.5)"
               style="page-break-inside: avoid; width: 100%; height: 400px"
               :options="optionpbroe"></v-chart>
    </div>
    <div style="page-break-inside: avoid; margin-top: 24px">
      <v-chart ref="hlodstcokConcentration"
               v-loading="empty2"
               autoresize
               element-loading-text="暂无数据"
               element-loading-spinner="el-icon-document-delete"
               element-loading-background="rgba(239, 239, 239, 0.5)"
               style="page-break-inside: avoid; width: 100%; height: 400px"
               :options="optionpbroe2"></v-chart>
    </div>
    <!-- <stockdetail ref='stockdetail'></stockdetail> -->
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import {
  ManagerHoldStocks,
  ManagerHoldRecent1y,
  ManagerTurnoverWithCon,
  FundHoldStockRecent1y,
  FundTurnoverWithConcentration,
  FundPBWithPE,
  ManagerPBWithPE
} from '@/api/pages/tools/compare.js';
import stockdetail from '../funddetail/funddetail.vue';
import sTable from '../SelfTable.vue';
import VCharts from 'vue-echarts';
export default {
  props: {
    comparetype: {
      type: String,
      default: 'manager' //fund
    },
    id: {
      type: String,
      default: '30189741,30441407'
    },
    type: {
      type: String,
      default: 'equity'
    },
    name: {
      type: String,
      default: '萧楠,胡昕炜'
    }
  },
  //import引入的组件需要注入到对象中才能使用
  components: {
    'v-chart': VCharts,
    stockdetail,
    sTable
  },
  data () {
    //这里存放数据
    return {
      hold_stock_msg: [],
      options2: [
        {
          value: '采掘',
          label: '采掘'
        },
        {
          value: '化工',
          name: '化工'
        },
        {
          value: '钢铁',
          name: '钢铁'
        },
        {
          value: '有色金属',
          name: '有色金属'
        },
        {
          value: '建筑材料',
          name: '建筑材料'
        },
        {
          value: '建筑装饰',
          name: '建筑装饰'
        },
        {
          value: '电气设备',
          name: '电气设备'
        },
        {
          value: '机械设备',
          name: '机械设备'
        },
        {
          value: '国防军工',
          name: '国防军工'
        },
        {
          value: '汽车',
          name: '汽车'
        },
        {
          value: '家用电器',
          name: '家用电器'
        },
        {
          value: '纺织服装',
          name: '纺织服装'
        },
        {
          value: '轻工制造',
          name: '轻工制造'
        },
        {
          value: '商业贸易',
          name: '商业贸易'
        },
        {
          value: '农林牧渔',
          name: '农林牧渔'
        },
        {
          value: ' 食品饮料',
          name: ' 食品饮料'
        },
        {
          value: '休闲服务',
          name: '休闲服务'
        },
        {
          value: '医药生物',
          name: '医药生物'
        },
        {
          value: '公用事业',
          name: '公用事业'
        },
        {
          value: '交通运输',
          name: '交通运输'
        },
        {
          value: '房地产',
          name: '房地产'
        },
        {
          value: ' 电子',
          name: ' 电子'
        },
        {
          value: '计算机',
          name: '计算机'
        },
        {
          value: '传媒',
          name: '传媒'
        },
        {
          value: '通信',
          name: '通信'
        },
        {
          value: '银行',
          name: '银行'
        },
        {
          value: '非银金融',
          name: '非银金融'
        },
        {
          value: '综合',
          name: '综合'
        }
      ],
      options1: [],
      value1: '',
      value2: '',
      nowindex: '',
      showdetailchoose: false,
      stock_holdcolumns: [],
      stock_holdcolumnsfund: [],
      fund_hold: [],
      stock_hold: [],
      empty1: false,
      empty2: false,
      optionpbroe: {},
      optionpbroe2: {},
      yearqtrlist: [],
      datatable: [],
      ccctype: '',
      stocksamelist: [],
      colorlist: [
        '#c92a2a',
        '#a61e4d',
        '#862e9c',
        '#5f3dc4',
        '#364fc7',
        '#1864ab',
        '#0b7285',
        '#087f5b',
        '#2b8a3e',
        '#5c940d',
        '#e67700',
        '#d9480f',
        '#ffe3e3',
        '#ffdeeb',
        '#f3d9fa',
        '#e5dbff',
        '#edf2ff',
        '#d0ebff',
        '#c5f6fa',
        '#d3f9d8',
        '#e9fac8',
        '#fff3bf',
        '#ffe8cc',
        '#ffa8a8',
        '#faa2c1',
        '#e599f7',
        '#eebefa',
        '#b197fc',
        '#91a7ff',
        '#74c0fc',
        '#66d9e8',
        '#63e6be',
        '#8ce99a',
        '#c0eb75',
        '#ffe066',
        '#ffc078',
        '#fa5252',
        '#f06595',
        '#4096FF',
        '#FFB6C1',
        '#DB7093',
        '#DA70D6',
        '#800080',
        '#9370DB',
        '#6A5ACD',
        '#4169E1',
        '#B0C4DE',
        '#4682B4',
        '#5F9EA0',
        '#8FBC8F',
        '#EEE8AA',
        '#FFD700',
        '#FFA500',
        '#FF6347',
        '#CD5C5C',
        '#B22222',
        '#D3D3D3',
        '#A9A9A9',
        '#FA8072',
        '#929694',
        '#40BFDD',
        '#C2B12F',
        '#ffa94d',
        '#fcc419',
        '#94d82d',
        '#94C5DE',
        '#B7A7D7',
        '#FDDBC7',
        '#F3A483',
        '#D45C4E',
        '#409eff',
        '#f39c12',
        '#ff1744',
        '#d500f9',
        '#2979ff',
        '#00e5ff',
        '#ff5722',
        '#ffea00',
        '#ff3d00',
        '#ff8a80',
        '#ff80ab',
        '#b388ff',
        '#8c9eff',
        '#a7ffeb',
        '#ffff00',
        '#ffab40',
        '#ffebee',
        '#e8eaf6',
        '#e1f5fe',
        '#fffde7',
        '#efebe9'
      ],
      PbPeColumns: [],
      PbPeData: [],
      loading: false,
      PbTable: []
    };
  },
  filters: {
    fix0 (value, multiple = 1, suffix) {
      if (value == '--' || value == null || value == '' || isNaN(value)) {
        return '--';
      } else {
        let str = (value * multiple).toFixed();
        str = suffix ? str + suffix.toString() : str;
        return str;
      }
    },
    fix1 (value, multiple = 1, suffix) {
      if (value == '--' || value == null || value == '' || isNaN(value)) {
        return '--';
      } else {
        let str = (value * multiple).toFixed(1);
        str = suffix ? str + suffix.toString() : str;
        return str;
      }
    },
    fix3xx (value, comparetype) {
      // //console.log(value);
      // //console.log(comparetype);
      if (comparetype == 'manager') {
        if (value == '--' || value == null || value == '') {
          return value;
        } else {
          return (Number(value) * 100).toFixed(2) + '%';
        }
      } else {
        if (value == '--' || value == null || value == '') {
          return value;
        } else {
          return Number(value).toFixed(2) + '%';
        }
      }
    },
    fix3 (value) {
      if (value == '--' || value == null || value == '') {
        return value;
      } else {
        return (value * 100).toFixed(2) + '%';
      }
    },
    fix2 (value) {
      return Number(value).toFixed(2) + '亿';
    }
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    customHeaderRow () {
      return {
        style: {
          'font-size': '12px'
        }
      };
    },
    customRow () {
      return {
        style: {
          'font-size': '12px'
        }
      };
    },
    // 股票详情分析
    gotodetailfund (val) {
      // //console.log(this.datatable)
      let tmanagercode = '';
      let tmanagername = '';
      let code = '';
      for (let i = 0; i < this.datatable.length; i++) {
        for (let j = 0; j < this.datatable[i].length; j++) {
          if (this.datatable[i][j].name == val) {
            code = this.datatable[i][j].stock_code;
            tmanagercode = this.datatable[i][j].code;
            tmanagername = this.datatable[i][j].manager_name;
          }
        }
      }

      this.$refs.stockdetail.showitem(code, val, this.comparetype, tmanagercode, tmanagername);
    },
    //导出excel
    outexcel () {
      const { export_json_to_excel } = require('@/vendor/Export2Excel');
      var list = [];
      list.push(this.dataexplain);
      let tHeader = [];

      tHeader = ['名称', '股票名称', '股票权重', '抱团度', '所持季度'];
      let filterVal = ['manager_name', 'name', 'weight', 'rank', 'yearqtr'];
      // //console.log(this.datatable)
      let temparr = [];
      for (let i = 0; i < this.datatable.length; i++) {
        temparr = temparr.concat(this.datatable[i]);
      }
      // console.log(this.datatable);
      // //console.log(temparr)
      for (let i = 0; i < temparr.length; i++) {
        list[i] = [];
        list[i][0] = this.comparetype == 'manager' ? temparr[i].manager_name : temparr[i].fund_name;
        list[i][1] = temparr[i].name;
        list[i][2] = temparr[i].weight;
        list[i][3] = temparr[i].rank;
        list[i][4] = temparr[i].yearqtr;
      }

      export_json_to_excel(tHeader, list, '比较近一年公布重仓');
    },
    changgeyearqtr (val) {
      this.getPbPeTable(val);
      this.nowindex = val;
      // --处理缺失数据
      if (this.datatable.length < this.$route.query.id.split(',').length) {
        let dateKey = this.yearqtrlist;
        let temp = [];
        let tempall = this.$route.query.id.split(',');
        for (let i = 0; i < this.datatable.length; i++) {
          if (temp.indexOf(this.datatable[i][0].code) < 0) {
            temp.push(this.datatable[i][0].code);
          }
          if (tempall.indexOf(this.datatable[i][0].code) < 0) {
            tempall.push(this.datatable[i][0].code);
          }
        }
        let t = tempall.filter((item) => !temp.includes(item));
        // console.log('xxxxxx');
        for (let k = 0; k < t.length; k++) {
          let arryT = [];
          for (let j = 0; j < this.datatable[0].length; j++) {
            arryT.push({
              code: t[k],
              change_weight: '--',
              excess: '--',
              industry_code: '--',
              old_weight: '--',
              quarter: '--',
              swname: '--',
              weight: '--',
              year: '--',
              yearqtr: dateKey[0],
              manager_name: this.$route.query.name.split(',')[this.$route.query.id.split(',').indexOf(t[k])]
            });
          }
          this.datatable.push(arryT);
        }
        this.datatable.sort((a, b) => {
          if (this.$route.query.id.split(',').indexOf(a[0].code) > this.$route.query.id.split(',').indexOf(b[0].code)) return 1;
          else return -1;
        });
      }
      // end

      this.stock_hold = [];
      let templength = [];
      let tempdataarr = [];

      for (let i = 0; i < this.datatable.length; i++) {
        let item2 = [];
        for (let j = 0; j < this.datatable[i].length; j++) {
          if (this.datatable[i][j].yearqtr == val) {
            item2.push(this.datatable[i][j]);
          }
        }
        tempdataarr.push(item2);
      }
      for (let i = 0; i < tempdataarr.length; i++) {
        templength.push(tempdataarr[i].length);
      }
      let index = templength.indexOf(Math.max(...templength));
      this.stock_hold = [];
      for (let i = 0; i < templength[index]; i++) {
        let item = {};
        for (let j = 0; j < tempdataarr.length; j++) {
          if (tempdataarr[j][i]) {
            if (tempdataarr[j][i].yearqtr == val) {
              item['name' + j] = tempdataarr[j][i]?.name || '--';
              item['weight' + j] = tempdataarr[j][i]?.weight || '--';
              item['rank' + j] = tempdataarr[j][i]?.rank || '--';
            }
          }
        }
        this.stock_hold.push(item);
      }
      this.hold_stock_msg = [['季度']];
      for (let i = 0; i < this.$route.query.id.split(',').length; i++) {
        this.hold_stock_msg[0].push('股票/权重/抱团度');
      }

      for (let i = 0; i < this.stock_hold.length; i++) {
        this.hold_stock_msg.push([]);
        this.hold_stock_msg[i + 1].push(this.nowindex);

        for (let j = 0; j < this.$route.query.id.split(',').length; j++) {
          this.hold_stock_msg[i + 1].push(
            (this.FUNC.isEmpty(this.stock_hold[i]['name' + j]) ? this.stock_hold[i]['name' + j] : '--') +
            '/' +
            (this.FUNC.isEmpty(this.stock_hold[i]['weight' + j])
              ? this.comparetype == 'manager'
                ? (Number(this.stock_hold[i]['weight' + j]) * 100).toFixed(2)
                : Number(this.stock_hold[i]['weight' + j]).toFixed(2)
              : '--') +
            '%/' +
            (this.FUNC.isEmpty(this.stock_hold[i]['rank' + j]) ? Number(this.stock_hold[i]['rank' + j]).toFixed(0) : '--')
          );
        }
      }
      this.stocksamelist = [];
      for (let i = 1; i < this.datatable.length; i++) {
        for (let j = 0; j < this.datatable[i].length; j++) {
          for (let k = 0; k < this.datatable[i - 1].length; k++) {
            if (
              this.datatable[i - 1][k].name == this.datatable[i][j].name &&
              this.stocksamelist.indexOf(this.datatable[i - 1][k].name) < 0 &&
              this.datatable[i - 1][k].yearqtr == this.nowindex &&
              this.datatable[i][j].yearqtr == this.nowindex
            ) {
              this.stocksamelist.push(this.datatable[i - 1][k].name);
            }
          }
        }
      }
      // //console.log(this.stocksamelist)
    },
    async getPbPeTable (yearQtr) {
      let data, multiple;
      if (this.comparetype == 'fund') {
        data = await FundPBWithPE({ fund_code: this.id, fund_name: this.name });
        multiple = 1;
        // data.data.sort((a, b) => {
        // 	if (this.$route.query.name.split(',').indexOf(a.name[1]) > this.$route.query.name.split(',').indexOf(b.name[1])) return 1;
        // 	else return -1;
        // });
      } else {
        data = await ManagerPBWithPE({ manager_code: this.id, manager_name: this.name, type: this.type });
        multiple = 100; // 基金经理的PBPE值需要*100
      }
      this.PbPeColumns = [];
      this.PbPeData[0] = {};
      let yearQtr_arr = data.data.filter((item) => item.name == 'yearqtr' || item.name.includes('yearqtr'))[0].value;
      let yearQtr_index = yearQtr_arr.indexOf(yearQtr);
      if (!yearQtr_index || yearQtr_index < 0) {
        console.error('error: ', `无 ${yearQtr} 的PB PE 数据`);
        return;
      }
      let parentColumnsName = this.comparetype == 'manager' ? 'stock_holdcolumns' : 'stock_holdcolumnsfund';
      let parentTitleList = this[parentColumnsName].map((item) => item.title);
      parentTitleList = this.$route.query.name.split(',');
      if (parentTitleList.length > 0 && data.data.length > 0) {
        for (let i = 0; i < parentTitleList.length; i++) {
          this.PbPeColumns.push({
            title: '',
            children: [
              {
                dataIndex: 'blank',
                key: 'blank',
                title: '',
                width: `${100 / 3 / parentTitleList.length}%`,
                scopedSlots: {
                  customRender: 'blank'
                }
              },
              {
                dataIndex: 'pb' + i,
                key: 'pb' + i,
                title: '加权PB',
                width: `${100 / 3 / parentTitleList.length}%`,
                scopedSlots: {
                  customRender: 'pb' + i
                }
              },
              {
                dataIndex: 'pe' + i,
                key: 'pe' + i,
                title: '加权PE',
                width: `${100 / 3 / parentTitleList.length}%`,
                scopedSlots: {
                  customRender: 'pe' + i
                }
              }
            ]
          });
          let name = parentTitleList[i];
          data.data.forEach((item) => {
            if (item.name[1] == name && item.name[0] == 'pb') {
              this.PbPeData[0]['pb' + i] = item.value[yearQtr_index] * multiple;
            } else if (item.name[1] == name && item.name[0] == 'pe') {
              this.PbPeData[0]['pe' + i] = item.value[yearQtr_index] * multiple;
            }
          });
          this.PbTable = [['加权PB'], ['加权PE']];
          for (let i = 0; i < this.$route.query.id.split(',').length; i++) {
            this.PbTable[0].push(this.FUNC.isEmpty(this.PbPeData[0]['pb' + i]) ? Number(this.PbPeData[0]['pb' + i]).toFixed(2) : '--');
            this.PbTable[1].push(this.FUNC.isEmpty(this.PbPeData[0]['pe' + i]) ? Number(this.PbPeData[0]['pe' + i]).toFixed(2) : '--');
          }
        }
      }
    },
    getdata () {
      Object.assign(this.$data, this.$options.data());
      this.loading = true;
      this.empty1 = false;
      this.empty2 = false;
      if (this.comparetype == 'manager') {
        this.getmanager();
        this.getmanager2();
      } else {
        this.gefunddata();
        this.gefunddata2();
      }
    },
    async getmanager () {
      let data = await ManagerHoldRecent1y({ manager_code: this.id, type: this.type, manager_name: this.name });
      if (data) {
        data.data.sort((a, b) => {
          if (this.$route.query.id.split(',').indexOf(a[0].code) > this.$route.query.id.split(',').indexOf(b[0].code)) return 1;
          else return -1;
        });
        //console.log('chigu1y');
        //console.log('res.data: ', data.data);
        this.yearqtrlist = [];
        if (data.data.length > 0) {
          for (let i = 0; i < data.data[0].length; i++) {
            if (this.yearqtrlist.indexOf(data.data[0][i].yearqtr) < 0) {
              {
                this.yearqtrlist.push(data.data[0][i].yearqtr);
              }
            }
          }
        }
        this.yearqtrlist.sort(this.orderlist);
        this.datatable = data.data;
        this.stock_holdcolumns = [];
        for (let i = 0; i < this.datatable.length; i++) {
          if (this.datatable[i].length > 0) {
            this.stock_holdcolumns.push({
              title: this.datatable[i][0].manager_name,
              children: [
                {
                  dataIndex: 'name' + i,
                  key: 'Name' + i,
                  title: '股票名称',
                  scopedSlots: {
                    customRender: 'Name' + i
                  }
                },
                {
                  dataIndex: 'weight' + i,
                  key: 'Weight' + i,
                  title: '权重',
                  sorter: (a, b) => a['weight' + i] - b['weight' + i],
                  scopedSlots: {
                    customRender: 'Weight' + i
                  }
                },
                {
                  dataIndex: 'rank' + i,
                  key: 'Rank' + i,
                  title: '抱团度',
                  scopedSlots: {
                    customRender: 'Rank' + i
                  },
                  sorter: (a, b) => a['rank' + i] - b['rank' + i]
                }
              ]
            });
          }
        }

        let max = this.yearqtrlist[0];
        for (let i = 0; i < this.yearqtrlist.length - 1; i++) {
          max = max < this.yearqtrlist[i + 1] ? this.yearqtrlist[i + 1] : max;
        }
        this.changgeyearqtr(max);
      }
    },
    // 集中度换手率
    async getmanager2 () {
      let data = await ManagerTurnoverWithCon({ manager_code: this.id, type: this.type, manager_name: this.name });
      this.loading = false;
      if (data) {
        if (JSON.stringify(data.data) == '{}' || JSON.stringify(data.data) == '[]' || JSON.stringify(data.data) == '') {
          this.empty1 = true;
          this.empty2 = true;
        } else {
          data.data.turnover.sort((a, b) => {
            if (this.$route.query.name.split(',').indexOf(a.name) > this.$route.query.name.split(',').indexOf(b.name)) return 1;
            else return -1;
          });
          data.data.con.sort((a, b) => {
            if (this.$route.query.name.split(',').indexOf(a.name) > this.$route.query.name.split(',').indexOf(b.name)) return 1;
            else return -1;
          });
          // //console.log('huanshoul')
          // //console.log(data)
          let serhuanshou = [];
          let huanshoudata = [];
          let serjizhong = [];
          let jizhongdata = [];
          let points = null;
          let nanlist = [];
          for (let i = 0; i < data.data.turnover.length; i++) {
            nanlist.push(0);
          }

          for (let i = 0; i < data.data.turnover.length; i++) {
            if (data.data.turnover[i].name == 'yearqtr') {
              huanshoudata = data.data.turnover[i].value;
            } else if (data.data.turnover[i].name == 'middle_turnover' || data.data.turnover[i].name == 'turnover_middle') {
              serhuanshou.push({
                name: '市场平均换手率',
                type: 'bar',
                symbol: 'none',
                data: data.data.turnover[i].value
              });
            } else {
              serhuanshou.push({
                name: data.data.turnover[i].name,
                type: 'bar',
                symbol: 'none',
                data: data.data.turnover[i].value
              });
              for (let j = 0; j < data.data.turnover[i].value.length; j++) {
                if (data.data.turnover[i].value[j] == 'nan') {
                  nanlist[i] = j;
                }
              }
            }
          }
          points = ((Math.max(...nanlist) + 1) / data.data.turnover[0].value.length) * 100 + 1;
          // //console.log(points)
          for (let i = 0; i < data.data.con.length; i++) {
            if (data.data.con[i].name == 'yearqtr') {
              jizhongdata = data.data.con[i].value;
            } else if (data.data.con[i].name == 'top3_concentration') {
              serjizhong.push({
                name: '行业前三集中度',
                type: 'bar',
                symbol: 'none',
                data: data.data.con[i].value
              });
            } else {
              serjizhong.push({
                name: data.data.con[i].name,
                type: 'bar',
                symbol: 'none',
                data: data.data.con[i].value
              });
            }
          }
          this.optionpbroe = {
            title: {
              // text:'换手率'
            },
            color: [
              '#929694',
              '#4096ff',
              '#4096ff',
              '#7388A9',
              '#6F80DD',
              '#4096FF',
              '#e040fb',
              '#ff3d00',
              '#929694',
              '#f4d1ff',
              '#e91e63',
              '#64dd17'
            ],
            tooltip: {
              trigger: 'axis',
              formatter: function (obj) {
                var value = obj[0].axisValue + `<br />`;
                for (let i = 0; i < obj.length; i++) {
                  value +=
                    `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:` +
                    obj[i].color +
                    `;"></span>` +
                    obj[i].seriesName +
                    ':' +
                    Number(obj[i].data).toFixed(2) +
                    '倍' +
                    `<br />`;
                }
                return value;
              }
            },
            legend: {},
            dataZoom: [
              {
                type: 'slider',
                show: true,
                height: 14,
                bottom: 10,
                borderColor: 'transparent',
                backgroundColor: '#fafafa',
                // 拖拽手柄样式 svg 路径
                handleIcon:
                  'M512 512m-208 0a6.5 6.5 0 1 0 416 0 6.5 6.5 0 1 0-416 0Z M512 192C335.264 192 192 335.264 192 512c0 176.736 143.264 320 320 320s320-143.264 320-320C832 335.264 688.736 192 512 192zM512 800c-159.072 0-288-128.928-288-288 0-159.072 128.928-288 288-288s288 128.928 288 288C800 671.072 671.072 800 512 800z',
                handleColor: '#aab6c6',
                handleSize: 20,
                handleStyle: {
                  borderColor: '#aab6c6',
                  shadowBlur: 4,
                  shadowOffsetX: 1,
                  shadowOffsetY: 1,
                  shadowColor: '#e5e5e5'
                },
                start: points,
                end: 100
              }
            ],
            grid: {
              left: '10px',
              right: '3%',
              bottom: '10%',
              top: '30px',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              boundaryGap: true,
              data: huanshoudata
            },
            yAxis: {
              axisLine: { show: false },
              axisTick: { show: false },
              splitLine: {
                show: true,
                lineStyle: {
                  type: 'dashed'
                }
              },
              min: 0,
              type: 'value',
              axisLabel: {
                formatter: function (obj) {
                  //   //console.log(obj)
                  // var value = obj.value;
                  return obj.toFixed(1);
                }
              }
            },
            series: serhuanshou
          };

          this.optionpbroe2 = {
            title: {
              // text:'集中度'
            },
            color: [
              '#929694',
              '#4096ff',
              '#4096ff',
              '#7388A9',
              '#6F80DD',
              '#4096FF',
              '#e040fb',
              '#ff3d00',
              '#929694',
              '#f4d1ff',
              '#e91e63',
              '#64dd17'
            ],
            tooltip: {
              trigger: 'axis',
              formatter: function (obj) {
                var value = obj[0].axisValue + `<br />`;
                for (let i = 0; i < obj.length; i++) {
                  value +=
                    `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:` +
                    obj[i].color +
                    `;"></span>` +
                    obj[i].seriesName +
                    ':' +
                    (Number(obj[i].data) * 100).toFixed(2) +
                    '%' +
                    `<br />`;
                }
                return value;
              }
            },
            legend: {},
            dataZoom: [
              {
                type: 'slider',
                show: true,
                height: 14,
                bottom: 10,
                borderColor: 'transparent',
                backgroundColor: '#fafafa',
                // 拖拽手柄样式 svg 路径
                handleIcon:
                  'M512 512m-208 0a6.5 6.5 0 1 0 416 0 6.5 6.5 0 1 0-416 0Z M512 192C335.264 192 192 335.264 192 512c0 176.736 143.264 320 320 320s320-143.264 320-320C832 335.264 688.736 192 512 192zM512 800c-159.072 0-288-128.928-288-288 0-159.072 128.928-288 288-288s288 128.928 288 288C800 671.072 671.072 800 512 800z',
                handleColor: '#aab6c6',
                handleSize: 20,
                handleStyle: {
                  borderColor: '#aab6c6',
                  shadowBlur: 4,
                  shadowOffsetX: 1,
                  shadowOffsetY: 1,
                  shadowColor: '#e5e5e5'
                },
                start: points,
                end: 100
              }
            ],
            grid: {
              left: '10px',
              right: '3%',
              bottom: '10%',
              top: '30px',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              boundaryGap: true,
              data: jizhongdata
            },
            yAxis: {
              axisLine: { show: false },
              axisTick: { show: false },
              splitLine: {
                show: true,
                lineStyle: {
                  type: 'dashed'
                }
              },
              min: 0,
              type: 'value',
              axisLabel: {
                formatter: function (obj) {
                  //   //console.log(obj)
                  // var value = obj.value;
                  return (obj * 100).toFixed(0) + '%';
                }
              }
            },
            series: serjizhong
          };
        }
      } else {
        this.empty1 = true;
        this.empty2 = true;
      }
    },
    orderlist (a, b) {
      if (a > b) return 1;
      else return -1;
    },
    async gefunddata () {
      let data = await FundHoldStockRecent1y({ fund_code: this.id, type: this.type, fund_name: this.name });
      if (data) {
        data.data.sort((a, b) => {
          if (this.$route.query.id.split(',').indexOf(a[0].code) > this.$route.query.id.split(',').indexOf(b[0].code)) return 1;
          else return -1;
        });
        this.yearqtrlist = [];
        if (data.data.length > 0) {
          for (let i = 0; i < data.data[0].length; i++) {
            if (this.yearqtrlist.indexOf(data.data[0][i].yearqtr) < 0) {
              {
                this.yearqtrlist.push(data.data[0][i].yearqtr);
              }
            }
          }
        }
        this.yearqtrlist.sort(this.orderlist);
        this.datatable = data.data;
        this.stock_holdcolumnsfund = [];
        for (let i = 0; i < this.datatable.length; i++) {
          if (this.datatable[i].length > 0) {
            this.stock_holdcolumnsfund.push({
              title: this.datatable[i][0].fund_name,
              children: [
                {
                  dataIndex: 'name' + i,
                  key: 'Name' + i,
                  title: '股票名称',
                  scopedSlots: {
                    customRender: 'Name' + i
                  }
                },
                {
                  dataIndex: 'weight' + i,
                  key: 'Weight' + i,
                  title: '权重',
                  sorter: (a, b) => a['weight' + i] - b['weight' + i],
                  scopedSlots: {
                    customRender: 'Weight' + i
                  }
                },
                {
                  dataIndex: 'rank' + i,
                  key: 'Rank' + i,
                  title: '抱团度',
                  scopedSlots: {
                    customRender: 'Rank' + i
                  },
                  sorter: (a, b) => a['rank' + i] - b['rank' + i]
                }
              ]
            });
          }
        }
        let max = this.yearqtrlist[0];
        for (let i = 0; i < this.yearqtrlist.length - 1; i++) {
          max = max < this.yearqtrlist[i + 1] ? this.yearqtrlist[i + 1] : max;
        }
        this.changgeyearqtr(max);
      }
    },
    async gefunddata2 () {
      let data = await FundTurnoverWithConcentration({ fund_code: this.id, type: this.type, fund_name: this.name });
      this.loading = false;
      if (data) {
        if (JSON.stringify(data.data) == '{}' || JSON.stringify(data.data) == '[]' || JSON.stringify(data.data) == '') {
          this.empty1 = true;
          this.empty2 = true;
        } else {
          data.data.turnover.sort((a, b) => {
            if (this.$route.query.name.split(',').indexOf(a.name) > this.$route.query.name.split(',').indexOf(b.name)) return 1;
            else return -1;
          });
          data.data.concentration.sort((a, b) => {
            if (this.$route.query.name.split(',').indexOf(a.name) > this.$route.query.name.split(',').indexOf(b.name)) return 1;
            else return -1;
          });
          // //console.log('huanshoul')
          // //console.log(data)
          let serhuanshou = [];
          let huanshoudata = [];
          let serjizhong = [];
          let jizhongdata = [];
          let nanlist = [];
          let points = null;
          for (let i = 0; i < data.data.turnover.length; i++) {
            nanlist.push(0);
          }

          for (let i = 0; i < data.data.turnover.length; i++) {
            if (data.data.turnover[i].name == 'yearqtr') {
              huanshoudata = data.data.turnover[i].value;
            } else if (data.data.turnover[i].name == 'middle_turnover') {
              serhuanshou.push({
                name: '市场平均换手率',
                type: 'bar',
                symbol: 'none',
                data: data.data.turnover[i].value
              });
            } else {
              serhuanshou.push({
                name: data.data.turnover[i].name,
                type: 'bar',
                symbol: 'none',
                data: data.data.turnover[i].value
              });
              for (let j = 0; j < data.data.turnover[i].value.length; j++) {
                if (data.data.turnover[i].value[j] == 'nan') {
                  nanlist[i] = j;
                }
              }
            }
          }
          points = ((Math.max(...nanlist) + 1) / data.data.turnover[0].value.length) * 100 + 1;
          for (let i = 0; i < data.data.concentration.length; i++) {
            if (data.data.concentration[i].name == 'yearqtr') {
              jizhongdata = data.data.concentration[i].value;
            } else if (data.data.concentration[i].name == 'middle_concentration') {
              serjizhong.push({
                name: '市场集中度中位数',
                type: 'bar',
                symbol: 'none',
                data: data.data.concentration[i].value
              });
            } else {
              serjizhong.push({
                name: data.data.concentration[i].name,
                type: 'bar',
                symbol: 'none',
                data: data.data.concentration[i].value
              });
            }
          }
          this.optionpbroe = {
            title: {
              // text:'换手率'
            },
            color: [
              '#929694',
              '#4096ff',
              '#4096ff',
              '#7388A9',
              '#6F80DD',
              '#4096FF',
              '#e040fb',
              '#ff3d00',
              '#929694',
              '#f4d1ff',
              '#e91e63',
              '#64dd17'
            ],
            tooltip: {
              trigger: 'axis',
              formatter: function (obj) {
                var value = obj[0].axisValue + `<br />`;
                for (let i = 0; i < obj.length; i++) {
                  value +=
                    `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:` +
                    obj[i].color +
                    `;"></span>` +
                    obj[i].seriesName +
                    ':' +
                    Number(obj[i].data).toFixed(2) +
                    '倍' +
                    `<br />`;
                }
                return value;
              }
            },
            legend: {},
            dataZoom: [
              {
                type: 'slider',
                show: true,
                height: 14,
                bottom: 10,
                borderColor: 'transparent',
                backgroundColor: '#fafafa',
                // 拖拽手柄样式 svg 路径
                handleIcon:
                  'M512 512m-208 0a6.5 6.5 0 1 0 416 0 6.5 6.5 0 1 0-416 0Z M512 192C335.264 192 192 335.264 192 512c0 176.736 143.264 320 320 320s320-143.264 320-320C832 335.264 688.736 192 512 192zM512 800c-159.072 0-288-128.928-288-288 0-159.072 128.928-288 288-288s288 128.928 288 288C800 671.072 671.072 800 512 800z',
                handleColor: '#aab6c6',
                handleSize: 20,
                handleStyle: {
                  borderColor: '#aab6c6',
                  shadowBlur: 4,
                  shadowOffsetX: 1,
                  shadowOffsetY: 1,
                  shadowColor: '#e5e5e5'
                },
                start: points,
                end: 100
              }
            ],
            grid: {
              left: '10px',
              right: '3%',
              bottom: '10%',
              top: '30px',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              boundaryGap: true,
              data: huanshoudata
            },
            yAxis: {
              axisLine: { show: false },
              axisTick: { show: false },
              splitLine: {
                show: true,
                lineStyle: {
                  type: 'dashed'
                }
              },
              min: 0,
              type: 'value',
              axisLabel: {
                formatter: function (obj) {
                  //   //console.log(obj)
                  // var value = obj.value;
                  return obj.toFixed(1);
                }
              }
            },
            series: serhuanshou
          };

          this.optionpbroe2 = {
            title: {
              // text:'集中度'
            },
            color: [
              '#929694',
              '#4096ff',
              '#4096ff',
              '#7388A9',
              '#6F80DD',
              '#4096FF',
              '#e040fb',
              '#ff3d00',
              '#929694',
              '#f4d1ff',
              '#e91e63',
              '#64dd17'
            ],
            tooltip: {
              trigger: 'axis',
              formatter: function (obj) {
                var value = obj[0].axisValue + `<br />`;
                for (let i = 0; i < obj.length; i++) {
                  value +=
                    `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:` +
                    obj[i].color +
                    `;"></span>` +
                    obj[i].seriesName +
                    ':' +
                    Number(obj[i].data).toFixed(2) +
                    '%' +
                    `<br />`;
                }
                return value;
              }
            },
            legend: {},
            dataZoom: [
              {
                type: 'slider',
                show: true,
                height: 14,
                bottom: 10,
                borderColor: 'transparent',
                backgroundColor: '#fafafa',
                // 拖拽手柄样式 svg 路径
                handleIcon:
                  'M512 512m-208 0a6.5 6.5 0 1 0 416 0 6.5 6.5 0 1 0-416 0Z M512 192C335.264 192 192 335.264 192 512c0 176.736 143.264 320 320 320s320-143.264 320-320C832 335.264 688.736 192 512 192zM512 800c-159.072 0-288-128.928-288-288 0-159.072 128.928-288 288-288s288 128.928 288 288C800 671.072 671.072 800 512 800z',
                handleColor: '#aab6c6',
                handleSize: 20,
                handleStyle: {
                  borderColor: '#aab6c6',
                  shadowBlur: 4,
                  shadowOffsetX: 1,
                  shadowOffsetY: 1,
                  shadowColor: '#e5e5e5'
                },
                start: points,
                end: 100
              }
            ],
            grid: {
              left: '10px',
              right: '3%',
              bottom: '10%',
              top: '30px',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              boundaryGap: true,
              data: jizhongdata
            },
            yAxis: {
              axisLine: { show: false },
              axisTick: { show: false },
              splitLine: {
                show: true,
                lineStyle: {
                  type: 'dashed'
                }
              },
              min: 0,
              type: 'value',
              axisLabel: {
                formatter: function (obj) {
                  //   //console.log(obj)
                  // var value = obj.value;
                  return obj.toFixed(1) + '%';
                }
              }
            },
            series: serjizhong
          };
        }
        if (JSON.stringify(data.data.concentration) == '[]') {
          this.empty2 = true;
        } else if (JSON.stringify(data.data.turnover) == '[]') {
          this.empty1 = true;
        }
      } else {
        this.empty1 = true;
        this.empty2 = true;
      }
    },
    // 获取季度
    getquarter (val) {
      let temp = null;
      var myDate = new Date();
      let year = myDate.getFullYear();
      let month = myDate.getMonth() + 1;
      let quarter = Math.floor(month % 3 == 0 ? month / 3 : month / 3 + 1);
      if (quarter - val - 1 <= 0) {
        return year - 1 + ' Q' + (4 + quarter - val - 1);
      } else {
        return year + ' Q' + (quarter - val - 1);
      }
    }, //
    createPrintWord () {
      let data = [];
      this.hold_stock_msg.map((obj, index) => {
        var list = [];
        obj.map((item, i) => {
          if (i !== 0) {
            let itemData = item.split('/');
            list.push(...itemData);
          }
        });
        data.push(list);
      });
      let name = this.name.split(',');
      data.unshift([...name]);
      let pb = [];
      this.PbTable.map((item) => {
        pb.push([...item]);
      });
      pb.unshift(['', ...name]);
      let heightTurnover = this.$refs['hlodstcokTurnover']?.$el.clientHeight;
      let widthTurnover = this.$refs['hlodstcokTurnover']?.$el.clientWidth;
      let chartTurnover = this.$refs['hlodstcokTurnover'].getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      });
      let heightConcentration = this.$refs['hlodstcokConcentration']?.$el.clientHeight;
      let widthConcentration = this.$refs['hlodstcokConcentration']?.$el.clientWidth;
      let chartConcentration = this.$refs['hlodstcokConcentration'].getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      });

      return [
        ...this.$exportWord.exportTitle('近一年公布重仓'),
        ...this.$exportWord.exportCompareTable(
          data,
          [
            {
              row: 0,
              cell: 0,
              rowSpan: 3
            },
            {
              row: 0,
              cell: 1,
              rowSpan: 3
            },
            {
              row: 0,
              cell: 2,
              rowSpan: 3
            },
            {
              row: 0,
              cell: 3,
              rowSpan: 3
            }
          ],
          true
        ),
        ...this.$exportWord.exportTitle('PB-PE加权'),
        ...this.$exportWord.exportCompareTable(pb, []),
        ...this.$exportWord.exportTitle('换手率'),
        ...this.$exportWord.exportChart(chartTurnover, { width: widthTurnover, height: heightTurnover }),
        ...this.$exportWord.exportTitle('集中度'),
        ...this.$exportWord.exportChart(chartConcentration, { width: widthConcentration, height: heightConcentration })
      ];
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () { },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
</style>
