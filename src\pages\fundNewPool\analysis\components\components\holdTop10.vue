<template>
  <!-- <div class="big_template"> -->
  <div class=" chart_one holdTop10"
       v-show="show"
       v-loading="loading">
    <div class="flex_between">
      <div class="title">前十大持仓</div>
      <div class="flex_start">
        <!-- <div class="mr-12">
          <el-cascader v-model="industry"
                       :options="industry_list"
                       :props="props"
                       placeholder="请选择具体行业"
                       collapse-tags
                       clearable
                       :show-all-levels="false"
                       @change="changeIndustry"></el-cascader>
        </div> -->
        <div class="mr-12">
          <el-radio-group v-model="range"
                          @change="getCombinationHoldStock">
            <el-radio-button label="全部"></el-radio-button>
            <el-radio-button label="新增"></el-radio-button>
          </el-radio-group>
        </div>
        <div class="mr-12">
          <el-radio-group v-model="type"
                          @change="getCombinationHoldStock">
            <el-radio-button label="权益"></el-radio-button>
            <el-radio-button label="债券"></el-radio-button>
          </el-radio-group>
        </div>
        <div>
          <el-dropdown>
            <span class="el-dropdown-link"
                  style>
              <i class="el-icon-more"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item>
                <div @click="exportExcel">导出Excel</div>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
    </div>
    <div class="mt-16">
      <div v-for="(ik, index) in dataTempTemp"
           style="margin-bottom:16px"
           :key="index">
        {{ ik[0].yearqtr }}
        <!-- <div class="mr-12">
          <el-button type="primary"
                     @click="openDialog(ik[0].yearqtr)">查看</el-button>
        </div> -->
        <el-table :data="ik"
                  style="width: 100%"
                  border>
          <!--  :span-method="objectSpanMethod" -->
          <el-table-column v-for="item in column.filter((itemx) => itemx.label != '季度')"
                           :key="item.label"
                           :prop="item.value"
                           :label="item.label"
                           :sortable="item.label == '简单权重合' || item.label == '加权重合' || item.label == '持有'"
                           show-overflow-tooltip
                           align="gotoleft">
            <el-table-column v-for="obj in item.children"
                             :key="obj.label"
                             :prop="obj.value"
                             :label="obj.label"
                             :sortable="obj.label == '简单权重合' || obj.label == '加权重合' || obj.label == '持有'"
                             show-overflow-tooltip
                             align="gotoleft">
              <template #header>
                <long-table-popover-chart v-if="obj.popover"
                                          :key="index"
                                          :data="formatTableData(ik)"
                                          date_key="code"
                                          :data_key="obj.value"
                                          :show_name="obj.label">
                  <div>{{ obj.label }}</div>
                </long-table-popover-chart>
                <div v-else>{{ obj.label }}</div>
              </template>
              <template slot-scope="{ row }">
                <div>{{ obj.format ? obj.format(row[obj.value]) : row[obj.value] }}</div>
              </template>
              <template slot-scope="{ row }">
                <div v-if="obj.value.includes('hold_fund')"
                     class="flex_start">
                  <span v-if="typeof row[obj.value] == 'object'"
                        class="mr-4">{{ row[obj.value].length }}只</span>
                  <div v-for="v in row[obj.value]"
                       :key="v.code"
                       :style="v.flag == 2 ? `min-width:10px;height:10px;background:${v.color};margin-right:4px` : ''"
                       :tooltip="v.name">
                    <div v-if="v.flag == 1"
                         class="mr-8 flex_start">
                      <svg width="14"
                           height="14"
                           viewBox="0 0 14 14"
                           fill="none"
                           xmlns="http://www.w3.org/2000/svg">
                        <path d="M12.4156 4.82735L8.94433 4.32286L7.39258 1.17696C7.35019 1.09083 7.28047 1.0211 7.19433 0.978716C6.97832 0.872075 6.71582 0.960943 6.60781 1.17696L5.05605 4.32286L1.58476 4.82735C1.48906 4.84102 1.40156 4.88614 1.33457 4.9545C1.25358 5.03774 1.20895 5.14973 1.21049 5.26586C1.21203 5.38199 1.25961 5.49276 1.34277 5.57383L3.8543 8.02247L3.26094 11.4801C3.24702 11.5605 3.25592 11.6432 3.28663 11.7189C3.31733 11.7945 3.36862 11.86 3.43466 11.908C3.50071 11.9559 3.57887 11.9845 3.66029 11.9903C3.74171 11.9961 3.82313 11.9789 3.89531 11.9408L7.00019 10.3084L10.1051 11.9408C10.1898 11.9859 10.2883 12.001 10.3826 11.9846C10.6205 11.9436 10.7805 11.718 10.7395 11.4801L10.1461 8.02247L12.6576 5.57383C12.726 5.50684 12.7711 5.41934 12.7848 5.32364C12.8217 5.08438 12.6549 4.8629 12.4156 4.82735Z"
                              fill="#FFD600" />
                      </svg>
                    </div>
                  </div>
                  <el-popover placement="right"
                              width="500"
                              trigger="click">
                    <el-table height="400px"
                              :data="row[obj.value]">
                      <el-table-column width="100"
                                       property="code"
                                       label="代码"></el-table-column>
                      <el-table-column width="250"
                                       property="name"
                                       align="gotoleft"
                                       label="名称"></el-table-column>
                      <el-table-column width="100"
                                       sortable
                                       property="weight"
                                       align="gotoleft"
                                       label="权重(%)"></el-table-column>
                    </el-table>
                    <span v-if="row[obj.value]"
                          slot="reference"
                          style="cursor: pointer">...</span>
                  </el-popover>
                </div>
                <div v-else
                     style="overflow: hidden; text-overflow: ellipsis">{{ obj.format ? obj.format(row[obj.value]) : row[obj.value] }}</div>
              </template>
            </el-table-column>
            <template #header>
              <long-table-popover-chart v-if="item.popover"
                                        :key="index"
                                        :data="formatTableData(ik)"
                                        :date_key="type=='权益'?'stock_name':'secuabbr'"
                                        :data_key="item.value"
                                        :show_name="item.label">
                <div>{{ item.label }}</div>
              </long-table-popover-chart>
              <div v-else>{{ item.label }}</div>
            </template>
            <template slot-scope="{ row }">
              <div>{{ item.format ? item.format(row[item.value]) : row[item.value] }}</div>
            </template>
            <template slot-scope="{ row }">
              <div v-if="item.value.includes('hold_fund')"
                   class="flex_start">
                <span v-if="typeof row[item.value] == 'object'"
                      class="mr-4">{{ row[item.value].length }}只</span>
                <el-tooltip v-for="v in row[item.value]"
                            :key="v.code"
                            effect="dark"
                            :content="v.name"
                            placement="top">
                  <div :style="v.flag == 2 ? `min-width:10px;height:10px;background:${v.color};margin-right:4px` : ''">
                    <div v-if="v.flag == 1"
                         class="mr-8 flex_start">
                      <svg width="14"
                           height="14"
                           viewBox="0 0 14 14"
                           fill="none"
                           xmlns="http://www.w3.org/2000/svg">
                        <path d="M12.4156 4.82735L8.94433 4.32286L7.39258 1.17696C7.35019 1.09083 7.28047 1.0211 7.19433 0.978716C6.97832 0.872075 6.71582 0.960943 6.60781 1.17696L5.05605 4.32286L1.58476 4.82735C1.48906 4.84102 1.40156 4.88614 1.33457 4.9545C1.25358 5.03774 1.20895 5.14973 1.21049 5.26586C1.21203 5.38199 1.25961 5.49276 1.34277 5.57383L3.8543 8.02247L3.26094 11.4801C3.24702 11.5605 3.25592 11.6432 3.28663 11.7189C3.31733 11.7945 3.36862 11.86 3.43466 11.908C3.50071 11.9559 3.57887 11.9845 3.66029 11.9903C3.74171 11.9961 3.82313 11.9789 3.89531 11.9408L7.00019 10.3084L10.1051 11.9408C10.1898 11.9859 10.2883 12.001 10.3826 11.9846C10.6205 11.9436 10.7805 11.718 10.7395 11.4801L10.1461 8.02247L12.6576 5.57383C12.726 5.50684 12.7711 5.41934 12.7848 5.32364C12.8217 5.08438 12.6549 4.8629 12.4156 4.82735Z"
                              fill="#FFD600" />
                      </svg>
                    </div>
                  </div>
                </el-tooltip>

                <el-popover placement="right"
                            width="500"
                            trigger="click"
                            :key="'popover' + index">
                  <el-table height="400px"
                            :data="row[item.value]">
                    <el-table-column width="100"
                                     property="code"
                                     label="代码"></el-table-column>
                    <el-table-column width="250"
                                     property="name"
                                     align="gotoleft"
                                     label="名称"></el-table-column>
                    <el-table-column width="100"
                                     sortable
                                     property="weight"
                                     align="gotoleft"
                                     label="权重(%)"></el-table-column>
                  </el-table>
                  <span v-if="row[item.value]"
                        slot="reference"
                        style="cursor: pointer">...</span>
                </el-popover>
              </div>
              <div v-else
                   style="overflow: hidden; text-overflow: ellipsis">{{ item.format ? item.format(row[item.value]) : row[item.value] }}</div>
            </template>
          </el-table-column>
        </el-table>
        <div @click="addColumns(dataTempTemp[index][0].yearqtr)"
             v-show="dataTempTemp[index].length < dataTemp[index].length"
             style="
						text-align: center;
						font-size: 16px;
						line-height: 24px;
						cursor: pointer;
						color: #4096FF;
						margin-top: 16px;
						margin-bottom: 16px;
					">查看更多</div>
      </div>
    </div>
    <big-stock-table ref="bigStockTable"
                     :info="info"></big-stock-table>
  </div>
  <!-- </div> -->
</template>

<script>
import { getCombinationHoldStock } from "@/api/pages/tools/pool.js";
import { filter_json_to_excel } from "@/utils/exportExcel.js";
import bigStockTable from "./components/bigStockTable.vue";
export default {
  components: { bigStockTable },
  props: {
    data_type: {
      type: String,
      default: "fund"
    },
    types: {},
  },
  data () {
    return {
      ismanager: false,
      dataTemp: [],
      dataTempTemp: [],
      show: true,
      loading: true,
      range: "全部",
      type: "权益",
      data: [],
      column: [
        {
          label: "季度",
          value: "yearqtr"
        },
        {
          label: "名称",
          value: "stock_name"
        },
        {
          label: "代码",
          value: "stock_code"
        },
        {
          label: "行业",
          value: "swname"
        },
        {
          label: "主题",
          value: "theme"
        },
        {
          label: "简单权重合",
          value: "equal_weight",
          format: this.fixp
        },
        {
          label: "加权重合",
          value: "netasset_weight",
          format: this.fixp_divide_100
        },
        {
          label: "持有",
          value: "hold_fund"
        }
      ],
      yearqtr_info: [],
      info: {},
      sliceIndex: 1,
      cache_data: [],
      industry_list: [],
      props: { multiple: true, checkStrictly: true },
      active_industry: [],
      industry: [],
      cache_industry: []
    };
  },
  methods: {
    async getData (info) {
      this.info = info;
      if (this.types == "equity") {
        this.type = "权益";
      } else {
        this.type = "债券";
      }
      this.getCombinationHoldStock();
    },
    refresInfo (info) {
      this.info = info;
      this.formatData(this.filterData(this.cache_data));
    },
    // 获取持仓数据
    async getCombinationHoldStock () {
      if (this.type == "权益") {
        this.column = [
          {
            label: "季度",
            value: "yearqtr"
          },
          {
            label: "名称",
            value: "stock_name"
          },
          {
            label: "代码",
            value: "code"
          },
          {
            label: "行业",
            value: "swname"
          },
          {
            label: "主题",
            value: "theme"
          },
          {
            label: "简单权重合",
            value: "equal_weight",
            popover: true,
            format: this.fixp
          },
          {
            label: "加权重合",
            value: "netasset_weight",
            popover: true,
            format: this.fixp_divide_100
          },
          {
            label: "持有",
            value: "hold_fund"
          }
        ];
      } else {
        this.column = [
          {
            label: "季度",
            value: "yearqtr"
          },
          {
            label: "名称",
            value: "secuabbr"
          },
          {
            label: "代码",
            value: "stockCode"
          },
          {
            label: "行业",
            value: "firstindustryname"
          },
          {
            label: "券种",
            value: "exchangenature"
          },
          {
            label: "简单权重合",
            value: "equal_weight",
            popover: true,
            format: this.fixp
          },
          {
            label: "加权重合",
            value: "netasset_weight",
            popover: true,
            format: this.fixp_divide_100
          },
          {
            label: "持有",
            value: "hold_fund"
          }
        ];
      }
      this.loading = true;
      let data = await getCombinationHoldStock({
        ids:
          this.data_type == "fund" || this.data_type == "manager"
            ? [{ code: this.info.code, type: "pool" }]
            : [
              ...this.info["children_pool_list"].map(item => {
                return {
                  code: item.code,
                  type: "pool"
                };
              })
            ],
        insert_time: this.info.date,
        yearqtr: this.info.quarter,
        isbond: this.type == "权益" ? 0 : 1,
        isnew: this.range == "全部" ? 0 : 1,
        flag: 5,
        type: this.info.type,
        ismanager: this.ismanager
      });
      let list = [];
      if (data?.mtycode == 200) {
        this.show = true;
        this.cache_data = data?.data;
        list = this.filterData(data?.data);
        if (this.type == "权益") {
          this.filterIndustryList();
        }
      } else {
        this.show = false;
      }
      this.loading = false;
      this.formatData(list);
    },
    filterIndustryList () {
      let data = [];
      this.cache_data.map(v1 => {
        let index1 = data.findIndex(v => v.value == v1.swlevel1code);
        if (index1 == -1) {
          data.push({
            label: v1.swlevel1,
            value: v1.swlevel1code,
            children: [
              {
                label: v1.swlevel2,
                value: v1.swlevel2code,
                children: [
                  {
                    label: v1.swlevel3,
                    value: v1.swlevel3code
                  }
                ]
              }
            ]
          });
        } else {
          let index2 = data[index1].children.findIndex(
            v => v.value == v1.swlevel2code
          );
          if (index2 == -1) {
            data[index1].children.push({
              label: v1.swlevel2,
              value: v1.swlevel2code,
              children: [
                {
                  label: v1.swlevel3,
                  value: v1.swlevel3code
                }
              ]
            });
          } else {
            let index3 = data[index1].children[index2].children.findIndex(
              v => v.value == v1.swlevel3code
            );
            if (index3 == -1) {
              data[index1].children[index2].children.push({
                label: v1.swlevel3,
                value: v1.swlevel3code
              });
            }
          }
        }
      });
      this.industry_list = data;
    },
    filterData (data) {
      let list = [];
      data.map(item => {
        let index = list.findIndex(v => v.yearqtr == item.yearqtr);
        let obj = { ...item };
        delete obj.yearqtr;
        let concept_name = Array.from(new Set(item.concept_name));
        if (index == -1) {
          list.push({
            yearqtr: item.yearqtr,
            children: [
              {
                ...obj,
                stock_code: item.stock_code,
                stock_name: item.name,
                swname:
                  item.swlevel1 + "-" + item.swlevel2 + "-" + item.swlevel3,
                theme:
                  typeof concept_name == "object"
                    ? concept_name.join(",")
                    : "--",
                hold_fund: item.code_list?.map(v => {
                  return {
                    ...this.info["code_list"]?.find(n => n.code == v.code),
                    weight: (v.weight * 1).toFixed(2)
                  };
                })
              }
            ]
          });
        } else {
          list[index].children.push({
            ...obj,
            stock_code: item.stock_code,
            stock_name: item.name,
            swname: item.swlevel1 + "-" + item.swlevel2 + "-" + item.swlevel3,
            theme:
              typeof concept_name == "object" ? concept_name.join(",") : "--",
            hold_fund: item.code_list?.map(v => {
              return {
                ...this.info["code_list"]?.find(n => n.code == v.code),
                weight: (v.weight).toFixed(2),
                code: v.code
              };
            })
          });
        }
      });
      if (this.data_type == "pool") {
        list = this.filterPoolData(list);
      }
      console.log(list);
      return list;
    },
    formatTableData (ik) {
      let data = [];
      ik.map(item => {
        let obj = { ...item };
        for (const key in item) {
          let format = this.column.find(v => {
            return v.value == key;
          })?.format;
          if (format) {
            let val = format(item[key]);
            obj[key] =
              typeof val == "string"
                ? val.includes("%")
                  ? val?.split("%")?.[0] * 1
                  : !isNaN(val)
                    ? val * 1
                    : val
                : val;
          } else {
            let index = this.column.findIndex(v => key.indexOf(v.value) != -1);
            if (index != -1) {
              let formatter = this.column[index].children?.find(
                v => v.value == key
              )?.format;
              if (formatter) {
                let val = formatter(item[key]);
                obj[key] =
                  typeof val == "string"
                    ? val.includes("%")
                      ? val?.split("%")?.[0] * 1
                      : !isNaN(val)
                        ? val * 1
                        : val
                    : val;
              }
            }
          }
        }
        data.push(obj);
      });
      console.log(data);
      return data;
    },
    filterPoolData (data) {
      let arr = [];
      data.map(item => {
        let index = arr.findIndex(v => v.yearqtr == item.yearqtr);
        if (index == -1) {
          let temp_list = [];
          item.children.map(obj => {
            let i = temp_list.findIndex(v => v.label == obj.fund_pool_id);
            if (i == -1) {
              temp_list.push({
                label: obj.fund_pool_id,
                children: [{ ...obj }]
              });
            } else {
              temp_list[i].children.push({ ...obj });
            }
          });
          arr.push({
            yearqtr: item.yearqtr,
            children: temp_list
          });
        }
      });
      let merge_data = [];

      console.log(arr);
      arr.map(item => {
        let arr_data = [];
        item.children.map(obj => {
          let key_data = [];
          obj.children.map(v => {
            let o = { yearqtr: item.yearqtr };
            for (const key in v) {
              o[obj.label + key] = v[key];
            }
            key_data.push(o);
          });
          arr_data.push(key_data);
        });
        merge_data.push({
          label: item.yearqtr,
          value: arr_data,
          max: arr_data.reduce((max, current) => {
            const length = current.length;
            return length > max ? length : max;
          }, 0)
        });
      });
      let list = [];
      merge_data.map(item => {
        let cache_data = [];
        for (let index = 0; index < item.max; index++) {
          let merge_obj = {};
          item.value.map(obj => {
            merge_obj = obj[index]
              ? { ...merge_obj, ...obj[index] }
              : { ...merge_obj };
          });
          cache_data.push(merge_obj);
        }
        let i = list.findIndex(v => v.yearqtr == item.label);
        if (i == -1) {
          list.push({
            yearqtr: item.label,
            children: cache_data.sort((a, b) => {
              return Object.keys(b).length - Object.keys(a).length;
            })
          });
        }
      });

      this.column = this.info["children_pool_list"].map(item => {
        if (this.type == "权益") {
          return {
            label: item.name,
            children: [
              // {
              // 	label: '季度',
              // 	value: item.code + 'yearqtr'
              // },
              {
                label: "名称",
                value: item.code + "stock_name"
              },
              // {
              // 	label: '代码',
              // 	value: item.code + 'stock_code'
              // },
              {
                label: "行业",
                value: item.code + "swname"
              },
              // {
              // 	label: '主题',
              // 	value: item.code + 'theme'
              // },
              {
                label: "简单权重合",
                value: item.code + "equal_weight",
                format: this.fixp
              },
              {
                label: "加权重合",
                value: item.code + "netasset_weight",
                format: this.fixp_divide_100
              },
              {
                label: "持有",
                value: item.code + "hold_fund"
              }
            ]
          };
        } else {
          return {
            label: item.name,
            children: [
              {
                label: "名称",
                value: item.code + "secuabbr"
              },
              // {
              // 	label: '代码',
              // 	value: item.code + 'stock_code_x'
              // },
              // {
              // 	label: '行业',
              // 	value: item.code + 'firstindustryname'
              // },
              {
                label: "券种",
                value: item.code + "exchangenature"
              },
              {
                label: "简单权重合",
                value: item.code + "equal_weight",
                popover: true,
                format: this.fixp
              },
              {
                label: "加权重合",
                value: item.code + "netasset_weight",
                popover: true,
                format: this.fixp_divide_100
              },
              {
                label: "持有",
                value: item.code + "hold_fund"
              }
            ]
          };
        }
      });
      return list;
    },
    formatData (data) {
      this.sliceIndex = [];
      this.dataTemp = [];
      this.formatSpan(data);
      let arr = [];
      data.map((item, indexs) => {
        this.dataTemp.push([]);
        this.sliceIndex.push({ key: item.yearqtr, value: 1 });
        let list = [];
        let temp = item.children;
        if (this.data_type == "fund") {
          temp = item.children.sort((a, b) => {
            if (a?.hold_fund?.length < b?.hold_fund?.length) return 1;
            else {
              return -1;
            }
          });
        }
        temp.map((obj, index) => {
          if (index == 0) {
            list.push({
              ...obj,
              yearqtr: item.yearqtr
            });
            this.dataTemp[indexs].push({
              ...obj,
              yearqtr: item.yearqtr
            });
          } else {
            list.push({
              ...obj
            });
            this.dataTemp[indexs].push({
              ...obj
            });
          }
        });
        arr.push(...list);
      });
      // this.dataTemp.map(items => {
      //   return items.sort((a, b) => {
      //     if (a.hold_fund.length < b.hold_fund.length) return 1
      //     else {
      //       return -1
      //     }
      //   });
      // })
      this.data = arr;
      this.dataTempTemp = JSON.parse(JSON.stringify(this.dataTemp));
      this.resetData(
        this.sliceIndex,
        data.map(item => {
          return item.yearqtr;
        })
      );
    },
    resetData (index, yearqtr) {
      // console.log(this.dataTempTemp, this.dataTemp);
      for (let i = 0; i < this.dataTempTemp.length; i++) {
        if (
          yearqtr.findIndex(item => item == this.dataTempTemp[i][0].yearqtr) >=
          0
        ) {
          this.$set(
            this.dataTempTemp,
            i,
            this.dataTemp[i].slice(
              0,
              index[
                index.findIndex(
                  item => item.key == this.dataTempTemp[i][0].yearqtr
                )
              ].value * 10
            )
          );
          // this.dataTempTemp[i] = this.dataTemp[i].slice(0, index * 10)
        }
      }
      this.column = [...this.column];
    },
    addColumns (yearqtr) {
      this.sliceIndex[
        this.sliceIndex.findIndex(item => item.key == yearqtr)
      ].value += 1;
      this.resetData(this.sliceIndex, [yearqtr]);
    },
    handleBeforeSelect (node, selectedNodes) {
      if (node.children && node.children.length > 0) {
        // 如果选择的是父节点，则禁止选择该父节点的子节点
        for (const child of node.children) {
          const index = selectedNodes.findIndex(selectedNode => {
            return selectedNode.value === child.value;
          });
          if (index !== -1) {
            return false; // 返回 false 禁止选择
          }
        }
      } else if (node.parent) {
        // 如果选择的是子节点，则禁止选择该子节点的父节点
        const parent = node.parent;
        const index = selectedNodes.findIndex(selectedNode => {
          return selectedNode.value === parent.value;
        });
        if (index !== -1) {
          return false; // 返回 false 禁止选择
        }
      }

      return true; // 返回 true 允许选择
    },
    // 判断行业选择是否规范
    isSpecification (array) {
      if (array.length <= 1) {
        return true;
      }
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        let next_array = array.slice(index + 1, array.length);
        let index = next_array.findIndex(v => v[0] == element[0]);
        if (index != -1) {
          if (next_array[index].length != element.length) {
            return false;
          }
        }
      }
      return true;
    },
    // 监听行业选择
    changeIndustry (val) {
      let industry_list = [];
      let active_industry = [];
      if (!this.isSpecification(val)) {
        this.$message.warning("行业选择错误");
        this.industry = [...this.cache_industry];
        return;
      }
      this.cache_industry = [...val];
      this.industry_list.map(item => {
        industry_list.push({ label: item.label, value: item.value, level: 1 });
        if (item.children?.length != 0) {
          item.children.map(obj => {
            industry_list.push({
              label: obj.label,
              value: obj.value,
              level: 2,
              parant_id: item.value
            });
            if (obj.children?.length != 0) {
              obj.children.map(tem => {
                industry_list.push({
                  label: tem.label,
                  value: tem.value,
                  level: 3,
                  parant_id: obj.value
                });
              });
            }
          });
        }
      });
      val.map(item => {
        item.map((tmp, index) => {
          if (index == 0) {
            let i = active_industry.findIndex(v => v.value == tmp);
            if (i == -1) {
              active_industry.push({
                ...industry_list.find(v => v.value == tmp),
                children: []
              });
            }
          } else if (index == 1) {
            let i1 = active_industry.findIndex(v => v.value == item[0]);
            if (i1 != -1) {
              active_industry[i1].children.push({
                ...industry_list.find(v => v.value == tmp),
                children: []
              });
            }
          } else if (index == 2) {
            let i1 = active_industry.findIndex(v => v.value == item[0]);
            if (i1 != -1) {
              let i2 = active_industry[i1].children.findIndex(
                v => v.value == item[1]
              );
              if (i2 != -1) {
                active_industry[i1].children[i2].children.push({
                  ...industry_list.find(v => v.value == tmp)
                });
              }
            }
          }
        });
      });

      this.active_industry = active_industry;
    },
    openDialog (yearqtr) {
      let data = this.cache_data.filter(v => v.yearqtr == yearqtr);
      this.$refs["bigStockTable"]?.getData(this.active_industry, data);
    },

    // 记录当前页每个季度的数量
    formatSpan (data) {
      this.yearqtr_info = [];
      data.map(item => {
        let index = this.yearqtr_info.findIndex(obj => {
          return obj.label == item.yearqtr;
        });
        if (index == -1) {
          this.yearqtr_info.push({
            label: item.yearqtr,
            num: item.children.length
          });
        }
      });
    },
    // 计算合并行
    objectSpanMethod ({ row, column, rowIndex, columnIndex }) {
      let index = this.yearqtr_info.findIndex(item => {
        return item.label == row?.yearqtr;
      });
      // 第一列
      if (columnIndex === 0) {
        // 如果存在yearqtr字段,合并对应数据行
        if (index != -1) {
          return {
            rowspan: this.yearqtr_info[index].num,
            colspan: 1
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }
    },
    fixp (val) {
      return val * 1 && !isNaN(val) ? (val * 1).toFixed(2) + "%" : "--";
    },
    fixp_divide_100 (val) {
      return val * 1 && !isNaN(val) ? (val * 1).toFixed(2) + "%" : "--";
    },
    // 导出Excel
    exportExcel () {
      let list = [];
      if (this.data_type == "fund") {
        list = this.column.map(item => {
          return {
            label: item.label,
            value: item.value
          };
        });
      } else {
        list = [];
        this.column.map(item => {
          if (item.children) {
            item.children.map(obj => {
              list.push({
                label: item.label + obj.label,
                ...obj
              });
            });
          }
        });
      }

      let data = [];
      this.dataTempTemp.map(item => {
        data.push(
          ...item.map(item => {
            let obj = { ...item };
            for (const key in item) {
              if (this.data_type == "fund") {
                let index = this.column.findIndex(v => v.value == key);
                if (index != -1) {
                  let format = this.column[index]?.format;
                  if (format) {
                    obj[key] = format(item[key]);
                  }
                }
              } else {
                let index = list.findIndex(v => v.value == key);
                if (index != -1) {
                  let format = list[index]?.format;
                  if (format) {
                    obj[key] = format(item[key]);
                  }
                }
              }
            }
            if (this.data_type == "fund") {
              return {
                ...obj,
                hold_fund: obj.hold_fund?.map(
                  v =>
                    this.info.code_list.find(o => v.code == o.code)?.name +
                    `(${v.weight})`
                )
              };
            } else {
              for (const key in obj) {
                if (key.includes("hold_fund")) {
                  obj[key] = obj[key]?.map(
                    v =>
                      (this.info.code_list.find(o => v.code == o.code)?.name ||
                        v.code) + `(${v.weight})`
                  );
                }
              }
              return obj;
            }
          })
        );
      });
      filter_json_to_excel(
        list.map(v => {
          return { label: v.label, value: v.value };
        }),
        data,
        this.range + "前十大持仓" + this.type
      );
    }
  },
  mounted () {
    this.ismanager = String(this.$route.query.ismanager) == 'true' ? true : false
  },
};
</script>

<style lang="scss" scoped>
.holdTop10 {
	::v-deep .el-radio-button__orig-radio:checked + .el-radio-button__inner {
		color: #4096ff;
		background-color: transparent;
	}
}
</style>
