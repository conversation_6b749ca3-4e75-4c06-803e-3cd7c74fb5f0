<template>
	<div>
		<analysis-card-title title="基金利润分析" @downloadExcel="exportExcel">
			<el-button @click="flage = !flage" icon="el-icon-refresh"></el-button>
		</analysis-card-title>
		<div v-loading="showzhuban">
			<div v-show="flage">
				<el-table
					v-loading="loading"
					:data="profitanalysis"
					stripe
					height="400"
					ref="multipleTable"
					header-cell-class-name="table-header"
					:cell-style="cellStyle"
				>
					<el-table-column
						v-for="item in column"
						:key="item.value"
						:prop="item.value"
						:align="item.align ? item.align : 'gotoleft'"
						:label="item.label"
						border
						stripe
						sortable
					>
						<template #header>
							<long-table-popover-chart
								v-if="item.popover"
								:data="profitanalysis"
								date_key="终止日"
								:data_key="item.value"
								:show_name="item.label"
							>
								<span>{{ item.label }}</span>
							</long-table-popover-chart>
							<span v-else>{{ item.label }}</span>
						</template>
						<template slot-scope="{ row }">
							<span>
								{{ item.format ? item.format(row[item.value]) : row[item.value] }}
							</span>
						</template>
					</el-table-column>
					<template slot="empty">
						<el-empty image-size="160"></el-empty>
					</template>
				</el-table>
			</div>
			<div v-show="!flage">
				<bar-chart-component ref="barChartComponent" />
			</div>
		</div>
	</div>
</template>

<script>
import { exportTitle, exportChart } from '@/utils/exportWord.js';
import barChartComponent from '@/components/components/fundComponents/chartComponent/barChart.vue';
import { filter_json_to_excel } from '@/utils/exportExcel.js';

// 基金收益来源分析
import { getProfitAnalysis } from '@/api/pages/Analysis.js';
// 基金利润分析
export default {
	name: 'fundProfitAnalysis',
	components: { barChartComponent },
	data() {
		return {
			showzhuban: true,
			flage: false,
			analysismsg: '',
			profitanalysis: [],
			touzifenxioption2: {},
			info: {},
			column: [
				{ label: '起始日', value: '起始日' },
				{ label: '终止日', value: '终止日' },
				{ label: '利息收益', value: '利息收益', format: this.fix2p, fill: 'red_or_green', popover: true },
				{ label: '投资收益', value: '投资收益', format: this.fix2p, fill: 'red_or_green', popover: true },
				{ label: '打新收益', value: '打新收益', format: this.fix2p, fill: 'red_or_green', popover: true },
				{ label: '公允变动收益', value: '公允变动收益', format: this.fix2p, fill: 'red_or_green', popover: true }
			]
		};
	},
	methods: {
		// 基金收益来源分析
		async getProfitAnalysis() {
			this.showzhuban = true;
			let data = await getProfitAnalysis({
				code: this.info.code,
				flag: this.info.flag,
				start_date: this.info.start_date,
				end_date: this.info.end_date,
				template: 'fundProfitAnalysis'
			});
			this.showzhuban = false;
			if (data?.mtycode == 200) {
				this.picdata = data?.data;
				this.profitanalysis = data?.data.map((item, index) => {
					return {
						起始日: item.startdate,
						终止日: item?.enddate,
						利息收益: item?.interestreturn,
						投资收益: item?.investreturn,
						打新收益: item?.ipoinvestincome,
						公允变动收益: item?.fairvaluereturn
					};
				}); //基金利润分析
				this.drawtouzi();
			}
		},
		// 获取数据
		getData(info) {
			this.info = info;
			this.getProfitAnalysis();
		},
		// 画图
		drawtouzi() {
			if (this.picdata.length) {
				this.visible_touzi = true;
				let data = {};
				data.series = [
					{
						name: '利息收益',
						type: 'bar',
						stack: '总量',
						barWidth: '100%',
						data: this.picdata.map((v) => v.interestreturn)
					},
					{
						name: '投资收益',
						type: 'bar',
						stack: '总量',
						barWidth: '100%',
						data: this.picdata.map((v) => v.investreturn)
					},
					{
						name: '打新收益',
						type: 'bar',
						stack: '总量',
						barWidth: '100%',
						data: this.picdata.map((v) => v.ipoinvestincome)
					},
					{
						name: '公允变动收益',
						type: 'bar',
						stack: '总量',
						barWidth: '100%',
						data: this.picdata.map((v) => v.fairvaluereturn)
					}
				];
				data.legend = ['利息收益', '投资收益', '打新收益', '公允变动收益'];
				data.xAxis = this.picdata.map((v) => v.enddate);
				this.$refs['barChartComponent'].getData(data);
			} else {
				this.visible_touzi = false;
			}
		},
		fix2p(val) {
			return val * 1 && !isNaN(val) ? (val * 100).toFixed(2) + '%' : '--';
		},
		// 行样式
		cellStyle({ row, column, rowIndex, columnIndex }) {
			let obj = this.column.find((v) => v.value == column.property);
			if (obj?.fill == 'red_or_green') {
				if (row[obj.value] > 0) {
					return {
						color: '#CF1322'
					};
				} else if (row[obj.value] < 0) {
					return {
						color: '#389E0D'
					};
				}
			}
		},
		// 导出为Excel
		exportExcel() {
			let list = [
				{
					label: '起始日',
					value: '起始日'
				},
				{
					label: '终止日',
					value: '终止日'
				},
				{
					label: '利息收益',
					value: '利息收益',
					format: 'fix2p'
				},
				{
					label: '投资收益',
					value: '投资收益',
					format: 'fix2p'
				},
				{
					label: '打新收益',
					value: '打新收益',
					format: 'fix2p'
				},
				{
					label: '公允变动收益',
					value: '公允变动收益',
					format: 'fix2p'
				}
			];
			filter_json_to_excel(list, this.profitanalysis, '基金利润分析');
		},
		createPrintWord() {
			let height = this.$refs['barChartComponent'].$el.clientHeight;
			let width = this.$refs['barChartComponent'].$el.clientWidth;
			let chart = this.$refs['barChartComponent'].createPrintWord();
			return [...exportTitle('基金利润分析'), ...exportChart(chart, { width, height })];
		}
	}
};
</script>

<style scope></style>
