<template>
	<div>
		<el-dialog title="添加" :visible.sync="dialogVisible">
			<div>
				<div class="flex_start mb-24">
					<div class="mr-8"><span style="color: red">*</span>一级分类</div>
					<el-input v-model="formInline.name" style="width: 200px" placeholder="请输入一级分类"></el-input>
				</div>
				<div class="flex_between mb-16">
					<div><span style="color: red">*</span>二级分类</div>
					<el-link @click="addSecond">+添加二级分类</el-link>
				</div>
				<el-table :data="formInline.children" style="width: 100%" max-height="500px">
					<el-table-column prop="name" label="标签名称">
						<template slot-scope="{ row }">
							<el-input v-model="row.name" placeholder="请输入标签名称"></el-input>
						</template>
					</el-table-column>
					<el-table-column prop="setting" label="操作" width="50px">
						<template slot-scope="scope">
							<el-link @click="deleteItem(scope.$index)">删除</el-link>
						</template>
					</el-table-column>
				</el-table>
			</div>
			<div slot="footer">
				<el-button @click="dialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="submit">确 定</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
export default {
	data() {
		return {
			dialogVisible: false,
			formInline: { name: '', children: [] },
			id: ''
		};
	},
	methods: {
		getData(data) {
			if (data) {
				this.id = data?.id;
				this.formInline.name = data?.first_type;
				this.formInline.children = data?.second_type;
			} else {
				this.id = '';
				this.formInline = { name: '', children: [] };
			}
			this.dialogVisible = true;
		},
		addSecond() {
			this.formInline.children.unshift({ name: '' });
		},
		deleteItem(index) {
			this.formInline.children.splice(index, 1);
		},
		submit() {
			this.dialogVisible = false;
			this.$emit('resolveFather', this.id ? { ...this.formInline, id: this.id } : this.formInline);
		}
	}
};
</script>

<style></style>
