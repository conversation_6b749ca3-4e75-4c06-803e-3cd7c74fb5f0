<!--  -->
<template>
	<div class="homebodyfontsize affixstyle">
		<div style="float: right; display: block; clear: both; background: rgba(255, 255, 255, 0.3)">
			<!-- <div style="display: flex; flex-wrap: wrap">
				<el-button class="buttonmargin" @click="showtable">查看临时池</el-button>
				<el-button class="buttonmargin" @click="gotocpmpare(0)">比较</el-button>
				<el-button class="buttonmargin">组合</el-button> -->
			<!-- </div> -->
			<el-button
				type="primary"
				class="affixbotton"
				@click="showtable"
				v-on:mouseover="visibleshow = true"
				icon="el-icon-shopping-bag-1"
				slot="reference"
			></el-button>
		</div>
		<el-dialog title="临时池" width="60vw;" :visible.sync="visibleshow" :modal-append-to-body="false">
			<div>
				<div style="display: flex; flex-wrap: wrap; margin-bottom: 10px">
					<el-button style="margin-left: 0" class="buttonmargin" @click="addpool">入池</el-button>
					<el-button class="buttonmargin" @click="printconsole">另存为</el-button>
					<el-button class="buttonmargin" @click="gotocpmpare(1)">比较分析</el-button>
					<el-button class="buttonmargin">组合分析</el-button>
					<el-button class="buttonmargin" @click="addfundvis2 = true">添加基金入篮</el-button>
				</div>
				<el-table height="50vh" :data="ana" @selection-change="handleSelectionChange">
					<el-table-column type="selection" align="gotoleft" :width="returnwidth(55)"> </el-table-column>
					<el-table-column prop="fund_code" align="gotoleft" label="代码"> </el-table-column>
					<el-table-column prop="name" align="gotoleft" :width="returnwidth(220)" label="名称"> </el-table-column>
					<el-table-column prop="manager_name" align="gotoleft" label="基金经理"> </el-table-column>
					<el-table-column prop="scale" align="gotoleft" label="规模">
						<template slot-scope="scope">{{ (scope.row.scale / 100000000).toFixed(2) }}亿</template>
					</el-table-column>
					<el-table-column prop="type1" align="gotoleft" label="类型"> </el-table-column>
					<el-table-column prop="new_hold" align="gotoleft" :show-overflow-tooltip="true" label="最新持仓"> </el-table-column>
					<el-table-column align="center">
						<template slot-scope="scope">
							<el-popover :ref="`popover-${scope.$index}`" placement="top" width="160">
								<p>是否确定删除</p>
								<div style="text-align: right; margin: 0">
									<el-button type="primary" size="mini" @click="delebasket(scope)">删除</el-button>
								</div>
								<el-button class="buttonclass" type="text" icon="el-icon-delete" slot="reference"></el-button>
							</el-popover>
						</template>
						<!-- <template slot-scope="scope"><div @click="delebasket(scope.row.id,scope.row.name)"><i  class="el-icon-delete-solid icon_color"></i></div></template>   -->
					</el-table-column>
				</el-table>
			</div>
		</el-dialog>
		<el-dialog title="选择添加的基金池" :visible.sync="addfundvis" width="20%" :modal-append-to-body="false">
			<div v-loading="loadingpool">
				<div class="jijinchizisize">基金池：</div>
				<el-select style="width: 100%" v-model="choosepoolvalue" placeholder="请选择您的基金池">
					<el-option v-for="item in optionsbase" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>
				<!-- <br/>理由:<br/><el-input type="textarea" label="选择的理由"></el-input> -->
			</div>
			<span slot="footer" class="dialog-footer">
				<el-button type="primary" @click="saveEdit()">提 交</el-button>
			</span>
		</el-dialog>

		<el-dialog title="选择添加的基金" :visible.sync="addfundvis2" width="20%" :modal-append-to-body="false">
			<el-select
				class="selsss"
				v-model="values"
				:remote-method="searchpeople"
				filterable
				remote
				:loading="loading"
				placeholder="输入信息查询基金"
			>
				<el-option
					v-for="item in havefundmanager"
					:key="item.code"
					:label="item.code + ' ' + item.name"
					:value="item.code + '|' + item.name"
				></el-option>
			</el-select>
			<!-- <br/>理由:<br/><el-input type="textarea" label="选择的理由"></el-input> -->

			<span slot="footer" class="dialog-footer">
				<el-button type="primary" @click="saveEditfund()">提 交</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
import axios from '@/api/index.js';
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { fontSize } from '@/assets/js/echartsrpxtorem'; //注意路径
import { TypeMsg } from '@/api/pages/SystemAlpha.js';
export default {
	//import引入的组件需要注入到对象中才能使用
	components: {},
	data() {
		//这里存放数据
		return {
			showtrue: false,
			delflagss: false,
			tabledatass: [],
			optionsbase: [],
			visible: false,
			visibleshow: false,
			ana: null,
			addfundvis: false,
			loadingpool: true,
			choosebasket: [],
			choosepoolvalue: null,
			codearray: null,
			addfundvis2: false,
			values: '',
			loading: true,
			havefundmanager: []
		};
	},
	//监听属性 类似于data概念
	computed: {},
	//监控data中的数据变化
	watch: {},
	//方法集合
	methods: {
		saveEditfund() {
			let temp1 = this.values.split('|')[0];
			let temp2 = this.values.split('|')[1];
			let id = temp1;
			let fname = temp2;
			// this.addfundvis = true
			// this.choosefundid = id
			// this.choosefundname=name
			let that = this;
			if (id.length > 6 || id.length <= 5) {
				that.$message('此处是基金篮子，不要添加基金经理');
			} else {
				axios
					.post(that.$baseUrl + '/pool/basket_fund/', { fund_code: id })
					.then((res) => {
						that.$message('新增成功' + '  ' + id + ' ' + fname);
						that.showtable();
						that.addfundvis2 = false;
					})
					.catch((err) => {
						that.addfundvis2 = false;
						//  that.$message('失败')
						////console.log(err)
						//that.$message('数据缺失')
					});
			}
		},
		searchpeople(query) {
			////console.log(query)
			////console.log(this.values)
			let that = this;
			axios
				.get(this.$baseUrl + '/header_search_all/?message=' + query)
				.then((res) => {
					if (res.status == 200) {
						that.havefundmanager = res.data.filter((item) => item.flag == 'fund');
					}
					that.loading = false;
				})
				.catch((error) => {
					//that.$message('数据缺失');
				});
		},
		printconsole() {
			const { export_json_to_excel } = require('@/vendor/Export2Excel');
			var list = [];
			let tHeader = [];
			let filterVal = [];

			tHeader = ['代码', '名称', '基金经理', '规模', '类型', '最新持仓'];
			filterVal = ['fund_code', 'name', 'manager_name', 'scale', 'type1', 'new_hold'];
			// ////console.log(this.colums)
			for (let i = 0; i < this.ana.length; i++) {
				list[i] = [];
				list[i][0] = this.ana[i].fund_code;
				list[i][1] = this.ana[i].name;
				list[i][2] = this.ana[i].manager_name;
				list[i][3] = (Number(this.ana[i]['scale']) / 100000000).toFixed(2) + '亿';
				list[i][4] = this.ana[i]['type1'];
				list[i][5] = this.ana[i]['new_hold'];
			}

			export_json_to_excel(tHeader, list, '基金篮子');
		},
		showtable() {
			this.visibleshow = true;
			let that = this;
			axios
				.get(that.$baseUrl + '/pool/basket_fund/')
				.then((res) => {
					////console.log('datess')
					////console.log(res.data)
					that.ana = res.data;
				})
				.catch((err) => {
					//  that.$message('失败')
					////console.log(err)
					//that.$message('数据缺失')
				});
		},
		delebasket(scope) {
			let that = this;
			axios
				.delete(that.$baseUrl + '/pool/basket_fund/?id=' + scope.row.id)
				.then((res) => {
					scope._self.$refs[`popover-${scope.$index}`].doClose();
					// that.showtrue=false
					that.showtable();
				})
				.catch((err) => {
					//  that.$message('失败')
					////console.log(err)
					//that.$message('数据缺失')
				});
		},
		addpool() {
			//添加入池
			if (this.choosebasket.length == 0) {
				this.$message('请选择入池基金');
			} else {
				this.addfundvis = true;
				this.loadingpool = true;
				let tempstring = '';
				for (let k = 0; k < this.choosebasket.length; k++) {
					tempstring += this.choosebasket[k].fund_code + '     ';
				}
				this.codearray = tempstring;
				let that = this;
				axios
					.get(that.$baseUrl + '/pool/fund_pool/')
					.then((res) => {
						////console.log('pool')
						let haveoption = [];
						for (let i = 0; i < res.data.length; i++) {
							haveoption.push({ value: res.data[i].id, label: res.data[i].name });
						}
						that.optionsbase = haveoption;
						if (that.optionsbase.length > 0) {
							that.choosepoolvalue = that.optionsbase[0].value;
						}
						this.loadingpool = false;
						////console.log( that.optionsbase)
					})
					.catch((err) => {
						////console.log(err)
						this.loadingpool = false;
						//that.$message('数据缺失')
					});
			}
		},
		handleSelectionChange(val) {
			//获取选中状态
			// ////console.log(val)
			this.choosebasket = val;
		},
		// gettable() {
		//         //获取篮子数据
		// },
		returnwidth(val) {
			return fontSize(val);
		},
		saveEdit() {
			let temparr = [];
			for (let i = 0; i < this.choosebasket.length; i++) {
				temparr.push({ fund_pool_id: this.choosepoolvalue, fund_code: this.choosebasket[i].fund_code });
			}
			// ////console.log(temparr)
			let that = this;
			axios
				.post(that.$baseUrl + '/pool/pool_funds/', { temparr })
				.then((res) => {
					that.$message('新增成功');
					that.addfundvis = false;
				})
				.catch((err) => {
					////console.log(err)
					//that.$message('数据缺失')
				});
		},
		async gotocpmpare(val) {
			//前往对比
			let that = this;
			if (val == 0) {
				// axios
				// 	.get(that.$baseUrl + '/pool/basket_fund/')
				// 	.then((res) => {
				// 		let temp = [];
				// 		for (let i = 0; i < res.data.length; i++) {
				// 			temp.push(res.data[i].fund_code);
				// 		}
				// 		// ////console.log(temp)
				// 		if (temp.length > 0) {
				// 			that.$router.push({ name: 'comparefundormanager', query: { id: temp } });
				// 		}
				// 	})
				// 	.catch((err) => {
				// 		//  that.$message('失败')
				// 		////console.log(err)
				// 		//that.$message('数据缺失')
				// 	});
			} else {
				if (this.choosebasket.length > 1 && this.choosebasket.length <= 4) {
					let tempcode = '';
					let tempname = '';
					let temptype = null;
					for (let i = 0; i < this.choosebasket.length; i++) {
						tempcode = tempcode + this.choosebasket[i].fund_code + ',';
						tempname = tempname + this.choosebasket[i].name + ',';
					}
					tempcode = tempcode.slice(0, tempcode.length - 1);
					tempname = tempname.slice(0, tempname.length - 1);
					let data = await TypeMsg({ code: tempcode });
					if (data) {
						// //console.log(data)
						if (data.data) {
							if (data.data.length == 0) {
								this.$message.error('请选择具有相同类型的基金进行比较');
							} else if (data.data.length == 1) {
								temptype = data.data[0];
								if (
									temptype == 'bond' ||
									temptype == 'cbond' ||
									temptype == 'purebond' ||
									temptype == 'bill' ||
									temptype == 'equity' ||
									temptype == 'obond'
								) {
									this.$router.push({
										path: '/fundcompare',
										query: {
											id: tempcode,
											type: temptype,
											name: tempname
										}
									});
								} else {
									this.$message('暂时只提供主动权益，二级债，债券类产品的比较');
								}
							} else if (data.data.length > 1) {
								// this.showitem = true
								// this.options = []
								// for(let i = 0; i < data.data.length; i++){
								//     this.options.push({value:data.data[i],label:data.data[i]})
								// }
								this.$message('服务器错误');
							}
						}
					}

					// //console.log(this.choosebasket)
					// this.visibleshow = false;
					// let tarr = [];
					// for (let i = 0; i < this.choosebasket.length; i++) {
					// 	tarr.push(this.choosebasket[i].fund_code);
					// }
					// this.$router.push({ name: 'comparefundormanager', query: { id: tarr } });
				} else {
					this.$message('请选择需要比较的基金，最多四个,最少二个');
				}
			}
		}
	},
	//生命周期 - 创建完成（可以访问当前this实例）
	created() {
		// this.gettable()
		// this.showtable()
	},
	//生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	beforeCreate() {}, //生命周期 - 创建之前
	beforeMount() {}, //生命周期 - 挂载之前
	beforeUpdate() {}, //生命周期 - 更新之前
	updated() {}, //生命周期 - 更新之后
	beforeDestroy() {}, //生命周期 - 销毁之前
	destroyed() {}, //生命周期 - 销毁完成
	activated() {} //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style>
.jijinchizisize {
	font-size: 16px;
}
.affixstyle {
	z-index: 2001;
	position: fixed;
	bottom: 40px;
	right: 40px;
	color: #409eff;
}
.affixbotton {
	width: 40px;
	padding: 10px 10px !important;
	height: 40px;
	border-radius: 50% !important;
}
.affixstyle .el-icon-shopping-bag-1 {
	font-size: 20px;
}
.buttonmargin {
	margin: 10px;
}
</style>
