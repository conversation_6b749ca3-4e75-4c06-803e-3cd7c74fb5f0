<template>
	<div id="industryAllocationPerformanceChart">
		<div class="flex_end mb-16">
			<div class="mr-16">
				<span style="margin-right: 8px">请选择x轴数据</span>
				<el-select v-model="active_type" placeholder="" @change="changeActiveType">
					<el-option v-for="item in type_list" :key="item" :label="item" :value="item"> </el-option>
				</el-select>
			</div>
			<div>
				<span style="margin-right: 8px">请选择数据项</span>
				<el-select v-model="active" placeholder="" @change="changeActive">
					<el-option v-for="item in data_list" :key="item" :label="item" :value="item"> </el-option>
				</el-select>
			</div>
		</div>
		<!-- <v-chart style="width: 100%; height: 123px" ref="allocationBar" autoresize :options="allocationBar" @ready="onChartReady"></v-chart> -->
		<v-chart style="width: 100%; height: 400px" ref="allocation" autoresize :options="allocation" @ready="onChartReady"></v-chart>
	</div>
</template>

<script>
import ECharts from 'echarts';
import { barChartOption, triangleCircleOption } from '@/utils/chartStyle.js';
import { getAllocationDetails } from '@/api/pages/Analysis.js';
export default {
	data() {
		return {
			cardLoading: true,
			loading: true,
			equityWeght: [],
			requestData: [],
			industries: [],
			industryNameList: [],
			tempvalueselect: {},
			dataZoom: { start: 0, end: 100 },
			chartInstances: [],
			allocationBar: {},
			allocation: {},
			echartBar: {},
			info: {},
			active: '',
			active_type: '报告期',
			date_list: [],
			industry_list: [],
			data_list: [],
			type_list: ['报告期', '行业'],
			data: []
		};
	},
	methods: {
		async getData(data, info) {
			this.info = info;
			// await this.getAllocationDetails();
			// 对原始数据进行排序
			let sortData = data.sort((a, b) => {
				return this.moment(this.moment(a.yearqtr, 'YYYY QQ').format()).isBefore(this.moment(b.yearqtr, 'YYYY QQ').format()) ? -1 : 1;
			});
			// 对数组去重，获取行业列表
			this.industry_list = Array.from(new Set(data.map((v) => v.industry_name)));
			// 获取季度列表
			this.date_list = Array.from(new Set(data.map((v) => v.yearqtr))).sort((a, b) => {
				return this.moment(this.moment(b, 'YYYY QQ').format()).isBefore(this.moment(a, 'YYYY QQ').format()) ? -1 : 1;
			});
			this.active_type = '报告期';
			this.data_list = this.industry_list;
			this.active = this.data_list[0];

			this.data = sortData;
			this.updateChart();
			// this.getScattarChartData(data);
			// this.onChartReady();
		},
		changeActive(val) {
			this.active = val;
			this.updateChart();
		},
		changeActiveType(val) {
			this.active_type = val;
			if (this.active_type == '报告期') {
				this.data_list = this.industry_list;
			} else {
				this.data_list = this.date_list;
			}
			this.active = this.data_list[0];
			this.updateChart();
		},
		// 图形展示修改 v2.0
		updateChart() {
			this.cardLoading = false;
			let series = this.data.filter((v) => (this.active_type == '报告期' ? v.industry_name == this.active : v.yearqtr == this.active));
			let date = Array.from(new Set(series.map((v) => v.yearqtr))).sort((a, b) => {
				return this.moment(this.moment(b, 'YYYY QQ').format()).isBefore(this.moment(a, 'YYYY QQ').format()) ? -1 : 1;
			});
			let industrys = Array.from(new Set(series.map((v) => v.industry_name)));
			const option = barChartOption({
				toolbox: 'none',
				xAxis: [
					{
						type: 'category',
						data: this.active_type == '报告期' ? date : industrys
					}
				],
				yAxis: [
					{
						type: 'value',
						name: '占净值比(%)',
						// min: 0,
						// max: 100,
						position: 'left'
					},
					{
						type: 'value',
						name: '收益率',
						// min: 0,
						// max: 10,
						position: 'right',
						axisLabel: {
							formatter: '{value}%'
						}
					}
				],
				legend: {
					data: [this.active, '行业基准收益率', '行业超额收益率', '行业收益率']
				},
				tooltip: {
					// 坐标轴指示器，坐标轴触发有效
					type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
					formatter: (obj) => {
						var value = `<div style="font-size:14px;">` + obj?.[0].axisValue + `</div>`;
						for (let i = 0; i < obj.length; i++) {
							if (obj?.[i].seriesName == this.active) {
								value +=
									`<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
									`<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:#2b5ebd
									;"></div>` +
									`<div style="font-family: PingFang SC;">` +
									obj?.[i].seriesName +
									'配置权重</div></div>' +
									`<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
									(obj?.[i].value?.[1] * 1).toFixed(2) +
									'%</div>' +
									`</div>`;
							} else if (obj?.[i].seriesName == '行业基准收益率') {
								value +=
									`<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
									`<div style="display:flex;align-items:center;"><div style="margin-right:8px;width:0px;height:0px;border-left: 4px solid transparent;border-right: 4px solid transparent;border-bottom:8px solid ` +
									obj?.[i].color +
									`;"></div>` +
									`<div style="font-family: PingFang SC;">` +
									obj?.[i].seriesName +
									'</div></div>' +
									`<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
									(obj?.[i].value?.[1] * 1).toFixed(2) +
									'%</div>' +
									`</div>`;
							} else {
								value +=
									`<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
									`<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
									obj?.[i].color +
									`;"></div>` +
									`<div style="font-family: PingFang SC;">` +
									obj?.[i].seriesName +
									'</div></div>' +
									`<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
									(obj?.[i].value?.[1] * 1).toFixed(2) +
									'%</div>' +
									`</div>`;
							}
						}
						return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
					}
				},
				series: [
					{
						name: this.active,
						type: 'bar',
						data: series.map((v) => {
							return [this.active_type == '报告期' ? v.yearqtr : v.industry_name, v.weight * 1];
						}),
						itemStyle: {
							color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
								{ offset: 0, color: '#2b5ebd' },
								// { offset: 0.5, color: '#5470c6' },
								{ offset: 1, color: '#83bff6' }
							])
						}
					},
					{
						name: '行业基准收益率',
						type: 'scatter',
						yAxisIndex: 1,
						data: series.map((v) => {
							return [this.active_type == '报告期' ? v.yearqtr : v.industry_name, v.industry_return * 1];
						}),
						symbol: 'triangle',
						symbolSize: 8,
						itemStyle: {
							color: '#ee6666'
						}
					},
					{
						name: '行业收益率',
						type: 'scatter',
						yAxisIndex: 1,
						data: series.map((v) => {
							return [this.active_type == '报告期' ? v.yearqtr : v.industry_name, v.excess_return * 1 + v.industry_return * 1];
						}),
						symbol: 'circle',
						symbolSize: 8,
						itemStyle: {
							color: '#ee6666'
						}
					}
					// {
					// 	name: '行业收益率',
					// 	type: 'scatter',
					// 	yAxisIndex: 1,
					// 	data: data
					// 		.filter((v) => v.industry_name == this.industry_list[0])
					// 		.map((v) => {
					// 			return [v.yearqtr, v.excess_return - v.industry_return * 1];
					// 		}),
					// 	symbol: 'star',
					// 	symbolSize: 10,
					// 	itemStyle: {
					// 		color: '#ee6666'
					// 	}
					// }
				]
				// grid: {
				// 	right: '20%'
				// },
			});
			this.allocation = option;
		},
		// 请求各报告期资产配置数据
		async getAllocationDetails() {
			let data = await getAllocationDetails({
				flag: this.info.flag,
				type: this.info.type,
				code: this.info.code,
				start_date: '',
				end_date: '',
				have: 'stock'
			});
			if (data?.mtycode == 200) {
				this.equityWeght = [];
				this.equityWeght = data?.data
					.sort((a, b) => {
						return this.moment(this.moment(a.yearqtr, 'YYYY QQ').format()).isBefore(this.moment(b.yearqtr, 'YYYY QQ').format()) ? -1 : 1;
					})
					.map((item) => {
						return {
							name: item.yearqtr,
							value: item.equityWeight
						};
					});
				this.getBarChartData();
			}
		},
		// 获取柱状图数据
		getBarChartData() {
			this.allocationBar = barChartOption({
				toolbox: 'none',
				grid: {
					left: '34px',
					top: '8px',
					bottom: '12px',
					right: '8px'
				},
				xAxis: [
					{
						show: true,
						offset: 0,
						type: 'category',
						axisLabel: { show: false },
						boundaryGap: false,
						data: this.equityWeght.map((item) => {
							return item.name;
						})
					}
				],
				yAxis: [
					{
						show: true,
						type: 'value',
						min: 0,
						max: 100,
						formatter: function (params) {
							return params + '%';
						}
					}
				],
				dataZoom: {
					// 这个dataZoom组件，默认控制x轴。
					type: 'slider', // 这个 dataZoom 组件是 slider 型 dataZoom 组件
					start: this.dataZoom.start, // 左边在 10% 的位置。
					end: this.dataZoom.end, // 右边在 60% 的位置。
					show: false
				},
				tooltip: {
					trigger: 'axis',
					type: 'shadow',
					formatter: function (params) {
						return `季度：${params[0].name} <br/> 总权重：${params[0].value}%`;
					}
				},
				series: [
					{
						name: '配置总额',
						data: this.equityWeght.map((item) => {
							return Number(item.value).toFixed(2);
						}),
						type: 'bar',
						barWidth: '15px',
						itemStyle: {
							barBorderRadius: [15, 15, 0, 0],
							borderWidth: 0,
							color: new echarts.graphic.LinearGradient(
								0,
								0,
								0,
								1, // 渐变方向，这里表示从上到下
								[
									{ offset: 0, color: '#FFAB3E' }, // 渐变起始颜色
									{ offset: 1, color: '#FFC67D' } // 渐变结束颜色
								]
							)
						}
					}
				]
			});
			console.log('bar', this.allocationBar);
		},
		// 获取散点图数据
		getScattarChartData(data) {
			this.$nextTick(() => {
				this.cardLoading = false;
				// 对原始数据进行排序
				let sortData = data.sort((a, b) => {
					return this.moment(this.moment(a.yearqtr, 'YYYY QQ').format()).isBefore(this.moment(b.yearqtr, 'YYYY QQ').format()) ? -1 : 1;
				});
				// 获取季度列表
				let date = Array.from(new Set(sortData.map((v) => v.yearqtr)));
				// 对数组去重，获取行业列表
				let industries = Array.from(new Set(sortData.map((v) => v.industry_name)));
				// 处理图表数据-serise-data
				let tableData = [];
				// 权重最大最小值
				let max = 0;
				let min = 100;
				// 格式化数据，拼接为三角圆圈图所需要的数据格式
				sortData.map((item) => {
					if (item.weight * 1 > max) {
						max = item.weight * 1;
					}
					if (item.weight * 1 < min) {
						min = item.weight * 1;
					}
					let index = industries.indexOf(item.industry_name);
					tableData.push([item.yearqtr, index, item.industry_return, item.weight, item.industry_return - 0 > 0 ? 'True' : 'False']);
				});
				this.allocation = triangleCircleOption({
					toolbox: 'none',
					visualMap: {
						max,
						min
					},
					xAxis: [{ type: 'category', data: date }],
					yAxis: [
						{
							type: 'value',
							min: 0,
							max: industries.length - 1,
							interval: 1,
							formatter: function (value, index) {
								if (value < industries.length) return industries[value].toString();
							}
						}
					],
					tooltip: {
						trigger: 'item',
						axisPointer: {
							type: 'cross'
						},
						formatter(params) {
							return (
								'日期：' +
								params.data[0] +
								'季度，行业：' +
								industries[params.data[1]] +
								'，权重：' +
								params.data[3].toFixed(2) +
								'%，行业估算收益率：' +
								parseInt(params.data[2] * 100).toFixed(2) +
								'%'
							);
						}
					},
					dataZoom: {
						// 这个dataZoom组件，默认控制x轴。
						type: 'slider', // 这个 dataZoom 组件是 slider 型 dataZoom 组件
						start: 0, // 左边在 10% 的位置。
						end: 100 // 右边在 60% 的位置。
					},
					series: [
						{
							name: '大类资产配置',
							type: 'scatter',
							data: tableData,
							symbolSize: 5
						}
					]
				});
				console.log('scattar', this.allocation);
				this.loading = false;
			});
		},
		// 监听datazoom操作
		handleDataZoom(val) {
			this.dataZoom.start = val.start;
			this.dataZoom.end = val.end;
			this.getBarChartData();
		},
		// 监听图初始化完成
		onChartReady() {
			this.$nextTick(() => {
				let chartA = this.$refs.allocationBar;
				let chartB = this.$refs.allocation;
				if (chartA && chartB) {
					chartA.group = 'group1';
					chartB.group = 'group1';
					ECharts.connect('group1');

					// ECharts.connect([chartA, chartB]);
				}
			});
		},
		/* 写一个函数,计算两个对象数组重合的部分 */
		getSame(arr1, arr2) {
			var arr = [];
			for (var j = 0; j < arr2.length; j++) {
				for (var i = 0; i < arr1.length; i++) {
					if (arr1[i] == arr2[j].name) {
						arr.push(arr2[j]);
					}
				}
			}
			return arr;
		},
		// 格式化规模柱状图数据
		formatterData(data) {
			// 深拷贝数组
			let arr = [];
			data.map((item) => {
				arr.push(item);
			});
			let yearData = [];
			arr.map((item) => {
				let index = yearData.findIndex((obj) => {
					return obj.yearqtr == item.yearqtr;
				});
				if (index == -1) {
					yearData.push({ yearqtr: item.yearqtr, weight: item.weight });
				} else {
					yearData[index].weight = yearData[index].weight * 1 + item.weight * 1;
				}
			});
			return yearData
				.sort((a, b) => {
					return this.moment(a.yearqtr, 'YYYY Q')._d.getTime() - this.moment(b.yearqtr, 'YYYY Q')._d.getTime();
				})
				.map((item) => {
					return item.weight;
				});
		},
		async createPrintWord(data, info) {
			await this.getData(data, info);
			let key = 'industryAllocationPerformanceChart';
			let height = document.getElementById(key)?.clientHeight;
			let width = document.getElementById(key)?.clientWidth;
			let canvas = await this.html2canvas(document.getElementById(key), {
				scale: 3
			});
			return [
				...this.$exportWord.exportTitle('行业配置变化年度堆叠图'),
				...this.$exportWord.exportChart(canvas.toDataURL('image/jpg'), {
					width,
					height
				})
			];
		}
	}
};
</script>

<style></style>
