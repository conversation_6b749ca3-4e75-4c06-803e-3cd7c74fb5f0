function sortExport(arr) {
	let { keys, list, name } = sort(arr);
	return { arr: list, index: keys, name };
}
function sort(arr) {
	let arr1 = [];
	let keys = [];
	let list = [];
	let name = [];
	arr.map((item, index) => {
		if (item.name !== '') {
			arr1.push({ item: item.return, index, name: item.name });
		}
	});
	arr1
		.sort((a, b) => {
			return a.item - b.item;
		})
		.map(item => {
			keys.push(item.index);
			list.push(item.item);
			name.push(item.name);
		});
	return { keys, list, name };
}
export default sortExport;
