<template>
  <div v-loading="loadingFOF"
       class="border_table">
    <!-- 头部区域 -->
    <div class="border_table_header">
      <!-- 右侧标题区域 -->
      <div class="border_table_header_title">
        <div class="block" />
        <div>直投FOF业绩看板</div>
        <img alt=""
             class="icon-question"
             src="../../../../../assets/img/question.png">
      </div>
      <!-- 左侧筛选区域 -->
      <div class="border_table_header_filter">
        <!-- 单选框 -->
        <!-- <div class="border_table_header_radio">
          <el-radio v-model="checked"
                    label="true"
                    @click.native="changeRadio($event)">税费后</el-radio>
        </div> -->
        <div>
          <!-- <el-radio-group v-model="radio"
                          @change="getPerformanceBoardFOF">
            <el-radio-button label="CWRR" />
            <el-radio-button label="TWRR" />
          </el-radio-group> -->
        </div>
        <!-- 下载 -->
        <div class="border_table_header_upload"
             @click="downloadExcel()">
          <i class="el-icon-download"></i>
        </div>
      </div>
    </div>
    <!-- 表格区域 -->
    <el-table :data="tableData"
              border
              class="kanbantable"
              stripe>
      <el-table-column align="gotoleft"
                       label="策略"
                       fixed
                       prop="strategyName"
                       width="150" />
      <el-table-column :show-overflow-tooltip="true"
                       align="gotoleft"
                       fixed
                       label="投资经理"
                       prop="managerName"
                       width="200" />
      <el-table-column align=""
                       label="期末账面价值(万)"
                       prop="endingBookValue"
                       sortable
                       width="160"><template slot-scope="{row}">
          {{handDataForm(row.endingBookValue,'YW')}}</template> </el-table-column>
      <el-table-column align="center"
                       label="分析区间">
        <!-- <el-table-column :show-overflow-tooltip="true"
                         align=""
                         label="平均资金占用(亿)"
                         prop="intervalAvgFundsUtilized"
                         sortable
                         width="160 "><template slot-scope="{row}">
            {{handDataForm(row.intervalAvgFundsUtilized,'Y')}}</template> </el-table-column> -->
        <el-table-column :show-overflow-tooltip="true"
                         align=""
                         label="财务收益(万)"
                         prop="intervalFinancial"
                         sortable
                         width="130"><template slot-scope="{row}">
            {{handDataForm(row.intervalFinancial,'YW')}}</template> </el-table-column>
        <el-table-column :show-overflow-tooltip="true"
                         align=""
                         label="市值收益(万)"
                         prop="intervalMarketValue"
                         sortable
                         width="130"><template slot-scope="{row}">
            {{handDataForm(row.intervalMarketValue,'YW')}}</template> </el-table-column>
        <el-table-column :show-overflow-tooltip="true"
                         align=""
                         label="市值收益率"
                         prop="intervalMarketValueRate"
                         sortable
                         width="120"><template slot-scope="{row}">
            {{handDataForm(row.intervalMarketValueRate,'2p')}}</template> </el-table-column>
        <el-table-column :show-overflow-tooltip="true"
                         align=""
                         label="基准收益率"
                         prop="intervalBenchmarkRate"
                         sortable
                         width="120"><template slot-scope="{row}">
            {{handDataForm(row.intervalBenchmarkRate,'2p')}}</template> </el-table-column>
        <el-table-column :show-overflow-tooltip="true"
                         align=""
                         label="超额收益率"
                         prop="intervalExcessRate"
                         sortable
                         width="120"><template slot-scope="{row}">
            {{handDataForm(row.intervalExcessRate,'2p')}}</template> </el-table-column>
      </el-table-column>
      <el-table-column align="center"
                       label="当日">
        <el-table-column :show-overflow-tooltip="true"
                         align=""
                         label="市值收益率"
                         prop="dayMarketValueRate"
                         sortable
                         width="120"><template slot-scope="{row}">
            {{handDataForm(row.dayMarketValueRate,'2p')}}</template> </el-table-column>
        <el-table-column :show-overflow-tooltip="true"
                         align=""
                         label="超额收益率"
                         prop="dayExcessReturnRate"
                         sortable
                         width="120"><template slot-scope="{row}">
            {{handDataForm(row.dayExcessReturnRate,'2p')}}</template> </el-table-column>
      </el-table-column>
      <el-table-column align="center"
                       label="近1周">
        <el-table-column :show-overflow-tooltip="true"
                         align=""
                         label="市值收益率"
                         prop="weekMarketValueRate"
                         sortable
                         width="120"><template slot-scope="{row}">
            {{handDataForm(row.weekMarketValueRate,'2p')}}</template> </el-table-column>
        <el-table-column :show-overflow-tooltip="true"
                         align=""
                         label="超额收益率"
                         prop="weekExcessRate"
                         sortable
                         width="120"><template slot-scope="{row}">
            {{handDataForm(row.weekExcessRate,'2p')}}</template> </el-table-column>
      </el-table-column>
      <el-table-column align="center"
                       label="近1月">
        <el-table-column :show-overflow-tooltip="true"
                         align=""
                         label="市值收益率"
                         prop="monthMarketValueRate"
                         sortable
                         width="120"><template slot-scope="{row}">
            {{handDataForm(row.monthMarketValueRate,'2p')}}</template> </el-table-column>
        <el-table-column :show-overflow-tooltip="true"
                         align=""
                         label="超额收益率"
                         prop="monthExcessRate"
                         sortable
                         width="120"><template slot-scope="{row}">
            {{handDataForm(row.monthExcessRate,'2p')}}</template> </el-table-column>
      </el-table-column>
      <template slot="empty">
        <el-empty :image-size="160"></el-empty>
      </template>
    </el-table>
  </div>
</template>

<script>
import { getPerformanceBoardFOF } from '@/api/pages/tkdesign/performance'
import { filter_json_to_excel_inside, changColumnToRow, filter_json_to_excel_inside_multiHeader } from '@/utils/exportExcel.js';

export default {
  props: {
    time: {
      type: Array,
    }
  },
  data () {
    return {
      // 搜索区域input绑定的数据
      checked: false,
      radio: 'TWRR',
      loadingFOF: false,
      tableData: [], //表格数据源
    };
  },
  watch: {
    time () {
      this.getPerformanceBoardFOF()
    }
  },
  mounted () {
    // this.getPerformanceBoardFOF()
  },
  methods: {
    handDataForm (data, flag) {
      let result = ''
      if (data === '' || data === 'NaN' || data === undefined) {
        return '--'
      }
      if (flag == '2p') {
        result = (data * 100).toFixed(2) + '%'
      }
      else if (flag == '2') {
        result = (data * 1).toFixed(2)
      }
      else if (flag == 'Y') {
        result = (data / 100000000).toFixed(2)
      }
      else if (flag == 'YW') {
        result = (data / 10000).toFixed(2)
      }
      return result
    },
    downloadExcel () {
      const title1 = ['策略', '投资经理', '期末账面价值',
        '分析区间', '', '', '', '', '',
        '当日', '', '近1周', '', '近1月', '']
      const title2 = ['', '', '',
        '财务收益', '市值收益', '市值收益率', '基准收益率', '超额收益率',
        '市值收益率', '超额收益率', '市值收益率', '超额收益率', '市值收益率', '超额收益率']
      const merge = ['A1:A2', 'B1:B2', 'C1:C2', 'D1:I1', 'J1:K1', 'L1:M1', 'N1:O1'];
      let Aindex = 3;
      let rowIndex = 0;
      let dataArray = []
      this.tableData && this.tableData.map(line => {
        const temp = JSON.parse(JSON.stringify(line))
        if (line.rowIndex === rowIndex) {
          const str = `A${Aindex}:A${Aindex + line.length - 1}`
          Aindex += line.length;
          rowIndex += line.length;
          merge.push(str);
        } else {
          temp.strategyName = '';
        }
        dataArray.push(temp);
      })
      const format = [
        { value: 'strategyName', format: '' },
        { value: 'managerName', format: '' },
        { value: 'endingBookValue', format: '' },
        // { value: 'intervalAvgFundsUtilized', format: '' },
        { value: 'intervalFinancial', format: '' },

        { value: 'intervalMarketValue', format: '' },
        { value: 'intervalMarketValueRate', format: '' },
        { value: 'intervalBenchmarkRate', format: '' },
        { value: 'intervalExcessRate', format: '' },

        { value: 'dayMarketValueRate', format: '' },
        { value: 'dayExcessReturnRate', format: '' },
        { value: 'weekMarketValueRate', format: '' },
        { value: 'weekExcessRate', format: '' },
        { value: 'monthMarketValueRate', format: '' },
        { value: 'monthExcessRate', format: '' },
      ]

      filter_json_to_excel_inside_multiHeader([title1, title2], dataArray, [], '直投FOF业绩看板', merge, format)
    },
    /**
     * 给某一单元格添加样式
     */
    cellStyle ({ row, column, rowIndex, columnIndex }) {
      if (rowIndex === 0 && columnIndex === 1)
        return {
          backgroundImage: 'linear-gradient(90deg, rgba(242, 208, 211,1) 0%, rgba(0, 0, 0, 0) 100%)',
        }
      if (rowIndex === 0 && columnIndex === 2)
        return {
          backgroundImage: 'linear-gradient(90deg, rgba(218, 236, 207,1) 0%, rgba(218, 236, 207,0) 100%)',
        }
    },

    /**
     * 合并单元格
     * @param rowIndex 行数（从0开始）
     * @param columnIndex 列数（从0开始）
     */
    spanMethod ({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        if (rowIndex === row.rowIndex) {
          return {
            rowspan: row.length,
            colspan: 1,
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          }
        }
      }
    },

    /**
     * 单选框选中处理
     */
    changeRadio (event) {
      if (event.target.tagName === 'INPUT') {
        this.checked = !this.checked
        this.getPerformanceBoardFOF()
      }
    },

    /**
     * 获取直投MOM-业绩看板
     * @param commission 是否是税费后。ture；false
     * @param startDate 时间区间的开始时间：2023-02-02
     * @param endDate 时间区间的结束时间：2023-02-02
     * @param measure 是否剔除税延及专属商业养老。CWRR、TWRR选其一
     */
    getPerformanceBoardFOF () {
      this.loadingFOF = true
      this.tableData = []
      let params = {
        commission: this.checked,
        startDate: this.moment(this.time[0]).format('YYYY-MM-DD'),
        endDate: this.moment(this.time[1]).format('YYYY-MM-DD'),
        measure: this.radio
      }
      getPerformanceBoardFOF(params).then(res => {
        this.loadingFOF = false
        if (res && (res.mtycode == 200 || res.code == 200)) {
          console.log(res.data);
          const arr = res.data;
          let index = 0
          // arr.forEach((item) => {
          //   item.managerList.forEach((citem) => {
          //     let obj = {
          //       ...citem,
          //       strategyName: item.strategyName,
          //       rowIndex: index,
          //       length: item.managerList.length,
          //     }
          //     this.tableData.push(obj)
          //   })
          //   index += item.managerList.length
          // })
          this.tableData = arr
        }
      })
    }
  },
}
</script>
<style>
.kanbantable .el-table__fixed .cell {
	line-height: 47px !important;
}
</style>
<style lang="scss" scoped>
@import '../../../tkdesign';

.border_table_header_title {
	display: flex;
	align-items: center;

	.block {
		width: 6px;
		height: 20px;
		border-radius: 35px;
		background-color: #4096ff;
		margin-right: 16px;
	}

	.icon-question {
		margin-left: 3px;
	}
}

.border_table_header_filter {
	display: flex;
	align-items: center;
	font-size: 14px;

	.border_table_header_radio {
		display: flex;
		align-items: center;
		margin-right: 16px;
	}

	.border_table_header_upload {
		width: 32px;
		line-height: 30px;
		border-radius: 4px;
		border: 1px solid #d9d9d9;
		text-align: center;
		margin-left: 16px;
	}
}
</style>

