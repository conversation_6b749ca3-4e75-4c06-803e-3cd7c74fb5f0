<template>
	<div class="boxCzJzOnly">
		<span style="font-weight: 600; margin-left: 0px; margin-right: 16px">{{ haveName }}</span>
		<div style="margin-top: 16px; display: flex; align-items: center">
			<operator v-if="is_range" ref="operator" @resolveMathRange="resolveMathRange"></operator>
			<el-dropdown @command="command3">
				<el-button type="primary">
					<span>{{
						yearqtr2 == '' ? '选择分位或者值' : quarterList2[quarterList2.findIndex((item) => item.value == yearqtr2)].label
					}}</span>
					<i class="el-icon-arrow-down el-icon--right"></i>
				</el-button>
				<el-dropdown-menu slot="dropdown">
					<el-dropdown-item v-for="(item, index) in quarterList2" :command="item.value" :key="index">{{ item.label }}</el-dropdown-item>
				</el-dropdown-menu>
			</el-dropdown>
			<div style="width: 16px"></div>
			<el-dropdown @command="command">
				<el-button type="primary">
					{{ iconFlag != '' ? (iconFlag == 'all' ? '所有' : iconFlag) : '运算符' }}<i class="el-icon-arrow-down el-icon--right"></i>
				</el-button>
				<el-dropdown-menu slot="dropdown">
					<el-dropdown-item command="all">所有</el-dropdown-item>
					<el-dropdown-item command="<">&lt;</el-dropdown-item>
					<el-dropdown-item command="=">=</el-dropdown-item>
					<el-dropdown-item command=">">&gt;</el-dropdown-item>
					<el-dropdown-item command="<=">&lt;=</el-dropdown-item>
					<el-dropdown-item command=">=">&gt;=</el-dropdown-item>
				</el-dropdown-menu>
			</el-dropdown>
			<div style="margin-left: 0px; display: flex; align-items: center">
				<div style="margin-left: 16px">
					<el-input
						type="number"
						@input="inputChange"
						:placeholder="
							yearqtr2 == '值'
								? '请输入具体物理含义值'
								: placeholder.indexOf('前百分之') >= 0
								? placeholder
								: '请输入，单位为：' + placeholder
						"
						v-model="input"
					>
					</el-input>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import operator from '@/pages/filter/fund/beta/componentsFilter/components/operator.vue';

export default {
	props: {
		is_range: {
			type: Boolean,
			default: false
		},
		haveName: {
			type: String,
			default: ''
		},
		dataX: {
			type: Object,
			default: {}
		},
		placeholder: {
			type: String
		},
		indexFlag: {
			type: Number
		},
		baseIndexFlag: {
			type: Number
		}
	},
	//import引入的组件需要注入到对象中才能使用
	components: { operator },
	data() {
		//这里存放数据
		return {
			yearqtr2: '分位',
			quarterList2: [
				{
					value: '分位',
					label: '分位'
				},
				{
					value: '值',
					label: '值'
				}
			],
			field103: '因子暴露',
			field103Options: [
				{
					label: '因子暴露',
					value: '因子暴露'
				}
			],
			field103Options2: [
				{
					label: '因子暴露',
					value: '因子暴露'
				}
			],
			iconFlag: '',
			showBox: false,
			input: '',
			yearqtr: '1q',
			quarterList: [
				{
					value: '1q',
					label: '一季'
				},
				{
					value: '2q',
					label: '半年'
				},
				{
					value: '1y',
					label: '一年'
				}
			],
			mathRange: { mathRange: 'avg' }
		};
	},
	//监听属性 类似于data概念
	computed: {},
	//监控data中的数据变化
	// watch: {
	// 	dataX(val) {
	// 		if (val.dataResult && val.dataResult.length > 0) {
	// 			this.showBox = true;
	// 			this.iconFlag = val.dataResult[0].flag;
	// 			this.input = val.dataResult[0].value;
	// 			this.yearqtr = val.dataResult[0].yearqtr;
	// 			this.field103 = val.dataResult[0].label;
	// 			this.yearqtr2 = val.dataResult[0].rank_value;
	// 			this.mathRange = val.dataResult[0].mathRange;
	// 			if (this.$refs['operator']) {
	// 				this.$refs['operator'].getFlag(val.dataResult[0].mathRange);
	// 			}
	// 		}
	// 	}
	// },
	//方法集合
	methods: {
		resolveMathRange(obj) {
			this.mathRange = obj;
			this.resolveFather();
		},
		resolveFather() {
			this.$emit(
				'boxCzJzOnlyChange',
				this.baseIndexFlag,
				this.indexFlag,
				this.input,
				this.iconFlag,
				this.field103,
				this.yearqtr,
				this.yearqtr2,
				this.FUNC.isEmpty(this.yearqtr) && this.FUNC.isEmpty(this.input) && this.FUNC.isEmpty(this.iconFlag),
				this.mathRange
			);
		},
		changeNode(e) {
			this.yearqtr = '';
			this.iconFlag = '';
			this.showBox = false;
			this.input = '';
			this.resolveFather();
		},
		command(e) {
			this.iconFlag = e;
			this.showBox = true;
			this.resolveFather();
		},
		command3(e) {
			this.yearqtr2 = e;
			this.resolveFather();
		},
		command2(e) {
			this.yearqtr = e;
			this.resolveFather();
		},
		inputChange() {
			this.resolveFather();
		},
		getQuarter() {
			// this.quarterList = []
			// let option = [];
			// 	let qList = ['Q1', 'Q2', 'Q3', 'Q4'];
			// 	let pre = this.moment(Date.now()-86400000*365*3).format('YYYY-MM-DD');
			// 	let now = this.FUNC.transformDate(new Date());
			// 	let preYear = pre.slice(0, 4);
			// 	let nowYear = now.slice(0, 4);
			// 	let preQ = this.FUNC.dateToQuarter(pre).slice(5);
			// 	let nowQ = this.FUNC.dateToQuarter(now).slice(5);
			// 	let yList = Array.from({ length: nowYear - preYear + 1 }, (item, index) => (item = parseInt(preYear) + index));
			// 	for (let y of yList) {
			// 		let yobj = {
			// 			value: y,
			// 			label: y,
			// 			children: []
			// 		};
			// 		if (y == preYear) {
			// 			qList.forEach(q => {
			// 				if (q >= preQ) {
			// 					yobj.children.push({ value: q, label: q });
			// 				}
			// 			});
			// 		} else if (y == nowYear) {
			// 			qList.forEach(q => {
			// 				if (q <= nowQ) {
			// 					yobj.children.push({ value: q, label: q });
			// 				}
			// 			});
			// 		} else {
			// 			qList.forEach(q => yobj.children.push({ value: q, label: q }));
			// 		}
			// 		option.push(yobj);
			// 	}
			//     for(let i = option.length-1 ; i>= 0; i--) {
			//         for(let j = option[i].children.length-1 ; j>=0; j--) {
			//             this.quarterList.push({value:option[i].value+' '+option[i].children[j].value,label:option[i].value+' '+option[i].children[j].value})
			//         }
			//     }
		}
	},
	//生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	//生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.getQuarter();
		if (JSON.stringify(this.dataX) != '{}') {
			if (this.dataX.dataResult && this.dataX.dataResult.length > 0) {
				this.showBox = true;
				this.iconFlag = this.dataX.dataResult[0].flag;
				this.input = this.dataX.dataResult[0].value;
				this.yearqtr = this.dataX.dataResult[0].yearqtr;
				this.field103 = this.dataX.dataResult[0].label;
				this.yearqtr2 = this.dataX.dataResult[0].rank_value;
				this.mathRange = this.dataX.dataResult[0].mathRange;
				if (this.$refs['operator']) {
					this.$refs['operator'].getFlag(this.dataX.dataResult[0].mathRange);
				}
			}
		}
	},
	beforeCreate() {}, //生命周期 - 创建之前
	beforeMount() {}, //生命周期 - 挂载之前
	beforeUpdate() {}, //生命周期 - 更新之前
	updated() {}, //生命周期 - 更新之后
	beforeDestroy() {}, //生命周期 - 销毁之前
	destroyed() {}, //生命周期 - 销毁完成
	activated() {} //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
.boxOnlyYSF {
	display: flex;
	align-items: center;
}
</style>
