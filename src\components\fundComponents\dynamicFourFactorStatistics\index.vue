<template>
	<div v-loading="loading">
		<div style="display: flex; align-items: center; justify-content: space-between; position: relative">
			<div class="title" style="margin-bottom: 24px">动态多因子统计 &nbsp;&nbsp;&nbsp;&nbsp;{{ timesel }}</div>
			<el-button class="print_show" icon="el-icon-document-delete" @click="exportExcel">导出Excel</el-button>
		</div>
		<div style="width: 100%" v-show="!loading">
			<el-table :data="factor" max-height="215px" class="table" ref="multipleTable" header-cell-class-name="table-header">
				<el-table-column prop="name" label="因子名称" align="gotoleft"> </el-table-column>
				<el-table-column prop="factor" label="因子均值" align="gotoleft">
					<template slot-scope="scope"
						><span>{{ scope.row.factor | fix3 }}</span></template
					>
				</el-table-column>
				<el-table-column prop="std" align="gotoleft" label="方差">
					<template slot-scope="scope"
						><span>{{ scope.row.std | fix3 }}</span></template
					>
				</el-table-column>
				<el-table-column prop="gainorloss" label="因子收益" align="gotoleft">
					<template slot-scope="scope"
						><span>{{ scope.row.gainorloss | fix2p }}</span></template
					>
				</el-table-column>
				<el-table-column prop="adaptivity" align="gotoleft" label="动态因子增益收益">
					<template slot-scope="scope"
						><span>{{ scope.row.adaptivity | fix2p }}</span></template
					>
				</el-table-column>
			</el-table>
			<el-empty v-show="loading" image-size="100"></el-empty>
		</div>
	</div>
</template>

<script>
import { exportTitle, exportTable } from '@/utils/exportWord.js';
import { filter_json_to_excel } from '@/utils/exportExcel.js';

// 动态4因子统计
export default {
	name: 'dynamicFourFactorStatistics',
	data() {
		return {
			timesel: '',
			factor: [],
			loading: true
		};
	},
	filters: {
		fix3(value) {
			return parseInt(value * 1000) / 1000;
		},
		fix2p(value) {
			if (value == '--' || value == undefined || value == 'nan' || value == null || value == 'NAN') return value;
			else return (value * 100).toFixed(2) + '%';
		}
	},
	methods: {
		getData(data) {
			this.loading = false;
			this.factor = data;
			this.timesel = '统计开始时间：' + data?.[0]?.start_from;
		},
		hideLoading() {
			this.loading = false;
		},
		exportExcel() {
			let list = [
				{
					label: '因子名称',
					fill: 'header',
					value: 'name'
				},
				{
					label: '因子均值',
					fill: 'header',
					value: 'factor',
					format: 'fix3'
				},
				{
					label: '方差',
					value: 'std',
					format: 'fix3'
				},
				{
					label: '因子收益',
					value: 'gainorloss',
					format: 'fix2p'
				},
				{
					label: '动态因子增益收益',
					value: 'adaptivity',
					format: 'fix2p'
				}
			];
			filter_json_to_excel(list, this.factor, '动态4因子统计');
		},
		createPrintWord() {
			let list = [
				{
					label: '因子名称',
					fill: 'header',
					value: 'name'
				},
				{
					label: '因子均值',
					fill: 'header',
					value: 'factor',
					format: 'fix3'
				},
				{
					label: '方差',
					value: 'std',
					format: 'fix3'
				},
				{
					label: '因子收益',
					value: 'gainorloss',
					format: 'fix2p'
				},
				{
					label: '动态因子增益收益',
					value: 'adaptivity',
					format: 'fix2p'
				}
			];
			if (this.factor.length) {
				return [...exportTitle('动态4因子统计' + this.timesel), ...exportTable(list, this.factor)];
			} else {
				return [];
			}
		}
	}
};
</script>

<style></style>
