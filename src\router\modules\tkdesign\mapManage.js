export default [
	{
		path: '/customMarketDivision',
		component: () => import(/* webpackChunkName: "tkdesignAfter" */ '../../../pages/tkdesign/mapManage/customMarketDivision/index.vue'),
		meta: { title: '自定义市场划分', tagShow: false }
	},
	{
		path: '/customMarketDivision/item',
		component: () => import(/* webpackChunkName: "tkdesignAfter" */ '../../../pages/tkdesign/mapManage/customMarketDivision/item.vue'),
		meta: { title: '自定义市场划分', tagShow: false }
	},
	{
		path: '/industryMap',
		component: () => import(/* webpackChunkName: "tkdesignAfter" */ '../../../pages/tkdesign/mapManage/indexBoardIndustryMap.vue'),
		meta: { title: '行业映射管理列表', tagShow: false }
	},
	{
		path: '/productMap',
		component: () => import(/* webpackChunkName: "tkdesignAfter" */ '../../../pages/tkdesign/mapManage/indexBoardProductMap.vue'),
		meta: { title: '产品映射管理列表', tagShow: false }
	},
	{
		path: '/productRecord',
		component: () => import(/* webpackChunkName: "tkdesignAfter" */ '../../../pages/tkdesign/mapManage/productManagementRecord .vue'),
		meta: { title: '产品管理记录', tagShow: false }
	},
  {
		path: '/Tl4Map',
		component: () => import(/* webpackChunkName: "tkdesignAfter" */ '../../../pages/tkdesign/mapManage/TL4Map.vue'),
		meta: { title: 'TL4映射列表', tagShow: false }
	},
	{
		path: '/blacklist',
		component: () => import(/* webpackChunkName: "tkdesignAfter" */ '../../../pages/tkdesign/mapManage/blacklist.vue'),
		meta: { title: '黑名单管理列表', tagShow: false }
	},
	{
		path: '/whitelist',
		component: () => import(/* webpackChunkName: "tkdesignAfter" */ '../../../pages/tkdesign/mapManage/whitelist.vue'),
		meta: { title: '白名单管理列表', tagShow: false }
	}
];
