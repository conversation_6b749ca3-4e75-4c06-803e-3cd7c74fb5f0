<!--  -->
<template>
  <div v-loading="loading"
       class="holdindustry">
    <div style="height: 10px"></div>
    <div style="display: flex; align-items: center; width: 100%; position: relative; justify-content: space-between">
      <div style="display: flex; align-items: center">
        <div class="TitltCompare">个股配置</div>
      </div>
      <div style="display: flex">
        <div class="block">
          <el-cascader v-model="targetQuarter"
                       :options="quarterList"
                       separator=" "
                       @change="changgedate"></el-cascader>
        </div>
        <div style="margin-left: 16px">
          <!-- <el-select v-model="value3" @change="swinscha()" remote prefix-icon="el-icon-search" :loading="loading" placeholder="选择行业">
						<el-option v-for="item in options2" :key="item.value" :label="item.label" :value="item.value"> </el-option>
					</el-select> -->
        </div>
      </div>
    </div>

    <div style="margin-top: 16px">
      <div v-for="(item, index) in arrlist"
           :key="index"
           style="margin-top: 16px">
        <sTable :data="item"
                typeFlag="1"></sTable>
      </div>
    </div>
    <!-- <stockdetail ref='stockdetail'></stockdetail> -->
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import sTable from '../SelfTable.vue';
import stockdetail from '../funddetail/funddetail.vue';
import { ManagerHoldStocks, FundHoldStocks } from '@/api/pages/tools/compare.js';
export default {
  //import引入的组件需要注入到对象中才能使用
  components: { stockdetail, sTable },
  props: {
    comparetype: {
      type: String,
      default: 'manager' //fund
    },
    id: {
      type: String,
      default: '30189741,30441407'
    },
    type: {
      type: String,
      default: 'equity'
    },
    name: {
      type: String,
      default: '萧楠,胡昕炜'
    }
  },
  filters: {
    fix3xx (value, comparetype) {
      // //console.log(value);
      // //console.log(comparetype);
      if (comparetype == 'manager') {
        if (value == '--' || value == null || value == '' || value == 'nan' || value == 'NAN') {
          return '--';
        } else {
          return (Number(value) * 100).toFixed(2) + '%';
        }
      } else {
        if (value == '--' || value == null || value == '' || value == 'nan' || value == 'NAN') {
          return '--';
        } else {
          return Number(value).toFixed(2) + '%';
        }
      }
    },
    fix3 (value) {
      if (value == '--' || value == null || value == '' || value == 'nan' || value == 'NAN') {
        return '--';
      } else {
        return (value * 100).toFixed(2) + '%';
      }
    },
    fix2 (value) {
      return Number(value).toFixed(2) + '亿';
    },
    fixnan (value) {
      if (value == '--' || value == null || value == '' || value == 'nan' || value == 'NAN') {
        return '--';
      } else {
        return value;
      }
    }
  },
  data () {
    //这里存放数据
    return {
      loading: false,
      companyCreateDate: '2008-01-01',
      targetQuarter: '',
      quarterList: [],
      options2: [
        {
          value: '采掘',
          label: '采掘'
        },
        {
          value: '化工',
          name: '化工'
        },
        {
          value: '钢铁',
          name: '钢铁'
        },
        {
          value: '有色金属',
          name: '有色金属'
        },
        {
          value: '建筑材料',
          name: '建筑材料'
        },
        {
          value: '建筑装饰',
          name: '建筑装饰'
        },
        {
          value: '电气设备',
          name: '电气设备'
        },
        {
          value: '机械设备',
          name: '机械设备'
        },
        {
          value: '国防军工',
          name: '国防军工'
        },
        {
          value: '汽车',
          name: '汽车'
        },
        {
          value: '家用电器',
          name: '家用电器'
        },
        {
          value: '纺织服装',
          name: '纺织服装'
        },
        {
          value: '轻工制造',
          name: '轻工制造'
        },
        {
          value: '商业贸易',
          name: '商业贸易'
        },
        {
          value: '农林牧渔',
          name: '农林牧渔'
        },
        {
          value: '食品饮料',
          name: '食品饮料'
        },
        {
          value: '休闲服务',
          name: '休闲服务'
        },
        {
          value: '医药生物',
          name: '医药生物'
        },
        {
          value: '公用事业',
          name: '公用事业'
        },
        {
          value: '交通运输',
          name: '交通运输'
        },
        {
          value: '房地产',
          name: '房地产'
        },
        {
          value: ' 电子',
          name: ' 电子'
        },
        {
          value: '计算机',
          name: '计算机'
        },
        {
          value: '传媒',
          name: '传媒'
        },
        {
          value: '通信',
          name: '通信'
        },
        {
          value: '银行',
          name: '银行'
        },
        {
          value: '非银金融',
          name: '非银金融'
        },
        {
          value: '综合',
          name: '综合'
        }
      ],
      showdetailchoose: false,
      radio: '2',
      value2: '',
      value3: '',
      arrlist: [],
      tablecolumns: [
        {
          dataIndex: 'manager_name',
          key: 'Manager_name',
          title: '基金经理'
        },
        {
          dataIndex: 'name',
          key: 'Name',
          title: '股票名称',
          scopedSlots: {
            customRender: 'Name'
          }
        },
        {
          dataIndex: 'yearqtr',
          key: 'Yearqtr',
          title: '季度'
        },
        {
          dataIndex: 'weight',
          key: 'Weight',
          title: '配置权重',
          sorter: (a, b) => a.weight - b.weight,
          scopedSlots: {
            customRender: 'Weight'
          }
        },
        {
          dataIndex: 'change_weight',
          key: 'Change_weight',
          title: '较上期变化',
          sorter: (a, b) => a.change_weight - b.change_weight,
          scopedSlots: {
            customRender: 'Change_weight'
          }
        },
        {
          dataIndex: 'level',
          key: 'Level',
          title: '黑白马',
          scopedSlots: {
            customRender: 'Level'
          }
        },
        {
          dataIndex: 'times',
          key: 'Times',
          title: '总配置次数'
        }
      ],
      tablecolumnsf: [
        {
          dataIndex: 'fund_name',
          key: 'fund_name',
          title: '基金名称'
        },
        {
          dataIndex: 'name',
          key: 'Name',
          title: '股票名称',
          scopedSlots: {
            customRender: 'Name'
          }
        },
        {
          dataIndex: 'yearqtr_x',
          key: 'yearqtr_x',
          title: '季度'
        },
        {
          dataIndex: 'weight',
          key: 'Weight',
          title: '配置权重',
          sorter: (a, b) => a.weight - b.weight,
          scopedSlots: {
            customRender: 'Weight'
          }
        },
        {
          dataIndex: 'change_weight',
          key: 'Change_weight',
          title: '较上期变化',
          sorter: (a, b) => a.change_weight - b.change_weight,
          scopedSlots: {
            customRender: 'Change_weight'
          }
        },
        {
          dataIndex: 'level',
          key: 'Level',
          title: '黑白马'
        },
        {
          dataIndex: 'times',
          key: 'Times',
          title: '总配置次数'
        }
      ]
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {
    // value2(val){
    //     let arr = this.value2.split('-')
    //     let date = arr[0]+' Q' +Math.floor( ( arr[1] % 3 == 0 ? ( arr[1] / 3 ) : ( arr[1] / 3 + 1 ) ) );
    //     this.getmanager(date)
    //     // this.getmanager()
    // }
  },
  //方法集合
  methods: {
    gogogo (val) {
      // //console.log(this.arrlist)
      let tmanagercode = '';
      let tmanagername = '';
      let code = '';
      for (let i = 0; i < this.arrlist.length; i++) {
        if (this.arrlist[i].name == val) {
          code = this.arrlist[i].stock_code;
          tmanagercode = this.arrlist[i].code;
          tmanagername = this.arrlist[i].manager_name;
        }
      }

      this.$refs.stockdetail.showitem(code, val, 'manager', tmanagercode, tmanagername);
    },
    swinscha () {
      if (this.comparetype == 'manager') {
        // this.getmanager(this.getquarter(0))
        this.getmanager(this.targetQuarter.join(' '));
      } else {
        this.gefunddata(this.targetQuarter.join(' '));
      }
      // if(this.value2!=''){
      //     let arr = this.getymd(this.value2).split('-')
      //      // //console.log(this.getymd(this.value2))
      //      // //console.log(arr)
      //      let date = arr[0]+' Q' +Math.floor( ( arr[1] % 3 == 0 ? ( arr[1] / 3 ) : ( arr[1] / 3 + 1 ) ) );
      //      if(this.comparetype=='manager'){
      //     // this.getmanager(this.getquarter(0))
      //      this.getmanager(date)
      //      }
      //         else{
      //     this.gefunddata(date)
      //          }
      // }
      // else{
      //     if(this.comparetype=='manager'){
      //     // this.getmanager(this.getquarter(0))
      //     this.getmanager(this.getquarter(1))
      // }
      // else{
      //     this.gefunddata(this.getquarter(1))
      // }
      // }
    },
    generateQuarterList () {
      let option = [];
      let qList = ['Q1', 'Q2', 'Q3', 'Q4'];
      let pre = this.companyCreateDate;
      let now = this.FUNC.transformDate(new Date());

      let preYear = pre.slice(0, 4);
      let nowYear = now.slice(0, 4);
      let preQ = this.FUNC.dateToQuarter(pre).slice(5);
      let nowQ = this.FUNC.dateToQuarter(now).slice(5);

      let yList = Array.from({ length: nowYear - preYear + 1 }, (item, index) => (item = parseInt(preYear) + index));

      for (let y of yList) {
        let yobj = {
          value: y,
          label: y,
          children: []
        };
        if (y == preYear) {
          qList.forEach((q) => {
            if (q >= preQ) {
              yobj.children.push({ value: q, label: q });
            }
          });
        } else if (y == nowYear) {
          qList.forEach((q) => {
            if (q <= nowQ) {
              yobj.children.push({ value: q, label: q });
            }
          });
        } else {
          qList.forEach((q) => yobj.children.push({ value: q, label: q }));
        }
        option.push(yobj);
      }
      this.quarterList = option;
      var myDate = new Date();
      let month = myDate.getMonth() + 1;
      let quarter = Math.floor(month % 3 == 0 ? month / 3 : month / 3 + 1);
      if (quarter - 1 <= 0) {
        quarter = 4;
      } else {
        quarter = quarter - 1;
      }

      let temps = 0;
      if (quarter == 4) {
        temps = 1;
      }
      this.targetQuarter = [option[option.length - 1 - temps].value, option[option.length - 1 - temps].children[quarter - 1].value];
      // //console.log("object");
      // //console.log(this.targetQuarter);
      if (this.comparetype == 'manager') {
        this.getmanager(this.targetQuarter.join(' '));
      } else {
        this.gefunddata(this.targetQuarter.join(' '));
      }
      // option示例格式:
      // option = [{
      //   label: 'label',
      //   value: 'value',
      //   children: [{
      //     label: 'label',
      //     value: 'value'
      //   }]
      // }];
    },
    getymd (dateStr) {
      var d = new Date(dateStr);
      var resDate = d.getFullYear() + '-' + (d.getMonth() + 1);
      return resDate;
    },
    // 日期变化
    changgedate () {
      // let arr = this.getymd(this.value2).split('-')
      // // //console.log(this.getymd(this.value2))
      // // //console.log(arr)
      // let date = arr[0]+' Q' +Math.floor( ( arr[1] % 3 == 0 ? ( arr[1] / 3 ) : ( arr[1] / 3 + 1 ) ) );
      if (this.comparetype == 'manager') {
        // this.getmanager(this.getquarter(0))
        this.getmanager(this.targetQuarter.join(' '));
      } else {
        this.gefunddata(this.targetQuarter.join(' '));
      }
    },
    getdata () {
      Object.assign(this.$data, this.$options.data());
      this.loading = true;
      this.generateQuarterList();
      // if(this.comparetype=='manager'){
      //     this.getmanager(this.getquarter(1))
      // }
      // else{
      //     this.gefunddata(this.getquarter(1))
      // }
    },
    async getmanager (val) {
      this.arrlist = [];
      let data = await ManagerHoldStocks({
        manager_code: this.id,
        type: this.type,
        manager_name: this.name,
        swname: this.value3,
        yearqtr: val
      });
      this.loading = false;
      if (data && JSON.stringify(data.data) != '{}') {
        let dateKey = [val];
        let temp = [];
        let tempall = this.$route.query.id.split(',');
        for (let i = 0; i < data.data.length; i++) {
          if (temp.indexOf(data.data[i][0].code) < 0) {
            temp.push(data.data[i][0].code);
          }
          if (tempall.indexOf(data.data[i][0].code) < 0) {
            tempall.push(data.data[i][0].code);
          }
        }
        let t = tempall.filter((item) => !temp.includes(item));
        // console.log('xxxxxx');
        for (let k = 0; k < t.length; k++) {
          let arryT = [];
          for (let j = 0; j < data.data[0].length; j++) {
            arryT.push({
              code: t[k],
              change_weight: '--',
              level: '--',
              name: '--',
              old_weight: '--',
              stock_code: '--',
              stockcode: '--',
              swname: '--',
              times: '--',
              weight: '0',
              yearqtr: dateKey[0],
              manager_name: this.$route.query.name.split(',')[this.$route.query.id.split(',').indexOf(t[k])]
            });
          }
          data.data.push(arryT);
        }

        data.data.sort((a, b) => {
          if (this.$route.query.id.split(',').indexOf(a[0].code) > this.$route.query.id.split(',').indexOf(b[0].code)) return 1;
          else return -1;
        });
        let max = 0;
        this.arrlist = [];
        // this.arrlist = [['名称/权重'], ['较上期变化'], ['黑白马'], ['总配置次数']];
        for (let i = 0; i < data.data.length; i++) {
          data.data[i].sort((a, b) => {
            return Number(b.weight) - Number(a.weight);
          });
          if (max < data.data[i].length) max = data.data[i].length;
        }
        for (let j = 0; j < max; j++) {
          this.arrlist[j] = [['名称/权重'], ['较上期变化'], ['总配置次数']];
          for (let i = 0; i < data.data.length; i++) {
            if (data.data[i].length > j) {
              this.arrlist[j][0].push(data.data[i][j].name + '/' + (Number(data.data[i][j].weight) * 100).toFixed(2) + '%');
              this.FUNC.isEmpty(data.data[i][j].change_weight)
                ? this.arrlist[j][1].push((Number(data.data[i][j].change_weight) * 100).toFixed(2) + '%')
                : this.arrlist[j][1].push('--');
              this.FUNC.isEmpty(data.data[i][j].times) ? this.arrlist[j][2].push(data.data[i][j].times) : this.arrlist[j][2].push('--');
            } else {
              this.arrlist[j][0].push('--');
              this.arrlist[j][1].push('--');
              this.arrlist[j][2].push('--');
            }
          }
        }
      }
    },
    async gefunddata (val) {
      this.arrlist = [];
      let data = await FundHoldStocks({ fund_code: this.id, type: this.type, fund_name: this.name, swname: this.value3, yearqtr: val });
      this.loading = false;

      if (data && JSON.stringify(data.data) != '{}' && JSON.stringify(data.data) != '[]') {
        let dateKey = [val];
        let temp = [];
        let tempall = this.$route.query.id.split(',');
        for (let i = 0; i < data.data.length; i++) {
          if (temp.indexOf(data.data[i][0].code) < 0) {
            temp.push(data.data[i][0].code);
          }
          if (tempall.indexOf(data.data[i][0].code) < 0) {
            tempall.push(data.data[i][0].code);
          }
        }
        let t = tempall.filter((item) => !temp.includes(item));
        // console.log('xxxxxx');
        for (let k = 0; k < t.length; k++) {
          let arryT = [];
          for (let j = 0; j < data.data[0].length; j++) {
            arryT.push({
              code: t[k],
              change_weight: '--',
              level: '--',
              name: '--',
              old_weight: '--',
              stock_code: '--',
              stockcode: '--',
              swname: '--',
              times: '--',
              weight: '0',
              yearqtr: dateKey[0],
              manager_name: this.$route.query.name.split(',')[this.$route.query.id.split(',').indexOf(t[k])]
            });
          }
          data.data.push(arryT);
        }
        data.data.sort((a, b) => {
          if (this.$route.query.id.split(',').indexOf(a[0].code) > this.$route.query.id.split(',').indexOf(b[0].code)) return 1;
          else return -1;
        });
        let max = 0;
        this.arrlist = [];
        // this.arrlist = [['名称/权重'], ['较上期变化'], ['黑白马'], ['总配置次数']];
        for (let i = 0; i < data.data.length; i++) {
          data.data[i].sort((a, b) => {
            return Number(b.weight) - Number(a.weight);
          });
          if (max < data.data[i].length) max = data.data[i].length;
        }
        for (let j = 0; j < max; j++) {
          this.arrlist[j] = [['名称/权重'], ['较上期变化'], ['总配置次数']];
          for (let i = 0; i < data.data.length; i++) {
            if (data.data[i].length > j) {
              this.arrlist[j][0].push(data.data[i][j].name + '/' + Number(data.data[i][j].weight).toFixed(2) + '%');
              this.FUNC.isEmpty(data.data[i][j].change_weight)
                ? this.arrlist[j][1].push(Number(data.data[i][j].change_weight).toFixed(2) + '%')
                : this.arrlist[j][1].push('--');
              this.FUNC.isEmpty(data.data[i][j].times) ? this.arrlist[j][2].push(data.data[i][j].times) : this.arrlist[j][3].push('--');
            } else {
              this.arrlist[j][0].push('--');
              this.arrlist[j][1].push('--');
              this.arrlist[j][2].push('--');
            }
          }
        }
      }
    },
    getquarter (val) {
      let temp = null;
      var myDate = new Date();
      let year = myDate.getFullYear();
      let month = myDate.getMonth() + 1;
      let quarter = Math.floor(month % 3 == 0 ? month / 3 : month / 3 + 1);
      if (quarter - val - 1 <= 0) {
        return year - 1 + ' Q' + (4 + quarter - val - 1);
      } else {
        return year + ' Q' + (quarter - val - 1);
      }
    }, //
    createPrintWord () {
      let data = [];
      this.arrlist.map((item) => {
        data.push(...item);
      });
      let name = this.name.split(',');
      data.unshift(['', ...name]);
      return [...this.$exportWord.exportTitle('个股配置'), ...this.$exportWord.exportCompareTable(data, [], true)];
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () { },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
</style>
<style>
.holdindustry .el-input__inner {
	/* padding-left: 30px !important; */
}
</style>
