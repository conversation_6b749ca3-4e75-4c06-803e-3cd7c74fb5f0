<!--  -->
<template>
  <div class="filterNewResult">
    <div style="background: white; padding: 16px 24px 0px 24px">
      <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 8px">
        <div style="
						font-family: 'PingFang';
						font-style: normal;
						font-weight: 500;
						font-size: 16px;
						line-height: 24px;
						text-align: center;
						color: rgba(0, 0, 0, 0.85);
					">
          打分结果
        </div>
        <div>
          <el-button :disabled="fundlisttemp.length > 4 || fundlisttemp.length <= 1"
                     @click="gotocompare">比较</el-button>
          <el-button @click="cretezuhequ">创建基金池</el-button>
          <el-button @click="creteportfolio">创建组合</el-button>
          <el-button @click="outExcel"
                     type="">导出打分结果</el-button>
        </div>
      </div>
      <el-table :data="filterDataList"
                @sort-change="sort_change"
                class="table"
                style="min-height: calc(100vh - 673px)"
                ref="multipleTable"
                header-cell-class-name="table-header"
                @selection-change="handleSelectzuhe"
                :row-key="getRowKey">
        <el-table-column type="selection"
                         align="gotoleft"
                         :reserve-selection="true"
                         :width="55"> </el-table-column>
        <el-table-column :show-overflow-tooltip="true"
                         prop="name"
                         :min-width="240"
                         align="gotoleft"
                         label="基金名称">
          <template slot-scope="scope"><a style="border-bottom: 1px solid #4096ff"
               @click="godetail(scope.row.code, scope.row.name)">{{
							scope.row.name | isDefault
						}}</a></template>
          <!-- <template slot-scope="scope"><a style="border-bottom: 1px solid #4096ff"
                                @click="godetail(scope.row.code, scope.row.name)">{{
									scope.row.name
								}}</a></template> -->
        </el-table-column>
        <el-table-column prop="code"
                         :min-width="100"
                         label="基金代码"
                         align="gotoleft"> </el-table-column>
        <el-table-column :min-width="178"
                         prop="manager_name"
                         label="基金经理"
                         align="gotoleft">
          <template slot-scope="scope"><a style="border-bottom: 1px solid #4096ff"
               @click="godetailP(scope.row.manager_code.split(',')[0], scope.row.manager_name.split(',')[0])">{{ scope.row.manager_name.split(',')[0] | isDefault }}</a><span v-if="scope.row.manager_code.split(',').length >= 2">,<a style="border-bottom: 1px solid #4096ff"
                 @click="godetailP(scope.row.manager_code.split(',')[1], scope.row.manager_name.split(',')[1])">{{ scope.row.manager_name.split(',')[1] | isDefault }}</a></span></template>
          <!-- <template slot-scope="scope"><a style="border-bottom: 1px solid #4096ff"
                                @click="godetail(scope.row.manager_code.split(',')[0], scope.row.manager_name.split(',')[0])">{{ scope.row.manager_name.split(',')[0] }}</a><span
                                v-if="scope.row.manager_code.split(',').length >= 2">,<a
                                    style="border-bottom: 1px solid #4096ff"
                                    @click="godetail(scope.row.manager_code.split(',')[1], scope.row.manager_name.split(',')[1])">{{ scope.row.manager_name.split(',')[1] }}</a></span></template> -->
        </el-table-column>
        <el-table-column sortable="custom"
                         v-for="(item, index) in arrtablecol"
                         :min-width="150"
                         :prop="item.keyName"
                         :key="item.name + index"
                         :label="item.name"
                         align="right">
          <!-- <el-table-column  v-for="(name, i) in item.keyName" :width="getfontSize(150)" :key="i" :label="name" align="gotoleft"> -->
          <template slot="header">{{ item.name | repaceN }}</template>
          <template slot-scope="scope">
            <span v-if="item.flag == 1 || item.flag == '1'">{{ scope.row[item.keyName.replace('now,', '')] | fixxx3 }}</span>
            <span v-if="item.flag == 2"> {{ scope.row[item.keyName.replace('now,', '')] | fixxx2 }}</span>
          </template>
          <!-- </el-table-column> -->
        </el-table-column>
        <el-table-column label="总分"
                         :min-width="128"
                         sortable="custom"
                         align="right"
                         prop="final_score">
          <template slot-scope="scope">{{ scope.row.final_score | fix2px }}</template>
        </el-table-column>
        <el-table-column label="打分组"
                         :min-width="128"
                         sortable="custom"
                         align="right"
                         prop="group">
          <template slot-scope="scope">{{ scope.row.group }}</template>
        </el-table-column>
        <el-table-column prop="managedTime"
                         :min-width="100"
                         label="基金经理任职时长"
                         align="gotoleft"> </el-table-column>
        <!-- <el-table-column v-for="(item, index) in listCol"
                         :key="index"
                         :prop="item.value"
                         :show-overflow-tooltip="true"
                         :min-width="168"
                         sortable="custom"
                         :label="item.label"
                         align="gotoleft">
          <el-table-column v-show="item.children"
                           v-for="obj in item.children"
                           :key="obj.value"
                           :prop="obj.value"
                           :show-overflow-tooltip="true"
                           :min-width="168"
                           sortable="custom"
                           :label="obj.label"
                           align="gotoleft">
            <template slot-scope="scope">{{ scope.row[obj.value] | fix2pa(obj.flag, obj.value) }}</template>
          </el-table-column>
          <template v-show="!item.children"
                    slot-scope="scope">{{ scope.row[item.value] | fix2pa(item.flag, item.value) }}</template>
        </el-table-column> -->
        <el-table-column prop="1w"
                         :width="128"
                         sortable="custom"
                         label="近一周收益"
                         align="right">
          <template slot-scope="scope">{{ scope.row['1w'] | fix2p }}</template>
        </el-table-column>
        <el-table-column prop="1m"
                         :width="128"
                         sortable="custom"
                         label="近一月收益"
                         align="right">
          <template slot-scope="scope">{{ scope.row['1m'] | fix2p }}</template>
        </el-table-column>
        <el-table-column prop="1q"
                         :width="128"
                         sortable="custom"
                         label="近一季收益"
                         align="right">
          <template slot-scope="scope">{{ scope.row['1q'] | fix2p }}</template>
        </el-table-column>
        <el-table-column prop="1y"
                         :width="128"
                         sortable="custom"
                         label="近一年收益"
                         align="right">
          <template slot-scope="scope">{{ scope.row['1y'] | fix2p }}</template>
        </el-table-column>
        <el-table-column prop="yearly"
                         :min-width="128"
                         sortable="custom"
                         label="年初至今收益"
                         align="right">
          <template slot-scope="scope">{{ scope.row['yearly'] | fix2p }}</template>
        </el-table-column>
        <el-table-column v-for="(itemX) in yearList"
                         :key='itemX'
                         :prop="itemX"
                         :min-width="128"
                         sortable="custom"
                         :label="itemX+'年收益'"
                         align="right">
          <template slot-scope="scope">{{ scope.row[itemX] | fix2p }}</template>
        </el-table-column>
        <!-- <el-table-column :width="100"
                         prop="netasset"
                         sortable="custom"
                         label="规模"
                         align="right">
          <template slot-scope="scope">{{ scope.row['netasset'] | fix10Y }}亿</template>
        </el-table-column>

        <el-table-column prop="bigs"
                         :show-overflow-tooltip="true"
                         label="重仓股"
                         :min-width="328"
                         align="gotoleft"> </el-table-column> -->
      </el-table>
      <!-- <div style="margin-top: 16px; display: flex; align-items: center; justify-content: right">
				<el-pagination
					style="display: flex; align-items: center"
					background
					layout="total, sizes, prev, pager, next, jumper"
					:current-page.sync="pageIndex"
					:page-size="ps"
					@size-change="handleSizeChange"
					:page-sizes="[10, 20, 40, 60, 80, 100]"
					:total="pageTotal"
					@current-change="handlePageChange"
				></el-pagination>
			</div> -->
      <div class="flex_between mt-16">
        <div style="font-family: 'PingFang'; font-style: normal; font-weight: 400; font-size: 14px; color: rgba(0, 0, 0, 0.65)">
          共{{ pageTotal }}条数据
        </div>
        <div class="flex_start">
          <el-select v-model="ps"
                     filterable
                     allow-create
                     default-first-option
                     placeholder=""
                     style="width: 60px"
                     @change="handleSizeChange">
            <el-option v-for="item in ps_list"
                       :key="item.value"
                       :label="item.labe"
                       :value="item.value"> </el-option>
          </el-select>
          <span style="margin-left: 8px; font-size: 13px">条/页</span>
          <el-pagination @current-change="handlePageChange"
                         :current-page="pageIndex"
                         layout="prev, pager, next, jumper"
                         :total="pageTotal">
          </el-pagination>
        </div>
      </div>
    </div>
    <div style="margin-top: 16px; width: 100%; height: 1px; background: #e9e9e9"></div>
    <div style="text-align: right; margin-right: 24px; padding-top: 24px; padding-bottom: 24px"
         class="demo-drawer__footer">
      <el-button @click="changeStep2(1)"
                 type="">上一步</el-button>
    </div>
    <poolC ref="poolC"></poolC>
    <portC ref="portC"></portC>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { alphaGo } from '@/assets/js/alpha_type.js';
import poolC from '@/components/components/components/poolcreate/index.vue';
import { TypeMsg } from '@/api/pages/SystemAlpha.js';
import { filter_json_to_excel } from '@/utils/exportExcel.js';
import portC from '@/components/components/components/portfoliocreat/index.vue';
export default {
  //import引入的组件需要注入到对象中才能使用
  components: { poolC, portC },
  data () {
    //这里存放数据
    return {
      yearList: [],
      arrtablecol: [],
      filterData: [],
      filterDataList: [],
      pageIndex: 1,
      pageTotal: 0,
      ps: 10,
      ps_list: [
        {
          value: 10,
          label: 10
        },
        {
          value: 20,
          label: 20
        },
        {
          value: 50,
          label: 50
        },
        {
          value: 100,
          label: 100
        }
      ],
      listCol: [],
      field104Options: [
        {
          label: '波动率', //波动率：区间数据（默认下届为0）
          value: 'volatility'
        },
        {
          label: '最大回撤', //区间数据（默认下届为0）
          value: 'maxdrawdown'
        },
        {
          label: '平均下行周期', //区间数据（默认下届为0）
          value: 'averagelength'
        },
        {
          label: '平均恢复周期', //区间数据（默认下届为0）
          value: 'averagerecovery'
        },
        {
          label: '最大回撤比', //区间数据（默认下届为0.5，上届为1）
          value: 'maxdrawdown_ratio'
        },
        {
          label: '在险价值', //区间数据
          value: 'VaR05'
        },
        {
          label: '期望损失', //区间数据
          value: 'ES05'
        },
        {
          label: '下行风险', //区间数据
          value: 'downsidevolatility'
        },
        {
          label: '波动率比', //区间数据（默认下届为0.5，上届为1）
          value: 'volatilityratio'
        },
        {
          label: '痛苦指数',
          value: 'painindex'
        }
      ],
      field105Options: [
        {
          label: '年化收益率',
          value: 'ave_return'
        },
        {
          label: '累计收益率',
          value: 'cum_return'
        },
        {
          label: '夏普率（rf==0）',
          value: 'sharpe0'
        },
        {
          label: '夏普率（rf==4%）',
          value: 'sharpe04'
        },
        {
          label: '夏普率（动态rf）',
          value: 'sharpe'
        },
        {
          label: '卡码率',
          value: 'calmar'
        },
        {
          label: '索提诺系数（rf==0）',
          value: 'sortino0'
        },
        {
          label: '索提诺系数（rf==4%）',
          value: 'sortino04'
        },
        {
          label: '索提诺系数（动态rf）',
          value: 'sortino'
        },
        {
          label: '稳定系数',
          value: 'hurstindex'
        },
        {
          label: '凯利系数',
          value: 'kelly'
        },
        {
          label: '信息⽐率',
          value: 'information'
        },
        {
          label: '上攻潜力（周）',
          value: 'upsidepotential'
        },
        {
          label: '月胜率',
          value: 'monthly_win_ratio'
        },
        {
          label: '詹森系数',
          value: 'jensen'
        },
        {
          label: '特诺系数',
          value: 'treynor'
        },
        {
          label: '上行捕获',
          value: 'bullreturn'
        },
        {
          label: '下行捕获',
          value: 'bearreturn'
        },
        {
          label: '择时gamma',
          value: 'gamma'
        },
        {
          label: 'M2',
          value: 'msquared'
        }
      ],
      disabledfalg: true,
      fundlisttemp: [],
      listSelect: []
    };
  },
  filters: {
    fix2pa (value, type, valueType) {
      if (
        valueType.includes('cum_return') ||
        valueType.includes('ave_return') ||
        valueType.includes('hold_weight') ||
        valueType.includes('equity_weight') ||
        valueType.includes('bond_weight') ||
        valueType.includes('fund_weight') ||
        valueType.includes('option_weight') ||
        valueType.includes('repo_weight') ||
        valueType.includes('cash_weight') ||
        valueType.includes('abs_weight') ||
        valueType.includes('active_weight') ||
        valueType.includes('passive_weight') ||
        valueType.includes('other_weight') ||
        valueType.includes('cbond_weight') ||
        valueType.includes('equity_weight_avg') ||
        valueType.includes('bond_weight_avg') ||
        valueType.includes('cbond_weight_avg') ||
        valueType.includes('new_mathRange') ||
        valueType.includes('股票_weight') ||
        valueType.includes('港股_weight') ||
        valueType.includes('美股_weight') ||
        valueType.includes('台股_weight') ||
        valueType.includes('政策性金融债_weight') ||
        valueType.includes('金融债_weight') ||
        valueType.includes('国债_weight') ||
        valueType.includes('同业存单仓位_weight') ||
        valueType.includes('央行票据_weight') ||
        valueType.includes('地方政府债_weight') ||
        valueType.includes('公司债_weight') ||
        valueType.includes('短期融资券_weight') ||
        valueType.includes('中期票据_weight') ||
        valueType.includes('中小企业私募债_weight') ||
        valueType.includes('可转债_weight') ||
        valueType.includes('企业债_weight') ||
        valueType.includes('其他债券_weight') ||
        // valueType.includes('mathRange') ||
        // 基金大类资产约束
        valueType.includes('_净') ||
        valueType.includes('_总') ||
        valueType.includes('_债')
      ) {
        if (value == 'nan' || value == '--' || value == '- -' || value == '') return '--';
        else return Number(value).toFixed(2) + '%';
      } else if (
        valueType.includes('volatility') ||
        valueType.includes('maxdrawdown') ||
        valueType.includes('maxdrawdown_ratio') ||
        valueType.includes('VaR05') ||
        valueType.includes('ES05') ||
        valueType.includes('downsidevolatility') ||
        valueType.includes('volatilityratio') ||
        valueType.includes('monthly_win_ratio') ||
        valueType.includes('jensen') ||
        valueType.includes('bullreturn') ||
        valueType.includes('bearreturn') ||
        valueType.includes('msquared') ||
        valueType.includes('mean_conceration') ||
        valueType.includes('股票关注期') ||
        valueType.includes('持有股票抱团度') ||
        valueType.includes('win_ratio') ||
        valueType.includes('lose_ratio') ||
        valueType.includes('trackingerror') ||
        valueType.includes('value_rank') ||
        valueType.includes('growth_rank') ||
        valueType.includes('bp_rank') ||
        valueType.includes('momentum_rank') ||
        valueType.includes('earningyield_rank') ||
        valueType.includes('beta_rank') ||
        valueType.indexOf('买入') !== -1 ||
        valueType.indexOf('卖出') !== -1 ||
        valueType == '行业超低配' ||
        (valueType.indexOf('_weight') >= 0 &&
          valueType != 'equity_weight_chg' &&
          valueType != 'hkequity_weight_chg' &&
          valueType != 'cbond_weight_chg' &&
          valueType != 'interest_weight_chg' &&
          valueType != 'bond_weight' &&
          valueType != 'bond_weight_avg')
      ) {
        if (value == 'nan' || value == '--' || value == '- -' || value == '') return '--';
        else return (Number(value) * 100).toFixed(2) + '%';
      }

      if (valueType.indexOf('year') != -1 || valueType == 'managed_time') {
        if (value == 'nan' || value == '--' || value == '- -' || value == '') return '--年';
        else return Number(value).toFixed(2) + '年';
      }
      if (
        valueType == 'averagelength' ||
        valueType == 'averagerecovery' ||
        valueType == 'painindex' ||
        valueType == 'sharpe' ||
        valueType == 'sharpe0' ||
        valueType == 'sharpe04' ||
        valueType == 'calmar' ||
        valueType == 'sortino0' ||
        valueType == 'sortino04' ||
        valueType == 'sortino' ||
        valueType == 'hurstindex' ||
        valueType == 'kelly' ||
        valueType == 'information' ||
        valueType == 'upsidepotential' ||
        valueType == 'treynor' ||
        valueType == 'gamma'
      ) {
        if (value == 'nan' || value == '--' || value == '- -' || value == '') return '--';
        else return Number(value).toFixed(2);
      }
      if (type && type == 1) {
        if (value == 'nan' || value == '--' || value == '- -' || value == '') return '--';
        else return (Number(value) * 100).toFixed(2);
      } else if (type && type == 3) {
        if (value == 'nan' || value == '--' || value == '- -' || value == '') return '--';
        else return value;
      } else {
        if (value == 'nan' || value == '--' || value == '- -') return '--';
        else return Number(value).toFixed(2);
      }
    },
    repaceN (value) {
      if (value.indexOf('1w') >= 0) {
        return value.replace('now,1w', '/一周');
      } else if (value.indexOf('2w') >= 0) {
        return value.replace('now,2w', '/两周');
      } else if (value.indexOf('1m') >= 0) {
        return value.replace('now,1m', '/一月');
      } else if (value.indexOf('2m') >= 0) {
        return value.replace('now,2m', '/两月');
      } else if (value.indexOf('1q') >= 0) {
        return value.replace('now,1q', '/一季');
      } else if (value.indexOf('2q') >= 0) {
        return value.replace('now,2q', '/半年');
      } else if (value.indexOf('1y') >= 0) {
        return value.replace('now,1y', '/一年');
      } else if (value.indexOf('2y') >= 0) {
        return value.replace('now,2y', '/两年');
      } else if (value.indexOf('3y') >= 0) {
        return value.replace('now,3y', '/三年');
      } else if (value.indexOf('4y') >= 0) {
        return value.replace('now,4y', '/四年');
      } else if (value.indexOf('5y') >= 0) {
        return value.replace('now,5y', '/五年');
      } else return value;
    },
    fix10Y (value) {
      if (value == '--' || value == undefined || value == 'nan') return '--';
      else return (Number(value) / 100000000).toFixed(2);
    },
    fix6 (value) {
      return value.substring(0, 10);
    },
    fix3 (value) {
      return parseInt(value * 1000) / 1000;
    },
    fix2p (value) {
      if (value == '--' || value == undefined || value == 'nan') return '--';
      else return (value * 100).toFixed(2) + '%';
    },
    fix2px (value) {
      if (value == '--' || value == undefined || value == 'nan') return '--';
      else {
        return value.toFixed(2);
      }
    },
    fix2 (value) {
      if (value == '--' || value == undefined || value == 'nan') return '--';
      else return (value * 100).toFixed(2);
    },
    fixxx3 (value) {
      if (value == '--' || value == undefined || value == 'nan') return '--';
      else return Number(value).toFixed(2);
    },
    fixxx2 (value) {
      if (value == '--' || value == undefined || value == 'nan') return '--';
      else return Number(value).toFixed(2);
    }
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    getYearList () {
      const currentYear = new Date().getFullYear();
      // 生成近三年的年度数组
      const threeYearArray = [];
      for (let i = currentYear - 2; i <= currentYear; i++) {
        threeYearArray.push(i);
      }
      this.yearList = currentYear
    },
    getRowKey: function (row) {
      return row.code;
    },
    handleSelectzuhe (val) {
      if (val.length > 0) {
        this.disabledfalg = false;
      } else {
        this.disabledfalg = true;
      }

      this.fundlisttemp = val;
    },
    godetail (id, name) {
      //带参进去
      alphaGo(id, name, this.$route.path);
    },
    godetailP (id, name) {
      //带参进去
      alphaGo(id, name, this.$route.path);
    },
    repaceNX (value) {
      if (value.indexOf('1w') >= 0) {
        return value.replace('now,1w', '/一周');
      } else if (value.indexOf('2w') >= 0) {
        return value.replace('now,2w', '/两周');
      } else if (value.indexOf('1m') >= 0) {
        return value.replace('now,1m', '/一月');
      } else if (value.indexOf('2m') >= 0) {
        return value.replace('now,2m', '/两月');
      } else if (value.indexOf('1q') >= 0) {
        return value.replace('now,1q', '/一季');
      } else if (value.indexOf('2q') >= 0) {
        return value.replace('now,2q', '/半年');
      } else if (value.indexOf('1y') >= 0) {
        return value.replace('now,1y', '/一年');
      } else if (value.indexOf('2y') >= 0) {
        return value.replace('now,2y', '/两年');
      } else if (value.indexOf('3y') >= 0) {
        return value.replace('now,3y', '/三年');
      } else if (value.indexOf('4y') >= 0) {
        return value.replace('now,4y', '/四年');
      } else if (value.indexOf('5y') >= 0) {
        return value.replace('now,5y', '/五年');
      } else {
        return value;
      }
    },
    async gotocompare () {
      let tempcode = '';
      let tempname = '';
      let temptype = null;
      for (let i = 0; i < this.fundlisttemp.length; i++) {
        tempcode = tempcode + this.fundlisttemp[i].code + ',';
        tempname = tempname + this.fundlisttemp[i].name + ',';
      }
      tempcode = tempcode.slice(0, tempcode.length - 1);
      tempname = tempname.slice(0, tempname.length - 1);
      let res = await TypeMsg({ code: tempcode });
      if (res.mtycode == 200) {
        let data = res.data;
        // //console.log(data)
        if (data.data) {
          if (data.data.length == 0) {
            this.$router.push({
              path: '/fundcompareDiff',
              query: {
                id: tempcode,
                type: '',
                types: data.type.join(','),
                name: tempname
              }
            });
            // this.$message.error('请选择具有相同类型的基金进行比较');
          } else if (data.data.length == 1) {
            temptype = data.data[0];
            if (
              temptype == 'bond' ||
              temptype == 'cbond' ||
              temptype == 'purebond' ||
              temptype == 'bill' ||
              temptype.indexOf('equity') >= 0 ||
              temptype == 'obond'
            ) {
              this.$router.push({
                path: '/fundcompare',
                query: {
                  id: tempcode,
                  type: temptype,
                  name: tempname
                }
              });
            } else {
              this.$message('暂时只提供主动权益，二级债，债券类产品的比较');
            }
          } else if (data.data.length > 1) {
            this.$router.push({
              path: '/fundcompareDiff',
              query: {
                id: tempcode,
                type: '',
                types: data.type.join(','),
                name: tempname
              }
            });
            // this.$message.error('请选择具有相同类型的基金进行比较');
            // this.showitem = true
            // this.options = []
            // for(let i = 0; i < data.data.length; i++){
            //     this.options.push({value:data.data[i],label:data.data[i]})
            // }
          }
        }
      }
    },
    creteportfolio () {
      let templist = this.fundlisttemp;
      if (this.fundlisttemp.length == 0) {
        templist = this.filterData
      }
      this.$refs.portC.show(templist, 'beta');
    },
    cretezuhequ () {
      let templist = this.fundlisttemp;
      if (this.fundlisttemp.length == 0) {
        templist = this.filterData
      }
      this.listSelect = JSON.parse(this.localStorage.getItem('mty_filterListSelect')) || [];
      let formData = {};
      formData['recent_time'] = this.getTime();
      formData['isUpDown'] = this.isSame;
      formData['fund_netasset'] = [];
      formData['manager_netasset'] = [];
      formData['managed_time'] = [];
      formData['isbigforpid'] = [];
      formData['equitytype'] = [];
      formData['field103'] = [];
      formData['risk_feature'] = [];
      formData['risk_return_feature'] = [];
      formData['style'] = [];
      formData['industry'] = [];
      formData['stock_class'] = [];
      formData['index_code'] = [];
      formData['conceration'] = [];
      formData['roe'] = [];
      formData['turnover'] = [];

      for (let i = 0; i < this.listSelect.length; i++) {
        for (let j = 0; j < this.listSelect[i].data.length; j++) {
          if (this.listSelect[i].data[j].labelName == '基金规模') {
            formData['fund_netasset'].push({
              value: this.listSelect[i].data[j].dataResult[0].value,
              operation: this.listSelect[i].data[j].dataResult[0].flag
            });
          } else if (this.listSelect[i].data[j].labelName == '基金经理规模') {
            formData['manager_netasset'].push({
              value: this.listSelect[i].data[j].dataResult[0].value,
              operation: this.listSelect[i].data[j].dataResult[0].flag
            });
          } else if (this.listSelect[i].data[j].labelName == '基金经理管理经验') {
            formData['managed_time'].push({
              value: this.listSelect[i].data[j].dataResult[0].value,
              operation: this.listSelect[i].data[j].dataResult[0].flag
            });
          } else if (this.listSelect[i].data[j].labelName == '可申购金额') {
            formData['isbigforpid'].push({
              value: this.listSelect[i].data[j].dataResult[0].value,
              operation: this.listSelect[i].data[j].dataResult[0].flag
            });
          } else if (this.listSelect[i].data[j].labelName == '基金分类') {
            formData['equitytype'].push({ value: this.listSelect[i].data[j].dataResult[0].value });
          } else if (this.listSelect[i].data[j].labelName == '基金持有人特征') {
            formData['field103'].push({ value: this.listSelect[i].data[j].dataResult[0].value });
          } else if (this.field104Options.findIndex((item) => item.label == this.listSelect[i].data[j].labelName) >= 0) {
            formData['risk_feature'].push({
              value: Number(this.listSelect[i].data[j].dataResult[0].value / 100),
              name: this.field104Options[this.field104Options.findIndex((item) => item.label == this.listSelect[i].data[j].labelName)]
                .value,
              operation: this.listSelect[i].data[j].dataResult[0].flag,
              recent_time: this.listSelect[i].data[j].dataResult[0].date[1]
            });
          } else if (this.field105Options.findIndex((item) => item.label == this.listSelect[i].data[j].labelName) >= 0) {
            formData['risk_return_feature'].push({
              value: Number(this.listSelect[i].data[j].dataResult[0].value / 100),
              name: this.field105Options[this.field105Options.findIndex((item) => item.label == this.listSelect[i].data[j].labelName)]
                .value,
              operation: this.listSelect[i].data[j].dataResult[0].flag,
              recent_time: this.listSelect[i].data[j].dataResult[0].date[1]
            });
          } else if (this.listSelect[i].data[j].labelName == '成长' || this.listSelect[i].data[j].labelName == '价值') {
            formData['style'].push({
              name: this.listSelect[i].data[j].labelName,
              recent_time: this.listSelect[i].data[j].dataResult[0].yearqtr,
              rank_value: this.listSelect[i].data[j].dataResult[0].rank_value,
              value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
              operation: this.listSelect[i].data[j].dataResult[0].flag
            });
          } else if (this.listSelect[i].data[j].labelName == '估值') {
            formData['style'].push({
              name: this.listSelect[i].data[j].labelName,
              recent_time: this.listSelect[i].data[j].dataResult[0].yearqtr,
              rank_value: this.listSelect[i].data[j].dataResult[0].rank_value,
              value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
              operation: this.listSelect[i].data[j].dataResult[0].flag
            });
          } else if (this.listSelect[i].data[j].labelName == '申万一级行业') {
            formData['industry'].push({
              intersection: this.isMerge,
              industry_section: '申万(2021)',
              industry_code: this.listSelect[i].data[j].dataResult[0].industryValue,
              value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
              operation: this.listSelect[i].data[j].dataResult[0].flag
            });
          } else if (this.listSelect[i].data[j].labelName == '申万二级行业') {
            formData['industry'].push({
              intersection: this.isMerge,
              industry_section: '申万二级(2021)',
              industry_code: this.listSelect[i].data[j].dataResult[0].industryValue,
              value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
              operation: this.listSelect[i].data[j].dataResult[0].flag
            });
          } else if (this.listSelect[i].data[j].labelName == '申万三级行业') {
            formData['industry'].push({
              intersection: this.isMerge,
              industry_section: '申万三级(2021)',
              industry_code: this.listSelect[i].data[j].dataResult[0].industryValue,
              value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
              operation: this.listSelect[i].data[j].dataResult[0].flag
            });
          } else if (this.listSelect[i].data[j].labelName == '恒生一级行业') {
            formData['industry'].push({
              intersection: this.isMerge,
              industry_section: '恒生一级',
              industry_code: this.listSelect[i].data[j].dataResult[0].industryValue,
              value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
              operation: this.listSelect[i].data[j].dataResult[0].flag
            });
          } else if (this.listSelect[i].data[j].labelName == '大行业') {
            formData['industry'].push({
              intersection: this.isMerge,
              industry_section: '大行业',
              industry_code: this.listSelect[i].data[j].dataResult[0].industryValue,
              value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
              operation: this.listSelect[i].data[j].dataResult[0].flag
            });
          } else if (this.listSelect[i].data[j].labelName == '主题判断') {
            formData['stock_class'].push({
              stockclass: this.listSelect[i].data[j].dataResult[0].industryValue,
              value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
              operation: this.listSelect[i].data[j].dataResult[0].flag
            });
          } else if (this.listSelect[i].data[j].labelName == '指数匹配') {
            formData['index_code'].push({
              index_code: this.listSelect[i].data[j].dataResult[0].index_code,
              value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
              operation: this.listSelect[i].data[j].dataResult[0].flag
            });
          } else if (this.listSelect[i].data[j].labelName == '前十大集中度') {
            formData['conceration'].push({
              value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
              operation: this.listSelect[i].data[j].dataResult[0].flag
            });
          } else if (this.listSelect[i].data[j].labelName == 'ROE') {
            formData['roe'].push({
              value: this.listSelect[i].data[j].dataResult[0].value,
              operation: this.listSelect[i].data[j].dataResult[0].flag
            });
          } else if (this.listSelect[i].data[j].labelName == '换手率') {
            formData['turnover'].push({
              value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
              operation: this.listSelect[i].data[j].dataResult[0].flag
            });
          }
        }
      }
      let dataobj = {
        clicktype: true,
        ftype: 'equity',
        ismanager: false,
        formData: formData
      };
      this.$refs.poolC.show(templist, dataobj, 'beta');
    },
    getData (col, data, number) {
      let listNumber = number == 'all' ? 9999999 : number
      let tempdata = [];
      for (let i = 0; i < data.length; i++) {
        tempdata = tempdata.concat(
          data[i].value.map((item) => {
            return { ...item, group: data[i].key };
          })
        );
      }
      // console.log('object', data);
      let flag = 0;
      try {
        this.localStorage.setItem('mty_score_new_col', JSON.stringify(col));
        this.localStorage.setItem('mty_score_new_result', JSON.stringify(data));
      } catch (e) {
        // console.log('object');
        this.localStorage.setItem('mty_score_new_col', JSON.stringify([]));
        this.localStorage.setItem('mty_score_new_result', JSON.stringify([]));
        // this.$message.warning('缓存已满，无法存入');
      }
      this.arrtablecol = [];
      this.filterData = tempdata;
      this.setmarch();
      if (flag == 0) {
        this.filterData = this.filterData.sort(this.my_desc_sort('final_score')).slice(0, listNumber);
      } else {
        this.filterData = this.filterData.sort(this.my_desc_sort(tempKey[0])).slice(0, listNumber);
      }
      this.pageTotal = tempdata.length;
      this.filterDataList = this.filterData.slice((1 - 1) * this.ps, 1 * this.ps);
    },
    setmarch () {
      // 写入表格列
      this.listCol = [];
      let flag = 0;
      let tempKey = [];
      if (
        this.localStorage.getItem('mty_filter_new_result') != null &&
        this.localStorage.getItem('mty_filter_new_result') != undefined &&
        this.localStorage.getItem('mty_filter_new_result') != 'null' &&
        this.localStorage.getItem('mty_filter_new_result') != 'undefined'
      ) {
        let data = JSON.parse(this.localStorage.getItem('mty_filter_new_result'));
        if (data.length > 0) {
          for (var key in data[0]) {
            if (key.indexOf('scale') >= 0) {
            } else if (key.indexOf('index_name') >= 0) {
              this.formatColumnList({ key, label: '跟踪标的', value: 'index_name', flag: 3 });
            } else if (key.indexOf('managed_year') >= 0) {
              this.formatColumnList({ key, label: '基金经理任职时长', value: 'managed_year' });
            } else if (key.indexOf('managed_time') >= 0) {
              this.formatColumnList({ key, label: '基金经理管理经验', value: 'managed_time' });
            } else if (key.indexOf('fund_year') >= 0) {
              this.formatColumnList({ key, label: '基金存续时长', value: 'fund_year' });
            } else if (key.indexOf('largeapplyingmax') >= 0) {
              this.formatColumnList({ key, label: '申购（十万）', value: 'largeapplyingmax' });
            } else if (key.indexOf('value_rank') >= 0) {
              this.formatColumnList({ key, label: '价值', value: 'value_rank' });
            } else if (key.indexOf('growth_rank') >= 0) {
              this.formatColumnList({ key, label: '成长', value: 'growth_rank' });
            } else if (key.indexOf('bp_rank') >= 0) {
              this.formatColumnList({ key, label: '估值', value: 'bp_rank' });
            } else if (key.indexOf('momentum_rank') >= 0) {
              this.formatColumnList({ key, label: '动量', value: 'momentum_rank' });
            } else if (key.indexOf('earningyield_rank') >= 0) {
              this.formatColumnList({ key, label: '盈利', value: 'earningyield_rank' });
            } else if (key.indexOf('section_weight') >= 0) {
              let label = key.replace(/.*_mathRange_(申万(.*)_季度)_section_weight/, '$1景气度');
              this.formatColumnList({ key, label, value: key.split('_mathRange_')?.[1] });
            } else if (key.indexOf('wheel_dynamic_weight') >= 0) {
              let label = key.replace(/.*_mathRange_(.*)_申万((.*))_wheel_dynamic_weight/, '申万$2$1轮动值');
              this.formatColumnList({ key, label, value: key.split('_mathRange_')?.[1] });
            } else if (key.indexOf('FdurationReport') >= 0) {
              this.formatColumnList({ key, label: '估算报告久期', value: key, flag: 4 });
            } else if (key.indexOf('durationReport') >= 0) {
              this.formatColumnList({ key, label: '报告久期', value: key, flag: 4 });
            } else if (key.indexOf('credit_down') >= 0) {
              this.formatColumnList({ key, label: '信用挖掘', value: key, flag: 3 });
            } else if (key.indexOf('beta_rank') >= 0) {
              this.formatColumnList({ key, label: '贝塔', value: 'beta_rank' });
            } else if (key.indexOf('mean_conceration') >= 0) {
              this.formatColumnList({ key, label: '前十大集中度', value: 'mean_conceration' });
            } else if (key.indexOf('mean_turnover') >= 0) {
              this.formatColumnList({ key, label: '换手率', value: 'mean_turnover' });
            } else if (key.indexOf('行业超低配') >= 0) {
              this.formatColumnList({ key, label: '行业超低配', value: '行业超低配' });
            } else if (key.indexOf('mean_roe') >= 0) {
              this.formatColumnList({ key, label: 'ROE', value: 'mean_roe' });
            } else if (key.indexOf('股票关注期') >= 0) {
              this.formatColumnList({ key, value: '股票关注期', label: '股票关注期', flag: 1, type: 'first' });
            } else if (key.indexOf('monthly_win_ratio') >= 0) {
              this.formatColumnList({
                key,
                value: 'monthly_win_ratio',
                label: '胜率',
                flag: key.indexOf('percent') >= 0 ? 1 : 0,
                type: 'first'
              });
            } else if (key.indexOf('持有股票抱团度') >= 0) {
              this.formatColumnList({ key, label: '持有股票抱团度', value: '持有股票抱团度' });
            } else if (key.indexOf('lose_ratio') >= 0) {
              this.formatColumnList({ key, label: '赔率', value: 'lose_ratio' });
            } else if (key.indexOf('win_ratio') >= 0) {
              this.formatColumnList({ key, label: '胜率', value: 'win_ratio' });
            } else if (key.indexOf('买入') >= 0) {
              this.formatColumnList({ key, label: key, value: key, flag: 1 });
            } else if (key.indexOf('卖出') >= 0) {
              this.formatColumnList({ key, label: key, value: key, flag: 1 });
            } else if (key.indexOf('hkequity_weight_avg') >= 0) {
              this.formatColumnList({ key, label: '港股仓位中枢', value: key, flag: 4 });
            } else if (key.indexOf('hkequity_weight_chg') >= 0) {
              this.formatColumnList({ key, label: '港股仓位变化', value: key, flag: 3 });
            } else if (key.indexOf('hkequity_weight') >= 0) {
              this.formatColumnList({ key, label: '港股仓位占比', value: key, flag: 4 });
            } else if (key.indexOf('equity_weight_avg') >= 0) {
              this.formatColumnList({ key, label: 'A股仓位中枢', value: key, flag: 4 });
            } else if (key.indexOf('equity_weight_chg') >= 0) {
              this.formatColumnList({ key, label: 'A股仓位变化', value: key, flag: 3 });
            } else if (key.indexOf('equity_weight') >= 0) {
              if (key.includes('equity_weight_asset')) {
                this.formatColumnList({ key, label: '股票(总)仓位占比', value: 'equity_weight_asset', flag: 4 });
              } else {
                this.formatColumnList({ key, label: '股票(净)仓位占比', value: 'equity_weight', flag: 4 });
              }
            } else if (key.indexOf('interest_weight_avg') >= 0) {
              this.formatColumnList({ key, label: '利率债仓位中枢', value: key, flag: 4 });
            } else if (key.indexOf('interest_weight_chg') >= 0) {
              this.formatColumnList({ key, label: '利率债仓位变化', value: key, flag: 3 });
            } else if (key.indexOf('interest_weight') >= 0) {
              if (key.includes('interest_weight_asset')) {
                this.formatColumnList({ key, label: '利率债(总)仓位占比', value: 'interest_weight_asset', flag: 4 });
              } else {
                this.formatColumnList({ key, label: '利率债(净)仓位占比', value: 'interest_weight', flag: 4 });
              }
            } else if (key.indexOf('cbond_weight_avg') >= 0) {
              this.formatColumnList({ key, label: '转债仓位中枢', value: key, flag: 4 });
            } else if (key.indexOf('cbond_weight_chg') >= 0) {
              this.formatColumnList({ key, label: '转债仓位变化', value: key, flag: 3 });
            } else if (key.indexOf('cbond_weight') >= 0) {
              if (key.includes('cbond_weight_asset')) {
                this.formatColumnList({ key, label: '转债(总)仓位占比', value: 'cbond_weight_asset', flag: 4 });
              } else {
                this.formatColumnList({ key, label: '转债(净)仓位占比', value: 'cbond_weight', flag: 4 });
              }
            } else if (key.indexOf('cbond_style') >= 0) {
              this.formatColumnList({ key, label: '转债类型', value: key, flag: 3 });
            } else if (key.indexOf('bond_weight_avg') >= 0) {
              this.formatColumnList({ key, label: '债券仓位中枢', value: key, flag: 4 });
            } else if (key.indexOf('bond_weight') >= 0) {
              if (key.includes('bond_weight_asset')) {
                this.formatColumnList({ key, label: '债券(总)仓位占比', value: 'bond_weight_asset', flag: 4 });
              } else {
                this.formatColumnList({ key, label: '债券(净)仓位占比', value: 'bond_weight', flag: 4 });
              }
            } else if (key.indexOf('fund_weight') >= 0) {
              if (key.includes('fund_weight_asset')) {
                this.formatColumnList({ key, label: '基金(总)仓位占比', value: 'fund_weight_asset', flag: 4 });
              } else {
                this.formatColumnList({ key, label: '基金(净)仓位占比', value: 'fund_weight', flag: 4 });
              }
            } else if (key.indexOf('option_weight') >= 0) {
              if (key.includes('option_weight_asset')) {
                this.formatColumnList({ key, label: '权证(总)仓位占比', value: 'option_weight_asset', flag: 4 });
              } else {
                this.formatColumnList({ key, label: '权证(净)仓位占比', value: 'option_weight', flag: 4 });
              }
            } else if (key.indexOf('repo_weight') >= 0) {
              if (key.includes('repo_weight_asset')) {
                this.formatColumnList({ key, label: '质押式回购(总)仓位占比', value: 'repo_weight_asset', flag: 4 });
              } else {
                this.formatColumnList({ key, label: '质押式回购(净)仓位占比', value: 'repo_weight', flag: 4 });
              }
            } else if (key.indexOf('cash_weight') >= 0) {
              if (key.includes('cash_weight_asset')) {
                this.formatColumnList({ key, label: '现金及等价物(总)仓位占比', value: 'cash_weight_asset', flag: 4 });
              } else {
                this.formatColumnList({ key, label: '现金及等价物(净)仓位占比', value: 'cash_weight', flag: 4 });
              }
            } else if (key.indexOf('abs_weight') >= 0) {
              if (key.includes('abs_weight_asset')) {
                this.formatColumnList({ key, label: '资产支持证券(总)仓位占比', value: 'abs_weight_asset', flag: 4 });
              } else {
                this.formatColumnList({ key, label: '资产支持证券(净)仓位占比', value: 'abs_weight', flag: 4 });
              }
            } else if (key.indexOf('active_weight') >= 0) {
              if (key.includes('active_weight_asset')) {
                this.formatColumnList({ key, label: '主动投资占比(总)仓位占比', value: 'active_weight_asset', flag: 4 });
              } else {
                this.formatColumnList({ key, label: '主动投资占比(净)仓位占比', value: 'active_weight', flag: 4 });
              }
            } else if (key.indexOf('passive_weight') >= 0) {
              if (key.includes('passive_weight_asset')) {
                this.formatColumnList({ key, label: '被动投资占比(净)仓位占比', value: 'passive_weight_asset', flag: 4 });
              } else {
                this.formatColumnList({ key, label: '被动投资占比(净)仓位占比', value: 'passive_weight', flag: 4 });
              }
            } else if (key.indexOf('other_weight') >= 0) {
              if (key.includes('other_weight_asset')) {
                this.formatColumnList({ key, label: '其他占比(净)仓位占比', value: 'other_weight_asset', flag: 4 });
              } else {
                this.formatColumnList({ key, label: '其他占比(净)仓位占比', value: 'other_weight', flag: 4 });
              }
            } else if (key.includes('_净') || key.includes('_总') || key.includes('_债')) {
              if (key.includes('_净')) {
                this.formatColumnList({
                  key,
                  label: key.replace(/.*_(.*)_(.*)_.*$/, '$1占净值比'),
                  value: key.split('_mathRange_')?.[1],
                  flag: 4
                });
              } else if (key.includes('_总')) {
                this.formatColumnList({
                  key,
                  label: key.replace(/.*_(.*)_(.*)_.*$/, '$1占总资产比'),
                  value: key.split('_mathRange_')?.[1],
                  flag: 4
                });
              } else if (key.includes('_债')) {
                this.formatColumnList({
                  key,
                  label: key.replace(/.*_(.*)_(.*)_.*$/, '$1占债券比'),
                  value: key.split('_mathRange_')?.[1],
                  flag: 4
                });
              }
            } else if (key.includes('new_mathRange_') && key.includes('_weight')) {
              if (key.includes('_hold_weight')) {
                this.formatColumnList({
                  key,
                  value: key.split('_mathRange_')?.[1],
                  label: key.replace(/^.*_(.*)_.*_.*$/, '最近一期$1仓位占比'),
                  flag: flag ? flag : key.indexOf('percent') >= 0 ? 1 : 0,
                  type: 'first'
                });
              } else {
                this.formatColumnList({
                  key,
                  value: key.split('_mathRange_')?.[1],
                  label: key.replace(/^.*_(.*)_.*$/, '最近一期$1仓位占比'),
                  flag: flag ? flag : key.indexOf('percent') >= 0 ? 1 : 0,
                  type: 'first'
                });
              }
            } else if (key.includes('_mathRange_') && key.includes('_weight')) {
              if (key.includes('_hold_weight') || key.includes('_weight_全持仓') || key.includes('_weight_重仓')) {
                this.formatColumnList({ key, label: key.replace(/^.*_(.*)_.*_.*$/, '$1仓位占比'), value: key.split('_mathRange_')?.[1] });
              } else {
                this.formatColumnList({ key, label: key.replace(/^.*_(.*)_.*$/, '$1仓位占比'), value: key.split('_mathRange_')?.[1] });
              }
            } else if (key.indexOf('_weight') >= 0) {
              this.formatColumnList({ key, label: key.replace('_weight', '') + '匹配度', value: key.split('_mathRange_')?.[1] });
              flag = 1;
              tempKey.push(key);
            } else if (key.indexOf('mean_top1_conceration_rank') >= 0) {
              this.formatColumnList({ key, label: '前一大个股集中度', value: key });
            } else if (key.indexOf('mean_top3_conceration_rank') >= 0) {
              this.formatColumnList({ key, label: '前三大个股集中度', value: key });
            } else if (key.indexOf('mean_top5_conceration_rank') >= 0) {
              this.formatColumnList({ key, label: '前五大个股集中度', value: key });
            } else if (key.indexOf('_rank') >= 0) {
              if (this.field104Options.concat(this.field105Options).findIndex((item) => item.value == key.replace('_rank', '')) >= 0) {
                this.formatColumnList({
                  key,
                  label: this.field104Options.concat(this.field105Options)[
                    this.field104Options.concat(this.field105Options).findIndex((item) => item.value == key.replace('_rank', ''))
                  ].label,
                  value: key
                });
              }
            } else if (
              key != '1w' &&
              key != '1m' &&
              key != '1q' &&
              key != '1y' &&
              key != 'code' &&
              key != 'manager_code' &&
              key != 'manager_name' &&
              key != 'name' &&
              key != 'netasset'
            ) {
              if (this.field104Options.concat(this.field105Options).findIndex((item) => item.value == key) >= 0) {
                this.formatColumnList({
                  key,
                  label: this.field104Options.concat(this.field105Options)[
                    this.field104Options.concat(this.field105Options).findIndex((item) => item.value == key)
                  ].label,
                  value: key.includes('_mathRange_') ? key.split('_mathRange_')?.[1] : key
                });
              }
            }
          }
        }
        console.log(this.listCol);
        this.listCol = this.listCol;
        //合并表格

        for (let i = 0; i < this.filterData.length; i++) {
          let index = data.findIndex((item) => item.code == this.filterData[i].code);
          if (index >= 0) {
            this.filterData[i] = { ...data[index], ...this.filterData[i] };
          }
        }
      } else {
        this.listCol = [];
      }
    },
    handleSizeChange (val) {
      this.ps = val;
      this.pageIndex = 1;
      this.handlePageChange(1);
    },
    handlePageChange (val) {
      this.filterDataList = this.filterData.slice((val - 1) * this.ps, val * this.ps);
    },
    // 格式化表头
    formatColumnList ({ key, label, value, flag, type }) {
      let index = this.listCol.findIndex((item) => {
        return item.value == value;
      });
      if (index == -1) {
        if (type == 'first') {
          this.listCol.push({
            value: key,
            label: label,
            flag: flag ? flag : key.indexOf('percent') >= 0 ? 1 : 0
          });
        } else {
          if (key.includes('_mathRange')) {
            this.listCol.push({
              value: value,
              label: label,
              flag: flag ? flag : key.indexOf('percent') >= 0 ? 1 : 0,
              children: [
                {
                  label: this.formatKey(key.split('_mathRange')?.[0]),
                  value: key,
                  flag: key.indexOf('percent') >= 0 ? 1 : 0
                }
              ]
            });
          } else {
            this.listCol.push({
              value: value,
              label: label,
              flag: flag ? flag : key.indexOf('percent') >= 0 ? 1 : 0
            });
          }
        }
      } else {
        let i = this.listCol[index].children.findIndex((item) => {
          return item.value == key;
        });
        if (i == -1) {
          this.listCol[index].children.push({
            label: this.formatKey(key.split('_mathRange')?.[0]),
            value: key,
            flag: flag ? flag : key.indexOf('percent') >= 0 ? 1 : 0
          });
        }
      }
    },
    // 格式化key
    formatKey (val) {
      switch (val) {
        case 'avg':
          return '平均';
        case 'max':
          return '最大';
        case 'min':
          return '最小';
        case 'range':
          return '范围';
        case 'all':
          return '平均';
        case 'prod':
          return '平均';
      }
    },
    my_desc_sort (name) {
      if (name == 'group') {
        return function (a, b) {
          if (a[name] > b[name]) return 1;
          else return -1;
        };
      } else {
        //  ////console.log(name)
        return function (a, b) {
          if (a[name] === '--' || a[name] === 'nan' || a[name] === '- -' || b[name] === '--' || b[name] === 'nan' || b[name] === '- -') {
            if (a[name] === '--' || a[name] === 'nan' || a[name] === '- -') {
              return 1;
            } else if (b[name] === '--' || b[name] === 'nan' || b[name] === '- -') {
              return -1;
            }
          } else if (Number(a[name]) > Number(b[name])) {
            return -1;
          } else if (Number(a[name]) < Number(b[name])) {
            return 1;
          } else {
            return 0;
          }
        };
      }
    },
    my_asc_sort (name) {
      if (name == 'group') {
        return function (a, b) {
          if (a[name] < b[name]) return 1;
          else return -1;
        };
      } else {
        return function (a, b) {
          if (a[name] === '--' || a[name] === 'nan' || a[name] === '- -' || b[name] === '--' || b[name] === 'nan' || b[name] === '- -') {
            if (a[name] === '--' || a[name] === 'nan' || a[name] === '- -') {
              return 1;
            } else if (b[name] === '--' || b[name] === 'nan' || b[name] === '- -') {
              return -1;
            }
          } else if (Number(a[name]) < Number(b[name])) {
            return -1;
          } else if (Number(a[name]) > Number(b[name])) {
            return 1;
          } else {
            return 0;
          }
        };
      }
    },

    sort_change (column) {
      // ////console.log(column)
      // ////console.log('colum')
      this.pageIndex = 1; // return to the first page after sorting
      if (column.prop === 'code') {
        if (column.order === 'descending') {
          this.filterData = this.filterData.sort(this.my_desc_sort('code'));
        } else if (column.order === 'ascending') {
          this.filterData = this.filterData.sort(this.my_asc_sort('code'));
        }
      } else if (column.prop === '1y') {
        if (column.order === 'descending') {
          this.filterData = this.filterData.sort(this.my_desc_sort('1y'));
        } else if (column.order === 'ascending') {
          this.filterData = this.filterData.sort(this.my_asc_sort('1y'));
        }
      } else if (column.prop === '1m') {
        if (column.order === 'descending') {
          this.filterData = this.filterData.sort(this.my_desc_sort('1m'));
        } else if (column.order === 'ascending') {
          this.filterData = this.filterData.sort(this.my_asc_sort('1m'));
        }
      } else if (column.prop === '1q') {
        if (column.order === 'descending') {
          this.filterData = this.filterData.sort(this.my_desc_sort('1q'));
        } else if (column.order === 'ascending') {
          this.filterData = this.filterData.sort(this.my_asc_sort('1q'));
        }
      } else if (column.prop === '1w') {
        if (column.order === 'descending') {
          this.filterData = this.filterData.sort(this.my_desc_sort('1w'));
        } else if (column.order === 'ascending') {
          this.filterData = this.filterData.sort(this.my_asc_sort('1w'));
        }
      } else if (column.prop === 'netasset') {
        if (column.order === 'descending') {
          this.filterData = this.filterData.sort(this.my_desc_sort('netasset'));
        } else if (column.order === 'ascending') {
          this.filterData = this.filterData.sort(this.my_asc_sort('netasset'));
        }
      } else if (column.prop === 'long_stock_pick') {
        if (column.order === 'descending') {
          this.filterData = this.filterData.sort(this.my_desc_sort('long_stock_pick'));
        } else if (column.order === 'ascending') {
          this.filterData = this.filterData.sort(this.my_asc_sort('long_stock_pick'));
        }
      } else if (column.prop === 'short_trade') {
        if (column.order === 'descending') {
          this.filterData = this.filterData.sort(this.my_desc_sort('short_trade'));
        } else if (column.order === 'ascending') {
          this.filterData = this.filterData.sort(this.my_asc_sort('short_trade'));
        }
      } else if (column.prop === 'long_adaptive') {
        if (column.order === 'descending') {
          this.filterData = this.filterData.sort(this.my_desc_sort('long_adaptive'));
        } else if (column.order === 'ascending') {
          this.filterData = this.filterData.sort(this.my_asc_sort('long_adaptive'));
        }
      } else if (column.prop === 'long_industry_cap') {
        if (column.order === 'descending') {
          this.filterData = this.filterData.sort(this.my_desc_sort('long_industry_cap'));
        } else if (column.order === 'ascending') {
          this.filterData = this.filterData.sort(this.my_asc_sort('long_industry_cap'));
        }
      } else if (column.prop === 'long_stockclass_cap') {
        if (column.order === 'descending') {
          this.filterData = this.filterData.sort(this.my_desc_sort('long_stockclass_cap'));
        } else if (column.order === 'ascending') {
          this.filterData = this.filterData.sort(this.my_asc_sort('long_stockclass_cap'));
        }
      } else if (column.prop === 'window_score') {
        if (column.order === 'descending') {
          this.filterData = this.filterData.sort(this.my_desc_sort('window_score'));
        } else if (column.order === 'ascending') {
          this.filterData = this.filterData.sort(this.my_asc_sort('window_score'));
        }
      } else if (column.prop === 'final_score_industry') {
        if (column.order === 'descending') {
          this.filterData = this.filterData.sort(this.my_desc_sort('final_score_industry'));
        } else if (column.order === 'ascending') {
          this.filterData = this.filterData.sort(this.my_asc_sort('final_score_industry'));
        }
      } else if (column.prop === 'stockclass_hold_weight') {
        if (column.order === 'descending') {
          this.filterData = this.filterData.sort(this.my_desc_sort('stockclass_hold_weight'));
        } else if (column.order === 'ascending') {
          this.filterData = this.filterData.sort(this.my_asc_sort('stockclass_hold_weight'));
        }
      } else if (column.prop === 'industry_weight') {
        if (column.order === 'descending') {
          this.filterData = this.filterData.sort(this.my_desc_sort('industry_weight'));
        } else if (column.order === 'ascending') {
          this.filterData = this.filterData.sort(this.my_asc_sort('industry_weight'));
        }
      } else if (column.prop === 'index_weight') {
        if (column.order === 'descending') {
          this.filterData = this.filterData.sort(this.my_desc_sort('index_weight'));
        } else if (column.order === 'ascending') {
          this.filterData = this.filterData.sort(this.my_asc_sort('index_weight'));
        }
      } else if (column.prop === 'group') {
        if (column.order === 'descending') {
          this.filterData = this.filterData.sort(this.my_desc_sort('group'));
        } else if (column.order === 'ascending') {
          this.filterData = this.filterData.sort(this.my_asc_sort('group'));
        }
      } else if (column.prop === 'final_score') {
        if (column.order === 'descending') {
          this.filterData = this.filterData.sort(this.my_desc_sort('final_score'));
        } else if (column.order === 'ascending') {
          this.filterData = this.filterData.sort(this.my_asc_sort('final_score'));
        }
      } else {
        for (let i = 0; i < this.arrtablecol.length; i++) {
          if (column.prop == this.arrtablecol[i].keyName) {
            if (column.order === 'descending') {
              this.filterData = this.filterData.sort(this.my_desc_sort(this.arrtablecol[i].keyName.replace('now,', '')));
            } else if (column.order === 'ascending') {
              this.filterData = this.filterData.sort(this.my_asc_sort(this.arrtablecol[i].keyName.replace('now,', '')));
            }
          }
        }
        for (let i = 0; i < this.listCol.length; i++) {
          if (column.prop == this.listCol[i].value) {
            if (column.order === 'descending') {
              this.filterData = this.filterData.sort(this.my_desc_sort(column.prop));
            } else if (column.order === 'ascending') {
              this.filterData = this.filterData.sort(this.my_asc_sort(column.prop));
            }
          }
        }
      }
      this.filterDataList = this.filterData.slice(0, this.ps); // show only one page
    },
    getTime () {
      let radioType = JSON.parse(this.localStorage.getItem('mty_filterradioType'));
      let radioInput = JSON.parse(this.localStorage.getItem('mty_filterradioInput'));
      let flag = 0;
      if (radioType == 'latest') {
        flag = 90;
        return this.moment(Date.now() - 86400000 * flag).format('YYYY-MM-DD');
      } else if (radioType == '3') {
        flag = 3 * 365;
        return this.moment(Date.now() - 86400000 * flag).format('YYYY-MM-DD');
      } else if (radioType == '6') {
        flag = 6 * 365;
        return this.moment(Date.now() - 86400000 * flag).format('YYYY-MM-DD');
      } else if (radioType == 'radioSelf') {
        flag = 365 * Number(radioInput);
        return this.moment(Date.now() - 86400000 * flag).format('YYYY-MM-DD');
      } else if (radioType == 'created') {
        return '2000-01-01';
      } else {
        flag = 90;
        return this.moment(Date.now() - 86400000 * flag).format('YYYY-MM-DD');
      }
    },
    elcellstyle ({ row, column, rowIndex, columnIndex }) {
      let t = this.arrtablecol.length + 2;

      // ////console.log(row[0])
      if (columnIndex == 3 + t) {
        if (row['1w'] >= 0) {
          return 'color: #E85D2D;';
        } else return 'color: #20995B;';
      }
      if (columnIndex == 4 + t) {
        if (row['1m'] >= 0) {
          return 'color: #E85D2D;';
        } else return 'color: #20995B;';
      }
      if (columnIndex == 5 + t) {
        if (row['1q'] >= 0) {
          return 'color: #E85D2D;';
        } else return 'color: #20995B;';
      }
      if (columnIndex == 6 + t) {
        if (row['1y'] >= 0) {
          return 'color: #E85D2D;';
        } else return 'color: #20995B;';
      }
    },
    changeStep2 (val) {
      this.fundlisttemp = [];
      this.$refs.multipleTable.clearSelection();
      if (val == 1) {
        this.$emit('changeStep2', 1);
      } else {
        this.$emit('changeStep2', 2);
      }
    },
    outExcel () {
      let tHeader = [];
      let tHeader2 = [];
      let filterVal = [];

      tHeader = [
        { label: '基金名称', value: 'name' },
        { label: '基金经理姓名', value: 'manager_name' },
        { label: '基金代码', value: 'code' }
      ];
      for (let i = 0; i < this.arrtablecol.length; i++) {
        tHeader.push({ label: this.repaceNX(this.arrtablecol[i].name + '_分数'), value: this.arrtablecol[i].keyName.replace('now,', '') });
      }
      tHeader2 = [
        // { label: '近一周收益', value: '1w' },
        // { label: '近一月收益', value: '1m' },
        // { label: '近一季收益', value: '1q' },
        // { label: '近一年收益', value: '1y' },
        // { label: '规模', value: 'netasset' },
        // { label: '重仓股', value: 'bigs' },
        { label: '总分', value: 'final_score' },
        { label: '打分组', value: 'group' }
      ];
      let data = this.fundlisttemp.length ? this.fundlisttemp : this.filterData;
      let list = [];
      this.listCol.map((item) => {
        if (item.children?.length) {
          item.children.map((obj) => {
            list.push({
              ...obj,
              label: item.label + '(' + obj.label + ')'
            });
          });
        } else {
          list.push({ ...item });
        }
      });
      filter_json_to_excel([...tHeader, ...list, ...tHeader2], data, '打分结果');
    }

    // const { export_json_to_excel } = require('@/vendor/Export2Excel');
    // var list = [];
    // // list.push(this.dataexplain);
    // let tHeader = [];
    // let tHeader2 = [];
    // let filterVal = [];

    // tHeader = ['基金名称', '基金经理姓名', '基金代码'];
    // for (let i = 0; i < this.arrtablecol.length; i++) {
    // 	tHeader.push({ label: this.repaceNX(this.arrtablecol[i].name + '_分数'), value: this.arrtablecol[j].keyName.replace('now,', '') });
    // }
    // // for (let i = 0; i < this.listCol.length; i++) {
    // // 	tHeader.push(this.repaceNX(this.listCol[i].label));
    // // }
    // tHeader2 = ['总分', '近一周收益', '近一月收益', '近一季收益', '近一年收益', '规模', '重仓股'];
    // // ////console.log(this.colums)
    // let data = this.fundlisttemp.length ? this.fundlisttemp : this.filterData;
    // for (let i = 0; i < data.length; i++) {
    // 	list[i] = [];
    // 	list[i][0] = data[i].name;
    // 	list[i][1] = data[i].manager_name;
    // 	list[i][2] = data[i].code;
    // 	for (let j = 0; j < this.arrtablecol.length; j++) {
    // 		list[i][3 + j] = data[i][this.arrtablecol[j].keyName.replace('now,', '')];
    // 	}
    // 	for (let j = 0; j < this.listCol.length; j++) {
    // 		list[i][3 + j + this.arrtablecol.length] = data[i][this.listCol[j].value.replace('now,', '')];
    // 	}
    // 	list[i][3 + this.arrtablecol.length + this.listCol.length] = data[i]['final_score'];
    // 	list[i][4 + this.arrtablecol.length + this.listCol.length] = data[i]['1w'];
    // 	list[i][5 + this.arrtablecol.length + this.listCol.length] = data[i]['1m'];
    // 	list[i][6 + this.arrtablecol.length + this.listCol.length] = data[i]['1q'];
    // 	list[i][7 + this.arrtablecol.length + this.listCol.length] = data[i]['1y'];
    // 	list[i][8 + this.arrtablecol.length + this.listCol.length] = data[i]['netasset'];
    // 	list[i][9 + this.arrtablecol.length + this.listCol.length] = data[i].bigs;
    // }

    // // export_json_to_excel(tHeader.concat(tHeader2), list, '打分结果');
    // console.log(this.arrtablecol);
    // }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () {
    if (
      this.localStorage.getItem('mty_score_new_result') != null &&
      this.localStorage.getItem('mty_score_new_result') != undefined &&
      this.localStorage.getItem('mty_score_new_result') != 'null' &&
      this.localStorage.getItem('mty_score_new_result') != 'undefined'
    ) {
      this.$nextTick(() => {
        this.getData(
          JSON.parse(this.localStorage.getItem('mty_score_new_col')),
          JSON.parse(this.localStorage.getItem('mty_score_new_result'))
        );
      });
    }
    if (
      this.localStorage.getItem('mty_score_new_col') != null &&
      this.localStorage.getItem('mty_score_new_col') != undefined &&
      this.localStorage.getItem('mty_score_new_col') != 'null' &&
      this.localStorage.getItem('mty_score_new_col') != 'undefined'
    ) {
      this.$nextTick(() => {
        this.arrtablecol = JSON.parse(this.localStorage.getItem('mty_score_new_col'));
      });
    }
  },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
.filterNewResult {
  margin-top: 1px;
  background: white;
}
//@import url(); 引入公共css类
</style>
