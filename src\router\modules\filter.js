export default [
	{
		path: '/alphaHeader',
		component: () => import(/* webpackChunkName: "filter" */ '../../pages/filter/fund/index.vue'),
		meta: {
			title: '基金市场',
			tagShow: false,
			keepAlive: true
		}
	},
	{
		path: '/filterScoreFund',
		component: () => import(/* webpackChunkName: "filter" */ '../../pages/filter/fund/score/index.vue'),
		meta: { title: '基金打分卡', tagShow: false }
	},
	{
		path: '/alphaHeadermanager',
		component: () => import(/* webpackChunkName: "filter" */ '../../pages/filter/manager/indexInner.vue'),
		meta: { title: '基金经理', tagShow: false }
	},
	{
		path: '/dashboardcompany',
		component: () => import(/* webpackChunkName: "filter" */ '../../pages/filter/company/index.vue'),
		meta: { title: '基金公司', tagShow: false }
	},
	{
		path: '/custom',
		component: () => import(/* webpackChunkName: "filter" */ '../../pages/filter/custom/index.vue'),
		meta: { title: '基础设置', tagShow: false }
	}
];
