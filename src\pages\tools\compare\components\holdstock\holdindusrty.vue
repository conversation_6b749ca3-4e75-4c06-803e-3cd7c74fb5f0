<!--  -->
<template>
  <div v-loading="loading"
       class="holdindustry">
    <div style="display: flex; align-items: center; width: 100%; position: relative; justify-content: space-between">
      <div style="display: flex; align-items: center">
        <div class="TitltCompare">行业配置</div>
      </div>
      <div style="display: flex; align-items: center">
        <div class="block">
          <el-cascader v-model="targetQuarter"
                       :options="quarterList"
                       separator=" "
                       @change="changgedate"></el-cascader>
        </div>
        <div style="margin-left: 16px">
          <el-radio v-model="radio"
                    label="1">与上个半年报/年报比</el-radio>
          <el-radio v-model="radio"
                    label="2">与上季度比</el-radio>
        </div>
      </div>
    </div>

    <div style="margin-top: 16px">
      <div v-for="(item, index) in arrlist"
           :key="index"
           style="margin-top: 16px">
        <sTable :data="item"
                typeFlag="1"></sTable>
      </div>
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import sTable from '../SelfTable.vue';
import { ManagerSwindustryHold, FundStocksIndustry } from '@/api/pages/tools/compare.js';
export default {
  //import引入的组件需要注入到对象中才能使用
  components: { sTable },
  props: {
    comparetype: {
      type: String,
      default: 'manager' //fund
    },
    id: {
      type: String,
      default: '30189741,30441407'
    },
    type: {
      type: String,
      default: 'equity'
    },
    name: {
      type: String,
      default: '萧楠,胡昕炜'
    }
  },
  filters: {
    fix3xx (value, comparetype) {
      // //console.log(value);
      // //console.log(comparetype);
      if (comparetype == 'manager') {
        if (value == '--' || value == null || value == '' || value == 'nan' || value == 'NAN') {
          return '--';
        } else {
          return (Number(value) * 100).toFixed(2) + '%';
        }
      } else {
        if (value == '--' || value == null || value == '' || value == 'nan' || value == 'NAN') {
          return '--';
        } else {
          return Number(value).toFixed(2) + '%';
        }
      }
    },
    fix3 (value) {
      if (value == '--' || value == null || value == '' || value == 'nan' || value == 'NAN') {
        return '--';
      } else {
        return (value * 100).toFixed(2) + '%';
      }
    },
    fix2 (value) {
      return Number(value).toFixed(2) + '亿';
    }
  },
  data () {
    //这里存放数据
    return {
      loading: false,
      companyCreateDate: '2008-01-01',
      targetQuarter: '',
      quarterList: [],
      showdetailchoose: false,
      radio: '2',
      value2: '',
      arrlist: [],
      tablecolumns: [
        {
          dataIndex: 'manager_name',
          key: 'Manager_name',
          title: '基金经理'
        },
        {
          dataIndex: 'swname',
          key: 'Swname',
          title: '行业名称',
          defaultSortOrder: 'ascend',
          sorter: (a, b) => {
            if (a.swname > b.swname) {
              return 1;
            } else {
              return -1;
            }
          }
        },
        {
          dataIndex: 'yearqtr',
          key: 'Yearqtr',
          title: '季度'
        },
        {
          dataIndex: 'weight',
          key: 'Weight',
          title: '配置权重',
          sorter: (a, b) => a.weight - b.weight,
          scopedSlots: {
            customRender: 'Weight'
          }
        },
        {
          dataIndex: 'change_weight',
          key: 'Change_weight',
          title: '较上期变化',
          sorter: (a, b) => a.change_weight - b.change_weight,
          scopedSlots: {
            customRender: 'Change_weight'
          }
        },
        {
          dataIndex: 'excess',
          key: 'Excess',
          title: '行业配置超额收益',
          scopedSlots: {
            customRender: 'Excess'
          }
        }
      ],
      tablecolumnsf: [
        {
          dataIndex: 'fund_name',
          key: 'fund_name',
          title: '基金名称'
        },
        {
          dataIndex: 'swname',
          key: 'Swname',
          title: '行业名称',
          defaultSortOrder: 'ascend',
          sorter: (a, b) => {
            if (a.swname > b.swname) {
              return 1;
            } else {
              return -1;
            }
          }
        },
        {
          dataIndex: 'yearqtr',
          key: 'yearqtr',
          title: '季度'
        },
        {
          dataIndex: 'weight',
          key: 'Weight',
          title: '配置权重',
          sorter: (a, b) => a.weight - b.weight,
          scopedSlots: {
            customRender: 'Weight'
          }
        },
        {
          dataIndex: 'change_weight',
          key: 'Change_weight',
          title: '较上期变化',
          sorter: (a, b) => a.change_weight - b.change_weight,
          scopedSlots: {
            customRender: 'Change_weight'
          }
        },
        {
          dataIndex: 'excess',
          key: 'Excess',
          title: '行业配置超额收益',
          scopedSlots: {
            customRender: 'Excess'
          }
        }
      ]
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {
    radio (val) {
      // if(this.value2=='') {
      if (this.comparetype == 'manager') {
        this.getmanager(this.targetQuarter.join(' '));
      } else {
        this.gefunddata(this.targetQuarter.join(' '));
      }
    }
  },
  //方法集合
  methods: {
    generateQuarterList () {
      let option = [];
      let qList = ['Q1', 'Q2', 'Q3', 'Q4'];
      let pre = this.companyCreateDate;
      let now = this.FUNC.transformDate(new Date());

      let preYear = pre.slice(0, 4);
      let nowYear = now.slice(0, 4);
      let preQ = this.FUNC.dateToQuarter(pre).slice(5);
      let nowQ = this.FUNC.dateToQuarter(now).slice(5);

      let yList = Array.from({ length: nowYear - preYear + 1 }, (item, index) => (item = parseInt(preYear) + index));

      for (let y of yList) {
        let yobj = {
          value: y,
          label: y,
          children: []
        };
        if (y == preYear) {
          qList.forEach((q) => {
            if (q >= preQ) {
              yobj.children.push({ value: q, label: q });
            }
          });
        } else if (y == nowYear) {
          qList.forEach((q) => {
            if (q <= nowQ) {
              yobj.children.push({ value: q, label: q });
            }
          });
        } else {
          qList.forEach((q) => yobj.children.push({ value: q, label: q }));
        }
        option.push(yobj);
      }
      this.quarterList = option;
      var myDate = new Date();
      let month = myDate.getMonth() + 1;
      let quarter = Math.floor(month % 3 == 0 ? month / 3 : month / 3 + 1);
      if (quarter - 1 <= 0) {
        quarter = 4;
      } else {
        quarter = quarter - 1;
      }
      let temps = 0;
      if (quarter == 4) {
        temps = 1;
      }
      this.targetQuarter = [option[option.length - 1 - temps].value, option[option.length - 1 - temps].children[quarter - 1].value];
      // //console.log("object");
      // //console.log(this.targetQuarter);
      if (this.comparetype == 'manager') {
        this.getmanager(this.targetQuarter.join(' '));
      } else {
        this.gefunddata(this.targetQuarter.join(' '));
      }
    },
    getymd (dateStr) {
      var d = new Date(dateStr);
      var resDate = d.getFullYear() + '-' + (d.getMonth() + 1);
      return resDate;
    },
    // 日期变化
    changgedate () {
      if (this.comparetype == 'manager') {
        this.getmanager(this.targetQuarter.join(' '));
      } else {
        this.gefunddata(this.targetQuarter.join(' '));
      }
    },
    getdata () {
      Object.assign(this.$data, this.$options.data());
      this.loading = true;
      this.generateQuarterList();
    },
    async getmanager (val) {
      this.loading = true;
      this.arrlist = [];
      let data = await ManagerSwindustryHold({
        manager_code: this.id,
        type: this.type,
        manager_name: this.name,
        flag: this.radio,
        yearqtr: val
      });
      this.loading = false;
      if (data && JSON.stringify(data.data) != '{}') {
        let dateKey = [val];
        let temp = [];
        let tempall = this.$route.query.id.split(',');
        for (let i = 0; i < data.data.length; i++) {
          if (temp.indexOf(data.data[i][0].code) < 0) {
            temp.push(data.data[i][0].code);
          }
          if (tempall.indexOf(data.data[i][0].code) < 0) {
            tempall.push(data.data[i][0].code);
          }
        }
        let t = tempall.filter((item) => !temp.includes(item));
        // console.log('xxxxxx');
        for (let k = 0; k < t.length; k++) {
          let arryT = [];
          for (let j = 0; j < data.data[0].length; j++) {
            arryT.push({
              code: t[k],
              change_weight: '--',
              excess: '--',
              industry_code: '--',
              old_weight: '--',
              quarter: '--',
              swname: '--',
              weight: '0',
              year: '--',
              yearqtr: dateKey[0],
              manager_name: this.$route.query.name.split(',')[this.$route.query.id.split(',').indexOf(t[k])]
            });
          }
          data.data.push(arryT);
        }

        data.data.sort((a, b) => {
          if (this.$route.query.id.split(',').indexOf(a[0].code) > this.$route.query.id.split(',').indexOf(b[0].code)) return 1;
          else return -1;
        });
        let max = 0;
        this.arrlist = [];
        // this.arrlist = [['名称/权重'], ['较上期变化'], ['黑白马'], ['总配置次数']];
        for (let i = 0; i < data.data.length; i++) {
          data.data[i].sort((a, b) => {
            return Number(b.weight) - Number(a.weight);
          });
          if (max < data.data[i].length) max = data.data[i].length;
        }
        for (let j = 0; j < max; j++) {
          this.arrlist[j] = [['名称/权重'], ['较上期变化'], ['行业配置超额收益']];
          for (let i = 0; i < data.data.length; i++) {
            if (data.data[i].length > j) {
              this.arrlist[j][0].push(data.data[i][j].swname + '/' + (Number(data.data[i][j].weight) * 100).toFixed(2) + '%');
              this.FUNC.isEmpty(data.data[i][j].change_weight)
                ? this.arrlist[j][1].push((Number(data.data[i][j].change_weight) * 100).toFixed(2) + '%')
                : this.arrlist[j][1].push('--');
              this.FUNC.isEmpty(data.data[i][j].excess)
                ? this.arrlist[j][2].push((Number(data.data[i][j].excess) * 100).toFixed(2) + '%')
                : this.arrlist[j][2].push('--');
            } else {
              this.arrlist[j][0].push('--');
              this.arrlist[j][1].push('--');
              this.arrlist[j][2].push('--');
            }
          }
        }
      }
      //  //console.log(this.arrlist)
    },
    async gefunddata (val) {
      this.loading = true;
      this.arrlist = [];
      let data = await FundStocksIndustry({ fund_code: this.id, type: this.type, fund_name: this.name, flag: this.radio, yearqtr: val });
      this.loading = false;
      if (data && JSON.stringify(data.data) != '{}') {
        let dateKey = [val];
        let temp = [];
        let tempall = this.$route.query.id.split(',');
        for (let i = 0; i < data.data.length; i++) {
          if (temp.indexOf(data.data[i][0].code) < 0) {
            temp.push(data.data[i][0].code);
          }
          if (tempall.indexOf(data.data[i][0].code) < 0) {
            tempall.push(data.data[i][0].code);
          }
        }
        let t = tempall.filter((item) => !temp.includes(item));
        // console.log('xxxxxx');
        for (let k = 0; k < t.length; k++) {
          let arryT = [];
          for (let j = 0; j < data.data[0].length; j++) {
            arryT.push({
              code: t[k],
              change_weight: '--',
              excess: '--',
              industry_code: '--',
              old_weight: '--',
              quarter: '--',
              swname: '--',
              weight: '0',
              year: '--',
              yearqtr: dateKey[0],
              manager_name: this.$route.query.name.split(',')[this.$route.query.id.split(',').indexOf(t[k])]
            });
          }
          data.data.push(arryT);
        }
        data.data.sort((a, b) => {
          if (this.$route.query.id.split(',').indexOf(a[0].code) > this.$route.query.id.split(',').indexOf(b[0].code)) return 1;
          else return -1;
        });
        let max = 0;
        this.arrlist = [];
        // this.arrlist = [['名称/权重'], ['较上期变化'], ['黑白马'], ['总配置次数']];
        for (let i = 0; i < data.data.length; i++) {
          data.data[i].sort((a, b) => {
            return Number(b.weight) - Number(a.weight);
          });
          if (max < data.data[i].length) max = data.data[i].length;
        }
        for (let j = 0; j < max; j++) {
          this.arrlist[j] = [['名称/权重'], ['较上期变化'], ['行业配置超额收益']];
          for (let i = 0; i < data.data.length; i++) {
            if (data.data[i].length > j) {
              this.arrlist[j][0].push(data.data[i][j].swname + '/' + Number(data.data[i][j].weight).toFixed(2) + '%');
              this.FUNC.isEmpty(data.data[i][j].change_weight)
                ? this.arrlist[j][1].push(Number(data.data[i][j].change_weight).toFixed(2) + '%')
                : this.arrlist[j][1].push('--');
              this.FUNC.isEmpty(data.data[i][j].excess)
                ? this.arrlist[j][2].push((Number(data.data[i][j].excess) * 100).toFixed(2) + '%')
                : this.arrlist[j][2].push('--');
            } else {
              this.arrlist[j][0].push('--');
              this.arrlist[j][1].push('--');
              this.arrlist[j][2].push('--');
            }
          }
        }
      }
    },
    getquarter (val) {
      let temp = null;
      var myDate = new Date();
      let year = myDate.getFullYear();
      let month = myDate.getMonth() + 1;
      let quarter = Math.floor(month % 3 == 0 ? month / 3 : month / 3 + 1);
      if (quarter - val - 1 <= 0) {
        return year - 1 + ' Q' + (4 + quarter - val - 1);
      } else {
        return year + ' Q' + (quarter - val - 1);
      }
    }, //
    createPrintWord () {
      let data = [];
      this.arrlist.map((item) => {
        data.push(...item);
      });
      let name = this.name.split(',');
      data.unshift(['', ...name]);
      return [...this.$exportWord.exportTitle('行业配置'), ...this.$exportWord.exportCompareTable(data, [], true)];
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () { },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
</style>
<style>
.holdindustry .el-input__inner {
	/* padding-left: 30px !important; */
}
</style>
