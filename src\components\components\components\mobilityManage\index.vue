<template>
	<div>
		<analysis-card-title title="短期流动性管理" @downloadExcel="exportExcel"></analysis-card-title>
		<div style="width: 100%" v-loading="loading">
			<el-table :data="table" class="table" ref="multipleTable" max-height="400px" stripe header-cell-class-name="table-header">
				<el-table-column v-for="item in column" :key="item.value" :prop="item.value" :label="item.label" sortable align="gotoleft">
					<template slot-scope="{ row }">
						{{ item.format ? item.format(row[item.value]) : row[item.value] }}
					</template>
				</el-table-column>
				<template slot="empty">
					<el-empty></el-empty>
				</template>
			</el-table>
		</div>
	</div>
</template>

<script>
import { exportTitle, exportTable } from '@/utils/exportWord.js';
import { filter_json_to_excel } from '@/utils/exportExcel.js';
// 短期流动性管理
import { getFofLiquidity } from '@/api/pages/Analysis.js';
export default {
	data() {
		//这里存放数据
		return {
			table: [],
			notesData: {
				ldx001: ''
			},
			show: true,
			info: {},
			loading: false,
			column: [
				{ label: '报告期', value: 'yearqtr' },
				{ label: '货币及货币基金占净值比', value: 'money_weight', format: this.fixp },
				{ label: '短债基金占净值比', value: 'bill_weight', format: this.fixp }
			]
		};
	},
	methods: {
		// 短期流动性管理
		async getFofLiquidity() {
			this.loading = true;
			let data = await getFofLiquidity({
				code: this.info.code,
				type: this.info.type,
				flag: this.info.flag,
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			this.loading = false;
			if (data?.mtycode == 200) {
				if (data?.data?.money?.length || data?.data?.bill?.length) {
					let yearqte_list = Array.from(
						new Set([...data?.data?.money.map((v) => v.yearqtr), ...data?.data?.bill.map((v) => v.yearqtr)])
					).sort((a, b) => {
						return this.moment(this.moment(a, 'YYYY QQ').format()).isAfter(this.moment(b, 'YYYY QQ').format()) ? -1 : 1;
					});
					let table = [];
					yearqte_list.map((item) => {
						let index = table.findIndex((v) => v.yearqtr == item);
						if (index == -1) {
							let money = data?.data?.money?.find((v) => v.yearqtr == item);
							let bill = data?.data?.bill?.find((v) => v.yearqtr == item);
							table.push({ yearqtr: item, money_weight: money?.weight || '数据缺失', bill_weight: bill?.weight || '数据缺失' });
						}
					});
					this.table = table;
				} else {
					this.table = [];
				}
			}
		},
		getData(info) {
			this.info = info;
			this.getFofLiquidity();
		},
		hideLoading() {
			this.show = false;
		},
		fixp(val) {
			return val * 1 && !isNaN(val) ? (val * 1).toFixed(2) + '%' : val || '--';
		},
		exportExcel() {
			let list = this.column.map((v) => {
				return {
					label: v.label,
					value: v.value
				};
			});
			let data = this.table;
			filter_json_to_excel(list, data, '短期流动性管理');
		},
		createPrintWord() {
			let list = this.column.map((v) => {
				return {
					label: v.label,
					value: v.value
				};
			});
			let data = this.table.map((item) => {
				for (const key in item) {
					let format = this.column.find((v) => v.value == key)?.format;
					let obj = { ...item };
					if (format) {
						obj[key] = format(item[key]);
					}
					return obj;
				}
			});

			return data?.length ? [...exportTitle('短期流动性管理'), ...exportTable(list, data)] : [];
		}
	}
};
</script>
