<template>
  <div class="chart_one"
       v-show="show"
       style="overflow: auto;width:100%">
    <div class="flex_between mb-16">
      <div class="title">{{ name }}</div>
      <div>
        <i class="el-icon-download"
           @click="exportImage"
           v-show="downloadShow"></i>
      </div>
    </div>
    <div v-if="showChart == 0"
         :id="name + 'dom'"
         :style="tableStyle"
         class="dom"></div>
    <div v-else-if="showChart == 1"
         :style="tableStyle"
         class="charts_fill_class">
      <v-chart ref="charts"
               :options="option"
               :lazy-update="true"
               element-loading-text="正在拼命绘制图形,请稍后..."
               element-loading-spinner="el-icon-loading"
               element-loading-background="rgba(239, 239, 239, 0.5)"
               class="charts_one_class"
               :style="`width:${width};height:600px`"
               autoresize />
    </div>
    <el-table v-else
              :data="data"
              :style="tableStyle">
      <el-table-column v-for="item in column"
                       :key="item.value"
                       :prop="item.value"
                       :label="item.label"
                       :min-width="item.width"
                       align="gotoleft">
        <template #header>
          <long-table-popover-chart v-if="item.popover"
                                    :data="formatTableData()"
                                    date_key="code"
                                    :data_key="item.value"
                                    :show_name="item.label">
            <div>{{ item.label }}</div>
          </long-table-popover-chart>
          <div v-else>{{ item.label }}</div>
        </template>
        <template slot-scope="{ row }">
          <div v-if="item.value == 'name'"
               class="overflow_ellipsis flex_start">
            <div v-show="row['flag'] == 2"
                 :style="`width:12px;height:12px;background:${row['color']}`"
                 class="mr-8"></div>
            <div v-show="row['flag'] == 1"
                 class="flex_start">
              <div :style="`width:12px;height:12px;background:#4096ff`"
                   class="mr-8"></div>
              <div class="mr-8 flex_start">
                <svg width="14"
                     height="14"
                     viewBox="0 0 14 14"
                     fill="none"
                     xmlns="http://www.w3.org/2000/svg">
                  <path d="M12.4156 4.82735L8.94433 4.32286L7.39258 1.17696C7.35019 1.09083 7.28047 1.0211 7.19433 0.978716C6.97832 0.872075 6.71582 0.960943 6.60781 1.17696L5.05605 4.32286L1.58476 4.82735C1.48906 4.84102 1.40156 4.88614 1.33457 4.9545C1.25358 5.03774 1.20895 5.14973 1.21049 5.26586C1.21203 5.38199 1.25961 5.49276 1.34277 5.57383L3.8543 8.02247L3.26094 11.4801C3.24702 11.5605 3.25592 11.6432 3.28663 11.7189C3.31733 11.7945 3.36862 11.86 3.43466 11.908C3.50071 11.9559 3.57887 11.9845 3.66029 11.9903C3.74171 11.9961 3.82313 11.9789 3.89531 11.9408L7.00019 10.3084L10.1051 11.9408C10.1898 11.9859 10.2883 12.001 10.3826 11.9846C10.6205 11.9436 10.7805 11.718 10.7395 11.4801L10.1461 8.02247L12.6576 5.57383C12.726 5.50684 12.7711 5.41934 12.7848 5.32364C12.8217 5.08438 12.6549 4.8629 12.4156 4.82735Z"
                        fill="#FFD600" />
                </svg>
              </div>
            </div>
            <el-link @click="alphaGo(row['code'], row['name'])">{{ row[item.value] }}</el-link>
          </div>
          <div v-else>{{ item.format ? item.format(row[item.value]) : row[item.value] }}</div>
        </template>
      </el-table-column>
    </el-table>
    <!-- <div :id="name + 'capabilityStatistics'" v-loading="loading">
			<template v-for="(y, yi) in y_list">
				<div :key="yi" style="width: fit-content; min-width: 100%">
					<div :key="yi" style="height: 6px"></div>
					<div :key="yi" style="position: relative; height: 20px" class="flex_between">
						<div class="x_split_line"></div>
						<div style="width: 33px; position: relative" class="mr-8">
							<span v-show="y.show">{{ y.label }}</span>
						</div>
						<div v-for="(item, xi) in data_list" :key="xi" style="flex: 1; min-width: 150px" class="flex_start">
							<div v-if="item.industry == x_list[xi]" class="flex_start">
								<div v-for="r in item.y_list" :key="r.rank" class="flex_start">
									<div
										class="td-background flex_start"
										v-show="r.rank == y.label"
										v-for="(v, i) in r.name_list.slice(0, 10)"
										:key="v.name"
										:style="
											v.flag == 0
												? `z-index:${100 - i};cursor: pointer`
												: v.color
												? `color:${v.color};background:${v.color};z-index:${100 - i};cursor: pointer;border:none`
												: `z-index:${100 - i};cursor: pointer;border:none;background-color:transparent`
										"
									>
										<el-tooltip class="item" effect="dark" :content="v.name" placement="top-start">
											<span v-if="v.flag != 1">{{ v.name.slice(0, 1) }}</span>
											<div v-else class="flex_start">
												<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
													<path
														d="M12.4156 4.82735L8.94433 4.32286L7.39258 1.17696C7.35019 1.09083 7.28047 1.0211 7.19433 0.978716C6.97832 0.872075 6.71582 0.960943 6.60781 1.17696L5.05605 4.32286L1.58476 4.82735C1.48906 4.84102 1.40156 4.88614 1.33457 4.9545C1.25358 5.03774 1.20895 5.14973 1.21049 5.26586C1.21203 5.38199 1.25961 5.49276 1.34277 5.57383L3.8543 8.02247L3.26094 11.4801C3.24702 11.5605 3.25592 11.6432 3.28663 11.7189C3.31733 11.7945 3.36862 11.86 3.43466 11.908C3.50071 11.9559 3.57887 11.9845 3.66029 11.9903C3.74171 11.9961 3.82313 11.9789 3.89531 11.9408L7.00019 10.3084L10.1051 11.9408C10.1898 11.9859 10.2883 12.001 10.3826 11.9846C10.6205 11.9436 10.7805 11.718 10.7395 11.4801L10.1461 8.02247L12.6576 5.57383C12.726 5.50684 12.7711 5.41934 12.7848 5.32364C12.8217 5.08438 12.6549 4.8629 12.4156 4.82735Z"
														fill="#FFD600"
													/>
												</svg>
											</div>
										</el-tooltip>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</template>

			<div style="position: relative" class="flex_between mt-16">
				<div></div>
				<div style="width: 33px; position: relative; margin-top: -26px" class="mr-8"><span>0%</span></div>
				<div v-for="(x, xi) in x_list" :key="'a' + xi" style="flex: 1; min-width: 150px" class="x_axis_line">
					<div class="pt-8">
						<span style="display: inline-block">{{ x }}</span>
					</div>
				</div>
			</div>
    </div>-->
  </div>
</template>

<script>
import { alphaGo } from "@/assets/js/alpha_type.js";
export default {
  props: {
    name: {
      type: String
    }
  },
  data () {
    return {
      show: true,
      downloadShow: true,
      y_list: [
        { label: "100%", show: true },
        { label: "95%", show: false },
        { label: "90%", show: false },
        { label: "85%", show: false },
        { label: "80%", show: true },
        { label: "75%", show: false },
        { label: "70%", show: false },
        { label: "65%", show: false },
        { label: "60%", show: true },
        { label: "55%", show: false },
        { label: "50%", show: false },
        { label: "45%", show: false },
        { label: "40%", show: true },
        { label: "35%", show: false },
        { label: "30%", show: false },
        { label: "25%", show: false },
        { label: "20%", show: true },
        { label: "15%", show: false },
        { label: "10%", show: false },
        { label: "5%", show: false }
      ],
      x_list: [],
      data_list: [],
      loading: true,
      option: {},
      width: "100%",
      showChart: 0,
      data: [],
      column: []
    };
  },
  computed: {
    tableStyle () {
      const screenWidth = window.innerWidth;
      let tableWidth = 1220; // 默认宽度

      // 根据不同的分辨率调整表格宽度
      if (screenWidth > 1920) {
        tableWidth = 1795; // 大于1920时宽度为1660px
      } else if (screenWidth >= 1360 && screenWidth <= 1920) {
        // 在1360和1920之间时，根据分辨率动态计算宽度在1160px到1660px之间
        tableWidth = 1220 + (screenWidth - 1360) * ((1795 - 1220) / (1920 - 1360));
      }

      return {
        width: `${tableWidth}px`,
      };
    },
  },
  methods: {
    getData (data, info) {
      if (this.FUNC.isEmpty(data)) {
        this.show = true;
      } else {
        this.show = false;
      }
      this.loading = false;
      this.x_list = data.map(item => {
        return item.industry_name;
      });
      this.data_list = data.map(item => {
        return { industry: item.industry_name };
      });
      data.map(item => {
        let index = this.data_list.findIndex(obj => {
          return obj.industry == item.industry_name;
        });
        if (index != -1) {
          let y_list = [];
          item.children.map(obj => {
            let i = y_list.findIndex(v => {
              return (
                v.rank ==
                this.findNearestNumber(
                  obj.rank * 100,
                  this.y_list.map(item => {
                    return item.label.split("%")?.[0] * 1;
                  })
                )
              );
            });
            if (i == -1) {
              y_list.push({
                rank: this.findNearestNumber(
                  obj.rank * 100,
                  this.y_list.map(y => {
                    return y.label.split("%")?.[0] * 1;
                  })
                ),
                name_list: [
                  {
                    code: obj.code,
                    name: obj.name,
                    color: obj.color,
                    flag: obj.flag
                  }
                ]
              });
            } else {
              y_list[i].name_list.push({
                code: obj.code,
                name: obj.name,
                color: obj.color,
                flag: obj.flag
              });
            }
          });
          this.data_list[index]["y_list"] = y_list.map(y => {
            return {
              ...y,
              name_list: y.name_list.sort((a, b) => {
                const sortOrder = [1, 2, 0];
                return sortOrder.indexOf(a.flag) - sortOrder.indexOf(b.flag);
              })
            };
          });
        }
      });
      if (info.code_list.length > 50) {
        this.showChart = 1;
        this.formatMoreChart();
      } else {
        if (info.code_list.length > 5) {
          this.showChart = 0;
          this.setDom();
        } else {
          this.showChart = 2;
          this.mateColumn(data);
        }
      }
    },
    mateColumn (data) {
      this.column = this.x_list.map(v => {
        return { label: v, value: v, format: this.fix2p, popover: true };
      });
      this.column.unshift({ label: "名称", value: "name", width: "200px" });
      this.data = [];
      data.map(item => {
        item.children.map(obj => {
          let index = this.data.findIndex(v => v.code == obj.code);
          if (index == -1) {
            let res = {
              ...obj
            };
            res[item.industry_name] = obj.rank;
            this.data.push(res);
          } else {
            this.data[index][item.industry_name] = obj.rank;
          }
        });
      });
      console.log(this.data);
    },
    formatTableData () {
      let data = [];
      this.data.map(item => {
        let obj = { ...item };
        for (const key in item) {
          let format = this.column.find(v => {
            return v.value == key;
          })?.format;
          if (format) {
            let val = format(item[key]);
            obj[key] =
              typeof val == "string"
                ? val.includes("%")
                  ? val?.split("%")?.[0] * 1
                  : !isNaN(val)
                    ? val * 1
                    : val
                : val;
          } else {
            let index = this.column.findIndex(v => key.indexOf(v.value) != -1);
            if (index != -1) {
              let formatter = this.column[index].children?.find(
                v => v.value == key
              )?.format;
              if (formatter) {
                let val = formatter(item[key]);
                obj[key] =
                  typeof val == "string"
                    ? val.includes("%")
                      ? val?.split("%")?.[0] * 1
                      : !isNaN(val)
                        ? val * 1
                        : val
                    : val;
              }
            }
          }
        }
        data.push(obj);
      });
      return data;
    },
    fix2p (val) {
      return val * 1 && !isNaN(val) ? (val * 100).toFixed(2) + "%" : "--";
    },
    alphaGo (code, name) {
      if (this.data_type == "pool") {
        this.$router.push({
          path: "/poolDetail/" + code,
          hash: "",
          query: {
            id: code,
            name: this.info.code_list.find(v => v.code == code).name,
            user_id: this.info.user_id,
            isdb: 0,
            isChildren: 1
          }
        });
      } else {
        alphaGo(code, name, this.$route.path);
      }
    },
    // 计算rnak与坐标轴距离匹配度
    findNearestNumber (target, numbers) {
      let closest = numbers[0];
      let closestDistance = Math.abs(target - closest);

      for (let i = 1; i < numbers.length; i++) {
        let distance = Math.abs(target - numbers[i]);

        if (distance < closestDistance) {
          closest = numbers[i];
          closestDistance = distance;
        }
      }
      return closest + "%";
    },
    formatMoreChart () {
      this.width = this.data_list.length * 200 + "px";
      let data = this.data_list.map(item => {
        let num = 0;
        item.y_list.map(o => {
          num = num + o.name_list.length;
        });
        return {
          ...item,
          y_list: item.y_list.map(v => {
            return {
              ...v,
              data: v.name_list.length / num
            };
          })
        };
      });

      this.filterOption(data);
    },
    filterOption (date_list) {
      // date_list = [date_list[0], date_list[1]];
      let columns = this.y_list.map(v => v.label);
      columns.push("0%");
      columns.reverse();
      let grid = [];
      let xAxis = [];
      let yAxis = [];
      let series = [];
      let scatter = [];
      date_list.map((item, index) => {
        // grid
        let ave = 100 / date_list.length;
        grid.push({
          containLabel: true,
          left: ave * index + "%",
          right: ave * (date_list.length - 1 - index) + "%"
        });
        // x轴
        xAxis.push({
          // name: item.industry,
          // nameLocation: 'start',
          axisLabel: { show: false },
          z: 2,
          type: "value",
          gridIndex: index,
          // name: '(元/㎡)',
          nameTextStyle: {
            color: "#999"
            // padding: [0, -14 * (item.industry.length + 1)]
          },
          axisLine: {
            show: false
          },
          min: 0,
          max: 100,
          axisTick: {
            show: false
          },
          splitLine: {
            lineStyle: {
              color: ["#EEE"]
            }
          }
        });
        // y轴
        yAxis.push({
          name: item.industry,
          axisLabel: { show: index == 0 ? true : false, color: "#999" },
          gridIndex: index,
          type: "category",
          data: columns,
          boundaryGap: true,
          nameGap: 30,
          splitArea: {
            show: false
          },
          splitLine: {
            lineStyle: {
              type: "dashed"
            }
          },
          axisLine: {
            lineStyle: {
              color: ["#ccc"],
              width: 2
            }
          },
          axisTick: {
            show: false
          }
        });
        series.push({
          name: "占比",
          type: "bar",
          label: {
            show: true,
            color: "#ccc",
            position: "right",
            formatter: function (val) {
              return val.value == 0 ? "" : val.value.toFixed(2) + "%";
            }
          },
          itemStyle: {
            color: "#4096ff1A",
            borderColor: "#4096ff"
          },
          xAxisIndex: index,
          yAxisIndex: index,
          tooltip: {
            formatter: "{b}分位{a}: {c}%"
          },
          data: columns.map(v => {
            let i = item.y_list.findIndex(o => o.rank == v);
            if (i == -1) {
              return { name: v, value: 0 };
            } else {
              return {
                name: v,
                value: (item.y_list[i].data * 100).toFixed(2) * 1
              };
            }
          })
        });
        item.y_list.map(obj => {
          obj.name_list.map((tem, n) => {
            if (tem.flag != 0) {
              let i = scatter.findIndex(v => v.name == item.industry);
              if (i == -1) {
                scatter.push({
                  name: item.industry,
                  xAxisIndex: index,
                  yAxisIndex: index,
                  type: "scatter",
                  tooltip: { formatter: "{b}" },
                  data: [
                    {
                      name: tem.name,
                      value: [n * 5, obj.rank],
                      itemStyle: {
                        color: tem.flag == 1 ? "#FFD600" : tem.color
                      }
                    }
                  ],
                  symbol:
                    tem?.flag == 1
                      ? "path://M12.4156 4.82735L8.94433 4.32286L7.39258 1.17696C7.35019 1.09083 7.28047 1.0211 7.19433 0.978716C6.97832 0.872075 6.71582 0.960943 6.60781 1.17696L5.05605 4.32286L1.58476 4.82735C1.48906 4.84102 1.40156 4.88614 1.33457 4.9545C1.25358 5.03774 1.20895 5.14973 1.21049 5.26586C1.21203 5.38199 1.25961 5.49276 1.34277 5.57383L3.8543 8.02247L3.26094 11.4801C3.24702 11.5605 3.25592 11.6432 3.28663 11.7189C3.31733 11.7945 3.36862 11.86 3.43466 11.908C3.50071 11.9559 3.57887 11.9845 3.66029 11.9903C3.74171 11.9961 3.82313 11.9789 3.89531 11.9408L7.00019 10.3084L10.1051 11.9408C10.1898 11.9859 10.2883 12.001 10.3826 11.9846C10.6205 11.9436 10.7805 11.718 10.7395 11.4801L10.1461 8.02247L12.6576 5.57383C12.726 5.50684 12.7711 5.41934 12.7848 5.32364C12.8217 5.08438 12.6549 4.8629 12.4156 4.82735Z"
                      : "circle"
                });
              } else {
                scatter[i].data.push({
                  name: tem.name,
                  value: [n * 5, obj.rank],
                  itemStyle: {
                    color: tem.flag == 1 ? "#FFD600" : tem.color
                  }
                });
              }
            }
          });
        });
      });
      this.option = {
        grid,
        xAxis,
        tooltip: { trigger: "item" },
        yAxis,
        series: [...series, ...scatter]
      };
    },
    setDom () {
      // 创建一个 DocumentFragment 对象
      const fragment = document.createDocumentFragment();
      // 添加提示框的样式
      const tooltipStyle = document.createElement("style");
      tooltipStyle.textContent = `
                    .my-tooltip {
                     position: relative;
                    display: inline-block;
                    cursor: pointer;
                z-index:2147483647;
                 }

            .my-tooltip:hover:after {
                content: attr(data-tooltip);
                position: absolute;
                top: -200%;
                left: 50%;
                transform: translateX(-50%);
                background-color: #333;
                color: #fff;
                padding: 8px;
                border-radius: 4px;
                font-size: 14px;
                white-space: nowrap;
                z-index:2147483647;
            }
`;
      fragment.appendChild(tooltipStyle);

      // 创建 div 元素
      const containerDiv = document.createElement("div");
      containerDiv.setAttribute("id", this.name + "capabilityStatistics");
      containerDiv.setAttribute("v-loading", "loading");

      // 循环创建子元素
      this.y_list.forEach((y, yi) => {
        const outerDiv = document.createElement("div");
        outerDiv.style.width = "fit-content";
        outerDiv.style.minWidth = "100%";

        const spacerDiv = document.createElement("div");
        spacerDiv.style.height = "6px";
        outerDiv.appendChild(spacerDiv);

        const innerDiv = document.createElement("div");
        innerDiv.style.position = "relative";
        innerDiv.style.height = "20px";
        innerDiv.classList.add("flex_between");

        const splitLineDiv = document.createElement("div");
        splitLineDiv.classList.add("x_split_line");
        innerDiv.appendChild(splitLineDiv);

        const labelDiv = document.createElement("div");
        labelDiv.style.width = "33px";
        labelDiv.style.position = "relative";
        labelDiv.classList.add("mr-8");
        const labelSpan = document.createElement("span");
        if (!y.show) {
          labelSpan.style.display = "none";
        }
        labelSpan.textContent = y.label;
        labelDiv.appendChild(labelSpan);
        innerDiv.appendChild(labelDiv);

        this.data_list.forEach((item, xi) => {
          const dataDiv = document.createElement("div");
          dataDiv.style.flex = "1";
          dataDiv.style.minWidth = "150px";
          dataDiv.classList.add("flex_start");

          if (item.industry === this.x_list[xi]) {
            const innerDataDiv = document.createElement("div");
            innerDataDiv.classList.add("flex_start");
            item.y_list.forEach(r => {
              if (r.rank === y.label) {
                r.name_list.slice(0, 10).forEach((v, i) => {
                  const tdDiv = document.createElement("div");
                  tdDiv.classList.add("td-background");
                  tdDiv.classList.add("flex_start");
                  if (v.flag === 0) {
                    tdDiv.style.zIndex = 100 - i;
                    tdDiv.style.cursor = "pointer";
                    tdDiv.style.border = "1px solid ";
                  } else if (v.color) {
                    tdDiv.style.color = v.color;
                    tdDiv.style.background = v.color;
                    tdDiv.style.zIndex = 100 - i;
                    tdDiv.style.cursor = "pointer";
                    tdDiv.style.border = "none";
                  } else {
                    tdDiv.style.zIndex = 100 - i;
                    tdDiv.style.cursor = "pointer";
                    tdDiv.style.border = "none";
                    tdDiv.style.backgroundColor = "transparent";
                  }
                  tdDiv.addEventListener("mouseenter", function () {
                    this.style.zIndex = "100";
                  });

                  // 添加鼠标移出事件监听器
                  tdDiv.addEventListener("mouseleave", function () {
                    this.style.zIndex = 100 - i; // 恢复默认的 z-index 值
                  });

                  const tooltip = document.createElement("el-tooltip");
                  tooltip.setAttribute("class", "item");
                  tooltip.setAttribute("effect", "dark");
                  tooltip.setAttribute("content", v.name);
                  tooltip.setAttribute("placement", "top-start");

                  const tooltipContent = document.createElement("span");
                  if (v.flag !== 1) {
                    tooltipContent.textContent = v.name.slice(0, 1);
                    tooltipContent.classList.add("my-tooltip");
                    tooltipContent.setAttribute("data-tooltip", v.name);
                  } else {
                    const svg = document.createElementNS(
                      "http://www.w3.org/2000/svg",
                      "svg"
                    );
                    svg.setAttribute("width", "16");
                    svg.setAttribute("height", "16");
                    svg.setAttribute("viewBox", "0 0 16 16");
                    svg.setAttribute("fill", "none");
                    const path = document.createElementNS(
                      "http://www.w3.org/2000/svg",
                      "path"
                    );
                    path.setAttribute(
                      "d",
                      "M12.4156 4.82735L8.94433 4.32286L7.39258 1.17696C7.35019 1.09083 7.28047 1.0211 7.19433 0.978716C6.97832 0.872075 6.71582 0.960943 6.60781 1.17696L5.05605 4.32286L1.58476 4.82735C1.48906 4.84102 1.40156 4.88614 1.33457 4.9545C1.25358 5.03774 1.20895 5.14973 1.21049 5.26586C1.21203 5.38199 1.25961 5.49276 1.34277 5.57383L3.8543 8.02247L3.26094 11.4801C3.24702 11.5605 3.25592 11.6432 3.28663 11.7189C3.31733 11.7945 3.36862 11.86 3.43466 11.908C3.50071 11.9559 3.57887 11.9845 3.66029 11.9903C3.74171 11.9961 3.82313 11.9789 3.89531 11.9408L7.00019 10.3084L10.1051 11.9408C10.1898 11.9859 10.2883 12.001 10.3826 11.9846C10.6205 11.9436 10.7805 11.718 10.7395 11.4801L10.1461 8.02247L12.6576 5.57383C12.726 5.50684 12.7711 5.41934 12.7848 5.32364C12.8217 5.08438 12.6549 4.8629 12.4156 4.82735Z"
                    );
                    path.setAttribute("fill", "#FFD600");
                    svg.appendChild(path);
                    tooltipContent.appendChild(svg);
                  }

                  tooltip.appendChild(tooltipContent);
                  tdDiv.appendChild(tooltip);
                  innerDataDiv.appendChild(tdDiv);
                });
              }
            });
            dataDiv.appendChild(innerDataDiv);
          }
          innerDiv.appendChild(dataDiv);
        });

        outerDiv.appendChild(innerDiv);
        fragment.appendChild(outerDiv);
      });

      const bottomDiv = document.createElement("div");
      bottomDiv.style.position = "relative";
      bottomDiv.classList.add("flex_between");
      const emptyDiv = document.createElement("div");
      bottomDiv.appendChild(emptyDiv);

      const percentageDiv = document.createElement("div");
      percentageDiv.style.width = "33px";
      percentageDiv.style.position = "relative";
      percentageDiv.style.marginTop = "-26px";
      percentageDiv.classList.add("mr-8");
      const percentageSpan = document.createElement("span");
      percentageSpan.textContent = "0%";
      percentageDiv.appendChild(percentageSpan);
      bottomDiv.appendChild(percentageDiv);

      this.x_list.forEach((x, xi) => {
        const xDiv = document.createElement("div");
        xDiv.style.flex = "1";
        xDiv.style.minWidth = "150px";
        xDiv.classList.add("x_axis_line");

        const ptDiv = document.createElement("div");
        ptDiv.classList.add("pt-8");

        const xSpan = document.createElement("span");
        xSpan.style.display = "inline-block";
        xSpan.textContent = x;
        ptDiv.appendChild(xSpan);

        xDiv.appendChild(ptDiv);
        bottomDiv.appendChild(xDiv);
      });

      containerDiv.appendChild(fragment);
      containerDiv.appendChild(bottomDiv);
      const parentElement = document.getElementById(this.name + "dom");
      parentElement.innerHTML = "";
      parentElement.appendChild(containerDiv);
    },
    // 导出图片
    exportImage () {
      this.downloadShow = false;
      document.getElementById(this.name + "capabilityStatistics").style =
        "width:fit-content;min-width:100%";
      setTimeout(() => {
        this.html2canvas(
          document.getElementById(this.name + "capabilityStatistics"),
          { scale: 3 }
        ).then(canvas => {
          let base64Str = canvas.toDataURL("image/png");
          let aLink = document.createElement("a");
          aLink.style.display = "none";
          aLink.href = base64Str;
          aLink.download = this.name + ".jpg";
          // 触发点击-然后移除
          document.body.appendChild(aLink);
          aLink.click();
          document.body.removeChild(aLink);
          document.getElementById(this.name + "capabilityStatistics").style =
            "";
          this.downloadShow = true;
        });
      }, 100);
    }
  }
};
</script>
<style lang="scss" scoped>
.dom {
	::v-deep.x_split_line {
		width: 100%;
		height: 1px;
		border: 1px dashed #e9e9e9;
		position: absolute;
		left: 41px;
		top: 8px;
	}
	::v-deep.x_axis_line {
		padding-top: 8px;
		border-top: 1px solid #e9e9e9;
	}
	::v-deep.td-background {
		position: relative;
		margin-left: -6px;
		width: 16px;
		height: 16px;
		border-radius: 50%;
		border: 1px solid #4096ff;
		background-color: #fff;
		color: #4096ff;
		text-align: center;
		line-height: 16px;
	}
}
</style>
