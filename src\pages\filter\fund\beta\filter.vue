<!--  -->
<template>
	<div v-loading="loading" style="background: white;padding-bottom:24px" class="">
		<!-- body -->
		<div style="display: flex; background: white; margin-top: 1px">
			<div class="tool-sidebar">
					<div class="tool-header">模板</div>
					<div class="tool-content">
						<div class="template-section">
							<div class="section-title">模板指标</div>
							<template-list ref="templateList" :nowModel="nowModel" @chooseModel="chooseModel" @saveModel="showModel = true"></template-list>
						</div>
						<!-- <div class="category-selectors">
							<div class="selector-item">
								<span>证监会分类：</span>
								<el-select size="small" placeholder="全" clearable v-model="outTypeSelect" multiple collapse-tags @change="changeOutType">
									<el-option v-for="(item, index) in outTypeList" :key="index" :label="item.label" :value="item.value"></el-option>
								</el-select>
							</div>
							<div class="selector-item">
								<span>慧捕基分类：</span>
								<el-select size="small" placeholder="全" clearable v-model="mtyTypeSelect" multiple collapse-tags @change="changeMtyType">
									<el-option v-for="(item, index) in mtyTypeList" :key="index" :label="item.label" :value="item.value"></el-option>
								</el-select>
							</div>
							<div class="selector-item">
								<span>基金池筛选：</span>
								<el-select size="small" placeholder="全" clearable v-model="poollistSelect" multiple collapse-tags @change="changePoolList">
									<el-option v-for="(item, index) in poollistOptions" :key="index" :label="item.label" :value="item.value"></el-option>
								</el-select>
							</div>
						</div> -->
					</div>
				</div>
			<div style="flex: 5;padding-left: 16px;">
				<!-- <div style="height: 55px; display: flex; align-items: center; border-bottom: 1px solid #e9e9e9">
					<div style="margin-left: 24px; font-weight: 500; font-size: 16px; line-height: 24px; color: rgba(0, 0, 0, 0.85)">
						{{ nowModel || '新建模板' }}
					</div>
				</div> -->
				<!-- 过滤指标 -->
				<div>
					<dialogFilter
						ref="dialogfilter"
						:radioType2="radioType"
						:radioType3="radioType2"
						:radioInput2="radioInput"
						:radioInput3="radioInput2"
						:isSame2="isSame"
						:radios="radio"
						:dataIndustry="dataIndustry"
						:datacategories="datacategories"
						:listSelectX="listSelect"
						@closeDialog="closeDialog"
						:openFilterFlag="openFilterFlag"
						@changeIsSanme="changeIsSanme"
						@resolveListSelect="resolveListSelect"
					>
						<!-- 使用具名插槽将筛选范围放入dialogFilter组件中 -->
						<template v-slot:filter-range>
							<div class="filter-section-container2">
								<div class="tool-header">筛选范围</div>
								<div class="tool-content">
									<range-choose ref="rangeChoose1" :filter_type="filter_type"></range-choose>
									<!-- <active-conditions :listSelect="listSelect" @closeTags="closeTags"></active-conditions> -->
								</div>
							</div>
						</template>
					</dialogFilter>
				</div>
				<el-dialog title="保存模板" :visible.sync="showModel" width="30%">
					<div style="width: 100%; display: flex; align-items: center; margin-left: 10px">
						<div style="margin-right: 10px">名称</div>
						<div><el-input v-model="input" placeholder="请输入模板名称"></el-input></div>
					</div>
					<div v-if="showjslogo" style="margin-top: 10px; margin-left: 10px">
						<el-checkbox v-model="checked">作为<span style="color: red; font-weight: 600">慧捕基模板</span></el-checkbox>
					</div>
					<div style="text-align: right; margin-right: 10px; margin-top: 10px">
						<el-button type="primary" @click="submitModel">确认</el-button>
					</div>
				</el-dialog>
				<!-- <filter-conditions></filter-conditions> -->
			</div>
		</div>
	</div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import dialogFilter from './componentsFilter/dialogFilter.vue';
import axios from '@/api/index';
import { alphaGo } from '@/assets/js/alpha_type.js';
import { alphamsg, AlphaFilterV2 } from '@/api/pages/SystemAlpha.js';
import { BetaFilter } from '@/api/pages/SystemBeta.js';
// import alphachoosepool from '@/components/components/alphafilter/alphachoosepool.vue';
import { getModelList, saveModel, editModel, deleteModel } from '@/api/pages/Tools.js';

import rangeChoose from './components/rangeChoose.vue';
import activeConditions from './components/activeConditions.vue';
import templateList from './components/templateList.vue';
import filterConditions from './components/filterConditions.vue';
import alpha from '../alpha';
export default {
	//import引入的组件需要注入到对象中才能使用
	components: { dialogFilter, rangeChoose, activeConditions, templateList, filterConditions },
	data() {
		//这里存放数据
		return {
			nowModel: '',
			showjslogo: false,
			modelid: '',
			outTypeSelect: ['all'],
			mtyTypeSelect: ['all'],
			poollistSelect: ['all'],
			outTypeList: [
				{
					label: '全部类型',
					value: 'all'
				}
			],
			mtyTypeList: [
				{
					label: '全部类型',
					value: 'all'
				}
			],
			poollistOptions: [
				{
					label: '全市场基金',
					value: 'all'
				}
			],
			modelList: [
				{ value: '1', label: '1' },
				{ value: '2', label: '2' },
				{ value: '3', label: '3' },
				{ value: '4', label: '4' }
			],
			isMerge: false,
			pageTotal: 0,
			pageIndex: 1,
			dataList: [],
			dataListAll: [],
			showModelList: false,
			checked: false,
			input: '',
			clickIndex: 'equity',
			selectFilter: [],
			isDianDao: false,
			openFilterFlag: false,
			dataIndustry: {},
			datacategories: {},
			listSelect: [],
			radioType: 'latest',
			isSame: true,
			showModel: false,
			radioInput: '',
			radioInput2: '',
			radioType2: 'b',
			defaultModel: {},
			field104Options: [
				{
					label: '波动率', //波动率：区间数据（默认下届为0）
					value: 'volatility'
				},
				{
					label: '最大回撤', //区间数据（默认下届为0）
					value: 'maxdrawdown'
				},
				{
					label: '平均下行周期', //区间数据（默认下届为0）
					value: 'averagelength'
				},
				{
					label: '平均恢复周期', //区间数据（默认下届为0）
					value: 'averagerecovery'
				},
				{
					label: '最大回撤比', //区间数据（默认下届为0.5，上届为1）
					value: 'maxdrawdown_ratio'
				},
				{
					label: '在险价值', //区间数据
					value: 'VaR05'
				},
				{
					label: '期望损失', //区间数据
					value: 'ES05'
				},
				{
					label: '下行风险', //区间数据
					value: 'downsidevolatility'
				},
				{
					label: '波动率比', //区间数据（默认下届为0.5，上届为1）
					value: 'volatilityratio'
				},
				{
					label: '痛苦指数',
					value: 'painindex'
				}
			],
			field105Options: [
				{
					label: '年化收益率',
					value: 'ave_return'
				},
				{
					label: '累计收益率',
					value: 'cum_return'
				},
				{
					label: '夏普率（rf==0）',
					value: 'sharpe0'
				},
				{
					label: '夏普率（rf==4%）',
					value: 'sharpe04'
				},
				{
					label: '夏普率（动态rf）',
					value: 'sharpe'
				},
				{
					label: '卡码率',
					value: 'calmar'
				},
				{
					label: '索提诺系数（rf==0）',
					value: 'sortino0'
				},
				{
					label: '索提诺系数（rf==4%）',
					value: 'sortino04'
				},
				{
					label: '索提诺系数（动态rf）',
					value: 'sortino'
				},
				{
					label: '稳定系数',
					value: 'hurstindex'
				},
				{
					label: '凯利系数',
					value: 'kelly'
				},
				{
					label: '信息比率',
					value: 'information'
				},
				{
					label: '跟踪误差',
					value: 'trackingerror'
				},
				{
					label: '上攻潜力（周）',
					value: 'upsidepotential'
				},
				{
					label: '月胜率',
					value: 'monthly_win_ratio'
				},
				{
					label: '詹森系数',
					value: 'jensen'
				},
				{
					label: '特诺系数',
					value: 'treynor'
				},
				{
					label: '上行捕获',
					value: 'bullreturn'
				},
				{
					label: '下行捕获',
					value: 'bearreturn'
				},
				{
					label: '择时gamma',
					value: 'gamma'
				},
				{
					label: 'M2',
					value: 'msquared'
				}
			],
			loading: false,
			listCol: [],
			listar: [], //mty
			gridData: [],
			poollist: []
		};
	},
	props: {
		filter_type: {
			type: String,
			default: 'filter'
		},
		pool_id: {
			type: Number
		}
	},
	//监听属性 类似于data概念
	computed: {},
	//监控data中的数据变化
	watch: {},
	//方法集合
	methods: {
		// 处理证监会分类选择
		changeOutType(val) {
			if (val.includes('all')) {
				this.outTypeSelect = ['all'];
			} else if (val.length === 0) {
				this.outTypeSelect = ['all'];
			}
			if (this.$refs['rangeChoose1']) {
				this.$refs['rangeChoose1'].outType = this.outTypeSelect;
				this.$refs['rangeChoose1'].changetype('out');
			}
		},
		// 处理慧捕基分类选择
		changeMtyType(val) {
			if (val.includes('all')) {
				this.mtyTypeSelect = ['all'];
			} else if (val.length === 0) {
				this.mtyTypeSelect = ['all'];
			}
			if (this.$refs['rangeChoose1']) {
				this.$refs['rangeChoose1'].mtyType = this.mtyTypeSelect;
				this.$refs['rangeChoose1'].changetype('mty');
			}
		},
		// 处理基金池选择
		changePoolList(val) {
			if (val.includes('all')) {
				this.poollistSelect = ['all'];
			} else if (val.length === 0) {
				this.poollistSelect = ['all'];
			}
			if (this.$refs['rangeChoose1'] && this.$refs['rangeChoose1'].$refs['alphachoosepool']) {
				this.$refs['rangeChoose1'].$refs['alphachoosepool'].arrlist = this.poollistSelect;
				this.$refs['rangeChoose1'].$refs['alphachoosepool'].changeValue(this.poollistSelect);
			}
		},
		// 获取分类和基金池选项
		getFilterOptions() {
			// 获取证监会分类和慧捕基分类
			if (this.$refs['rangeChoose1']) {
				this.outTypeList = this.$refs['rangeChoose1'].outTypeList;
				this.mtyTypeList = this.$refs['rangeChoose1'].mtyTypeList;
			}
			// 获取基金池选项
			if (this.$refs['rangeChoose1'] && this.$refs['rangeChoose1'].$refs['alphachoosepool']) {
				this.poollistOptions = this.$refs['rangeChoose1'].$refs['alphachoosepool'].arrlistOptions;
			}
		},
		// 修改正选反选
		changeIsSanme(val) {
			this.isSame = val;
		},
		returnModelObj() {
			let { mtytype, csrctype, poollist } = this.$refs['rangeChoose1'].resolveList();
			let nowObj = {
				type: this.radio,
				ismanager: 'false',
				model_name: this.input,
				model_description: '',
				ispublic: this.checked,
				model_args: {
					value: this.listSelect,
					isSame: this.isSame,
					radioType: this.radioType,
					radioInput: this.radioInput,
					csrctype,
					mtytype,
					poollist
				},
				flag: 'filter'
			};
			if (JSON.stringify(nowObj) == JSON.stringify(this.defaultModel)) {
				return true;
			} else {
				return false;
			}
		},

		deepCopy(obj) {
			var a = JSON.stringify(obj);
			var newobj = JSON.parse(a);
			return newobj;
		},
		async submitModel() {
			if (this.input == null || this.input == '') {
				that.$message('请输入模板名称');
			} else {
				let { mtytype, csrctype, poollist } = this.$refs['rangeChoose1'].resolveList();
				await saveModel({
					type: 'equity',
					ismanager: 'false',
					model_name: this.input,
					model_description: '',
					ispublic: this.checked,
					model_args: {
						value: this.listSelect,
						isSame: this.isSame,
						radioType: this.radioType,
						radioInput: this.radioInput,
						mtytype,
						csrctype,
						poollist: this.filter_type == 'filter' ? poollist : [this.pool_id]
					},
					flag: 'filter'
				}).catch((err) => {
					this.$message.error('模板保存失败');
				});
				this.$message.success('保存模板成功');
				// this.getmodals();
				this.$refs['templateList']?.getmodals();
				this.showModel = false;
			}
		},
		chooseModel(scope) {
			console.log(scope);
			this.nowModel = scope.model_name;
			this.modelid = scope.modelid;
			let scope1 = this.deepCopy(scope);
			this.showModelList = false;
			this.listSelect = scope1.model_args.value;
			this.isSame = scope1.model_args.isSame;
			this.radioType = scope1.model_args.radioType;
			this.radioInput = scope1.model_args.radioInput;
			this.$refs['rangeChoose1'].getList({
				mtytype: scope1.model_args?.mtytype,
				csrctype: scope1.model_args?.csrctype,
				poollist: scope1.model_args?.poollist
			});
			this.$refs['dialogfilter'].getFormData();

			this.defaultModel = {
				type: this.radio,
				ismanager: 'false',
				model_name: this.input,
				model_description: '',
				ispublic: this.checked,
				model_args: {
					value: this.deepCopyObj(this.listSelect),
					isSame: this.isSame,
					radioType: this.radioType,
					radioInput: this.radioInput
				},
				flag: 'filter'
			};
		},
		resolveListSelect(list) {
			this.listSelect = list;
		},
		delModel() {
			this.$refs['templateList'].delModel();
		},
		// 深拷贝
		deepCopyObj(obj) {
			// 只拷贝对象
			if (typeof obj !== 'object') return;
			// 根据obj的类型判断是新建一个数组还是一个对象
			let newObj = obj instanceof Array ? [] : {};
			for (let key in obj) {
				// 遍历obj,并且判断是obj的属性才拷贝
				if (obj.hasOwnProperty(key)) {
					// 判断属性值的类型，如果是对象递归调用深拷贝
					newObj[key] = typeof obj[key] === 'object' ? this.deepCopy(obj[key]) : obj[key];
				}
			}
			return newObj;
		},

		my_desc_sort(name) {
			//  ////console.log(name)
			return function (a, b) {
				if (a[name] === '--' || a[name] === 'nan' || a[name] === '- -' || b[name] === '--' || b[name] === 'nan' || b[name] === '- -') {
					if (a[name] === '--' || a[name] === 'nan' || a[name] === '- -') {
						return 1;
					} else if (b[name] === '--' || b[name] === 'nan' || b[name] === '- -') {
						return -1;
					}
				} else if (Number(a[name]) > Number(b[name])) {
					return -1;
				} else if (Number(a[name]) < Number(b[name])) {
					return 1;
				} else {
					return 0;
				}
			};
		},
		my_asc_sort(name) {
			return function (a, b) {
				if (a[name] === '--' || a[name] === 'nan' || a[name] === '- -' || b[name] === '--' || b[name] === 'nan' || b[name] === '- -') {
					if (a[name] === '--' || a[name] === 'nan' || a[name] === '- -') {
						return 1;
					} else if (b[name] === '--' || b[name] === 'nan' || b[name] === '- -') {
						return -1;
					}
				} else if (Number(a[name]) < Number(b[name])) {
					return -1;
				} else if (Number(a[name]) > Number(b[name])) {
					return 1;
				} else {
					return 0;
				}
			};
		},

		sort_change(column) {
			// ////console.log(column)
			// ////console.log('colum')
			this.pageIndex = 1; // return to the first page after sorting
			if (column.prop === 'code') {
				if (column.order === 'descending') {
					this.dataListAll = this.dataListAll.sort(this.my_desc_sort('code'));
				} else if (column.order === 'ascending') {
					this.dataListAll = this.dataListAll.sort(this.my_asc_sort('code'));
				}
			} else if (column.prop === '1y') {
				if (column.order === 'descending') {
					this.dataListAll = this.dataListAll.sort(this.my_desc_sort('1y'));
				} else if (column.order === 'ascending') {
					this.dataListAll = this.dataListAll.sort(this.my_asc_sort('1y'));
				}
			} else if (column.prop === '1m') {
				if (column.order === 'descending') {
					this.dataListAll = this.dataListAll.sort(this.my_desc_sort('1m'));
				} else if (column.order === 'ascending') {
					this.dataListAll = this.dataListAll.sort(this.my_asc_sort('1m'));
				}
			} else if (column.prop === '1q') {
				if (column.order === 'descending') {
					this.dataListAll = this.dataListAll.sort(this.my_desc_sort('1q'));
				} else if (column.order === 'ascending') {
					this.dataListAll = this.dataListAll.sort(this.my_asc_sort('1q'));
				}
			} else if (column.prop === '1w') {
				if (column.order === 'descending') {
					this.dataListAll = this.dataListAll.sort(this.my_desc_sort('1w'));
				} else if (column.order === 'ascending') {
					this.dataListAll = this.dataListAll.sort(this.my_asc_sort('1w'));
				}
			} else if (column.prop === 'netasset') {
				if (column.order === 'descending') {
					this.dataListAll = this.dataListAll.sort(this.my_desc_sort('netasset'));
				} else if (column.order === 'ascending') {
					this.dataListAll = this.dataListAll.sort(this.my_asc_sort('netasset'));
				}
			} else if (column.prop === 'long_stock_pick') {
				if (column.order === 'descending') {
					this.dataListAll = this.dataListAll.sort(this.my_desc_sort('long_stock_pick'));
				} else if (column.order === 'ascending') {
					this.dataListAll = this.dataListAll.sort(this.my_asc_sort('long_stock_pick'));
				}
			} else if (column.prop === 'short_trade') {
				if (column.order === 'descending') {
					this.dataListAll = this.dataListAll.sort(this.my_desc_sort('short_trade'));
				} else if (column.order === 'ascending') {
					this.dataListAll = this.dataListAll.sort(this.my_asc_sort('short_trade'));
				}
			} else if (column.prop === 'long_adaptive') {
				if (column.order === 'descending') {
					this.dataListAll = this.dataListAll.sort(this.my_desc_sort('long_adaptive'));
				} else if (column.order === 'ascending') {
					this.dataListAll = this.dataListAll.sort(this.my_asc_sort('long_adaptive'));
				}
			} else if (column.prop === 'long_industry_cap') {
				if (column.order === 'descending') {
					this.dataListAll = this.dataListAll.sort(this.my_desc_sort('long_industry_cap'));
				} else if (column.order === 'ascending') {
					this.dataListAll = this.dataListAll.sort(this.my_asc_sort('long_industry_cap'));
				}
			} else if (column.prop === 'long_stockclass_cap') {
				if (column.order === 'descending') {
					this.dataListAll = this.dataListAll.sort(this.my_desc_sort('long_stockclass_cap'));
				} else if (column.order === 'ascending') {
					this.dataListAll = this.dataListAll.sort(this.my_asc_sort('long_stockclass_cap'));
				}
			} else if (column.prop === 'window_score') {
				if (column.order === 'descending') {
					this.dataListAll = this.dataListAll.sort(this.my_desc_sort('window_score'));
				} else if (column.order === 'ascending') {
					this.dataListAll = this.dataListAll.sort(this.my_asc_sort('window_score'));
				}
			} else if (column.prop === 'final_score_industry') {
				if (column.order === 'descending') {
					this.dataListAll = this.dataListAll.sort(this.my_desc_sort('final_score_industry'));
				} else if (column.order === 'ascending') {
					this.dataListAll = this.dataListAll.sort(this.my_asc_sort('final_score_industry'));
				}
			} else if (column.prop === 'stockclass_hold_weight') {
				if (column.order === 'descending') {
					this.dataListAll = this.dataListAll.sort(this.my_desc_sort('stockclass_hold_weight'));
				} else if (column.order === 'ascending') {
					this.dataListAll = this.dataListAll.sort(this.my_asc_sort('stockclass_hold_weight'));
				}
			} else if (column.prop === 'industry_weight') {
				if (column.order === 'descending') {
					this.dataListAll = this.dataListAll.sort(this.my_desc_sort('industry_weight'));
				} else if (column.order === 'ascending') {
					this.dataListAll = this.dataListAll.sort(this.my_asc_sort('industry_weight'));
				}
			} else if (column.prop === 'index_weight') {
				if (column.order === 'descending') {
					this.dataListAll = this.dataListAll.sort(this.my_desc_sort('index_weight'));
				} else if (column.order === 'ascending') {
					this.dataListAll = this.dataListAll.sort(this.my_asc_sort('index_weight'));
				}
			} else {
				for (let i = 0; i < this.listCol.length; i++) {
					if (column.prop == this.listCol[i].value) {
						if (column.order === 'descending') {
							this.dataListAll = this.dataListAll.sort(this.my_desc_sort(column.prop));
						} else if (column.order === 'ascending') {
							this.dataListAll = this.dataListAll.sort(this.my_asc_sort(column.prop));
						}
					}
				}
			}
			this.dataList = this.dataListAll.slice(0, 20); // show only one page
		},
		elcellstyle({ row, column, rowIndex, columnIndex }) {
			let t = this.listCol.length;

			// ////console.log(row[0])
			if (columnIndex == 3 + t) {
				if (row['1w'] >= 0) {
					return 'color: #E85D2D;';
				} else return 'color: #20995B;';
			}
			if (columnIndex == 4 + t) {
				if (row['1m'] >= 0) {
					return 'color: #E85D2D;';
				} else return 'color: #20995B;';
			}
			if (columnIndex == 5 + t) {
				if (row['1q'] >= 0) {
					return 'color: #E85D2D;';
				} else return 'color: #20995B;';
			}
			if (columnIndex == 6 + t) {
				if (row['1y'] >= 0) {
					return 'color: #E85D2D;';
				} else return 'color: #20995B;';
			}
		},
		godetailP(id, name) {
			//带参进去
			this.$router.push({ path: '/fundmanagerdetail/' + id, hash: '', query: { id: id, name: name } });
		},
		godetail(id, name) {
			//带参进去
			alphaGo(id, name, this.$route.path);
		},
		openModelFilter() {
			this.showModelList = true;
		},
		printconsole() {
			const { export_json_to_excel } = require('@/vendor/Export2Excel');
			var list = [];
			// list.push(this.dataexplain);
			let tHeader = [];
			let tHeader2 = [];
			let filterVal = [];

			tHeader = ['基金名称', '基金经理姓名', '基金代码'];
			for (let i = 0; i < this.listCol.length; i++) {
				tHeader.push(this.listCol[i].label);
			}
			tHeader2 = ['近一周收益', '近一月收益', '近一季收益', '近一年收益', '规模', '重仓股'];
			// ////console.log(this.colums)
			for (let i = 0; i < this.dataListAll.length; i++) {
				list[i] = [];
				list[i][0] = this.dataListAll[i].name;
				list[i][1] = this.dataListAll[i].manager_name;
				list[i][2] = this.dataListAll[i].code;
				for (let j = 0; j < this.listCol.length; j++) {
					list[i][3 + j] = this.dataListAll[i][this.listCol[j].value];
				}
				list[i][3 + this.listCol.length] = this.dataListAll[i]['1w'];
				list[i][4 + this.listCol.length] = this.dataListAll[i]['1m'];
				list[i][5 + this.listCol.length] = this.dataListAll[i]['1q'];
				list[i][6 + this.listCol.length] = this.dataListAll[i]['1y'];
				list[i][7 + this.listCol.length] = (Number(this.dataListAll[i]['netasset']) / 100000000).toFixed(2) + '亿';
				list[i][8 + this.listCol.length] = this.dataListAll[i].bigs;
			}

			export_json_to_excel(tHeader.concat(tHeader2), list, '主动权益筛选结果');
		},
		async gotoFilter() {
			// this.$refs.multipleTable.clearSort();
			let { mtytype, csrctype, poollist } = this.$refs['rangeChoose1'].resolveList();
			if (
				this.localStorage.getItem('mty_filterListSelect') == JSON.stringify(this.listSelect) &&
				this.localStorage.getItem('mty_filterRadioType') == JSON.stringify(this.radioType) &&
				this.localStorage.getItem('mty_filterRadioType2') == JSON.stringify(this.radioType2) &&
				this.localStorage.getItem('mty_filterradioInput') == JSON.stringify(this.radioInput) &&
				this.localStorage.getItem('mty_filterradioInput2') == JSON.stringify(this.radioInput2) &&
				this.localStorage.getItem('mty_filtermtytype') == JSON.stringify(mtytype) &&
				this.localStorage.getItem('mty_filtercsrctype') == JSON.stringify(csrctype) &&
				this.localStorage.getItem('mty_filterpoollist') == JSON.stringify(poollist) &&
				this.localStorage.setItem('mty_filterradioInput3', JSON.stringify(this.isSame))
			) {
				this.$emit('sameResult');
			} else {
				try {
					this.loading = true;
					let formData = {};
					formData['mathRange'] = this.radioType2 == 'radioSelf2' ? this.radioInput2 : this.radioType2;
					formData['recent_time'] = this.getTime()[0];
					formData['end_time'] = this.getTime()[1];
					formData['reversed'] = !this.isSame;
					formData['fund_netasset'] = [];
					formData['return_base'] = [];
					formData['is_quant'] = [];
					formData['manager_netasset'] = [];
					formData['manager_experience'] = [];
					formData['redeem_ceil'] = [];
					formData['equitytype'] = [];
					formData['credit'] = [];
					formData['duration'] = [];
					formData['styleLabel'] = []; //风格标签
					formData['industryLabel'] = []; //行业标签
					formData['holdertype'] = [];
					formData['risk_feature'] = [];
					formData['risk_return_feature'] = [];
					formData['style'] = [];
					formData['industry'] = [];
					formData['stock'] = [];
					formData['stock_class'] = [];
					formData['bond_class'] = [];
					formData['index_overlap'] = [];
					formData['similar_index'] = [];
					formData['conceration'] = [];
					formData['roe'] = [];
					formData['turnover'] = [];
					formData['stock_huddle'] = [];
					formData['stock_attention'] = [];
					formData['win_ratio'] = [];
					formData['bet_odds'] = [];
					formData['buymode'] = [];
					formData['sellmode'] = [];
					formData['deltaweight'] = [];
					formData['redeemable'] = [];
					formData['fund_year'] = [];
					formData['managed_year'] = [];
					formData['asset_constraint'] = {};
					formData['better_section'] = [];
					formData['rotation'] = [];
					// 港股
					formData['equityhkNow'] = [];
					formData['equityhkAvg'] = [];
					formData['equityhkChange'] = [];
					// 被动add(&跟踪误差)
					formData['tracetype'] = [];
					formData['fundname'] = [];
					// 固收add（修改基金分类）
					formData['equityNow'] = [];
					formData['equityAvg'] = [];
					formData['equityChange'] = [];
					formData['cbondNow'] = [];
					formData['cbondAvg'] = [];
					formData['cbondChange'] = [];
					formData['creditReport'] = [];
					formData['durationReport'] = [];
					formData['cbondType'] = [];
					// 利率债
					formData['interestNow'] = [];
					formData['interestAvg'] = [];
					formData['interestChange'] = [];
					// 其他类型债券
					formData['creditestimated'] = [];
					formData['durationestimated'] = [];
					formData['durationop'] = [];
					formData['equity_market'] = [];

					for (let i = 0; i < this.listSelect.length; i++) {
						for (let j = 0; j < this.listSelect[i].data.length; j++) {
							if (this.listSelect[i].data[j].labelName == '基金规模') {
								formData['fund_netasset'].push({
									value: this.listSelect[i].data[j].dataResult[0].value,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName == '基金名称') {
								formData['fundname'].push({
									value: this.listSelect[i].data[j].dataResult[0].value,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName == '业绩基准') {
								formData['return_base'].push({
									value: this.listSelect[i].data[j].dataResult[0].value,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName == '指数基金跟踪类型') {
								formData['tracetype'].push({
									value: this.listSelect[i].data[j].dataResult[0].value,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg'
								});
							} else if (this.listSelect[i].data[j].labelName == '基金经理规模') {
								formData['manager_netasset'].push({
									value: this.listSelect[i].data[j].dataResult[0].value,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName == '基金存续时长') {
								formData['fund_year'].push({
									value: this.listSelect[i].data[j].dataResult[0].value,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName == '信用') {
								formData['credit'].push({
									value: this.listSelect[i].data[j].dataResult[0].value,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName == '久期') {
								formData['duration'].push({
									value: this.listSelect[i].data[j].dataResult[0].value,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName == '风格标签') {
								formData['styleLabel'].push({
									value: this.listSelect[i].data[j].dataResult[0].value,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName == '行业标签') {
								formData['industryLabel'].push({
									value: this.listSelect[i].data[j].dataResult[0].value,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName == '基金经理任职时长') {
								formData['managed_year'].push({
									value: this.listSelect[i].data[j].dataResult[0].value,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName == '转债类型') {
								formData['cbondType'].push({
									value: this.listSelect[i].data[j].dataResult[0].value,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg'
								});
							} else if (this.listSelect[i].data[j].labelName == '报告信用挖掘') {
								formData['creditReport'].push({
									value: this.listSelect[i].data[j].dataResult[0].value,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg'
								});
							} else if (this.listSelect[i].data[j].labelName == '报告久期') {
								formData['durationReport'].push({
									value: this.listSelect[i].data[j].dataResult[0].value,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName == '估算信用挖掘') {
								formData['creditestimated'].push({
									value: this.listSelect[i].data[j].dataResult[0].value,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg'
								});
							} else if (this.listSelect[i].data[j].labelName == '久期操作(含杠杆)') {
								formData['durationop'].push({
									value: this.listSelect[i].data[j].dataResult[0].value,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg'
								});
							} else if (this.listSelect[i].data[j].labelName == '估算久期') {
								formData['durationestimated'].push({
									value: this.listSelect[i].data[j].dataResult[0].value,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName == '基金经理管理经验') {
								formData['manager_experience'].push({
									value: this.listSelect[i].data[j].dataResult[0].value,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName == '可申购金额') {
								formData['redeem_ceil'].push({
									value: this.listSelect[i].data[j].dataResult[0].value,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName == '基金分类') {
								formData['equitytype'].push({
									value: this.listSelect[i].data[j].dataResult[0].value,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg'
								});
							} else if (this.listSelect[i].data[j].labelName == '是否量化') {
								formData['is_quant'].push({
									value: this.listSelect[i].data[j].dataResult[0].value,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg'
								});
							} else if (this.listSelect[i].data[j].labelName == '基金持有人特征') {
								formData['holdertype'].push({
									value: this.listSelect[i].data[j].dataResult[0].value,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg'
								});
							} else if (this.listSelect[i].data[j].labelName == '申购类型') {
								formData['redeemable'].push({
									value: this.listSelect[i].data[j].dataResult[0].value,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg'
								});
							} else if (this.field104Options.findIndex((item) => item.label == this.listSelect[i].data[j].labelName) >= 0) {
								let value = this.listSelect[i].data[j].dataResult[0].value;
								let name =
									this.field104Options[this.field104Options.findIndex((item) => item.label == this.listSelect[i].data[j].labelName)];
								let value_value = ['波动率', '最大回撤', '在险价值', '期望损失', '下行风险'].includes(name.label)
									? Number(value / 100)
									: Number(value);
								formData['risk_feature'].push({
									value: this.listSelect[i].data[j].dataResult[0].valueType == 'value' ? Number(value_value) : Number(value / 100),
									// mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									name: name.value,
									operation: this.listSelect[i].data[j].dataResult[0].flag,
									dateType: this.listSelect[i].data[j].dataResult[0].date[0],
									recent_time: this.listSelect[i].data[j].dataResult[0].date[1],
									valueType: this.listSelect[i].data[j].dataResult[0].valueType,
									benchmark: this.listSelect[i].data[j].dataResult[0].benchmark
								});
							} else if (this.field105Options.findIndex((item) => item.label == this.listSelect[i].data[j].labelName) >= 0) {
								let value = this.listSelect[i].data[j].dataResult[0].value;
								let name =
									this.field105Options[this.field105Options.findIndex((item) => item.label == this.listSelect[i].data[j].labelName)];
								let value_value =
									['年化收益率', '累计收益率', '月胜率', '特诺系数', '上行捕获', '下行捕获', 'M2'].includes(name.label) &&
									this.listSelect[i].data[j].dataResult[0].valueType == 'value'
										? Number(value / 100)
										: Number(value);
								formData['risk_return_feature'].push({
									value: this.listSelect[i].data[j].dataResult[0].valueType == 'value' ? Number(value_value) : Number(value / 100),
									name: name.value,
									operation: this.listSelect[i].data[j].dataResult[0].flag,
									dateType: this.listSelect[i].data[j].dataResult[0].date[0],
									recent_time: this.listSelect[i].data[j].dataResult[0].date[1],
									valueType: this.listSelect[i].data[j].dataResult[0].valueType,
									benchmark: this.listSelect[i].data[j].dataResult[0].benchmark
								});
							} else if (
								this.listSelect[i].data[j].labelName == '成长' ||
								this.listSelect[i].data[j].labelName == '价值' ||
								this.listSelect[i].data[j].labelName == '动量' ||
								this.listSelect[i].data[j].labelName == '盈利' ||
								this.listSelect[i].data[j].labelName == '贝塔' ||
								this.listSelect[i].data[j].labelName == '规模'
							) {
								let rank_value = this.listSelect[i].data[j].dataResult[0].rank_value;
								let value = Number(this.listSelect[i].data[j].dataResult[0].value);
								formData['style'].push({
									name: this.listSelect[i].data[j].labelName,
									recent_time: this.listSelect[i].data[j].dataResult[0].yearqtr,
									rank_value,
									value: rank_value == '分位' ? value / 100 : value,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName == '估值') {
								formData['style'].push({
									name: this.listSelect[i].data[j].labelName,
									recent_time: this.listSelect[i].data[j].dataResult[0].yearqtr,
									rank_value: this.listSelect[i].data[j].dataResult[0].rank_value,
									value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName == '申万一级行业') {
								formData['industry'].push({
									type: this.listSelect[i].labelIndex == 'j1' ? 'big' : 'all',
									intersection: this.isMerge,
									industry_section: '申万(2021)',
									industry_code: this.listSelect[i].data[j].dataResult[0].industryValue,
									value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName == '申万二级行业') {
								formData['industry'].push({
									intersection: this.isMerge,
									type: this.listSelect[i].labelIndex == 'j1' ? 'big' : 'all',
									industry_section: '申万二级(2021)',
									industry_code: this.listSelect[i].data[j].dataResult[0].industryValue,
									value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName == '申万三级行业') {
								formData['industry'].push({
									intersection: this.isMerge,
									type: this.listSelect[i].labelIndex == 'j1' ? 'big' : 'all',
									industry_section: '申万三级(2021)',
									industry_code: this.listSelect[i].data[j].dataResult[0].industryValue,
									value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName == '恒生一级行业') {
								formData['industry'].push({
									intersection: this.isMerge,
									type: this.listSelect[i].labelIndex == 'j1' ? 'big' : 'all',
									industry_section: '恒生一级',
									industry_code: this.listSelect[i].data[j].dataResult[0].industryValue,
									value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName == '多行业权重判断') {
								formData['industry'].push({
									intersection: this.isMerge,
									type: this.listSelect[i].labelIndex == 'j1' ? 'big' : 'all',
									industry_section: '多行业权重判断',
									industry_code: this.listSelect[i].data[j].dataResult[0].industryValue,
									value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName == '景气行业占比') {
								formData['better_section'].push({
									industry_section: this.listSelect[i].data[j].dataResult[0].industryValue,
									period_type: this.listSelect[i].data[j].dataResult[0].period_type,
									hold_type: this.listSelect[i].data[j].dataResult[0].hold_type,
									value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName == '行业轮动') {
								formData['rotation'].push({
									industry_level: this.listSelect[i].data[j].dataResult[0].industryValue,
									hold_type: this.listSelect[i].data[j].dataResult[0].hold_type,
									value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName == '行业超低配') {
								formData['deltaweight'].push({
									value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName == '大行业') {
								formData['industry'].push({
									intersection: this.isMerge,
									industry_section: '大行业',
									type: this.listSelect[i].labelIndex == 'j1' ? 'big' : 'all',
									industry_code: this.listSelect[i].data[j].dataResult[0].industryValue,
									value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName.indexOf('主题判断') >= 0) {
								formData['stock_class'].push({
									stockclass: this.listSelect[i].data[j].dataResult[0].industryValue,
									type: this.listSelect[i].labelIndex == 'k1' ? 'big' : 'all',
									value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName.indexOf('指数匹配') >= 0) {
								formData['index_overlap'].push({
									index_code: this.listSelect[i].data[j].dataResult[0].index_code,
									type: this.listSelect[i].labelIndex == 'l1' ? 'big' : 'all',
									value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName.indexOf('指数相似度') >= 0) {
								formData['similar_index'].push({
									index_code: this.listSelect[i].data[j].dataResult[0].index_code,
									value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName.indexOf('个股匹配') >= 0) {
								formData['stock'].push({
									index_code: this.listSelect[i].data[j].dataResult[0].index_code,
									type: this.listSelect[i].labelIndex == 'l1' ? 'big' : 'all',
									value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName == '前十大集中度') {
								formData['conceration'].push({
									value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName == 'ROE') {
								formData['roe'].push({
									name: 'roe',
									value: this.listSelect[i].data[j].dataResult[0].value / 100,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName == 'PB') {
								formData['pb'].push({
									name: 'pb',
									value: this.listSelect[i].data[j].dataResult[0].value / 100,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName == 'PE') {
								formData['pe'].push({
									name: 'pe',
									value: this.listSelect[i].data[j].dataResult[0].value / 100,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName == '换手率') {
								formData['turnover'].push({
									value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName == '持有股票抱团度') {
								formData['stock_huddle'].push({
									value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName == '股票关注期') {
								formData['stock_attention'].push({
									value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName == '胜率') {
								formData['win_ratio'].push({
									value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName == '赔率') {
								formData['bet_odds'].push({
									value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName == '买入模式') {
								formData['buymode'].push({
									name: this.listSelect[i].data[j].dataResult[0].industryValue,
									value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].data[j].labelName == '卖出模式') {
								formData['sellmode'].push({
									name: this.listSelect[i].data[j].dataResult[0].industryValue,
									value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].labelIndex == 'o') {
								formData['bond_class'].push({
									name: this.listSelect[i].data[j].labelName.replace(/\(/g, '_').replace(/\)/g, ''),
									value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].labelIndex == 'c') {
								if (!formData['asset_constraint'][this.formatAssetKey(this.listSelect[i].data[j].labelName)]) {
									formData['asset_constraint'][this.formatAssetKey(this.listSelect[i].data[j].labelName)] = [];
								}
								formData['asset_constraint'][this.formatAssetKey(this.listSelect[i].data[j].labelName)].push({
									value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag
								});
							} else if (this.listSelect[i].labelIndex == 'n') {
								formData['equity_market'].push({
									name: this.formatHoldKey(this.listSelect[i].data[j].labelName),
									value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
									mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
									operation: this.listSelect[i].data[j].dataResult[0].flag,
									is_new: 0
								});
							} else if (this.listSelect[i].labelIndex == 'n1') {
								// if (this.listSelect[i].data[j].labelName == '最新一期A股仓位') {
								// 	formData['equityNow'].push({
								// 		value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
								// 		operation: this.listSelect[i].data[j].dataResult[0].flag
								// 	});
								// } else if (this.listSelect[i].data[j].labelName == '最新一期港股仓位') {
								// 	formData['equityhkNow'].push({
								// 		value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
								// 		operation: this.listSelect[i].data[j].dataResult[0].flag
								// 	});
								// }
								if (this.listSelect[i].data[j].labelName == '最新一期台股仓位') {
									formData['equity_market'].push({
										name: this.formatHoldKey('台股仓位'),
										value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
										mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
										operation: this.listSelect[i].data[j].dataResult[0].flag,
										is_new: 1
									});
								} else if (this.listSelect[i].data[j].labelName == '最新一期美股仓位') {
									formData['equity_market'].push({
										name: this.formatHoldKey('美股仓位'),
										value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
										mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
										operation: this.listSelect[i].data[j].dataResult[0].flag,
										is_new: 1
									});
								} else if (this.listSelect[i].data[j].labelName == '最新一期A股仓位') {
									formData['equity_market'].push({
										name: this.formatHoldKey('A股仓位'),
										value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
										mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
										operation: this.listSelect[i].data[j].dataResult[0].flag,
										is_new: 1
									});
								} else if (this.listSelect[i].data[j].labelName == '最新一期港股仓位') {
									formData['equity_market'].push({
										name: this.formatHoldKey('港股仓位'),
										value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
										mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
										operation: this.listSelect[i].data[j].dataResult[0].flag,
										is_new: 1
									});
								} else {
									formData['bond_class'].push({
										name: this.listSelect[i].data[j].labelName.match(/最新一期(.*)仓位/)?.[1],
										value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
										mathRange: this.listSelect[i].data[j].dataResult[0].mathRange || 'avg',
										operation: this.listSelect[i].data[j].dataResult[0].flag,
										is_new: 1
									});
								}
							}
						}
					}
					let flag = 0;
					let tempKey = [];
					let modelid = this.modelid ? (this.returnModelObj() ? this.modelid : undefined) : undefined;
					let postData = {
						flag:"1",
						filter: formData,
						csrctype,
						mtytype,
						modelid,
						poollist: this.filter_type == 'filter' ? poollist : [this.pool_id],
						page: 1,
						page_size: 10,
						page_size_enable: 1,
						sort: '1y',
						ascend: 0
					};
					let data = await BetaFilter(postData);
					this.$emit('resolvePostData', postData);
					if (data.data?.length && data.mtycode == 200) {
						if (this.filter_type != 'filter') {
							this.loading = false;
							this.$emit('resolveFather', data?.data);
							return;
						}
						this.listCol = [];
						if (data.data?.length > 0) {
							for (var key in data.data[0]) {
								if (key.indexOf('scale') >= 0) {
									this.listCol.push({ value: 'scale', label: '基金经理规模', flag: key.indexOf('percent') >= 0 ? 1 : 0 });
								} else if (key.indexOf('managed_year') >= 0) {
									this.listCol.push({ value: 'managed_year', label: '基金经理年限', flag: key.indexOf('percent') >= 0 ? 1 : 0 });
								} else if (key.indexOf('largeapplyingmax') >= 0) {
									this.listCol.push({ value: 'largeapplyingmax', label: '申购（十万）', flag: key.indexOf('percent') >= 0 ? 1 : 0 });
								} else if (key.indexOf('credit') >= 0) {
									this.listCol.push({ value: key, label: '信用', flag: key.indexOf('percent') >= 0 ? 1 : 0 });
								} else if (key.indexOf('duration') >= 0) {
									this.listCol.push({ value: key, label: '久期', flag: key.indexOf('percent') >= 0 ? 1 : 0 });
								} else if (key.indexOf('value_rank') >= 0) {
									this.listCol.push({ value: 'value_rank', label: '价值', flag: key.indexOf('percent') >= 0 ? 1 : 0 });
								} else if (key.indexOf('growth_rank') >= 0) {
									this.listCol.push({ value: 'growth_rank', label: '成长', flag: key.indexOf('percent') >= 0 ? 1 : 0 });
								} else if (key.indexOf('momentum_rank') >= 0) {
									this.listCol.push({ value: 'momentum_rank', label: '动量', flag: key.indexOf('percent') >= 0 ? 1 : 0 });
								} else if (key.indexOf('earningyield_rank') >= 0) {
									this.listCol.push({ value: 'earningyield_rank', label: '盈利', flag: key.indexOf('percent') >= 0 ? 1 : 0 });
								} else if (key.indexOf('beta_rank') >= 0) {
									this.listCol.push({ value: 'beta_rank', label: '贝塔', flag: key.indexOf('percent') >= 0 ? 1 : 0 });
								} else if (key.indexOf('bp_rank') >= 0) {
									this.listCol.push({ value: 'bp_rank', label: '估值', flag: key.indexOf('percent') >= 0 ? 1 : 0 });
								} else if (key.indexOf('mean_conceration') >= 0) {
									this.listCol.push({ value: 'mean_conceration', label: '前十大集中度', flag: key.indexOf('percent') >= 0 ? 1 : 0 });
								} else if (key.indexOf('mean_turnover') >= 0) {
									this.listCol.push({ value: 'mean_turnover', label: '换手率', flag: key.indexOf('percent') >= 0 ? 1 : 0 });
								} else if (key.indexOf('mean_roe') >= 0) {
									this.listCol.push({ value: 'mean_roe', label: 'ROE', flag: key.indexOf('percent') >= 0 ? 1 : 0 });
								} else if (key.indexOf('股票关注期') >= 0) {
									this.listCol.push({ value: '股票关注期', label: '股票关注期', flag: key.indexOf('percent') >= 0 ? 1 : 0 });
								} else if (key.indexOf('持有股票抱团度') >= 0) {
									this.listCol.push({ value: '持有股票抱团度', label: '持有股票抱团度', flag: key.indexOf('percent') >= 0 ? 1 : 0 });
								} else if (key.indexOf('win_ratio') >= 0) {
									this.listCol.push({ value: 'win_ratio', label: '胜率', flag: key.indexOf('percent') >= 0 ? 1 : 0 });
								} else if (key.indexOf('bet_odds') >= 0) {
									this.listCol.push({ value: 'bet_odds', label: '赔率', flag: key.indexOf('percent') >= 0 ? 1 : 0 });
								} else if (key.indexOf('买入') >= 0) {
									this.listCol.push({ value: key, label: key, flag: 1 });
								} else if (key.indexOf('卖出') >= 0) {
									this.listCol.push({ value: key, label: key, flag: 1 });
								} else if (key.indexOf('_weight') >= 0) {
									this.listCol.push({
										value: key,
										label: key.replace('_weight', '') + '匹配度',
										flag: key.indexOf('percent') >= 0 ? 1 : 0
									});
									flag = 1;
									tempKey.push(key);
								} else if (key.indexOf('mean_top1_conceration_rank') >= 0) {
									this.listCol.push({ value: key, label: '前一大行业集中度', flag: key.indexOf('percent') >= 0 ? 1 : 0 });
								} else if (key.indexOf('mean_top3_conceration_rank') >= 0) {
									this.listCol.push({ value: key, label: '前三大行业集中度', flag: key.indexOf('percent') >= 0 ? 1 : 0 });
								} else if (key.indexOf('mean_top5_conceration_rank') >= 0) {
									this.listCol.push({ value: key, label: '前五大行业集中度', flag: key.indexOf('percent') >= 0 ? 1 : 0 });
								} else if (key.indexOf('_rank') >= 0) {
									if (this.field104Options.concat(this.field105Options).findIndex((item) => item.value == key.replace('_rank', '')) >= 0) {
										this.listCol.push({
											value: key,
											label: this.field104Options.concat(this.field105Options)[
												this.field104Options.concat(this.field105Options).findIndex((item) => item.value == key.replace('_rank', ''))
											].label,
											flag: key.indexOf('percent') >= 0 ? 1 : 0
										});
									}
								} else if (
									key != '1w' &&
									key != '1m' &&
									key != '1q' &&
									key != '1y' &&
									key != 'code' &&
									key != 'manager_code' &&
									key != 'manager_name' &&
									key != 'name' &&
									key != 'netasset'
								) {
									if (this.field104Options.concat(this.field105Options).findIndex((item) => item.value == key) >= 0) {
										this.listCol.push({
											value: key,
											label: this.field104Options.concat(this.field105Options)[
												this.field104Options.concat(this.field105Options).findIndex((item) => item.value == key)
											].label,
											flag: key.indexOf('percent') >= 0 ? 1 : 0
										});
									}
								}
							}
						}
						try {
							this.localStorage.setItem('mty_filterListSelect', JSON.stringify(this.listSelect));
							this.localStorage.setItem('mty_filterRadioType', JSON.stringify(this.radioType));
							this.localStorage.setItem('mty_filterRadioType2', JSON.stringify(this.radioType2));
							this.localStorage.setItem('mty_filterradioInput', JSON.stringify(this.radioInput));
							this.localStorage.setItem('mty_filterradioInput2', JSON.stringify(this.radioInput2));
							this.localStorage.setItem('mty_filterradioInput3', JSON.stringify(this.isSame));
						} catch (e) {
							// this.$message.warning('缓存已满，无法存入');
							this.localStorage.setItem('mty_filterListSelect', JSON.stringify([]));
							this.localStorage.setItem('mty_filterRadioType', JSON.stringify('latest'));
							this.localStorage.setItem('mty_filterRadioType2', JSON.stringify('a'));
							this.localStorage.setItem('mty_filterradioInput', JSON.stringify(''));
							this.localStorage.setItem('mty_filterradioInput2', JSON.stringify(''));
							this.localStorage.setItem('mty_filterradioInput3', JSON.stringify('true'));
						}

						this.$nextTick(() => {
							this.$emit('sendResult', data);
						});
					} else if (data.mtycode == 200) {
						this.$message.warning(data.mtymessage || '未筛选出结果');
						this.pageTotal = 0;
						this.dataListAll = [];
						this.dataList = [];
						this.$emit('clearTableData');
					} else if (data.mtycode == 204) {
						this.$message.warning(data.mtymessage || '未筛选出结果');
						this.pageTotal = 0;
						this.dataListAll = [];
						this.dataList = [];
						this.$emit('clearTableData');
					} else {
						this.$message.warning('未筛选出结果');
						this.$emit('clearTableData');
					}
					this.loading = false;
				} catch (e) {
					console.log(e);
					this.loading = false;
					this.pageTotal = 0;
					this.dataListAll = [];
					this.dataList = [];
					this.$emit('clearTableData');
					this.$message.error('服务器错误');
				}
			}
		},
		handlePageChange() {
			this.dataList = this.dataListAll.slice((this.pageIndex - 1) * 20, this.pageIndex * 20 - 1);
		},
		formatAssetKey(val) {
			switch (val) {
				case '股票(净)':
					return 'equity_weight';
				case '股票(总)':
					return 'equity_weight_asset';
				case '债券(净)':
					return 'bond_weight';
				case '债券(总)':
					return 'bond_weight_asset';
				case '基金(净)':
					return 'fund_weight';
				case '基金(总)':
					return 'fund_weight_asset';
				case '权证(净)':
					return 'option_weight';
				case '权证(总)':
					return 'option_weight_asset';
				case '质押式回购(净)':
					return 'repo_weight';
				case '质押式回购(总)':
					return 'repo_weight_asset';
				case '现金及等价物(净)':
					return 'cash_weight';
				case '现金及等价物(总)':
					return 'cash_weight_asset';
				case '资产支持证券(净)':
					return 'abs_weight';
				case '资产支持证券(总)':
					return 'abs_weight_asset';
				case '主动投资占比(净)':
					return 'active_weight';
				case '主动投资占比(总)':
					return 'active_weight_asset';
				case '被动投资占比(净)':
					return 'passive_weight';
				case '被动投资占比(总)':
					return 'passive_weight_asset';
				case '其他占比(净)':
					return 'other_weight';
				case '其他占比(总)':
					return 'other_weight_asset';
			}
		},
		formatHoldKey(val) {
			switch (val) {
				case '台股仓位':
					return '台股';
				case '美股仓位':
					return '美股';
				case 'A股仓位':
					return 'A股';
				case '港股仓位':
					return '港股';
				default:
					return val;
			}
		},
		getTime(type) {
			try {
				this.localStorage.setItem('mty_filterradioType', JSON.stringify(this.radioType));
			} catch (e) {
				this.localStorage.setItem('mty_filterradioType', JSON.stringify(''));
			}
			let flag = 0;
			if (this.radioType == 'latest') {
				flag = 90;
				return [this.moment(Date.now() - 86400000 * flag).format('YYYY-MM-DD'), ''];
			} else if (this.radioType == '3') {
				flag = 3 * 365;
				return [this.moment(Date.now() - 86400000 * flag).format('YYYY-MM-DD'), ''];
			} else if (this.radioType == '6') {
				flag = 6 * 365;
				return [this.moment(Date.now() - 86400000 * flag).format('YYYY-MM-DD'), ''];
			} else if (this.radioType == 'radioSelf') {
				// flag = 365 * Number(this.radioInput);
				// return this.moment(Date.now() - 86400000 * flag).format('YYYY-MM-DD');
				return this.radioInput;
			} else if (this.radioType == 'created') {
				return ['2000-01-01', 0];
			}
		},
		closeTags(index, index1) {
			if (this.listSelect[index].data.length == 1) {
				this.listSelect.splice(index, 1);
			} else if (this.listSelect[index].data.length > 1) {
				this.listSelect[index].data.splice(index1, 1);
			}
		},
		closeDialog(e, e2, e3, e4, e5, e6) {
			// return;
			if (e == '--') {
				this.listSelect = [];
			} else {
				try {
					this.listSelect = e;
					this.openFilterFlag = false;
					this.radioType = e2;
					this.isSame = e4;
					this.radioInput = e3;
					this.radioInput2 = e6;
					this.radioType2 = e5;

					this.gotoFilter();
				} catch (err) {
					this.$message.error('筛选失败，请确认运算符、数值等条件是否选择');
				}
			}
		},
		async getIndustryInfo() {
			let res = await alphamsg();
			if (res.mtycode == 200) {
				let data = res.data;
				let level1 = [];
				let level2 = [];
				let level3 = [];
				let stockclass = data.stockclass.map((item) => {
					return {
						label: item,
						value: item
					};
				});
				data.industry_tree.map((industry1) => {
					//  一级行业
					level1.push({ label: industry1.industry_name, value: industry1.industry_code });
					industry1.children.map((industry2) => {
						//  二级行业
						level2.push({ label: industry2.industry_name, value: industry2.industry_code });
						industry2.children.map((industry3) => {
							//  二级行业
							level3.push({ label: industry3.industry_name, value: industry3.industry_code });
						});
					});
				});
				let alpha = [
					{ '申万(2021)': level1 },
					{ '申万二级(2021)': level2 },
					{ '申万三级(2021)': level3 },
					{
						stockclass,
						恒生一级: data.hkindustry.map((item) => {
							return { label: item.industry_name, value: item.industry_code };
						})
					},
					stockclass
				];
				let jsonString = [{ alpha: alpha }];
				this.dataIndustry = jsonString[0];
			}
		},
		async getdatacategories() {
			let that = this;
			axios
				.get(that.$baseUrl + '/system/alpha/index_condition/')
				.then((res) => {
					////console.log(res.data)
					that.datacategories = {
						index: res.data.data.indexes,
						industries: res.data.data.industries,
						theme: res.data.data.theme,
						barra: res.data.data.barra,
						hk: ''
					};
				})
				.catch((error) => {
					//that.$message('数据缺失');
					that.datacategories = {};
				});
		},

		Init() {
			let that = this;
			this.getdatacategories();
			this.getIndustryInfo();
			this.$refs.dialogfilter.getDate();

			if (that.$route.query.model_name) {
				// this.tabName = '0'
			} else {
				this.listCol = JSON.parse(this.localStorage.getItem('mty_filter_new_listcol')) || [];
				console.log(JSON.parse(this.localStorage.getItem('mty_filterListSelect')));
				this.listSelect = JSON.parse(this.localStorage.getItem('mty_filterListSelect')) || [];
				this.radioType = JSON.parse(this.localStorage.getItem('mty_filterRadioType')) || 'latest';
				this.radioType2 = JSON.parse(this.localStorage.getItem('mty_filterRadioType2')) || 'a';
				this.radioInput = JSON.parse(this.localStorage.getItem('mty_filterradioInput')) || '';
				this.radioInput2 =
					JSON.parse(this.localStorage.getItem('mty_filterradioInput2')) * 1
						? JSON.parse(this.localStorage.getItem('mty_filterradioInput2'))
						: '' || '';
				if (
					JSON.parse(this.localStorage.getItem('mty_filterDataListAll')) != null &&
					JSON.parse(this.localStorage.getItem('mty_filterDataListAll')) != 'null' &&
					JSON.parse(this.localStorage.getItem('mty_filterDataListAll')) != undefined &&
					JSON.parse(this.localStorage.getItem('mty_filterDataListAll')) != 'undefined'
				) {
					this.$nextTick(() => {
						this.$nextTick(() => {
							this.dataListAll = JSON.parse(this.localStorage.getItem('mty_filterDataListAll'));
							this.pageTota = this.dataListAll.length;
							this.dataList = this.dataListAll.slice(0, 20);
							this.pageIndex = 1;
						});
					});
				}
			}
		}
	},
	//生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	//生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.$nextTick(() => {
			if (this.$store.state.userType.indexOf('staff') >= 0 || this.$store.state.userType.indexOf('super') >= 0) {
				this.showjslogo = true;
			} else {
				this.showjslogo = false;
			}
			this.$refs.rangeChoose1.getTypeList();

			// 初始化下拉选项
			setTimeout(() => {
				this.getFilterOptions();
			}, 1000);
		});
	},
	beforeCreate() {}, //生命周期 - 创建之前
	beforeMount() {}, //生命周期 - 挂载之前
	beforeUpdate() {}, //生命周期 - 更新之前
	updated() {}, //生命周期 - 更新之后
	beforeDestroy() {}, //生命周期 - 销毁之前
	destroyed() {}, //生命周期 - 销毁完成
	activated() {} //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style>
.newFilterTag .el-tag .el-icon-close {
	color: rgba(0, 0, 0, 0.45) !important;
}

.tool-sidebar {
  width: 210px;
  /* box-shadow: 1px 0px 0px #e9e9e9; */
  border-radius: 0px 0px 0px 4px;
  border: 1px solid rgba(217, 217, 217, 0.5);
  /* background-color: #f5f7fa; */
}

.tool-header {
  height: 40px;
  line-height: 40px;
  padding-left: 16px;
  font-weight: 500;
  font-size: 14px;
  color: #333;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e9e9e9;
}

.tool-content {
  display: flex;
  flex-direction: column;
}

.template-section {
  padding-bottom: 10px;
}

.section-title {
  padding: 10px 16px;
  font-size: 13px;
  color: #606266;
  font-weight: 500;
}

.category-selectors {
  padding: 10px 16px;
  border-top: 1px solid #e9e9e9;
}

.selector-item {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.selector-item span {
  font-size: 13px;
  color: #606266;
}

.selector-item .el-select {
  width: 80px;
}

/* 筛选范围和筛选条件样式 */
.filter-section-container2 {
  width: 100%;
  /* height: 556px;
  overflow-y: auto; */
  background-color: #f5f7fa;
  border-bottom: 1px solid #e9e9e9;
}
</style>
