<template>
  <div class="analysis_main">
    <header-info @updatePortOut="updatePortOut"
                 ref="headerInfo"
                 @getFundList="getFundList"
                 :comDetailInfo="comDetailInfo"
                 v-loading="headerloading"
                 :endDate="endDate"></header-info>
    <div style="display: flex; justify-content: space-between; align-items: center"
         class="analysis_menu comBox">
      <el-menu :default-active="activeIndex"
               class="el-menu-demo type_menu"
               mode="horizontal"
               @select="handleSelect">
        <el-menu-item v-for="item in activeComponentsList"
                      :key="item.key"
                      :index="item.key">{{ item.label }}</el-menu-item>
      </el-menu>

      <div style="margin-right: 12px; display: flex; align-items: center">
        <div style="flex: 1;margin-right: 20px;">
          <span>货币：</span>
          <span style="font-size: 14px;">人民币</span>
          <!-- <el-select v-model="walletValue" placeholder="请选择">
						<el-option
						v-for="item in options"
						:key="item.value"
						:label="item.label"
						:value="item.value">
						</el-option>
					</el-select> -->
        </div>
        <span style="
						font-family: 'PingFang';
						font-style: normal;
						font-weight: 400;
						margin-right: 8px;
					">
          比较基准:
        </span>
        <search-components type="index"
                           @resolveFather="getIndexInfo"
                           ref="search"></search-components>
        <el-button type="primary"
                   id="printWord"
                   @click="print">生成分析</el-button>
      </div>
    </div>
    <div class="line"></div>
    <component v-for="item in activeComponentsList"
               :key="item.key"
               v-show="activeIndex === item.key"
               class="comBox"
               :is="item.key"
               :ref="item.key"
               :list="list"
               @overRequest="overRequest"
               :indexInfo="indexInfo"
               :endDate="endDate"
               :active="activeIndex"
               :template="item.templateList"></component>
  </div>
</template>

<script>
import '@/pages/assets/css/page-container.scss';
import headerInfo from './components/header.vue';
import combinationMonitoring from './components/combinationMonitoring.vue';
import riskEarningAnalysis from './components/riskEarningAnalysis.vue';
import newAssetAllocationAnalysis from './components/newAssetAllocationAnalysis.vue';
import businessAnalysis from './components/businessAnalysis.vue';
import individualShareAnalysis from './components/individualShareAnalysis.vue';
// import performance from './components/performance';
// import assetAllocationAnalysis from './components/assetAllocationAnalysis';
import searchComponents from '@/components/components/search/index.vue';

import { newPortfolioDetailComponentsList } from '@/utils/newCombinationComponents';
import { combinationDetail } from '@/api/pages/tkAnalysis/portfolio.js';
export default {
  components: {
    headerInfo,
    combinationMonitoring,
    riskEarningAnalysis,
    newAssetAllocationAnalysis,
    businessAnalysis,
    individualShareAnalysis,
    searchComponents
  },
  data () {
    return {
      options: [{
        value: '人民币',
        label: '人民币'
      }],
      walletValue: '人民币',
      activeIndex: 'combinationMonitoring',
      activeComponentsList: [],
      info: {
        code: '53',
        name: '组合',
        type: 'portfolio',
        classType: 'portfolio',
        flag: 4
      },
      indexInfo: {
        id: '',
        name: '沪深300',
        flag: 'index'
      },
      list: [],
      printActive: false,
      comDetailInfo: {},
      headerloading: true,
      endDate: ''
    };
  },
  mounted () {
    this.init();
  },
  methods: {
    updatePortOut () {
      this.getComDetail();
    },
    async init () {
      this.headerloading = true;
      const { id, indexCode, endDate } = this.$route.query;
      this.endDate = endDate;

      if (!id) {
        this.headerloading = false;
        return;
      }
      this.info = { ...this.info, code: id, indexCode };
      this.activeComponentsList = newPortfolioDetailComponentsList;
      await this.$refs.search.setDefaultValue(indexCode);

    },
    filterTemplateList (key) {
      return this.activeComponentsList.filter((item) => {
        return item.key == key;
      })?.[0]?.templateList;
    },
    // 监听menu的切换
    handleSelect (key) {
      if (this.activeIndex == key) {
        return;
      }
      this.activeIndex = key;
      this.getData();
    },
    getIndexInfo (info) {
      console.log('info:::', info)
      if (info) {
        this.indexInfo = info;
      }
      this.getComDetail();
    },
    async getComDetail () {
      //获取组合详情
      let { mtycode, data } = await combinationDetail({
        id: this.info.code,
        indexCode: this.indexInfo.id || this.info.indexCode || ''
      });
      if (mtycode == 200) {
        this.comDetailInfo = data || {};
        this.comDetailInfo.combinationId = this.info.code
      }
      this.headerloading = false;
      this.getData();
    },
    getData () {
      this.$nextTick(() => {
        this.$refs[this.activeIndex]?.[0].getData()
      })

    },


    // 执行word导出
    downloadWord () {
      /* this.$nextTick(() => {
        setTimeout(async () => {
          let header = await this.$refs['headerInfo'].createPrintWord();
          let current = 2;
          let downloadList = [...header];
          this.activeComponentsList.map((item) => {
            if (this.$refs[item.key]?.[0].createPrintWord) {
              if (item.key !== 'onePagePass') {
                switch (current) {
                  case 2:
                    downloadList.push(...this.$exportWord.exportFirstTitle('二、' + item.label));
                    break;
                  case 3:
                    downloadList.push(...this.$exportWord.exportFirstTitle('三、' + item.label));
                    break;
                  case 4:
                    downloadList.push(...this.$exportWord.exportFirstTitle('四、' + item.label));
                    break;
                  case 5:
                    downloadList.push(...this.$exportWord.exportFirstTitle('五、' + item.label));
                    break;
                  case 6:
                    downloadList.push(...this.$exportWord.exportFirstTitle('六、' + item.label));
                    break;
                  case 7:
                    downloadList.push(...this.$exportWord.exportFirstTitle('七、' + item.label));
                    break;
                  case 8:
                    downloadList.push(...this.$exportWord.exportFirstTitle('八、' + item.label));
                    break;
                  case 9:
                    downloadList.push(...this.$exportWord.exportFirstTitle('九、' + item.label));
                    break;
                  case 10:
                    downloadList.push(...this.$exportWord.exportFirstTitle('十、' + item.label));
                    break;
                }
                current = current + 1;
              }

              downloadList.push(...this.$refs[item.key]?.[0].createPrintWord());
            }
          });

          let imgType = 'image/png';
          var xhr = new XMLHttpRequest();
          xhr.responseType = 'arraybuffer';
          xhr.open('GET', 'https://cdn.owl-portfolio.com/img/logoForWord.png', true);
          xhr.onload = () => {
            var result = xhr.response;
            var file = new File([result], 'foo.' + imgType.match(/\/([A-Za-z]+)/)[1], {
              type: imgType
            });
            var reader = new FileReader();
            reader.onload = (evt) => {
              // callBack(evt.target.result);
              this.$exportWord.downloadWord(this.info.name, [...downloadList], evt.target.result);
              this.loading.close();
              this.printActive = false;
            };
            reader.readAsDataURL(file);
          };
          xhr.send(null);
        }, 2000);
      }); */
    },
    // 点击打印
    async print () {
      this.loading = this.$loading({
        lock: true,
        text: '正在生成word报告,请稍等...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      this.printActive = true;
      let that = this;
      if (this.printTimeOut) {
        this.printTimeOut = null;
        clearTimeout(this.printTimeOut);
      }
      // 一分钟超时中断
      this.printTimeOut = setTimeout(() => {
        if (that.printActive) {
          that.loading.close();
          that.printActive = false;
          that.$message.warning('打印超时,请检查网络后重试');
        }
      }, 60000);
      this.overComponents = this.activeComponentsList
        .map((item) => {
          if (this.$refs[item.key]?.[0].createPrintWord) {
            return item.key;
          }
        })
        .filter((item) => {
          return item !== undefined;
        });
      console.log(this.overComponents);
      this.overComponents.map((item) => {
        this.$refs[item]?.[0].getTemplateList(this.filterTemplateList(item));
        this.$refs[item]?.[0].getData(this.info);
      });
    }
  }
};
</script>

<style scoped>
.analysis_menu .el-menu.el-menu--horizontal {
	/* width: 100%; */
	border-bottom: none;
}
.type_menu {
	background-color: transparent !important;
}

.comBox {
	background-color: transparent !important;
}
.analysis_menu {
	background-color: #fff !important;
}
</style>
