<!--  -->
<template>
	<div>
		<analysis-card-title title="基金持仓分析" @downloadExcel="exportExcel">
			<div class="mr-8">报告期:</div>
			<el-select v-model="targetQuarter" placeholder="" @change="changgedate">
				<el-option v-for="item in quarterList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
			</el-select>
		</analysis-card-title>
		<div>
			<el-table
				v-loading="loading"
				:data="fourdata1"
				style="width: 99% !important"
				class="table"
				:default-sort="{ prop: 'ratioinN', order: 'descending' }"
				ref="multipleTable"
				header-cell-class-name="table-header"
				max-height="400px"
				stripe
			>
				<el-table-column prop="name" label="名称" align="gotoleft"> </el-table-column>
				<el-table-column prop="managerName" label="基金经理" align="gotoleft"> </el-table-column>
				<el-table-column prop="type_name" label="基金类型" align="gotoleft"> </el-table-column>
				<el-table-column prop="netasset" sortable label="规模" align="gotoleft">
					<template slot-scope="scope">
						{{ scope.row.netasset | fixY }}
					</template>
				</el-table-column>
				<el-table-column sortable prop="ratioinN" label="持仓权重" align="gotoleft">
					<template slot-scope="scope">
						{{ scope.row.ratioinN | fix2p }}
					</template>
				</el-table-column>
				<template slot="empty">
					<el-empty></el-empty>
				</template>
			</el-table>
		</div>
	</div>
</template>

<script>
import { exportTitle, exportTable } from '@/utils/exportWord.js';
import { filter_json_to_excel } from '@/utils/exportExcel.js';
// 最新报告持仓分析
import { getDateList, getBasicInfo, getFofHoldFund } from '@/api/pages/Analysis.js';

// 基金持仓分析
export default {
	name: 'fundPositionAnalysis',
	data() {
		//这里存放数据
		return {
			info: {},
			companyCreateDate: '',
			quarterList: [],
			targetQuarter: '',
			datatable: [],
			benchmarkvalue: '',
			benchmarkvaluename: '',
			benchmarkoptions: [],
			fourdata1: [],
			fourdata2: [],
			notesData: {
				fholdfund: ''
			},
			loading: true
		};
	},
	filters: {
		fix2p(value) {
			if (value == '--') return value;
			else return (value * 1).toFixed(2) + '%';
		},
		fixY(value) {
			if (value == '--') return value;
			else {
				return Number(value).toFixed(2) + '亿';
			}
		},
		fixp(value) {
			if (value == '--') return value;
			else {
				return Number(value).toFixed(2);
			}
		}
	},
	watch: {
		benchmarkvalue(value) {
			for (let i = 0; i < this.benchmarkoptions.length; i++) {
				if (this.benchmarkoptions[i].index_code == value) this.benchmarkvaluename = this.benchmarkoptions[i].index_name;
			}
			this.getlabeldata();
		}
	},
	//方法集合
	methods: {
		// 获取基金成立日
		async getFundMessage() {
			let data = await getBasicInfo({
				code: this.info.code,
				flag: this.info.flag,
				type: this.info.type,
				start_date: this.info.start_date,
				end_date: this.info.end_date,
				template: 'fundPositionAnalysis'
			});
			if (data?.mtycode == 200) {
				this.companyCreateDate = data?.data?.founddate || '暂无数据';
			}
		},
		// 获取基金持仓分析
		async getFofHoldFund() {
			let data = await getFofHoldFund({
				code: this.info.code,
				type: this.info.type,
				flag: this.info.flag,
				start_date: this.FUNC.earlyAndLateDate([this.targetQuarter]).earlyDate,
				end_date: this.FUNC.earlyAndLateDate([this.targetQuarter]).lateDate
			});
			if (data?.mtycode == 200) {
				this.loading = false;
				this.fourdata1 = data?.data;
			} else {
				this.loading = false;
				this.fourdata1 = [];
			}
		},
		getData(info) {
			this.info = info;
			this.getDateList();
			this.getFundMessage();
		},
		// 获取报告持仓季度列表
		async getDateList() {
			let data = await getDateList({ code: this.info.code, type: this.info.type, flag: this.info.flag });
			if (data?.mtycode == 200) {
				this.getDateListData(data?.data);
			}
		},
		// 获取持仓季度列表
		getDateListData(data) {
			this.quarterList = data
				?.sort((a, b) => {
					return this.moment(this.moment(a, 'YYYY QQ').format()).isAfter(this.moment(b, 'YYYY QQ').format()) ? -1 : 1;
				})
				?.map((item) => {
					return { label: item, value: item };
				});
			this.targetQuarter = this.quarterList?.[0]?.value;
			this.resolveData();
		},
		changgedate() {
			this.resolveData();
		},
		resolveData() {
			this.loading = true;
			this.getFofHoldFund();
		},
		generateQuarterList() {
			let option = [];
			let qList = ['Q1', 'Q2', 'Q3', 'Q4'];
			let pre = this.companyCreateDate;
			let now = this.FUNC.transformDate(new Date());
			let preYear = pre.slice(0, 4);
			let nowYear = now.slice(0, 4);
			let preQ = this.FUNC.dateToQuarter(pre).slice(5);
			let nowQ = this.FUNC.dateToQuarter(now).slice(5);
			let yList = Array.from({ length: Math.abs(nowYear - preYear + 1) }, (item, index) => (item = parseInt(preYear) + index));

			for (let y of yList) {
				let yobj = {
					value: y,
					label: y,
					children: []
				};
				if (y == preYear) {
					qList.forEach((q) => {
						if (q >= preQ) {
							yobj.children.push({ value: q, label: q });
						}
					});
				} else if (y == nowYear) {
					qList.forEach((q) => {
						if (q <= nowQ) {
							yobj.children.push({ value: q, label: q });
						}
					});
				} else {
					qList.forEach((q) => yobj.children.push({ value: q, label: q }));
				}
				option.push(yobj);
			}
			this.quarterList = option;
			if (option[option.length - 1].children.length == 1) {
				this.targetQuarter = [
					option[option.length - 2].value,
					option[option.length - 2].children[option[option.length - 2].children.length - 1].value
				];
			} else {
				this.targetQuarter = [
					option[option.length - 1].value,
					option[option.length - 1].children[option[option.length - 1].children.length - 2].value
				];
			}
			this.resolveData();
		},

		exportExcel() {
			let list = [
				{
					label: '名称',
					value: 'name'
				},
				{
					label: '基金经理',
					value: 'manager_name'
				},
				{
					label: '基金类型',
					value: 'type'
				},
				{
					label: '规模',
					value: 'netasset',
					format: 'fixY'
				},
				{
					label: '持仓权重',
					value: 'fof_weight',
					format: 'fix2p'
				},
				{
					label: '占总净值比例',
					value: 'ratioinN',
					format: 'fix2b'
				},
				{
					label: '持券数量(万)',
					value: 'holdings',
					format: 'fix2b'
				},
				{
					label: '持仓市值(亿)',
					value: 'value',
					format: 'fix2b'
				}
			];
			filter_json_to_excel(list, this.fourdata1, '基金持仓分析');
		},
		createPrintWord() {
			let list = [
				{
					label: '名称',
					value: 'name'
				},
				{
					label: '基金经理',
					value: 'manager_name'
				},
				{
					label: '基金类型',
					value: 'type'
				},
				{
					label: '规模',
					value: 'netasset',
					format: 'fixY'
				},
				{
					label: '持仓权重',
					value: 'fof_weight',
					format: 'fix2p'
				},
				{
					label: '占总净值比例',
					value: 'ratioinN',
					format: 'fix2b'
				},
				{
					label: '持券数量(万)',
					value: 'holdings',
					format: 'fix2b'
				},
				{
					label: '持仓市值(亿)',
					value: 'value',
					format: 'fix2b'
				}
			];
			if (this.fourdata1.length) {
				return [...exportTitle('基金持仓分析'), ...exportTable(list, this.fourdata1)];
			} else {
				return [];
			}
		}
	}
};
</script>
