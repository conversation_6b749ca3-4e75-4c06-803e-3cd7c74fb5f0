<!--  -->
<template>
  <div class="returns">
    <div style="text-align: right; margin-top: 10px">
      <el-select v-model="value"
                 :remote-method="searchpeople"
                 filterable
                 remote
                 @focus="focusF"
                 prefix-icon="el-icon-search"
                 :loading="loading"
                 placeholder="选择基准">
        <el-option-group v-for="groups in options"
                         :key="groups.label"
                         :label="groups.label">
          <el-option v-for="group in groups.options"
                     :key="group.code"
                     :label="group.name"
                     :value="group.code"> </el-option>
        </el-option-group>
      </el-select>
    </div>

    <div v-loading="loading"
         style="page-break-inside: avoid; text-align: left; margin-top: 16px">
      <v-chart ref="holdstockweight"
               v-loading="empty1"
               autoresize
               element-loading-text="暂无数据"
               element-loading-spinner="el-icon-document-delete"
               element-loading-background="rgba(239, 239, 239, 0.5)"
               style="page-break-inside: avoid; width: 100%; height: 400px"
               :options="optionpbroe"
               @legendselectchanged="legendselectchanged"
               @datazoom="datazoom"></v-chart>
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { FundAllocationWithIndex, ManagerAllocationWithIndex } from '@/api/pages/tools/compare.js';
import { getIndexReturn } from '@/api/pages/tools/compare.js';
import { getFundOrBase } from '@/api/pages/components/yejiheader.js';
import { getAllDate } from '@/utils/getfulldate.js';
import VCharts from 'vue-echarts';
export default {
  props: {
    comparetype: {
      type: String,
      default: 'manager' //fund
    },
    id: {
      type: String,
      default: '30189741,30441407'
    },
    type: {
      type: String,
      default: 'equity'
    },
    name: {
      type: String,
      default: '萧楠,胡昕炜'
    }
  },
  //import引入的组件需要注入到对象中才能使用
  components: { 'v-chart': VCharts },
  data () {
    //这里存放数据
    return {
      showdetailchoose: false,
      fund_hold: [],
      empty1: false,
      value: '000300.SH',
      options: '',
      loading: false,
      optionpbroe: {},
      baseperson: '',
      namebench: '沪深300',
      baseindexlist: [],
      basepersonfund: [],
      datazoomObj: {
        start: 0,
        end: 100
      },
      selected: null
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {
    value (val) {
      this.empty1 = false;
      for (let i = 0; i < this.options[0].options.length; i++) {
        if (val == this.options[0].options[i].code) {
          this.namebench = this.options[0].options[i].name;
        }
      }
      // //console.log(this.namebench)
      if (this.comparetype == 'manager') {
        this.getmanagerdata();
      } else {
        this.gefunddata();
      }
    }
  },
  //方法集合
  methods: {
    focusF () {
      if (this.value == '000300.SH') {
        this.options = this.COMMON.optionBasic;
      }
    },
    datazoom (val) {
      this.datazoomObj = { start: val.start, end: val.end };
    },
    legendselectchanged (val) {
      this.selected = val.selected;
    },
    // 获取基准
    async searchpeople (query) {
      this.loading = false;
      let { data } = await getFundOrBase({ message: query, flag: 6 });
      if (data) {
        let temparr = [
          {
            label: '参考基准',
            options: []
          }
        ];
        for (let i = 0; i < data.length; i++) {
          if (data[i].flag == 'index') {
            temparr[0].options.push(data[i]);
          }
        }
        this.options = temparr;
        // //console.log(data)
      }
    },
    getdata () {
      // console.log('sss');
      this.empty1 = false;
      if (this.comparetype == 'manager') {
        this.getmanagerdata();
      } else {
        this.gefunddata();
      }
    },
    async getmanagerdata () {
      // let _this = this
      let data = await ManagerAllocationWithIndex({
        manager_code: this.id,
        type: this.type,
        index_code: this.value,
        manager_name: this.name
      });
      if (data) {
        if (JSON.stringify(data.data) == '{}' || JSON.stringify(data.data) == '[]' || JSON.stringify(data.data) == '') {
          this.empty1 = true;
          this.loading = false;
        } else {
          // //console.log('仓位估算')
          // //console.log(data)
          this.loading = false;
          // //console.log('仓位估算')
          // //console.log(data)
          let mindata = null;
          let maxdata = [];
          let seriess = [];
          for (let i = 0; i < data.data.allocation.length; i++) {
            data.data.allocation.sort((a, b) => {
              if (this.$route.query.id.split(',').indexOf(a.code[0]) > this.$route.query.id.split(',').indexOf(b.code[0])) return 1;
              else return -1;
            });
            seriess.push({
              name: data.data.allocation[i].manager_name[0],
              type: 'bar',
              data: []
            });
            for (let k = 0; k < data.data.allocation[i].equity_weight.length; k++) {
              seriess[i].data.push([data.data.allocation[i].yearqtr[k], Number(data.data.allocation[i].equity_weight[k]).toFixed(2)]);
            }
            if (maxdata.length < data.data.allocation[i].yearqtr.length) {
              maxdata = data.data.allocation[i].yearqtr;
            }
          }
          mindata = maxdata[0];
          let tempslicedata = mindata.split(' ')[0] + '-' + Number(mindata.split(' ')[1][1]) * 3 + '-' + '30';
          if (Number(mindata.split(' ')[1][1]) == 1) {
            tempslicedata = Number(mindata.split(' ')[0]) - 1 + '-12-31';
          } else if (Number(mindata.split(' ')[1][1]) == 2) {
            tempslicedata = mindata.split(' ')[0] + '-03-31';
          } else if (Number(mindata.split(' ')[1][1]) == 3) {
            tempslicedata = mindata.split(' ')[0] + '-06-30';
          } else if (Number(mindata.split(' ')[1][1]) == 4) {
            tempslicedata = mindata.split(' ')[0] + '-09-30';
          }
          let rpt_list = [];
          data.data.allocation.map((item) => {
            rpt_list.push(...item.rpt_date);
          });
          let index_data = data.data.index_return;
          let date1 = index_data?.cum_return// this.computedReturn(index_data?.cum_return);
          let arrlist1 = index_data?.date;
          // //console.log(data.data.index_return.date);
          let datebench = getAllDate(tempslicedata, arrlist1[arrlist1.length - 1]);
          // //console.log(datebench)
          // //console.log("datebench")
          let keytemp = 0;
          for (let i = 0; i < arrlist1.length; i++) {
            while (arrlist1[i] >= datebench[keytemp]) {
              if (arrlist1[i] == datebench[keytemp]) {
                keytemp++;
                continue;
              }
              if (keytemp == 0) {
                date1.splice(keytemp, 0, null);
              } else {
                date1.splice(keytemp, 0, date1[keytemp - 1]);
              }
              keytemp++;
              if (keytemp >= datebench.length) break;
            }
          }
          // if((Number(mindata.split(' ')[1][1])*3).toString().length==1) tempslicedata = mindata.split(' ')[0]+'-0'+Number(mindata.split(' ')[1][1])*3 +'-' +'30'
          seriess.push({
            name: this.namebench,
            type: 'line',
            symbol: 'none',
            data: date1,
            yAxisIndex: 1,
            xAxisIndex: 1
          });
          this.optionpbroe = {
            title: {
              // text:'换手率'
            },
            color: [
              '#4096ff',
              '#4096ff',
              '#7388A9',
              '#6F80DD',
              '#4096FF',
              '#e040fb',
              '#ff3d00',
              '#929694',
              '#f4d1ff',
              '#e91e63',
              '#64dd17'
            ],
            tooltip: {
              trigger: 'axis',
              formatter: function (obj) {
                if (obj.length - 1 > 0) {
                  // //console.log(obj)
                  var value = obj[0].axisValue + `<br />`;
                  for (let i = 0; i < obj.length - 1; i++) {
                    value +=
                      `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:` +
                      obj[i].color +
                      `;"></span>` +
                      obj[i].seriesName +
                      ':' +
                      obj[i].data[1] +
                      '%' +
                      `<br />`;
                  }
                  return value;
                }
              }
            },
            legend: {
              selected: this.selected
            },
            dataZoom: {
              type: 'slider',
              xAxisIndex: [0, 1],
              show: true,
              height: 14,
              bottom: 10,
              borderColor: 'transparent',
              backgroundColor: '#fafafa',
              // 拖拽手柄样式 svg 路径
              handleIcon:
                'M512 512m-208 0a6.5 6.5 0 1 0 416 0 6.5 6.5 0 1 0-416 0Z M512 192C335.264 192 192 335.264 192 512c0 176.736 143.264 320 320 320s320-143.264 320-320C832 335.264 688.736 192 512 192zM512 800c-159.072 0-288-128.928-288-288 0-159.072 128.928-288 288-288s288 128.928 288 288C800 671.072 671.072 800 512 800z',
              handleColor: '#aab6c6',
              handleSize: 20,
              handleStyle: {
                borderColor: '#aab6c6',
                shadowBlur: 4,
                shadowOffsetX: 1,
                shadowOffsetY: 1,
                shadowColor: '#e5e5e5'
              },
              start: this.datazoomObj.start || 0,
              end: this.datazoomObj.end || 100
            },
            grid: {
              left: '10px',
              right: '3%',
              bottom: '10%',
              top: '30px',
              containLabel: true
            },
            xAxis: [
              {
                type: 'category',
                boundaryGap: true,
                data: maxdata
              },
              {
                show: false,
                type: 'category',
                boundaryGap: false,
                data: datebench
              }
            ],
            yAxis: [
              {
                name: '仓位',
                axisLine: { show: false },
                axisTick: { show: false },
                splitLine: {
                  show: true,
                  lineStyle: {
                    type: 'dashed'
                  }
                },
                min: 0,
                // min: (value) => {
                // 	//  //console.log(value)
                // 	//  //console.log('123')
                // 	return value.min;
                // },
                type: 'value',
                axisLabel: {
                  formatter: function (obj) {
                    //   //console.log(obj)
                    // var value = obj.value;
                    return obj + '%';
                  }
                }
              },
              {
                name: '基准累计收益率',
                axisLine: { show: false },
                axisTick: { show: false },
                splitLine: {
                  show: false,
                  lineStyle: {
                    type: 'dashed'
                  }
                },
                // min: 0,
                min: (value) => {
                  //  //console.log(value)
                  //  //console.log('123')
                  return value.min;
                },
                type: 'value',
                axisLabel: {
                  formatter: function (obj) {
                    //   //console.log(obj)
                    // var value = obj.value;
                    return (obj * 100).toFixed(2) + '%';
                  }
                }
              }
            ],
            series: seriess
          };
        }
      } else {
        this.empty1 = true;
      }
    },
    insertStr (soure, start, newStr) {
      return soure.slice(0, start) + newStr + soure.slice(start);
    },
    async gefunddata () {
      // console.log('///');
      // let _this = this
      this.loading = true;
      let data = await FundAllocationWithIndex({ fund_code: this.id, type: this.type, fund_name: this.name, index_code: this.value });
      if (data) {
        if (JSON.stringify(data.data) == '{}' || JSON.stringify(data.data) == '[]' || JSON.stringify(data.data) == '') {
          // console.log(data)
          this.empty1 = true;
          this.loading = false;
        } else {
          this.loading = false;
          // console.log('仓位估算')
          // console.log(data)
          let mindata = null;
          let maxdata = [];
          let seriess = [];
          for (let i = 0; i < data.data.allocation.length; i++) {
            data.data.allocation.sort((a, b) => {
              if (this.$route.query.id.split(',').indexOf(a.fund_code[0]) > this.$route.query.id.split(',').indexOf(b.fund_code[0]))
                return 1;
              else return -1;
            });
            seriess.push({
              name: data.data.allocation[i].fund_name[0],
              type: 'bar',
              data: []
            });
            for (let k = 0; k < data.data.allocation[i].equity_weight.length; k++) {
              seriess[i].data.push([data.data.allocation[i].yearqtr[k], Number(data.data.allocation[i].equity_weight[k]).toFixed(2)]);
            }
            if (maxdata.length < data.data.allocation[i].yearqtr.length) {
              maxdata = data.data.allocation[i].yearqtr;
            }
          }
          mindata = maxdata[0];
          // let tempslicedata = mindata.split(' ')[0]+'-'+Number(mindata.split(' ')[1][1])*3 +'-' +'30'
          let tempslicedata = mindata.split(' ')[0] + '-' + Number(mindata.split(' ')[1][1]) * 3 + '-' + '30';
          // //console.log(tempslicedata)
          //  //console.log(mindata)
          if (Number(mindata.split(' ')[1][1]) == 1) {
            tempslicedata = Number(mindata.split(' ')[0]) - 1 + '-12-31';
          } else if (Number(mindata.split(' ')[1][1]) == 2) {
            tempslicedata = mindata.split(' ')[0] + '-03-31';
          } else if (Number(mindata.split(' ')[1][1]) == 3) {
            tempslicedata = mindata.split(' ')[0] + '-06-30';
          } else if (Number(mindata.split(' ')[1][1]) == 4) {
            tempslicedata = mindata.split(' ')[0] + '-09-30';
          }
          let rpt_list = [];
          data.data.allocation.map((item) => {
            rpt_list.push(...item.rpt_date);
          });
          let index_data = data.data.index_return;
          let date1 = index_data?.cum_return// this.computedReturn(index_data?.cum_return);
          let arrlist1 = index_data?.date;
          // let index_data = await this.getIndexReturn(rpt_list);
          // let date1 = this.computedReturn(index_data.data?.[0].value);
          // let arrlist1 = index_data.data?.[0].date.map((v) => v.slice(0, 10));
          // //console.log(data.data.index_return.date);
          let datebench = getAllDate(tempslicedata, arrlist1[arrlist1.length - 1]);
          // //console.log(datebench)
          // //console.log("datebench")
          let keytemp = 0;
          for (let i = 0; i < arrlist1.length; i++) {
            while (arrlist1[i] >= datebench[keytemp]) {
              if (arrlist1[i] == datebench[keytemp]) {
                keytemp++;
                continue;
              }
              if (keytemp == 0) {
                date1.splice(keytemp, 0, null);
              } else {
                date1.splice(keytemp, 0, date1[keytemp - 1]);
              }
              keytemp++;
              if (keytemp >= datebench.length) break;
            }
          }
          // if((Number(mindata.split(' ')[1][1])*3).toString().length==1) tempslicedata = mindata.split(' ')[0]+'-0'+Number(mindata.split(' ')[1][1])*3 +'-' +'30'
          seriess.push({
            name: this.namebench,
            type: 'line',
            symbol: 'none',
            data: date1,
            yAxisIndex: 1,
            xAxisIndex: 1
          });
          this.optionpbroe = {
            title: {
              // text:'换手率'
            },
            color: [
              '#4096ff',
              '#4096ff',
              '#7388A9',
              '#6F80DD',
              '#4096FF',
              '#e040fb',
              '#ff3d00',
              '#929694',
              '#f4d1ff',
              '#e91e63',
              '#64dd17'
            ],
            tooltip: {
              trigger: 'axis',
              formatter: function (obj) {
                if (obj.length - 1 > 0) {
                  // //console.log(obj)
                  var value = obj[0].axisValue + `<br />`;
                  for (let i = 0; i < obj.length - 1; i++) {
                    value +=
                      `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:` +
                      obj[i].color +
                      `;"></span>` +
                      obj[i].seriesName +
                      ':' +
                      obj[i].data[1] +
                      '%' +
                      `<br />`;
                  }
                  return value;
                }
              }
            },
            legend: {
              selected: this.selected
            },
            dataZoom: {
              type: 'slider',
              xAxisIndex: [0, 1],
              show: true,
              height: 14,
              bottom: 10,
              borderColor: 'transparent',
              backgroundColor: '#fafafa',
              // 拖拽手柄样式 svg 路径
              handleIcon:
                'M512 512m-208 0a6.5 6.5 0 1 0 416 0 6.5 6.5 0 1 0-416 0Z M512 192C335.264 192 192 335.264 192 512c0 176.736 143.264 320 320 320s320-143.264 320-320C832 335.264 688.736 192 512 192zM512 800c-159.072 0-288-128.928-288-288 0-159.072 128.928-288 288-288s288 128.928 288 288C800 671.072 671.072 800 512 800z',
              handleColor: '#aab6c6',
              handleSize: 20,
              handleStyle: {
                borderColor: '#aab6c6',
                shadowBlur: 4,
                shadowOffsetX: 1,
                shadowOffsetY: 1,
                shadowColor: '#e5e5e5'
              },
              start: this.datazoomObj.start || 0,
              end: this.datazoomObj.end || 100
            },
            grid: {
              left: '10px',
              right: '3%',
              bottom: '10%',
              top: '30px',
              containLabel: true
            },
            xAxis: [
              {
                type: 'category',
                boundaryGap: true,
                data: maxdata
              },
              {
                show: false,
                type: 'category',
                boundaryGap: false,
                data: datebench
              }
            ],
            yAxis: [
              {
                name: '仓位',
                axisLine: { show: false },
                axisTick: { show: false },
                splitLine: {
                  show: true,
                  lineStyle: {
                    type: 'dashed'
                  }
                },
                min: 0,
                // min: (value) => {
                // 	//  //console.log(value)
                // 	//  //console.log('123')
                // 	return value.min;
                // },
                type: 'value',
                axisLabel: {
                  formatter: function (obj) {
                    //   //console.log(obj)
                    // var value = obj.value;
                    return obj + '%';
                  }
                }
              },
              {
                name: '基准累计收益率',
                axisLine: { show: false },
                axisTick: { show: false },
                splitLine: {
                  show: false,
                  lineStyle: {
                    type: 'dashed'
                  }
                },
                // min: 0,
                min: (value) => {
                  //  //console.log(value)
                  //  //console.log('123')
                  return value.min;
                },
                type: 'value',
                axisLabel: {
                  formatter: function (obj) {
                    //   //console.log(obj)
                    // var value = obj.value;
                    return (obj * 100).toFixed(2) + '%';
                  }
                }
              }
            ],
            series: seriess
          };
          console.log(this.optionpbroe);
        }
      }
      // //console.log(data.data)
      // //console.log('????')
      // this.$emit('changefund',this.nameList)
    },
    async getIndexReturn (date_list) {
      let start_date = date_list.sort((a, b) => {
        return this.moment(this.moment(a, 'YYYY-MM-DD').format()).isAfter(this.moment(b, 'YYYY-MM-DD').format()) ? 1 : -1;
      })?.[0];
      let end_date = date_list.sort((a, b) => {
        return this.moment(this.moment(a, 'YYYY-MM-DD').format()).isBefore(this.moment(b, 'YYYY-MM-DD').format()) ? 1 : -1;
      })?.[0];
      let data = await getIndexReturn({ index_codes: [this.value], start_date, end_date });
      return data;
    },
    // 累计收益计算
    computedReturn (data) {
      console.log(data);
      let cum_return = 1;
      let cum = data.map((item) => {
        cum_return = cum_return * (1 + item);
        console.log(cum_return, item);
        return cum_return - 1;
      });
      // console.log(cum);
      return cum;
    },
    createPrintWord () {
      let height = this.$refs['holdstockweight']?.$el.clientHeight;
      let width = this.$refs['holdstockweight']?.$el.clientWidth;
      let chart = this.$refs['holdstockweight'].getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      });
      return [...this.$exportWord.exportFirstTitle('仓位披露与比较'), ...this.$exportWord.exportChart(chart, { width, height })];
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () { },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
</style>
