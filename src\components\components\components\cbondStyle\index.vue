<template>
	<div id="cbondStyle">
		<analysis-card-title title="转债正股风格" image_id="cbondStyle"></analysis-card-title>
		<div class="charts_fill_class" v-loading="chicangloading">
			<v-chart
				ref="positionStyle"
				v-loading="showemptychicang"
				class="charts_one_class"
				autoresize
				element-loading-text="暂无数据"
				element-loading-spinner="el-icon-document-delete"
				element-loading-background="rgba(239, 239, 239, 0.5)"
				:options="optionex"
			/>
			<br />
		</div>
	</div>
</template>

<script>
import { exportTitle, exportChart } from '@/utils/exportWord.js';
import { barChartOption } from '@/utils/chartStyle.js';
// 转债正股风格
import { getcbondStyleData } from '@/api/pages/Analysis.js';
export default {
	name: 'cbondStyle',
	data() {
		return {
			optionex: {},
			chicangloading: true,
			showemptychicang: true,
			info: []
		};
	},
	methods: {
		// 获取转债正股风格数据
		async getcbondStyle() {
			let data = await getcbondStyleData({
				code: this.info.code,
				type: this.info.type,
				flag: Number(this.info.flag),
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			if (data?.mtycode == 200) {
				this.getChartData(data?.data);
			}
		},
		getData(info) {
			this.info = info;
			this.getcbondStyle();
		},
		// 获取父组件传递数据
		getChartData(data) {
			this.chicangloading = false;
			if (data == '' || data == 'error' || (typeof data == 'object' && Object.keys(data).length == 0)) {
				this.showemptychicang = true;
			} else {
				let result = data.sort((a, b) => {
					return this.moment(this.moment(a.yearqtr, 'YYYY QQ').format()).isBefore(this.moment(b.yearqtr, 'YYYY QQ').format()) ? -1 : 1;
				});
				let { series, legend } = this.filterData(result);
				this.optionex = {
					tooltip: {
						textStyle: {
							fontSize: '14px'
						},
						trigger: 'axis',
						axisPointer: {
							// 坐标轴指示器，坐标轴触发有效
							type: 'line' // 默认为直线，可选为：'line' | 'shadow'
						},
						formatter: (params) => {
							let str = `时间: ${params[0].axisValue} <br />`;
							for (let i = params.length - 1; i >= 0; i--) {
								let dotHtml =
									'<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' +
									params[i].color +
									'"></span>';
								str += dotHtml + `${params[i].seriesName}: ${(params[i].value[1] * 1).toFixed(3) + '%'}<br />`;
							}
							return str;
						}
					},
					legend: {
						// textStyle: {
						// 	fontSize: '12px'
						// },

						orient: 'vertical',
						right: 0
						// data: legend
					},
					grid: {
						left: '0',
						right: '100px',
						bottom: '48px',
						top: '24px',
						containLabel: true,
						backgroundColor: '#fafafa'
					},
					dataZoom: [
						{
							// 这个dataZoom组件，默认控制x轴。
							type: 'slider', // 这个 dataZoom 组件是 slider 型 dataZoom 组件
							start: 0, // 左边在 10% 的位置。
							end: 100,
							bottom: '0',
							show: true // 右边在 60% 的位置。
						}
					],
					xAxis: {
						nameTextStyle: {
							fontSize: '14px'
						},
						axisLabel: {
							show: true,
							color: 'rgba(0, 0, 0, 0.65)',
							textStyle: {
								fontSize: '12px'
							},
							showMinLabel: true, // 显示最小刻度标签
							showMaxLabel: true
							// interval: 0
						},
						axisTick: {
							show: false
						},
						axisLine: {
							lineStyle: {
								color: '#e9e9e9'
							}
						},
						type: 'category',
						data: Array.from(new Set(result.map((v) => v.yearqtr)))
					},
					yAxis: {
						axisLine: {
							show: false
						},
						axisTick: {
							show: false
						},
						nameTextStyle: {
							fontSize: '14px'
						},
						axisLabel: {
							show: true,
							textStyle: {
								fontSize: '14px'
							},
							formatter: function (val) {
								return (val * 1).toFixed(2) + '%';
							}
						},
						// max: 1.0,
						type: 'value'
					},
					color: [
						'#2464ac',
						'#418bbf',
						'#94c5de',
						'#d3e5f0',
						'#b7a7d7',
						'#fddbc7',
						'#f3a483',
						'#d45c4e',
						'#b0002c',
						'#409eff',
						'#67C23A',
						'#F56C6C',
						'#409eff'
					],
					series
				};
				this.showemptychicang = false;
			}
		},
		exportImage() {
			let chart = this.$refs['positionStyle'].getDataURL({
				type: 'png',
				pixelRatio: 3,
				backgroundColor: '#fff',
				excludeComponents: ['dataZoom']
			});
			let aLink = document.createElement('a');
			aLink.style.display = 'none';
			aLink.href = chart;
			aLink.download = '持仓风格.jpg';
			// 触发点击-然后移除
			document.body.appendChild(aLink);
			aLink.click();
			document.body.removeChild(aLink);
		},
		// 过滤接收数据
		filterData(data) {
			let legend = Array.from(new Set(data.map((v) => v.valuegrowth + v.bigsmall)));
			let series = [];
			data.map((item) => {
				let index = series.findIndex((v) => v.name == item.valuegrowth + item.bigsmall);
				if (index == -1) {
					series.push({
						name: item.valuegrowth + item.bigsmall,
						type: 'bar',
						stack: '总量',
						barWidth: '100%',
						data: [[item.yearqtr, item.weight]]
					});
				} else {
					series[index].data.push([item.yearqtr, item.weight]);
				}
			});
			// for (const key in data) {
			// 	if (key !== 'yearqtr') {
			// 		legend.push(key);
			// 		series.push({
			// 			name: key,
			// 			type: 'bar',
			// 			stack: '总量',
			// 			barWidth: '100%',
			// 			data: data?.[key]
			// 		});
			// 	}
			// }
			// console.log(series);
			return { legend, series };
		},

		createPrintWord() {
			let height = this.$refs['positionStyle'].$el.clientHeight;
			let width = this.$refs['positionStyle'].$el.clientWidth;
			let chart = this.$refs['positionStyle'].getDataURL({
				type: 'png',
				pixelRatio: 2,
				backgroundColor: '#fff',
				excludeComponents: ['dataZoom']
			});
			return [...exportTitle('持仓风格'), ...exportChart(chart, { width, height })];
		}
	}
};
</script>

<style></style>
