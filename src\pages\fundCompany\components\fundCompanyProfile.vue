<template>
	<div>
		<div style="margin-bottom: 16px">
			<div>
				<span
					style="
						font-family: 'PingFang';
						font-style: normal;
						font-weight: 400;
						font-size: 14px;
						line-height: 24px;

						color: rgba(0, 0, 0, 0.46);
					"
					>基金公司/</span
				><span
					style="
						font-family: 'PingFang SC';
						font-style: normal;
						font-weight: 500;
						font-size: 20px;
						line-height: 28px;
						color: rgba(0, 0, 0, 0.85);
					"
					>基金公司详情</span
				>
			</div>
		</div>
		<div class="fund-company-profile">
			<div class="profile-header">
				<div class="company-name">{{ name }}</div>
				<div class="company-code">{{ code }}</div>
			</div>
			<div class="fund_company_info">
				<div>
					<div class="item-label">{{ tableLabel.ceo }}:</div>
					<div class="item-value">{{ tableData.ceo || '暂无数据' }}</div>
				</div>

				<div>
					<div class="item-label">{{ tableLabel.from }}:</div>
					<div class="item-value">{{ tableData.from || '暂无数据' }}</div>
				</div>
				<div>
					<div class="item-label">{{ tableLabel.managed_asset }}:</div>
					<div class="item-value">
						{{ tableData.netasset ? parseFloat(tableData.netasset).toFixed(2) + ' 亿' : '暂无数据' }}
					</div>
				</div>

				<div>
					<div class="item-label">{{ tableLabel.fund_num }}:</div>
					<div class="item-value">{{ tableData.fund_num + '只' || '暂无数据' }}</div>
				</div>
				<div>
					<div class="item-label">{{ tableLabel.manager_num }}:</div>
					<div class="item-value">{{ tableData.manager_num || '暂无数据' }}</div>
				</div>
				<div>
					<div class="item-label"></div>
					<div class="item-value"></div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
// 基金公司基础信息
import { getBasicMsg } from '@/api/pages/SystemOther.js';
export default {
	data() {
		return {
			tableLabel: {
				ceo: '总经理',
				from: '成立日期',
				managed_asset: '管理规模',
				ownership: '',
				fund_num: '基金数量',
				// managementExpenses: '管理费用',
				manager_num: '基金经理数量'
				// companyGrade: '公司评级'
			},
			tableData: {
				ceo: '',
				from: '',
				managed_asset: '',
				ownership: '',
				fund_num: '',
				managementExpenses: '',
				manager_num: '',
				companyGrade: ''
			},
			creditEvent: '',
			info: {}
		};
	},
	methods: {
		// 获取基金公司数据
		async getData(info) {
			this.info = info;
			let data = await getBasicMsg({ code: info.code });
			if (data) {
				this.tableData = data;
			}
		}
	}
};
</script>

<style scoped lang="scss">
.fund-company-profile {
	background: #fff;
	padding: 16px 24px 24px 24px;
}
.company-name {
	font-family: 'PingFang';
	font-style: normal;
	font-weight: 500;
	font-size: 20px;
	line-height: 28px;
	color: rgba(0, 0, 0, 0.85);
}
.company-code {
	font-family: 'Helvetica Neue';
	font-style: normal;
	font-weight: 400;
	font-size: 14px;
	line-height: 22px;
	color: rgba(0, 0, 0, 0.85);
}
.fund_company_info {
	width: 516px;
	height: 156px;
	margin-top: 24px;
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	border-left: 1px solid #d9d9d9;
	border-top: 1px solid #d9d9d9;
	border-right: 1px solid #d9d9d9;
	box-sizing: border-box;
	div {
		width: 50%;
		height: 52px;
		display: flex;
		align-items: center;
	}
}
.item-label {
	border-bottom: 1px solid #d9d9d9;
	flex: 1.5;
	height: 52px;
	padding: 15px 0;
	background-color: #fafafa;
	text-align: right;
	font-family: 'PingFang';
	font-style: normal;
	font-weight: 400;
	font-size: 14px;
	line-height: 22px;
	color: rgba(0, 0, 0, 0.85);
}
.item-value {
	border-bottom: 1px solid #d9d9d9;
	flex: 2;
	height: 52px;
	padding: 15px 0;
	background-color: #ffffff;
	text-align: left;
	font-family: 'PingFang';
	font-style: normal;
	font-weight: 400;
	font-size: 14px;
	line-height: 22px;
	color: rgba(0, 0, 0, 0.65);
}
</style>
