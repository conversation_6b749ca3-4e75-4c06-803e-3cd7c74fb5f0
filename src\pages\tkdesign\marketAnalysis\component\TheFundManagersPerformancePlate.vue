<template>
  <div class="plate-wrapper fund-performance-wrapper">
    <VerticalLineHeader title="主动基金经理的分位数业绩"
                        showDownloadBtn
                        @downloadClick="exportExcel">
      <template slot="right">
        <el-form ref="form"
                 :model="form"
                 label-width="80px"
                 class="title-right-form">
          <el-form-item size="small"
                        style="margin-right: 16px;"
                        label="截止日期:">
            <el-date-picker value-format="yyyy-MM-dd"
                            type="date"
                            placeholder="选择日期"
                            v-model="deadline"
                            @change="intervalChange"
                            style="width: 100%"></el-date-picker>
          </el-form-item>
          <div>
            间隔：
            <el-radio-group class="radio-group-wrapper"
                            v-model="interval"
                            size="small"
                            @input="intervalChange">
              <el-radio-button label="5">5%</el-radio-button>
              <el-radio-button label="10">10%</el-radio-button>
              <el-radio-button label="25">25%</el-radio-button>
            </el-radio-group>
          </div>
          <el-radio-group class="radio-group-wrapper"
                          v-model="dateFlag"
                          size="small"
                          @input="intervalChange">
            <el-radio-button label="0">近期业绩</el-radio-button>
            <el-radio-button label="1">自然年份业绩</el-radio-button>
          </el-radio-group>
        </el-form>
      </template>
    </VerticalLineHeader>
    <div class="select-form-wrapper">
      <div class="select-form-wrapper">
        <RadioGroup ref="RadioGroup"
                    class="radio-group-wrapper"
                    :defaultValue="defaultValue"
                    :configList="configList"
                    selectOnClick
                    @change="handleTypeChange"></RadioGroup>
      </div>
    </div>
    <el-table class="content-table-wrapper"
              style="width: 100%"
              :data="tableData"
              :stripe="true"
              :border="true"
              @sort-change="sortChange"
              v-loading="loading">
      <template v-for="(item, index) in tableHeader">
        <el-table-column :prop="item.prop"
                         :label="item.label"
                         min-width="120"
                         :sortable="index === 0 ? false : 'custom'"
                         align="gotoleft"
                         :key="index"
                         :formatter="item.format">
          <template slot="header"
                    v-if="item.prop === 'customTime'">
            区间收益
            <DatePickerBtn @change="handleDateChange"
                           @click.native.stop></DatePickerBtn>
          </template>
        </el-table-column>
      </template>
    </el-table>
  </div>
</template>
<script>
import RadioGroup from './RadioGroup.vue';
import VerticalLineHeader from './VerticalLineHeader.vue';
import { getQuantilePerformance, getFundCode } from '@/api/pages/tkAnalysis/captial-market.js';
import DatePickerBtn from './DatePickerBtn.vue';
import stringTool from '@/pages/tkdesign/components/string.tool';
import { filter_json_to_excel } from '@/utils/exportExcel.js';
export default {
  name: 'TheFundManagersPerformancePlate',
  components: {
    RadioGroup,
    VerticalLineHeader,
    DatePickerBtn
  },
  data () {
    return {
      deadline: '',
      interval: '5',
      dateFlag: '0',
      form: {},
      loading: true,
      IndexStyleOption: [],
      tableData: [],
      selectList: {},
      selectVal: {
        industry: [],
        theme: [],
        type: [],
        scale: [],
        TKIndex: [],
        style: []
      },
      defaultValue: {
        radioValue: 'industry'
        // selectValue:{name:'动态市盈率',value:'pe'}
      },
      firstType: '',
      subType: [],
      configList: [
        { type: 'select', value: '', label: 'type', text: '类型', option: [{ label: '全部类型' }] },
        { type: 'select', value: '', label: 'industry', text: '行业', option: [{ label: '全部行业', value: [] }] },
        { type: 'select', value: '', label: 'theme', text: '主题', option: [{ label: '全部主题', value: [] }] },
        { type: 'select', value: '', label: 'optionalPool', text: '自选池', option: [{ label: '全部自选池', value: [] }] },
        { type: 'select', value: '', label: 'taikang', text: '泰康分类', option: [{ label: '全部泰康分类', value: [] }] },
        { type: 'select', value: '', label: 'style', text: '风格', option: [{ label: '全部风格', value: [] }] }
      ],
      tableHeader: [
        {
          prop: 'quantile',
          label: '分位数'
        },
        {
          prop: 'yearToDate',
          label: '年初至今',
          format: this.formatter
        },
        {
          prop: 'lastWeek',
          label: '近一周',
          format: this.formatter
        },
        {
          prop: 'lastMounth',
          label: '近一月',
          format: this.formatter
        },
        {
          prop: 'lastSeason',
          label: '近一季',
          format: this.formatter
        },
        {
          prop: 'lastHalfYears',
          label: '近半年',
          format: this.formatter
        },
        {
          prop: 'lastYear',
          label: '近一年',
          format: this.formatter
        },
        {
          prop: 'lastThreeYear',
          label: '近三年',
          format: this.formatter
        },
        {
          prop: 'lastFiveYear',
          label: '近五年',
          format: this.formatter
        },
        {
          prop: 'customTime',
          label: '自定义区间',
          format: this.formatter
        }
      ]
    };
  },
  mounted () {
    this.deadline = this.moment().subtract(1, 'day').format('YYYY-MM-DD');
    this.getFundCode();
    this.$nextTick(() => {
      if (this.localStorage.getItem('TheFundManagersPerformancePlate')) {
        let key_list = ['form', 'firstType', ' subType', 'interval', 'dateFlag', 'defaultValue'];
        for (let key of key_list) {
          this[key] = this.localStorage.getItem('TheFundManagersPerformancePlate')?.[key] || this[key];
        }
        let index = this.configList.findIndex((v) => v.label == this.defaultValue.radioValue);
        this.$set(this.configList, index, { ...this.configList[index], value: this.defaultValue.selectValue });
        this.$refs['RadioGroup'].setValue(this.defaultValue);
      }
      this.getData();
    });
  },
  methods: {
    sortChange (row) {
      const { column, prop, order } = row;
      console.log(column, prop, order);
      console.log(this.tableData);

      this.tableData.sort((item1, item2) => {
        let orderVal = order === 'ascending' ? -(item1[prop] - item2[prop]) : item1[prop] - item2[prop];
        return orderVal;
      });
    },
    // 导出excel
    exportExcel () {
      let list = this.tableHeader.map((item) => {
        return {
          ...item,
          value: item.prop,
          format: ''
        };
      });
      filter_json_to_excel(list, this.tableData, '主动基金经理的分位数业绩');
    },
    formatter (row, column, cellValue, index) {
      return stringTool.fix2px(cellValue);
    },
    intervalChange () {
      this.getData();
    },
    async getFundCode () {
      let params = {
        deadline: ''
      };
      let req = await getFundCode(params);
      let { data, code, message } = req || {};
      if (code == 200) {
        // {type:'select',value:'',label:'type',text:'类型',option:[{label:'类型'}]},
        // {type:'select',value:'',label:'industry',text:'行业',option:[{label:'行业'}]},
        // {type:'select',value:'',label:'theme',text:'主题',option:[{label:'主题'}]},
        // {type:'select',value:'',label:'pool',text:'自选池',option:[{label:'自选池'}]},
        // {type:'select',value:'',label:'taikang',text:'泰康分类',option:[{label:'泰康分类'}]},
        // {type:'select',value:'',label:'style',text:'风格',option:[{label:'风格'}]}],
        this.configList = this.configList.map((item) => {
          let dataList = data[item.label + 'List'] || [];
          let curOption = [];
          if (item.label == 'optionalPool') {
            curOption = dataList.map((item) => {
              return {
                label: item.name,
                value: { name: item.name, value: item.id }
              };
            });
          } else {
            curOption = this.dulConfigOption(dataList);
          }
          item.option.push(...curOption);
          return item;
        });
      } else {
      }
    },
    dulConfigOption (dataList) {
      // {label:'动态市盈率',value:{name:'动态市盈率',value:'pe'}},
      // {label:'静态市盈率',value:{name:'静态市盈率',value:'staticState_pe'}},
      // {label:'滚动市盈率',value:{name:'滚动市盈率',value:'trends_pe'}},
      return dataList.map((item) => {
        return {
          label: item,
          value: { name: item, value: item }
        };
      });
    },
    handleTypeChange (value) {
      this.defaultValue = value;
      //重新设置chart
      console.log(value);
      this.firstType = value.radioValue;
      this.subType = [value?.selectValue?.value];
      this.getData();
    },
    handleDateChange (value) {
      //自定义区间收益，只影响自定义列表
      this.form['startDate'] = value[0];
      this.form['endDate'] = value[1];
      this.getData();
    },
    async getData () {
      this.loading = true;
      let params = {
        marketType: 'manager',
        type: this.firstType || 'industry',
        subType: this.subType || [],
        interval: this.interval,
        deadline: this.deadline,
        dateFlag: this.dateFlag,
        ...this.form
      };
      this.localStorage.setItem('TheFundManagersPerformancePlate', {
        firstType: this.firstType,
        subType: this.subType,
        interval: this.interval,
        dateFlag: this.dateFlag,
        form: this.form,
        defaultValue: this.defaultValue
      });
      let req = await getQuantilePerformance(params);
      let { data, code } = req || {};
      if (code == 200) {
        this.tableData = data;
        if (this.dateFlag === '1') {
          let dateList = [];
          this.tableData.forEach((item) => {
            item.naturalList.forEach((res) => {
              dateList.push(res.naturalDate);
            });
          });
          dateList = [...new Set(dateList?.sort((a, b) => {
            if (a < b) return 1
            else return -1
          }))];
          const arr = dateList.map((item) => {
            return {
              prop: item,
              label: item,
              format: this.formatter
            };
          });
          this.tableHeader = [
            {
              prop: 'quantile',
              label: '分位数'
            },

            ...arr
          ];
          this.tableData = this.tableData?.map((item) => {
            item.naturalList?.forEach((element) => {
              item[element.naturalDate] = element.meter;
            });
            return item;
          });
        } else {
          this.tableHeader = [
            {
              prop: 'quantile',
              label: '分位数'
            },
            {
              prop: 'yearToDate',
              label: '年初至今',
              format: this.formatter
            },
            {
              prop: 'lastWeek',
              label: '近一周',
              format: this.formatter
            },
            {
              prop: 'lastMounth',
              label: '近一月',
              format: this.formatter
            },
            {
              prop: 'lastSeason',
              label: '近一季',
              format: this.formatter
            },
            {
              prop: 'lastHalfYears',
              label: '近半年',
              format: this.formatter
            },
            {
              prop: 'lastYear',
              label: '近一年',
              format: this.formatter
            },
            {
              prop: 'lastThreeYear',
              label: '近三年',
              format: this.formatter
            },
            {
              prop: 'lastFiveYear',
              label: '近五年',
              format: this.formatter
            },
            {
              prop: 'customTime',
              label: '自定义区间',
              format: this.formatter
            }
          ];
        }
      } else {
        this.tableData = [];
      }
      this.loading = false;
    }
  }
};
</script>
<style lang="scss" scoped>
.fund-performance-wrapper {
	padding-bottom: 20px;
	.select-form-wrapper {
		margin-bottom: 16px;
		display: flex;
	}
	.content-table-wrapper {
		margin-bottom: 32px;
	}
}
</style>
