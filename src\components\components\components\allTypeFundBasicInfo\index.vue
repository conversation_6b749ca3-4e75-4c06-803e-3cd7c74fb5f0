<template>
  <div class="chart_one allTypeFundBaicInfo">
    <div class="title">所属基金概况</div>
    <div class="list"
         v-loading="loading">
      <div style="display:flex;flex-wrap:wrap;">
        <div v-for="item in list"
             :key="item.title"
             class="item">
          <div class="title">{{ item.title }}</div>
          <div>
            <div class="content_flex">
              <div>
                <span class="bold_content">{{ item.netasset }}</span>亿元
              </div>
              <div>占比：{{ item.netasset_percentage }}%</div>
            </div>
            <div>
              <el-progress color="#4096ff"
                           :percentage="item.netasset_percentage"
                           :format="format"></el-progress>
            </div>
            <div>较上次：{{ item.netasset_change }}亿元({{ item.netasset_change_percentage }}%)</div>
          </div>
          <div>
            <div class="content_flex">
              <div>
                <span class="bold_content">{{ item.num }}</span>
                个
              </div>
              <div>占比：{{ item.num_percentage }}%</div>
            </div>
            <div>
              <el-progress :percentage="item.num_percentage"
                           :format="format"></el-progress>
            </div>
            <div>较上次：{{ item.num_change }}个({{ item.num_change_percentage }}%)</div>
          </div>
        </div>
      </div>
      <el-empty v-show="list.length == 0"
                image-size="160"
                style="margin: 0 auto"></el-empty>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      loading: true,
      list: []
    };
  },
  methods: {
    // 获取数据
    getData (data) {
      this.loading = false;
      let netasset_all = 0;
      let num_all = 0;
      this.list = data
        ?.map((item) => {
          netasset_all = netasset_all + (item.netasset_newest * 1 || 0);
          num_all = num_all + (item.number_newest * 1 || 0);
          return item;
        })
        .map((item) => {
          return {
            ...item,
            netasset_percentage: item.netasset_newest * 1 ? ((item.netasset_newest * 100) / netasset_all).toFixed(2) * 1 : 0,
            num_percentage: item.number_newest * 1 ? ((item.number_newest * 100) / num_all).toFixed(2) * 1 : 0
          };
        })
        .map((item) => {
          return {
            title: this.FUNC.textConverter(this.COMMON.fundType_zh_en, item.type, 'en', 'zh'),
            netasset: item.netasset_newest * 1 ? this.addComma(((item.netasset_newest * 1) / 10 ** 8).toFixed(2)) : '--',
            netasset_change:
              item.netasset_change * 1
                ? (item.netasset_change * 1 > 0 ? '+' : '-') + this.addComma(((item.netasset_change * 1) / 10 ** 8).toFixed(2))
                : '--',
            netasset_change_percentage:
              item.netasset_newest * 1 && item.netasset_change * 1
                ? (((item.netasset_change * 1) / (item.netasset_newest * 1 - item.netasset_change * 1)) * 100).toFixed(2)
                : 0,
            netasset_percentage: item.netasset_percentage,
            num: item.number_newest * 1 ? this.addComma((item.number_newest * 1).toFixed(0)) : '--',
            num_change:
              item.number_change * 1 ? (item.number_change * 1 > 0 ? '+' : '-') + this.addComma((item.number_change * 1).toFixed(0)) : '0',
            num_percentage: item.num_percentage,
            num_change_percentage:
              item.number_newest * 1 && item.number_change * 1
                ? (((item.number_change * 1) / (item.number_newest * 1 - item.number_change * 1)) * 100).toFixed(2)
                : 0
          };
        });
    },
    // 添加千位符
    addComma (value) {
      let suffix = '';
      if (value && value !== 'NULL' && value !== 'undefined' && isNaN(Number(value))) {
        return value;
      } else if (!value) {
        return '-';
      } else {
        let pSuffix = '';
        value = value.toString();
        let intPart = Math.floor(Math.abs(Number(value))); // 获取整数部分
        let intPartFormat = intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,'); // 将整数部分逢三一断
        intPartFormat = pSuffix + intPartFormat;
        let floatPart = ''; // 预定义小数部分
        let value2Array = value.split('.');
        // =2表示数据有小数位
        if (value2Array.length === 2) {
          floatPart = value2Array[1].toString(); // 拿到小数部分
          if (floatPart.length === 1) {
            // 补0,实际上用不着
            return intPartFormat + '.' + floatPart + '0' + suffix;
          } else {
            return intPartFormat + '.' + floatPart + suffix;
          }
        } else {
          return intPartFormat + floatPart + suffix;
        }
      }
    },
    format (val) {
      return '';
    }
  }
};
</script>

<style lang="scss">
.allTypeFundBaicInfo {
	.list {
		.el-progress-bar {
			padding-right: 0;
		}
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 16px;
		.title {
			font-size: 16px;
		}
		.item:nth-child(1) {
			padding-right: 16px;
			padding-left: 0px;
		}
		.item:nth-last-child(1) {
			border: none;
		}
		.item {
			flex: 1;
			max-width: 388px;
			border-right: 1px solid #e9e9e9;
			padding: 0 8px;
			.bold_content {
				font-family: 'Helvetica Neue';
				font-style: normal;
				font-weight: 500;
				font-size: 14px;
				color: rgba(0, 0, 0, 0.85);
			}
			.content_flex {
				display: flex;
				justify-content: space-between;
				align-items: center;
			}
		}
	}
}
</style>
