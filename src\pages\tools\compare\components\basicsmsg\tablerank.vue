<!--  -->
<template>
  <div v-loading="loading"
       class="tablerank">
    <div>
      <sTable :data="fund_hold"
              typeFlag="1"></sTable>
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import sTable from '../SelfTable.vue';
import { ManagerBasicMsg, FundBasicMsg } from '@/api/pages/tools/compare.js';
export default {
  props: {
    comparetype: {
      type: String,
      default: 'manager' //fund
    },
    id: {
      type: String,
      default: '30189741,30441407'
    },
    type: {
      type: String,
      default: 'equity'
    },
    name: {
      type: String,
      default: '萧楠,胡昕炜'
    },
    fund_hold: {
      type: Array
    }
  },
  components: { sTable },
  filters: {
    fix3 (value) {
      if (value === '' || value === null || isNaN(Number(value))) {
        return value;
      } else {
        return Number(value * 100).toFixed(2) + '%';
      }
    },
    fix3p (value) {
      if (value === '' || value === null || isNaN(Number(value))) {
        return value;
      } else {
        return Number(value).toFixed(2) + '%';
      }
    },
    fix2 (value) {
      if (value === '' || value === null || isNaN(Number(value))) {
        return value;
      } else {
        return Number(value).toFixed(2) + '亿';
      }
    },
    fix4 (value) {
      if (value === '' || value === null || isNaN(Number(value))) {
        return value;
      } else {
        return Number(value).toFixed(4);
      }
    }
  },
  data () {
    //这里存放数据
    return {
      managerholdcolumnsH: [
        { dataIndex: 'name_x', key: 'name_x', title: '基金经理' },
        { dataIndex: 'glr', key: 'glr', title: '基金公司', scopedSlots: { customRender: 'glr' } },
        { dataIndex: 'founddate', key: 'founddate', title: '成立日期' },
        { dataIndex: 'cnav', key: 'cnav', title: '单位净值', scopedSlots: { customRender: 'cnav' } },
        { title: '', key: 'action', scopedSlots: { customRender: 'action' } }
      ],
      fundholdcolumnsH: [
        { dataIndex: 'name', key: 'name', title: '基金名称' },
        { dataIndex: 'glr', key: 'glr', title: '基金公司', scopedSlots: { customRender: 'glr' } },
        { dataIndex: 'founddate', key: 'founddate', title: '成立日期' },
        { dataIndex: 'cnav', key: 'cnav', title: '单位净值', scopedSlots: { customRender: 'cnav' } },
        { title: '', key: 'action', scopedSlots: { customRender: 'action' } }
      ],
      fundholdcolumns: [
        { dataIndex: 'name', key: 'name', title: '基金名称' },
        { dataIndex: 'manager_name', key: 'manager_name', title: '基金经理' },
        { dataIndex: 'manage_from', key: 'managed_from', title: '现任基金经理管理起始日期' },
        // { dataIndex: 'managed_year', key: 'Managed_year', title: '成立年限', align: 'left' , scopedSlots: { customRender: 'Managed_year' } },
        { dataIndex: 'netasset', key: 'Netasset', title: '规模', align: 'left', scopedSlots: { customRender: 'Netasset' } },
        { dataIndex: 'rate', key: 'rate', title: '年化收益', align: 'left', scopedSlots: { customRender: 'rate' } }
      ],
      managerholdcolumns: [
        { dataIndex: 'name_x', key: 'Name', title: '基金经理' },
        { dataIndex: 'managed_year', key: 'Managed_year', title: '管理年限', align: 'left', scopedSlots: { customRender: 'Managed_year' } },
        { dataIndex: 'netasset', key: 'Netasset', title: '管理规模', align: 'left', scopedSlots: { customRender: 'Netasset' } },
        { dataIndex: 'avg_return', key: 'Avg_return', title: '年化收益', align: 'left', scopedSlots: { customRender: 'Avg_return' } },
        { dataIndex: 'fund_name', key: 'Fund', title: '代表基金' }
      ],
      fund_hold: [],
      loading: false
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    deleteSelect (val) {
      this.$emit('deleteSelect', val.code);
    },
    async getdata () { }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () { },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
</style>
