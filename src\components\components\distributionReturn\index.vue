<!--  -->
<template>
	<div class="chart_one">
		<div v-loading="loadyejis">
			<div
				style="
					display: flex;
					align-items: center;
					justify-content: space-between;
					position: relative;
					height: 56px;
					border-bottom: 1px solid #e9e9e9;
				"
			>
				<div class="title">
					收益率分布直方图
					<i class="el-icon-video-camera-solid videoIconDes" @click="openvideo"></i>
				</div>
				<div>
					<el-button @click="changgedate('daily')" :style="flagitem == 'daily' ? 'background:#4096FF !important;color:white' : ''" type=""
						>日收益</el-button
					>
					<el-button @click="changgedate('weekly')" :style="flagitem == 'weekly' ? 'background:#4096FF !important;color:white' : ''" type=""
						>周收益</el-button
					>
					<el-button
						@click="changgedate('monthly')"
						:style="flagitem == 'monthly' ? 'background:#4096FF !important;color:white' : ''"
						type=""
						>月收益</el-button
					>
					<el-select
						v-show="!indexInfo"
						v-model="values"
						class="ml5 print_show"
						:remote-method="searchpeople"
						filterable
						remote
						@change="changename"
						prefix-icon="el-icon-search"
						:loading="loading"
						placeholder="输入简拼/代码/名称选对比项"
					>
						<el-option-group v-for="groups in havefundmanager" :key="groups.label" :label="groups.label">
							<el-option v-for="group in groups.options" :key="group.code" :label="group.name" :value="group.code + '|' + group.flag">
							</el-option>
						</el-option-group>
					</el-select>
				</div>
			</div>
			<div
				style="
					font-family: 'PingFang';
					font-style: normal;
					font-weight: 400;
					font-size: 14px;
					line-height: 22px;
					color: rgba(0, 0, 0, 0.85);
					margin-top: 16px;
				"
			>
				{{ fcsy }}
			</div>
			<div class="charts_fill_class">
				<el-empty v-show="yejishowflag" :image-size="160"></el-empty>
				<v-chart
					v-show="!yejishowflag"
					v-loading="yejishowflag"
					element-loading-text="暂无数据"
					element-loading-spinner="el-icon-document-delete"
					element-loading-background="rgba(239, 239, 239, 0.5)"
					class="charts_one_class"
					ref="distributionReturn"
					autoresize
					:options="yejioptions"
				/>
			</div>
		</div>
	</div>
</template>

<script>
// 收益率分布直方图
import { Search } from '@/api/pages/Analysis.js';
import { exportTitle, exportChart, exportDescripe } from '@/utils/exportWord.js';
import { barChartOption } from '@/utils/chartStyle.js';
export default {
	name: 'distributionReturn',
	data() {
		return {
			values: '',
			valuestemp: '',
			index_code_flag: '',
			havefundmanager: [],
			yejioptions: {},
			loadyejis: true,
			flagitem: 'weekly',
			fcsy: '',
			postData: { date_flag: 'weekly', index_code: '', index_flag: '' }
		};
	},
	props: {
		info: { type: Object, default: {} },
		indexInfo: {
			type: {
				type: Object,
				default: {}
			}
		}
	},
	//方法集合
	methods: {
		openvideo() {
			window.open('https://www.bilibili.com/video/BV1QT411J7h5?share_source=copy_web');
		},
		createPrintWord() {
			if (this.yejishowflag) {
				return [];
			} else {
				this.$refs['distributionReturn'].mergeOptions({ toolbox: { show: false } });
				let height = this.$refs['distributionReturn']?.$el.clientHeight;
				let width = this.$refs['distributionReturn']?.$el.clientWidth;
				let chart = this.$refs['distributionReturn'].getDataURL({
					type: 'png',
					pixelRatio: 3,
					backgroundColor: '#fff'
				});
				this.$refs['distributionReturn'].mergeOptions({ toolbox: { show: true } });
				return [
					...exportTitle('收益率分布直方图' + (this.flagitem == 'daily' ? '日收益' : this.flagitem == 'weekly' ? '周收益' : '月收益')),
					...exportDescripe(this.fcsy),
					...exportChart(chart, { width, height })
				];
			}
		},
		calculateFrequency(dataArray, intervalArray) {
			// 创建一个用于存储区间数据出现次数的对象
			var frequency = {};
			var bins = [];
			// 遍历数据数组
			for (var i = 0; i < dataArray.length; i++) {
				var data = dataArray[i];

				// 遍历统计区间数组
				for (var j = 0; j < intervalArray.length; j++) {
					var interval = intervalArray[j];
					var x0 = interval[0];
					var x1 = interval[1];

					// 检查数据是否在当前区间范围内
					if (data >= x0 && data < x1) {
						// 如果区间在频率对象中不存在，则将其初始化为0
						if (!frequency.hasOwnProperty(j)) {
							frequency[j] = 0;
						}

						// 将当前区间的出现次数加1
						frequency[j]++;
						let index = bins.findIndex((v) => v.x0 == x0 && v.x1 == x1);
						if (index == -1) {
							bins.push({ x0, x1, sample: [data] });
						} else {
							bins[index].sample.push(data);
						}
						break; // 跳出内部循环，继续处理下一个数据
					}
				}
			}

			let customData = [];
			for (const key in frequency) {
				customData.push([...intervalArray[key], frequency[key]]);
			}
			let result = {
				bins,
				customData
			};
			return result;
		},
		// 获取父组件传递数据
		getData({ data, mtycode }) {
			let that = this;
			that.loadyejis = true;
			if (mtycode == 200) {
				let length1;
				let length2;
				if (data?.code_return && data?.code_return?.length && typeof data?.code_return == 'object') {
					var result = ecStat.histogram(data.code_return);
					length1 = data.code_return.length;
				} else {
					return;
				}
				if (data?.index_return && JSON.stringify(data.index_return) != '[]') {
					let customData = [];
					result.customData.map((item) => {
						customData.push([item[0], item[1]]);
					});
					var result2 = this.calculateFrequency(data.index_return, customData);
					// var result2 = ecStat.histogram(data.index_return, 0.1);
					// console.log(result2);
					length2 = data.index_return.length;
				}
				this.fcsy = '';
				let indexName = '';
				this.havefundmanager.map((item) => {
					item.options.map((val) => {
						if (val.code == this.values.split('|')?.[0]) {
							indexName = val.name;
							return;
						}
					});
				});
				if (data.code_return_std) {
					this.fcsy += '自身标准差：' + (Number(data.code_return_std) * 100).toFixed(2) + '%。';
				}
				if (data.code_return_mean) {
					this.fcsy += '自身平均收益率:' + (Number(data.code_return_mean) * 100).toFixed(2) + '%。';
				}
				if (data.index_return_std) {
					this.fcsy += '参考基准:' + (indexName || '') + ', 标准差：' + (Number(data.index_return_std) * 100).toFixed(2) + '%。';
				}
				if (data.index_return_mean) {
					this.fcsy += '参考基准:' + (indexName || '') + ', 平均收益率' + (Number(data.index_return_mean) * 100).toFixed(2) + '%。';
				}
				if (data?.index_return && JSON.stringify(data.index_return) != '[]') {
					let datalist = [];
					let datalist2 = [];
					let datalist3 = [];
					let bdatalist = [];
					let bdatalist2 = [];
					let bdatalist3 = [];
					let interval;
					let interval1;
					let interval2;
					var min = Infinity;
					var max = -Infinity;
					let data = echarts.util.map(result.data, function (item, index) {
						var x0 = result.bins[index].x0;
						var x1 = result.bins[index].x1;
						interval1 = x1 - x0;
						min = Math.min(min, x0);
						max = Math.max(max, x1);
						return [x0, x1, item[1]];
					});
					let data2 = result2.customData;
					// let data2 = echarts.util.map(result2.data, function (item, index) {
					// 	var x0 = result2.bins[index].x0;
					// 	var x1 = result2.bins[index].x1;
					// 	interval2 = x1 - x0;
					// 	min = Math.min(min, x0);
					// 	max = Math.max(max, x1);
					// 	return [x0, x1, item[1]];
					// });
					if (length1 > length2) {
						interval = interval1;
					} else {
						interval = interval2;
					}
					for (let i = 0; i < result.bins.length; i++) {
						datalist.push(String(Number(result.bins[i].x0)) + '~' + String(Number(result.bins[i].x1)));
						datalist2.push(result.bins[i].sample.length);
						datalist3.push(result.bins[i].sample);
					}
					for (let i = 0; i < result2.bins.length; i++) {
						bdatalist.push(String(Number(result2.bins[i].x0)) + '~' + String(Number(result2.bins[i].x1)));
						bdatalist2.push(result2.bins[i].sample.length);
						bdatalist3.push(result2.bins[i].sample);
					}
					this.yejioptions = barChartOption({
						color: ['#4096ff', 'rgba(255,145,3,0.3)'],
						tooltip: {
							formatter(value) {
								return value
									.map((item) => {
										let name = item.data[0] + '~' + item.data[1];
										let name2 = (item.data[0] * 100).toFixed(2) + '%~' + (item.data[1] * 100).toFixed(2) + '%';
										let t3 = 0;
										let t4 = 0;
										let t3l = 0;
										let t4l = 0;
										let t5 = 0;
										if (item.seriesName == '自身收益分布直方图') {
											let index = datalist.indexOf(name);
											for (let i = 0; i < datalist.length; i++) {
												if (i <= index) {
													for (let j = 0; j < datalist3[i].length; j++) {
														t3 += Number(datalist3[i][j]);
														t3l += 1;
													}
												}
												if (i >= index) {
													for (let k = 0; k < datalist3[i].length; k++) {
														t4 += Number(datalist3[i][k]);
														t4l += 1;
													}
												}
												for (let l = 0; l < datalist3[i].length; l++) {
													t5 += 1;
												}
											}
										} else {
											let index = bdatalist.indexOf(name);
											for (let i = 0; i < bdatalist.length; i++) {
												if (i >= index) {
													for (let j = 0; j < bdatalist3[i].length; j++) {
														t3 += Number(bdatalist3[i][j]);
														t3l += 1;
													}
												}
												if (i <= index) {
													for (let k = 0; k < bdatalist3[i].length; k++) {
														t4 += Number(bdatalist3[i][k]);
														t4l += 1;
													}
												}
												for (let l = 0; l < bdatalist3[i].length; l++) {
													t5 += 1;
												}
											}
										}

										return `${item.seriesName == '自身收益分布直方图' ? '自身收益区间' : '参考基准收益区间'}${name2}:${
											item.data[2]
										}次<br />左侧频率(包含此区间)：${((t3l / t5) * 100).toFixed(2)}%<br />左侧收益率均值(包含此区间)：${(t3l == 0
											? 0
											: (t3 / t3l) * 100
										).toFixed(2)}%<br />右侧频率(包含此区间)：${((t4l / t5) * 100).toFixed(2)}%<br />右侧收益率均值(包含此区间)：${(t4l == 0
											? 0
											: (t4 / t4l) * 100
										).toFixed(2)}%`;
									})
									.join('<br />');
							}
						},
						xAxis: [
							{
								show: true,
								name: '收益率分布',
								formatter(value) {
									return (value * 100).toFixed(1) + '%';
								},
								type: 'value',
								min: min,
								max: max,
								axisLabel: {
									interval: interval
								}
							}
						],
						yAxis: [
							{
								name: '次数'
							}
						],
						series: [
							{
								name: '自身收益分布直方图',
								type: 'custom',
								barGap: '-100%',
								renderItem: that.renderItem,
								encode: {
									x: [0, 1],
									y: 2,
									tooltip: 2,
									label: 2
								},
								data: data
							},
							{
								name: '参考基准收益分布直方图',
								type: 'custom',
								renderItem: that.renderItem,
								encode: {
									x: [0, 1],
									y: 2,
									tooltip: 2,
									label: 2
								},
								data: data2
							}
						]
					});
				} else {
					let datalist = [];
					let datalist2 = [];
					let datalist3 = [];
					let interval;
					var min = Infinity;
					var max = -Infinity;
					let data = echarts.util.map(result.data, function (item, index) {
						var x0 = result.bins[index].x0;
						var x1 = result.bins[index].x1;
						interval = x1 - x0;
						min = Math.min(min, x0);
						max = Math.max(max, x1);
						return [x0, x1, item[1]];
					});
					for (let i = 0; i < result.bins.length; i++) {
						datalist.push(String(Number(result.bins[i].x0)) + '~' + String(Number(result.bins[i].x1)));
						datalist2.push(result.bins[i].sample.length);
						datalist3.push(result.bins[i].sample);
					}
					that.yejioptions = barChartOption({
						color: ['#4096ff', '#4096ff'],
						toolbox: false,
						tooltip: {
							formatter(value) {
								let name = value[0].data[0] + '~' + value[0].data[1];
								let name2 = (value[0].data[0] * 100).toFixed(2) + '%~' + (value[0].data[1] * 100).toFixed(2) + '%';
								let index = datalist.indexOf(name);
								let t1 = 0;
								let t2 = 0;
								let t3 = 0;
								let t4 = 0;
								let t3l = 0;
								let t4l = 0;
								let t5 = 0;

								for (let i = 0; i < datalist.length; i++) {
									if (i <= index) {
										for (let j = 0; j < datalist3[i].length; j++) {
											t3 += Number(datalist3[i][j]);
											t3l += 1;
										}
									}
									if (i >= index) {
										for (let k = 0; k < datalist3[i].length; k++) {
											t4 += Number(datalist3[i][k]);
											t4l += 1;
										}
									}
									for (let l = 0; l < datalist3[i].length; l++) {
										t5 += 1;
									}
								}
								return `收益区间${name2}:${value[0].data[2]}次<br />左侧频率(包含此区间)：${((t3l / t5) * 100).toFixed(
									2
								)}%<br />左侧收益率均值(包含此区间)：${((t3 / t3l) * 100).toFixed(2)}%<br />右侧频率(包含此区间)：${(
									(t4l / t5) *
									100
								).toFixed(2)}%<br />右侧收益率均值(包含此区间)：${((t4 / t4l) * 100).toFixed(2)}%`;
							}
						},
						xAxis: [
							{
								show: true,
								name: '收益率分布',
								formatter(value) {
									return (value * 100).toFixed(1) + '%';
								},
								type: 'value',
								min: min,
								max: max,
								axisLabel: {
									interval: interval
								}
							}
						],
						yAxis: [
							{
								name: '次数'
							}
						],
						series: [
							{
								name: '自身收益分布直方图',
								type: 'custom',
								renderItem: that.renderItem,
								encode: {
									x: [0, 1],
									y: 2,
									tooltip: 2,
									label: 2
								},
								data: data
							}
						]
					});
				}
			}
			console.log(this.yejioptions);
			that.loadyejis = false;
		},
		// 监听对比项选择
		changename(value) {
			if (value.split('|').length == 2) {
				this.valuestemp = value.split('|')[0];
				this.index_code_flag = value.split('|')[1];
			} else {
				this.valuestemp = '';
				this.index_code_flag = '';
			}
			this.postData.index_code = this.valuestemp;
			this.postData.index_flag = this.index_code_flag;
			this.resolveFather();
		},
		// 监听时间改变
		changgedate(value) {
			this.flagitem = value;
			this.postData.date_flag = value;
			if (this.indexInfo?.id) {
				this.postData.index_code = this.indexInfo?.id;
				this.postData.index_flag = this.indexInfo?.flag;
			}
			this.resolveFather();
		},
		// 搜索基金/基准
		async searchpeople(query) {
			this.loading = false;
			let res = await Search({
				message: query,
				flag: '1,2,6'
			});
			let data = res.data;
			if (data) {
				let temparr = [
					{
						label: '基金产品',
						options: []
					},
					{
						label: '基金经理',
						options: []
					},
					{
						label: '基金基准',
						options: []
					}
				];
				for (let i = 0; i < data.length; i++) {
					if (data[i].flag == 'fund') {
						temparr[0].options.push(data[i]);
					} else if (data[i].flag == 'index') {
						temparr[2].options.push(data[i]);
					} else if (data[i].flag == 'manager') {
						temparr[1].options.push(data[i]);
					}
				}
				this.havefundmanager = temparr;
			}
		},
		// 向父组件传递数据
		resolveFather() {
			this.loadyejis = true;
			this.$emit('resolveFather', this.postData);
		},
		// 格式化item
		renderItem(params, api) {
			var yValue = api.value(2);
			var start = api.coord([api.value(0), yValue]);
			var size = api.size([api.value(1) - api.value(0), yValue]);
			var style = api.style();
			return {
				type: 'rect',
				shape: {
					x: start[0] + 1,
					y: start[1],
					width: size[0] - 2,
					height: size[1]
				},
				style: style
			};
		}
	}
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
.ml5 {
	margin-left: 5px;
}
.f14ml {
	font-size: 14px !important;
	color: #000000ce;
}
</style>
