{"code": 200, "message": "sed sint in", "stackInfo": null, "traceId": null, "success": true, "data": [{"industryCode": "34", "industryName": "需压变", "ratioList": [{"dateList": ["2020-11-01", "2020-11-02", "2020-11-03", "2020-11-04", "2020-11-05"], "rateList": ["20", "10", "1", "32", "40"]}], "aveRatio": 63, "overRatioList": [{"dateList": ["2020-11-01", "2020-11-02", "2020-11-03", "2020-11-04", "2020-11-05"], "rateList": ["20", "10", "1", "32", "40"]}], "aveOverRatio": "112", "sumExcess": "200", "industryIncome": "115", "bondIncome": "6565", "otherIncome": "-15454"}, {"industryCode": "28", "industryName": "金感地斗主", "ratioList": [{"dateList": ["2020-11-01", "2020-11-02", "2020-11-03", "2020-11-04", "2020-11-05"], "rateList": ["20", "10", "1", "32", "40"]}], "aveRatio": 63, "overRatioList": [{"dateList": ["2020-11-01", "2020-11-02", "2020-11-03", "2020-11-04", "2020-11-05"], "rateList": ["20", "10", "1", "32", "40"]}], "aveOverRatio": "112", "sumExcess": "200", "industryIncome": "115", "bondIncome": "6565", "otherIncome": "-15454"}, {"industryCode": "72", "industryName": "众为下子按东", "ratioList": [{"dateList": ["2020-11-01", "2020-11-02", "2020-11-03", "2020-11-04", "2020-11-05"], "rateList": ["20", "10", "1", "32", "40"]}], "aveRatio": 63, "overRatioList": [{"dateList": ["2020-11-01", "2020-11-02", "2020-11-03", "2020-11-04", "2020-11-05"], "rateList": ["20", "10", "1", "32", "40"]}], "aveOverRatio": "112", "sumExcess": "200", "industryIncome": "115", "bondIncome": "6565", "otherIncome": "-15454"}, {"industryCode": "67", "industryName": "化属算", "ratioList": [{"dateList": ["2020-11-01", "2020-11-02", "2020-11-03", "2020-11-04", "2020-11-05"], "rateList": ["20", "10", "1", "32", "40"]}], "aveRatio": 63, "overRatioList": [{"dateList": ["2020-11-01", "2020-11-02", "2020-11-03", "2020-11-04", "2020-11-05"], "rateList": ["20", "10", "1", "32", "40"]}], "aveOverRatio": "112", "sumExcess": "200", "industryIncome": "115", "bondIncome": "6565", "otherIncome": "-15454"}, {"industryCode": "43", "industryName": "油必六量", "ratioList": [{"dateList": ["2020-11-01", "2020-11-02", "2020-11-03", "2020-11-04", "2020-11-05"], "rateList": ["20", "10", "1", "32", "40"]}], "aveRatio": 63, "overRatioList": [{"dateList": ["2020-11-01", "2020-11-02", "2020-11-03", "2020-11-04", "2020-11-05"], "rateList": ["20", "10", "1", "32", "40"]}], "aveOverRatio": "112", "sumExcess": "200", "industryIncome": "115", "bondIncome": "6565", "otherIncome": "-15454"}]}