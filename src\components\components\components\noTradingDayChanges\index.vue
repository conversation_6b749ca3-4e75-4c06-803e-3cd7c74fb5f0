<template>
	<div class="homebodyfontsize">
		<div style="margin-top: 10px">
			<el-row :gutter="10">
				<el-col :span="24">
					<el-card>
						<div slot="header" style="display: flex" class="clearfix">
							<div class="points"></div>
							<span
								>基金非交易日收益率变动<el-tooltip
									class="item"
									effect="dark"
									content="基金半年报和年报会披露个人投资者和机构投资者持有比例，近年还增加披露了前十大持有人的持 有比例，慧捕基根据货币基金定期报告披露信息对其进行了分析展示，国金众赢货币在 季度末的不同投资者持有占比以及过去三年的平均占比。 "
									placement="right-start"
								>
									<svg width="14" height="14" viewBox="0 0 14 14" fill="none">
										<path
											fill-rule="evenodd"
											clip-rule="evenodd"
											d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
											fill="black"
											fill-opacity="0.45"
										/>
									</svg> </el-tooltip
							></span>
						</div>
						<v-chart
							v-loading="empytzichanpeizh"
							element-loading-text="暂无数据"
							element-loading-spinner="el-icon-document-delete"
							element-loading-background="rgba(239, 239, 239, 0.5)"
							style="width: 100%; height: 50vh"
							autoresize
							:options="peizhioptions"
						/>
						<!-- <div>过去的 3 年中，国金众赢货币个人投资者平均持有比例为 86.7%, 机构投资者平均持有比例为 13.3%, 前十大持有人平均持有比例为 16.5%” [1] “最新季报中披露的个人投资者持有比例为国金众 赢货币, 机构投资者持有比例为 71.0%, 前十大持有人持有比例为 29.0%</div> -->
					</el-card>
				</el-col>
			</el-row>
			<div style="height: 20px"></div>
			<el-row :gutter="10">
				<el-col :span="24">
					<el-card>
						<div slot="header" style="display: flex" class="clearfix">
							<div class="points"></div>
							<span
								>基金不同时间段非交易日万份收益<el-tooltip
									class="item"
									effect="dark"
									content="基金半年报和年报会披露个人投资者和机构投资者持有比例，近年还增加披露了前十大持有人的持 有比例，慧捕基根据货币基金定期报告披露信息对其进行了分析展示，国金众赢货币在 季度末的不同投资者持有占比以及过去三年的平均占比。 "
									placement="right-start"
								>
									<svg width="14" height="14" viewBox="0 0 14 14" fill="none">
										<path
											fill-rule="evenodd"
											clip-rule="evenodd"
											d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
											fill="black"
											fill-opacity="0.45"
										/>
									</svg> </el-tooltip
							></span>
						</div>

						<el-table :data="tabledatas" class="table" ref="multipleTable" header-cell-class-name="table-header">
							<el-table-column sortable prop="date" label="时间" align="center">
								<template slot-scope="scope">{{ scope.row.date | fixd }}</template>
							</el-table-column>
							<el-table-column sortable prop="ret" label="本基金近 28 个非交易日日均万份收益（元）" align="center">
								<template slot-scope="scope">{{ scope.row.ret | fix3 }}</template>
							</el-table-column>
							<el-table-column sortable prop="market_ret" align="center" label=" 市场均值（元）">
								<template slot-scope="scope">{{ scope.row.market_ret | fix3 }}</template>
							</el-table-column>
							<el-table-column sortable prop="rank" align="center" label="本基金近 28 个非交易日日均万份收益排名分位">
								<template slot-scope="scope">{{ scope.row.rank | fix3 }}</template>
							</el-table-column>
						</el-table>
					</el-card>
				</el-col>
			</el-row>
		</div>
	</div>
</template>
<script>
import axios from '@/api/index.js';
import { getUntradday } from '@/api/pages/Analysis.js';
export default {
	filters: {
		fix6(value) {
			return value.substring(0, 10);
		},
		fix3(value) {
			return parseInt(value * 1000) / 1000;
		},
		fix4bf(value) {
			return (value * 100).toFixed(2) + '%';
		},
		fixd(value) {
			return '近' + value / 30 + '月';
		}
	},
	data() {
		return {
			empytzichanpeizh: false,
			peizhioptions: {},
			loadflag: 0,
			tabledatas: [],
			info: {}
		};
	},
	methods: {
		getData(info) {
			this.info = info;
			this.getstyle();
		},
		async getstyle() {
			let data = await getUntradday({ code: this.info.code, type: this.info.type, flag: this.info.flag });
			if (data?.mtycode == 200) {
				this.tabledatas = data.data.untradday_ret_table;
				this.peizhioptions = {
					title: {
						textStyle: {
							fontSize: 14
						},
						text: ' 基金非交易日收益率变动'
					},
					tooltip: {
						textStyle: {
							fontSize: 14
						},
						trigger: 'axis',
						axisPointer: {
							// 坐标轴指示器，坐标轴触发有效
							type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
						}
					},
					grid: {},
					legend: {
						textStyle: {
							fontSize: 14
						}
					},
					xAxis: [
						{
							nameTextStyle: {
								fontSize: 14
							},
							axisLabel: {
								show: true,
								textStyle: {
									fontSize: 14
								}
							},
							type: 'category',
							data: data.data.date,
							axisTick: {
								alignWithLabel: true
							}
						}
					],
					yAxis: [
						{
							nameTextStyle: {
								fontSize: 14
							},
							axisLabel: {
								show: true,
								textStyle: {
									fontSize: 14
								}
							},
							type: 'value',
							name: '非交易日日均万份收益'
						}
					],
					series: [
						{
							name: '本产品非交易日日均万份收益',
							type: 'scatter',
							// barWidth: '60%',
							symbolSize: 3,
							data: data.data.untradingdayRet
						},
						{
							name: '全市场货币基金非交易日日均万份收益',
							type: 'scatter',
							// barWidth: '60%',
							symbolSize: 3,
							data: data.data.aveUntradingdayRet
						}
					]
				};
			}
		}
	}
};
</script>
<style>
.wavePatternss {
	width: 60vw;
	height: 20vh;
}
</style>
<style lang="scss" scoped>
.managerDetailPage {
	width: 98%;
	padding: 10px;
}

.row {
	margin: -10px;
	display: flex;
}

.left {
	width: 155px;
	flex: 0 0 auto;
	margin-right: 10px;
}

.right {
	position: relative;
	flex: 1 1 100px;
}
</style>
<style lang="scss">
.comment-section {
	padding: 5px 15px 0 15px;
}

.comment {
	background: linear-gradient(90deg, #3b64f2, #1b8eff);
	color: white;
	font-size: 12px;
	padding: 12px 24px;
}

.comment.center {
	text-align: center;
}

.section {
	padding: 15px 15px 0 15px;
}

.double-table {
	display: flex;
	flex-basis: 10px;
	justify-content: space-between;

	.single-table {
		flex: 1;
	}

	.cell {
		font-size: 14px !important;
		font-weight: 400 !important;
		text-align: center !important;
		padding: 0 !important;
	}

	th {
		padding: 5px 0 !important;
	}
}

.split-cell {
	display: flex;
	align-items: center;
	justify-content: center;

	div {
		width: 40px;
	}
}
</style>
<style lang="scss" scoped>
.title {
	background: #f3f4f8;
	font-weight: 600;
	padding: 5px 15px;
}
.sub-title {
	font-size: 14px;
	font-weight: 600;
	border-left: 2px solid dodgerblue;
	margin-bottom: 6px;
	padding-left: 3px;
	line-height: 22px;
	height: 22px;
	flex: 1 1 auto;
}
.title-change-fund {
	display: flex;
	align-items: center;
	margin-bottom: 4px;
	label {
		font-size: 14px;
		margin-right: 10px;
	}
}
.backbut {
	position: absolute;
	right: 5px;
	bottom: 10px;
	top: 10px;
	margin: auto;
}
</style>
