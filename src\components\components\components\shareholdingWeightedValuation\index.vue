<template>
	<div>
		<analysis-card-title title="持股加权估值水平" @downloadExcel="exportExcel"></analysis-card-title>
		<div style="width: 100%">
			<el-table
				v-loading="loading"
				:data="value"
				style="width: 99%"
				height="400px"
				class="table"
				ref="multipleTable"
				:default-sort="{ prop: 'yearqtr', order: 'descending' }"
				header-cell-class-name="table-header"
			>
				<el-table-column v-for="item in column" :key="item.value" align="gotoleft" :prop="item.value" :label="item.label" sortable>
					<template #header>
						<long-table-popover-chart
							v-if="item.popover"
							:data="formatTableData()"
							date_key="yearqtr"
							:data_key="item.value"
							:show_name="item.label"
						>
							<span>{{ item.label }}</span>
						</long-table-popover-chart>
						<span v-else>{{ item.label }}</span>
					</template>
					<template slot-scope="{ row }">
						<span>{{ item.format ? item.format(row[item.value]) : row[item.value] }}</span>
					</template>
				</el-table-column>
				<template slot="empty">
					<el-empty image-size="160"></el-empty>
				</template>
			</el-table>
		</div>
	</div>
</template>

<script>
import { exportTitle, exportTable } from '@/utils/exportWord.js';
import { filter_json_to_excel } from '@/utils/exportExcel.js';

// 持股加权估值水平
import { getStockHistoryHold } from '@/api/pages/Analysis.js';

// 持股加权估值水平
export default {
	name: 'shareholdingWeightedValuation',
	data() {
		return {
			value: [],
			loading: true,
			column: [
				{
					label: '季度',
					value: 'yearqtr',
					popover: false
				},
				{
					label: '市净率',
					value: 'pb',
					format: this.fix3,
					popover: true
				},
				{
					label: '市盈率',
					value: 'pe',
					format: this.fix3,
					popover: true
				},
				{
					label: '市现率',
					value: 'pcfttm',
					format: this.fix3,
					popover: true
				},
				{
					label: '市净率分位',
					value: 'pblevel',
					format: this.fix3,
					popover: true
				},
				{
					label: '市盈率分位',
					value: 'pelevel',
					format: this.fix3,
					popover: true
				},
				{
					label: '市现率分位',
					value: 'pcflevel',
					format: this.fix3,
					popover: true
				}
			],
			info: {}
		};
	},
	methods: {
		// 持仓加权盈利能力
		async getPositionWeighted() {
			let postData = {
				flag: this.info.flag,
				code: this.info.code,
				type: this.info.type,
				feature_flag: '3',
				start_date: this.info.start_date,
				end_date: this.info.end_date
			};
			let data = await getStockHistoryHold(postData);
			if (data?.mtycode == 200) {
				this.loading = false;
				this.value = data?.data;
			}
		},
		getData(info) {
			this.info = info;
			this.getPositionWeighted();
		},
		fix3(value) {
			return !value || parseInt(value * 1000) == NaN ? '--' : parseInt(value * 1000) / 1000;
		},
		formatTableData() {
			let data = [];
			this.value.map((item) => {
				let obj = { ...item };
				for (const key in item) {
					let format = this.column.find((obj) => {
						return obj.value == key;
					})?.format;
					if (format) {
						let val = format(item[key]);
						obj[key] = typeof val == 'string' ? (val.includes('%') ? val?.split('%')?.[0] * 1 : !isNaN(val) ? val : val) : val;
					}
				}
				data.push(obj);
			});
			return data;
		},
		exportExcel() {
			let list = [
				{
					label: '季度',
					fill: 'header',
					value: 'yearqtr'
				},
				{
					label: '市净率',
					value: 'pb',
					format: 'fix3'
				},
				{
					label: '市盈率',
					value: 'pe',
					format: 'fix3'
				},
				{
					label: '市现率',
					value: 'pcfttm',
					format: 'fix3'
				},
				{
					label: '市净率分位',
					value: 'pblevel',
					format: 'fix3'
				},
				{
					label: '市盈率分位',
					value: 'pelevel',
					format: 'fix3'
				},
				{
					label: '市现率分位',
					value: 'pcflevel',
					format: 'fix3'
				}
			];
			filter_json_to_excel(
				list,
				this.value.sort((a, b) => {
					return this.moment(this.moment(a.yearqtr, 'YYYY QQ').format()).isAfter(this.moment(b.yearqtr, 'YYYY QQ').format()) ? -1 : 1;
				}),
				'持股加权估值水平(近年)'
			);
		},
		createPrintWord() {
			let list = [
				{
					label: '季度',
					fill: 'header',
					value: 'yearqtr'
				},
				{
					label: '市净率',
					value: 'pb',
					format: 'fix3'
				},
				{
					label: '市盈率',
					value: 'pe',
					format: 'fix3'
				},
				{
					label: '市现率',
					value: 'pcfttm',
					format: 'fix3'
				},
				{
					label: '市净率分位',
					value: 'pblevel',
					format: 'fix3'
				},
				{
					label: '市盈率分位',
					value: 'pelevel',
					format: 'fix3'
				},
				{
					label: '市现率分位',
					value: 'pcflevel',
					format: 'fix3'
				}
			];
			if (this.value.length) {
				return [
					...exportTitle('持股加权估值水平'),
					...exportTable(
						list,
						this.value
							.sort((a, b) => {
								return this.moment(this.moment(a.yearqtr, 'YYYY QQ').format()).isAfter(this.moment(b.yearqtr, 'YYYY QQ').format()) ? -1 : 1;
							})
							.slice(0, 10),
						'',
						true
					)
				];
			} else {
				return [];
			}
		}
	}
};
</script>

<style></style>
