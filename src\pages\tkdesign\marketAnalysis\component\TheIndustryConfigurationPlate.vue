<template>
    <div>
        <heavyWarehouseInfomation :title="title" :tableData="tableData" key="industry" marketType="industry"></heavyWarehouseInfomation>
    </div>
</template>
<script>
import heavyWarehouseInfomation from './heavyWarehouseInfomation.vue';
import { getHeavyIndustryList } from '@/api/pages/tkAnalysis/captial-market.js';

export default {
    name:'TheIndustryConfigurationPlate',
    components:{
        heavyWarehouseInfomation
    },
    data(){
        return {
            title: '行业配置'
        }
    },
    methods: {
        getData(){
            getHeavyIndustryList({
                pageSize: 10,
                pageNum: 1,
            }).then(res=>{
                this.tableData = res.data;
            })
        }
    },
    created(){
        // this.getData()
    }
}
</script>
<style lang="scss" scoped>

</style>