<template>
  <div class="plate-wrapper financial-indicator-wrapper"
       v-loading="loading">
    <VerticalLineHeader title="各类指数财务指标表"
                        showDownloadBtn
                        @downloadClick="exportExcel">
      <template slot="right">
        <el-form ref="form"
                 :model="form"
                 label-width="80px"
                 class="title-right-form">
          <QuickTimePicker class="radio-group-wrapper"
                           v-model="dateSelect"
                           @change="handleFormChange"></QuickTimePicker>
        </el-form>
      </template>
    </VerticalLineHeader>
    <div>
      <!-- <el-radio-group class="lq-radio-group type-radio-group-wrapper" :value="form.indexType" @input="handleIndicatorChange" size="small">
            <el-radio-button label="left" @click.native="handleIndicatorChange('custom')">自选指数</el-radio-button>
            <el-radio-button label="right1">全行业指数</el-radio-button>
            <el-radio-button label="right2">规模指数</el-radio-button>
            <el-radio-button label="right3">规模指数</el-radio-button>
            <el-radio-button label="right4">泰康分类</el-radio-button>
            <el-radio-button label="right5">全球主要指数</el-radio-button>
      </el-radio-group>-->
      <div style="display: flex; justify-content: space-between">
        <RadioGroup ref="RadioGroup"
                    class="lq-radio-group type-radio-group-wrapper radio-group-wrapper"
                    :configList="configList"
                    :defaultValue="defaultValue"
                    @change="handleIndicatorChange"></RadioGroup>
        <div>
          <el-button size="small"
                     @click="openCustomIndex">自定义指数</el-button>
        </div>
      </div>
      <el-dialog title="自定义指数"
                 width="80%"
                 :visible.sync="dialogFormVisible">
        <TransferBox ref="TransferBox"
                     @selectChange="handleCodeSelectChange"></TransferBox>
        <div slot="footer"
             class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取 消</el-button>
          <el-button type="primary"
                     @click="handleCustomSelect">确 定</el-button>
        </div>
      </el-dialog>
    </div>
    <el-table class="financial-indicator-table-wrapper"
              style="width: 100%"
              :data="tableDataNow"
              :stripe="true"
              :border="true"
              @sort-change="handleSortChange">
      <el-table-column prop="indexName"
                       label="指数名称"
                       align="gotoleft"
                       width="180"></el-table-column>
      <el-table-column label="财务指标及其分位数">
        <el-table-column prop="measure_PETTM"
                         :formatter="dataFormatter"
                         align="gotoleft"
                         label="PE-TTM"
                         sortable="custom"
                         width="180"></el-table-column>
        <el-table-column prop="percentile_PETTM"
                         :formatter="dataFormatter"
                         label="百分位"
                         sortable="custom"
                         align="gotoleft"></el-table-column>
        <el-table-column prop="measure_PB"
                         :formatter="dataFormatter"
                         label="PB"
                         sortable="custom"
                         align="gotoleft"></el-table-column>
        <el-table-column prop="percentile_PB"
                         :formatter="dataFormatter"
                         label="百分位"
                         sortable="custom"
                         align="gotoleft"></el-table-column>
        <el-table-column prop="measure_股息率"
                         :formatter="dataFormatter"
                         label="股息率"
                         sortable="custom"
                         align="gotoleft"></el-table-column>
        <el-table-column prop="percentile_股息率"
                         :formatter="dataFormatter"
                         label="百分位"
                         sortable="custom"
                         align="gotoleft"></el-table-column>
        <el-table-column prop="measure_PEG"
                         :formatter="dataFormatter"
                         label="PEG"
                         sortable="custom"
                         align="gotoleft"></el-table-column>
        <el-table-column prop="percentile_PEG"
                         :formatter="dataFormatter"
                         label="百分位"
                         sortable="custom"
                         align="gotoleft"></el-table-column>
        <el-table-column :prop="'measure_' + roeSelect"
                         :formatter="dataFormatter"
                         label="ROE"
                         sortable="custom"
                         align="gotoleft">
          <template slot="header">
            <el-dropdown @command="handleCommand">
              <span class="el-dropdown-link">
                {{ roeSelect }}
                <i class="el-icon-arrow-down el-icon--right"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="ROE">ROE</el-dropdown-item>
                <el-dropdown-item command="ROIC">ROIC</el-dropdown-item>
                <el-dropdown-item command="ROA">ROA</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
        <el-table-column :prop="'percentile_' + roeSelect"
                         :formatter="dataFormatter"
                         label="百分位"
                         sortable="custom"
                         align="gotoleft"></el-table-column>
        <!-- <el-table-column prop="optDate"
                         label="时间区间"
                         sortable="custom"
                         align="gotoleft"></el-table-column> -->
      </el-table-column>
    </el-table>
    <el-pagination style="display: flex; justify-content: right; padding-top: 16px; padding-bottom: 16px"
                   class="pagination-footer-wrapper"
                   @size-change="handleSizeChange"
                   @current-change="handleCurrentChange"
                   :current-page.sync="pageInfo.currentPage"
                   :page-sizes="[100, 200, 300, 400]"
                   :page-size="pageInfo.pageSize"
                   layout="total, sizes, prev, pager, next, jumper"
                   :total="pageInfo.total"></el-pagination>
  </div>
</template>
<script>
import VerticalLineHeader from './VerticalLineHeader.vue';
import QuickTimePicker from './QuickTimePicker.vue';
import TransferBox from '../../components/TransferBoxNew.vue';
import { getFinancialIndexList, getIndexCode } from '@/api/pages/tkAnalysis/captial-market.js';
import stringTool from '../../components/string.tool';
import RadioGroup from './RadioGroup.vue';
import { filter_json_to_excel } from '../../../../utils/exportExcel';
export default {
  name: 'TheFinancialIndicatorPlate',
  components: {
    VerticalLineHeader,
    QuickTimePicker,
    TransferBox,
    RadioGroup
  },
  data () {
    return {
      loading: false,
      selectList: [],
      defaultValue: {
        radioValue: 'scale'
      },
      dateSelect: {
        radioValue: '1'
      },
      form: {
        indexType: 'scale',
        codes: []
      },
      pageInfo: {
        pageSize: 100,
        currentPage: 0,
        total: 0
      },
      tableData: [],
      roeSelect: 'ROE',
      dialogFormVisible: false,
      indicatorRadioList: [],
      configList: [
        { label: 'custom', text: '自选指数', option: [] },
        { label: 'industry', text: '全行业指数', option: [] },
        { label: 'scale', text: '规模指数', option: [] },
        { label: 'style', text: '风格指数', option: [] },
        { label: 'taikang', text: '泰康分类', option: [] },
        { label: 'global', text: '全球主要指数', option: [] }
      ],
      //弹窗正在选中的值，只有点确认才会同步至页面
      boxCodeList: [],
      //用于请求的值
      codes: [],
      tableDataNow: [],
      //暂时只用于导出
      tableHeader: [
        {
          prop: 'indexName',
          label: '指数名称'
        },
        {
          prop: 'measure_PETTM',
          label: 'PE-TTM'
        },
        {
          prop: 'percentile_PETTM',
          label: '全历史百分位'
        },
        {
          prop: 'measure_PB',
          label: 'PB'
        },
        {
          prop: 'percentile_PB',
          label: '全历史百分位'
        },
        {
          prop: 'measure_股息率',
          label: '股息率'
        },
        {
          prop: 'percentile_股息率',
          label: '百分位'
        },
        {
          prop: 'measure_PEG',
          label: 'PEG'
        },
        {
          prop: 'percentile_PEG',
          label: '百分位'
        },
        {
          prop: 'measure_ROE',
          label: 'ROE'
        },
        {
          prop: 'percentile_ROE',
          label: '百分位'
        },
        {
          prop: 'measure_ROIC',
          label: 'ROIC'
        },
        {
          prop: 'percentile_ROIC',
          label: '百分位'
        },
        {
          prop: 'measure_ROA',
          label: 'ROA'
        },
        {
          prop: 'percentile_ROA',
          label: '百分位'
        }
      ]
    };
  },
  mounted () {
    if (this.localStorage.getItem('TheFinancialIndicatorPlate')) {
      let key_list = ['form', 'dateSelect', ' codes', 'defaultValue'];
      for (let key of key_list) {
        this[key] = this.localStorage.getItem('TheFinancialIndicatorPlate')?.[key] || this[key];
      }
      let index = this.configList.findIndex((v) => v.label == this.defaultValue.radioValue);
      this.$set(this.configList, index, { ...this.configList[index], value: this.defaultValue.selectValue });
      this.$refs['RadioGroup'].setValue(this.defaultValue);
    }
    this.getData();
  },
  methods: {
    // 打开自定义指数弹窗
    openCustomIndex () {
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs['TransferBox'].setValue(this.codes);
      });
    },
    // 导出excel
    exportExcel () {
      console.log(this.tableHeader);
      let list = this.tableHeader.map((item) => {
        return {
          ...item,
          value: item.prop,
          format: ''
        };
      });
      filter_json_to_excel(list, this.tableData, '各类指数财务指标表');
    },
    handleSortChange ({ column, prop, order }) {
      console.log(column, prop, order);
      this.tableData.sort((item1, item2) => {
        const a1 = item1[prop] || 0;
        const a2 = item2[prop] || 0;
        let orderVal = order === 'ascending' ? -(a1 - a2) : a1 - a2;
        return orderVal;
      });
      console.log(this.tableData);
      this.dulData();
    },
    handleSizeChange (value) {
      this.pageInfo.currentPage = 1;
      this.pageInfo.pageSize = value;
      this.dulData();
    },
    handleCurrentChange (value) {
      this.pageInfo.currentPage = value;
      this.dulData();
    },
    handleFormChange () {
      console.log('**', this.form.optDate);
      this.pageInfo.currentPage = 1;
      this.getData();
    },
    dataFormatter (row, column, cellValue, index) {
      let arr = ['PE-TTM', 'PB', 'PEG', 'ROE', 'ROIC', 'ROA'];
      if (arr.includes(column.label)) {
        return stringTool.fix2(cellValue);
      }
      return stringTool.fix2pxxx(cellValue);
    },
    // 获取列表数据
    async getData () {
      this.loading = true;
      let optDate = this.dateSelect?.radioValue === 'custom' ? '' : this.dateSelect?.radioValue;
      let { startDate, endDate } = this.dateSelect || {};
      let params = {
        ...this.form,
        optDate,
        startDate,
        endDate
      };
      if (this.form.indexType === 'custom') {
        if (this.codes && this.codes.length > 0) {
          params.codes = this.codes;
        } else {
          this.$message.warning('请选择自定义指数');
          this.loading = false;
          return;
        }
      }
      this.localStorage.setItem('TheFinancialIndicatorPlate', {
        form: this.form,
        dateSelect: this.dateSelect,
        codes: this.codes,
        defaultValue: this.defaultValue
      });
      let req = await getFinancialIndexList(params);
      this.loading = false;
      let { data, code, message } = req || {};
      if (code == 200) {
        // this.pageInfo.total = data?.total || 0
        // this.pageInfo.currentPage = data?.currentPage || 1
        // this.pageInfo.pageSize = data?.pageSize || 20
        let { dataList, endDate, startDate } = data;
        this.tableData = dataList || [];
        this.pageInfo.total = this.tableData.length;
        this.tableData = this.tableData.map((item) => {
          let measureList = item.measureList || [];
          let obj = {};
          measureList = measureList.forEach((element) => {
            let measureName = 'measure_' + element.measure;
            obj[measureName] = element.meter;
            let percentileName = 'percentile_' + element.measure;
            obj[percentileName] = element.percentile;
          });

          return {
            optDate: startDate + '~' + endDate,
            indexName: item.indexName,
            ...obj
          };
        });
      } else {
        this.$message.warning(message);
        this.tableData = [];
        this.tableDataNow = [];
      }
      this.dulData();
    },
    dulData () {
      let { currentPage, pageSize } = this.pageInfo;
      this.tableDataNow = this.tableData.slice((currentPage - 1) * pageSize, currentPage * pageSize);
      console.log(this.tableDataNow, '***');
    },
    // async getRadioList(){
    //     let params={}
    //     let  req= await getFinancialIndexList(params)
    //     let { data, code, message } =req||{}
    //     if (code == 200) {
    //         // this.pageInfo.total = data?.total || 0
    //         // this.pageInfo.currentPage = data?.currentPage || 1
    //         // this.pageInfo.pageSize = data?.pageSize || 20
    //         this.tableData = data?.dataList
    //         this.pageInfo.total = this.tableData.length
    //         this.dulData()
    //     }
    //     else {
    //         this.tableData = []
    //         // this.pageInfo={
    //         //     total:0,
    //         //     currentPage:1,
    //         //     pageSize:20
    //         // }
    //     }

    // },
    handleCommand (command) {
      this.roeSelect = command;
    },
    //指数类型选择时
    handleIndicatorChange (value) {
      this.form.indexType = value.radioValue;
      //自选指数
      // if(this.form.indexType==='custom'){
      //     this.dialogFormVisible=true
      //     return
      // }
      this.defaultValue = value;
      this.getData();
    },
    //自定义指数选择确认
    handleCustomSelect () {
      this.codes = this.boxCodeList;
      // this.handleFormChange();
      this.dialogFormVisible = false;
    },
    handleCodeSelectChange (value = []) {
      this.boxCodeList = value.map((item) => {
        return item.value;
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.financial-indicator-wrapper {
	overflow: hidden;
	.type-radio-group-wrapper {
		padding-bottom: 20px;
	}
}
</style>
