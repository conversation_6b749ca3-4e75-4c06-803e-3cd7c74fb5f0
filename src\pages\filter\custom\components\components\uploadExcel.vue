<template>
	<div class="alphaownpool">
		<div class="remind">
			<span style="margin-left: 10px; font-size: 14px; font-weight: 400; color: #000000d9">上传前请先按Excel模板中的格式编辑内容</span>
			<img style="margin-left: 12px" :src="require('../../../../../../public/images/excelImg.svg')" alt="Excel" />
			<a href="./samplepool.xlsx" download="示例excel模板">下载Excel模板</a>
		</div>
		<el-upload
			style="margin-top: 10px"
			class="upload-demo"
			drag
			:action="action1"
			:headers="headersss"
			:on-preview="handlePreview"
			:on-remove="handleRemove"
			:on-error="errorok"
			:on-success="successok"
			:auto-upload="false"
			:before-remove="beforeRemove"
			:multiple="false"
			:limit="1"
			:on-change="loadJsonFromFile"
			ref="upload"
			:before-upload="beforeAvatarUpload"
			:on-exceed="handleExceed"
			:file-list="formfile"
		>
			<div class="upBox">
				<i class="el-icon-upload"></i>
				<div>{{ categoryId }}</div>
				<div class="el-upload__text">将文件拖拽至此区域，或<em>点击添加</em></div>
				<div class="el-upload__tip" slot="tip">支持.xls，.xlsx 格式，限500kb以内</div>
			</div>
		</el-upload>
		<div class="footer" style="margin-top: 5px">
			<div style="font-size: 16px; font-weight: 600">导入规则</div>
			<div style="margin-top: 5px">
				<div class="one" style="font-size: 14px; font-weight: 500">1.请先下载模板，在模板中按要求填写信息，然后上传该文件。</div>
				<div class="two" style="font-size: 14px; font-weight: 500">2.文件大小不超过500KB</div>
			</div>
		</div>
	</div>
</template>
<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';`
import axios from '@/api/index.js';
import store from '@/store/store';
import { VueEasyJwt } from 'vue-easy-jwt';
const jwt = new VueEasyJwt();
export default {
	//import引入的组件需要注入到对象中才能使用
	props: {
		categoryId: { type: Number },
		append: { type: Boolean }
	},
	computed: {
		action1() {
			return this.$baseUrl + '/start/fund/pool/importByFile?categoryId=' + this.categoryId + '&append=' + this.append;
		}
	},
	data() {
		//这里存放数据
		return {
			id: '',
			headersss: null,
			alphaownshow: false,
			formfile: null,
			file: null,
			sampledata: [
				{ code: '000001', weight: '50' },
				{ code: '000057', weight: '30' },
				{ code: '110011', weight: '20' },
				{ code: '......', weight: '0' }
			]
		};
	},
	//方法集合
	methods: {
		beforeAvatarUpload(file) {
			var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1);
			const extension = testmsg === 'xls';
			const extension2 = testmsg === 'xlsx';
			const isLt2M = file.size / 1024 / 1024 < 0.5;
			if (!extension && !extension2) {
				this.$message.error('上传文件类型只能是.xls或者.xlsx格式');
				return false;
			}
			if (!isLt2M) {
				this.$message.error('上传文件大小不能超过 500KB!');
			}
			return (extension || extension2) && isLt2M;
		},
		loadJsonFromFile(file, fileList) {
			this.file = file;
			this.formfile = fileList;
		},
		submit() {
			this.$refs.upload.submit();
		},
		errorok() {
			this.$message.error('上传失败');
		},
		showitem(val) {
			this.id = val;
			this.alphaownshow = true;
		},
		successok() {
			this.alphaownshow = false;
			this.$message.success('上传成功');
			this.$emit('refrshtable');
			this.$refs.upload.clearFiles();
		},
		handleRemove(file, fileList) {
			//console.log(file, fileList);
		},
		handlePreview(file) {
			//console.log(file);
		},
		handleExceed(files, fileList) {
			this.$message.warning(`当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
		},
		beforeRemove(file, fileList) {
			return this.$confirm(`确定移除 ${file.name}？`);
		}
	},
	//生命周期 - 创建完成（可以访问当前this实例）
	created() {
		this.headersss = {
			Authorization: store.state.token
		};
	}
};
</script>
<style scoped>
.leftmargin20 {
	margin-left: 10px !important;
}

.remind {
	width: 100%;
	height: 42px;
	background-color: #f5f5f5;
	display: flex;
	align-items: center;
}

.upload-demo {
	/* width: 100%;
	height: 700px; */
}
.savemodelfile {
	border-top: 1px solid #e5e5e5;
	border-bottom: 1px solid #e5e5e5;
	padding: 20px;
}
</style>
<style>
.alphaownpool .el-dialog__body {
	padding: 10px 20px !important;
}
.el-upload {
	width: 100%;
	height: 350px;
}
.el-upload-dragger {
	width: 100% !important;
	height: 100% !important;
}
.upBox {
	margin-top: 55px;
	/* display: flex;
	justify-content: center;
	align-items: center; */
}
</style>
