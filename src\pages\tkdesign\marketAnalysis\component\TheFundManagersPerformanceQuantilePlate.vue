<template>
  <div class="plate-wrapper fund-performance-wrapper">
    <VerticalLineHeader title="按不同时间段排序的基金经理业绩"
                        showDownloadBtn
                        @downloadClick="exportExcel">
      <template slot="right">
        <el-form ref="form"
                 :model="form"
                 label-width="80px"
                 class="title-right-form">
          <el-form-item size="small"
                        label="截止日期:">
            <el-date-picker value-format="yyyy-MM-dd"
                            type="date"
                            placeholder="选择日期"
                            v-model="deadline"
                            @change="intervalChange"
                            style="width: 100%"></el-date-picker>
          </el-form-item>
          <el-radio-group class="radio-group-wrapper"
                          v-model="dateFlag"
                          size="small"
                          @input="intervalChange">
            <el-radio-button label="0">近期业绩</el-radio-button>
            <el-radio-button label="1">自然年份业绩</el-radio-button>
          </el-radio-group>
        </el-form>
      </template>
    </VerticalLineHeader>
    <div class="select-form-wrapper">
      <RadioGroup ref="RadioGroup"
                  class="radio-group-wrapper"
                  :defaultValue="defaultValue"
                  :configList="configList"
                  @change="handleTypeChange"></RadioGroup>
    </div>
    <el-table class="content-table-wrapper"
              style="width: 100%"
              :data="tableDataNow"
              :stripe="true"
              :border="true"
              v-loading="loading"
              @sort-change="sortChange">
      <template v-for="(item, index) in tableHeader">
        <el-table-column :prop="item.prop"
                         :label="item.label"
                         min-width="120"
                         :sortable="index === 0 ? false : 'custom'"
                         align="gotoleft"
                         :key="index"
                         :formatter="item.format">
          <template slot="header"
                    v-if="item.prop === 'customTime'">
            区间收益
            <DatePickerBtn @change="handleDateChange"
                           @click.native.stop></DatePickerBtn>
          </template>
          <template slot-scope="{ row }">
            <div v-if="item.prop == 'fundManager'">
              <el-link @click="goDetail(row.managerCode, row.fundManager)">{{ row.fundManager }}</el-link>
            </div>
            <div v-else>{{ item.format ? item.format(row[item.prop]) : row[item.prop] }}</div>
          </template>
        </el-table-column>
      </template>
    </el-table>
    <el-pagination style="display: flex; justify-content: right; padding-top: 16px; padding-bottom: 16px"
                   class="pagination-footer-wrapper"
                   @size-change="handleSizeChange"
                   @current-change="handleCurrentChange"
                   :current-page.sync="pageInfo.currentPage"
                   :page-sizes="[10, 20, 30, 40]"
                   :page-size="pageInfo.pageSize"
                   layout="total, sizes, prev, pager, next, jumper"
                   :total="pageInfo.total">
    </el-pagination>
  </div>
</template>
<script>
import RadioGroup from './RadioGroup.vue';
import VerticalLineHeader from './VerticalLineHeader.vue';
import { performanceSort, getFundCode } from '@/api/pages/tkAnalysis/captial-market.js';
import DatePickerBtn from './DatePickerBtn.vue';
import stringTool from '@/pages/tkdesign/components/string.tool';
import { filter_json_to_excel } from '@/utils/exportExcel.js';
import { alphaGo } from '@/assets/js/alpha_type.js';
import { format } from '@/utils/getfulldate';
export default {
  name: 'TheFundManagersPerformanceQuantilePlate',
  components: {
    VerticalLineHeader,
    DatePickerBtn,
    RadioGroup
  },
  data () {
    return {
      deadline: '',
      interval: '5',
      dateFlag: '0',
      form: {},
      loading: true,
      defaultValue: {
        radioValue: 'industry'
        // selectValue:{name:'动态市盈率',value:'pe'}
      },
      tableData: [],
      selectList: {},
      selectVal: {
        industry: [],
        theme: [],
        type: [],
        scale: [],
        TKIndex: [],
        style: []
      },
      pageInfo: {
        pageSize: 10,
        currentPage: 0,
        total: 0
      },
      firstType: '',
      subType: [],
      configList: [
        { type: 'select', value: '', label: 'type', text: '类型', option: [{ label: '全部类型' }] },
        { type: 'select', value: '', label: 'industry', text: '行业', option: [{ label: '全部行业', value: [] }] },
        { type: 'select', value: '', label: 'theme', text: '主题', option: [{ label: '全部主题', value: [] }] },
        { type: 'select', value: '', label: 'optionalPool', text: '自选池', option: [{ label: '全部自选池', value: [] }] },
        { type: 'select', value: '', label: 'taikang', text: '泰康分类', option: [{ label: '全部泰康分类', value: [] }] },
        { type: 'select', value: '', label: 'style', text: '风格', option: [{ label: '全部风格', value: [] }] }
      ],
      tableDataNow: [],
      fundTypeKeyList: {
        industryList: {
          key: 'industry',
          label: '行业'
        },
        themeList: {
          key: 'theme',
          label: '主题'
        },
        typeList: {
          key: 'type',
          label: '类型'
        },
        scaleList: {
          key: 'scale',
          label: '规模'
        },
        TKIndexList: {
          key: 'taikang',
          label: '泰康自定义行业'
        },
        styleList: {
          key: 'style',
          label: '风格'
        },
        taikangList: {
          key: 'taikangIndex',
          label: '泰康'
        },
        poolList: {
          key: 'poolIndex',
          label: '自选'
        }
      },
      tableHeader: [
        {
          prop: 'fundManager',
          label: '基金经理'
        },
        {
          prop: 'fundScale',
          label: '最新规模(亿)',
          format: this.formatterScale
        },
        {
          prop: 'yearToDate',
          label: '年初至今',
          format: this.formatter
        },
        {
          prop: 'lastWeek',
          label: '近一周',
          format: this.formatter
        },
        {
          prop: 'lastMounth',
          label: '近一月',
          format: this.formatter
        },
        {
          prop: 'lastSeason',
          label: '近一季',
          format: this.formatter
        },
        {
          prop: 'lastHalfYears',
          label: '近半年',
          format: this.formatter
        },
        {
          prop: 'lastYear',
          label: '近一年',
          format: this.formatter
        },
        {
          prop: 'lastThreeYear',
          label: '近三年',
          format: this.formatter
        },
        {
          prop: 'lastFiveYear',
          label: '近五年',
          format: this.formatter
        },
        {
          prop: 'customTime',
          label: '自定义区间',
          format: this.formatter
        }
      ]
    };
  },
  created () {
    this.deadline = this.moment().subtract(1, 'day').format('YYYY-MM-DD');
    this.getFundCode();
    this.$nextTick(() => {
      if (this.localStorage.getItem('TheFundManagersPerformanceQuantilePlate')) {
        let key_list = ['form', 'firstType', ' subType', 'interval', 'dateFlag', 'defaultValue'];
        for (let key of key_list) {
          this[key] = this.localStorage.getItem('TheFundManagersPerformanceQuantilePlate')?.[key] || this[key];
        }
        let index = this.configList.findIndex((v) => v.label == this.defaultValue.radioValue);
        this.$set(this.configList, index, { ...this.configList[index], value: this.defaultValue.selectValue });
        this.$refs['RadioGroup'].setValue(this.defaultValue);
      }
      this.getData();
    });
  },
  methods: {
    goDetail (code, name) {
      alphaGo(code, name, this.$route.path);
    },
    sortChange (row) {
      const { column, prop, order } = row;
      this.tableData.sort((item1, item2) => {
        const a1 = item1[prop] || 0;
        const a2 = item2[prop] || 0;
        let orderVal = order === 'ascending' ? -(a1 - a2) : a1 - a2;
        return orderVal;
      });
      this.dulData();
    },
    handleTypeChange (value) {
      this.defaultValue = value;
      //重新设置chart
      console.log(value);
      this.firstType = value.radioValue;
      this.subType = [value?.selectValue?.value];
      this.getData();
    },
    formatterScale (cellValue) {
      return stringTool.fix2(cellValue) === '0.00' ? '0.00(以万为单位)' : stringTool.fix2(cellValue);
    },
    // 导出excel
    exportExcel () {
      let list = this.tableHeader.map((item) => {
        return {
          ...item,
          value: item.prop,
          format: ''
        };
      });
      filter_json_to_excel(list, this.tableData, '按不同时间段排序的基金经理业绩');
    },
    formatter (cellValue) {
      return stringTool.fix2px(cellValue);
    },
    handleSizeChange (value) {
      this.pageInfo.currentPage = 1;
      this.pageInfo.pageSize = value;
      this.dulData();
    },
    handleCurrentChange (value) {
      this.pageInfo.currentPage = value;
      this.dulData();
    },
    intervalChange () {
      this.pageInfo.currentPage = 1;
      this.getData();
    },
    dulData () {
      let { currentPage, pageSize } = this.pageInfo;
      this.tableDataNow = this.tableData.slice((currentPage - 1) * pageSize, currentPage * pageSize);
    },
    getFundCode () {
      let self = this;
      this.pageInfo.currentPage = 1;
      getFundCode({ deadline: '' })
        .then((res) => {
          if (res.code === 200) {
            console.log('self::::', self);
            self.configList = self.configList.map((item) => {
              let dataList = res.data[item.label + 'List'] || [];
              let curOption = [];
              if (item.label == 'optionalPool') {
                curOption = dataList.map((item) => {
                  return {
                    label: item.name,
                    value: { name: item.name, value: item.id }
                  };
                });
              } else {
                curOption = self.dulConfigOption(dataList);
              }
              item.option.push(...curOption);
              return item;
            });
          }
          console.log('this.configList::::', self.configList);
        })
        .catch(() => { });
    },
    dulConfigOption (dataList) {
      // {label:'动态市盈率',value:{name:'动态市盈率',value:'pe'}},
      // {label:'静态市盈率',value:{name:'静态市盈率',value:'staticState_pe'}},
      // {label:'滚动市盈率',value:{name:'滚动市盈率',value:'trends_pe'}},
      return dataList.map((item) => {
        return {
          label: item,
          value: { name: item, value: item }
        };
      });
    },
    selectChange (val, key) {
      for (const key2 in this.selectVal) {
        if (key2 !== key) {
          this.selectVal[key2] = [];
        }
      }
      this.firstType = key;
      this.subType = val;
      this.pageInfo.currentPage = 1;
      this.getData();
    },
    handleDateChange (value) {
      //自定义区间收益，只影响自定义列表
      this.form['startDate'] = value[0];
      this.form['endDate'] = value[1];
      this.pageInfo.currentPage = 1;
      this.getData();
    },
    async getData () {
      this.loading = true;
      let params = {
        marketType: 'manager',
        deadline: this.deadline,
        type: this.firstType || 'industry',
        subType: this.subType || [],
        dateFlag: this.dateFlag,
        ...this.form
      };
      this.localStorage.setItem('TheFundManagersPerformanceQuantilePlate', {
        firstType: this.firstType,
        subType: this.subType,
        interval: this.interval,
        dateFlag: this.dateFlag,
        form: this.form,
        defaultValue: this.defaultValue
      });
      let req = await performanceSort(params);
      let { data, code } = req || {};
      if (code == 200) {
        this.tableData = data || [];
        this.pageInfo.total = this.tableData.length;
        if (this.dateFlag === '1') {
          let dateList = [];
          this.tableData.forEach((item) => {
            item.naturalList.forEach((res) => {
              dateList.push(res.naturalDate);
            });
          });
          dateList = [...new Set(dateList?.sort((a, b) => {
            if (a < b) return 1
            else return -1
          }))];
          const arr = dateList.map((item) => {
            return {
              prop: item,
              label: item,
              format: this.formatter
            };
          });
          this.tableHeader = [
            {
              prop: 'fundManager',
              label: '基金经理'
            },
            {
              prop: 'fundScale',
              label: '所选类型规模(亿)',
              format: this.formatterScale
            },
            ...arr
          ];
          this.tableData = this.tableData?.map((item) => {
            item.naturalList?.forEach((element) => {
              item[element.naturalDate] = element.meter;
            });
            return item;
          });
        } else {
          this.tableHeader = [
            {
              prop: 'fundManager',
              label: '基金经理'
            },
            {
              prop: 'fundScale',
              label: '所选类型规模(亿)',
              format: this.formatterScale
            },
            {
              prop: 'yearToDate',
              label: '年初至今',
              format: this.formatter
            },
            {
              prop: 'lastWeek',
              label: '近一周',
              format: this.formatter
            },
            {
              prop: 'lastMounth',
              label: '近一月',
              format: this.formatter
            },
            {
              prop: 'lastSeason',
              label: '近一季',
              format: this.formatter
            },
            {
              prop: 'lastHalfYears',
              label: '近半年',
              format: this.formatter
            },
            {
              prop: 'lastYear',
              label: '近一年',
              format: this.formatter
            },
            {
              prop: 'lastThreeYear',
              label: '近三年',
              format: this.formatter
            },
            {
              prop: 'lastFiveYear',
              label: '近五年',
              format: this.formatter
            },
            {
              prop: 'customTime',
              label: '自定义区间',
              format: this.formatter
            }
          ];
        }
      } else {
        this.tableData = [];
      }
      this.dulData();
      this.loading = false;
    }
  }
};
</script>
<style lang="scss" scoped>
.fund-performance-wrapper {
	.select-form-wrapper {
		margin-bottom: 16px;
		display: flex;
	}
	.content-table-wrapper {
		margin-bottom: 32px;
	}
}
</style>
