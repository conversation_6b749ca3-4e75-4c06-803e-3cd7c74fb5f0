<!--  -->
<template>
	<div style="margin-top: 16px" :class="typeFilter != 'beta' ? 'alphachoosepool' : 'betachoosepool'">
		<el-form>
			<el-form-item label="筛选池">
				<template slot="label"
					><span :class="typeFilter != 'beta' ? '' : 'normalFont'">范围基金池</span> <span v-if="typeFilter == 'beta'">:</span></template
				>
				<el-checkbox-group @change="changeValue" v-model="arrlist" size="medium">
					<el-checkbox v-for="(item, index) in arrlistOptions" :key="index" :label="item.value" :disabled="item.disabled">{{
						item.label
					}}</el-checkbox>
				</el-checkbox-group>
			</el-form-item>
		</el-form>
		<el-divider v-if="typeFilter != 'beta'" content-position="left"></el-divider>
	</div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { getPoolList, getManagerPoolList } from '@/api/pages/SystemMixed.js';

export default {
	//import引入的组件需要注入到对象中才能使用
	props: {
		ismanagertype: {
			type: String,
			default: 'fund'
		},
		typeFilter: {
			type: String,
			default: 'alpha'
		}
	},
	components: {},
	data() {
		//这里存放数据
		return {
			arrlist: ['all'],
			arrlistOptions: []
		};
	},
	//监听属性 类似于data概念
	computed: {},
	//监控data中的数据变化
	watch: {
		arrlist(val) {
			this.$emit('changepool', val);
		},
		$route(to, from) {
			setTimeout(() => {
				if (this.FUNC.isEmpty(this.$route.query.code)) {
					if (typeof this.$route.query.code == 'string') {
						this.arrlist = [];
						this.arrlist.push(Number(this.$route.query.code));
					} else {
						this.arrlist = this.$route.query.code.map((item) => Number(item));
					}
				}
			}, 500);
		}
	},
	//方法集合
	methods: {
		getPool() {
			if (this.ismanagertype == 'fund') {
				this.getoption();
			} else {
				this.getoptionmanager();
			}
		},
		getData(val) {
			this.arrlist = val || ['all'];
		},
		changeValue(e) {
			if (e.length > 1 && e[e.length - 1] != 'all' && e.findIndex((item) => item == 'all') >= 0) {
				this.arrlist.splice(
					e.findIndex((item) => item == 'all'),
					1
				);
			}
		},
		async getoption() {
			this.arrlistOptions = [
				{
					value: 'all',
					label: '全市场基金'
				}
			];
			let data = await getPoolList({ date: new Date().getTime() });
			if (data) {
				for (let i = 0; i < data.length; i++) {
					this.arrlistOptions.push({ value: data[i].id, label: data[i].name });
				}
			}
		},
		async getoptionmanager() {
			this.arrlistOptions = [
				{
					value: 'all',
					label: '全市场基金经理'
				}
			];
			let data = await getPoolList({ date: new Date().getTime() });
			if (data) {
				for (let i = 0; i < data.length; i++) {
					this.arrlistOptions.push({ value: data[i].id, label: data[i].name });
				}
			}
		}
	},
	//生命周期 - 创建完成（可以访问当前this实例）
	// created() {
	// 	if (this.ismanagertype == 'fund') {
	// 		this.getoption();
	// 	} else {
	// 		this.getoptionmanager();
	// 	}
	// },
	//生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		setTimeout(() => {
			if (this.FUNC.isEmpty(this.$route.query.code)) {
				if (typeof this.$route.query.code == 'string') {
					this.arrlist = [];
					this.arrlist.push(Number(this.$route.query.code));
				} else {
					this.arrlist = this.$route.query.code.map((item) => Number(item));
				}
			}
		}, 500);
		this.getPool();
	},
	beforeCreate() {}, //生命周期 - 创建之前
	beforeMount() {}, //生命周期 - 挂载之前
	beforeUpdate() {}, //生命周期 - 更新之前
	updated() {}, //生命周期 - 更新之后
	beforeDestroy() {}, //生命周期 - 销毁之前
	destroyed() {}, //生命周期 - 销毁完成
	activated() {} //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
.alphachoosepool_list_flex {
	display: flex;
	flex-wrap: wrap;
}
.betachoosepool {
	.el-form {
		margin-left: 24px;
		.el-form-item {
			margin-bottom: 0px !important;
			::v-deep .el-form-item__label {
				text-align: left;
				width: 100px !important;
			}
			::v-deep .el-form-item__content {
				margin-left: 100px !important;
			}
		}
	}
}
</style>
