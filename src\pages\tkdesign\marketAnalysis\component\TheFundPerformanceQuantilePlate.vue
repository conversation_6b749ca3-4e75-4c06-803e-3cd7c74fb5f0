<template>
  <div class="plate-wrapper fund-performance-wrapper"
       v-loading="loading">
    <VerticalLineHeader title="按不同时段排序的基金业绩"
                        showDownloadBtn
                        @downloadClick="exportExcel">
      <template slot="right">
        <el-form ref="form"
                 :model="form"
                 label-width="80px"
                 class="title-right-form">
          <el-form-item size="small"
                        label="截止日期:">
            <el-date-picker value-format="yyyy-MM-dd"
                            type="date"
                            placeholder="选择日期"
                            v-model="deadline"
                            @change="handleSelectHeader"
                            style="width: 100%"></el-date-picker>
          </el-form-item>
          <el-radio-group class="lq-radio-group radio-group-wrapper"
                          v-model="form.dateFlag"
                          @change="handleSelectHeader"
                          size="small">
            <el-radio-button v-for="radioItem in DateTypeOption"
                             :label="radioItem.value"
                             :key="radioItem.value">{{
							radioItem.label
						}}</el-radio-button>
          </el-radio-group>
        </el-form>
      </template>
    </VerticalLineHeader>
    <div class="select-form-wrapper">
      <!-- <el-radio-group class="radio-group-wrapper" v-model="form.test1" size="small">
                <el-radio-button label="left">类型</el-radio-button>
                <el-radio-button label="right">行业</el-radio-button>
                <el-radio-button label="right">主题</el-radio-button>
                <el-radio-button label="right">风格</el-radio-button>
                <el-radio-button label="right">自选池</el-radio-button>
                <el-radio-button label="right">泰康分类</el-radio-button>
            </el-radio-group> -->
      <RadioGroup ref="RadioGroup"
                  class="radio-group-wrapper"
                  :defaultValue="defaultValue"
                  :configList="configList"
                  @change="handleTypeChange"></RadioGroup>
    </div>
    <el-table class="content-table-wrapper"
              style="width: 100%"
              :data="tableData"
              :stripe="true"
              :border="true"
              @sort-change="handeleSortChange">
      <el-table-column prop="fundName"
                       label="基金简称"
                       align="gotoleft"
                       width="180">
        <template slot-scope="scope">
          <el-link @click="goDetail(scope.row.code, scope.row.fundName)">{{ scope.row.fundName }}</el-link>
        </template>
      </el-table-column>
      <el-table-column prop="fundManager"
                       label="基金经理"
                       align="gotoleft"
                       width="180">
        <template slot-scope="scope">
          <el-popover :visible-arrow="false"
                      placement="top-start"
                      title="历任基金经理"
                      trigger="hover">
            <el-table :data="scope.row.formerManagerList"
                      :stripe="true"
                      :border="true">
              <el-table-column width="100"
                               property="fundManager"
                               align="gotoleft"
                               label="基金经理">
                <template slot-scope="{ row }">
                  <el-link @click="goDetail(row.managerCode, row.fundManager)">{{ row.fundManager }}</el-link>
                </template>
              </el-table-column>
              <el-table-column width="100"
                               property="formerStartDate"
                               align="gotoleft"
                               label="任职日期"></el-table-column>
              <el-table-column width="100"
                               property="formerEndDate"
                               align="gotoleft"
                               label="管理至"></el-table-column>
            </el-table>
            <el-link slot="reference">{{ scope.row.fundManager }}</el-link>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="fundScale"
                       label="最新规模（亿）"
                       sortable="custom"
                       align="gotoleft"
                       width="180">
        <template slot-scope="{ row }">
          <div>{{ fix2(row['fundScale']) }}</div>
        </template>
      </el-table-column>
      <template v-for="item in tableHeader">
        <el-table-column :key="item.prop"
                         v-if="item.prop === 'customTime'"
                         prop="customTime"
                         label="区间收益"
                         sortable="custom"
                         align="gotoleft">
          <template slot="header">
            区间收益
            <DatePickerBtn trigger="click"
                           @change="handleDateChange"
                           @click.native.stop="() => {}"></DatePickerBtn>
          </template>
          <template slot-scope="{ row }">
            <div>{{ item.format ? item.format(row[item.prop]) : row[item.prop] }}</div>
          </template>
        </el-table-column>
        <el-table-column v-else
                         :prop="item.prop"
                         :key="item.prop"
                         min-width="120"
                         :label="item.label"
                         :sortable="item.sortable !== false ? 'custom' : false"
                         align="gotoleft">
          <template slot-scope="{ row }">
            <div>{{ item.format ? item.format(row[item.prop]) : row[item.prop] }}</div>
          </template>
        </el-table-column>
      </template>
    </el-table>
    <el-pagination style="display: flex; justify-content: right; padding-top: 16px; padding-bottom: 16px"
                   class="pagination-footer-wrapper"
                   @size-change="handleSizeChange"
                   @current-change="handleCurrentChange"
                   :current-page.sync="pageInfo.currentPage"
                   :page-sizes="[10, 20, 30, 40]"
                   :page-size="pageInfo.pageSize"
                   layout="total, sizes, prev, pager, next, jumper"
                   :total="pageInfo.total">
    </el-pagination>
  </div>
</template>
<script>
import VerticalLineHeader from './VerticalLineHeader.vue';
import RadioGroup from './RadioGroup.vue';
import { performanceSort, PerformanceType, PaginationFlag, getFundCode } from '@/api/pages/tkAnalysis/captial-market.js';
import DatePickerBtn from './DatePickerBtn.vue';
import { filter_json_to_excel } from '@/utils/exportExcel.js';
import { alphaGo } from '@/assets/js/alpha_type.js';
export default {
  name: 'TheFundPerformanceQuantilePlate',
  components: {
    VerticalLineHeader,
    RadioGroup,
    DatePickerBtn
  },
  data () {
    let defaultTypeValue = 'industry';
    return {
      loading: false,
      pageInfo: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      deadline: '',
      form: {
        type: defaultTypeValue,
        subType: [],
        dateFlag: '0'
      },
      IndexStyleOption: [],
      tableData: [],
      defaultValue: {
        radioValue: defaultTypeValue
        // selectValue:{name:'动态市盈率',value:'pe'}
      },
      tableHeader1: [
        {
          prop: 'yearToDate',
          label: '年初至今',
          format: this.fix2p
        },
        {
          prop: 'lastWeek',
          label: '近一周',
          format: this.fix2p
        },
        {
          prop: 'lastMounth',
          label: '近一月',
          format: this.fix2p
        },
        {
          prop: 'lastSeason',
          label: '近一季',
          format: this.fix2p
        },
        {
          prop: 'lastHalfYears',
          label: '近半年',
          format: this.fix2p
        },
        {
          prop: 'lastYear',
          label: '近一年',
          format: this.fix2p
        },
        {
          prop: 'lastThreeYear',
          label: '近三年',
          format: this.fix2p
        },
        {
          prop: 'lastFiveYear',
          label: '近五年',
          format: this.fix2p
        },
        {
          prop: 'customTime',
          label: '自定义区间',
          format: this.fix2p
        }
      ],
      DateTypeOption: [
        { label: '近期业绩', value: '0' },
        { label: '自然年份业绩', value: '1' }
      ],
      configList: [
        { type: 'select', value: '', label: 'type', text: '类型', option: [{ label: '全部类型' }] },
        { type: 'select', value: '', label: 'industry', text: '行业', option: [{ label: '全部行业', value: [] }] },
        { type: 'select', value: '', label: 'theme', text: '主题', option: [{ label: '全部主题', value: [] }] },
        { type: 'select', value: '', label: 'optionalPool', text: '自选池', option: [{ label: '全部自选池', value: [] }] },
        { type: 'select', value: '', label: 'taikang', text: '泰康分类', option: [{ label: '全部泰康分类', value: [] }] },
        { type: 'select', value: '', label: 'style', text: '风格', option: [{ label: '全部风格', value: [] }] }
      ],
      allData: [],
      tableHeader: []
    };
  },
  created () {
    this.getFundCode();
  },
  mounted () {
    this.deadline = this.moment().subtract(1, 'day').format('YYYY-MM-DD');
    if (this.localStorage.getItem('TheFundPerformanceQuantilePlate')) {
      let key_list = ['form', 'defaultValue'];
      for (let key of key_list) {
        this[key] = this.localStorage.getItem('TheFundPerformanceQuantilePlate')?.[key] || this[key];
      }
      let index = this.configList.findIndex((v) => v.label == this.defaultValue.radioValue);
      this.$set(this.configList, index, { ...this.configList[index], value: this.defaultValue.selectValue });
      this.$refs['RadioGroup'].setValue(this.defaultValue);
    }
    this.getData();
  },
  methods: {
    // 查看基金/基金经理详情
    goDetail (code, name) {
      alphaGo(code, name, this.$route.path);
    },
    handeleSortChange ({ column, prop, order }) {
      this.allData.sort((item1, item2) => {
        const a1 = item1[prop] || 0;
        const a2 = item2[prop] || 0;
        let orderVal = order === 'ascending' ? -(a1 - a2) : a1 - a2;
        return orderVal;
      });
      this.sliceTableData();
    },
    // 导出excel
    exportExcel () {
      let columnList = [
        {
          prop: 'fundName',
          label: '基金简称'
        },
        {
          prop: 'fundManager',
          label: '基金经理'
        },
        {
          prop: 'fundScale',
          label: '最新规模（亿）'
        }
      ];
      columnList.push(...this.tableHeader);
      let list = columnList.map((item) => {
        return {
          ...item,
          value: item.prop,
          format: ''
        };
      });
      filter_json_to_excel(list, this.allData, '按不同时段排序的基金业绩');
    },
    /**
     * 格式化数据
     * 乘以100并保留两位小数 + 百分号(%)
     */
    fix2p (val) {
      return val * 1 && !isNaN(val) ? (val * 100).toFixed(2) + '%' : '--';
    },
    /**
     * 格式化数据
     * 保留两位小数
     */
    fix2 (val) {
      return val * 1 && !isNaN(val) ? (val * 1).toFixed(2) : '--';
    },
    handleSelectHeader () {
      // this.pageInfo.currentPage=1;
      //接口调用
      this.getData();
    },
    handleTypeChange (value) {
      this.defaultValue = value;
      //重新设置chart
      console.log(value);
      this.form.type = value.radioValue;
      this.form.subType = [value?.selectValue?.value];
      this.getData();
    },
    // 获取列表数据
    async getData () {
      this.loading = true;
      let params = {
        // pageSize: this.pageInfo.pageSize,
        // currentPage: this.pageInfo.currentPage,
        marketType: PerformanceType.FundQuantileByTime.value,
        PaginationFlag: PaginationFlag.N.value,
        ...this.form,
        deadline: this.deadline,
      };
      this.localStorage.setItem('TheFundPerformanceQuantilePlate', { form: this.form, defaultValue: this.defaultValue });
      let req = await performanceSort(params);
      let { data, code, message } = req || {};
      if (code == 200) {
        this.allData = data;
        this.pageInfo.total = this.allData.length;
        if (this.form.dateFlag == 1) {
          let naturalList = data[0]?.naturalList;
          this.tableHeader = naturalList.map((item) => {
            return {
              prop: item.naturalDate,
              label: item.naturalDate,
              format: this.fix2p
            };
          })?.sort((a, b) => {
            if (a?.prop < b?.prop) return 1
            else return -1
          });
          this.allData = this.allData.map((item) => {
            let obj = { ...item };
            item.naturalList.map((v) => {
              obj[v.naturalDate] = v.meter;
            });
            return obj;
          });
        } else {
          this.tableHeader = this.tableHeader1;
        }
      } else {
        this.allData = [];
      }
      this.loading = false;
      this.sliceTableData();
    },
    // 监听分页Size改变
    handleSizeChange (val) {
      this.pageInfo.pageSize = val;
      this.sliceTableData();
    },
    // 监听分页页码改变
    handleCurrentChange (val) {
      this.pageInfo.currentPage = val;
      this.sliceTableData();
    },
    // 数据分页
    sliceTableData () {
      this.tableData = this.allData.slice(
        (this.pageInfo.currentPage - 1) * this.pageInfo.pageSize,
        this.pageInfo.currentPage * this.pageInfo.pageSize
      );
    },
    handleDateChange (value) {
      //自定义区间收益，只影响自定义列表
      this.form['startDate'] = value[0];
      this.form['endDate'] = value[1];
      this.getData();
    },
    async getFundCode () {
      let params = {
        deadline: ''
      };
      let req = await getFundCode(params);
      let { data, code, message } = req || {};
      if (code == 200) {
        // {type:'select',value:'',label:'type',text:'类型',option:[{label:'类型'}]},
        // {type:'select',value:'',label:'industry',text:'行业',option:[{label:'行业'}]},
        // {type:'select',value:'',label:'theme',text:'主题',option:[{label:'主题'}]},
        // {type:'select',value:'',label:'pool',text:'自选池',option:[{label:'自选池'}]},
        // {type:'select',value:'',label:'taikang',text:'泰康分类',option:[{label:'泰康分类'}]},
        // {type:'select',value:'',label:'style',text:'风格',option:[{label:'风格'}]}],
        this.configList = this.configList.map((item) => {
          let dataList = data[item.label + 'List'] || [];
          let curOption = [];
          if (item.label == 'optionalPool') {
            curOption = dataList.map((item) => {
              return {
                label: item.name,
                value: { name: item.name, value: item.id }
              };
            });
          } else {
            curOption = this.dulConfigOption(dataList);
          }
          item.option.push(...curOption);
          return item;
        });
      } else {
        this.tableData = [];
      }
    },
    dulConfigOption (dataList) {
      // {label:'动态市盈率',value:{name:'动态市盈率',value:'pe'}},
      // {label:'静态市盈率',value:{name:'静态市盈率',value:'staticState_pe'}},
      // {label:'滚动市盈率',value:{name:'滚动市盈率',value:'trends_pe'}},
      return dataList.map((item) => {
        return {
          label: item,
          value: { name: item, value: item }
        };
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.fund-performance-wrapper {
	padding-bottom: 20px;
	.select-form-wrapper {
		margin-bottom: 16px;
	}
}
</style>
