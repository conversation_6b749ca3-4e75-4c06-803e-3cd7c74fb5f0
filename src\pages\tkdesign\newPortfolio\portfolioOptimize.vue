<template>
  <div class="portfolit-optimize-wrapper">
    <div style="margin-bottom: 16px">
      <span class="headerFontSmall">资产配置/</span><span class="headerFontSmall">{{ $route.meta.title }}</span>
    </div>
    <div class="portfolit-optimize-step-wrapper">
      <!-- <el-steps :active="active" :space="200" finish-status="success">
				<el-step class="step1" title="资产选择和分析"></el-step>
				<el-step class="step2" title="参数设置与组合优化"></el-step>
				<el-step title="保存方案"></el-step>
			</el-steps> -->
			<el-steps :active="active" :space="200" finish-status="success">
				<el-step v-for="item in stepsList" :style="{ flexBasis: (item.width || 200) + 'px' }" class="step1" :title="item.title"></el-step>
			</el-steps>
		</div>
		<div>
			<assetSelection ref="assetSelection" v-show="active === 0" @nextStep="handleSetInfo1" @backStep="handleBackStep"></assetSelection>
			<settingAndOptimization
				ref="settingAndOptimization"
				:otherInfo="selectPageInfo"
				:flag="flag"
				:marketType="marketType"
				v-show="active === 1"
				@nextStep="handleSetInfo2"
				@backStep="handleBackStep"
			></settingAndOptimization>
			<backTest
				v-if="active === 2"
				:parameterJson="parameterJson"
				:listItemInfo="listItemInfo"
				@nextStep="handleNextStep"
				@backStep="handleBackStep"
			></backTest>
		</div>
	</div>
</template>
<script>
import assetSelection from './components/assetSelection.vue';
import settingAndOptimization from './components/settingAndOptimization.vue';
import backTest from './components/backTest.vue';
export default {
	components: {
		assetSelection,
		settingAndOptimization,
		backTest
	},
	data() {
		return {
			active: 0,
			//配置策略研究进度条
			configStrategySteps: [
				{
					title: '资产选择和分析',
					width: '216'
				},
				{
					title: '参数设置',
					width: '168'
				},
				{
					title: '预览/回测',
					width: '176'
				},
				{
					title: '保存方案',
					width: '120'
				}
			],
			//组合策略研究进度条
			combinationStrategySteps: [
				{ title: '资产选择和分析', width: '216' },
				{ title: '参数设置', width: '168' },
				{ title: '预览及微调', width: '184' },
				{ title: '保存方案', width: '120' }
			],
			//选择页面需要传递的内容
			selectPageInfo: {
				selectList: []
			},
			//设置页面需要传递的内容
			setPageInfo: {},
			parameterJson: {},
			listItemInfo: {}
		};
	},
	computed: {
		flag() {
			// 	"1": "fund",基金
			// "2": "manager",基金经理
			// "3": "company",基金公司
			// "4": "combination",组合
			// "5": "pool",基金池
			// "6": "index",指数
			// "7": "user_index",
			// "8": "self",
			// "9": "stock"
			//如果进入的地址为配置策略研究则返回--指数，否则返回组合策略研究--基金
			if (this.$route.path === '/configurationStrategySteps') {
				return '6';
			}
			return '1';
		},
		marketType() {
			//如果进入的地址为配置策略研究则返回--指数，否则返回组合策略研究--基金
			if (this.$route.path === '/configurationStrategySteps') {
				return 'index';
			}
			return 'fund';
		},
		stepsList() {
			//如果进入的地址为配置策略研究则返回配置策略研究的进度条，否则返回组合策略研究的进度条
			if (this.$route.path === '/configurationStrategySteps') {
				return this.configStrategySteps;
			}
			return this.combinationStrategySteps;
		}
	},
	created() {
		this.active = Number(this.$route.query.active || 0);
		this.handleInitPage();
	},
	methods: {
		handleInitPage() {
			this.$nextTick(() => {
				if (this.active === 0) {
					console.log(this.$refs['assetSelection']);
					this.$refs['assetSelection']?.initData && this.$refs['assetSelection']?.initData();
					return;
				}
				if (this.active === 1) {
					this.$refs['settingAndOptimization']?.initData && this.$refs['settingAndOptimization']?.initData();
				}
			});
		},
		handleNextStep() {
			if (this.active++ > this.stepsList.length - 1) {
				this.active = 0;
			}
			this.handleInitPage();
		},
		handleBackStep() {
			if (this.active > 0) {
				this.active--;
				return;
			}
			//否则回退到上衣页面
			history.back();
		},
		handleSetInfo1(value) {
			this.selectPageInfo = value;
			this.handleNextStep();
		},
		handleSetInfo2(parameterJson, listItemInfo) {
			this.parameterJson = parameterJson;
			this.listItemInfo = listItemInfo;
			this.handleNextStep();
		}
	}
};
</script>
<style lang="scss" scoped>
.portfolit-optimize-wrapper {
	margin: 24px;
}
.portfolit-optimize-step-wrapper {
	padding: 24px 0;
	background-color: #ffffff;
	border-radius: 4px;
	border: 1px solid #d4d8e5;
	/* 主模块投影 */
	box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.1);
	margin-bottom: 16px;
	::v-deep .el-steps {
		justify-content: center;
		.el-step__title.is-process {
			color: #4096ff;
		}
		.el-step__title.is-success {
			color: rgba(0, 0, 0, 0.85);
		}
		.el-step__head.is-process {
			color: #4096ff;
			border-color: #4096ff;
			.el-step__icon {
				color: #fff;
				background-color: #4096ff;
			}
		}
		.el-step__head.is-success {
			color: #4096ff;
			border-color: #4096ff;
		}
		// .step1 {
		// 	flex-basis: 224px !important;
		// }
		// .step2 {
		// 	flex-basis: 272px !important;
		// }
		.el-step.is-horizontal {
			.el-step__line {
				right: 16px;
				width: 32px;
				height: 1px;
				left: unset;
			}
		}
	}
	::v-deep .el-step__main {
		height: 100%;
		position: absolute;
		top: 50%;
		left: 39px;
		transform: translateY(-50%);
		.el-step__title {
			line-height: unset;
		}
	}
}
</style>
