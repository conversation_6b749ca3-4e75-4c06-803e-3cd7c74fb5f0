<!--  -->
<template>
	<div class="nameSelect">
		<div
			v-show="haveName != ''"
			style="
				font-weight: 400;
				font-size: 14px;
				line-height: 22px;
				color: rgba(0, 0, 0, 0.85);
				margin-left: 0px;
				margin-right: 16px;
				margin-bottom: 4px;
			"
		>
			{{ haveName }}包含字段
		</div>
		<div style="display: flex">
			<div style="margin-right: 16px">
				<operator v-if="is_range" ref="operator" @resolveMathRange="resolveMathRange"></operator>
				<el-dropdown @command="command">
					<el-button type="primary">
						{{ iconFlag != '' ? (iconFlag == 'in' ? '包含' : '除外') : '运算符' }}<i class="el-icon-arrow-down el-icon--right"></i>
					</el-button>
					<el-dropdown-menu slot="dropdown">
						<el-dropdown-item command="in">包含</el-dropdown-item>
						<el-dropdown-item command="out">除外</el-dropdown-item>
					</el-dropdown-menu>
				</el-dropdown>
			</div>
			<div>
				<el-input v-model="equitytype" @change="changeNode" placeholder="输入基金名称包含字段"> </el-input>
			</div>
		</div>
	</div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import operator from '@/pages/filter/fund/beta/componentsFilter/components/operator.vue';

export default {
	props: {
		is_range: {
			type: Boolean,
			default: false
		},
		haveName: {
			type: String,
			default: ''
		},
		dataX: {
			type: Object,
			default: {}
		},
		placeholder: {
			type: String
		},
		indexFlag: {
			type: Number
		},
		baseIndexFlag: {
			type: Number
		},
		fundType: {
			type: String,
			default: 'equity'
		}
	},
	//import引入的组件需要注入到对象中才能使用
	components: { operator },
	data() {
		//这里存放数据
		return {
			equitytype: '',
			iconFlag: 'in',
			mathRange: { mathRange: 'avg' }
		};
	},
	//监听属性 类似于data概念
	computed: {},
	//监控data中的数据变化
	watch: {
		dataX(val) {
			if (val.dataResult && val.dataResult.length > 0) {
				this.equitytype = val.dataResult[0].value;
				this.iconFlag = val.dataResult[0].flag;
				if (this.$refs['operator']) {
					this.$refs['operator'].getFlag(val.dataResult[0].mathRange);
				}
			}
		}
	},
	//方法集合
	methods: {
		resolveMathRange(obj) {
			this.mathRange = obj;
			this.resolveFather();
		},
		resolveFather() {
			this.$emit(
				'nameChange',
				this.baseIndexFlag,
				this.indexFlag,
				this.equitytype,
				this.iconFlag,
				this.FUNC.isEmpty(this.equitytype) && this.FUNC.isEmpty(this.iconFlag),
				this.mathRange
			);
		},
		changeNode(e) {
			this.resolveFather();
		},
		command(e) {
			this.iconFlag = e;
			this.showBox = true;
			this.resolveFather();
		}
	},
	//生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	//生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		if (JSON.stringify(this.dataX) != '{}') {
			if (this.dataX.dataResult && this.dataX.dataResult.length > 0) {
				this.equitytype = this.dataX.dataResult[0].value;
				this.iconFlag = this.dataX.dataResult[0].flag;
				if (this.$refs['operator']) {
					this.$refs['operator'].getFlag(this.dataX.dataResult[0].mathRange);
				}
			}
		}
	},
	beforeCreate() {}, //生命周期 - 创建之前
	beforeMount() {}, //生命周期 - 挂载之前
	beforeUpdate() {}, //生命周期 - 更新之前
	updated() {}, //生命周期 - 更新之后
	beforeDestroy() {}, //生命周期 - 销毁之前
	destroyed() {}, //生命周期 - 销毁完成
	activated() {} //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
</style>
