
<template>
  <div v-loading="loading"
       class="FTB">
    <div style="margin-bottom: 16px">
      <span class="headerFontSmall">资产配置/</span><span class="headerFontBig">{{ title }}</span>
    </div>
    <!-- <el-menu :default-active="activeIndex" mode="horizontal" >
			<el-menu-item v-for="item in activeComponentsList" :key="item.key" :index="item.key">{{ item.label }}</el-menu-item>
		</el-menu> -->
    <div class="boxMain">
      <div style="margin-bottom: 8px;display: flex; align-items: center; justify-content: space-between;">

        <el-button-group class="left">
          <el-button plain
                     v-for="item in buttonList"
                     :key="item.key"
                     @click="changePublic(item.key)"
                     :class="{activeBtn:activeBtn===item.key}">{{ item.label }}</el-button>
        </el-button-group>
        <el-button @click="goCombinationStrategySteps"
                   type="primary"
                   v-if="pagekey !== 'combination'">+&nbsp;{{ pagekey==='ploy' ? '创建配置策略' :'创建组合策略' }}</el-button>
        <el-dropdown trigger="click"
                     v-else>
          <el-button type="primary">创建组合 <i class="el-icon-arrow-down el-icon--right"></i></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item icon="el-icon-plus"
                              @click.stop.native="createPortfolio(false)">手动创建</el-dropdown-item>
            <el-dropdown-item icon="el-icon-circle-plus"
                              @click.stop.native="goCombination">模型创建</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>

      </div>
      <div>
        <el-table class="content-table-wrapper"
                  style="width: 100%"
                  :data="tableDataNow"
                  v-loading="loading">
          <template v-for="(item, index) in tableHeader">
            <el-table-column v-if="item.prop === 'opt'"
                             :prop="item.prop"
                             :label="item.label"
                             :sortable="index === 0 ? false : 'custom'"
                             :align="'left'"
                             :min-width="pagekey === 'ploy'?'50' :'220'"
                             show-overflow-tooltip
                             :key="index">
              <template slot-scope="scope">
                <template v-for="(btn, btnkey) in item.optList">
                  <el-dropdown v-if="btn.key === 'more'"
                               :key="btnkey"
                               trigger="click"
                               style="float: left;height: 40px;display: flex;align-items: center;">
                    <el-button type="text"
                               style="color: #4096ff;float: left;">
                      {{btn.label}}<i class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item v-for="(more, morekey) in btn.moreList"
                                        :key="morekey"
                                        @click.native.stop="moreClick(more.label,scope.row)">{{ more.label }}</el-dropdown-item>

                    </el-dropdown-menu>
                  </el-dropdown>
                  <el-button type="text"
                             @click="createPortfolio(true,btn.key,scope.row.id,scope.row)"
                             style="color: #4096ff;float: left;height: 40px; "
                             v-else
                             :key="btnkey">
                    {{btn.label}}
                  </el-button>
                </template>
              </template>
              <!-- <template  v-else slot-scope="scope" >
							{{ scope.row[item.prop] }}
					</template> -->
            </el-table-column>
            <el-table-column v-else-if="item.showTip"
                             width="250"
                             prop="item.prop"
                             :align="'left'"
                             :key="index"
                             show-overflow-tooltip
                             :label="item.label">
              <template slot-scope="scope">
                <el-link class="showOverTooltip"
                         type="primary"
                         v-if="item.prop === 'combinationName'"
                         @click="goCombinationDetail(scope.row.id,scope.row.indexCode,scope.row.endDate)"
                         style="text-align: left;width: 100%;display: block;">
                  {{scope.row[item.prop]}}
                </el-link>
                <span class="showOverTooltip"
                      v-else
                      style="text-align: left;width: 100%;display: block;">{{scope.row[item.prop]}}</span>
              </template>
            </el-table-column>
            <el-table-column v-else
                             :prop="item.prop"
                             :label="item.label === '涨跌幅' ? `${item.label}(${tableDataNow.length > 0 ? tableDataNow[0].netDate :''})` : item.label"
                             :min-width="item.width || item.label === '涨跌幅' ? '190' :item.label.length === 4 ? '100' : item.label.length === 3 ? '90':item.label.length >= 6?'200':'120'"
                             sortable
                             :align="'left'"
                             :key="index">

              <template slot-scope="scope">
                <span style="text-align: left;width: 100%;display: block;">{{ item.format ? item.format(scope.row[item.prop]) :scope.row[item.prop]}}</span>
              </template>

            </el-table-column>
          </template>
        </el-table>
        <el-pagination background
                       style="display: flex; justify-content: right; padding-top: 16px; padding-bottom: 24px"
                       @size-change="handleSizeChange"
                       @current-change="handleCurrentChange"
                       :current-page.sync="currentPage"
                       :page-sizes="pageSizeList"
                       :page-size="currentPageSize"
                       layout="total, sizes, prev, pager, next, jumper"
                       :total="total">
        </el-pagination>
      </div>
    </div>
    <el-dialog class="FTBdialog"
               width="686px"
               height="444px"
               :visible.sync="showAdd">
      <div slot="title">
        <span style="
						font-family: 'PingFang';
						font-style: normal;
						font-weight: 500;
						font-size: 16px;
						line-height: 24px;
						color: rgba(0, 0, 0, 0.85);
						width: 100%;
					">{{ nowName }}</span>
      </div>
      <div style="width: 100%; height: 1px; background: rgba(0, 0, 0, 0.06); margin-bottom: 16px"></div>
      <div style="display: flex; margin-bottom: 16px">
        <div style="flex: 1"><span style="color: red;">*</span>组合名称：<el-input v-model="name"
                    style="width: 216px"
                    placeholder="请输入"></el-input></div>
        <div style="flex: 1;text-align: right;">
          <span style="flex-shrink: 0;width: 70px;display: inline-block;">创建人：</span>
          <el-input disabled
                    v-model="creater"
                    style="width: 216px"
                    placeholder="请输入"></el-input>
        </div>
      </div>

      <div style="display: flex; margin-bottom: 16px">
        <div style="flex: 1">

          <el-tooltip class="item"
                      effect="dark"
                      content="成立日期指组合记录的创建日期"
                      placement="top-start">
            <span>成立日期<img src="@/assets/img/tkdesign/question.png">：</span>
          </el-tooltip>
          <el-date-picker disabled
                          v-model="createdate"
                          type="date"
                          value-format="yyyy-MM-dd"
                          style="width: 216px"
                          placeholder="请选择">
          </el-date-picker>
        </div>
        <div style="flex: 1;text-align: right;">
          <el-tooltip class="item"
                      effect="dark"
                      content="结束日期指组合数据的截止日期，超过本日期后组合数据不再计算"
                      placement="top-start">
            <span>结束日期<img src="@/assets/img/tkdesign/question.png">：</span>
          </el-tooltip>
          <el-date-picker v-model="enddate"
                          type="date"
                          value-format="yyyy-MM-dd"
                          style="width: 216px"
                          placeholder="无结束日期">
          </el-date-picker>
        </div>

      </div>
      <div style="display: flex; margin-bottom: 24px">
        <div style="flex: 1">
          <span>分红处理：</span>
          <el-radio-group v-model="moneyDo">
            <el-radio :label="false">现金提取</el-radio>
            <el-radio :label="true">分红再投资</el-radio>
          </el-radio-group>
        </div>
        <div style="flex: 1;display: flex;justify-content:flex-end;align-items: center;">
          <span style="flex-shrink: 0;flex-basis: 70px;text-align: right;">是否公共：</span>
          <el-radio-group v-model="categray"
                          style="flex-basis: 216px;height:16px">
            <el-radio label="1">私有</el-radio>
            <el-radio label="2">公共</el-radio>
          </el-radio-group>
        </div>

      </div>
      <div style="display: flex; margin-bottom: 24px">
        <div style="flex: 1">
          <span><span style="color: red;">*</span>比较基准：</span>
          <el-select v-model="comparisonValue"
                     placeholder="请选择基准"
                     filterable
                     :remote-method="remoteMethod"
                     remote
                     :loading="selectLoading">
            <el-option v-for="item in options"
                       :key="item.code"
                       :label="item.name"
                       :value="item.code">
            </el-option>
          </el-select>
        </div>

      </div>
      <div style="margin-bottom: 16px;display: flex; text-align: right">
        <span style="flex-shrink: 0;flex-basis: 70px;">说明：</span>
        <el-input v-model="description"
                  :autosize="{ minRows: 4, maxRows: 8 }"
                  placeholder="请输入"
                  type="textarea"></el-input>
      </div>
      <div style="text-align: right">
        <el-button type=""
                   @click="showAdd = false">取消</el-button>
        <el-button type="primary"
                   @click="nowName == '创建组合' ? addCom() : upCom()">确认</el-button>
      </div>
    </el-dialog>
    <assetDetails @updatePort="updatePort"
                  v-model="showDetails"
                  :combinationName="showDetailscomName"
                  :endDate="showDetailsEndDate"
                  ref="assetDetails"
                  :comId="showDetailsID"></assetDetails>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { getPolicyList, combinationList, deletePolicy, getSearchList, createCombination, createImitateCombination, updateCombination, refreshHoldingDetails } from '@/api/pages/tkAnalysis/portfolio.js';
import assetDetails from './assetDetails';
export default {
  name: 'FTB',
  props: {
    title: {},
    tableHeader: {
      type: Array,
      default: []
    },
    buttonList: {
      type: Array,
      default: [{ key: '1', label: '私有的' },
      { key: '2', label: '公共的' },]
    },
    pagekey: {
      type: String,
      default: "combination"
    },
    pageSize: {
      type: Number,
      default: 10
    }
  },
  //import引入的组件需要注入到对象中才能使用
  components: { assetDetails },
  filters: {
    fix2p (value) {
      return value &&
        value != '' &&
        value != '--' &&
        value != '- -' &&
        JSON.stringify(value) != '[]' &&
        JSON.stringify(value) != '{}' &&
        value != 'NAN' &&
        value != 'nan'
        ? (Number(value) * 100).toFixed(2) + '%'
        : '--';
    },
    fix2 (value) {
      return value &&
        value != '' &&
        value != '--' &&
        value != '- -' &&
        JSON.stringify(value) != '[]' &&
        JSON.stringify(value) != '{}' &&
        value != 'NAN' &&
        value != 'nan'
        ? Number(value).toFixed(2)
        : '--';
    },
    fixY (value) {
      return value &&
        value != '' &&
        value != '--' &&
        value != '- -' &&
        JSON.stringify(value) != '[]' &&
        JSON.stringify(value) != '{}' &&
        value != 'NAN' &&
        value != 'nan'
        ? Number(value / 100000000).toFixed(2) + '亿'
        : '--';
    }
  },
  data () {
    //这里存放数据
    return {
      selectLoading: false,
      options: [],
      comparisonValue: '',
      activeIndex: '组合列表',
      activeComponentsList: [
        { key: '组合列表', label: '组合列表' },
      ],

      activeBtn: '1',
      tableData: [],
      tableDataNow: [],
      showAdd: false,
      name: '',
      creater: '',
      createdate: '',
      enddate: '',
      showDetailsEndDate: '',
      moneyDo: false,
      categray: "1",
      istop: false,
      description: '',
      nowName: '创建组合',
      updateId: '',
      showDetails: false,
      showDetailsID: '',
      showDetailscomName: '',
      userId: '',
      loading: false,
      currentPageSize: 10,
      pageSizeList: [10, 20, 40, 60, 80, 100],
      currentPage: 1,
      total: 0,
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {
    pageSize (val) {
      this.currentPageSize = val;
      this.pageSizeList.indexOf(val) == -1 && this.pageSizeList.push(val);
      this.pageSizeList.sort((a, b) => a - b);
      this.handleSizeChange(val);
    },
    showDetails (val) {
      if (!val) {
        this.showDetailsEndDate = '';
        this.showDetailsID = '';
        this.showDetailscomName = '';
      }
    }

  },
  //方法集合
  methods: {

    updatePort () {
      this.getData();
    },
    goCombinationStrategySteps () {
      this.$router.push({
        path: this.pagekey === 'ploy' ? "/configurationStrategySteps" : "/combinationStrategySteps",
        query: {
          active: 0,
        }
      })
    },
    goCombinationSteps (index, item) {
      this.$router.push({
        path: this.pagekey === 'ploy' ? "/configurationStrategySteps" : "/combinationStrategySteps",
        query: {
          active: index || 1,
          listItemInfo: JSON.stringify(item),
          isEditPage: index === 0 ? false : true
        }
      })
    },
    goCombination () {
      this.$router.push({
        path: "/combinationStrategySteps",
        query: {
          active: 0,
        }
      })
    },
    async moreClick (type, item) {
      if (type === '调仓') {
        let { mtycode, mtymessage } = await refreshHoldingDetails({ combinationId: item.combinationId })
        if (mtycode != '200') {
          this.$message.error(mtymessage);
          return;
        }
        this.showDetails = true;
        this.showDetailsID = item.combinationId;
        this.showDetailscomName = item.combinationName;
        this.showDetailsEndDate = item.endDate;
        return;
      }
      if (type === "编辑") {
        console.log(item);
        this.goCombinationSteps(1, item)
      } else if (type === "删除") {
        this.delCom(item.id)
      }
    },
    changePublic (key) {
      this.activeBtn = key;
      this.getData();
    },
    handleSizeChange (val) {
      this.currentPageSize = val;
      this.currentPage = 1;
      this.getData(1);
    },
    handleCurrentChange (val) {
      this.currentPage = val;
      this.getData();
    },
    // 前往组合分析
    goDetail (val) {
      this.$router.push({ path: '/portfolioAnalysis/' + val.combination_id, hash: '', query: { id: val.combination_id, name: val.name } });
    },
    // 获取组合列表
    async getData (val) {
      this.loading = true;
      let params = this.pagekey === 'combination' ? { isPublic: this.activeBtn, pageNum: this.currentPage, pageSize: this.currentPageSize } : { ispublic: this.activeBtn, pageNum: this.currentPage, pageSize: this.currentPageSize, type: this.pagekey }
      let { data, message, mtycode } = this.pagekey === 'combination' ? await combinationList(params) : await getPolicyList(params);
      this.loading = false;
      if (mtycode == "200") {
        let key = this.pagekey === 'combination' ? 'combinationList' : 'dataList'
        this.tableDataNow = data[key] || [];
        this.total = data.total || 0;
      } else {
        this.tableDataNow = [];
      }
      this.loading = false;
    },
    // 获取组合详情
    async getComdetail (item) {
      if (item) {
        this.updateId = item.id;
        this.nowName = '编辑组合';
        this.creater = this.$store.state.username;
        this.name = item.combinationName;
        this.createdate = item.establishDate;
        // this.moneyDo = data[0].flag;
        this.moneyDo = item.flag;
        this.categray = item.isPublic.toString();
        this.description = item.description || '';
        // this.istop = data.isshow;
        this.comparisonValue = item.indexCode || '';
        this.showAdd = true;
      } else {
        this.$message.warning('暂无数据');
      }
    },
    // 点击创建组合
    createPortfolio (edit, key, id, item) {
      if (key === "生成模拟组合") {
        this.createImitateCombination(id)
        return;
      }
      if (key === "回测") {
        this.goCombinationSteps(2, item)
        return;
      }
      if (this.pagekey === "combination") {
        this.nowName = edit ? '编辑组合' : '创建组合';
        this.showAdd = edit ? false : true;
        this.createdate = this.moment(new Date()).format('YYYY-MM-DD');
        this.creater = this.$store.state.username;
        if (edit) {
          this.getComdetail(item)
        }
      } else {
        this.goCombinationSteps(1, item)
      }

    },
    async createImitateCombination (id) {
      this.loading = true;
      let { mtycode, data, mtymessage } = await createImitateCombination({
        ployId: Number(id)
      });
      if (mtycode == "200") {
        this.goCombinationDetail(data.combinationId)
      } else {
        this.$message.error(mtymessage);
      }
      this.loading = false;
    },
    goCombinationDetail (id, indexCode, date) {
      this.$router.push({
        path: "/portfolioDetail",
        query: {
          id: id || '',
          indexCode: indexCode || ' ',
          endDate: date || ' '

        }
      })
    },
    // 确认创建组合
    async addCom () {
      if (this.name.trim() == '') {
        this.$message.error('请输入组合名称');
        return;
      }
      if (this.comparisonValue.trim() == '') {
        this.$message.error('请选择比较基准');
        return;
      }
      let { data, mtymessage, mtycode } = await createCombination({
        name: this.name,
        createDate: this.createdate,
        // createBy: this.creater,
        endDate: this.enddate,
        flag: this.moneyDo,
        ispublic: this.categray,
        description: this.description,
        establishDate: this.createdate,
        indexCode: this.comparisonValue,
      });
      if (mtycode == "200") {
        this.$message.success('创建成功');
        this.showAdd = false;
        this.handleCurrentChange(1);
        // this.changeFund(this.tableData[this.tableData.length - 1].combination_id, this.tableData[this.tableData.length - 1].date);//由于苟煜无法确定新建的位置故无法自动打开新建的组合
      } else {
        this.$message.error(mtymessage);
      }
    },
    // 删除组合
    async delCom (id) {
      let { data, mtymessage, mtycode } = await deletePolicy({ id: id, type: this.pagekey });
      if (mtycode == "200") {
        this.getData();
        this.$message.success('删除成功');
      } else {
        this.$message.warning(mtymessage);
      }
    },
    // 确认修改组合
    async upCom () {
      let { data, message, mtycode } = await updateCombination({
        id: this.updateId,
        name: this.name,
        createDate: this.createdate,
        flag: false,
        // date: this.founddate,
        endDate: this.enddate,
        // flag: this.moneyDo,
        ispublic: this.FUNC.isEmpty(this.categray) ? this.categray : 1,
        // isshow: this.istop,
        description: this.description,
        indexCode: this.comparisonValue,
        establishDate: this.createdate,
      });
      if (mtycode == 200) {
        this.$message.success('修改成功');
        this.showAdd = false;
        this.getData(this.title);
      } else {
        this.$message.warning('修改失败');
      }
    },
    // 组合内资产详情open
    changeFund (id, date) {
      this.$refs.assetDetails.showdialog(id, date);
      this.showDetailsID = id;
    },
    async getSearchList (message = "") {
      let res = await getSearchList({
        message,
        flag: "6"
      })
      if (res && res.mtycode == 200) {
        this.options = res.data;
      }
    },
    async remoteMethod (query) {
      if (query !== '') {
        this.selectLoading = true;

        await this.getSearchList(query)
        this.selectLoading = false;

      } else {
        this.options = [];
      }
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () {
    this.getData()
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () {
    // if (window.innerHeight > 1600) {
    // 	this.pageSIze = 20;
    // 	document.getElementById('tablePortfolio1').style = 'min-height:1152px';
    // } else {
    // 	this.pageSIze = 10;
    // 	document.getElementById('tablePortfolio1').style = 'min-height:612px';
    // }
    this.userId = localStorage.getItem('id');
  },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style>
.showOverTooltip {
	display: -webkit-box;
	text-overflow: ellipsis;
	overflow: hidden;
	/*这里是3行*/
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
	margin-bottom: 0px;
}
.FTBdialog .el-dialog__body {
	padding-top: 0 !important;
}
</style>
<style lang="scss" scoped>
.FTB {
	padding: 24px;
}
.activeBtn {
	color: #4096ff;
	border-color: rgb(255, 222, 179);
	border: 1px solid;
}
.content-table-wrapper {
	::v-deep .cell {
		height: 40px !important;
		line-height: 40px !important;
	}
}

//@import url(); 引入公共css类
</style>
