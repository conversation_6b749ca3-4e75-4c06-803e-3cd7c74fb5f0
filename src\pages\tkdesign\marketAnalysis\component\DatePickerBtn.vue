<template>
	<el-popover v-model="visible" :trigger="trigger" popper-class="date-picker-wrapper"  @click.stop>
		<el-date-picker
			popper-class="lq-date-picker"
			v-model="dateValue"
			type="daterange"
			@change="handleChange"
			:append-to-body="false"
			value-format="yyyy-MM-dd"
			start-placeholder="开始日期"
			end-placeholder="结束日期"
			:unlink-panels="true"
		>
		</el-date-picker>
		<div style="text-align: right; margin-top: 16px">
			<el-button size="mini" @click="visible = false">取消</el-button>
			<el-button type="primary" size="mini" @click="handleConfirm">确定</el-button>
		</div>
		<el-button :class="btnType === 'text' ? 'date-icon-text' : 'date-icon-btn'" :type="btnType" slot="reference">
			<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
				<path
					class="icon-path"
					d="M12.0556 2.53992H9.75873V1.66492C9.75873 1.60476 9.70951 1.55554 9.64935 1.55554H8.88373C8.82357 1.55554 8.77435 1.60476 8.77435 1.66492V2.53992H5.27435V1.66492C5.27435 1.60476 5.22514 1.55554 5.16498 1.55554H4.39935C4.3392 1.55554 4.28998 1.60476 4.28998 1.66492V2.53992H1.9931C1.75111 2.53992 1.5556 2.73542 1.5556 2.97742V12.0555C1.5556 12.2975 1.75111 12.493 1.9931 12.493H12.0556C12.2976 12.493 12.4931 12.2975 12.4931 12.0555V2.97742C12.4931 2.73542 12.2976 2.53992 12.0556 2.53992ZM11.5087 11.5087H2.53998V6.31336H11.5087V11.5087ZM2.53998 5.38367V3.52429H4.28998V4.18054C4.28998 4.2407 4.3392 4.28992 4.39935 4.28992H5.16498C5.22514 4.28992 5.27435 4.2407 5.27435 4.18054V3.52429H8.77435V4.18054C8.77435 4.2407 8.82357 4.28992 8.88373 4.28992H9.64935C9.70951 4.28992 9.75873 4.2407 9.75873 4.18054V3.52429H11.5087V5.38367H2.53998Z"
					fill="black"
					fill-opacity="0.45"
				/></svg>
		</el-button>
	</el-popover>
</template>
<script>
export default {
	props: {
		btnType: {
			type: String,
			default: 'text'
		},
		trigger: {
			type: String,
			default: 'click'
		}
	},
	data() {
		return {
			visible: false,
			dateValue: ''
		};
	},
	methods: {
		handleChange() {
			this.hasChanged = true;
		},
		handleConfirm() {
			if (this.dateValue && this.dateValue.length === 2) {
				console.log(this.dateValue);
				this.$emit('change', this.dateValue);
				this.visible = false;
			} else {
				this.$message.error('请选择时间');
			}
		}
	}
};
</script>
<style lang="scss" scoped>
::v-deep .date-icon-text {
	margin: unset;
	padding: unset;
}
.date-picker-wrapper {
	.el-date-editor {
		width: 246px;
	}
}
</style>
