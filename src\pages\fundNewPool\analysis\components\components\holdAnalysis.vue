<template>
  <div>
    <el-menu :default-active="activeIndex"
             class=""
             style='margin-right:16px;margin-bottom:16px'
             mode="horizontal"
             @select="menuSelect">
      <el-menu-item v-for="item in menuList"
                    :key="item.value"
                    :index="item.value">{{ item.label }}</el-menu-item>
    </el-menu>
    <div class="flex_card">
      <!-- <div> -->
      <component v-for="item in templateList"
                 :key="item.value"
                 v-show="item.isshow && item.class.some((v) => v == activeIndex)"
                 :class="item.type"
                 :is="item.is"
                 :ref="item.value"
                 :types.sync="activeIndex"
                 @resolveFather="item.methods"
                 v-loading="loading"></component>
      <!-- </div> -->
    </div>
  </div>
</template>

<script>
// 表现风格
import expressionStyle from "@/pages/fundNewPool/analysis/components/components/expressionStyle.vue";
// 前十大持仓
import holdTop10 from "@/pages/fundNewPool/analysis/components/components/holdTop10.vue";
// 行业能力
import industryCapability from "@/pages/fundNewPool/analysis/components/components/industryCapability.vue";
// 市场适应性
import marketCapability from "@/pages/fundNewPool/analysis/components/components/marketCapability.vue";
// 转债分析
import cbondAnalysis from "@/pages/fundNewPool/analysis/components/components/cbondAnalysis.vue";
// 信用下沉
import creditDown from "@/pages/fundNewPool/analysis/components/components/creditDown.vue";
// 久期拉长
import durationLong from "@/pages/fundNewPool/analysis/components/components/durationLong.vue";

export default {
  components: {
    expressionStyle,
    holdTop10,
    industryCapability,
    marketCapability,
    creditDown,
    durationLong,
    cbondAnalysis
  },
  data () {
    return {
      info: {},
      activeIndex: "equity",
      menuList: [
        {
          label: "权益分析",
          value: "equity"
        },
        {
          label: "债券分析",
          value: "bond"
        }
      ],
      templateList: [
        {
          name: "表现风格",
          is: "expressionStyle",
          value: "expressionStyle",
          class: ["equity"],
          type: "big_template",
          isshow: true,
          is_request: false,
          getData: "getExpressionStyleData",
          getRequestData: "getExpressionStyle"
        },
        {
          name: "前十大持仓",
          is: "holdTop10",
          class: ["equity"],
          value: "holdTop10",
          type: "big_template",
          isshow: true,
          is_request: false,
          getData: "getHoldTop10Data",
          getRequestData: "getHoldTop10"
        },
        {
          name: "行业能力",
          is: "industryCapability",
          value: "industryCapability",
          class: ["equity"],
          type: "big_template",
          isshow: true,
          is_request: false,
          getData: "getIndustryCapabilityData",
          getRequestData: "getIndustryCapability"
        },
        {
          name: "市场适应性",
          is: "marketCapability",
          value: "marketCapability",
          class: ["equity"],
          type: "big_template",
          isshow: true,
          is_request: false,
          getData: "getMarketCapabilityData",
          getRequestData: "getMarketCapability"
        },
        {
          name: "前五大持仓",
          is: "holdTop10",
          class: ["bond"],
          value: "holdTop5",
          type: "big_template",
          isshow: true,
          is_request: false,
          getData: "getHoldTop10Data2",
          getRequestData: "getHoldTop102"
        },
        {
          name: "转债分析",
          is: "cbondAnalysis",
          value: "cbondAnalysis",
          class: ["bond"],
          type: "big_template",
          isshow: true,
          is_request: false,
          getData: "getCbondAnalysisData",
          getRequestData: "getCbondAnalysis"
        },
        {
          name: "市场适应性",
          is: "marketCapability",
          value: "marketCapabilityBond",
          class: ["bond"],
          type: "big_template",
          isshow: true,
          is_request: false,
          getData: "getMarketCapabilityData",
          getRequestData: "getMarketCapability"
        },
        {
          name: "信用下沉",
          is: "creditDown",
          value: "creditDown",
          class: ["bond"],
          type: "big_template",
          isshow: true,
          is_request: false,
          getData: "getMarketCapabilityData",
          getRequestData: "getMarketCapability"
        },
        {
          name: "久期拉长",
          is: "durationLong",
          value: "durationLong",
          class: ["bond"],
          type: "big_template",
          isshow: true,
          is_request: false,
          getData: "getDurationLongData",
          getRequestData: "getDurationLong"
        }
      ]
    };
  },
  methods: {
    getData (info) {
      this.info = info;
      this.templateList = this.templateList.map(item => {
        return { ...item, is_request: false };
      });
      this.requestChilden();
    },
    changeCareList (info) {
      this.info = info;
      if (
        this.templateList
          .filter(v => v.class.includes("type"))
          .every(v => v.is_request)
      ) {
        this.$refs["expressionStyle"]?.[0].getData(this.info);
        this.$refs["holdTop10"]?.[0].refresInfo(this.info);
        this.$refs["industryCapability"]?.[0].refresInfo(this.info);
        this.$refs["marketCapability"]?.[0].refresInfo(this.info);
      }
      if (
        this.templateList
          .filter(v => v.class.includes("bond"))
          .every(v => v.is_request)
      ) {
        this.$refs["holdTop5"]?.[0].refresInfo(this.info);
        this.$refs["cbondAnalysis"]?.[0].refresInfo(this.info);
        this.$refs["marketCapabilityBond"]?.[0].refresInfo(this.info);
        this.$refs["creditDown"]?.[0].refresInfo(this.info);
        this.$refs["durationLong"]?.[0].refresInfo(this.info);
      }
    },
    menuSelect (val) {
      this.activeIndex = val;
      this.$nextTick(() => {
        this.requestChilden();
      });
    },
    requestChilden () {
      this.templateList = this.templateList.map(item => {
        if (item.class.some(v => v == this.activeIndex && !item.is_request)) {
          this.$refs[item.value]?.[0].getData(this.info);
          return {
            ...item,
            is_request: true
          };
        } else {
          return { ...item };
        }
      });
    }
  }
};
</script>

<style></style>
