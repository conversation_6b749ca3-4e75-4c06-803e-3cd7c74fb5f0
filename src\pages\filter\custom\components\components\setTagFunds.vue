<template>
	<el-dialog title="配置产品" :visible.sync="dialogVisible">
		<div>
			<el-form :inline="true" :model="formInline" class="demo-form-inline">
				<el-form-item label="一级标签">
					<el-input v-model="formInline.name" disabled placeholder="一级标签"></el-input>
				</el-form-item>
				<el-form-item required label="二级标签">
					<el-select v-model="formInline.region" placeholder="二级标签" @change="getFundCode">
						<el-option v-for="item in second_list" :key="item.value" :label="item.label" :value="item.value"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="添加方式">
					<el-radio-group v-model="formInline.resource">
						<el-radio label="0">手动添加</el-radio>
						<el-radio label="1">批量导入产品代码</el-radio>
					</el-radio-group>
				</el-form-item>
			</el-form>
			<div v-show="formInline.resource == 0">
				<div class="flex_start mb-8" style="align-items: start">
					<div style="width: 72px">产品代码</div>
					<el-input type="textarea" rows="8" v-model="formInline.desc" placeholder="请复制产品代码到本区域，一行一个代码"></el-input>
				</div>
				<span>已输入{{ code_nums }}个代码</span>
			</div>
			<upload-excel
				v-show="formInline.resource == 1"
				@refrshtable="refrshtable"
				:categoryId="formInline.region"
				:append="formInline.append"
				ref="uploadExcel"
			></upload-excel>
			<el-checkbox style="float: right" class="mt-8" v-model="formInline.append">是否覆盖已有记录</el-checkbox>
		</div>
		<div slot="footer">
			<el-button @click="dialogVisible = false">取 消</el-button>
			<el-button type="primary" @click="submit">确 定</el-button>
		</div>
	</el-dialog>
</template>

<script>
import uploadExcel from './uploadExcel.vue';
import { getFundCode, importTagByHand } from '@/api/pages/SystemMixed.js';

export default {
	components: { uploadExcel },
	data() {
		return {
			dialogVisible: false,
			second_list: [],
			id: '',
			formInline: { name: '', region: '', resource: '0', desc: '', append: false }
		};
	},
	computed: {
		code_nums() {
			return this.formInline.desc.split('\n').length - 1;
		}
	},
	methods: {
		getData(data) {
			this.id = data?.id;
			this.formInline.name = data?.first_type;
			this.second_list = data?.second_type.map((v) => {
				return { label: v.name, value: v.id };
			});
			this.$set(this.formInline, 'region', this.second_list[0].value);
			this.getFundCode();
			this.dialogVisible = true;
		},
		// 获取分类下的基金代码
		async getFundCode() {
			let data = await getFundCode({ categoryId: this.formInline.region });
			if (data?.mtycode == 200) {
				this.$set(this.formInline, 'desc', data?.data.join('\n'));
			}
		},
		submit() {
			if (this.formInline.resource == 0) {
				this.setTagFunds();
			} else {
				this.$refs.uploadExcel.submit();
				this.$emit('resolveFather');
				this.dialogVisible = false;
			}
		},
		// 获取配置基金参数
		async setTagFunds() {
			let data = await importTagByHand({
				append: !this.formInline.append,
				categoryId: this.formInline.region,
				codes: this.formInline.desc.split('\n').join(',')
			});
			if (data?.mtycode == 200) {
				this.dialogVisible = false;
				this.$emit('resolveFather');
			}
		},
		refrshtable() {}
	}
};
</script>

<style></style>
