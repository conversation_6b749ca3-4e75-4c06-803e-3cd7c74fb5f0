<!--  -->
<template>
	<div class="homebodyfontsize affixstyle">
		<el-popover
			placement="left"
			trigger="click"
			v-model="visible"
			style="float: right; display: block; clear: both; background: rgba(255, 255, 255, 0.3)"
		>
			<div style="display: flex; flex-wrap: wrap">
				<el-button class="buttonmargin">开发中</el-button>
				<!-- <el-button class="buttonmargin" @click='showtable'>查看</el-button>
             <el-button class="buttonmargin" @click='gotocpmpare(0)'>比较</el-button>
             <el-button class="buttonmargin">组合</el-button> -->
			</div>
			<el-button
				type="primary"
				class="affixbotton"
				v-on:mouseover="visible = true"
				icon="el-icon-shopping-bag-1"
				slot="reference"
			></el-button>
		</el-popover>
		<el-dialog title="篮子" width="60vw;" :visible.sync="visibleshow" :modal-append-to-body="false">
			<div>
				<div style="display: flex; flex-wrap: wrap">
					<el-button style="margin-left: 0" class="buttonmargin" @click="addpool">入池</el-button>
					<el-button class="buttonmargin" @click="printconsole">另存为</el-button>
					<el-button class="buttonmargin" @click="gotocpmpare(1)">比较分析</el-button>
					<el-button class="buttonmargin">组合分析</el-button>
				</div>
				<el-table height="50vh" :data="ana" @selection-change="handleSelectionChange">
					<el-table-column type="selection" :width="returnwidth(55)"> </el-table-column>
					<el-table-column prop="manager_code" label="代码"> </el-table-column>
					<el-table-column prop="name" label="姓名"> </el-table-column>
					<el-table-column prop="fund_co" label="基金公司"> </el-table-column>
					<el-table-column prop="education" label="学历"> </el-table-column>
					<el-table-column prop="gender" :show-overflow-tooltip="true" label="性别"> </el-table-column>

					<el-table-column align="center">
						<template slot-scope="scope">
							<el-popover placement="top" width="160" :ref="`popover-${scope.$index}`">
								<p>是否确定删除</p>
								<div style="text-align: right; margin: 0">
									<el-button type="primary" size="mini" @click="delebasket(scope)">删除</el-button>
								</div>
								<el-button class="buttonclass" type="text" icon="el-icon-delete" slot="reference"></el-button>
							</el-popover>
						</template>
						<!-- <template slot-scope="scope"><div @click="delebasket(scope.row.id,scope.row.name)"><i  class="el-icon-delete-solid icon_color"></i></div></template>   -->
					</el-table-column>
				</el-table>
			</div>
		</el-dialog>
		<el-dialog title="选择添加的基金池" :visible.sync="addfundvis" width="20%" :modal-append-to-body="false">
			基金代码:<br /><el-input type="textarea" :disabled="true" v-model="codearray" label="基金代码"></el-input>
			<!-- 基金名称:<br/><el-input  type="text" :disabled="true"  label="基金名称" ></el-input> -->
			<div v-loading="loadingpool">
				基金池：<br /><el-select style="width: 100%" v-model="choosepoolvalue" placeholder="请选择您的基金池">
					<el-option v-for="item in optionsbase" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>
				<!-- <br/>理由:<br/><el-input type="textarea" label="选择的理由"></el-input> -->
			</div>
			<span slot="footer" class="dialog-footer">
				<el-button type="primary" @click="saveEdit()">提 交</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
import axios from '@/api/index.js';
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { fontSize } from '@/assets/js/echartsrpxtorem'; //注意路径
export default {
	//import引入的组件需要注入到对象中才能使用
	components: {},
	data() {
		//这里存放数据
		return {
			delflagss: false,
			tabledatass: [],
			optionsbase: [],
			visible: false,
			visibleshow: false,
			ana: null,
			addfundvis: false,
			loadingpool: true,
			choosebasket: [],
			choosepoolvalue: null,
			codearray: null
		};
	},
	//监听属性 类似于data概念
	computed: {},
	//监控data中的数据变化
	watch: {},
	//方法集合
	methods: {
		printconsole() {
			const { export_json_to_excel } = require('@/vendor/Export2Excel');
			var list = [];
			let tHeader = [];
			let filterVal = [];

			tHeader = ['代码', '姓名', '基金公司', '学历', '性别'];
			filterVal = ['manager_code', 'name', 'fund_co', 'education', 'gender'];
			// ////console.log(this.colums)
			for (let i = 0; i < this.ana.length; i++) {
				list[i] = [];
				list[i][0] = this.ana[i].manager_code;
				list[i][1] = this.ana[i].name;
				list[i][2] = this.ana[i].fund_co;
				list[i][3] = this.ana[i]['education'];
				list[i][4] = this.ana[i]['gender'];
			}

			export_json_to_excel(tHeader, list, '基金经理篮子');
		},
		showtable() {
			this.visibleshow = true;
			let that = this;
			axios
				.get(that.$baseUrl + '/pool/basket_manager/')
				.then((res) => {
					////console.log('datess')
					////console.log(res.data)
					that.ana = res.data;
				})
				.catch((err) => {
					//  that.$message('失败')
					////console.log(err)
					//that.$message('数据缺失')
				});
		},
		delebasket(scope) {
			let that = this;
			axios
				.delete(that.$baseUrl + '/pool/basket_manager/?id=' + scope.row.id)
				.then((res) => {
					that.delflagss = true;
					scope._self.$refs[`popover-${scope.$index}`].doClose();
					that.showtable();
				})
				.catch((err) => {
					//  that.$message('失败')
					////console.log(err)
					//that.$message('数据缺失')
				});
		},
		addpool() {
			//添加入池
			if (this.choosebasket.length == 0) {
				this.$message('请选择入池基金经理');
			} else {
				this.addfundvis = true;
				this.loadingpool = true;
				let tempstring = '';
				for (let k = 0; k < this.choosebasket.length; k++) {
					tempstring += this.choosebasket[k].manager_code + '     ';
				}
				this.codearray = tempstring;
				let that = this;
				axios
					.get(that.$baseUrl + '/pool/manager_pool/')
					.then((res) => {
						////console.log('pool')
						let haveoption = [];
						for (let i = 0; i < res.data.length; i++) {
							haveoption.push({ value: res.data[i].id, label: res.data[i].name });
						}
						that.optionsbase = haveoption;
						if (that.optionsbase.length > 0) {
							that.choosepoolvalue = that.optionsbase[0].value;
						}
						this.loadingpool = false;
						////console.log( that.optionsbase)
					})
					.catch((err) => {
						////console.log(err)
						this.loadingpool = false;
						//that.$message('数据缺失')
					});
			}
		},
		handleSelectionChange(val) {
			//获取选中状态
			// ////console.log(val)
			this.choosebasket = val;
		},
		// gettable() {
		//         //获取篮子数据
		// },
		returnwidth(val) {
			return fontSize(val);
		},
		saveEdit() {
			let temparr = [];
			for (let i = 0; i < this.choosebasket.length; i++) {
				temparr.push({ manager_pool_id: this.choosepoolvalue, manager_code: this.choosebasket[i].manager_code });
			}
			// ////console.log(temparr)
			let that = this;
			axios
				.post(that.$baseUrl + '/pool/pool_managers/', { temparr })
				.then((res) => {
					that.$message('新增成功');
					that.addfundvis = false;
				})
				.catch((err) => {
					////console.log(err)
					//that.$message('数据缺失')
				});
		},
		gotocpmpare(val) {
			//前往对比
			if (val == 0) {
				this.$router.push('/comparefundormanager');
			} else {
				this.visibleshow = false;
				this.$router.push('/comparefundormanager');
			}
		}
	},
	//生命周期 - 创建完成（可以访问当前this实例）
	created() {
		// this.gettable()
		// this.showtable()
	},
	//生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	beforeCreate() {}, //生命周期 - 创建之前
	beforeMount() {}, //生命周期 - 挂载之前
	beforeUpdate() {}, //生命周期 - 更新之前
	updated() {}, //生命周期 - 更新之后
	beforeDestroy() {}, //生命周期 - 销毁之前
	destroyed() {}, //生命周期 - 销毁完成
	activated() {} //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style>
.affixstyle {
	z-index: 2001;
	position: fixed;
	bottom: 40px;
	right: 40px;
	color: #409eff;
}
.affixbotton {
	width: 40px;
	padding: 10px 10px !important;
	height: 40px;
	border-radius: 50% !important;
}
.affixstyle .el-icon-shopping-bag-1 {
	font-size: 20px;
}
.buttonmargin {
	margin: 10px;
}
</style>
