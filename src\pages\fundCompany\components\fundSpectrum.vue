<template>
	<div>
		<!-- <span>基金产品频谱</span> -->
		<el-card>
			<div slot="header" class="header-title">
				<i class="points"></i>
				<span>基金产品频谱</span>
			</div>
			<el-row>
				<el-col>
					<div id="spectrum-main" style="height: 80vh; width: 100%"></div>
				</el-col>
			</el-row>
			<el-row>
				<el-col class="chart-box">
					<div id="fund-spectrum-main" style="height: 60vh; width: 100%"></div>
				</el-col>
			</el-row>
		</el-card>
	</div>
</template>

<script>
import { fontSize } from '@/assets/js/echartsrpxtorem'; //注意路径
import axios from '@/api/index';
export default {
	data() {
		return {};
	},
	created() {},
	mounted() {
		this.generateChart();
		this.testChart();
	},
	methods: {
		generateChart() {
			let id = 'fund-spectrum-main';
			if (document.getElementById(id) == null) {
				//console.log('error: 不存在该DOM');
				return;
			}
			echarts.dispose(document.getElementById(id));
			let myChart = echarts.init(document.getElementById(id));
			// FIXME: 虚拟数据
			let data = [
				{
					name: 'flare',
					children: [
						{
							name: 'analytics',
							children: [
								{ name: 'aaa', value: (Math.random() * 100).toFixed(2) },
								{ name: 'bbb', value: (Math.random() * 100).toFixed(2) },
								{ name: 'ccc', value: (Math.random() * 100).toFixed(2) }
							]
						},
						{
							name: 'mergeedge',
							children: [
								{ name: 'ddd', value: (Math.random() * 100).toFixed(2) },
								{ name: 'eee', value: (Math.random() * 100).toFixed(2) },
								{ name: 'fff', value: (Math.random() * 100).toFixed(2) }
							]
						},
						{
							name: 'indexType',
							children: [
								{ name: 'ggg', value: (Math.random() * 100).toFixed(2) },
								{ name: 'hhh', value: (Math.random() * 100).toFixed(2) },
								{ name: 'iii', value: (Math.random() * 100).toFixed(2) }
							]
						}
					]
				}
			];
			let option = {
				tooltip: {
					trigger: 'item',
					triggerOn: 'mousemove'
				},
				series: [
					{
						type: 'tree',
						orient: 'TB',
						edgeShape: 'polyline',
						roam: true,
						emphasis: {
							focus: 'descendant'
						},
						label: {
							position: 'top',
							fontSize: fontSize(14)
						},
						leaves: {
							// 叶子节点
							label: {
								position: 'right',
								verticalAlign: 'middle',
								align: 'left'
							}
						},
						data: data
					}
				]
			};

			myChart.setOption(option, true);
		},
		testChart() {
			let id = 'spectrum-main';
			if (document.getElementById(id) == null) {
				//console.log('error: 不存在该DOM');
				return;
			}
			echarts.dispose(document.getElementById(id));
			let myChart = echarts.init(document.getElementById(id));
			// FIXME: 虚拟数据
			let data = [
				{
					name: '行业',
					children: [
						{
							name: '科技',
							children: [
								{
									name: '易方达科技混合债券A',
									value: 1
								}
							]
						},
						{
							name: '医疗',
							children: [
								{
									name: '易方达医疗混合债券A',
									value: 1
								}
							]
						},
						{
							name: '消费',
							children: [
								{
									name: '易方达消费混合债券A',
									value: 1
								}
							]
						},
						{
							name: '综合',
							children: [
								{
									name: '易方达综合混合债券A',
									value: 1
								}
							]
						},
						{
							name: '新能源',
							children: [
								{
									name: '易方达新能源混合债券A',
									value: 1
								}
							]
						}
					]
				},
				{
					name: '基金类别',
					children: [
						{
							name: '股票',
							children: [
								{
									name: '易方达科技股票平衡A',
									value: 1
								}
							]
						},
						{
							name: '债券',
							children: [
								{
									name: '易方达科技债券A',
									value: 1
								}
							]
						},
						{
							name: 'QDII',
							children: [
								{
									name: '易方达科技货币QDII',
									value: 1
								}
							]
						}
					]
				},
				{
					name: '成长价值',
					children: [
						{
							name: '偏成长',
							children: [
								{
									name: '易方达成长混合A',
									value: 1
								}
							]
						},
						{
							name: '偏价值',
							children: [
								{
									name: '易方达价值混合A',
									value: 1
								}
							]
						}
					]
				}
			];
			let option = {
				series: {
					type: 'sunburst',
					data: data,
					radius: ['0%', '80%'],
					emphasis: {
						focus: 'ancestor'
					},
					levels: [
						{},
						{
							r0: '15%',
							r: '35%',
							itemStyle: {
								borderWidth: 5
							},
							label: {
								rotate: 'tangential' // tangential / radial
							}
						},
						{
							r0: '35%',
							r: '55%',
							label: {
								align: 'right'
							},
							itemStyle: {
								borderWidth: 5
							}
						},
						{
							r0: '55%',
							r: '60%',
							label: {
								position: 'outside',
								padding: 3,
								silent: false
							},
							itemStyle: {
								borderWidth: 5
							}
						}
					]
				}
			};
			myChart.setOption(option, true);
		}
	}
};
</script>

<style scoped lang="scss">
.header-title {
	font-size: 16px;
}
.chart-box {
	border: 1px solid #e1e1e1;
	border-radius: 10px;
}
</style>
