<template>
	<!--服务机构页面-->
	<div class="company-management">
		<div class="container">
			<div style="text-align: right">
				<el-button class="add-company" type="primary" @click="openCompanyDrawer(1)">新增机构</el-button>
			</div>
			<!--按照条件自动搜索-->
			<el-form :inline="true">
				<el-form-item>
					<el-input v-model="searchCompany" placeholder="输入机构名称搜索" />
				</el-form-item>

				<el-form-item>
					<el-input v-model="searchContact" placeholder="输入销售计划表关键字" />
				</el-form-item>

				<!-- <el-form-item>
					<el-input v-model="searchMobile" placeholder="输入联系电话搜索" />
				</el-form-item> -->
				<el-form-item>
					<el-button type="primary" @click="onSearchCompany">确认</el-button>
				</el-form-item>
			</el-form>
			<el-table v-loading="tableDataLoading" :data="tableDataNow" class="company-table" height="calc(100vh - 535px)">
				<el-table-column prop="name" label="机构名称" :width="240" show-overflow-tooltip align="gotoleft"> </el-table-column>
				<el-table-column prop="contact" label="联系人" align="gotoleft" :width="120"></el-table-column>
				<el-table-column prop="contact" label="机构服务状态" align="gotoleft" :width="180">
					<template slot-scope="{ row }">
						<span v-show="row.validStatus == 1" style="color: #20995b">正式用户</span>
						<span v-show="row.validStatus == 2">试用用户</span>
						<span v-show="row.validStatus == 3" style="color: #f56c6c">过期用户</span>
						<span v-show="!row.validStatus">无有效销售计划</span>
					</template>
				</el-table-column>
				<el-table-column prop="mobile" label="联系电话" align="gotoleft" :width="200"></el-table-column>
				<el-table-column prop="serviceplansText" label="销售计划表" align="gotoleft" show-overflow-tooltip></el-table-column>
				<el-table-column type="expand" width="20" align="gotoleft">
					<template slot-scope="{ row }">
						<el-table :data="row.serviceplans" class="serviceplans-list-table">
							<el-table-column prop="title" label="合约名称" align="gotoleft" :width="240" show-overflow-tooltip></el-table-column>
							<el-table-column prop="amount" label="金额" align="gotoleft" :width="120"></el-table-column>
							<el-table-column prop="effect_from" label="有效时间" align="gotoleft" :width="200">
								<template slot-scope="{ row }">
									<span v-if="row.effect_from && row.expired_at">
										{{ row.effect_from.slice(0, 10) + '~' + row.expired_at.slice(0, 10) }}
									</span>
									<span v-else>数据有误</span>
								</template>
							</el-table-column>
							<el-table-column prop="groupsText" label="权限" align="gotoleft" show-overflow-tooltip></el-table-column>
							<el-table-column label="对应销售" :width="120">
								<template slot-scope="{ row }">
									<span v-if="row.salesmanText">
										{{ row.salesmanText }}
									</span>
									<span v-else style="color: #f56c6c; cursor: help" title="请将对应销售修改为销售角色员工">{{ row.salesman }}</span>
								</template>
							</el-table-column>
							<el-table-column align="gotoleft" label="操作" :width="283">
								<template slot-scope="{ row }">
									<el-button type="text" size="small" @click="openSaleDrawer('check', row.institute, row.id)">查看</el-button>
									<el-button
										type="text"
										size="small"
										@click="openSaleDrawer('edit', row.institute, row.id)"
										v-if="row.expired_at && new Date(row.expired_at) > new Date()"
										>编辑</el-button
									>
								</template>
							</el-table-column>
						</el-table>
					</template>
				</el-table-column>
				<el-table-column align="gotoleft" label="操作" :width="400">
					<template slot-scope="{ row }">
						<div class="user-management-button">
							<el-button @click="openCompanyDrawer(3, row.id)" type="text" size="small">查看</el-button>
							<el-button @click="openCompanyDrawer(2, row.id)" type="text" size="small">编辑</el-button>
							<el-button @click="companyDelete(row.id)" type="text" size="small" style="color: #f56c6c">删除</el-button>
							<el-button @click="openSaleDrawer('add', row.id)" type="text" size="small">新增销售计划对应表</el-button>
						</div>
					</template>
				</el-table-column>
			</el-table>
			<!--分页功能-->
			<el-pagination
				background
				style="display: flex; justify-content: right; padding-top: 16px; padding-bottom: 24px"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
				:current-page.sync="currentPage"
				:page-sizes="[10, 20, 40, 60, 80, 100]"
				:page-size="pageSIze"
				layout="total, sizes, prev, pager, next, jumper"
				:total="tableData.length"
			>
			</el-pagination>
			<div>
				<!-- 新增机构 -->
				<el-drawer :visible.sync="companyDrawer" :with-header="false" :wrapperClosable="false" size="40%">
					<div class="container">
						<institute-form
							:visible="companyDrawer"
							@companyclose="closeCompanyDrawer"
							:id="editCompanyId"
							:isCheck="companyDrawerIscheck"
						></institute-form>
					</div>
				</el-drawer>
				<!-- 新增 机构销售计划对应表 -->
				<el-drawer
					class="sale-drawer"
					title="机构销售计划对应表"
					:visible.sync="saleDrawer"
					:with-header="false"
					:wrapperClosable="false"
					size="40%"
				>
					<div class="container">
						<div class="drawer-header">
							<div class="header-title">
								<span>机构销售计划对应表</span>
							</div>
							<div class="close-btn" @click="saleCancel">
								<i class="el-icon-close"></i>
							</div>
						</div>
						<el-form ref="saleForm" :model="saleForm" :rules="saleRules" class="edit-sale">
							<el-form-item label="计划名称" prop="title">
								<el-input v-model="saleForm.title" placeholder="请输入" :disabled="!saleEditable"></el-input>
							</el-form-item>
							<el-form-item label="对应销售" prop="salesman">
								<el-select v-model="saleForm.salesman" placeholder="请选择" :disabled="!saleEditable" filterable>
									<el-option v-for="item of salesmanList" :key="item.id" :value="item.id" :label="item.username" filterable></el-option>
								</el-select>
							</el-form-item>
							<el-form-item label="金额" prop="amount">
								<el-input v-model="saleForm.amount" placeholder="请输入" :disabled="!saleEditable">
									<template slot="append">万元</template>
								</el-input>
							</el-form-item>
							<el-form-item label="机构外键" prop="company">
								<el-select v-model="saleForm.company" placeholder="请选择" disabled>
									<el-option v-for="item of companyList" :key="item.id" :value="item.id" :label="item.name"></el-option>
								</el-select>
							</el-form-item>
							<el-form-item label="有效时间" prop="duration">
								<el-date-picker
									v-model="saleForm.duration"
									type="daterange"
									:unlink-panels="true"
									range-separator="~"
									start-placeholder="起始日期"
									end-placeholder="结束日期"
									:disabled="!saleEditable"
								></el-date-picker>
							</el-form-item>

							<el-form-item label="权限管理">
								<br />
								<el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange" :disabled="!saleEditable"
									>全选</el-checkbox
								>
								<el-checkbox-group v-model="saleForm.groups" @change="checkedPermissionChange">
									<el-checkbox
										v-for="(permission, index) of permissionList"
										:label="permission"
										:key="permission"
										:disabled="!saleEditable"
										>{{ permissionLabelList[index] }}</el-checkbox
									>
								</el-checkbox-group>
							</el-form-item>

							<el-form-item class="submit-btn">
								<div v-if="saleEditable">
									<el-button type="primary" @click="saleSubmitForm()">保存</el-button>
									<el-button @click="saleCancel()">取消</el-button>
								</div>
								<div v-else>
									<el-button @click="saleDrawer = false">关闭</el-button>
								</div>
							</el-form-item>
						</el-form>
					</div>
				</el-drawer>
			</div>
		</div>
	</div>
</template>

<script>
import instituteForm from './components/instituteForm.vue';
import {
	getInstituesList,
	getUsersSalesman,
	getUsersGroups,
	refreshUserGroups,
	deleteInstitues,
	getServiceplans,
	patchServiceplans,
	postServiceplans
} from '@/api/pages/SystemOther.js';
export default {
	data() {
		return {
			title: '机构管理',
			tableData: [],
			tableDataNow: [],
			currentPage: 1,
			pageSIze: 10,
			allData: [],
			searchCompany: '',
			searchContact: '',
			searchMobile: '',
			// 新增机构-抽屉
			companyDrawer: false,
			companyDrawerIscheck: false,
			editCompanyId: '',
			// 新增机构销售计划对应表-抽屉
			saleDrawer: false,
			saleForm: {
				id: '',
				title: '',
				salesman: '',
				amount: '',
				duration: [],
				company: '',
				groups: []
			},
			isIndeterminate: false,
			permissionList: [],
			permissionLabelList: [],
			checkAll: false,
			// 选择下拉列表
			salesmanList: [],
			positionList: [],
			companyList: [],
			saleRules: {
				title: { required: true, message: '计划名称不能为空', trigger: 'blur' },
				salesman: { required: true, message: '对应销售不能为空', trigger: 'blur' },
				amount: [
					{ required: true, message: '金额不能为空', trigger: 'blur' },
					{ type: 'string', pattern: /^\d*(\.\d+)?$/, message: '请输入正确的金额', trigger: 'blur' }
				],
				company: { required: true, message: '机构外键不能为空', trigger: 'blur' },
				duration: { required: true, message: '有效时间不能为空', trigger: 'blur' }
			},
			haveDebounce: false,
			saleEditable: false,
			tableDataLoading: true
		};
	},
	components: {
		instituteForm
	},
	async created() {
		await this.getSalesmanList();
		this.getpermissionList();
		this.getCompanyList();
	},
	methods: {
		// 输入搜索-防抖
		// inputSearchCompany(val) {
		// 	if (this.haveDebounce) return;
		// 	this.haveDebounce = true;
		// 	setTimeout(() => {
		// 		this.getCompanyList(val);
		// 		this.haveDebounce = false;
		// 	}, 500);
		// },
		handleSizeChange(val) {
			this.pageSIze = val;
			this.chagePageAndSize();
		},
		handleCurrentChange(val) {
			this.currentPage = val;
			this.chagePageAndSize();
		},
		chagePageAndSize() {
			this.tableDataNow = this.tableData.slice((this.currentPage - 1) * this.pageSIze, this.currentPage * this.pageSIze);
		},
		// 获取机构列表
		async getCompanyList() {
			this.tableDataLoading = true;
			let data = await getInstituesList();
			this.tableDataLoading = false;
			let list = data.slice();
			list.forEach((company) => {
				company.serviceplansText = ''; // 生成销售计划表title文字
				if (Array.isArray(company.serviceplans) && company.serviceplans.length > 0) {
					company.serviceplansText = company.serviceplans.map((item) => item.title).join(', ');
					let validStatus = 0; // 机构服务状态
					for (let plan of company.serviceplans) {
						// 生成权限描述文字
						plan.groupsText = '';
						if (Array.isArray(plan.groups) && plan.groups.length > 0) {
							plan.groupsText = plan.groups.map((item) => item.description).join(', ');
						} else {
							plan.groupsText = '无';
						}
						// 显示销售名称
						let sale = this.salesmanList.find((item) => item.id == plan.salesman);
						plan.salesmanText = sale ? sale.last_name + sale.first_name : '';
						// 判断机构服务状态：
						// 有效时间包含现在 + 金额 > 0  => 正式用户-validStatus=1
						// 有效时间包含现在 + 金额=0 => 试用用户-validStatus=2
						// 有效时间不包含现在 + 金额 > 0 => 过期用户-validStatus=3
						// 无销售表 => 未添加销售表-validStatus=0
						if (plan.effect_from && plan.expired_at) {
							if (new Date(plan.effect_from) <= new Date() && new Date(plan.expired_at) >= new Date() && parseFloat(plan.amount) > 0) {
								validStatus = 1;
							} else if (
								new Date(plan.effect_from) <= new Date() &&
								new Date(plan.expired_at) >= new Date() &&
								parseFloat(plan.amount) == 0 &&
								validStatus !== 1
							) {
								validStatus = 2;
							} else if (parseFloat(plan.amount) > 0 && validStatus == 0) {
								validStatus = 3;
							}
						}
					}
					company.validStatus = validStatus;
				} else {
					company.serviceplansText = '无';
				}
			});
			this.allData = data;
			this.tableData = data;
			this.chagePageAndSize();
			this.companyList = data;
		},

		// 监听机构名称搜索
		onSearchCompany() {
			if (this.searchCompany || this.searchContact) {
				this.tableData = this.allData.filter((obj) => {
					return (
						(this.searchCompany ? obj.name.indexOf(this.searchCompany) !== -1 : true) &&
						(this.searchContact ? obj.notes.indexOf(this.searchContact) !== -1 : true)
					);
				});
			} else {
				this.tableData = this.allData;
			}
			this.chagePageAndSize();
		},

		async getSalesmanList() {
			let data = await getUsersSalesman();
			this.salesmanList = data.data;
		},
		async getpermissionList() {
			let data = await getUsersGroups();
			this.permissionList = data.map((item) => item.id);
			this.permissionLabelList = data.map((item) => item.description);
		},
		// 机构
		openCompanyDrawer(editType, id) {
			this.editCompanyId = id ? id : '';
			this.companyDrawerIscheck = editType === 3;
			this.companyDrawer = true;
		},
		companyDelete(id) {
			this.$confirm('确认删除该机构?', '提示', {
				distinguishCancelAndClose: true,
				confirmButtonText: '确认',
				cancelButtonText: '取消'
			})
				.then(async () => {
					let data = deleteInstitues(id);
					if (data.mtycode == 200) {
						this.$message.success('删除成功');
						this.getCompanyList();
						this.callRefresh();
					} else {
						this.$message.error(`删除失败: ${data.mtymessage}`);
					}
				})
				.catch(() => {
					//console.log('cancel companyDelete');
				});
		},
		closeCompanyDrawer(haveEdit) {
			this.companyDrawer = false;
			this.editCompanyId = '';
			this.companyDrawerIscheck = false;
			if (haveEdit) {
				this.getCompanyList();
			}
		},
		// 销售
		async openSaleDrawer(openType, instituteId, plansId) {
			this.saleEditable = openType == 'add' || openType == 'edit' ? true : false;

			if (openType == 'add') {
				this.saleForm.company = instituteId;
			} else {
				let data = await getServiceplans(plansId);
				if (data.mtycode == 200) {
					this.saleForm = {
						id: data.id,
						title: data.title,
						salesman: data.salesman,
						amount: data.amount,
						company: data.institute || instituteId,
						duration: [this.FUNC.transformDate(data.effect_from), this.FUNC.transformDate(data.expired_at)],
						groups: data.groups.map((item) => item.id)
					};
				}
			}
			this.saleDrawer = true;
		},
		saleCancel() {
			this.saleDrawer = false;
			this.resetSaleForm();
		},
		saleSubmitForm() {
			if (!this.salesmanList.find((item) => item.id == this.saleForm.salesman)) {
				this.$message.error('请选择"销售"角色的员工为对应销售');
				return;
			}
			this.$refs.saleForm.validate(async (valid) => {
				if (valid) {
					// 由于接口设计问题，日期格式需要符合 YYYY-MM-DDThh:mm[:ss[.uuuuuu]][+HH:MM|-HH:MM|Z]
					let data = {
						title: this.saleForm.title,
						salesman: this.saleForm.salesman,
						amount: this.saleForm.amount,
						effect_from: this.FUNC.transformDate(this.saleForm.duration[0]) + 'T00:00',
						expired_at: this.FUNC.transformDate(this.saleForm.duration[1]) + 'T00:00',
						institute: this.saleForm.company,
						groups: this.saleForm.groups.map((item) => (item = { id: item }))
					};
					let result = {};
					if (!!this.saleForm.id) {
						result = await patchServiceplans(this.saleForm.id, data);
					} else {
						result = await postServiceplans(data);
					}
					if (result.id == 200) {
						this.$message.success('成功');
						this.saleDrawer = false;
						this.resetSaleForm();
						this.getCompanyList();
						this.callRefresh();
					}
				}
			});
		},
		resetSaleForm() {
			this.saleForm = {
				id: '',
				title: '',
				salesman: '',
				amount: '',
				duration: [],
				company: '',
				groups: []
			};
			this.saleEditable = false;
			this.$refs.saleForm.resetFields();
		},
		handleCheckAllChange(val) {
			this.saleForm.groups = val ? this.permissionList : [];
			this.isIndeterminate = false;
		},
		checkedPermissionChange(value) {
			let checkedCount = value.length;
			this.checkAll = checkedCount === this.permissionList.length;
			this.isIndeterminate = checkedCount > 0 && checkedCount < this.permissionList.length;
		},
		async callRefresh() {
			await refreshUserGroups();
			// let data =
			// if (data) {
			// 	this.$message.error('error: ' + data.mtymessage);
			// }
		}
	}
};
</script>

<style lang="scss" scoped>
.company-management {
	::v-deep.el-form-item__content {
		margin-left: auto !important;
	}
	.company-table {
		margin: 50px 0;
	}
	// 新增抽屉-机构 & 销售
	.edit-company,
	.edit-sale {
		.business-card-upload,
		.el-form-item {
			padding: 10px 0;
		}
	}
	.edit-sale {
		.el-select {
			width: 100%;
		}
	}
}

.edit-company,
.edit-sale {
	.submit-btn {
		display: flex;
		justify-content: flex-end;
	}
}
.serviceplans-list-table {
	color: #999;
	border: 1px solid #e9e9e9;
	border-radius: 5px;
}

.sale-drawer .submit-btn {
	display: flex;
	justify-content: flex-end;
}

.sale-drawer {
	// 抽屉自定义表单头样式
	.drawer-header {
		margin-bottom: 20px;
		display: flex;
		justify-content: space-between;
		font-size: 22px;
		.header-title {
			border-bottom: 2px solid #f68136;
		}
		.close-btn {
			cursor: pointer;
		}
	}
}
.user-management-button .el-button--small {
	padding: 12px 20px;
}
</style>
