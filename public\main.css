
.print_header{
    flex-wrap: wrap;
}
.color4096FF{
	width: 42px;
	margin: auto;
	height: 15.4px;
	background: #4096FF;
	border: 1px solid #4096FF;
}
.flex_card {
	width: 100%;
	display: flex;
	justify-content: space-between;
	align-items: center;
	flex-wrap: wrap;
}
.flex_card .card_header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	position: relative;
	padding: 16px 0;
}
.flex_card > .small_template {
	flex: 1;
	min-width: 564px;
	/* height: 288px; */
	padding: 16px 24px 0 24px;
	margin: 12px;
	background: #ffffff;
	box-shadow: 0px 5px 12px 4px rgba(0, 0, 18, 0.02);
	border-radius: 4px;
    position: relative;
	page-break-inside: avoid;
}
.flex_card > .big_template {
	height: auto;
	width: 100%;
	flex-basis: 100%;
}
.flex_card > .small_template .chart_title {
	position: absolute;
	left: 24px;
	top: 16px;
	font-family: 'PingFang';
	font-style: normal;
	font-weight: 500;
	font-size: 16px;
	line-height: 24px;
	color: rgba(0, 0, 0, 0.85);
}
.flex_card > .small_template .title {
	font-family: 'PingFang SC';
}
.flex_card > div .title {
	font-family: 'PingFang';
	font-style: normal;
	font-weight: 500;
	font-size: 16px;
	line-height: 24px;
	color: rgba(0, 0, 0, 0.85);
}
.manager_info_title {
	font-family: 'PingFang';
	font-style: normal;
	font-weight: 500;
	font-size: 18px;
	line-height: 26px;
	color: rgba(0, 0, 0, 0.85);
	margin-left: 12px;
}
.chart_one {
	min-width: 1100px;
	/* height: 390px; */
	padding: 16px 24px 24px 24px;
	margin: 12px;
	background: #ffffff;
	box-shadow: 0px 5px 12px 4px rgba(0, 0, 18, 0.02);
	border-radius: 4px;
    position: relative;
	page-break-inside: avoid;
}
.combination .chart_one {
	min-width: 1152px;
	/* height: 390px; */
	padding: 0 24px 24px 24px;
	margin: 12px;
	background: #ffffff;
	box-shadow: 0px 5px 12px 4px rgba(0, 0, 18, 0.02);
	border-radius: 4px;
}
.emptyClass .title {
	font-family: 'PingFang';
	font-style: normal;
	font-weight: 500;
	font-size: 16px;
	line-height: 24px;
	color: rgba(0, 0, 0, 0.85);
	background: #ffffff;
}
.chart_one .title {
	font-family: 'PingFang';
	font-style: normal;
	font-weight: 500;
	font-size: 16px;
	line-height: 24px;
	color: rgba(0, 0, 0, 0.85);
	background: #ffffff;
}

div .charts_one_class {
	position: relative;
	page-break-inside: avoid;
}
.charts_fill_class .charts_one_class {
	width: 100%;
}
.charts_fill_class .charts_two_class {
	width: 100%;
}
.charts_fill_class .charts_analysis_class {
	width: 100%;
}
.industry_ability .charts_two_class {
	height: 380px;
}
.industry_ability > .small_template {
	height: 440px;
}
div .charts_center_class {
	width: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
}
div .charts_two_class {
	width: 564px;
	height: 210px;
	position: relative;
	page-break-inside: avoid;
}
div .charts_analysis_class {
	height: 280px;
	position: relative;
	page-break-inside: avoid;
}

.analysis_main {
	background: #f7f9fa;
	padding: 12px;
}
.analysis_main .box-card {
	width: 550px;
	height: 280px;
	border: 1px solid #e9e9e9;
	display: flex;
	justify-content: center;
	align-items: center;
}
.analysis_main .flex_card {
	width: 100%;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	flex-wrap: wrap;
}
.analysis_main_company {
	background: #f7f9fa;
	padding: 24px;
}
.backbut {
	position: absolute;
	right: 5px;
	bottom: 10px;
	top: 10px;
	margin: auto;
}
.backbuts {
	position: absolute;
	right: 110px;
	bottom: 10px;
	top: 10px;
	margin: auto;
}
div .type_menu {
	position: -webkit-sticky;
	position: sticky !important;
	top: 0px;
	z-index: 100;
	background: #f7f9fa !important;
	margin: 12px;
}
div .drag_template_list_type_menu {
	display: flex;
	overflow: scroll;
}
div .drag_template_list_type_menu::-webkit-scrollbar {
	height: 0;
}
.main {
	width: 100%;
	height: 100%;
}
.fixed-height-short {
	height: 250px;
}
.flex_basic_info {
	flex: 1;
	min-width: 564px;
	max-width: 564px;
	background: #ffffff;
	margin: 12px;
}
.flex_return {
	flex: 2;
	width: 100%;
	min-width: 564px;
	padding: 16px 40px 24px 24px;
	background: #ffffff;
	margin: 12px;
}
.fundBasicInformation_fundInfo .flex_two {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}
.fundBasicInformation_fundInfo  .flex_two > div {
    min-width: 50%;
    margin-top: 8px;
}
.fundBasicInformation_fundInfo  .ability_progress {
		display: flex;
		justify-content: space-between;
		align-items: center;
		align-self: center;
		height: 100%;
		flex-wrap: wrap;
		padding: 0 7px;
}
.fundBasicInformation_fundInfo  .ability_progress > div {
		width: 140px;
}
.sixBuyAndSellingModes .flex_card > div {
	min-width: 30%;
	flex: 1;
}
.hangyedes {
	/* margin-left: 20px; */
	margin-top: 24px;
	font-family: 'PingFang';
	font-style: normal;
	font-weight: 400;
	font-size: 14px;
	line-height: 22px;
	color: rgba(0, 0, 0, 0.85);
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}
.hangyedes > div {
	margin: 6px 0;
}
#managerInfo .nametitle {
	font-family: 'PingFang';
	font-style: normal;
	font-weight: 500;
	font-size: 18px;
	line-height: 26px;
	margin-left: 16px;
	/* identical to box height, or 144% */

	color: rgba(0, 0, 0, 0.85);
}
#managerInfo .mrring10 {
	margin-right: 10px;
}
#managerInfo .mtysay {
	padding: 10px 0 0;
	margin-right: 20px;
	font-size: 12px;
	color: #606266;
}
#managerInfo .managerpic {
	width: 80px;
	height: 80px;
	/* background:grey; */
}
#managerInfo .managerpic img {
	width: 80px;
	height: 80px;
	background: #d8d8d8;
	border-radius: 50%;
}
#managerInfo .heightjs {
	height: 175px;
	width: 100%;
}
#managerInfo .mf20x {
	margin-left: 20px;
}
#managerInfo .title {
	font-family: 'PingFang';
	font-style: normal;
	font-weight: 500;
	font-size: 16px;
	line-height: 24px;
	color: rgba(0, 0, 0, 0.85);
}
#managerInfo .info {
	font-family: 'PingFang';
	font-style: normal;
	font-weight: 400;
	font-size: 14px;
	line-height: 22px;
	color: rgba(0, 0, 0, 0.65);
	text-overflow: ellipsis;
}
#managerInfo .flex_two {
	width: 100%;
	display: flex;
	justify-content: space-between;
	align-items: center;
	flex-wrap: wrap;
}
#managerInfo .flex_two > div {
	min-width: 50%;
	margin-top: 8px;
}

.active_equity_summary .summary_card {
	/* width: 254px; */
	height: 82px;
	flex: 1;
	display: flex;
	background: #ffffff;
	box-shadow: 0px 2px 8px 2px rgba(0, 0, 0, 0.03);
	border-radius: 4px;
	padding: 16px;
}
.active_equity_summary  .summary_card > div {
	font-family: 'PingFang';
	font-style: normal;
	font-weight: 400;
	font-size: 14px;
	line-height: 22px;
	color: rgba(0, 0, 0, 0.85);
}
.v-note-op{
    display: none;
}
.v-note-edit{
    display: none;
}
.v-note-show{
    display: none;
    width: 100%;
    flex: 0 0 100%;
}
.ql-toolbar.ql-snow{
    display: none;
}
.ql-tooltip{
    display: none;
}
.container .editor-btn{
    display: none;
}