<template>
	<div id="averageRemainingMaturity">
		<analysis-card-title title="基金平均剩余期限" image_id="averageRemainingMaturity"></analysis-card-title>
		<div v-loading="loading">
			<el-row :gutter="10">
				<el-col :span="24">
					<el-card>
						<v-chart
							v-loading="empytzichanpeizh"
							element-loading-text="暂无数据"
							element-loading-spinner="el-icon-document-delete"
							element-loading-background="rgba(239, 239, 239, 0.5)"
							style="width: 100%; height: 50vh"
							:options="peizhioptions"
						/>
						<div>
							基金季报会披露报告期债券回购融资情况，用报告期末债券回购融资余额占基
							金资产净值的比例来表示报告期末时点杠杆率，用报告期内债券回购融资余额占基金资产净值的 比例来表示报告期内平均杠杆率。”
						</div>
					</el-card>
				</el-col>
			</el-row>
		</div>
	</div>
</template>
<script>
import { getTerm } from '@/api/pages/Analysis.js';
import { lineChartOption } from '@/utils/chartStyle.js';

export default {
	data() {
		return {
			empytzichanpeizh: false,
			loading: true,
			peizhioptions: {},
			loadflag: 0,
			newhold: [
				{ id: '1', a2: '2', a3: '3', a4: '4', a5: '5', a6: '6', a7: '7', a8: '8', a9: '9' },
				{ id: '1', a2: '2', a3: '3', a4: '4', a5: '5', a6: '6', a7: '7', a8: '8', a9: '9' },
				{ id: '1', a2: '2', a3: '3', a4: '4', a5: '5', a6: '6', a7: '7', a8: '8', a9: '9' },
				{ id: '1', a2: '2', a3: '3', a4: '4', a5: '5', a6: '6', a7: '7', a8: '8', a9: '9' },
				{ id: '1', a2: '2', a3: '3', a4: '4', a5: '5', a6: '6', a7: '7', a8: '8', a9: '9' },
				{ id: '1', a2: '2', a3: '3', a4: '4', a5: '5', a6: '6', a7: '7', a8: '8', a9: '9' },
				{ id: '1', a2: '2', a3: '3', a4: '4', a5: '5', a6: '6', a7: '7', a8: '8', a9: '9' }
			],
			info: {}
		};
	},
	methods: {
		getData(info) {
			this.info = info;
			this.getstyle();
		},
		async getstyle() {
			this.loading = true;
			let data = await getTerm({ code: this.info.code, type: this.info.type, flag: this.info.flag });
			this.loading = false;
			if (data?.mtycode == 200) {
				this.peizhioptions = lineChartOption({
					xAxis: [{ data: data.data.date.map((v) => v.slice(0, 10)) }],
					yAxis: [{ name: '平均剩余期限（天）' }],
					series: [
						{
							name: '本产品平均剩余期限',
							type: 'line',
							data: data.data.fund_term
						},
						{
							name: '全市场货币基金平均剩余期限',
							type: 'line',
							data: data.data.market_all
						}
					]
				});
			}
		}
	}
};
</script>
<style>
.wavePatternss {
	width: 60vw;
	height: 20vh;
}
</style>
<style lang="scss" scoped>
.managerDetailPage {
	width: 98%;
	padding: 10px;
}

.row {
	margin: -10px;
	display: flex;
}

.left {
	width: 155px;
	flex: 0 0 auto;
	margin-right: 10px;
}

.right {
	position: relative;
	flex: 1 1 100px;
}
</style>
<style lang="scss">
.comment-section {
	padding: 5px 15px 0 15px;
}

.comment {
	background: linear-gradient(90deg, #3b64f2, #1b8eff);
	color: white;
	font-size: 12px;
	padding: 12px 24px;
}

.comment.center {
	text-align: center;
}

.section {
	padding: 15px 15px 0 15px;
}

.double-table {
	display: flex;
	flex-basis: 10px;
	justify-content: space-between;

	.single-table {
		flex: 1;
	}

	.cell {
		font-size: 14px !important;
		font-weight: 400 !important;
		text-align: center !important;
		padding: 0 !important;
	}

	th {
		padding: 5px 0 !important;
	}
}

.split-cell {
	display: flex;
	align-items: center;
	justify-content: center;

	div {
		width: 40px;
	}
}
</style>
<style lang="scss" scoped>
.title {
	background: #f3f4f8;
	font-weight: 600;
	padding: 5px 15px;
}
.sub-title {
	font-size: 14px;
	font-weight: 600;
	border-left: 2px solid dodgerblue;
	margin-bottom: 6px;
	padding-left: 3px;
	line-height: 22px;
	height: 22px;
	flex: 1 1 auto;
}
.title-change-fund {
	display: flex;
	align-items: center;
	margin-bottom: 4px;
	label {
		font-size: 14px;
		margin-right: 10px;
	}
}
.backbut {
	position: absolute;
	right: 5px;
	bottom: 10px;
	top: 10px;
	margin: auto;
}
</style>
