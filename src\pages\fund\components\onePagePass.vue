<!-- 一页通 -->
<template>
	<div>
		<div class="flex_card">
			<div v-for="item in templateList" :key="item.value" v-show="item.isshow" :class="item.type">
				<component :is="item.is" :showType="item.type" :ref="item.value" @resolveFather="item.methods" v-loading="loading"></component>
			</div>
		</div>
	</div>
</template>

<script>
// 基金经理管理产品
import managedFundAll from '@/components/components/components/managedFundAll/index.vue';
import managementProducts from '@/components/components/components/managementProducts/index.vue';
import managementProductsDetail from '@/components/components/components/managementProductsDetail/index.vue';

// 业绩评价
import performanceCapabilityEvaluation from '@/components/components/components/performanceCapabilityEvaluation/index.vue';
// 业绩表现
import performanceYear from '@/components/components/components/performanceYear/index.vue';
// 表现风格
import performanceStyle from '@/components/components/components/performanceStyle/index.vue';
// 操盘风格
import holdingStyle from '@/components/components/components/holdingStyle/index.vue';
// 近期公告风格
import recentAnnouncementStyle from '@/components/components/components/recentAnnouncementStyle/index.vue';
// 因子暴露与收益
import barraReturnLine from '@/components/components/components/barraReturnLine/index.vue';
// 行业能力圈
import industryCapacity from '@/components/components/components/industryCapacity/index.vue';
// 市场适应性
import marketAdaptability from '@/components/components/components/marketAdaptability/index.vue';
// 行业高低配
import industryEvaluation from '@/components/components/components/industryEvaluation/index.vue';
// 规模及持有人结构
import sizeStructure from '@/components/components/fundComponents/sizeStructure/index.vue';

// 除固收+外其他债券基金经理评价
import evaluateManagers from '@/components/components/fundComponents/evaluateManagers/index.vue';
// 基金经理评价
import fundManagerEvaluation from '@/components/components/fundComponents/fundManagerEvaluation/index.vue';
// 年度风格
import debtBasedStyle from '@/components/components/fundComponents/debtBasedStyle/index.vue';
// 近期风格
import recentStyle from '@/components/components/fundComponents/recentStyle/index.vue';
// 固收+加法描述
import additiveDescription from '@/components/components/components/additiveDescription/index.vue';
// 市场风格表现
import stockDebtSynthesis from '@/components/components/components/stockDebtSynthesis/index.vue';

export default {
	components: {
		managementProducts,
		managementProductsDetail,
		managedFundAll,
		performanceCapabilityEvaluation,
		performanceYear,
		holdingStyle,
		performanceStyle,
		barraReturnLine,
		industryCapacity,
		marketAdaptability,
		recentAnnouncementStyle,
		industryEvaluation,
		sizeStructure,
		fundManagerEvaluation,
		debtBasedStyle,
		recentStyle,
		additiveDescription,
		stockDebtSynthesis,
		evaluateManagers
	},
	data() {
		return {
			info: {},
			templateList: [],
			requestOver: [],
			requestAll: null,
			showGD: false,
			loading: true,
			componentsName: 'onePagePass',
			manager_components: [
				{
					name: '基金经理管理产品概况',
					is: 'managedFundAll',
					value: 'managedFundAll',
					type: 'big_template',
					typelist: ['*'],
					isshow: true
				},
				{
					name: '基金经理管理产品列表',
					is: 'managementProducts',
					value: 'managementProducts',
					type: 'big_template',
					typelist: ['*'],
					isshow: true
				},
				{
					name: '基金经理管理产品业绩表现',
					is: 'managementProductsDetail',
					value: 'managementProductsDetail',
					type: 'big_template',
					typelist: ['*'],
					isshow: true
				}
			]
		};
	},
	props: {
		showEditor: {
			type: Boolean,
			default: false
		}
	},
	methods: {
		// 获取数据
		getData(data) {
			this.info = data;
			this.loading = true;
			this.formatTemplatList();
		},
		// 获取打印数据
		async createPrintWord(info) {
			this.info = info;
			let printData = [];
			this.templateList.map((item) => {
				if (item.isshow) {
					if (this.$refs[item.value]?.[0].createPrintWord) {
						let list = this.$refs[item.value]?.[0].createPrintWord(this.info);
						printData.push(list);
					}
				}
			});
			let data = await Promise.all(printData);
			return data;
		},

		// 格式化模板列表
		formatTemplatList() {
			if (this.info.flag == 2) {
				this.templateList.unshift(...this.manager_components);
			}
			this.$nextTick(() => {
				this.templateList.map((item) => {
					if (item.typelist.indexOf(this.info.type) !== -1 || item.typelist.includes('*')) {
						this.$refs[item.value]?.[0]?.getData(this.info);
						this.loading = false;
					}
				});
			});
		},
		// 接收/返回组件列表
		getTemplateList(list) {
			if (list) {
				// 是光大
				if (this.isGDBank()) {
					this.templateList = list.filter((item) => {
						return item.isshow;
					});
				} else {
					// 不是光大
					this.templateList = list.filter((item) => {
						return item.is !== 'GDBankDetailequity' && item.is !== 'GDBankDetailbond' && item.isshow;
					});
				}
				this.$forceUpdate();
			} else {
				return this.templateList;
			}
		},
		// 判断是否是光大
		isGDBank() {
			if (window.localStorage.getItem('mty_modulesName') == 'GDBank') {
				this.showGD = true;
				return true;
			} else {
				this.showGD = false;
				return false;
			}
		}
	}
};
</script>

<style></style>
