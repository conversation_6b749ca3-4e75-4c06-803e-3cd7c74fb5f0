<template>
	<div>
		<analysis-card-title title="基金报告期披露信用债评级分布(近年)" @downloadWord="exportExcel">
			<div>
				季度选择:
				<el-select v-model="quarter" style="margin-left: 16px" @change="changeQuarter">
					<el-option v-for="item in quarterList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>
			</div>
		</analysis-card-title>
		<el-table
			v-loading="loading"
			height="400px"
			style="width: 99%"
			:data="level"
			class="table"
			row-key="id"
			ref="multipleTable"
			header-cell-class-name="table-header"
			:tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
		>
			>
			<el-table-column sortable prop="yearqtr" label="报告期" align="gotoleft"> </el-table-column>
			<el-table-column sortable prop="shortorlong" label="shortorlong" align="gotoleft"> </el-table-column>
			<el-table-column sortable prop="bondCredit" align="gotoleft" label="债券评级"> </el-table-column>
			<el-table-column sortable prop="value" align="gotoleft" label=" 市值(亿)">
				<template slot-scope="scope">{{ scope.row.value | fix3 }}</template>
			</el-table-column>
			<el-table-column sortable prop="ratioinN" align="gotoleft" label=" 占净值比">
				<template slot-scope="scope">{{ scope.row.ratioinN | fix2p }}</template>
			</el-table-column>
		</el-table>
	</div>
</template>

<script>
import { exportTitle, exportTable, downloadWord } from '@/utils/exportWord.js';
import { filter_json_to_excel } from '@/utils/exportExcel.js';
// 基金报告期披露信用债评级分布(近年)
import { getCreditLevel } from '@/api/pages/Analysis.js';
export default {
	name: 'creditBondRatingDistribution',
	data() {
		return {
			level: [],
			loading: true,
			quarterList: [],
			quarter: [],
			allData: [],
			info: {}
		};
	},
	filters: {
		fix3(value) {
			if (value) {
				return (parseInt(value) / 100000000).toFixed(4);
			} else {
				return '数据缺失';
			}
		},
		fix2p(value) {
			if (value) {
				return (value * 100).toFixed(2) + '%';
			} else {
				return '数据缺失';
			}
		}
	},
	methods: {
		// 获取基金报告期披露信用债评级分布(近年)数据
		async getCreditLevel() {
			let data = await getCreditLevel({
				flag: this.info.flag,
				code: this.info.code,
				type: this.info.type,
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			if (data?.mtycode == 200) {
				this.getChartData(data?.data);
			}
		},
		getData(info) {
			this.info = info;
			this.getCreditLevel();
		},
		getChartData(data) {
			this.loading = false;
			if (data?.length) {
				this.allData = data.map((item, index) => {
					return { ...item, id: index };
				});
				this.quarterList = [];
				data.map((item) => {
					let index = this.quarterList.findIndex((obj) => {
						return obj.value == item.yearqtr;
					});
					if (index == -1) {
						this.quarterList.push({
							label: item.yearqtr,
							value: item.yearqtr
						});
					}
				});
				this.quarterList.sort((a, b) => {
					return this.moment(this.moment(a.value, 'YYYY QQ').format()).isAfter(this.moment(b.value, 'YYYY QQ').format()) ? -1 : 1;
				});
				this.quarter = this.quarterList?.[0]?.value;
				this.changeQuarter();
			} else {
				this.level = [];
			}
		},
		changeQuarter() {
			this.level = [];
			let data = this.allData.filter((item) => {
				return item.yearqtr == this.quarter;
			});
			let treeData = [];
			data.map((item) => {
				if (item.bondCredit == '合计') {
					let index = treeData.findIndex((obj) => {
						return obj.bonCredit == item.bondCredit && obj.shortorlong == item.shortorlong;
					});
					if (index == -1) {
						treeData.push({
							...item,
							children: []
						});
					}
				}
			});
			data.map((item) => {
				let index = treeData.findIndex((obj) => {
					return obj.shortorlong == item.shortorlong && item.bondCredit != '合计';
				});
				if (index != -1) {
					treeData[index]['children'].push(item);
				}
			});
			this.level = treeData;
		},
		exportExcel() {
			let list = [
				{
					label: '报告期',
					value: 'yearqtr',
					fill: 'header'
				},
				{
					label: 'shortorlong',
					value: 'shortorlong'
				},
				{
					label: '债券评级',
					value: 'bondCredit'
				},
				{
					label: '市值(亿)',
					value: 'value',
					format: 'fix3'
				},
				{
					label: '占净值比',
					value: 'ratioinN',
					format: 'fix2p'
				}
			];
			filter_json_to_excel(list, this.level, '基金报告期披露信用债评级分布(近年)');
		},
		createPrintWord() {
			let list = [
				{
					label: '报告期',
					value: 'yearqtr',
					fill: 'header'
				},
				{
					label: 'shortorlong',
					value: 'shortorlong'
				},
				{
					label: '债券评级',
					value: 'bondCredit'
				},
				{
					label: '市值(亿)',
					value: 'value',
					format: 'fix3'
				},
				{
					label: '占净值比',
					value: 'ratioinN',
					format: 'fix2p'
				}
			];
			if (this.level.length) {
				return [
					...exportTitle('基金报告期披露信用债评级分布(近年)'),
					...exportTable(
						list,
						this.level
							.sort((a, b) => {
								return b.ratioinN - a.ratioinN;
							})
							.slice(0, 10)
					)
				];
			} else {
				return [];
			}
		}
	}
};
</script>

<style></style>
