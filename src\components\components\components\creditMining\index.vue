<template>
	<div id="creditMining" v-show="show">
		<analysis-card-title title="信用挖掘" image_id="creditMining"></analysis-card-title>
		<div class="charts_fill_class" v-loading="loading">
			<v-chart
				ref="creditMining"
				v-loading="loading"
				element-loading-text="暂无数据"
				element-loading-spinner="el-icon-document-delete"
				element-loading-background="rgba(239, 239, 239, 0.5)"
				class="charts_one_class"
				autoresize
				:options="credchangingOption"
			/>
		</div>
	</div>
</template>

<script>
import { exportTitle, exportChart } from '@/utils/exportWord.js';
import { lineChartOption } from '@/utils/chartStyle.js';
// 信用挖掘
import { getCreditDownRation } from '@/api/pages/Analysis.js';
export default {
	name: 'creditMining',
	data() {
		return {
			loading: true,
			show: true,
			credchangingOption: {},
			info: {}
		};
	},
	methods: {
		// 获取信用挖掘数据
		async getCreditDownRation() {
			let data = await getCreditDownRation({
				flag: this.info.flag,
				code: this.info.code,
				type: this.info.type,
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			if (data?.mtycode == 200) {
				this.getChartData(data?.data);
			} else {
				this.hideLoading();
			}
		},
		getData(info) {
			this.info = info;
			this.getCreditDownRation();
		},
		// 获取数据
		getChartData(data) {
			this.loading = false;
			this.show = true;
			let result = data.sort((a, b) => {
				return this.moment(this.moment(a.yearqtr, 'YYYY QQ').format()).isBefore(this.moment(b.yearqtr, 'YYYY QQ').format()) ? -1 : 1;
			});
			this.credchangingOption = lineChartOption({
				toolbox: 'none',
				legend: ['下沉占信用债比例', '下沉占净值比'],
				tooltip: { type: 'shadow' },
				xAxis: [{ data: result.map((v) => v.yearqtr) }],
				yAxis: [{ type: 'value' }],
				series: [
					{
						name: '下沉占信用债比例',
						type: 'line',
						symbol: 'none',
						data: result.map((item) => {
							return item.downratioinC * 1 || item.downratioinC == 0 ? (item.downratioinC * 100).toFixed(2) : 0;
						})
					},
					{
						name: '下沉占净值比',
						type: 'line',
						symbol: 'none',
						data: result.map((item) => {
							return item.downratioinN * 1 || item.downratioinN == 0 ? (item.downratioinN * 100).toFixed(2) : 0;
						})
					}
				]
			});
		},
		// 无数据隐藏
		hideLoading() {
			this.show = false;
		},
		createPrintWord() {
			this.$refs['creditMining'].mergeOptions({ toolbox: { show: false } });
			let height = this.$refs['creditMining'].$el.clientHeight;
			let width = this.$refs['creditMining'].$el.clientWidth;
			let chart = this.$refs['creditMining'].getDataURL({
				type: 'png',
				pixelRatio: 2,
				backgroundColor: '#fff'
			});
			this.$refs['creditMining'].mergeOptions({ toolbox: { show: true } });
			return [...exportTitle('信用挖掘'), ...exportChart(chart, { width, height })];
		}
	}
};
</script>

<style></style>
