import request from '@/utils/request';
import axios from "axios";

/**
 * 获取预警记录列表
 * @param params
 * @returns {*}
 */
export function getList(params) {
    return request({
        url: '/api/taikang/alarm/history/list',
        method: 'get',
        params
    });
}
/**
 * 阅读预警记录
 * @param params
 * @returns {*}
 */
export function readList(data) {
  return request({
      url: '/api/taikang/alarm/history/readList',
      method: 'post',
      data
  });
}

/**
 *获取预警设置规则
 * @param params
 * @returns {*}
 */
export function getRuleList(params) {
    return request({
        url: '/api/taikang/alarm/rule/list',
        method: 'get',
        params
    });
}

/**
 * 启/停规则
 * @param params
 * @returns {*}
 */
export function active(params) {
    return request({
        url: '/api/taikang/alarm/rule/active',
        method: 'post',
        params
    });
}

/**
 * 删除接口
 * @param id
 * @returns {*}
 */
export function deleteRule(id) {
    return request({
        url: `/api/taikang/alarm/rule/del?id=${id}`,
        method: 'post',
    });
}

/**
 * 保存规则
 * @param params
 * @returns {*}
 */
export function saveRule(data) {
    return request({
        url: '/api/taikang/alarm/rule/save',
        method: 'post',
        data
    });
}
