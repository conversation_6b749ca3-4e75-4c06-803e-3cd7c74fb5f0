<template>
  <div class="box_Board">
    <div class="header_box">
      <span class="header_inactive">投后&nbsp;/&nbsp;映射管理&nbsp;/&nbsp;</span>
      TL4映射管理
    </div>
    <div class="border_table">
      <!-- 头部区域 -->
      <div class="border_table_header">
        <!-- 左侧标题区域 -->
        <div class="border_table_header_title">TL4映射管理列表</div>
        <!-- 右侧按钮区域 -->
        <div class="border_table_header_button">
          <!-- 提示是否删除 -->
          <div v-show="promptMessage"
               class="prompt-message">
            <div class="block" />
            <div><i class="el-icon-warning"></i>确认删除内容吗</div>
            <el-button class="cancel"
                       @click="closePromptMessage">取消</el-button>
            <el-button class="warning"
                       @click="closePromptMessage">确认</el-button>
          </div>

          <el-button icon="el-icon-plus"
                     type="primary"
                     @click="addRow">新增</el-button>
          <el-button class="ml"
                     type="primary"
                     @click="showDialog = true">上传映射表</el-button>
          <!-- <el-button class="button-del ml"
                     @click="promptMessage = !promptMessage">删除原有映射</el-button> -->
          <!-- <el-button class
                     @click="showDialogRecord = true">修改记录</el-button> -->
        </div>
      </div>
      <!-- 查询区域 -->
      <el-form class="search-box search-area">
        <el-form-item class="search-box_item">
          证券TL4名称:
          <el-select v-model="formData.type"
                     filterable
                     clearable
                     default-first-option
                     placeholder="请选择"
                     style="width: 240px">
            <el-option v-for="item in  ['一般规则','特殊规则']"
                       :key="item"
                       :label="item"
                       :value="item"
                       style="width: 240px"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="search-box_item">
          证券TL4名称:
          <el-select v-model="formData.tl4Type"
                     filterable
                     clearable
                     default-first-option
                     placeholder="请选择"
                     style="width: 240px">
            <el-option v-for="item in tl4TypeList"
                       :key="item"
                       :label="item"
                       :value="item"
                       style="width: 240px"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="search-box_item">
          交易系统基金名称：
          <el-select v-model="formData.systemName"
                     filterable
                     clearable
                     default-first-option
                     placeholder="请选择"
                     style="width: 240px">
            <el-option v-for="item in systemNameList"
                       :key="item"
                       :label="item"
                       :value="item"
                       style="width: 240px"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="search-box_item">
          资产名称：
          <el-select v-model="formData.assetName"
                     filterable
                     clearable
                     default-first-option
                     placeholder="请选择"
                     style="width: 240px">
            <el-option v-for="item in assetNameList"
                       :key="item"
                       :label="item"
                       :value="item"
                       style="width: 240px"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="search-box_item">
          交易场所：
          <el-select v-model="formData.tradingVenue"
                     filterable
                     clearable
                     default-first-option
                     placeholder="请选择"
                     style="width: 240px">
            <el-option v-for="item in tradingVenueList"
                       :key="item"
                       :label="item"
                       :value="item"
                       style="width: 240px"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="search-box_item">
          虚拟组合层代码：
          <el-select v-model="formData.rule"
                     filterable
                     clearable
                     default-first-option
                     placeholder="请选择"
                     style="width: 240px">
            <el-option v-for="item in ruleList"
                       :key="item"
                       :label="item"
                       :value="item"
                       style="width: 240px"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="search-box_item"></el-form-item>
        <el-form-item>
          <el-button type="primary"
                     @click="getListTL4Table(formData)">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
      <div style="margin-bottom: 16px;">
        <span>手动刷新数据：</span>
        <el-date-picker v-model="valueDate"
                        type="daterange"
                        align="right"
                        unlink-panels
                        value-format="yyyy-MM-dd"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        :picker-options="pickerOptions">
        </el-date-picker>
        <el-button icon="el-icon-refresh"
                   type="primary"
                   style="margin-left:16px;"
                   @click="refreshData()">刷数</el-button>
        <span v-show="showDateTime"
              style="margin-left: 16px;">{{ dateTime }}</span>
      </div>
      <!-- 表格区域 -->
      <el-table :data="tableData"
                border
                v-loading="loading.tableLoading"
                stripe>
        <el-table-column align="gotoleft"
                         label="证券TL4类别"
                         prop="code">
          <template slot-scope="scope">
            <div v-if="scope.row.addFlag">
              <el-select v-model="scope.row.type"
                         filterable
                         allow-create
                         clearable
                         placeholder="请选择 ">
                <el-option v-for="item in TL4Cate"
                           :key="item.key"
                           :label="item.value"
                           :value="item.key"></el-option>
              </el-select>
            </div>
            <div v-else
                 :style="scope.row.type ? '' : 'color:red;'">
              {{ scope.row.type ? scope.row.type=='1'?'一般规则': '特殊规则' : '' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column align="gotoleft"
                         label="证券TL4名称"
                         prop="name">
          <template slot-scope="scope">
            <div v-if="scope.row.addFlag">
              <el-input v-model="scope.row.tl4Type"
                        placeholder="请输入"
                        clearable></el-input>
            </div>
            <div v-else
                 :style="scope.row.tl4Type ? '' : 'color:red;'">{{ scope.row.tl4Type ? scope.row.tl4Type : '' }}</div>
          </template>
        </el-table-column>
        <el-table-column align="gotoleft"
                         label="交易系统基金名称"
                         prop="name">
          <template slot-scope="scope">
            <div v-if="scope.row.addFlag">
              <el-input v-model="scope.row.systemName"
                        placeholder="请输入"
                        clearable></el-input>
            </div>
            <div v-else
                 :style="scope.row.systemName ? '' : 'color:red;'">{{ scope.row.systemName ? scope.row.systemName : '' }}</div>
          </template>
        </el-table-column>
        <el-table-column align="gotoleft"
                         label="资产名称"
                         prop="industrialOwnership">
          <template slot-scope="scope">
            <div v-if="scope.row.addFlag">
              <el-input v-model="scope.row.assetsName"
                        placeholder="请输入"
                        clearable></el-input>
            </div>
            <div v-else
                 :style="scope.row.assetsName ? '' : 'color:red;'">{{ scope.row.assetsName ? scope.row.assetsName : '' }}</div>
          </template>
        </el-table-column>
        <el-table-column align="gotoleft"
                         label="交易场所"
                         prop="centerIndustry">
          <template slot-scope="scope">
            <div v-if="scope.row.addFlag">
              <el-input v-model="scope.row.tradingVenue"
                        placeholder="请输入"
                        clearable></el-input>
            </div>
            <div v-else
                 :style="scope.row.tradingVenue ? '' : 'color:red;'">{{ scope.row.tradingVenue ? scope.row.tradingVenue : '' }}</div>
          </template>
        </el-table-column>
        <el-table-column align="gotoleft"
                         label="虚拟组合层代码"
                         prop="centerIndustry">
          <template slot-scope="scope">
            <div v-if="scope.row.addFlag">
              <el-input v-model="scope.row.rule"
                        placeholder="请输入"
                        clearable></el-input>

              <!-- <el-input style="width: 70px;margin-left:6px"
                        v-model="scope.row.right"
                        placeholder="请输入"
                        clearable></el-input> -->
            </div>
            <div v-else
                 :style="scope.row.rule ? '' : 'color:red;'">{{ scope.row.rule ? scope.row.rule : '' }}</div>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <div v-if="!scope.row.addFlag"
                 class="flex">
              <el-button class="button-color"
                         type="text"
                         @click="edit(scope.row, scope.$index)">编辑</el-button>
              <el-button class="button-color"
                         type="text"
                         @click="delListTL4Table(scope.row.id)">删除</el-button>
            </div>
            <div v-else
                 class="flex">
              <el-button type="text"
                         @click="saveListTL4Table(scope.row)">保存</el-button>
              <el-button type="text"
                         @click="cancel(scope.row, scope.$index)">取消</el-button>
            </div>
          </template>
        </el-table-column>
        <template slot="empty">
          <el-empty :image-size="160" />
        </template>
      </el-table>
      <!-- 分页器 -->
      <div class="pagination_board">
        <el-pagination :current-page.sync="pagination.pageIndex"
                       :page-size="pagination.pageSize"
                       :total="pagination.total"
                       background
                       layout="total, sizes, prev, pager, next"
                       @size-change="sizeChange"
                       @current-change="currentChange" />
        <div>
          <el-input type="textarea"
                    style="padding-top: 16px;"
                    :autosize="{ minRows: 2, maxRows: 4}"
                    placeholder="请输入批注"
                    @change="textareaChange"
                    v-model="textarea2">
          </el-input>

        </div>
      </div>
    </div>

    <!-- 上传映射表弹框 -->
    <el-dialog :visible.sync="showDialog"
               title="上传映射表"
               width="900px">
      <ExcelPort :excelUrl="`tl4.xlsx`"
                 :path="`/api/taikang/tl4/upload`"
                 @refrshtable="uploadSuccess"
                 :data="{ type: '' }" />
    </el-dialog>
    <!-- 修改记录弹框 -->
    <el-dialog :visible.sync="showDialogRecord"
               title="修改记录"
               width="1200px">
      <!-- 表格 -->
      <el-table :data="tableDataRecord"
                v-loading="loading.dialogLoading"
                height="400">
        <el-table-column align="gotoleft"
                         label="股票代码"
                         prop="context.code" />
        <el-table-column align="gotoleft"
                         label="修改人 ID"
                         prop="context.userId" />
        <el-table-column align="gotoleft"
                         label="产业归属"
                         prop="context.industrialOwnership" />
        <el-table-column align="gotoleft"
                         label="中信行业"
                         prop="context.centerIndustry" />
        <el-table-column align="gotoleft"
                         label="操作类别"
                         prop="actionType">
          <template slot-scope="scope">
            {{ scope.row.actionType === 1 ? '新增' : scope.row.actionType === 2 ? '删除' : '修改' }}
          </template>
        </el-table-column>
        <el-table-column align="gotoleft"
                         label="修改时间"
                         prop="insertDate" />
        <template slot="empty">
          <el-empty :image-size="160" />
        </template>
      </el-table>
      <!-- 分页器 -->
      <div class="pagination_board">
        <el-pagination :current-page.sync="paginationRecord.pageIndex"
                       :page-size="paginationRecord.pageSize"
                       :total="paginationRecord.total"
                       background
                       layout="total, sizes, prev, pager, next"
                       @size-change="sizeChangeRecord"
                       @current-change="currentChangeRecord" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ExcelPort from './component/map/alphaownpool.vue';
import {
  // getIndustryList,
  // getCenterIndustry,
  // getOwnership,
  // saveIndustry,
  // deleteIndustry,
  // deleteAll
  UPDescription,
  GetDescription,
  getListTL4,
  refreshData,
  getListTL4Table,
  delListTL4Table,
  saveListTL4Table,
  refreshDateTime
} from '../../../api/pages/tkdesign/industryController';
import { getRecordList } from '../../../api/pages/tkdesign/historyRecord';

export default {
  components: {
    ExcelPort
  },
  data () {
    return {
      textarea2: '',
      dateTime: '',
      showDateTime: false,
      tl4TypeList: [],
      systemNameList: [],
      assetNameList: [],
      tradingVenueList: [],
      ruleList: [],
      TL4Cate: [{ key: 1, value: '一般规则' }, { key: 2, value: '特殊调整' }],
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick (picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick (picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick (picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      // 删除原有映射按钮提示显示与隐藏
      promptMessage: false,

      tableData: [], // 页面表格数据源
      tableDataRecord: [], // 修改记录表格数据源

      pagination: {
        pageIndex: 1, // 当前页码
        pageSize: 10, // 页面显示几条数据
        total: 0
      },

      paginationRecord: {
        pageIndex: 1, // 当前页码
        pageSize: 10, // 页面显示几条数据
        total: 0
      },

      showDialog: false, // 绑定上传黑名单的dialog
      showDialogRecord: false, // 绑定修改记录的dialog

      // 搜索框对应的数据
      formData: {
        rule: '',
        tradingVenue: '',
        type: '',
        assetName: '',
        systemName: '',
        tl4Type: ''
      },

      loading: {
        tableLoading: false,
        dialogLoading: false
      },
      // 模糊查询的数据
      centerIndustryOptions: [],
      ownershipOptions: [],
      codeOptions: [],
      oldValue: {},
      valueDate: ''
    };
  },
  methods: {
    // 
    textareaChange () {

      let params = {
        description: this.textarea2,
      };
      UPDescription(params).then((res) => {
        if (res.code === 200) {

          // let array = [];
          // res.data.forEach((item) => {
          //   if (item.code) {
          //     array.push(item.code);
          //   }
          // });
          // this.codeOptions = Array.from(new Set(array));
        } else {
        }
      });
    },
    getdes () {
      GetDescription().then((res) => {
        if (res.code === 200) {
          this.textarea2 = res.data
          // let array = [];
          // res.data.forEach((item) => {
          //   if (item.code) {
          //     array.push(item.code);
          //   }
          // });
          // this.codeOptions = Array.from(new Set(array));
        } else {
          this.textarea2 = ''

        }
      });
    },
    refreshData () {
      if (this.valueDate != '') {
        refreshData({ startDate: this.valueDate[0], endDate: this.valueDate[1] }).then((res) => {
          this.loading.tableLoading = false;
          if (res.code === 200) {
            this.$message.success('刷新数据中请耐心等待')
            this.refreshDateTime()
          } else { this.$message.error('刷新失败') }
        });
      }
      else {
        this.$message.warning('请选择刷新日期')
      }
    },
    refreshDateTime () {
      setTimeout(() => {
        refreshDateTime().then((res) => {
          if (res.code === 200) {
            this.dateTime = res.data.lastDate;
            this.showDateTime = true; // 显示刷新时间
          } else { this.showDateTime = false }
        })
      }, 3600000)
    },
    // 上传成功
    uploadSuccess () {
      this.showDialog = false;
      this.getListTL4Table();
    },
    // 关闭删除提示
    closePromptMessage () {
      this.deleteAll();
      this.promptMessage = false;
    },
    // 每页条数改变时触发的回调
    sizeChange (value) {
      this.pagination.pageSize = value;
      this.getListTL4Table(this.formData);
    },
    sizeChangeRecord (value) {
      this.paginationRecord.pageSize = value;
      this.getRecordList();
    },

    // 当前页数改变时触发的回调
    currentChange (value) {
      this.pagination.pageIndex = value;
      this.getListTL4Table(this.formData);
    },
    currentChangeRecord (value) {
      this.paginationRecord.pageIndex = value;
      this.getRecordList();
    },
    /**
     * 处在编辑状态时，不允许对其他数据进行操作
     */
    ban () {
      let result = this.tableData.filter((v) => v.addFlag);
      if (result.length > 0) return true;
      else return false;
    },
    /**
     * 新增
     */
    addRow () {
      //判断是否处于编辑状态 如果正在编辑则不允许点击新增按钮
      if (this.ban()) return;

      const row = {
        addFlag: true,
        actionType: 1
      };
      this.tableData.unshift(row);
    },

    /**
     * 编辑
     * @param obj 该行的数据
     * @param number 该行的下标
     */
    edit (obj, number) {
      //判断是否处于编辑状态 如果正在编辑则不允许点击其他编辑按钮
      let result = this.tableData.filter((v) => v.addFlag);
      if (result.length > 0) return;
      //保存传过来的的对象，留着在用户点击取消的时候还原数据
      this.oldValue = JSON.parse(JSON.stringify(obj));
      //这里addFlag为true但是页面没有实现响应式
      obj.addFlag = true;
      //这里是为了解决addFlag不能实现响应式的问题 （数组重写）
      //将tableData里对应的该行数据删除，然后再把addFlag为true的obj添加到删除的位置
      this.tableData.splice(number, 1);
      this.tableData.splice(number, 0, obj);
    },

    /**
     * 取消
     * @param obj
     * @param number
     */
    cancel (obj, number) {
      if (obj.actionType === 1) {
        this.tableData.shift();
      } else {
        this.tableData.splice(number, 1);
        this.tableData.splice(number, 0, this.oldValue);
      }
    },
    /**
     * 重置
     */
    reset () {
      this.formData = {
        rule: '',
        type: '',
        tradingVenue: '',
        assetName: '',
        systemName: '',
        tl4Type: ''
      };
      this.getListTL4Table();
    },
    /**
     * 获取tl4映射列表
     */
    getListTL4Table (data) {
      this.loading.tableLoading = true;
      let params = {
        ...data,
        type: (data?.type == '一般规则' ? 1 : data?.type == '特殊规则' ? 2 : '') || '',
        calCode: data?.rule || '',
        allocation: data?.tradingVenue || '',
        assetName: data?.assetName || '',
        tradeFundName: data?.systemName || '',
        TL4cate: data?.tl4Type || '',
        current: this.pagination.pageIndex,
        pageSize: this.pagination.pageSize
      };
      getListTL4Table(params).then((res) => {
        this.loading.tableLoading = false;
        if (res.code === 200) {
          this.tableData = res.data;
          this.pagination.total = res.total;
          this.tableData.forEach((item) => {
            item.addFlag = false;
          });
          this.pagination.total = res.total;
        } else {
          this.tableData = [];
          this.pagination.total = 0;
        }
      });
    },
    /**
     * 获取搜索选择项
     */
    getListTL4 () {
      let params = {
        current: 1,
        pageSize: 100000
      };
      getListTL4(params).then((res) => {
        if (res.code === 200) {
          this.tl4TypeList = res.data.tl4TypeList
          this.systemNameList = res.data.systemNameList
          this.assetNameList = res.data.assetNameList
          this.tradingVenueList = res.data.tradingVenueList
          this.ruleList = res.data.ruleList
          // let array = [];
          // res.data.forEach((item) => {
          //   if (item.code) {
          //     array.push(item.code);
          //   }
          // });
          // this.codeOptions = Array.from(new Set(array));
        } else {
          this.tl4TypeList = [];
        }
      });
    },
    /**
     * 获取中信行业
     */
    getCenterIndustry () {
      getCenterIndustry().then((res) => {
        if (res.code === 200) this.centerIndustryOptions = res.data;
        else this.centerIndustryOptions = [];
      });
    },
    /**
     * 获取行业归属
     */
    getOwnership () {
      getOwnership().then((res) => {
        if (res.code === 200) this.ownershipOptions = res.data;
        else this.ownershipOptions = [];
      });
    },
    /**
     * 保存上传
     * @param params
     */
    saveListTL4Table (params) {
      const data = {
        ...params,
        rule: params?.rule || '',
        tradingVenue: params?.tradingVenue || '',
        assetName: params?.assetName || '',
        systemName: params?.systemName || '',
        tl4Type: params?.tl4Type || '',
        type: params?.type || '',
      };
      saveListTL4Table(data).then((res) => {
        if (res.code === 200) {
          this.$message.success('上传成功');
          this.getListTL4Table();
          this.getListTL4();
          this.getCenterIndustry();
          this.getOwnership();
          params.addFlag = false;
        } else {
          this.$message.error('上传失败');
        }
      });
    },
    /**
     * 删除
     * @param id
     */
    delListTL4Table (id) {
      if (this.ban()) return;

      this.$confirm('确定删除么?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          delListTL4Table({ id }).then((res) => {
            if (res.code === 200) {
              this.$message({
                type: 'success',
                message: '删除成功!'
              });
              this.getRecordList();
              this.getListTL4Table();
              this.getListTL4();
              this.getCenterIndustry();
              this.getOwnership();
            } else {
              this.$message.error('删除失败');
            }
          });
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
    },
    /**
     *删除全部数据
     */
    deleteAll () {
      this.$confirm('确定删除么?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteAll().then((res) => {
            if (res.code === 200) {
              this.$message({
                type: 'success',
                message: '删除成功!'
              });
              this.getRecordList();
              this.getListTL4Table();
              this.getListTL4();
              this.getCenterIndustry();
              this.getOwnership();
            } else {
              this.$message.error('删除失败');
              return;
            }
          });
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
    },
    /**
     * 获取历史修改记录
     */
    getRecordList () {
      this.loading.dialogLoading = true;
      const params = {
        current: this.paginationRecord.pageIndex,
        pageSize: this.paginationRecord.pageSize,
        type: 1
      };
      getRecordList(params).then((res) => {
        this.loading.dialogLoading = false;
        if (res.code === 200) {
          this.tableDataRecord = res.data;
          this.tableDataRecord.forEach((item) => {
            item.context = JSON.parse(item.context.value);
          });
          this.paginationRecord.total = res.total;
        } else {
          this.tableDataRecord = [];
          this.paginationRecord.total = 0;
        }
      });
    }
  },
  mounted () {
    this.getListTL4()
    this.getListTL4Table()
    this.getdes()
    // this.getIndustryList();
    // this.getOwnership();
    // this.getCenterIndustry();
    // this.getRecordList();
    // this.getCodeOptions();
  }
};
</script>
<style lang="scss" scoped>
@import '../tkdesign';

.ml {
	margin-left: 16px;
}

.border_table {
	padding: 16px 24px;
	background: white;

	.border_table_header_button {
		position: relative;

		.prompt-message {
			z-index: 99;
			position: absolute;
			bottom: -98px;
			right: 82px;
			padding: 16px;
			border-radius: 4px;
			box-shadow: 0 0 2px #ccc;
			background-color: #fff;

			.el-button {
				padding: 5px;
				margin: 20px 0 0;
			}

			.cancel {
				margin-left: 20px;
			}

			.warning {
				border-color: #4096ff;
				background: #4096ff;
				color: white;
				margin-left: 5px;
			}

			.block {
				position: absolute;
				width: 10px;
				height: 10px;
				box-shadow: 0.5px 0.5px 1px #ccc;
				background-color: #fff;
				top: 0;
				left: 50%;
				transform: translate(-50%, -50%) rotate(225deg);
			}
		}
	}

	.search-box {
		background-color: #fafafa;
		padding: 16px 24px 0;
		margin-bottom: 16px;
		display: flex;
		justify-content: space-between;
		flex-wrap: wrap;

		.search-box_item {
			width: 33%;

			.block {
				position: absolute;
				width: 10px;
				height: 10px;
				box-shadow: 0.5px 0.5px 1px #ccc;
				background-color: #fff;
				top: 0;
				left: 50%;
				transform: translate(-50%, -50%) rotate(225deg);
			}
		}
	}

	.search-box {
		background-color: #fafafa;
		padding: 16px 24px 0;
		margin-bottom: 16px;
		display: flex;
		justify-content: space-between;
		flex-wrap: wrap;

		.search-box_item {
			width: 33%;

			.el-input,
			.el-select {
				width: 60%;
			}

			::v-deep .el-form-item__content {
				margin-left: 0 !important;
			}
		}
	}
}
</style>

<style lang="scss">
.search-area {
	.el-form-item__content {
		margin-left: 0 !important;
	}
}
</style>
