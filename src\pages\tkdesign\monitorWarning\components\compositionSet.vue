<script>
import axios from 'axios';
import { getPoolList, getPoolInfo } from '@/api/pages/tools/pool.js';

export default {
  props: ['detail'],
  data () {
    return {
      loading: false,
      search: {
        fundName: '' //关联组合设置搜索框
      },
      showOptions: false,
      showPost: false,
      showBazaar: false,
      showFund: false,
      productTags: [],
      selectedTags: [],

      fundPoolArray: [],
      fundPoolOption: [],
      fundForm: {
        select: '',
        fundCode: ''
      },
      fund: [{ required: false, message: '请输入基金池', trigger: 'blur' }],
      postArray: [],
      postOption: [],
      postForm: {
        select: '',
        fundCode: '',
        flag: 1

      },
      post: [{ required: false, message: '请输入模板', trigger: 'blur' }],

      bazaarForm: {
        select: '',
        fundCode: '',
        flag: 2
      },
      bazaar: [{ required: false, message: '请输入基金名称/代码添加', trigger: 'blur' }]
    };
  },

  methods: {
    /**
     * 添加基金池
     * @param obj
     */
    handleFundSelect (obj) {
      console.log(obj);
      this.fundForm.select = obj.value;
      this.fundForm.fundCode = obj.fundCode;
    },
    /**
     * 添加投后
     * @param obj
     */
    handlePostSelect (obj) {
      console.log(obj);
      this.postForm.select = obj.value;
      this.postForm.fundCode = obj.fundCode;
      this.postForm.productId = 1;
    },
    /**
     * 添加全市场
     * @param obj
     */
    handleSelect (obj) {
      console.log(obj);
      this.bazaarForm.select = obj.value;
      this.bazaarForm.fundCode = obj.fundCode;
      this.bazaarForm.productId = 2;
    },
    // 改变tags标签状态
    changeTagType (tag) {
      console.log(tag);
      if (tag.type) tag.type = '';
      else tag.type = 'info';

      const i = this.productTags.indexOf(tag);
      this.productTags.splice(i, 1);
      this.productTags.splice(i, 0, tag);

      this.selectedTags = this.productTags.filter((v) => v.type === '');
    },

    // 标签删除
    handleClose (tag) {
      let i = null;
      let j = null;
      this.productTags.forEach((item, index) => {
        if (item.fundName === tag.fundName) {
          i = index;
        }
      });
      this.selectedTags.forEach((item, index) => {
        if (item.fundName === tag.fundName) {
          j = index;
        }
      });
      if (i !== null) {
        this.productTags.splice(i, 1);
      }
      if (j !== null) {
        this.selectedTags.splice(j, 1);
      }
    },

    //删除已选
    deleteSelected (tag) {
      const i = this.productTags.indexOf(tag);
      const j = this.selectedTags.indexOf(tag);

      this.selectedTags.splice(j, 1);
      this.productTags[i].type = 'info';
    },

    // 清除已选项
    clearSelected () {
      this.productTags.forEach((item) => {
        item.type = 'info';
      });
      this.selectedTags = this.productTags.filter((v) => v.type === '');
    },

    //关闭添加对象弹框
    closeProductDialog (i) {
      if (i === 1) {
        this.fundForm.select = '';
        return (this.showFund = false);
      }
      if (i === 2) {
        this.postForm.select = '';
        return (this.showPost = false);
      }
      if (i === 3) {
        this.bazaarForm.select = '';
        return (this.showBazaar = false);
      }
    },

    // 添加关联对象 product
    addProduct (data, i) {
      // console.log(data, i, 'sssssssss');
      if (i === 1) {
        this.querySearchFundPoolFund(data.fundCode);
      } else {
        const obj = {
          fundName: data.select,
          fundCode: data.fundCode,
          type: 'info',
          productId: data.productId
        };
        this.addProductDeduplication(obj);
      }
      this.closeProductDialog(i);
    },

    // 去重添加
    addProductDeduplication (item) {
      for (let index = 0; index < this.productTags.length; index++) {
        const element = this.productTags[index];
        if (element.fundCode === item.fundCode) {
          return;
        }
      }
      this.productTags.push(item);
      this.changeTagType(item)
      // console.log(item, 'bbbbbbbbbbbb');
    },
    //搜索全市场
    async querySearchAsync (queryString, cb) {
      let result = [];
      result = await this.$axios.get(this.$baseUrl + '/api/taikang/alarm/getGp3List', {
        params: {
          pageSize: 50,
          code: queryString
        },
        headers: { authorization: 'Bearer ' + this.$store.state.token }
      });
      // console.log(result.data.data);
      let array = [];
      for (let i = 0; i < result.data.data.length; i++) {
        if (result.data.data[i]) {
          array.push({
            value: result.data.data[i].gp3Code + ' ' + result.data.data[i].gp3Name,
            fundCode: result.data.data[i].gp3Code
          });
        }
      }
      cb(array);
    },
    //搜索基金池
    async querySearchFundPool () {
      let result = [];
      result = await getPoolList({ flag: 5 });
      let array = [];
      if (result.data) {
        for (let i = 0; i < result.data.length; i++) {
          if (result.data[i]) {
            array.push({
              value: result.data[i].name,
              fundCode: result.data[i].id
            });
          }
        }
      }
      this.fundPoolArray = array;
      this.fundPoolOption = array;
    },
    //获取基金池内基金列表
    async querySearchFundPoolFund (id) {
      this.loading = true;
      let result = [];
      if (!id) return;
      result = await getPoolInfo({
        flag: 5,
        id,
        insert_time: '',
        ismanager: false,
        item_list: [],
        type: 'equity'
      });

      try {
        if (result.data.length > 0) {
          for (let i = 0; i < result.data.length; i++) {
            if (result.data[i]) {
              const obj = {
                fundName: result.data[i].name,
                fundCode: result.data[i].code,
                type: 'info'
              };
              this.addProductDeduplication(obj);
            }
          }
        } else {
          this.$message.warning(result.data.mtymessage || '基金池无数据');
        }
      } catch (error) {
        this.$message.error('接口数据异常');
      }
      this.loading = false;
    },
    //搜索投后
    async querySearchPostAsync (queryString, cb) {
      let result = [];
      result = await this.$axios.get(this.$baseUrl + '/api/taikang/alarm/getTargetList', {
        params: {
          current: 1,
          pageSize: 100000,
          message: queryString
        },
        headers: { authorization: 'Bearer ' + this.$store.state.token }
      });
      let array = [];
      if (result.data.data) {
        for (let i = 0; i < result.data.data.length; i++) {
          if (result.data.data[i]) {
            array.push({
              value: result.data.data[i].targetName,
              fundCode: result.data.data[i].targetId
            });
          }
        }
      }
      this.postArray = array;
      this.postOption = array;
    },
    fundArrayDataFilter (val) {
      this.value = val; //给绑定值赋值
      if (val) {
        //val存在筛选数组
        this.fundPoolOption = this.fundPoolArray.filter((item) => {
          if (`${item.fundCode}`.includes(val) || item.value.includes(val)) {
            return true;
          } else {
            return false;
          }
        });
        console.log(this.options);
      } else {
        //val不存在还原数组
        this.fundPoolOption = this.fundPoolArray;
      }
    },
    postArrayDataFilter (val) {
      // this.value = val; //给绑定值赋值
      // console.log(this.value);
      if (val) {
        //val存在筛选数组
        console.log(this.postArray);
        this.postOption = this.postArray.filter((item) => {
          if (String(item.fundCode)?.includes(val) || item.value?.includes(val)) {
            return true;
          } else {
            return false;
          }
        });
        console.log(this.postOption);
      } else {
        //val不存在还原数组
        this.postOption = this.postArray;
      }
    }
  },

  watch: {
    selectedTags: {
      handler (newValue) {
        console.log(newValue);
        this.$emit('updateData', newValue);
      },
      deep: true
    },
    detail: {
      handler (newValue, oldValue) {
        if (JSON.stringify(newValue) === JSON.stringify(oldValue)) return;
        this.productTags = JSON.parse(JSON.stringify(newValue.products));
        this.productTags.forEach((item) => {
          item.type = '';
        });
        this.selectedTags = this.productTags.filter((v) => v.type === '');
      },
      deep: true
    }
  },

  mounted () {
    this.querySearchFundPool();
    this.querySearchPostAsync();
    this.productTags = JSON.parse(JSON.stringify(this.detail.products));
    this.productTags.forEach((item) => {
      item.type = '';
    });
    this.selectedTags = this.productTags.filter((v) => v.type === '');
  }
};
</script>

<template>
  <div>
    <el-row class="body">
      <el-col :span="6"
              class="left">
        <div class="left_header flex item-center justify-between">
          已选{{ selectedTags.length }}项
          <el-button class="button-color"
                     type="text"
                     @click="clearSelected"> 清空 </el-button>
        </div>
        <div class="chosen">
          <div v-for="item in selectedTags"
               class="chosenItem flex item-center justify-between">
            {{ item.fundName ? item.fundName : '未命名' }}
            <i class="el-icon-delete"
               @click="deleteSelected(item)"></i>
          </div>
        </div>
      </el-col>
      <el-col :span="18"
              class="right">
        <div class="search-box">
          <!-- <el-input v-show="false"
                    v-model="search.fundName"
                    class="search"
                    clearable
                    placeholder="搜索关联对象"
                    prefix-icon="el-icon-search" />
          <el-button type="primary"
                     v-show="false"
                     @click=""
                     class="search_button">查询</el-button> -->
          <el-popover append-to-body
                      placement="right"
                      width="200"
                      trigger="click">
            <!-- <div class="option"
                 @click="showFund = true">从投后单个GP3添加</div> -->
            <div class="option"
                 @click="showPost = true">从投后对象添加</div>
            <div class="option"
                 @click="showBazaar = true">从投后单个GP3添加</div>
            <el-button icon="el-icon-plus"
                       type="primary"
                       style="margin-bottom: 8px;margin-left:10px;"
                       slot="reference"> 添加关联对象 </el-button>
          </el-popover>

          <div class="tags"
               v-loading="loading">
            <el-tag v-for="(tag, index) in productTags"
                    :key="index"
                    :type="tag.type"
                    class="tagsItem flex item-center justify-between"
                    closable
                    disable-transitions
                    @click="changeTagType(tag)"
                    @close="handleClose(tag)">
              {{ tag.fundName ? tag.fundName : '未命名' }}
            </el-tag>
          </div>
        </div>
      </el-col>
    </el-row>
    <!-- 从基金池添加弹窗 -->
    <el-dialog :visible.sync="showFund"
               title="从基金池添加"
               width="500px"
               @close="closeProductDialog(1)">
      <div class="rule-list form-box">
        <el-form ref="fundForm"
                 :rule="fund"
                 :model="fundForm"
                 class=""
                 label-width="140px">
          <el-form-item label="选择添加基金池：">
            <el-select v-model="fundForm.select"
                       placeholder="请选择基金池"
                       filterable
                       :filter-method="fundArrayDataFilter">
              <el-option v-for="fund in fundPoolOption"
                         :label="fund.value"
                         :value="fund.fundCode"
                         :key="fund.fundCode"
                         @click.native="handleFundSelect(fund)"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="closeProductDialog(1)">取消</el-button>
        <el-button type="primary"
                   @click="addProduct(fundForm, 1)">添加</el-button>
      </div>
    </el-dialog>
    <!-- 从投后添加弹窗 -->
    <el-dialog :visible.sync="showPost"
               title="从投后分析对象添加"
               width="500px"
               @close="closeProductDialog(2)">
      <div class="rule-list form-box">
        <el-form ref="postForm"
                 :model="postForm"
                 :rule="post"
                 class=""
                 label-width="140px">
          <el-form-item label="选择添加模板：">
            <el-select v-model="postForm.select"
                       placeholder="请选择分析对象"
                       filterable>
              <el-option v-for="fund in postOption"
                         :label="fund.value"
                         :value="fund.fundCode"
                         :key="fund.fundCode+'BBA'"
                         @click.native="handlePostSelect(fund)"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="closeProductDialog(2)">取消</el-button>
        <el-button type="primary"
                   @click="addProduct(postForm, 2)">添加</el-button>
      </div>
    </el-dialog>
    <!-- 从全市场添加弹窗 -->
    <el-dialog :visible.sync="showBazaar"
               title="从投后单个GP3添加"
               width="500px"
               @close="closeProductDialog(3)">
      <div class="rule-list form-box">
        <el-form ref="bazaarForm"
                 :rule="bazaar"
                 :model="bazaarForm"
                 class="GP3ADD"
                 label-width="140px">
          <el-form-item label="搜索GP3添加：">
            <el-button slot="append"
                       icon="el-icon-search"></el-button>
            <el-autocomplete v-model="bazaarForm.select"
                             :fetch-suggestions="querySearchAsync"
                             class="input-with-select"
                             style='width:300px !important'
                             :popper-append-to-body="false"
                             width="260px"
                             placeholder="搜索内容"
                             @select="handleSelect"></el-autocomplete>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="closeProductDialog(3)">取消</el-button>
        <el-button type="primary"
                   @click="addProduct(bazaarForm, 3)">添加</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
@import '../../tkdesign';

.body {
	height: calc(100vh - 506px);
	padding-bottom: 80px;
	font-size: 14px;
}

.direction-column {
	flex-direction: column;
}

.left,
.right {
	scrollbar-width: none !important;
}

.left::-webkit-scrollbar,
.right::-webkit-scrollbar {
	width: 4px;
}

.left {
	height: 100%;
	overflow: auto;
	border-right: 1px solid #e9e9e9;

	.left_header {
		border-bottom: 1px solid #e9e9e9;
		padding: 0 16px 10px;
	}

	.chosen {
		padding: 8px 16px;

		.chosenItem {
			line-height: 36px;
			cursor: pointer;
		}
	}
}

.right {
	height: 100%;
	overflow: auto;
	border-right: 1px solid #e9e9e9;
	font-size: 14px;
	padding: 0 16px 0;

	.search-box {
		position: relative;

		.search_button {
			margin-right: 16px;
		}

		.search {
			margin: 0 16px 16px 0;
			width: 240px;
		}

		.tags {
			display: flex;
			flex-direction: row;
			flex-wrap: wrap;

			.tagsItem {
				align-self: baseline;
				line-height: 22px;
				margin: 4px 10px;
				cursor: pointer;
			}
		}
	}
}

.rule-list {
	padding: 40px 0 !important;
}
</style>
<style lang="scss">
.el-popover.el-popper {
	padding: 0;

	.option {
		line-height: 30px;
		text-align: center;
	}

	.option:hover {
		background-color: #4096ff;
		color: white;
	}
}
</style>

<style lang="scss">
.form-box {
	.el-form-item__content {
		margin-left: 100px !important;

		.el-input {
			width: auto !important;
		}
	}
}
</style>
<style >
.GP3ADD .el-popper[x-placement^='bottom'] {
	width: 300px !important;
}
</style>