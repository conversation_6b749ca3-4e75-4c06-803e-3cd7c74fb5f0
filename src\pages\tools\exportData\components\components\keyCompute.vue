<template>
	<div style="width: 100%" class="my-24">
		<div class="flex_between">
			<div class="title fs-16">字段选择器</div>
			<div class="flex_start">
				<div class="flex_start mr-12">
					<div style="width: 70px">时间区间:</div>
					<div>
						<el-date-picker
							v-model="date_list"
							type="daterange"
							range-separator="-"
							start-placeholder="开始日期"
							end-placeholder="结束日期"
							value-format="yyyy-MM-dd"
							@change="changeDate"
						></el-date-picker>
					</div>
				</div>
				<!-- <div class="flex_start">
					<div style="width: 70px">参考基准:</div>
					<div>
						<el-select v-model="active_index" placeholder="请选择参考基准">
							<el-option v-for="item in index_list" :key="item.value" :label="item.label" :value="item.value"></el-option>
						</el-select>
					</div>
				</div> -->
			</div>
		</div>
		<!-- <div>
      <el-table :data="data" style="width: 100%" border>
        <el-table-column prop="prop" label="数据名称"></el-table-column>
        <el-table-column prop="prop" label="操作" width="200px"></el-table-column>
      </el-table>
    </div>-->
		<div class="flex_start key-table-dark mt-20" style="background-color: #f5f5f5">
			<div class="table-column-first table-column-first-border"></div>
			<div class="table-column-main px-8 py-12">数据名称</div>
			<div class="table-column-end px-8 py-12">操作</div>
		</div>
		<div class="condition-choose-table">
			<div v-for="obj in condition_list" :key="obj.field" class="transform-style">
				<div class="flex_start key-table-dark">
					<div class="table-column-first table-column-first-border flex_center" style="cursor: pointer" @click="foldIcon(obj.field)">
						<svg v-show="obj.fold" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path
								d="M7.94266 10.6406C7.92264 10.6304 7.90534 10.6155 7.89219 10.5973L4.71361 6.20442C4.64576 6.10978 4.71361 5.97764 4.82968 5.97764L5.66719 5.97764C5.85111 5.97764 6.02254 6.06514 6.12968 6.21335L8.00826 8.80978L9.88504 6.21335C9.99219 6.06514 10.1654 5.97764 10.3475 5.97764L11.185 5.97764C11.3011 5.97764 11.369 6.10978 11.3011 6.20442L8.12254 10.5973C8.10939 10.6155 8.09209 10.6304 8.07206 10.6406C8.05204 10.6509 8.02986 10.6562 8.00736 10.6562C7.98487 10.6562 7.96269 10.6509 7.94266 10.6406Z"
								fill="black"
								fill-opacity="0.25"
							/>
							<path
								fill-rule="evenodd"
								clip-rule="evenodd"
								d="M6.99382e-07 8C1.08564e-06 3.58172 3.58172 -1.08564e-06 8 -6.99382e-07C12.4183 -3.13124e-07 16 3.58172 16 8C16 12.4183 12.4183 16 8 16C3.58172 16 3.13124e-07 12.4183 6.99382e-07 8ZM1.125 8C1.125 4.20304 4.20304 1.125 8 1.125C11.797 1.125 14.875 4.20304 14.875 8C14.875 11.797 11.797 14.875 8 14.875C4.20304 14.875 1.125 11.797 1.125 8Z"
								fill="black"
								fill-opacity="0.25"
							/>
						</svg>
						<svg v-show="!obj.fold" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path
								d="M8.05734 5.35935C8.07736 5.3696 8.09466 5.38447 8.10781 5.40272L11.2864 9.79558C11.3542 9.89022 11.2864 10.0224 11.1703 10.0224H10.3328C10.1489 10.0224 9.97746 9.93486 9.87032 9.78665L7.99174 7.19022L6.11496 9.78665C6.00782 9.93486 5.8346 10.0224 5.65246 10.0224H4.81496C4.69889 10.0224 4.63103 9.89022 4.69889 9.79558L7.87746 5.40272C7.89061 5.38447 7.90791 5.3696 7.92794 5.35935C7.94796 5.3491 7.97014 5.34375 7.99264 5.34375C8.01513 5.34375 8.03731 5.3491 8.05734 5.35935Z"
								fill="black"
								fill-opacity="0.25"
							/>
							<path
								fill-rule="evenodd"
								clip-rule="evenodd"
								d="M16 8C16 12.4183 12.4183 16 8 16C3.58172 16 0 12.4183 0 8C0 3.58172 3.58172 0 8 0C12.4183 0 16 3.58172 16 8ZM14.875 8C14.875 11.797 11.797 14.875 8 14.875C4.20304 14.875 1.125 11.797 1.125 8C1.125 4.20304 4.20304 1.125 8 1.125C11.797 1.125 14.875 4.20304 14.875 8Z"
								fill="black"
								fill-opacity="0.25"
							/>
						</svg>
					</div>
					<div class="table-column-main px-8 py-12">{{ obj.fieldName }}</div>
					<div class="table-column-end px-8 py-12">
						<el-link style="cursor: pointer" @click="delCondition(obj.field)">删除</el-link>
					</div>
				</div>
				<div v-show="obj.fold">
					<div class="flex_start key-table-dark" style="background-color: #f5f5f5">
						<div class="table-column-first"></div>
						<div v-if="obj.type == 'number'" class="table-column-main flex_start">
							<div class="table-column-r-border px-8 py-12" style="flex: 1">算子</div>
							<div class="table-column-r-border px-8 py-12" style="flex: 1">运算符</div>
							<div class="table-column-r-border px-8 py-12" style="flex: 1">值</div>
							<div class="px-8 py-12" style="flex: 1">单位</div>
						</div>
						<div v-if="obj.type == 'number'" class="table-column-end px-8 py-12"></div>
						<div v-else class="table-column-main px-8 py-12">{{ obj.fieldName }}</div>
					</div>
					<div class="flex_start key-table-dark" style="background-color: #f5f5f5" v-for="(item, index) in obj.children" :key="index">
						<div class="table-column-first"></div>
						<div v-if="obj.fieldType == 'char'" style="flex: 1">
							<div class="table-column-main px-8 py-12 flex_start">
								<el-input v-model="item.value" :placeholder="obj.fieldName" style="width: 240px; height: 32px"></el-input>
							</div>
						</div>
						<div v-else-if="obj.fieldType == 'number'" style="flex: 1">
							<div class="flex_start">
								<div class="table-column-main flex_start">
									<div class="table-column-r-border px-8 py-12" style="flex: 1">
										<el-select v-model="item.mathRange">
											<el-option v-for="v in mathRangeList" :key="v.value" :label="v.label" :value="v.value"></el-option>
										</el-select>
									</div>
									<div class="table-column-r-border px-8 py-12" style="flex: 1">
										<el-select v-model="item.operator">
											<el-option v-for="v in operationList" :key="v.value" :label="v.label" :value="v.value"></el-option>
										</el-select>
									</div>
									<div class="table-column-r-border px-8 py-12" style="flex: 1">
										<el-input v-model="item.value" placeholder></el-input>
									</div>
									<div class="px-8 py-12" style="flex: 1">{{ item.unit == 'value' ? '值' : '分位' }}</div>
								</div>
							</div>
						</div>
						<div v-else-if="obj.fieldType == 'date'">
							<el-date-picker
								v-model="item.value"
								type="daterange"
								format="yyyy-MM-dd"
								value-format="yyyy-MM-dd"
								range-separator="至"
								start-placeholder="开始日期"
								end-placeholder="结束日期"
							>
							</el-date-picker>
						</div>
						<div class="table-column-end px-8 py-12" @click="delItem(obj.field, index)">
							<i class="el-icon-delete"></i>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	data() {
		return {
			date_list: [],
			active_index: '',
			index_list: [],
			mathRangeList: [
				{ label: '平均', value: 'avg' }
				// { label: '最小', value: 'min' },
				// { label: '最大', value: 'max' },
				// { label: '所有', value: 'all' }
			],
			operationList: [
				{ label: '大于等于', value: 'ge' },
				{ label: '小于等于', value: 'le' },
				{ label: '等于', value: 'eq' },
				{ label: '所有', value: 'all' }
			],
			condition_list: []
		};
	},
	methods: {
		getCondition(item) {
			let index = this.condition_list.findIndex((v) => v.field == item.field);
			let children = item.fieldType == 'char' ? { value: '' } : { mathRange: 'avg', operator: '', value: '', unit: 'value' };
			if (index == -1) {
				this.condition_list.push({ ...item, fold: true, children: [children] });
			} else {
				this.condition_list.splice(index, 1);
				// this.condition_list[index].children.push(children);
			}
		},
		// 设置导出数据条件
		setConditionList(condition) {
			this.condition_list = condition.map((v) => {
				return { ...v, fold: true };
			});
		},
		// 获取导出数据条件
		getExportCondition() {
			return this.condition_list;
		},
		// 监听事件区间切换
		changeDate() {
			this.$emit('updateDate', { startDate: this.date_list?.[0], endDate: this.date_list?.[1] });
		},
		// 删除指标
		delCondition(field) {
			this.condition_list.splice(
				this.condition_list.findIndex((v) => v.field == field),
				1
			);
		},
		// 删除条件
		delItem(field, index) {
			this.condition_list[this.condition_list.findIndex((v) => v.field == field)].children.splice(index, 1);
		},
		foldIcon(value) {
			let index = this.condition_list.findIndex((v) => v.value == value);
			this.$set(this.condition_list, index, {
				...this.condition_list[index],
				fold: !this.condition_list[index].fold
			});
		}
	},
	mounted() {
		this.date_list = [this.moment().subtract(10, 'days').format('YYYY-MM-DD'), this.moment().format('YYYY-MM-DD')];
		this.changeDate();
	}
};
</script>

<style lang="scss" scoped>
.transform-style {
	transition: width 0.5s linear;
}
.condition-choose-table {
	height: 720px;
	overflow-y: auto;
}
.key-table-dark {
	height: 46px;
	font-size: 14px;
	color: rgba(0, 0, 0, 0.65);
	.table-column-first {
		width: 46px;
		height: 47px;
		border-left: 1px solid #e9e9e9;
	}
	.table-column-first-border {
		border-top: 1px solid #e9e9e9;
		border-bottom: 1px solid #e9e9e9;
	}
	.table-column-l-border {
		border-left: 1px solid #e9e9e9;
	}
	.table-column-r-border {
		border-right: 1px solid #e9e9e9;
	}
	.table-column-main {
		// width: 1090px;
		flex: 1;
		height: 47px;
		border: 1px solid #e9e9e9;
	}
	.table-column-end {
		width: 240px;
		height: 47px;
		border-top: 1px solid #e9e9e9;
		border-right: 1px solid #e9e9e9;
		border-bottom: 1px solid #e9e9e9;
	}
}
</style>
