<template>
	<div style="margin-top: 16px">
		<el-table
			v-loading="loading"
			lazy
			ref="multipleTable"
			:data="tableData"
			tooltip-effect="dark"
			border
			:cell-style="cellStyle"
			style="width: 100%"
			@selection-change="handleSelectionChange"
			:row-key="getRowKey"
		>
			<el-table-column type="selection" width="60" class-name="" align="center" :reserve-selection="true"></el-table-column>
			<!-- <el-table-column v-for="item in formatTableData" :key="item" :label="item[0]" :prop="item[1]" width="120" align="gotoleft">
				<template slot-scope="scope">
					{{ scope.row[item[0]] }}
				</template>
			</el-table-column> -->
			<el-table-column
				v-for="item in formatTableData"
				:key="item.value"
				:label="item.label"
				:prop="item.value"
				show-overflow-tooltip
				min-width="160"
				fit
				align="gotoleft"
			>
				<template slot-scope="scope">
					{{ scope.row[item.value] }}
				</template>
			</el-table-column>
		</el-table>
	</div>
</template>

<script>
export default {
	data() {
		return {
			tableData: [],
			multipleSelection: [],
			loading: false,
			formatTableData: ['字段', '名称', '备注']
		};
	},
	// computed: {
	// 	formatTableData() {
	// 		let _tableData = this.tableData;
	// 		this.cnt = this.cnt > _tableData.length ? 0 : this.cnt;
	// 		return Object.entries(_tableData[this.cnt++]);
	// 	}
	// },
	methods: {
		getRowKey(row) {
			// console.log('2rrow1', row);
			//有基金和基金经理，key值不同
			// 后端的唯一标识是由多个字段共同组成的，因此前端字段保证唯一性 需做 拼接处理
			return (
				row?.manager_code +
				row?.manage_to +
				row?.manage_from +
				row?.weight +
				row?.pb +
				row?.alpha +
				row?.allocation +
				row?.stock +
				row?.turnover +
				row?.top10_concentration +
				row?.top3_concentration +
				row?.bond_weight +
				row?.other_weight +
				row?.item +
				row?.equity_weight +
				row?.comparison +
				row?.loses +
				row?.wins +
				row?.prob_lose +
				row?.prob_win +
				row?.competitor +
				row?.hold_length +
				row?.ann_return +
				row?.maxdrawdown +
				row?.sharpe +
				row?.end_date +
				row?.start_date +
				row?.market +
				row?.downratioinC +
				row?.downratioinN +
				row?.ratioinN +
				row?.duration +
				row?.freq +
				row?.name +
				row?.style +
				row?.date +
				row?.fund_name +
				row?.timing +
				row?.netasset +
				row?.fund_code +
				row?.residualvolatility +
				row?.industry_code +
				row?.duration +
				row?.downratioinN +
				row?.downratioinC +
				row?.ratioinN +
				row?.quarter +
				row?.year +
				row?.description +
				row?.rel_rank +
				row?.item +
				row?.company_code +
				row?.name +
				row?.stock_code +
				row?.stock_name +
				row?.industry_name +
				row?.excess_return +
				row?.industry_return +
				row?.trading +
				row?.yearqtr +
				row?.time_start +
				row?.time_end +
				row?.periodname
			);
		},
		getData(data, tableData, loading) {
			// this.$refs.multipleTable.clearSelection();
			this.loading = loading;
			this.tableData = tableData;
			this.formatTableData = data;
			console.log('this.tableData', this.tableData);
		},
		toggleSelection(rows) {
			if (rows) {
				rows.forEach((row) => {
					this.$refs.multipleTable.toggleRowSelection(row);
				});
			} else {
				this.$refs.multipleTable.clearSelection();
			}
		},
		handleSelectionChange(val) {
			console.log('vdata', val);
			this.multipleSelection = val;
			this.$emit('changeSelectionDown', val);

			this.$emit('changeSelection', val);
		},
		cellStyle({ row, column, rowIndex, columnIndex }) {}
	}
};
</script>
