<template>
  <div class="box_Board">
    <div class="header_box"><span class="header_unactive">投后&nbsp;/&nbsp;投后分析&nbsp;/&nbsp;</span>分析对象管理<span></span></div>
    <div class="border_table">
      <div class="border_table_header"
           style="display:flex;justify-content: space-between;align-items: center;">
        <div class="border_table_header_title">投后分析对象</div>
        <div><el-button @click="showDialog=true"
                     style="border-color:#4096ff;background: #4096ff;color:white"
                     type="">新建分析</el-button></div>
      </div>
      <div>
        <el-table :data="tableData">
          <el-table-column align="gotoleft"
                           prop="objectName"
                           label="投后分析对象"></el-table-column>
          <el-table-column align="gotoleft"
                           prop="date"
                           label="创建时间"></el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <div style="display:flex">
                <el-button @click="editCell(scope.row.updateObj)"
                           style="color:#4096ff"
                           type="text">编辑</el-button>
                <el-button @click="deleteCell(scope.row.updateObj)"
                           style="color:#4096ff"
                           type="text">删除</el-button>
              </div>
            </template>
          </el-table-column>
          <template slot="empty">
            <el-empty image-size="160"></el-empty>
          </template>
        </el-table>
        <div class="pagination_board">
          <el-pagination background
                         layout="total, sizes, prev, pager, next"
                         :current-page.sync="pageIndex"
                         :page-size="pageSize"
                         :total="totalSize"
                         @size-change="handleSizeChange"
                         @current-change="handlePageChange"></el-pagination>
        </div>
      </div>
    </div>
    <el-dialog width="900"
               :visible.sync="showDialog"
               title="新建分析对象">
      <createView @submitDialog="submitDialog"
                  @cancelDialog="cancelDialog"></createView>
    </el-dialog>
  </div>
</template>

<script>
import { getList, insertObj, delObj, upObj } from '@/api/pages/tkAnalysis/index.js'
import createView from './component/board/create.vue'
export default {
  components: {
    createView
  },
  data () {
    return {
      pageIndex: 1,
      pageSize: 20,
      tableData: [],
      alldata: [],
      totalSize: 0,
      showDialog: false,
      selectData: [],
      createdName: ''
    };
  },
  mounted () {
    this.init(1);
  },
  methods: {
    cancelDialog () {
      this.showDialog = false;
    },
    submitDialog (e, e1) {
      this.showDialog = false;
      this.selectData = e
      this.createdName = e1
      this.submitObject()
    },
    async deleteCell (id) {
      let { data, mtycode, mtymessage } = await delObj({
        id: id,
      })
      if (mtycode == 200) {
        this.$message.success('删除成功')
        this.init(1)
      }
      else { this.$message.error(mtymessage) }
      this.init(1);
    },
    handlePageChange () {
      this.init(this.pageIndex)
    },
    handleSizeChange (val) {
      this.pageSize = val;
      this.init(this.pageIndex)
    },
    // 获取分析对象列表
    async init (pageIndex) {
      this.tableData = [{
        "id": "id",
        "objectName": "分析对象名称",
        "date": "创建时间"
      }]
      return;//TODO接口有问题按照我的来
      let { data, mtycode, mtymessage } = await getList({ currentPage: pageIndex, pageSize: this.pageSize })
      if (mtycode == 200) {
        this.totalSize = data?.pageSize * data?.totalPage || 0
        this.pageIndex = data?.currentPage || 1
        this.pageSize = data?.pageSize || 20
        this.tableData = data?.dataList
      }
      else {
        this.tableData = []
        this.totalSize = 0
        this.pageIndex = 1
        this.pageSize = 20
      }
    },
    // 提交分析对象
    async submitObject () {
      let { data, mtycode, mtymessage } = await insertObj({
        type: '层级',
        complete: '1',
        data: this.selectData
      })
      if (mtycode == 200) {
        this.$message.success('新建成功')
        this.init(1)
      }
      else { this.$message.error(mtymessage) }
    },
    // 修改分析对象
    async editCell (id) {
      let { data, mtycode, mtymessage } = await upObj({
        type: '层级',
        // complete: '1',
        objectId: id,
        data: this.selectData
      })
      if (mtycode == 200) {
        this.$message.success('修改成功')
        this.init(1)
      }
      else { this.$message.error(mtymessage) }
    }
  },
}
</script>
<style lang="scss">
.box_Board {
	padding: 0px 24px 16px 24px;
}
.border_table {
	padding-top: 16px;
	padding-bottom: 16px;
	padding-left: 24px;
	padding-right: 24px;
	background: white;
	.border_table_header {
		margin-bottom: 20px;
		.border_table_header_title {
			color: rgba(0, 0, 0, 0.85);
			text-align: center;
			font-size: 16px;
			font-style: normal;
			font-weight: 500;
			line-height: 24px; /* 150% */
		}
	}
	.pagination_board {
		text-align: right;
		margin-top: 16px;
	}
}
.header_box {
	margin-top: 16px;
	margin-bottom: 16px;
}
.header_unactive {
	font-size: 14px;
	font-weight: 400;
	line-height: 22px;
	text-align: left;
	color: rgba(0, 0, 0, 0.45);
}
.header_active {
	font-size: 14px;
	font-weight: 400;
	line-height: 22px;
	text-align: left;
	color: rgba(0, 0, 0, 0.85);
}
</style>
