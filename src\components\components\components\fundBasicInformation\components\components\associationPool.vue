<template>
	<div class="association_pool px-12 py-5 flex_start mt-12">
		<div class="association_pool_title mr-8">关联的基金池:</div>
		<div class="mr-4"><el-link>成长价值周期综合</el-link></div>
		<div class="flex_start mr-4">
			<div class="mr-4">/</div>
			<el-link>基金池名字2</el-link>
		</div>
		<div class="flex_start mr-4">
			<div class="mr-4">/</div>
			<el-link>基金池名字3</el-link>
		</div>
	</div>
</template>

<script>
export default {};
</script>
<style lang="scss" scoped>
.association_pool {
	border-radius: 4px;
	background: linear-gradient(270deg, rgba(255, 145, 3, 0.2) 0%, #ecf5ff 100%);
	font-size: 14px;
	font-weight: 400;
	.association_pool_title {
		color: rgba(0, 0, 0, 0.45);
	}
	::v-deep.el-link {
		font-weight: 400;
	}
}
</style>
