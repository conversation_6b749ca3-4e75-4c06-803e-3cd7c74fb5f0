<template>
  <div class="HolePageBox">
    <div class="boxTitleHeader">
      <span class="headerFontSmall">工具箱/</span>
      <span class="headerFontBig">比较</span>
    </div>
    <div class="ContentMainBox">
      <el-tabs v-model="activeName"
               @tab-click="handleClick">
        <el-tab-pane label="基金对比"
                     name="fund">
          <div style="width: 100%; display: flex; justify-content: space-between; align-items: center">
            <div style="font-weight: 500; font-size: 16px">对比记录</div>
            <el-button type="primary"
                       @click="addCompare">新建对比</el-button>
          </div>
          <el-table @cell-click="rowClick"
                    v-loading="loadingModel"
                    style="margin-top: 5px; cursor: pointer; min-height: calc(100vh - 475px)"
                    height="592px"
                    :data="tableDataNow">
            <el-table-column label="序号"
                             width="100px"
                             sortable
                             align="gotoleft"
                             prop="id"></el-table-column>
            <el-table-column label="对比组名称"
                             align="gotoleft"
                             prop="title"></el-table-column>
            <el-table-column label="对比组基金经理"
                             :show-overflow-tooltip="true"
                             align="gotoleft"
                             prop="model_description"></el-table-column>
            <el-table-column label="操作"
                             width="100px"
                             align="gotoleft">
              <template slot-scope="scope"><el-button type="text"
                           style="color: #4096ff"
                           @click="openFundCom(scope.row)">打开</el-button><el-button type="text"
                           style="color: red"
                           @click="delModel(scope)">删除</el-button></template>
            </el-table-column>
          </el-table>
          <el-pagination background
                         style="display: flex; justify-content: right; margin-top: 5px"
                         @size-change="handleSizeChange"
                         @current-change="handleCurrentChange"
                         :current-page.sync="currentPage"
                         :page-sizes="[10, 20, 40, 60, 80, 100]"
                         :page-size="pageSIze"
                         layout="total, sizes, prev, pager, next, jumper"
                         :total="tableData.length">
          </el-pagination>
        </el-tab-pane>
        <el-tab-pane label="基金经理对比"
                     name="manager">
          <div style="width: 100%; display: flex; justify-content: space-between; align-items: center">
            <div style="font-weight: 500; font-size: 16px">对比记录</div>
            <el-button type="primary"
                       @click="addCompare2">新建对比</el-button>
          </div>
          <el-table @cell-click="rowClick2"
                    v-loading="loadingModel2"
                    style="margin-top: 5px; cursor: pointer; min-height: calc(100vh - 475px)"
                    height="592px"
                    :data="tableDataNow2">
            <el-table-column label="序号"
                             width="100px"
                             sortable
                             align="gotoleft"
                             prop="id"></el-table-column>
            <el-table-column label="对比组名称"
                             align="gotoleft"
                             prop="title"></el-table-column>
            <el-table-column label="对比组基金经理"
                             :show-overflow-tooltip="true"
                             align="gotoleft"
                             prop="model_description"></el-table-column>
            <el-table-column label="操作"
                             width="100px"
                             align="gotoleft">
              <template slot-scope="scope"><el-button type="text"
                           style="color: #4096ff"
                           @click="openCom(scope.row)">打开</el-button><el-button type="text"
                           style="color: red"
                           @click="delModel2(scope)">删除</el-button></template>
            </el-table-column>
          </el-table>
          <el-pagination background
                         style="display: flex; justify-content: right; margin-top: 5px"
                         @size-change="handleSizeChange2"
                         @current-change="handleCurrentChange2"
                         :current-page.sync="currentPage2"
                         :page-sizes="[10, 20, 40, 60, 80, 100]"
                         :page-size="pageSIze2"
                         layout="total, sizes, prev, pager, next, jumper"
                         :total="tableData2.length">
          </el-pagination>
        </el-tab-pane>
      </el-tabs>
      <!-- <poollist ref='poollist'></poollist> -->
    </div>
    <el-dialog title="添加对比"
               :visible.sync="showbox"
               width="800px"
               destroy-on-close
               :close-on-click-modal="false">
      <div class="savemodel"
           style="width: 100%">
        <el-row>
          <div style="height: 10px"></div>
          <el-col :span="16"
                  style="border-right: 1px solid #e8e8e8">
            <el-input style="width: 200px; margin-bottom: 10px"
                      v-model="values"
                      @input="changeSelect"></el-input>
            <el-table style="padding-right: 20px"
                      v-loading="loadingSearch"
                      ref="multipleTables"
                      :height="300"
                      @selection-change="handleSelectionChange"
                      :data="select1">
              <el-table-column type="selection"
                               :width="55"> </el-table-column>
              <el-table-column align="gotoleft"
                               prop="name"
                               :show-overflow-tooltip="true"
                               label="基金名称"> </el-table-column>
              <el-table-column align="gotoleft"
                               prop="fundCo"
                               :show-overflow-tooltip="true"
                               label="基金公司"> </el-table-column>
              <el-table-column align="gotoleft"
                               prop="type"
                               :show-overflow-tooltip="true"
                               label="类型">
                <template slot-scope="scope"> <span v-if="scope.row.type == 'purebond'">纯债</span>
                  <span v-else-if="scope.row.type == 'bond'">固收+</span>
                  <span v-else-if="scope.row.type == 'bill'">中短债</span>
                  <span v-else-if="scope.row.type == 'cbond'">可转债</span>
                  <span v-else-if="scope.row.type == 'money'">货币</span>
                  <span v-else-if="scope.row.type == 'equityhk'">港股</span>
                  <span v-else-if="scope.row.type.indexOf('bond')>=0">其他债券</span>
                  <span v-else-if="scope.row.type == 'equity'||scope.row.type == 'equityenhance'">主动权益</span>
                  <span v-else-if="scope.row.type == 'equityindex'||scope.row.type == 'equityindexhk'">指数基金</span>
                  <span v-else-if="scope.row.type.indexOf('equity')>=0">主动权益</span>
                  <span v-else-if="scope.row.type.indexOf('QDII')>=0">QDII</span>
                  <span v-else>其他</span></template>
              </el-table-column>
            </el-table>
          </el-col>
          <el-col :span="8">
            <div style="margin-left: 10px">
              <div style="display: flex; justify-content: space-between; align-items: center">
                <div style="font-weight: 600">已选对比</div>
                <div><el-button type="text"
                             style="color: red"
                             @click="clear">清空</el-button></div>
              </div>
              <el-scrollbar style="height: 300px">
                <div style="text-align: center; margin: 10px 0 0 0"
                     v-for="(item, index) in endSelect"
                     :key="index">
                  <el-tag style="min-width: 200px;background:#fff !important;display: flex;align-items: center;justify-content: space-between;color: rgba(0, 0, 0, 0.85); !important"
                          closable
                          @close="closeTags(index)"
                          type="info">{{ item.name }}</el-tag>
                </div>
              </el-scrollbar>
            </div>
          </el-col>
        </el-row>
        <div style="display: flex; justify-content: flex-end; align-items: center; width: 100%; margin-top: 10px">
          <el-button @click="showbox = false">取消</el-button>
          <el-button @click="gotoCompare"
                     type="primary">确定</el-button>
        </div>
      </div>
    </el-dialog>
    <el-dialog :close-on-click-modal="false"
               title="添加对比"
               :visible.sync="showbox2"
               width="800px"
               destroy-on-close>
      <div class="savemodel"
           style="width: 100%">
        <el-row>
          <div style="height: 10px"></div>
          <el-col :span="16"
                  style="border-right: 1px solid #e8e8e8">
            <el-input style="width: 200px; margin-bottom: 10px"
                      v-model="values2"
                      @input="changeSelect2"></el-input>
            <el-table style="padding-right: 20px"
                      v-loading="loadingSearch2"
                      ref="multipleTables2"
                      :height="300"
                      @selection-change="handleSelectionChange2"
                      :data="select12">
              <el-table-column type="selection"
                               :width="55"> </el-table-column>
              <el-table-column align="gotoleft"
                               prop="name"
                               :show-overflow-tooltip="true"
                               label="基金经理"> </el-table-column>
              <el-table-column align="gotoleft"
                               prop="fundCo"
                               :show-overflow-tooltip="true"
                               label="基金公司"> </el-table-column>
              <!-- <el-table-column align="gotoleft"
                               prop="type"
                               :show-overflow-tooltip="true"
                               label="类型">

              </el-table-column> -->
            </el-table>
          </el-col>
          <el-col :span="8">
            <div style="margin-left: 10px">
              <div style="display: flex; justify-content: space-between; align-items: center">
                <div style="font-weight: 600">已选对比</div>
                <div><el-button type="text"
                             style="color: red"
                             @click="clear2">清空</el-button></div>
              </div>
              <el-scrollbar style="height: 300px">
                <div style="text-align: center; margin: 10px 0 0 0"
                     v-for="(item, index) in endSelect2"
                     :key="index">
                  <el-tag style="min-width: 200px"
                          closable
                          @close="closeTags2(index)"
                          type="info">{{ item.name }}</el-tag>
                </div>
              </el-scrollbar>
            </div>
          </el-col>
        </el-row>
        <div style="display: flex; justify-content: flex-end; align-items: center; width: 100%; margin-top: 10px">
          <el-button @click="showbox2 = false">取消</el-button>
          <el-button @click="gotoCompare2"
                     type="primary">确定</el-button>
        </div>
      </div>
    </el-dialog>
    <!-- --------------------------------------------------------------------------- -->
    <el-dialog :close-on-click-modal="false"
               title="选择比较的类型"
               :visible.sync="showitem"
               width="30%"
               destroy-on-close>
      <div class="savemodel"
           style="width: 100%">
        <el-form>
          <el-form-item>
            <el-select v-model="valuex"
                       placeholder="请选择比较类型">
              <el-option v-for="item in options"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value"> </el-option>
            </el-select>
          </el-form-item>
          <div class="height10border"></div>
          <div style="text-align: right"
               class="demo-drawer__footer">
            <el-button type="primary"
                       style="background: #d7dbe0 !important; color: balck !important; border: 1px solid #d7dbe0 !important"
                       @click="showitem = fasle">取消</el-button>
            <el-button type="primary"
                       @click="submitmodal">进入比较</el-button>
          </div>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
// import poollist from './components/poollist.vue'
import { basketFund, delBasketFund, search_all, FundCodeBasicMsg, TypeMsg, TemplaterGet, TemplaterDel } from '@/api/pages/tools/compare.js';

export default {
  //import引入的组件需要注入到对象中才能使用
  components: {},
  data () {
    //这里存放数据
    return {
      valuesSelect: [],
      loadingSearch: false,
      values: '',
      showbox: false,
      currentPage: 1,
      pageSIze: 10,
      activeName: 'fund',
      tableDataNow: [],
      tableData: [],
      select1: [],
      select1All: [],
      startSelect: [],
      endSelect: [],
      loadingModel: false,
      // manager
      loadingModel2: false,
      tableDataNow2: [],
      tableData2: [],
      currentPage2: 1,
      pageSIze2: 10,
      showbox2: false,
      values2: '',
      loadingSearch2: false,
      endSelect2: [],
      select12: [],
      select1All2: [],
      startSelect2: [],
      showitem: false,
      options: [],
      valuex: ''
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    // rowClick (row, column) {
    //   // console.log(row);
    //   if (column.property == 'id' || column.property == 'title' || column.property == 'model_description') {
    //     this.$router.push({
    //       path: '/fundcompare',
    //       query: {
    //         id: row.model_name,
    //         type: row.type,
    //         name: row.model_description,
    //         model_flag: 1
    //       }
    //     });
    //   }
    // },
    openFundCom (row) {
      // console.log(row);
      if (row.model_args?.types) {
        this.$router.push({
          path: '/fundcompareDiff',
          query: {
            id: row.model_name,
            type: row.type,
            name: row.model_description,
            model_flag: 1,
            types: row.model_args?.types,

          }
        });
      }
      else {
        this.$router.push({
          path: '/fundcompare',
          query: {
            id: row.model_name,
            type: row.type,
            name: row.model_description,
            model_flag: 1,
            types: row.model_args?.types,

          }
        });
      }
    },
    async getModel () {
      this.loadingModel = true;
      this.loadingModel2 = true;

      try {
        let data = await TemplaterGet({});
        if (data && data.mtycode != 204) {
          this.loadingModel = false;
          this.tableData = [];
          this.loadingModel2 = false;
          this.tableData2 = [];
          let x = 1;
          for (let i = 0; i < data.length; i++) {
            if (data[i].ismanager == 'False') {
              this.tableData.push(data[i]);
              this.tableData[x - 1]['id'] = x;
              x = x + 1;
            }
          }

          let y = 1;
          for (let j = 0; j < data.length; j++) {
            if (data[j].ismanager == 'True') {
              this.tableData2.push(data[j]);
              this.tableData2[y - 1]['id'] = y;
              y = y + 1;
            }
          }

          this.tableDataNow = this.tableData.slice(0, 10);
          this.tableDataNow2 = this.tableData2.slice(0, 10);
          // console.log(this.tableDataNow);
        } else {
          this.loadingModel = false;
          this.loadingModel2 = false;
        }
      } catch (e) {
        this.loadingModel = false;
        this.loadingModel2 = false;
      }
    },
    async delModel (scope) {
      let data = await TemplaterDel({ model_name: scope.row.model_name });
      if (data) {
        this.getModel();
      }
    },
    async gotoCompare () {
      if (this.endSelect.length <= 1 || this.endSelect.length > 4) {
        this.$message.error('请选择对比项目大于一支且小于五支基金');
      } else {
        let tempcode = '';
        let tempname = '';
        let temptype = null;
        for (let i = 0; i < this.endSelect.length; i++) {
          tempcode = tempcode + this.endSelect[i].code + ',';
          tempname = tempname + this.endSelect[i].name + ',';
        }
        tempcode = tempcode.slice(0, tempcode.length - 1);
        tempname = tempname.slice(0, tempname.length - 1);
        let data = await TypeMsg({ code: tempcode });
        if (data) {
          // //console.log(data)
          if (data.data) {
            if (data.data.length == 0) {
              this.$router.push({
                path: '/fundcompareDiff',
                query: {
                  id: tempcode,
                  type: '',
                  types: data.type.join(','),
                  name: tempname
                }
              });
              // this.$message.error('请选择具有相同类型的基金进行比较');
            } else if (data.data.length == 1) {
              temptype = data.data[0];
              if (
                temptype == 'bond' ||
                temptype == 'cbond' ||
                temptype == 'purebond' ||
                temptype == 'bill' ||
                temptype.indexOf('equity') >= 0 ||
                temptype == 'obond'
              ) {
                this.$router.push({
                  path: '/fundcompare',
                  query: {
                    id: tempcode,
                    type: temptype,
                    name: tempname
                  }
                });
              } else {
                this.$message('暂时只提供主动权益，二级债，债券类产品的比较');
              }
            } else if (data.data.length > 1) {
              this.$router.push({
                path: '/fundcompareDiff',
                query: {
                  id: tempcode,
                  type: '',
                  types: data.type.join(','),
                  name: tempname
                }
              });
              // this.$message.error('请选择具有相同类型的基金进行比较');
              // this.showitem = true
              // this.options = []
              // for(let i = 0; i < data.data.length; i++){
              //     this.options.push({value:data.data[i],label:data.data[i]})
              // }
            }
          }
        }
      }
    },
    clear () {
      this.startSelect = [];
      this.endSelect = [];
      this.$refs.multipleTables.clearSelection();
    },
    handleSizeChange (val) {
      this.pageSIze = val;
      this.currentPage = 1;
      this.handleCurrentChange(1);
    },
    handleCurrentChange (val) {
      this.tableDataNow = this.tableData.slice((val - 1) * this.pageSIze, val * this.pageSIze);
    },
    addCompare () {
      this.showbox = true;
    },

    async changeSelect () {
      this.loadingSearch = true;
      let that = this;
      let { data } = await search_all({
        message: that.values,
        flag: 1
      });
      if (data) {
        let temparr = [];
        for (let i = 0; i < data.length; i++) {
          if (data[i].flag == 'fund') {
            temparr.push(data[i]);
          }
        }
        // if(temparr.length > 0) {
        that.select1All = temparr;
        that.select1 = temparr.slice(0, 10);
        // }

        // //console.log(that.havefundmanager)
        that.loading = false;
      }
      this.loadingSearch = false;
    },

    handleSelectionChange (val) {
      let that = this;
      if (this.select1All.length > 0 && val.length > 0) {
        // 临时替代，课根据上一次的select1All发生变化了没来排除这个请客

        if (val.length >= this.startSelect.length) {
          for (let i = 0; i < val.length; i++) {
            if (this.endSelect.findIndex((item) => item.code == val[i].code) < 0) {
              this.endSelect.push(val[i]);
            }
          }
        } else {
          let x = 0;
          let flags = false;
          for (let i = 0; i < this.startSelect.length; i++) {
            if (val.findIndex((item) => item.code == this.startSelect[i].code) < 0) {
              x = i;
            } else {
              flags = true;
            }
          }

          if (
            this.endSelect.findIndex((item) => {
              if (item.code == this.startSelect[x].code) return true;
              item.code == this.startSelect[x].code;
            }) >= 0
          ) {
            this.endSelect.splice(
              this.endSelect.findIndex((item) => {
                if (item.code == this.startSelect[x].code) return true;
                item.code == this.startSelect[x].code;
              }),
              1
            );
          }
        }
      }
      this.startSelect = val;
      if (this.endSelect.length > 4) this.$message.error('比较数限制小于五个');
    },
    closeTags (index) {
      let t = this.select1.findIndex((item) => {
        if (item.code == this.endSelect[index].code) return true;
        item.code == this.endSelect[index].code;
      });

      let temp = [];
      for (let i = 0; i < this.endSelect.length; i++) {
        if (i != index) {
          temp.push(this.endSelect[i]);
        }
      }
      // console.log(this.endSelect);
      //   console.log(index);
      // console.log(temp);
      this.endSelect = temp;
      setTimeout(() => {
        if (t > -1) {
          this.$refs.multipleTables.toggleRowSelection(this.select1[t]);
        }
      }, 500);
    },
    // manager
    async delModel2 (scope) {
      let data = await TemplaterDel({ model_name: scope.row.model_name });
      if (data) {
        this.getModel();
      }
    },
    handleSizeChange2 (val) {
      this.pageSIze2 = val;
      this.currentPage2 = 1;
      this.handleCurrentChange2(1);
    },
    handleCurrentChange2 (val) {
      this.tableDataNow2 = this.tableData2.slice((val - 1) * this.pageSIze2, val * this.pageSIze2);
    },
    async changeSelect2 () {
      this.loadingSearch2 = true;
      let that = this;
      let data = await search_all({
        message: that.values2,
        flag: 2
      });
      if (data) {
        let temparr = [];
        for (let i = 0; i < data.data.length; i++) {
          if (data.data[i].flag == 'manager') {
            temparr.push(data.data[i]);
          }
        }
        // if(temparr.length > 0) {
        that.select1All2 = temparr;
        that.select12 = temparr.slice(0, 10);
        // }

        // //console.log(that.havefundmanager)
        that.loading = false;
      }
      this.loadingSearch2 = false;
    },
    addCompare2 () {
      this.showbox2 = true;
    },
    handleSelectionChange2 (val) {
      let that = this;
      if (this.select1All2.length > 0 && val.length > 0) {
        // 临时替代，课根据上一次的select1All发生变化了没来排除这个请客

        if (val.length >= this.startSelect2.length) {
          for (let i = 0; i < val.length; i++) {
            if (this.endSelect2.findIndex((item) => item.code == val[i].code) < 0) {
              this.endSelect2.push(val[i]);
            }
          }
        } else {
          let x = 0;
          let flags = false;
          for (let i = 0; i < this.startSelect2.length; i++) {
            if (val.findIndex((item) => item.code == this.startSelect2[i].code) < 0) {
              x = i;
            } else {
              flags = true;
            }
          }

          if (
            this.endSelect2.findIndex((item) => {
              if (item.code == this.startSelect2[x].code) return true;
              item.code == this.startSelect2[x].code;
            }) >= 0
          ) {
            this.endSelect2.splice(
              this.endSelect2.findIndex((item) => {
                if (item.code == this.startSelect2[x].code) return true;
                item.code == this.startSelect2[x].code;
              }),
              1
            );
          }
        }
      }
      this.startSelect2 = val;
      if (this.endSelect2.length > 4) this.$message.error('比较数限制小于五个');
    },
    closeTags2 (index) {
      let t = this.select12.findIndex((item) => {
        if (item.code == this.endSelect2[index].code) return true;
        item.code == this.endSelect2[index].code;
      });

      let temp = [];
      for (let i = 0; i < this.endSelect2.length; i++) {
        if (i != index) {
          temp.push(this.endSelect2[i]);
        }
      }
      // console.log(this.endSelect);
      //   console.log(index);
      // console.log(temp);
      this.endSelect2 = temp;
      setTimeout(() => {
        if (t > -1) {
          this.$refs.multipleTables2.toggleRowSelection(this.select12[t]);
        }
      }, 500);
    },
    clear2 () {
      this.startSelect2 = [];
      this.endSelect2 = [];
      this.$refs.multipleTables2.clearSelection();
    },
    async gotoCompare2 () {
      if (this.endSelect2.length <= 1 || this.endSelect2.length > 4) {
        this.$message.error('请选择对比项目大于一支且小于五支基金');
      } else {
        let tempcode = '';
        let tempname = '';
        let temptype = null;
        for (let i = 0; i < this.endSelect2.length; i++) {
          tempcode = tempcode + this.endSelect2[i].code + ',';
          tempname = tempname + this.endSelect2[i].name + ',';
        }
        tempcode = tempcode.slice(0, tempcode.length - 1);
        tempname = tempname.slice(0, tempname.length - 1);
        let data = await TypeMsg({ code: tempcode });
        if (data) {
          // //console.log(data)
          if (data.data) {
            if (data.data.length == 0) {
              this.$message.warning('无相同管理类型，请选择同类基金经理比较');
            } else if (data.data.length == 1) {
              temptype = data.data[0];
              if (temptype == 'equity') {
                this.$router.push({
                  path: '/managercompare',
                  query: {
                    id: tempcode,
                    type: temptype,
                    name: tempname
                  }
                });
              } else {
                this.$message('对于基金经理的比较我们只提供主动权益类型的比较，债券类型的比较以具体产品比较更优');
              }
            } else if (data.data.length > 1) {
              this.showitem = true;
              this.options = [];
              for (let i = 0; i < data.data.length; i++) {
                this.options.push({ value: data.data[i], label: data.data[i] });
              }
            }
          }
        }
      }
    },
    submitmodal () {
      if (this.endSelect2.length <= 1 || this.endSelect2.length > 4) {
        this.$message.error('请选择对比项目大于一支且小于五支基金');
      } else {
        let tempcode = '';
        let tempname = '';

        if (this.valuex == 'equity') {
          for (let i = 0; i < this.endSelect2.length; i++) {
            tempcode = tempcode + this.endSelect2[i].code + ',';
            tempname = tempname + this.endSelect2[i].name + ',';
          }
          tempcode = tempcode.slice(0, tempcode.length - 1);
          tempname = tempname.slice(0, tempname.length - 1);
          this.$router.push({
            path: '/managercompare',
            query: {
              id: tempcode,
              type: this.valuex,
              name: tempname
            }
          });
        } else {
          this.$message('对于基金经理的比较我们只提供主动权益类型的比较，债券类型的比较以具体产品比较更优');
        }
      }
    },
    rowClick2 (row, column) {
      if (column.property == 'id' || column.property == 'title' || column.property == 'model_description') {
        this.$router.push({
          path: '/managercompare',
          query: {
            id: row.model_name,
            type: row.type,
            name: row.model_description,
            model_flag: 1
          }
        });
      }
    },
    openCom (row) {
      this.$router.push({
        path: '/managercompare',
        query: {
          id: row.model_name,
          type: row.type,
          name: row.model_description,
          model_flag: 1
        }
      });
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () {
    this.getModel();
    // this.$refs.poollist.showtable()
  },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<!-- <style lang="scss" scoped>
.HolePageBox {
	/deep/ .el-tabs--border-card {
		border: 0px;
		-webkit-box-shadow: 0 0 0 0;
		box-shadow: 0 0 0 0;
	}
}
</style> -->
