// /api/taikang/exportData/getFieldList
import request from '@/utils/request';

const server = '/api/taikang';

// 获取投后数据导出字段列表
export function getFieldList(data) {
	return request({
		url: server + '/exportData/getFieldList',
		method: 'post',
		data
	});
}
// 导出数据-保存筛选条件模板
export function addFieldTemplate(data) {
	return request({
		url: server + '/exportData/addFieldTemplate',
		method: 'post',
		data
	});
}
// 导出数据-删除筛选条件模板
export function deleteFieldTemplate(data) {
	return request({
		url: server + '/exportData/deleteFieldTemplate',
		method: 'post',
		data
	});
}
// 导出数据-筛选条件模板列表查询
export function getFieldTemplateList(data) {
	return request({
		url: server + '/exportData/getFieldTemplateList',
		method: 'post',
		data
	});
}
// 自定义导出列模版列表查询
export function getCustomExportFieldTemplateList(data) {
	return request({
		url: server + '/exportData/getCustomExportFieldTemplateList',
		method: 'post',
		data
	});
}
// 自定义导出列模版保存
export function addCustomExportFieldTemplate(data) {
	return request({
		url: server + '/exportData/addCustomExportFieldTemplate',
		method: 'post',
		data
	});
}
// 自定义导出列模版删除
export function deleteCustomExportFieldTemplate(data) {
	return request({
		url: server + '/exportData/deleteCustomExportFieldTemplate',
		method: 'post',
		data
	});
}
// 投后数据导出Excel
export function exportToExcel(data) {
	return request({
		url: server + '/exportData/exportToExcel',
		method: 'post',
		data
	});
}
// 捕基能手基金准则列表查询
export function getMtyPeriodnameList(params) {
	return request({
		url: server + '/exportData/getMtyPeriodnameList',
		method: 'get',
		params
	});
}
// 捕基能手接口数据导出
export function mtyExportToExcel(data) {
	return request({
		url: server + '/exportData/mtyExportToExcel',
		method: 'post',
		data
	});
}
