<template>
	<div id="PBROEcharacteristics">
		<analysis-card-title title="交易股票估值-盈利特征统计" image_id="PBROEcharacteristics"></analysis-card-title>
		<div id="PBROEcharacteristicsMain">
			<div class="flex_start pb-20">
				<div
					v-for="(item, index) in data"
					:key="item.name"
					:class="index == 0 ? 'type_item' : 'type_item ml-20'"
					:style="rowStyle(item.name)"
				>
					<div style="position: absolute; z-index: 1; width: 194px">
						<div class="flex_start">
							<div class="flex_start type_item_name">
								<div class="mr-4">{{ item.name }}</div>
								<!-- <div><i class="el-icon-warning-outline"></i></div> -->
							</div>
						</div>
						<div class="type_item_descriptiom">{{ item.description }}</div>
						<div class="type_item_ratio" :style="fontStyle(item.name)">
							{{ fix2p(item.ratio) }}
						</div>
					</div>

					<div class="position_svg">
						<div v-if="svgShow(item.name) == 'red'">
							<svg width="62" height="60" viewBox="0 0 62 60" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path
									d="M23.5184 0.866046C22.9009 0.118895 21.7607 0.118896 21.1431 0.866046L0.359788 26.0113C-0.476818 27.0235 0.238789 28.558 1.54742 28.558H13.065V51.5031H31.5987V28.558H43.1141C44.4227 28.558 45.1384 27.0235 44.3018 26.0113L23.5184 0.866046Z"
									fill="url(#paint0_linear_5210_3661)"
								/>
								<path
									d="M40.0147 43.6058V43.3058H39.7147H31.5914C30.9226 43.3058 30.5569 42.5262 30.9844 42.0119L45.6428 24.3806C45.9584 24.001 46.5411 24.001 46.8567 24.3806L61.5151 42.0119C61.9426 42.5262 61.5769 43.3058 60.9081 43.3058H52.7864H52.4864V43.6058V59.3943H40.0147V43.6058Z"
									fill="url(#paint1_linear_5210_3661)"
									stroke="url(#paint2_linear_5210_3661)"
									stroke-width="0.6"
								/>
								<defs>
									<linearGradient
										id="paint0_linear_5210_3661"
										x1="22.3308"
										y1="0.305683"
										x2="22.3308"
										y2="51.5031"
										gradientUnits="userSpaceOnUse"
									>
										<stop stop-color="#FFDCDB" />
										<stop offset="1" stop-color="#FFE0DF" stop-opacity="0" />
									</linearGradient>
									<linearGradient
										id="paint1_linear_5210_3661"
										x1="46.2498"
										y1="23.7959"
										x2="46.2498"
										y2="59.6943"
										gradientUnits="userSpaceOnUse"
									>
										<stop stop-color="#FFBFBE" />
										<stop offset="1" stop-color="#FFCECD" stop-opacity="0" />
									</linearGradient>
									<linearGradient
										id="paint2_linear_5210_3661"
										x1="46.2498"
										y1="23.7959"
										x2="46.2498"
										y2="59.6943"
										gradientUnits="userSpaceOnUse"
									>
										<stop stop-color="#FFA19F" />
										<stop offset="0.741535" stop-color="#FFA6A5" stop-opacity="0" />
									</linearGradient>
								</defs>
							</svg>
						</div>
						<div v-else-if="svgShow(item.name) == 'green'">
							<svg width="62" height="60" viewBox="0 0 62 60" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path
									d="M23.5184 59.1346C22.9009 59.8818 21.7607 59.8818 21.1431 59.1346L0.359788 33.989C-0.476818 32.9768 0.238789 31.4423 1.54742 31.4423H13.065V8.49691H31.5987V31.4423H43.1141C44.4227 31.4423 45.1384 32.9768 44.3018 33.989L23.5184 59.1346Z"
									fill="url(#paint0_linear_5210_4037)"
								/>
								<path
									d="M40.0147 16.3937V16.6937H39.7147H31.5914C30.9226 16.6937 30.5569 17.4734 30.9844 17.9877L45.6428 35.6192C45.9584 35.9988 46.5411 35.9988 46.8567 35.6192L61.5151 17.9877C61.9426 17.4734 61.5769 16.6937 60.9081 16.6937H52.7864H52.4864V16.3937V0.604998H40.0147V16.3937Z"
									fill="url(#paint1_linear_5210_4037)"
									stroke="url(#paint2_linear_5210_4037)"
									stroke-width="0.6"
								/>
								<defs>
									<linearGradient
										id="paint0_linear_5210_4037"
										x1="22.3308"
										y1="59.695"
										x2="22.3308"
										y2="8.49691"
										gradientUnits="userSpaceOnUse"
									>
										<stop stop-color="#BAF4A2" />
										<stop offset="1" stop-color="#BAF4A2" stop-opacity="0" />
									</linearGradient>
									<linearGradient
										id="paint1_linear_5210_4037"
										x1="46.2498"
										y1="36.2039"
										x2="46.2498"
										y2="0.304998"
										gradientUnits="userSpaceOnUse"
									>
										<stop stop-color="#9AEB73" />
										<stop offset="1" stop-color="#9AEB73" stop-opacity="0" />
									</linearGradient>
									<linearGradient
										id="paint2_linear_5210_4037"
										x1="46.2498"
										y1="36.2039"
										x2="46.2498"
										y2="0.304998"
										gradientUnits="userSpaceOnUse"
									>
										<stop stop-color="#6CD241" />
										<stop offset="0.741535" stop-color="#6CD241" stop-opacity="0" />
									</linearGradient>
								</defs>
							</svg>
						</div>
					</div>
				</div>
			</div>
			<div class="flex_start">
				<div style="flex: 1" class="mr-20" v-loading="loading">
					<el-table :data="data" class="table" height="420px" ref="multipleTable" border header-cell-class-name="table-header">
						<el-table-column prop="name" label="类型" align="gotoleft"></el-table-column>
						<el-table-column prop="ratio" label="频率" align="gotoleft" sortable>
							<template slot-scope="{ row }">
								{{ fix2p(row.ratio) }}
							</template>
						</el-table-column>
						<el-table-column prop="description" label="说明" align="gotoleft"></el-table-column>
					</el-table>
				</div>
				<div style="width: 602px" v-if="showDescription">
					<analysis-description title="盈利特征统计" :description="description"></analysis-description>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import analysisDescription from '@/components/components/components/analysisDescription/index.vue';
import { filter_json_to_excel } from '@/utils/exportExcel.js';

// PB-ROE特征
import { getCharacteristics } from '@/api/pages/Analysis.js';
// PB-ROE特征
export default {
	name: 'PBROEcharacteristics',
	components: { analysisDescription },
	props: {
		showDescription: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			data: [],
			loading: true,
			info: {}
		};
	},
	computed: {
		description() {
			return `首先通过统计基金经理长期持股，对每只股票在出现在公告中和消失在公告中的这段区 间及附近时间进行统计，统计该股票的 roe 和 pe 的变化情况（采用二次多项式回归的方法 进行统计），将 roe 的变化区分为以下若干类别
			1）盈利提升； 2）杀盈利； 3）盈利稍向上； 4）盈利下行中预期或反转； 5）盈利上升中预期或反转； 6）盈利加速向下； 7）盈利缓慢向上； 8）盈利偏向下； 将 pe 的变化也划分为以下若干类：
			1）估值提升； 2）估值稍向上； 3）杀估值； 4）估值下行中预期反转； 5）估值提升； 6）估值上升中预期或反转； 7）估值加速向下； 8）估值偏向下； 同样对于该股票在统计区间收益>1%视为盈利，<-1%视为亏损； 对所有股票进行统计，最后计算各种归因的出现频率，视为结果。`;
		}
	},
	methods: {
		openvideo() {
			window.open('https://www.bilibili.com/video/BV1AU4y1B735?share_source=copy_web');
		},
		fix2p(val) {
			return val * 1 && !isNaN(val) ? (val * 100).toFixed(2) + '%' : '--';
		},
		// 获取PB-ROE特征数据
		async getCharacteristics() {
			this.loading = true;
			let data = await getCharacteristics({
				code: this.info.code,
				type: this.info.type,
				flag: this.info.flag,
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			this.loading = false;
			if (data?.mtycode == 200) {
				let other = data?.data
					.filter((v) => v.name == '其他')
					.map((v) => {
						return { ...v, description: v.name };
					});
				this.data = [
					...(data?.data
						.filter((v) => v.name != '其他')
						.map((v) => {
							return { ...v, description: v.name };
						})
						.slice(0, 6) || []),
					...other
				];
			}
		},
		// 获取数据
		getData(info) {
			this.info = info;
			this.getCharacteristics();
		},
		// 动态渲染行背景颜色
		rowStyle(name) {
			if (name.indexOf('击') !== -1 || name.indexOf('挣') !== -1 || name.indexOf('提升') !== -1) {
				return 'background: linear-gradient(90deg, #FFECE7 0%, rgba(255, 236, 231, 0.25) 100%);border-color:rgba(253, 104, 101, 1)';
			} else if (name.indexOf('杀') !== -1) {
				return 'background: linear-gradient(90deg, #E2F7E7 -0.07%, rgba(226, 247, 231, 0.25) 100%);border-color:rgba(56, 158, 13, 1)';
			} else {
				return 'background: linear-gradient(90deg, rgba(111, 128, 221, 0.20) 0%, rgba(111, 128, 221, 0.06) 100%);border-color:rgba(111, 128, 221, 1)';
			}
		},
		// 动态判断字体颜色
		fontStyle(name) {
			if (name.indexOf('击') !== -1 || name.indexOf('挣') !== -1 || name.indexOf('提升') !== -1) {
				return { color: 'rgba(207, 19, 34, 1)' };
			} else if (name.indexOf('杀') !== -1) {
				return { color: 'rgba(56, 158, 13, 1)' };
			} else {
				return { color: 'rgba(0, 0, 0, 0.85)' };
			}
		},
		// 动态判断svg显示
		svgShow(name) {
			if (name.indexOf('击') !== -1 || name.indexOf('挣') !== -1 || name.indexOf('提升') !== -1) {
				return 'red';
			} else if (name.indexOf('杀') !== -1) {
				return 'green';
			} else {
				return '';
			}
		},
		exportExcel() {
			let list = [
				{
					label: 'PB-ROE特征',
					value: 'description'
				},
				{
					label: '',
					value: 'ratio',
					format: 'fix2p'
				}
			];
			filter_json_to_excel(list, this.data, 'PB-ROE特征');
		},
		async createPrintWord(info) {
			await this.getData(info);
			return await new Promise((resolve, reject) => {
				this.$nextTick(async () => {
					let key = 'PBROEcharacteristicsMain';
					let height = document.getElementById(key).clientHeight;
					let width = document.getElementById(key).clientWidth;
					let canvas = await this.html2canvas(document.getElementById(key), {
						scale: 3
					});
					resolve([
						...this.$exportWord.exportTitle('交易股票估值-盈利特征统计'),
						...this.$exportWord.exportChart(canvas.toDataURL('image/jpg'), {
							width,
							height
						})
					]);
				});
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.type_item {
	position: relative;
	width: 218px;
	height: 104px;
	border-radius: 4px;
	padding: 12px;
	border: 1px solid #6f80dd;
	.type_item_name {
		color: rgba(0, 0, 0, 0.85);
		font-family: PingFang SC;
		font-size: 18px;
		font-style: normal;
		font-weight: 400;
		overflow: hidden;
		.mr-4 {
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
		}
	}
	.type_item_description {
		color: rgba(0, 0, 0, 0.45);
		font-family: PingFang SC;
		font-size: 12px;
		font-style: normal;
		font-weight: 400;
	}
	.type_item_ratio {
		color: rgba(0, 0, 0, 0.85);
		font-family: Helvetica Neue;
		font-size: 28px;
		font-style: normal;
		font-weight: 500;
	}
	.position_svg {
		position: absolute;
		right: 12px;
		top: 20px;
	}
}
</style>
