/* eslint-disable */
require('script-loader!file-saver');
// require('script-loader!@/Excel/Blob');
require('./Blob');
import * as XLSX2 from 'xlsx';
// require('script-loader!xlsx/dist/xlsx.core.min');
function generateArray(table) {
	var out = [];
	var rows = table.querySelectorAll('tr');
	var ranges = [];
	for (var R = 0; R < rows.length; ++R) {
		var outRow = [];
		var row = rows[R];
		var columns = row.querySelectorAll('td');
		for (var C = 0; C < columns.length; ++C) {
			var cell = columns[C];
			var colspan = cell.getAttribute('colspan');
			var rowspan = cell.getAttribute('rowspan');
			var cellValue = cell.innerText;
			if (cellValue !== '' && cellValue == +cellValue) cellValue = +cellValue;

			//Skip ranges
			ranges.forEach(function (range) {
				if (R >= range.s.r && R <= range.e.r && outRow.length >= range.s.c && outRow.length <= range.e.c) {
					for (var i = 0; i <= range.e.c - range.s.c; ++i) outRow.push(null);
				}
			});

			//Handle Row Span
			if (rowspan || colspan) {
				rowspan = rowspan || 1;
				colspan = colspan || 1;
				ranges.push({ s: { r: R, c: outRow.length }, e: { r: R + rowspan - 1, c: outRow.length + colspan - 1 } });
			}
			//Handle Value
			outRow.push(cellValue !== '' ? cellValue : null);

			//Handle Colspan
			if (colspan) for (var k = 0; k < colspan - 1; ++k) outRow.push(null);
		}
		out.push(outRow);
	}
	return [out, ranges];
}

function datenum(v, date1904) {
	if (date1904) v += 1462;
	var epoch = Date.parse(v);
	return (epoch - new Date(Date.UTC(1899, 11, 30))) / (24 * 60 * 60 * 1000);
}

function sheet_from_array_of_arrays(data, opts) {
	var ws = {};
	var range = { s: { c: 10000000, r: 10000000 }, e: { c: 0, r: 0 } };
	for (var R = 0; R != data.length; ++R) {
		for (var C = 0; C != data[R].length; ++C) {
			if (range.s.r > R) range.s.r = R;
			if (range.s.c > C) range.s.c = C;
			if (range.e.r < R) range.e.r = R;
			if (range.e.c < C) range.e.c = C;
			var cell = { v: data[R][C] };
			if (cell.v == null) continue;
			var cell_ref = XLSX2.utils.encode_cell({ c: C, r: R });

			if (typeof cell.v === 'number') cell.t = 'n';
			else if (typeof cell.v === 'boolean') cell.t = 'b';
			else if (cell.v instanceof Date) {
				cell.t = 'n';
				cell.z = XLSX2.SSF._table[14];
				cell.v = datenum(cell.v);
			} else cell.t = 's';

			ws[cell_ref] = cell;
		}
	}
	if (range.s.c < 10000000) ws['!ref'] = XLSX2.utils.encode_range(range);
	return ws;
}

function Workbook() {
	if (!(this instanceof Workbook)) return new Workbook();
	this.SheetNames = [];
	this.Sheets = {};
}

function s2ab(s) {
	var buf = new ArrayBuffer(s.length);
	var view = new Uint8Array(buf);
	for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xff;
	return buf;
}

export function export_table_to_excel(id) {
	var theTable = document.getElementById(id);
	console.log('a');
	var oo = generateArray(theTable);
	var ranges = oo[1];

	/* original data */
	var data = oo[0];
	var ws_name = 'SheetJS';
	console.log(data);

	var wb = new Workbook(),
		ws = sheet_from_array_of_arrays(data);

	/* add ranges to worksheet */
	// ws['!cols'] = ['apple', 'banan'];
	ws['!merges'] = ranges;

	/* add worksheet to workbook */
	wb.SheetNames.push(ws_name);
	wb.Sheets[ws_name] = ws;

	var wbout = XLSX2.write(wb, { bookType: 'xlsx', bookSST: false, type: 'binary' });

	saveAs(new Blob([s2ab(wbout)], { type: 'application/octet-stream' }), 'test.xlsx');
}

function formatJson(jsonData) {
	console.log(jsonData);
}
export function export_json_to_excel(th, jsonData, defaultTitle) {
	/* original data */

	var data = jsonData;
	data.unshift(th);
	var ws_name = 'SheetJS';

	var wb = new Workbook(),
		ws = sheet_from_array_of_arrays(data);

	/* add worksheet to workbook */
	wb.SheetNames.push(ws_name);
	wb.Sheets[ws_name] = ws;

	var wbout = XLSX2.write(wb, { bookType: 'xlsx', bookSST: false, type: 'binary' });
	var title = defaultTitle || '列表';
	saveAs(new Blob([s2ab(wbout)], { type: 'application/octet-stream' }), title + '.xlsx');
}

/**
 * 
 * @param {[[]]} multiHeader 
 * @param {[]} merges  // ['A1:A2', 'B1:F1', 'G1:G2'] excel内需要合并的格子
 * @param {[]} jsonData 
 * @param {''} defaultTitle 
 */
function calculateColumnWidths(data) {
  const columnWidths = [];

  data.forEach(row => {
      row.forEach((cell, idx) => {
          // 确保列索引存在
          if (!columnWidths[idx]) {
              columnWidths[idx] = { wch: 10 }; // 默认宽度
          }

          if (cell != null) {
              const cellStr = cell.toString();
              let cellWidth = 10; // 默认宽度

              // 判断是否包含中文字符
              if (/[⁨-⁵]/.test(cellStr)) {
                  cellWidth = cellStr.length * 2; // 中文字符宽度加倍
              } else {
                  cellWidth = cellStr.length;
              }

              // 更新最大宽度
              if (columnWidths[idx].wch < cellWidth) {
                  columnWidths[idx].wch = cellWidth;
              }
          }
      });
  });

  return columnWidths;
}
export function export_json_to_excel_multiHeader2(multiHeader, merges, data, defaultTitle = '列表') {
  // 1. 合并多重表头和数据
  const combinedData = [...multiHeader, ...data];

  // 2. 使用 aoa_to_sheet 创建工作表
  const ws = XLSX2.utils.aoa_to_sheet(combinedData);

  // 3. 处理合并单元格
  if (merges && merges.length > 0) {
      ws['!merges'] = (ws['!merges'] || []).concat(
          merges.map(range => XLSX2.utils.decode_range(range))
      );
  }

  // 4. 优化列宽计算
  const columnWidths = calculateColumnWidths(combinedData);
  ws['!cols'] = columnWidths;

  // 5. 创建工作簿并添加工作表
  const wb = XLSX2.utils.book_new();
  XLSX2.utils.book_append_sheet(wb, ws, 'Sheet1');

  // 6. 导出 Excel 文件
  const wbout = XLSX2.write(wb, { bookType: 'xlsx', type: 'array' });
  saveAs(new Blob([wbout], { type: 'application/octet-stream' }), `${defaultTitle}.xlsx`);
}
export function export_json_to_excel_multiHeader(multiHeader,merges, data, defaultTitle) {
 
	for (let i = multiHeader.length - 1; i > -1; i--) {
    data.unshift(multiHeader[i])
  }

	var ws_name = 'SheetJS';
	var wb = new Workbook(),
		ws = sheet_from_array_of_arrays(data);

	if (merges && merges.length > 0) {
		if (!ws['!merges']) ws['!merges'] = [];
		merges.forEach(item => {
			ws['!merges'].push(XLSX2.utils.decode_range(item))
		})
	}

	  // /*设置worksheet每列的最大宽度*/
    const colWidth = data.map(row => row.map(val => {
        /*先判断是否为null/undefined*/
        if (val == null) {
            return { 'wch': 10 };
        }
        /*再判断是否为中文*/
        else if (val.toString().charCodeAt(0) > 255) {
            return { 'wch': val.toString().length * 2 };
        } else {
            return { 'wch': val.toString().length };
        }
    }))
    /*以第一行为初始值*/
    let result = colWidth[0];
    for (let i = 1; i < colWidth.length; i++) {
        for (let j = 0; j < colWidth[i].length; j++) {
            if (result[j]['wch'] < colWidth[i][j]['wch']) {
                result[j]['wch'] = colWidth[i][j]['wch'];
            }
        }
    }
    ws['!cols'] = result;
 

	/* add worksheet to workbook */
	wb.SheetNames.push(ws_name);
	wb.Sheets[ws_name] = ws;

	var wbout = XLSX2.write(wb, { bookType: 'xlsx', bookSST: false, type: 'binary' });
	var title = defaultTitle || '列表';
	saveAs(new Blob([s2ab(wbout)], { type: 'application/octet-stream' }), title + '.xlsx');
}
//支持多sheet导出
//[{ th, jsonData, sheetName }]
export function export_json_to_excel2(sheetData = [], defaultTitle) {
	/* original data */
	let wb = new Workbook();
	sheetData.forEach((item) => {
		let { th, jsonData, sheetName } = item || {};
		let data = jsonData;
		data.unshift(th);
		let ws_name = sheetName || 'SheetJS';
		let ws = sheet_from_array_of_arrays(data);
		/* add worksheet to workbook */
		wb.SheetNames.push(ws_name);
		wb.Sheets[ws_name] = ws;
	});
	var wbout = XLSX2.write(wb, { bookType: 'xlsx', bookSST: false, type: 'binary' });
	var title = defaultTitle || '列表';
	saveAs(new Blob([s2ab(wbout)], { type: 'application/octet-stream' }), title + '.xlsx');
}
