<template>
	<div class="chart_one" v-loading="loading">
		<div style="margin-bottom: 24px">
			<div style="display: flex; justify-content: space-between; align-items: center">
				<div class="title" style="display: flex; align-items: center">
					分时段业绩表现
					<el-tooltip class="item" effect="dark" :content="EXPLAIN.performancePage['分时段业绩表现']" placement="right-start">
						<svg width="14" height="14" viewBox="0 0 14 14" fill="none">
							<path
								fill-rule="evenodd"
								clip-rule="evenodd"
								d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
								fill="black"
								fill-opacity="0.45"
							/>
						</svg>
					</el-tooltip>
					<i class="el-icon-video-camera-solid videoIconDes" @click="openvideo"></i>
				</div>
				<div>
					<span style="font-size: 14px"> 市场区间： </span>
					<el-select v-model="valuebench" @change="changeValuebench" placeholder="请选择市场区间">
						<el-option v-for="item in optionsbench" :key="item.value" :label="item.label" :value="item.value"> </el-option>
					</el-select>
					<el-button class="print_show" icon="el-icon-document-delete" @click="exportExcel" style="margin-left: 16px">导出Excel</el-button>
				</div>
			</div>
		</div>
		<el-table :data="timing" class="table" ref="multipleTable" header-cell-class-name="table-header">
			<el-table-column
				v-for="item in column"
				:key="item.value"
				align="gotoleft"
				:prop="item.value"
				:label="item.label"
				:show-overflow-tooltip="true"
				sortable
			>
				<template #header>
					<long-table-popover-chart
						v-if="item.popover"
						:data="formatTableData()"
						date_key="date"
						:data_key="item.value"
						:show_name="item.label"
					>
						<span>{{ item.label }}</span>
					</long-table-popover-chart>
					<span v-else>{{ item.label }}</span>
				</template>
				<template slot-scope="{ row }">
					<span
						v-if="item.value == 'ave_return' || item.value == 'excessreturn'"
						:style="row[item.value] * 1 > 0 ? 'color:red' : 'color:green'"
						>{{ item.format ? item.format(row[item.value]) : row[item.value] }}</span
					>
					<span v-else>{{ item.format ? item.format(row[item.value]) : row[item.value] }}</span>
				</template>
			</el-table-column>
			<template slot="empty">
				<el-empty image-size="160"></el-empty>
			</template>
		</el-table>
	</div>
</template>

<script>
import { exportTitle, exportTable } from '@/utils/exportWord.js';
// 分时段业绩表现
import { filter_json_to_excel } from '@/utils/exportExcel.js';

export default {
	name: 'timePhasedPerformance',
	data() {
		return {
			selectParting: [],
			timing: [],
			manageStart: '',
			manageEnd: '',
			showicon: false,
			optionsbench: [],
			valuebench: '',
			postData: {},
			loading: true,
			columnBond: [
				{
					label: '日期',
					value: 'date',
					format: this.repalce,
					popover: false
				},
				{
					label: '描述',
					value: 'description',
					popover: false
				},
				{
					label: 'alpha',
					value: 'alpha',
					format: this.fixp,
					popover: true
				},
				{
					label: '年化收益',
					value: 'ave_return',
					format: this.fix2b,
					popover: true
				},
				{
					label: '信用',
					value: 'credit',
					format: this.fix2b,
					popover: true
				},
				{
					label: '违约风险',
					value: 'defaultrisk',
					format: this.fix2b,
					popover: true
				},
				{
					label: '久期',
					value: 'duration',
					format: this.fix3,
					popover: true
				},
				{
					label: '权益',
					value: 'equity',
					format: this.fix2b,
					popover: true
				},
				{
					label: '利率',
					value: 'interest',
					format: this.fix2b,
					popover: true
				},
				{
					label: '最大回撤',
					value: 'maxdrawdown',
					format: this.fix2b,
					popover: true
				},
				{
					label: '夏普率',
					value: 'sharpe0',
					format: this.fix3,
					popover: true
				}
			],
			columnEquity: [
				{
					label: '日期',
					value: 'date',
					format: this.repalce,
					popover: false
				},
				{
					label: '描述',
					value: 'description',
					popover: false
				},
				{
					label: 'alpha',
					value: 'alpha',
					format: this.fixp,
					popover: true
				},
				{
					label: '修正alpha',
					value: 'alpha_explain',
					format: this.fix3_2,
					popover: true
				},
				{
					label: 'smart贝塔',
					value: 'smartbeta',
					format: this.fixp,
					popover: true
				},
				{
					label: '贝塔',
					value: 'beta',
					format: this.fix3_2,
					popover: true
				},
				{
					label: '仓位',
					value: 'beta_explain',
					format: this.fix2b,
					popover: true
				},
				{
					label: '年化超额',
					value: 'excessreturn',
					format: this.fixp,
					popover: true
				},
				{
					label: '最大回撤比',
					value: 'maxdrawback',
					format: this.fixp,
					popover: true
				},
				{
					label: '夏普率',
					value: 'sharpe',
					format: this.fix3_2,
					popover: true
				},
				{
					label: '信息率',
					value: 'info',
					format: this.fix3_2,
					popover: true
				}
			],
			column: [],
			allData: []
		};
	},
	methods: {
		// 监听排序
		sortChange(val) {
			if (val.order) {
				this.timing.sort((a, b) => {
					if (val.prop) {
						if (a[val.prop].indexOf('%') != -1) {
							return val.order == 'descending'
								? b[val.prop].split('%')[0] * 1 - a[val.prop].split('%')[0] * 1
								: val.order == 'ascending'
								? a[val.prop].split('%')[0] * 1 - b[val.prop].split('%')[0] * 1
								: 1;
						} else {
							return val.order == 'descending'
								? b[val.prop] * 1 - a[val.prop] * 1
								: val.order == 'ascending'
								? a[val.prop] * 1 - b[val.prop] * 1
								: 1;
						}
					}
				});
			} else {
				this.timing = this.allData;
			}
		},
		openvideo() {
			window.open('https://www.bilibili.com/video/BV12e4y1R7tj?share_source=copy_web');
		},
		getBenchmarkList(data) {
			this.optionsbench = data;
			this.valuebench = data?.[0]?.value;
			this.postData.periodname = this.valuebench;
			this.$emit('resolveFather', this.postData);
		},
		formatTableData() {
			let data = [];
			this.timing.map((item) => {
				let obj = { ...item };
				for (const key in item) {
					let format = this.column.find((obj) => {
						return obj.value == key;
					})?.format;
					if (format) {
						let val = format(item[key]);
						obj[key] = typeof val == 'string' ? (val.includes('%') ? val?.split('%')?.[0] * 1 : !isNaN(val) ? val * 1 : val) : val;
					}
				}
				data.push(obj);
			});
			return data;
		},
		// 获取数据
		getData(data, info, callback) {
			if (info.type.indexOf('equity') != -1) {
				this.column = this.columnEquity;
			} else {
				this.column = this.columnBond;
			}
			this.loading = false;
			this.timing = data;
			this.allData = [...this.timing];
		},
		// 监听基准选择
		changeValuebench() {
			this.loading = true;
			this.postData.periodname = this.valuebench;
			this.$emit('resolveFather', this.postData);
		},
		// 监听时间选择
		changeParting() {
			this.loading = true;
			this.postData.start_date = this.selectParting?.[0];
			this.postData.end_date = this.selectParting?.[1];
			this.$emit('resolveFather', this.postData);
		},
		fix3(value) {
			return value * 1 ? Number(value).toFixed(3) : '--';
		},
		repalce(value) {
			return value.replace('::', '~');
		},
		fix2b(value) {
			return value * 1 ? (value * 100).toFixed(2) + '%' : '--%';
		},
		fix3_2(value) {
			return value * 1 ? Number(value).toFixed(3) : '--';
		},
		repalce(value) {
			return value.replace('::', '~');
		},
		fixp(value) {
			return value * 1 ? value.toFixed(2) + '%' : '--%';
		},
		// 导出excel
		exportExcel() {
			let list = this.column.map((item) => {
				return {
					...item,
					format: ''
				};
			});
			filter_json_to_excel(list, this.timing, '分时段业绩表现');
		},
		// 导出
		createPrintWord() {
			let list = this.column.map((item) => {
				return {
					...item,
					format: ''
				};
			});
			let data = this.timing.map((item) => {
				let obj = {};
				for (const key in item) {
					let index = this.column.findIndex((v) => v.value == key);
					if (this.column[index]?.format) {
						obj[key] = this.column[index]?.format(item[key]);
					} else {
						obj[key] = item[key];
					}
				}
				return obj;
			});
			if (this.timing.length) {
				return [...exportTitle('分时段业绩表现'), ...exportTable(list, data, '', data?.length > 5 ? true : false)];
			} else {
				return [];
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.parting-header {
	display: flex;
	align-items: gotoleft;
	justify-content: space-between;

	.filter-box {
		display: flex;
		align-items: gotoleft;
		.filter-box-item {
			margin-left: 20px;
			display: inline-block;
		}
	}
}
</style>
