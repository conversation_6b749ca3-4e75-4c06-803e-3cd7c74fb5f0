<template>
  <div class="pool_detail_analysis">
    <div>
      <div class="flex_card"
           style="overflow-x: hidden;">
        <!-- <div> -->
        <component v-for="item in templateList"
                   :key="item.value"
                   v-show="item.isshow"
                   :class="item.type"
                   :is="item.is"
                   :ref="item.value"
                   @resolveFather="item.method"
                   v-loading="loading"
                   @hideComponents="hideComponents"></component>
        <!-- </div> -->
      </div>
    </div>
  </div>
</template>

<script>
// 子池
import categraydetail from "@/pages/fundNewPool/analysis/components/components/categraydetail.vue";
// 指标选择器
import targetChoose from "@/pages/fundNewPool/analysis/components/components/targetChoose.vue";
// 相关矩阵
import correlationMatrix from "@/pages/fundNewPool/analysis/components/components/correlationMatrix.vue";
// 相关系数
import correlationCoefficient from "@/pages/fundNewPool/analysis/components/components/correlationCoefficient.vue";
// alpha/beta
import alphabeta from "@/pages/fundNewPool/analysis/components/components/alphabeta.vue";
// 波动收益
import volility from "@/pages/fundNewPool/analysis/components/components/volility.vue";
// 资产配置分析
import assetAllocationAnalysis from "@/pages/fundNewPool/analysis/components/components/assetAllocationAnalysis.vue";
// 持仓分析
import holdAnalysis from "@/pages/fundNewPool/analysis/components/components/holdAnalysis.vue";
export default {
  components: {
    categraydetail,
    targetChoose,
    correlationMatrix,
    correlationCoefficient,
    alphabeta,
    volility,
    assetAllocationAnalysis,
    holdAnalysis
  },
  props: {
    ismanager: {
      type: Boolean
    }
  },
  data () {
    return {
      templateList: [
        {
          name: "子池",
          is: "categraydetail",
          value: "categraydetail",
          type: "big_template",
          isshow: true,
          getData: "getCategraydetailData",
          getRequestData: "getCategraydetail"
        },
        {
          name: "指标选择器",
          is: "targetChoose",
          value: "targetChoose",
          type: "big_template",
          isshow: true,
          method: this.poolCreated,
          getData: "getTargetChooseData",
          getRequestData: "getTargetChoose"
        },
        {
          name: "相关矩阵",
          is: "correlationMatrix",
          value: "correlationMatrix",
          type: "small_template",
          isshow: true,
          getData: "getCorrelationMatrixData",
          getRequestData: "getCorrelationMatrix"
        },
        {
          name: "相关系数",
          is: "correlationCoefficient",
          value: "correlationCoefficient",
          type: "big_template",
          isshow: true,
          getData: "getCorrelationCoefficientData",
          getRequestData: "getCorrelationCoefficient"
        },
        {
          name: "波动收益",
          is: "volility",
          value: "volility",
          type: "small_template",
          isshow: true,
          getData: "getVolilityData",
          getRequestData: "getVolility"
        },
        {
          name: "alpha/beta",
          is: "alphabeta",
          value: "alphabeta",
          type: "small_template",
          isshow: true,
          getData: "getAlphabetaData",
          getRequestData: "getAlphabeta"
        },
        {
          name: "资产配置分析",
          is: "assetAllocationAnalysis",
          value: "assetAllocationAnalysis",
          type: "big_template",
          isshow: true,
          getData: "getAssetAllocationAnalysisData",
          getRequestData: "getAssetAllocationAnalysis"
        },
        {
          name: "持仓分析",
          is: "holdAnalysis",
          value: "holdAnalysis",
          type: "",
          isshow: true,
          getData: "getHoldAnalysisData",
          getRequestData: "getHoldAnalysis"
        }
      ],
      info: {}
    };
  },
  methods: {
    poolCreated () {
      this.$refs["categraydetail"]?.[0].getData(this.info);
    },
    getData (info) {
      this.info = info;
      this.$refs["categraydetail"]?.[0].getData(this.info);
      this.$refs["targetChoose"]?.[0].getData(this.info);
      if (this.info.code_list.length > 10) {
        this.hideComponents("correlationMatrix");
        this.$refs["correlationCoefficient"]?.[0].getData(this.info);
      } else {
        this.hideComponents("correlationCoefficient");
        if (this.info.code_list.length > 2) {
          this.$refs["correlationMatrix"]?.[0].getData(this.info.code, 5);
        } else {
          this.hideComponents("correlationMatrix");
        }
      }
      this.$refs["volility"]?.[0].getData(this.info);
      this.$refs["alphabeta"]?.[0].getData(this.info);
      this.$refs["assetAllocationAnalysis"]?.[0].getData(this.info);
      this.$refs["holdAnalysis"]?.[0].getData(this.info);
    },
    // 隐藏模块
    hideComponents (value) {
      let index = this.templateList.findIndex(v => v.value == value);
      this.$set(this.templateList, index, {
        ...this.templateList[index],
        isshow: false
      });
    },
    // 接收关注基金变化
    changeCareList (info) {
      this.info = info;
      this.$refs["targetChoose"]?.[0].refresInfo(this.info);
      this.$refs["volility"]?.[0].refresInfo(this.info);
      this.$refs["alphabeta"]?.[0].refresInfo(this.info);
      this.$refs["assetAllocationAnalysis"]?.[0].refresInfo(this.info);
      this.$refs["holdAnalysis"]?.[0].changeCareList(this.info);
    },
    getAlphabeta (info) {
      this.info = info;
      this.$refs["alphabeta"]?.[0].getData(this.info);
    },
    getCategraydetail () {
      this.$refs["categraydetail"]?.[0].getData(this.info);
    }
  }
};
</script>

<style lang="scss" scoped>
.pool_detail_analysis {
	.small_template {
		height: 488px;
	}
}
</style>
