<template>
	<div class="manager_resume">
		<div class="resume_title mb-8">个人简历</div>
		<div class="resume_resume">
			{{ resumeString }}
			<div class="resume_resume_more" @click="openMoreResume">...更多</div>
		</div>
		<honrous-dialog ref="honrousDialog"></honrous-dialog>
	</div>
</template>

<script>
import { getBasicInfo } from '@/api/pages/Analysis.js';
import honrousDialog from './honrousDialog.vue';
export default {
	components: { honrousDialog },
	data() {
		return {
			info: {},
			resume: [
				'先后在上海德锦投资有限责任公司、上海申银万国证券研究所有限公司、华宝信托有限责任公司、中银国际证券有限责任公司工作，历任项目经理、研究员、投资经理、投资总监等',
				'自2015年4月进入万家基金管理有限公司，现任公司副总经理、投资总监、基金经理',
				'自2020年9月23日起，担任万家精选混合型证券投资基金的基金经理',
				'自2020年9月23日至2021年10月12日，担任万家瑞兴灵活配置混合型证券投资基金的基金经理',
				'自2020年9月23日起，担任万家新利灵活配置混合型证券投资基金的基金经理',
				'自2020年9月23日起，担任万家宏观择时多策略灵活配置混合型证券投资基金的基金经理'
			]
		};
	},
	computed: {
		resumeString() {
			return this.resume.join(';');
		}
	},
	methods: {
		getData(info) {
			this.info = info;
			this.getBasicInfo();
		},
		async getBasicInfo() {
			let data = await getBasicInfo({ code: this.info.code, type: this.info.type, flag: this.info.flag });
			if (data?.mtycode == 200) {
				this.resume = data.data?.bio?.split('。');
			}
		},
		openMoreResume() {
			this.$refs['honrousDialog'].getData(this.resume, '个人简介');
		},
		createPrintWord() {
			return this.$exportWord.exportDescripe(this.resumeString);
		}
	}
};
</script>

<style lang="scss" scoped>
.manager_resume {
	border-radius: 0 0 4px 4px;
	background: #ecf5ff;
	padding: 12px;
	border-top: 1px dashed #d9d9d9;
	.resume_title {
		color: rgba(0, 0, 0, 0.85);
		font-family: PingFang SC;
		font-size: 14px;
		font-style: normal;
		font-weight: 500;
	}
	.resume_resume {
		position: relative;
		overflow: hidden;
		color: rgba(0, 0, 0, 0.65);
		text-overflow: ellipsis;
		// white-space: nowrap;
		word-break: break-all;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		font-family: PingFang SC;
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
	}
	.resume_resume_more {
		// content: '...更多';
		cursor: pointer;
		text-align: right;
		position: absolute;
		bottom: -1px;
		right: 2px;
		width: 42px;
		height: 22px; // 这取决于你的行高
		background-color: #ecf5ff; // 这应与容器的背景色相匹配
		color: #4096ff;
		// pointer-events: none; // 确保这个元素不会影响文字的交互
	}
}
</style>
