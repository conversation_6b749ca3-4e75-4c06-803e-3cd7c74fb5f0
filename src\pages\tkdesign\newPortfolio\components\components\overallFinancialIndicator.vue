<template>
    <div class="plate-wrapper fund-performance-board-wrapper">
        <combinationComponentHeader title="整体财务指标" showMoreBtn @download="exportExcel">
            <template slot="right">
                持仓类型：<el-select v-model="form.positionType" placeholder="选择比较基准" style="margin-right: 12px;" @change="selectChange">
                    <el-option
                        v-for="item in options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                        </el-option>
                </el-select>
                <div style="margin-right: 12px;">
                    考察指标：<el-select v-model="form.index" placeholder="选择比较基准" style="margin-right: 12px;" @change="selectChange">
                    <el-option
                        v-for="item in options2"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                        </el-option>
                </el-select>
                    </div>
                    <div style="margin-right: 12px;">
                        时间区间：<el-select v-model="form.date" placeholder="选择比较基准" style="margin-right: 12px;" @change="selectChange">
                    <el-option
                        v-for="item in options3"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                        </el-option>
                </el-select>
                    </div>
            </template>
        </combinationComponentHeader>
  
        <div style="display: flex;justify-content: space-between;gap:12px;align-items: center;margin-top: 20px;">
            <div class="right" style="flex: 1;">
                <lineChartForFinancial ref="fund-performance-board-chart-container2" @tableData="getTableData"></lineChartForFinancial>
            </div>
        </div>
    </div>
</template>
<script>
import combinationComponentHeader from './combinationComponentHeader.vue';
import lineChartForFinancial from '../chart/lineChartForFinancial.vue';
import { filter_to_excel } from "@/utils/exportExcel.js";
const dayjs = require('dayjs');
export default {
    name:'overallFinancialIndicator',
    components:{
        combinationComponentHeader,
        lineChartForFinancial,
    },
    data(){
        return {
            form:{
                positionType:'big',
                index: 'pe',
                date:1,
                startDate: dayjs().subtract(1, 'year').format('YYYY-MM-DD'),
				endDate: dayjs().format('YYYY-MM-DD')
            },
            options:[{
                label:'前十大',
                value: 'big'
            },
            {
                label:'全持仓',
                value: 'all'
            }],
            options2:[{
                label:'PE',
                value: 'pe'
            },
            {
                label:'PB',
                value: 'pb'
            },{
                label:'ROE',
                value: 'roe'
            },{
                label:'净利润增长率',
                value: 'netIncomeYoy'
            },{
                label:'股息率',
                value: 'yield'
            },{
                label:'营业收入同比增长',
                value: 'incomeYoy'
            }],
            options3:[{
                label:'近一年',
                value: 1
            },
            {
                label:'近三年',
                value: 2
            },{
                label:'近五年',
                value: 3
            }],
            param:null,
            tableHeader:[{
                prop:'date',
                label: '日期',
            },
            {
                prop: '财务指标',
                label: '财务指标',
            },
            {
                prop: '沪深',
                label: '沪深',
            }],
            tableData:[]
        }
    },
    methods:{
        selectChange(){
            this.form.startDate = dayjs().subtract(this.form.date, 'year').format('YYYY-MM-DD');
            this.getData(this.param)
        },
        getData(param){
            this.param = param;
            let chartDom3 = this.$refs['fund-performance-board-chart-container2'];
            chartDom3?.getData({
                ...param,
                ...this.form
            });
        },
          // 获取表格数据的方法
       getTableData(val) {
        this.tableHeader[2] = {
            prop: val.name,
            label: val.name,
        }
         
         // 遍历图例列表，为每个图例创建一个表格行
         val.dateList.forEach((item, index) => {
           this.tableData.push({
            date: item, // 将图例名称作为表格行的 name 属性
            '财务指标':val.data1[index],
            [val.name]:val.data2[index],
           });
          
         });
       },
       exportExcel(){
          // 将表头数据进行遍历，生成新的数组list，每个元素包含原表头数据和format字段
          let list = this.tableHeader.map((item) => {
            return {
              ...item,
              format: ''
            };
          });
          // 调用filter_to_excel函数，传入list、表格数据this.tableData和文件名'基金标签'
          filter_to_excel(list, this.tableData, '整体财务指标');
        },
    },
}
</script>
<style lang="scss" scoped>
.fund-performance-board-wrapper {
    .select-form-wrapper {
        margin-bottom: 16px;
    }
    .content-table-wrapper {
        margin-bottom: 32px;
    }
}

</style>