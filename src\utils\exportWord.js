import {
	Document,
	Paragraph,
	TextRun,
	BorderStyle,
	Table,
	TableCell,
	TableRow,
	AlignmentType,
	HeadingLevel,
	Packer,
	UnderlineType,
	Header,
	Footer,
	WidthType,
	ShadingType,
	ImageRun,
	VerticalAlign,
	TextWrappingType,
	TextWrappingSide,
	LineRuleType
} from 'docx';
import { saveAs } from 'file-saver';
export class Format {
	// constructor() {}
	// 判断--
	fix(value) {
		return value ? value : '--';
	}
  fixE0(value) {
    if(value===0) return 0
    else  return value ? value : '--';
	}
	fixb0(value) {
		return (value ? value : '0') + '%';
	}
	// 添加百分号(%)
	fixb(value) {
		return (value ? value : '--') + '%';
	}
	// 百分化取整数
	fixp(value) {
		return (value == '--' ? value : Math.ceil(value * 100)) + '%';
	}
	// 取两位小数
	fix2(value) {
		return value == '--' ? value : Number(value).toFixed(2);
	}
	// 取两位小数数据
	fix2b(value) {
		if (value === '--' || isNaN(value)) {
			return '--';
		}
		return (value ? Number(value).toFixed(2) : '--') + '%';
	}
	// 百分化取两位小数数据
	fix2p(value) {
		if (isNaN(value)) return '';
		return (Number(value) * 100 ? (Number(value) * 100).toFixed(2) : '--') + '%';
	}
	fix4p(value) {
		return (value * 100 ? (value * 100).toFixed(4) : '--') + '%';
	}
	// 百分化取两位小数数据，无符号
	fix2pns(value) {
		return value * 100 ? (value * 100).toFixed(2) : '0';
	}
	// 元
	fix2m(value) {
		return (Number(value) ? value.toFixed(2) : '--') + '元';
	}
	// 取三位小数
	fix3(value) {
		return value == '--' ? value : Number(value).toFixed(3);
	}
	// 取四位小数
	fix4(value) {
		return value == '--' ? value : Number(value).toFixed(4);
	}
	// 取五位小数
	fix5(value) {
		return value == '--' ? value : Number(value).toFixed(5);
	}
	// 取六位小数
	fix6(value) {
		return value == '--' ? value : Number(value).toFixed(6);
	}
	// 亿元
	fix8(val) {
		return (val / 10 ** 8 ? (val / 10 ** 8).toFixed(2) : '--') + '亿元';
	}
	fix8fix4ns(val) {
		return val / 10 ** 8 ? (val / 10 ** 8).toFixed(4) : '--';
	}
	// 亿元，无单位
	fix8ns(val) {
		return val / 10 ** 8 ? (val / 10 ** 8).toFixed(2) : '0';
	}
	// 万元，无单位
	fix4ns(val) {
		return val / 10 ** 4 ? (val / 10 ** 4).toFixed(2) : '0';
	}
	// 取三位小数
	fix3b(value) {
		return (value == '--' ? value : Number(value).toFixed(3)) + '%';
	}
	// 特殊格式处理
	repalce(value) {
		return value.replace('::', '~');
	}
	// 格式化时间格式
	timeFix(val) {
		let timeList = val?.slice(0, 10)?.split('-');
		let time = timeList?.[0] + '年' + timeList?.[1] + '月' + timeList?.[2] + '日';
		// let year = new Date().getFullYear()
		return time;
	}
	// 格式化规模数据
	ficNetasset(val) {
		if (val) {
			return (val == '--' ? val : Number(val)?.toFixed(2)) + '亿';
		} else {
			return '--亿';
		}
	}
	// 格式化规模数据
	fixNet(val) {
		if (val) {
			return (val == '--' ? val : Number(val / 10 ** 8)?.toFixed(2)) + '亿';
		} else {
			return '--亿';
		}
	}
	// 转化为string类型
	returnDefault(val) {
		if (typeof val == 'string') {
			return val;
		} else {
			return JSON.stringify(val);
		}
	}
	// 基金经理管理时间
	mange(val) {
		return val == 'null' ? '至今' : val;
	}
	// 基金经理名字
	managername(val) {
		let nameList = val?.map((item) => {
			return item.name;
		});
		return nameList.length > 0 ? nameList.join(',') : nameList?.[0]?.name;
	}
	// 除以一亿
	fixY(value) {
		if (value == '--') return value;
		else {
			return (Number(value) / 100000000).toFixed(2) + '亿';
		}
	}
}
var format = new Format();
class TableFill {
	returnColor(item, obj) {
		if (obj?.fill == 'header') {
			return 'ffffff';
		} else if (obj?.fill == 'red_or_green') {
			if (item[obj.value] > 0) {
				return 'F5DADA';
			} else if (item[obj.value] < 0) {
				return 'B0DAB4';
			}
		} else {
			return 'FFFFFF';
		}
	}
	returnCell(item, obj, style) {
		if (obj?.type == 'image') {
			return [
				new Paragraph({
					children: [
						new ImageRun({
							data: Uint8Array.from(atob(item[obj.value].split(',')[1]), (c) => c.charCodeAt(0)),
							transformation: {
								width: 150,
								height: (150 / style.width) * style.height
							}
						})
					],
					alignment: AlignmentType.CENTER
				})
			];
		} else if (obj?.type == 'six_image') {
			return [
				new Paragraph({
					children: [
						new ImageRun({
							data: Uint8Array.from(atob(item[obj.value].split(',')[1]), (c) => c.charCodeAt(0)),
							transformation: {
								width: 200,
								height: (200 / style.width) * style.height
							}
						})
					],
					alignment: AlignmentType.CENTER
				})
			];
		} else {
			return [
				new Paragraph({
					text: obj.format ? format[obj.format](item[obj.value]) : format.returnDefault(item[obj.value]),
					alignment: AlignmentType.CENTER
				})
			];
		}
	}
	returnRowSpan({ spanObj, index, i }) {
		if (spanObj?.length) {
			let booleanIndex = spanObj.findIndex((item) => {
				return item.row == index && item.cell == i;
			});
			if (booleanIndex != -1) {
				return spanObj[booleanIndex].rowSpan;
			} else {
				return 1;
			}
		} else {
			return 1;
		}
	}
}
var tableFill = new TableFill();
function isWindows() {
	var agent = navigator.userAgent.toLowerCase();
	// var isMac = /macintosh|mac os x/i.test(navigator.userAgent); // mac
	if (agent.indexOf('win32') >= 0 || agent.indexOf('wow32') >= 0) {
		return true;
	}
	if (agent.indexOf('win64') >= 0 || agent.indexOf('wow64') >= 0) {
		return true;
	}
	return false;
}
var current_date = '';
var current_benchmark = '';
// 基金基础信息
export function exportFundBasicInfo(info, data) {
	current_date = data?.date;
	current_benchmark = data?.benchmarkDetail;
	return [
		new Paragraph({
			text: `基金深度分析报告:${info.name}`,
			heading: HeadingLevel.TITLE,
			shading: {
				fill: '333399',
				type: ShadingType.CLEAR, // 纯色背景
				color: 'FFFFFF'
			}
		}),
		new Paragraph({
			children: [
				// new TextRun({ text: `${info.code}` }),
				// new TextRun({ text: `\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t` }),
				new TextRun({ text: `${format.timeFix(data?.date)}` })
			],
			heading: HeadingLevel.HEADING_2,
			shading: {
				fill: '333399',
				type: ShadingType.CLEAR, // 纯色背景
				color: 'FFFFFF'
			}
		}),
		...exportFirstTitle('一、基金一页通'),
		...exportDescripe('数据截止日期:' + current_date + '; 对标基准:' + current_benchmark)
	];
}
// 基金经理基础信息
export function exportManagerInfo(info) {
	// if (isWindows()) {
	return [
		new Paragraph({
			text:
				'基金经理深度分析报告: ' +
				// data?.jigou
				// 	.replace('基金管理有限公司', '基金')
				// 	.replace('基金管理有限责任公司', '基金')
				// 	.replace('基金管理股份有限公司', '基金')
				// 	.replace('基金管理有限公司', '基金')
				// 	.replace('有限公司', '')
				// 	.replace('有限责任公司', '')
				// 	.replace('资产管理', '资管') +
				info.name,
			heading: HeadingLevel.TITLE,
			shading: {
				fill: '333399',
				type: ShadingType.CLEAR, // 纯色背景
				color: 'FFFFFF'
			}
		}),
		new Paragraph({
			text: new Date().getFullYear() + '年' + (new Date().getMonth() * 1 + 1) + '月' + new Date().getDate() + '日',
			heading: HeadingLevel.HEADING_2,
			shading: {
				fill: '333399',
				type: ShadingType.CLEAR, // 纯色背景
				color: 'FFFFFF'
			}
		}),
		...exportFirstTitle('一、基金经理一页通')
	];
}
// 组合基础信息
export function exportPortfolioInfo(info, time) {
	return [
		new Paragraph({
			text: `${info.name}分析报告:`,
			heading: HeadingLevel.TITLE,
			shading: {
				fill: '333399',
				type: ShadingType.CLEAR, // 纯色背景
				color: 'FFFFFF'
			}
		}),
		new Paragraph({
			children: [new TextRun({ text: `调仓日期${time}` })],
			heading: HeadingLevel.HEADING_2,
			shading: {
				fill: '333399',
				type: ShadingType.CLEAR, // 纯色背景
				color: 'FFFFFF'
			}
		}),
		...exportFirstTitle('一、组合一页通')
	];
}

// 下载word
export function downloadWord(info, children, img) {
	children.push(
		...[
			new Paragraph({
				shading: {
					fill: 'FFFFFF',
					type: ShadingType.CLEAR, // 纯色背景
					color: 'FFFFFF'
				}
			}),
			new Paragraph({
				shading: {
					fill: 'FFFFFF',
					type: ShadingType.CLEAR, // 纯色背景
					color: 'FFFFFF'
				}
			}),
			new Paragraph({
				shading: {
					fill: 'FFFFFF',
					type: ShadingType.CLEAR, // 纯色背景
					color: 'FFFFFF'
				}
			})
		]
	);
	if (
		info?.custom_description?.length &&
		info?.custom_description?.every((item) => {
			return item.title != '' && item.content != '';
		})
	) {
		info?.custom_description.map((item) => {
			children.push(
				...exportDescripe(item.title),
				new Paragraph({
					shading: {
						fill: 'FFFFFF',
						type: ShadingType.CLEAR, // 纯色背景
						color: 'FFFFFF'
					}
				}),
				...exportDescripe(item.content, '000000'),
				new Paragraph({
					shading: {
						fill: 'FFFFFF',
						type: ShadingType.CLEAR, // 纯色背景
						color: 'FFFFFF'
					}
				})
			);
		});
	} else {
		children.push(
			...exportDescripe('重要声明'),
			new Paragraph({
				shading: {
					fill: 'FFFFFF',
					type: ShadingType.CLEAR, // 纯色背景
					color: 'FFFFFF'
				}
			}),
			...exportDescripe(
				'本报告由慧捕基（简称“慧捕基”）编制，旨为发给慧捕基特定客户及其他专业人士内部参考。本报告基于合法取得的信息，但慧捕基对这些信息的准确性和完整性不作任何保证。本报告所包含的分析基于各种假设，不同假设可能导致分析结果出现重大不同。在任何情况下，本报告中的信息或所表述的意见并不构成对任何人的投资建议。除法律或规则规定必须承担的责任外，慧捕基不对使用本报告及其内容所引发的任何直接或间接损失负任何责任。本报告版权归慧捕基所有。未经慧捕基事先书面许可，任何机构和个人均不得以任何形式翻版、复制、引用、转载或其他任何形式的流传，否则，慧捕基将保留随时追究其法律责任的权利。',
				'000000'
			),
			new Paragraph({
				shading: {
					fill: 'FFFFFF',
					type: ShadingType.CLEAR, // 纯色背景
					color: 'FFFFFF'
				}
			}),
			...exportDescripe('风险提示'),
			new Paragraph({
				shading: {
					fill: 'FFFFFF',
					type: ShadingType.CLEAR, // 纯色背景
					color: 'FFFFFF'
				}
			}),
			...exportDescripe(
				'市场有风险，投资需谨慎。在进行基金投资前，请参阅基金的《基金合同》、《招募说明书》等法律文件。本资料仅供特定客户内部使用，不作为任何宣传推介、投资建议或保证，以及法律文件。基金管理人承诺以诚实信用、勤勉尽责的原则管理和运作基金资产，但不保证基金一定盈利，也不保证最低收益，同时基金的过往业绩及其净值高低并不预示其未来业绩表现，基金管理人管理的其他基金的业绩并不构成基金业绩表现的保证，投资者应自主做出投资决策并自行承担投资风险。',
				'000000'
			)
		);
	}

	const doc = new Document({
		creator: 'Clippy',
		title: 'Sample Document',
		description: 'A brief example of using docx',
		styles: {
			paragraphStyles: [
				{
					id: 'Title',
					name: 'Title',
					basedOn: 'Normal',
					next: 'Normal',
					quickFormat: true,
					run: {
						size: 28,
						bold: true,
						font: '微软雅黑',
						color: 'FFFFFF'
					},
					paragraph: {
						spacing: {
							after: 120
						}
					},
					shading: {
						type: ShadingType.CLEAR, // 纯色背景
						color: 'FFFFFF'
					}
				},
				{
					id: 'Heading1',
					name: 'Heading 1',
					basedOn: 'Normal',
					next: 'Normal',
					quickFormat: true,
					run: {
						size: 28,
						bold: true,
						font: '微软雅黑',
						color: '00007b'
					},
					paragraph: {
						spacing: {
							after: 120
						}
					},
					shading: {
						// fill: 'E7E6E6',
						// type: ShadingType.REVERSE_DIAGONAL_STRIPE, // 斜条背景
						// type: ShadingType.PERCENT_95, // 满天星背景
						// type: ShadingType.PERCENT_10, // 黑点点背景
						type: ShadingType.CLEAR, // 纯色背景
						color: 'FFFFFF'
					}
				},
				{
					id: 'Heading2',
					name: 'Heading 2',
					basedOn: 'Normal',
					next: 'Normal',
					quickFormat: true,
					run: {
						size: 22,
						bold: true,
						italics: false,
						font: '微软雅黑',
						color: 'FFFFFF'
					},
					paragraph: {
						spacing: {
							after: 120
						}
					},
					shading: {
						fill: 'E7E6E6',
						// type: ShadingType.REVERSE_DIAGONAL_STRIPE, // 斜条背景
						// type: ShadingType.PERCENT_95, // 满天星背景
						// type: ShadingType.PERCENT_10, // 黑点点背景
						type: ShadingType.CLEAR, // 纯色背景
						color: 'FFFFFF'
					}
				},
				{
					id: 'Heading3',
					name: 'Heading 3',
					basedOn: 'Normal',
					next: 'Normal',
					quickFormat: true,
					run: {
						size: 18,
						bold: false,
						italics: false,
						color: '170875'
					},
					background: {
						color: '170875'
					},
					paragraph: {
						spacing: {
							after: 120
						}
					}
				},
				{
					id: 'Heading4',
					name: 'Heading 4',
					basedOn: 'Normal',
					next: 'Normal',
					quickFormat: true,
					run: {
						size: 18,
						bold: false,
						font: '宋体',
						italics: false,
						color: '000000'
					}
				},
				{
					id: 'Heading5',
					name: 'Heading 5',
					basedOn: 'Normal',
					next: 'Normal',
					quickFormat: true,
					run: {
						size: 18,
						bold: false,
						font: '楷体_GB2312',
						italics: false,
						color: '170875'
					}
				},
				{
					id: 'Heading6',
					name: 'Heading 6',
					basedOn: 'Normal',
					next: 'Normal',
					quickFormat: true,
					run: {
						size: 22,
						bold: false,
						font: '楷体_GB2312',
						italics: false,
						color: '170875'
					}
				}
			]
		},
		numbering: {
			config: [
				{
					reference: 'my-crazy-numbering',
					levels: [
						{
							level: 0,
							format: 'lowerLetter',
							text: '%1)',
							alignment: AlignmentType.CENTER
						}
					]
				}
			]
		},
		sections: [
			{
				properties: {
					page: {
						margin: {
							top: 600,
							right: 600,
							bottom: 600,
							left: 600
						}
					}
				},
				headers: {
					default: new Header({
						children: [
							new Paragraph({
								heading: HeadingLevel.HEADING_6,
								children: [
									new TextRun({ text: '投资财富一体化研究平台' }),
									new ImageRun({
										data: Uint8Array.from(atob(img.split(',')[1]), (c) => c.charCodeAt(0)),
										transformation: {
											width: 180,
											height: 25
										},
										floating: {
											horizontalPosition: {
												offset: 5414400
											},
											verticalPosition: {
												offset: 364400
											},
											wrap: {
												type: TextWrappingType.NONE,
												side: TextWrappingSide.BOTH_SIDES
											}
										}
									})
								],
								alignment: AlignmentType.JUSTIFIED,
								border: {
									top: {
										style: BorderStyle.OUTSET,
										size: 0,
										color: 'ffffff'
									},
									left: {
										style: BorderStyle.OUTSET,
										size: 0,
										color: 'ffffff'
									},
									right: {
										style: BorderStyle.OUTSET,
										size: 0,
										color: 'ffffff'
									},
									bottom: {
										style: BorderStyle.OUTSET,
										size: 10,
										color: '000000'
									}
								}
							}),
							new Paragraph({
								shading: {
									fill: 'FFFFFF',
									type: ShadingType.CLEAR, // 纯色背景
									color: 'FFFFFF'
								}
							})
						]
					})
				},
				footers: {
					default: new Footer({
						children: [
							new Paragraph({
								text: '',
								heading: HeadingLevel.HEADING_5,
								alignment: AlignmentType.CENTER,
								style: {},
								border: {
									top: {
										style: BorderStyle.OUTSET,
										size: 25,
										color: '170875'
									},
									left: {
										style: BorderStyle.OUTSET,
										size: 0,
										color: 'ffffff'
									},
									right: {
										style: BorderStyle.OUTSET,
										size: 0,
										color: 'ffffff'
									},
									bottom: {
										style: BorderStyle.OUTSET,
										size: 0,
										color: 'ffffff'
									}
								}
							}),
							new Paragraph({
								text: '本报告由慧捕基制作，仅供内部员工和授权机构交流使用',
								heading: HeadingLevel.HEADING_5,
								alignment: AlignmentType.RIGHT
							})
						]
					})
				},
				children: children
			}
		]
	});
	Packer.toBlob(doc).then((buffer) => {
		saveAs(buffer, info.name + '深度分析报告.docx');
	});
}

// 报告模块标题
export function exportTitle(title) {
	return [
		new Paragraph({
			shading: {
				fill: 'FFFFFF',
				type: ShadingType.CLEAR, // 纯色背景
				color: 'FFFFFF'
			}
		}),
		new Paragraph({
			text: ' ' + title,
			heading: HeadingLevel.HEADING_2,
			shading: {
				fill: '333399',
				type: ShadingType.CLEAR, // 纯色背景
				color: 'ffffff'
			}
		}),
		new Paragraph({
			shading: {
				fill: 'FFFFFF',
				type: ShadingType.CLEAR, // 纯色背景
				color: 'FFFFFF'
			}
		})
	];
}
export function exportTitleWithSubtitle(title,subtitle) {
	return [
		new Paragraph({
			text: ' ' + title,
			heading: HeadingLevel.HEADING_2,
			shading: {
				fill: '333399',
				type: ShadingType.CLEAR, // 纯色背景
				color: 'ffffff'
			}
		}),
		new Paragraph({
			text: ' ' + subtitle,
			heading: HeadingLevel.HEADING_2,
			shading: {
				fill: '333399',
				type: ShadingType.CLEAR, // 纯色背景
				color: 'ffffff'
			}
		}),
		new Paragraph({
			shading: {
				fill: 'FFFFFF',
				type: ShadingType.CLEAR, // 纯色背景
				color: 'FFFFFF'
			}
		})
	];
}
// 报告一级标题
export function exportFirstTitle(title) {
	return [
		new Paragraph({
			shading: {
				fill: 'FFFFFF',
				type: ShadingType.CLEAR, // 纯色背景
				color: 'FFFFFF'
			}
		}),
		new Paragraph({
			text: title,
			heading: HeadingLevel.TITLE,
			alignment: AlignmentType.CENTER,
			shading: {
				fill: '333399',
				type: ShadingType.CLEAR, // 纯色背景
				color: 'FFFFFF'
			}
		}),
		new Paragraph({
			shading: {
				fill: 'FFFFFF',
				type: ShadingType.CLEAR, // 纯色背景
				color: 'FFFFFF'
			}
		})
	];
}
// 报告二级标题
export function exportSencondTitle(title) {
	return [
		new Paragraph({
			shading: {
				fill: 'FFFFFF',
				type: ShadingType.CLEAR, // 纯色背景
				color: 'FFFFFF'
			}
		}),
		new Paragraph({
			text: title,
			heading: HeadingLevel.HEADING_1
		}),
		new Paragraph({
			shading: {
				fill: 'FFFFFF',
				type: ShadingType.CLEAR, // 纯色背景
				color: 'FFFFFF'
			}
		})
	];
}
// 报告文字描述
export function exportDescripe(text, color) {
	if (color) {
		return [
			new Paragraph({
				text: text,
				heading: HeadingLevel.HEADING_4
			})
		];
	} else {
		return [
			new Paragraph({
				text: text,
				heading: HeadingLevel.HEADING_3
			})
		];
	}
}
// 报告表格
export function exportTable(list, data, style, flag) {
	let column = [];
	list.map((obj, index) => {
		column.push(
			new TableCell({
				width: {
					size: obj?.size ? obj.size : 100 / list.length,
					type: WidthType.PERCENTAGE
				},
				children: [
					new Paragraph({
						children: [new TextRun({ text: obj.label, bold: true })],
						alignment: AlignmentType.CENTER
					})
				],
				rowSpan: obj.rowSpan ? obj.rowSpan : 1,
				columnSpan: obj.columnSpan ? obj.columnSpan : 1,
				shading: {
					fill: 'ffffff',
					// type: ShadingType.REVERSE_DIAGONAL_STRIPE, // 斜条背景
					// type: ShadingType.PERCENT_95, // 满天星背景
					// type: ShadingType.PERCENT_10, ,// 黑点点背景
					type: ShadingType.CLEAR, // 纯色背景
					color: 'auto'
				},
				borders: {
					top: {
						style: BorderStyle.INSET,
						size: 5,
						color: 'D9D9D9'
						// color: obj?.type && obj?.type?.indexOf('image') != -1 ? 'ffffff' : 'D9D9D9'
					},
					left: {
						style: BorderStyle.INSET,
						size: 5,
						color: 'D9D9D9'
						// color: obj?.type && obj?.type?.indexOf('image') != -1 ? 'ffffff' : 'D9D9D9'
					},
					right: {
						style: BorderStyle.INSET,
						size: 5,
						color: 'D9D9D9'
						// color: obj?.type && obj?.type?.indexOf('image') != -1 ? 'ffffff' : 'D9D9D9'
					},
					bottom: {
						style: BorderStyle.INSET,
						size: 10,
						color: 'D9D9D9'
						// color: obj?.type && obj?.type?.indexOf('image') != -1 ? 'ffffff' : 'D9D9D9'
					}
				}
			})
		);
	});
	let rows = [];
	if (data?.length && typeof data == 'object') {
		data?.map((item, index) => {
			let children = [];
			list.map((obj) => {
				children.push(
					new TableCell({
						// width: {
						// 	size: 5505,
						// 	type: WidthType.DXA
						// },
						children: tableFill.returnCell(item, obj, style),
						shading: {
							fill: flag ? (index % 2 ? 'ffffff' : 'ececec') : 'ffffff',
							// fill: tableFill.returnColor(item, obj),
							type: ShadingType.CLEAR, // 纯色背景
							color: 'auto'
						},
						borders: {
							top: {
								style: BorderStyle.INSET,
								size: 5,
								color: 'D9D9D9'
								// color: obj?.type && obj?.type?.indexOf('image') != -1 ? 'ffffff' : 'D9D9D9'
							},
							left: {
								style: BorderStyle.INSET,
								size: 5,
								color: 'D9D9D9'
								// color: obj?.type && obj?.type?.indexOf('image') != -1 ? 'ffffff' : 'D9D9D9'
							},
							right: {
								style: BorderStyle.INSET,
								size: 5,
								color: 'D9D9D9'
								// color: obj?.type && obj?.type?.indexOf('image') != -1 ? 'ffffff' : 'D9D9D9'
							},
							bottom: {
								style: BorderStyle.INSET,
								size: 5,
								color: 'D9D9D9'
								// color: obj?.type && obj?.type?.indexOf('image') != -1 ? 'ffffff' : 'D9D9D9'
							}
						}
					})
				);
			});
			rows.push(
				new TableRow({
					children,
					tableHeader: true,
					cantSplit: true
				})
			);
		});
	}

	return [
		new Table({
			width: {
				size: 10500,
				type: WidthType.DXA
			},
			indent: {
				size: 500,
				type: WidthType.DXA
			},
			alignment: AlignmentType.CENTER,
			rows: [
				new TableRow({
					children: column,
					tableHeader: true,
					cantSplit: true
				}),
				...rows
			]
		})
	];
}

// 对比报告表格
export function exportCompareTable(data, spanObj, flag) {
	let rows = [];
	if (data?.length && typeof data == 'object') {
		data?.map((item, index) => {
			let children = [];
			item.map((val, i) => {
				children.push(
					new TableCell({
						width: {
							size: 100 / item.length,
							type: WidthType.PERCENTAGE
						},
						columnSpan: tableFill.returnRowSpan({ spanObj, index, i }),
						children: [
							new Paragraph({
								children: [new TextRun({ text: val, bold: index == 0 ? true : false })],
								alignment: AlignmentType.CENTER
							})
						],
						shading: {
							fill: flag ? (index % 2 ? 'ececec' : 'ffffff') : 'ffffff',
							// fill: tableFill.returnColor(item, obj),
							type: ShadingType.CLEAR, // 纯色背景
							color: 'auto'
						},
						borders: {
							top: {
								style: BorderStyle.INSET,
								size: 5,
								color: 'D9D9D9'
							},
							left: {
								style: BorderStyle.INSET,
								size: 5,
								color: 'D9D9D9'
							},
							right: {
								style: BorderStyle.INSET,
								size: 5,
								color: 'D9D9D9'
							},
							bottom: {
								style: BorderStyle.INSET,
								size: 5,
								color: 'D9D9D9'
							}
						}
					})
				);
			});
			rows.push(
				new TableRow({
					children,
					tableHeader: true,
					cantSplit: true
				})
			);
		});
	}
	return [
		new Table({
			width: {
				size: 10500,
				type: WidthType.DXA
			},
			indent: {
				size: 500,
				type: WidthType.DXA
			},
			alignment: AlignmentType.CENTER,
			rows: [...rows]
		})
	];
}

// 报告图
export function exportChart(base64Image, transformation) {
	if (transformation) {
		return [
			new Paragraph({
				children: [
					new ImageRun({
						data: Uint8Array.from(atob(base64Image.split(',')[1]), (c) => c.charCodeAt(0)),
						transformation: {
							width: 600,
							height: (600 / transformation.width) * transformation.height
						}
					})
				],
				alignment: AlignmentType.CENTER
			})
		];
	} else {
		return [
			new Paragraph({
				children: [
					new ImageRun({
						data: Uint8Array.from(atob(base64Image.split(',')[1]), (c) => c.charCodeAt(0)),
						transformation: {
							width: 600,
							height: 300
						}
					})
				],
				alignment: AlignmentType.CENTER
			})
		];
	}
}
// 报告竖型表头表格
export function exportDoubleColumnTable(list, data) {
	let rows = [];
	let children = [];
	list.map((item, index) => {
		children.push(
			new TableCell({
				width: {
					size: 25,
					type: WidthType.PERCENTAGE
				},
				children: [
					new Paragraph({
						children: [new TextRun({ text: item.label, bold: true })],
						alignment: AlignmentType.CENTER
					})
				],
				borders: {
					top: {
						style: BorderStyle.INSET,
						size: 5,
						color: 'D9D9D9'
					},
					left: {
						style: BorderStyle.INSET,
						size: 5,
						color: 'D9D9D9'
					},
					right: {
						style: BorderStyle.INSET,
						size: 5,
						color: 'D9D9D9'
					},
					bottom: {
						style: BorderStyle.INSET,
						size: 5,
						color: 'D9D9D9'
					}
				}
			}),
			new TableCell({
				width: {
					size: 25,
					type: WidthType.PERCENTAGE
				},
				columnSpan: item.rowSpan || 1,
				children: [
					new Paragraph({
						text: item.format ? format[item.format](data[item.value]) : data[item.value],
						shading: {
							// fill: tableFill.returnColor(data, item),
							// fill: item.color ? (data[item.value] > 0 ? 'F5DADA' : data[item.value] < 0 ? 'B0DAB4' : 'FFFFFF') : 'FFFFFF',
							type: ShadingType.CLEAR
						}
					})
				],
				borders: {
					top: {
						style: BorderStyle.INSET,
						size: 5,
						color: 'D9D9D9'
					},
					left: {
						style: BorderStyle.INSET,
						size: 5,
						color: 'D9D9D9'
					},
					right: {
						style: BorderStyle.INSET,
						size: 5,
						color: 'D9D9D9'
					},
					bottom: {
						style: BorderStyle.INSET,
						size: 5,
						color: 'D9D9D9'
					}
				}
			})
		);
		if (children.length >= 4) {
			rows.push(
				new TableRow({
					children
				})
			);
			children = [];
		} else if (index == list.length - 1) {
			if (children.length) {
				rows.push(
					new TableRow({
						children
					})
				);
			}
			children = [];
		}
	});
	return [
		new Paragraph({
			shading: {
				fill: 'FFFFFF',
				type: ShadingType.CLEAR, // 纯色背景
				color: 'FFFFFF'
			}
		}),
		new Table({
			width: {
				size: 10500,
				type: WidthType.DXA
			},
			indent: {
				size: 500,
				type: WidthType.DXA
			},
			alignment: AlignmentType.CENTER,
			rows
		})
	];
}

// 报告表格多表头
export function exportTableMergeHeader(headerList, data, format,style, flag) {
	// rowSpan 竖向合并
	// columnSpan 横向合并
	let headers = [];
	headerList.map(list=>{
		const column = [];
		list.map((obj, index) => {
			column.push(
				new TableCell({
					columnSpan:obj.columnSpan || 1,
					rowSpan:obj.rowSpan || 1,
					width: {
						size: obj?.size ? obj.size : 100 / list.length,
						type: WidthType.PERCENTAGE
					},
					children: [new Paragraph({ children: [new TextRun({ text: obj.label, bold: true })], alignment: AlignmentType.CENTER })],
					shading: {
						fill: 'ffffff',
						// type: ShadingType.REVERSE_DIAGONAL_STRIPE, // 斜条背景
						// type: ShadingType.PERCENT_95, // 满天星背景
						// type: ShadingType.PERCENT_10, ,// 黑点点背景
						type: ShadingType.CLEAR, // 纯色背景
						color: 'auto'
					},
					borders: {
						top: {
							style: BorderStyle.INSET,
							size: 5,
							color: obj?.type && obj?.type?.indexOf('image') != -1 ? 'ffffff' : 'D9D9D9'
						},
						left: {
							style: BorderStyle.INSET,
							size: 5,
							color: obj?.type && obj?.type?.indexOf('image') != -1 ? 'ffffff' : 'D9D9D9'
						},
						right: {
							style: BorderStyle.INSET,
							size: 5,
							color: obj?.type && obj?.type?.indexOf('image') != -1 ? 'ffffff' : 'D9D9D9'
						},
						bottom: {
							style: BorderStyle.INSET,
							size: 10,
							color: obj?.type && obj?.type?.indexOf('image') != -1 ? 'ffffff' : 'D9D9D9'
						}
					}
				})
			);
		});
		headers.push(	new TableRow({
			children: column,
			tableHeader: true,
			cantSplit: true
		}),);
	})
	
	let rows = [];
	if (data?.length && typeof data == 'object') {
		data?.map((item, index) => {
			let children = [];
			format.map((obj) => {
				children.push(
					new TableCell({
						children: tableFill.returnCell(item, obj, style),
						shading: {
							fill: flag ? (index % 2 ? 'ffffff' : 'ececec') : 'ffffff',
							// fill: tableFill.returnColor(item, obj),
							type: ShadingType.CLEAR, // 纯色背景
							color: 'auto'
						},
						borders: {
							top: {
								style: BorderStyle.INSET,
								size: 5,
								color: obj?.type && obj?.type?.indexOf('image') != -1 ? 'ffffff' : 'D9D9D9'
							},
							left: {
								style: BorderStyle.INSET,
								size: 5,
								color: obj?.type && obj?.type?.indexOf('image') != -1 ? 'ffffff' : 'D9D9D9'
							},
							right: {
								style: BorderStyle.INSET,
								size: 5,
								color: obj?.type && obj?.type?.indexOf('image') != -1 ? 'ffffff' : 'D9D9D9'
							},
							bottom: {
								style: BorderStyle.INSET,
								size: 5,
								color: obj?.type && obj?.type?.indexOf('image') != -1 ? 'ffffff' : 'D9D9D9'
							}
						}
					})
				);
			});
			rows.push(
				new TableRow({
					children,
					tableHeader: true,
					cantSplit: true
				})
			);
		});
	}

	return [
		new Table({
			width: {
				size: 10500,
				type: WidthType.DXA
			},
			indent: {
				size: 500,
				type: WidthType.DXA
			},
			alignment: AlignmentType.CENTER,
			rows: [
				...headers,
				...rows
			]
		})
	];
}
