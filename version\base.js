window.localStorage.setItem('mty_modulesName', process.env.MODEL);
import 'babel-polyfill';
import Vue from 'vue';
import App from './App.vue';
import router from '@/router';

Vue.prototype.$routerList = router;

import ElementUI from 'element-ui';
import '@/assets/css/icon.css';
import axios from 'axios';
import moment from 'moment';
import store from '@/store/store';
import COMMON from '@/utils/common.js';
import EXPLAIN from '@/utils/explain.js';
import FUNC from '@/utils/commonFunc.js';

import exportImage from '@/utils/exportImage.js';
Vue.prototype.exportImage = exportImage;

// 组件内请求状态监听
import { watch } from '@/utils/componentsWatch.js';

Vue.prototype.$compontentsWatch = watch;

// 数据缓存
import { getCacheData, setCacheData } from '@/utils/cacheAnaylsisData';

Vue.prototype.getCacheData = getCacheData;
Vue.prototype.setCacheData = setCacheData;

// locacStorage
import localStorage from '@/utils/localStorage';

Vue.prototype.localStorage = localStorage;

import vcolorpicker from 'vcolorpicker';

Vue.use(vcolorpicker);

// 导出word
import {
	exportTitle,
	exportChart,
	exportDescripe,
	exportDoubleColumnTable,
	exportFirstTitle,
	exportFundBasicInfo,
	exportManagerInfo,
	exportPortfolioInfo,
	exportSencondTitle,
	exportTable,
	exportCompareTable,
	downloadWord
} from '@/utils/exportWord.js';

let exportWord = {
	exportTitle,
	exportChart,
	exportDescripe,
	exportDoubleColumnTable,
	exportFirstTitle,
	exportFundBasicInfo,
	exportManagerInfo,
	exportPortfolioInfo,
	exportSencondTitle,
	exportTable,
	exportCompareTable,
	downloadWord
};
Vue.prototype.$exportWord = exportWord;

//登录组件
import logins from '@/components/plugin/login/login';

Vue.use(logins);
// 长表格数据展示柱状图组件
import longTablePopoverChart from '@/components/components/components/longTablePopoverChart/index.vue';
Vue.component('longTablePopoverChart', longTablePopoverChart);
// 图组件
import VChart from 'vue-echarts';

Vue.component('VChart', VChart);
// 分析模块卡片标题
import analysisCardTitle from '@/components/components/components/analysisCardTitle/index.vue';
Vue.component('analysisCardTitle', analysisCardTitle);

Vue.use(antd);
import '@/assets/js/flexible'; //字体自适应   node_modules\@vue\cli-service\lib\config\css.js 修改原型稿计算rem
Vue.config.productionTip = false;
if (!!window.ActiveXObject || 'ActiveXObject' in window) {
} else {
	// import VueParticles from 'vue-particles'
	// Vue.use(VueParticles)
	import('vue-particles').then((compontent) => {
		//console.log('use', compontent);
		// //console.log("use", VueCropper)
		Vue.use(compontent.default);
	});
}
// Vue.prototype.$modulesName = 'GDBank'
Vue.prototype.$axios = axios;
Vue.config.productionTip = false;
Vue.use(ElementUI, {
	size: 'small'
});

Vue.prototype.moment = moment;

import Vuex from 'vuex';

Vue.use(Vuex);
Vue.prototype.$baseUrl = process.env.VUE_APP_BASE_URL;
Vue.prototype.$webSocket = process.env.VUE_APP_WEB_SOCKET_URL;

Vue.prototype.$event = new Vue();

import html2canvas from 'html2canvas';

Vue.prototype.html2canvas = html2canvas;
import xss from 'xss';

Vue.prototype.xss = xss;

Vue.prototype.COMMON = COMMON; // 项目常量
Vue.prototype.EXPLAIN = EXPLAIN; // 项目常量
Vue.prototype.FUNC = FUNC; // 全局方法

//使用钩子函数对路由进行权限跳转
router.beforeEach((to, from, next) => {
	//需要做一个接口判断token过期则进入登录
	document.title = `${to.meta.title} - 慧捕基`;
	const token = store.state.token;
	if (!token && to.path !== '/dashboard') {
		localStorage.setItem('nextPath', JSON.stringify({ path: to.path, params: to.params, query: to.query }));
		Vue.prototype.$logins();
		next('/dashboard');
	} else if (to.path.indexOf(store.state.loginflag) > 0) {
		// 临时选择大于0 等列表好了改回小于
		// 如果是管理员权限则可进入，这里只是简单的模拟管理员权限而已
		next('/403');
		// 如果是管理员权限则可进入，这里只是简单的模拟管理员权限而已
		//role === 'admin' ? next() : next('/403');
	} else if (to.path == '/alphaHeader') {
		if (from.path == '/funddetail' || from.path == '/fundmanagerdetail') {
			next();
		} else {
			localStorage.setItem('mty_filterNew_index', JSON.stringify(1));
			// console.log('10086');
			next();
		}
	} else {
		// 简单的判断IE10及以下不进入富文本编辑器，该组件不兼容
		if (navigator.userAgent.indexOf('MSIE') > -1 && to.path === '/editor') {
			Vue.prototype.$alert('vue-quill-editor组件不兼容IE10及以下浏览器，请使用更高版本的浏览器查看', '浏览器不兼容通知', {
				confirmButtonText: '确定'
			});
		} else {
			next();
		}
	}
});
router.afterEach((route) => {
	if (document.getElementsByClassName('content').length > 0) {
		document.getElementsByClassName('content')[0].scrollTop = 0;
	}
});

// 全局指令
// select下拉加载
import pullDownLoad from '@/utils/selectPullDownLoadDirective.js';

Vue.use(pullDownLoad);

new Vue({
	router,
	// i18n,
	store,
	render: (h) => h(App)
}).$mount('#app');
