<script>
import { getDataBoardQueryConfig, getPivotTableData } from '@/api/pages/tkdesign/performance'
import { filter_json_to_excel_inside, changColumnToRow, filter_json_to_excel_inside_multiHeader } from '@/utils/exportExcel.js';

export default {
  data () {
    return {
      loading: false,
      type: 'TRD',
      date: [],
      time: [],
      tableData: [],
      rowList: [],
      colList: [],
      rowFilterList: [],
      colFilterList: [],
      rowCheckedIdList: [],
      colCheckedIdList: [],
      rowOrderList: [],
      colOrderList: [],
      tableColList: [],
    }
  },
  watch: {
    type: {
      handler (newVal) {
        this.rowCheckedIdList = [];
        this.colCheckedIdList = [];

        this.rowFilterList = [];
        this.colFilterList = [];

        this.rowOrderList = [];
        this.colOrderList = [];
        this.rowList.forEach(v => {
          if (v.type === newVal) {
            this.rowFilterList.push(v)
          }
        })
        this.colList.forEach(v => {
          if (v.type === newVal) {
            this.colFilterList.push(v)
          }
        })
      },
    },
  },
  methods: {
    downloadExcel () {
      console.log(this.tableData);
      console.log(this.tableColList);
      const title = [
        { label: '', value: 'name', format: '' },
      ];
      this.tableColList && this.tableColList.map(col => {
        const temp = {
          label: col.name,
          value: col.valuekey,
        }
        title.push(temp);
      })
      const space = '\xa0\xa0\xa0\xa0\xa0\xa0'
      function buildSpace (level) {
        let spaceStr = '';
        for (let index = 0; index < level; index++) {
          spaceStr += space;
        }
        return spaceStr;
      }
      function turnData (array, level) {
        let tempArray = [];
        if (array && array.length > 0) {
          array.map((item) => {
            const space = buildSpace(level);
            item.name = space + item.name;
            tempArray.push(item);

            if (item.children && item.children.length > 0) {
              const a = turnData(item.children, level + 1);
              tempArray = tempArray.concat(a);
            }
          })
        }
        return tempArray;
      }
      const data = turnData(this.tableData, 0);
      console.log(data);

      filter_json_to_excel_inside(title, data, [], '分管理人业绩');
    },
    getTableData () {
      this.loading = true;
      // console.log(this.rowOrderList, this.colOrderList);
      if (!this.rowOrderList || this.rowOrderList.length === 0 || !this.colOrderList || this.colOrderList.length === 0) {
        this.$message.warning('请选择行和列')
        return;
      }
      console.log(this.rowOrderList);
      const rowList = this.rowOrderList.map(row => row.id);
      const colList = this.colOrderList.map(row => row.id);
      // console.log(this.time);
      let params = {
        startDate: this.moment(this.date[0]).format('YYYY-MM-DD'),
        endDate: this.moment(this.date[1]).format('YYYY-MM-DD'),
        type: this.type,
        rowList,
        colList,
      }
      this.tableColList = []
      getPivotTableData(params).then(res => {
        this.loading = false;
        if (res && (res.mtycode > 0 || res.code == 200)) {
          function turnData (array, index) {
            if (array && array.length > 0) {
              return array.map((item, i) => {
                const id = index + i;
                const temp = { name: item.name, id }
                item.valueList && item.valueList.map((value, ii) => {
                  temp[`value${ii}`] = value.value;
                })

                if (item.childrenList && item.childrenList.length > 0) {
                  temp.children = turnData(item.childrenList, id * 100);
                }

                return temp;
              })
            }
            return [];
          }
          const data = turnData(res.data, 1);
          this.tableData = data;
          this.tableColList = JSON.parse(JSON.stringify(this.colOrderList))
        }
      })
    },
    /**
     * 获取账户配置
     */
    getDataBoardQueryConfig () {
      this.colList = [];
      this.rowList = [];
      this.rowFilterList = [];
      this.colFilterList = [];
      this.rowOrderList = [];
      this.colOrderList = [];
      getDataBoardQueryConfig({}).then(res => {
        if (res && (res.mtycode == 200 || res.code == 200)) {
          res.data.rowValueList.forEach(v => {
            this.rowList.push({
              name: v.value,
              id: v.name,
              type: v.type,
            })
            if (v.type === this.type) {
              this.rowFilterList.push({
                name: v.value,
                id: v.name,
                type: v.type,
              })
            }
          })
          res.data.columnValueList.forEach(v => {
            this.colList.push({
              name: v.value,
              id: v.name,
              type: v.type,
            })
            if (v.type === this.type) {
              this.colFilterList.push({
                name: v.value,
                id: v.name,
                type: v.type,
              })
            }
          })
        }
      })
    },
    //拖拽排序前的数组
    dragstartRow (value) {
      this.oldData = value
    },
    dragstartCol (value) {
      this.oldData = value
    },
    //拖拽排序后的数组
    dragenterRow (value, e) {
      this.newData = value
      e.preventDefault()
    },
    dragenterCol (value, e) {
      this.newData = value
      e.preventDefault()
    },
    //拖拽最后操作
    dragendRow (value, e) {
      if (this.oldData !== this.newData) {
        const oldIndex = this.rowOrderList.indexOf(this.oldData)
        const newIndex = this.rowOrderList.indexOf(this.newData)
        const newItems = [...this.rowOrderList]
        newItems.splice(oldIndex, 1)
        newItems.splice(newIndex, 0, this.oldData)
        this.rowOrderList = [...newItems]
      }
    },
    dragendCol (value, e) {
      if (this.oldData !== this.newData) {
        const oldIndex = this.colOrderList.indexOf(this.oldData)
        const newIndex = this.colOrderList.indexOf(this.newData)
        const newItems = [...this.colOrderList]
        newItems.splice(oldIndex, 1)
        newItems.splice(newIndex, 0, this.oldData)
        this.colOrderList = [...newItems]
        this.colOrderList.map((item, i) => {
          item.valuekey = `value${i}`;
        })
      }
    },
    // 拖动事件（主要是为了拖动时鼠标光标不变为禁止）
    dragoverRow (e) {
      e.preventDefault()
    },
    dragoverCol (e) {
      e.preventDefault()
    },

    handleRowChange (value) {
      this.rowOrderList = [];
      value && value.map(id => {
        this.rowList && this.rowList.map(item => {
          if (item.id === id) {
            if (this.rowOrderList.findIndex(itemx => itemx.id == id) < 0) {
              this.rowOrderList.push(item);
            }
          }
        })
      })
    },
    handleColChange (value) {
      this.colOrderList = [];
      value && value.map(id => {
        this.colList && this.colList.map(item => {
          if (item.id === id) {
            if (this.colOrderList.findIndex(itemx => itemx.id == id) < 0) {
              this.colOrderList.push(item);
            }
          }
        })
      })
      this.colOrderList.map((item, i) => {
        item.valuekey = `value${i}`;
      })
    },
  },
  mounted () {
    this.getDataBoardQueryConfig();

    // var today = new Date(); // 获取当前日期时间
    // var lastMonth = new Date(today); // 创建一个与今天相同的日期对象
    // lastMonth.setMonth(lastMonth.getMonth() - 1); // 将日期设置为上个月

    // var startDate = lastMonth; // 区间开始日期为上个月的今天
    // var endDate = today; // 区间结束日期为今天
    const currentYear = new Date().getFullYear();
    const startDate = new Date(currentYear, 0, 1);
    const now = new Date();
    const yesterday = new Date(now);
    yesterday.setDate(now.getDate() - 1);
    const endDate = new Date();
    // const end = new Date();
    // const start = new Date();
    // start.setFullYear(end.getFullYear() - 1);
    // const start = new Date(new Date().getFullYear(), 0, 1);

    this.date = [startDate, yesterday]
  }
}
</script>
<template>
  <div v-loading="loading"
       class="box_Board">
    <div class="header_box">
      <span class="header_inactive">投后&nbsp;/&nbsp;投后监控看板&nbsp;/&nbsp;</span>
      数据透视表
    </div>
    <div class="border_table">
      <!-- 头部区域 -->
      <div class="border_table_header">
        <!-- 右侧标题区域 -->
        <div class="border_table_header_title">
          <div class="block" />
          <div>分管理人业绩</div>
          <!-- <i class="el-icon-question"></i> -->
        </div>
        <!-- 左侧筛选区域 -->
        <div class="border_table_header_filter">
          <el-radio-group class="group"
                          v-model="type">
            <el-radio-button label="TRD">交易</el-radio-button>
            <el-radio-button label="POS">持仓</el-radio-button>
            <el-radio-button label="PNL">损益</el-radio-button>
          </el-radio-group>
          <!-- 时间选择 -->
          <el-date-picker v-model="date"
                          :unlink-panels="true"
                          end-placeholder="结束日期"
                          range-separator="-"
                          start-placeholder="开始日期"
                          type="daterange">
          </el-date-picker>
          <!-- 下载 -->
          <div class="border_table_header_upload"
               @click="downloadExcel()">
            <i class="el-icon-download"></i>
          </div>
        </div>
      </div>
      <el-row>
        <el-col :span="18">
          <!-- 表格区域 -->
          <el-table :data="tableData"
                    height="calc(100vh - 410px)"
                    row-key="id"
                    :tree-props="{children: 'children'}">
            <el-table-column align="gotoleft"
                             label=""
                             width="200"
                             prop="name" />
            <el-table-column v-for="(item, index) in tableColList"
                             :prop="item.valuekey"
                             :label="item.name"
                             width="200"
                             :key="index">
            </el-table-column>
            <template slot="empty">
              <el-empty :image-size="160"></el-empty>
            </template>
          </el-table>
        </el-col>
        <el-col :span="6"
                class="config">
          <div class="configItem">
            <div class="configItem_title">
              配置行
            </div>
            <div class="configItem_checkbox">
              <el-checkbox-group v-model="rowCheckedIdList"
                                 @change="handleRowChange">
                <el-checkbox class="configItem_checkbox_item"
                             v-for="row in rowFilterList"
                             :label="row.id"
                             :key="row.id">{{row.name}}</el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
          <div class="configItem">
            <div class="configItem_title">
              配置列
            </div>
            <div class="configItem_checkbox">
              <el-checkbox-group v-model="colCheckedIdList"
                                 @change="handleColChange">
                <el-checkbox class="configItem_checkbox_item"
                             v-for="col in colFilterList"
                             :label="col.id"
                             :key="col.id">{{col.name}}</el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
          <div style='padding-bottom: 24px;'
               class="configItem">
            <div class="configItem_title">
              行
            </div>
            <div class="configItem_checkbox">
              <div v-for="(item,index) in rowOrderList"
                   :key="index"
                   :draggable="true"
                   class="configItem_checkbox_item"
                   @dragend="dragendRow(item,$event)"
                   @dragenter="dragenterRow(item,$event)"
                   @dragover="dragoverRow($event)"
                   @dragstart="dragstartRow(item)">
                <img draggable="false"
                     src="../../../assets/img/move.png"
                     width="12">
                {{ item.name }}
              </div>
            </div>
          </div>
          <div style='padding-bottom: 24px;'
               class="configItem">
            <div class="configItem_title">
              列
            </div>
            <div class="configItem_checkbox">
              <div class="configItem_checkbox">
                <div v-for="(item,index) in colOrderList"
                     :key="index"
                     :draggable="true"
                     class="configItem_checkbox_item"
                     @dragend="dragendCol(item,$event)"
                     @dragenter="dragenterCol(item,$event)"
                     @dragover="dragoverCol($event)"
                     @dragstart="dragstartCol(item)">
                  <img draggable="false"
                       src="../../../assets/img/move.png"
                       width="12">
                  {{ item.name }}
                </div>
              </div>
            </div>
          </div>
          <div class="submitBtn"
               @click="getTableData()"> 查询 </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<style lang="scss" scoped>
@import '../tkdesign';
.submitBtn {
	background-color: #4096ff;
	width: 100%;
	height: 30px;
	margin-top: 4px;
	text-align: center;
	line-height: 30px;
	color: white;
	font-size: large;
	cursor: pointer;
}

.border_table_header {
	padding-bottom: 16px;
	border-bottom: 1px solid #ccc;

	.group {
		margin-right: 20px;
	}

	.border_table_header_title {
		display: flex;
		align-items: center;

		.block {
			width: 6px;
			height: 20px;
			border-radius: 35px;
			background-color: #4096ff;
			margin-right: 16px;
		}

		i {
			margin-left: 3px;
		}
	}

	.border_table_header_filter {
		display: flex;
		align-items: center;
		font-size: 14px;

		.border_table_header_radio {
			display: flex;
			align-items: center;
		}

		.border_table_header_select {
			margin-left: 16px;
		}

		.border_table_header_upload {
			width: 32px;
			line-height: 30px;
			border-radius: 4px;
			border: 1px solid #d9d9d9;
			text-align: center;
			margin-left: 16px;
		}
	}
}

.config {
	padding-left: 16px;
	display: flex;
	justify-content: space-between;
	align-content: space-between;
	flex-wrap: wrap;
	height: calc(100vh - 410px);

	.configItem {
		width: 49%;
		height: 47%;
		background-color: #fafafa;
		padding: 8px 12px;

		.configItem_title {
			font-size: 16px;
			font-family: PingFang SC;
			font-weight: 500;
			line-height: 24px;
		}

		.configItem_checkbox {
			display: flex;
			flex-direction: column;
			font-size: 14px;
			font-weight: 400;
			height: 100%;
			overflow-y: auto;
			.configItem_checkbox_item {
				padding: 4px 0;
			}
		}
	}
}
</style>
