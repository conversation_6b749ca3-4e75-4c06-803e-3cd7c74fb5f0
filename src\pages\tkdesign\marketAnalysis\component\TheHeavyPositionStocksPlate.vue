<template>
	<div>
		<heavyWarehouseInfomation
			:title="title"
			:tableHeader.sync="tableHeader"
			:tableData="tableData"
			key="stock"
			marketType="stock"
		></heavyWarehouseInfomation>
	</div>
</template>
<script>
import heavyWarehouseInfomation from './heavyWarehouseInfomation.vue';
import { getHeavyStockList } from '@/api/pages/tkAnalysis/captial-market.js';
import stringTool from '@/pages/tkdesign/components/string.tool';
export default {
	name: 'TheHeavyPositionStocksPlate',
	components: {
		heavyWarehouseInfomation
	},
	data() {
		return {
			title: '重仓股票',
			tableHeader: [
				{
					prop: 'name',
					label: '重仓股票'
				},
				{
					prop: 'averageWeight',
					label: '平均配置权重',
					formatter: (val) => {
						return stringTool.fix2pxx(val);
					}
				},
				{
					prop: 'positionNum',
					label: '持仓基金数'
				},
				{
					prop: 'positionValue',
					label: '持仓市值（亿）'
				},
				{
					prop: 'proportion',
					label: '占股票流动市值比',
					formatter: (val) => {
						return stringTool.fix2px(val);
					}
				},
				{
					prop: 'lastSeasonAmplitude',
					label: '近一季变化幅度',
					formatter: (val) => {
						return stringTool.fix2pxx(val);
					}
				},
				{
					prop: 'yearToDate',
					label: '年初至今',
					formatter: (val) => {
						return stringTool.fix2px(val);
					}
				},
				{
					prop: 'lastWeek',
					label: '近1周',
					formatter: (val) => {
						return stringTool.fix2px(val);
					}
				},
				{
					prop: 'lastMounth',
					label: '近1月',
					formatter: (val) => {
						return stringTool.fix2px(val);
					}
				},
				{
					prop: 'lastSeason',
					label: '近1季',
					formatter: (val) => {
						return stringTool.fix2px(val);
					}
				},
				{
					prop: 'lastHalfYears',
					label: '近6月',
					formatter: (val) => {
						return stringTool.fix2px(val);
					}
				},
				{
					prop: 'lastYear',
					label: '近1年',
					formatter: (val) => {
						return stringTool.fix2px(val);
					}
				},
				{
					prop: 'quarterIncome',
					label: '季度间收益率',
					formatter: (val) => {
						return stringTool.fix2px(val);
					}
				}
			],
			tableData: []
		};
	},
	methods: {
		getData() {
			getHeavyStockList({
				pageSize: 10,
				pageNum: 1
			}).then((res) => {
				this.tableData = res.data;
			});
		}
	},
	created() {
		// this.getData()
	}
};
</script>
<style lang="scss" scoped></style>
