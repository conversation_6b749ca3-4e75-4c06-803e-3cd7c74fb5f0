<template>
  <div class="plate-wrapper valuation-percentile-wrapper"
       v-loading="loading">
    <VerticalLineHeader title="复合指标的互相运算"
                        showDownloadBtn
                        @downloadClick="exportExcel">
      <template slot="right">
        <el-form ref="form"
                 :model="form"
                 label-width="80px"
                 class="title-right-form">
          <el-form-item size="small"
                        label="计算周期:">
            <el-select v-model="form.scrollWindow"
                       placeholder="请选择"
                       @change="handleFormChange">
              <el-option v-for="item in ScrollWindowOption"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item size="small"
                        label="计算频率:">
            <el-select v-model="form.scrollFrequency"
                       placeholder="请选择"
                       @change="handleFormChange">
              <el-option v-for="item in ScrollFrequencyOption"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item size="small" label="指数模版:">
                    <el-select v-model="form.index_template" placeholder="请选择" @change="handleFormChange">
                    <el-option v-for="item in IndexTemplateOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                </el-form-item> -->
          <el-from-item class="el-form-item el-form-item--small"
                        size="small"
                        label="指数模版:">
            <label style="width: 80px"
                   class="el-form-item__label">指数模版:</label>
            <el-cascader :options="templateOptions"
                         :show-all-levels="false"
                         v-model="selectedTempletInfo"
                         @change="handleTemplateChange"
                         popper-class="template-cascader"
                         :props="indexTemplateConfig">
              <template slot-scope="{ node, data }">
                <div v-if="!node.parent">{{ data.label }}</div>
                <div v-else>
                  <div class="templet-item-wrapper">
                    <div class="templet-item-title">{{ data.label }}</div>
                    <div class="templet-item-content">
                      <div class="tic-item">创建时间: {{ data.label }}</div>
                      <div class="tic-item">描述: {{ data.label }}</div>
                    </div>
                  </div>
                </div>
              </template>
            </el-cascader>
          </el-from-item>
          <!-- <QuickTimePicker class="radio-group-wrapper" v-model="form.preset_time" @change="handleFormChange"></QuickTimePicker> -->
        </el-form>
      </template>
    </VerticalLineHeader>
    <div class="scheme-form-wrapper">
      <el-form ref="form"
               :model="addForm"
               class="scheme-inside-form-wrapper">
        <el-form-item prop="codeFirst"
                      class="mgr-10"
                      size="small"
                      :rules="[{ required: true, message: '请选择', trigger: 'change' }]">
          <!-- <el-select v-model="addForm.codeFirst" placeholder="选择指数">
                <el-option v-for="item in IndexTypeOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select> -->
          <el-cascader v-model="addForm.codeFirst"
                       @change="chooseIndex"
                       :options="IndexTypeOption"
                       placeholder="选择指数"></el-cascader>
        </el-form-item>
        <el-form-item prop="indicatorLibraryFirst"
                      :rules="[{ required: true, message: '请选择', trigger: 'change' }]"
                      class="mgr-10"
                      size="small">
          <el-select v-model="addForm.indicatorLibraryFirst"
                     placeholder="指标库">
            <el-option v-for="item in IndicatorLibraryOption"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="indicesCalculusFirst"
                      class="mgr-10"
                      size="small">
          <el-select v-model="addForm.indicesCalculusFirst"
                     placeholder="指标运算">
            <el-option v-for="item in IndicesCalculusOption"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="operator"
                      :rules="[{ required: true, message: '请选择', trigger: 'change' }]"
                      class="mgr-10"
                      style="width: 94px">
          <el-select v-model="addForm.operator"
                     placeholder="运算符"
                     class="plain-active">
            <el-option v-for="item in OpertatorOption"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <!-- <el-button class="mgr-10" type="primary" size="small">运算符</el-button> -->
        <el-form-item prop="codeSecond"
                      :rules="[{ required: true, message: '请选择', trigger: 'change' }]"
                      class="mgr-10"
                      size="small">
          <!-- <el-select type="primary" v-model="addForm.codeSecond" placeholder="选择指数">
                <el-option v-for="item in IndexTypeOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select> -->
          <el-cascader v-model="addForm.codeSecond"
                       :options="IndexTypeOption"
                       @change="chooseIndex"
                       placeholder="选择指数"></el-cascader>
        </el-form-item>
        <el-form-item prop="indicatorLibrarySecond"
                      :rules="[{ required: true, message: '请选择', trigger: 'change' }]"
                      class="mgr-10"
                      size="small">
          <el-select v-model="addForm.indicatorLibrarySecond"
                     placeholder="指标库">
            <el-option v-for="item in IndicatorLibraryOption"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="indicesCalculusSecond"
                      class="mgr-10"
                      size="small">
          <el-select v-model="addForm.indicesCalculusSecond"
                     placeholder="指标运算">
            <el-option v-for="item in IndicesCalculusOption"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <el-dialog width="25%"
                 title="新增模板"
                 :visible.sync="dialogFormVisible">
        <el-form class="add-from-wrapper"
                 ref="addForm"
                 :model="addForm">
          <el-form-item prop="templateName"
                        label="模板名称"
                        :rules="[{ required: true, message: '请输入模板名称', trigger: 'blur' }]">
            <el-input v-model="addForm.templateName"
                      autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item prop="templateDescribe"
                        label="模板描述"
                        :rules="[{ required: true, message: '请输入模板描述', trigger: 'blur' }]">
            <el-input v-model="addForm.templateDescribe"
                      autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item prop="openFlag"
                        label="公开模板">
            <el-switch active-value="true"
                       inactive-value="false"
                       v-model="addForm.openFlag"></el-switch>
          </el-form-item>
        </el-form>
        <div slot="footer"
             class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取 消</el-button>
          <el-button type="primary"
                     @click="submitAddForm">确 定</el-button>
        </div>
      </el-dialog>
      <el-button type="primary"
                 size="small"
                 @click="dialogFormVisible = true">保存当前方案</el-button>
      <el-button type="primary"
                 size="small"
                 @click="handleFormSubmit">提交</el-button>
    </div>
    <div class="checkbox-wrapper">
      <!-- <el-checkbox-group v-model="checkList">
                <el-checkbox label="回归线"></el-checkbox>
                <el-checkbox label="显示值/分位点"></el-checkbox>
            </el-checkbox-group> -->
      <div class="score-item">z-score：{{ zScore }}</div>
    </div>
    <CompositeIndicesOperationChart ref="composite-chart"
                                    @legendselectchanged="legendselectchanged"
                                    :chartData="chartData"></CompositeIndicesOperationChart>
  </div>
</template>
<script>
// import QuickTimePicker from './QuickTimePicker.vue';
import VerticalLineHeader from './VerticalLineHeader.vue';
import TkChart from './TkChart.vue';
import CompositeIndicesOperationChart from './chart/CompositeIndicesOperationChart.vue';
import {
  addIndicatorsOperationTemplate,
  getIndicatorsOperationTemplate,
  getIndexCode,
  getIndicatorsOperationData
} from '@/api/pages/tkAnalysis/captial-market.js';
import stringTool from '@/pages/tkdesign/components/string.tool';

import '@/pages/assets/css/page-container.scss';
export default {
  name: 'TheCompositeIndicesOperation',
  components: {
    VerticalLineHeader,
    // QuickTimePicker,
    TkChart,
    CompositeIndicesOperationChart
  },
  data () {
    let id = 0;
    let that = this;
    return {
      form: {
        scrollWindow: '5',
        scrollFrequency: 'day'
      },
      templateOptions: [
        {
          value: '1',
          label: '我的模板'
        },
        {
          value: '0',
          label: '公开模板'
        }
      ],
      selectedTempletInfo: {},
      indexTemplateConfig: {
        lazy: true,
        async lazyLoad (node, resolve) {
          const { level, value } = node;
          // 获取列表数据
          let params = {
            openFlag: value
          };
          let reqData = await getIndicatorsOperationTemplate(params);
          let { data = [], code, message } = reqData || {};
          if (code === 200) {
            const nodes = data.map((item) => ({
              value: { ...item },
              label: item.templateName,
              leaf: true
            }));
            // 通过调用resolve将子节点数据返回，通知组件数据加载完成
            resolve(nodes);
          } else {
            that.$message.warning(message);
            resolve([]);
          }
        }
      },
      addForm: {
        codeFirst: [],
        codeSecond: [],
        indicatorLibraryFirst: '',
        indicatorLibrarySecond: '',
        indicesCalculusFirst: '',
        indicesCalculusSecond: '',
        operator: '',
        templateName: '',
        templateDescribe: '',
        openFlag: false
      },
      checkList: [],
      ScrollWindowOption: [
        { label: '3年', value: '3' },
        { label: '5年', value: '5' },
        { label: '7年', value: '7' },
        { label: '10年', value: '10' }
      ],
      ScrollFrequencyOption: [
        { label: '天', value: 'day' },
        { label: '周', value: 'week' },
        { label: '月', value: 'month' }
      ],
      IndexTypeOption: [],
      //指数运算
      IndicesCalculusOption: [
        { label: '原指标', value: '' },
        { label: '倒数', value: 'countBackwards' },
        { label: '平方', value: 'square' }
      ],
      //指标库
      // 市盈率:pe、股息率:yield（待确定）、成交量:volume、成交额:amount，收益率:cum_return（待确定），收盘价:close
      IndicatorLibraryOption: [
        { label: '市盈率', value: 'pe' },
        { label: '股息率', value: 'yield' },
        { label: '成交量', value: 'volume ' },
        { label: '成交额', value: 'amount' },
        { label: '收益率', value: 'cum_return' },
        { label: '收盘价', value: 'close' },
        { label: '到期收益率', value: 'ep_bond' }
      ],
      //指数模板 接口获取
      IndexTemplateOption: [],
      //运算符
      OpertatorOption: [
        { label: '+', value: 'add' },
        { label: '-', value: 'subt' },
        { label: '×', value: 'mult ' },
        { label: '÷', value: 'division' }
      ],
      chartOption: null,
      dialogFormVisible: false,
      chartData: [],
      zScore: '',
      loading: false,
      deviceValue: true
    };
  },
  mounted () {
    this.getIndexOptions();
    if (this.localStorage.getItem('TheCompositeIndicesOperation')?.form) {
      let key_list = ['form', 'addForm'];
      for (let key of key_list) {
        this[key] = this.localStorage.getItem('TheCompositeIndicesOperation')?.[key] || this[key];
      }
      this.getData();
    }
  },
  watch: {
    deviceValue () {
      this.updateChart();
    }
  },
  methods: {
    //选择指数
    chooseIndex (w) {
      if (w?.[0] == 'bond') {
        this.IndicatorLibraryOption = [
          { label: '市盈率', value: 'pe' },
          { label: '股息率', value: 'yield' },
          { label: '成交量', value: 'volume ' },
          { label: '成交额', value: 'amount' },
          { label: '收益率', value: 'cum_return' },
          { label: '收盘价', value: 'close' },
          { label: '到期收益率', value: 'ep_bond' }
        ]
      }
      else {
        this.IndicatorLibraryOption = [
          { label: '市盈率', value: 'pe' },
          { label: '股息率', value: 'yield' },
          { label: '成交量', value: 'volume ' },
          { label: '成交额', value: 'amount' },
          { label: '收益率', value: 'cum_return' },
          { label: '收盘价', value: 'close' },
        ]
      }
    },
    exportExcel () {
      this.$refs['composite-chart']?.exportExcel();
    },
    async getIndexOptions () {
      let params = {
        ...this.form,
        flag: 1
      };
      let reqData = await getIndexCode(params);
      let { data = [], code, message } = reqData || {};
      if (code == 200) {
        this.IndexTypeOption = data.map((item) => {
          let children = item?.dataList?.map((childrenItem) => {
            return {
              ...childrenItem,
              label: childrenItem.name,
              value: childrenItem.code
            };
          });
          return { ...item, label: item.indexName, value: item.indexType, children: children };
        });
      } else {
        this.$message.warning(message);
      }
    },
    handleFormChange () {
      this.getData();
    },
    handleFormSubmit () {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.getData();
        }
      });
    },
    handleTemplateChange (value) {
      let result = value[value.length - 1];
      this.addForm = { ...result };
      this.addForm.codeFirst = result?.codeFirst.split(',');
      this.addForm.codeSecond = result?.codeSecond.split(',');
    },
    submitAddForm () {
      this.$refs.addForm.validate(async (valid) => {
        if (valid) {
          let params = {
            ...this.addForm,
            codeFirst: this.addForm.codeFirst.join(),
            codeSecond: this.addForm.codeSecond.join()
          };
          let data = await addIndicatorsOperationTemplate(params);
          let { code, message } = data || {};
          if (code == 200) {
            this.resetForm();
            this.$message.success('保存成功');
          } else {
            this.$message.error('保存失败: ' + message);
          }
        }
      });
    },
    resetForm () {
      this.dialogFormVisible = false;
      this.$refs.addForm.resetFields();
    },
    legendselectchanged (value) {
      this.deviceValue = value.selected.分位点;
    },
    updateChart () {
      this.updateZScore();
      let chartDataNew = this.chartData.map((item) => {
        let option = this.deviceValue ? item.pointValue : item.quantileValue;
        return {
          ...item,
          ...option
        };
      });
      this.$nextTick(() => {
        this.$refs['composite-chart'].getData(chartDataNew, {});
      });
    },
    // 更新z-score
    updateZScore () {
      let key = this.deviceValue ? 'pointValue' : 'quantileValue';
      this.zScore = this.fix2(this.chartData.length > 0 ? this.chartData[this.chartData.length - 1][key]?.zScore : '--');
    },
    // 格式化数据
    fix2 (val) {
      return val * 1 && !isNaN(val) ? (val * 1).toFixed(2) : '--';
    },
    // 获取列表数据
    async getData () {
      this.loading = true;
      let params = {
        ...this.form,
        ...this.addForm,
        codeFirst: this.addForm.codeFirst[1],
        codeSecond: this.addForm.codeSecond[1]
      };
      this.chartData = [];
      this.localStorage.setItem('TheCompositeIndicesOperation', { form: this.form, addForm: this.addForm });
      let reqData = await getIndicatorsOperationData(params);
      let { data, code, message } = reqData || {};
      if (code == 200) {
        let { dataList } = data || {};
        this.chartData = dataList || [];
        this.chartData = this.chartData.map((item) => {
          return {
            ...item
          };
        });
      } else {
        this.$message.warning(message);
        this.chartData = [];
      }
      this.updateChart();
      // this.$refs['composite-chart'].getData(this.chartData, {});
      this.loading = false;
    }
  }
};
</script>
<style lang="scss" scoped>
.templet-item-wrapper {
	font-size: 12px;
	line-height: normal;
	.templet-item-title {
		padding: 10px 5px 0;
	}
	.templet-item-content {
		display: flex;
		.tic-item {
			padding: 0 5px;
		}
		padding-bottom: 10px;
	}
}
.mgr-10 {
	margin-right: 10px;
}
#composite-chart-container {
	position: relative;
	height: 334px;
	overflow: hidden;
}
.valuation-percentile-wrapper {
	padding-bottom: 20px;
	.scheme-form-wrapper {
		padding: 24px;
		border-radius: 4px;
		background: #f5f5f5;
		.scheme-inside-form-wrapper {
			margin-bottom: 16px;
			display: flex;
			::v-deep .el-form-item--small {
				width: 176px;
				&.el-form-item {
					margin-bottom: unset;
				}
			}
		}
	}
	.checkbox-wrapper {
		padding: 16px 0;
		display: flex;
		justify-content: flex-end;
		.score-item {
			border-radius: 4px;
			border: 1px solid #d9d9d9;
			background: #f5f5f5;
			padding: 5px 16px;
			line-height: 1;
			color: #000;
			text-align: center;
			font-size: 14px;
			font-style: normal;
			font-weight: 400;
		}
		padding-bottom: 16px;
	}
	.add-from-wrapper {
		::v-deep .el-form-item {
			display: flex;
			.el-form-item__label {
				width: 90px;
			}
		}
	}
}
</style>
