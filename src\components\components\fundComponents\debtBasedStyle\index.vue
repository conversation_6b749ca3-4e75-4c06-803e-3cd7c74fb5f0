<template>
	<div v-loading="loading" id="debtBasedStyle">
		<analysis-card-title title="年度风格" image_id="debtBasedStyle">
			<div>
				<span v-show="info.type == 'bond' || info.type == 'cbond'" @click="pause()"
					><i
						:class="pauseFlag ? 'el-icon-video-pause' : 'el-icon-video-play'"
						style="color: #4096ff; margin-left: 5px; cursor: pointer; font-size: 26px"
					></i
				></span>
			</div>
		</analysis-card-title>
		<div>
			<div v-show="!isBookValue && info.type != 'cbond' && info.type != 'bond'">
				<div style="display: flex; flex-wrap: wrap; justify-content: center; align-items: center">
					<div
						style="
							text-align: center;
							display: flex;
							justify-content: center;
							align-items: center;
							font-size: 14px;
							font-weight: 500;
							font-family: PingFang;
						"
						class="width60"
					>
						时间
					</div>
					<div class="th_bg">久期分析</div>
					<div class="th_bg">
						久期变化<el-tooltip class="item" effect="dark" placement="top">
							<div slot="content">久期变化分为温和、 有界、 大幅、 剧烈</div>
							<svg width="14" height="14" viewBox="0 0 14 14" fill="none">
								<path
									fill-rule="evenodd"
									clip-rule="evenodd"
									d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
									fill="black"
									fill-opacity="0.45"
								/>
							</svg>
						</el-tooltip>
					</div>
					<div v-show="show1" class="th_bg">信用等级</div>
					<!-- 股票和转债的股信综合表现出来的对中证800的ß -->
					<div v-show="show2" class="th_bg width60">信用挖掘</div>
					<div v-show="show3" class="th_bg">券种特征</div>
					<div v-show="show4" class="th_bg">
						等效ß<el-tooltip class="item" effect="dark" placement="top">
							<div slot="content">股票和转债的股信综合表现出来的对中证800的ß</div>
							<svg width="14" height="14" viewBox="0 0 14 14" fill="none">
								<path
									fill-rule="evenodd"
									clip-rule="evenodd"
									d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
									fill="black"
									fill-opacity="0.45"
								/>
							</svg>
						</el-tooltip>
					</div>
					<div v-show="show5" class="th_bg">alpha</div>
				</div>
				<div style="display: flex; flex-wrap: wrap; justify-content: center; height: 38px" v-for="item in styleList" :key="item.year">
					<div
						style="
							text-align: center;
							display: flex;
							justify-content: center;
							align-items: center;
							font-size: 14px;
							font-weight: 500;
							font-family: PingFang;
						"
						class="width60"
					>
						{{ item.year }}
					</div>
					<div class="icon_bg progrsserhead">
						<div>
							<el-progress
								style="margin-left: 10px"
								:percentage="
									item.duration == '短'
										? 20
										: item.duration == '中短'
										? 40
										: item.duration == '中'
										? 60
										: item.duration == '中长'
										? 80
										: 100
								"
								:format="format"
								color="#1890FF"
							></el-progress>
						</div>
					</div>
					<div style="display: flex; justify-content: center; text-align: center" class="icon_bg">
						<i class="iconfont fs20" v-show="item.duration_chg == '有界'">
							<svg width="12" height="6" class="hamburger" viewBox="0 0 12 6" xmlns="http://www.w3.org/2000/svg">
								<path
									fill-rule="evenodd"
									clip-rule="evenodd"
									d="M4.71268 0.112534C4.9076 0.131567 5.0843 0.235351 5.19585 0.396331L7.75716 4.09253L9.00312 2.48118C9.12937 2.3179 9.32413 2.22231 9.53052 2.22231H11.3333C11.7015 2.22231 12 2.52079 12 2.88898C12 3.25717 11.7015 3.55564 11.3333 3.55564H9.85774L8.25509 5.62829C8.1251 5.79641 7.92274 5.89248 7.71029 5.88693C7.49785 5.88138 7.30078 5.77488 7.17974 5.6002L4.54771 1.80196L2.93364 3.36752C2.80925 3.48817 2.64277 3.55564 2.46948 3.55564H0.666667C0.298477 3.55564 0 3.25717 0 2.88898C0 2.52079 0.298477 2.22231 0.666667 2.22231H2.19928L4.18373 0.297504C4.32431 0.161144 4.51775 0.0935006 4.71268 0.112534Z"
									fill="#1890FF"
									fill-opacity="0.65"
								/>
							</svg>
						</i>
						<i class="iconfont fs20" v-show="item.duration_chg == '温和'">
							<svg width="14" height="4" class="hamburger" viewBox="0 0 14 4" xmlns="http://www.w3.org/2000/svg">
								<path
									fill-rule="evenodd"
									clip-rule="evenodd"
									d="M3.08443 0.1114C3.30797 -0.0371332 3.59878 -0.0371332 3.82233 0.1114L6.33022 1.77778H13.2223C13.5905 1.77778 13.8889 2.07625 13.8889 2.44444C13.8889 2.81263 13.5905 3.11111 13.2223 3.11111H6.12893C5.99766 3.11111 5.86932 3.07236 5.75998 2.99971L3.45338 1.46708L1.14677 2.99971C0.840105 3.20348 0.42632 3.12006 0.222555 2.81339C0.0187908 2.50673 0.102209 2.09294 0.408874 1.88918L3.08443 0.1114Z"
									fill="#1890FF"
									fill-opacity="0.65"
								/>
							</svg>
						</i>
						<i class="iconfont fs20" v-show="item.duration_chg == '大幅'">
							<svg width="16" height="16" class="hamburger" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
								<path
									d="M7.33337 2.66667C7.33337 2.29848 7.63185 2 8.00004 2C8.36823 2 8.66671 2.29848 8.66671 2.66667V12.8889C8.66671 13.2571 8.36823 13.5556 8.00004 13.5556C7.63185 13.5556 7.33337 13.2571 7.33337 12.8889V2.66667Z"
									fill="#1890FF"
									fill-opacity="0.65"
								/>
								<path
									d="M10 4.40104C10 4.03285 10.2985 3.73438 10.6667 3.73438C11.0349 3.73438 11.3333 4.03285 11.3333 4.40104V11.1566C11.3333 11.5248 11.0349 11.8233 10.6667 11.8233C10.2985 11.8233 10 11.5248 10 11.1566V4.40104Z"
									fill="#1890FF"
									fill-opacity="0.65"
								/>
								<path
									d="M4.66663 4.40104C4.66663 4.03285 4.9651 3.73438 5.33329 3.73438C5.70148 3.73438 5.99996 4.03285 5.99996 4.40104V11.1566C5.99996 11.5248 5.70148 11.8233 5.33329 11.8233C4.9651 11.8233 4.66663 11.5248 4.66663 11.1566V4.40104Z"
									fill="#1890FF"
									fill-opacity="0.65"
								/>
								<path
									d="M2 6.13151C2 5.76332 2.29848 5.46484 2.66667 5.46484C3.03486 5.46484 3.33333 5.76332 3.33333 6.13151V9.4204C3.33333 9.78859 3.03486 10.0871 2.66667 10.0871C2.29848 10.0871 2 9.78859 2 9.4204V6.13151Z"
									fill="#1890FF"
									fill-opacity="0.65"
								/>
								<path
									d="M12.6666 6.13151C12.6666 5.76332 12.9651 5.46484 13.3333 5.46484C13.7015 5.46484 14 5.76332 14 6.13151V9.4204C14 9.78859 13.7015 10.0871 13.3333 10.0871C12.9651 10.0871 12.6666 9.78859 12.6666 9.4204V6.13151Z"
									fill="bl#1890FFack"
									fill-opacity="0.65"
								/>
							</svg>
						</i>
						<i class="iconfont fs20" v-show="item.duration_chg == '剧烈'">
							<svg width="12" height="8" class="hamburger" viewBox="0 0 12 8" xmlns="http://www.w3.org/2000/svg">
								<path
									fill-rule="evenodd"
									clip-rule="evenodd"
									d="M4.86116 0.222752C5.15013 0.217863 5.40934 0.399721 5.50307 0.673108L6.98082 4.98322L8.13787 1.33163C8.22164 1.06725 8.46033 0.882348 8.73726 0.867315C9.01418 0.852283 9.27149 1.01026 9.38338 1.26402L10.3908 3.54869L10.7351 2.84819C10.8974 2.51774 11.297 2.38149 11.6274 2.54386C11.9579 2.70624 12.0941 3.10576 11.9317 3.43621L10.9565 5.42088C10.8422 5.65348 10.6035 5.79876 10.3443 5.7934C10.0852 5.78803 9.85273 5.63299 9.74816 5.39586L8.87568 3.41727L7.6413 7.31292C7.55439 7.58721 7.3013 7.77479 7.01359 7.77817C6.72587 7.78154 6.46846 7.59994 6.37514 7.32776L4.90902 3.05158L3.98621 6.07259C3.90245 6.34682 3.65271 6.53677 3.36606 6.54427C3.07942 6.55177 2.82009 6.37513 2.7221 6.10565L1.87817 3.78478L1.29144 5.35962C1.1629 5.70464 0.778999 5.88013 0.433977 5.75159C0.0889546 5.62305 -0.086536 5.23915 0.0420076 4.89412L1.26106 1.62209C1.35859 1.36029 1.60902 1.18708 1.8884 1.18818C2.16777 1.18928 2.41683 1.36446 2.5123 1.62702L3.29365 3.77579L4.23486 0.694565C4.31929 0.418163 4.57219 0.227642 4.86116 0.222752Z"
									fill="#1890FF"
									fill-opacity="0.65"
								/>
							</svg>
						</i>
						{{ item.duration_chg | filter_change }}
					</div>
					<div v-show="show1" style="display: flex; justify-content: center; text-align: center" class="icon_bg">
						<span class="boxclass11">{{ item.credit_level }}</span>
					</div>
					<div v-show="show2" style="display: flex; justify-content: center; text-align: center" class="icon_bg width60">
						<span class="boxclass11">{{ item.creditseeking }}</span>
					</div>
					<div class="icon_bg" style="display: flex; justify-content: center; text-align: center" v-show="show3">
						<span class="boxclass11">{{ item.interest_credit_ratio }}</span>
					</div>
					<div class="icon_bg" style="display: flex; justify-content: center; text-align: center" v-show="show4">
						<span class="boxclass11">{{ Number(item.beta_equivalent).toFixed(2) }}</span>
					</div>
					<div class="icon_bg" style="display: flex; justify-content: center; text-align: center" v-show="show5">
						<span class="boxclass11">{{ (Number(item.alpher_bond) * 100).toFixed(2) }}%</span>
					</div>
				</div>
			</div>
			<div v-show="isBookValue && info.type != 'cbond' && info.type != 'bond'" class="noTableBorder" style="height: 170px">
				<el-table :data="table" height="200px">
					<el-table-column sortable prop="year" align="gotoleft" label="时间">
						<template slot-scope="scope">
							<div>{{ scope.row.year }}</div>
						</template>
					</el-table-column>
					<el-table-column props="time_start" :show-overflow-tooltip="true" align="gotoleft" label="Q1">
						<template slot-scope="scope">
							<div>{{ scope.row['1'] }}</div>
						</template>
					</el-table-column>
					<el-table-column props="time_end" :show-overflow-tooltip="true" align="gotoleft" label="Q2">
						<template slot-scope="scope">
							<div>{{ scope.row['2'] }}</div>
						</template>
					</el-table-column>
					<el-table-column props="time_end" :show-overflow-tooltip="true" align="gotoleft" label="Q3">
						<template slot-scope="scope">
							<div>{{ scope.row['3'] }}</div>
						</template>
					</el-table-column>
					<el-table-column props="time_end" :show-overflow-tooltip="true" align="gotoleft" label="Q4">
						<template slot-scope="scope">
							<div>{{ scope.row['4'] }}</div>
						</template>
					</el-table-column>
				</el-table>
			</div>
			<div v-loading="loading2" v-if="info.type == 'bond' || info.type == 'cbond'" style="display: flex; cursor: pointer; width: 100%">
				<v-chart
					style="height: 300px; flex: 1; min-width: 150px"
					autoresize
					v-if="show1"
					:options="changingOption1"
					ref="industryChangeChart"
				/>
				<v-chart
					style="height: 300px; flex: 1; min-width: 150px"
					autoresize
					v-if="show2"
					:options="changingOption2"
					ref="industryChangeChart2"
				/>
				<v-chart
					style="height: 300px; flex: 1; min-width: 150px"
					autoresize
					v-show="show3 && info.type != 'cbond'"
					:options="changingOption3"
					ref="industryChangeChart3"
				/>
			</div>
		</div>
	</div>
</template>

<script>
import { exportTitle, exportTable, exportChart } from '@/utils/exportWord.js';
// 债基风格
import { getBondStyleYearlyInfo, getPurebondStyleYearlyInfo } from '@/api/pages/Analysis.js';
// 年度风格
export default {
	name: 'debtBasedStyle',
	data() {
		return {
			pauseFlag: true,
			styleList: [],
			loading: true,
			loading2: true,
			info: {},
			fundtype: '',
			show1: false,
			show2: false,
			show3: false,
			show4: false,
			show5: false,
			isBookValue: false,
			table: [],
			changingOption1: {},
			changingOption2: {},
			changingOption3: {},
			list: [],
			industryList: [],
			yearList: [],
			list2: [],
			industryList2: [],
			yearList2: [],
			list3: [],
			industryList3: [],
			yearList3: [],
			show1: true,
			show2: true,
			show3: true,
			index2: 0,
			index3: 0,
			data21: [],
			data22: [],
			data23: [],
			timeOut: null,
			timeOut2: null,
			timeOut3: null,
			interval: null,
			interval2: null,
			interval3: null,
			info: {}
		};
	},
	methods: {
		// 暂停启动
		pause() {
			let that = this;
			this.pauseFlag = !this.pauseFlag;
			if (this.pauseFlag) {
				this.timeOut = setTimeout(function () {
					that.run(that.data21);
				}, 0);
				this.interval = setInterval(function () {
					that.run(that.data21);
				}, 5000);

				this.timeOut2 = setTimeout(function () {
					that.run2(that.data22);
				}, 0);
				this.interval2 = setInterval(function () {
					that.run2(that.data22);
				}, 5000);
				if (this.fundtype != 'cbond') {
					this.timeOut3 = setTimeout(function () {
						that.run3();
					}, 0);
					this.interval3 = setInterval(function () {
						that.run3();
					}, 5000);
				}
			} else {
				for (let i = 1; i < 100000; i++) {
					clearInterval(i);
				}
			}
		},
		// 获取债基风格
		async getBondStyleYearlyInfo() {
			this.loading = true;
			let data = await getBondStyleYearlyInfo({
				code: this.info.code,
				flag: this.info.flag,
				type: this.info.type,
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			this.loading = false;
			if (data?.mtycode == 200) {
				return data?.data;
			} else {
				return [];
			}
		},
		// 获取除固收外债券风格
		async getPurebondStyleYearlyInfo() {
			this.loading = true;
			let data = await getPurebondStyleYearlyInfo({
				code: this.info.code,
				type: this.info.type,
				flag: this.info.flag,
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			console.warn(this.loading);
			this.loading = false;
			if (data?.mtycode == 200) {
				this.styleList = data?.data?.reverse()?.slice(0, 4);
			}
		},
		// 获取数据
		getData(info) {
			this.info = info;
			if (this.timeOut) {
				this.timeOut = null;
				clearTimeout(this.timeOut);
			}
			if (this.timeOut2) {
				this.timeOut2 = null;
				clearTimeout(this.timeOut2);
			}
			if (this.timeOut3) {
				this.timeOut3 = null;
				clearTimeout(this.timeOut3);
			}
			if (this.interval) {
				this.interval = null;
				clearInterval(this.interval);
			}
			if (this.interval2) {
				this.interval2 = null;
				clearInterval(this.interval2);
			}
			if (this.interval3) {
				this.interval3 = null;
				clearInterval(this.interval3);
			}
			for (let i = 1; i < 5; i++) {
				clearInterval(i);
			}
			if (info.isbookvalue) {
				this.isBookValue = info.isbookvalue;
			} else {
				this.isBookValue = false;
			}
			this.fundtype = info.type;
			this.info = info;
			this.getShow();
			this.loading = false;

			if (this.info.type == 'bond' || this.info.type == 'obond' || this.info.type == 'cbond') {
				this.getBondData();
			} else {
				this.getPurebondStyleYearlyInfo();
			}
		},

		// 获取固收+和转债
		async getBondData() {
			let data = await this.getBondStyleYearlyInfo();
			if (!data && typeof data !== 'object') {
				return;
			}
			if (this.fundtype == 'bond') {
				if (JSON.stringify(data?.industry_sector)) {
					this.drawpic(data?.industry_sector);
					this.show1 = true;
				} else {
					this.show1 = false;
				}
				if (JSON.stringify(data?.bond_sector)) {
					this.drawpic2(data?.bond_sector);
					this.show2 = true;
				} else {
					this.show2 = false;
				}
				if (JSON.stringify(data.style)) {
					this.drawpic3(data.style);
					this.show3 = true;
				} else {
					this.show3 = false;
				}
				if (JSON.stringify(data?.bond_sector)) {
					this.drawpic2(data?.bond_sector);
					this.show2 = true;
				} else {
					this.show2 = false;
				}
				if (JSON.stringify(data?.style)) {
					this.drawpic3(data?.style);
					this.show3 = true;
				} else {
					this.show3 = false;
				}
			} else if (this.fundtype == 'cbond') {
				if (JSON.stringify(data?.industry_sector)) {
					this.drawpic(data?.industry_sector, data?.obs_ratio);
					this.show1 = true;
				} else {
					this.show1 = false;
				}
				if (JSON.stringify(data?.cbond_style)) {
					this.drawpic2(data.cbond_style, data?.obs_ratio);
					this.show2 = true;
				} else {
					this.show2 = false;
				}
				this.show3 = false;
			}

			// if(this.table&&this.table.length>0) {

			// }
			this.table = data.data?.bookvalue.reverse() || [];
		},
		// 行业
		drawpic(data, data2) {
			this.loading2 = false;
			console.log(data);
			this.list = [];
			this.industryList = Array.from(
				new Set(
					data.map((item) => {
						return item.industry_section;
					})
				)
			);
			this.yearList = Array.from(
				new Set(
					data.map((item) => {
						return item.yearqtr;
					})
				)
			).sort();
			this.yearList.map((yearqtr, index) => {
				this.list.push({
					yearqtr,
					data: []
				});
				this.industryList.map((industry) => {
					this.list[index].data.push({
						industry,
						data: 0
					});
				});
			});
			data.map((item) => {
				this.list.map((obj, index) => {
					if (item.yearqtr == obj.yearqtr) {
						obj.data.map((industry) => {
							if (item.industry_section == industry.industry) {
								industry.data = item.weight ? Number(item.weight).toFixed(2) : 0;
							}
						});
					}
				});
			});
			let that = this;
			this.color = [
				'#DB7093',
				'#DA70D6',
				'#800080',
				'#9370DB',
				'#6A5ACD',
				'#4169E1',
				'#B0C4DE',
				'#4682B4',
				'#FDDBC7',
				'#F3A483',
				'#D45C4E',
				'#409eff',
				'#f39c12',
				'#ff1744',
				'#d500f9',
				'#2979ff',
				'#00e5ff',
				'#ff5722',
				'#ffea00',
				'#ff3d00',
				'#ff8a80',
				'#ff80ab',
				'#b388ff',
				'#8c9eff',
				'#a7ffeb',
				'#ffff00',
				'#ffab40',
				'#ffebee',
				'#e8eaf6',
				'#e1f5fe',
				'#fffde7',
				'#efebe9'
			];
			this.index = 0;
			this.changingOption1 = {
				xAxis: {
					margin: 12,
					// max: Math.ceil(max),
					axisLabel: {
						fontSize: '16px',
						color: 'rgba(0,0,0,0.65)'
					},
					axisLine: {
						show: false,
						lineStyle: {
							color: '#e9e9e9'
						}
					},
					splitLine: {
						lineStyle: {
							type: 'dashed'
						}
					},
					axisTick: {
						show: false
					}
				},
				grid: { top: '0', left: 0, right: 20, bottom: '40px', containLabel: true },
				yAxis: {
					margin: 16,
					type: 'category',
					data: this.industryList,
					inverse: true,
					axisLabel: {
						fontSize: '8px',
						color: 'rgba(0,0,0,0.65)'
					},
					splitLine: {
						lineStyle: {
							type: 'dashed'
						}
					},
					axisLine: {
						lineStyle: {
							color: '#e9e9e9'
						}
					},
					axisTick: {
						show: false
					},
					animationDuration: 300,
					animationDurationUpdate: 300
				},
				series: [
					{
						type: 'bar',
						realtimeSort: true,
						label: {
							show: true,
							position: 'right',
							formatter: function (val) {
								return val.name;
							}
						},
						data: this.list
							.filter((item) => {
								return this.yearList[this.index] == item.yearqtr;
							})[0]
							.data.map((item) => {
								return item.data;
							}),
						itemStyle: {
							color: function (param) {
								return that.color[
									that.industryList.findIndex((item) => {
										return param.name == item;
									})
								];
							}
						}
					}
				],
				legend: {
					show: false,
					textStyle: {
						fontSize: '16px'
					}
				},
				graphic: {
					elements: [
						{
							type: 'text',
							right: '30px',
							bottom: '60px',
							// align: 'center',
							style: {
								textAlign: 'center',
								text: this.yearList[this.index],
								// this.info.type != 'cbond'
								// 	? this.yearList[this.index]
								// 	: this.yearList[this.index] +
								// 	  '(' +
								// 	  this.getHigh(data2[data2.findIndex((item) => item.yearqtr == this.yearList[this.index])]?.obs_ratio) +
								// 	  ')',
								font: 'bolder 14px monospace',
								fill: 'rgba(100, 100, 100, 0.25)',
								fontSize: 12
							},
							z: 100
						}
					]
				}
			};
			this.timeOut = null;
			this.interval = null;
			console.warn(this.changingOption1);
			this.setRun(data2);
		},
		getHigh(data) {
			if (Number(data) > 0 && Number(data) <= 20) return '可信度低';
			else if (Number(data) > 20 && Number(data) <= 40) return '可信度偏低';
			else if (Number(data) > 40 && Number(data) <= 60) return '可信度中';
			else if (Number(data) > 60 && Number(data) <= 80) return '可信度偏高';
			else if (Number(data) > 80 && Number(data) <= 1000) return '可信度高';
			else return '可信度未知';
			return;
		},
		run(data2) {
			let that = this;
			this.index = this.index + 1;
			if (this.index == this.yearList.length) {
				this.index = 0;
			}
			let val = [
				...this.list
					.filter((item) => {
						return this.yearList[this.index] == item.yearqtr;
					})[0]
					.data.map((item) => {
						return item.data;
					})
			];
			let sortList = val
				.map((item, i) => {
					return { name: that.industryList[i], value: item };
				})
				.sort((a, b) => {
					return b.value - a.value;
				});
			let option = {
				animationDuration: 0,
				animationDurationUpdate: 3000,
				animationEasing: 'linear',
				animationEasingUpdate: 'linear',
				yAxis: {
					margin: 16,
					type: 'category',
					data: sortList.map((item) => {
						return item.name;
					}),
					splitLine: {
						lineStyle: {
							type: 'dashed'
						}
					},
					axisLabel: {
						fontSize: '8px',
						color: 'rgba(0,0,0,0.65)'
					},
					axisLine: {
						lineStyle: {
							color: '#e9e9e9'
						}
					},
					axisTick: {
						show: false
					},
					inverse: true,
					animationDuration: 300,
					animationDurationUpdate: 300
				},
				graphic: {
					elements: [
						{
							type: 'text',
							right: '30px',
							bottom: '60px',
							style: {
								textAlign: 'center',
								text: this.yearList[this.index],
								// this.info.type != 'cbond'
								// 	? this.yearList[this.index]
								// 	: this.yearList[this.index] +
								// 	  '(' +
								// 	  this.getHigh(data2[data2.findIndex((item) => item.yearqtr == this.yearList[this.index])]?.obs_ratio) +
								// 	  ')',
								font: 'bolder 80px monospace',
								fill: 'rgba(100, 100, 100, 0.25)'
							},
							z: 100
						}
					]
				},
				series: [
					{
						type: 'bar',
						data: sortList.map((item) => {
							return item.value;
						}),
						realtimeSort: true,
						label: {
							show: true,
							position: 'right',
							formatter: function (val) {
								return val.name;
							}
						},
						itemStyle: {
							color: function (param) {
								return that.color[
									that.industryList.findIndex((item) => {
										return param.name == item;
									})
								];
							}
						}
					}
				]
			};
			// this.option.animationDuration = option.animationDuration;
			// this.option.animationDurationUpdate = option.animationDurationUpdate;
			// this.option.animationEasing = option.animationEasing;
			// this.option.animationEasingUpdate = option.animationEasingUpdate;
			// this.option.yAxis = option.yAxis;
			// this.option.graphic = option.graphic;
			// this.option.series = option.series;
			this.$refs['industryChangeChart']?.mergeOptions(option);
		},
		setRun(data2) {
			this.data21 = data2;
			let that = this;
			if (this.timeOut && this.interval) {
				clearInterval(this.timeOut);
				clearTimeout(this.interval);
				this.timeOut = null;
				this.interval = null;
			} else {
				setTimeout(function () {
					this.timeOut = setTimeout(function () {
						that.run(data2);
						that.run2(data2);
						if (that.fundtype != 'cbond') that.run3(data2);
					}, 0);
					this.interval = setInterval(function () {
						that.run(data2);
						that.run2(data2);
						if (that.fundtype != 'cbond') that.run3(data2);
					}, 5000);
				}, 5000);
			}
		},
		// play() {
		// 	this.cardLoading = true;
		// 	setTimeout(() => {
		// 		this.cardLoading = false;
		// 	}, 8000);
		// 	this.setRun();
		// },
		//信用
		drawpic2(data, data2) {
			this.loading2 = false;
			// let max = data.sort((a, b) => {
			// 	return b.weight - a.weight;
			// })[0].weight;
			this.list2 = [];
			this.industryList2 = this.fundtype == 'bond' ? ['信用债', '利率债', '转债'] : ['平底溢价率', '转股溢价率'];
			this.yearList2 = Array.from(
				new Set(
					data.map((item) => {
						return item.yearqtr;
					})
				)
			).sort();
			this.yearList2.map((yearqtr, index) => {
				this.list2.push({
					yearqtr,
					data: []
				});
				this.industryList2.map((industry) => {
					this.list2[index].data.push({
						industry,
						data: 0
					});
				});
			});
			data.map((item) => {
				this.list2.map((obj, index) => {
					if (item.yearqtr == obj.yearqtr) {
						obj.data.map((industry) => {
							if (industry.industry == '信用债') {
								industry.data = item.credit ? Number(item.credit).toFixed(2) : 0;
							} else if (industry.industry == '利率债') {
								industry.data = item.interest ? Number(item.interest).toFixed(2) : 0;
							} else if (industry.industry == '转债') {
								industry.data = item.convertiable ? Number(item.convertiable).toFixed(2) : 0;
							} else if (industry.industry == '平底溢价率') {
								industry.data = item.convertpremiumrate ? Number(item.convertpremiumrate).toFixed(2) : 0;
							} else if (industry.industry == '转股溢价率') {
								industry.data = item.convertparprice ? Number(item.convertparprice).toFixed(2) : 0;
							}
						});
					}
				});
			});
			let that = this;
			this.color = [
				'#DB7093',
				'#DA70D6',
				'#800080',
				'#9370DB',
				'#6A5ACD',
				'#4169E1',
				'#B0C4DE',
				'#4682B4',
				'#FDDBC7',
				'#F3A483',
				'#D45C4E',
				'#409eff',
				'#f39c12',
				'#ff1744',
				'#d500f9',
				'#2979ff',
				'#00e5ff',
				'#ff5722',
				'#ffea00',
				'#ff3d00',
				'#ff8a80',
				'#ff80ab',
				'#b388ff',
				'#8c9eff',
				'#a7ffeb',
				'#ffff00',
				'#ffab40',
				'#ffebee',
				'#e8eaf6',
				'#e1f5fe',
				'#fffde7',
				'#efebe9'
			];
			this.index2 = 0;
			this.changingOption2 = {
				xAxis: {
					margin: 12,
					// max: Math.ceil(max),
					axisLabel: {
						fontSize: '16px',
						color: 'rgba(0,0,0,0.65)'
					},
					axisLine: {
						show: false,
						lineStyle: {
							color: '#e9e9e9'
						}
					},
					splitLine: {
						lineStyle: {
							type: 'dashed'
						}
					},
					axisTick: {
						show: false
					}
				},
				grid: { top: '0', left: 0, right: 20, bottom: '40px', containLabel: true },
				yAxis: {
					margin: 16,
					type: 'category',
					data: this.industryList2,
					inverse: true,
					axisLabel: {
						fontSize: '8px',
						color: 'rgba(0,0,0,0.65)'
					},
					splitLine: {
						lineStyle: {
							type: 'dashed'
						}
					},
					axisLine: {
						lineStyle: {
							color: '#e9e9e9'
						}
					},
					axisTick: {
						show: false
					},
					animationDuration: 300,
					animationDurationUpdate: 300
				},
				series: [
					{
						type: 'bar',
						realtimeSort: true,
						label: {
							show: true,
							position: 'right',
							formatter: function (val) {
								return val.name;
							}
						},
						data: this.list2
							.filter((item) => {
								return this.yearList2[this.index2] == item.yearqtr;
							})[0]
							.data.map((item) => {
								return item.data;
							}),
						itemStyle: {
							color: function (param) {
								return that.color[
									that.industryList2.findIndex((item) => {
										return param.name == item;
									})
								];
							}
						}
					}
				],
				legend: {
					show: false,
					textStyle: {
						fontSize: '16px'
					}
				},
				graphic: {
					elements: [
						{
							type: 'text',
							right: '30px',
							bottom: '60px',
							style: {
								text: this.yearList2[this.index2],
								// this.info.type != 'cbond'
								// 	? this.yearList2[this.index2]
								// 	: this.yearList2[this.index2] +
								// 	  '(' +
								// 	  this.getHigh(data2[data2.findIndex((item) => item.yearqtr == this.yearList2[this.index2])]?.obs_ratio) +
								// 	  ')',
								font: 'bolder 14px monospace',
								fill: 'rgba(100, 100, 100, 0.25)',
								fontSize: 12
							},
							z: 100
						}
					]
				}
			};
			this.timeOut2 = null;
			this.interval2 = null;
			this.setRun2(data2);
		},
		run2(data2) {
			let that = this;
			this.index2 = this.index2 + 1;
			if (this.index2 == this.yearList2.length) {
				this.index2 = 0;
			}
			let val = [
				...this.list2
					.filter((item) => {
						return this.yearList2[this.index2] == item.yearqtr;
					})[0]
					.data.map((item) => {
						return item.data;
					})
			];
			let sortList = val
				.map((item, i) => {
					return { name: that.industryList2[i], value: item };
				})
				.sort((a, b) => {
					return b.value - a.value;
				});
			let option = {
				animationDuration: 0,
				animationDurationUpdate: 3000,
				animationEasing: 'linear',
				animationEasingUpdate: 'linear',
				yAxis: {
					margin: 16,
					type: 'category',
					data: sortList.map((item) => {
						return item.name;
					}),
					splitLine: {
						lineStyle: {
							type: 'dashed'
						}
					},
					axisLabel: {
						fontSize: '8px',
						color: 'rgba(0,0,0,0.65)'
					},
					axisLine: {
						lineStyle: {
							color: '#e9e9e9'
						}
					},
					axisTick: {
						show: false
					},
					inverse: true,
					animationDuration: 300,
					animationDurationUpdate: 300
				},
				graphic: {
					elements: [
						{
							type: 'text',
							right: '30px',
							bottom: '60px',
							style: {
								text: this.yearList2[this.index2],
								// this.info.type != 'cbond'
								// 	? this.yearList2[this.index2]
								// 	: this.yearList2[this.index2] +
								// 	  '(' +
								// 	  this.getHigh(data2[data2.findIndex((item) => item.yearqtr == this.yearList2[this.index2])]?.obs_ratio) +
								// 	  ')',
								font: 'bolder 80px monospace',
								fill: 'rgba(100, 100, 100, 0.25)'
							},
							z: 100
						}
					]
				},
				series: [
					{
						type: 'bar',
						data: sortList.map((item) => {
							return item.value;
						}),
						realtimeSort: true,
						label: {
							show: true,
							position: 'right',
							formatter: function (val) {
								return val.name;
							}
						},
						itemStyle: {
							color: function (param) {
								return that.color[
									that.industryList2.findIndex((item) => {
										return param.name == item;
									})
								];
							}
						}
					}
				]
			};
			// this.option.animationDuration = option.animationDuration;
			// this.option.animationDurationUpdate = option.animationDurationUpdate;
			// this.option.animationEasing = option.animationEasing;
			// this.option.animationEasingUpdate = option.animationEasingUpdate;
			// this.option.yAxis = option.yAxis;
			// this.option.graphic = option.graphic;
			// this.option.series = option.series;
			this.$refs['industryChangeChart2']?.mergeOptions(option);
		},
		setRun2(data2) {
			this.data22 = data2;

			let that = this;
			if (this.timeOut2 && this.interval2) {
				clearInterval(this.timeOut2);
				clearTimeout(this.interval2);
				this.timeOut2 = null;
				this.interval2 = null;
			} else {
				// setTimeout(function () {
				// this.timeOut2 = setTimeout(function () {
				// 	that.run2(data2);
				// }, 0);
				// this.interval2 = setInterval(function () {
				// 	that.run2(data2);
				// }, 5000);
				// }, 5000);
			}
		},
		// mix
		drawpic3(data) {
			this.loading3 = false;
			// let max = data.sort((a, b) => {
			// 	return b.weight - a.weight;
			// })[0].weight;
			this.list3 = [];
			this.industryList3 = ['成长', '价值', '均衡'];
			this.yearList3 = Array.from(
				new Set(
					data.map((item) => {
						return item.yearqtr;
					})
				)
			).sort();
			this.yearList3.map((yearqtr, index) => {
				this.list3.push({
					yearqtr,
					data: []
				});
				this.industryList3.map((industry) => {
					this.list3[index].data.push({
						industry,
						data: 0
					});
				});
			});
			data.map((item) => {
				this.list3.map((obj, index) => {
					if (item.yearqtr == obj.yearqtr) {
						obj.data.map((industry) => {
							if (industry.industry == '成长') {
								industry.data = item.growth ? Number(item.growth).toFixed(2) : 0;
							} else if (industry.industry == '价值') {
								industry.data = item.value ? Number(item.value).toFixed(2) : 0;
							}
							if (industry.industry == '均衡') {
								industry.data = item.mix ? Number(item.mix).toFixed(2) : 0;
							}
						});
					}
				});
			});
			this.color = [
				'#DB7093',
				'#DA70D6',
				'#800080',
				'#9370DB',
				'#6A5ACD',
				'#4169E1',
				'#B0C4DE',
				'#4682B4',
				'#FDDBC7',
				'#F3A483',
				'#D45C4E',
				'#409eff',
				'#f39c12',
				'#ff1744',
				'#d500f9',
				'#2979ff',
				'#00e5ff',
				'#ff5722',
				'#ffea00',
				'#ff3d00',
				'#ff8a80',
				'#ff80ab',
				'#b388ff',
				'#8c9eff',
				'#a7ffeb',
				'#ffff00',
				'#ffab40',
				'#ffebee',
				'#e8eaf6',
				'#e1f5fe',
				'#fffde7',
				'#efebe9'
			];
			let that = this;
			this.index3 = 0;
			this.changingOption3 = {
				xAxis: {
					margin: 12,
					// max: Math.ceil(max),
					axisLabel: {
						fontSize: '16px',
						color: 'rgba(0,0,0,0.65)'
					},
					axisLine: {
						show: false,
						lineStyle: {
							color: '#e9e9e9'
						}
					},
					splitLine: {
						lineStyle: {
							type: 'dashed'
						}
					},
					axisTick: {
						show: false
					}
				},
				grid: { top: '0', left: 0, right: 20, bottom: '40px', containLabel: true },
				yAxis: {
					margin: 16,
					type: 'category',
					data: this.industryList3,
					inverse: true,
					axisLabel: {
						fontSize: '8px',
						color: 'rgba(0,0,0,0.65)'
					},
					splitLine: {
						lineStyle: {
							type: 'dashed'
						}
					},
					axisLine: {
						lineStyle: {
							color: '#e9e9e9'
						}
					},
					axisTick: {
						show: false
					},
					animationDuration: 300,
					animationDurationUpdate: 300
				},
				series: [
					{
						type: 'bar',
						realtimeSort: true,
						label: {
							show: true,
							position: 'right',
							formatter: function (val) {
								return val.name;
							}
						},
						data: this.list3
							.filter((item) => {
								return this.yearList3[this.index3] == item.yearqtr;
							})[0]
							.data.map((item) => {
								return item.data;
							}),
						itemStyle: {
							color: function (param) {
								return that.color[
									that.industryList3.findIndex((item) => {
										return param.name == item;
									})
								];
							}
						}
					}
				],
				legend: {
					show: false,
					textStyle: {
						fontSize: '16px'
					}
				},
				graphic: {
					elements: [
						{
							type: 'text',
							right: '30px',
							bottom: '60px',
							style: {
								text: this.yearList3[this.index3],
								font: 'bolder 14px monospace',
								fill: 'rgba(100, 100, 100, 0.25)',
								fontSize: 12
							},
							z: 100
						}
					]
				}
			};
			this.timeOut3 = null;
			this.interval3 = null;
			this.setRun3();
		},
		run3() {
			let that = this;
			this.index3 = this.index3 + 1;
			if (this.index3 == this.yearList3.length) {
				this.index3 = 0;
			}
			let val = [
				...this.list3
					.filter((item) => {
						return this.yearList3[this.index3] == item.yearqtr;
					})[0]
					.data.map((item) => {
						return item.data;
					})
			];
			let sortList = val
				.map((item, i) => {
					return { name: that.industryList3[i], value: item };
				})
				.sort((a, b) => {
					return b.value - a.value;
				});
			let option = {
				animationDuration: 0,
				animationDurationUpdate: 3000,
				animationEasing: 'linear',
				animationEasingUpdate: 'linear',
				yAxis: {
					margin: 16,
					type: 'category',
					data: sortList.map((item) => {
						return item.name;
					}),
					splitLine: {
						lineStyle: {
							type: 'dashed'
						}
					},
					axisLabel: {
						fontSize: '8px',
						color: 'rgba(0,0,0,0.65)'
					},
					axisLine: {
						lineStyle: {
							color: '#e9e9e9'
						}
					},
					axisTick: {
						show: false
					},
					inverse: true,
					animationDuration: 300,
					animationDurationUpdate: 300
				},
				graphic: {
					elements: [
						{
							type: 'text',
							right: '30px',
							bottom: '60px',
							style: {
								text: this.yearList3[this.index3],
								font: 'bolder 80px monospace',
								fill: 'rgba(100, 100, 100, 0.25)'
							},
							z: 100
						}
					]
				},
				series: [
					{
						type: 'bar',
						data: sortList.map((item) => {
							return item.value;
						}),
						realtimeSort: true,
						label: {
							show: true,
							position: 'right',
							formatter: function (val) {
								return val.name;
							}
						},
						itemStyle: {
							color: function (param) {
								return that.color[
									that.industryList3.findIndex((item) => {
										return param.name == item;
									})
								];
							}
						}
					}
				]
			};
			// this.option.animationDuration = option.animationDuration;
			// this.option.animationDurationUpdate = option.animationDurationUpdate;
			// this.option.animationEasing = option.animationEasing;
			// this.option.animationEasingUpdate = option.animationEasingUpdate;
			// this.option.yAxis = option.yAxis;
			// this.option.graphic = option.graphic;
			// this.option.series = option.series;
			this.$refs['industryChangeChart3']?.mergeOptions(option);
		},
		setRun3() {
			let that = this;
			if (this.timeOut3 && this.interval3) {
				clearInterval(this.timeOut3);
				clearTimeout(this.interval3);
				this.timeOut3 = null;
				this.interval3 = null;
			} else {
				// this.timeOut3 = setTimeout(function () {
				// 	that.run3();
				// }, 0);
				// this.interval3 = setInterval(function () {
				// 	that.run3();
				// }, 5000);
			}
		},
		getShow() {
			if (this.fundtype == 'purebond' || this.fundtype == 'bill') {
				if (this.info.type3 == 'interest' && !this.info.isbookvalue) {
					this.show1 = false;
					this.show2 = false;
					this.show3 = false;
				} else if (this.info.isbookvalue) {
					// TODO 整块不同
					this.show1 = false;
					this.show2 = false;
					this.show3 = false;
				} else {
					this.show1 = true;
					this.show2 = true;
					this.show3 = true;
				}
				this.show4 = false;
				this.show5 = false;
			} else if (this.fundtype == 'bondindex') {
				this.show1 = true;
				this.show2 = true;
				this.show3 = true;
				this.show4 = false;
				this.show5 = false;
			} else if (this.fundtype == 'bond') {
				this.show1 = true;
				this.show2 = true;
				this.show3 = false;
				this.show4 = true;
				this.show5 = true;
			} else if (this.fundtype == 'obond') {
				this.show1 = true;
				this.show2 = true;
				this.show3 = false;
				this.show4 = true;
				this.show5 = true;
			} else if (this.fundtype == 'cbond') {
				this.show1 = true;
				this.show2 = true;
				this.show3 = false;
				this.show4 = true;
				this.show5 = true;
			} else {
				this.show1 = true;
				this.show2 = true;
				this.show3 = false;
				this.show4 = false;
				this.show5 = false;
			}
		},
		//
		format(percentage) {
			switch (percentage) {
				case 20:
					return '短期';
				case 40:
					return '中短期';
				case 60:
					return '中期';
				case 80:
					return '中长期';
				case 100:
					return '长期';
			}
		},
		exportImage() {
			this.html2canvas(document.getElementById('debtBasedStyle'), { scale: 3 }).then(function (canvas) {
				let base64Str = canvas.toDataURL('image/png');
				let aLink = document.createElement('a');
				aLink.style.display = 'none';
				aLink.href = base64Str;
				aLink.download = '年度风格.jpg';
				// 触发点击-然后移除
				document.body.appendChild(aLink);
				aLink.click();
				document.body.removeChild(aLink);
			});
		},
		createPrintWord() {
			if (!this.isBookValue && this.info.type != 'cbond' && this.info.type != 'bond') {
				let list = [
					{ label: '时间', value: 'year' },
					{ label: '久期分析', value: 'duration_level' },
					{ label: '久期变化', value: 'duration_change' },
					{ label: '信用等级', value: 'credit_level' },
					{ label: '信用挖掘', value: 'down_credit' },
					{ label: '券种特征', value: 'interest_credit_ratio' },
					{ label: '等效ß', value: 'beta_equivalent' },
					{ label: 'alpha', value: 'alpha' }
				];
				return [...exportTitle('年度风格'), ...exportTable(list, this.styleList)];
			} else {
				return [];
			}
		}
	}
};
</script>

<style scoped>
.hamburger {
	display: inline-block;
	vertical-align: middle;
	fill: #fff;
}
.boxclassCbond {
	width: 100%;
	height: 32px;
	display: flex;
	align-items: center;
	justify-content: center;
}
.th_bg {
	margin: 3px;
	flex: 1;
	font-size: 14px;
	font-weight: 500;
	font-family: 'PingFang';
}
.borderbottom2px {
	font-size: 16px;
	border-bottom: 2px solid #e85d2d;
}
.icon_bg {
	margin: 3px;
	flex: 1;
	background: #e6f7ff;
	border: 1px solid #91d5ff;
	display: flex;
	color: #1890ff;
	align-items: center;
	border-radius: 4px;
}
.icon_bg > div {
	width: 100%;
}
.fs20 {
	font-size: 20px;
}
</style>
