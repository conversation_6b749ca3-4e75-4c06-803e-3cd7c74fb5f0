<template>
  <div class="chart_one"
       v-show="show">
    <div class="title mb-16">转债股性债性分析</div>
    <div>
      <el-table :data="data"
                v-loading="loading"
                style="width: 100%">
        <el-table-column v-for="item in column"
                         :key="item.value"
                         :prop="item.value"
                         :label="item.label"
                         align="gotoleft">
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { getCbondConvert } from '@/api/pages/SystemMixed.js';
export default {
  data () {
    return {
      data: [],
      column: [
        {
          label: '转债名称',
          value: 'name'
        },
        {
          label: '季度',
          value: 'yearqtr'
        },
        {
          label: '平底溢价率',
          value: ''
        },
        {
          label: '转股溢价率',
          value: ''
        },
        {
          label: '股性债性',
          value: ''
        },
        {
          label: '规模',
          value: 'netasset'
        }
      ],
      info: {},
      loading: true,
      show: true
    };
  },
  props: {
    ismanager: {
      type: Boolean,
    }
  },
  methods: {
    getData (info) {
      this.info = info;
      this.getCbondConvert();
    },
    async getCbondConvert () {
      this.loading = true;
      let data = await getCbondConvert({
        ids: [
          ...this.info['code_list'].map((item) => {
            return {
              code: item.code,
              type: this.$router?.history?.current?.path.indexOf('manage') ? 'manager' : 'fund'
            };
          })
        ],
        yearqtr: this.info.quarter,
        insert_time: this.info.date,
        flag: this.info.flag,
        ismanager: this.ismanager
      });
      this.loading = false;
      if (data?.mtycode == 200) {
        this.show = true;
      } else {
        this.show = false;
      }
    }
  }
};
</script>

<style></style>
