<template>
	<div class="charts_fill_class" v-loading="loading">
		<v-chart
			ref="barLineChartComponent"
			class="charts_one_class"
			autoresize
			element-loading-text="暂无数据"
			element-loading-spinner="el-icon-document-delete"
			element-loading-background="rgba(239, 239, 239, 0.5)"
			:options="option"
			@legendselectchanged="legendselectchanged"
		/>
	</div>
</template>

<script>
import vChart from 'vue-echarts';
import { barChartOption } from '@/utils/chartStyle.js';

export default {
	components: { vChart },
	data() {
		return {
			option: {},
			loading: true,
			styleColor: [
				'rgba(253, 156, 255, 0.7)',
				'rgba(254, 208, 238, 0.7)',
				'rgba(254, 174, 174, 0.7)',
				'rgba(253, 208, 159, 0.7)',
				'rgba(251, 227, 142, 0.7)',
				'rgba(169, 244, 208, 0.7)',
				'rgba(208, 232, 255, 0.7)',
				'rgba(159, 212, 253, 0.7)',
				'rgba(174, 201, 254, 0.7)',
				'rgba(219, 174, 255, 0.7)',
				'rgba(154, 137, 255, 0.7)',
				'#FD9CFF',
				'#FED0EE',
				'#FEAEAE',
				'#FDD09F',
				'#FBE38E',
				'#A9F4D0',
				'#D0E8FF',
				'#9FD4FD',
				'#AEC9FE',
				'#DBAEFF',
				'#9A89FF'
			]
		};
	},
	methods: {
		getData({ series, legend, xAxis, dateDayList }) {
			this.loading = false;
			this.$nextTick(() => {
				this.option = barChartOption({
					// color: this.styleColor,
					toolbox: false,
					legend,
					dataZoom: true,
					xAxis: [
						{ data: xAxis, isAlign: true },
						{ data: dateDayList, show: false }
					],
					yAxis: [
						{
							formatter: function (value) {
								return value + '亿';
							}
						},
						{
							formatter: function (value) {
								return value + '只';
							}
						}
					],
					series
				});
			});
		},
		legendselectchanged(val) {
			this.$emit('legendselectchanged', val.selected);
		},
		createPrintWord() {
			this.$refs['barLineChartComponent'].mergeOptions({ toolbox: { show: false } });
			let chart = this.$refs['barLineChartComponent'].getConnectedDataURL({
				type: 'jpg',
				pixelRatio: 2,
				backgroundColor: '#fff',
				excludeComponents: ['dataZoom']
			});
			this.$refs['barLineChartComponent'].mergeOptions({ toolbox: { show: true } });
			return chart;
		}
	}
};
</script>

<style></style>
