<template>
	<div class="analysis_main">
		<el-breadcrumb separator="/">
			<el-breadcrumb-item :to="{ path: '/subscriptionCenter' }">订阅中心</el-breadcrumb-item>
			<el-breadcrumb-item>新增报告模板</el-breadcrumb-item>
		</el-breadcrumb>
		<div class="chart_one">
			<div class="subscription_form">
				<div class="subscription_form_item">
					<div class="subscription_title">模板名称</div>
					<div>
						<el-input v-model="name" placeholder="请输入" style="width: 440px"></el-input>
					</div>
				</div>
				<div class="subscription_form_item">
					<div class="subscription_title">模板简介</div>
					<div>
						<el-input type="textarea" v-model="des" placeholder="请输入内容" :rows="1" style="width: 668px"></el-input>
					</div>
				</div>
				<!-- <div class="subscription_form_item">
          <div style="margin-bottom: 16px">选择基金/基金经理模板</div>
          <div
            v-for="(obj, index) in subscriptionList"
            :key="index"
            @mouseenter="mouseenterItem(index)"
            @mouseleave="hoverIndex = null"
            :style="
              hoverIndex == index
                ? 'background: rgba(0, 0, 0, 0.04);margin-bottom: 8px'
                : 'margin-bottom: 8px'
            "
          >
            <div class="subscription_title subscription_content">
              <el-select
                v-model="obj.type"
                placeholder=""
                style="width: 144px"
                @change="changeType"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  :disabled="activeTypeList.indexOf(item.value) != -1"
                >
                </el-option>
              </el-select>
              <div style="margin-left: 8px">
                <el-select
                  v-model="obj.model"
                  placeholder="请选择打印模版"
                  style="width: 400px"
                >
                  <el-option
                    v-for="item in obj.modelList"
                    :key="item.model_id"
                    :label="item.model_name"
                    :value="item.model_id"
                  >
                  </el-option>
                </el-select>
                <el-link style="margin-left: 16px" @click="goAddModel(obj)"
                  >没有想要的？前往新增模版</el-link
                >
              </div>
              <div
                class="delete_icon"
                style="margin-top: 4px"
                v-show="index == hoverIndex && subscriptionList.length > 1"
                @click="deleteItem(index)"
              >
                <svg
                  width="14"
                  height="14"
                  viewBox="0 0 14 14"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M4.62476 1.8736H4.49976C4.56851 1.8736 4.62476 1.81735 4.62476 1.7486V1.8736H9.37476V1.7486C9.37476 1.81735 9.43101 1.8736 9.49976 1.8736H9.37476V2.9986H10.4998V1.7486C10.4998 1.19703 10.0513 0.748596 9.49976 0.748596H4.49976C3.94819 0.748596 3.49976 1.19703 3.49976 1.7486V2.9986H4.62476V1.8736ZM12.4998 2.9986H1.49976C1.22319 2.9986 0.999756 3.22203 0.999756 3.4986V3.9986C0.999756 4.06735 1.05601 4.1236 1.12476 4.1236H2.06851L2.45444 12.2955C2.47944 12.8283 2.92007 13.2486 3.45288 13.2486H10.5466C11.081 13.2486 11.5201 12.8298 11.5451 12.2955L11.931 4.1236H12.8748C12.9435 4.1236 12.9998 4.06735 12.9998 3.9986V3.4986C12.9998 3.22203 12.7763 2.9986 12.4998 2.9986ZM10.4263 12.1236H3.57319L3.19507 4.1236H10.8044L10.4263 12.1236Z"
                    fill="black"
                    fill-opacity="0.45"
                  />
                </svg>
              </div>
            </div>
          </div>
          <div
            class="add_subscription"
            v-show="activeTypeList.length != options.length"
            style="width: 551px"
            @click="addSubscription"
          >
            <svg
              width="14"
              height="14"
              viewBox="0 0 14 14"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M7.46875 1.375C7.55208 1.375 7.59375 1.41667 7.59375 1.5L7.593 6.406L12.25 6.40625C12.3333 6.40625 12.375 6.44792 12.375 6.53125V7.46875C12.375 7.55208 12.3333 7.59375 12.25 7.59375L7.593 7.593L7.59375 12.5C7.59375 12.5833 7.55208 12.625 7.46875 12.625H6.53125C6.44792 12.625 6.40625 12.5833 6.40625 12.5L6.406 7.593L1.75 7.59375C1.66667 7.59375 1.625 7.55208 1.625 7.46875V6.53125C1.625 6.44792 1.66667 6.40625 1.75 6.40625L6.406 6.406L6.40625 1.5C6.40625 1.41667 6.44792 1.375 6.53125 1.375H7.46875Z"
                fill="black"
                fill-opacity="0.65"
              />
            </svg>
            <span>添加自定义模板</span>
          </div>
        </div>-->
				<div class="subscription_form_item">
					<div class="subscription_title">自定义logo</div>
					<div>
						<el-upload
							class="avatar-uploader"
							:action="$baseUrl + '/subscription/readFile'"
							:show-file-list="false"
							:on-success="uploadOver"
							:headers="{ Authorization: token }"
							accept="image/jpg, image/jpeg, image/png"
						>
							<img v-show="image" id="img" :src="'data:image/png;base64,' + image" class="avatar" />
							<i v-show="!image" class="el-icon-plus avatar-uploader-icon"></i>
						</el-upload>
					</div>
				</div>
				<div class="subscription_form_item">
					<div class="subscription_title">自定义报告介绍</div>
					<div style="margin-bottom: 8px">
						<span style="margin-right: 8px">类型:</span>
						<el-select v-model="custom_introduce_type" placeholder style="width: 104px">
							<el-option label="文字" value="text"></el-option>
							<el-option label="图片" value="image"></el-option>
						</el-select>
					</div>
					<div style="display: flex">
						<span style="margin-right: 8px">内容:</span>
						<el-input
							v-show="custom_introduce_type == 'text'"
							v-model="custom_introduce"
							type="textarea"
							placeholder="请输入介绍"
							:rows="3"
							style="width: 668px"
						></el-input>
						<el-upload
							v-show="custom_introduce_type == 'image'"
							class="content-uploader"
							:action="$baseUrl + '/readFile'"
							:show-file-list="false"
							:on-success="uploadIntroduceOver"
							accept="image/jpg, image/jpeg, image/png"
						>
							<img v-show="imageIntroduce" id="img" :src="'data:image/png;base64,' + imageIntroduce" class="avatar" />
							<i v-show="!imageIntroduce" class="el-icon-plus avatar-uploader-icon"></i>
						</el-upload>
					</div>
				</div>
				<div class="subscription_form_item">
					<div class="subscription_title">自定义法律声明/免责声明</div>
					<div
						v-for="(item, index) in customDescription"
						:key="index"
						@mouseenter="mouseenterCustomItem(index)"
						@mouseleave="customIndex = null"
						:style="customIndex == index ? 'background: rgba(0, 0, 0, 0.04)' : ''"
					>
						<div style="display: flex; margin-bottom: 8px">
							<span style="margin-right: 8px">标题:</span>
							<el-input v-model="item.title" placeholder="请输入内容" style="width: 668px"></el-input>
							<div class="delete_icon" v-show="index == customIndex && customDescription.length > 1" @click="deleteCustomItem(index)">
								<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path
										d="M4.62476 1.8736H4.49976C4.56851 1.8736 4.62476 1.81735 4.62476 1.7486V1.8736H9.37476V1.7486C9.37476 1.81735 9.43101 1.8736 9.49976 1.8736H9.37476V2.9986H10.4998V1.7486C10.4998 1.19703 10.0513 0.748596 9.49976 0.748596H4.49976C3.94819 0.748596 3.49976 1.19703 3.49976 1.7486V2.9986H4.62476V1.8736ZM12.4998 2.9986H1.49976C1.22319 2.9986 0.999756 3.22203 0.999756 3.4986V3.9986C0.999756 4.06735 1.05601 4.1236 1.12476 4.1236H2.06851L2.45444 12.2955C2.47944 12.8283 2.92007 13.2486 3.45288 13.2486H10.5466C11.081 13.2486 11.5201 12.8298 11.5451 12.2955L11.931 4.1236H12.8748C12.9435 4.1236 12.9998 4.06735 12.9998 3.9986V3.4986C12.9998 3.22203 12.7763 2.9986 12.4998 2.9986ZM10.4263 12.1236H3.57319L3.19507 4.1236H10.8044L10.4263 12.1236Z"
										fill="black"
										fill-opacity="0.45"
									/>
								</svg>
							</div>
						</div>
						<div style="display: flex; margin-bottom: 8px">
							<span style="margin-right: 8px">说明:</span>
							<el-input type="textarea" v-model="item.content" placeholder="请输入内容" :rows="3" style="width: 668px"></el-input>
						</div>
					</div>
					<div class="add_subscription" style="margin-left: 40px; width: 669px" @click="addCustomDescription">
						<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path
								fill-rule="evenodd"
								clip-rule="evenodd"
								d="M7.46875 1.375C7.55208 1.375 7.59375 1.41667 7.59375 1.5L7.593 6.406L12.25 6.40625C12.3333 6.40625 12.375 6.44792 12.375 6.53125V7.46875C12.375 7.55208 12.3333 7.59375 12.25 7.59375L7.593 7.593L7.59375 12.5C7.59375 12.5833 7.55208 12.625 7.46875 12.625H6.53125C6.44792 12.625 6.40625 12.5833 6.40625 12.5L6.406 7.593L1.75 7.59375C1.66667 7.59375 1.625 7.55208 1.625 7.46875V6.53125C1.625 6.44792 1.66667 6.40625 1.75 6.40625L6.406 6.406L6.40625 1.5C6.40625 1.41667 6.44792 1.375 6.53125 1.375H7.46875Z"
								fill="black"
								fill-opacity="0.65"
							/>
						</svg>
						<span>添加声明</span>
					</div>
				</div>
				<div class="subscription_form_item">
					<el-button type="primary" @click="saveTemplateDialog">提交</el-button>
					<el-button @click="goBack">取消</el-button>
				</div>
			</div>
		</div>
		<model-save-dialog ref="modelSaveDialog" @resolveTemplateInfo="submit"></model-save-dialog>
	</div>
</template>

<script>
import emailGlobal from './components/emailGlobal.vue';
import searchComponent from '@/components/components/components/search/index.vue';
import modelSaveDialog from '@/components/components/components/modelSaveDialog/index';

// 获取组件json配置
import { saveModel } from '@/api/pages/Tools.js';

import { getUserConfig } from '@/api/pages/SystemAlpha.js';
import { getReportTemplateList, postReportTemplate, putReportTemplate, getReportTemplateDetail } from '@/api/pages/NodeServer.js';
import store from '@/store/store';

import { alphaGo } from '@/assets/js/alpha_type.js';
export default {
	components: {
		emailGlobal,
		searchComponent,
		modelSaveDialog
	},
	data() {
		return {
			custom_introduce_type: 'text',
			custom_introduce: '',
			name: '',
			des: '',
			imageId: '',
			image: '',
			imageIntroduce: '',
			customDescription: [
				{
					title: '',
					content: ''
				}
			],
			hoverIndex: null,
			customIndex: null,
			status: true,
			current: 0,
			imgage: '',
			options: [
				{
					label: '主动权益型基金',
					value: 'equity',
					flag: 'fund',
					default: '003834',
					name: '华夏能源革新股票A'
				},
				{
					label: '含港股主动权益型基金',
					value: 'equitywithhk',
					flag: 'fund',
					default: '010709',
					name: '安信医药健康股票A'
				},
				{
					label: '港股型基金',
					value: 'equityhk',
					flag: 'fund',
					default: '005847',
					name: '富国沪港深业绩驱动混合型A'
				},
				{
					label: '被动权益型基金',
					value: 'equityindex',
					flag: 'fund',
					default: '510050',
					name: '华夏上证50ETF'
				},
				{
					label: '固收+型基金',
					value: 'bond',
					flag: 'fund',
					default: '110007',
					name: '易方达稳健收益债券A'
				},
				{
					label: '纯债型基金',
					value: 'purebond',
					flag: 'fund',
					default: '004200',
					name: '博时富瑞纯债债券A'
				},
				{
					label: '中短债型基金',
					value: 'bill',
					flag: 'fund',
					default: ''
				},
				{
					label: '可转债型基金',
					value: 'cbond',
					flag: 'fund',
					default: '000003',
					name: '中海可转债债券A'
				},
				{
					label: 'FOF型基金',
					value: 'fof',
					flag: 'fund',
					default: '006880',
					name: '交银安享稳健养老一年(FOF)'
				},
				{
					label: '基金经理',
					value: 'manager',
					flag: 'manager',
					default: '30075384',
					name: '张清华'
				}
			],
			info: {},
			model: '',
			modelList: [],
			interval: '',
			intervalList: [
				{
					label: '1天',
					value: '1'
				},
				{
					label: '2天',
					value: '2'
				},
				{
					label: '3天',
					value: '3'
				},
				{
					label: '4天',
					value: '4'
				},
				{
					label: '5天',
					value: '5'
				},
				{
					label: '6天',
					value: '6'
				}
			],
			emailList: [],
			subscriptionList: [
				{
					type: 'equity',
					name: '',
					model: '',
					modelList: [],
					flag: 'fund'
				}
			],
			userConfigList: [],
			poolList: [],
			activeTypeList: ['equity'],
			template_id: null
		};
	},
	computed: {
		dayList() {
			return this.intervalType == 'week' ? this.weekList : this.intervalType == 'month' ? this.monthList : [];
		},
		token() {
			return 'Bearer ' + store.state.token;
		}
	},
	async mounted() {
		this.template_id = this.$route.query?.id || null;
		await this.getUserConfig();
		if (this.template_id) {
			this.getReportTemplateDetail();
		}
	},
	methods: {
		// 获取用户设置模版
		async getUserConfig() {
			let data = await getUserConfig();
			if (data?.mtycode == 200) {
				this.userConfigList = data?.data;
				this.matchingModelList();
				// this.filterUserConfig();
			}
		},
		// 获取报告模板详情
		async getReportTemplateDetail() {
			let data = await getReportTemplateDetail({
				template_id: this.template_id
			});
			if (data?.mtycode == 200) {
				this.name = data?.data.template_name;
				this.des = data?.data.template_description;
				this.subscriptionList = data?.data.fund_manager_model_list?.map((item) => {
					return {
						...item,
						model: item.model_id
					};
				});
				this.image = data?.data.custom_logo;
				this.custom_introduce_type = data?.data.custom_introduce?.type;
				this.custom_introduce_type == 'text'
					? (this.custom_introduce = data?.data.custom_introduce?.content)
					: (this.imageIntroduce = data?.data.custom_introduce?.content);
				this.customDescription = data?.data.custom_description;
				this.matchingModelList();
			}
		},
		// 过滤模版数据
		filterUserConfig() {
			let data = this.userConfigList;
			if (this.info.flag == 'fund') {
				this.subscriptionList[this.current].modelList = data.filter((item) => {
					return !item.ismanager && item.type == this.info.type;
				});
			} else {
				this.subscriptionList[this.current].modelList = data.filter((item) => {
					return item.ismanager;
				});
			}
			this.subscriptionList[this.current].model = this.subscriptionList[this.current].modelList?.[0]?.model_id;
		},
		// 匹配对应模板列表
		matchingModelList() {
			this.$nextTick(() => {
				this.subscriptionList = this.subscriptionList.map((item) => {
					console.log(item, this.userConfigList);
					let modelList = this.userConfigList.filter((obj) => {
						return (obj.ismanager && item.flag == 'manager') || (!obj.ismanager && item.flag == 'fund' && obj.type == item.type);
					});
					return {
						...item,
						modelList
					};
				});
			});

			console.log(this.subscriptionList);
		},
		// 鼠标移入item
		mouseenterItem(index) {
			this.hoverIndex = index;
			this.current = index;
		},
		mouseenterCustomItem(index) {
			this.customIndex = index;
		},
		// 获取选择基金基本信息
		getFundInfo(val) {
			this.info = {
				...val,
				code: val.id
			};
			this.subscriptionList[this.current].info = this.info;
			// this.getUserConfig();
			this.filterUserConfig();
		},
		// 获取池子信息
		changePool(val) {
			this.subscriptionList[this.current]['info'] = {
				code: val,
				name: this.poolList.filter((item) => {
					return item.id == val;
				})?.[0]?.name,
				flag: 'pool',
				type: '*'
			};
		},
		// 选择订阅邮箱
		async chooseEmail() {
			this.$refs['emailGlobal'].getData(this.emailList);
		},
		// 添加订阅
		addSubscription() {
			let type = this.options.find((item) => {
				return this.activeTypeList.indexOf(item.value) == -1;
			})?.value;
			this.activeTypeList.push(type);
			this.subscriptionList.push({
				type,
				name: '',
				model: '',
				flag: type == 'manager' ? type : 'fund',
				modelList: []
			});
			this.matchingModelList();
		},
		// 添加声明
		addCustomDescription() {
			this.customDescription.push({
				title: '',
				content: ''
			});
		},
		// 前往新增模版
		goAddModel(item) {
			let info = this.options.find((obj) => {
				return obj.value == item.type;
			});
			this.$router.push(
				'/reportCenter?code=' + info.default + '&type=' + info.value + '&flag=' + item.flag + '&name=' + info.name + '&showType=show'
			);
		},
		// 删除条目
		deleteItem(index) {
			this.subscriptionList.splice(index, 1);
		},
		deleteCustomItem(index) {
			this.customDescription.splice(index, 1);
		},
		// 获取子组件传递邮箱id列表
		resolveEmailIds(ids) {
			this.$refs['emailGlobal']?.closeDialog();
			this.emailList = ids;
		},
		// 关闭选中邮箱标签
		closeTag(id) {
			let index = this.emailList.findIndex((item) => {
				return item.email_id == id;
			});
			this.emailList.splice(index, 1);
		},
		// 文件上传结束
		uploadOver(response) {
			if (response.mtycode == 200) {
				setTimeout(() => {
					this.image = response.data;
				}, 1000);
			} else {
				this.$message.warn('图片上传失败,请重新上传');
			}
		},
		// 介绍图片上传结束
		uploadIntroduceOver(response) {
			if (response.mtycode == 200) {
				setTimeout(() => {
					this.imageIntroduce = response.data;
				}, 1000);
			} else {
				this.$message.warn('图片上传失败,请重新上传');
			}
		},
		// 打开保存模板弹窗
		saveTemplateDialog() {
			this.$refs['modelSaveDialog']?.getData({ name: this.name });
		},
		// 提交表单
		async submit(templateInfo) {
			// 模板名称
			let name = this.name;
			// 模板描述
			let description = this.des;
			// 基金/基金经理打印模板
			let subscriptionList = this.subscriptionList.map((obj) => {
				return {
					model_id: obj.model,
					type: obj.type,
					flag: obj.flag
				};
			});
			// 自定义logo
			let custom_logo = this.image;
			// 自定义声明
			let custom_description = this.customDescription;
			// 自定义报告介绍
			let custom_introduce = this.custom_introduce
				? {
						type: this.custom_introduce_type,
						content: this.custom_introduce_type == 'text' ? this.custom_introduce : this.imageIntroduce
				  }
				: {};
			let postdData = {
				template_name: name,
				template_description: description,
				fund_manager_model_list: subscriptionList,
				custom_logo,
				custom_description,
				custom_introduce,
				user_id: localStorage.getItem('id')
			};
			let template_id = await this.postReportTemplate(postdData);
			this.submitAddTemplate(templateInfo, {
				...postdData,
				template_id: template_id
			});
		},
		// 新增报告模板
		async postReportTemplate(postdData) {
			let data = await postReportTemplate(postdData);
			if (data?.mtycode == 200) {
				return data?.data?.template_id;
			}
		},
		// 保存模板
		async submitAddTemplate(templateForm, form) {
			let model_args = { ...form };
			await saveModel({
				ismanager: 'true',
				ispublic: templateForm.ispublic,
				model_args: model_args,
				model_description: '',
				model_name: templateForm.name,
				user_list: templateForm.user_list,
				type: 'equity',
				flag: 'print_report',
				title: '',
				source: 'print_report',
				user_permission: []
			});
			this.$message.success('保存模板成功');
			this.$router.push('/subscriptionCenter');
		},
		// 修改报告模板
		async putReportTemplate(postdData) {
			let data = await putReportTemplate(postdData);
			if (data?.mtycode == 200) {
				this.$message.success(data?.mtymessage || '修改成功');
				this.$router.push('/subscriptionCenter');
			} else {
				this.$message.warning(data?.mtymessage || '修改失败');
			}
		},
		// 监听订阅类型选择
		changeType(val) {
			let flag = this.options.find((item) => {
				return item.value == val;
			}).flag;
			let index = this.subscriptionList.findIndex((item) => {
				return item.type == val;
			});
			this.subscriptionList[index]['flag'] = flag;
			this.activeTypeList = this.subscriptionList.map((item) => {
				return item.type;
			});
			this.subscriptionList[index]['model'] = '';
			this.matchingModelList();
		},
		// 取消编辑
		goBack() {
			this.$router.push('/subscriptionCenter');
		}
	}
};
</script>

<style lang="scss" scoped>
.subscription_form {
	margin: 8px 24px 0 24px;
	font-family: 'PingFang';
	font-style: normal;
	font-weight: 400;
	font-size: 14px;
	line-height: 22px;
	color: rgba(0, 0, 0, 0.85);
	::v-deep.avatar-uploader .el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
		width: 100px;
		height: 100px;
	}
	::v-deep.content-uploader .el-upload {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
		width: 400px;
		height: 200px;
	}
	.avatar-uploader .el-upload:hover {
		border-color: #409eff;
	}
	.subscription_form_item {
		margin-bottom: 24px;
		.avatar-uploader-icon {
			font-size: 28px;
			color: #8c939d;
			width: 100px;
			height: 100px;
			line-height: 100px;
			text-align: center;
		}
		.avatar {
			width: 100px;
			height: 100px;
			display: block;
			object-fit: cover;
		}
		.subscription_title {
			margin-bottom: 8px;
		}
		.email_list_tags {
			width: 668px;
			border: 1px solid #d9d9d9;
			border-radius: 4px;
		}
		.subscription_content {
			display: flex;
			align-items: center;
			.delete_icon {
				margin-left: 16px;
				cursor: pointer;
			}
			.delete_icon:hover {
				svg {
					path {
						fill: #4096ff;
					}
				}
			}
		}
		.delete_icon {
			margin-top: 8px;
			margin-left: 16px;
			cursor: pointer;
		}
		.delete_icon:hover {
			svg {
				path {
					fill: #4096ff;
				}
			}
		}
		.add_subscription {
			display: flex;
			justify-content: center;
			align-items: center;
			cursor: pointer;
			width: 440px;
			height: 32px;
			background: #ffffff;
			border: 1px dashed #d9d9d9;
			box-shadow: 0px 2px 0px rgba(0, 0, 0, 0.016);
			border-radius: 4px;
		}
		.add_subscription:hover {
			border-color: #4096ff;
		}
	}
}
</style>
