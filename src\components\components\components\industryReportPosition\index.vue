<template>
	<div id="industryReportPosition">
		<analysis-card-title title="已披露行业配置" image_id="industryReportPosition">
			<el-select v-model="quarter" placeholder="" @change="changeQuarter">
				<el-option v-for="item in quarterList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
			</el-select>
		</analysis-card-title>
		<div v-loading="loading">
			<div style="position: relative">
				<div style="display: flex; width: 100%">
					<el-table
						style="flex: 1"
						:data="data"
						border
						stripe
						height="368px"
						class="industry_peizhi_h400 table"
						ref="multipleTable"
						:default-sort="{ prop: 'weight', order: 'descending' }"
						@sort-change="sortChange"
						header-cell-class-name="table-header"
					>
						<el-table-column
							v-for="item in industryColumn"
							:key="item.value"
							:align="item.align || 'gotoleft'"
							:prop="item.value"
							:label="item.label"
							:sortable="item.sortable ? item.sortable : false"
						>
							<template #header>
								<long-table-popover-chart
									v-if="item.popover"
									:data="formatIndustryTableData()"
									date_key="swname"
									:data_key="item.value"
									:show_name="item.label"
								>
									<span>{{ item.label }}</span>
								</long-table-popover-chart>
								<span v-else>{{ item.label }}</span>
							</template>
							<template slot-scope="{ row }">
								<span>{{ item.format ? item.format(row[item.value]) : row[item.value] }}</span>
							</template>
						</el-table-column>
						<template slot="empty">
							<el-empty image-size="160"></el-empty>
						</template>
					</el-table>
					<div class="charts_center_class" style="flex: 1" v-loading="loading">
						<div id="container" class="tree_temp px-20 py-20 ml-20"></div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { filter_json_to_excel } from '@/utils/exportExcel.js';
import { Treemap } from '@antv/g2plot';
// 最新报告持仓分析
import { getHoldStocks, getDateList } from '@/api/pages/Analysis.js';

export default {
	name: 'latestReportedPosition',
	data() {
		return {
			loading: true,
			newholding: [],
			newtime: '',
			name: '',
			show: true,
			quarterList: [],
			quarter: '',
			data: [],
			color: ['#4096ff', '#4096ff', '#7388A9', '#6F80DD', '#6C96F2', '#FD6865', '#83D6AE', '#88C9E9', '#ED589D', '#FA541C'],
			optionbaogao: {},
			industryColumn: [
				{
					label: '行业',
					value: 'swname',
					popover: false
				},
				{
					label: '占净值比',
					value: 'weight',
					format: this.fix3p,
					align: 'right',
					sortable: 'custom',
					popover: false
				},
				{
					label: '市值(万元)',
					value: 'value',
					format: this.fix3,
					align: 'right',
					sortable: 'custom',
					popover: false
				}
			],
			info: {},
			// 图dom实例
			treemapPlot: null
		};
	},
	methods: {
		async getData(info) {
			this.info = info;
			await this.getDateList();
		},
		// 获取报告持仓季度列表
		async getDateList() {
			let data = await getDateList({
				code: this.info.code,
				type: this.info.type,
				flag: this.info.flag,
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			if (data?.mtycode == 200) {
				this.quarterList = data?.data
					?.sort((a, b) => {
						return this.moment(this.moment(a, 'YYYY QQ').format()).isAfter(this.moment(b, 'YYYY QQ').format()) ? -1 : 1;
					})
					?.map((item) => {
						return { label: item, value: item };
					});
				this.quarter = this.quarterList?.[0]?.value;
				this.getHoldStocks();
			}
		},
		// 切换季度
		changeQuarter() {
			this.loading = true;
			this.getHoldStocks();
		},
		// 获取最新报告持仓数据
		async getHoldStocks() {
			let data = await getHoldStocks({
				flag: this.info.flag,
				code: this.info.code,
				type: this.info.type,
				yearqtr: this.quarter,
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			this.loading = false;
			if (data?.mtycode == 200) {
				let res = data?.data.map((item) => {
					return {
						...item,
						weight: item.weight * 1 && !isNaN(item.weight) ? item.weight * 1 : '--',
						swname: item.swlevel1 + '-' + item.swlevel2 + '-' + item.swlevel3
					};
				});
				this.data = this.formatTableData(res).sort((a, b) => {
					if (a?.['weight'] === '--' && b?.['weight'] !== '--') return 1;
					if (a?.['weight'] !== '--' && b?.['weight'] === '--') return -1;
					if (a?.['weight'] === '--' && b?.['weight'] === '--') return 0;
					return b?.['weight'] * 1 - a?.['weight'] * 1;
				});
				this.getChartData(data?.data);
			} else {
				this.data = [];
				this.getChartData([]);
			}
		},
		formatTableData(data) {
			let list = [];
			data.map((item) => {
				let index = list.findIndex((v) => v.swname == item.swname);
				if (index == -1) {
					list.push(item);
				} else {
					list[index].weight =
						(list[index].weight * 1 && !isNaN(list[index].weight) ? list[index].weight * 1 : 0) +
						(item.weight * 1 && !isNaN(item.weight) ? item.weight * 1 : 0);
				}
			});
			return list;
		},
		getChartData(data) {
			let option = {
				data: this.formatChartData(data),
				colorField: 'name',
				legend: {
					position: 'top-left'
				},
				drilldown: {
					breadCrumb: {
						rootText: '一级行业'
					}
				},
				tooltip: {
					formatter: (v) => {
						const root = v.path[v.path.length - 1];
						return {
							name: v.name,
							value: `${v.value}%`
						};
					}
				},
				interactions: [{ type: 'treemap-drill-down' }],
				// 开启动画
				animation: {}
			};
			if (this.treemapPlot) {
				this.treemapPlot.destroy();
			}
			this.treemapPlot = new Treemap('container', option);
			this.treemapPlot.render();
		},
		formatChartData(data) {
			let tree = [];
			data.map((item) => {
				let index1 = tree.findIndex((v) => v.name == item.swlevel1);
				// 一级行业不存在
				if (index1 == -1) {
					tree.push({
						name: item.swlevel1,
						children: [
							{
								name: item.swlevel2,
								children: [
									{
										name: item.swlevel3,
										value: item.weight * 1 && !isNaN(item.weight) ? item.weight : 0
									}
								]
							}
						]
					});
				} else {
					let index2 = tree[index1].children.findIndex((v) => v.name == item.swlevel2);
					// 二级行业不存在
					if (index2 == -1) {
						tree[index1].children.push({
							name: item.swlevel2,
							children: [
								{
									name: item.swlevel3,
									value: item.weight * 1 && !isNaN(item.weight) ? item.weight : 0
								}
							]
						});
					} else {
						let index3 = tree[index1].children[index2].children.findIndex((v) => v.name == item.swlevel3);
						if (index3 == -1) {
							tree[index1].children[index2].children.push({
								name: item.swlevel3,
								value: item.weight * 1 && !isNaN(item.weight) ? item.weight : 0
							});
						} else {
							tree[index1].children[index2].children[index3].value =
								tree[index1].children[index2].children[index3].value + (item.weight * 1 && !isNaN(item.weight) ? item.weight : 0);
						}
					}
				}
			});
			return {
				name: '行业',
				children: tree
			};
		},
		hideLoading() {
			this.show = false;
		},
		formatIndustryTableData() {
			let data = [];
			this.data.map((item) => {
				let obj = { ...item };
				for (const key in item) {
					let format = this.industryColumn.find((obj) => {
						return obj.value == key;
					})?.format;
					if (format) {
						let val = format(item[key]);
						obj[key] = typeof val == 'string' ? (val.includes('%') ? val?.split('%')?.[0] * 1 : !isNaN(val) ? val * 1 : val) : val;
					}
				}
				data.push(obj);
			});
			return data;
		},
		fix3(value) {
			return value * 1 && !isNaN(value) ? (value / 10 ** 4).toFixed(3) : '--';
		},
		fix3p(value) {
			return value * 1 && !isNaN(value) ? (value * 1).toFixed(3) + '%' : '--';
		},
		// 表格排序
		sortChange(val) {
			let data = this.data;
			// 降序
			if (val?.order == 'descending') {
				data = data?.sort((a, b) => {
					if (a?.[val?.prop] === '--' && b?.[val?.prop] !== '--') return 1;
					if (a?.[val?.prop] !== '--' && b?.[val?.prop] === '--') return -1;
					if (a?.[val?.prop] === '--' && b?.[val?.prop] === '--') return 0;
					return b?.[val?.prop] * 1 - a?.[val?.prop] * 1;
				});
			} else if (val?.order == 'ascending') {
				// 升序
				data = data?.sort((a, b) => {
					if (a?.[val?.prop] === '--' && b?.[val?.prop] !== '--') return 1;
					if (a?.[val?.prop] !== '--' && b?.[val?.prop] === '--') return -1;
					if (a?.[val?.prop] === '--' && b?.[val?.prop] === '--') return 0;
					return a?.[val?.prop] * 1 - b?.[val?.prop] * 1;
				});
			}
		},
		exportExcel() {
			let list = this.column.map((item) => {
				return { label: item.label, value: item.value };
			});
			filter_json_to_excel(
				list,
				this.data.sort((a, b) => {
					return b.weight - a.weight;
				}),
				'已披露行业配置'
			);
		},
		exportIndustryExcel() {
			let list = [
				{
					label: '行业',
					fill: 'header',
					value: 'swname'
				},
				{
					label: '占净值比',
					value: 'weight',
					format: 'fix2b'
				}
			];
			filter_json_to_excel(
				list,
				this.data.sort((a, b) => {
					return b.weight - a.weight;
				}),
				'已披露股票行业配置'
			);
		},
		async createPrintWord(info) {
			if (this.quarter) {
				this.info = info;
				await this.getHoldStocks();
			} else {
				await this.getData(info);
			}
			return await new Promise((resolve, reject) => {
				this.$nextTick(async () => {
					let list = this.industryColumn.map((item) => {
						return { label: item.label, value: item.value };
					});
					let data = this.data.map((item) => {
						let obj = {};
						for (const key in item) {
							let index = this.industryColumn.findIndex((v) => v.value == key);
							if (this.industryColumn[index]?.format) {
								obj[key] = this.industryColumn[index]?.format(item[key]);
							} else {
								obj[key] = item[key];
							}
						}
						return obj;
					});
					let key = 'container';
					let height = document.getElementById(key).clientHeight;
					let width = document.getElementById(key).clientWidth;
					let canvas = await this.html2canvas(document.getElementById(key), {
						scale: 3
					});
					if (data.length) {
						resolve([
							...this.$exportWord.exportTitle('已披露行业配置'),
							...this.$exportWord.exportDescripe('数据报告期为：' + this.quarter),
							...this.$exportWord.exportTable(list, data, '', true),
							...this.$exportWord.exportChart(canvas.toDataURL('image/jpg'), {
								width,
								height
							})
						]);
					} else {
						resolve([]);
					}
				});
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.tree_temp {
	width: 100%;
	height: 368px;
	border-radius: 4px;
	border: 1px solid #d9d9d9;
}
</style>
