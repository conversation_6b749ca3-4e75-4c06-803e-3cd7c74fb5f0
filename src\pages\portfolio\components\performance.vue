<template>
	<div>
		<div class="flex_card">
			<div v-for="item in templateList" :key="item.value" v-show="item.isshow" :class="item.type">
				<component :is="item.is" :ref="item.value" :indexInfo="indexInfo" @resolveFather="item.methods" v-loading="loading"></component>
			</div>
		</div>
	</div>
</template>

<script>
// 收益率分布直方图
import distributionReturn from '@/components/components/distributionReturn/index.vue';
// 风险收益指标
import riskReturnIndex from '@/components/fundComponents/riskReturnIndex/index.vue';
// 风险收益指标&&同类排名比较
import comparisonSimilarRankingAndRiskReturnIndex from '@/components/components/comparisonSimilarRankingAndRiskReturnIndex/index.vue';
// 风险收益关系
import riskReturnRelationship from '@/components/components/riskReturnRelationship/index.vue';
// 分时段业绩表现
import timePhasedPerformance from '@/components/components/timePhasedPerformance/index.vue';
// 滚动胜率
import holdingPressure from '@/components/fundComponents/holdingPressure/index.vue';
// 动态4因子分析
import dynamicFourFactorAnalysis from '@/components/components/dynamicFourFactorAnalysis/index.vue';
// 动态4因子统计
import dynamicFourFactorStatistics from '@/components/fundComponents/dynamicFourFactorStatistics/index.vue';
// TM模型分析
import TMModelAnalysis from '@/components/fundComponents/TMModelAnalysis/index.vue';
// 同类排名比较
import comparisonSimilarRanking from '@/components/fundComponents/comparisonSimilarRanking/index.vue';

import {
	getFundReturnSection,
	getBestBenchmarks,
	getRiskFeatureYearly,
	getMarketWindowReturn,
	getFundPeriod,
	getHoldPressureInfo,
	getAnalysisIndex,
	getDynamicStatistics,
	getTMStatistics,
	getFofMeasureSinceRank
} from '@/api/pages/SystemAlpha.js';

export default {
	components: {
		distributionReturn,
		riskReturnIndex,
		riskReturnRelationship,
		timePhasedPerformance,
		holdingPressure,
		dynamicFourFactorAnalysis,
		dynamicFourFactorStatistics,
		TMModelAnalysis,
		comparisonSimilarRanking,
		comparisonSimilarRankingAndRiskReturnIndex
	},
	data() {
		return {
			info: {},
			distributionPostData: {},
			riskReturnIndexData: {},
			riskReturnRelationshipData: {},
			timePhasedData: {},
			holdingPressureData: {},
			templateList: [],
			requestOver: 0,
			requestAll: 0,
			loading: true,
			riskFeatureYearlyData: {}
		};
	},
	props: {
		indexInfo: {
			type: Object,
			default: {}
		},
		active: {
			type: String,
			default: ''
		}
	},
	watch: {
		indexInfo() {
			if (this.active == 'performance') {
				this.getDistributionReturn();
				this.getRiskFeatureRecent();
			}
		}
	},
	methods: {
		// 接收/返回组件列表
		getTemplateList(list) {
			if (list) {
				this.templateList = [...list];
			} else {
				return this.templateList;
			}
		},
		// 获取父组件数据
		getData(data) {
			this.info = data;
			this.loading = true;
			this.requestOver = 0;
			this.watch();
			this.initPostData();
			this.formatTemplatList();
		},
		// 添加watch函数式监听(因为watch侦听器在页面切换时失效)
		watch() {
			let unwatch = this.$watch('requestOver', (val) => {
				if (val == this.requestAll) {
					this.templateList.map((item) => {
						if (item.getData && item.getData !== 'None') {
							this?.[item.getData]();
						}
					});
					this.loading = false;
					this.$emit('overRequest', 'performance');
					unwatch();
				}
			});
		},
		// 获取打印数据
		createPrintWord() {
			let printData = [];
			this.templateList.map((item) => {
				if (item.isshow) {
					if (this.$refs[item.value]?.[0].createPrintWord) {
						printData.push(...this.$refs[item.value]?.[0].createPrintWord());
					}
				}
			});
			return printData;
		},
		// 格式化模板列表
		formatTemplatList() {
			let requestList = [];
			this.requestAll = 0;
			this.templateList.map((item) => {
				if (item.methods && typeof item.methods == 'string') {
					item.methods = this?.[item.methods];
				}
				if (item.typelist.indexOf(this.info.type) !== -1 || item.typelist.indexOf('*') !== -1) {
					if (requestList.indexOf(item.getRequestData) == -1) {
						if (item.getRequestData && item.getRequestData !== 'None') {
							this?.[item.getRequestData]();
							requestList.push(item.getRequestData);
							this.requestAll = this.requestAll + 1;
						}
					}
				}
			});
		},

		// 初始化请求数据
		initPostData() {
			// 收益率分布直方图
			this.distributionPostData = {
				code: this.info.code,
				flag: this.info.flag,
				type: this.info.type,
				index_code: this.indexInfo.id,
				index_flag: this.indexInfo.flag,
				date_flag: 'weekly'
			};
			// 风险收益指标
			this.riskReturnIndexData = {
				code: this.info.code,
				fund_name: this.info.name,
				fund_type: this.info.type
			};
			// 风险收益关系
			this.riskReturnRelationshipData = {
				code: this.info.code,
				type: this.info.type,
				flag: this.info.flag,
				start_date: '',
				end_date: '',
				index_code: this.indexInfo.id
			};
			// 分时段业绩表现
			this.timePhasedData = {
				flag: this.info.flag,
				code: this.info.code,
				type: this.info.type,
				start_date: '',
				end_date: '',
				periodname: ''
			};
			// 滚动胜率
			this.holdingPressureData = {
				flag: this.info.flag,
				code: this.info.code,
				type: this.info.type,
				start_date: '',
				end_date: '',
				benchmark: ''
			};
			this.riskFeatureYearlyData = {
				code: this.info.code,
				type: this.info.type,
				flag: this.info.flag,
				tag: 'fof'
			};
		},
		// 获取收益率分布直方图
		async getDistributionReturn() {
			let data = await getFundReturnSection(this.distributionPostData);
			this.fundReturnSection = data?.data;
			this.$refs['distributionReturn']?.[0].getData(this.fundReturnSection, this.info);
			this.requestOver = this.requestOver + 1;
		},
		// 收益率分布直方图
		getDistributionReturnData() {},
		// 获取收益率分布直方图传递参数
		getDistributionData(data) {
			this.distributionPostData = { ...this.distributionPostData, ...data };
			this.getDistributionReturn();
		},
		// 获取风险收益指标
		async getRiskFeatureYearly() {
			if (this.getCacheData('riskFeatureYearly')) {
				this.riskFeatureYearly = this.getCacheData('riskFeatureYearly');
			} else {
				let data = await getRiskFeatureYearly(riskFeatureYearlyData);
				this.riskFeatureYearly = data?.data;
				this.setCacheData('riskFeatureYearly', data?.data);
			}
			this.requestOver = this.requestOver + 1;
		},
		// 获取风险收益指标
		getRiskReturnIndexData() {
			let data = this.riskFeatureYearly;
			if (data?.mtycode == 200) {
				this.$refs['riskReturnIndex']?.[0].getData(data?.data, this.info);
			} else {
				this.$refs['riskReturnIndex']?.[0].hideLoading();
				this.$message.warning('风险收益指标' + (data?.mtymessage || '暂无数据'));
			}
		},
		// 获取风险收益关系基准列表
		async getBestBenchmarks() {
			if (this.getCacheData('bestBenchmarks')) {
				this.bestBenchmarks = this.getCacheData('bestBenchmarks');
			} else {
				let data = await getBestBenchmarks({ fund_code: this.info.code });
				this.bestBenchmarks = data?.data;
				this.setCacheData('bestBenchmarks', data?.data);
			}
			this.requestOver = this.requestOver + 1;
		},
		// 风险收益关系
		getBestBenchmarksData() {
			let data = this.bestBenchmarks;
			if (data?.mtycode == 200) {
				this.getBenchmarkList(data?.data);
			} else {
				this.$message.warning('风险收益关系基准列表' + (data?.mtymessage || '暂无数据'));
			}
		},
		// 风险收益指标&&同类排名
		async getComparisonSimilarRankingAndRiskReturnIndex() {
			let data = await getRiskFeatureYearly(this.riskFeatureYearlyData);
			if (data?.mtycode == 200) {
				this.$refs['comparisonSimilarRankingAndRiskReturnIndex']?.[0].getData(
					data?.data?.data,
					this.filterComparisonSimilarRankingData(data?.data?.data),
					this.info
				);
			}
			this.requestOver = this.requestOver + 1;
		},
		// 风险收益指标&&同类排名
		getComparisonSimilarRankingAndRiskReturnIndexData() {},
		// 风险收益指标&&同类排名
		getComparisonSimilarRankingAndRiskReturnIndexTag(tag) {
			this.riskFeatureYearlyData.tag = tag;
			this.getComparisonSimilarRankingAndRiskReturnIndex();
		},
		// 获取基准列表
		getBenchmarkList(data) {
			let optionsbase = [];
			if (data?.length) {
				for (let i = 0; i < data?.length; i++) {
					optionsbase.push({
						label: data?.[i]?.benchmark_name,
						value: data?.[i]?.benchmark_code
					});
				}
			}
			this.$refs['riskReturnRelationship']?.[0]?.getBenchmarkList(optionsbase, this.info);
		},
		// 获取风险收益关系
		getRiskFeatureRecent() {
			this.$refs['riskReturnRelationship']?.[0].getData(this.info);
			this.requestOver = this.requestOver + 1;
		},
		// 获取基准概念列表
		async getFundPeriod() {
			if (this.getCacheData('fundPeriod')) {
				this.fundPeriod = this.getCacheData('fundPeriod');
			} else {
				let postData = { fund_code: this.info.code, fund_type: this.info.type };
				let data = await getFundPeriod(postData);
				this.fundPeriod = data?.data;
				this.setCacheData('fundPeriod', data?.data);
			}
			this.requestOver = this.requestOver + 1;
		},
		// 分时段业绩表现
		getTimePhasedPerformanceData() {
			this.$refs['timePhasedPerformance']?.[0].getBenchmarkList(this.fundPeriod);
		},
		// 获取分时段业绩表现
		async getMarketWindowReturn() {
			let data = await getMarketWindowReturn(this.timePhasedData);
			if (data?.mtycode == 200) {
				this.$refs['timePhasedPerformance']?.[0].getData(data?.data?.data, this.info);
			} else {
				this.$message.warning('分时段业绩表现' + (data?.mtymessage || '暂无数据'));
			}
		},
		// 获取滚动胜率基准列表
		async getAnalysisIndex() {
			if (this.getCacheData('analysisIndex')) {
				this.analysisIndex = this.getCacheData('analysisIndex');
			} else {
				let data = await getAnalysisIndex({ code: this.info.code });
				this.analysisIndex = data?.data;
				this.setCacheData('analysisIndex', data?.data);
			}
			this.requestOver = this.requestOver + 1;
		},
		// 滚动胜率
		getHoldingPressureData() {
			this.$refs['holdingPressure']?.[0].getBenchmarkList(this.analysisIndex?.benchmark_all);
		},
		// 获取滚动胜率
		async getHoldPressureInfo() {
			let data = await getHoldPressureInfo(this.holdingPressureData);
			if (data?.mtycode == 200) {
				this.$refs['holdingPressure']?.[0].getData(data?.data?.data);
			} else {
			}
		},
		// 获取动态4因子分析数据
		async getDynamicAnalysis() {
			if (this.getCacheData('dynamicAnalysis')) {
				this.dynamicAnalysis = this.getCacheData('dynamicAnalysis');
			} else {
				let data = await getDynamicStatistics({
					flag: 1,
					type: this.info.type,
					code: this.info.code,
					start_date: '',
					end_date: '',
					status: 'details'
				});
				this.dynamicAnalysis = data?.data;
				this.setCacheData('dynamicAnalysis', data?.data);
			}
			this.requestOver = this.requestOver + 1;
		},
		// 动态四因子分析
		getDynamicFourFactorAnalysisData() {
			let data = this.dynamicAnalysis;
			if (data?.mtycode == 200) {
				this.$refs['dynamicFourFactorAnalysis']?.[0].getData(data?.data, this.info);
			} else {
				this.$message.warning('动态4因子分析' + (data?.mtymessage || '暂无数据'));
				this.$refs['dynamicFourFactorAnalysis']?.[0].hideLoading();
			}
		},
		// 获取动态4因子统计数据
		async getDynamicStatistics() {
			if (this.getCacheData('dynamicStatistics')) {
				this.dynamicStatistics = this.getCacheData('dynamicStatistics');
			} else {
				let data = await getDynamicStatistics({
					flag: 1,
					type: this.info.type,
					code: this.info.code,
					start_date: '',
					end_date: '',
					status: 'summary'
				});
				this.dynamicStatistics = data?.data;
				this.setCacheData('dynamicStatistics', data?.data);
			}
			this.requestOver = this.requestOver + 1;
		},
		// 动态四因子统计
		getDynamicFourFactorStatisticsData() {
			let data = this.dynamicStatistics;
			if (data?.mtycode == 200) {
				this.$refs['dynamicFourFactorStatistics']?.[0].getData(data?.data);
			} else {
				this.$message.warning('动态4因子统计' + (data?.mtymessage || '暂无数据'));
				this.$refs['dynamicFourFactorStatistics']?.[0].hideLoading();
			}
		},
		// 获取TM模型分析数据
		async getTMStatistics() {
			if (this.getCacheData('tmStatistics')) {
				this.tmStatistics = this.getCacheData('tmStatistics');
			} else {
				let data = await getTMStatistics({
					code: this.info.code
				});
				this.tmStatistics = data?.data;
				this.setCacheData('tmStatistics', data?.data);
			}
			this.requestOver = this.requestOver + 1;
		},
		// TM模型分析
		getTMModelAnalysisData() {
			this.$refs['TMModelAnalysis']?.[0].getData(this.tmStatistics, this.info);
		},
		// 获取同类排名比较
		async getFofMeasureSinceRank() {
			if (this.getCacheData('fofMeasureSinceRank')) {
				this.fofMeasureSinceRank = this.getCacheData('fofMeasureSinceRank');
			} else {
				let data = await getFofMeasureSinceRank({ fund_code: this.info.code });
				this.fofMeasureSinceRank = data?.data;
				this.setCacheData('fofMeasureSinceRank', data?.data);
			}
			this.requestOver = this.requestOver + 1;
		},
		// 同类排名比较
		getComparisonSimilarRankingData() {
			let data = this.riskFeatureYearly;
			if (data?.mtycode == 200) {
				this.$refs['comparisonSimilarRanking']?.[0].getData(this.filterComparisonSimilarRankingData(data?.data));
			} else {
				this.$message.warning('同类排名比较' + (data?.mtymessage || '暂无数据'));
				this.$refs['comparisonSimilarRanking']?.[0].hideLoading();
			}
		},
		// 格式化同类排名比较数据
		filterComparisonSimilarRankingData(data) {
			let arr = [];
			data?.map((item) => {
				let index = arr.findIndex((obj) => {
					return obj.year == item.year;
				});
				if (index == -1) {
					let obj = {};
					obj[item.measure] = item.description;
					arr.push({ year: item.year, ...obj });
				} else {
					let obj = {};
					obj[item.measure] = item.description;
					arr[index] = { ...arr[index], ...obj };
				}
			});
			return arr;
		},
		// 获取风险收益关系传递数据
		getRiskRecentData(data) {
			this.riskReturnRelationshipData = {
				...this.riskReturnRelationshipData,
				index_code: data?.index
			};
			this.getRiskFeatureRecent();
		},
		// 获取分时段业绩表现传递数据
		getTimePhasedData(data) {
			this.timePhasedData = { ...this.timePhasedData, ...data };
			this.getMarketWindowReturn();
		},
		// 获取滚动胜率传递数据
		getHoldPressureData(data) {
			this.holdingPressureData = { ...this.holdingPressureData, ...data };
			this.getHoldPressureInfo();
		}
	}
};
</script>

<style></style>
