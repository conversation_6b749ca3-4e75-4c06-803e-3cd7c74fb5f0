<!-- 组合收益贡献度走势 -->
<template>
	<div class="chart_one">
		<div v-loading="loadyejis">
			<div class="card_header">
				<div class="title">组合收益贡献度走势</div>
			</div>
			<div class="charts_fill_class">
				<v-chart
					element-loading-text="暂无数据"
					element-loading-spinner="el-icon-document-delete"
					element-loading-background="rgba(239, 239, 239, 0.5)"
					class="charts_one_class"
					ref="contributionTrend"
					style="height: 442px"
					autoresize
					:options="option"
				/>
			</div>
		</div>
	</div>
</template>

<script>
// @click.native="watchMouseDown"
// @zr:click="watchMouseUp"
// @highlight="watchCilck"
import VChart from 'vue-echarts';
import { barChartOption } from '@/utils/chartStyle';
export default {
	name: 'contributionTrend',
	components: { VChart },
	data() {
		return {
			options: [
				{
					label: '总回报',
					value: 'all'
				}
			],
			model: 'all',
			option: {},
			data: [],
			currentXAxis: ''
		};
	},
	watch: {
		currentXAxis() {}
	},
	methods: {
		getData(data) {
			let dateList = [];
			data?.map((item) => {
				dateList.push(...item.date);
			});
			let xAxis = Array.from(new Set(dateList)).sort();
			let legend = data?.map((item) => {
				return item.name;
			});
			let series = data?.map((item) => {
				return {
					name: item.name,
					type: 'bar',
					barWidth: '100%',
					data: item.date.sort().map((obj, index) => {
						return [obj, (item.cum_return[index] * 100).toFixed(6)];
					}),
					symbol: 'none',
					areaStyle: {},
					stack: 'Total',
					stackStrategy: 'all'
				};
			});
			let that = this;
			this.option = barChartOption({
				toolbox: false,
				legend,
				dataZoom: true,
				xAxis: [{ data: xAxis }],
				tooltip: {
					formatter: function (obj) {
						that.currentXAxis = obj[0].axisValue;
						var value = obj[0].axisValue + `<br />`;
						for (let i = 0; i < obj.length; i++) {
							value +=
								`<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:` +
								obj[i].color +
								`;"></span>` +
								obj[i].seriesName +
								':' +
								Number(obj[i].data[1]).toFixed(6) +
								'%' +
								`<br />`;
						}
						return value;
					}
				},
				yAxis: [
					{
						type: 'value',
						name: '收益率',
						scale: true,
						formatter: function (val) {
							return val + '%';
						}
					}
				],
				series
			});
		},
		watchMouseDown(event, el) {
			console.log(event, el);
		},
		watchMouseUp(event, el) {
			console.log(event, el);
		},
		watchCilck(event, el) {
			console.log(event, el);
		},
		createPrintWord() {
			this.$refs['contributionTrend'].mergeOptions({ toolbox: { show: false } });
			let height = this.$refs['contributionTrend']?.$el.clientHeight;
			let width = this.$refs['contributionTrend']?.$el.clientWidth;
			let chart = this.$refs['contributionTrend'].getDataURL({
				type: 'png',
				pixelRatio: 3,
				backgroundColor: '#fff'
			});
			this.$refs['contributionTrend'].mergeOptions({ toolbox: { show: true } });
			return [...this.$exportWord.exportTitle('组合收益贡献度走势'), ...this.$exportWord.exportChart(chart, { width, height })];
		}
	}
};
</script>

<style></style>
