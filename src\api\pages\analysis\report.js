import request from '@/utils/request';

/**
 * 产品业绩表现图
 * @param {reportID; startFrom; endTo; industryStandard; selectedCuts}
 */
export function getObjectPerformance(params) {
    return request({
        url: '/api/taikang/getObjectPerformance',
        method: 'get',
        params
    });
}

/**
 * 产品业绩表现表
 * @param {reportID; startFrom; endTo; industryStandard; selectedCuts}
 */
export function getObjectPerformanceStat(params) {
    return request({
        url: '/api/taikang/getObjectPerformanceStat',
        method: 'get',
        params
    });
}

/**
 * 大类资产业绩汇总
 * @param {reportID; startFrom; endTo; industryStandard; selectedCuts}
 */
export function getObjectAssetAllocation(params) {
    return request({
        url: '/api/taikang/getObjectAssetAllocation',
        method: 'get',
        params
    });
}

/**
 * 基金持仓分析表
 * @param {reportID; startFrom; endTo; industryStandard; selectedCuts}
 */
export function getObjectFundStat(params) {
    return request({
        url: '/api/taikang/getObjectFundStat',
        method: 'get',
        params
    });
}
/**
 * 基金持仓分析表
 * @param {reportID; startFrom; endTo; industryStandard; selectedCuts}
 */
export function getObjectFundStat2(params) {
  return request({
      url: 'api/taikang/getObjectInsuranceStat',
      method: 'get',
      params
  });
}

/**
 * 前十大集中度
 * @param {reportID; startFrom; endTo; industryStandard; selectedCuts}
 */
export function getObjectConcertion(params) {
    return request({
        url: '/api/taikang/getObjectConcertion',
        method: 'get',
        params
    });
}

/**
 * 换手率
 * @param {reportID; startFrom; endTo; industryStandard; selectedCuts}
 */
export function getObjectTurnover(params) {
    return request({
        url: '/api/taikang/getObjectTurnover',
        method: 'get',
        params
    });
}

/**
 * 基金颗粒度特技贡献比较
 * @param {reportID; startFrom; endTo; industryStandard; selectedCuts}
 */
export function getObjectCutPerformance(params) {
    return request({
        url: '/api/taikang/getObjectCutPerformance',
        method: 'get',
        params
    });
}

/**
 * 基本颗粒度比较
 * @param {reportID; startFrom; endTo; industryStandard; selectedCuts}
 */
export function getObjectCutAllocation(params) {
    return request({
        url: '/api/taikang/getObjectCutAllocation',
        method: 'get',
        params
    });
}

/**
 * FOF行业操作复盘
 */
export function getObjectIndustryOperation(params) {
    return request({
        url: '/api/taikang/getObjectIndustryOperation',
        method: 'get',
        params
    });
}

/**
 * 行业配置表现 和 行业持仓分析
 */
export function getObjectIndustry(params) {
    return request({
        url: '/api/taikang/getObjectIndustry',
        method: 'get',
        params
    });
}

/**
 * 行业持仓分析FOF
 */
export function getObjectFOFIndustryOperation(params) {
    return request({
        url: '/api/taikang/getObjectFOFIndustryOperation',
        method: 'get',
        params
    });
}

/**
 * 长期持股行业分布
 */
export function getObjectIndustryStat(params) {
    return request({
        url: '/api/taikang/getObjectIndustryStat',
        method: 'get',
        params
    });
}

/**
 * 当前权益持仓风格getObjectStockFinance
 */
export function getObjectStockStyle(params) {
    return request({
        url: '/api/taikang/getObjectStockStyle',
        method: 'get',
        params
    });
}

/**
 * 组合个股分析
 */
export function getObjectStockFactors(data) {
    return request({
        url: '/api/taikang/getObjectStockFactors',
        method: 'post',
        data
    });
}

/**
 * 整体财务指标
 */
export function getObjectStockFinance(data) {
    return request({
        url: '/api/taikang/getObjectStockFinance',
        method: 'post',
        data
    });
}

/**
 * 持仓风格
 */
export function getObjectStyle(data) {
    return request({
        url: '/api/taikang/getObjectStyle',
        method: 'get',
        params:data,
    });
}

/**
 * 风险因子分析
 */
export function getObjectBarraAnalysis(data) {
    return request({
        url: '/api/taikang/getObjectBarraAnalysis',
        method: 'post',
        data
    });
}


export function getObjectIndustryBrison(params) {
    return request({
        url: '/api/taikang/getObjectIndustryBrison',
        method: 'get',
        params
    });
}

/**
 * 组合各个股分析弹窗
 * @param params
 * @returns {*}
 */
export function getObjectStockInfo(data) {
    return request({
        url: '/api/taikang/getObjectStockInfo',
        method: 'post',
        data
    });
}

/**
 * 行业持仓风险分析
 */
export function getObjectAllIndustry (params) {
    return request({
        url: '/api/taikang/getObjectAllIndustry',
        method: 'get',
        params
    });
}
/**
 * 行业三角方块图
 */
export function getObjectAllIndustryOperation (params) {
  return request({
      url: '/api/taikang/getObjectIndustryDetails',
      method: 'get',
      params
  });
}
