import { poolRequest as request } from '@/api/request';


/**
 *
 * @param {微服务基金池} params
 * @returns
 */
/**

/**

 * @param {获取基金池list} data
 * @returns
 */
export function getPoolList(params) {
  // console.log(poolRequest);
	return request({
		url: '/PoolList/',
		method: 'get',
		params
	});
}
/**
 * @param {池子新增基金} data
 * id   池子ID
 * code 基金code
 * flag
 * @returns
 */
export function postFunds(data) {
	return request({
		url:  '/DetailOperation/',
		method: 'post',
		data
	});
}

/**
 * @param {删除池子内基金} params
 * @returns
 */
export function deletePoolFund(params) {
	return request({
		url:  '/DetailOperation/',
		method: 'delete',
		params
	});
}
/**
 * @param {获取池子基础信息} data
 * @returns
 */
export function getPoolInfo(data) {
	return request({
		url:  '/BasicInfo/',
		method: 'post',
		data
	});
}
/**
 * @param {生成子池} params
 * @returns
 */
export function createdChildren(data) {
	return request({
		url:  '/ChildPool/',
		method: 'post',
		data
	});
}

/**
 * @param {获取池子更新时间} params
 * @returns
 */
export function getPoolDateList(params) {
	return request({
		url:  '/Combination/DateList/',
		method: 'get',
		params
	});
}
/* @param {自定义指标计算} data
* ids  Array
* insert_time  String
* yearqtr  Array
* start_date  String 开始时间
* end_date   String 结束时间
* cut_flag String 时间切分
* measure  Array 计算指标
* flag 5:基金池
* @returns
*/
export function getCustomMeasure(data) {
 return request({
   url:  '/Measure/',
   method: 'post',
   data
 });
}
/**
 * @param {获取池子中基金分年度波动收益} params
 * @returns
 */
export function getCombinationRisk(data) {
	return request({
		url: '/AvereturnVolatility/',
		method: 'post',
		data
	});
}
/**
 * @param {获取池子中alphabeta} params
 * @returns
 */
export function getCombinationAB(data) {
	return request({
		url: '/AlphaBeta/',
		method: 'post',
		data
	});
}
/**
 * @param {大类资产配置} params
 * @returns
 */
export function getAllocate(data) {
	return request({
		url: '/AllocationDetails/',
		method: 'post',
		data
	});
}
/**
 * @param {获取基金池行业信息} data
 * ids  Array
 * insert_time  String
 * yearqtr  Array
 * industry_standard   申万(2021) 申万二级
 * industry_code
 * method   等权
 * flag 5:基金池
 * @returns
 */
export function postIndustryInfo(data) {
	return request({
		url:  '/IndustryDetails/',
		method: 'post',
		data
	});
}
/**
 * @param {获取基金池持仓信息} data
 * ids  Array
 * insert_time  String
 * yearqtr  Array
 * isbond   0:股票  1:债券
 * isnew    0:全部  1:最新
 * flag 5:基金池
 * @returns
 */
export function getCombinationHoldStock(data) {
	return request({
		url: '/HoldDetails/',
		method: 'post',
		data
	});
}
/**
 * @param {获取基金池基金行业能力} data
 * @returns
 */
export function getIndustryRankInfo(data) {
	return request({
		url:   '/IndustryRank/',
		method: 'post',
		data
	});
}
/**
 * @param {获取基金池基金行业能力} data
 * @returns
 */
export function getMarketRankInfo(data) {
	return request({
		url:   '/MarketRankInfo/',
		method: 'post',
		data
	});
}
/**
 * @param {获取基金池表现风格} data
 * ids  Array
 * insert_time  String
 * yearqtr  Array
 * flag 5:基金池
 * @returns
 */
export function getStyleInfo(data) {
	return request({
		url:  '/StyleInfo/',
		method: 'post',
		data
	});
}
/**
 * @param {获取转债股性债性} data
 * ids  Array
 * insert_time  String
 * yearqtr  Array
 * method   等权
 * flag 5:基金池
 * @returns
 */
export function getCbondConvert(data) {
	return request({
		url:   '/CbondConvert/',
		method: 'post',
		data
	});
}
/**
 * @param {久期} params
 * @returns
 */
export function getDurationInfo(data) {
	return request({
		url:   '/DurationInfo/',
		method: 'post',
		data
	});
}
/**
 * @param {获取基金池信用下沉} data
 * ids  Array
 * insert_time  String
 * yearqtr  Array
 * method   等权
 * flag 5:基金池
 * @returns
 */
export function getCreditDownInfo(data) {
	return request({
		url:   '/CreditDownInfo/',
		method: 'post',
		data
	});
}
/**
 * @param {获取相似系数} params
 * @returns
 */
export function getCombinationSimilar(data) {
	return request({
		url:   '/SimilarInfo/',
		method: 'post',
		data
	});
}
/**
 * @param {获取相似矩阵} params
 * @returns
 */
export function getFofReturnRelevat(data) {
	return request({
		url:   '/Correlation/',
		method: 'post',
		data
	});
}
/**
 * @param {获取基金池基金行业能力} data
 * @returns
 */
export function getMarketRankInfo2(data) {
	return request({
		url: '/MarketInfo/',
		method: 'post',
		data
	});
}
/**
 * @param {获取子分类统计数据} params
 * @returns
 */
export function getChildrenCategory(data) {
	return request({
		url:  '/ChildPoolInfo/',
		method: 'post',
		data
	});
}
/**
 * @param {更改池子内基金状态} data
 * id   池子ID
 * code 基金code
 * flag
 * @returns
 */
export function updateStatus(data) {
	return request({
		url:   '/DetailOperation/',
		method: 'put',
		data
	});
}
/**
 * @param {删除基金池信息} params
 * @returns
 */
export function deletePool(params) {
	return request({
		url:  '/PoolOperation/',
		method: 'delete',
		params
	});
}
/**
 * @param {新增基金池信息} data
 * @returns
 */
export function postPoolInfo(data) {
	return request({
		url:   '/PoolOperation/',
		method: 'post',
		data
	});
}

/**
 * @param {编辑基金池信息} data
 * @returns
 */
export function putPoolInfo(data) {
	return request({
		url:   '/PoolOperation/',
		method: 'put',
		data
	});
}

/**
 * @param {合并基金池} data
 * @returns
 */
export function mergeFundPool(data) {
	return request({
		url:   '/PoolCombine/',
		method: 'post',
		data
	});
}
// -----------/------------------------------------
export function getDateList(params) {
	return request({
		url:  '/DateList/',
		method: 'get',
		params
	});
}

// 获取风险特征、风险收益特征可选时间列表
export function filter_risk_future(params) {
	return request({
		url:  '/filter_risk_future/',
		method: 'get',
		params
	});
}
// 获取对比类型
export function TypeMsg(params) {
	return request({
		url:  '/compare/TypeMsg/',
		method: 'get',
		params
	});
}
// beta筛选器
export function AlphaFilterV2(data) {
	return request({
		url:  '/AlphaFilterV2/',
		method: 'post',
		data
	});
} // 获取主题&行业列表
export function alphamsg() {
	return request({
		url:  '/alphamsg/',
		method: 'get'
	});
}
export function getStyleList(params) {
	return requestSystem({
		url:  '/filter/styleList/',
		method: 'get',
		params
	});
}
export function getTKandJYcategory(params) {
	return requestSystem({
		url:  '/filter/TKandJYcategory/',
		method: 'get',
		params
	});
}
// 获得筛选结果
export function filterResult(data) {
	return request({
		url:  '/ScoreCard/ScoreSelectFund/',
		method: 'post',
		data
	});
}
// 获得打分结果
export function SelectedScore(data) {
	return request({
		url:  '/ScoreCard/SelectedScore/',
		method: 'post',
		data
	});
}
// 获得打分结果v2
export function SelectedScoreV2(data) {
	return request({
		url:  '/ScoreCard/ScoreCardV2/',
		method: 'post',
		data
	});
}
// 获取动态回撤数据
export function getFofDrawdown(params) {
	return request({
		url:  '/FofDrawdown/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {转债调性}} params
 * /TimmingStyle/?flag=1&type=bond&code=000045
 * @returns
 */
export function cbondstylebenchmark(params) {
	return request({
		url:  '/cbondstylebenchmark/',
		method: 'get',
		params
	});
}
// 获取基金/基金经理/基金公司收益曲线
export function getFundOrManagerReturn(data) {
	return request({
		url:  '/FundOrManagerReturn/',
		method: 'post',
		data
	});
}
// 获取基准收益曲线
export function getIndexReturnInfo(data) {
	return request({
		url:  '/IndexReturnInfo/',
		method: 'post',
		data
	});
}
// 获取基金/基金经理基础信息
export function getBasicInfo(params) {
	return request({
		url:  '/ReturnInfo/',
		method: 'get',
		params
	});
}
// 收益率分布直方图
export function getFundReturnSection(data) {
	return request({
		url:  '/ReturnSummary/',
		method: 'post',
		data
	});
}
/**
 *
 * @param {风险收益指标基准列表} params
 *  fund_code: str(基金代码)
 *  type: str(基金类型)
 * @returns
 */

export function getBestBenchmarks(params) {
	return request({
		url:  '/BestBenchmarks/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {风险收益指标} params
 *  code: str(基金代码)
 *  type: str(基金类型)
 *  flag: str(基金/基金经理)
 * @returns
 */
export function getRiskFeatureYearly(params) {
	return request({
		url:  '/RiskFeatureYearly/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {风险收益关系} params
 * @returns
 */
export function getRiskFeatureRecent(params) {
	return request({
		url:  '/RiskFeatureRecent/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {分时段业绩表现} params
 * lag=1&code=110022&type=equity&start_date&end_date&periodname=股票牛熊市场
 * @returns
 */
export function getMarketWindowReturn(params) {
	return request({
		url:  '/MarketWindowReturn/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {基准概念列表} params
 * @returns
 */
export function getFundPeriod(params) {
	return request({
		url:  '/MacroPeriod/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {持有压力} params
 *  flag=1&code=110022&type=equity&start_date&end_date&benchmark=0
 * @returns
 */

export function getHoldPressureInfo(params) {
	return request({
		url:  '/HoldPressureInfo/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {持有压力基准列表} params
 *  code
 * @returns
 */
export function getAnalysisIndex(params) {
	return request({
		url:  '/analysisindex/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {动态4因子统计}} params
 *  /FactorSummary/?flag=1&type=equity&code=000001&start_date=&end_date=&status=summary
 * @returns
 */
export function getDynamicStatistics(params) {
	return request({
		url:  '/FactorSummary/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {TM模型分析}} params
 * @returns
 */
export function getTMStatistics(params) {
	return request({
		url:  '/TMCapability/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {同类排名比较}} params
 * @returns
 */
export function getFofMeasureSinceRank(params) {
	return request({
		url:  '/RiskFeatureYearly/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {报告期持仓统计指数列表} params
 * type
 * @returns
 */
export function getIndexList(params) {
	return request({
		url:  '/BenchmarkList/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {报告期持仓统计指数收益} params
 * code
 * @returns
 */

export function getIndexReturn(data) {
	return request({
		url: '/RateInfo/',
		method: 'post',
		data
	});
}

export function getAllocationDetails(params) {
	return request({
		url:  '/AllocationDetails/',
		method: 'get',
		params
	});
}
export function getFofAllocationDetails(params) {
	return request({
		url:  '/FofAllocationDetails/',
		method: 'get',
		params
	});
}
export function getFundMessage(params) {
	return request({
		url:  '/fund_message/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {基金持仓分析} params
 * @returns
 * yearqtr fund_code
 */
export function getFofHoldFund(params) {
	return request({
		url:  '/FofHoldFund/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {最新各类型基金配置情况}} params
 * @returns
 */
export function getFofAllocationMsg(params) {
	return request({
		url:  '/FofAllocationMsg/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {权益基金alpha/beta/smartbeta分解} params
 * @returns
 *  fund_code
 */
export function getFofAlphaRank(params) {
	return request({
		url:  '/FofAlphaRank/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {权益基金标签分析}} params
 * @returns
 */
export function getFoFEquityTag(params) {
	return request({
		url:  '/FoFEquityTag/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {权益基金股票分析}} params
 * @returns
 * fund_code
 */
export function getFoFHoldingNewest(params) {
	return request({
		url:  '/HoldStocks/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {长期持有个股} params
 * /StocksLongHold/?flag=1&code=110022&type=equity
 * @returns
 */

export function getStocksLongHold(params) {
	return request({
		url:  '/StocksLongHold/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {直投股票capm分析}} params
 * @returns
 */
export function getCapmBenchmark(params) {
	return request({
		url:  '/CapmBenchmark/',
		method: 'get',
		params
	});
}
export function getCapmAnalysis(params) {
	return request({
		url:  '/CapmAnalysis/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {股票风格特征}} params
 * /CreditDownRation/?flag=1&code=000045&type=bond
 * @returns
 */
export function getCharacteristicsBarra(params) {
	return request({
		url:  '/BarraInfo/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {产品分析} params
 * @returns
 * fund_code,type,index_code,start_date,end_date
 */
export function getFofImmediateAsset(params) {
	return request({
		url:  '/FofImmediateAsset/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {产品分析-基准列表} params
 * @returns
 * type
 */
export function getIndexBasicMsg(params) {
	return request({
		url:  '/BenchmarkList/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {短期流动性管理} params
 *  fund_code
 * @returns
 */
export function getFofLiquidity(params) {
	return request({
		url:  '/FofLiquidity/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {行业配置表现} params
 *  { flag: 2, code: this.manager_code, type: 'activeequity', industry_section: '申万(2021)' }
 * @returns
 */

// 获取基金/基金经理 行业评价数据
export function getIndustryInfo(params) {
	return request({
		url:  '/IndustryInfo/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {持仓债券分析} params
 *
 * @returns
 */
export function getBondAnalysise(params) {
	return request({
		url:  '/BondHoldingMsg/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {风格择时能力}} params
 * /TimmingStyle/?flag=1&type=bond&code=000045
 * @returns
 */
export function getStyleTiming(params) {
	return request({
		url:  '/TimingStyle/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {What-if 假想验证：调仓时的资产配置作对了吗} params
 * @returns
 */
export function getWhatIf(params) {
	return request({
		url:  '/ImaginaryNav/',
		method: 'get',
		params
	});
}
// 异常点
export function abnorlmalPoint(params) {
	return request({
		url:  '/ReturnAbnormal/',
		method: 'get',
		params
	});
}
// 获取自定义情景列表
export function getSceneList(params) {
	return request({
		url:  '/custom/page',
		method: 'get',
		params
	});
}

// 新增或修改自定义场景
export function postSceneList(data) {
	return request({
		url:  '/custom/save',
		method: 'post',
		data
	});
}
// 获取情景分析数据
export function deleteCustomScene(params) {
	return request({
		url:  '/custom/delete',
		method: 'post',
		params
	});
}

// 获取情景分析数据
export function getSceneChartData(data) {
	return request({
		url:  '/custom/chartData',
		method: 'post',
		data
	});
}
/**
 *
 * @param {获取自定义显示模版列表} params
 * @returns
 */
export function getUserConfig(params) {
	return request({
		url:  '/UserConfig/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {获取自定义模版详情} params
 * @returns
 */
export function getUserConfigInfo(params) {
	return request({
		url:  '/UserConfigInfo/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {新增自定义模版} params
 * @returns
 */
export function postUserConfigInfo(data) {
	return request({
		url:  '/UserConfigInfo/',
		method: 'post',
		data
	});
}
/**
 *
 * @param {修改自定义模版} params
 * @returns
 */
export function putUserConfigInfo(data) {
	return request({
		url:  '/UserConfigInfo/',
		method: 'put',
		data
	});
}
/**
 *
 * @param {删除自定义模版详} params
 * @returns
 */
export function deleteUserConfigInfo(params) {
	return request({
		url:  '/UserConfigInfo/',
		method: 'delete',
		params
	});
}
// 获取基金 规模及持有人结构
export function getMoneyScale(params) {
	return request({
		url:  '/money_scale/',
		method: 'get',
		params
	});
}
export function getFundHold(params) {
	return request({
		url:  '/fund_hold/',
		method: 'get',
		params
	});
}
// 获取基金/基金经理 行业评价数据
export function getHoldStocks(params) {
	return request({
		url:  '/HoldStocks/',
		method: 'get',
		params
	});
}
