<template>
	<div>
		<analysis-card-title title="股票PLUS" @downloadExcel="exportExcel">
			<div>
				<el-date-picker
					v-model="value"
					type="daterange"
					align="right"
					unlink-panels
					range-separator="至"
					start-placeholder="开始日期"
					end-placeholder="结束日期"
					format="yyyy-MM-dd"
					value-format="yyyy-MM-dd"
					@change="changeDate"
				>
				</el-date-picker>
			</div>
		</analysis-card-title>
		<div class="charts_fill_class" v-loading="loading">
			<el-empty v-show="showEmpty" description="暂无数据"></el-empty>
			<v-chart
				v-show="!showEmpty"
				element-loading-text="暂无数据"
				element-loading-spinner="el-icon-document-delete"
				element-loading-background="rgba(239, 239, 239, 0.5)"
				class="charts_one_class"
				ref="cbondReturnPlus"
				style="height: 442px; margin-top: 8px"
				autoresize
				:options="option"
			/>
		</div>
		<div>
			<el-table :data="data" style="width: 100%" height="300px" :default-sort="{ prop: 'year', order: 'descending' }">
				<el-table-column v-for="item in column" :key="item.value" :prop="item.value" :label="item.label" sortable align="gotoleft">
					<template slot-scope="{ row }">
						<span v-if="item.format == 'fix2'">{{ row[item.value] | fix2 }}</span>
						<span v-else-if="item.format == 'fix2p'">
							<span v-if="item.color" :style="cellStyle(row[item.value])">{{ row[item.value] | fix2p }}</span>
							<span v-else>{{ row[item.value] | fix2p }}</span>
						</span>
						<span v-else>{{ row[item.value] }}</span>
					</template>
				</el-table-column>
			</el-table>
		</div>
	</div>
</template>

<script>
import { barChartOption } from '@/utils/chartStyle';
// 获取基准收益 固收PLUS
import { getRateInfo, getCbondReturns } from '@/api/pages/Analysis.js';
import { downloadWord } from '@/utils/exportWord.js';
export default {
	data() {
		return {
			loading: true,
			option: {},
			showEmpty: true,
			value: [],
			info: {},
			bondData: null,
			data: [],
			fund_type: '',
			column: [
				{
					label: '年份',
					value: 'year'
				},
				{
					label: '年化收益',
					value: 'ave_return',
					format: 'fix2p'
				},
				{
					label: '累积收益',
					value: 'cum_return',
					format: 'fix2p'
				},
				{
					label: '夏普率',
					value: 'sharpe',
					format: 'fix2'
				},
				{
					label: '波动率',
					value: 'volatility',
					format: 'fix2p'
				}
			]
		};
	},
	filters: {
		fix2(val) {
			return val * 1 && !isNaN(val) ? (val * 1).toFixed(2) : '--';
		},
		fix2p(val) {
			return val * 1 && !isNaN(val) ? (val * 100).toFixed(2) + '%' : '--';
		}
	},
	methods: {
		// 获取固收PLUS
		async getCbondReturns() {
			let data = await getCbondReturns({
				code: this.info.code,
				type: this.info.type,
				status: 'stock',
				flag: this.info.flag,
				start_date: this.value?.[0] || '',
				end_date: this.value?.[1] || ''
			});
			if (data?.mtycode == 200) {
				this.getChartData(data?.data);
			} else {
				this.hideLoading();
			}
		},
		getData(info) {
			this.info = info;
			this.value = [this.info.start_date, this.info.end_date];
			this.getCbondReturns();
		},
		// 获取数据
		getChartData(data) {
			this.$nextTick(() => {
				this.data = data?.yearly_returns.map((item) => {
					return { ...item, year: item.year == 'all' ? '区间' : item.year };
				});
				this.value = [data?.daily_returns?.[1]?.date, data?.daily_returns?.[data?.daily_returns?.length - 1]?.date];
				this.bondData = data?.daily_returns;
				this.getIndexReturnInfo();
			});
		},
		// 无数据
		hideLoading() {
			this.loading = false;
			this.showEmpty = true;
		},
		// 获取基准收益
		async getIndexReturnInfo() {
			let data = await getRateInfo({
				codes: ['000300.SH'],
				type: this.info.type,
				flag: [6],
				start_date: this.value?.[0] || '',
				end_date: this.value?.[1] || ''
			});
			if (data?.mtycode == 200) {
				this.option = barChartOption({
					legend: ['plus组合', '沪深300(000300.SH)'],
					xAxis: [
						{
							data: data?.data.map((v) => v.date)
						}
					],
					dataZoom: true,
					yAxis: [{ type: 'value', name: '收益率(%)' }],
					series: [
						{
							name: 'plus组合',
							data: this.bondData?.map((item, index) => {
								return [item.date, (this.computedReturn(this.bondData.map((v) => v.rate || 0))?.[index] * 100).toFixed(2)];
							}),
							type: 'line',
							smooth: false
						},
						{
							name: '沪深300(000300.SH)',
							data: data?.data?.map((item, index) => {
								return [item.date, (this.computedReturn(data?.data?.map((v) => v.rate || 0))?.[index] * 100).toFixed(2)];
							}),
							type: 'line',
							smooth: false
						}
					]
				});
				this.showEmpty = false;
				this.loading = false;
			}
		},
		// 切换时间区间
		changeDate() {
			this.loading = true;
			this.getCbondReturns();
		},
		// 累计收益计算
		computedReturn(data) {
			let cum_return = 1;
			let cum = data.map((item) => {
				cum_return = cum_return * (1 + item);
				return cum_return - 1;
			});
			return cum;
		},

		// 动态样式设置
		cellStyle(val) {
			return val * 1 && !isNaN(val) ? (val == 0 ? 'color:black' : val > 0 ? 'color:red' : 'color:green') : 'color:black';
		}, // 导出excel
		exportExcel() {
			let list = this.column.map((item) => {
				return {
					label: item.label,
					value: item.value
				};
			});
			filter_json_to_excel(list, this.data, '股票PLUS');
		}
	}
};
</script>

<style></style>
