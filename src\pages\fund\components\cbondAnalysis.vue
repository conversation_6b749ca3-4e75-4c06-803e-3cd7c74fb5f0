<!-- 历史持仓 -->
<template>
	<div>
		<div class="flex_card">
			<div v-for="item in templateList" :key="item.value" v-show="item.isshow" :class="item.type">
				<component :is="item.is" :ref="item.value" @resolveFather="item.methods" v-loading="loading" :showDescription="true"></component>
			</div>
		</div>
	</div>
</template>

<script>
// 转债季度信息
import bondQuarterInfo from '@/components/components/components/bondQuarterInfo/index.vue';
// 转债PLUS
import cbondReturnPlus from '@/components/components/components/cbondReturnPlus/index.vue';
// 转债整股风格
import cbondStyle from '@/components/components/components/cbondStyle/index.vue';
// 转债股性债性概览
import cbondHoldEquityBond from '@/components/components/components/cbondHoldEquityBond/index.vue';
export default {
	components: {
		bondQuarterInfo,
		cbondReturnPlus,
		cbondStyle,
		cbondHoldEquityBond
	},
	data() {
		return {
			name: '转债分析',
			info: {},
			templateList: [],
			requestOver: [],
			requestAll: 0,
			loading: true
		};
	},
	props: {
		showEditor: {
			type: Boolean,
			default: false
		}
	},
	methods: {
		// 接收/返回组件列表
		getTemplateList(list) {
			if (list) {
				this.templateList = [...list];
			} else {
				return this.templateList;
			}
		},
		// 获取父组件数据
		getData(data) {
			this.info = data;
			this.loading = true;
			this.requestOver = [];
			this.formatTemplatList();
		},
		// 获取打印数据
		async createPrintWord(info) {
			this.info = info;
			let printData = [];
			this.templateList.map((item) => {
				if (item.isshow) {
					if (this.$refs[item.value]?.[0].createPrintWord) {
						let list = this.$refs[item.value]?.[0].createPrintWord(this.info);
						printData.push(list);
					}
				}
			});
			let data = await Promise.all(printData);
			data.unshift(this.$exportWord.exportFirstTitle(this.name));
			return data;
		},
		// 格式化模板列表
		formatTemplatList() {
			this.$nextTick(() => {
				this.templateList.map((item) => {
					if (item.typelist.indexOf(this.info.type) !== -1) {
						this.$refs[item.value]?.[0]?.getData(this.info);
						this.loading = false;
					}
				});
			});
		}
	}
};
</script>

<style></style>
