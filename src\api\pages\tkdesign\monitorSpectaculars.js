import request from '@/utils/request'

/**
 * 个股快速分析
 * @param params
 * @returns {*}
 */
export function getObjectStockProfitInfo(params) {
    return request({
        url: '/api/taikang/getObjectStockProfitInfoNew',
        method: 'get',
        params
    });
}

/**
 * 重点关注个股收益
 * @param params
 * @returns {*}
 */
export function getObjectStocksReturnInfo(params) {
    return request({
        url: '/api/taikang/getObjectStocksReturnInfo',
        method: 'get',
        params
    });
}

/**
 * 重点关注个股指标
 * @param params
 * @returns {*}
 */
export function getObjectStocksMeasureInfo(params) {
    return request({
        url: '/api/taikang/getObjectStocksMeasureInfo',
        method: 'get',
        params
    });
}
