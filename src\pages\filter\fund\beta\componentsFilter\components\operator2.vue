<template>
  <div style="display: flex; align-items: center">
    <el-dropdown @command="command"
                 style="margin-right: 16px">
      <el-button type="primary"> {{ iconFlag != '' ? iconFlag : '算子' }}<i class="el-icon-arrow-down el-icon--right"></i> </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="对数">对数</el-dropdown-item>
        <el-dropdown-item command="Z变换">Z变换</el-dropdown-item>
        <el-dropdown-item command="rank(0-100)">rank(0-100)</el-dropdown-item>
        <!-- <el-dropdown-item command="范围">范围</el-dropdown-item> -->
        <el-dropdown-item command="离差标准化">离差标准化</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <el-input v-show="iconFlag == '满足率'"
              v-model="num"
              @input="submit"
              placeholder="满足率(%)>="
              style="width: 110px; margin-right: 16px"></el-input>
  </div>
</template>

<script>
export default {
  props: {
    operator: {
      type: String
    },
  },
  data () {
    return {
      iconFlag: 'rank(0-100)',
      num: ''
    };
  },
  // props: {
  // 	dataX: {
  // 		type: Object,
  // 		default: {}
  // 	}
  // },
  // watch: {
  // 	dataX(val1, val2) {
  // 		this.$nextTick(() => {
  // 			this.iconFlag = this.formatKey(val1.dataResult[0].mathRange);
  // 			this.$forceUpdate();
  // 		});
  // 	}
  // },
  // mounted() {
  // 	// this.$nextTick(() => {
  // 	if (this.dataX?.dataResult) {
  // 		this.iconFlag = this.formatKey(this.dataX.dataResult[0].mathRange);
  // 		this.$forceUpdate();
  // 		this.submit();
  // 	}
  // 	// });
  // },
  watch: {
    operator (val) {
      // console.log(val, 'sssssssssssssss')
      if (this.FUNC.isEmpty(val)) {
        if (val == 'rank(0-100)') {
          this.iconFlag = 'rank(0-100)'
        }
        else if (val == 'log') {
          this.iconFlag = '对数'
        }
        else if (val == 'std') {
          this.iconFlag = 'Z变换'
        }

        else if (val == 'max-min') {
          this.iconFlag = '离差标准化'
        }


      }
      else {
        this.iconFlag = 'rank(0-100)'
      }
    }
  },
  methods: {
    command (val) {
      this.iconFlag = val;
      this.submit();
    },
    getFlag (val) {
      if (val != undefined) {
        this.iconFlag = this.formatKey(val);
      }
      this.submit();
    },
    submit () {
      console.log('iconFlag', this.iconFlag);
      this.$emit('resolveMathRange', { mathRange: this.formatKey(this.iconFlag) });
    },
    formatKey (val) {
      switch (val) {
        case '对数':
          return 'log';
        case 'Z变换':
          return 'std';
        case 'rank(0-100)':
          return 'line';
        case '离差标准化':
          return 'max-min';
        case '满足率':
          return this.num;
        case 'avg':
          return '平均';
        case 'max':
          return '最大';
        case 'min':
          return '最小';
        case 'range':
          return '范围';
        default:
          this.num = val;
          return '满足率';
      }
    }
  }
};
</script>

<style></style>
