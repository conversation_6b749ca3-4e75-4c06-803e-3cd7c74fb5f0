import request from '@/utils/request';

/**
 * 获取产品映射管理列表
 * @param params
 * @returns {*}
 */
export function getProductList(params) {
    return request({
        url: '/api/taikang/product/getProductInfo',
        method: 'get',
        params
    });
}

/**
 * 获取全部管理人
 * @returns {*}
 */
export function getManagerList() {
    return request({
        url: '/api/taikang/product/option/manager',
        method: 'get',
    });
}

/**
 * 获取全部一级策略分类
 * @returns {*}
 */
export function getStrategy1List(name) {
    return request({
        url: `/api/taikang/product/option/strategy1/?name=${name}`,
        method: 'get',
    });
}

/**
 * 获取全部二级策略分类
 * @returns {*}
 */
export function getStrategy2List(name) {
    return request({
        url: `/api/taikang/product/option/strategy2/?name=${name}`,
        method: 'get',
    });
}

/**
 * 删除
 * @param gp3
 * @returns {*}
 */
export function deleteProduct(gp3) {
    return request({
        url: `/api/taikang/product/del?gp3=${gp3}`,
        method: 'post',
    });
}

/**
 * 保存，新增接口
 * @param gp3
 * @returns {*}
 */
export function saveProduct(data) {
    return request({
        url: `/api/taikang/product/save`,
        method: 'post',
        data
    });
}

/**
 * 删除全部
 * @returns {*}
 */
export function deleteAll() {
    return request({
        url: `/api/taikang/product/delAll`,
        method: 'post',
    });
}
