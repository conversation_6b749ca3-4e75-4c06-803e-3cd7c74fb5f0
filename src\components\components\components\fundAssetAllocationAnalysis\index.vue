<template>
	<div class="positionStatistics">
		<analysis-card-title title="大类资产配置" image_id="positionStatistics">
			<el-select v-model="activeBenchmark" @change="onChangeIndex" placeholder="请选择">
				<el-option v-for="item in benchmarkList" :key="item.code" :label="item.name" :value="item.code"></el-option>
			</el-select>
		</analysis-card-title>
		<bar-chart-component v-loading="loading" @legendselectchanged="legendselectchanged" ref="barChartComponent"></bar-chart-component>
	</div>
</template>

<script>
// 报告期持仓统计
import barChartComponent from '@/components/components/fundComponents/chartComponent/barLineChart.vue';

import { getBenchmarkList, getRateInfo, getAllocationDetails } from '@/api/pages/Analysis.js';

export default {
	name: 'positionStatistics',
	components: { barChartComponent },
	props: {
		iscompany: {
			type: <PERSON>olean,
			default: false
		},
		fundTypeList_2: {}
	},
	data() {
		return {
			benchmarkList: [],
			activeBenchmark: [],
			postData: {},
			loading: true,
			fundType_5: '',
			title: '大类资产配置',
			selectedList: [],
			info: {},
			indexReturnData: {},
			tradstyleData: null
		};
	},
	watch: {
		fundTypeList_2(val) {
			this.fundType_5 = val[0].type;
		}
	},
	methods: {
		// 获取基准列表
		async getBenchmarkList() {
			let data = await getBenchmarkList({
				code: this.info.code,
				flag: this.info.flag,
				type: this.info.type
			});
			this.benchmarkList = [];
			this.activeBenchmark = '';
			if (data?.mtycode == 200) {
				this.benchmarkList = data?.data
					.sort((a, b) => {
						return b.isdefault - a.isdefault;
					})
					.map((v) => {
						return { code: v.indexCode, name: v.indexName, flag: v.flag };
					});
				if (this.benchmarkList.findIndex((v) => v.code == '000300.SH') != -1) {
					this.activeBenchmark = '000300.SH';
				} else {
					this.activeBenchmark = this.benchmarkList?.[0]?.code;
				}
			} else {
				this.benchmarkList = this.COMMON.default_index[this.info.type].map((v) => {
					return { code: v.indexCode, name: v.indexName, flag: '6' };
				});
				this.activeBenchmark = this.benchmarkList?.[0]?.code;
			}
			// this.benchmarkList.unshift({ code: '0', name: '收益率0%', flag: '6' });
			this.postData.codes = this.activeBenchmark;
			this.getAllocationDetails();
		},
		// 获取报告期持仓统计数据
		async getAllocationDetails() {
			let data = await getAllocationDetails({
				flag: this.info.flag,
				type: this.info.type,
				code: this.info.code,
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			if (data?.mtycode == 200) {
				let yearqtr = data?.data.map((item) => {
					return item.yearqtr;
				});
				this.indexReturnData.start_date = this.FUNC.earlyAndLateDate(yearqtr).earlyDate;
				this.indexReturnData.end_date = this.FUNC.earlyAndLateDate(yearqtr).lateDate;
				this.tradstyleData = data?.data;
				this.getIndexReturn();
			}
		},
		// 获取报告期持仓统计指数收益
		async getIndexReturn() {
			let data = await getRateInfo({
				codes: [this.activeBenchmark],
				flag: [this.benchmarkList.find((v) => v.code == this.activeBenchmark)?.flag],
				type: this.info.type,
				start_date: this.indexReturnData.start_date,
				end_date: this.indexReturnData.end_date
			});
			if (data?.mtycode == 200) {
				this.getChartData(this.tradstyleData, data?.data);
			} else {
				this.getChartData(this.tradstyleData, []);
			}
		},
		getBondTopTenData() {
			this.$emit('changechooseFundAll', this.fundType_5);
			this.loading = true;
		},
		// 累计收益计算
		computedReturn(data) {
			let cum_return = 1;
			let cum = data.map((item) => {
				cum_return = cum_return * (1 + (item || 0));
				return cum_return - 1;
			});
			return cum;
		},
		// 监听图例点击
		legendselectchanged(val) {
			this.selectedList = val;
		},
		getData(info, status) {
			this.info = info;
			if (status) {
				this.getPositionClass();
			} else {
				this.getBenchmarkList();
			}
		},
		// 需求更改为双x双y
		getChartData(tradstyleData, indexReturnData) {
			this.title = '大类资产配置';
			this.loading = false;
			if (tradstyleData?.length > 0) {
				let dateDayList = []; // 每日日期
				let equity_weight = [],
					abs_weight = [],
					bond_weight = [],
					cash_weight = [],
					fund_weight = [],
					other_weight = [];

				let data = tradstyleData.sort((a, b) => {
					return this.moment(this.moment(a.yearqtr, 'YYYY QQ').format()).isBefore(this.moment(b.yearqtr, 'YYYY QQ').format()) ? -1 : 1;
				});
				equity_weight = data.map((v) => {
					return [this.FUNC.getQuarterStartDate(v.yearqtr.slice(6, 7), v.yearqtr.slice(0, 4)), v.equityWeight];
				});
				abs_weight = data.map((v) => {
					return [this.FUNC.getQuarterStartDate(v.yearqtr.slice(6, 7), v.yearqtr.slice(0, 4)), v.absWeight];
				});
				bond_weight = data.map((v) => {
					return [this.FUNC.getQuarterStartDate(v.yearqtr.slice(6, 7), v.yearqtr.slice(0, 4)), v.bondWeight];
				});
				cash_weight = data.map((v) => {
					return [this.FUNC.getQuarterStartDate(v.yearqtr.slice(6, 7), v.yearqtr.slice(0, 4)), v.cashWeight];
				});
				fund_weight = data.map((v) => {
					return [this.FUNC.getQuarterStartDate(v.yearqtr.slice(6, 7), v.yearqtr.slice(0, 4)), v.fundWeight];
				});
				other_weight = data.map((v) => {
					return [this.FUNC.getQuarterStartDate(v.yearqtr.slice(6, 7), v.yearqtr.slice(0, 4)), v.otherWeight];
				});
				// 生成连续每日日期
				dateDayList = this.FUNC.generateDateList(
					this.FUNC.returnQuarter(data[0]?.yearqtr, 'start', 'quarter'),
					this.FUNC.returnQuarter(data[data.length - 1]?.yearqtr, 'end', 'quarter')
				);
				let cum = [],
					index_data = [];
				if (indexReturnData) {
					// 根据每日日期生成每日指数值
					index_data = indexReturnData.sort((a, b) => {
						this.moment(this.moment(a.date, 'YYYY-MM-DD').format()).isAfter(this.moment(b.date, 'YYYY-MM-DD').format()) ? -1 : 1;
					});
					cum = this.computedReturn(index_data.map((v) => v.rate));
				}
				let series = [
					{
						name: '股票',
						type: 'bar',
						stack: '总量',
						barWidth: tradstyleData?.yearqtr?.length > 40 ? 12 : 22,
						data: equity_weight
					},
					{
						name: 'abs',
						type: 'bar',
						stack: '总量',
						barWidth: tradstyleData?.yearqtr?.length > 40 ? 12 : 22,
						data: abs_weight
					},
					{
						name: '债券',
						type: 'bar',
						barWidth: tradstyleData?.yearqtr?.length > 40 ? 12 : 22,
						stack: '总量',
						data: bond_weight
					},
					{
						name: '货币',
						type: 'bar',
						barWidth: tradstyleData?.yearqtr?.length > 40 ? 12 : 22,
						stack: '总量',
						data: cash_weight
					},
					{
						name: '基金',
						type: 'bar',
						stack: '总量',
						barWidth: tradstyleData?.yearqtr?.length > 40 ? 12 : 22,
						data: fund_weight
					},
					{
						name: '其他',
						barWidth: tradstyleData?.yearqtr?.length > 40 ? 12 : 22,
						type: 'bar',
						stack: '总量',
						data: other_weight
					},
					{
						name: '指数',
						type: 'line',
						symbol: 'none',
						data: cum.map((v, i) => {
							return [index_data[i].date, (v * 100).toFixed(2)];
						}),
						// xAxisIndex: 1,
						yAxisIndex: 1
					}
				];
				let legend = {
					selected: this.selectedList,
					data: ['股票', 'abs', '债券', '货币', '基金', '其他', { name: '指数', icon: 'line' }]
				};
				let xAxis = dateDayList;
				this.$refs['barChartComponent'].getData({
					series,
					legend,
					xAxis,
					dateDayList
				});
			}
		},
		// 获取持仓分类
		async getPositionClass() {
			let data = await getAllocationDetails({
				flag: this.info.flag,
				type: this.info.type,
				code: this.info.code,
				start_date: this.info.start_date,
				end_date: this.info.end_date,
				status: 'True'
			});
			if (data?.mtycode == 200) {
				this.getDataCompany(data?.data);
			}
		},
		getDataCompany(tradstyleData, indexReturnData) {
			this.title = '债券资产配置';
			this.loading = false;

			if (tradstyleData?.length > 0) {
				let dateDayList = []; // 每日日期
				let indexData = []; // 指数数据
				let data = tradstyleData.sort((a, b) => {
					return this.moment(this.moment(a.yearqtr, 'YYYY QQ').format()).isBefore(this.moment(b.yearqtr, 'YYYY QQ').format()) ? -1 : 1;
				});
				let abs_weight = data.map((v) => v['ABS']),
					cbond_weight = data.map((v) => v['可转债']),
					bond_weight = data.map((v) => v['中期票据']),
					bond_weight_1 = data.map((v) => v['企业债']),
					bond_weight_2 = data.map((v) => v['其他债券']),
					buy_sell_all = data.map((v) => v['买入返售金融资产']),
					cundan = data.map((v) => v['同业存单']),
					guozhai = data.map((v) => v['国债']),
					yhpaioju = data.map((v) => v['央行票据']),
					cash_weight = data.map((v) => v['现金存款']),
					short_fund = data.map((v) => v['短期融资券']),
					fund_weight = data.map((v) => v['金融债']),
					other_weight = data.map((v) => v['其他资产']);

				// 生成季度类数据
				// tradstyleData.forEach((item, index) => {
				// 	abs_weight.push(tradstyleData['ABS']?.[index]);
				// 	cbond_weight.push(tradstyleData['可转债']?.[index]);
				// 	bond_weight.push(tradstyleData['中期票据']?.[index]);
				// 	bond_weight_1.push(tradstyleData['企业债']?.[index]);
				// 	bond_weight_2.push(tradstyleData['其他债券']?.[index]);
				// 	buy_sell_all.push(tradstyleData['买入返售金融资产']?.[index]);
				// 	cundan.push(tradstyleData['同业存单']?.[index]);
				// 	guozhai.push(tradstyleData['国债']?.[index]);
				// 	yhpaioju.push(tradstyleData['央行票据']?.[index]);
				// 	cash_weight.push(tradstyleData['现金存款']?.[index]);
				// 	short_fund.push(tradstyleData['短期融资券']?.[index]);
				// 	fund_weight.push(tradstyleData['金融债']?.[index]);
				// 	other_weight.push(tradstyleData['其他资产']?.[index]);
				// });

				// 生成连续每日日期
				dateDayList = this.FUNC.generateDateList(
					this.FUNC.returnQuarter(data[0].yearqtr, 'start', 'quarter'),
					this.FUNC.returnQuarter(data[data.length - 1].yearqtr, 'end', 'quarter')
				);

				if (indexReturnData) {
					// 根据每日日期生成每日指数值
					let data = indexReturnData.sort((a, b) => {
						return this.moment(this.moment(a.date, 'YYYY-MM-DD').format()).isAfter(this.moment(b.date, 'YYYY-MM-DD').format()) ? -1 : 1;
					});
					let date = data.map((v) => v.date);
					cum = this.computedReturn(data.map((v) => v.rate));
					let curDate = date.shift(),
						preCum = null,
						curCum = cum.shift();
					dateDayList.forEach((item) => {
						if (item == curDate) {
							indexData.push(Number(curCum * 100).toFixed(2));
							preCum = curCum;
							curDate = date.shift();
							curCum = cum.shift();
						} else if (item < curDate) {
							indexData.push(Number(preCum * 100).toFixed(2));
						} else if (item > indexReturn.date[indexReturn.date.length - 1]) {
							indexData.push(null);
						}
					});
				}
				let series = [
					// {
					// 	name: 'A股股票',
					// 	type: 'bar',
					// 	stack: '总量',
					// 	barCategoryGap: '30%',
					// 	data: equity_weight
					// },
					{
						name: 'abs',
						type: 'bar',
						stack: '总量',
						barCategoryGap: '30%',
						data: abs_weight
					},
					{
						name: '可转债',
						type: 'bar',
						barCategoryGap: '30%',
						stack: '总量',
						data: cbond_weight
					},
					{
						name: '中期票据',
						type: 'bar',
						barCategoryGap: '30%',
						stack: '总量',
						data: bond_weight
					},
					{
						name: '企业债',
						type: 'bar',
						barCategoryGap: '30%',
						stack: '总量',
						data: bond_weight_1
					},
					{
						name: '其他债券',
						type: 'bar',
						barCategoryGap: '30%',
						stack: '总量',
						data: bond_weight_2
					},
					{
						name: '买入返售金融资产',
						type: 'bar',
						barCategoryGap: '30%',
						stack: '总量',
						data: buy_sell_all
					},
					{
						name: '同业存单',
						type: 'bar',
						barCategoryGap: '30%',
						stack: '总量',
						data: cundan
					},
					{
						name: '国债',
						type: 'bar',
						barCategoryGap: '30%',
						stack: '总量',
						data: guozhai
					},
					{
						name: '央行票据',
						type: 'bar',
						barCategoryGap: '30%',
						stack: '总量',
						data: yhpaioju
					},
					{
						name: '现金存款',
						type: 'bar',
						barCategoryGap: '30%',
						stack: '总量',
						data: cash_weight
					},
					{
						name: '短期融资券',
						type: 'bar',
						barCategoryGap: '30%',
						stack: '总量',
						data: short_fund
					},
					{
						name: '其他资产',
						type: 'bar',
						barCategoryGap: '30%',
						stack: '总量',
						data: other_weight
					},
					{
						name: '指数',
						type: 'line',
						symbol: 'none',
						data: indexData,
						xAxisIndex: 1,
						yAxisIndex: 1
					}
				];
				let legend = [
					'A股股票',
					'abs',
					'可转债',
					'中期票据',
					'企业债',
					'其他债券',
					'买入返售金融资产',
					'同业存单',
					'国债',
					'央行票据',
					'现金存款',
					'短期融资券',
					'金融债',
					'其他资产'
				];
				let xAxis = tradstyleData.yearqtr;
				this.$refs['barChartComponent'].getData({
					series,
					legend,
					xAxis,
					dateDayList
				});
			}
		},
		// 监听指数变化
		onChangeIndex() {
			this.loading = true;
			this.getIndexReturn();
		},
		hideLoading() {
			this.loading = false;
		},
		async createPrintWord(info) {
			if (this.activeBenchmark) {
				this.info = info;
				await this.getAllocationDetails();
			} else {
				await this.getData(info);
			}
			return await new Promise((resolve, reject) => {
				this.$nextTick(async () => {
					let height = this.$refs['barChartComponent'].$el.clientHeight;
					let width = this.$refs['barChartComponent'].$el.clientWidth;
					let chart = this.$refs['barChartComponent'].createPrintWord();
					resolve([
						...this.$exportWord.exportTitle('大类资产配置'),
						...this.$exportWord.exportDescripe('基准指数为：' + this.benchmarkList.find((v) => v.code == this.activeBenchmark)?.name),
						...this.$exportWord.exportChart(chart, { width, height })
					]);
				});
			});
		}
	}
};
</script>

<style scoped>
.hold-statistic-header {
	display: flex;
	justify-content: space-between;
}
</style>
