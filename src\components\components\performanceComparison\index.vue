<template>
	<div class="chart_one">
		<div v-loading="loadyejis">
			<div class="card_header">
				<div class="title">业绩对比</div>
			</div>
			<div class="charts_fill_class" style="margin-top: 8px" v-loading="loading">
				<v-chart
					element-loading-text="暂无数据"
					element-loading-spinner="el-icon-document-delete"
					element-loading-background="rgba(239, 239, 239, 0.5)"
					class="charts_one_class"
					ref="performanceComparison"
					autoresize
					:options="option"
				/>
			</div>
			<el-table :data="data">
				<el-table-column v-for="(item, index) in columnList" :key="index" :label="item.label" :prop="item.value" align="gotoleft">
					<template slot-scope="{ row }">
						<span v-show="item.format">{{ row[item.value] | fix2p }}</span>
						<span v-show="!item.format">{{ row[item.value] }}</span>
					</template>
				</el-table-column>
			</el-table>
		</div>
	</div>
</template>

<script>
import VChart from 'vue-echarts';
import searchComponents from '@/components/components/components/search/index.vue';
import { lineChartOption } from '@/utils/chartStyle';
export default {
	name: 'performanceComparison',
	components: { VChart, searchComponents },
	data() {
		return {
			info: {},
			options: [
				{
					label: '总回报',
					value: 'all'
				}
			],
			model: '总回报',
			option: {},
			data: [],
			columnList: [
				{
					label: '收益率',
					value: 'cum_return',
					format: 'fix2p'
				},
				{
					label: '本周收益率',
					value: 'week_return',
					format: 'fix2p'
				},
				{
					label: '本月收益率',
					value: 'month_return',
					format: 'fix2p'
				},
				{
					label: '本季收益率',
					value: 'quarter_return',
					format: 'fix2p'
				},
				{
					label: '本年收益率',
					value: 'year_return',
					format: 'fix2p'
				},
				{
					label: '年化收益率',
					value: 'ann_return',
					format: 'fix2p'
				},
				{
					label: '年化波动率',
					value: 'ann_vol',
					format: 'fix2p'
				},
				{
					label: '超额收益率',
					value: 'excess_return',
					format: 'fix2p'
				}
				// {
				// 	label: '年化超额收益率',
				// 	value: 'annualized_excess_return',
				// 	format: 'fix2p'
				// },
				// {
				// 	label: '夏普率',
				// 	value: 'sharp',
				// 	format: 'fix2p'
				// },
				// {
				// 	label: '卡玛比率',
				// 	value: 'kama_ratio',
				// 	format: 'fix2p'
				// },
				// {
				// 	label: '索提诺比率',
				// 	value: 'sortino_ratio',
				// 	format: 'fix2p'
				// }
				// {
				// 	label: 'Alpha',
				// 	value: 'alpha'
				// },
				// {
				// 	label: 'Beta',
				// 	value: 'beta'
				// },
				// {
				// 	label: '信息比率',
				// 	value: 'tracking_error'
				// },
				// {
				// 	label: '最大回撤',
				// 	value: 'maxdown'
				// },
				// {
				// 	label: '最大回撤回补期(日)',
				// 	value: 'maxdown_replenishment'
				// },
				// {
				// 	label: '日胜率',
				// 	value: 'daily_success',
				// 	format: 'fix2p'
				// },
				// {
				// 	label: '操作',
				// 	value: 'setting'
				// }
			],
			combinationData: [],
			loading: true
		};
	},
	props: {
		indexInfo: {
			type: Object,
			default: {}
		}
	},
	filters: {
		fix2p(val) {
			return val * 100 ? (val * 100).toFixed(2) + '%' : '--';
		}
	},
	methods: {
		getData(data, info) {
			this.info = info;
			this.combinationData = data;
			this.setOption(this.combinationData);
			// this.$emit('resolveFather', this.indexInfo);
		},
		filterData(data) {
			let value = data.filter((item) => {
				return this.info.code == item.name;
			})?.[0];
			let index = data.filter((item) => {
				return this.info.code != item.name;
			})?.[0];
			let excessReturn = {
				date: value?.date,
				name: '超额收益',
				value: value?.date?.map((item, i) => {
					let j = index?.date
						.map((time) => {
							return time.slice(0, 10);
						})
						.indexOf(item);
					return value?.value[i] - index?.value[j];
				})
			};
			return [...data, excessReturn];
		},
		getIndexData(data) {
			this.setOption(
				this.filterData([
					...this.combinationData,
					...data.map((item) => {
						return {
							...item,
							value: this.computedReturn(item.value)
						};
					})
				])
			);
		},
		// 累计收益计算
		computedReturn(data) {
			let cum_return = 1;
			let cum = data.map((item) => {
				cum_return = cum_return * (1 + item);
				return cum_return - 1;
			});
			return cum;
		},
		setOption(data) {
			this.loading = false;
			data = data?.map((item) => {
				return {
					...item,
					name: item.name == this.info.code ? this.info.name : item.name == this.indexInfo.id ? this.indexInfo.name : item.name
				};
			});
			let legend = data?.map((item) => {
				return item.name + '总回报';
			});
			let xAxis = data?.filter((item) => {
				return item.name == this.info.name;
			})?.[0]?.date;
			let series = data?.map((item) => {
				return {
					name: item.name + '总回报',
					type: 'line',
					symbol: 'none',
					connectNulls: true,
					data: item.value.map((obj, index) => {
						return [item.date[index].slice(0, 10), obj];
					})
				};
			});
			this.option = lineChartOption({
				legend,
				dataZoom: true,
				tooltip: {
					formatter: function (obj) {
						var value = obj[0].axisValue + `<br />`;
						for (let i = 0; i < obj.length; i++) {
							value +=
								`<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:` +
								obj[i].color +
								`;"></span>` +
								obj[i].seriesName +
								':' +
								(Number(obj[i].data[1]) * 100).toFixed(2) +
								'%' +
								`<br />`;
						}
						return value;
					}
				},
				xAxis: [
					{
						data: xAxis,
						formatter: function (obj) {
							let date = obj.split('-');
							return date[0] + '-' + date[1];
						}
					}
				],
				yAxis: [
					{
						formatter: function (obj) {
							return (obj * 100 > 10 || -(obj * 100) > 10 ? (obj * 100).toFixed(0) : (obj * 100).toFixed(2)) + '%';
						}
					}
				],
				series
			});
		},
		getIndexInfo(info) {
			this.indexInfo = info;
			this.loading = true;
			this.$emit('resolveFather', info);
		},
		getInfoData(data) {
			this.data = [{ ...data, cum_return: data?.cum_return?.[0] }];
		},
		createPrintWord() {
			this.$refs['performanceComparison'].mergeOptions({ toolbox: { show: false } });
			let height = this.$refs['performanceComparison']?.$el.clientHeight;
			let width = this.$refs['performanceComparison']?.$el.clientWidth;
			let chart = this.$refs['performanceComparison'].getDataURL({
				type: 'png',
				pixelRatio: 3,
				backgroundColor: '#fff'
			});
			this.$refs['performanceComparison'].mergeOptions({ toolbox: { show: true } });
			return [
				...this.$exportWord.exportTitle('业绩对比'),
				...this.$exportWord.exportChart(chart, { width, height }),
				...this.$exportWord.exportTable(this.columnList, this.data)
			];
		}
	}
};
</script>

<style scoped></style>
