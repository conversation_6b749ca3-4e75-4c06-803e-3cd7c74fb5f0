<!--  -->
<template>
  <div class="industryTheme">
    <operator v-if="is_range"
              ref="operator"
              @resolveMathRange="resolveMathRange"></operator>
    <search-component type="stock"
                      select-style="width: 454px"
                      @resolveFather="getFundInfo"></search-component>
    <el-dropdown style="margin-left: 16px"
                 @command="command">
      <el-button type="primary">
        {{ iconFlag != '' ? (iconFlag == 'all' ? '所有' : iconFlag) : '运算符' }}<i class="el-icon-arrow-down el-icon--right"></i>
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="all">所有</el-dropdown-item>
        <el-dropdown-item command="<">&lt;</el-dropdown-item>
        <el-dropdown-item command="=">=</el-dropdown-item>
        <el-dropdown-item command=">">&gt;</el-dropdown-item>
        <el-dropdown-item command="<=">&lt;=</el-dropdown-item>
        <el-dropdown-item command=">=">&gt;=</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <div v-show="showBox"
         style="margin-left: 0px; display: flex; align-items: center">
      <div style="margin-left: 16px">
        <el-input type="number"
                  @input="inputChange"
                  placeholder="输入10,即持仓占比为10%"
                  v-model="input"></el-input>
      </div>
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import operator from '@/pages/filter/fund/beta/componentsFilter/components/operator.vue';
import searchComponent from '@/components/components/components/search/index.vue';
export default {
  props: {
    is_range: {
      type: Boolean,
      default: false
    },
    haveName: {
      type: String,
      default: ''
    },
    dataX: {
      type: Object,
      default: {}
    },
    placeholder: {
      type: String
    },
    indexFlag: {
      type: Number
    },
    baseIndexFlag: {
      type: Number
    },
    dataIndustry: {
      type: Object
    }
  },
  //import引入的组件需要注入到对象中才能使用
  components: { operator, searchComponent },
  data () {
    //这里存放数据
    return {
      value: '',
      iconFlag: '',
      showBox: false,
      input: '',
      quarterList: [],
      industry_name: '',
      industry_nameT: '',
      mathRange: { mathRange: 'avg' }
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {
    dataIndustry (val) {
      this.getObject();
    },
    dataX (val) {
      if (val.dataResult && val.dataResult.length > 0) {
        this.showBox = true;
        this.iconFlag = val.dataResult[0].flag;
        this.input = val.dataResult[0].value;
        this.industry_name = val.dataResult[0].industryValue;
        this.value = this.industry_name;
        this.industry_nameT = val.dataResult[0].industryName;
        if (this.$refs['operator']) {
          this.$refs['operator'].getFlag(val.dataResult[0].mathRange);
        }
      }
    }
  },
  //方法集合
  methods: {
    getFundInfo (val) {
      this.industry_name = val?.id;
      this.industry_nameT = val?.name;
      this.$emit('StockChange', [{ stock_code: val.id }]);
    },
    resolveMathRange (obj) {
      this.mathRange = obj;
      this.resolveFather();
    },
    resolveFather () {
      this.$emit(
        'StockChange',
        this.baseIndexFlag,
        this.indexFlag,
        this.input,
        this.iconFlag,
        this.industry_name,
        this.value,
        this.FUNC.isEmpty(this.industry_name) && this.FUNC.isEmpty(this.input) && this.FUNC.isEmpty(this.iconFlag),
        this.mathRange
      );
    },
    command (e) {
      this.iconFlag = e;
      // this.industry_nameT =
      //   this.quarterList.findIndex((item) => item.value == this.industry_name) >= 0
      //     ? this.quarterList[this.quarterList.findIndex((item) => item.value == this.industry_name)][
      //     this.haveName.indexOf('主题') >= 0 ? 'lable' : 'label'
      //     ]
      //     : '';
      this.showBox = true;
      this.resolveFather();
    },
    command2 () {
      this.industry_name = this.value;
      this.resolveFather();
    },
    inputChange () {
      this.resolveFather();
    },
    getObject () {
      if (JSON.stringify(this.dataIndustry) != '{}') {
        if (this.haveName.indexOf('主题') >= 0) {
          this.quarterList = this.dataIndustry.alpha[4];
        } else if (this.haveName.indexOf('申万一级') >= 0) {
          this.quarterList = this.dataIndustry.alpha[0]['申万(2021)'];
        } else if (this.haveName.indexOf('申万二级') >= 0) {
          this.quarterList = this.dataIndustry.alpha[1]['申万二级(2021)'];
        } else if (this.haveName.indexOf('申万三级') >= 0) {
          this.quarterList = this.dataIndustry.alpha[2]['申万三级(2021)'];
        } else if (this.haveName.indexOf('恒生') >= 0) {
          this.quarterList = this.dataIndustry.alpha[3]['恒生一级'];
        }
      }
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () {
    if (JSON.stringify(this.dataX) != '{}') {
      if (this.dataX.dataResult && this.dataX.dataResult.length > 0) {
        this.showBox = true;
        this.iconFlag = this.dataX.dataResult[0].flag;
        this.input = this.dataX.dataResult[0].value;
        this.industry_nameT = this.dataX.dataResult[0].industryName;
        this.industry_name = this.dataX.dataResult[0].industryValue;
        this.value = this.industry_name;
        if (this.$refs['operator']) {
          this.$refs['operator'].getFlag(this.dataX.dataResult[0].mathRange);
        }
      }
    }
    this.getObject();
  },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () {
    this.getObject();
  }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style>
.industry_theme_drapDown {
	height: 300px !important;
	overflow: auto !important;
}
</style>
<style lang="scss" scoped>
//@import url(); 引入公共css类
.industryTheme {
	display: flex;
	align-items: center;
}
</style>
