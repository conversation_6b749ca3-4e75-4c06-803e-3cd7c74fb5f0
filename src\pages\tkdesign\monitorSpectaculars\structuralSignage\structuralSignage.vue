<template>
  <div>
    <div class="box_Board">
      <div class="header_box">
        <span class="header_inactive">投后&nbsp;/&nbsp;投后监控看板&nbsp;/&nbsp; </span>
        结构看板
        <div class="header-button">
          <!-- 单选框 -->
          <div class="border_table_header_radio">
            <el-checkbox v-model="params.checkedOld">剔除税延及专属商业养老</el-checkbox>
          </div>
          <!-- 单选框 -->
          <!-- <div class="border_table_header_radio">
              <el-checkbox v-model="params.checkedTax"  >税费后</el-checkbox>
          </div> -->
          <!-- <el-radio-group class="group"
                          v-model="params.measure">
            <el-radio-button label="1">期末账面价值</el-radio-button>
           <el-radio-button label="2">平均资金占用</el-radio-button>
          </el-radio-group> -->
          <div style="margin-left:24px"
               class="block">
            <el-date-picker v-model="params.time"
                            unlink-panels
                            end-placeholder="结束时间"
                            start-placeholder="起始时间"
                            type="daterange" />
          </div>
        </div>
      </div>
    </div>
    <SubManager :params="params" />
    <DivisionAssets :params="params" />
    <structuralGrouping :params="params" />
  </div>
</template>

<script>


import DivisionAssets from "./components/divisionAssets.vue";
import StructuralGrouping from "./components/structuralGrouping.vue";
import SubManager from "./components/subManager.vue";

export default {
  components: { StructuralGrouping, DivisionAssets, SubManager },
  data () {
    return {
      params: {
        time: [],
        measure: '1',
        checkedOld: false,
        checkedTax: false,
      }
    };
  },
  mounted () {
    // var today = new Date(); // 获取当前日期时间
    // var lastMonth = new Date(today); // 创建一个与今天相同的日期对象
    // lastMonth.setMonth(lastMonth.getMonth() - 1); // 将日期设置为上个月

    // var startDate = lastMonth; // 区间开始日期为上个月的今天
    // var endDate = today; // 区间结束日期为今天
    const currentYear = new Date().getFullYear();
    const startDate = new Date(currentYear, 0, 1);
    const now = new Date();
    const yesterday = new Date(now);
    yesterday.setDate(now.getDate() - 1);
    const endDate = new Date();
    // const end = new Date();
    // const start = new Date();
    // start.setFullYear(end.getFullYear() - 1);
    // // const start = new Date(new Date().getFullYear(), 0, 1);
    // this.time = [startDate, endDate]
    // const end = new Date();
    // const start = new Date();
    // start.setFullYear(end.getFullYear() - 1);
    // const start = new Date(new Date().getFullYear(), 0, 1);

    this.params.time = [startDate, yesterday]
  },
  methods: {

  },
}
</script>
<style lang="scss" scoped>
.box_Board {
	padding: 0 24px 16px 24px;
}

.header-button {
	position: absolute;
	display: flex;
	flex-direction: row;
	justify-content: flex-end;
	align-items: center;
	right: 22px;

	.border_table_header_radio {
		display: flex;
		align-items: center;
		margin-left: 20px;
	}
}

.block {
}

.group {
	margin: 0 20px;
}

.vertical {
	width: 6px;
	height: 20px;
	border-radius: 35px;
	background: #4096ff;
	position: absolute;
}

.border_table {
	padding: 16px 24px;
	background: white;

	.border_table_header {
		margin-top: 7px;

		.border_table_header_title {
			color: rgba(0, 0, 0, 0.85);
			text-align: center;
			font-size: 16px;
			font-style: normal;
			font-weight: 500;
			line-height: 26px; /* 150% */
			margin-left: 18px;
		}

		.border_table_header_search {
			display: flex;

			.search-security {
				width: 250px;
				margin-right: 10px;
			}
		}
	}
}

.header_box {
	display: flex;
	margin-top: 16px;
	margin-bottom: 16px;
	font-size: 14px;
}

.header_inactive {
	font-size: 14px;
	font-weight: 400;
	line-height: 22px;
	text-align: left;
	color: rgba(0, 0, 0, 0.45);
}

.header_active {
	font-size: 14px;
	font-weight: 400;
	line-height: 22px;
	text-align: left;
	color: rgba(0, 0, 0, 0.85);
}

.demo-date-picker {
	display: flex;
	width: 100%;
	padding: 0;
	flex-wrap: wrap;
}

.demo-date-picker .block {
	padding: 30px 0;
	text-align: center;
	border-right: solid 1px var(--el-border-color);
	flex: 1;
}

.demo-date-picker .block:last-child {
	border-right: none;
}

.demo-date-picker .demonstration {
	display: block;
	color: var(--el-text-color-secondary);
	font-size: 14px;
	margin-bottom: 20px;
}
</style>
