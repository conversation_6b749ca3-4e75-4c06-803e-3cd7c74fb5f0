<template>
	<div>
		<analysis-card-title title="投资收益分析" @downloadExcel="exportExcel">
			<el-button @click="flage = !flage" icon="el-icon-refresh"></el-button>
		</analysis-card-title>
		<div v-loading="loading">
			<div v-show="flage">
				<el-table
					v-loading="loading"
					:data="investreturn"
					stripe
					height="400"
					ref="multipleTable"
					header-cell-class-name="table-header"
					:cell-style="cellStyle"
				>
					<el-table-column
						v-for="item in column"
						:key="item.value"
						:prop="item.value"
						:align="item.align ? item.align : 'gotoleft'"
						:label="item.label"
						border
						stripe
						sortable
					>
						<template #header>
							<long-table-popover-chart
								v-if="item.popover"
								:data="investreturn"
								date_key="终止日"
								:data_key="item.value"
								:show_name="item.label"
							>
								<span>{{ item.label }}</span>
							</long-table-popover-chart>
							<span v-else>{{ item.label }}</span>
						</template>
						<template slot-scope="{ row }">
							<span>
								{{ item.format ? item.format(row[item.value]) : row[item.value] }}
							</span>
						</template>
					</el-table-column>
					<template slot="empty">
						<el-empty image-size="160"></el-empty>
					</template>
				</el-table>
			</div>
			<div v-show="!flage">
				<bar-chart-component ref="barChartComponent" />
			</div>
		</div>
	</div>
</template>

<script>
import { exportTitle, exportChart } from '@/utils/exportWord.js';
import barChartComponent from '@/components/components/fundComponents/chartComponent/barChart.vue';
import { filter_json_to_excel } from '@/utils/exportExcel.js';

// 基金收益来源分析
import { getProfitAnalysis } from '@/api/pages/Analysis.js';
// 投资收益分析
export default {
	name: 'investmentIncomeAnalysis',
	components: {
		barChartComponent
	},
	data() {
		return {
			showzhuban: true,
			picdata: {},
			touzimsg: '',
			investreturn: [],
			touzifenxioption: {},
			flage: false,
			info: {},
			column: [
				{ label: '起始日', value: '起始日' },
				{ label: '终止日', value: '终止日' },
				{ label: '基金', value: '基金', format: this.fix2p, fill: 'red_or_green', popover: true },
				{ label: '股票', value: '股票', format: this.fix2p, fill: 'red_or_green', popover: true },
				{ label: '债券', value: '债券', format: this.fix2p, fill: 'red_or_green', popover: true },
				{ label: 'abs', value: 'abs', format: this.fix2p, fill: 'red_or_green', popover: true },
				{ label: '贵金属', value: '贵金属', format: this.fix2p, fill: 'red_or_green', popover: true },
				{ label: '衍生物', value: '衍生物', format: this.fix2p, fill: 'red_or_green', popover: true },
				{ label: '股息', value: '股息', format: this.fix2p, fill: 'red_or_green', popover: true },
				{ label: '其他', value: '其他', format: this.fix2p, fill: 'red_or_green', popover: true }
			]
		};
	},
	filters: {
		fix3(value) {
			return parseInt(value * 1000) / 1000;
		},
		fix2p(value) {
			return (value * 100).toFixed(2) + '%';
		}
	},
	methods: {
		// 基金收益来源分析
		async getProfitAnalysis() {
			this.showzhuban = true;
			let data = await getProfitAnalysis({
				code: this.info.code,
				start_date: this.info.start_date,
				flag: this.info.flag,
				end_date: this.info.end_date,
				template: 'investmentIncomeAnalysis'
			});
			this.showzhuban = false;
			if (data?.mtycode == 200) {
				this.picdata = data?.data;
				this.drawtouzi();
				// this.touzimsg = data.b1 + data.b2 + data.b3 + data.b4 + data.b5 + data.b6 + data.b7;
				this.investreturn = data?.data?.map((item, index) => {
					return {
						起始日: item.startdate,
						终止日: item?.enddate,
						基金: item?.fundinvestincome,
						股票: item?.stockinvestincome,
						债券: item?.bondinvestincome,
						abs: item?.absinvestincome,
						贵金属: item?.rmetalinvestincome,
						衍生物: item?.derivativeinvestincome,
						股息: item?.dividendincome,
						其他: item?.otherinvestincome
					};
				}); //投资收益分析
			}
		},
		getData(info) {
			this.info = info;
			this.getProfitAnalysis();
		},
		drawtouzi() {
			if (this.picdata.length) {
				let data = {};
				data.legend = ['基金', '股票', '债券', 'ABS', '贵金属', '衍生物', '股息', '其他'];
				data.xAxis = this.picdata.map((v) => v.enddate);
				data.series = [
					{
						name: '基金',
						type: 'bar',
						stack: '总量',
						barWidth: '100%',
						data: this.picdata.map((v) => v.fundinvestincome)
					},
					{
						name: '股票',
						type: 'bar',
						stack: '总量',
						barWidth: '100%',
						data: this.picdata.map((v) => v.stockinvestincome)
					},
					{
						name: '债券',
						type: 'bar',
						stack: '总量',
						barWidth: '100%',
						data: this.picdata.map((v) => v.bondinvestincome)
					},
					{
						name: 'ABS',
						type: 'bar',
						stack: '总量',
						barWidth: '100%',
						data: this.picdata.map((v) => v.absinvestincome)
					},
					{
						name: '贵金属',
						type: 'bar',
						stack: '总量',
						barWidth: '100%',
						data: this.picdata.map((v) => v.rmetalinvestincome)
					},
					{
						name: '衍生物',
						type: 'bar',
						stack: '总量',
						barWidth: '100%',
						data: this.picdata.map((v) => v.derivativeinvestincome)
					},
					{
						name: '股息',
						type: 'bar',
						stack: '总量',
						barWidth: '100%',
						data: this.picdata.map((v) => v.dividendincome)
					},
					{
						name: '其他',
						type: 'bar',
						stack: '总量',
						barWidth: '100%',
						data: this.picdata.map((v) => v.otherinvestincome)
					}
				];
				this.$refs['barChartComponent'].getData(data);
			}
		},
		fix2p(val) {
			return val * 1 && !isNaN(val) ? (val * 100).toFixed(2) + '%' : '--';
		},
		// 行样式
		cellStyle({ row, column, rowIndex, columnIndex }) {
			let obj = this.column.find((v) => v.value == column.property);
			if (obj?.fill == 'red_or_green') {
				if (row[obj.value] > 0) {
					return {
						color: '#CF1322'
					};
				} else if (row[obj.value] < 0) {
					return {
						color: '#389E0D'
					};
				}
			}
		},
		// 导出为Excel
		exportExcel() {
			let list = [
				{
					label: '起始日',
					value: '起始日'
				},
				{
					label: '终止日',
					value: '终止日'
				},
				{
					label: '基金',
					value: '基金',
					format: 'fix2p'
				},
				{
					label: '股票',
					value: '股票',
					format: 'fix2p'
				},
				{
					label: '债券',
					value: '债券',
					format: 'fix2p'
				},
				{
					label: 'abs',
					value: 'abs',
					format: 'fix2p'
				},
				{
					label: '贵金属',
					value: '贵金属',
					format: 'fix2p'
				},
				{
					label: '衍生物',
					value: '衍生物',
					format: 'fix2p'
				},
				{
					label: '股息',
					value: '股息',
					format: 'fix2p'
				},
				{
					label: '其他',
					value: '其他',
					format: 'fix2p'
				}
			];
			filter_json_to_excel(list, this.investreturn, '投资收益分析');
		},
		createPrintWord() {
			let height = this.$refs['barChartComponent'].$el.clientHeight;
			let width = this.$refs['barChartComponent'].$el.clientWidth;
			let chart = this.$refs['barChartComponent'].createPrintWord();
			return [...exportTitle('投资收益分析'), ...exportChart(chart, { width, height })];
		}
	}
};
</script>

<style></style>
