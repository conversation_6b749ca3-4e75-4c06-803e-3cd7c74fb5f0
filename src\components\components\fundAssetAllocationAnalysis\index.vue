<template>
	<div :class="iscompany ? '' : 'chart_one'" v-loading="loading">
		<div>
			<div
				style="
					display: flex;
					align-items: center;
					justify-content: space-between;
					position: relative;
					height: 56px;
					border-bottom: 1px solid #e9e9e9;
				"
			>
				<div class="title">{{ title }}</div>
				<div style="display: flex">
					<div v-if="iscompany">
						<el-select @change="getBondTopTenData" style="margin-left: 16px" v-model="fundType_5" placeholder="请选择">
							<el-option v-for="item in fundTypeList_2" :key="item.type" :label="item.name" :value="item.type"> </el-option>
						</el-select>
					</div>
					<div style="margin-left: 16px" v-show="indexTypeList.length">
						<span>参考指数：</span>
						<el-select v-model="indexType" @change="onChangeIndex" placeholder="请选择">
							<el-option v-for="item in indexTypeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
						</el-select>
					</div>
				</div>
			</div>
		</div>
		<bar-chart-component @legendselectchanged="legendselectchanged" ref="barChartComponent"></bar-chart-component>
	</div>
</template>

<script>
import { exportTitle, exportChart } from '@/utils/exportWord.js';
// 报告期持仓统计
import barChartComponent from '@/components/components/fundComponents/chartComponent/barLineChart.vue';
export default {
	name: 'positionStatistics',
	components: { barChartComponent },
	props: {
		iscompany: {
			type: Boolean,
			default: false
		},
		fundTypeList_2: {}
	},
	data() {
		return {
			indexTypeList: [],
			indexType: [],
			postData: {},
			loading: true,
			fundType_5: '',
			title: '大类资产配置',
			selectedList: []
		};
	},
	watch: {
		fundTypeList_2(val) {
			this.fundType_5 = val[0].type;
		}
	},
	methods: {
		getBondTopTenData() {
			this.$emit('changechooseFundAll', this.fundType_5);
			this.loading = true;
		},
		async getIndexList(data) {
			// 获取指数类列表
			this.indexTypeList = data.data;
			this.indexType = this.indexTypeList[0].value;
			this.postData.index_codes = [this.indexType];
			this.$emit('resolveFather', this.postData);
		},
		// 累计收益计算
		computedReturn(data) {
			let cum_return = 1;
			let cum = data.map((item) => {
				cum_return = cum_return * (1 + item);
				return cum_return - 1;
			});
			return cum;
		},
		// 监听图例点击
		legendselectchanged(val) {
			this.selectedList = val;
		},
		// 需求更改为双x双y
		getData(tradstyleData, indexReturnData) {
			this.title = '大类资产配置';
			this.loading = false;
			if (tradstyleData?.equity_weight && tradstyleData?.equity_weight?.length > 0) {
				let dateDayList = []; // 每日日期
				let indexData = []; // 指数数据
				let equity_weight = [],
					abs_weight = [],
					bond_weight = [],
					cash_weight = [],
					fund_weight = [],
					other_weight = [];

				// 生成季度类数据
				tradstyleData.yearqtr.forEach((item, index) => {
					equity_weight.push([
						this.FUNC.getQuarterStartDate(tradstyleData.yearqtr[index].slice(6, 7), tradstyleData.yearqtr[index].slice(0, 4)),
						tradstyleData.equity_weight[index]
					]);
					abs_weight.push([
						this.FUNC.getQuarterStartDate(tradstyleData.yearqtr[index].slice(6, 7), tradstyleData.yearqtr[index].slice(0, 4)),
						tradstyleData.abs_weight[index]
					]);
					bond_weight.push([
						this.FUNC.getQuarterStartDate(tradstyleData.yearqtr[index].slice(6, 7), tradstyleData.yearqtr[index].slice(0, 4)),
						tradstyleData.bond_weight[index]
					]);
					cash_weight.push([
						this.FUNC.getQuarterStartDate(tradstyleData.yearqtr[index].slice(6, 7), tradstyleData.yearqtr[index].slice(0, 4)),
						tradstyleData.cash_weight[index]
					]);
					fund_weight.push([
						this.FUNC.getQuarterStartDate(tradstyleData.yearqtr[index].slice(6, 7), tradstyleData.yearqtr[index].slice(0, 4)),
						tradstyleData.fund_weight[index]
					]);
					other_weight.push([
						this.FUNC.getQuarterStartDate(tradstyleData.yearqtr[index].slice(6, 7), tradstyleData.yearqtr[index].slice(0, 4)),
						tradstyleData.other_weight[index]
					]);
				});

				// 生成连续每日日期
				dateDayList = this.FUNC.generateDateList(
					this.FUNC.returnQuarter(tradstyleData.yearqtr[0], 'start', 'quarter'),
					this.FUNC.returnQuarter(tradstyleData.yearqtr[tradstyleData.yearqtr.length - 1], 'end', 'quarter')
				);
				let indexReturn = indexReturnData?.[0];
				if (indexReturn) {
					// 根据每日日期生成每日指数值
					let date = indexReturn.date.map((item) => {
							return item.slice(0, 10);
						}),
						cum = this.computedReturn(indexReturn.value.slice());
					let curDate = date.shift(),
						preCum = null,
						curCum = cum.shift();
					dateDayList.forEach((item) => {
						if (item == curDate) {
							indexData.push(Number(curCum * 100).toFixed(2));
							preCum = curCum;
							curDate = date.shift();
							curCum = cum.shift();
						} else if (item < curDate) {
							indexData.push(Number(preCum * 100).toFixed(2));
						} else if (item > indexReturn.date[indexReturn.date.length - 1]) {
							indexData.push(null);
						}
					});
				}
				let series = [
					{
						name: '股票',
						type: 'bar',
						stack: '总量',
						barWidth: tradstyleData?.yearqtr?.length > 40 ? 12 : 22,
						data: equity_weight
					},
					{
						name: 'abs',
						type: 'bar',
						stack: '总量',
						barWidth: tradstyleData?.yearqtr?.length > 40 ? 12 : 22,
						data: abs_weight
					},
					{
						name: '债券',
						type: 'bar',
						barWidth: tradstyleData?.yearqtr?.length > 40 ? 12 : 22,
						stack: '总量',
						data: bond_weight
					},
					{
						name: '货币',
						type: 'bar',
						barWidth: tradstyleData?.yearqtr?.length > 40 ? 12 : 22,
						stack: '总量',
						data: cash_weight
					},
					{
						name: '基金',
						type: 'bar',
						stack: '总量',
						barWidth: tradstyleData?.yearqtr?.length > 40 ? 12 : 22,
						data: fund_weight
					},
					{
						name: '其他',
						barWidth: tradstyleData?.yearqtr?.length > 40 ? 12 : 22,
						type: 'bar',
						stack: '总量',
						data: other_weight
					},
					{
						name: '指数',
						type: 'line',
						symbol: 'none',
						data: indexData,
						// xAxisIndex: 1,
						yAxisIndex: 1
					}
				];
				let legend = { selected: this.selectedList, data: ['股票', 'abs', '债券', '货币', '基金', '其他', '指数'] };
				let xAxis = dateDayList;
				this.$refs['barChartComponent'].getData({ series, legend, xAxis, dateDayList });
			}
		},
		getDataCompany(tradstyleData, indexReturnData) {
			this.title = '债券资产配置';
			this.loading = false;

			if (tradstyleData?.['yearqtr'] && tradstyleData?.['yearqtr']?.length > 0) {
				let dateDayList = []; // 每日日期
				let indexData = []; // 指数数据
				let equity_weight = [],
					abs_weight = [],
					cbond_weight = [],
					bond_weight = [],
					bond_weight_1 = [],
					bond_weight_2 = [],
					buy_sell_all = [],
					cundan = [],
					guozhai = [],
					yhpaioju = [],
					cash_weight = [],
					short_fund = [],
					fund_weight = [],
					other_weight = [];

				// 生成季度类数据
				tradstyleData.yearqtr.forEach((item, index) => {
					abs_weight.push(tradstyleData['ABS']?.[index]);
					cbond_weight.push(tradstyleData['可转债']?.[index]);
					bond_weight.push(tradstyleData['中期票据']?.[index]);
					bond_weight_1.push(tradstyleData['企业债']?.[index]);
					bond_weight_2.push(tradstyleData['其他债券']?.[index]);
					buy_sell_all.push(tradstyleData['买入返售金融资产']?.[index]);
					cundan.push(tradstyleData['同业存单']?.[index]);
					guozhai.push(tradstyleData['国债']?.[index]);
					yhpaioju.push(tradstyleData['央行票据']?.[index]);
					cash_weight.push(tradstyleData['现金存款']?.[index]);
					short_fund.push(tradstyleData['短期融资券']?.[index]);
					fund_weight.push(tradstyleData['金融债']?.[index]);
					other_weight.push(tradstyleData['其他资产']?.[index]);
				});

				// 生成连续每日日期
				dateDayList = this.FUNC.generateDateList(
					this.FUNC.returnQuarter(tradstyleData.yearqtr[0], 'start', 'quarter'),
					this.FUNC.returnQuarter(tradstyleData.yearqtr[tradstyleData.yearqtr.length - 1], 'end', 'quarter')
				);
				let indexReturn = indexReturnData?.[0];

				if (indexReturn) {
					// 根据每日日期生成每日指数值
					let date = indexReturn.date.map((item) => {
							return item.slice(0, 10);
						}),
						cum = this.computedReturn(indexReturn.value.slice());
					let curDate = date.shift(),
						preCum = null,
						curCum = cum.shift();
					dateDayList.forEach((item) => {
						if (item == curDate) {
							indexData.push(Number(curCum * 100).toFixed(2));
							preCum = curCum;
							curDate = date.shift();
							curCum = cum.shift();
						} else if (item < curDate) {
							indexData.push(Number(preCum * 100).toFixed(2));
						} else if (item > indexReturn.date[indexReturn.date.length - 1]) {
							indexData.push(null);
						}
					});
				}
				let series = [
					// {
					// 	name: 'A股股票',
					// 	type: 'bar',
					// 	stack: '总量',
					// 	barCategoryGap: '30%',
					// 	data: equity_weight
					// },
					{
						name: 'abs',
						type: 'bar',
						stack: '总量',
						barCategoryGap: '30%',
						data: abs_weight
					},
					{
						name: '可转债',
						type: 'bar',
						barCategoryGap: '30%',
						stack: '总量',
						data: cbond_weight
					},
					{
						name: '中期票据',
						type: 'bar',
						barCategoryGap: '30%',
						stack: '总量',
						data: bond_weight
					},
					{
						name: '企业债',
						type: 'bar',
						barCategoryGap: '30%',
						stack: '总量',
						data: bond_weight_1
					},
					{
						name: '其他债券',
						type: 'bar',
						barCategoryGap: '30%',
						stack: '总量',
						data: bond_weight_2
					},
					{
						name: '买入返售金融资产',
						type: 'bar',
						barCategoryGap: '30%',
						stack: '总量',
						data: buy_sell_all
					},
					{
						name: '同业存单',
						type: 'bar',
						barCategoryGap: '30%',
						stack: '总量',
						data: cundan
					},
					{
						name: '国债',
						type: 'bar',
						barCategoryGap: '30%',
						stack: '总量',
						data: guozhai
					},
					{
						name: '央行票据',
						type: 'bar',
						barCategoryGap: '30%',
						stack: '总量',
						data: yhpaioju
					},
					{
						name: '现金存款',
						type: 'bar',
						barCategoryGap: '30%',
						stack: '总量',
						data: cash_weight
					},
					{
						name: '短期融资券',
						type: 'bar',
						barCategoryGap: '30%',
						stack: '总量',
						data: short_fund
					},
					{
						name: '其他资产',
						type: 'bar',
						barCategoryGap: '30%',
						stack: '总量',
						data: other_weight
					},
					{
						name: '指数',
						type: 'line',
						symbol: 'none',
						data: indexData,
						xAxisIndex: 1,
						yAxisIndex: 1
					}
				];
				let legend = [
					'A股股票',
					'abs',
					'可转债',
					'中期票据',
					'企业债',
					'其他债券',
					'买入返售金融资产',
					'同业存单',
					'国债',
					'央行票据',
					'现金存款',
					'短期融资券',
					'金融债',
					'其他资产'
				];
				let xAxis = tradstyleData.yearqtr;
				this.$refs['barChartComponent'].getData({ series, legend, xAxis, dateDayList });
			}
		},
		// 监听指数变化
		onChangeIndex() {
			this.loading = true;
			this.postData.index_codes = [this.indexType];
			this.$emit('resolveFather', this.postData);
		},
		hideLoading() {
			this.loading = false;
		},
		createPrintWord() {
			let height = this.$refs['barChartComponent'].$el.clientHeight;
			let width = this.$refs['barChartComponent'].$el.clientWidth;
			let chart = this.$refs['barChartComponent'].createPrintWord();
			return [...exportTitle('大类资产配置'), ...exportChart(chart, { width, height })];
		}
	}
};
</script>

<style scoped>
.hold-statistic-header {
	display: flex;
	justify-content: space-between;
}
</style>
