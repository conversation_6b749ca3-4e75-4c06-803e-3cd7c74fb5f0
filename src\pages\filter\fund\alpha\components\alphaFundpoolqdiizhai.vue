<template>
  <div class="homebodyfontsize">
    <tempbasket ref="tempbasketfund"
                type="fund"></tempbasket>
    <div>
      <div style="padding-bottom: 20px; margin-top: 10px; background: white">
        <div class="title"
             style="display: flex">
          <div class="pointssearch"></div>
          QDII债券
        </div>
        <div v-loading="loadddddd">
          <el-table :default-sort="{ prop: 'code' }"
                    :data="tableData"
                    :cell-style="elcellstyle"
                    class="table"
                    ref="multipleTable"
                    header-cell-class-name="table-header">
            <el-table-column :width="getfontSize(320)"
                             prop="name"
                             align="gotoleft"
                             label="基金名称">
              <template slot-scope="scope"><a style="border-bottom: 1px solid #4096ff"
                   @click="godetail(scope.row.code, scope.row.name)">{{
									scope.row.name | isDefault
								}}</a></template>
            </el-table-column>
            <el-table-column sortable
                             prop="code"
                             label="基金代码"
                             align="gotoleft"> </el-table-column>
            <!-- <el-table-column sortable prop="date" label="最新净值" align="gotoleft">
                </el-table-column> -->
            <el-table-column prop="1w"
                             sortable
                             label="近一周收益"
                             align="gotoleft">
              <template slot-scope="scope">{{ scope.row['1w'] | fix2p }}</template>
            </el-table-column>
            <el-table-column prop="1m"
                             sortable
                             label="近一月收益"
                             align="gotoleft">
              <template slot-scope="scope">{{ scope.row['1m'] | fix2p }}</template>
            </el-table-column>
            <el-table-column prop="1q"
                             sortable
                             label="近一季收益"
                             align="gotoleft">
              <template slot-scope="scope">{{ scope.row['1q'] | fix2p }}</template>
            </el-table-column>
            <el-table-column prop="1y"
                             sortable
                             label="近一年收益"
                             align="gotoleft">
              <template slot-scope="scope">{{ scope.row['1y'] | fix2p }}</template>
            </el-table-column>
            <el-table-column prop="manager"
                             label="基金经理"
                             align="gotoleft">
              <template slot-scope="scope">{{ scope.row.manager | isDefault }}</template>
            </el-table-column>
            <el-table-column prop="found_date"
                             sortable
                             label="起始时间"
                             align="gotoleft">
              <template slot-scope="scope">{{ scope.row.found_date }}</template>
            </el-table-column>
            <!-- <el-table-column label="查看详情" width="100" align="gotoleft">
                    <template slot-scope="scope"><div @click="godetail(scope.row.code,scope.row.name)"><i  class="el-icon-tickets icon_color"></i></div></template>  
                </el-table-column> -->
            <el-table-column label="关注"
                             width="100"
                             align="gotoleft">
              <template slot-scope="scope">
                <div @click="addpool(scope.row.code, scope.row.name)"><i class="el-icon-circle-plus icon_color"></i></div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <el-dialog title="选择添加的基金池"
               :visible.sync="addfundvis"
               width="20%"
               destroy-on-close>
      基金代码:<br /><el-input type="text"
                :disabled="true"
                :value="choosefundid"
                label="基金代码"></el-input> 基金名称:<br /><el-input type="text"
                :disabled="true"
                :value="choosefundname"
                label="基金名称"></el-input>
      基金池：<br /><el-select style="width: 100%"
                 v-model="choosedpool"
                 placeholder="请选择您的基金池">
        <el-option v-for="item in options"
                   :key="item.value"
                   :label="item.label"
                   :value="item.value"> </el-option>
      </el-select>
      <br />理由:<br /><el-input type="textarea"
                v-model="choosereason"
                label="选择的理由"></el-input>
      <span slot="footer"
            class="dialog-footer">
        <el-button type="primary"
                   @click="saveEdit(form)">提 交</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { alphaGo } from '@/assets/js/alpha_type.js';
import tempbasket from './components/tempbasket';
import { fontSize } from '@/assets/js/echartsrpxtorem'; //注意路径
import axios from '@/api/index.js';
export default {
  components: { tempbasket },
  props: [],
  filters: {
    fix2p (value) {
      return (value * 100).toFixed(2) + '%';
    },
    isDefault (value) {
      return value == '--' ? '' : value;
    }
  },
  data () {
    return {
      loadddddd: true,
      hangyeshow: false,
      fenggeshow: false,
      kuanjishow: true,
      choosedpool: null,
      choosereason: null,
      choosefundid: null,
      choosefundname: null,
      options: [
        {
          value: '选项1',
          label: '池子1'
        },
        {
          value: '选项2',
          label: '池子2'
        },
        {
          value: '选项3',
          label: '池子3'
        },
        {
          value: '选项4',
          label: '池子4'
        },
        {
          value: '选项5',
          label: '池子5'
        }
      ],
      addfundvis: false,
      tableData: [],
      fundname: null,
      formData: {
        field101: '宽基',
        field102: undefined,
        field103: undefined,
        field104: undefined,
        field105: undefined,
        field106: undefined
      },
      field101Options: [
        {
          label: '宽基',
          value: '宽基'
        },
        {
          label: '行业',
          value: '行业'
        },
        {
          label: '风格',
          value: '风格'
        }
      ],
      field102Options: [
        {
          label: '沪深300',
          value: '沪深300'
        },
        {
          label: '中证500',
          value: '中证500'
        },
        {
          label: '中证800',
          value: '中证800'
        },
        {
          label: '中证1000',
          value: '中证1000'
        }
      ],
      field103Options: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ],
      field104Options: [
        {
          label: '从数据库里拉行业',
          value: 1
        },
        {
          label: '选项二',
          value: 2
        },
        {
          label: '选项二',
          value: 3
        },
        {
          label: '选项二',
          value: 4
        },
        {
          label: '选项二',
          value: 5
        },
        {
          label: '选项二',
          value: 6
        },
        {
          label: '选项二',
          value: 7
        },
        {
          label: '选项二',
          value: 8
        },
        {
          label: '选项二',
          value: 9
        },
        {
          label: '选项二',
          value: 10
        }
      ],
      field105Options: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ],
      field106Options: [
        {
          label: '从数据库里拉',
          value: 1
        },
        {
          label: '选项二',
          value: 2
        }
      ],
      is_request_active: false
    };
  },
  computed: {},
  watch: {},
  created () {
    let that = this;
    if (that.is_request_active) return;
    that.is_request_active = true;
    axios
      .get(this.$baseUrl + '/system/alpha/qd_bond_funds/')
      .then((res) => {
        that.is_request_active = false;
        this.$emit('overRequest');
        ////console.log(res.data)
        that.loadddddd = false;
        // that.alldata = res.data
        that.tableData = res.data.data.bond;
        // that.pageTotal = res.data.length
      })
      .catch((err) => {
        that.loadddddd = false;
        that.$message('筛选失败');
      });
  },
  mounted () { },
  methods: {
    getfontSize (val) {
      return fontSize(val);
    },
    elcellstyle ({ row, column, rowIndex, columnIndex }) {
      // ////console.log(row[0])

      if (columnIndex == 2) {
        if (row['1w'] >= 0) {
          return 'color: #E85D2D;';
        } else return 'color: #20995B;';
      }
      if (columnIndex == 3) {
        if (row['1m'] >= 0) {
          return 'color: #E85D2D;';
        } else return 'color: #20995B;';
      }
      if (columnIndex == 4) {
        if (row['1q'] >= 0) {
          return 'color: #E85D2D;';
        } else return 'color: #20995B;';
      }
      if (columnIndex == 5) {
        if (row['1y'] >= 0) {
          return 'color: #E85D2D;';
        } else return 'color: #20995B;';
      }
    },
    godetail (id, name) {
      //带参进去
      alphaGo(id, name, this.$route.path);
    },
    addpool (id, name) {
      let that = this;
      axios
        .post(that.$baseUrl + '/system/alpha/pool/basket_fund/', { fund_code: id })
        .then((res) => {
          that.$message('新增成功' + '  ' + id + ' ' + name);
        })
        .catch((err) => {
          //  that.$message('失败')
          ////console.log(err)
          //that.$message('数据缺失')
        });
    },
    submitForm () {
      // this.$refs['elForm'].validate(valid => {
      //   if (!valid) return
      //   // TODO 提交表单
      // })
      ////console.log(this.formData)
    },
    resetForm () {
      this.$refs['elForm'].resetFields();
    },
    handleSearch () {
      //搜索基金
    }
  }
};
</script>
<style>
/* input[readonly]{
background-color: #f1f1f5
} */
.el-form-item {
	margin-bottom: 0;
}
.homebodyfontsize .el-form-item__label {
	width: 200px !important;
	text-align: left;
}
.homebodyfontsize .el-form-item__content {
	margin-left: 200px;
}
</style>
<style lang="scss" scoped>
.managerDetailPage {
	margin-left: 2%;
	width: 96%;
	background: #d0d7df;
	padding: 20px;
}

.row {
	margin: -10px;
	display: flex;
}

.left {
	width: 155px;
	flex: 0 0 auto;
	margin-right: 10px;
}

.right {
	position: relative;
	flex: 1 1 100px;
}
</style>
<style lang="scss">
.comment-section {
	padding: 5px 15px 0 15px;
}

.comment {
	background: linear-gradient(90deg, #3b64f2, #1b8eff);
	color: white;
	font-size: 12px;
	padding: 12px 24px;
}

.comment.center {
	text-align: center;
}

.section {
	padding: 15px 15px 0 15px;
}

.double-table {
	display: flex;
	flex-basis: 10px;
	justify-content: space-between;

	.single-table {
		flex: 1;
	}

	.cell {
		font-size: 14px !important;
		font-weight: 400 !important;
		text-align: center !important;
		padding: 0 !important;
	}

	th {
		padding: 5px 0 !important;
	}
}

.split-cell {
	display: flex;
	align-items: center;
	justify-content: center;

	div {
		width: 40px;
	}
}
</style>
<style lang="scss" scoped>
.title {
	width: 99%;
	font-weight: 600;
	padding: 10px 15px;
}
.sub-title {
	font-size: 14px;
	font-weight: 600;
	border-left: 2px solid dodgerblue;
	margin-bottom: 6px;
	padding-left: 3px;
	line-height: 22px;
	height: 22px;
	flex: 1 1 auto;
}
.title-change-fund {
	display: flex;
	align-items: center;
	margin-bottom: 4px;
	label {
		font-size: 14px;
		margin-right: 10px;
	}
}
</style>
