<template>
	<div v-loading="loading">
		<!-- <v-chart
				ref="companySizeChange"
				:options="option"
				element-loading-text="暂无数据"
				element-loading-spinner="el-icon-document-delete"
				element-loading-background="rgba(239, 239, 239, 0.5)"
				class="charts_one_class"
				autoresize
			></v-chart> -->
		<div id="industryConfigBar" style="width: 100%; height: 120px"></div>
		<div id="industryConfigPerformance" style="width: 100%; height: 720px"></div>
	</div>
</template>

<script>
import VChart from 'vue-echarts';

import { triangleCircleOption } from '@/utils/chartStyle.js';
// import { getTimeingInfo } from '@/api/pages/tkAnalysis/captial-market.js'
export default {
	components: { VChart },
	data() {
		return {
			option: {},
			loading: true,
			industries: [],
			equityWeght: []
		};
	},
	methods: {
		formatterData(data) {
			return data.map((item) => item.weight * 1);
		},
		getSame(date, data) {
			let dataList = [];
			date.forEach((day) => {
				let sum = 0;
				data.forEach((item) => {
					if (item.yearqtr == day) {
						sum += item.weight;
					}
				});
				dataList.push({
					name: day,
					value: sum
				});
			});
			return dataList;
		},
		async getData(data) {
			this.allocationBar = {};
			this.loading = false;
			this.$nextTick(() => {
				this.cardLoading = false;
				let maxtemp = 27;
				let scale = [];
				let date = [];
				// let list = [];
				let that = this;
				// this.industries.map((item) => list.push({ text: item, value: item }));
				// this.industryNameList = list;
				let requestData = data.map((item) => {
					item.weight = item.weight * 100;
					item.bogeyReturn = item.bogeyReturn * 100;
					item.industryReturn = item.industryReturn * 100;

					return {
						...item
					};
				});
				requestData = requestData.sort((a, b) => {
					return this.moment(a.yearqtr, 'YYYY Q')._d.getTime() - this.moment(b.yearqtr, 'YYYY Q')._d.getTime();
				});
				this.industries = [];
				for (let i = 0; i < requestData.length; i++) {
					if (this.industries.indexOf(requestData[i].industryName) < 0) {
						this.industries.push(requestData[i].industryName);
					}
				}
				maxtemp = this.industries.length - 1;
				let list = [];
				this.industries.map((item) => list.push({ text: item, value: item }));
				this.industryNameList = list;
				this.tempvalueselect = this.industries[0];
				// }

				let maxp = 0;
				for (let c = 0; c < requestData.length; c++) {
					if (maxp < requestData[c].weight) {
						maxp = requestData[c].weight * 1;
					}
				}
				// 处理表格数据-tableDataArr; tableDataObj
				let tableDataArr = [],
					tableDataObj = {};
				requestData.forEach((item) => {
					if (!tableDataObj[item.industryName]) {
						tableDataObj[item.industryName] = [];
					}
					tableDataObj[item.industryName].push(item);
				});

				for (let key in tableDataObj) {
					tableDataArr.push(...tableDataObj[key]);
				}
				this.industryList = tableDataArr.map((item) => {
					return {
						...item,
						industry_index_return: item.bogeyReturn ? item.bogeyReturn : item.industryReturn,
						industry_excess_return: item.excess_return ? item.excess_return : item.industryReturn * 1 - item.bogeyReturn * 1
					};
				});
				// this.$refs['industryExcessTable']?.getData({
				// 	industryNameList: this.industryNameList,
				// 	industryList: this.industryList
				// });

				// 处理图表数据-data
				let tableData = [];
				let whereend = null;
				let industries = this.industries.slice();
				for (let i = 0; i < requestData.length; i++) {
					let temp = industries.indexOf(requestData[i].industryName);
					tableData.push([
						requestData[i].yearqtr,
						temp,
						requestData[i].industryReturn * 1,
						requestData[i].weight * 1,
						requestData[i].industryReturn * 1 - 0 > 0 ? 'True' : 'False'
					]);
					if (date.indexOf(requestData[i].yearqtr) == -1) {
						date.push(requestData[i].yearqtr);
					}
				}
				date = date.sort((a, b) => {
					return this.moment(a, 'YYYY Q')._d.getTime() - this.moment(b, 'YYYY Q')._d.getTime();
				});
				this.equityWeght = this.getSame(date, requestData);
				scale = this.formatterData(requestData);

				this.requestData = requestData;
				let options = {
					xAxis: {
						margin: 12,
						nameTextStyle: {
							fontSize: '14px',
							color: 'rgba(0,0,0,0.65)'
						},
						axisLabel: {
							show: true,
							textStyle: {
								fontSize: '14px',
								color: 'rgba(0,0,0,0.65)'
							}
						},
						axisLine: {
							lineStyle: {
								color: '#e9e9e9'
							}
						},
						type: 'category',
						boundaryGap: false,
						splitLine: {
							show: false,
							lineStyle: {
								color: '#e9e9e9',
								type: 'dashed',
								width: 1
							}
						},
						axisTick: {
							show: false
						},
						data: date,
						interval: 0.25,
						length: date.length
					},
					dataZoom: [
						{
							// 这个dataZoom组件，默认控制x轴。
							type: 'slider', // 这个 dataZoom 组件是 slider 型 dataZoom 组件
							start: whereend, // 左边在 10% 的位置。
							end: 100, // 右边在 60% 的位置。
							show: true,
							filterMode: 'empty'
						}
					],
					yAxis: {
						margin: 16,
						nameTextStyle: {
							fontSize: '14px',
							color: '#4096ff'
						},
						axisTick: {
							show: false
						},
						type: 'value',
						axisLine: {
							show: false,
							color: '#e9e9e9'
						},
						splitLine: {
							show: true,
							lineStyle: {
								color: '#e9e9e9',
								type: 'dashed',
								width: 1
							}
						},
						splitArea: {
							show: false,
							areaStyle: {
								color: '#f5f7fa'
							}
						},
						min: 0,
						max: maxtemp,
						interval: 1,
						axisLabel: {
							show: true,
							textStyle: {
								fontSize: '14px',
								color: '#4096ff'
							},
							formatter: function (value, index) {
								if (value < that.industries.length) return that.industries[value].toString();
							}
						}
					},
					tooltip: {
						textStyle: {
							fontSize: '14px'
						},
						trigger: 'item',
						axisPointer: {
							type: 'cross'
						},
						formatter(params) {
							if (
								that.serIndex !==
								date
									.map((item) => {
										item = JSON.stringify(item);
										return item.slice(0, 4) + ' Q' + item.slice(-1);
									})
									.indexOf(params.name)
							) {
								that.serIndex = date
									.map((item) => {
										item = JSON.stringify(item);
										return item.slice(0, 4) + ' Q' + item.slice(-1);
									})
									.indexOf(params.name);
								// that.barScattar(echartBar);
							}

							return (
								'日期：' +
								params.data[0] +
								'行业：' +
								industries[params.data[1]] +
								'，权重：' +
								params.data[3].toFixed(2) +
								'%，行业估算收益率：' +
								params.data[2].toFixed(2) +
								'%'
							);
						}
					},
					grid: {
						left: '64px',
						right: '32px',
						bottom: '64px',
						top: '18px',
						// containLabel: true,
						backgroundColor: '#fafafa'
					},
					visualMap: [
						{
							show: false,
							type: 'piecewise', // 定义为分段型 visualMap
							right: '0',
							bottom: '20px',
							dimension: 4,
							splitNumber: 2,
							precision: 1,
							itemWidth: 10,
							itemHeight: 10,
							textGap: 5,
							textStyle: {
								color: 'black'
							},
							// categories 定义了 visualMap-piecewise 组件显示出来的项。
							categories: ['True', 'False'],
							// 表示 目标系列 的视觉样式 和 visualMap-piecewise 共有的视觉样式。
							inRange: {
								symbol: ['triangle', 'circle']
							},
							// 表示 visualMap-piecewise 本身的视觉样式。
							controller: {
								inRange: {
									color: ['black', 'black'],
									symbol: ['triangle', 'circle']
								}
							}
						},
						{
							show: false,
							right: '0px',
							top: '150px',
							dimension: 3,
							min: 0,
							max: maxp,
							itemWidth: 30,
							itemHeight: 120,
							precision: 0,
							text: ['配置权重%'],
							textGap: 5,
							textStyle: {
								color: 'black',
								fontSize: 10
							},
							inRange: {
								symbolSize: [6, 24]
							},
							controller: {
								inRange: {
									color: ['black']
								}
							}
						},
						{
							show: false,
							right: '5px',
							top: '5%',
							dimension: 2,
							min: -1.0,
							max: 1.0,
							itemHeight: 100,
							precision: 1,
							text: ['1', '-1.0'],
							textGap: 0,
							textStyle: {
								color: 'black',
								fontSize: 10
							},
							inRange: {
								color: ['green', '#A3A3A3', 'red']
								// color: [
								// 	'rgba(35, 195, 67, 1)',
								// 	'rgba(35, 195, 67, 0.8)',
								// 	'rgba(35, 195, 67, 0.6)',
								// 	'rgba(35, 195, 67, 0.4)',
								// 	'rgba(247, 101, 96, 1)',
								// 	'rgba(247, 101, 96, 0.8)',
								// 	'rgba(247, 101, 96, 0.6)',
								// 	'rgba(247, 101, 96, 0.4)'
								// ]
							},
							outOfRange: {
								color: ['rgba(255,255,255,.2)']
							},
							controller: {
								inRange: {
									// color: ['#23C343', '#23C343CC', '#23C34399', '#23C34366', '#F7656066', '#F7656099', '#F76560CC', '#F76560']
									color: ['green', '#A3A3A3', 'red']
								}
							}
						}
					],
					series: [
						{
							name: '大类资产配置',
							type: 'scatter',
							data: tableData,
							symbolSize: 5
						}
					]
				};
				this.allocation = options;
				this.allocationBar = {
					xAxis: {
						show: false,
						margin: 12,
						nameTextStyle: {
							fontSize: '14px'
						},
						axisLabel: {
							show: false,
							textStyle: {
								fontSize: '14px'
							}
						},
						type: 'category',
						boundaryGap: true,
						splitLine: {
							show: false,
							lineStyle: {
								color: 'white',
								type: 'solid',
								width: 1
							}
						},
						data: this.equityWeght.map((item) => {
							return item.name;
						}),
						interval: 0.25,
						length: this.equityWeght.length
					},
					yAxis: {
						show: true,
						margin: 16,
						nameTextStyle: {
							fontSize: '14px'
						},
						type: 'value',
						axisLine: {
							show: false
						},
						axisTick: {
							show: false
						},
						splitLine: {
							show: true,
							lineStyle: {
								color: 'white',
								type: 'solid',
								width: 1
							}
						}
						// splitArea: {
						//   show: true,
						//   areaStyle: {
						//     color: "#f5f7fa"
						//   }
						// },
						// axisLabel: {
						//   show: true,
						//   textStyle: {
						//     fontSize: "14px"
						//   },
						//   formatter: function(value, index) {
						//     return "非银金融";
						//   }
						// }
					},
					dataZoom: [
						{
							// 这个dataZoom组件，默认控制x轴。
							type: 'slider', // 这个 dataZoom 组件是 slider 型 dataZoom 组件
							start: whereend, // 左边在 10% 的位置。
							end: 100, // 右边在 60% 的位置。
							show: false
						}
					],
					tooltip: {
						trigger: 'axis',
						textStyle: {
							fontSize: '14px'
						},
						axisPointer: {
							type: 'shadow'
						},
						formatter: function (params) {
							return `时间：${params[0].name} <br/> 总权重：${params[0].value}%`;
						}
					},
					grid: {
						show: true,
						left: '64px',
						right: '32px',
						bottom: '-100px',
						top: '20px',
						height: 50,
						borderWidth: 0,
						// containLabel: true,
						backgroundColor: '#ecf5ff'
					},
					series: [
						{
							name: '配置总额',
							data: that.equityWeght.map((item) => {
								return Number(item.value).toFixed(2);
							}),
							type: 'bar',
							itemStyle: {
								color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
									{
										//只要修改前四个参数就ok
										offset: 1,
										color: 'rgba(255, 171, 62, 1)'
									}, //柱图渐变色
									{
										offset: 0,
										color: 'rgba(255, 210, 152, 1)'
									}
								]),
								barBorderRadius: [20, 20, 0, 0]
							},
							barWidth: '10px',
							emphasis: {
								itemStyle: {
									color: '#4096ff'
								},
								label: {
									color: 'rgba(0, 0, 0, 0.65)',
									textStyle: {
										fontSize: '14px'
									}
								}
							},
							label: {
								show: false,
								position: 'top'
							}
						}
					]
				};
				let echartScatter = echarts.init(document.getElementById('industryConfigPerformance'));
				let echartBar = echarts.init(document.getElementById('industryConfigBar'));
				this.echartBar = echartScatter;
				echartScatter.setOption(this.allocation);
				echartBar.setOption(this.allocationBar);
				echartScatter.resize();
				echartBar.resize();
				window.addEventListener('resize', function () {
					echartScatter.resize();
					echartBar.resize();
				});
				echartScatter.on('datazoom', function (params) {
					this.allocationBar = {
						xAxis: {
							margin: 12,
							nameTextStyle: {
								fontSize: '14px'
							},
							axisLabel: {
								show: true,
								textStyle: {
									fontSize: '14px'
								}
							},
							type: 'category',
							boundaryGap: true,
							splitLine: {
								show: true,
								lineStyle: {
									color: 'white',
									type: 'solid',
									width: 1
								}
							},
							data: date,
							interval: 0.25,
							length: date.length
						},
						yAxis: {
							margin: 16,
							show: true,
							nameTextStyle: {
								fontSize: '14px'
							},

							type: 'value',
							axisLine: {
								show: false
							},
							axisTick: {
								show: false
							},
							splitLine: {
								show: true,
								lineStyle: {
									color: 'white',
									type: 'solid',
									width: 1
								}
							},
							// splitArea: {
							//   show: true,
							//   areaStyle: {
							//     color: "#4096ff"
							//   }
							// },
							min: 0,
							max: maxtemp,
							interval: 1
							// axisLabel: {
							//   show: true,
							//   textStyle: {
							//     fontSize: "14px"
							//   },
							//   formatter: function(value, index) {
							//     return "非银金融";
							//   }
							// }
						},
						dataZoom: [
							{
								// 这个dataZoom组件，默认控制x轴。
								type: 'slider', // 这个 dataZoom 组件是 slider 型 dataZoom 组件
								start: params.start, // 左边在 10% 的位置。
								end: params.end, // 右边在 60% 的位置。
								show: false
							}
						],
						tooltip: {
							trigger: 'axis',
							axisPointer: {
								type: 'shadow'
							},
							formatter: function (params) {
								return `时间：${params[0].name} <br/> 总权重：${params[0].value}%`;
							}
						},
						grid: {
							show: true,
							left: '64px',
							right: '32px',
							bottom: '-100px',
							top: '20px',
							height: 50,
							borderWidth: 0,
							// containLabel: true,
							backgroundColor: '#ecf5ff'
						},
						series: [
							{
								name: '配置总额',
								data: that.equityWeght.map((item) => {
									return {
										value: (Number(item.value) * 100).toFixed(2),
										symbolSize: Number(item.value).toFixed(2)
									};
								}),
								type: 'bar',
								itemStyle: {
									color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
										{
											//只要修改前四个参数就ok
											offset: 1,
											color: 'rgba(255, 210, 152, 1)'
										}, //柱图渐变色
										{
											offset: 0,
											color: 'rgba(255, 171, 62, 1)'
										}
									]),
									barBorderRadius: [20, 20, 0, 0]
								},
								emphasis: {
									itemStyle: {
										color: '#4096ff'
									},
									label: {
										color: 'rgba(0, 0, 0, 0.65)'
									}
								},
								label: {
									show: false,
									position: 'top'
								}
							}
						]
					};
				});
				echarts.connect([echartBar, echartScatter]);
			});
		},
		filterData(data) {
			const date_list = data.map((item) => item.date);
			const data1 = data.map((item) => item.exponentNav);
			const data2 = data.map((item) => item.stockValue);
			return { date_list, data1, data2 };
		}
	}
};
</script>

<style scoped>
.chart_one {
	padding: 0;
	box-shadow: none;
}
</style>
