<template>
	<div>
		<el-dialog :title="title" :visible.sync="dialogVisible">
			<div>
				<div class="flex_start mb-24">
					<div class="flex_start mr-16">
						<span style="color: red" class="mr-4">*</span>
						<div style="width: 70px">情景名称</div>
						<div>
							<el-input v-model="name" placeholder="请输入情景名称"></el-input>
						</div>
					</div>
					<div class="mr-16">
						<span style="color: red" class="mr-4">*</span>
						<span>配色</span>
						<el-tooltip class="item" effect="dark" content="主要用于该情景在时间轴图表里展示时进行颜色区分。" placement="right-start">
							<svg width="14" height="14" viewBox="0 0 14 14" fill="none">
								<path
									fill-rule="evenodd"
									clip-rule="evenodd"
									d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
									fill="black"
									fill-opacity="0.45"
								/></svg
						></el-tooltip>
					</div>
					<colorPicker v-model="color" />
				</div>
				<div class="flex_start mb-24">
					<div class="flex_start">
						<span style="color: red" class="mr-4">*</span>
						<div style="width: 70px">时间段</div>
						<div>
							<el-date-picker v-model="value" type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期">
							</el-date-picker>
						</div>
					</div>
				</div>
				<div>
					<div class="mb-8">
						<span style="color: red" class="mr-4">*</span>
						<span>情景描述</span>
					</div>
					<div class="ml-8">
						<el-input type="textarea" rows="8" v-model="description" placeholder="请输入对于自定义情景的描述"></el-input>
					</div>
				</div>
			</div>
			<div slot="footer">
				<el-button @click="dialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="submit">确 定</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import vueColorPicker from 'vcolorpicker';
export default {
	components: { vueColorPicker },
	data() {
		return {
			dialogVisible: false,
			name: '',
			value: '',
			description: '',
			color: '#fba451',
			id: ''
		};
	},
	computed: {
		title() {
			return '创建情景';
		}
	},
	methods: {
		getData(data) {
			if (data) {
				this.id = data?.id;
				this.name = data.name;
				this.value = data.date_list;
				this.description = data.description;
				this.color = data.color;
			} else {
				this.id = '';
				this.name = '';
				this.value = '';
				this.description = '';
				this.color = '#fba451';
			}
			this.dialogVisible = true;
		},
		submit() {
			let postData = {
				id: this.id,
				color: this.color,
				description: this.description,
				startDate: this.value?.[0],
				endDate: this.value?.[1],
				name: this.name,
				publicType: 0,
				userId: this.localStorage.getItem('id')
			};
			this.dialogVisible = false;
			this.$emit('resolveFather', postData);
		}
	}
};
</script>

<style></style>
