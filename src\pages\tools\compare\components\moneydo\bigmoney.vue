<!--  -->
<template>
  <div class="holdstock">
    <div style="display: flex; align-items: center; width: 100%; position: relative; justify-content: space-between">
      <div style="display: flex; align-items: center">
        <div class="TitltCompare">大类资产配置</div>
      </div>
      <div style="text-align: right">
        <div style="margin-top: 10px">
          <el-select @change="change()"
                     v-model="value"
                     placeholder="请选择比较资产种类">
            <el-option v-for="item in options"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"> </el-option>
          </el-select>
        </div>
      </div>
    </div>
    <div v-loading="loading"
         style="page-break-inside: avoid; margin-top: 16px">
      <v-chart ref="bigmoney"
               v-loading="empty2"
               autoresize
               element-loading-text="暂无数据"
               element-loading-spinner="el-icon-document-delete"
               element-loading-background="rgba(239, 239, 239, 0.5)"
               style="page-break-inside: avoid; width: 100%; height: 400px"
               :options="optionpbroe2"></v-chart>
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { FundAllocationDetails } from '@/api/pages/tools/compare.js';
import VCharts from 'vue-echarts';
export default {
  props: {
    comparetype: {
      type: String,
      default: 'manager' //fund
    },
    id: {
      type: String,
      default: '30189741,30441407'
    },
    type: {
      type: String,
      default: 'equity'
    },
    name: {
      type: String,
      default: '萧楠,胡昕炜'
    }
  },
  //import引入的组件需要注入到对象中才能使用
  components: {
    'v-chart': VCharts
  },
  data () {
    //这里存放数据
    return {
      loading: false,
      empty2: false,
      showdetailchoose: false,
      options2: [
        {
          value: '采掘',
          label: '采掘'
        },
        {
          value: '化工',
          name: '化工'
        },
        {
          value: '钢铁',
          name: '钢铁'
        },
        {
          value: '有色金属',
          name: '有色金属'
        },
        {
          value: '建筑材料',
          name: '建筑材料'
        },
        {
          value: '建筑装饰',
          name: '建筑装饰'
        },
        {
          value: '电气设备',
          name: '电气设备'
        },
        {
          value: '机械设备',
          name: '机械设备'
        },
        {
          value: '国防军工',
          name: '国防军工'
        },
        {
          value: '汽车',
          name: '汽车'
        },
        {
          value: '家用电器',
          name: '家用电器'
        },
        {
          value: '纺织服装',
          name: '纺织服装'
        },
        {
          value: '轻工制造',
          name: '轻工制造'
        },
        {
          value: '商业贸易',
          name: '商业贸易'
        },
        {
          value: '农林牧渔',
          name: '农林牧渔'
        },
        {
          value: ' 食品饮料',
          name: ' 食品饮料'
        },
        {
          value: '休闲服务',
          name: '休闲服务'
        },
        {
          value: '医药生物',
          name: '医药生物'
        },
        {
          value: '公用事业',
          name: '公用事业'
        },
        {
          value: '交通运输',
          name: '交通运输'
        },
        {
          value: '房地产',
          name: '房地产'
        },
        {
          value: ' 电子',
          name: ' 电子'
        },
        {
          value: '计算机',
          name: '计算机'
        },
        {
          value: '传媒',
          name: '传媒'
        },
        {
          value: '通信',
          name: '通信'
        },
        {
          value: '银行',
          name: '银行'
        },
        {
          value: '非银金融',
          name: '非银金融'
        },
        {
          value: '综合',
          name: '综合'
        }
      ],
      options: [
        { value: 'equity', label: '股票' },
        { value: 'abs', label: 'abs' },
        { value: 'bond', label: '债券' },
        { value: 'cash', label: '货币' },
        { value: 'fund', label: '基金' },
        { value: 'other', label: '其他' }
      ],
      value: 'bond',
      optionpbroe2: {}
    };
  },
  filters: {
    fix3 (value) {
      if (value == '--' || value == null || value == '') {
        return value;
      } else {
        return (value * 100).toFixed(2) + '%';
      }
    },
    fix2 (value) {
      return Number(value).toFixed(2) + '亿';
    }
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    change (e) {
      // console.log(this.value);
      this.getdata(1);
    },
    getdata (e) {
      console.log(this.comparetype, 'ssssssssss');
      if (e != 1) {
        Object.assign(this.$data, this.$options.data());

      }
      this.loading = true;
      this.empty2 = false;
      if (this.comparetype == 'manager') {
        // this.getmanager()
      } else {
        this.gefunddata();
      }
    },
    async getmanager () {
      let data = await ManagerHoldRecent1y({ manager_code: this.id, type: this.type, manager_name: this.name });
      if (data) {
        // //console.log('chigu1y')
        // //console.log(data)
        this.yearqtrlist = [];
        if (data.data.length > 0) {
          for (let i = 0; i < data.data[0].length; i++) {
            if (this.yearqtrlist.indexOf(data.data[0][i].yearqtr) < 0) {
              {
                this.yearqtrlist.push(data.data[0][i].yearqtr);
              }
            }
          }
        }
        this.datatable = data.data;
        this.stock_holdcolumns = [];
        for (let i = 0; i < this.datatable.length; i++) {
          if (this.datatable[i].length > 0) {
            this.stock_holdcolumns.push({
              title: this.datatable[i][0].manager_name,
              children: [
                {
                  dataIndex: 'name' + i,
                  key: 'Name' + i,
                  title: '债券名称',
                  scopedSlots: {
                    customRender: 'Name' + i
                  }
                },
                {
                  dataIndex: 'weight' + i,
                  key: 'Weight' + i,
                  title: '权重',
                  sorter: (a, b) => a['weight' + i] - b['weight' + i],
                  scopedSlots: {
                    customRender: 'Weight' + i
                  }
                }
              ]
            });
          }
        }
        let max = this.yearqtrlist[0];
        for (let i = 0; i < this.yearqtrlist.length - 1; i++) {
          max = max < this.yearqtrlist[i + 1] ? yearqtrlist[i + 1] : max;
        }
        this.changgeyearqtr(max);
      }
    },
    async gefunddata () {
      console.log("object");
      let data = await FundAllocationDetails({
        fund_code: this.id,
        type: this.type,
        fund_name: this.name,
        name: this.value
      });
      this.loading = false;

      if (data) {
        if (JSON.stringify(data.data) == '{}' || JSON.stringify(data.data) == '[]' || JSON.stringify(data.data) == '') {
          this.empty2 = true;
        } else {
          // //console.log('chigu1y')
          // //console.log(data)
          let temn = '';
          for (let i = 0; i < this.options.length; i++) {
            if (this.options[i].value == this.value) temn = this.options[i].label;
          }
          let datelist = [];
          let seriess = [];
          for (let i = 0; i < data.data.length; i++) {
            data.data.sort((a, b) => {
              if (this.$route.query.name.split(',').indexOf(a.name) > this.$route.query.name.split(',').indexOf(b.name)) return 1;
              else return -1;
            });
            if (data.data[i].name == 'yearqtr') {
              datelist = data.data[i].value;
            } else {
              seriess.push({
                name: data.data[i].name,
                type: 'bar',
                data: data.data[i].value
              });
            }
          }

          this.optionpbroe2 = {
            title: {
              text: temn + '大类资产配置'
            },
            color: ['#4096ff', '#4096ff', '#7388A9', '#6F80DD', '#4096FF', '#929694', '#f4d1ff', '#e91e63', '#64dd17'],
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                // 坐标轴指示器，坐标轴触发有效
                type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
              },
              textStyle: {
                fontSize: 14
              },
              formatter: (params) => {
                //  //console.log(params)
                let str = `时间: ${params[0].axisValue} <br />`;
                for (let i = params.length - 1; i >= 0; i--) {
                  let dotHtml =
                    '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' +
                    params[i].color +
                    '"></span>';
                  str += dotHtml + `${params[i].seriesName}: ${Number(params[i].value).toFixed(2) + '%'}<br />`;
                }
                return str;
              }
            },
            dataZoom: [
              {
                type: 'slider',
                show: true,
                height: 14,
                bottom: 10,
                borderColor: 'transparent',
                backgroundColor: '#fafafa',
                // 拖拽手柄样式 svg 路径
                handleIcon:
                  'M512 512m-208 0a6.5 6.5 0 1 0 416 0 6.5 6.5 0 1 0-416 0Z M512 192C335.264 192 192 335.264 192 512c0 176.736 143.264 320 320 320s320-143.264 320-320C832 335.264 688.736 192 512 192zM512 800c-159.072 0-288-128.928-288-288 0-159.072 128.928-288 288-288s288 128.928 288 288C800 671.072 671.072 800 512 800z',
                handleColor: '#aab6c6',
                handleSize: 20,
                handleStyle: {
                  borderColor: '#aab6c6',
                  shadowBlur: 4,
                  shadowOffsetX: 1,
                  shadowOffsetY: 1,
                  shadowColor: '#e5e5e5'
                },
                start: 0,
                end: 100
              }
            ],
            legend: {},
            grid: {
              top: '12%',
              left: '10px',
              right: '4%',
              bottom: '10%',
              containLabel: true
            },

            xAxis: [
              {
                type: 'category',
                data: datelist,
                axisLabel: {
                  show: true,
                  textStyle: {
                    fontSize: 14
                  },
                  interval: 0,
                  rotate: 40
                }
              }
            ],
            yAxis: [
              {
                type: 'value',

                axisTick: {
                  show: false
                },
                splitLine: {
                  show: true,
                  lineStyle: {
                    type: 'dashed'
                  }
                },
                axisLabel: {
                  show: true,
                  textStyle: {
                    fontSize: 14
                  },
                  formatter (value) {
                    return value + '%';
                  }
                }
              }
            ],
            series: seriess
          };
        }
      }
    },
    createPrintWord () {
      let height = this.$refs['bigmoney']?.$el.clientHeight;
      let width = this.$refs['bigmoney']?.$el.clientWidth;
      let chart = this.$refs['bigmoney'].getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      });
      return [
        ...this.$exportWord.exportFirstTitle('资产配置'),
        ...this.$exportWord.exportTitle('大类资产配置'),
        ...this.$exportWord.exportChart(chart, { width, height })
      ];
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () { },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
</style>
