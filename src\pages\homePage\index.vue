<template>
	<div>
		<div class="bg">
			<div class="search">
				<div class="search_title">画像搜索</div>
				<div>
					<div class="searchfundormanager">
						<el-select
							style="margin-right: 12px"
							v-model="values"
							:remote-method="searchpeople"
							filterable
							remote
							prefix-icon="el-icon-search"
							:loading="loading"
							placeholder="输入简拼、代码、名称查询基金/经理/公司"
							ref="select"
							@hook:mounted="cancalReadOnly"
							@visible-change="cancalReadOnly"
						>
							<template slot="prefix">
								<div style="width: 24px; height: 100%; display: flex; justify-content: center; align-items: center; matgin-left: 13.1px">
									<i class="el-icon-search" style="color: #00000073"></i>
								</div>
							</template>
							<el-option-group v-for="groups in havefundmanager" :key="groups.label" :label="groups.label">
								<el-option
									v-for="(group, index) in groups.options"
									:key="group.code + ' ' + index"
									:label="
										group.flag == 'fund'
											? `${group.code}-${group.name}-${group.fundCo.split('基金')[0]}`
											: group.flag == 'manager'
											? `${group.name}-${group.fundCo.split('基金')[0]}`
											: group.flag == 'company'
											? group.name
											: `${group.name}-${group.code}`
									"
									:value="group.code + '|' + group.name + '|' + group.flag"
								>
								</el-option>
							</el-option-group>
						</el-select>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { alphaGo } from '@/assets/js/alpha_type.js';

import { Search } from '@/api/pages/Analysis.js';
export default {
	data() {
		return {
			loading: true,
			values: null,
			havefundmanager: null
		};
	},
	methods: {
		searchpeople(query) {
			this.havefundmanager = null;
			clearTimeout(this.timeout);
			this.timeout = setTimeout(() => {
				this.getResultsList(query);
			}, 500);
		},
		async getResultsList(query) {
			// axios
			//     .get(this.$baseUrl + '/Analysis/Search/?flag=1,2&message=' + query)
			// this.$api.compare.header_search_all({
			//     message: query,
			//     flag:'1,2'
			// })
			let data = await Search({
				message: query,
				flag: '1,2,3'
			});
			let temparr = [
				{
					label: '基金产品',
					options: []
				},
				{
					label: '基金经理',
					options: []
				},
				{
					label: '基金公司',
					options: []
				}
				// FIXME: 暂不开放股票入口
				// {
				// 	label: '股票',
				// 	options: []
				// }
			];
			for (let i = 0; i < data.data.length; i++) {
				if (data.data[i].flag === 'fund') {
					temparr[0].options.push(data.data[i]);
				} else if (data.data[i].flag == 'manager') {
					temparr[1].options.push(data.data[i]);
				} else if (data.data[i].flag == 'company') {
					temparr[2].options.push(data.data[i]);
				}
				// FIXME: 暂不开放股票入口
				// else if (res.data[i].flag == 'stock') {
				// 	temparr[3].options.push(res.data[i]);
				// }
			}
			this.havefundmanager = temparr;
			this.loading = false;
		},
		cancalReadOnly(value) {
			let plat = navigator.userAgent.match(
				// 判断不同端
				/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
			);
			if (plat) {
				this.$nextTick(() => {
					if (!value) {
						const { select } = this.$refs;
						const input = select.$el.querySelector('.el-input__inner');
						input.removeAttribute('readonly');
						// this.$refs.select.blur();  根据tip自行判断是否添加
					}
				});
			}
		}
	},
	watch: {
		values(val) {
			this.havefundmanager = null;
			//console.log('val', val)
			if (val == '') {
			} else {
				let id = val.split('|')[0];
				let name = val.split('|')[1];
				let flag = val.split('|')[2];

				// 若为基金公司或股票,不需要再请求接口,直接跳转即可
				if (flag == 'company') {
					this.$router.push({
						path: '/fundCompany/' + id,
						hash: '',
						query: {
							id: id,
							name: name
						}
					});
					this.values = '';
					return;
				} else if (flag == 'stock') {
					this.$router.push({
						path: '/equitychoose/' + id,
						hash: '',
						query: {
							id: id,
							name: name
						}
					});
					this.values = '';
					return;
				}
				console.log(id, name, this.$route.path);
				alphaGo(id, name, this.$route.path);
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.bg {
	position: relative;
	// padding-bottom: 0px !important;
	width: 100%;
	height: 560px;
	background: url('../../assets/img/fundbg.jpg');
	background-repeat: no-repeat;

	.search {
		// width: 802px;
		// height: 81px;
		//   background-color: white;
		/* 子容器开启绝对定位*/
		position: absolute;
		text-align: center;
		/* 水平垂直居中 */
		top: 30%;
		//   margin-top: -150px;
		left: 30%;

		//   margin-left: -150px;
		.search_title {
			color: #3f598c;
			font-family: Source Han Sans CN;
			font-size: 40px;
			font-style: normal;
			font-weight: 700;
			line-height: normal;
			margin-bottom: 20px;
		}
	}
	::v-deep .el-input__inner {
		width: 600px !important;
		border-radius: 50px !important;
		height: 55px !important;
		line-height: 36px !important;
	}
	::v-deep ::-webkit-input-placeholder {
		font-size: 16px !important;
	}
	::v-deep .el-input__inner::placeholder {
		padding-left: 15px;
	}
	::v-deep .el-input__inner {
		padding: 0 25px !important;
	}
	::v-deep .el-input__prefix,
	.el-input__suffix {
		width: 30px;
		height: 54px !important;
		/* background: #FFFFFF; */
		position: absolute;
		top: 1px !important;
		right: 0 !important;
		height: 100%;
		color: #d8dce6;
		text-align: center;
		border-radius: 0 4px 4px 0;
		display: flex;
		align-items: center;
		justify-content: center;
	}
}
</style>
