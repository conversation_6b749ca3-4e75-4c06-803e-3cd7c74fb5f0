<template>
	<div id="bondQuarterInfo">
		<analysis-card-title title="转债季度信息" image_id="bondQuarterInfo"></analysis-card-title>
		<div class="charts_fill_class" v-loading="loading">
			<v-chart
				element-loading-text="暂无数据"
				element-loading-spinner="el-icon-document-delete"
				element-loading-background="rgba(239, 239, 239, 0.5)"
				class="charts_one_class"
				ref="bondQuarterInfo"
				style="height: 442px; margin-top: 8px"
				autoresize
				:options="option"
			/>
		</div>
	</div>
</template>

<script>
import { barChartOption } from '@/utils/chartStyle';
// 转债季度信息
import { getBondQuarterInfo } from '@/api/pages/Analysis.js';
export default {
	data() {
		return {
			loading: true,
			option: {},
			info: {}
		};
	},
	methods: {
		// 获取转债季度信息
		async getBondQuarterInfo() {
			let data = await getBondQuarterInfo({
				code: this.info.code,
				type: this.info.type,
				flag: this.info.flag,
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			if (data?.mtycode == 200) {
				this.getChartData(data?.data);
			}
		},
		getData(info) {
			this.info = info;
			this.getBondQuarterInfo();
		},
		// 获取数据
		getChartData(data) {
			this.loading = false;
			let max = Math.max.apply(null, data.ratioinN);
			let min = Math.min.apply(null, data.ratioinN);
			this.option = barChartOption({
				toolbox: 'none',
				legend: ['收益率', '可转债仓位', '平底溢价率', '置信度'],
				xAxis: [{ data: data?.map((v) => v.yearqtr) }],
				yAxis: [
					{ type: 'value', name: '收益率' }
					// { type: 'value', name: '置信度' }
				],
				dataZoom: true,
				series: [
					{
						name: '收益率',
						type: 'bar',
						data: data?.map((item) => {
							return item.cumreturn * 1 ? (item.cumreturn * 100).toFixed(2) : '--';
						}),
						itemStyle: {
							color: 'green'
						}
					},
					{
						name: '可转债仓位',
						type: 'bar',
						data: data.map((item) => {
							return item.ratioinN * 1 ? (item.ratioinN * 1).toFixed(2) : '--';
						}),
						itemStyle: {
							color: 'blue'
						}
					},
					{
						name: '平底溢价率',
						type: 'bar',
						data: data.map((item) => {
							return item.convertpremiumrate * 1 ? (item.convertpremiumrate * 1).toFixed(2) : '--';
						}),
						itemStyle: {
							color: 'red'
						}
					},
					{
						name: '置信度',
						type: 'line',
						// yAxisIndex: 1,
						// areaStyle: {},
						showSymbol: false,
						data: data.map((item) => {
							return item.ratioinn * 1 ? (item.ratioinn * 1).toFixed(2) : '--';
						})
					}
				]
			});
			console.log(this.option);
		},
		// 提供最大值和最小值，取值相对于归一化的最值间的对数
		logScale(minValue, maxValue, value) {
			const range = maxValue - minValue;
			const offset = value - minValue;
			const ratio = offset / range;
			const logRatio = Math.log10(ratio + 1);
			return logRatio;
		}
	}
};
</script>

<style></style>
