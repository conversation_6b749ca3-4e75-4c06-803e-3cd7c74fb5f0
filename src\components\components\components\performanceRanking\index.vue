<template>
	<div class="chart_one">
		<div style="display: flex; justify-content: space-between; align-items: center">
			<div class="title">最新基金业绩排名</div>
			<div>
				<el-select style="margin-right: 16px" v-model="model" placeholder="" @change="changeType">
					<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>
				<el-button icon="el-icon-document-delete" @click="exportExcel">导出Excel</el-button>
			</div>
		</div>
		<div v-loading="loading" style="margin-top: 16px">
			<el-table :data="data" style="width: 100%" height="400px" :default-sort="sortDefault" @sort-change="sortChange">
				<el-table-column
					v-for="item in column"
					:key="item.value"
					:prop="item.value"
					:label="item.label"
					:sortable="item.sortable ? item.sortable : false"
					align="gotoleft"
				>
					<template #header>
						<long-table-popover-chart
							v-if="item.popover"
							:data="data"
							date_key="name"
							:data_key="item.value"
							:show_name="item.label"
							:formatter="
								function (val) {
									return val;
								}
							"
						>
							<span>{{ item.label }}</span>
						</long-table-popover-chart>
						<span v-else>{{ item.label }}</span>
					</template>
					<template slot-scope="scope">
						<el-link v-if="item.value == 'manager_name'" @click="goDetail(scope.row.manager_code, scope.row.manager_name)">{{
							scope.row[item.value]
						}}</el-link>
						<el-link v-else-if="item.value == 'name'" @click="goDetail(scope.row.code, scope.row.name)">{{
							scope.row[item.value]
						}}</el-link>
						<span
							v-else-if="item.color == 'red_or_green'"
							:style="
								item.color == 'red_or_green'
									? `${scope.row[item.value] > 0 ? 'color:red' : scope.row[item.value] < 0 ? 'color:green' : ''}`
									: ''
							"
							>{{ scope.row[item.value] }}%</span
						>
						<span v-else>{{ scope.row[item.value] }}</span>
					</template>
				</el-table-column>
				<template slot="empty">
					<el-empty image-size="160"></el-empty>
				</template>
			</el-table>
		</div>
	</div>
</template>

<script>
// 最新基金业绩排名
import { getFundRecentReturn } from '@/api/pages/SystemOther.js';
import { alphaGo } from '@/assets/js/alpha_type.js';
import { filter_json_to_excel } from '@/utils/exportExcel.js';
export default {
	data() {
		return {
			model: '',
			options: [],
			data: [],
			sortDefault: { prop: 'theyear', order: 'descending' },
			column: [
				{
					label: '基金名称',
					value: 'name',
					popover: false
				},
				{
					label: '基金经理',
					value: 'manager_name',
					popover: false
				},
				{
					label: '今年以来(交易日)',
					value: 'theyear',
					format: this.fix2p,
					color: 'red_or_green',
					sortable: true,
					popover: true
				},
				{
					label: '近3月(交易日)',
					value: '1quarter',
					color: 'red_or_green',
					format: this.fix2p,
					sortable: true,
					popover: true
				},
				{
					label: '近6月(交易日)',
					value: '2quarter',
					color: 'red_or_green',
					format: this.fix2p,
					sortable: true,
					popover: true
				},
				{
					label: '近一年(交易日)',
					value: '1year',
					color: 'red_or_green',
					format: this.fix2p,
					sortable: true,
					popover: true
				},
				{
					label: '近两年(交易日)',
					value: '2year',
					color: 'red_or_green',
					format: this.fix2p,
					sortable: true,
					popover: true
				},
				{
					label: '基金规模(亿元)',
					value: 'netasset',
					format: this.fix8,
					sortable: true,
					popover: true
				}
			],
			loading: true,
			info: {}
		};
	},
	methods: {
		// 获取基金类型列表
		getTypeList(data) {
			this.info = data;
			this.options = data?.type?.map((item) => {
				return {
					label: this.FUNC.textConverter(this.COMMON.fundType_zh_en, item, 'en', 'zh'),
					value: item
				};
			});
			this.model = this.options?.[0].value;
			this.getFundRecentReturn(this.model);
			// this.$emit('resolveFather', this.model);
		},
		// 切换基金类型
		changeType(val) {
			this.model = val;
			this.getFundRecentReturn(this.model);
			this.loading = true;
		},
		// 前往详情
		goDetail(code, name) {
			alphaGo(code, name, this.$route.path);
		},
		async getFundRecentReturn(type) {
			let data = await getFundRecentReturn({ code: this.info.code, type });
			if (data?.mtycode == 200) {
				this.getData(data?.data);
			}
		},

		// 获取数据
		getData(data) {
			this.loading = false;
			this.data = data?.map((item) => {
				let formatData = { ...item };
				for (const key in item) {
					let index = this.column.findIndex((obj) => {
						return obj.value == key;
					});
					if (index != -1) {
						if (this.column[index]?.format) {
							formatData[key] = this.column[index]?.format(item[key]);
						}
					}
				}
				return formatData;
			});
		},
		// 导出excel
		exportExcel() {
			let column = this.column.map((item) => {
				return {
					...item,
					format: ''
				};
			});
			let name = this.options.find((item) => {
				return item.value == this.model;
			})?.label;
			filter_json_to_excel(column, this.data, this.info.name + '-' + name + '最新基金业绩排名');
		},
		// 表格排序
		// sortChange({ column, prop, order }) {
		// 	this.sortDefault = {
		// 		prop,
		// 		order
		// 	};
		// },
		sortChange(sortVal) {
			let order = sortVal.order;
			let key = sortVal.prop;
			if (!order) {
				this.data = this.data.slice();
			} else if (order == 'ascending' && key) {
				let haveValList = this.data.filter((item) => !isNaN(parseFloat(item[key])));
				let noValList = this.data.filter((item) => isNaN(parseFloat(item[key])));
				haveValList.sort((a, b) => a[key] - b[key]);
				this.data = [...haveValList, ...noValList];
			} else if (order == 'descending' && key) {
				let haveValList = this.data.filter((item) => !isNaN(parseFloat(item[key])));
				let noValList = this.data.filter((item) => isNaN(parseFloat(item[key])));
				haveValList.sort((a, b) => b[key] - a[key]);
				this.data = [...haveValList, ...noValList];
			}
		},
		// 百分化
		fix2p(val) {
			return val * 1 ? (val * 100).toFixed(2) * 1 : '--';
		},
		// 亿元化
		fix8(val) {
			return val * 1 ? (val / 10 ** 8).toFixed(2) * 1 : '--';
		}
	}
};
</script>

<style></style>
