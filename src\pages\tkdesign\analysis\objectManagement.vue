<template>
  <div class="box_Board">
    <div class="header_box">
      <span class="header_inactive">投后&nbsp;/&nbsp;投后分析&nbsp;/&nbsp;</span>
      分析对象管理
    </div>
    <div class="border_table">
      <!-- 头部区域 -->
      <div class="border_table_header">
        <!-- 左侧标题区域 -->
        <div class="border_table_header_title">分析对象管理列表</div>
        <!-- 右侧按钮区域 -->
        <div class="border_table_header_button">
          <el-button icon="el-icon-plus"
                     type="primary"
                     @click="openAddObj"> 新增分析对象 </el-button>
        </div>
      </div>
      <!-- 表格区域 -->
      <el-table :data="tableData"
                height="calc(100vh - 440px)">
        <el-table-column align="gotoleft"
                         label="投后分析对象"
                         prop="targetName">
          <template slot-scope="scope">
            <div v-if="scope.row.addFlag">
              <el-input v-model="scope.row.targetName"
                        placeholder="请输入"
                        clearable />
            </div>
            <div v-else>{{ scope.row.targetName }}</div>
          </template>
        </el-table-column>
        <el-table-column align="gotoleft"
                         label="创建时间"
                         prop="insertDate"> </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <div v-if="!scope.row.addFlag"
                 class="flex">
              <el-button class="button-color"
                         type="text"
                         @click="edit2(scope.row, scope.$index)">编辑 </el-button>
              <el-button class="button-color"
                         type="text"
                         @click="deleteObject(scope.row.id)"> 删除 </el-button>
            </div>
            <div v-else
                 class="flex">
              <el-button class="button-color"
                         type="text"
                         @click="save(scope.row)">保存 </el-button>
              <el-button class="button-color"
                         type="text"
                         @click="cancel(scope.row, scope.$index)">取消 </el-button>
            </div>
          </template>
        </el-table-column>
        <template slot="empty">
          <el-empty :image-size="160" />
        </template>
      </el-table>
      <!-- 分页器 -->
      <div class="pagination_board">
        <el-pagination :current-page.sync="pagination.pageIndex"
                       :page-size="pagination.pageSize"
                       :total="pagination.total"
                       background
                       layout="total, sizes, prev, pager, next"
                       @size-change="sizeChange"
                       @current-change="currentChange" />
      </div>
    </div>
    <!-- 新建分析对象弹框 -->
    <el-dialog :visible.sync="showDialog"
               class="dialog"
               :title="editFlag?'编辑分析对象':'新建分析对象'"
               width="80%"
               :close-on-press-escape='false'
               :close-on-click-modal="false"
               :show-close="false"
               @close="dialogCancel">
      <div class="dialog_box">
        <div class="dialog_box_main">
          <div class="main_target">
            <div class="main_target_search pd">
              <span class="main_target_search_title">对象名称：</span>
              <el-input class="search_input"
                        v-model="targetName"
                        placeholder="请输入对象名称" />
            </div>
            <div class="main_target_tabs">
              <!-- tabs标签 -->
              <el-tabs v-model="activeName">
                <el-tab-pane v-for="item in tabs"
                             :name="item.value">
                  <div slot="label">
                    <span style="padding: 0 16px">{{ item.title }}</span>
                  </div>
                  <el-input v-if="activeName === 1"
                            placeholder="输入关键字进行过滤"
                            v-model="filterText"
                            class="tree-filter-input" />
                  <div v-show="activeName === 1"
                       class="tab-pane_first">
                    <el-row>
                      <el-col v-loading="threeLoading"
                              :span="17"
                              class="tab-pane_first_left">
                        <el-tree ref="tree"
                                 draggable
                                 :data="treeData"
                                 :props="defaultProps"
                                 node-key="treeNodeId"
                                 show-checkbox
                                 :filter-node-method="filterNode"
                                 @check-change="changeNode"
                                 @node-drag-end="changeNode"
                                 style="height: 540px; overflow-y: auto" />
                      </el-col>
                      <el-col :span="7"
                              class="tab-pane_first_right">
                        <div v-for="(item, index) in list"
                             :key="index"
                             :draggable="index !== 0 && index !== list.length - 1"
                             :class="index !== 0 && index !== list.length - 1 ? 'tab-pane_first_right_item' : 'pane_first_right_item_disable'"
                             @dragend="dragend(item, $event)"
                             @dragenter="dragenter(item, $event)"
                             @dragover="dragover($event)"
                             @dragstart="dragstart(item)">
                          <div v-if="item.name !== '一级策略' && item.name !== '二级策略'">
                            <img draggable="false"
                                 src="../../../assets/img/move.png"
                                 width="12" />
                            {{ item.name }}
                          </div>
                          <div v-if="item.name === '一级策略'">
                            <div class="tab-pane_first_right_item"
                                 style="padding-top: 0">
                              <img draggable="false"
                                   src="../../../assets/img/move.png"
                                   width="12" />
                              {{ item.name }}
                            </div>
                            <div class="tab-pane_first_right_item"
                                 style="padding-bottom: 0">
                              <img draggable="false"
                                   src="../../../assets/img/move.png"
                                   width="12" />
                              二级策略
                            </div>
                          </div>
                        </div>
                      </el-col>
                    </el-row>
                  </div>
                  <div v-show="activeName === 2"
                       class="tab-pane_second">
                    <el-row>
                      <el-col :span="8"
                              class="tab-pane_second_left">
                        <div v-for="(item, index) in prefixContent"
                             :key="index"
                             :style="item.focus ? 'backgroundColor:#fff4e6;borderRight:3px solid #4096ff;color:#4096ff;' : ''"
                             @click="selectPrefixContent(item)">
                          {{ item.name }}
                        </div>
                      </el-col>
                      <el-col :span="16"
                              class="tab-pane_second_right">
                        <el-checkbox-group v-model="checkList"
                                           class="right_checkBox">
                          <el-checkbox label="个红"></el-checkbox>
                          <el-checkbox label="传统"></el-checkbox>
                          <el-checkbox label="QDII港股及ADR(境外权益泰康资产香港)"></el-checkbox>
                          <el-checkbox label="个万A"></el-checkbox>
                          <el-checkbox label="个万B"></el-checkbox>
                          <el-checkbox label="个万丙"></el-checkbox>
                          <el-checkbox label="投连1号"></el-checkbox>
                          <el-checkbox label="直投账户"></el-checkbox>
                        </el-checkbox-group>
                      </el-col>
                    </el-row>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </div>
          <div class="flex main_button">
            <el-button class="main_button_item"
                       @click="deleteAll">清空已选</el-button>
          </div>
          <div class="main_selected">
            <div class="main_selected_header">已选对象{{ filterSelectedData().length }}</div>
            <div class="dialog_table">
              <div class="dialog_table-header">
                <div class="name">名称</div>
                <div class="group">所属组</div>
                <div class="icon"></div>
              </div>
              <div class="dialog_table-body"
                   v-for="item in filterSelectedData()"
                   :key="item.treeNodeId">
                <div class="name">{{ item.name }}</div>
                <div class="group">{{ getGroupName(item.type) }}</div>
                <div class="icon"><i class="el-icon-delete"
                     @click="deleteIcon(item)"></i></div>
              </div>
              <el-empty v-if="!selectedData.length"
                        image-size="160" />
            </div>
          </div>
        </div>
      </div>
      <div class="dialog_footer">
        <el-button @click=cancelBox()>取消</el-button>
        <el-button type="primary"
                   @click="saveObjectData">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import RuleSet from '../monitorWarning/components/ruleSet.vue';
import CompositionSet from '../monitorWarning/components/compositionSet.vue';
import DisposeSet from '../monitorWarning/components/disposeSet.vue';
import { getList, getTree, deleteObject, saveObject } from '../../../api/pages/analysis/objectManagement';

export default {
  components: { DisposeSet, CompositionSet, RuleSet },
  data () {
    return {
      editFlag: false,
      // 删除原有映射按钮提示显示与隐藏
      promptMessage: false,

      activeName: 1,

      pagination: {
        pageIndex: 1, // 当前页码
        pageSize: 10, // 页面显示几条数据
        total: 0
      },
      tableData: [], // 页面表格数据源
      oldObject: {},
      oldValue: '',
      selectedData: [], // 弹框表格数据源
      prefixContent: [
        {
          name: '账户',
          focus: true
        },
        {
          name: '策略',
          focus: false
        },
        {
          name: '投管人',
          focus: false
        },
        {
          name: '部门',
          focus: false
        },
        {
          name: '投资经理',
          focus: false
        },
        {
          name: 'GP3',
          focus: false
        }
      ],

      showDialog: false, // 绑定修改记录的dialog
      tabs: [
        {
          title: '层级',
          value: 1
        }
        // {
        //   title: '维度',
        //   value: 2
        // }
      ],
      list: [
        { name: '大账户', id: 10 },
        { name: '子账户', id: 11 },
        { name: '一级管理人', id: 20 },
        { name: '一级策略', id: 21 },
        { name: '部门', id: 30 },
        { name: '投资经理', id: 40 },
        { name: 'GP3', id: 50 }
      ],
      defaultProps: {
        children: 'children',
        label: 'name',
        disabled: 'disabled'
      },
      treeData: [],
      uploadTreeData: [],
      targetName: '',
      direction: '',
      order: '',
      checkList: [],
      threeLoading: false,
      filterText: '',
      tempTree: undefined,
      editID: undefined
    };
  },

  watch: {
    filterText (val) {
      this.$refs.tree[0].filter(val);
    }
  },

  mounted () {
    this.getList();
  },

  methods: {
    cancelBox () {
      this.showDialog = false; this.editFlag = false; this.targetName = ''
    },
    edit2 (params, index) {
      this.editFlag = true
      this.getObjTree();
      this.editID = params.id
      this.targetName = params.targetName
      console.log(params);
      // this.uploadTreeData = params.contextObj.nodes
      this.showDialog = true
      // this.changeNode()
      setTimeout(() => {
        this.$nextTick(() => {
          // console.log(this.$refs.tree[0].root, this.$refs.tree[0].root.data);
          // console.log(this.$refs.tree[0].getCheckedNodes(), this.$refs.tree[0].getCurrentNode());
          // console.log(params?.code);
          this.getAllNode(this.$refs.tree[0]?.root?.data || [], params?.code || [])
          // console.log(this.$refs.tree[0].getCheckedNodes(), this.$refs.tree[0].getCurrentNode());
          this.changeNode()
        })
      }, 500)
      // this.selectedData = params?.code?.map((item, index) => {
      //   return {
      //     name: item,
      //     type: 50,
      //     treeNodeId: index
      //   }
      // }) || []
    },
    getAllNode (node, codeList) {
      // console.log(node, codeList);
      for (let i = 0; i < node.length; i++) {
        if (codeList.indexOf(node[i].name) > -1) {
          // console.log("yes", node[i].$treeNodeId);
          this.$refs.tree[0].setChecked(node[i], true, true)
        }
        if (node[i]?.children && node[i]?.children.length > 0) {
          this.getAllNode(node[i]?.children, codeList);
        }
      }
    },
    filterSelectedData () {
      // console.log(this.selectedData);
      // 只显示gp3的
      const temp = (this.selectedData.filter((item) => item.type == 50)).sort((a, b) => { if (b.name < a.name) return 1; else return -1 });
      console.log(temp);
      return temp;
    },
    /**
     * 树查询
     */
    filterNode (value, data) {
      if (!value) return true;
      if (data.children == null) {
        return data?.filterTag?.indexOf(value) !== -1
      }
      return (data.name || "").indexOf(value) !== -1;
    },
    // filterNode (value, data) {
    //   // 如果没有输入过滤条件，显示所有节点
    //   if (!value) return true;

    //   // 如果当前节点没有子节点，显示该节点（最后一层）
    //   if (!data.children || data.children.length === 0) {
    //     // 检查所有父节点是否符合过滤条件
    //     return this.checkAllParents(value, data);
    //   }

    //   // 如果当前节点符合过滤条件，显示该节点及其所有子节点
    //   if (data.name.indexOf(value) !== -1) {
    //     this.markAllChildren(data, true);
    //     return true;
    //   }

    //   // 递归检查子节点是否符合过滤条件
    //   return data.children.some(child => this.filterNode(value, child));
    // },
    // checkAllParents (value, node) {
    //   let parent = this.findParent(this.treeData, node);
    //   while (parent) {
    //     if (parent.name.indexOf(value) !== -1) {
    //       return true;
    //     }
    //     parent = this.findParent(this.treeData, parent);
    //   }
    //   return false;
    // },
    // findParent (data, node) {
    //   // console.log(data);
    //   for (let i = 0; i < data.length; i++) {
    //     const item = data[i];
    //     if (item.children && item.children.length > 0) {
    //       if (item.children.includes(node)) {
    //         return item;
    //       }
    //       const found = this.findParent(item.children, node);
    //       if (found) {
    //         return found;
    //       }
    //     }
    //   }
    //   return null;
    // },
    // markAllChildren (node, value) {
    //   if (node.children) {
    //     node.children.forEach(child => {
    //       child.visible = value;
    //       this.markAllChildren(child, value);
    //     });
    //   }
    // },
    /**
     * 每页条数改变时触发的回调
     * @param value
     */
    sizeChange (value) {
      this.pagination.pageSize = value;
      this.getList();
    },

    /**
     * 当前页数改变时触发的回调
     * @param value
     */
    currentChange (value) {
      this.pagination.pageIndex = value;
      this.getList();
    },

    /**
     * 处在编辑状态时，不允许对其他数据进行操作
     */
    ban () {
      let result = this.tableData.filter((v) => v.addFlag);
      return result.length > 0;
    },

    /**
     * 编辑
     * @param obj 该行的数据
     * @param number 该行的下标
     */
    edit (obj, number) {
      //判断是否处于编辑状态 如果正在编辑则不允许点击其他编辑按钮
      let result = this.tableData.filter((v) => v.addFlag);
      if (result.length > 0) return;
      //保存传过来的的对象，留着在用户点击取消的时候还原数据
      this.oldObject = JSON.parse(JSON.stringify(obj));
      //这里addFlag为true但是页面没有实现响应式
      obj.addFlag = true;
      //这里是为了解决addFlag不能实现响应式的问题 （数组重写）
      //将tableData里对应的该行数据删除，然后再把addFlag为true的obj添加到删除的位置
      this.tableData.splice(number, 1);
      this.tableData.splice(number, 0, obj);
    },

    /**
     * 取消
     * @param obj
     * @param number
     */
    cancel (obj, number) {
      if (obj.actionType === 1) {
        this.tableData.shift();
      } else {
        this.tableData.splice(number, 1);
        this.tableData.splice(number, 0, this.oldObject);
      }
    },

    /**
     * 保存
     * @param row
     */
    save (row) {
      const data = {
        startDate: row.startDate,
        endDate: row.endDate,
        targetName: row.targetName
      };
      if (row.id) {
        data.id = row.id;
      }
      console.log(JSON.parse(row.context.value).nodes, 'sssssssssssssssss');
      if (row.context) {
        if (JSON.parse(row.context.value).nodes) data.nodes = JSON.parse(row.context.value).nodes;
        if (JSON.parse(row.context.value).order) data.order = JSON.parse(row.context.value).order;
        if (JSON.parse(row.context.value).direction) data.direction = JSON.parse(row.context.value).direction;
      }
      saveObject(data).then((res) => {
        if (res.code === 200) {
          this.$message.success('上传成功');
          this.getList();
        } else {
          this.$message.error('上传失败');
        }
        row.addFlag = false;
      });
    },

    /**
     * 删除
     */
    deleteObject (id) {
      this.$confirm('确定删除么?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteObject(id, {}).then((res) => {
          if (res.code === 200) {
            this.$message.success('删除成功');
            this.getList();
          } else {
            this.$message.error('删除失败');
          }
        });
      });
    },

    /**
     * 获取列表
     */
    getList () {
      const data = {
        current: this.pagination.pageIndex,
        pageSize: this.pagination.pageSize,
        endDate: 20230327,
        startDate: 20230328
      };
      getList(data).then((res) => {
        if (res.code === 200) {
          this.tableData = res.data;
          this.pagination.total = res.total;
        } else {
          this.tableData = [];
          this.pagination.total = 0;
        }
      });
    },

    /**
     * 添加分析对象
     */
    openAddObj () {
      this.showDialog = true;
      this.getObjTree();
    },

    /**
     * 获取分析对象树
     */
    getObjTree (arr) {
      if (arr) {
        this.tempTree = arr
      }
      this.threeLoading = true;
      let params = {
        direction: this.activeName,
        order: this.tempTree ? this.tempTree.join(',') : '10,11,20,21,22,30,40,50',
        endDate: 20230327,
        startDate: 20230328
      };
      getTree(params).then(async (res) => {
        this.threeLoading = false;
        if (res.code === 200) {
          let that = this;
          await that.setCheckedNodes(res.data.nodes, null);
          that.treeData = res.data.nodes;
          that.uploadTreeData = JSON.parse(JSON.stringify(res.data.nodes));
          that.direction = res.data.direction;
          that.order = res.data.order;
          await that.$refs.tree[0].setCheckedNodes(that.selectedData);
        }
      });
    },

    /**
     * 递归给节点增加ID
     */
    async setCheckedNodes (arr, key) {
      await arr.forEach((item, index) => {
        item.treeNodeId = key ? `${key}-${index}` : `${index}`;
        if (item.selected) {
          this.selectedData.push({
            name: item.name,
            treeNodeId: item.treeNodeId,
            type: item.type
          });
        }
        if (item.children) {
          this.setCheckedNodes(item.children, item.treeNodeId);
        }
      });
    },

    /**
     * 修改原数据内的参数
     */
    handData (arr) {
      for (let i = 0; i < arr.length; i++) {
        for (let j = 0; j < this.selectedData.length; j++) {
          if (arr[i].treeNodeId === this.selectedData[j].treeNodeId) {
            arr[i].selected = true;
            break;
          }
        }
        if (arr[i].children) {
          this.handData(arr[i].children);
        }
      }
    },

    /**
     * 获取组名
     */
    getGroupName (type) {
      return type === 22 ? '二级策略' : this.list.filter((v) => v.id === type)[0].name;
    },

    /**
     *递归将节点禁用
     */
    async setCheckedDisabled (arr, key) {
      let that = this;
      await arr.forEach((item, index) => {
        if (key !== null && key === '投连账户' && item.accountType1 !== '投连账户') {
          this.$set(item, 'disabled', true);
        }
        if (key !== null && key !== '投连账户' && item.accountType1 === '投连账户') {
          this.$set(item, 'disabled', true);
        }
        if (key === null) {
          this.$set(item, 'disabled', false);
        }
        if (item.children) {
          that.setCheckedDisabled(item.children, key);
        }
      });
    },

    /**
     * 节点状态改变
     */
    async changeNode () {
      let that = this;
      let arr = [];
      let key = '';
      await setTimeout(() => {
        that.$refs.tree[0].getCheckedNodes().forEach((item) => {
          arr.push({
            name: item.name,
            treeNodeId: item.treeNodeId,
            type: item.type,
            accountType1: item.accountType1
          });
          key = item.accountType1;
        });
        arr.sort((a, b) => a.type - b.type);
        that.setCheckedDisabled(that.treeData, !key ? null : key);
      }, 0);

      await setTimeout(() => {
        that.selectedData = arr;
        that.uploadTreeData = JSON.parse(JSON.stringify(that.treeData));
        that.handData(that.uploadTreeData);
      }, 0);
    },

    /**
     * 对数据进行排序
     */
    compare (a, b) {
      if (a.type < b.type) {
        return -1;
      }
      if (a.type > b.type) {
        return 1;
      }
      return 0;
    },

    /**
     * 清空已选
     */
    deleteAll () {
      this.selectedData.forEach((item) => {
        this.$refs.tree[0].setChecked(item.treeNodeId, false, true);
        this.changeNode()
      });
    },

    /**
     * 删除节点
     */
    deleteIcon (obj) {
      // console.log(obj, this.filterSelectedData());
      this.$refs.tree[0].setChecked(obj.treeNodeId, false, true);
      this.changeNode()
    },

    /**
     * 保存分析对象
     */
    filterNodes (nodes) {
      const filtered = [];

      nodes.forEach(node => {
        // 深拷贝节点，避免修改原始数据
        const nodeCopy = JSON.parse(JSON.stringify(node));

        // 检查 'children' 是否存在且为数组
        if (Array.isArray(nodeCopy.children)) {
          // 递归处理子节点
          nodeCopy.children = this.filterNodes(nodeCopy.children);

          // 如果子节点为空数组，则移除 'children' 键
          if (nodeCopy.children.length === 0) {
            delete nodeCopy.children;
          }
        } else {
          // 如果 'children' 不存在或不是数组，确保移除 'children' 键
          delete nodeCopy.children;
        }

        // 判断当前节点是否应该保留
        if (nodeCopy.selected === true || (nodeCopy.children && nodeCopy.children.length > 0)) {
          // 如果节点的 'selected' 为 false，但有保留的子节点
          // 则保留节点，并保留原有的 'selected' 状态
          filtered.push(nodeCopy);
        }
        // 否则，不保留该节点
      });

      return filtered;
    },
    saveObjectData () {
      console.log(this.uploadTreeData);
      let temp = this.filterNodes(this.uploadTreeData)
      console.log((temp));
      let data = {
        order: this.order,
        direction: this.direction,
        nodes: temp,
        targetName: this.targetName,
        startDate: this.moment().format('YYYY-MM-DDTHH:mm:ss'),
        endDate: this.moment().format('YYYY-MM-DDTHH:mm:ss'),
        id: this.editFlag ? this.editID : undefined
      };
      let arr = this.list.map((v) => v.id);
      let index = arr.indexOf(21);
      arr.splice(index + 1, 0, 22);
      data.order = arr.join(',');
      saveObject(data).then((res) => {
        if (res.code === 200) {
          this.$message.success('保存成功');
          this.showDialog = false;
          this.getList();
        } else {
          this.$message.error(res.message);
          this.showDialog = false;
        }
      });
    },

    /**
     * 拖拽方法
     * @param value
     */
    dragstart (value) {
      this.oldData = value;
    },

    /**
     * 拖拽排序后的数组
     * @param value
     * @param e
     */
    dragenter (value, e) {
      if (value.name === '大账户' || value.name === 'GP3') return;
      this.newData = value;
      e.preventDefault();
    },

    /**
     * 拖拽最后操作
     * @param value
     * @param e
     */
    dragend (value, e) {
      if (this.oldData !== this.newData) {
        const oldIndex = this.list.indexOf(this.oldData);
        const newIndex = this.list.indexOf(this.newData);
        const newItems = [...this.list];
        newItems.splice(oldIndex, 1);
        newItems.splice(newIndex, 0, this.oldData);
        this.list = [...newItems];
        const idList = [];
        this.list.forEach((value, index) => {
          idList.push(value.id);
        });
      }
      let arr = this.list.map((v) => v.id);
      let index = arr.indexOf(21);
      arr.splice(index + 1, 0, 22);
      this.getObjTree(arr);
    },

    /**
     * 拖动事件（主要是为了拖动时鼠标光标不变为禁止）
     * @param e
     */
    dragover (e) {
      e.dataTransfer.dropEffect = 'move';
      e.preventDefault();
    },

    /**
     * 弹框左侧-纬度-左侧点击事件
     * @param item
     */
    selectPrefixContent (item) {
      if (item.name !== this.oldValue) {
        this.prefixContent.forEach((item) => {
          item.focus = false;
        });
        item.focus = true;
      }
      this.oldValue = item.name;
    },

    dialogCancel () {
      this.filterText = '';
      this.targetName = '';
    }
  }
};
</script>
<style lang="scss" scoped>
@import '../tkdesign';

.pd {
	padding: 8px 16px;
}

.border_table_header_button {
	position: relative;

	.prompt-message {
		z-index: 99;
		position: absolute;
		bottom: -98px;
		right: 82px;
		padding: 16px;
		border-radius: 4px;
		box-shadow: 0 0 2px #ccc;
		background-color: #fff;

		.el-button {
			padding: 5px;
			margin: 20px 0 0;
		}

		.cancel {
			margin-left: 20px;
		}

		.warning {
			border-color: #4096ff;
			background: #4096ff;
			color: white;
			margin-left: 5px;
		}

		.block {
			position: absolute;
			width: 10px;
			height: 10px;
			box-shadow: 0.5px 0.5px 1px #ccc;
			background-color: #fff;
			top: 0;
			left: 50%;
			transform: translate(-50%, -50%) rotate(225deg);
		}
	}
}

.header_box {
	margin-top: 16px;
	margin-bottom: 16px;
	font-size: 14px;
}

.dialog_box {
	border-bottom: #f4f4f4 solid 1px;
	border-top: #f4f4f4 solid 1px;
	margin-top: 10px;
	font-size: 16px;

	.dialog_box_main {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20px 24px;

		.main_target {
			height: 650px;
			width: 50%;
			border: 1px solid #e9e9e9;
			border-radius: 4px;

			.main_target_search {
				display: flex;
				justify-content: space-between;
				align-items: center;
				border-bottom: 1px solid #e9e9e9;

				.main_target_search_title {
					width: 100px;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}
			}

			.main_target_tabs {
				height: 600px;

				.tab-pane_first {
					height: 560px;

					.tab-pane_first_left {
						padding: 10px 16px;
					}

					.tab-pane_first_right {
						height: 560px;
						background: #f5f5f5;
						padding: 10px;

						.tab-pane_first_right_item {
							padding: 7px 0;
							white-space: nowrap;
							overflow: hidden;
							text-overflow: ellipsis;
							cursor: move;
						}

						.pane_first_right_item_disable {
							color: #bbbaba;
							padding: 7px 0;
							white-space: nowrap;
							overflow: hidden;
							text-overflow: ellipsis;
						}
					}
				}

				.tab-pane_second {
					height: 560px;

					.tab-pane_second_left {
						border-right: 1px solid #e9e9e9;
						height: 560px;
						padding-top: 10px;

						div {
							padding-left: 16px;
							line-height: 40px;
						}

						div:hover {
							background-color: #fff4e6;
							border-right: 3px solid #4096ff;
							color: #4096ff;
						}
					}

					.tab-pane_second_right {
						height: 560px;
						padding: 10px 16px;

						.right_checkBox {
							display: flex;
							flex-direction: column;

							.el-checkbox {
								margin: 10px 0;
							}
						}
					}
				}
			}
		}

		.main_button {
			flex-direction: column;
			align-items: center;
			width: 80px;

			.main_button_item {
				margin: 5px 0;
				width: 100%;
			}
		}

		.main_selected {
			width: 40%;
			border: 1px solid #e9e9e9;
			border-radius: 4px;
			height: 650px;

			.main_selected_header {
				padding: 10px 16px;
				border-bottom: 1px solid #e9e9e9;
			}
		}
	}
}

.dialog_footer {
	display: flex;
	justify-content: flex-end;
	padding: 10px;
}
</style>
<style lang="scss">
.tree-filter-input {
	margin-left: 0;
}

.dialog .el-dialog__body {
	padding: 0;
}

.main_target_tabs .el-tabs__header {
	margin-bottom: 0;
}

.dialog_table {
	height: 600px;
	overflow-y: auto;

	.dialog_table-header {
		display: flex;
		background-color: rgb(245, 245, 245);
		padding: 8px 0;

		.name {
			width: 60%;
			padding-left: 10px;
		}

		.group {
			width: 30%;
			padding-left: 10px;
		}

		.icon {
			width: 10%;
		}
	}

	.dialog_table-body {
		display: flex;
		padding: 4px 0;

		&:hover {
			background-color: #fff4e6 !important;
		}

		.name {
			width: 60%;
			padding-left: 10px;
		}

		.group {
			width: 30%;
			padding-left: 10px;
		}

		.icon {
			width: 10%;
			cursor: pointer;
		}
	}
}
</style>
