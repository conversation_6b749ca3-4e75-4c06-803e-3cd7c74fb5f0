<template>
	<div :class="info.flag == 2 ? 'chart_one' : ''" v-loading="showfamaloading">
		<div class="title" style="display: flex; align-items: center">
			动态多因子分析
			<el-tooltip class="item" effect="dark" :content="EXPLAIN.abilityPage['动态多因子分析']" placement="right-start">
				<svg width="14" height="14" viewBox="0 0 14 14" fill="none">
					<path
						fill-rule="evenodd"
						clip-rule="evenodd"
						d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
						fill="black"
						fill-opacity="0.45"
					/>
				</svg>
			</el-tooltip>
			<i class="el-icon-video-camera-solid videoIconDes" @click="openvideo"></i>
		</div>
		<div class="charts_fill_class">
			<el-empty v-show="abilityoptionsempty" image-size="160"></el-empty>
			<v-chart
				v-show="!abilityoptionsempty"
				ref="dynamicFourFactorAnalysis"
				:class="info.flag == 2 ? 'charts_one_class' : 'charts_analysis_class'"
				autoresize
				v-loading="abilityoptionsempty"
				element-loading-text="暂无数据"
				element-loading-spinner="el-icon-document-delete"
				element-loading-background="rgba(239, 239, 239, 0.5)"
				:options="abilityoptions"
			/>
		</div>
	</div>
</template>

<script>
import { exportTitle, exportChart } from '@/utils/exportWord.js';
import { lineChartOption } from '@/utils/chartStyle.js';
import VChart from 'vue-echarts';
// 动态4因子分析
export default {
	name: 'dynamicFourFactorAnalysis',
	components: {
		VChart
	},
	data() {
		return {
			showfamaloading: true,
			abilityoptionsempty: true,
			abilityoptions: {},
			info: {}
		};
	},
	methods: {
		openvideo() {
			window.open('https://www.bilibili.com/video/BV1xW4y1m7g2?share_source=copy_web');
		},
		getData(data, info) {
			this.info = info;
			this.showfamaloading = false;
			if (data.date == null || data.date == [] || data.date == '' || data.date == {}) {
				this.abilityoptionsempty = true;
			} else {
				let tempseries = [
					{
						name: '阿尔法',
						type: 'line',
						data: data.alpha.map((item) => {
							return Number(item) ? (item * 1).toFixed(4) : '--';
						}),
						symbol: 'none'
					},
					{
						name: '贝塔',
						type: 'line',
						data: data.beta.map((item) => {
							return Number(item) ? (item * 1).toFixed(4) : '--';
						}),
						symbol: 'none'
					},
					{
						name: '高估值偏好',
						type: 'line',
						data: data.pb.map((item) => {
							return Number(item) ? (item * 1).toFixed(4) : '--';
						}),
						symbol: 'none'
					},
					{
						name: '小盘偏好',
						type: 'line',
						data: data.size.map((item) => {
							return Number(item) ? (item * 1).toFixed(4) : '--';
						}),
						symbol: 'none'
					}
				];
				if (data.hk_beta) {
					tempseries.push({
						name: '港股高估值偏好',
						type: 'line',
						data: data.hk_beta.map((item) => {
							return Number(item) ? (item * 1).toFixed(4) : '--';
						}),
						symbol: 'none'
					});
				}
				this.abilityoptions = lineChartOption({
					legend: tempseries.map((item) => {
						return item.name;
					}),
					xAxis: [{ name: '日期', data: data.date }],
					yAxis: [{ type: 'value', scale: true }],
					series: tempseries
				});
				this.abilityoptionsempty = false;
			}
		},
		hideLoading() {
			this.abilityoptionsempty = true;
			this.showfamaloading = false;
		},
		createPrintWord() {
			this.$refs['dynamicFourFactorAnalysis'].mergeOptions({ toolbox: { show: false } });
			let height = this.$refs['dynamicFourFactorAnalysis'].$el.clientHeight;
			let width = this.$refs['dynamicFourFactorAnalysis'].$el.clientWidth;
			let chart = this.$refs['dynamicFourFactorAnalysis'].getDataURL({
				type: 'png',
				pixelRatio: 2,
				backgroundColor: '#fff'
			});
			this.$refs['dynamicFourFactorAnalysis'].mergeOptions({ toolbox: { show: true } });
			return [...exportTitle('动态4因子分析'), ...exportChart(chart, { width, height })];
		}
	}
};
</script>

<style></style>
