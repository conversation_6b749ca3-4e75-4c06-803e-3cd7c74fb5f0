import request from '@/utils/request';

/**
 * 获取模板列表
 * @param params
 * @returns {*}
 */
export function getReportManagementList(params) {
    return request({
        url: '/api/taikang/report/list',
        method: 'get',
        params
    });
}

/**
 * 获取投后分析对象
 * @returns {*}
 */
export function getTarget() {
    return request({
        url: '/api/taikang/report/option/target',
        method: 'get',
    });
}

/**
 * 保存提交
 */
export function saveReport(data) {
    return request({
        url: '/api/taikang/report/save',
        method: 'post',
        data
    });
}

/**
 * 删除数据
 */
export function deleteReport(id) {
    return request({
        url: `/api/taikang/report/del?id=${id}`,
        method: 'post',
    });
}
