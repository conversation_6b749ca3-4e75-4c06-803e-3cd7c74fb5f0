<template>
  <div style="overflow-x: hidden;"
       class="box_Board">
    <div class="header_box">
      <span class="header_inactive">投后&nbsp;/&nbsp;投后监控看板&nbsp;/&nbsp;</span>
      重点关注个股信息
    </div>
    <div class="border_table">
      <!-- 左侧筛选区域   :key="item.value" :label="item.label" :value="item.value"-->
      <div class="border_table_header_filter search-area">
        <el-form style="width:100%"
                 :inline="true"
                 :model="formData">
          <el-form-item label="搜索个股："
                        class="search_item">
            <el-autocomplete v-model="formData.stockName"
                             :fetch-suggestions="querySearchAsync"
                             class="search-stock"
                             clearable
                             value-key="label"
                             @select="handleSelect"
                             placeholder="搜索股票代码">
              <template slot-scope="{ item }">{{ item.label }}</template>
            </el-autocomplete>
          </el-form-item>
          <el-form-item label="分析报告："
                        class="search_item">
            <el-select v-model="formData.reportID"
                       class="search-security"
                       clearable
                       @change="changeObj"
                       placeholder="请选择分析报告">
              <el-option v-for="item in reportOptions"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="业绩基准："
                        class="search_item">
            <el-select v-model="formData.indexCode"
                       class="search-security"
                       clearable
                       placeholder="请选择业绩基准">
              <el-option v-for="item in standard"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="颗粒度："
                        class="search_item">
            <el-select v-model="formData.selectedCuts"
                       class="search-security"
                       clearable
                       @clear="graininessDetail.model = ''"
                       placeholder="请选择颗粒度">
              <el-option v-for="item in graininessOptions"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value"> </el-option>
            </el-select>
          </el-form-item>
          <!-- 时间选择 -->
          <el-form-item label="时间区间：">
            <el-date-picker v-model="time"
                            end-placeholder="结束日期"
                            clearable
                            range-separator="-"
                            value-format="yyyyMMdd"
                            start-placeholder="开始日期"
                            type="daterange">
            </el-date-picker>
          </el-form-item>

          <el-form-item class="search_item">
            <el-button type="primary"
                       @click="submitSTock">查看分析结果</el-button>
            <el-button @click="reset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <div v-if="showMain">
        <div class="border_table_header">
          <!-- 右侧标题区域 -->
          <div class="border_table_header_title">
            <div class="block" />
            <div>个股快速分析</div>
            <img alt=""
                 src="../../../assets/img/question.png"
                 class="question" />
          </div>
          <!-- 下载 -->
          <div class="flex">
            <el-select v-model="graininessDetail.model"
                       class="search-security"
                       placeholder="请选择颗粒度明细"
                       :disabled="loading"
                       @change="getChartData(performance.data)">
              <el-option v-for="item in graininessDetail.option"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value"> </el-option>
            </el-select>
            <el-select v-model="areaGraph.model"
                       class="search-security"
                       :disabled="loading"
                       @change="getChartData(performance.data)"
                       placeholder="请选择面积图基准">
              <el-option v-for="item in areaGraph.option"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value"> </el-option>
            </el-select>
            <div class="border_table_header_upload">
              <i class="el-icon-download"></i>
            </div>
          </div>
        </div>
        <div class="area-body">
          <div class="chart">
            <v-chart v-loading="loading"
                     autoresize
                     id="1"
                     style="height: 340px; width: 100% !important"
                     :options="performance.options" />
          </div>
        </div>

      </div>
      <div v-show="showMain">
        <Returns ref="returnS" />
      </div>
      <div v-show="!showMain"
           v-loading="loading">
        <el-empty :description="message"></el-empty>
      </div>
    </div>
  </div>
</template>

<script>
import Returns from './components/returns.vue';
import VChart from 'vue-echarts';
import { lineChartOption } from '@/utils/chartStyle';
import { getObjectStockProfitInfo } from '../../../api/pages/tkdesign/monitorSpectaculars';
import { getList } from '../../../api/pages/analysis/objectManagement';
import { handleData } from '../../../utils/count';
import { getReportManagementList } from '../../../api/pages/tkdesign/reportManagement';
export default {
  components: { VChart, Returns },
  data () {
    return {
      formData: {
        stockCode: '',
        stockName: '',
        reportID: '',
        indexCode: '',
        selectedCuts: ''
      },
      showMain: false,
      loading: false,
      message: '请选择条件查看分析结果',
      time: [],
      reportOptions: [],

      standard: [
        { label: '沪深300', value: '000300.SH' },
        { label: '深证100', value: '399330.SZ' },
        { label: '中证1000', value: '000852.SH' },
        { label: '创业板指', value: '399006.SZ' },
        { label: '恒生综合', value: 'HSCI.HI' },
        { label: '上证180', value: '000010.SH' },
        { label: '上证A指', value: '000002.SH' },
        { label: '股债50:50', value: 'B50E50' },
        { label: '股债60:40', value: 'B40E60' },
        { label: '股债50:50', value: 'B20E80' }
      ], //业绩基准
      graininessOptions: [
        { label: '部门', value: 'department' },
        { label: '大类账户', value: 'account_type1' },
        { label: '一般账户', value: 'account_type2' },
        { label: '一级策略', value: 'strategy1' },
        { label: '二级策略', value: 'strategy2' },
        { label: '投管人', value: 'manager1' },
        { label: '投资经理', value: 'manager' },
        { label: 'GP3', value: 'gp3_code' }
      ], //颗粒度
      graininessDetail: {
        option: [],
        model: ''
      }, //颗粒度明细
      areaGraph: {
        option: [
          {
            label: '账面价值 ',
            value: 'compoentBookValue'
          },
          {
            label: '持有数量',
            value: 'num'
          }
        ], // 面积图基准
        model: 'compoentBookValue'
      }, //面积图
      promptMessage: false,
      performance: {
        data: [],
        options: [] //
        //showEmpty: false,
      }
    };
  },
  methods: {
    /**
     * 调取接口方法
     */
    async getObjectStockProfitInfo () {
      this.showMain = true;
      this.graininessDetail.option = [];
      // this.graininessDetail.option.push({
      //   label: '整体',
      //   value: 'all'
      // });
      this.loading = true;
      this.$refs['returnS'].loadingTableLoading() //= this.$refs['return'].loading.chartLoading = true;
      const params = {
        startFrom: this.time[0],
        endTo: this.time[1],
        ...this.formData
      };
      await getObjectStockProfitInfo(params).then(async (res) => {
        this.loading = false;
        if (res.code === 200) {
          //拿到颗粒度明细得下拉框
          let array = [];
          res.data.rows.forEach((item) => {
            if (item.data['颗粒度'] !== 'nan' && item.data['颗粒度'] !== 'NaN' && item.data['颗粒度'] !== '--') {
              array.push(item.data['颗粒度']);
            }
          });
          array = Array.from(new Set(array));
          array.forEach((item) => {
            this.graininessDetail.option.push({
              label: item,
              value: item
            });
          });

          this.graininessDetail.model = this.graininessDetail.option[0].value;
          this.performance.data = res.data.rows;
          this.getChartData(this.performance.data);
        } else {
          this.showMain = false;
          this.$message.warning(res?.message || '暂无数据');
          this.graininessDetail.option = [];
        }
      });
      // console.log(this.graininessDetail);

      await this.$refs['returnS'].getObjectStocksReturnInfo(params, this.graininessDetail.option);
      this.$refs['returnS'].getTableData(params);
    },
    handleSelect (item) {
      this.formData.stockCode = item.value;
    },
    /**
     * 获取分析对象下拉框
     */
    getReportID () {
      const data = {
        current: 1,
        pageSize: 10000,
        endDate: 20230327,
        startDate: 20230328
      };
      getReportManagementList(data).then((res) => {
        if (res.code === 200) {
          res.data.forEach((item) => {
            this.reportOptions.push({
              label: item.targetName,
              value: item.id,
              ...item
            });
          });
        } else {
          this.reportOptions = [];
        }
      });
    },
    // 切换对象
    changeObj (obj) {
      let data = this.reportOptions?.[this.reportOptions?.findIndex(item => item?.value == obj)] || {};
      // console.log(data);
      this.formData.selectedCuts = data?.graininess || 'gp3_code'
      this.formData.indexCode = data?.analyticalBasis || '000300.SH'
      this.time = [data?.startDate?.slice(0, 10)?.replaceAll('-', ''), data?.endDate.slice(0, 10)?.replaceAll('-', '')]
      // console.log(this.time);
    },
    //搜索个股
    async querySearchAsync (queryString, cb) {
      let result = [];
      //todo 记得更换接口
      result = await this.$axios.get(this.$baseUrl + '/Analysis/Search/', {
        params: {
          flag: 9,
          message: queryString
        },
        headers: { authorization: 'Bearer ' + this.$store.state.token }
      });
      let array = [];
      for (let i = 0; i < 10; i++) {
        if (result.data.data[i]) {
          array.push({
            value: result.data.data[i].code,
            label: `${result.data.data[i].name}-${result.data.data[i].code}`
          });
        }
      }
      cb(array);
    },
    /**
     * 查询
     */
    submitSTock () {
      this.getObjectStockProfitInfo();
    },
    /**
     * 重置数据
     */
    reset () {
      this.formData = {
        stockCode: '',
        reportID: '',
        indexCode: '',
        selectedCuts: ''
      };
      this.message = '请选择条件查看分析结果';
      this.time = [];
      this.loading = false;
    },
    /**
     * 表格数据生成
     */
    getChartData (data) {
      // console.log(data);
      let rows = []
      if (this.graininessDetail.model == 'all') {

        rows = data;
      }
      else {
        rows = data.filter((item) => item.data['颗粒度'] === this.graininessDetail.model);

      }
      // 获取图表数据
      const date = [];
      const obj = {};
      const arr = ['基准收益', '申万行业基准收益', '泰康一级行业基准收益', '个股净值走势', this.areaGraph.model]; //颗粒度类型
      let name = '';
      // 时间排序
      rows.sort((a, b) => {
        const dateA = new Date(a.data.date);
        const dateB = new Date(b.data.date);
        return dateA - dateB;
      });
      rows.forEach((item) => {
        arr.forEach((v) => {
          if (!obj[v]) {
            obj[v] = [];
          }
          obj[v].push(handleData(item.data[v], true));
        });
        date.push(item.data.date);
      });
      // console.log(obj);
      for (let key in obj) {
        if (key === 'compoentBookValue') {
          name = '金额';
          const value = obj['compoentBookValue'];
          delete obj['compoentBookValue'];
          obj['账面价值'] = value;
        } else if (key === 'num') {
          name = '数量';
          const value = obj['num'];
          delete obj['num'];
          obj['持有数量'] = value;
        }
      }
      if (arr[arr.length - 1] === 'compoentBookValue') {
        arr.splice(arr.length - 1, 1, '账面价值');
      } else if (arr[arr.length - 1] === 'num') {
        arr.splice(arr.length - 1, 1, '持有数量');
      }
      let series = [];
      for (const key in obj) {
        if (key !== '账面价值' && key !== '持有数量')
          series.push({
            name: key,
            type: 'line',
            yAxisIndex: 0,
            data: obj[key],
            symbol: 'none',
            stack: 'Total',
            emphasis: {
              focus: 'series'
            },
            connectNulls: true,
            lineStyle: {
              width: 2
            }
          });
        else
          series.push({
            name: key,
            type: 'line',
            yAxisIndex: 1,
            data: obj[key],
            symbol: 'none',
            stack: 'Total',
            connectNulls: true,
            emphasis: {
              focus: 'series'
            },
            lineStyle: {
              width: 1
            },
            areaStyle: {
              opacity: 0.5
            }
          });
      }
      this.performance.options = lineChartOption({
        dataZoom: false,
        toolbox: 'none',
        legend: {
          data: arr
        },
        xAxis: [
          {
            name: '',
            data: date,
            boundaryGay: true
          }
        ],
        yAxis: [
          {
            type: 'value',
            position: 'left',
            axisLabel: {
              formatter: '{value}'
            },
            alignTicks: true
          },
          {
            name: name === '金额' ? name + '-亿' : name === '数量' ? name + '-万' : (value),
            type: 'value',
            position: 'right',
            formatter: function (value, index) {
              // console.log(value);
              return name === '金额' ? Number(value) / 10000000000 : name === '数量' ? Number(value) / 1000000 : Number(value)
            },
            axisLabel: {
              formatter: function (value, index) {
                // console.log(value);
                return name === '金额' ? Number(value) / 10000000000 : name === '数量' ? Number(value) / 1000000 : Number(value)
              }
            },
            alignTicks: true
          }
        ],
        series: series,
        tooltip: {
          formatter: function (obj) {
            // console.log(obj);
            let value = `<div style="font-size:14px;">` + obj?.[0].axisValue + `</div>`;
            for (let i = 0; i < obj.length; i++) {
              value +=
                `<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
                `<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
                obj?.[i].color +
                `;"></div>` +
                `<div style="font-family: PingFang SC;">` +
                obj?.[i].seriesName +
                '</div></div>' +
                `<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +

                ((obj[i].seriesName === '账面价值' ? Number(obj?.[i].value) / 10000000000 + '亿' : obj[i].seriesName === '持有数量' ? Number(obj?.[i].value) / 1000000 + '万' : Number(obj?.[i].value)) + (obj[i].seriesName === '账面价值' || obj[i].seriesName === '持有数量' ? '' : '%')) +
                '</div>' +
                `</div>`;
            }
            return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
          }
        }
      });
    }
  },
  mounted () {
    this.getReportID();
  }
};
</script>

<style lang="scss" scoped>
@import '../tkdesign';

.border_table_header_filter {
	display: flex;
	align-items: center;
	font-size: 14px;
	background-color: #f5f5f5;
	padding: 20px 20px 0;
	margin-bottom: 20px;

	.search_item {
		// width: 24% !important;
	}

	.search_item:last-child {
		padding-left: 150px;
		width: 1604px !important;
		margin-right: 0 !important;
	}
}

.main-filter {
	margin: 0 auto 16px;
	text-align: center;
}

.border_table_header {
	padding-bottom: 16px;
	border-bottom: 1px solid #ccc;

	.border_table_header_title {
		display: flex;
		align-items: center;

		.block {
			width: 6px;
			height: 20px;
			border-radius: 35px;
			background-color: #4096ff;
			margin-right: 12px;
		}

		.question {
			margin-left: 3px;
		}
	}

	.border_table_header_upload {
		width: 32px;
		line-height: 30px;
		border-radius: 4px;
		border: 1px solid #d9d9d9;
		text-align: center;
	}
}

.search-security {
	width: 210px;
	margin-right: 16px;
}

.search-stock {
	width: 210px;
	margin-right: 16px;
}
</style>

<style lang="scss">
.search-area {
	.el-form-item__label {
		width: 150px !important;
	}

	.el-form-item__content {
		margin-left: 0 !important;
	}
}

.search-stock .el-input__inner {
	padding: 0 30px !important;
}
</style>
