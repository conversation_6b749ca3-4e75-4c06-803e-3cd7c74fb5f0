<!--  -->
<template>
  <div v-loading="loading"
       style="font-size: 16px; display: flex; flex-wrap: wrap"
       class="equityrank">
    <div v-for="(item, index) in managersmsg"
         class="equityrankprintUni"
         :style="
				Number(inwidth) > 1810
					? 'flex: 1; min-width: 360px; background: #fff; padding: 24px 24px 12px 24px; margin-left: 24px; margin-top: 24px'
					: Number(inwidth) > 1440
					? 'flex: 1; min-width:  560px; background: #fff; padding: 24px 24px 12px 24px; margin-left: 24px; margin-top: 24px'
					: 'flex: 1; min-width: 560px; background: #fff; padding: 24px 24px 12px 24px; margin-left: 24px; margin-top: 24px'
			"
         :key="index"
         :id="'bondrank' + index">
      <div>
        <div style="page-break-inside: avoid"
             class="equityrankscorebox">
          <div class="pbai"
               style="display: flex">
            <!-- 评分1 -->
            <div style="display: flex; justify-content: center; flex: 1">
              <div class="equityrankscore">
                <el-progress :format="for1"
                             type="dashboard"
                             width="60"
                             :percentage="(item[0]['适应性排名'] * 100).toFixed(0)"
                             :color="colorList[index]">
                </el-progress>
              </div>
              <div style="display: flex; align-items: center; flex-direction: column; margin: 5px">
                <div style="font-size: 17px">适应评分</div>
                <div v-if="item[0]['适应性分位']">
                  <span :style="'font-size: 18px;color:' + colorList[index]">{{ item[0]['适应性分位'].split('/')[0] }}</span>/<span style="font-size: 13px">{{ item[0]['适应性分位'].split('/')[1] }}</span>
                </div>
              </div>
            </div>
            <!-- 评分2 -->
            <div style="display: flex; justify-content: center; flex: 1">
              <div class="equityrankscore">
                <el-progress :format="for1"
                             type="dashboard"
                             width="60"
                             :percentage="(item[0]['综合排名'] * 100).toFixed(0)"
                             :color="colorList[index]">
                </el-progress>
              </div>
              <div style="display: flex; align-items: center; flex-direction: column; margin: 5px">
                <div style="font-size: 17px">综合评分</div>
                <div v-if="item[0]['综合分位']">
                  <span :style="' font-size: 18px;color:' + colorList[index]">{{ item[0]['综合分位'].split('/')[0] }}</span>/<span>{{ item[0]['综合分位'].split('/')[1] }}</span>
                </div>
              </div>
            </div>
          </div>
          <!-- 投研能力 -->
          <div class="pbai">
            <div style="display: flex; align-items: center; margin-top: 24px">
              <div><span style="font-size: 16px; color: rgba(0, 0, 0, 0.85); font-weight: 500">投研能力</span></div>
            </div>
            <div style="display: flex">
              <div style="flex: 1; margin-top: 16px; padding-right: 24px">
                <div style="align-items: center">
                  <div style="color: #5c6e8f; font-size: 14px; font-weight: 400">
                    宏观能力{{ (item[0]['大类资产能力'] * 100).toFixed(0) }}分
                  </div>
                  <el-progress :show-text="false"
                               style="width: 100%; margin-top: 4px"
                               :format="for1"
                               :percentage="(item[0]['大类资产能力'] * 100).toFixed(0)"
                               :color="colorList[index]"></el-progress>
                </div>
                <div style="align-items: center; margin-top: 16px">
                  <div style="color: #5c6e8f; font-size: 14px; font-weight: 400">
                    中观能力{{ (item[0]['行业能力'] * 100).toFixed(0) }}分
                  </div>
                  <el-progress :show-text="false"
                               style="width: 100%; margin-top: 4px"
                               :format="for1"
                               :percentage="(item[0]['行业能力'] * 100).toFixed(0)"
                               :color="colorList[index]"></el-progress>
                </div>
              </div>
              <div style="flex: 1; margin-top: 16px; padding-right: 24px">
                <div style="align-items: center">
                  <div style="color: #5c6e8f; font-size: 14px; font-weight: 400">
                    择股能力{{ (item[0]['择股能力'] * 100).toFixed(0) }}分
                  </div>
                  <el-progress :show-text="false"
                               style="width: 100%; margin-top: 4px"
                               :format="for1"
                               :percentage="(item[0]['择股能力'] * 100).toFixed(0)"
                               :color="colorList[index]"></el-progress>
                </div>
                <div style="align-items: center; margin-top: 16px">
                  <div style="color: #5c6e8f; font-size: 14px; font-weight: 400">
                    交易能力{{ (item[0]['交易能力'] * 100).toFixed(0) }}分
                  </div>
                  <el-progress :show-text="false"
                               style="width: 100%; margin-top: 4px"
                               :format="for1"
                               :percentage="(item[0]['交易能力'] * 100).toFixed(0)"
                               :color="colorList[index]"></el-progress>
                </div>
              </div>
            </div>
          </div>
          <!-- 业绩能力评价 -->
          <div class="pbai"
               style="margin-top: 20px">
            <div style="display: flex; align-items: center">
              <div><span style="font-size: 16px; color: rgba(0, 0, 0, 0.85); font-weight: 500">业绩能力评价</span></div>
            </div>
            <div style="display: flex">
              <div style="flex: 1; margin-top: 16px; padding-right: 24px">
                <div style="align-items: center">
                  <div style="color: #5c6e8f; font-size: 14px; font-weight: 400">
                    收益能力{{ (item[0]['收益能力'] * 100).toFixed(0) }}分
                  </div>
                  <el-progress :show-text="false"
                               style="width: 100%; margin-top: 4px"
                               :format="for1"
                               :percentage="(item[0]['收益能力'] * 100).toFixed(0)"
                               :color="colorList[index]"></el-progress>
                </div>

                <div style="align-items: center; margin-top: 16px">
                  <div style="color: #5c6e8f; font-size: 14px; font-weight: 400">
                    胜率稳定{{ (item[0]['胜率稳定性'] * 100).toFixed(0) }}分
                  </div>
                  <el-progress :show-text="false"
                               style="width: 100%; margin-top: 4px"
                               :format="for1"
                               :percentage="(item[0]['胜率稳定性'] * 100).toFixed(0)"
                               :color="colorList[index]"></el-progress>
                </div>
              </div>
              <div style="flex: 1; margin-top: 16px; padding-right: 24px">
                <div style="align-items: center">
                  <div style="color: #5c6e8f; font-size: 14px; font-weight: 400">
                    信用调节{{ (item[0]['信用调节'] * 100).toFixed(0) }}分
                  </div>
                  <el-progress :show-text="false"
                               style="width: 100%; margin-top: 4px"
                               :format="for1"
                               :percentage="(item[0]['信用调节'] * 100).toFixed(0)"
                               :color="colorList[index]"></el-progress>
                </div>
                <div style="align-items: center; margin-top: 16px">
                  <div style="color: #5c6e8f; font-size: 14px; font-weight: 400">
                    久期调节{{ (item[0]['久期调节'] * 100).toFixed(0) }}分
                  </div>
                  <el-progress :show-text="false"
                               style="width: 100%; margin-top: 4px"
                               :format="for1"
                               :percentage="(item[0]['久期调节'] * 100).toFixed(0)"
                               :color="colorList[index]"></el-progress>
                </div>
              </div>
            </div>
          </div>
          <!-- 选股标准 -->
          <div class="pbai"
               style="margin-top: 20px">
            <div style="display: flex; align-items: center">
              <div><span style="font-size: 16px; color: rgba(0, 0, 0, 0.85); font-weight: 500">选股标准</span></div>
            </div>
            <div style="display: flex; align-items: center; flex-wrap: wrap; margin-top: 16px">
              <div style="
									margin: 5px;
									width: 100px;
									flex: 1;
									height: 92px;
									background: #fafafa;
									border-radius: 5px;
									text-align: center;
									color: rgba(0, 0, 0, 0.65);
									font-size: 14px;
								">
                <div style="margin-top: 8px; margin-bottom: 4px">选股估值</div>
                <div class="font-fgB"
                     :style="'font-weight: 700; font-size: 32px; line-height: 40px; color:' + colorList[index]">
                  {{ (item[0]['估值要求_分数'] * 1).toFixed(2) }}<span style="font-size: 14px; font-family: PingFang"></span>
                </div>
              </div>
              <!--  -->
              <div style="
									margin: 5px;
									width: 100px;
									height: 92px;
									background: #fafafa;
									flex: 1;
									border-radius: 5px;
									text-align: center;
									color: rgba(0, 0, 0, 0.65);
									font-size: 14px;
								">
                <div style="margin-top: 8px; margin-bottom: 4px">选股盈利</div>
                <div class="font-fgB"
                     :style="'font-weight: 700; font-size: 32px; line-height: 40px; color:' + colorList[index]">
                  {{ (item[0]['盈利要求_分数'] * 1).toFixed(2) }}<span style="font-size: 14px; font-family: PingFang"></span>
                </div>
              </div>
              <!--  -->
              <div style="
									margin: 5px;
									width: 100px;
									height: 92px;
									background: #fafafa;
									border-radius: 5px;
									flex: 1;
									text-align: center;
									color: rgba(0, 0, 0, 0.65);
									font-size: 14px;
								">
                <div style="margin-top: 8px; margin-bottom: 4px">选股增长</div>
                <div class="font-fgB"
                     :style="'font-weight: 700; font-size: 32px; line-height: 40px; color:' + colorList[index]">
                  {{  (item[0]['成长性要求_分数'] * 1).toFixed(2)  }}
                </div>
              </div>
              <!--  -->
            </div>
          </div>
          <!-- 操盘风格 -->
          <div class="pbai"
               style="margin-top: 20px">
            <div style="display: flex; align-items: center">
              <div><span style="font-size: 16px; color: rgba(0, 0, 0, 0.85); font-weight: 500">操盘风格</span></div>
            </div>
            <div style="display: flex; align-items: center; flex-wrap: wrap; margin-top: 16px">
              <div style="
									margin: 5px;
									width: 100px;
									height: 92px;
									background: #fafafa;
									border-radius: 5px;
									flex: 1;
									text-align: center;
									color: rgba(0, 0, 0, 0.65);
									font-size: 14px;
								">
                <div style="margin-top: 8px; margin-bottom: 4px">股票集中度</div>
                <div class="font-fgB"
                     :style="'font-weight: 700; font-size: 32px; line-height: 40px; color:' + colorList[index]">
                  {{ Number(item[0]['股票集中度_分数']).toFixed(2) }}<span style="font-size: 14px; font-family: PingFang">%</span>
                </div>
              </div>
              <!--  -->
              <div style="
									margin: 5px;
									width: 100px;
									height: 92px;
									background: #fafafa;
									flex: 1;
									border-radius: 5px;
									text-align: center;
									color: rgba(0, 0, 0, 0.65);
									font-size: 14px;
								">
                <div style="margin-top: 8px; margin-bottom: 4px">债券集中度</div>
                <div class="font-fgB"
                     :style="'font-weight: 700; font-size: 32px; line-height: 40px; color:' + colorList[index]">
                  {{ Number(item[0]['债券集中度_分数']).toFixed(2) }}<span style="font-size: 14px; font-family: PingFang">%</span>
                </div>
              </div>
              <!--  -->
              <div style="
									margin: 5px;
									width: 100px;
									flex: 1;
									height: 92px;
									background: #fafafa;
									border-radius: 5px;
									text-align: center;
									color: rgba(0, 0, 0, 0.65);
									font-size: 14px;
								">
                <div style="margin-top: 8px; margin-bottom: 4px">券种集中度</div>
                <div class="font-fgB"
                     :style="'font-weight: 700; font-size: 32px; line-height: 40px; color:' + colorList[index]">
                  {{ Number(item[0]['券种集中度_分数']).toFixed(2) }}<span style="font-size: 14px; font-family: PingFang">%</span>
                </div>
              </div>
              <!--  -->
              <!-- <div style='height:30px'></div>
                              <div style='display:flex;align-items: center;width:100%;flex-direction:row;margin-left:5px'>
                                 <div style='width: 54px;height: 30px;background: #4096ff;border-radius: 5px 0 0 5px ;color:white;font-size:12px;display: flex;align-items:center;justify-content:center'>换手率</div> 
                                 <div style='width: 54px;height: 30px;background: #4096ff;border-radius: 0 5px  5px  0;color:white;font-size:12px;display: flex;align-items:center;justify-content:center'>{{item[0]['换手率']}}</div> 
                              </div> -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { ManagerCapability, FundCapabilityRank } from '@/api/pages/tools/compare.js';
export default {
  //import引入的组件需要注入到对象中才能使用
  props: {
    comparetype: {
      type: String,
      default: 'manager' //fund
    },
    id: {
      type: String,
      default: '30159178,30177032'
    },
    type: {
      type: String,
      default: 'bond'
    },
    name: {
      type: String,
      default: '张芊,谭昌杰'
    }
  },
  components: {},
  data () {
    //这里存放数据
    return {
      managersmsg: [],
      colorList: ['#4096ff', '#4096ff', '#7388A9', '#6F80DD'],
      inwidth: window.innerWidth,
      loading: false
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    getdata () {
      Object.assign(this.$data, this.$options.data());
      this.loading = true;
      if (this.comparetype == 'manager') {
        this.getmanagerdata();
      } else {
        this.gefunddata();
      }
    },
    async getmanagerdata () {
      let data = await ManagerCapability({ manager_code: this.id, type: this.type, manager_name: this.name });
      this.loading = false;
      if (data) {
        // //console.log(data)
        // //console.log('mtydafenka')
        this.managersmsg = data.data;
      }
    },
    async gefunddata () {
      let data = await FundCapabilityRank({ fund_code: this.id, type: this.type, fund_name: this.name });
      this.loading = false;
      if (data) {
        //console.log(data)
        //console.log('mtydafenka')
        this.managersmsg = data.data.sort((a, b) => {
          if (this.$route.query.id.split(',').indexOf(a[0].fund_code) > this.$route.query.id.split(',').indexOf(b[0].fund_code)) return 1;
          else return -1;
        });
      }
    },
    for1 (percentage) {
      return percentage + '分';
    },
    async createPrintWord () {
      let data = [];
      let name = this.name.split(',');
      for (let index = 0; index < this.managersmsg.length; index++) {
        let canvas = await this.html2canvas(document.getElementById('bondrank' + index), { scale: 3 });
        let chart = canvas.toDataURL('image/png');
        let width = document.getElementById('bondrank' + index).clientWidth;
        let height = document.getElementById('bondrank' + index).clientHeight;
        data.push(...this.$exportWord.exportTitle(name[index]), ...this.$exportWord.exportChart(chart, { width, height }));
      }
      return [...this.$exportWord.exportFirstTitle('慧捕基打分卡'), ...data];
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () { },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style scoped>
.equityrankscorebox {
}
</style>
<style>
.equityrankscorebox .el-progress-bar__outer {
	height: 6px !important;
}
</style>
