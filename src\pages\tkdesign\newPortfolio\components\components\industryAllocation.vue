<template>
    <div class="plate-wrapper fund-performance-board-wrapper"  v-loading="loading">
        <combinationComponentHeader title="行业配置表现" showMoreBtn @download="exportExcel">
            <template slot="right">
                <el-select v-model="form.industryStandard" placeholder="行业口径" style="margin-right: 12px;" @input="radioChange">
                    <el-option
                        v-for="item in options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                        </el-option>
                </el-select>
                <!-- <el-select v-model="comparisonValue" placeholder="分析对象" style="margin-right: 12px;">
                    <el-option
                        v-for="item in options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                        </el-option>
                </el-select> -->
                <div style="margin-right: 16px;">
                    <FormTimePicker v-model="preset_time" @input="handleFormChange"></FormTimePicker>
                </div>
            </template>
        </combinationComponentHeader>
            <!-- <barChart ref="fund-performance-board-chart-container3" style="flex: 1;margin-bottom: 20px;" ></barChart> -->
            <scatterChart ref="fund-performance-board-chart-container4" class="scatter-chart" ></scatterChart>
    </div>
</template>
<script>
import combinationComponentHeader from './combinationComponentHeader.vue';
import barChart from '../chart/barChart.vue';
import scatterChart from '../chart/scatterChart.vue';
import { industryInfo } from '@/api/pages/tkAnalysis/portfolio.js';
import FormTimePicker from './formTimePicker.vue';
import { filter_to_excel } from "@/utils/exportExcel.js";
export default {
    name:'industryAllocation',
    components:{
        combinationComponentHeader,
        barChart,
        scatterChart,
        FormTimePicker
    },
    data(){
        return {
            form:{
                industryStandard:'sw',
                startDate: this.moment().subtract(1, 'year').format('YYYY-MM-DD'),
                endDate: this.moment().format('YYYY-MM-DD')
            },
            preset_time: {
                radioValue: '1',
                startDate: this.moment().subtract(1, 'year').format('YYYY-MM-DD'),
                endDate: this.moment().format('YYYY-MM-DD')
            },
            options:[{
                label:'申万一级行业',
                value: 'sw'
            },{
                label:'泰康一级行业',
                value: 'tk'
            }],
            loading:true,
            tableHeader:[{
                prop: 'yearqtr',
                label: '时间'
            },{
                prop: 'industryCode',
                label: '行业代码'
            },{
                prop: 'industryName',
                label: '行业名称'
            },{
                prop: 'weight',
                label: '权重'
            },{
                prop: 'industryReturn',
                label: '行业估算收益率'
            }],
            tableData:[],
        }
    },
    methods:{
        handleFormChange(val) {
            this.preset_time = val;
            this.form.startDate = val.startDate;
            this.form.endDate = val.endDate;
            this.getData(this.param);
		},
        radioChange(){
            // this.$refs['valuation-chart2']?.getData()
            this.getData(this.param)
        },
       async getData(param){
            this.loading = true;
            this.param = param;
            let res = await industryInfo({
                ...param,
				...this.form
            })
            this.loading = false;
            console.log("industry::::",res)
            this.tableData = res.data;
            let chartDom4 = this.$refs['fund-performance-board-chart-container4'];
            chartDom4?.getData(res.data || []);
        },
        exportExcel(){
          // 将表头数据进行遍历，生成新的数组list，每个元素包含原表头数据和format字段
          let list = this.tableHeader.map((item) => {
            return {
              ...item,
              format: ''
            };
          });
          // 调用filter_to_excel函数，传入list、表格数据this.tableData和文件名'基金标签'
          filter_to_excel(list, this.tableData, '行业配置表现');
        },

    },
    mounted(){
        // let chartDom3 = this.$refs['fund-performance-board-chart-container3'];
        // chartDom3?.getData();
        // let chartDom4 = this.$refs['fund-performance-board-chart-container4'];
        // chartDom4?.getData();
    },
}
</script>
<style lang="scss" scoped>
.fund-performance-board-wrapper {
    .select-form-wrapper {
        margin-bottom: 16px;
    }
    .content-table-wrapper {
        margin-bottom: 32px;
    }
}
.charts_fill_class{
    height: 125px;
    ::v-deep{
        .echarts{
            height: 100%;

        }
    }
}
.scatter-chart{
    min-height: 500px;
    ::v-deep{
        .echarts{
            height: 100%;

        }
    }
}
</style>