<template>
	<div class="report_template">
		<div style="margin-bottom: 16px">
			<el-button type="primary" @click="addReportTemplate()">新建模板</el-button>
		</div>
		<el-table :data="data" style="width: 100%; min-height: calc(100vh - 339px)">
			<el-table-column
				v-for="item in column"
				:key="item.value"
				:prop="item.value"
				:label="item.label"
				align="gotoleft"
				:min-width="item.width"
			>
				<template slot-scope="{ row }">
					<span v-show="item.value != 'custom_logo' && item.value != 'setting'">{{ row[item.value] }}</span>
					<el-image
						v-show="item.value == 'custom_logo'"
						style="width: 50px; height: 50px"
						:src="'data:image/png;base64,' + row[item.value]"
						:preview-src-list="['data:image/png;base64,' + row[item.value]]"
					>
					</el-image>
					<div v-show="item.value == 'setting' && user_id == row['user_id']">
						<!-- <el-link @click="addReportTemplate(row['template_id'])"
              >复制</el-link
            > -->
						<el-link style="margin-left: 8px" @click="deleteTemplate(row)">删除</el-link>
					</div>
				</template>
			</el-table-column>
		</el-table>
	</div>
</template>

<script>
// 获取报告模板列表
import { deleteReportTemplate } from '@/api/pages/NodeServer.js';
import { getModelList, deleteModel } from '@/api/pages/Tools.js';
import { getUserList } from '@/api/pages/SystemMixed.js';

export default {
	data() {
		return {
			user_id: localStorage.getItem('id'),
			data: [],
			column: [
				{
					label: '模版名称',
					value: 'template_name'
					// width: '200px'
				},
				{
					label: '自定义logo',
					value: 'custom_logo',
					width: '100px'
				},
				{
					label: '模板简介',
					value: 'template_description'
				},
				{
					label: '创建人',
					value: 'user_name'
				},
				{
					label: '是否公开',
					value: 'ispublic'
				},
				{
					label: '模板类型',
					value: 'model_type'
				},
				{
					label: '创建时间',
					value: 'model_time'
				},
				{
					label: '操作',
					value: 'setting',
					width: '100px'
				}
			],
			user_list: []
		};
	},
	methods: {
		// 获取用户数据
		async getUserList() {
			let data = await getUserList();
			if (data?.mtycode == 200) {
				this.user_list = data.data;
			}
			this.getData();
		},
		// 获取数据
		async getData() {
			if (this.user_list.length == 0) {
				this.getUserList();
				return;
			}
			let res = await getModelList({
				type: 'equity',
				ismanager: 'true',
				flag: 'print_report',
				title: '',
				source: 'print_report'
			});
			if (res.mtycode == 200) {
				let data = res.data;
				let list = [];
				for (const key in data) {
					if (key != 'user_list') {
						data[key].map((item) => {
							let index = list.findIndex((obj) => {
								return obj.model_id == item.model_id;
							});
							item = { ...item, model_args: JSON.parse(item.model_args) };
							if (index == -1) {
								list.push({
									...item,
									model_id: item.model_id,
									template_id: item.model_args?.template_id,
									template_name: item.model_name,
									custom_logo: item.model_args?.custom_logo,
									template_description: item.model_args?.template_description,
									model_time: this.moment(item.model_time).format('YYYY-MM-DD'),
									ispublic: item.ispublic == 'False' ? '不公开' : '公开',
									model_type: key.includes('self') ? '个人模板' : key.includes('owl') ? '慧捕基模板' : '公开模板',
									user_name: this.user_list.find((obj) => {
										return obj.id == item.user_id;
									}).username
								});
							} else {
								if (list[index].model_type !== '个人模板') {
									list[index] = {
										...item,
										model_id: item.model_id,
										template_id: item.model_args?.template_id,
										template_name: item.model_name,
										custom_logo: item.model_args?.custom_logo,
										template_description: item.model_args?.template_description,
										model_time: this.moment(item.model_time).format('YYYY-MM-DD'),
										ispublic: item.ispublic == 'False' ? '不公开' : '公开',
										model_type: key.includes('self') ? '个人模板' : key.includes('owl') ? '慧捕基模板' : '公开模板',
										user_name: this.user_list.find((obj) => {
											return obj.id == item.user_id;
										}).username
									};
								}
							}
						});
					}
				}

				this.data = list;
				console.log(this.data);
			}
		},
		// 删除模板
		async deleteTemplate(val) {
			await deleteReportTemplate({ template_id: val?.template_id, user_id });
			await deleteModel({
				model_id: val.model_id,
				model_name: val.model_name,
				type: 'equity',
				ismanager: 'true',
				flag: 'print_report'
			});
		},
		// 新建模板
		addReportTemplate(id) {
			if (id) {
				this.$router.push('/addReportTemplate?id=' + id);
			} else {
				this.$router.push('/addReportTemplate');
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.report_template {
	margin: 0 12px;
}
</style>
