// src/components/Dialog/index.js
import Vue from 'vue';
import Dialog from './login.vue';

// 使用 Vue.extend() 创建 Dialog 的构造器
const DialogConstructor = Vue.extend(Dialog);

const dialog = function (options = {}) {
	// 创建 dialog 实例，通过构造函数传参，
	// 并调用 Vue 实例上的 $mount() 手动挂载
	const dialogInstance = new DialogConstructor({
		data: options
	}).$mount();

	// 手动把真实 dom 挂到 html 的 body 上
	document.body.appendChild(dialogInstance.$el);

	return dialogInstance;
};

// 导出包装好的 dialog 方法
export default dialog;
