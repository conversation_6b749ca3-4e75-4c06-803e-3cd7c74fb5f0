<template>
	<QuickTimePicker v-model="value" @change="handleFormChange"></QuickTimePicker>
</template>
<script>
import QuickTimePicker from '../../../marketAnalysis/component/QuickTimePicker.vue';
const dayjs = require('dayjs');
export default {
	name: 'FormTimePicker',
	components: {
		QuickTimePicker
	},
	props: {
        value: {
			type: Object,
			default: () => {
				return {
					radioValue: '1',
					startDate: dayjs().subtract(1, 'year').format('YYYY-MM-DD'),
					endDate: dayjs().format('YYYY-MM-DD')
				};
			}
		},
	},
	data() {
		return {
            preset_time: {
				radioValue: '1'
			},
        };
	},

	methods: {
		//原生ladio选中时会触发
		handleFormChange(value) {

            if(value.radioValue !== 'custom'){
                value.startDate = dayjs().subtract(Number(value.radioValue), 'year').format('YYYY-MM-DD');
                value.endDate = dayjs().format('YYYY-MM-DD');
            }
            console.log('value:::',value)

			this.$emit('input', value);
		},
	}
};
</script>
<style lang="scss" scoped>

</style>
