<template>
	<div>
		<analysis-card-title title="PB-ROE估值" @downloadExcel="exportExcel">
			<el-select v-model="model" placeholder="" @change="filterData">
				<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
			</el-select>
		</analysis-card-title>
		<div v-loading="loading">
			<el-table
				:data="pbroe"
				:cell-style="elcellstyle"
				style="width: 99%"
				height="500px"
				class="table pb_roe_value_h50"
				ref="multipleTable"
				header-cell-class-name="table-header"
			>
				<el-table-column v-for="item in column" :key="item.value" align="gotoleft" :prop="item.value" :label="item.label" sortable>
					<template #header>
						<long-table-popover-chart
							v-if="item.popover"
							:data="formatTableData()"
							date_key="name"
							:data_key="item.value"
							:show_name="item.label"
						>
							<span>{{ item.label }}</span>
						</long-table-popover-chart>
						<span v-else>{{ item.label }}</span>
					</template>
					<template slot-scope="{ row }">
						<span>{{ item.format ? item.format(row[item.value]) : row[item.value] }}</span>
					</template>
				</el-table-column>
				<template slot="empty">
					<el-empty image-size="160"></el-empty>
				</template>
			</el-table>
		</div>
	</div>
</template>

<script>
import { exportTitle, exportTable, downloadWord } from '@/utils/exportWord.js';
import { filter_json_to_excel } from '@/utils/exportExcel.js';

// PB-ROE估值
import { getStocksValuationInfo } from '@/api/pages/Analysis.js';
export default {
	name: 'PBROEvaluation',
	data() {
		return {
			loading: true,
			pbroe: [],
			info: {},
			column: [
				{
					label: '名称',
					value: 'name',
					popover: false
				},
				{
					label: '行业',
					value: 'swname',
					popover: false
				},
				{
					label: 'pb',
					value: 'pb',
					format: this.fix3,
					popover: true
				},
				{
					label: '合理pb',
					value: 'balance_pb',
					format: this.fix3,
					popover: true
				},
				{
					label: '当前ROE',
					value: 'roe',
					format: this.fix3,
					popover: true
				},
				{
					label: '当前 PE(TTM)',
					value: 'pe',
					format: this.fix3,
					popover: true
				}
			],
			options: [],
			model: '',
			all_data: []
		};
	},
	methods: {
		// 获取PB-ROE估值分析数据
		async getStocksValuationInfo() {
			let data = await getStocksValuationInfo({
				flag: this.info.flag,
				code: this.info.code,
				type: this.info.type,
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			this.loading = false;
			if (data?.mtycode == 200) {
				this.options = Array.from(new Set(data?.data.map((v) => v.yearqtr)))
					.sort((a, b) => {
						return this.moment(this.moment(a, 'YYYY QQ').format()).isAfter(this.moment(b, 'YYYY QQ').format()) ? -1 : 1;
					})
					.map((item) => {
						return { label: item, value: item };
					});
				this.model = this.options?.[0].value;
				this.all_data = data?.data;
				this.filterData();
			} else {
				this.hideLoading();
			}
		},
		// 获取数据
		getData(info) {
			if (info.type != 'hkequity' && info.type != 'equityhk') {
				if (
					this.column.every((item) => {
						return item.value != 'roeEst' && item.value != 'peFtm';
					})
				) {
					this.column.push(
						{
							label: '预期ROE',
							value: 'roeEst',
							format: this.fix3,
							popover: true
						},
						{
							label: '预期年后 PE',
							value: 'peFtm',
							format: this.fix3,
							popover: true
						}
					);
				}
			}
			this.info = info;
			this.getStocksValuationInfo();
		},
		filterData() {
			this.pbroe = this.all_data.filter((v) => v.yearqtr == this.model);
		},
		hideLoading() {
			this.loading = false;
		},
		// 动态设置表格样式
		elcellstyle({ row, column, rowIndex, columnIndex }) {
			if (row.pb >= row.balance_pb) {
				if (columnIndex == 2 || columnIndex == 3) return 'color: #E85D2D;';
				else return '';
			} else {
				if (columnIndex == 2 || columnIndex == 3) return 'color: #18C2A0;';
				else return '';
			}
		},
		formatTableData() {
			let data = [];
			this.pbroe.map((item) => {
				let obj = { ...item };
				for (const key in item) {
					let format = this.column.find((obj) => {
						return obj.value == key;
					})?.format;
					if (format) {
						let val = format(item[key]);
						obj[key] = typeof val == 'string' ? (val.includes('%') ? val?.split('%')?.[0] * 1 : !isNaN(val) ? val * 1 : val) : val;
					}
				}
				data.push(obj);
			});
			return data;
		},
		fix3(value) {
			if (value == '--') {
				return value;
			} else return parseInt(value * 1000) / 1000;
		},
		exportExcel() {
			let list = [
				{
					label: '名称',
					fill: 'header',
					value: 'name'
				},
				{
					label: '行业',
					fill: 'header',
					value: 'swname'
				},
				{
					label: 'pb',
					value: 'pb',
					format: 'fix3'
				},
				{
					label: '合理 pb',
					value: 'balancePb',
					format: 'fix3'
				}
			];
			let noHk = [
				{
					label: '当前 ROE',
					value: 'roe',
					format: 'fix3'
				},
				{
					label: '预期 ROE',
					value: 'roe_est',
					format: 'fix3'
				},
				{
					label: '当前 PE(TTM)',
					value: 'pe',
					format: 'fix3'
				},
				{
					label: '预期年后 PE',
					value: 'peFtm',
					format: 'fix3'
				}
			];
			let hk = [
				{
					label: '当前 ROE',
					value: 'roe',
					format: 'fix3'
				},
				{
					label: '当前 PE(TTM)',
					value: 'pe',
					format: 'fix3'
				}
			];
			if (this.info.type != 'hkequity' && this.info.type != 'equityhk') {
				list.push(...noHk);
			} else {
				list.push(...hk);
			}
			filter_json_to_excel(list, this.pbroe, 'PB-ROE估值');
		},
		createPrintWord() {
			let list = [
				{
					label: '名称',
					fill: 'header',
					value: 'name'
				},
				{
					label: '行业',
					fill: 'header',
					value: 'swname'
				},
				{
					label: 'pb',
					value: 'pb',
					format: 'fix3'
				},
				{
					label: '合理 pb',
					value: 'balancePb',
					format: 'fix3'
				}
			];
			let noHk = [
				{
					label: '当前 ROE',
					value: 'roe',
					format: 'fix3'
				},
				{
					label: '预期 ROE',
					value: 'roe_est',
					format: 'fix3'
				},
				{
					label: '当前 PE(TTM)',
					value: 'pe',
					format: 'fix3'
				},
				{
					label: '预期年后 PE',
					value: 'peFtm',
					format: 'fix3'
				}
			];
			let hk = [
				{
					label: '当前 ROE',
					value: 'roe',
					format: 'fix3'
				},
				{
					label: '当前 PE(TTM)',
					value: 'pe',
					format: 'fix3'
				}
			];
			if (this.info.type != 'hkequity' && this.info.type != 'equityhk') {
				list.push(...noHk);
			} else {
				list.push(...hk);
			}
			if (this.pbroe.length) {
				return [
					...exportTitle('PB-ROE估值'),
					...exportTable(
						list,
						this.pbroe
							.sort((a, b) => {
								return b.weight - a.weight;
							})
							.slice(0, 10),
						'',
						true
					)
				];
			} else {
				return [];
			}
		}
	}
};
</script>

<style></style>
