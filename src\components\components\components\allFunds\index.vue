<template>
	<div class="chart_one" v-loading="allFundLoading" v-show="show">
		<div style="display: flex; justify-content: space-between; align-items: center">
			<div class="title">全部基金</div>
			<div style="display: flex; align-items: center">
				<div>基金类型：</div>
				<el-select v-model="type" @change="filterData" placeholder="请选择">
					<el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>
			</div>
		</div>
		<div style="margin-top: 24px">
			<el-table class="fund-analysis-table" :data="allFundDataSort" max-height="400px" @sort-change="sortAllFund">
				<el-table-column
					v-for="item in column"
					:key="item.value"
					align="gotoleft"
					:prop="item.value"
					:label="item.label"
					:width="item.width"
				></el-table-column>
				<template slot="empty">
					<el-empty image-size="160"></el-empty>
				</template>
			</el-table>
		</div>
	</div>
</template>

<script>
// 所属全部基金
export default {
	name: 'allFunds',
	data() {
		return {
			show: true,
			allFundLoading: true,
			allFundData: [],
			typeList: [],
			type: '',
			allFundDataSort: [],
			column: [
				{
					label: '基金代码',
					value: 'code',
					width: '120px'
				},
				{
					label: '基金名称',
					value: 'name_x',
					width: '350px'
				},
				{
					label: '基金类型',
					value: 'type'
				},
				{
					label: '日涨跌幅',
					value: 'nav'
				},
				{
					label: '收益率',
					value: 'cum_return'
				},
				{
					label: '规模(亿)',
					value: 'netasset'
				},
				{
					label: '基金经理',
					value: 'manager_name'
				},
				{
					label: '申购赎回',
					value: 'redeemtype'
				},
				{
					label: '成立日期',
					value: 'founddate'
				}
			]
		};
	},
	methods: {
		// 获取数据
		getData(data, info) {
			this.allFundLoading = false;
			if (data?.length) {
				this.filterType(data);
				this.allFundData = data.slice().map((item) => {
					return {
						...item,
						type: this.FUNC.textConverter(this.COMMON.fundType_zh_en, item.type, 'en', 'zh'),
						nav: !isNaN(parseFloat(item.nav)) ? (item.nav * 100).toFixed(2) + '%' : item.nav,
						cum_return: !isNaN(parseFloat(item.cum_return)) ? (item.cum_return * 100).toFixed(2) + '%' : item.cum_return,
						netasset: !isNaN(parseFloat(item.netasset)) ? (item.netasset / 10 ** 8).toFixed(2) : item.netasset,
						redeemtype: `${item.applyingtype}${item.applyingtype && item.redeemtype ? ',' : ''}${item.redeemtype}`
					};
				});
				this.filterData();
			}
		},
		hideLoading() {
			this.show = false;
		},
		// 过滤出type列表
		filterType(data) {
			let arr = [];
			data.map((obj) => {
				if (obj.type !== '--') {
					arr.push({ value: obj.type, label: this.FUNC.textConverter(this.COMMON.fundType_zh_en, obj.type, 'en', 'zh') });
				}
			});
			let deWeightThree = () => {
				let map = new Map();
				for (let item of arr) {
					if (!map.has(item.value)) {
						map.set(item.value, item);
					}
				}
				return [...map.values()];
			};
			this.typeList = deWeightThree();
			this.type = this.typeList[0].value;
		},
		// 根据类型筛选基金
		filterData() {
			this.allFundDataSort = this.allFundData.filter((item) => {
				return item.type == this.type;
			});
		},
		// 所有基金排序
		sortAllFund(sortVal) {
			this.allFundDataSort =
				sortVal.order && sortVal.prop ? this.sortChange(sortVal, this.allFundDataSort.slice()) : this.allFundData.slice();
		},
		// 排序
		sortChange(sortVal, list) {
			let order = sortVal.order;
			let key = sortVal.prop;
			if (order == 'ascending') {
				let haveValList = list.filter((item) => !isNaN(parseFloat(item[key])));
				let noValList = list.filter((item) => isNaN(parseFloat(item[key])));
				haveValList.sort((a, b) => a[key] - b[key]);
				return [...haveValList, ...noValList];
			} else if (order == 'descending') {
				let haveValList = list.filter((item) => !isNaN(parseFloat(item[key])));
				let noValList = list.filter((item) => isNaN(parseFloat(item[key])));
				haveValList.sort((a, b) => b[key] - a[key]);
				return [...haveValList, ...noValList];
			}
		}
	}
};
</script>

<style></style>
