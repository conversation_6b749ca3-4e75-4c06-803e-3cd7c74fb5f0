<!--  -->
<template>
  <div v-loading="loading"
       class="buyselldmod">
    <div style="display: flex; align-items: center">
      <div class="TitltCompare">买入模式</div>
    </div>
    <sTable :data="stock_hold"
            typeFlag="1"></sTable>
    <div style="height: 16px"></div>

    <div style="display: flex; align-items: center">
      <div class="TitltCompare">卖出模式</div>
    </div>
    <sTable :data="stock_hold2"
            typeFlag="1"></sTable>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import sTable from '../SelfTable.vue';
import { ManagerBuySellMod, FundBuySell } from '@/api/pages/tools/compare.js';
export default {
  //import引入的组件需要注入到对象中才能使用
  components: { sTable },
  props: {
    comparetype: {
      type: String,
      default: 'manager' //fund
    },
    id: {
      type: String,
      default: '30189741,30441407'
    },
    type: {
      type: String,
      default: 'equity'
    },
    name: {
      type: String,
      default: '萧楠,胡昕炜'
    }
  },
  data () {
    //这里存放数据
    return {
      stock_holdcolumns: [],
      stock_holdcolumnsfund: [],
      stock_hold: [],
      stock_holdcolumns2: [],
      stock_holdcolumnsfund2: [],
      stock_hold2: [],
      loading: false
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  filters: {
    fix3 (value) {
      if (value == '--' || value == null || value == '' || value == 'nan') {
        return '--';
      } else {
        return (value * 100).toFixed(2) + '%';
      }
    },
    fix2 (value) {
      return Number(value).toFixed(2) + '亿';
    }
  },
  //方法集合
  methods: {
    getdata () {
      Object.assign(this.$data, this.$options.data());
      this.loading = true;
      if (this.comparetype == 'manager') {
        this.getmanager();
      } else {
        this.gefunddata();
      }
    },
    async getmanager () {
      this.stock_hold = [];
      this.stock_hold2 = [];
      this.stock_holdcolumns = [
        {
          dataIndex: 'description1',
          key: 'description1',
          title: '买入模式'
        }
      ];
      this.stock_holdcolumns2 = [
        {
          dataIndex: 'description2',
          key: 'description2',
          title: '卖出模式'
        }
      ];
      for (let i = 0; i < this.name.split(',').length; i++) {
        this.stock_holdcolumns.push({
          dataIndex: this.name.split(',')[i],
          key: this.name.split(',')[i],
          title: this.name.split(',')[i],
          scopedSlots: {
            customRender: 'name'
          }
        });
        this.stock_holdcolumns2.push({
          dataIndex: this.name.split(',')[i],
          key: this.name.split(',')[i],
          title: this.name.split(',')[i],
          scopedSlots: {
            customRender: 'name'
          }
        });
      }
      let buylist = [];
      let sellist = [];
      let tempbuy = [];
      let tempsell = [];
      let data = await ManagerBuySellMod({ manager_code: this.id, type: this.type, manager_name: this.name });
      this.loading = false;

      if (data) {
        //console.log(data)
        //console.log('hereindustry')
        //  let temp
        for (let i = 0; i < data.data.buy.length; i++) {
          for (let j = 0; j < data.data.buy[i].length; j++) {
            if (buylist.indexOf(data.data.buy[i][j].name) < 0) {
              buylist.push(data.data.buy[i][j].name);
              tempbuy.push({ description1: data.data.buy[i][j].name });
            }
          }
        }
        for (let i = 0; i < data.data.buy.length; i++) {
          for (let j = 0; j < data.data.buy[i].length; j++) {
            if (buylist.indexOf(data.data.buy[i][j].name) >= 0) {
              if (tempbuy[buylist.indexOf(data.data.buy[i][j].name)][data.data.buy[i][j].manager_name]) {
                tempbuy[buylist.indexOf(data.data.buy[i][j].name)][data.data.buy[i][j].manager_name] += data.data.buy[i][j].freq;
              } else {
                tempbuy[buylist.indexOf(data.data.buy[i][j].name)][data.data.buy[i][j].manager_name] = data.data.buy[i][j].freq;
              }
            }
          }
        }
        this.stock_hold = [];
        for (let i = 0; i < tempbuy.length; i++) {
          this.stock_hold[i] = [];
          this.stock_hold[i].push(tempbuy[i].description1);
          for (let j = 0; j < this.$route.query.name.split(',').length; j++) {
            this.FUNC.isEmpty(tempbuy[i][this.$route.query.name.split(',')[j]])
              ? this.stock_hold[i].push((Number(tempbuy[i][this.$route.query.name.split(',')[j]]) * 100).toFixed(2) + '%')
              : this.stock_hold[i].push('--');
          }
        }
        //console.log(this.stock_hold)
        for (let i = 0; i < data.data.sell.length; i++) {
          for (let j = 0; j < data.data.sell[i].length; j++) {
            if (sellist.indexOf(data.data.sell[i][j].name) < 0) {
              sellist.push(data.data.sell[i][j].name);
              tempsell.push({ description2: data.data.sell[i][j].name });
            }
          }
        }
        for (let i = 0; i < data.data.sell.length; i++) {
          for (let j = 0; j < data.data.sell[i].length; j++) {
            if (sellist.indexOf(data.data.sell[i][j].name) >= 0) {
              if (tempsell[sellist.indexOf(data.data.sell[i][j].name)][data.data.sell[i][j].manager_name]) {
                tempsell[sellist.indexOf(data.data.sell[i][j].name)][data.data.sell[i][j].manager_name] += data.data.sell[i][j].freq;
              } else {
                tempsell[sellist.indexOf(data.data.sell[i][j].name)][data.data.sell[i][j].manager_name] = data.data.sell[i][j].freq;
              }
            }
          }
        }
        this.stock_hold2 = [];

        for (let i = 0; i < tempsell.length; i++) {
          this.stock_hold2[i] = [];
          this.stock_hold2[i].push(tempsell[i].description2);
          for (let j = 0; j < this.$route.query.name.split(',').length; j++) {
            this.FUNC.isEmpty(tempsell[i][this.$route.query.name.split(',')[j]])
              ? this.stock_hold2[i].push((Number(tempsell[i][this.$route.query.name.split(',')[j]]) * 100).toFixed(2) + '%')
              : this.stock_hold2[i].push('--');
          }
        }
      }
      //  //console.log(this.arrlist)
    },
    async gefunddata () {
      this.stock_hold = [];
      this.stock_hold2 = [];
      this.stock_holdcolumnsfund = [
        {
          dataIndex: 'description1',
          key: 'description1',
          title: '买入模式'
        }
      ];
      this.stock_holdcolumnsfund2 = [
        {
          dataIndex: 'description2',
          key: 'description2',
          title: '卖出模式'
        }
      ];
      for (let i = 0; i < this.name.split(',').length; i++) {
        this.stock_holdcolumnsfund.push({
          dataIndex: this.name.split(',')[i],
          key: this.name.split(',')[i],
          title: this.name.split(',')[i],
          scopedSlots: {
            customRender: 'name'
          }
        });
        this.stock_holdcolumnsfund2.push({
          dataIndex: this.name.split(',')[i],
          key: this.name.split(',')[i],
          title: this.name.split(',')[i],
          scopedSlots: {
            customRender: 'name'
          }
        });
      }
      let buylist = [];
      let sellist = [];
      let tempbuy = [];
      let tempsell = [];
      let data = await FundBuySell({ fund_code: this.id, type: this.type, fund_name: this.name });
      this.loading = false;

      if (data) {
        data.data.buy.sort((a, b) => {
          if (this.$route.query.id.split(',').indexOf(a[0].code) > this.$route.query.id.split(',').indexOf(b[0].code)) return 1;
          else return -1;
        });
        data.data.sell.sort((a, b) => {
          if (this.$route.query.id.split(',').indexOf(a[0].code) > this.$route.query.id.split(',').indexOf(b[0].code)) return 1;
          else return -1;
        });
        //console.log(data)
        //console.log('hereindustry')
        //  let temp
        for (let i = 0; i < data.data.buy.length; i++) {
          for (let j = 0; j < data.data.buy[i].length; j++) {
            if (buylist.indexOf(data.data.buy[i][j].name) < 0) {
              buylist.push(data.data.buy[i][j].name);
              tempbuy.push({ description1: data.data.buy[i][j].name });
            }
          }
        }
        for (let i = 0; i < data.data.buy.length; i++) {
          for (let j = 0; j < data.data.buy[i].length; j++) {
            if (buylist.indexOf(data.data.buy[i][j].name) >= 0) {
              if (tempbuy[buylist.indexOf(data.data.buy[i][j].name)][data.data.buy[i][j].fund_name]) {
                tempbuy[buylist.indexOf(data.data.buy[i][j].name)][data.data.buy[i][j].fund_name] += data.data.buy[i][j].freq;
              } else {
                tempbuy[buylist.indexOf(data.data.buy[i][j].name)][data.data.buy[i][j].fund_name] = data.data.buy[i][j].freq;
              }
            }
          }
        }
        this.stock_hold = [];
        for (let i = 0; i < tempbuy.length; i++) {
          this.stock_hold[i] = [];
          this.stock_hold[i].push(tempbuy[i].description1);
          for (let j = 0; j < this.$route.query.name.split(',').length; j++) {
            this.FUNC.isEmpty(tempbuy[i][this.$route.query.name.split(',')[j]])
              ? this.stock_hold[i].push((Number(tempbuy[i][this.$route.query.name.split(',')[j]]) * 100).toFixed(2) + '%')
              : this.stock_hold[i].push('--');
          }
        }
        // sell
        for (let i = 0; i < data.data.sell.length; i++) {
          for (let j = 0; j < data.data.sell[i].length; j++) {
            if (sellist.indexOf(data.data.sell[i][j].name) < 0) {
              sellist.push(data.data.sell[i][j].name);
              tempsell.push({ description2: data.data.sell[i][j].name });
            }
          }
        }
        for (let i = 0; i < data.data.sell.length; i++) {
          for (let j = 0; j < data.data.sell[i].length; j++) {
            if (sellist.indexOf(data.data.sell[i][j].name) >= 0) {
              if (tempsell[sellist.indexOf(data.data.sell[i][j].name)][data.data.sell[i][j].fund_name]) {
                tempsell[sellist.indexOf(data.data.sell[i][j].name)][data.data.sell[i][j].fund_name] += data.data.sell[i][j].freq;
              } else {
                tempsell[sellist.indexOf(data.data.sell[i][j].name)][data.data.sell[i][j].fund_name] = data.data.sell[i][j].freq;
              }
            }
          }
        }
        this.stock_hold2 = [];

        for (let i = 0; i < tempsell.length; i++) {
          this.stock_hold2[i] = [];
          this.stock_hold2[i].push(tempsell[i].description2);
          for (let j = 0; j < this.$route.query.name.split(',').length; j++) {
            this.FUNC.isEmpty(tempsell[i][this.$route.query.name.split(',')[j]])
              ? this.stock_hold2[i].push((Number(tempsell[i][this.$route.query.name.split(',')[j]]) * 100).toFixed(2) + '%')
              : this.stock_hold2[i].push('--');
          }
        }
      }
    },
    createPrintWord () {
      let name = this.name.split(',');
      let data1 = [['', ...name], ...this.stock_hold];
      let data2 = [['', ...name], ...this.stock_hold2];
      return [
        ...this.$exportWord.exportTitle('买入模式'),
        ...this.$exportWord.exportCompareTable(data1, [], true),
        ...this.$exportWord.exportTitle('卖出模式'),
        ...this.$exportWord.exportCompareTable(data2, [], true)
      ];
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () { },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
</style>
