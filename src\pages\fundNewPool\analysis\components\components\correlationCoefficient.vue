<template>
  <div class="chart_one"
       v-show="show">
    <div v-loading="loading">
      <div class="card_header flex_between">
        <div class="title">相关系数</div>
        <div class="flex_start">
          <el-select v-model="active_fund"
                     multiple
                     collapse-tags
                     placeholder="请选择标的查看">
            <el-option v-for="item in fund_list"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"></el-option>
          </el-select>
          <el-button class="ml-8"
                     type="primary"
                     @click="submit">确认</el-button>
        </div>
      </div>
      <div :id="data_type + 'container'"
           class="correlationCoefficient"
           style="width: 100%; min-width: 1000px; max-height: 600px; overflow: auto">
        <!-- <table>
					<tr class="correlationCoefficient_tr" style="position: sticky; top: 0; background: #ffffff">
						<th v-for="(item, index) in headerList" :key="'th' + index" :style="index == 0 ? 'position: sticky; left: 0' : ''">
							{{ item.label }}
						</th>
					</tr>
					<tr class="correlationCoefficient_tr" v-for="(item, index) in data" :key="'tr' + index">
						<td
							v-for="(obj, i) in headerList"
							:key="'td' + i"
							:style="`${cellStyle(item[obj.value])};${i == 0 ? 'position: sticky; left: 0;background:#ffffff' : ''}`"
						>
							{{ item[obj.value] }}
						</td>
					</tr>
        </table>-->
      </div>
      <div v-if="headerList.length == 0">
        <el-empty :description="show ? '数据太多，无法显示，请手动选择标的查看' : '暂无数据'"></el-empty>
      </div>
    </div>
  </div>
</template>

<script>
// 相关系数
import { getFofReturnRelevat } from "@/api/pages/tools/pool.js";
import { filter_json_to_excel } from "@/utils/exportExcel.js";
export default {
  name: "correlationCoefficient",
  data () {
    return {
      ismanager: false,
      headerList: [],
      tableData: [],
      data: [],
      show: true,
      loading: true,
      list: [],
      flage: true,
      info: {},
      fund_list: [],
      active_fund: []
    };
  },
  props: {
    data_type: {
      type: String,
      default: "fund"
    },
  },
  methods: {
    // 获取数据
    async getData (info) {
      console.log(info);
      this.info = info;
      this.loading = true;
      this.fund_list = this.info.code_list.map(item => {
        return {
          label: item.name,
          value: item.code
        };
      });
      if (this.info.code_list.length > 200) {
        this.loading = false;
        return;
      }
      this.active_fund = this.info["code_list"].map(v => v.code);
      let data = await getFofReturnRelevat({
        ids: [
          ...this.info["code_list"].map(item => {
            return {
              code: item.code,
              type: this.data_type == 'fund' ? this.ismanager ? 'manager' : 'fund' : this.data_type
            };
          })
        ],
        id: this.info.code,
        flag: '5',
        ismanager: this.ismanager,
        type: this.$route.query?.type
      });
      if (data?.mtycode == 200) {
        let list = [];
        for (const key in data?.data?.[0]) {
          list.push(key);
        }
        this.formatData(data?.data, list);
        this.setDom();
      }
    },
    submit () {
      if (this.active_fund.length != 0) {
        this.getFofReturnRelevat();
      } else {
        this.$message.warning("至少选择一个查看");
      }
    },
    async getFofReturnRelevat () {
      this.loading = true;
      let data = await getFofReturnRelevat({
        ids: [
          ...this.active_fund.map(item => {
            return {
              code: item,
              type: this.data_type == 'fund' ? this.ismanager ? 'manager' : 'fund' : this.data_type
            };
          })
        ],
        id: this.info.code,
        flag: 5,
        ismanager: this.ismanager,
        type: this.$route.query?.type
      });
      if (data?.mtycode == 200) {
        let list = [];
        for (const key in data?.data?.[0]) {
          list.push(key);
        }
        this.formatData(data?.data, list);
        this.setDom();
      }
    },
    setDom () {
      // 创建 DocumentFragment
      const fragment = document.createDocumentFragment();

      // 创建 <table> 元素
      const tableElement = document.createElement("table");

      // 创建表头行
      const headerRowElement = document.createElement("tr");
      headerRowElement.classList.add("correlationCoefficient_tr");
      headerRowElement.style.position = "sticky";
      headerRowElement.style.top = "0";
      headerRowElement.style.background = "#ffffff";

      // 添加表头单元格
      for (let index = 0; index < this.headerList.length; index++) {
        const item = this.headerList[index];
        const thElement = document.createElement("th");
        thElement.textContent = item.label;

        if (index === 0) {
          thElement.style.position = "sticky";
          thElement.style.left = "0";
        }

        headerRowElement.appendChild(thElement);
      }

      // 将表头行添加到 <table> 中
      tableElement.appendChild(headerRowElement);

      // 创建数据行
      for (let index = 0; index < this.data.length; index++) {
        const item = this.data[index];
        const dataRowElement = document.createElement("tr");
        dataRowElement.classList.add("correlationCoefficient_tr");

        // 添加数据单元格
        for (let i = 0; i < this.headerList.length; i++) {
          const obj = this.headerList[i];
          const tdElement = document.createElement("td");
          tdElement.style.cssText = `${this.cellStyle(item[obj.value])};${i === 0 ? "position: sticky; left: 0;background:#ffffff" : ""
            }`;
          tdElement.textContent = item[obj.value];
          dataRowElement.appendChild(tdElement);
        }

        // 将数据行添加到 <table> 中
        tableElement.appendChild(dataRowElement);
      }

      // 将 <table> 元素添加到 DocumentFragment 中
      fragment.appendChild(tableElement);

      // 将 DocumentFragment 插入到 DOM 中的目标容器
      const containerElement = document.getElementById(
        this.data_type + "container"
      );
      containerElement.innerHTML = "";
      containerElement.appendChild(fragment);
    },
    formatData (data, list) {
      this.loading = false;
      if (list?.length) {
        this.list = list;
      } else {
        this.showFlag = false;
      }
      let headerLst = [];
      if (!data?.length) {
        return;
      }
      if (data?.[0]) {
        let obj = data?.[0];
        for (const key in obj) {
          headerLst.push({ label: key, value: key });
        }
        headerLst.unshift({
          label: "",
          value: "name"
        });
      }
      this.headerList = headerLst;
      this.data = data.map(item => {
        let name = "";
        for (const key in item) {
          if (item[key] == 1) {
            name = key;
          }
          item[key] = item[key].toFixed(4);
        }
        return {
          ...item,
          name
        };
      });
    },
    hideLoading () {
      this.show = false;
    },
    cellStyle (value) {
      if (value <= 1 && value > 0.75) {
        return "background:rgba(247, 101, 96, 1)";
      } else if (value <= 0.75 && value > 0.5) {
        return "background:rgba(247, 101, 96, 0.8)";
      } else if (value <= 0.5 && value > 0.25) {
        return "background:rgba(247, 101, 96, 0.6)";
      } else if (value <= 0.25 && value > 0) {
        return "background:rgba(247, 101, 96, 0.4)";
      } else if (value <= 0 && value > -0.25) {
        return "background:rgba(35, 195, 67, 0.4)";
      } else if (value <= -0.25 && value > -0.5) {
        return "background:rgba(35, 195, 67, 0.6)";
      } else if (value <= -0.5 && value > -0.75) {
        return "background:rgba(35, 195, 67, 0.8)";
      } else if (value <= -0.75 && value > -1) {
        return "background:rgba(35, 195, 67, 1)";
      } else {
        return "";
      }
    },
    exportExcel () {
      let list = [];
      let obj = {};
      this.headerList.map(item => {
        obj[item.label] = item.value;
        list.push(item);
      });
      let data = this.data.map((item, index) => {
        return {
          ...item,
          name: this.headerList[index + 1].value
        };
      });
      filter_json_to_excel(list, data, "相关系数");
    },
    createPrintWord () {
      let list = [];
      let obj = {};
      this.headerList.map(item => {
        obj[item.label] = item.value;
        list.push(item);
      });
      let data = this.data.map((item, index) => {
        return {
          ...item,
          name: this.headerList[index + 1].value
        };
      });
      return [];
      // return [...this.$exportWord.exportTitle('相关系数'), ...this.$exportWord.exportTable(list, data, '', true)];
    }
  },
  mounted () {
    this.ismanager = String(this.$route.query.ismanager) == 'true' ? true : false
  },
};
</script>

<style lang="scss" scoped>
.correlationCoefficient {
	::v-deep table {
		width: 100%;
	}

	::v-deep .correlationCoefficient_tr {
		width: 100%;
		// overflow-x: scroll;
		display: flex;
		justify-content: space-around;
		height: 32px;
		align-items: center;
		th {
			flex: 1;
			min-width: 240px;
			height: 100%;
			text-align: center;
			font-family: 'PingFang';
			font-style: normal;
			font-weight: 400;
			font-size: 14px;
			line-height: 32px;
			color: rgba(0, 0, 0, 0.65);
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
		td {
			flex: 1;
			min-width: 240px;
			height: 100%;
			text-align: center;
			font-family: 'Helvetica Neue';
			font-style: normal;
			font-weight: 400;
			font-size: 14px;
			line-height: 32px;
			color: rgba(0, 0, 0, 0.65);
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
	}
}
</style>
