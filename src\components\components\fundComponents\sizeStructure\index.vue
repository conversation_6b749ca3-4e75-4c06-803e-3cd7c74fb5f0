<template>
	<div id="sizeStructure">
		<div v-loading="loadyeji2">
			<analysis-card-title title="规模及持有人结构" image_id="sizeStructure"></analysis-card-title>
			<div class="charts_fill_class">
				<v-chart ref="sizeStructure" style="width: 100%; height: 404px" autoresize :options="changingOption" />
			</div>
			<div class="mt-20 flex_start">
				<div style="width: 806px" class="mr-20">
					<div class="title mb-8">{{ start_date }}以来数据</div>
					<div>
						<el-table :data="avgData" border style="width: 100%">
							<el-table-column prop="individual" label="个人投资者平均持有比例" align="gotoleft"> </el-table-column>
							<el-table-column prop="institution" label="机构投资者平均持有比例" align="gotoleft"> </el-table-column>
						</el-table>
					</div>
				</div>
				<div style="width: 806px">
					<div class="title mb-8">最新半年报(年报)数据</div>
					<div>
						<el-table :data="newData" border style="width: 100%">
							<el-table-column prop="individual" label="个人投资者持有比例" align="gotoleft"> </el-table-column>
							<el-table-column prop="institution" label="机构投资者持有比例" align="gotoleft"> </el-table-column>
						</el-table>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
// 规模及持有人结构
import { barChartOption } from '@/utils/chartStyle.js';

// 规模及持有人结构
import { getHoldInfo } from '@/api/pages/Analysis.js';
export default {
	name: 'sizeStructure',
	data() {
		return {
			loadyeji2: true,
			changingOption: {},
			avgData: [],
			newData: [],
			start_date: '',
			info: {}
		};
	},
	methods: {
		// 获取规模及持有人结构
		async getMoneyScale() {
			this.loadyeji2 = true;
			let data = await getHoldInfo({
				code: this.info.code,
				type: this.info.type,
				flag: this.info.flag,
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			this.loadyeji2 = false;
			if (data?.mtycode == 200) {
				this.filterHoldRank(data?.data);
				return data?.data;
			} else {
				return [];
			}
		},
		filterHoldRank(data) {
			let avgData = { individual: 0, institution: 0 };
			let newData = { individual: 0, institution: 0 };
			let result = data.sort((a, b) => {
				return this.moment(this.moment(a.yearqtr, 'YYYY QQ').format()).isBefore(this.moment(b.yearqtr, 'YYYY QQ').format()) ? -1 : 1;
			});
			// 过去平均持有
			this.start_date = result?.[0]?.enddate.slice(0, 10);
			result.map((item) => {
				avgData.individual =
					avgData.individual + (item.individualholdratio * 1 && !isNaN(item.individualholdratio) ? item.individualholdratio : 0);
				avgData.institution =
					avgData.institution + (item.individualholdratio * 1 && !isNaN(item.institutionholdratio) ? item.institutionholdratio : 0);
			});
			avgData.individual = (avgData.individual / result.length).toFixed(2) + '%';
			avgData.institution = (avgData.institution / result.length).toFixed(2) + '%';
			this.avgData = [avgData];
			// 最新报告期持有
			let newItem = result.reverse().find((v) => v.yearqtr.includes('Q2') || v.yearqtr.includes('Q4'));
			newData.individual = (newItem.individualholdratio || 0) + '%';
			newData.institution = (newItem.institutionholdratio || 0) + '%';
			this.newData = [newData];
		},
		// 获取数据
		async getData(info) {
			this.info = info;
			let data = await this.getMoneyScale();
			this.loadyeji = false;
			if (data?.length) {
				this.drawNewLine(
					data.sort((a, b) => {
						return this.moment(this.moment(a.yearqtr, 'YYYY QQ').format()).isBefore(this.moment(b.yearqtr, 'YYYY QQ').format()) ? -1 : 1;
					})
				);
			}
			this.loadyeji2 = false;
		},
		// 画图
		drawNewLine(data) {
			this.changingOption = barChartOption({
				toolbox: 'none',
				color: ['#4096FF', '#F1D338', 'rgba(32, 118, 255, 0.2)', '#B5EC30'],
				legend: {
					bottom: '0',
					data: ['机构规模', '个人规模', '整体规模']
				},
				grid: {
					top: '12px',
					bottom: '80px'
				},
				tooltip: {
					// 坐标轴指示器，坐标轴触发有效
					type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
				},
				xAxis: [{ data: data.map((v) => v.yearqtr), isAlign: true }],
				yAxis: [{ type: 'value' }],
				dataZoom: {
					bottom: '30px',
					start: 0,
					end: 100
				},
				series: [
					{
						name: '机构规模',
						type: 'bar',
						stack: '总量',
						barCategoryGap: '60%',
						data: data
							.filter((v) => v.yearqtr.includes('Q2') || v.yearqtr.includes('Q4'))
							.map((v) => [v.yearqtr, ((1 * v.netasset * v.institutionholdratio) / 100).toFixed(2)])
					},
					{
						name: '个人规模',
						type: 'bar',
						stack: '总量',
						barCategoryGap: '60%',
						data: data
							.filter((v) => v.yearqtr.includes('Q2') || v.yearqtr.includes('Q4'))
							.map((v) => [v.yearqtr, ((1 * v.netasset * v.individualholdratio) / 100).toFixed(2)])
					},
					{
						name: '整体规模',
						type: 'bar',
						stack: '总量',
						barCategoryGap: '60%',
						data: data
							.filter((v) => v.yearqtr.includes('Q1') || v.yearqtr.includes('Q3'))
							.map((v) => [v.yearqtr, (1 * v.netasset).toFixed(2)])
					}
				]
			});
			console.log(this.changingOption);
			this.loadyeji2 = false;
		},
		// 处理图表数据
		drawLine(resScale) {
			let dateList = [],
				scaleList = [],
				personData = [],
				instituteData = [],
				temparr = [];
			let scaleDate = resScale.map((item) => item[0]);
			resScale.forEach((item, index) => {
				dateList.push(item[0]);
				if (item[0].indexOf('Q1') >= 0 || item[0].indexOf('Q3') >= 0) {
					temparr.push([item[0], Number(item[1]).toFixed(2)]);
				}
			});
			resData.yearqtr.forEach((item, index) => {
				if (item.indexOf('Q2') !== -1 || item.indexOf('Q4') !== -1) {
					personData.push([item, resData['个人投资者持有比例'][index]]);
					instituteData.push([item, resData['机构投资者持有比例'][index]]);
					if (scaleDate.includes(item)) {
						let scale_index = scaleDate.indexOf(item);
						scaleList.push(resScale[scale_index][1]);
					} else {
						scaleList.push(0);
					}
				}
			});
			let qtr = dateList?.[dateList.length - 1];
			let personScaleData = personData.map((item, index) => (item = [item[0], parseFloat((item[1] * scaleList[index]) / 100).toFixed(2)]));
			let instituteScaleData = instituteData.map(
				(item, index) => (item = [item[0], parseFloat((item[1] * scaleList[index]) / 100).toFixed(2)])
			);
			if (qtr.indexOf('Q2') != -1 || qtr.indexOf('Q4') != -1) {
				if (instituteScaleData?.[instituteScaleData.length - 1][0] != qtr && personScaleData?.[personScaleData.length - 1] != qtr) {
					temparr.push([resScale[resScale.length - 1][0], (resScale[resScale.length - 1][1] * 1)?.toFixed(2)]);
				}
			}

			this.changingOption = barChartOption({
				color: ['#4096FF', '#F1D338', 'rgba(32, 118, 255, 0.2)', '#B5EC30'],
				legend: ['机构规模', '个人规模', '整体规模'],
				xAxis: [
					{
						data: this.isExist([...instituteScaleData, ...personScaleData, ...temparr], dateList),
						isAlign: true
					}
				],
				yAxis: [{ type: 'value' }],
				series: [
					{
						name: '机构规模',
						type: 'bar',
						stack: '总量',
						barCategoryGap: '60%',
						data: instituteScaleData
					},
					{
						name: '个人规模',
						type: 'bar',
						stack: '总量',
						barCategoryGap: '60%',
						data: personScaleData
					},
					{
						name: '整体规模',
						type: 'bar',
						stack: '总量',
						barCategoryGap: '60%',
						data: temparr
					}
				]
			});
			this.loadyeji2 = false;
		},
		isExist(arr, a) {
			var arr1 = [];
			for (var j = 0; j < a.length; j++) {
				for (var i = 0; i < arr.length; i++) {
					if (arr[i][0] == a[j]) {
						if (arr1.indexOf(a[j]) == -1) {
							arr1.push(a[j]);
						}
					}
				}
			}
			return arr1;
		},
		async createPrintWord(info) {
			await this.getData(info);
			let height = this.$refs['sizeStructure'].$el.clientHeight;
			let width = this.$refs['sizeStructure'].$el.clientWidth;
			let chart = this.$refs['sizeStructure'].getDataURL({
				type: 'jpg',
				pixelRatio: 3,
				backgroundColor: '#fff'
			});
			let list = [
				{ label: '个人投资者平均持有比例', value: 'avg_individual' },
				{ label: '机构投资者平均持有比例', value: 'avg_institution' },
				{ label: '个人投资者平均持有比例', value: 'new_individual' },
				{ label: '机构投资者平均持有比例', value: 'new_institution' }
			];
			let data = [
				{
					avg_individual: this.avgData[0]?.individual,
					avg_institution: this.avgData[0]?.institution,
					new_individual: this.newData[0]?.individual,
					new_institution: this.newData[0]?.institution
				}
			];
			return [
				...this.$exportWord.exportTitle('规模及持有人结构'),
				...this.$exportWord.exportChart(chart, { width, height }),
				...this.$exportWord.exportTable(
					[
						{ label: this.start_date + '以来数据', columnSpan: 2 },
						{ label: '最新半年报(年报)数据', columnSpan: 2 }
					],
					'',
					'',
					true
				),
				...this.$exportWord.exportTable(list, data, '', true)
			];
		}
	}
};
</script>

<style scoped>
.charts_one_class_for_equity {
	width: 100%;
	height: 180px;
	position: relative;
	page-break-inside: avoid;
}
</style>
