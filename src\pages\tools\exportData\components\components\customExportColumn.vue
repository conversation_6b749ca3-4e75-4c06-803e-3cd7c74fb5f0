<template>
	<div>
		<el-dialog class="custom-export-column" title="自定义导出列" :visible.sync="dialogVisible" :close-on-click-modal="false" width="900px">
			<div v-loading="loading">
				<!-- <div class="title">自定义导出列</div> -->
				<div class="flex_between">
					<div class="flex_start my-20">
						<div class="mr-8">字段模版:</div>
						<div class="mr-8">
							<el-select v-model="template" placeholder="请选择模版" @change="changeTemplate">
								<el-option v-for="item in templateList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
							</el-select>
						</div>
						<div>
							<el-link :disabled="!template" class="mr-8" @click="save">另存为模版</el-link>
							<el-link :disabled="!template" @click="resetTemplate">新建模版</el-link>
						</div>
					</div>
					<div>
						<el-button :disabled="template != ''" type="primary" @click="save">保 存</el-button>
						<el-button :disabled="!template" @click="del">删除模版</el-button>
					</div>
				</div>
				<div class="flex_start">
					<div class="wait-condition">
						<!-- <choose-condition ref="chooseCondition"></choose-condition> -->
						<div class="flex_start py-8 px-16" style="border-bottom: 1px solid #e9e9e9">
							<div class="mr-8">搜索指标:</div>
							<div><el-input v-model="content" placeholder="请输入指标名称" @input="onInputName"></el-input></div>
						</div>
						<div class="ml-24">
							<el-checkbox v-model="selectAll" @change="changeSelect">全选</el-checkbox>
						</div>
						<div class="condition-list">
							<el-tree
								ref="tree"
								:data="data"
								:props="defaultProps"
								:filter-node-method="filterNode"
								node-key="id"
								show-checkbox
								@node-click="checkTree"
								@check="checkTree"
							></el-tree>
						</div>
					</div>
					<div class="flex_center mx-16" style="flex-direction: column">
						<div>
							<el-button @click="addActive">添加<i class="el-icon-arrow-right"></i></el-button>
						</div>
						<div class="my-8">
							<el-button @click="reomveActive"><i class="el-icon-arrow-left"></i>删除</el-button>
						</div>
						<div><el-button @click="clearActive">清空已选</el-button></div>
					</div>
					<div class="active-condition">
						<div class="flex_start py-10 px-16" style="border-bottom: 1px solid #e9e9e9">
							<div style="height: 28px; line-height: 28px">已选择列({{ activeData.length }}/{{ max_num }})</div>
						</div>
						<div class="condition-list">
							<el-tree
								ref="tree_active"
								:data="activeData"
								:props="defaultProps"
								node-key="id"
								show-checkbox
								@node-click="checkAvtiveTree"
								@check="checkAvtiveTree"
							>
								<div style="width: 100%" class="flex_between" slot-scope="{ node, data }">
									<div>{{ node.label }}</div>
									<div class="mr-16">
										<el-button type="text" size="mini" @click="() => remove(node, data)"> Delete </el-button>
									</div>
								</div>
							</el-tree>
						</div>
					</div>
				</div>
			</div>
			<div slot="footer">
				<el-button @click="dialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="submit">确认导出</el-button>
			</div>
		</el-dialog>
		<save-model-dialog ref="saveModelDialog" @submitModel="submitModel"></save-model-dialog>
	</div>
</template>

<script>
import chooseCondition from './chooseCondition.vue';
// 保存模版弹窗
import saveModelDialog from './saveModelDialog.vue';
import { getCustomExportFieldTemplateList, addCustomExportFieldTemplate, deleteCustomExportFieldTemplate } from '@/api/pages/ApiTaikang.js';

export default {
	components: { chooseCondition, saveModelDialog },
	props: {
		templateType: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			loading: true,
			max_num: 50,
			content: '',
			dialogVisible: false,
			template: '',
			templateList: [],
			data: [],
			cacheData: [],
			defaultProps: {
				children: 'children',
				label: 'fieldName'
			},
			activeData: [],
			selectAll: false,
			shiftDown: false,
			start_id: ''
		};
	},
	mounted() {
		// keyCode === 16 Shift键
		window.addEventListener('keydown', (e) => {
			if (e.keyCode === 16) {
				this.shiftDown = true;
			}
		});
		window.addEventListener('keyup', (e) => {
			if (e.keyCode === 16) {
				this.shiftDown = false;
			}
		});
	},
	methods: {
		getData(list) {
			this.cacheData = [...list];
			this.data = [...list];
			this.dialogVisible = true;
			this.getCustomExportFieldTemplateList();
			// this.$refs.chooseCondition.getData(this.data);
		},
		// 获取自定义导出列模版列表
		async getCustomExportFieldTemplateList() {
			this.loading = true;
			let data = await getCustomExportFieldTemplateList({ templateType: this.templateType });
			this.loading = false;
			if (data.code == 200) {
				this.templateList = data.data.map((item) => {
					return { ...item, label: item.templateName, value: item.id };
				});
			}
		},
		// 监听模板切换
		changeTemplate() {
			let activeData = this.templateList.find((v) => v.value == this.template)?.fieldList;
			this.activeData = activeData;
			this.activeData.map((item) => {
				let index = this.data.findIndex((v) => v.field == item.field);
				if (index != -1) {
					this.data.splice(index, 1);
				}
			});
		},
		// 重置模版
		resetTemplate() {
			this.activeData = [];
			this.data = [...this.cacheData];
			this.template = '';
		},
		// 保存模版
		save() {
			this.$refs['saveModelDialog'].getData();
		},
		// 删除模版
		del() {
			this.$confirm('此操作将永久删除该模版, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(async () => {
					let data = await deleteCustomExportFieldTemplate({ id: this.template });
					if (data.code == 200) {
						this.$message({
							type: 'success',
							message: '删除成功!'
						});
						this.resetTemplate();
						this.getCustomExportFieldTemplateList();
					} else {
						this.$message({
							type: 'warning',
							message: data.message
						});
					}
				})
				.catch(() => {
					this.$message({
						type: 'info',
						message: '已取消删除'
					});
				});
		},
		// 获取模版名称
		submitModel(val) {
			this.addCustomExportFieldTemplate(val?.name);
		},
		// 保存自定义导出列为模版
		async addCustomExportFieldTemplate(name) {
			let templateName = name;
			let fieldList = this.activeData.map((item) => {
				return { field: item.field, fieldName: item.fieldName };
			});
			this.loading = true;
			let data = await addCustomExportFieldTemplate({ templateType: this.templateType, templateName, fieldList });
			this.loading = false;
			if (data.code == 200) {
				this.$message.success('创建模版成功');
				this.getCustomExportFieldTemplateList();
			}
		},
		// 监听输入框内容
		onInputName() {
			this.$refs.tree.filter(this.content);
		},
		filterNode(value, data) {
			if (!value) return true;
			return data.fieldName.indexOf(value) !== -1;
		},
		// 监听节点被点击
		checkTree(data) {
			this.handleShift(data, this.data, 'tree');
		},
		// 监听已选择节点被点击
		checkAvtiveTree(data) {
			this.handleShift(data, this.activeData, 'tree_active');
		},
		// Shift快捷操作
		handleShift(data, tree, ref) {
			if (this.shiftDown) {
				let end_id = data.id;
				let start_index = tree.findIndex((v) => v.id === this.start_id);
				let end_index = tree.findIndex((v) => v.id === end_id);
				let id_list = [];
				if (start_index < end_index) {
					id_list = tree.slice(start_index, end_index).map((v) => v.id);
				} else {
					id_list = tree.slice(end_index, start_index).map((v) => v.id);
				}
				let list = this.$refs[ref].getCheckedKeys();
				this.$refs[ref].setCheckedKeys([...list, ...id_list]);
			} else {
				this.start_id = data.id;
			}
		},
		// 添加选中条件
		addActive() {
			if (this.activeData.length >= this.max_num) {
				this.$message.warning('选择超出最大数量限制');
			} else {
				let list = this.$refs.tree.getCheckedNodes();
				let max_add_num = this.max_num - this.activeData.length;
				if (max_add_num < list.length) {
					this.$message.warning('超出最大限制,只选中部分条件');
				}
				let active_list = list.slice(0, max_add_num);
				this.activeData.push(...active_list);
				active_list.map((item) => {
					this.data.splice(
						this.data.findIndex((v) => v.id === item.id),
						1
					);
				});
			}
		},
		//移除选中条件
		reomveActive() {
			let list = this.$refs.tree_active.getCheckedNodes();
			this.data.push(...list);
			list.map((item) => {
				this.activeData.splice(
					this.activeData.findIndex((v) => v.id === item.id),
					1
				);
			});
			this.data = this.cacheData.filter((item) => this.activeData.findIndex((v) => v.id == item.id) === -1);
		},
		// 移除已选
		remove(node, data) {
			const parent = node.parent;
			const children = parent.data.children || parent.data;
			const index = children.findIndex((d) => d.id === data.id);
			children.splice(index, 1);
		},
		// 清空已选
		clearActive() {
			this.data = [...this.cacheData];
			this.activeData = [];
		},
		// 全选
		changeSelect() {
			this.$refs.tree.setCheckedKeys(this.selectAll ? this.data.map((v) => v.id) : []);
		},
		// 确认导出
		submit() {
			this.dialogVisible = false;
			this.$emit('returnRows', this.activeData);
		}
	}
};
</script>

<style lang="scss" scoped>
.custom-export-column {
	::v-deep .el-dialog .el-dialog__header {
		border-bottom: 1px solid #e9e9e9;
	}
	.wait-condition {
		flex: 1;
		border-radius: 4px;
		border: 1px solid #d9d9d9;
		height: 432px;
		.condition-list {
			height: 352px;
			overflow-x: hidden;
			overflow-y: auto;
		}
		.condition-list::-webkit-scrollbar {
			width: 2px;
		}
	}
	.active-condition {
		width: 300px;
		border-radius: 4px;
		border: 1px solid #d9d9d9;
		height: 432px;
		.condition-list {
			height: 373px;
			overflow-x: hidden;
			overflow-y: auto;
		}
		.condition-list::-webkit-scrollbar {
			width: 2px;
		}
	}
}
</style>
