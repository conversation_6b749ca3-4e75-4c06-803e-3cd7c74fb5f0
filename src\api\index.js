//import request from '../utils/request';
import axios from 'axios'; //引入axios
//下面这两个不一定需要引入，看你项目需要拦截的时候做什么操作，但是一般都需要引入store
//import store from '@/store/index'  //引入store
import router from '@/router'; //引入router
import qs from 'qs';
import Vue from 'vue';
import store from '../store/store';
// import axiosrequest from '@/api/index.js';
import { VueEasyJwt } from 'vue-easy-jwt';

// -- 中断重复请求接口
// const CancelToken = axios.CancelToken
function generateReqKey(config) {
	const { method, url, params, data } = config;
	return [method, url, qs.stringify(params), qs.stringify(data)].join('&');
}

const pendingRequest = new Map();
function addPendingRequest(config) {
	const requestKey = generateReqKey(config);
	config.cancelToken =
		config.cancelToken ||
		new axios.CancelToken((cancel) => {
			if (!pendingRequest.has(requestKey)) {
				pendingRequest.set(requestKey, cancel);
			}
		});
}

function removePendingRequest(config) {
	const requestKey = generateReqKey(config);
	if (pendingRequest.has(requestKey)) {
		const cancelToken = pendingRequest.get(requestKey);
		cancelToken(requestKey);
		pendingRequest.delete(requestKey);
	}
}
// --end

const jwt = new VueEasyJwt();
let flag = 0;
let instance = axios.create({
	headers: {
		innerUserId: '165'
		//   'content-type': 'application/x-www-form-urlencoded'
	}
});
let baseUrlx = process.env.VUE_APP_BASE_URL;
instance.interceptors.request.use(
	(config) => {
		removePendingRequest(config); // 检查是否存在重复请求
		addPendingRequest(config); // 将当前请求信息添加到 pendingRequest对象中
		// //console.log(config)
		// if (jwt.isExpired(store.state.token) && config.url.indexOf('login') < 0 && config.url.indexOf('VerificationUser') < 0) {
		// try{
		// if (config.url.indexOf('MtyContent/SelectFile/?flag=out') >= 0) {
		config.headers.authorization = 'Bearer ' + store.state.token;
		return config;
		// 	}
		// 	let tempconfig = axios
		// 		.post(baseUrlx + '/api-token-refresh/', {
		// 			refresh: store.state.retoken
		// 		})
		// 		.then((res) => {
		// 			// //console.log('res......')
		// 			// //console.log(res);
		// 			if (res && res.data && res.data.access) {
		// 				store.dispatch('changetoken', res.data.access);
		// 				config.headers.authorization = 'Bearer ' + store.state.token;
		// 				return config;
		// 			} else {
		// 				if (config.url.indexOf('/show-menu') >= 0 || config.url.indexOf('MtyContent/SelectFile/?flag=out') >= 0) {
		// 					config.headers.authorization = '';
		// 					return config;
		// 				}
		// 				store.dispatch('changetoken', '');
		// 				store.dispatch('changeusername', '');
		// 				store.dispatch('changeuserid', '');
		// 				store.dispatch('changerefreshtoken', '');
		// 				store.dispatch('changeuserrole', '');
		// 				localStorage.removeItem('username');
		// 				localStorage.removeItem('token');
		// 				localStorage.removeItem('id');
		// 				localStorage.removeItem('retoken');
		// 				localStorage.removeItem('userType');
		// 				// console.log(router, config);
		// 				router.push({ path: '/dashboard', query: { flaglogin: 'login' } });
		// 				if (config.url.indexOf('eader_search_all') >= 0 && router.history.current.path == '/dashboard') {
		// 					Vue.prototype.$logins();
		// 					if (flag == 0) {
		// 						flag += 1;
		// 						// Vue.prototype.$message('登录状态过期,请重新登录');
		// 					}
		// 				} else {
		// 					if (flag == 0) {
		// 						flag += 1;
		// 						Vue.prototype.$message('登录状态过期,请重新登录');
		// 					}
		// 				}
		// 				// Vue.prototype.$logins();
		// 				// router.replace({
		// 				// 	path: 'login'
		// 				// });
		// 				// if (flag == 0) {
		// 				// 	flag += 1;
		// 				// 	Vue.prototype.$message('登录状态过期,请重新登录');
		// 				// }
		// 				config.headers.authorization = 'Bearer ' + store.state.token;
		// 				return config;
		// 			}
		// 		})
		// 		.catch((err) => {
		// 			store.dispatch('changetoken', '');
		// 			store.dispatch('changeusername', '');
		// 			store.dispatch('changeuserid', '');
		// 			store.dispatch('changerefreshtoken', '');
		// 			localStorage.removeItem('username');
		// 			localStorage.removeItem('token');
		// 			localStorage.removeItem('id');
		// 			localStorage.removeItem('retoken');
		// 			// router.replace({
		// 			// 	path: 'login'
		// 			// });
		// 			router.push({ path: '/dashboard', query: { flaglogin: 'login' } });
		// 			// Vue.prototype.$logins();
		// 			if (flag == 0) {
		// 				flag += 1;
		// 				Vue.prototype.$message('登录状态过期,请重新登录');
		// 			}
		// 			config.headers.authorization = 'Bearer ' + store.state.token;
		// 			return config;
		// 		});
		// 	return tempconfig;
		// 	// }
		// 	// catch(err){
		// 	// 	//console.log('trycatvh')
		// 	// 	//console.log(err)
		// 	// }
		// } else {
		// 	config.headers.authorization = 'Bearer ' + store.state.token;
		// 	if (config.url.indexOf('VerificationUser') >= 0) {
		// 		config.headers.authorization = '';
		// 	}
		// 	return config;
		// }
	},
	(err) => {
		//console.log('xsaa');
		//console.log(err);
		return Promise.reject(err);
	}
);

// http response 拦截器
instance.interceptors.response.use(
	(response) => {
		// 终端重复请i去
		removePendingRequest(response.config); // 从 pendingRequest对象中移除请求
		//
		if (
			response.status === 401 ||
			response.mtycode === 401 ||
			(response.data && response.data.mtycode && response.data.mtycode == 401) ||
			response.status === 6100001 ||
			response.mtycode === 6100001 ||
			(response.data && response.data.mtycode && response.data.mtycode == 6100001)
		) {
			if (flag == 0) {
				flag += 1;
				store.dispatch('changetoken', '');
				store.dispatch('changeusername', '');
				store.dispatch('changeuserid', '');
				store.dispatch('changerefreshtoken', '');
				store.dispatch('changeuserrole', '');
				localStorage.removeItem('username');
				localStorage.removeItem('token');
				localStorage.removeItem('id');
				localStorage.removeItem('retoken');
				localStorage.removeItem('userType');
				// router.replace({
				// 	path: 'login'
				// });
				router.push({ path: '/dashboard', query: { flaglogin: 'login' } });
				// Vue.prototype.$logins();
				Vue.prototype.$message('登录状态过期,请重新登录');
			}
		} else {
			flag = 0;
			return response;
		}
	},
	//接口错误状态处理，也就是说无响应时的处理
	(error) => {
		removePendingRequest(error.config || {}); // 从 pendingRequest对象中移除请求
		if (axios.isCancel(error)) {
			// console.log(error.message);
			// return Promise.reject();
		} else {
			// //console.log(flag)
			//console.log(error.response);
			if (error.response.data.detail && error.response.data.detail == '没有提供正确的登录信息') {
				// alert('没有提供正确的登录信息');
			}
			// //console.log(error.response.data== '登陆错误')
			else if (error.response.data == '登录错误' || error.response.data == '登录过期' || error.response.status == 401) {
				if (flag == 0) {
					// //console.log('herein');
					flag += 1;
					store.dispatch('changetoken', '');
					store.dispatch('changeusername', '');
					store.dispatch('changeuserid', '');
					store.dispatch('changerefreshtoken', '');
					localStorage.removeItem('username');
					localStorage.removeItem('token');
					localStorage.removeItem('id');
					localStorage.removeItem('retoken');
					localStorage.removeItem('userType');
					// router.replace({
					// 	path: 'login'
					// 	// query: {
					// 	//   redirect: router.currentRoute.fullPath
					// 	// }
					// });
					router.push({ path: '/dashboard', query: { flaglogin: 'login' } });
					// Vue.prototype.$logins();
					// Vue.prototype.$message('登录状态过期,请重新登录');
				}
			}
		}
		// ////console.log(error.response)
		//////console.log(store.state.token, store.state.username+'??')
		return Promise.reject(error.response); // 返回接口返回的错误信息
	}
);

export default instance;
// export const fetchData = query => {
//     return request({
//         url: './table.json',
//         method: 'get',
//         params: query
//     });
// };

// export const GetPicMsg = query => {
//     return request({
//         url: '/fund_allocation_details',
//         method: 'put',
//         params: query
//     });
// };
