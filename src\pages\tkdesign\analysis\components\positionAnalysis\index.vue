<script>
import VChart from 'vue-echarts';
import longHold from './longHold.vue'
import { lineChartOption, barChartOption, triangleCircleOption } from '@/utils/chartStyle';
import {
  getObjectIndustryOperation, // MOM行业操作复盘
  getObjectAllIndustry, // 混合
  getObjectIndustry, // MOM行业分析、 行业配置
  getObjectIndustryStat,
  getObjectIndustryBrison, // 归因
  getObjectFOFIndustryOperation, // FOF行业分析、FOF行业操作复盘
  getObjectAllIndustryOperation // 三角方块图
} from '@/api/pages/analysis/report';
import { handleData, colorList } from '@/utils/count';
import { filter_json_to_excel_inside, changColumnToRow, filter_json_to_excel_inside_multiHeader } from '@/utils/exportExcel.js';
import { export_json_to_excel_multiHeader2 } from '@/vendor/Export2Excel.js';
import {
  downloadWord,
  exportTitleWithSubtitle,
  exportTableMergeHeader,
  exportTitle,
  exportFirstTitle,
  exportChart,
  exportTable,
  Format,
  exportSencondTitle
} from '@/utils/exportWord.js';

export default {
  components: { VChart, longHold },
  data () {
    return {
      uploadState: false,
      // 行业口径
      industryCaliberOption: [
        {
          value: 3,
          label: '申万一级行业'
        },
        {
          value: 2,
          label: '泰康一级行业'
        }
      ],
      // 分析对象
      analysisOptions: [
        { value: '', label: '整体' },
        { value: 'manager', label: '投资经理' },
        { value: 'department', label: '部门' },
        { value: 'account_type1', label: '大类账户 ' },
        { value: 'account_type2', label: '一般账户' },
        { value: 'strategy1', label: '一级策略' },
        { value: 'strategy2', label: '二级策略' },
        { value: 'manager1', label: '投管人' }
      ],
      // 行业持仓分析
      position: {
        industryStandard: 3,
        tableData: [], // 分颗粒度的行业持仓分析数据
        allTableData: [], // 所有的行业持仓分析数据
        particleList: ['整体'], // 次级颗粒度列表
        particle: '整体', // 次级颗粒度
        chart: null,
        index: '',
        object: 'department', // 颗粒度
        indexOptions: [],
        loading: false,
        showEmpty: true,
        radio: [],
        date: [],
        visible: false,
        industries: [] // 行业列表
      },
      // 行业三角方块图持仓分析
      position2: {
        industryStandard: 3,
        tableData: [], // 分颗粒度的行业持仓分析数据
        allTableData: [], // 所有的行业持仓分析数据
        particleList: ['整体'], // 次级颗粒度列表
        particle: '整体', // 次级颗粒度
        chart: null,
        index: '',
        object: 'department', // 颗粒度
        indexOptions: [],
        loading: false,
        showEmpty: true,
        radio: [],
        date: [],
        visible: false,
        industries: [] // 行业列表
      },
      // MOM行业持仓分析
      MOMPosition: {
        codeList: [],
        code: '',
        industryStandard: 3,
        tableData: [], // 分颗粒度的行业持仓分析数据
        allTableData: [], // 所有的行业持仓分析数据
        particleList: ['整体'], // 次级颗粒度列表
        particle: '整体', // 次级颗粒度
        chart: null,
        index: '',
        object: 'department', // 颗粒度
        indexOptions: [],
        loading: false,
        showEmpty: true,
        radio: [],
        date: [],
        visible: false,
        industries: [] // 行业列表
      },
      // FOF行业持仓分析
      FOFPosition: {
        codeList: [],
        code: '',
        industryStandard: 3,
        tableData: [], // 分颗粒度的行业持仓分析数据
        allTableData: [], // 所有的行业持仓分析数据
        particleList: ['整体'], // 次级颗粒度列表
        particle: '整体', // 次级颗粒度
        chart: null,
        index: '',
        object: 'department', // 颗粒度
        indexOptions: [],
        loading: false,
        showEmpty: true,
        radio: [],
        date: [],
        visible: false,
        industries: [] // 行业列表
      },
      // 持股行业分布
      shareholding: {
        numberChart: null, // 长期持股行业次数
        weightChart: null, // 长期持股行业权重
        loading: false,
        countEmpty: true,
        weightEmpty: true
      },
      // Brison归因
      brison: {
        reportTemplate: 'MOM',
        tableData: [],
        oldTableData: [],
        loading: false,
        pageIndex: 1,
        pageSize: 10,
        params: {
          industryStandard: 3
        }
      },

      MOMData: {
        codeList: [],
        code: '',
        tableData: [],
        allTableData: [],
        chart: {},
        MOMCareers: [],
        caliber: '',
        industry: '',
        industryStandard: 3,
        object: 'department',
        loading: false,
        showEmpty: true,
        radio: [],
        date: [],
        visible: false
      },
      FOFData: {
        codeList: [],
        code: '',
        tableData: [],
        allTableData: [],
        chart: {},
        FOFCareers: [],
        caliber: '',
        industry: '',
        industryStandard: 3,
        object: 'department',
        showEmpty: true,
        loading: false,
        radio: [],
        date: [],
        visible: false
      },
      performance: {
        loading: false,
        tableData: [],
        allTableData: [],
        allocation: {}, // 箭头表（柱状图）
        allocationBar: {}, // 箭头表（箭头图）
        industryStandard: 3,
        object: '', // 分析对象
        objectRadio: '',
        showEmpty: true,
        radio: [],
        date: [],
        visible: false,
        dataZoom: { start: 0, end: 100 },
        params: {},
        //弹窗需要的参数和数据
        dialogData: {
          industries: [], //行业列表
          selectCode: [], //所选颗粒度
          code: {},
          dialogTitle: '',
          optionBar: {},
          option: {},
          dataZoom: { start: 0, end: 100 }
        }
      },
      showDialogRecord: false, // 绑定弹窗dialog
      showGranularity: false, //绑定行业配置表现-选择粒度弹窗
      showGranularityChart: false, //绑定行业配置表现-选择粒度弹窗2
      histogramOptions: {},
      attributionData: [], // 弹窗页面表格数据源
      params: {},
      showMOM: true,
      showFOF: true,
      showOneYear: true,
      showTwoYear: true,
      showThreeYear: true,
      //设置时间范围
      pickerOptions: {
        disabledDate: (date) => {
          const startTime = this.moment(this.$route.query.startDate).subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss');
          const endTime = this.moment(this.$route.query.endDate).add(1, 'day').format('YYYY-MM-DD HH:mm:ss');
          const checkTime = this.moment(date, 'YYYY-MM-DD HH:mm:ss');
          return !checkTime.isBetween(startTime, endTime);
        }
      },

      pieChartData: [],
      reportTemplate: 'ALL'
    };
  },
  filters: {
    fixY (value) {
      if (value == null || value == 'null' || value == '--') {
        return '--';
      } else return (parseInt(value) / 100000000).toFixed(2);
    },
    fix2p (value) {
      if (value == null || value == 'null' || value == '--') {
        return '--';
      } else return ((value) * 100).toFixed(2) + '%';
    },
    fixp (value) {
      if (value == null || value == 'null' || value == '--') {
        return '--';
      } else return ((value) * 1).toFixed(2) + '%';
    }
  },
  mounted () {
    this.reportTemplate = this.$route.query?.reportTemplate || 'ALL';
    let that = this
    if (that.$route.query.reportTemplate === 'FOF') {
      that.showMOM = false;
    }
    if (that.$route.query.reportTemplate === 'MOM') {
      that.showFOF = false;
    }
    if (that.$route.query.reportTemplate === 'ALL') {
      that.showMOM = true;
      that.showFOF = true;
    }

  },
  methods: {
    /**
     * 排序
     */
    sortBrison ({ column, prop, order }) {
      // let key = prop.split('.')[1];
      // let arr = JSON.parse(JSON.stringify(this.brison.tableData.filter((v) => v.data[key] !== 'nan' && v.data[key] !== 'NaN')));
      // let noArr = this.brison.tableData.filter((v) => v.data[key] === 'nan' || v.data[key] === 'NaN');
      // if (order === 'ascending') {
      //   this.brison.tableData = noArr.concat(arr.sort((a, b) => Number(a.data[key]) - Number(b.data[key])));
      // }
      // if (order === 'descending') {
      //   this.brison.tableData = arr.sort((a, b) => Number(b.data[key]) - Number(a.data[key])).concat(noArr);
      // }
      // if (order === null) {
      //   this.brison.tableData = this.brison.oldTableData;
      // }
    },
    /**
     * 获取cell颜色
     * @param row
     * @param column
     * @param rowIndex
     * @param columnIndex
     * @returns {{color: string}}
     */
    cellStyle ({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0 && rowIndex > 0) {
        return {
          color: '#4096ff'
        };
      }
    },

    /**
     * 获取行业数据并分配颜色
     */
    getIndustryData (type, arr) {
      if (type === 'all') {
        this.position.industries = [];
        let arr =
          this.position.allTableData &&
          this.position.allTableData
            .map((item) => item.data.industryName)
            .filter((name, index, array) => {
              return index === 0 || name !== array[index - 1]; // 移除重复的职业
            });
        arr = Array.from(new Set(arr));
        arr.forEach((item, index) => {
          this.position.industries.push({ name: item, color: colorList[index] });
        });
      }

      if (type === 'MOM') {
        this.MOMPosition.industries = [];
        let arrs = arr
          .map((item) => item.data.industryName)
          .filter((name, index, array) => {
            return index === 0 || name !== array[index - 1]; // 移除重复的职业
          });

        arrs = Array.from(new Set(arrs));
        arrs.forEach((item, index) => {
          this.MOMPosition.industries.push({ name: item, color: colorList[index] });
        });
      }

      if (type === 'FOF') {
        this.FOFPosition.industries = [];
        let arrs = arr
          .map((item) => item.data.industryName)
          .filter((name, index, array) => {
            return index === 0 || name !== array[index - 1]; // 移除重复的职业
          });
        arrs = Array.from(new Set(arrs));
        arrs.forEach((item, index) => {
          this.FOFPosition.industries.push({ name: item, color: colorList[index] });
        });
      }
    },

    /**
     * 参数写入
     */
    getParams () {
      let reportID = Number(this.$route.query.id);
      let industryStandard = 3;
      let startFrom = Number(this.moment(this.$route.query.startDate).format('YYYYMMDD'));
      let endTo = Number(this.moment(this.$route.query.endDate).format('YYYYMMDD'));
      this.FOFPosition.params =
        this.MOMPosition.params =
        this.position.params =
        this.position2.params =
        {
          reportID,
          industryStandard,
          startFrom,
          endTo,
          selectedCuts: this.$route.query.graininess
        };

      this.performance.params = {
        reportID,
        industryStandard,
        startFrom,
        endTo,
        selectedCuts: this.$route.query.graininess
      };
      this.shareholding.params = {
        reportID,
        industryStandard,
        startFrom,
        endTo,
        selectedCuts: this.$route.query.graininess
      };
      this.FOFData.params = {
        reportID,
        industryStandard,
        startFrom,
        endTo,
        selectedIndustry: this.MOMData.industry,
        selectedCuts: this.$route.query.graininess
      };
      this.MOMData.params = {
        reportID,
        industryStandard,
        startFrom,
        endTo,
        selectedIndustry: this.MOMData.industry,
        selectedCuts: this.$route.query.graininess
      };
    },

    /**
     * 行业持仓分析选择近年时间
     */
    changePositionParamsRadio (row) {
      this.position.radio = [row[row.length - 1]];
      this.position.date = [];
      if (this.position.radio.length) {
        this.position.params.startFrom = Number(this.moment().subtract(this.position.radio, 'years').format('YYYYMMDD'));
        this.position.params.endTo = Number(this.moment().format('YYYYMMDD'));
      } else {
        this.position.params.startFrom = Number(this.moment(this.$route.query.startDate).format('YYYYMMDD'));
        this.position.params.endTo = Number(this.moment(this.$route.query.endDate).format('YYYYMMDD'));
      }
      // this.getObjectAllIndustryOperation();
      this.getConfigurationData();
    },

    /**
     * 行业持仓分析选择时间区间
     */
    changePositionParamsDate () {
      this.position.radio = [];
      this.position.visible = false;
      if (this.position.date.length) {
        this.position.params.startFrom = Number(Number(this.moment(this.position.date[0]).format('YYYYMMDD')));
        this.position.params.endTo = Number(this.moment(this.position.date[1]).format('YYYYMMDD'));
      } else {
        this.position.params.startFrom = Number(this.moment(this.$route.query.startDate).format('YYYYMMDD'));
        this.position.params.endTo = Number(this.moment(this.$route.query.endDate).format('YYYYMMDD'));
      }
      this.getConfigurationData();
      // this.getObjectAllIndustryOperation();
    },

    /**
     * 行业持仓分析
     */
    getConfigurationData () {
      this.position.loading = true;
      let params = {
        ...this.position.params,
        industryStandard: this.position.industryStandard
      };
      // 全部数据
      params.selectedCuts = '';
      return getObjectAllIndustry(params).then((res) => {
        this.position.loading = false;
        if (res.code === 200) {
          this.position.showEmpty = false;
          this.position.allTableData = res.data.rows;
          this.getIndustryData('all'); // 分配颜色
          this.getChartData();

          return res.data.rows;
        }
        return 'error';
      });
    },

    /**
     * 获取行业持仓分析图表
     */
    getChartData () {
      let arr = [];
      arr = this.position.allTableData;
      const data = {};
      let line = [];
      arr.forEach((item) => {
        if (item.data.industryName == 'nan') {
          item.data.industryName = '其他'
        }
        if (!data[item.data.industryName]) {
          data[item.data.industryName] = [];
        }
        data[item.data.industryName].push(
          item.data.weight === 'nan' ? [item.data['yearqtr'], 0] : [item.data['yearqtr'], item.data['weight']]
        );
      });
      let dataList = arr
        .map((item) => item.data.yearqtr)
        .sort()
        .filter((time, index, array) => {
          return index === 0 || time !== array[index - 1]; // 移除重复的时间
        })
      console.log(data);
      for (let i = 0; i < dataList.length; i++) {
        for (const item in data) {
          if (data[item].findIndex(items => items[0] == dataList[i]) < 0) {
            data[item].push([dataList[i], 0])
          }

        }
      }
      for (const item in data) {
        data[item].sort((a, b) => {
          if (a[0] > b[0]) return -1
          else return 1
        })
      }
      for (let key in data) {
        line.push({
          name: key,
          type: 'line',
          stack: 'Total',
          emphasis: {
            focus: 'series'
          },
          lineStyle: {
            width: 1
          },
          areaStyle: {},
          data: data[key]
        });
      }
      console.log('lineline', line);
      const legendNames = [];
      const column = [];
      line.forEach((item) => {
        if (item.data.length !== 1) {
          item.symbol = 'none';
        }
        legendNames.push(item.name);
        column.push(item.data);
      });

      this.position.chart = lineChartOption({
        // animation:false,
        grid: { left: '124px', right: '48px', top: '24px', bottom: '60px' }, // 位置
        dataZoom: true,
        toolbox: true,
        legend: {
          data: legendNames,
          type: 'plain'
        },
        tooltip: {
          formatter: function (obj) {
            obj = obj
              .filter((v) => Number((Number(v.data?.[1]) * 100).toFixed(2)) > 0)
              .sort((a, b) => (Number(a.data?.[1]) > Number(b.data?.[1]) ? -1 : 1));
            let value = `<div style="font-size:14px;">` + obj?.[0]?.axisValue + `</div>`;
            for (let i = 0; i < obj.length; i++) {
              value +=
                `<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
                `<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
                obj?.[i].color +
                `;"></div>` +
                `<div style="font-family: PingFang SC;">` +
                obj?.[i].seriesName +
                '</div></div>' +
                `<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
                (Number(obj?.[i].value?.[1]) * 100).toFixed(2) +
                '%</div>' +
                `</div>`;
            }
            return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
          }
        },
        xAxis: [
          {
            name: '日期',
            data: arr
              .map((item) => item.data.yearqtr)
              .sort()
              .filter((time, index, array) => {
                return index === 0 || time !== array[index - 1]; // 移除重复的时间
              })
          }
        ],
        series: line,
        yAxis: [
          {
            max: 1,
            formatter: function (value, index) {
              return `${value * 100}%`;
            }
          }
        ]
      });
    },

    /**
     * 行业持仓分析FOF选择近年时间
     */
    changeFOFPositionParamsRadio (row) {
      this.FOFPosition.radio = [row[row.length - 1]];
      this.FOFPosition.date = [];
      if (this.FOFPosition.radio.length) {
        this.FOFPosition.params.startFrom = Number(this.moment().subtract(this.FOFPosition.radio, 'years').format('YYYYMMDD'));
        this.FOFPosition.params.endTo = Number(this.moment().format('YYYYMMDD'));
      } else {
        this.FOFPosition.params.startFrom = Number(this.moment(this.$route.query.startDate).format('YYYYMMDD'));
        this.FOFPosition.params.endTo = Number(this.moment(this.$route.query.endDate).format('YYYYMMDD'));
      }
      this.getObjectFOFIndustryOperation();
      this.getObjectFOFIndustryOperation1();
    },

    /**
     * 行业持仓分析FOF选择时间区间
     */
    changeFOFPositionParamsDate () {
      this.FOFPosition.radio = [];
      this.FOFPosition.visible = false;
      if (this.FOFPosition.date.length) {
        this.FOFPosition.params.startFrom = Number(Number(this.moment(this.FOFPosition.date[0]).format('YYYYMMDD')));
        this.FOFPosition.params.endTo = Number(this.moment(this.FOFPosition.date[1]).format('YYYYMMDD'));
      } else {
        this.FOFPosition.params.startFrom = Number(this.moment(this.$route.query.startDate).format('YYYYMMDD'));
        this.FOFPosition.params.endTo = Number(this.moment(this.$route.query.endDate).format('YYYYMMDD'));
      }
      this.getObjectFOFIndustryOperation();
      this.getObjectFOFIndustryOperation1();
    },

    /**
     * FOF持仓分析
     */
    async getObjectFOFIndustryOperation () {
      this.FOFPosition.codeList = [{ label: '整体', value: 'all' }];
      this.FOFPosition.code = 'all';
      this.FOFPosition.loading = true;
      let params = {
        ...this.FOFPosition.params
      };
      params.industryStandard = this.FOFPosition.industryStandard;
      // 颗粒度数据
      return getObjectFOFIndustryOperation(params).then((res) => {
        if (res.code === 200) {
          this.FOFPosition.showEmpty = false;
          this.FOFPosition.tableData = res.data.rows.filter((v) => v.data['weight'] !== 'nan');
          this.FOFPosition.codeList = this.FOFPosition.codeList.concat(
            Array.from(
              new Set(
                res.data.rows
                  .map((v) => {
                    if (v.data.name && v.data.code) return { label: v.data.name, value: v.data.code };
                  })
                  .map(JSON.stringify)
              )
            ).map(JSON.parse)
          );
        } else {
          this.FOFPosition.showEmpty = true;
        }

        return res;
      });
    },
    async getObjectFOFIndustryOperation1 () {
      if (this.FOFPosition.codeList?.length == 0) {
        this.FOFPosition.codeList = [{ label: '整体', value: 'all' }];
        this.FOFPosition.code = 'all';
      }
      this.FOFPosition.loading = true;
      let params = {
        ...this.FOFPosition.params
      };
      params.industryStandard = this.FOFPosition.industryStandard;
      // 整体
      params.selectedCuts = '';
      return getObjectFOFIndustryOperation(params).then((res) => {
        if (res.code === 200) {
          this.FOFPosition.showEmpty = false;
          this.FOFPosition.allTableData = res.data.rows.filter((v) => v.data['weight'] !== 'nan');
          this.getFOFIndustryChartData();
        } else {
          this.FOFPosition.showEmpty = true;
        }
        return res;
      });
    },

    /**
     * 获取行业持仓分析FOF图表
     */
    getFOFIndustryChartData () {
      this.FOFPosition.loading = false;
      let arr =
        this.FOFPosition.code === 'all'
          ? this.FOFPosition.allTableData
          : this.FOFPosition.tableData.filter((v) => v.data.code === this.FOFPosition.code);
      this.getIndustryData('FOF', arr);
      const data = {};
      let line = [];
      arr.forEach((item) => {
        if (item.data.industryName == 'nan') {
          item.data.industryName = '其他'
        }
        if (!data[item.data.industryName]) {
          data[item.data.industryName] = [];
        }
        data[item.data.industryName].push(
          item.data.weight === 'nan' ? [item.data['date'], 0] : [item.data['date'], item.data['weight']]
        );
      });
      let dataList = arr
        .map((item) => item.data.date)
        .sort()
        .filter((time, index, array) => {
          return index === 0 || time !== array[index - 1]; // 移除重复的时间
        })
      // console.log(data);
      for (let i = 0; i < dataList.length; i++) {
        for (const item in data) {
          if (data[item].findIndex(items => items[0] == dataList[i]) < 0) {
            data[item].push([dataList[i], 0])
          }

        }
      }
      for (const item in data) {
        data[item].sort((a, b) => {
          if (a[0] > b[0]) return -1
          else return 1
        })
      }
      for (let key in data) {
        line.push({
          name: key,
          type: 'line',
          stack: 'Total',
          emphasis: {
            focus: 'series'
          },
          lineStyle: {
            width: 1
          },
          areaStyle: {},
          data: data[key]
        });
      }
      const legendNames = [];
      line.forEach((item) => {
        if (item.data.length !== 1) {
          item.symbol = 'none';
        }
        legendNames.push(item.name);
      });
      this.FOFPosition.chart = lineChartOption({
        grid: { left: '124px', right: '48px', top: '24px', bottom: '60px' }, // 位置
        dataZoom: true,
        toolbox: true,
        legend: {
          data: legendNames,
          type: 'plain'
        },
        tooltip: {
          formatter: function (obj) {
            obj = obj
              .filter((v) => Number((Number(v.data?.[1]) * 100).toFixed(2)) > 0)
              .sort((a, b) => (Number(a.data?.[1]) > Number(b.data?.[1]) ? -1 : 1));
            let value = `<div style="font-size:14px;">` + obj?.[0]?.axisValue + `</div>`;
            for (let i = 0; i < obj.length; i++) {
              value +=
                `<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
                `<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
                obj?.[i].color +
                `;"></div>` +
                `<div style="font-family: PingFang SC;">` +
                obj?.[i].seriesName +
                '</div></div>' +
                `<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
                (Number(obj?.[i].value?.[1]) * 100).toFixed(2) +
                '%</div>' +
                `</div>`;
            }
            return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
          }
        },
        xAxis: [
          {
            name: '日期',
            data: arr
              .map((item) => item.data.date)
              .sort()
              .filter((time, index, array) => {
                return index === 0 || time !== array[index - 1]; // 移除重复的时间
              })
          }
        ],
        series: line,
        yAxis: [
          {
            max: 1,
            formatter: function (value, index) {
              return `${value * 100}%`;
            }
          }
        ]
      });
    },

    /**
     * 行业持仓分析MOM选择近年时间
     */
    changeMOMPositionParamsRadio (row) {
      this.MOMPosition.radio = [row[row.length - 1]];
      this.MOMPosition.date = [];
      if (this.MOMPosition.radio.length) {
        this.MOMPosition.params.startFrom = Number(this.moment().subtract(this.MOMPosition.radio, 'years').format('YYYYMMDD'));
        this.MOMPosition.params.endTo = Number(this.moment().format('YYYYMMDD'));
      } else {
        this.MOMPosition.params.startFrom = Number(this.moment(this.$route.query.startDate).format('YYYYMMDD'));
        this.MOMPosition.params.endTo = Number(this.moment(this.$route.query.endDate).format('YYYYMMDD'));
      }
      this.getObjectFOFIndustryOperation();
      this.getObjectFOFIndustryOperation1();
    },

    /**
     * 行业持仓分析MOM选择时间区间
     */
    changeMOMPositionParamsDate () {
      this.MOMPosition.radio = [];
      this.MOMPosition.visible = false;
      if (this.MOMPosition.date.length) {
        this.MOMPosition.params.startFrom = Number(Number(this.moment(this.MOMPosition.date[0]).format('YYYYMMDD')));
        this.MOMPosition.params.endTo = Number(this.moment(this.MOMPosition.date[1]).format('YYYYMMDD'));
      } else {
        this.MOMPosition.params.startFrom = Number(this.moment(this.$route.query.startDate).format('YYYYMMDD'));
        this.MOMPosition.params.endTo = Number(this.moment(this.$route.query.endDate).format('YYYYMMDD'));
      }
      this.getObjectMOMIndustryOperation();
    },

    /**
     * MOM持仓分析
     */
    async getObjectMOMIndustryOperation () {
      this.MOMPosition.loading = true;
      // industryStandard
      this.MOMPosition.codeList = [{ label: '整体', value: 'all' }];
      this.MOMPosition.code = 'all';
      let params = {
        ...JSON.parse(JSON.stringify(this.MOMPosition.params))
      };
      params.industryStandard = this.MOMPosition.industryStandard;
      // 颗粒度数据
      return getObjectIndustry(params).then((res) => {
        this.MOMPosition.loading = false;
        if (res.code === 200) {
          this.MOMPosition.showEmpty = false;
          this.MOMPosition.tableData = res.data.rows.filter((v) => v.data['weight'] !== 'nan');
          // this.MOMPosition.allTableData = res.data.rows.filter((v) => v.data['index_rate'] !== 'nan');
          // console.log(this.MOMPosition,'.ssssssssssssssssss10')
          this.MOMPosition.codeList = this.MOMPosition.codeList.concat(
            Array.from(
              new Set(
                res.data.rows
                  .map((v) => {
                    return { label: v.data.code, value: v.data.code };
                  })
                  .map(JSON.stringify)
              )
            ).map(JSON.parse)
          );
          this.MOMPosition.code = this.MOMPosition.codeList[0].value;
        } else {
          this.MOMPosition.showEmpty = true;
        }
        return res;
      });
    },
    async getObjectMOMIndustryOperation1 () {
      this.MOMPosition.loading = true;
      if (this.MOMPosition.codeList?.length == 0) {
        this.MOMPosition.codeList = [{ label: '整体', value: 'all' }];
      }
      this.MOMPosition.code = 'all';
      let params = {
        ...JSON.parse(JSON.stringify(this.MOMPosition.params))
      };
      // 整体
      params.selectedCuts = '';
      params.industryStandard = this.MOMPosition.industryStandard;
      return getObjectIndustry(params).then((res) => {
        this.MOMPosition.loading = false;
        if (res.code === 200) {
          this.MOMPosition.showEmpty = false;
          // this.MOMPosition.tableData = res.data.rows.filter((v) => v.data['weight'] !== 'nan');
          // console.log(this.MOMPosition,'.ssssssssssssssssss1')
          this.MOMPosition.allTableData = res.data.rows.filter((v) => v.data['index_rate'] !== 'nan');
          this.getMOMIndustryChartData();
        } else {
          this.MOMPosition.showEmpty = true;
        }
        return res;
      });
    },
    async getObjectMOMIndustryOperationIndustryOnChange () {
      // console.log('****')
      await this.getObjectMOMIndustryOperation();
      await this.getObjectMOMIndustryOperation1();
    },

    /**
     * 获取行业持仓分析图表
     */
    getMOMIndustryChartData () {
      // console.log(this.MOMPosition,'.sssssssssssssssss2s')
      let arr =
        this.MOMPosition.code === 'all'
          ? this.MOMPosition.allTableData.filter((v) => v.data.code === this.MOMPosition.code)
          : this.MOMPosition.tableData.filter((v) => v.data.code === this.MOMPosition.code);
      // console.log(this.MOMPosition.allTableData.filter((v) => v.data.date == '2024-04-06'))
      this.getIndustryData('MOM', arr);
      let data = {};
      let line = [];
      arr.forEach((item) => {
        if (item.data.industryName == 'nan') {
          item.data.industryName = '其他'
        }
        if (!data[item.data.industryName]) {
          data[item.data.industryName] = [];
        }
        data[item.data.industryName].push(
          item.data.weight === 'nan' ? [item.data['date'], 0] : [item.data['date'], item.data['weight']]
        );
      });
      let dataList = arr
        .map((item) => item.data.date)
        .sort()
        .filter((time, index, array) => {
          return index === 0 || time !== array[index - 1]; // 移除重复的时间
        })
      // console.log(data);
      for (let i = 0; i < dataList.length; i++) {
        for (const item in data) {
          if (data[item].findIndex(items => items[0] == dataList[i]) < 0) {
            data[item].push([dataList[i], 0])
          }

        }
      }
      for (const item in data) {
        data[item].sort((a, b) => {
          if (a[0] > b[0]) return -1
          else return 1
        })
      }
      // console.log(data)
      for (let key in data) {
        line.push({
          name: key,
          type: 'line',
          stack: 'Total',
          emphasis: {
            focus: 'series'
          },
          lineStyle: {
            width: 1
          },
          areaStyle: {},
          data: data[key]
        });
      }
      // console.log(line)
      const legendNames = [];
      line.forEach((item) => {
        if (item.data.length !== 1) {
          item.symbol = 'none';
        }
        legendNames.push(item.name);
      });
      this.MOMPosition.chart = lineChartOption({
        grid: { left: '124px', right: '48px', top: '24px', bottom: '60px' }, // 位置
        dataZoom: true,
        toolbox: true,
        legend: {
          data: legendNames,
          type: 'plain'
        },
        tooltip: {
          formatter: function (obj) {
            // console.log(obj);
            obj = obj
              .filter((v) => Number((Number(v.data?.[1]) * 100).toFixed(2)) > 0)
              .sort((a, b) => (Number(a.data[1]) > Number(b.data[1]) ? -1 : 1));
            let value = `<div style="font-size:14px;">` + obj?.[0]?.axisValue + `</div>`;
            for (let i = 0; i < obj.length; i++) {
              value +=
                `<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
                `<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
                obj?.[i].color +
                `;"></div>` +
                `<div style="font-family: PingFang SC;">` +
                obj?.[i].seriesName +
                '</div></div>' +
                `<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
                (Number(obj?.[i].value?.[1]) * 100).toFixed(2) +
                '%</div>' +
                `</div>`;
            }
            return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
          }
        },
        xAxis: [
          {
            name: '日期',
            data: arr
              .map((item) => item.data.date)
              .sort()
              .filter((time, index, array) => {
                return index === 0 || time !== array[index - 1]; // 移除重复的时间
              })
          }
        ],
        series: line,
        yAxis: [
          {
            max: 1,
            formatter: function (value, index) {
              return `${value * 100}%`;
            }
          }
        ]
      });
    },

    /**
     * 获取长期持股行业分布
     */
    getIndustryShareholdingChart (arr) {
      this.shareholding.numberChart = {
        grid: { left: '24px', right: '48px', top: '24px', bottom: '36px' },
        tooltip: {
          trigger: 'item',
          backgroundColor: '#ffffff',
          textStyle: {
            color: '#333333'
          }
        },
        legend: {
          show: false,
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            type: 'pie',
            radius: '80%',
            data: arr.map((item) => {
              return { value: item.data.number, name: item.data.swlevel1 };
            }),
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      };
      this.shareholding.weightChart = {
        grid: { left: '24px', right: '48px', top: '50px', bottom: '36px' },
        tooltip: {
          trigger: 'item',
          backgroundColor: '#ffffff',
          textStyle: {
            color: '#333333'
          }
        },
        legend: {
          show: false,
          orient: 'vertical',
          left: 'left'
        },

        series: [
          {
            type: 'pie',
            radius: '80%',
            data: arr.map((item) => {
              return { value: (Number(item.data.weight) * 100).toFixed(4), name: item.data.swlevel1 };
            }),
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      };
    },

    /**
     * 长期持股行业分布数据
     */
    getIndustryShareholdingData () {
      this.shareholding.loading = true;
      return getObjectIndustryStat({ ...this.shareholding.params, reportTemplate: 'MOM' }).then((res) => {
        this.shareholding.loading = false;
        if (res.code === 200) {
          this.shareholding.countEmpty = false;
          this.shareholding.weightEmpty = false;
          this.pieChartData = res.data.rows;
          this.getIndustryShareholdingChart(res.data.rows);
        } else {
          this.shareholding.countEmpty = true;
          this.shareholding.weightEmpty = true;
        }
        return res;
      });
    },

    /**
     * MOM参数
     */
    changeMOMParams () {
      this.MOMData.params.selectedCuts = this.MOMData.object;
      this.MOMData.params.industryStandard = this.MOMData.industryStandard;
      this.getMOMChartData();
      this.getMOMChartData1();
    },

    /**
     *修改行业口径
     */
    changeMOMIndustryStandard () {
      this.MOMData.params.selectedCuts = this.MOMData.object;
      this.MOMData.params.industryStandard = this.MOMData.industryStandard;
      this.MOMData.industry = '';
      this.getMOMChartData();
      this.getMOMChartData1();
    },

    changeMOMParamsRadio (row) {
      this.MOMData.radio = [row[row.length - 1]];
      this.MOMData.date = [];
      if (this.MOMData.radio.length) {
        this.MOMData.params.startFrom = Number(this.moment().subtract(this.MOMData.radio, 'years').format('YYYYMMDD'));
        this.MOMData.params.endTo = Number(this.moment().format('YYYYMMDD'));
      } else {
        this.MOMData.params.startFrom = Number(this.moment(this.$route.query.startDate).format('YYYYMMDD'));
        this.MOMData.params.endTo = Number(this.moment(this.$route.query.endDate).format('YYYYMMDD'));
      }
      this.getMOMChartData();
      this.getMOMChartData1();
    },

    changeMOMParamsDate () {
      this.MOMData.radio = [];
      this.MOMData.visible = false;
      if (this.MOMData.date.length) {
        this.MOMData.params.startFrom = Number(Number(this.moment(this.MOMData.date[0]).format('YYYYMMDD')));
        this.MOMData.params.endTo = Number(this.moment(this.MOMData.date[1]).format('YYYYMMDD'));
      } else {
        this.MOMData.params.startFrom = Number(this.moment(this.$route.query.startDate).format('YYYYMMDD'));
        this.MOMData.params.endTo = Number(this.moment(this.$route.query.endDate).format('YYYYMMDD'));
      }
      this.getMOMChartData();
      this.getMOMChartData1();
    },

    /**
     * 获取MOM行业操作复盘数据
     */
    getMOMChartData (flag) {
      this.MOMData.codeList = [{ label: '整体', value: 'all' }];
      if (flag != 3) this.MOMData.code = 'all';
      this.MOMData.loading = true;
      let params = {
        ...JSON.parse(JSON.stringify(this.MOMData.params)),
        industryStandard: this.MOMData.industryStandard
      };
      params.selectedIndustry = '';
      return getObjectIndustryOperation(params).then((res) => {
        this.MOMData.loading = false;
        if (res.code === 200) {
          this.MOMData.showEmpty = false;
          res.data.rows.forEach((item, index) => {
            if (item.data['index_rate'] === 'nan' || item.data['index_rate'] === 'NaN' || item.data['index_rate'] === '--') {
              if (index == 0) item.data['index_rate'] = 0
              else item.data['index_rate'] = res.data.rows[index - 1].data['index_rate'];
            }
          });
          this.MOMData.codeList = this.MOMData.codeList.concat(
            Array.from(
              new Set(
                res.data.rows
                  .map((v) => {
                    return { label: v.data.name, value: v.data.code };
                  })
                  .map(JSON.stringify)
              )
            ).map(JSON.parse)
          );
          this.MOMData.tableData = res.data.rows;
          // this.getMOMChart(flag);
        } else {
          this.MOMData.showEmpty = true;
        }
        return res;
      });
    },
    getMOMChartData1 (flag) {
      if (this.MOMData.codeList?.length == 0) {
        this.MOMData.codeList = [{ label: '整体', value: 'all' }];
        this.MOMData.code = 'all';
      }
      this.MOMData.loading = true;
      let params = {
        ...JSON.parse(JSON.stringify(this.MOMData.params)),
        industryStandard: this.MOMData.industryStandard
      };
      params.selectedIndustry = '';
      params.selectedCuts = '';
      return getObjectIndustryOperation(params).then((res) => {
        this.MOMData.loading = false;
        if (res.code === 200) {
          this.MOMData.showEmpty = false;
          res.data.rows.forEach((item, index) => {
            if (item.data['index_rate'] === 'nan' || item.data['index_rate'] === 'NaN' || item.data['index_rate'] === '--') {
              item.data['index_rate'] = res.data.rows?.[index - 1]?.data['index_rate'] || 0;
            }
          });
          this.MOMData.allTableData = res.data.rows;
          this.getMOMChart(flag);
        } else {
          this.MOMData.showEmpty = true;
        }
        return res;
      });
    },
    async getMOMChartChange (flag) {
      // this.MOMData.codeList = []
      await this.getMOMChartData(flag)
      await this.getMOMChartData1(flag)
    },
    sort_change ({ column, prop, order }) {
      let fieldname = prop;
      let sortType = order;
      // 数字排序
      this.getNums(fieldname, sortType)

    },
    // 数字排序
    getNums (fieldname, sortType) {
      console.log(fieldname, this.brison.tableData[0], this.brison.tableData[0].fieldname);
      if (sortType === "ascending") {
        this.brison.tableData = this.brison.tableData.sort((a, b) => Number(b[fieldname.split('.')[0]][fieldname.split('.')[1]]) - Number(a[fieldname.split('.')[0]][fieldname.split('.')[1]]));
        // console.log(this.tableData);
      } else if (sortType === "descending") {
        this.brison.tableData = this.brison.tableData.sort((a, b) => Number(a[fieldname.split('.')[0]][fieldname.split('.')[1]]) - Number(b[fieldname.split('.')[0]][fieldname.split('.')[1]]));
      }
    },
    /**
     * 获取MOM图表
     */
    getMOMChart (flag) {

      console.log(this.MOMData, 'xxxxxxxx')
      let arr =
        this.MOMData.code === 'all' ? this.MOMData.allTableData : this.MOMData.tableData.filter((v) => v.data.code === this.MOMData.code);
      const data = {};
      let line = [];
      let weights = [];
      // console.log(arr);
      this.MOMData.MOMCareers = [];
      arr.forEach((item, index) => {
        if (!data[item.data.industryName]) {
          data[item.data.industryName] = [];
        }
        data[item.data.industryName].push(item);
      });
      for (let key in data) {
        this.MOMData.MOMCareers.push({ label: key, value: key });
      }
      if (!this.MOMData.industry && this.MOMData?.MOMCareers?.length > 0) {
        this.MOMData.industry = this.MOMData.MOMCareers[0].value;
      }
      else {
        if (flag == 1) {
          this.MOMData.industry = this.MOMData.MOMCareers[0].value;
        }
      }
      console.log(arr
        .map((item) => item.data.date)
        .sort()
        .filter((time, index, array) => {
          return index === 0 || time !== array[index - 1]; // 移除重复的时间
        }), data[this.MOMData.industry]);
      // console.log('item.dat', data);
      const temp = data[this.MOMData.industry];
      if (temp) {
        line.push({
          color: '#4096ff',
          name: this.MOMData.industry,
          yAxisIndex: 0,
          type: 'line',
          // areaStyle: {},
          lineStyle: {
            width: 3
          },
          data: temp
            ? temp.map((item) => {
              // console.log(item.data['index_rate']);
              const temp = handleData(item.data['index_rate'], true);
              return temp === '--' ? [item.data.date, 0] : [item.data.date, temp];
            })
            : []
        });
        weights.push({
          name: this.MOMData.industry,
          type: 'bar',
          yAxisIndex: 1,
          lineStyle: {
            width: 3
          },
          data: temp
            ? temp.map((item) => {
              // console.log(item.data['weight']);
              const temp = handleData(item.data['weight'], true);
              return temp === '--' ? [item.data.date, 0] : [item.data.date, temp];
            })
            : []
        });
      }
      line.forEach((item) => {
        if (item.data.length !== 1) {
          item.symbol = 'none';
        }
      });
      // console.log(weights);
      this.MOMData.chart = lineChartOption({
        dataZoom: false,
        toolbox: false,
        tooltip: {
          formatter: function (obj) {
            var value = `<div style="font-size:14px;">` + obj?.[0].axisValue + `</div>`;
            for (let i = 0; i < obj.length; i++) {
              value +=
                `<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
                `<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
                obj?.[i].color +
                `;"></div>` +
                `<div style="font-family: PingFang SC;">` +
                obj?.[i].seriesName + (i == 0 ? '指数走势' : '持仓权重') +
                '</div></div>' +
                `<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
                (Number(obj?.[i].value) || Number(obj?.[i].value?.[1]) || '--') +
                '%</div>' +
                `</div>`;
            }
            return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
          }
        },
        xAxis: [
          {
            boundaryGap: true,
            name: '日期',
            data: arr
              .map((item) => item.data.date)
              .sort()
              .filter((time, index, array) => {
                return index === 0 || time !== array[index - 1]; // 移除重复的时间
              })
          }
        ],
        series: line.concat(weights),
        yAxis: [
          {
            name: '行业走势',
            type: 'value',
            formatter: function (value, index) {
              //Y轴的自定义刻度值，对应上图
              return `${value}%`;
            }
          },
          {
            name: '权重',
            type: 'value',
            formatter: function (value, index) {
              //Y轴的自定义刻度值，对应上图
              return `${value}%`;
            }
          }
        ]
      });
    },

    /**
     * FOF参数
     */
    changeFOFParams () {
      this.FOFData.params.selectedCuts = this.FOFData.object;
      this.FOFData.params.industryStandard = this.FOFData.industryStandard;
      this.getFOFChartData();
      this.getFOFChartData1();
    },

    changeFOFIndustryStandard () {
      this.FOFData.params.selectedCuts = this.FOFData.object;
      this.FOFData.params.industryStandard = this.FOFData.industryStandard;
      this.FOFData.industry = '';
      this.getFOFChartData();
      this.getFOFChartData1();
    },

    changeFOFParamsRadio (row) {
      this.FOFData.radio = [row[row.length - 1]];
      this.FOFData.date = [];
      if (this.FOFData.radio.length) {
        this.FOFData.params.startFrom = Number(this.moment().subtract(this.FOFData.radio, 'years').format('YYYYMMDD'));
        this.FOFData.params.endTo = Number(this.moment().format('YYYYMMDD'));
      } else {
        this.FOFData.params.startFrom = Number(this.moment(this.$route.query.startDate).format('YYYYMMDD'));
        this.FOFData.params.endTo = Number(this.moment(this.$route.query.endDate).format('YYYYMMDD'));
      }
      this.getFOFChartData();
      this.getFOFChartData1();
    },

    changeFOFParamsDate () {
      this.FOFData.radio = [];
      this.FOFData.visible = false;
      if (this.FOFData.date.length) {
        this.FOFData.params.startFrom = Number(Number(this.moment(this.FOFData.date[0]).format('YYYYMMDD')));
        this.FOFData.params.endTo = Number(this.moment(this.FOFData.date[1]).format('YYYYMMDD'));
      } else {
        this.FOFData.params.startFrom = Number(this.moment(this.$route.query.startDate).format('YYYYMMDD'));
        this.FOFData.params.endTo = Number(this.moment(this.$route.query.endDate).format('YYYYMMDD'));
      }
      this.getFOFChartData();
      this.getFOFChartData1();
    },

    /**
     * 获取FOF行业操作复盘数据
     */
    getFOFChartData () {
      this.FOFData.codeList = [{ label: '整体', value: 'all' }];
      this.FOFData.code = 'all';
      this.FOFData.loading = true;
      let params = {
        ...JSON.parse(JSON.stringify(this.FOFData.params)),
        industryStandard: this.FOFData.industryStandard
      };
      params.selectedIndustry = '';
      return getObjectFOFIndustryOperation(params).then((res) => {
        this.FOFData.loading = false;
        if (res.code === 200) {
          this.FOFData.showEmpty = false;
          this.FOFData.tableData = res.data.rows;
          this.FOFData.codeList = this.FOFData.codeList.concat(
            Array.from(
              new Set(
                res.data.rows
                  .map((v) => {
                    return { label: v.data.name, value: v.data.code };
                  })
                  .map(JSON.stringify)
              )
            ).map(JSON.parse)
          );
          this.getFOFChart();
        } else {
          this.FOFData.showEmpty = true;
        }
        return res;
      });
    },
    getFOFChartData1 () {
      if (this.FOFData.codeList?.length == 0) {
        this.FOFData.codeList = [{ label: '整体', value: 'all' }];
      }
      this.FOFData.code = 'all';
      this.FOFData.loading = true;
      let params = {
        ...JSON.parse(JSON.stringify(this.FOFData.params)),
        industryStandard: this.FOFData.industryStandard
      };

      params.selectedCuts = '';
      params.selectedIndustry = '';
      return getObjectFOFIndustryOperation(params).then((res) => {
        this.FOFData.loading = false;
        if (res.code === 200) {
          this.FOFData.showEmpty = false;
          this.FOFData.allTableData = res.data.rows;
          this.getFOFChart();
        } else {
          this.FOFData.showEmpty = true;
        }
        return res;
      });
    },

    async getObjectFOFIndustryOperationIndustryChange () {
      await this.getObjectFOFIndustryOperation();
      await this.getObjectFOFIndustryOperation1();
    },

    /**
     * 获取FOF图表
     */
    getFOFChart (flag) {
      let arr =
        this.FOFData.code === 'all' ? this.FOFData.allTableData : this.FOFData.tableData.filter((v) => v.data.code === this.FOFData.code);
      const data = {};
      let line = [];
      let weights = [];
      this.FOFData.FOFCareers = [];
      arr.forEach((item, index) => {
        if (!data[item.data.industryName]) {
          data[item.data.industryName] = [];
        }
        data[item.data.industryName].push(item);
      });
      for (let key in data) {
        this.FOFData.FOFCareers.push({ label: key, value: key });
      }
      if (!this.FOFData.industry) {
        this.FOFData.industry = this.FOFData.FOFCareers && this.FOFData.FOFCareers.length > 0 ? this.FOFData.FOFCareers[0].value : '';
      }
      if (flag == 1) {
        this.FOFData.industry = this.FOFData.FOFCareers && this.FOFData.FOFCareers.length > 0 ? this.FOFData.FOFCareers[0].value : '';
      }
      if (!data[this.FOFData.industry]) return;
      const temp = data[this.FOFData.industry];
      line.push({
        color: '#4096ff',
        name: this.FOFData.industry,
        yAxisIndex: 0,
        type: 'line',
        // areaStyle: {},
        lineStyle: {
          width: 3
        },
        data: temp
          ? temp.map((item) => {
            console.log(item.data['index_rate']);
            const temp = handleData(item.data['index_rate'], true);
            return temp === '--' ? 0 : temp;
          })
          : []
      });
      weights.push({
        name: this.FOFData.industry,
        type: 'bar',
        yAxisIndex: 1,
        lineStyle: {
          width: 3
        },
        data: temp
          ? temp.map((item) => {
            console.log(item.data['weight']);
            const temp = handleData(item.data['weight'], true);
            return temp === '--' ? 0 : temp;
          })
          : []
      });
      // line.push({
      //   name: this.FOFData.industry,
      //   type: 'line',
      //   lineStyle: {
      //     width: 1
      //   },
      //   areaStyle: {},
      //   data: data[this.FOFData.industry].map((item, index) =>
      //     handleData(Number(item.data['weight']), true) === '--' && index !== 0
      //       ? handleData(Number(this.FOFData.industry[index - 1].data['weight']), true)
      //       : handleData(Number(item.data['weight']), true)
      //   )
      // });
      line.forEach((item) => {
        if (item.data.length !== 1) {
          item.symbol = 'none';
        }
      });
      this.FOFData.chart = lineChartOption({
        dataZoom: false,
        toolbox: false,
        tooltip: {
          formatter: function (obj) {
            var value = `<div style="font-size:14px;">` + obj?.[0].axisValue + `</div>`;
            for (let i = 0; i < obj.length; i++) {
              value +=
                `<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
                `<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
                obj?.[i].color +
                `;"></div>` +
                `<div style="font-family: PingFang SC;">` +
                obj?.[i].seriesName + (i == 0 ? '指数走势' : '持仓权重') +
                '</div></div>' +
                `<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
                Number(obj?.[i].value) +
                '%</div>' +
                `</div>`;
            }
            return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
          }
        },
        xAxis: [
          {
            boundaryGap: true,
            name: '日期',
            data: arr
              .map((item) => item.data.date)
              .sort()
              .filter((time, index, array) => {
                return index === 0 || time !== array[index - 1]; // 移除重复的时间
              })
          }
        ],
        series: line.concat(weights),
        yAxis: [
          {
            name: '行业走势',
            type: 'value',
            formatter: function (value, index) {
              //Y轴的自定义刻度值，对应上图
              return `${value}%`;
            }
          },
          {
            name: '权重',
            type: 'value',
            formatter: function (value, index) {
              //Y轴的自定义刻度值，对应上图
              return `${value}%`;
            }
          }
        ]
      });
    },

    /**
     * 行业配置参数
     */
    changePerformanceParams () {
      this.performance.params.industryStandard = this.performance.industryStandard;
      this.getPerformanceData();
      this.getPerformanceData1();
    },

    changePerformanceParamsRadio (row) {
      this.performance.radio = [row[row.length - 1]];
      this.performance.date = [];
      if (this.performance.radio.length) {
        this.performance.params.startFrom = Number(this.moment().subtract(this.performance.radio, 'years').format('YYYYMMDD'));
        this.performance.params.endTo = Number(this.moment().format('YYYYMMDD'));
      } else {
        this.performance.params.startFrom = Number(this.moment(this.$route.query.startDate).format('YYYYMMDD'));
        this.performance.params.endTo = Number(this.moment(this.$route.query.endDate).format('YYYYMMDD'));
      }
      this.getObjectAllIndustryOperation()
      // this.getConfigurationData();
    },

    changePerformanceParamsDate () {
      this.performance.radio = [];
      this.performance.visible = false;
      if (this.performance.date.length) {
        this.performance.params.startFrom = Number(Number(this.moment(this.performance.date[0]).format('YYYYMMDD')));
        this.performance.params.endTo = Number(this.moment(this.performance.date[1]).format('YYYYMMDD'));
      } else {
        this.performance.params.startFrom = Number(this.moment(this.$route.query.startDate).format('YYYYMMDD'));
        this.performance.params.endTo = Number(this.moment(this.$route.query.endDate).format('YYYYMMDD'));
      }
      // this.getConfigurationData();
      this.getObjectAllIndustryOperation()
    },

    /**
     * 行业配置表现
     */
    getPerformanceData () {
      // this.performance.loading = true;
      // let params = {
      //   ...JSON.parse(JSON.stringify(this.performance.params))
      // };
      // // 整体
      // return getObjectIndustry(params).then((res) => {
      //   this.performance.loading = false;
      //   if (res.code === 200) {
      //     this.performance.showEmpty = false;
      //     this.performance.tableData = res.data.rows;
      //     //获取不同行业的颗粒度
      //     res.data.rows.forEach((item, index) => {
      //       if (!this.performance.dialogData.code[item.data.industryName]) {
      //         this.performance.dialogData.code[item.data.industryName] = [];
      //       }
      //       this.performance.dialogData.code[item.data.industryName].push(item.data.code);
      //     });
      //     for (let key in this.performance.dialogData.code) {
      //       this.performance.dialogData.code[key] = Array.from(new Set(this.performance.dialogData.code[key]));
      //     }
      //   } else {
      //     this.performance.showEmpty = true;
      //   }
      //   return res;
      // });
    },
    getPerformanceData1 () {
      // this.performance.loading = true;
      // let params = {
      //   ...JSON.parse(JSON.stringify(this.performance.params))
      // };
      // params.selectedCuts = '';
      // return getObjectIndustry(params).then((res) => {
      //   this.performance.loading = false;
      //   if (res.code === 200) {
      //     this.performance.showEmpty = false;
      //     this.performance.allTableData = res.data.rows;
      //     this.getBarCharts();
      //   } else {
      //     this.performance.showEmpty = true;
      //   }
      //   return res;
      // });
    },

    /**
     * 获取整体归因表格数据
     */
    getObjectIndustryBrison () {
      this.brison.loading = true;
      this.brison.params.reportID = Number(this.$route.query.id);
      this.brison.params.industryStandard = this.brison.params.industryStandard;
      this.brison.params.reportTemplate = (this.$route.query?.reportTemplate);
      this.brison.params.selectedCuts = this.$route.query.graininess;
      this.brison.params.startFrom = Number(this.moment(this.$route.query.startDate).format('YYYYMMDD'));
      this.brison.params.endTo = Number(this.moment(this.$route.query.endDate).format('YYYYMMDD'));
      return getObjectIndustryBrison(this.brison.params).then((res) => {
        this.brison.loading = false;
        if (res.code === 200) {
          this.brison.tableData = res.data.rows.map((item) => {
            // console.log(item.data.industry_name);
            if (item?.data?.industry_name == '汇总') {
              // console.log("object");
              return {
                data: {
                  ...item.data,
                  // "weight": item.data.weight,
                  // "stock_return": item.data.stock_return,
                  // "marketValueGainLoss": item.data.marketValueGainLoss,
                  rp: '--',
                  // "industry_hold_return": item.data.industry_hold_return,
                  // "date": item.data.date,
                  // "industry_name": item.data?.industry_name || '汇总',
                  // "industry_code": item.data.industry_code,
                  // "excessReturn": item.data.excessReturn,
                  industry_index_return: '--',
                  // "total_buy": item.data.total_buy,
                  // "chaoqianpei": item.data.chaoqianpei,
                  // "meanWeight": item.data?.meanWeight,
                  // "changeWeightMean": item.data.changeWeightMean,
                },
                parentRowID: item.parentRowID,
              }
            }
            else {
              return {
                ...item
              }
            }
          })
          console.log(this.brison.tableData);
          this.brison.oldTableData = this.brison.tableData?.sort((a, b) => Number(b.data?.meanWeight) - Number(a.data?.meanWeight));
          return res.data.rows;
        } else {
          this.brison.oldTableData = this.brison.tableData = [];
          return [];
        }
      });
    },

    /**
     * 切换时间
     */
    changeTime (time) {
      this.getBarCharts();
      this.getArrowCharts();
    },

    /**
     * 柱状图
     */
    getBarCharts () {
      let arr = this.performance.allTableData;
      let data = {};
      arr.forEach((item) => {
        if (!data[item.data.date]) {
          data[item.data.date] = [];
        }
        data[item.data.date].push(Number(item.data.weight));
      });
      // console.log(arr, this.performance.allTableData);
      let dataBar = [];
      let date = [];
      for (let key in data) {
        let total = 0;
        data[key].forEach((item) => {
          total += item;
        });
        dataBar.push(handleData(total, true));
        date.push(key);
      }
      if (date.length > 200) {
        date = date.slice(date.length - 200, date.length);
        console.log(date);
      }
      //1
      this.performance.allocationBar = barChartOption({
        toolbox: 'none',
        grid: {
          left: '34px',
          top: '8px',
          bottom: '12px',
          right: '8px'
        },
        xAxis: [
          {
            show: true,
            offset: 0,
            type: 'category',
            axisLabel: { show: false },
            boundaryGap: false,
            data: date
          }
        ],
        yAxis: [
          {
            show: true,
            type: 'value',
            min: 0,
            max: 100,
            formatter: function (params) {
              return params + '%';
            }
          }
        ],
        dataZoom: {
          // 这个dataZoom组件，默认控制x轴。
          type: 'slider', // 这个 dataZoom 组件是 slider 型 dataZoom 组件
          start: this.performance.dataZoom.start, // 左边在 10% 的位置。
          end: this.performance.dataZoom.end, // 右边在 60% 的位置。
          show: false
        },
        tooltip: {
          trigger: 'axis',
          type: 'shadow',
          formatter: function (params) {
            return `季度：${params[0].name} <br/> 总权重：${params[0].value}%`;
          }
        },
        series: [
          {
            name: '配置总额',
            data: dataBar,
            type: 'bar',
            barWidth: '15px',
            itemStyle: {
              barBorderRadius: [15, 15, 0, 0],
              borderWidth: 0,
              color: new echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1, // 渐变方向，这里表示从上到下
                [
                  { offset: 0, color: '#FFAB3E' }, // 渐变起始颜色
                  { offset: 1, color: '#FFC67D' } // 渐变结束颜色
                ]
              )
            }
          }
        ]
      });
    },
    changePerformanceParams2 () {
      this.performance.params.industryStandard = this.performance.industryStandard;
      // this.getPerformanceData();
      // this.getPerformanceData1();
      this.getObjectAllIndustryOperation();
    },
    // 三角方块图
    getObjectAllIndustryOperation () {
      let that = this
      this.performance.loading = true;
      this.performance.showEmpty = true;
      let params = {
        ...this.performance.params,
        industryStandard: this.performance.industryStandard,
        reportTemplate: this.$route.query.reportTemplate,
      };
      // 全部数据
      params.selectedCuts = '';
      return getObjectAllIndustryOperation(params).then((res) => {
        console.log(res.data);
        that.performance.loading = false;
        if (res.code === 200) {
          that.performance.showEmpty = false;
          that.performance.showEmpty = false;
          that.performance.allTableData = res.data.rows;
          that.getArrowCharts();
          that.getBarCharts();

          return res.data.rows;
        }
        return 'error';
      });
    },
    /**
     * 箭头图
     */
    getArrowCharts () {
      let data = this.performance.allTableData;
      this.performance.dialogData.code = [];
      // console.log(data);
      // 对原始数据进行排序
      let sortData = data.sort((a, b) => {
        if (a.data.date.indexOf('Q') > -1 && b.data.date.indexOf('Q') > -1) {
          return a.data.date < b.data.date ? -1 : 1;
        }
        else {
          return this.moment(this.moment(a.data.date, 'YYYY-MM-DD').format()).isBefore(this.moment(b.data.date, 'YYYY-MM-DD').format())
            ? -1
            : 1;
        }

      });
      // 获取季度列表
      let date = Array.from(new Set(sortData.map((v) => v.data.date)));
      if (date.length > 200) {
        date = date.slice(date.length - 200, date.length);
        console.log(date);
      }

      // 对数组去重，获取行业列表
      let industries = Array.from(new Set(sortData.map((v) => v.data.industryName)));
      this.performance.dialogData.industries = industries;
      // 处理图表数据-serise-data
      let tableData = [];
      // 格式化数据，拼接为三角圆圈图所需要的数据格
      sortData.map((item) => {
        let index = industries.indexOf(item.data.industryName);
        tableData.push([
          item.data.date,
          index,
          handleData(item.data.industry_return, true),
          Number(handleData(item.data.weight, true)),
          handleData(item.data.industry_return, true) - 0 > 0 ? 'True' : 'False'
        ]);
      });
      this.performance.allocation = triangleCircleOption({
        toolbox: false,
        xAxis: [{ type: 'category', data: date, triggerEvent: true }],
        yAxis: [
          {
            type: 'value',
            min: 0,
            max: industries.length - 1,
            interval: 1,
            triggerEvent: true,
            formatter: function (value, index) {
              if (value < industries.length) {
                return industries[value].toString();
              }
            }
          }
        ],
        tooltip: {
          trigger: 'item',
          axisPointer: {
            type: 'cross'
          },
          formatter (params) {
            return (
              '日期：' +
              params.data[0] +
              '季度，行业：' +
              industries[params.data[1]] +
              '，权重：' +
              Number(params.data[3]).toFixed(2) +
              '%，行业估算收益率：' +
              parseInt(params.data[2] * 100).toFixed(2) +
              '%'
            );
          }
        },
        dataZoom: {
          // 这个dataZoom组件，默认控制x轴。
          type: 'slider', // 这个 dataZoom 组件是 slider 型 dataZoom 组件
          start: 0, // 左边在 10% 的位置。
          end: 100 // 右边在 60% 的位置。
        },
        series: [
          {
            name: '大类资产配置',
            type: 'scatter',
            data: tableData,
            symbolSize: 5
          }
        ]
      });
      // console.log(this.performance.allocation);
    },

    /**
     * 获取弹窗图表
     */
    getData () {
      this.histogramOptions = barChartOption({
        grid: { left: '24px', right: '48px', top: '24px', bottom: '36px' }, // 位置
        dataZoom: true,
        toolbox: false,
        legend: {
          show: false
        },
        xAxis: [
          {
            data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
          }
        ],
        yAxis: [{}],
        series: [
          {
            type: 'bar',
            data: [23, 24, 18, 25, 27, 28, 25]
          }
        ]
      });
    },

    /**
     * 计算展示时间
     */
    handDate (startDate, endDate) {
      let start = this.moment(startDate);
      let end = this.moment(endDate);
      let diffInYears = end.diff(start, 'years');

      if (diffInYears < 1) {
        this.showOneYear = false;
        this.showTwoYear = false;
        this.showThreeYear = false;
      }

      if (diffInYears === 1) {
        this.showOneYear = true;
        this.showTwoYear = false;
        this.showThreeYear = false;
      }

      if (diffInYears === 2) {
        this.showOneYear = true;
        this.showTwoYear = true;
        this.showThreeYear = false;
      }

      if (diffInYears >= 3) {
        this.showOneYear = true;
        this.showTwoYear = true;
        this.showThreeYear = true;
      }
    },

    async uploadPage () {
      let that = this;
      that.handDate(that.$route.query.startDate, that.$route.query.endDate);
      await that.getParams();
      that.MOMData.object = that.FOFData.object = that.$route.query.graininess;
      if (that.$route.query.reportTemplate === 'MOM') {
        that.showFOF = false;
        that.getMOMChartData();
        that.getMOMChartData1();
        that.getObjectMOMIndustryOperation();
        that.getObjectMOMIndustryOperation1();
        that.getObjectAllIndustryOperation()
      }
      if (that.$route.query.reportTemplate === 'FOF') {
        that.showMOM = false;
        that.getFOFChartData();
        that.getFOFChartData1();
        that.getObjectFOFIndustryOperation();
        that.getObjectFOFIndustryOperation1();
        that.getObjectAllIndustryOperation()
      }
      if (that.$route.query.reportTemplate === 'ALL') {
        that.showMOM = true;
        that.showFOF = true;
        that.getObjectFOFIndustryOperation();
        that.getObjectFOFIndustryOperation1();
        that.getObjectMOMIndustryOperation();
        that.getObjectMOMIndustryOperation1();
        that.getConfigurationData();
        that.getMOMChartData();
        that.getMOMChartData1();
        that.getFOFChartData();
        that.getFOFChartData1();
        that.getObjectAllIndustryOperation()
      }
      setTimeout(() => {
        that.getPerformanceData();
        that.getPerformanceData1();
        setTimeout(() => {
          that.getIndustryShareholdingData();
          this.$refs.longHold.getIndustryShareholdingData(),
            setTimeout(() => {
              that.getObjectIndustryBrison();
            }, 500);
        }, 500);
      }, 500);
    },

    /**
     * 打开行业配置表现弹窗1
     * @param params
     */
    openDialog (params) {
      if (params.componentType === 'yAxis') {
        const row = params.value;
        this.performance.dialogData.dialogTitle = this.performance.dialogData.industries[row];
        this.showGranularity = true;
      }
    },

    /**
     * 打开行业配置表现弹窗2
     * @param params
     */
    startGet () {
      let that = this;
      that.showGranularity = false;
      that.showGranularityChart = true;
      //滤数据条件为 【行业、颗粒度】
      let arr = that.performance.tableData.filter(
        (v) =>
          v.data.industryName === that.performance.dialogData.dialogTitle &&
          JSON.stringify(that.performance.dialogData.selectCode).includes(v.data.code)
      );
      // 对数据进行排序
      let sortData = arr.sort((a, b) => {
        return that.moment(that.moment(a.data.date, 'YYYY-MM-DD').format()).isBefore(that.moment(b.data.date, 'YYYY-MM-DD').format())
          ? -1
          : 1;
      });

      // 获取季度列表
      let date = Array.from(new Set(sortData.map((v) => v.data.date)));
      let tableDataBar = []; //柱状图数据
      let tableData = []; //三角圆圈图数据
      let data = {};
      // 格式化数据，拼接为三角圆圈图所需要的数据格
      sortData.forEach((item) => {
        let index = that.performance.dialogData.selectCode.indexOf(item.data.code);
        tableData.push([
          item.data.date,
          index,
          handleData(item.data.industry_return, true),
          Number(handleData(item.data.weight, true)),
          handleData(item.data.industry_return, true) - 0 > 0 ? 'True' : 'False'
        ]);
        if (!data[item.data.date]) {
          data[item.data.date] = [];
        }
        data[item.data.date].push(Number(item.data.weight));
      });

      for (let key in data) {
        let total = 0;
        data[key].forEach((item) => {
          total += item;
        });
        tableDataBar.push(handleData(total, true));
      }
      this.performance.dialogData.optionBar = barChartOption({
        toolbox: 'none',
        grid: {
          left: '34px',
          top: '8px',
          bottom: '12px',
          right: '8px'
        },
        xAxis: [
          {
            show: true,
            offset: 0,
            type: 'category',
            axisLabel: { show: false },
            boundaryGap: false,
            data: date
          }
        ],
        yAxis: [
          {
            show: true,
            type: 'value',
            min: 0,
            max: 100,
            formatter: function (params) {
              return params + '%';
            }
          }
        ],
        dataZoom: {
          // 这个dataZoom组件，默认控制x轴。
          type: 'slider', // 这个 dataZoom 组件是 slider 型 dataZoom 组件
          start: this.performance.dialogData.dataZoom.start, // 左边在 10% 的位置。
          end: this.performance.dialogData.dataZoom.end, // 右边在 60% 的位置。
          show: false
        },
        tooltip: {
          trigger: 'axis',
          type: 'shadow',
          formatter: function (params) {
            return `季度：${params[0].name} <br/> 总权重：${params[0].value}%`;
          }
        },
        series: [
          {
            name: '配置总额',
            data: tableDataBar,
            type: 'bar',
            barWidth: '15px',
            itemStyle: {
              barBorderRadius: [15, 15, 0, 0],
              borderWidth: 0,
              color: new echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1, // 渐变方向，这里表示从上到下
                [
                  { offset: 0, color: '#FFAB3E' }, // 渐变起始颜色
                  { offset: 1, color: '#FFC67D' } // 渐变结束颜色
                ]
              )
            }
          }
        ]
      });

      that.performance.dialogData.option = triangleCircleOption({
        toolbox: false,
        xAxis: [{ type: 'category', data: date }],
        yAxis: [
          {
            type: 'value',
            min: 0,
            max: that.performance.dialogData.selectCode.length - 1,
            interval: 1,
            formatter: function (value, index) {
              if (value < that.performance.dialogData.selectCode.length) {
                return that.performance.dialogData.selectCode[value].toString();
              }
            }
          }
        ],
        tooltip: {
          trigger: 'item',
          axisPointer: {
            type: 'cross'
          },
          formatter (params) {
            return (
              '日期：' +
              params.data[0] +
              '季度，颗粒度：' +
              that.performance.dialogData.selectCode[params.data[1]] +
              '，权重：' +
              Number(params.data[3]).toFixed(2) +
              '%，颗粒度估算收益率：' +
              parseInt(params.data[2] * 100).toFixed(2) +
              '%'
            );
          }
        },
        dataZoom: {
          // 这个dataZoom组件，默认控制x轴。
          type: 'slider', // 这个 dataZoom 组件是 slider 型 dataZoom 组件
          start: 0, // 左边在 10% 的位置。
          end: 100 // 右边在 60% 的位置。
        },
        series: [
          {
            name: '大类资产配置',
            type: 'scatter',
            data: tableData,
            symbolSize: 5
          }
        ]
      });
    },

    /**
     * 关闭弹窗1
     */
    closeDialogOne () {
      this.performance.dialogData.selectCode = [];
      this.showGranularity = false;
    },

    /**
     * 关闭弹窗2
     */
    closeDialogTwo () {
      this.performance.dialogData.selectCode = [];
      this.performance.dialogData.dataZoom = {
        start: 0,
        end: 100
      };
      this.showGranularityChart = false;
    },
    async waitDom () {
      return new Promise((resovel) => {
        setTimeout(() => {
          resovel('等待dom加载完成');
        }, 1000);
      });
    },
    async createPrintWord () {
      let that = this;
      that.handDate(that.$route.query.startDate, that.$route.query.endDate);
      await that.getParams();
      that.MOMData.object = that.FOFData.object = that.$route.query.graininess;

      const getImg = (ref) => {
        const chart1 = this.$refs[ref];
        if (!chart1) return null;
        chart1.mergeOptions({ toolbox: { show: false } });
        const height = chart1.$el.clientHeight;
        const width = chart1.$el.clientWidth;
        const img = chart1.getConnectedDataURL({
          type: 'jpg',
          pixelRatio: 2,
          backgroundColor: '#fff',
          excludeComponents: ['dataZoom']
        });
        const chartImg = exportChart(img, { width, height });
        chart1.mergeOptions({ toolbox: { show: true } });
        return chartImg;
      };
      if (this.$route.query.reportTemplate === 'MOM') {
        return Promise.all([
          this.waitDom(),
          this.getObjectMOMIndustryOperation(),
          this.getObjectMOMIndustryOperation1(),
          this.getIndustryShareholdingData(),
          this.$refs.longHold.getIndustryShareholdingData(),
          this.getObjectIndustryBrison(),
          this.getMOMChartData(),
          this.getMOMChartData1(),
          this.getPerformanceData(),
          this.getPerformanceData1()
        ]).then((arr) => {
          return new Promise((resolve, reject) => {
            const head = exportFirstTitle('三、行业持仓分析');
            const subhead3 = exportTitle('MOM行业持仓分析');
            const chartimg3 = getImg('ChartComponent_momhyccfx');
            const subhead41 = exportTitle('长期持股行业分布(MOM)-长期持股行业次数（次）');
            const chartimg41 = getImg('ChartComponent_cqcghycs');
            const subhead411 = exportTitle('长期持股行业分布(FOF)-长期持股行业次数（次）');
            const chartimg411 = getImg('ChartComponent_cqcghycs2');
            const subhead42 = exportTitle('长期持股行业分布(MOM)-长期持股行业权重（%）');
            const chartimg42 = getImg('ChartComponent_cqcghyqz');
            const subhead422 = exportTitle('长期持股行业分布(FOF)-长期持股行业权重（%）');
            const chartimg422 = getImg('ChartComponent_cqcghyqz2');
            const subhead7 = exportTitle('整体组合行业Brison归因');
            let array7 = arr[4].map((item) => {
              return item.data;
            });
            const title7 = [
              { label: '行业', value: 'industry_name' },
              { label: '市值收益(亿元)', value: 'marketValueGainLoss', format: '' },
              { label: '净买入(亿元)', value: 'total_buy', format: '' },
              { label: '实际配置权重', value: 'weight', format: '' },
              { label: '超欠配', value: 'chaoqianpei', format: '' },
              { label: '行业配置贡献', value: 'industry_hold_return', format: '' },
              { label: '行业基准收益率', value: 'industry_index_return', format: '' }
            ];
            const table7 = exportTable(title7, array7, {}, {});
            const subhead8 = exportTitle('MOM行业操作复盘');
            const chartimg8 = getImg('ChartComponent_momhyczfp');
            const subhead10 = exportTitle('行业配置表现');
            const chartimg11 = getImg('ChartComponent_hypzbx1');
            const chartimg12 = getImg('ChartComponent_hypzbx2');
            resolve([
              ...head,
              ...subhead3,
              ...chartimg3,
              ...subhead41,
              ...chartimg41,
              ...subhead411,
              ...chartimg411,
              ...subhead42,
              ...chartimg42,
              ...subhead422,
              ...chartimg422,
              ...subhead7,
              ...table7,
              ...subhead8,
              ...chartimg8,
              ...subhead10,
              ...chartimg11,
              ...chartimg12
            ]);
          });
        });
      }
      if (this.$route.query.reportTemplate === 'FOF') {
        return Promise.all([
          this.waitDom(),
          this.getObjectFOFIndustryOperation(),
          this.getObjectFOFIndustryOperation1(),
          this.getIndustryShareholdingData(),
          this.$refs.longHold.getIndustryShareholdingData(),
          this.getObjectIndustryBrison(),
          this.getFOFChartData(),
          this.getFOFChartData1(),
          this.getPerformanceData(),
          this.getPerformanceData1()
        ]).then((arr) => {
          return new Promise((resolve, reject) => {
            setTimeout(() => {
              const head = exportFirstTitle('三、行业持仓分析');
              const subhead2 = exportTitle('FOF行业持仓分析');
              const chartimg2 = getImg('ChartComponent_fofhyccfx');
              const subhead41 = exportTitle('长期持股行业分布-长期持股行业次数（次）');
              const chartimg41 = getImg('ChartComponent_cqcghycs');
              const subhead42 = exportTitle('长期持股行业分布-长期持股行业权重（%）');
              const chartimg42 = getImg('ChartComponent_cqcghyqz');
              const subhead7 = exportTitle('整体组合行业Brison归因');
              let array7 = arr[4].map((item) => {
                return item.data;
              });
              const title7 = [
                { label: '行业', value: 'industry_name' },
                { label: '市值收益(亿元)', value: 'marketValueGainLoss', format: '' },
                { label: '净买入(亿元)', value: 'total_buy', format: '' },
                { label: '实际配置权重', value: 'weight', format: '' },
                { label: '超欠配', value: 'chaoqianpei', format: '' },
                { label: '行业配置贡献', value: 'industry_hold_return', format: '' },
                { label: '行业基准收益率', value: 'industry_index_return', format: '' }
              ];
              const table7 = exportTable(title7, array7, {}, {});
              const subhead9 = exportTitle('FOF行业操作复盘');
              const chartimg9 = getImg('ChartComponent_fofhyczfp');
              const subhead10 = exportTitle('行业配置表现');
              const chartimg11 = getImg('ChartComponent_hypzbx1');
              const chartimg12 = getImg('ChartComponent_hypzbx2');

              resolve([
                ...head,
                ...subhead2,
                ...chartimg2,
                ...subhead41,
                ...chartimg41,
                ...subhead42,
                ...chartimg42,
                ...subhead7,
                ...table7,
                ...subhead9,
                ...chartimg9,
                ...subhead10,
                ...chartimg11,
                ...chartimg12
              ]);
            }, 1000);
          });
        });
      }
      if (this.$route.query.reportTemplate === 'ALL') {
        return Promise.all([
          this.waitDom(),
          this.getConfigurationData(),
          this.getObjectFOFIndustryOperation(),
          this.getObjectFOFIndustryOperation1(),
          this.getObjectMOMIndustryOperation(),
          this.getObjectMOMIndustryOperation1(),
          this.getIndustryShareholdingData(),
          this.$refs.longHold.getIndustryShareholdingData(),
          this.getObjectIndustryBrison(),
          this.getMOMChartData(),
          this.getMOMChartData1(),
          this.getFOFChartData(),
          this.getFOFChartData1(),
          this.getPerformanceData(),
          this.getPerformanceData1()
        ]).then((arr) => {
          return new Promise((resolve, reject) => {
            setTimeout(() => {
              const head = exportFirstTitle('三、行业持仓分析');
              const subhead1 = exportTitle('混合行业持仓分析');
              const chartimg1 = getImg('ChartComponent_hhhyccfx');
              const subhead2 = exportTitle('FOF行业持仓分析');
              const chartimg2 = getImg('ChartComponent_fofhyccfx');
              const subhead3 = exportTitle('MOM行业持仓分析');
              const chartimg3 = getImg('ChartComponent_momhyccfx');

              const subhead41 = exportTitle('长期持股行业分布-长期持股行业次数（次）');
              const chartimg41 = getImg('ChartComponent_cqcghycs');

              const subhead42 = exportTitle('长期持股行业分布-长期持股行业权重（%）');
              const chartimg42 = getImg('ChartComponent_cqcghyqz');

              const subhead7 = exportTitle('整体组合行业Brison归因');
              let array7 = arr[7].map((item) => {
                return item.data;
              });

              const title7 = [
                { label: '行业', value: 'industry_name' },
                { label: '市值收益(亿元)', value: 'marketValueGainLoss', format: '' },
                { label: '净买入(亿元)', value: 'total_buy', format: '' },
                { label: '实际配置权重', value: 'weight', format: '' },
                { label: '超欠配', value: 'chaoqianpei', format: '' },
                { label: '行业配置贡献', value: 'industry_hold_return', format: '' },
                { label: '行业基准收益率', value: 'industry_index_return', format: '' }
              ];
              const table7 = exportTable(title7, array7, {}, {});

              const subhead8 = exportTitle('MOM行业操作复盘');
              const chartimg8 = getImg('ChartComponent_momhyczfp');
              const subhead9 = exportTitle('FOF行业操作复盘');
              const chartimg9 = getImg('ChartComponent_fofhyczfp');
              const subhead10 = exportTitle('行业配置表现');
              const chartimg11 = getImg('ChartComponent_hypzbx1');
              const chartimg12 = getImg('ChartComponent_hypzbx2');
              resolve([
                ...head,
                ...subhead1,
                ...chartimg1,
                ...subhead2,
                ...chartimg2,
                ...subhead3,
                ...chartimg3,
                ...subhead41,
                ...chartimg41,
                ...subhead42,
                ...chartimg42,
                ...subhead7,
                ...table7,
                ...subhead8,
                ...chartimg8,
                ...subhead9,
                ...chartimg9,
                ...subhead10,
                ...chartimg11,
                ...chartimg12
              ]);
            }, 1000);
          });
        });
      }
    },
    downloadExcel (name) {
      if (name === '混合行业持仓分析') {
        const type = this.position.industryStandard == 2 ? '泰康一级行业' : '申万一级行业';
        const [nameArray, column] = this.buildExcelData(this.position.allTableData, 'yearqtr', 'industryName', 'weight');
        const format = [
          '',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p'
        ];
        const data = changColumnToRow(column, format);
        export_json_to_excel_multiHeader2([nameArray], null, data, `${name}(${type})`);
      } else if (name === 'FOF行业持仓分析') {
        let kldName = '';
        const code = this.FOFPosition.code;
        this.FOFPosition.codeList.map((item) => {
          if (item.value === code) {
            kldName = item.label;
          }
        });

        let arr =
          this.FOFPosition.code === 'all'
            ? this.FOFPosition.allTableData
            : this.FOFPosition.tableData.filter((v) => v.data.code === this.FOFPosition.code);
        const type = this.FOFPosition.industryStandard == 2 ? '泰康一级行业' : '申万一级行业';
        const [nameArray, column] = this.buildExcelData(arr, 'date', 'industryName', 'weight');
        const format = [
          '',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p'
        ];
        const data = changColumnToRow(column, format);
        export_json_to_excel_multiHeader2([nameArray], null, data, `${name}(${type})-颗粒度${kldName}`);
      } else if (name === 'MOM行业持仓分析') {
        let kldName = '';
        const code = this.MOMPosition.code;
        this.MOMPosition.codeList.map((item) => {
          if (item.value === code) {
            kldName = item.label;
          }
        });
        let arr =
          this.MOMPosition.code === 'all'
            ? this.MOMPosition.allTableData.filter((v) => v.data.code === this.MOMPosition.code)
            : this.MOMPosition.tableData.filter((v) => v.data.code === this.MOMPosition.code);
        const type = this.MOMPosition.industryStandard == 2 ? '泰康一级行业' : '申万一级行业';
        const [nameArray, column] = this.buildExcelData(arr, 'date', 'industryName', 'weight');
        const format = [
          '',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p'
        ];
        const data = changColumnToRow(column, format);
        export_json_to_excel_multiHeader2([nameArray], null, data, `${name}(${type})-颗粒度${kldName}`);
      } else if (name === '长期持股行业分布') {
        const title = [
          { label: '行业名称', value: 'swlevel1' },
          { label: '长期持股行业次数', value: 'number' },
          { label: '长期持股行业权重（%）', value: 'weight', format: 'fix4p' }
        ];
        filter_json_to_excel_inside(title, this.pieChartData, ['data'], name);
      } else if (name === '整体组合行业Brison归因') {
        const type = this.brison.params.industryStandard == 2 ? '泰康一级行业' : '申万一级行业';
        const title = [
          { label: '行业', value: 'industry_name' },
          { label: '市值收益(亿元)', value: 'marketValueGainLoss', format: '' },
          { label: '净买入(亿元)', value: 'total_buy', format: '' },
          { label: '实际配置权重', value: 'meanWeight', format: '' },
          { label: '超欠配', value: 'changeWeightMean', format: '' },
          { label: '行业配置贡献', value: 'industry_hold_return', format: '' },
          { label: '股票选择贡献', value: 'stock_return', format: '' },
          { label: '行业持仓收益率', value: 'rp', format: '' },
          { label: '行业基准收益率', value: 'industry_index_return', format: '' }
        ];
        filter_json_to_excel_inside(title, this.brison.oldTableData, ['data'], name + '(' + type + ')');
      } else if (name === 'MOM行业操作复盘') {
        let kldName = '';
        const code = this.MOMData.code;
        this.MOMData.codeList.map((item) => {
          if (item.value === code) {
            kldName = item.label;
          }
        });
        let arr =
          this.MOMData.code === 'all' ? this.MOMData.allTableData : this.MOMData.tableData.filter((v) => v.data.code === this.MOMData.code);
        const type = this.MOMData.industryStandard == 2 ? '泰康一级行业' : '申万一级行业';
        const [nameArray, column] = this.buildExcelData(arr, 'date', 'industryName', 'index_rate');
        const format = [
          '',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p'
        ];
        const data = changColumnToRow(column, format);
        export_json_to_excel_multiHeader2([nameArray], null, data, `${name}(${type})-颗粒度${kldName}`);
      } else if (name === 'FOF行业操作复盘') {
        let kldName = '';
        const code = this.FOFData.code;
        this.FOFData.codeList.map((item) => {
          if (item.value === code) {
            kldName = item.label;
          }
        });
        let arr =
          this.FOFData.code === 'all' ? this.FOFData.allTableData : this.FOFData.tableData.filter((v) => v.data.code === this.FOFData.code);
        const type = this.FOFData.industryStandard == 2 ? '泰康一级行业' : '申万一级行业';
        const [nameArray, column] = this.buildExcelData(arr, 'date', 'industryName', 'weight');
        const format = [
          '',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p'
        ];
        const data = changColumnToRow(column, format);
        export_json_to_excel_multiHeader2([nameArray], null, data, `${name}(${type})-颗粒度${kldName}`);
      } else if (name === '行业配置表现') {
        if (this.performance.allTableData) {
          const temp = {};
          this.performance.allTableData.map((item) => {
            if (Object.keys(temp).includes(item.data.industryName)) {
              temp[item.data.industryName].push(item);
            } else {
              temp[item.data.industryName] = [item];
            }
          });
          let array = [];
          Object.keys(temp).map((key) => {
            array = array.concat(temp[key]);
          });

          const type = this.performance.industryStandard == 2 ? '泰康一级行业' : '申万一级行业';
          const title = [
            { label: '时间', value: 'date', format: '' },
            { label: '行业', value: 'industryName', format: '' },
            { label: '权重', value: 'weight', format: 'fix4p' },
            { label: '行业估算收益率', value: 'industry_return', format: 'fix4p' }
          ];
          filter_json_to_excel_inside(title, array, ['data'], name + '(' + type + ')');
        }
      }
    },
    buildExcelData (array, dateKey, titleKey, valueKey) {
      if (!array || array.length === 0) {
        return [[], []];
      }

      const nameSet = new Set();
      const dateSet = new Set();
      const dataMap = new Map();

      // 预处理数据，构建 Map 以提高查找效率
      array.forEach((item) => {
        const date = item?.data[dateKey];
        const name = item?.data[titleKey];
        const value = item?.data[valueKey];

        if (date !== undefined && name !== undefined) {
          dateSet.add(date);
          nameSet.add(name);
          // 使用 `${date}_${name}` 作为键，存储值
          dataMap.set(`${date}_${name}`, value);
        }
      });

      const nameArray = [...nameSet].sort(); // 可选：排序以确保一致性
      const dateArray = [...dateSet].sort(); // 可选：排序以确保一致性
      const column = [dateArray];

      nameArray.forEach((name) => {
        const tempArray = dateArray.map((date) => {
          return dataMap.get(`${date}_${name}`) || '';
        });
        column.push(tempArray);
      });

      const header = [''].concat(nameArray);
      return [header, column];
    },
    buildExcelData2 (array, dateKey, titleKey, valueKey) {
      if (array) {
        const nameSet = new Set();
        const dateSet = new Set();
        array.map((item) => {
          // console.log(item);
          dateSet.add(item?.data[dateKey]);
          nameSet.add(item?.data[titleKey]);
        });
        const nameArray = [...nameSet];
        const dateArray = [...dateSet];
        const column = [dateArray];
        nameArray.map((name) => {
          const tempArray = [];
          dateArray.map((date) => {
            let select;
            for (let index = 0; index < array.length; index++) {
              const element = array[index];
              if (element?.data[dateKey] === date && element?.data[titleKey] === name) {
                select = element;
              }
            }
            if (select) {
              tempArray.push(select.data[valueKey]);
            } else {
              tempArray.push('');
            }
          });
          column.push(tempArray);
        });
        const name = [''].concat(nameArray);
        return [name, column];
      }
    }
  }
};
</script>

<template>
  <div>
    <!-- 混合持仓 -->
    <div class="page-box"
         v-if="showFOF && showMOM">
      <div class="flex item-center justify-between">
        <div class="area-title">混合行业持仓分析</div>
        <div class="border_table_header_search">
          <span class="selector">行业口径：</span>
          <el-select v-model="position.industryStandard"
                     class="search-security"
                     placeholder="TK内部行业"
                     @change="getConfigurationData"
                     style="width: 240px">
            <el-option v-for="item in industryCaliberOption"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"
                       style="width: 240px">
            </el-option>
          </el-select>
          <el-checkbox-group v-if="showOneYear || showTwoYear || showThreeYear"
                             v-model="position.radio"
                             size="small"
                             @change="changePositionParamsRadio">
            <el-checkbox-button v-if="showOneYear"
                                :label="1">1y</el-checkbox-button>
            <el-checkbox-button v-if="showTwoYear"
                                :label="2">2y</el-checkbox-button>
            <el-checkbox-button v-if="showThreeYear"
                                :label="3">3y</el-checkbox-button>
          </el-checkbox-group>
          <el-popover placement="bottom"
                      width="400"
                      v-model="position.visible">
            <el-date-picker v-model="position.date"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            :unlink-panels="true"
                            end-placeholder="结束日期"
                            @change="changePositionParamsDate"
                            :picker-options="pickerOptions">
            </el-date-picker>
            <el-button label=""
                       slot="reference"><i class="el-icon-date"></i></el-button>
          </el-popover>
          <img alt=""
               class="download"
               src="../../../../../assets/img/download.png"
               @click="downloadExcel('混合行业持仓分析')" />
        </div>
      </div>
      <el-divider></el-divider>
      <div class="area-body">
        <div class="chart">
          <div class="chart-card">
            <div class="chart-card_body"
                 v-loading="position.loading">
              <!-- <div class="sidebar">
                <div v-for="(item,index) in position.industries" :key="index" class="flex item-center">
                  <div :style="`background-color: ${item.color}`" class="card"/>
                  {{ item.name }}
                </div>
              </div> -->
              <v-chart autoresize
                       ref="ChartComponent_hhhyccfx"
                       v-show="!position.showEmpty"
                       :options="position.chart"
                       element-loading-background="rgba(239, 239, 239, 0.5)"
                       element-loading-spinner="el-icon-document-delete"
                       element-loading-text="暂无数据"
                       style="height: 708px; width: 100% !important" />
              <el-empty style="width: 100%"
                        v-show="position.showEmpty"
                        :image-size="200"></el-empty>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- FOF持仓 -->
    <div class="page-box"
         v-if="showFOF">
      <div class="flex item-center justify-between">
        <div class="area-title">FOF行业持仓分析</div>
        <div class="border_table_header_search">
          <span class="selector">行业口径：</span>
          <el-select v-model="FOFPosition.industryStandard"
                     class="search-security"
                     placeholder="TK内部行业"
                     @change="getObjectFOFIndustryOperationIndustryChange"
                     style="width: 240px">
            <el-option v-for="item in industryCaliberOption"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"
                       style="width: 240px">
            </el-option>
          </el-select>
          <span class="selector">颗粒度：</span>
          <el-select v-model="FOFPosition.code"
                     class="search-security"
                     placeholder="颗粒度"
                     @change="getFOFIndustryChartData"
                     style="width: 240px">
            <el-option v-for="item in FOFPosition.codeList"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"
                       style="width: 240px">
            </el-option>
          </el-select>
          <el-checkbox-group v-if="showOneYear || showTwoYear || showThreeYear"
                             v-model="FOFPosition.radio"
                             size="small"
                             @change="changeFOFPositionParamsRadio">
            <el-checkbox-button v-if="showOneYear"
                                :label="1">1y</el-checkbox-button>
            <el-checkbox-button v-if="showTwoYear"
                                :label="2">2y</el-checkbox-button>
            <el-checkbox-button v-if="showThreeYear"
                                :label="3">3y</el-checkbox-button>
          </el-checkbox-group>
          <el-popover placement="bottom"
                      width="400"
                      v-model="FOFPosition.visible">
            <el-date-picker v-model="FOFPosition.date"
                            type="daterange"
                            :unlink-panels="true"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            @change="changeFOFPositionParamsDate"
                            :picker-options="pickerOptions">
            </el-date-picker>
            <el-button label=""
                       slot="reference"><i class="el-icon-date"></i></el-button>
          </el-popover>
          <img alt=""
               class="download"
               src="../../../../../assets/img/download.png"
               @click="downloadExcel('FOF行业持仓分析')" />
        </div>
      </div>
      <el-divider></el-divider>
      <div class="area-body">
        <div class="chart">
          <div class="chart-card">
            <div class="chart-card_body"
                 v-loading="FOFPosition.loading">
              <!-- <div class="sidebar">
                <div v-for="(item,index) in FOFPosition.industries" :key="index" class="flex item-center">
                  <div :style="`background-color: ${item.color}`" class="card"/>
                  {{ item.name }}
                </div>
              </div> -->
              <v-chart autoresize
                       ref="ChartComponent_fofhyccfx"
                       v-show="!FOFPosition.showEmpty"
                       :options="FOFPosition.chart"
                       element-loading-background="rgba(239, 239, 239, 0.5)"
                       element-loading-spinner="el-icon-document-delete"
                       element-loading-text="暂无数据"
                       style="height: 708px; width: 100% !important" />
              <el-empty style="width: 100%"
                        v-show="FOFPosition.showEmpty"
                        :image-size="200"></el-empty>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- MOM持仓 -->
    <div class="page-box"
         v-if="showMOM">
      <div class="flex item-center justify-between">
        <div class="area-title">MOM行业持仓分析</div>
        <div class="border_table_header_search">
          <el-select v-model="MOMPosition.industryStandard"
                     class="search-security"
                     placeholder="TK内部行业"
                     @change="getObjectMOMIndustryOperationIndustryOnChange"
                     style="width: 240px">
            <el-option v-for="item in industryCaliberOption"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"
                       style="width: 240px">
            </el-option>
          </el-select>
          <span class="selector">颗粒度：</span>
          <el-select v-model="MOMPosition.code"
                     class="search-security"
                     placeholder="颗粒度"
                     @change="getMOMIndustryChartData"
                     style="width: 240px">
            <el-option v-for="item in MOMPosition.codeList"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"
                       style="width: 240px">
            </el-option>
          </el-select>
          <el-checkbox-group v-if="showOneYear || showTwoYear || showThreeYear"
                             v-model="MOMPosition.radio"
                             size="small"
                             @change="changeMOMPositionParamsRadio">
            <el-checkbox-button v-if="showOneYear"
                                :label="1">1y</el-checkbox-button>
            <el-checkbox-button v-if="showTwoYear"
                                :label="2">2y</el-checkbox-button>
            <el-checkbox-button v-if="showThreeYear"
                                :label="3">3y</el-checkbox-button>
          </el-checkbox-group>
          <el-popover placement="bottom"
                      width="400"
                      v-model="MOMPosition.visible">
            <el-date-picker v-model="MOMPosition.date"
                            type="daterange"
                            :unlink-panels="true"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            @change="changeMOMPositionParamsDate"
                            :picker-options="pickerOptions">
            </el-date-picker>
            <el-button label=""
                       slot="reference"><i class="el-icon-date"></i></el-button>
          </el-popover>
          <img alt=""
               class="download"
               src="../../../../../assets/img/download.png"
               @click="downloadExcel('MOM行业持仓分析')" />
        </div>
      </div>
      <el-divider></el-divider>
      <div class="area-body">
        <div class="chart">
          <div class="chart-card">
            <div class="chart-card_body"
                 v-loading="MOMPosition.loading">
              <!-- <div class="sidebar">
                <div v-for="(item,index) in MOMPosition.industries"
                :style="item.showLegend?'color:red':'color:yellow'" 
                :key="index" class="flex item-center"
                @click="outsideLegendClick(item)"
                >
                  <div :style="`background-color: ${item.color}`" class="card"/>
                  {{ item.name }}
                </div>
              </div> -->
              <v-chart autoresize
                       ref="ChartComponent_momhyccfx"
                       v-show="!MOMPosition.showEmpty"
                       :options="MOMPosition.chart"
                       element-loading-background="rgba(239, 239, 239, 0.5)"
                       element-loading-spinner="el-icon-document-delete"
                       element-loading-text="暂无数据"
                       style="height: 708px; width: 100% !important" />
              <el-empty style="width: 100%"
                        v-show="MOMPosition.showEmpty"
                        :image-size="200"></el-empty>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 长期持股行业分布  -->
    <div v-show="reportTemplate=='ALL'||reportTemplate=='MOM'"
         class="page-box">
      <div class="flex item-center justify-between">
        <div class="area-title">长期持股行业分布(MOM)</div>
        <img alt=""
             class="download"
             src="../../../../../assets/img/download.png"
             @click="downloadExcel('长期持股行业分布')" />
      </div>
      <el-divider></el-divider>
      <div class="area-body">
        <div class="area-chart"
             v-loading="shareholding.loading">
          <div class="chart-card_half">
            <div class="chart-card_title">长期持股行业次数（次）</div>
            <v-chart autoresize
                     ref="ChartComponent_cqcghycs"
                     v-show="!shareholding.countEmpty"
                     :options="shareholding.numberChart"
                     element-loading-background="rgba(239, 239, 239, 0.5)"
                     element-loading-spinner="el-icon-document-delete"
                     element-loading-text="暂无数据"
                     style="height: 340px; width: 100% !important; padding-top: 20px" />
            <el-empty v-show="shareholding.countEmpty"
                      :image-size="200"></el-empty>
          </div>
          <div class="chart-card_half">
            <div class="chart-card_title">长期持股行业权重（%）</div>
            <v-chart autoresize
                     ref="ChartComponent_cqcghyqz"
                     v-if="!shareholding.weightEmpty"
                     :options="shareholding.weightChart"
                     element-loading-background="rgba(239, 239, 239, 0.5)"
                     element-loading-spinner="el-icon-document-delete"
                     element-loading-text="暂无数据"
                     style="height: 340px; width: 100% !important; padding-top: 20px" />
            <el-empty v-else
                      :image-size="200"></el-empty>
          </div>
        </div>
      </div>
    </div>
    <!-- 长期持股行业分布  -->
    <div v-show="reportTemplate=='ALL'||reportTemplate=='FOF'"
         class="page-box">
      <longHold ref="longHold"></longHold>
    </div>
    <!-- 整体组合行业Brison归因  -->
    <div class="page-box">
      <div class="flex item-center justify-between">
        <div class="area-title">整体组合行业Brison归因</div>
        <!-- @click="showDialogRecord = true" -->
        <div class="border_table_header_search">
          <span class="selector">行业口径：</span>
          <el-select v-model="brison.params.industryStandard"
                     class="search-security"
                     placeholder="请选择"
                     @change='getObjectIndustryBrison'
                     style="width: 240px">
            <el-option v-for="item in industryCaliberOption"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"
                       style="width: 240px">
            </el-option>
          </el-select>
          <img alt=""
               class="download"
               src="../../../../../assets/img/download.png"
               @click="downloadExcel('整体组合行业Brison归因')" />
        </div>
      </div>
      <el-divider></el-divider>
      <div class="area-body">
        <div class="table">
          <el-table v-loading="brison.loading"
                    :cell-style="cellStyle"
                    :data="brison.tableData"
                    :default-sort="{prop: 'data.meanWeight', order: 'descending'}"
                    @sort-change="sort_change"
                    border
                    stripe>
            <el-table-column align="gotoleft"
                             label="行业"
                             prop="data.industry_name">
              <template slot-scope="scope">
                <div style="color:#4096ff">{{scope.row.data.industry_name }}</div>
              </template>
            </el-table-column>
            <el-table-column align="gotoleft"
                             label="市值收益(亿元)"
                             prop="data.marketValueGainLoss"
                             sortable="custom"><template slot-scope="scope">{{scope.row.data.marketValueGainLoss | fixY}}</template>
            </el-table-column>
            <el-table-column align="gotoleft"
                             label="净买入(亿元)"
                             prop="data.total_buy"
                             sortable="custom"><template slot-scope="scope">{{scope.row.data.total_buy | fixY}}</template>
            </el-table-column>
            <el-table-column align="gotoleft"
                             label="实际配置权重"
                             prop="data.meanWeight"
                             sortable="custom"><template slot-scope="scope">{{scope.row.data.meanWeight| fix2p}}</template>
            </el-table-column>
            <el-table-column align="gotoleft"
                             label="超欠配"
                             prop="data.changeWeightMean"
                             sortable="custom"><template slot-scope="scope">{{scope.row.data.changeWeightMean | fix2p}}</template>
            </el-table-column>
            <el-table-column align="gotoleft"
                             label="行业配置贡献"
                             prop="data.industry_hold_return"
                             sortable="custom"><template slot-scope="scope">{{scope.row.data.industry_hold_return | fix2p}}</template>
            </el-table-column>
            <el-table-column align="gotoleft"
                             label="股票选择贡献"
                             prop="data.stock_return"
                             sortable="custom"><template slot-scope="scope">{{scope.row.data.stock_return | fix2p}}</template>
            </el-table-column>
            <el-table-column align="gotoleft"
                             label="行业持仓收益率"
                             prop="data.rp"
                             sortable="custom"><template slot-scope="scope">{{scope.row.data.rp | fix2p}}</template>
            </el-table-column>
            <el-table-column align="gotoleft"
                             label="行业基准收益率"
                             prop="data.industry_index_return"
                             sortable="custom"><template slot-scope="scope">{{scope.row.data.industry_index_return  | fix2p}}</template>
            </el-table-column>
            <el-empty :image-size="180" />
          </el-table>
        </div>
      </div>
    </div>
    <!-- MOM行业操作复盘   -->
    <div class="page-box"
         v-loading="MOMData.loading"
         v-if="showMOM">
      <div class="flex item-center justify-between">
        <div class="area-title">MOM行业操作复盘</div>
        <div class="border_table_header_search">
          <span class="selector">行业口径：</span>
          <el-select v-model="MOMData.industryStandard"
                     class="search-security"
                     placeholder="TK内部行业"
                     @change="getMOMChartChange('1')"
                     style="width: 140px">
            <el-option v-for="item in industryCaliberOption"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"
                       style="width: 140px">
            </el-option>
          </el-select>
          <span class="selector">行业：</span>
          <el-select v-model="MOMData.industry"
                     class="search-security"
                     placeholder="选择行业"
                     @change="getMOMChartChange(2)"
                     style="width: 140px">
            <el-option v-for="item in MOMData.MOMCareers"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"
                       style="width: 140px">
            </el-option>
          </el-select>
          <span class="selector">颗粒度：</span>
          <el-select v-model="MOMData.code"
                     class="search-security"
                     placeholder="颗粒度"
                     @change="getMOMChartChange(3)"
                     style="width: 140px">
            <el-option v-for="item in MOMData.codeList"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"
                       style="width: 140px">
            </el-option>
          </el-select>
          <el-checkbox-group v-if="showOneYear || showTwoYear || showThreeYear"
                             v-model="MOMData.radio"
                             size="small"
                             @change="changeMOMParamsRadio">
            <el-checkbox-button v-if="showOneYear"
                                :label="1">1y</el-checkbox-button>
            <el-checkbox-button v-if="showTwoYear"
                                :label="2">2y</el-checkbox-button>
            <el-checkbox-button v-if="showThreeYear"
                                :label="3">3y</el-checkbox-button>
          </el-checkbox-group>
          <el-popover placement="bottom"
                      width="400"
                      v-model="MOMData.visible">
            <el-date-picker v-model="MOMData.date"
                            type="daterange"
                            :unlink-panels="true"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            @change="changeMOMParamsDate"
                            :picker-options="pickerOptions">
            </el-date-picker>
            <el-button label=""
                       slot="reference"
                       class="dateBox"><i class="el-icon-date"></i></el-button>
          </el-popover>
          <img alt=""
               class="download"
               src="../../../../../assets/img/download.png"
               @click="downloadExcel('MOM行业操作复盘')" />
        </div>
      </div>
      <el-divider></el-divider>
      <div class="area-body">
        <div class="chart">
          <v-chart autoresize
                   ref="ChartComponent_momhyczfp"
                   v-if="!MOMData.showEmpty"
                   :options="MOMData.chart"
                   element-loading-background="rgba(239, 239, 239, 0.5)"
                   element-loading-spinner="el-icon-document-delete"
                   element-loading-text="暂无数据"
                   style="height: 340px; width: 100% !important" />
          <el-empty v-else
                    :image-size="200"></el-empty>
        </div>
      </div>
    </div>
    <!-- FOF行业操作复盘   -->
    <div class="page-box"
         v-loading="FOFData.loading"
         v-if="showFOF">
      <div class="flex item-center justify-between">
        <div class="area-title">FOF行业操作复盘</div>
        <div class="border_table_header_search">
          <span class="selector">行业口径：</span>
          <el-select v-model="FOFData.industryStandard"
                     class="search-security"
                     placeholder="TK内部行业"
                     @change="changeFOFIndustryStandard"
                     style="width: 140px">
            <el-option v-for="item in industryCaliberOption"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"
                       style="width: 140px">
            </el-option>
          </el-select>
          <span class="selector">行业：</span>
          <el-select v-model="FOFData.industry"
                     class="search-security"
                     placeholder="选择行业"
                     @change="getFOFChart(2)"
                     style="width: 140px">
            <el-option v-for="item in FOFData.FOFCareers"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"
                       style="width: 140px">
            </el-option>
          </el-select>
          <span class="selector">颗粒度：</span>
          <el-select v-model="FOFData.code"
                     class="search-security"
                     placeholder="颗粒度"
                     @change="getFOFChart"
                     style="width: 140px">
            <el-option v-for="item in FOFData.codeList"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"
                       style="width: 140px">
            </el-option>
          </el-select>
          <el-checkbox-group v-if="showOneYear || showTwoYear || showThreeYear"
                             v-model="FOFData.radio"
                             size="small"
                             @change="changeFOFParamsRadio">
            <el-checkbox-button v-if="showOneYear"
                                :label="1">1y</el-checkbox-button>
            <el-checkbox-button v-if="showTwoYear"
                                :label="2">2y</el-checkbox-button>
            <el-checkbox-button v-if="showThreeYear"
                                :label="3">3y</el-checkbox-button>
          </el-checkbox-group>
          <el-popover placement="bottom"
                      width="400"
                      v-model="FOFData.visible">
            <el-date-picker v-model="FOFData.date"
                            type="daterange"
                            range-separator="至"
                            :unlink-panels="true"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            @change="changeFOFParamsDate"
                            :picker-options="pickerOptions">
            </el-date-picker>
            <el-button label=""
                       slot="reference"><i class="el-icon-date"></i></el-button>
          </el-popover>
          <img alt=""
               class="download"
               src="../../../../../assets/img/download.png"
               @click="downloadExcel('FOF行业操作复盘')" />
        </div>
      </div>
      <el-divider></el-divider>
      <div class="area-body">
        <div class="chart">
          <v-chart autoresize
                   ref="ChartComponent_fofhyczfp"
                   v-if="!FOFData.showEmpty"
                   :options="FOFData.chart"
                   element-loading-background="rgba(239, 239, 239, 0.5)"
                   element-loading-spinner="el-icon-document-delete"
                   element-loading-text="暂无数据"
                   style="height: 340px; width: 100% !important" />
          <el-empty v-else
                    :image-size="200"></el-empty>
        </div>
      </div>
    </div>
    <div v-loading="performance.loading"
         class="page-box">
      <div class="flex item-center justify-between">
        <div class="area-title">行业配置表现<el-tooltip class="item"
                      effect="dark"
                      content="图中三角形代表跑赢行业指数，圆形代表跑输行业指数，图形大小代表相对权重，颜色越贴近红色表示赚的越多，颜色越贴近绿色表示亏得越多 "
                      placement="right-start">
            <svg width="14"
                 height="14"
                 viewBox="0 0 14 14"
                 fill="none">
              <path fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
                    fill="black"
                    fill-opacity="0.45" />
            </svg> </el-tooltip></div>
        <div class="border_table_header_search checkBox">
          <span class="selector">行业口径：</span>
          <el-select v-model="performance.industryStandard"
                     class="search-security"
                     placeholder="TK内部行业"
                     @change="changePerformanceParams2"
                     style="width: 240px">
            <el-option v-for="item in industryCaliberOption"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"
                       style="width: 240px">
            </el-option>
          </el-select>
          <el-checkbox-group v-if="showOneYear || showTwoYear || showThreeYear"
                             class="checkBox"
                             v-model="performance.radio"
                             size="small"
                             @change="changePerformanceParamsRadio">
            <el-checkbox-button v-if="showOneYear"
                                :label="1">1y</el-checkbox-button>
            <el-checkbox-button v-if="showTwoYear"
                                :label="2">2y</el-checkbox-button>
            <el-checkbox-button v-if="showThreeYear"
                                :label="3">3y</el-checkbox-button>
          </el-checkbox-group>
          <el-popover placement="bottom"
                      width="400"
                      v-model="performance.visible">
            <el-date-picker v-model="performance.date"
                            type="daterange"
                            range-separator="至"
                            :unlink-panels="true"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            @change="changePerformanceParamsDate"
                            :picker-options="pickerOptions">
            </el-date-picker>
            <el-button label=""
                       slot="reference"
                       class="dateBox"><i class="el-icon-date"></i></el-button>
          </el-popover>
          <img alt=""
               class="download"
               src="../../../../../assets/img/download.png"
               @click="downloadExcel('行业配置表现')" />
          <!-- @click="showGranularity = true"> -->
        </div>
      </div>
      <el-divider></el-divider>
      <div class="area-body">
        <v-chart v-if="!performance.showEmpty"
                 ref="ChartComponent_hypzbx1"
                 style="width: 100%; height: 123px"
                 autoresize
                 :options="performance.allocationBar"></v-chart>
        <v-chart v-if="!performance.showEmpty"
                 ref="ChartComponent_hypzbx2"
                 style="width: 100%; height: 720px"
                 autoresize
                 :options="performance.allocation" />
        <!-- @click="openDialog" -->
        <el-empty v-else
                  :image-size="200"></el-empty>
      </div>
    </div>
    <!-- 整体组合行业Brison归因弹窗 -->
    <el-dialog :visible.sync="showDialogRecord"
               title="食品饮料">
      <el-divider></el-divider>
      <el-table :data="attributionData"
                height="400">
        <el-table-column align="gotoleft"
                         label="行业"
                         prop="security" />
        <el-table-column align="gotoleft"
                         label="市值收益(亿元)"
                         prop="market" />
        <el-table-column align="gotoleft"
                         label="净买入(亿元)"
                         prop="purchase" />
        <el-table-column align="gotoleft"
                         label="实际配置权重"
                         prop="weight" />
        <el-table-column align="gotoleft"
                         label="超欠配"
                         prop="overmatch" />
        <el-table-column align="gotoleft"
                         label="行业配置贡献"
                         prop="contribution" />
        <el-table-column align="gotoleft"
                         label="行业基准收益率"
                         prop="benchmark" />
      </el-table>
    </el-dialog>
    <!-- 行业配置表现-选择颗粒度弹窗 -->
    <el-dialog :visible.sync="showGranularity"
               class="granularityDialog"
               :title="performance.dialogData.dialogTitle">
      <div class="dialog-body">
        <div class="dialog-body_title">选择颗粒度：</div>
        <div class="performance-dialog-checkbox">
          <el-checkbox-group v-model="performance.dialogData.selectCode">
            <el-checkbox-button v-for="(item, index) in performance.dialogData.code[performance.dialogData.dialogTitle]"
                                :key="index"
                                :label="item">{{ item }}
            </el-checkbox-button>
          </el-checkbox-group>
        </div>
      </div>
      <el-divider></el-divider>
      <div class="dialog-body_footer">
        <el-button @click="closeDialogOne">取消</el-button>
        <el-button type="primary"
                   @click="startGet">确认</el-button>
      </div>
    </el-dialog>
    <!-- 行业配置表现-选择颗粒度弹窗2 -->
    <el-dialog :visible.sync="showGranularityChart"
               class="granularityDialog"
               :title="performance.dialogData.dialogTitle"
               width="80%"
               @close="closeDialogTwo">
      <div class="chart-card_body"
           style="padding: 0 30px">
        <v-chart autoresize
                 :options="performance.dialogData.option"
                 element-loading-background="rgba(239, 239, 239, 0.5)"
                 element-loading-spinner="el-icon-document-delete"
                 element-loading-text="暂无数据"
                 :style="`width: 100% !important; height:calc(100px + ${performance.dialogData.selectCode.length * 40}px);`">
        </v-chart>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
@import '../../../tkdesign';

.download {
	padding-left: 25px;
}

.border_table_header_search {
	display: flex;
	justify-content: flex-end;
	position: relative;

	.selector {
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
		line-height: 22px;
		margin-top: 5px;
		color: rgba(0, 0, 0, 0.85);
	}

	.search-security {
		width: 250px;
		margin-right: 25px;
	}
}

.table {
	margin-top: 16px;
}

.area-chart {
	display: flex;
	justify-content: space-between;
	flex-wrap: wrap;

	.chart-card_half {
		width: calc(50% - 8px);
		padding: 0 20px 20px;
		border: 1px solid #d9d9d9;
		border-radius: 4px;

		.chart-card_title {
			display: flex;
			justify-content: space-between;
			color: rgba(0, 0, 0, 0.85);
			font-family: PingFang;
			height: 46px;
			line-height: 46px;
			font-size: 14px;
			font-style: normal;
			font-weight: 400;
			border-bottom: 1px solid #d9d9d9;
		}
	}
}

.chart-card {
	.chart-card_title {
		display: flex;
		justify-content: space-between;
		color: rgba(0, 0, 0, 0.85);
		font-family: PingFang;
		height: 46px;
		line-height: 46px;
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
		border-bottom: 1px solid #d9d9d9;
	}

	.chart-card_body {
		display: flex;

		.sidebar {
			width: 120px;
			padding: 20px 0px;
			gap: 40px;
			box-shadow: 19px 0px 20px 0px rgba(0, 0, 0, 0.04);
			background-color: #ffffff;
			height: 708px;

			.card {
				width: 12px;
				height: 8px;
				margin-right: 5px;
			}
		}
	}
}

.chart-card_header_bg {
	display: flex;
	flex-direction: row;
	align-items: center;
	border-bottom: 1px solid #d9d9d9;
	justify-content: space-between;
}

.charts_one_class {
	height: 680px;
}

.granularityDialog .dialog-body_footer {
	display: flex;
	justify-content: flex-end;
	padding: 0 20px;
}

.el-button--small {
	border-radius: 0 3px 3px 0;
}
</style>

<style lang="scss">
.granularityDialog .el-dialog__header {
	padding-bottom: 20px;
	border-bottom: 1px #e9e9e9 solid;
}

.granularityDialog .el-dialog__body {
	padding: 24px 12px !important;

	.dialog-body {
		padding: 0 20px;
		display: flex;

		.dialog-body_title {
			white-space: nowrap;
		}
	}
}

.el-checkbox-group > .el-checkbox-button:last-child .el-checkbox-button__inner {
	border-radius: 0;
	border-right: 0;
}

.performance-dialog-checkbox {
	.is-checked {
		.el-checkbox-button__inner {
			background-color: rgb(255, 244, 230) !important;
			color: #fc9004 !important;
			border-color: #fc9004 !important;
			box-shadow: none !important;
		}
	}

	.el-checkbox-button__inner {
		border: 1px solid #dcdfe6 !important;
		border-radius: 4px !important;
		margin-right: 10px;
	}
}
</style>
