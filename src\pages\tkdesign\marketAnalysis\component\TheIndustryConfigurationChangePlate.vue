<template>
	<div class="plate-wrapper valuation-percentile-wrapper">
		<VerticalLineHeader title="行业配置变化" showDownloadBtn @downloadClick="exportExcel">
			<template slot="right">
				<el-select v-model="fundValue" clearable placeholder="请选择基金类型" style="margin-right: 12px" @change="fundChange">
					<el-option v-for="item in fundOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>
				<el-select
					v-model="comparisonValue"
					clearable
					placeholder="请选择基金公司"
					style="margin-right: 12px"
					@change="companyChange"
					filterable
				>
					<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>
				<el-form ref="form" :model="form" label-width="80px" class="title-right-form">
					<div style="margin-right: 12px">
						<el-radio-group
							class="radio-group-wrapper"
							v-model="weight"
							size="small"
							style="margin-left: 0 !important"
							@input="weightChange"
						>
							<el-radio-button label="scaleWeighted">规模加权</el-radio-button>
							<el-radio-button label="equivalency">等权</el-radio-button>
						</el-radio-group>
					</div>
					<div style="margin-right: 12px">
						<QuickTimePicker v-model="preset_time" @change="radioChange"></QuickTimePicker>
						<!-- <el-radio-group
							class="radio-group-wrapper"
							v-model="optDate"
							size="small"
							style="margin-left: 0 !important"
							@input="radioChange"
						>
							<el-radio-button label="1">1Y</el-radio-button>
							<el-radio-button label="2">2Y</el-radio-button>
							<el-radio-button label="3">3Y</el-radio-button>
							<el-radio-button label="custom">
                                <i class="el-icon-date"></i>
                            </el-radio-button>
						</el-radio-group> -->
					</div>
				</el-form>
			</template>
		</VerticalLineHeader>
		<div style="height: 100%">
			<div class="select-form-wrapper">
				<div class="select-form-wrapper">
					<RadioGroup
						ref="RadioGroup"
						class="radio-group-wrapper"
						:defaultValue="defaultValue"
						:configList="configList"
						selectOnClick
						@change="handleTypeChange"
					></RadioGroup>
				</div>
			</div>
			<div id="chart-container2">
				<PolylineStackDiagramChart ref="valuation-chart2" @getChartData="getChartData"></PolylineStackDiagramChart>
			</div>
		</div>
	</div>
</template>
<script>
import QuickTimePicker from './QuickTimePicker.vue';
import RadioGroup from './RadioGroup.vue';
import TkChart from './TkChart.vue';
import PolylineStackDiagramChart from './chart/PolylineStackDiagramChart.vue';
import VerticalLineHeader from './VerticalLineHeader.vue';
import { getFundType, getFundCompany, getFundCode } from '../../../../api/pages/tkAnalysis/captial-market';
import { filter_json_to_excel } from '@/utils/exportExcel.js';
export default {
	name: 'TheIndustryConfigurationChangePlate',
	components: {
		QuickTimePicker,
		RadioGroup,
		VerticalLineHeader,
		TkChart,
		PolylineStackDiagramChart
	},
	data() {
		return {
			form: {},
			optDate: '1',
			preset_time: {
				radioValue: '1'
			},
			option: {},
			weight: 'equivalency',
			fundValue: '',
			fundOptions: [],
			comparisonValue: '',
			options: [],
			defaultValue: {
				radioValue: 'industry'
				// selectValue:{name:'动态市盈率',value:'pe'}
			},
			configList: [
				// { type: 'select', value: '', label: 'type', text: '类型', option: [{ label: '类型' }] },
				{ type: 'select', value: '', label: 'industry', text: '行业', option: [{ label: '全部行业' }] },
				{ type: 'select', value: '', label: 'theme', text: '主题', option: [{ label: '全部主题' }] },
				{ type: 'select', value: '', label: 'optionalPool', text: '自选池', option: [{ label: '全部自选池' }] },
				{ type: 'select', value: '', label: 'taikang', text: '泰康分类', option: [{ label: '全部泰康分类' }] },
				{ type: 'select', value: '', label: 'style', text: '风格', option: [{ label: '全部风格' }] }
			],
			firstType: 'industry',
			subType: '',
			tableHeader: [
				{
					prop: 'name',
					label: '行业名称'
				}
			],
			tableData: []
		};
	},
	created() {},
	mounted() {
		this.getData();
	},
	methods: {
		getChartData({ date_list, result }) {
			date_list.forEach((item) => {
				this.tableHeader.push({
					prop: item,
					label: item
				});
			});
			this.tableData = result.map((item) => {
				let list = {};
				item.proportionList.forEach((res) => {
					list[res.yearqtr] = res.proportion;
				});
				return {
					name: item.name,
					...list
				};
			});
			console.log('dateList::::', this.tableHeader, this.tableData);
		},
		// 导出excel
		exportExcel() {
			let list = this.tableHeader.map((item) => {
				return {
					...item,
					value: item.prop,
					format: ''
				};
			});
			filter_json_to_excel(list, this.tableData, '行业配置变化');
		},
		async getData() {
			await this.getFundCode();
			await this.getFundType('industry', '');
			if (this.localStorage.getItem('TheIndustryConfigurationChangePlate')) {
				let key_list = ['preset_time', 'weight', ' firstType', 'subType', 'fundValue', 'comparisonValue', 'defaultValue'];
				for (let key of key_list) {
					this[key] = this.localStorage.getItem('TheIndustryConfigurationChangePlate')?.[key] || this[key];
				}
				let index = this.configList.findIndex((v) => v.label == this.defaultValue.radioValue);
				this.$set(this.configList, index, { ...this.configList[index], value: this.defaultValue.selectValue });
				this.$refs['RadioGroup'].setValue(this.defaultValue);
			}
			this.getRequestData();
		},
		getRequestData() {
			let optDate = this.preset_time?.radioValue === 'custom' ? '' : this.preset_time?.radioValue;
			let { startDate, endDate } = this.preset_time || {};
			this.localStorage.setItem('TheIndustryConfigurationChangePlate', {
				optDate,
				weight: this.weight,
				firstType: this.firstType,
				subType: this.subType,
				fundValue: this.fundValue,
				preset_time: this.preset_time,
				comparisonValue: this.comparisonValue,
				defaultValue: this.defaultValue
			});
			this.$refs['valuation-chart2']?.getData({
				optDate,
				weight: this.weight,
				type: this.firstType || 'industry',
				subType: this.subType || [''],
				windType: this.fundValue,
				company: this.comparisonValue,
				startDate,
				endDate
			});
		},
		async getFundCode() {
			let params = {
				deadline: ''
			};
			let req = await getFundCode(params);
			let { data, code, message } = req || {};
			if (code == 200) {
				// {type:'select',value:'',label:'type',text:'类型',option:[{label:'类型'}]},
				// {type:'select',value:'',label:'industry',text:'行业',option:[{label:'行业'}]},
				// {type:'select',value:'',label:'theme',text:'主题',option:[{label:'主题'}]},
				// {type:'select',value:'',label:'pool',text:'自选池',option:[{label:'自选池'}]},
				// {type:'select',value:'',label:'taikang',text:'泰康分类',option:[{label:'泰康分类'}]},
				// {type:'select',value:'',label:'style',text:'风格',option:[{label:'风格'}]}],
				this.configList = this.configList.map((item) => {
					let dataList = data[item.label + 'List'] || [];
					let curOption = [];
					if (item.label == 'optionalPool') {
						curOption = dataList.map((item) => {
							return {
								label: item.name,
								value: { name: item.name, value: item.id }
							};
						});
					} else {
						curOption = this.dulConfigOption(dataList);
					}
					item.option.push(...curOption);
					return item;
				});
			} else {
			}
		},
		dulConfigOption(dataList) {
			// {label:'动态市盈率',value:{name:'动态市盈率',value:'pe'}},
			// {label:'静态市盈率',value:{name:'静态市盈率',value:'staticState_pe'}},
			// {label:'滚动市盈率',value:{name:'滚动市盈率',value:'trends_pe'}},
			return dataList.map((item) => {
				return {
					label: item,
					value: { name: item, value: item }
				};
			});
		},
		async getFundType(type, subType) {
			await getFundType({
				type: this.firstType || 'industry',
				subType: this.subType || ''
			})
				.then((res) => {
					console.log(res);
					if (res.code === 200) {
						this.fundOptions =
							res.data.windType.map((v) => {
								return { label: v, value: v };
							}) || [];
						this.getFundCompany();
					}
				})
				.catch(() => {});
		},
		getFundCompany(type, subType) {
			getFundCompany({
				type: this.firstType || 'industry',
				subType: this.subType || '',
				windType: this.fundValue || ''
			})
				.then((res) => {
					if (res.code === 200) {
						this.options =
							res.data.company.map((v) => {
								return { label: v, value: v };
							}) || [];
					}
				})
				.catch(() => {});
		},
		fundChange() {
			// this.getFundCompany();
			// this.getPositionConfiguration();
			this.getRequestData();
		},
		companyChange() {
			// this.getPositionConfiguration();
			this.getRequestData();
		},
		handleTypeChange(value) {
			this.defaultValue = value;
			//重新设置chart
			this.firstType = value.radioValue;
			this.subType = [value?.selectValue?.value];
			// this.form.type = value.radioValue;
			// this.form.subType = [value?.selectValue?.value];
			// this.getPositionConfiguration();
			this.getRequestData();
		},
		radioChange() {
			this.getRequestData();
			// this.$refs['valuation-chart2']?.getData(this.optDate, this.weight);
		},
		weightChange() {
			this.getRequestData();
			// this.$refs['valuation-chart2']?.getData(this.optDate, this.weight);
		}
	}
};
</script>
<style lang="scss" scoped>
.detail-download {
	width: 32px;
	height: 32px;
	line-height: 32px;
	text-align: center;
	border: 1px solid rgba(217, 217, 217, 1);
	border-radius: 4px;
	box-sizing: border-box;
	vertical-align: middle;
	img {
		// display: block;
	}
}
#chart-container {
	position: relative;
	height: 334px;
	overflow: hidden;
}
#chart-container2 {
	position: relative;
	height: 100%;
	overflow: hidden;
}
</style>
