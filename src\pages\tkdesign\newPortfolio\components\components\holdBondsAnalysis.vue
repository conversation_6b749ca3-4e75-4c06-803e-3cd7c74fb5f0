<template>
    <div class="plate-wrapper fund-performance-board-wrapper" v-loading="showLoading">
        <combinationComponentHeader title="当前持仓债券分析" showMoreBtn @download="exportExcel">
            <template slot="right">
                <div class="block" style="margin-right: 12px;">
                    <span class="demonstration">截止日期：</span>
                    <el-date-picker
                    v-model="deadline"
                    align="right"
                    @change="getData(param)"
                    type="date"
                    placeholder="选择日期"
                    :picker-options="pickerOptions"
                    >
                    </el-date-picker>
                </div>
                <el-radio-group class="radio-group-wrapper" v-model="form.penetrateFlag" size="small" style="margin-left: 0 !important;margin-right: 16px;" @change="radioChange">
                    <el-radio-button :label="true">穿透fof持仓</el-radio-button>
                    <el-radio-button :label="false">不穿透fof持仓</el-radio-button>
                    
                </el-radio-group>
                
            </template>
        </combinationComponentHeader>
  
        <el-table border stripe :data="tableDataNow">
            <el-table-column  align="gotoleft" prop="bondName" sortable show-overflow-tooltip label="债券名称">
                
            </el-table-column>
            <el-table-column  align="gotoleft" prop="bondCode" sortable show-overflow-tooltip label="债券代码"></el-table-column>
            <el-table-column  align="gotoleft" prop="exchangeNature" sortable label="个券类型"></el-table-column>
            <el-table-column  align="gotoleft" prop="firstIndustryName" sortable label="行业">
            </el-table-column>
            <el-table-column  align="gotoleft" sortable prop="creditRating" label="个券评级">
            </el-table-column>
            <el-table-column  align="gotoleft" sortable prop="ratioinN" label="占净资产比">
                <templete slot-scope="scope">{{ scope.row.ratioinN | fix2p }}</templete>
            </el-table-column>
            <el-table-column  align="gotoleft" sortable prop="holdings" label="持券数量(万)">
                <templete slot-scope="scope">{{ scope.row.holdings | fix2 }}</templete>
            </el-table-column>
            <el-table-column  align="gotoleft" sortable prop="value" label="持仓市值(万)">
                <templete slot-scope="scope">{{ scope.row.value | fix2 }}</templete>
            </el-table-column>
           

            <template slot="empty">
                <el-empty image-size="160"></el-empty>
            </template>
        </el-table>
        <el-pagination
            background
            style="display: flex; justify-content: right; padding-top: 16px; padding-bottom: 24px"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="currentPage"
            :page-sizes="[10, 20, 40, 60, 80, 100]"
            :page-size="pageSIze"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
        >
        </el-pagination>
        <div style="display: flex;justify-content: space-between;gap:12px;align-items: center;margin-top: 20px;">
            <div class="right" style="flex: 1;">
                <pieChart ref="fund-performance-board-chart-container2" ></pieChart>
            </div>
            <div class="right" style="flex: 1;">
                <pieChart ref="fund-performance-board-chart-container" ::chart-name="'industry'"></pieChart>
            </div>
        </div>
    </div>
</template>
<script>
import combinationComponentHeader from './combinationComponentHeader.vue';
import pieChart from '../chart/pieChart.vue';
import { BondHoldingMsg} from '@/api/pages/tkAnalysis/portfolio.js';
import { filter_to_excel } from "@/utils/exportExcel.js";
export default {
    name:'holdBondsAnalysis',
    components:{
        combinationComponentHeader,
        pieChart
    },
    props: {
		endDate:{
			type: String,
			default:""
		}
	},
    data(){
        return {
            tableData:[],
            tableDataNow: [],
            deadline:new Date().setTime(new Date().getTime() - 3600 * 1000 * 24),
            param:null,
            form:{
                penetrateFlag:true,
            },
            showLoading:true,
            pageSize: 10,
			currentPage: 1,
            total:0,
            allTableData:[],
            tableHeader:[{
                prop:'bondName',
                label:'债券名称'

            },{
                prop:'bondCode',
                label:'债券代码'

            },{
                prop:'exchangeNature',
                label:'个券类型'

            },{
                prop:'firstIndustryName',
                label:'行业'

            },{
                prop:'creditRating',
                label:'个券评级'

            },{
                prop:'ratioinN',
                label:'占净资产比'

            },{
                prop:'holdings',
                label:'持券数量(万)'

            },{
                prop:'value',
                label:'持仓市值(万)'

            }],
            pickerOptions: {
				disabledDate(time) {
					return false;
				},
			}	
        }
    },
    filters: {
		fix2p(value) {
			return value &&
				value != '' &&
				value != '--' &&
				value != '- -' &&
				JSON.stringify(value) != '[]' &&
				JSON.stringify(value) != '{}' &&
				value != 'NAN' &&
				value != 'nan'
				? (Number(value) * 100).toFixed(2) + '%'
				: '--';
		},
		fix2(value) {
			return value &&
				value != '' &&
				value != '--' &&
				value != '- -' &&
				JSON.stringify(value) != '[]' &&
				JSON.stringify(value) != '{}' &&
				value != 'NAN' &&
				value != 'nan'
				? Number(value).toFixed(2)
				: '--';
		},
		fixY(value) {
			return value &&
				value != '' &&
				value != '--' &&
				value != '- -' &&
				JSON.stringify(value) != '[]' &&
				JSON.stringify(value) != '{}' &&
				value != 'NAN' &&
				value != 'nan'
				? Number(value / 100000000).toFixed(2) + '亿'
				: '--';
		}
	},
    watch: {
		endDate:{
			handler(val){
				if(val.trim()){
					this.pickerOptions.disabledDate = (time) => {
						return time.getTime() > (new Date(val).getTime());
					}
                    this.deadline = val;
				}
			},
			immediate: true
		},
    }, 
    methods:{
        handleSizeChange(val) {
			this.pageSize = val;
			this.currentPage = 1;
			this.handleCurrentChange(1);
		},
		handleCurrentChange(val) {
			this.currentPage = val;
            this.tableDataNow = this.allTableData.slice((this.currentPage-1)*this.pageSize,this.currentPage*this.pageSize)
		},
        exportExcel(){
            let list = this.tableHeader.map((item) => {
				return {
					...item,
					format: ''
				};
			});
			filter_to_excel(list, this.allTableData, '当前持仓债券分析');
        },
        fix2(value) {
			return value &&
				value != '' &&
				value != '--' &&
				value != '- -' &&
				JSON.stringify(value) != '[]' &&
				JSON.stringify(value) != '{}' &&
				value != 'NAN' &&
				value != 'nan'
				? Number(value * 100).toFixed(2)
				: '--';
		},
        radioChange(){
            this.getData(this.param)
        },
        async getData(param){
            this.showLoading = true;
            this.param = param;
            let res = await BondHoldingMsg({
                ...param,
                ...this.param,
                ...this.form,
                
                deadline:this.moment(this.deadline).format('YYYY-MM-DD')
            });
            this.showLoading = false;
            if(res.mtycode == 200){
                this.allTableData = res.data.data || [];
                this.total = res.data.data?.length;
                this.handleCurrentChange(1);
                let chartDom = this.$refs['fund-performance-board-chart-container'];
                let industry = res.data.industry?.map(item=>{
                    return {
                        name: item.firstIndustryName,
                        value: this.fix2(item.ratioinN)
                    }
                })
                chartDom?.getData(industry || []);
                let chartDom2 = this.$refs['fund-performance-board-chart-container2'];
                let bond = res.data.bond?.map(item=>{
                    return {
                        name: item.exchangeNature,
                        value: this.fix2(item.ratioinN)
                    }
                })
                chartDom2?.getData(bond || []);
            }
        },
    }
}
</script>
<style lang="scss" scoped>
.fund-performance-board-wrapper {
    .select-form-wrapper {
        margin-bottom: 16px;
    }
    .content-table-wrapper {
        margin-bottom: 32px;
    }
}

</style>