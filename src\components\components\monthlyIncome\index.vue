<template>
	<div class="chart_one">
		<div v-loading="loadyejis">
			<div class="card_header">
				<div class="title">月度收益</div>
				<div>
					<span
						style="
							font-family: 'PingFang';
							font-style: normal;
							font-weight: 400;
							font-size: 14px;
							line-height: 22px;
							color: rgba(0, 0, 0, 0.85);
						"
						>统计周期:&nbsp;
					</span>
					<el-select v-model="model" placeholder="" @change="changeModel">
						<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
					</el-select>
					<el-button icon="el-icon-document-delete" style="margin-left: 16px" @click="exportExcel">导出Excel</el-button>
				</div>
			</div>
			<el-table :data="data">
				<el-table-column v-for="(item, index) in columnList" :key="index" :label="item.label" :prop="item.value" align="gotoleft">
					<template slot-scope="{ row }">
						<span v-show="item.formatter">{{ row[item.value] | fix2p }}</span>
						<span v-show="!item.formatter">{{ row[item.value] }}</span>
					</template>
				</el-table-column>
			</el-table>
			<div class="charts_fill_class">
				<v-chart
					element-loading-text="暂无数据"
					element-loading-spinner="el-icon-document-delete"
					element-loading-background="rgba(239, 239, 239, 0.5)"
					class="charts_one_class"
					ref="monthlyIncome"
					autoresize
					:options="option"
					@click="clickChart"
				/>
			</div>
		</div>
	</div>
</template>

<script>
import VChart from 'vue-echarts';
import { barChartOption } from '@/utils/chartStyle';
import { filter_json_to_excel } from '@/utils/exportExcel.js';
export default {
	components: { VChart },
	data() {
		return {
			options: [
				{
					label: '月',
					value: 'month'
				},
				{
					label: '季度',
					value: 'quarter'
				}
			],
			model: 'month',
			option: {},
			data: [],
			columnList: [],
			month: ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'],
			qtr: ['1', '2', '3', '4'],
			info: {}
		};
	},
	filters: {
		fix2p(val) {
			return (val * 100 ? (val * 100).toFixed(2) : '--') + '%';
		}
	},
	methods: {
		getData(data, info) {
			this.info = info;
			let column = {};
			let list = [];
			let columnList = [];
			data.map((item) => {
				column = { ...item, ...column };
			});

			let date = this.options.filter((obj) => {
				return obj.value == this.model;
			})?.[0].label;

			// for (const key in column) {
			// 	if (key !== 'year' && key !== 'all') {
			// 		list.push(key);
			// 	}
			// }
			if (this.model == 'month') {
				list = this.month;
			} else {
				list = this.qtr;
			}
			list.sort().map((item) => {
				let label = item + date;
				columnList.push({
					label,
					value: item,
					formatter: 'fix2p'
				});
			});
			columnList.unshift({
				label: '年份',
				value: 'year'
			});
			columnList.push({
				label: '全年',
				value: 'all',
				formatter: 'fix2p'
			});
			this.columnList = columnList;
			this.data = data;
			this.filterChartData(data);
		},
		changeModel(val) {
			this.$emit('resolveFather', val);
		},
		exportExcel() {
			filter_json_to_excel(this.columnList, this.data);
		},
		filterChartData(data) {
			let date = this.options.filter((obj) => {
				return obj.value == this.model;
			})?.[0].label;
			let xAxis = [];
			let series = [];
			data.map((obj) => {
				if (this.model == 'month') {
					this.month.map((num) => {
						if (obj[num] && obj[num] * 1) {
							series.push({ label: obj['year'] + '年' + num + date, value: obj[num] });
						}
					});
				} else {
					this.qtr.map((num) => {
						if (obj[num] && obj[num] * 1) {
							series.push({ label: obj['year'] + '年' + num + date, value: obj[num] });
						}
					});
				}
			});
			xAxis = series
				.map((item) => {
					return item.label;
				})
				.sort();
			this.option = barChartOption({
				// legend,
				xAxis: [{ data: xAxis }],
				yAxis: [
					{
						name: '收益率',
						type: 'value',
						formatter: function (val) {
							return val.toFixed(2) + '%';
						}
					}
				],
				series: [
					{
						name: this.info.name,
						type: 'bar',
						data: series.map((obj, index) => {
							return {
								value: [obj.label, (obj.value * 100).toFixed(2)],
								itemStyle: {
									color: obj.value > 0 ? '#F76560' : '#23C343'
								}
							};
						})
					}
				]
			});
		},
		clickChart(val) {
			console.log(val);
		},
		createPrintWord() {
			let name = this.model == 'month' ? '月度收益' : '季度收益';
			this.$refs['monthlyIncome'].mergeOptions({ toolbox: { show: false } });
			let height = this.$refs['monthlyIncome']?.$el.clientHeight;
			let width = this.$refs['monthlyIncome']?.$el.clientWidth;
			let chart = this.$refs['monthlyIncome'].getDataURL({
				type: 'png',
				pixelRatio: 3,
				backgroundColor: '#fff'
			});
			this.$refs['monthlyIncome'].mergeOptions({ toolbox: { show: true } });
			return [
				...this.$exportWord.exportTitle(name),
				...this.$exportWord.exportTable(
					this.columnList.map((item) => {
						return {
							...item,
							format: item.formatter ? 'fix2p' : undefined
						};
					}),
					this.data
				),
				...this.$exportWord.exportChart(chart, { width, height })
			];
		}
	}
};
</script>

<style></style>
