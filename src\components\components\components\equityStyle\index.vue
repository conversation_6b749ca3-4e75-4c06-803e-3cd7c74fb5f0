<template>
	<div id="equityStyle" class="equity_style">
		<analysis-card-title title="当前权益持仓风格" :image_id="showType == '数据' ? '' : 'equityStyle'" @downloadExcel="exportExcel">
			<div class="flex_start">
				<!-- <el-date-picker
					v-model="date"
					style="width: 240px"
					type="daterange"
					range-separator="~"
					unlink-panels
					start-placeholder="开始日期"
					end-placeholder="结束日期"
					value-format="yyyy-MM-dd"
					@change="getHoldStocksStyle"
				>
				</el-date-picker> -->
				<el-radio-group class="ml-12" v-model="showType">
					<el-radio-button label="图表"></el-radio-button>
					<el-radio-button label="数据"></el-radio-button>
				</el-radio-group>
			</div>
		</analysis-card-title>
		<div v-show="showType == '图表'" class="area-chart" id="equityStyleChart">
			<div class="piece flex_start" style="align-items: start">
				<div class="piece-list mr-16">
					<div v-for="item in bigsmall" :key="item" class="piece-row flex_start">
						<div class="piece-name mr-8">{{ item }}</div>
						<div class="piece-color_row">
							<div class="piece-color" v-for="(citem, cindex) in valuegrowth" :style="getColor(citem, item)" :key="cindex" />
						</div>
					</div>
					<div class="piece-color_row">
						<span class="piece-name" />
						<div class="piece-color" style="height: auto" v-for="citem in valuegrowth" :key="citem">
							{{ citem }}
						</div>
					</div>
				</div>
				<div style="width: 75px">
					<div class="mb-12">投资比例：</div>
					<div class="flex_start mb-12">
						<div class="card" style="background-color: #bf6c00" />
						<div>> 50%</div>
					</div>
					<div class="flex_start mb-12">
						<div class="card" style="background-color: #4096ff" />
						<div>25%-50%</div>
					</div>
					<div class="flex_start mb-12">
						<div class="card" style="background-color: #ffbe6a" />
						<div>10%-25%</div>
					</div>
					<div class="flex_start">
						<div class="card" style="background-color: #ffecd2" />
						<div>0%-10%</div>
					</div>
				</div>
			</div>
		</div>
		<div v-show="showType == '数据'">
			<el-table :data="data" border stripe style="width: 100%">
				<el-table-column prop="label" label="持仓风格" align="center"> </el-table-column>
				<el-table-column prop="weight" label="投资比例" align="center">
					<template slot-scope="{ row }">
						<div>{{ row.weight | fix2p }}</div>
					</template>
				</el-table-column>
			</el-table>
		</div>
	</div>
</template>

<script>
import { getHoldStocksStyle } from '@/api/pages/Analysis.js';
import { filter_json_to_excel } from '@/utils/exportExcel.js';
export default {
	data() {
		return {
			showType: '图表',
			date: [],
			bigsmall: ['大盘', '均衡', '小盘'],
			valuegrowth: ['价值', '均衡', '成长'],
			info: {},
			data: []
		};
	},
	filters: {
		fix2p(val) {
			return val * 1 && !isNaN(val) ? (val * 100).toFixed(2) + '%' : '--';
		}
	},
	methods: {
		getData(info) {
			this.info = info;
			this.getHoldStocksStyle();
		},
		// 请求接口数据
		async getHoldStocksStyle() {
			let data = await getHoldStocksStyle({
				code: this.info.code,
				type: this.info.type,
				flag: this.info.flag,
				start_date: this.date[0] || '',
				end_date: this.date[1] || ''
			});
			if (data?.mtycode == 200) {
				this.formatData(data?.data);
			}
		},
		formatData(data) {
			let list = [];
			data.map((item) => {
				let index = list.findIndex((v) => v.valuegrowth == item.valuegrowth && v.bigsmall == item.bigsmall);
				if (index == -1) {
					list.push({ valuegrowth: item.valuegrowth, bigsmall: item.bigsmall, weight: item.weight });
				} else {
					list[index].weight = item.weight * 1 + list[index].weight * 1;
				}
			});
			let all = 0;
			list.map((v) => {
				all = all + v.weight;
			});
			this.data = list.map((item) => {
				return {
					...item,
					label: item.valuegrowth + item.bigsmall,
					weight: item.weight / all
				};
			});
		},
		/**
		 * 获取颜色
		 */
		getColor(valuegrowth, bigsmall) {
			let item = this.data.find((v) => v.valuegrowth == valuegrowth && v.bigsmall == bigsmall)?.weight || 0;
			if (item > 0.5) return { backgroundColor: '#BF6C00' };
			if (item > 0.25 && item <= 0.5) return { backgroundColor: '#4096ff' };
			if (item > 0.1 && item <= 0.5) return { backgroundColor: '#FFBE6A' };
			if (item >= 0 && item <= 0.1) return { backgroundColor: '#FFECD2' };
		},
		async createPrintWord(info) {
			await this.getData(info);
			return await new Promise((resolve, reject) => {
				this.$nextTick(async () => {
					let height = document.getElementById('equityStyleChart').clientHeight;
					let width = document.getElementById('equityStyleChart').clientWidth;
					let canvas = await this.html2canvas(document.getElementById('equityStyleChart'), { scale: 3 });
					resolve(
						this.show
							? [
									...this.$exportWord.exportTitle('当前权益持仓风格'),
									...this.$exportWord.exportChart(canvas.toDataURL('image/jpg'), {
										width,
										height
									})
								]
							: []
					);
				});
			});
		},
		exportExcel() {
			let list = [
				{ label: '持仓风格', value: 'label' },
				{ label: '投资比例', value: 'weight', format: 'fix2p' }
			];
			filter_json_to_excel(list, this.data, '当前权益持仓风格');
		}
	}
};
</script>

<style lang="scss" scoped>
.equity_style {
	::v-deep .el-radio-button__orig-radio:checked + .el-radio-button__inner {
		color: #4096ff;
		background-color: #fff;
	}
	.piece {
		// width: 100%;
		flex: 1;

		.card {
			width: 10px;
			height: 10px;
			margin-right: 4px;
		}
	}

	.piece-list {
		// width: 95%;
		flex: 1;

		.piece-row {
			display: flex;
			flex: 1;
		}

		.piece-name {
			color: rgba(0, 0, 0, 0.65);
			text-align: right;
			font-family: Helvetica Neue;
			font-size: 12px;
			font-style: normal;
			font-weight: 400;
		}

		.piece-color_row {
			display: flex;
			// width: calc(100% - 50px);
			flex: 1;
			margin-top: 1px;

			.piece-color {
				// width: 502px;
				flex: 1;
				height: 100px;
				line-height: 40px;
				margin-right: 1px;
				text-align: center;
			}
		}
	}
	.area-chart {
		display: flex;
		justify-content: space-between;
		flex-wrap: wrap;
	}
}
</style>
