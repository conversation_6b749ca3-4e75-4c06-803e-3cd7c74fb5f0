<!--  -->
<template>
  <div class="returns">
    <div style="display: flex; align-items: center; width: 100%; position: relative; justify-content: space-between">
      <div style="display: flex; align-items: center">
        <div class="TitltCompare">业绩曲线</div>
      </div>
      <div class="TitltCompare"
           style="display: flex; justify-content: flex-end; align-items: center; font-size: 14px">
        <el-date-picker @change="changgealphabeta"
                        v-model="valuedata"
                        unlink-panels
                        style="width: 216px"
                        value-format="yyyy-MM-dd"
                        type="daterange"
                        range-separator="|"
                        :picker-options="pickerOptions"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期">
        </el-date-picker>
        <div style="width: 24px"></div>
        基准选择：
        <el-select v-model="value"
                   :remote-method="searchpeople"
                   filterable
                   remote
                   @focus="focusF"
                   prefix-icon="el-icon-search"
                   :loading="loading"
                   placeholder="请选择">
          <el-option-group v-for="groups in options"
                           :key="groups.label"
                           :label="groups.label">
            <el-option v-for="group in groups.options"
                       :key="group.code"
                       :label="group.name"
                       :value="group.code"> </el-option>
          </el-option-group>
        </el-select>
        <div style="width: 24px"></div>

        <el-checkbox v-model="checked">起点对齐</el-checkbox>
        <div style="width: 24px"></div>
      </div>
    </div>

    <div v-loading="loadingT"
         style="page-break-inside: avoid">
      <v-chart ref="basicsmsgReturn"
               v-loading="empty1"
               autoresize
               element-loading-text="暂无数据"
               element-loading-spinner="el-icon-document-delete"
               element-loading-background="rgba(239, 239, 239, 0.5)"
               style="page-break-inside: avoid; width: 100%; height: 400px; margin-top: 16px"
               :options="optionpbroe"></v-chart>
    </div>
    <div v-loading="loadingB"
         style="margin-top: 24px">
      <div v-for="(item, index) in fund_hold"
           :key="index"
           style="margin-top: 16px">
        <div style="font-weight: 500; font-size: 16px; line-height: 24px; color: rgba(0, 0, 0, 0.65); opacity: 0.45">
          {{ item.value }}
        </div>
        <sTable :data="item.list"
                typeFlag="1"></sTable>
      </div>
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { ManagerReturn, ManagerSinceFeature, FundRiskMsg, ManagedFundReturn } from '@/api/pages/tools/compare.js';
import { getIndexReturn } from '@/api/pages/components/fundAssetAllocationAnalysis.js';
import sTable from '../SelfTable.vue';
import { getFundOrBase } from '@/api/pages/components/yejiheader.js';
import VCharts from 'vue-echarts';
export default {
  props: {
    comparetype: {
      type: String,
      default: 'manager' //fund
    },
    id: {
      type: String,
      default: '30189741,30441407'
    },
    type: {
      type: String,
      default: 'equity'
    },
    name: {
      type: String,
      default: '萧楠,胡昕炜'
    },
    returnsHold: {
      type: Array
    },
    indexLists: {
      type: Object
    }
  },
  filters: {
    fix3 (value) {
      if (value == '--' || value == null || value == '') {
        return value;
      } else {
        return (value * 100).toFixed(2) + '%';
      }
    },
    fix2 (value) {
      return Number(value).toFixed(2) + '亿';
    }
  },
  //import引入的组件需要注入到对象中才能使用
  components: { 'v-chart': VCharts, sTable },
  data () {
    //这里存放数据
    return {
      checked: false,
      loadingT: false,
      loadingB: false,
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一周',
            onClick (picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近一个月',
            onClick (picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近三个月',
            onClick (picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近六个月',
            onClick (picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90 * 2);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近一年',
            onClick (picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 365);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近三年',
            onClick (picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 365 * 3);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近五年',
            onClick (picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 365 * 5);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      },
      managerholdcolumns: [
        { dataIndex: 'manager_name', key: 'Manager_name', title: '基金经理' },
        { dataIndex: 'year', key: 'year', defaultSortOrder: 'ascend', title: '年份', align: 'left', sorter: (a, b) => a.year - b.year },
        {
          dataIndex: 'meter_cum_return',
          key: 'Meter_cum_return',
          title: '收益率',
          align: 'left',
          scopedSlots: { customRender: 'Meter_cum_return' },
          sorter: (a, b) => a.meter_cum_return - b.meter_cum_return
        },
        {
          dataIndex: 'rank_cum_return',
          key: 'Rank_cum_return',
          title: '排名（越大越前）',
          align: 'left',
          scopedSlots: { customRender: 'Rank_cum_return' },
          sorter: (a, b) => a.rank_cum_return - b.rank_cum_return
        },
        {
          dataIndex: 'meter_volatility',
          key: 'meter_volatility',
          title: '波动率',
          align: 'left',
          scopedSlots: { customRender: 'meter_volatility' },
          sorter: (a, b) => a.meter_volatility - b.meter_volatility
        },
        {
          dataIndex: 'rank_volatility',
          key: 'rank_volatility',
          title: '排名（越大越前）',
          align: 'left',
          scopedSlots: { customRender: 'rank_volatility' },
          sorter: (a, b) => a.rank_volatility - b.rank_volatility
        }
      ],
      fundholdcolumns: [
        { dataIndex: 'fund_name', key: 'fund_name', title: '基金名称' },
        { dataIndex: 'year', key: 'year', defaultSortOrder: 'ascend', title: '年份', align: 'left', sorter: (a, b) => a.year - b.year },
        {
          dataIndex: 'meter_cum_return',
          key: 'Meter_cum_return',
          title: '收益率',
          align: 'left',
          scopedSlots: { customRender: 'Meter_cum_return' },
          sorter: (a, b) => a.meter_cum_return - b.meter_cum_return
        },
        {
          dataIndex: 'rank_cum_return',
          key: 'Rank_cum_return',
          title: '排名（越大越前）',
          align: 'left',
          scopedSlots: { customRender: 'Rank_cum_return' },
          sorter: (a, b) => a.rank_cum_return - b.rank_cum_return
        },
        {
          dataIndex: 'meter_volatility',
          key: 'meter_volatility',
          title: '波动率',
          align: 'left',
          scopedSlots: { customRender: 'meter_volatility' },
          sorter: (a, b) => a.meter_volatility - b.meter_volatility
        },
        {
          dataIndex: 'rank_volatility',
          key: 'rank_volatility',
          title: '排名（越大越前）',
          align: 'left',
          scopedSlots: { customRender: 'rank_volatility' },
          sorter: (a, b) => a.rank_volatility - b.rank_volatility
        }
      ],
      showdetailchoose: false,
      fund_hold: [],
      empty1: false,
      value: '',
      options: '',
      optionpbroe: {},
      optionpbroe2: {},
      baseperson: '',
      baseindexlist: [],
      basepersonfund: [],
      start_date: '',
      end_date: '',
      valuedata: '',
      fund_or_manager_date: {
        start_date: '',
        end_date: ''
      }
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {
    returnsHold (val) {
      if (val && val != '' && val.length == 2) {
        let that = this;
        setTimeout(() => {
          that.value = val[0];
          that.options = val[1];
        }, 100);
      }
    },
    checked (val) {
      if (val) {
        let series = {};
        let index = 0;
        for (let i = 0; i < this.optionpbroe.series.length; i++) {
          for (let j = 0; j < this.optionpbroe.series[i].data.length; j++) {
            if (this.optionpbroe.series[i].data[j] != 'nan') {
              if (j > index) index = j;
              break;
            }
          }
        }
        series = this.optionpbroe;
        series.xAxis.data = series.xAxis.data.slice(index);
        for (let i = 0; i < this.optionpbroe.series.length; i++) {
          let ttt = series.series[i].data[index];
          for (let j = index; j < series.series[i].data.length; j++) {
            series.series[i].data[j] = series.series[i].data[j] - ttt;
          }
          series.series[i].data = series.series[i].data.slice(index);
        }
        this.optionpbroe = series;
      } else {
        this.empty1 = false;
        this.loadingT = true;

        if (this.comparetype == 'manager') {
          this.getmanagerdata();
        } else {
          this.gefunddata();
        }
      }
    },
    value (val) {
      this.empty1 = false;
      this.loadingT = true;
      this.getIndexReturn();

      // if (this.comparetype == 'manager') {
      // 	this.getmanagerdata();
      // } else {
      // 	this.gefunddata();
      // }
      // let valuename = this.options[0].options.findIndex(item=>item.code == val)
      this.$emit('outBasic', [val, this.options]);
    }
  },
  //方法集合
  methods: {
    focusF () {
      if (this.value == '') {
        this.searchpeople('');
      }
    },
    changgealphabeta () {
      if (this.valuedata) {
        if (this.valuedata?.length == 2) {
          this.start_date = this.valuedata[0];
          this.end_date = this.valuedata[1];
          this.getdata('AAA');
        }
      } else {
        this.start_date = '';
        this.end_date = '';
        this.getdata('AAA');
      }
    },
    // 获取基准
    async searchpeople (query) {
      if (query == '') {
        this.loading = false;
        this.options = [];
        // let data = await getFundOrBase({'message':"1"})
        this.options = this.indexLists;
        // this.options[0].options.unshift({ name: '无', code: '' });
      } else {
        this.loading = false;
        let { data } = await getFundOrBase({ message: query, flag: 6 });
        if (data) {
          let temparr = [
            {
              label: '参考基准',
              options: []
            }
          ];
          for (let i = 0; i < data.length; i++) {
            if (data[i].flag == 'index') {
              temparr[0].options.push(data[i]);
            }
          }
          // temparr[0].options.unshift({ label: '无', value: '' });
          this.options = temparr;
        }
      }
    },
    getdata (flag) {
      if (flag != 'AAA') {
        Object.assign(this.$data, this.$options.data());
      }
      this.loadingT = true;
      this.loadingB = true;
      this.empty1 = false;
      if (this.comparetype == 'manager') {
        this.getmanagerdata();
        this.gettablemsg();
      } else {
        this.gefunddata();
        this.getfundtablemsg();
      }
    },
    // 基金的风险收益关系
    async getfundtablemsg () {
      let data = await FundRiskMsg({ fund_code: this.id, type: this.type, index_code: this.value, fund_name: this.name });
      this.loadingB = false;
      if (data) {
        let now = this.moment().year();
        let last = this.moment().year() - 1;
        let laster = this.moment().year() - 2;
        let flag1 = 0;
        let flag2 = 0;
        let flag3 = 0;

        let dateKey = [String(now), String(last), String(laster)];
        let temp = [];
        let tempall = this.$route.query.id.split(',');
        for (let i = 0; i < data.data.length; i++) {
          if (temp.indexOf(data.data[i][0].fund_code) < 0) {
            temp.push(data.data[i][0].fund_code);
          }
          if (tempall.indexOf(data.data[i][0].fund_code) < 0) {
            tempall.push(data.data[i][0].fund_code);
          }
        }
        let t = tempall.filter((item) => !temp.includes(item));
        for (let k = 0; k < t.length; k++) {
          if (t[k] != '') {
            let arryT = [];
            for (let j = 0; j < dateKey.length; j++) {
              arryT.push({
                fund_code: t[k],
                meter_cum_return: '--',
                meter_maxdrawdown: '--',
                meter_sharpe: '--',
                meter_volatility: '--',
                rank_cum_return: '--',
                rank_maxdrawdown: '--',
                rank_sharpe: '--',
                rank_volatility: '--',
                year: dateKey[j],
                fund_name: this.$route.query.name.split(',')[this.$route.query.id.split(',').indexOf(t[k])]
              });
            }
            data.data.push(arryT);
          }
        }
        let dataList = data.data.sort((a, b) => {
          if (this.$route.query.id.split(',').indexOf(a[0].fund_code) > this.$route.query.id.split(',').indexOf(b[0].fund_code)) return 1;
          else return -1;
        });

        this.fund_hold = [
          { value: now, list: [['收益率'], ['收益率排名'], ['波动率'], ['波动率排名']] },
          { value: last, list: [['收益率'], ['收益率排名'], ['波动率'], ['波动率排名']] },
          { value: laster, list: [['收益率'], ['收益率排名'], ['波动率'], ['波动率排名']] }
        ];
        for (let i = 0; i < dataList.length; i++) {
          if (dataList[i].findIndex((item) => item.year == String(now)) > -1) {
            if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(now))].meter_cum_return)) {
              flag1 = 1;
              this.fund_hold[0].list[0].push(
                (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(now))].meter_cum_return) * 100).toFixed(2) + '%'
              );
            } else {
              this.fund_hold[0].list[0].push('--');
            }
            if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(now))].rank_cum_return)) {
              flag1 = 1;
              this.fund_hold[0].list[1].push(
                (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(now))].rank_cum_return) * 100).toFixed(2) + '%'
              );
            } else {
              this.fund_hold[0].list[1].push('--');
            }
            if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(now))].meter_volatility)) {
              flag1 = 1;
              this.fund_hold[0].list[2].push(
                (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(now))].meter_volatility) * 100).toFixed(2) + '%'
              );
            } else {
              this.fund_hold[0].list[2].push('--');
            }
            if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(now))].rank_volatility)) {
              flag1 = 1;
              this.fund_hold[0].list[3].push(
                (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(now))].rank_volatility) * 100).toFixed(2) + '%'
              );
            } else {
              this.fund_hold[0].list[3].push('--');
            }
          } else {
            this.fund_hold[0].list[0].push('--');
            this.fund_hold[0].list[1].push('--');
            this.fund_hold[0].list[2].push('--');
            this.fund_hold[0].list[3].push('--');
          }
          if (dataList[i].findIndex((item) => item.year == String(laster)) > -1) {
            if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(laster))].meter_cum_return)) {
              flag3 = 1;
              this.fund_hold[2].list[0].push(
                (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(laster))].meter_cum_return) * 100).toFixed(2) + '%'
              );
            } else {
              this.fund_hold[2].list[0].push('--');
            }
            if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(laster))].rank_cum_return)) {
              flag3 = 1;
              this.fund_hold[2].list[1].push(
                (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(laster))].rank_cum_return) * 100).toFixed(2) + '%'
              );
            } else {
              this.fund_hold[2].list[1].push('--');
            }
            if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(laster))].meter_volatility)) {
              flag3 = 1;
              this.fund_hold[2].list[2].push(
                (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(laster))].meter_volatility) * 100).toFixed(2) + '%'
              );
            } else {
              this.fund_hold[2].list[2].push('--');
            }
            if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(laster))].rank_volatility)) {
              flag3 = 1;
              this.fund_hold[2].list[3].push(
                (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(laster))].rank_volatility) * 100).toFixed(2) + '%'
              );
            } else {
              this.fund_hold[2].list[3].push('--');
            }
          } else {
            this.fund_hold[2].list[0].push('--');
            this.fund_hold[2].list[1].push('--');
            this.fund_hold[2].list[2].push('--');
            this.fund_hold[2].list[3].push('--');
          }
          if (dataList[i].findIndex((item) => item.year == String(last)) > -1) {
            if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(last))].meter_cum_return)) {
              flag2 = 1;
              this.fund_hold[1].list[0].push(
                (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(last))].meter_cum_return) * 100).toFixed(2) + '%'
              );
            } else {
              this.fund_hold[1].list[0].push('--');
            }
            if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(last))].rank_cum_return)) {
              flag2 = 1;
              this.fund_hold[1].list[1].push(
                (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(last))].rank_cum_return) * 100).toFixed(2) + '%'
              );
            } else {
              this.fund_hold[1].list[1].push('--');
            }
            if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(last))].meter_volatility)) {
              flag2 = 1;
              this.fund_hold[1].list[2].push(
                (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(last))].meter_volatility) * 100).toFixed(2) + '%'
              );
            } else {
              this.fund_hold[1].list[2].push('--');
            }
            if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(last))].rank_volatility)) {
              flag2 = 1;
              this.fund_hold[1].list[3].push(
                (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(last))].rank_volatility) * 100).toFixed(2) + '%'
              );
            } else {
              this.fund_hold[1].list[3].push('--');
            }
          } else {
            this.fund_hold[1].list[0].push('--');
            this.fund_hold[1].list[1].push('--');
            this.fund_hold[1].list[2].push('--');
            this.fund_hold[1].list[3].push('--');
          }
        }
        if (flag3 == 0) this.fund_hold.splice(2, 1);
        if (flag2 == 0) this.fund_hold.splice(1, 1);
        if (flag1 == 0) this.fund_hold.splice(0, 1);
      }
    },
    async gettablemsg () {
      let _this = this;
      let data = await ManagerSinceFeature({ manager_code: this.id, type: this.type, index_code: this.value, manager_name: this.name });
      this.loadingB = false;
      if (data) {
        let now = this.moment().year();
        let last = this.moment().year() - 1;
        let laster = this.moment().year() - 2;
        let flag1 = 0;
        let flag2 = 0;
        let flag3 = 0;
        this.fund_hold = [
          { value: now, list: [['收益率'], ['收益率排名'], ['波动率'], ['波动率排名']] },
          { value: last, list: [['收益率'], ['收益率排名'], ['波动率'], ['波动率排名']] },
          { value: laster, list: [['收益率'], ['收益率排名'], ['波动率'], ['波动率排名']] }
        ];
        let dataList = [];
        for (let i = 0; i < this.$route.query.id.split(',').length; i++) {
          dataList.push([]);
        }
        for (let i = 0; i < data.data.length; i++) {
          dataList[this.$route.query.id.split(',').indexOf(data.data[i].manager_code)].push(data.data[i]);
        }
        for (let i = 0; i < dataList.length; i++) {
          if (dataList[i].findIndex((item) => item.year == String(now)) > -1) {
            if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(now))].meter_cum_return)) {
              flag1 = 1;
              this.fund_hold[0].list[0].push(
                (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(now))].meter_cum_return) * 100).toFixed(2) + '%'
              );
            } else {
              this.fund_hold[0].list[0].push('--');
            }
            if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(now))].rank_cum_return)) {
              flag1 = 1;
              this.fund_hold[0].list[1].push(
                (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(now))].rank_cum_return) * 100).toFixed(2) + '%'
              );
            } else {
              this.fund_hold[0].list[1].push('--');
            }
            if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(now))].meter_volatility)) {
              flag1 = 1;
              this.fund_hold[0].list[2].push(
                (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(now))].meter_volatility) * 100).toFixed(2) + '%'
              );
            } else {
              this.fund_hold[0].list[2].push('--');
            }
            if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(now))].rank_volatility)) {
              flag1 = 1;
              this.fund_hold[0].list[3].push(
                (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(now))].rank_volatility) * 100).toFixed(2) + '%'
              );
            } else {
              this.fund_hold[0].list[3].push('--');
            }
          } else {
            this.fund_hold[0].list[0].push('--');
            this.fund_hold[0].list[1].push('--');
            this.fund_hold[0].list[2].push('--');
            this.fund_hold[0].list[3].push('--');
          }
          if (dataList[i].findIndex((item) => item.year == String(laster)) > -1) {
            if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(laster))].meter_cum_return)) {
              flag3 = 1;
              this.fund_hold[2].list[0].push(
                (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(laster))].meter_cum_return) * 100).toFixed(2) + '%'
              );
            } else {
              this.fund_hold[2].list[0].push('--');
            }
            if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(laster))].rank_cum_return)) {
              flag3 = 1;
              this.fund_hold[2].list[1].push(
                (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(laster))].rank_cum_return) * 100).toFixed(2) + '%'
              );
            } else {
              this.fund_hold[2].list[1].push('--');
            }
            if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(laster))].meter_volatility)) {
              flag3 = 1;
              this.fund_hold[2].list[2].push(
                (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(laster))].meter_volatility) * 100).toFixed(2) + '%'
              );
            } else {
              this.fund_hold[2].list[2].push('--');
            }
            if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(laster))].rank_volatility)) {
              flag3 = 1;
              this.fund_hold[2].list[3].push(
                (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(laster))].rank_volatility) * 100).toFixed(2) + '%'
              );
            } else {
              this.fund_hold[2].list[3].push('--');
            }
          } else {
            this.fund_hold[2].list[0].push('--');
            this.fund_hold[2].list[1].push('--');
            this.fund_hold[2].list[2].push('--');
            this.fund_hold[2].list[3].push('--');
          }
          if (dataList[i].findIndex((item) => item.year == String(last)) > -1) {
            if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(last))].meter_cum_return)) {
              flag2 = 1;
              this.fund_hold[1].list[0].push(
                (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(last))].meter_cum_return) * 100).toFixed(2) + '%'
              );
            } else {
              this.fund_hold[1].list[0].push('--');
            }
            if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(last))].rank_cum_return)) {
              flag2 = 1;
              this.fund_hold[1].list[1].push(
                (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(last))].rank_cum_return) * 100).toFixed(2) + '%'
              );
            } else {
              this.fund_hold[1].list[1].push('--');
            }
            if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(last))].meter_volatility)) {
              flag2 = 1;
              this.fund_hold[1].list[2].push(
                (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(last))].meter_volatility) * 100).toFixed(2) + '%'
              );
            } else {
              this.fund_hold[1].list[2].push('--');
            }
            if (this.FUNC.isEmpty(dataList[i][dataList[i].findIndex((item) => item.year == String(last))].rank_volatility)) {
              flag2 = 1;
              this.fund_hold[1].list[3].push(
                (Number(dataList[i][dataList[i].findIndex((item) => item.year == String(last))].rank_volatility) * 100).toFixed(2) + '%'
              );
            } else {
              this.fund_hold[1].list[3].push('--');
            }
          } else {
            this.fund_hold[1].list[0].push('--');
            this.fund_hold[1].list[1].push('--');
            this.fund_hold[1].list[2].push('--');
            this.fund_hold[1].list[3].push('--');
          }
        }
        if (flag3 == 0) this.fund_hold.splice(2, 1);
        if (flag2 == 0) this.fund_hold.splice(1, 1);
        if (flag1 == 0) this.fund_hold.splice(0, 1);
      }
    },
    // 累计收益计算
    computedReturn (data) {
      let cum_return = 1;
      let cum = data.map((item) => {
        cum_return = cum_return * (1 + item);
        return cum_return - 1;
      });
      return cum;
    },
    async getIndexReturn () {
      let data = await getIndexReturn({
        index_codes: [this.value],
        start_date: this.fund_or_manager_date.start_date,
        end_date: this.fund_or_manager_date.end_date
      });
      this.loadingT = false;
      if (data?.mtycode == 200) {
        let series = [...this.fund_or_manager_date.series];
        for (let i = 0; i < data.data.length; i++) {
          series.push({
            name: '参考指数曲线',
            type: 'line',
            symbol: 'none',
            data: this.computedReturn(data.data[i].value)
          });
        }
        this.optionpbroe.series = series;
      }
    },
    async getmanagerdata () {
      let _this = this;
      let data = await ManagerReturn({
        manager_code: this.id,
        type: this.type,
        index_code: '',
        manager_name: this.name,
        start_date: this.start_date,
        end_date: this.end_date
      });

      this.loadingT = false;
      if (data) {
        if (JSON.stringify(data.data) == '{}' || JSON.stringify(data.data) == '[]' || JSON.stringify(data.data) == '') {
          this.empty1 = true;
        } else {
          let dataarr = data.data.data.sort((a, b) => {
            if (this.$route.query.name.split(',').indexOf(a.name) > this.$route.query.name.split(',').indexOf(b.name)) return 1;
            else return -1;
          });

          let indexlist = [];
          let indexnum = [];
          for (let j = 0; j < dataarr.length; j++) {
            indexlist.push(0);
            indexnum.push(0);
          }
          // 数据列表
          if (dataarr.length > 0) {
            dataarr = dataarr.map((item) => {
              let value = [];
              // 有数据之后的，如为nan，则为前一天数据
              item.value.map((val, index) => {
                value.push(val == 'nan' ? (value[index - 1] * 1 ? value[index - 1] * 1 : 'nan') : val * 1);
              });
              return {
                ...item,
                value
              };
            });
            // 深拷贝
            let sortData = JSON.parse(JSON.stringify(dataarr));
            // 获取时间最长的数据
            let max = sortData.sort((a, b) => {
              return (
                a.value.findIndex((val) => {
                  return val != 'nan';
                }) -
                b.value.findIndex((val) => {
                  return val != 'nan';
                })
              );
            })?.[0].value;
            dataarr = dataarr.map((item) => {
              // 存在数据第一天的所在的索引
              let i = item.value.findIndex((val) => {
                return val != 'nan';
              });

              // 计算比例
              let scale = (max[i] * 1 + 1 || this.lastData(max, i)) / (item.value[i] * 1 + 1);
              // 计算从当前点开始的累积收益
              let value = item.value.map((val, index) => {
                if (index >= i) {
                  let cum = val * 1 || item.value[index - 1];

                  return (cum + 1) * scale - 1;
                } else {
                  return val;
                }
              });
              return {
                ...item,
                value
              };
            });
            // 遍历管理时间最长的数据
            // for (let k = 0; k < dataarr[0].value.length; k++) {
            // 	// 遍历所有
            // 	for (let j = 0; j < dataarr.length; j++) {
            // 		// 当前收益为nan
            // 		if (dataarr[j].value[k] == 'nan') {
            // 			// 如果当前索引减去存储最大索引大于1，当日value等于上一天收益；否则则对indexlist重新赋值
            // 			if (k - indexlist[j] > 1) {
            // 				dataarr[j].value[k] = dataarr[j].value[k - 1];
            // 			} else {
            // 				indexlist[j] = k;
            // 			}
            // 		} else {
            // 			// 如果缺失数据索引起始不为0
            // 			if (indexlist[j] != 0) {
            // 				// value = 当前value+管理时间最长的当前索引value
            // 				dataarr[j].value[k] = Number(dataarr[j].value[k]) + Number(dataarr[indexlist.indexOf(0)].value[indexlist[j]]);
            // 			}
            // 		}
            // 	}
            // }
          }
          this.baseperson = dataarr[indexlist.indexOf(0)].name;
          this.baseindexlist = [];
          for (let i = 0; i < indexlist.length; i++) {
            if (indexlist[i] == 0) this.baseindexlist.push({ name: dataarr[i].name, minpoint: 0 });
            else this.baseindexlist.push({ name: dataarr[i].name, minpoint: dataarr[i].value[indexlist[i] + 1] });
            // this.baseindexlist.push({name: dataarr[i].name,minpoint:dataarr[i].value[indexlist[i]+1]})
          }

          let seriesss = [];
          for (let i = 0; i < dataarr.length; i++) {
            if (this.name.split(',').indexOf(dataarr[i].name) > -1) {
              seriesss.push({
                name: this.name.split(',')[this.name.split(',').indexOf(dataarr[i].name)],
                type: 'line',
                symbol: 'none',
                data: dataarr[i].value
              });
            }
          }
          this.optionpbroe = {
            color: [
              '#ff9003',
              '#4096ff',
              '#7388A9',
              '#6F80DD',
              '#e040fb',
              '#ff3d00',
              '#929694',
              '#f4d1ff',
              '#e91e63',
              '#4096FF',
              '#64dd17'
            ],
            tooltip: {
              trigger: 'axis',
              formatter: function (obj) {
                var value = obj[0].axisValue + `<br />`;

                for (let i = 0; i < obj.length; i++) {
                  if (obj[i].seriesName == _this.baseperson) {
                    value +=
                      `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:` +
                      obj[i].color +
                      `;"></span>` +
                      obj[i].seriesName +
                      ':' +
                      (Number(obj[i].data) * 100).toFixed(2) +
                      '%' +
                      `<br />`;
                  } else {
                    let temp = 0;
                    for (let k = 0; k < _this.baseindexlist.length; k++) {
                      if (_this.baseindexlist[k].name == obj[i].seriesName) temp = _this.baseindexlist[k].minpoint;
                    }
                    value +=
                      `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:` +
                      obj[i].color +
                      `;"></span>` +
                      obj[i].seriesName +
                      ':' +
                      ((Number(obj[i].data) - Number(temp)) * 100).toFixed(2) +
                      '%' +
                      `<br />`;
                  }
                }
                return value;
              }
            },
            legend: {},
            grid: {
              left: '10px',
              right: '3%',
              bottom: '3%',
              top: '30px',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              boundaryGap: false,
              data: data.data.date
            },
            yAxis: {
              axisLine: { show: false },
              axisTick: { show: false },
              splitLine: {
                show: true,
                lineStyle: {
                  type: 'dashed'
                }
              },
              min: (value) => {
                return value.min;
              },
              type: 'value',
              axisLabel: {
                formatter: function (obj) {
                  // var value = obj.value;
                  return (obj * 100).toFixed(0) + '%';
                }
              }
            },
            series: seriesss
          };
          this.fund_or_manager_date = {
            start_date: data.data.date?.[0],
            end_date: data.data.date?.[data.data.date.length - 1],
            series: seriesss
          };
          if (this.value) {
            this.getIndexReturn();
          }
        }
      } else {
        this.empty1 = true;
      }
    },
    async gefunddata () {
      let _this = this;
      let data = await ManagedFundReturn({
        fund_code: this.id,
        type: this.type,
        fund_name: this.name,
        index_code: '',
        start_date: this.start_date,
        end_date: this.end_date
      });
      this.loadingT = false;
      if (data) {
        if (JSON.stringify(data.data) == '{}' || JSON.stringify(data.data) == '[]' || JSON.stringify(data.data) == '') {
          this.empty1 = true;
        } else {
          let dataList = data.data.sort((a, b) => {
            if (this.$route.query.id.split(',').indexOf(a.name) > this.$route.query.id.split(',').indexOf(b.name)) return 1;
            else return -1;
          });
          let datatemp = [];
          let dataarr = [];
          let indexlist = [];
          let indexnum = [];
          for (let i = 0; i < dataList.length; i++) {
            if (dataList[i].name == 'date') {
              datatemp = dataList[i].value;
            } else {
              dataarr.push(dataList[i]);
              indexlist.push(0);
              indexnum.push(0);
            }
          }
          // 数据列表
          if (dataarr.length > 0) {
            // 深拷贝
            let sortData = JSON.parse(JSON.stringify(dataarr));
            // 获取时间最长的数据
            let max = sortData.sort((a, b) => {
              return (
                a.value.findIndex((val) => {
                  return val != 'nan';
                }) -
                b.value.findIndex((val) => {
                  return val != 'nan';
                })
              );
            })?.[0].value;

            dataarr = dataarr.map((item) => {
              // 存在数据第一天的所在的索引
              let i = item.value.findIndex((val) => {
                return val != 'nan';
              });

              let value = [];
              // 有数据之后的，如为nan，则为前一天数据
              item.value.map((val, index) => {
                value.push(val == 'nan' ? (value[index - 1] * 1 ? value[index - 1] * 1 : 'nan') : val * 1);
              });
              // 计算比例
              let scale = (max[i] * 1 + 1 || this.lastData(max, i)) / (value[i] * 1 + 1);
              // 计算从当前点开始的累积收益
              value = value.map((val, index) => {
                if (index >= i) {
                  let cum = val * 1 + 1;
                  return cum * scale - 1;
                } else {
                  return val;
                }
              });
              return {
                ...item,
                value
              };
            });

            // 遍历管理时间最长的数据
            // for (let k = 0; k < dataarr[0].value.length; k++) {
            // 	// 遍历所有
            // 	for (let j = 0; j < dataarr.length; j++) {
            // 		// 当前收益为nan
            // 		if (dataarr[j].value[k] == 'nan') {
            // 			// 如果当前索引减去存储最大索引大于1，当日value等于上一天收益；否则则对indexlist重新赋值
            // 			if (k - indexlist[j] > 1) {
            // 				dataarr[j].value[k] = dataarr[j].value[k - 1];
            // 			} else {
            // 				indexlist[j] = k;
            // 			}
            // 		} else {
            // 			// 如果缺失数据索引起始不为0
            // 			if (indexlist[j] != 0) {
            // 				// value = 当前value+管理时间最长的当前索引value
            // 				dataarr[j].value[k] = Number(dataarr[j].value[k]) + Number(dataarr[indexlist.indexOf(0)].value[indexlist[j]]);
            // 			}
            // 		}
            // 	}
            // }
          }
          // if (dataarr.length > 0) {
          // 	for (let k = 0; k < dataarr[0].value.length; k++) {
          // 		for (let j = 0; j < dataarr.length; j++) {
          // 			if (dataarr[j].value[k] == 'nan') {
          // 				if (k - indexlist[j] > 1) {
          // 					dataarr[j].value[k] = dataarr[j].value[k - 1];
          // 				} else {
          // 					indexlist[j] = k;
          // 				}
          // 			} else {
          // 				if (indexlist[j] != 0) {
          // 					dataarr[j].value[k] = Number(dataarr[j].value[k]) + Number(dataarr[indexlist.indexOf(0)].value[indexlist[j]]);
          // 				}
          // 			}
          // 		}
          // 	}
          // }
          this.basepersonfund = dataarr[indexlist.indexOf(0)].name;

          this.baseindexlist = [];
          for (let i = 0; i < indexlist.length; i++) {
            if (indexlist[i] == 0) this.baseindexlist.push({ name: dataarr[i].name, minpoint: 0 });
            else this.baseindexlist.push({ name: dataarr[i].name, minpoint: dataarr[i].value[indexlist[i] + 1] });
          }

          //
          let seriesss = [];
          for (let i = 0; i < dataarr.length; i++) {
            if (this.id.split(',').indexOf(dataarr[i].name) > -1) {
              seriesss.push({
                name: this.name.split(',')[this.id.split(',').indexOf(dataarr[i].name)],
                type: 'line',
                symbol: 'none',
                data: dataarr[i].value
              });
            }
          }

          this.optionpbroe = {
            color: [
              '#ff9003',
              '#4096ff',
              '#7388A9',
              '#6F80DD',
              '#e040fb',
              '#ff3d00',
              '#929694',
              '#f4d1ff',
              '#e91e63',
              '#4096FF',
              '#64dd17'
            ],
            tooltip: {
              trigger: 'axis',
              formatter: function (obj) {
                var value = obj[0].axisValue + `<br />`;
                for (let i = 0; i < obj.length; i++) {
                  if (obj[i].seriesName == _this.baseperson) {
                    value +=
                      `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:` +
                      obj[i].color +
                      `;"></span>` +
                      obj[i].seriesName +
                      ':' +
                      (Number(obj[i].data) * 100).toFixed(2) +
                      '%' +
                      `<br />`;
                  } else {
                    let temp = 0;
                    for (let k = 0; k < _this.baseindexlist.length; k++) {
                      if (_this.baseindexlist[k].name == obj[i].seriesName) temp = _this.baseindexlist[k].minpoint;
                    }
                    value +=
                      `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:` +
                      obj[i].color +
                      `;"></span>` +
                      obj[i].seriesName +
                      ':' +
                      ((Number(obj[i].data) - Number(temp)) * 100).toFixed(2) +
                      '%' +
                      `<br />`;
                  }
                }
                return value;
              }
            },
            legend: {},
            grid: {
              left: '10px',
              right: '3%',
              bottom: '3%',
              top: '30px',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              boundaryGap: false,
              data: datatemp
            },
            yAxis: {
              axisLine: { show: false },
              axisTick: { show: false },
              splitLine: {
                show: true,
                lineStyle: {
                  type: 'dashed'
                }
              },
              min: (value) => {
                return value.min;
              },
              type: 'value',
              axisLabel: {
                formatter: function (obj) {
                  // var value = obj.value;
                  return (obj * 100).toFixed(1) + '%';
                }
              }
            },
            series: seriesss
          };
          this.fund_or_manager_date = {
            start_date: data.data.find((v) => v.name == 'date').value?.[0],
            end_date: data.data.find((v) => v.name == 'date').value?.[data.data.find((v) => v.name == 'date').value.length - 1],
            series: seriesss
          };
          if (this.value) {
            this.getIndexReturn();
          }
        }
      }
    },
    lastData (data, i) {
      var num;
      while (isNaN(num) || typeof (num * 1) != 'number') {
        if (i > 0) {
          num = data[i - 1];
        } else {
          num = 0;
        }
      }
      return num * 1 + 1;
    },
    createPrintWord () {
      let arr = [];
      let name = this.name.split(',');
      this.fund_hold.map((item) => {
        let list = [];
        arr.push({ ...item, list: [['', ...name], ...item.list] });
      });
      let table = [];
      arr.map((item) => {
        table.push(...this.$exportWord.exportTitle(item.value), ...this.$exportWord.exportCompareTable(item.list));
      });
      let height = this.$refs['basicsmsgReturn']?.$el.clientHeight;
      let width = this.$refs['basicsmsgReturn']?.$el.clientWidth;
      let chart = this.$refs['basicsmsgReturn'].getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      });
      return [...this.$exportWord.exportFirstTitle('业绩曲线对比'), ...this.$exportWord.exportChart(chart, { width, height }), ...table];
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () { },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style>
.returncomparebox .el-range-editor--small .el-range-separator {
	line-height: 24px;
	font-size: 13px;
}
.returncomparebox .el-date-editor .el-range-separator {
	width: 16px;
}
</style>
