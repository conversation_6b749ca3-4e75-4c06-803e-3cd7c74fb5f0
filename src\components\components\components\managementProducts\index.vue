<template>
	<div>
		<analysis-card-title title="管理产品" @downloadExcel="exportExcel"></analysis-card-title>
		<el-table
			max-height="400px"
			:data="managerfund"
			:default-sort="{ prop: 'manage_to', order: 'descending' }"
			class="table"
			ref="multipleTable"
			style="width: 99%"
			header-cell-class-name="table-header"
			v-loading="loading"
		>
			<el-table-column sortable prop="code" label="代码" align="gotoleft" />
			<el-table-column sortable prop="name" label="名称" align="gotoleft">
				<template slot-scope="scope">
					<div style="cursor: pointer" @click="godetail(scope.row.code, scope.row.name)">
						<i style="color: #4096ff">{{ scope.row.name }}</i>
					</div>
				</template>
			</el-table-column>
			<!-- <el-table-column prop="type" align="gotoleft" label="类型" /> -->
			<el-table-column sortable prop="manageFrom" align="gotoleft" label="开始管理" />
			<el-table-column sortable prop="manageTo" align="gotoleft" label="管理至" />
			<el-table-column sortable prop="netasset" align="gotoleft" label="最新规模（亿）">
				<template slot-scope="scope">{{ scope.row.netasset | fix3 }}</template>
			</el-table-column>
		</el-table>
	</div>
</template>

<script>
import { exportTitle, exportTable } from '@/utils/exportWord.js';
import { filter_json_to_excel } from '@/utils/exportExcel.js';
// 管理产品
import { alphaGo } from '@/assets/js/alpha_type.js';
import { getManagedFunds } from '@/api/pages/Analysis.js';

export default {
	name: 'managementProducts',
	data() {
		return {
			loading: true,
			managerfund: [],
			info: {}
		};
	},
	filters: {
		fix3(val) {
			return val * 1 && !isNaN(val) ? (val * 1).toFixed(2) + '亿' : '--';
		}
	},
	methods: {
		// 获取管理产品数据
		async getManagedFunds(status) {
			let postData = { code: this.info.code, flag: this.info.flag };
			if (status) {
				postData.status = 'all';
			} else {
				postData.type = this.info.type;
			}
			let data = await getManagedFunds(postData);
			this.loading = false;
			if (data?.mtycode == 200) {
				this.managerfund = data?.data; //基金管理信息
			}
		},
		getData(info, status) {
			this.info = info;
			this.getManagedFunds(status);
		},
		godetail(id, name) {
			alphaGo(id, name, this.$route.path);
		},
		exportExcel() {
			let list = [
				{
					label: '代码',
					value: 'code'
				},
				{
					label: '名称',
					value: 'name'
				},
				{
					label: '开始管理',
					value: 'manageFrom'
				},
				{
					label: '管理至',
					value: 'manageTo'
				},
				{
					label: '最新规模（亿）',
					value: 'netasset',
					format: 'fixNet'
				}
			];
			filter_json_to_excel(list, this.managerfund, '管理产品');
		},
		createPrintWord() {
			let list = [
				{
					label: '代码',
					value: 'code'
				},
				{
					label: '名称',
					value: 'name'
				},
				{
					label: '开始管理',
					value: 'manage_from'
				},
				{
					label: '管理至',
					value: 'manage_to'
				},
				{
					label: '最新规模（亿）',
					value: 'netasset',
					format: 'fixNet'
				}
			];
			return [...exportTitle('管理产品'), ...exportTable(list, this.managerfund, '', true)];
		}
	}
};
</script>

<style></style>
