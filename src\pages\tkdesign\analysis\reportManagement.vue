<template>
  <div class="box_Board">
    <div class="header_box">
      <span class="header_inactive">投后&nbsp;/&nbsp;投后分析&nbsp;/&nbsp;</span>
      分析报告管理
    </div>
    <div class="border_table">
      <!-- 头部区域 -->
      <div class="border_table_header">
        <!-- 左侧标题区域 -->
        <div class="border_table_header_title">投后分析组合模板列表</div>
        <!-- 右侧按钮区域 -->
        <div class="border_table_header_button">
          <el-button icon="el-icon-plus"
                     @click="addRow"> 新增报告模板 </el-button>
        </div>
      </div>
      <!-- 表格区域 -->
      <el-table v-loading="loading"
                :data="tableData"
                height="calc(100vh - 440px)">
        <el-table-column align="center"
                         type="index"
                         width="50" />
        <el-table-column align="gotoleft"
                         label="投后分析对象"
                         min-width="150"
                         prop="targetName">
          <template slot-scope="scope">
            <div v-if="scope.row.addFlag">
              <el-select v-model="scope.row.targetName"
                         clearable
                         filterable
                         @change="changeTargetName(scope.row)">
                <el-option v-for="item in targetNameOptions"
                           :key="item.id"
                           :value="item.targetName"
                           :label="item.targetName"></el-option>
              </el-select>
            </div>
            <div v-else>{{ scope.row.targetName }}</div>
          </template>
        </el-table-column>
        <el-table-column align="gotoleft"
                         min-width="100"
                         label="分析基准"
                         prop="analyticalBasis">
          <template slot-scope="scope">
            <div v-if="scope.row.addFlag">
              <el-select v-model="scope.row.analyticalBasis"
                         placeholder="请选择"
                         clearable
                         prefix-icon="el-icon-search"
                         remote
                         filterable>
                <el-option v-for="(item, index) in standardOptions"
                           :key="index"
                           :label="item.label"
                           :value="item.value"> </el-option>
              </el-select>
              <!-- <el-autocomplete v-model="scope.row.analyticalBasis"
                               :fetch-suggestions="handleGetIdnex"
                               highlight-first-item
                               placeholder="请输入内容"
                               @change="
									(value) => {
										return handleInputChange(value, index, childIndex);
									}
								"
                               @select="
									(value) => {
										return handelItemSelect(leftItem[typeName], leftItemChildren[codeName], value);
									}
								"> -->
              <!-- <template slot-scope="{ item }">
                <span>{{ item.name }}</span>
              </template>
              </el-autocomplete> -->
            </div>
            <div v-else>
              {{
              // getvalues(scope.row.analyticalBasis)
							scope.row.analyticalBasisName 
							}}
            </div>
          </template>
        </el-table-column>
        <el-table-column align="gotoleft"
                         label="模板"
                         min-width="100"
                         prop="reportTemplate">
          <template slot-scope="scope">
            <div v-if="scope.row.addFlag">
              <el-select v-model="scope.row.reportTemplate"
                         placeholder="请选择"
                         clearable
                         filterable>
                <el-option v-for="(item, index) in reportTemplateOptions"
                           :key="index"
                           :label="item.label"
                           :value="item.value"> </el-option>
              </el-select>
            </div>
            <div v-else>
              {{
								reportTemplateOptions.filter((v) => v.value === scope.row.reportTemplate)[0]
									? reportTemplateOptions.filter((v) => v.value === scope.row.reportTemplate)[0].label
									: ''
							}}
            </div>
          </template>
        </el-table-column>
        <el-table-column align="gotoleft"
                         label="颗粒度"
                         prop="graininess">
          <template slot-scope="scope">
            <div v-if="scope.row.addFlag">
              <el-select v-if="scope.row.targetName"
                         v-model="scope.row.graininess"
                         placeholder="请选择"
                         clearable
                         filterable>
                <el-option v-for="(item, index) in getGraininessOptions(scope.row)"
                           :key="index"
                           :label="item.label"
                           :value="item.value">
                </el-option>
              </el-select>
            </div>
            <div v-else>
              {{
								getAccountType(scope.row) !== '投连账户'
									? optionObject.option_one.filter((v) => v.value === scope.row.graininess)[0]
										? optionObject.option_one.filter((v) => v.value === scope.row.graininess)[0].label
										: ''
									: optionObject.option_two.filter((v) => v.value === scope.row.graininess)[0]
									? optionObject.option_two.filter((v) => v.value === scope.row.graininess)[0].label
									: ''
							}}
            </div>
          </template>
        </el-table-column>
        <el-table-column align="gotoleft"
                         label="时间区间"
                         prop=""
                         sortable
                         min-width="260">
          <template slot-scope="scope">
            <div v-if="scope.row.addFlag">
              <el-date-picker v-model="date"
                              type="daterange"
                              value-format="yyyy-MM-ddTHH:mm:ss"
                              range-separator="~"
                              clearable
                              :unlink-panels="true"
                              start-placeholder="开始日期"
                              end-placeholder="结束日期"
                              @change="changeDate">
              </el-date-picker>
            </div>
            <div v-else>
              {{ scope.row.startDate ? scope.row.startDate.slice(0, 10) : '—— ——' }} ~
              {{ scope.row.endDate ? scope.row.endDate.slice(0, 10) : '—— ——' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column align="gotoleft"
                         label="最后编辑时间"
                         prop="insertDate"
                         sortable
                         min-width="170">
          <template slot-scope="scope">
            <div>{{ scope.row.insertDate ? scope.row.insertDate.slice(0, 16).replace('T', ' ') : '—— ——' }}</div>
          </template></el-table-column>
        <el-table-column align="gotoleft"
                         label="最后更改者"
                         prop="userName"
                         :show-overflow-tooltip="true"
                         sortable
                         min-width="140">
          <!-- <template slot-scope="scope">
            <div>{{ scope.row.insertDate ? scope.row.insertDate.slice(0, 16).replace('T', ' ') : '—— ——' }}</div>
          </template> -->
        </el-table-column>
        <el-table-column min-width="180"
                         label="操作">
          <template slot-scope="scope">
            <div v-if="!scope.row.addFlag"
                 class="flex">
              <el-button class="button-color"
                         type="text"
                         @click="edit(scope.row, scope.$index)"> 配置 </el-button>
              <div style="margin-left: 4px">
                <el-button class="button-color"
                           :disabled="false"
                           type="text"
                           @click="gotoReport(scope.row)"> 查看报告 </el-button>
              </div>
              <div v-if="scope.row.progress < 100"
                   class="reportManagementProgress"
                   style="display: flex; align-items: center; margin-left: 4px">
                <el-progress style="width: 90px; display: flex; align-items: center"
                             color="#409eff"
                             :show-text="true"
                             :format="formatProgress"
                             :percentage="scope.row.progress"></el-progress>
              </div>
            </div>
            <div v-else
                 class="flex">
              <el-button class="button-color"
                         type="text"
                         @click="saveReport(scope.row)"> 保存 </el-button>
              <el-button class="button-color"
                         type="text"
                         @click="deleteReport(scope.row)"> 删除 </el-button>
              <el-button class="button-color"
                         type="text"
                         @click="cancel(scope.row, scope.$index)"> 取消 </el-button>
            </div>
          </template>
        </el-table-column>
        <template slot="empty">
          <el-empty :image-size="160" />
        </template>
      </el-table>
      <!-- 分页器 -->
      <div class="pagination_board">
        <el-pagination :current-page.sync="pagination.pageIndex"
                       :page-size="pagination.pageSize"
                       :total="pagination.total"
                       background
                       layout="total, sizes, prev, pager, next"
                       @size-change="sizeChange"
                       @current-change="currentChange" />
      </div>
    </div>
  </div>
</template>

<script>
import { deleteReport, getReportManagementList, getTarget, saveReport } from '../../../api/pages/tkdesign/reportManagement';

import { getIndexSearchList } from '@/api/pages/tkAnalysis/portfolio.js';
export default {
  data () {
    return {
      intervalId: null,
      socket: '',
      pagination: {
        pageIndex: 1, // 当前页码
        pageSize: 10, // 页面显示几条数据
        total: 0
      },

      tableData: [], // 页面表格数据源,
      objectManagementData: [],
      oldValue: {},
      date: [],
      targetNameOptions: [],
      loading: false,

      standardOptions: [
        { value: "HSTECH.HI", label: "恒生科技指数" },
        { value: "H00852.SH", label: "中证1000全收益" },
        { value: "000688CNY01.SH", label: "上证科创板50成份指数(全)" },
        { value: "399852.SZ", label: "中证1000指数" },
        { value: "HSTECHT.HI", label: "恒生科技指数股息累计指数" },
        { value: "H00016.SH", label: "上证50全收益" },
        { value: "801080.SWI", label: "电子(申万)" },
        { value: "H00300.CSI", label: "沪深300全收益" },
        { value: "000300.SH", label: "沪深300" },
        { value: "H00905.CSI", label: "中证500全收益" },
        { value: "H00906.CSI", label: "中证800全收益" },
        { value: "H00922.CSI", label: "中证红利全收益" },
        { value: "H00933.CSI", label: "中证医药卫生全收益指数" },
        { value: "801010.SWI", label: "农林牧渔(申万)" },
        { value: "801110.SWI", label: "家用电器(申万)" },
        { value: "801120.SWI", label: "食品饮料(申万)" },
        { value: "801130.SWI", label: "纺织服装(申万)" },
        { value: "801140.SWI", label: "轻工制造(申万)" },
        { value: "000001.SH", label: "上证综指" },
        { value: "000002.SH", label: "上证A指" },
        { value: "000010.SH", label: "上证180" },
        { value: "000016.SH", label: "上证50" },
        { value: "399606.SZ", label: "创业板指R" },
        { value: "801150.SWI", label: "医药生物(申万)" },
        { value: "000688.SH", label: "上证科创板50成份指数" },
        { value: "399001.SZ", label: "深证成指" },
        { value: "000852.SH", label: "中证1000" },
        { value: "000903.SH", label: "中证100" },
        { value: "000905.SH", label: "中证500" },
        { value: "000906.SH", label: "中证800" },
        { value: "000933.SH", label: "中证医药" },
        { value: "399002.SZ", label: "深成指R" },
        { value: "399005.SZ", label: "中小板指" },
        { value: "399006.SZ", label: "创业板指" },
        { value: "399333.SZ", label: "中小板指R" },
        { value: "801030.SWI", label: "化工(申万)" },
        { value: "801160.SWI", label: "公用事业(申万)" },
        { value: "801040.SWI", label: "钢铁(申万)" },
        { value: "801050.SWI", label: "有色金属(申万)" },
        { value: "801170.SWI", label: "交通运输(申万)" },
        { value: "801180.SWI", label: "房地产(申万)" },
        { value: "801200.SWI", label: "商业贸易(申万)" },
        { value: "801210.SWI", label: "休闲服务(申万)" },
        { value: "801230.SWI", label: "综合(申万)" },
        { value: "801710.SWI", label: "建筑材料(申万)" },
        { value: "801720.SWI", label: "建筑装饰(申万)" },
        { value: "801730.SWI", label: "电气设备(申万)" },
        { value: "801740.SWI", label: "国防军工(申万)" },
        { value: "801750.SWI", label: "计算机(申万)" },
        { value: "801760.SWI", label: "传媒(申万)" },
        { value: "801770.SWI", label: "通信(申万)" },
        { value: "801780.SWI", label: "银行(申万)" },
        { value: "801790.SWI", label: "非银金融(申万)" },
        { value: "801880.SWI", label: "汽车(申万)" },
        { value: "801890.SWI", label: "机械设备(申万)" },
        { value: "801950.SWI", label: "申万一级煤炭指数" },
        { value: "801960.SWI", label: "申万一级石油石化指数" },
        { value: "801970.SWI", label: "申万一级环保指数" },
        { value: "801980.SWI", label: "申万一级美容护理指数" },
        { value: "HSCI.HI", label: "恒生综合" },
        { value: "930890.CSI", label: "主动股基" },
        { value: "H11021.CSI", label: "股票基金" },
        { value: "930950.CSI", label: "偏股基金" },
        { label: '股债10:90', value: 'B90E10' },
        { label: '股债20:80', value: 'B80E20' },
        { label: '股债30:70', value: 'B70E30' },
        { label: '股债40:60', value: 'B60E40' },
        { label: '股债50:50', value: 'B50E50' },
        { label: '股债60:40', value: 'B40E60' },
        { label: '股债70:30', value: 'B30E70' },
        { label: '股债80:20', value: 'B20E80' },
        { label: '股债90:10', value: 'B10E90' },

      ], //分析基准
      reportTemplateOptions: [
        {
          label: 'MOM模板',
          value: 'MOM'
        },
        {
          label: 'FOF模板',
          value: 'FOF'
        },
        {
          label: '混合模板',
          value: 'ALL'
        }
      ], //模板
      graininessOptions: [], //颗粒度
      optionObject: {
        option_one: [
          { label: '部门', value: 'department' },
          { label: '子账户', value: 'account_type2' },
          { label: '一级策略', value: 'strategy1' },
          { label: '二级策略', value: 'strategy2' },
          { label: '投管人', value: 'manager1' },
          { label: '投资经理', value: 'manager' },
          { label: 'GP3', value: 'gp3_code' }
        ],
        option_two: [
          { label: '部门', value: 'department' },
          { label: '产品名称', value: 'manager1' },
          { label: '产品类型', value: 'strategy1' },
          { label: '组合类型', value: 'strategy2' },
          { label: '投资经理', value: 'manager' },
          { label: 'GP3', value: 'gp3_code' }
        ]
      }
    };
  },
  filters: {
    changeBasic (values) {
      let standardOptions = [
        { value: "HSTECH.HI", label: "恒生科技指数" },
        { value: "H00852.SH", label: "中证1000全收益" },
        { value: "000688CNY01.SH", label: "上证科创板50成份指数(全)" },
        { value: "399852.SZ", label: "中证1000指数" },
        { value: "HSTECHT.HI", label: "恒生科技指数股息累计指数" },
        { value: "H00016.SH", label: "上证50全收益" },
        { value: "801080.SWI", label: "电子(申万)" },
        { value: "H00300.CSI", label: "沪深300全收益" },
        { value: "000300.SH", label: "沪深300" },
        { value: "H00905.CSI", label: "中证500全收益" },
        { value: "H00906.CSI", label: "中证800全收益" },
        { value: "H00922.CSI", label: "中证红利全收益" },
        { value: "H00933.CSI", label: "中证医药卫生全收益指数" },
        { value: "801010.SWI", label: "农林牧渔(申万)" },
        { value: "801110.SWI", label: "家用电器(申万)" },
        { value: "801120.SWI", label: "食品饮料(申万)" },
        { value: "801130.SWI", label: "纺织服装(申万)" },
        { value: "801140.SWI", label: "轻工制造(申万)" },
        { value: "000001.SH", label: "上证综指" },
        { value: "000002.SH", label: "上证A指" },
        { value: "000010.SH", label: "上证180" },
        { value: "000016.SH", label: "上证50" },
        { value: "399606.SZ", label: "创业板指R" },
        { value: "801150.SWI", label: "医药生物(申万)" },
        { value: "000688.SH", label: "上证科创板50成份指数" },
        { value: "399001.SZ", label: "深证成指" },
        { value: "000852.SH", label: "中证1000" },
        { value: "000903.SH", label: "中证100" },
        { value: "000905.SH", label: "中证500" },
        { value: "000906.SH", label: "中证800" },
        { value: "000933.SH", label: "中证医药" },
        { value: "399002.SZ", label: "深成指R" },
        { value: "399005.SZ", label: "中小板指" },
        { value: "399006.SZ", label: "创业板指" },
        { value: "399300.SZ", label: "沪深300指数" },
        { value: "399333.SZ", label: "中小板指R" },
        { value: "801030.SWI", label: "化工(申万)" },
        { value: "801160.SWI", label: "公用事业(申万)" },
        { value: "801040.SWI", label: "钢铁(申万)" },
        { value: "801050.SWI", label: "有色金属(申万)" },
        { value: "801170.SWI", label: "交通运输(申万)" },
        { value: "801180.SWI", label: "房地产(申万)" },
        { value: "801200.SWI", label: "商业贸易(申万)" },
        { value: "801210.SWI", label: "休闲服务(申万)" },
        { value: "801230.SWI", label: "综合(申万)" },
        { value: "801710.SWI", label: "建筑材料(申万)" },
        { value: "801720.SWI", label: "建筑装饰(申万)" },
        { value: "801730.SWI", label: "电气设备(申万)" },
        { value: "801740.SWI", label: "国防军工(申万)" },
        { value: "801750.SWI", label: "计算机(申万)" },
        { value: "801760.SWI", label: "传媒(申万)" },
        { value: "801770.SWI", label: "通信(申万)" },
        { value: "801780.SWI", label: "银行(申万)" },
        { value: "801790.SWI", label: "非银金融(申万)" },
        { value: "801880.SWI", label: "汽车(申万)" },
        { value: "801890.SWI", label: "机械设备(申万)" },
        { value: "801950.SWI", label: "申万一级煤炭指数" },
        { value: "801960.SWI", label: "申万一级石油石化指数" },
        { value: "801970.SWI", label: "申万一级环保指数" },
        { value: "801980.SWI", label: "申万一级美容护理指数" },
        { value: "HSCI.HI", label: "恒生综合" },
        { value: "HSI.HI", label: "恒生指数" }
      ] //分析基准
      // console.log(values);
      // console.log(standardOptions.findIndex(item => item.value === values) > -1 ? standardOptions[standardOptions.findIndex(item => item.value === values)].label : '--' || '');
      return standardOptions.findIndex(item => item.value === values) > -1 ? standardOptions[standardOptions.findIndex(item => item.value === values)].label : '--' || ''
    }
  },
  destroyed () {
    // 销毁监听
    this.socket.onclose = this.close;
    // console.log(this.socket);
    // this.socket.close()
    // console.log(this.socket);
    clearInterval(this.intervalId);
  },
  methods: {
    getvalues (values) {
      console.log(this.standardOptions.findIndex(item => item.value === values) > -1 ? this.standardOptions[this.standardOptions.findIndex(item => item.value === values)].label : '--' || '');
      return this.standardOptions.findIndex(item => item.value === values) > -1 ? this.standardOptions[this.standardOptions.findIndex(item => item.value === values)].label : '--' || ''

    },
    handleGetIdnex (query, callBack) {
      this.FUNC.debounceFunc(() => {
        return this.getIndexList(query, callBack);
      }, 1000)();
    },
    async getIndexList (query, callBack) {
      // this.loading = true;
      this.options = [];
      let params = {
        flag: '6', //指数
        message: query || ''
      };
      let reqData = await getIndexSearchList(params);
      let { data = [], mtycode, mtymessage } = reqData || {};
      if (mtycode == 200) {
        this.standardOptions = data.map(item => {
          return {
            ...item,
            value: item.code,
            label: item.name
          }
        }) || [];
      } else {
        this.$message.warning(mtymessage);
        this.standardOptions = [];
      }
      // callBack(this.standardOptions);
      // this.loading = false;
    },
    initWebScoket () {
      if (typeof WebSocket === 'undefined') {
        this.$message.error('您的浏览器不支持socket');
      } else {
        // 实例化socket
        this.socket = new WebSocket('wss://iw.taikang.com:443/cloud/api/taikang/websocket/report/list'); //https://iw.taikang.com:443/cloud   192.168.199.105:28082
        // 监听socket连接
        this.socket.onopen = this.open;
        // 监听socket错误信息
        this.socket.onerror = this.error;
        // 监听socket消息
        this.socket.onmessage = this.getMessage;
      }
    },
    // webscoret
    send (params) {
      this.intervalId = setInterval(() => {
        // console.log("???");
        // console.log('gotosend');
        this.socket.send(JSON.stringify({ ...params, userId: this.$store.state.id }));
      }, 120 * 1000);
    },
    open () {
      // console.log("socket连接成功")
    },
    error () {
      // console.log("连接错误")
    },
    getMessage (msg) {
      console.log('in');
      let res = JSON.parse(msg.data);
      // console.log(res, 'success');
      if (res.code === 200) {
        this.loading = false;
        this.tableData = res.data;
        this.pagination.total = res.total;
      } else {
        this.tableData = [];
        this.pagination.total = 0;
      }
    },
    close () {
      // console.log("socket已经关闭")
    },
    formatProgress (percentage) {
      return `生成中`;
    },
    /**
     * 获取颗粒度数据
     * @param obj
     */
    getGraininessOptions (obj) {
      if (!obj.targetId) return;
      let id = obj.targetId;
      const array = this.targetNameOptions.filter((v) => v.id === id);
      if (!array || array.length === 0) return;
      let key = array[0].accountType1;
      if (key === '投连账户') {
        return this.optionObject.option_two;
      } else {
        return this.optionObject.option_one;
      }
    },

    // 每页条数改变时触发的回调
    sizeChange (value) {
      this.pagination.pageSize = value;
      this.getReportManagementList();
    },

    // 当前页数改变时触发的回调
    currentChange (value) {
      this.pagination.pageIndex = value;
      this.getReportManagementList();
    },

    /**
     * 处在编辑状态时，不允许对其他数据进行操作
     */
    ban () {
      let result = this.tableData.filter((v) => v.addFlag);
      return result.length > 0;
    },
    /**
     * 添加一行
     */
    addRow () {
      //判断是否处于编辑状态 如果正在编辑则不允许点击新增按钮
      if (this.ban()) return;

      const row = {
        actionType: 1,
        addFlag: true
      };
      this.tableData.unshift(row);
    },

    /**
     * 编辑
     * @param obj 该行的数据
     * @param number 该行的下标
     */
    edit (obj, number) {
      this.date = [obj.startDate, obj.endDate];
      //判断是否处于编辑状态 如果正在编辑则不允许点击其他编辑按钮
      let result = this.tableData.filter((v) => v.addFlag);
      if (result.length > 0) return;
      //保存传过来的的对象，留着在用户点击取消的时候还原数据
      this.oldValue = JSON.parse(JSON.stringify(obj));
      //这里addFlag为true但是页面没有实现响应式
      obj.addFlag = true;
      //这里是为了解决addFlag不能实现响应式的问题 （数组重写）
      //将tableData里对应的该行数据删除，然后再把addFlag为true的obj添加到删除的位置
      if (this.getAccountType(obj) !== '投连账户') this.graininessOptions = this.optionObject.option_one;
      else this.graininessOptions = this.optionObject.option_two;
      this.tableData.splice(number, 1);
      this.tableData.splice(number, 0, obj);
    },
    /**
     * 时间修改
     */
    changeDate (arr) {
      this.date = arr;
    },
    /**
     * 生成报告
     */
    gotoReport (obj) {
      this.$router.push({ path: '/report', query: { ...obj } });
    },

    /**
     * 取消
     * @param obj
     * @param number
     */
    cancel (obj, number) {
      if (obj.actionType === 1) {
        this.tableData.shift();
      } else {
        this.tableData.splice(number, 1);
        this.tableData.splice(number, 0, this.oldValue);
      }
    },
    /**
     *判断账户类型
     */
    getAccountType (row) {
      for (let i = 0; i < this.targetNameOptions.length; i++) {
        if (this.targetNameOptions[i].targetName === row.targetName) {
          if (this.targetNameOptions[i].accountType1) {
            return this.targetNameOptions[i].accountType1;
          } else return '一般账户-委托';
        }
      }
    },
    /**
     * 投后分析对象选择框change事件
     * @param row
     */
    changeTargetName (row) {
      this.targetNameOptions.forEach((item) => {
        if (item.targetName === row.targetName) {
          row.targetId = item.id;
        }
      });
      if (this.getAccountType(row) !== '投连账户') this.graininessOptions = this.optionObject.option_one;
      else this.graininessOptions = this.optionObject.option_two;
    },
    /**
     * 获取模板列表
     * @param params
     */
    getReportManagementList () {
      let that = this;
      this.loading = true;
      const params = {
        current: this.pagination.pageIndex,
        pageSize: this.pagination.pageSize
      };
      getReportManagementList(params).then((res) => {
        if (res.code === 200) {
          this.loading = false;
          this.tableData = res.data;
          this.pagination.total = res.total;
          try {
            // console.log('close');
            // that.socket.onclose = that.close
          } catch { }
          that.send(params);
        } else {
          this.tableData = [];
          this.pagination.total = 0;
        }
      });
    },
    /**
     * 获取投后分析对象
     */
    getTarget () {
      getTarget().then((res) => {
        if (res.code === 200) this.targetNameOptions = res.data;
        else this.targetNameOptions = [];
      });
    },
    /**
     * 保存上传数据
     */
    saveReport (params) {
      const data = {
        ...params,
        startDate: this.date[0],
        endDate: this.date[1]
      };
      this.date = [];
      saveReport(data).then((res) => {
        if (res.code === 200) {
          this.$message.success('配置完成，正在生成报告中');
          this.getReportManagementList();
          params.addFlag = false;
        } else {
          this.$message.error('配置失败');
        }
      });
    },
    /**
     * 删除数据
     */
    deleteReport (target) {
      this.$confirm('确定删除么?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const id = target.id;
          deleteReport(id).then((res) => {
            if (res.code === 200) {
              this.$message({
                type: 'success',
                message: '删除成功!'
              });
              this.getReportManagementList();
            } else {
              this.$message.error('删除失败');
            }
          });
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
    }
  },
  mounted () {
    this.getReportManagementList();
    this.getTarget();
    this.initWebScoket();
  }
};
</script>
<style scoped>
.reportManagementProgress ::v-deep .el-progress__text {
	font-size: 12px !important;
	font-family: 'PingFang';
	color: #a9a9a9;
}
</style>
<style lang="scss" scoped>
@import '../tkdesign';

.border_table {
	padding: 16px 24px;
	background: white;

	.border_table_header_button {
		position: relative;

		.prompt-message {
			z-index: 99;
			position: absolute;
			bottom: -98px;
			right: 82px;
			padding: 16px;
			border-radius: 4px;
			box-shadow: 0 0 2px #ccc;
			background-color: #fff;

			.el-button {
				padding: 5px;
				margin: 20px 0 0;
			}

			.cancel {
				margin-left: 20px;
			}

			.warning {
				border-color: #4096ff;
				background: #4096ff;
				color: white;
				margin-left: 5px;
			}

			.block {
				position: absolute;
				width: 10px;
				height: 10px;
				box-shadow: 0.5px 0.5px 1px #ccc;
				background-color: #fff;
				top: 0;
				left: 50%;
				transform: translate(-50%, -50%) rotate(225deg);
			}
		}
	}

	.search-box {
		background-color: #fafafa;
		padding: 16px 24px;
		margin-bottom: 16px;
	}
}
</style>
