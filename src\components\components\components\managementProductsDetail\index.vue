<template>
	<div>
		<analysis-card-title title="管理产品业绩表现" @downloadExcel="exportExcel"></analysis-card-title>
		<el-table
			max-height="400px"
			:data="managerfund"
			:default-sort="{ prop: 'manage_to', order: 'descending' }"
			class="table"
			style="width: 99%"
			ref="multipleTable"
			header-cell-class-name="table-header"
			:cell-style="cellstyle"
			v-loading="loading"
		>
			<el-table-column
				v-for="item in column"
				:key="item.value"
				:sortable="item.sortable"
				:prop="item.value"
				:label="item.label"
				:show-overflow-tooltip="true"
				align="gotoleft"
			>
				<template slot-scope="{ row }">
					<div v-if="item.value == 'name'" style="cursor: pointer" @click="godetail(row.code, row.name)">
						<i style="color: #4096ff">{{ row.name }}</i>
					</div>
					<div v-else>
						{{ item.format ? item.format(row[item.value]) : row[item.value] }}
					</div>
				</template>
			</el-table-column>
		</el-table>
	</div>
</template>

<script>
// 管理产品
import { alphaGo } from '@/assets/js/alpha_type.js';
import { exportTitle, exportTable } from '@/utils/exportWord.js';
import { filter_json_to_excel } from '@/utils/exportExcel.js';
import { getManagedFundsAllReturn } from '@/api/pages/Analysis.js';

export default {
	name: 'managementProducts',
	data() {
		return {
			loading: true,
			managerfund: [],
			info: {},
			column: [
				{ label: '基金代码', value: 'code' },
				{ label: '基金名称', value: 'name' },
				{ label: '基金类型', value: 'type' },
				{ label: '近一周', value: '1week', format: this.fix2p, fill: 'red_or_green', sortable: true },
				{ label: '近三月', value: '1quarter', format: this.fix2p, fill: 'red_or_green', sortable: true },
				{ label: '近六月', value: '2quarter', format: this.fix2p, fill: 'red_or_green', sortable: true },
				{ label: '近一年', value: '1year', format: this.fix2p, fill: 'red_or_green', sortable: true },
				{ label: '成立以来', value: 'eversince', format: this.fix2p, fill: 'red_or_green', sortable: true },
				{ label: '今年以来', value: 'theyear', format: this.fix2p, fill: 'red_or_green', sortable: true }
			]
		};
	},
	methods: {
		async getManagedFundsDetail() {
			let data = await getManagedFundsAllReturn({ code: this.info.code, flag: this.info.flag });
			if (data?.mtycode == 200) {
				this.loading = false;
				this.managerfund = data?.data.map((item) => {
					let obj = { ...item };
					if (!item.manage_to) {
						obj.manage_to = '至今';
					}
					obj.type = this.COMMON.fundType_zh_en.find((v) => v.en == item.type)?.zh || '--';
					return obj;
				});
			}
		},
		getData(info) {
			this.info = info;
			this.getManagedFundsDetail();
		},
		godetail(id, name) {
			alphaGo(id, name, this.$route.path);
		},
		// 行样式
		cellstyle({ row, column, rowIndex, columnIndex }) {
			if (columnIndex > 2) {
				if (row[column.property] >= 0) {
					return 'color: #CF1322;';
				} else return 'color: #389E0D;';
			}
		},
		fix2p(value) {
			if (value && value != '' && value != '--' && value != 'NAN') return (Number(value) * 100).toFixed(2) + '%';
			else return '--';
		},
		exportExcel() {
			let list = this.column.map((item) => {
				return {
					label: item.label,
					value: item.value
				};
			});
			filter_json_to_excel(list, this.managerfund, '管理产品业绩表现');
		},
		createPrintWord() {
			let list = this.column.map((item) => {
				return {
					label: item.label,
					value: item.value,
					fill: item.fill
				};
			});
			let data = this.managerfund.map((item) => {
				let obj = { ...item };
				for (const key in item) {
					let format = this.column.find((v) => v.value == key)?.format;
					if (format) {
						obj[key] = format(item[key]);
					}
					return obj;
				}
			});
			return [...exportTitle('管理产品业绩表现'), ...exportTable(list, data, '', true)];
		}
	}
};
</script>

<style></style>
