<template>
	<div class="custom_index">
		<div>
			<div class="flex_between mb-16">
				<div>在这里你可以创建自定义的分析时间。</div>
				<div><el-button type="primary" @click="openDialog()">创建</el-button></div>
			</div>
			<el-table :data="data" v-loading="loading">
				<el-table-column
					v-for="item in column"
					:key="item.value"
					:prop="item.value"
					:label="item.label"
					:sortable="item.sortable"
					align="gotoleft"
				>
					<template slot-scope="{ row }">
						<div v-if="item.value.includes('is')">
							<el-switch
								v-model="row[item.value]"
								active-color="#4096ff"
								inactive-color="#e7e7e7"
								@change="changeData(row, item)"
							></el-switch>
						</div>
						<div v-else>{{ row[item.value] }}</div>
					</template>
				</el-table-column>
				<el-table-column align="gotoleft" label="操作">
					<template slot-scope="{ row }">
						<div class="flex_start">
							<el-link class="mr-8" @click="openDialog(row)">编辑</el-link>
							<el-link class="mr-8" @click="deleteItem(row)">删除</el-link>
						</div>
					</template>
				</el-table-column>
				<template slot="empty">
					<el-empty></el-empty>
				</template>
			</el-table>
		</div>
		<edit-analysis-date ref="editAnalysisDate" @resolveFather="getData"></edit-analysis-date>
	</div>
</template>

<script>
import editAnalysisDate from './components/editAnalysisDate';
import { getModelList, updateModel, deleteModel } from '@/api/pages/Tools.js';
export default {
	components: { editAnalysisDate },
	data() {
		return {
			loading: false,
			dialogVisible: false,
			active_data: [],
			defaultProps: {
				children: 'children',
				label: 'label'
			},
			column: [
				{
					label: '名称',
					value: 'model_name'
				},
				{
					label: '是否公开',
					value: 'ispubilc'
				},
				{
					label: '创建人',
					value: 'user_name'
				},
				{
					label: '是否默认',
					value: 'isdefault'
				}
			],
			data: []
		};
	},
	methods: {
		async getData() {
			this.loading = true;
			let data = await getModelList({ source: 'customAnalysisDate', type: 'equity', ismanager: false });
			this.loading = false;
			if (data?.mtycode == 200) {
				this.data = data?.data?.self_data.map((v) => {
					return {
						...v,
						ispublic: v.ispublic == 'True' ? true : false,
						isdefault: v.model_description == 'default' ? true : false
					};
				});
			} else {
				this.data = [];
			}
		},
		changeData(row, item) {
			if (item.label == '是否默认') {
				let obj = this.data.find((v) => v.model_id == row.model_id);
				if (row[item.value]) {
					let index = this.data.find((v) => v.model_description == 'default');
					if (index?.model_id) {
						index.model_description = '';
						index.model_args = JSON.parse(index.model_args);
						index.ismanager = false;
						index.user_permission = [];
						index.title = '--';
						this.updateModel(index);
					}
					obj.model_description = 'default';
				} else {
					obj.model_description = '';
				}
				obj.model_args = JSON.parse(obj.model_args);
				obj.ismanager = false;
				obj.user_permission = [];
				obj.title = '--';
				this.updateModel(obj);
			}
		},
		// 修改模版
		async updateModel(postData) {
			let data = await updateModel(postData);
			if (data?.mtycode == 200) {
				this.$message.success('修改成功');
				this.getData();
			}
		},
		openDialog() {
			this.$refs['editAnalysisDate'].getData();
		},
		async deleteItem(row) {
			let data = await deleteModel({ model_id: row.model_id });
			if (data?.mtycode == 200) {
				this.$message.success('删除成功');
				this.getData();
			} else {
				this.$message.warning('删除失败');
			}
		}
	}
};
</script>

<style lang="scss">
.custom_index {
	.el-form-item__label {
		width: auto !important;
	}
	.el-form-item__content {
		margin-left: auto !important;
	}
	// .el-dialog__header {
	// 	padding: 0;
	// }
	// .el-dialog__body {
	// 	padding: 0;
	// }
}
</style>
