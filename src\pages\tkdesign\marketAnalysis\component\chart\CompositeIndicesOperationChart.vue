<template>
	<div v-show="show">
		<div style="page-break-inside: avoid; position: relative">
			<div style="page-break-inside: avoid; position: relative">
				<div class="charts_fill_class">
					<v-chart
						v-show="!industryrequestflag"
						class="charts_one_class"
						ref="ValuationPercentileChart"
						autoresize
						v-loading="industryrequestflag"
						:options="industryoption"
						@legendselectchanged="legendselectchanged"
					/>
					<el-empty v-show="industryrequestflag" description="暂无数据"></el-empty>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
// 行业评价
import VChart from 'vue-echarts';
import { lineChartOption } from '../../../components/chart/chartStyle.js';
import { filter_json_to_excel } from '../../../../../utils/exportExcel';
export default {
	name: 'CompositeIndicesOperationChart',
	components: {
		VChart
	},
	data() {
		let legendName = {
			indicatorPoints: {
				name: '第一个指数点位',
				key: 'pointFirst'
			},
			pointSecond: {
				name: '第二个指数点位',
				key: 'pointSecond'
			},
			lineValue: {
				name: '回归线',
				key: 'lineValue'
			},
			risk: {
				name: '计算值',
				key: 'riskPremium'
			},
			dividedIntoPoints: {
				name: '分位点',
				key: 'dividedIntoPoints'
			},
			positiveStandardDeviation: {
				name: '标准差（+1）',
				key: 'positiveStandardDeviation'
			},
			negativeStandardDeviation: {
				name: '标准差（-1）',
				key: 'negativeStandardDeviation'
			},
			average: {
				name: '平均值',
				key: 'average'
			}
		};
		return {
			activeKey: 'pointValue',
			industryrequestflag: true,
			show: true,
			info: {},
			legendName: legendName,
			industryoption: null,
			selectedOption: {
				culType: 'ttm'
			},
			selected: {
				[legendName.risk.name]: false
			},
			typeLegendName: legendName.risk.name,
			typeLegendKey: legendName.risk.key,
			tableHeader: [],
			tableData: []
		};
	},
	methods: {
		exportExcel() {
			let list = this.tableHeader.map((item) => {
				return {
					...item,
					value: item.prop,
					format: ''
				};
			});
			filter_json_to_excel(list, this.tableData, '估值分位');
		},
		// 获取父组件传递数据
		getData(data, info) {
			this.show = true;
			this.info = info;
			this.industryrequestflag = false;
			this.industryoption = this.getIndustryoption(data, info);
		},
		getIndustryoption(chartData, info = {}) {
			let data = chartData;
			let icon = {
				dashed:
					'path://M304.43 532.76H221.4c-11.47 0-20.76-9.3-20.76-20.76s9.29-20.76 20.76-20.76h83.03c11.47 0 20.76 9.3 20.76 20.76s-9.29 20.76-20.76 20.76zM581.19 532.76H442.81c-11.47 0-20.76-9.3-20.76-20.76s9.29-20.76 20.76-20.76h138.38c11.47 0 20.76 9.3 20.76 20.76s-9.3 20.76-20.76 20.76zM802.59 532.76h-83.03c-11.47 0-20.76-9.3-20.76-20.76s9.29-20.76 20.76-20.76h83.03c11.47 0 20.76 9.3 20.76 20.76s-9.29 20.76-20.76 20.76z'
			};
			let legendOption = {
				indicatorPoints: { name: this.legendName.indicatorPoints.name, icon: 'line' },
				pointSecond: { name: this.legendName.pointSecond.name, icon: 'line' },
				type: { name: this.typeLegendName, icon: 'rect' },
				dividedIntoPoints: { name: this.legendName.dividedIntoPoints.name, icon: 'rect' },
				positiveStandardDeviation: { name: this.legendName.positiveStandardDeviation.name, icon: icon.dashed },
				negativeStandardDeviation: { name: this.legendName.negativeStandardDeviation.name, icon: icon.dashed },
				average: { name: this.legendName.average.name, icon: icon.dashed },
				lineValue: { name: this.legendName.lineValue.name, icon: 'line' }
			};

			let currentLegend = [];
			currentLegend = [
				legendOption['indicatorPoints'],
				legendOption['pointSecond'],
				legendOption['type'],
				legendOption['dividedIntoPoints'],
				legendOption['lineValue'],
				legendOption['positiveStandardDeviation'],
				legendOption['negativeStandardDeviation'],
				legendOption['average']
			];
			this.tableHeader = [
				{
					label: '日期',
					prop: 'date'
				},
				{
					label: this.legendName.indicatorPoints.name,
					prop: this.legendName.indicatorPoints.key
				},
				{
					label: this.legendName.pointSecond.name,
					prop: this.legendName.pointSecond.key
				},
				{
					label: this.typeLegendName,
					prop: this.typeLegendKey
				},
				{
					label: this.legendName.dividedIntoPoints.name,
					prop: this.legendName.dividedIntoPoints.key
				},
				{
					label: this.legendName.lineValue.name,
					prop: this.legendName.lineValue.key
				},
				{
					label: this.legendName.positiveStandardDeviation.name,
					prop: this.legendName.positiveStandardDeviation.key
				},
				{
					label: this.legendName.negativeStandardDeviation.name,
					prop: this.legendName.negativeStandardDeviation.key
				},
				{
					label: this.legendName.average.name,
					prop: this.legendName.average.key
				}
			];
			this.tableData = data;
			return lineChartOption({
				toolbox: 'none',
				color: ['#4096ff', '#4096ff', '#7388A9', '#7388A9', '#7388A9', '#ff00e6', '#389E0D'],
				legend: {
					selected: this.selected,
					data: [...currentLegend]
				},
				tooltip: {
					backgroundColor: '#ffffff',
					formatter: (obj) => {
						// let dulTextDisplay = (value, name) => {
						// 	if (
						// 		name === this.legendName.indicatorPoints.name ||
						// 		name == this.legendName.pointSecond.name ||
						// 		name === this.typeLegendName
						// 	) {
						// 		return (Number(value) * 1).toFixed(2);
						// 	}
						// 	return (Number(value) * 1).toFixed(2) + '%';
						// };
						let dulTextDisplay = (value, name) => {
							if (
								name === this.legendName.indicatorPoints.name ||
								name === this.legendName.pointSecond.name ||
								name === this.typeLegendName ||
								this.selected[this.typeLegendName]
							) {
								return (Number(value) * 1).toFixed(2);
							}
							return (Number(value) * 1).toFixed(2) + '%';
						};
						var value = `<div style="font-size:14px;">` + obj?.[0].axisValue + `</div>`;
						for (let i = 0; i < obj.length; i++) {
							value +=
								`<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
								`<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
								obj?.[i].color +
								`;"></div>` +
								`<div style="font-family: PingFang SC;">` +
								obj?.[i].seriesName +
								'</div></div>' +
								`<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
								dulTextDisplay(obj?.[i].value, obj?.[i].seriesName) +
								'</div>' +
								`</div>`;
						}
						return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
					}
				},
				xAxis: [
					{
						data: data.map((item) => {
							return item.date;
						}),
						isAlign: true,
						axisLabelRotate: -45
					}
				],
				yAxis: [
					{
						type: 'value',
						formatter(value) {
							return value;
						}
					},
					{
						type: 'value',
						splitLine: false
					}
				],
				series: [
					{
						name: this.legendName.indicatorPoints.name,
						type: 'line',
						symbol: 'none',
						data: data.map((item) => {
							return item[this.legendName.indicatorPoints.key] || '--';
						})
					},
					{
						name: this.legendName.pointSecond.name,
						type: 'line',
						symbol: 'none',
						data: data.map((item) => {
							return item[this.legendName.pointSecond.key] || '--';
						})
					},
					{
						name: this.typeLegendName,
						type: 'line',
						symbol: 'none',
						areaStyle: {},
						lineStyle: {
							opacity: 0
						},
						yAxisIndex: 1,
						data: data.map((item) => {
							//
							//todo: typeLegendName
							return item[this.typeLegendKey] || '--';
						})
					},
					{
						name: this.legendName.dividedIntoPoints.name,
						type: 'line',
						symbol: 'none',
						areaStyle: {},
						yAxisIndex: 1,
						lineStyle: {
							opacity: 0
						},
						data: data.map((item) => {
							return item[this.legendName.dividedIntoPoints.key] || '--';
						})
					},

					{
						name: this.legendName.lineValue.name,
						type: 'line',
						yAxisIndex: 1,
						symbol: 'none',
						data: data.map((item) => {
							return item[this.legendName.lineValue.key] || '--';
						})
					},
					{
						name: this.legendName.positiveStandardDeviation.name,
						type: 'line',
						yAxisIndex: 1,
						symbol: 'none',
						lineStyle: {
							type: 'dashed'
						},
						data: data.map((item) => {
							return item[this.legendName.positiveStandardDeviation.key] || '--';
						})
					},
					{
						name: this.legendName.negativeStandardDeviation.name,
						type: 'line',
						symbol: 'none',
						yAxisIndex: 1,
						lineStyle: {
							type: 'dashed'
						},
						data: data.map((item) => {
							return item[this.legendName.negativeStandardDeviation.key] || '--';
						})
					},
					{
						name: this.legendName.average.name,
						type: 'line',
						symbol: 'none',
						yAxisIndex: 1,
						lineStyle: {
							type: 'dashed'
						},
						data: data.map((item) => {
							return item[this.legendName.average.key] || '--';
						})
					}
				]
			});
		},
		// 数据获取失败
		hideLoading() {
			this.industryrequestflag = false;
			this.show = false;
		},
		// 更新图
		updateOption() {
			this.industryoption = this.getIndustryoption(this.tableData);
		},
		legendselectchanged(params) {
			this.$emit('legendselectchanged', params);
			this.selected = params.selected;
			if (params.selected[this.legendName.dividedIntoPoints.name] !== params.selected[this.typeLegendName]) {
				return;
			}
			let key = '';
			if (params.name === this.legendName.dividedIntoPoints.name) {
				this.$refs['ValuationPercentileChart']?.dispatchAction({
					type: 'legendToggleSelect',
					name: this.typeLegendName // 选中"consistent gets"图例
				});
			}
			if (params.name === this.typeLegendName) {
				this.$refs['ValuationPercentileChart']?.dispatchAction({
					type: 'legendToggleSelect',
					name: this.legendName.dividedIntoPoints.name // 选中"consistent gets"图例
				});
			}
		}
		// legendselectchanged(params) {
		// 	let obj = {};
		// 	// 隐藏指数点位，显示分位点
		// 	if (params.name === this.legendName.dividedIntoPoints.name) {
		// 		this.activeKey = 'pointValue';
		// 		obj[this.typeLegendName] = !params.selected[this.typeLegendName];
		// 		this.selected = { ...params.selected, ...obj };
		// 		this.updateOption();
		// 	}
		// 	// 隐藏分位点，显示指数点位
		// 	if (params.name === this.typeLegendName) {
		// 		this.activeKey = 'quantileValue';
		// 		obj[this.legendName.dividedIntoPoints.name] = !params.selected[this.legendName.dividedIntoPoints.name];
		// 		this.selected = { ...params.selected, ...obj };
		// 		this.updateOption();
		// 	}
		// }
	}
};
</script>

<style></style>
