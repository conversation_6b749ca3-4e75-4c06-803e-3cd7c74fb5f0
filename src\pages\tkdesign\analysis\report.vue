<template>
  <div class="box_Board">
    <div class="header_box">
      <span class="header_inactive">
        投后&nbsp;/&nbsp;投后分析&nbsp;/&nbsp;
        <span style="cursor: pointer"
              @click="gotoPage">分析报告管理</span>
        /</span>
      生成报告
    </div>
    <div class="box_tool">
      <div class="flex item-center justify-between">
        <div class="box_tool-title">{{ reportName }}</div>
        <div class="border_table_header_search">
          <!-- <el-button class="report" @click="downloadWord()"> 导出报告 </el-button> -->
        </div>
      </div>
      <el-tabs v-model="activeName"
               @tab-click="tabClick">
        <el-tab-pane label="产品业绩表现"
                     name="first">
          <span slot="label"
                :style="activeName === 'first' ? 'color:#4096ff' : ''">产品业绩表现</span>
        </el-tab-pane>
        <el-tab-pane label="风格分析"
                     name="second">
          <span slot="label"
                :style="activeName === 'second' ? 'color:#4096ff' : ''">风格分析</span>
        </el-tab-pane>
        <el-tab-pane label="行业持仓分析"
                     name="third">
          <span slot="label"
                :style="activeName === 'third' ? 'color:#4096ff' : ''">行业持仓分析</span>
        </el-tab-pane>
        <el-tab-pane label="组合个股分析"
                     name="fourth">
          <span slot="label"
                :style="activeName === 'fourth' ? 'color:#4096ff' : ''">组合个股分析</span>
        </el-tab-pane>
        <el-tab-pane label="风险因子分析"
                     name="fifth">
          <span slot="label"
                :style="activeName === 'fifth' ? 'color:#4096ff' : ''">风险因子分析</span>
        </el-tab-pane>
      </el-tabs>
    </div>
    <ProductPerformance ref="firstRef"
                        v-show="activeName === 'first' || printActive == true" />
    <StyleAnalysis ref="secondRef"
                   v-show="activeName === 'second' || printActive == true"
                   :reportName="reportName" />
    <PositionAnalysis ref="thirdRef"
                      v-show="activeName === 'third' || printActive == true" />
    <PortfolioAnalysis ref="fourthRef"
                       v-show="activeName === 'fourth' || printActive == true" />
    <RiskAnalysis ref="fifthRef"
                  v-show="activeName === 'fifth' || printActive == true" />
  </div>
</template>

<script>
import ProductPerformance from './components/productPerformance';
import PortfolioAnalysis from './components/portfolioAnalysis';
import PositionAnalysis from './components/positionAnalysis';
import RiskAnalysis from './components/riskAnalysis';
import StyleAnalysis from './components/styleAnalysis';
import { downloadWord, exportTitleWithSubtitle, exportFirstTitle, exportChart } from '@/utils/exportWord.js';

export default {
  components: { ProductPerformance, PortfolioAnalysis, PositionAnalysis, RiskAnalysis, StyleAnalysis },
  data () {
    return {
      activeName: 'first',
      firstRef: null,
      secondRef: null,
      third: null,
      fourthRrf: null,
      fifthRef: null,
      reportName: '',

      printActive: false,
      loading: null,

      icon: 'data:image/png;base64,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'
    };
  },

  methods: {
    async downloadWord () {
      this.loading = this.$loading({
        lock: true,
        text: '正在生成word报告,请稍等...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      try {
        this.printActive = true;
        const title = exportTitleWithSubtitle(this.reportName, this.moment().format('YYYY年MM月DD日'));
        const chart1 = await this.$refs['firstRef'].createPrintWord();
        const chart2 = await this.$refs['secondRef'].createPrintWord();
        const chart3 = await this.$refs['thirdRef'].createPrintWord();
        const chart4 = await this.$refs['fourthRef'].createPrintWord();
        const chart5 = await this.$refs['fifthRef'].createPrintWord();

        downloadWord({ name: this.reportName }, [...title, ...chart1, ...chart2, ...chart3, ...chart4, ...chart5], this.icon);
        this.printActive = false;
        this.loading.close();
      } catch (error) {
        console.error(error);
        this.printActive = false;
        this.loading.close();
      }
    },
    tabClick (e) {
      switch (e.name) {
        case 'first':
          if (!this.$refs.firstRef.uploadState) {
            this.$refs.firstRef.uploadPage();
            this.$refs.firstRef.uploadState = true;
          }
          break;
        case 'second':
          if (!this.$refs.secondRef.uploadState) {
            this.$refs.secondRef.uploadPage();
            this.$refs.secondRef.uploadState = true;
          }
          break;
        case 'third':
          if (!this.$refs.thirdRef.uploadState) {
            this.$refs.thirdRef.uploadPage();
            this.$refs.thirdRef.uploadState = true;
          }
          break;
        case 'fourth':
          if (!this.$refs.fourthRef.uploadState) {
            this.$refs.fourthRef.uploadPage();
            this.$refs.fourthRef.uploadState = true;
          }
          break;
        case 'fifth':
          if (!this.$refs.fifthRef.uploadState) {
            this.$refs.fifthRef.uploadPage();
            this.$refs.fifthRef.uploadState = true;
          }
          break;
      }
    },
    // 子组件点击顶部面包屑返回本页面
    gotoPage () {
      this.$router.replace('/reportManagement');
    }
  },
  mounted () {
    if (!this.$refs.firstRef.uploadState) {
      this.$refs.firstRef.uploadPage();
      this.$refs.firstRef.uploadState = true;
    }
    if (this.$route.query && this.$route.query.targetName) {
      this.reportName = this.$route.query.targetName;
    }
  },
  beforeRouteLeave (to, from, next) {
    window._axiosPromiseArr.forEach((item, index) => {
      item.cancel();
      delete window._axiosPromiseArr[index];
    });
    next();
  }
};
</script>

<style lang="scss" scoped>
@import '../tkdesign';

.box_Board {
	.box_tool {
		background-color: white;
		padding: 20px 23px 0;
		margin-bottom: 16px;

		.box_tool-title {
			color: rgba(0, 0, 0, 0.85);
			height: 32px;
			font-family: PingFang;
			font-size: 18px;
			font-style: normal;
			font-weight: 500;
			line-height: 32px;
			margin-bottom: 16px;
		}
	}
}

.report {
	margin-bottom: 20px;
}
</style>

<style lang="scss">
.box_Board {
	.box_tool {
		.el-tabs {
			height: 64px;
			border-top: 1px solid #d9d9d9;

			.el-tabs__header {
				margin-bottom: 0 !important;

				.el-tabs__item {
					height: 64px;
					color: rgba(0, 0, 0, 0.65);
					font-family: PingFang;
					font-size: 16px;
					font-style: normal;
					font-weight: 400;
					line-height: 64px;
				}
			}
		}
	}
}

.page-box {
	background-color: #ffffff;
	padding: 16px 24px;
	margin-bottom: 16px;

	.area-title {
		color: #000000d8;
		font-size: 16px;
		font-weight: 500;
		height: 24px;
		line-height: 24px;
		font-family: PingFang;
	}

	.area-title::before {
		content: '';
		display: inline-block;
		width: 6px;
		height: 20px;
		transform: translateY(4px);
		border-radius: 35px;
		background: #4096ff;
		margin-right: 12px;
	}

	.area-body {
		.table,
		.chart {
			margin-bottom: 16px;
		}
	}
}
</style>
