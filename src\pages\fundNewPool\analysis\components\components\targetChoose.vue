<template>
  <div v-loading="loading"
       class="big_template targetChoose">
    <!-- 指标计算器 -->
    <div class="flex_between">
      <div style="padding-top:16px"
           class="flex_start">
        <div class="title">指标计算器</div>
        <el-button class="ml-16"
                   type="primary"
                   :disabled="multipleSelection.length == 0"
                   @click="createdSelf">创建子池</el-button>
        <!-- <el-button class="ml-8"
                   type="primary"
                   :disabled="multipleSelection.length == 0"
                   @click="createPool">创建组合</el-button> -->
        <!-- <el-button class="ml-8"
                   type="primary"
                   :disabled="
            multipleSelection.length < 2 || multipleSelection.length > 5
          "
                   @click="goCompare">前往比较</el-button> -->
      </div>
      <div>
        <el-dropdown>
          <span class="el-dropdown-link"
                style>
            <i class="el-icon-more"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <div @click="exportExcel">导出Excel</div>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
    <div class="target_background my-16 py-8 px-16">
      <div class="flex_between mb-16">
        <el-date-picker style="flex: 1"
                        v-model="date"
                        type="daterange"
                        unlink-panels
                        range-separator="-"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="yyyy-MM-dd"
                        @change="changeDate"></el-date-picker>
        <div style="flex: 1"
             class="flex_start">
          <el-select style="flex: 1"
                     class="ml-24"
                     v-model="measure"
                     placeholder="计算指标选择"
                     multiple
                     @change="changeMeasure">
            <el-option v-for="item in measure_list"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"></el-option>
          </el-select>
          <el-button type="primary"
                     class="ml-8"
                     @click="submit">确认</el-button>
        </div>
      </div>
      <div>
        <el-radio-group v-model="model"
                        @change="changeModel">
          <el-radio v-for="item in model_list"
                    :key="item.value"
                    :label="item.value">{{ item.label }}</el-radio>
        </el-radio-group>
      </div>
    </div>
    <div>
      <el-table ref="multipleTablePool"
                :data="data"
                height="600px"
                style="width: 100%"
                row-key="code"
                @selection-change="handleSelectionChange">
        <el-table-column label="勾选比较"
                         type="selection"
                         align="center"
                         width="55px"
                         :reserve-selection="true"></el-table-column>
        <el-table-column v-for="item in column"
                         :key="item.value"
                         :prop="item.value"
                         :label="item.label"
                         :min-width="item.width"
                         :sortable="item.sortable"
                         show-overflow-tooltip
                         align="gotoleft"
                         header-align="gotoleft">
          <el-table-column v-show="item.children"
                           v-for="obj in item.children"
                           :key="obj.value"
                           :prop="obj.value"
                           :label="obj.label"
                           :sortable="obj.sortable"
                           min-width="120px"
                           align="gotoleft">
            <template #header>
              <long-table-popover-chart v-if="obj.popover"
                                        :data="formatTableData()"
                                        date_key="code"
                                        :data_key="obj.value"
                                        :show_name="obj.label">
                <div>{{ obj.label }}</div>
              </long-table-popover-chart>
              <div v-else>{{ obj.label }}</div>
            </template>
            <template slot-scope="{ row }">
              <span v-if="obj.fill"
                    :style="fillStyle(row[obj.value])">{{ obj.format ? obj.format(row[obj.value]) : row[obj.value] }}</span>
              <span v-else>{{ obj.format ? obj.format(row[obj.value]) : row[obj.value] }}</span>
            </template>
          </el-table-column>
          <template #header>
            <long-table-popover-chart v-if="item.popover"
                                      :data="formatTableData()"
                                      date_key="code"
                                      :data_key="item.value"
                                      :show_name="item.label">
              <div>{{ item.label }}</div>
            </long-table-popover-chart>
            <div v-else>{{ item.label }}</div>
          </template>
          <template slot-scope="{ row }">
            <!-- <div v-if="item.value == 'name'" class="overflow_ellipsis">
							<el-link>{{ row[item.value] }}</el-link>
            </div>-->
            <div v-if="item.value == 'name'"
                 class="overflow_ellipsis flex_start">
              <div v-show="row['type'] == 2"
                   :style="`width:12px;height:12px;background:${row['color']}`"
                   class="mr-8"></div>
              <div v-show="row['type'] == 1"
                   class="flex_start">
                <div :style="`width:12px;height:12px;background:#4096ff`"
                     class="mr-8"></div>
                <div class="mr-8 flex_start">
                  <svg width="14"
                       height="14"
                       viewBox="0 0 14 14"
                       fill="none"
                       xmlns="http://www.w3.org/2000/svg">
                    <path d="M12.4156 4.82735L8.94433 4.32286L7.39258 1.17696C7.35019 1.09083 7.28047 1.0211 7.19433 0.978716C6.97832 0.872075 6.71582 0.960943 6.60781 1.17696L5.05605 4.32286L1.58476 4.82735C1.48906 4.84102 1.40156 4.88614 1.33457 4.9545C1.25358 5.03774 1.20895 5.14973 1.21049 5.26586C1.21203 5.38199 1.25961 5.49276 1.34277 5.57383L3.8543 8.02247L3.26094 11.4801C3.24702 11.5605 3.25592 11.6432 3.28663 11.7189C3.31733 11.7945 3.36862 11.86 3.43466 11.908C3.50071 11.9559 3.57887 11.9845 3.66029 11.9903C3.74171 11.9961 3.82313 11.9789 3.89531 11.9408L7.00019 10.3084L10.1051 11.9408C10.1898 11.9859 10.2883 12.001 10.3826 11.9846C10.6205 11.9436 10.7805 11.718 10.7395 11.4801L10.1461 8.02247L12.6576 5.57383C12.726 5.50684 12.7711 5.41934 12.7848 5.32364C12.8217 5.08438 12.6549 4.8629 12.4156 4.82735Z"
                          fill="#FFD600" />
                  </svg>
                </div>
              </div>
              <el-link @click="alphaGo(row['code'], row['name'])">
                {{
                row[item.value]
                }}
              </el-link>
            </div>
            <div v-else>
              <span v-if="item.fill"
                    :style="fillStyle(row[item.value])">
                {{
                item.format ? item.format(row[item.value]) : row[item.value]
                }}
              </span>
              <span v-else>
                {{
                item.format ? item.format(row[item.value]) : row[item.value]
                }}
              </span>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <port-c ref="portC"></port-c>
  </div>
</template>

<script>
import axios from '@/api/index';
import portC from "@/components/components/components/portfoliocreat/index.vue";
import { filter_json_to_excel } from "@/utils/exportExcel.js";
import {
  getPoolInfo,
  postFunds,
  updateStatus,
  getPoolDateList,
  deletePoolFund,
} from "@/api/pages/SystemMixed.js";
import {
  getCustomMeasure,
  createdChildren
} from "@/api/pages/tools/pool.js";
import { getTypeInfo } from "@/api/pages/Analysis.js";
import { TypeMsg } from "@/api/pages/SystemAlpha.js";
import { alphaGo } from "@/assets/js/alpha_type.js";
export default {
  components: { portC },
  props: {
    data_type: {
      type: String,
      default: "fund"
    },
  },
  data () {
    return {
      ismanager: false,
      data: [],
      date: ["2020-01-01", "2023-04-19"],
      measure: ["cum_return"],
      cache_measure: ["cum_return"],
      measure_list: [
        {
          label: "累计收益",
          value: "cum_return"
        },
        {
          label: "年化收益",
          value: "ave_return"
        },
        {
          label: "波动率",
          value: "volatility"
        },
        {
          label: "夏普率",
          value: "sharpe"
        },
        {
          label: "最大回撤",
          value: "maxdrawdown"
        },
        {
          label: "周胜率",
          value: "weekly_win_ratio"
        },
        {
          label: "月胜率",
          value: "monthly_win_ratio"
        },
        {
          label: "季度胜率",
          value: "quarterly_win_ratio"
        },
        {
          label: "年度胜率",
          value: "yearly_win_ratio"
        }
      ],
      model: "",
      model_list: [
        {
          label: "不切分",
          value: ""
        },
        {
          label: "周度",
          value: "weekly"
        },
        {
          label: "月度",
          value: "monthly"
        },
        {
          label: "季度",
          value: "quarterly"
        },
        {
          label: "半年",
          value: "halfyearly"
        },
        {
          label: "年度",
          value: "yearly"
        }
      ],
      column: [
        {
          label: "名称",
          value: "name",
          width: "200px"
        }
      ],
      info: {},
      loading: true,
      original_data: [],
      multipleSelection: []
    };
  },
  methods: {
    getData (info) {
      this.info = info;
      this.date = [
        this.moment()
          .subtract(1, "years")
          .format("YYYY-MM-DD"),
        this.moment().format("YYYY-MM-DD")
      ];
      this.measure =
        typeof this.localStorage.getItem("fund_pool_measure") == "object"
          ? this.localStorage.getItem("fund_pool_measure")
          : ["cum_return"];
      this.getCustomMeasure();
    },
    async getCustomMeasure (measure) {
      this.localStorage.setItem("fund_pool_measure", this.measure);
      this.loading = true;
      let data = await getCustomMeasure({
        ids: [
          ...this.info["code_list"].map(item => {
            return {
              code: item.code,
              type: this.data_type == 'fund' ? this.ismanager ? 'manager' : 'fund' : this.data_type
            };
          })
        ],
        yearqtr: this.info.quarter,
        insert_time: this.info.date,
        flag: this.info.flag,
        start_date: this.date?.[0],
        end_date: this.date?.[1],
        measure: measure ? measure : this.measure,
        cut_flag: this.model,
        ismanager: this.ismanager,
        type: this.info.type,
        insert_time: ''
      });
      this.loading = false;
      if (data?.mtycode == 200) {
        if (measure) {
          this.original_data = this.original_data.map(item => {
            let index = data?.data.findIndex(
              v => v.code == item.code && v.flag == item.flag
            );
            if (index != -1) {
              return {
                ...item,
                ...data?.data[index]
              };
            }
          });
        } else {
          this.original_data = data?.data;
        }
      } else {
        this.original_data = [];
      }
      this.formatColumn(this.original_data);
      this.formatData(this.original_data);
    },
    // 刷新info
    refresInfo (info) {
      this.info = info;
      this.formatData(this.original_data);
    },
    formatTableData () {
      let data = [];
      this.data.map(item => {
        let obj = { ...item };
        for (const key in item) {
          let format = this.column.find(v => {
            return v.value == key;
          })?.format;
          if (format) {
            let val = format(item[key]);
            obj[key] =
              typeof val == "string"
                ? val.includes("%")
                  ? val?.split("%")?.[0] * 1
                  : !isNaN(val)
                    ? val * 1
                    : val
                : val;
          } else {
            let index = this.column.findIndex(v => key.indexOf(v.value) != -1);
            if (index != -1) {
              let formatter = this.column[index].children?.find(
                v => v.value == key
              )?.format;
              if (formatter) {
                let val = formatter(item[key]);
                obj[key] =
                  typeof val == "string"
                    ? val.includes("%")
                      ? val?.split("%")?.[0] * 1
                      : !isNaN(val)
                        ? val * 1
                        : val
                    : val;
              }
            }
          }
        }
        data.push(obj);
      });
      return data;
    },
    // 监听时间区间选择
    changeDate () {
      this.getCustomMeasure();
    },
    // 监听表格选择
    handleSelectionChange (val) {
      this.multipleSelection = val;
    },
    alphaGo (code, name) {
      if (this.data_type == "pool") {
        this.$router.push({
          path: "/poolDetail/" + code,
          hash: "",
          query: {
            id: code,
            name: this.info.code_list.find(v => v.code == code).name,
            user_id: this.info.user_id,
            isdb: 0,
            isChildren: 1
          }
        });
      } else {
        alphaGo(code, name, this.$route.path);
      }
    },
    // 手动生成子池子
    async createdSelf () {
      this.$prompt("请输入池子名称", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消"
      })
        .then(async ({ value }) => {
          this.loading = true;
          let data = await createdChildren({
            id: this.info.code,
            codes: this.multipleSelection.map(item => {
              return item.code;
            }),
            types: "hands",
            description: "",
            ispublic: false,
            name: value,
            ismanager: this.ismanager,
            flag: '',
            type: this.info.type
          });
          this.loading = false;

          if (data?.mtycode == 200) {
            this.$message.success("生成成功");
            this.multipleSelection = [];
            this.$refs.multipleTablePool.clearSelection();
            this.$emit("resolveFather");
          } else {
            this.$message.error("生成失败");
          }
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "取消输入"
          });
        });
    },
    // 前往比较
    async goCompare () {
      const loading = this.$loading({
        lock: true,
        text: "正在获取基金信息",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)"
      });
      let data = await TypeMsg({
        code: this.multipleSelection.map(v => v.code).join(","),
        flag: this.ismanager ? 2 : 1,
        ismanager: this.ismanager
      });
      loading.close();
      let temptype;
      if (data) {
        if (data.data) {
          if (data.data.length == 0) {
            this.$router.push({
              path: "/fundcompareDiff",
              query: {
                id: this.multipleSelection.map(v => v.code).join(","),
                type: "",
                types: data.type.join(","),
                name: this.multipleSelection.map(v => v.name).join(",")
              }
            });
          } else if (data.data.length == 1) {
            temptype = data.data[0];
            if (
              temptype == "bond" ||
              temptype == "cbond" ||
              temptype == "purebond" ||
              temptype == "bill" ||
              temptype.indexOf("equity") >= 0 ||
              temptype == "obond"
            ) {
              this.$router.push({
                path: "/fundcompare",
                query: {
                  id: this.multipleSelection.map(v => v.code).join(","),
                  type: temptype,
                  name: this.multipleSelection.map(v => v.name).join(",")
                }
              });
            } else {
              this.$message("暂时只提供主动权益，二级债，债券类产品的比较");
            }
          } else if (data.data.length > 1) {
            this.$router.push({
              path: "/fundcompareDiff",
              query: {
                id: this.multipleSelection.map(v => v.code).join(","),
                type: "",
                types: data.type.join(","),
                name: this.multipleSelection.map(v => v.name).join(",")
              }
            });
          }
        }
      }
    },
    // 创建组合
    createPool () {
      this.$refs.portC.show(this.multipleSelection);
    },
    // 监听计算指标选择
    changeMeasure (val) {
      // if (this.measure.length > this.cache_measure.length) {
      // 	let measure = this.measure.find((item) => this.cache_measure.findIndex((v) => item == v) == -1);
      // 	this.getCustomMeasure(measure);
      // } else {
      // 	this.formatColumn(this.original_data);
      // }
      // this.cache_measure = this.measure;
    },
    submit () {
      if (this.measure.length > this.cache_measure.length) {
        let measure = this.measure.filter(
          item => this.cache_measure.findIndex(v => item == v) == -1
        );
        this.getCustomMeasure(measure);
      } else {
        this.formatColumn(this.original_data);
      }
      this.cache_measure = this.measure;
    },
    // 监听计算窗口选择
    changeModel (val) {
      this.model = val;
      this.getCustomMeasure();
    },
    // 字体颜色
    fillStyle (val) {
      if (val * 1 > 0) {
        return "color:red";
      } else if (val * 1 < 0) {
        return "color:green";
      } else {
        return "color:black";
      }
    },
    // 格式化表头
    formatColumn (data) {
      this.column = [
        {
          label: "名称",
          value: "name",
          width: "200px"
        }
      ];
      let flag_list = [...new Set(data.map(v => v.flag))].sort((a, b) => {
        const aArr = a.split("_");
        const bArr = b.split("_");
        const aYear = parseInt(aArr[0]);
        const bYear = parseInt(bArr[0]);
        const aMonth = parseInt(aArr[1]);
        const bMonth = parseInt(bArr[1]);
        if (aYear !== bYear) {
          return aYear - bYear;
        } else {
          return aMonth - bMonth;
        }
      });
      // console.log(this.measure, this.measure_list);
      let column = this.measure.map(item => {
        let label = this.measure_list?.find(v => v.value == item).label;
        if (this.model) {
          return {
            label,
            value: item,
            children: flag_list.map(v => {
              return {
                label: v,
                value: v + item,
                format: label == "夏普率" ? this.fix2 : this.fix2p,
                sortable: true,
                popover: true,
                fill:
                  label == "夏普率" || label.includes("收益")
                    ? "red_or_green"
                    : ""
              };
            })
          };
        } else {
          return {
            label,
            value: flag_list?.[0] + item,
            format: label == "夏普率" ? this.fix2 : this.fix2p,
            fill:
              label == "夏普率" || label.includes("收益") ? "red_or_green" : "",
            sortable: true,
            popover: true
            // children: flag_list.map((v) => {
            // 	return { label: v, value: v + item, format: label == '夏普率' ? this.fix2 : this.fix2p, sortable: true };
            // })
          };
        }
      });
      this.column.push(...column);
    },
    async getname (query) {
      let data = ''
      return await axios
        .get(this.$baseUrl + '/Analysis/Search/?message=' + query + '&flag=6')
        .then((res) => {
          data = res?.data?.data[0]?.name || query
          // console.log(data);
          return data
        })
        .catch(err => {
          data = query
          console.log(data);
          return data
        })

    },
    // 格式化数据
    formatData (data) {
      let v_data = [];
      let that = this;
      data.map(async item => {
        let name =
          item.flag == "pool"
            ? this.info.name
            : this.info["code_list"].find(v => v.code == item.code)?.name;
        if (name == undefined) {
          name = await that.getname(item.code)
          // console.log(name);
        }
        // name = name == undefined ? item.code : name
        let color =
          item.flag == "pool"
            ? "#4096ff"
            : this.info["code_list"].find(v => v.code == item.code)?.color;
        let type =
          item.flag == "pool"
            ? 0
            : this.info["code_list"].find(v => v.code == item.code)?.flag;

        let index = v_data.findIndex(v => v.code == item.code);
        let obj = {
          code: item.code,
          name,
          color,
          type
        };

        let column_list = ["code", "flag"];
        for (const key in item) {
          if (!column_list.some(v => v == key)) {
            obj[item["flag"] + key] = item[key];
          }
        }
        if (index == -1) {
          v_data.push(obj);
        } else {
          v_data[index] = { ...v_data[index], ...obj };
        }
      });
      let sort_order = [1, 2, 0];
      this.data = v_data.sort((a, b) => {
        return sort_order.indexOf(a.type) - sort_order.indexOf(b.type);
      });
    },
    fix2p (val) {
      return !isNaN(val) && typeof (val * 1) == "number"
        ? (val * 100).toFixed(2) + "%"
        : "--";
    },
    fix2 (val) {
      return !isNaN(val) && typeof (val * 1) == "number"
        ? (val * 1).toFixed(2)
        : "--";
    },
    exportExcel () {
      let list = [];
      this.column.map(item => {
        if (item.children) {
          item.children.map(obj => {
            list.push({
              label: obj.label + item.label,
              value: obj.value
            });
          });
        } else {
          list.push({
            label: item.label,
            value: item.value
          });
        }
      });
      let data = this.data.map(item => {
        let obj = { ...item };
        for (const key in item) {
          let index = this.column.findIndex(v => key.indexOf(v.value) != -1);
          if (index != -1) {
            let format = this.column[index]?.format;
            if (format) {
              obj[key] = format(item[key]);
            } else {
              let children = this.column[index]?.children;
              if (children) {
                let formatter = children.find(v => v.value == key)?.format;
                if (formatter) {
                  obj[key] = formatter(item[key]);
                }
              }
            }
          }
        }
        return obj;
      });
      list.unshift({ label: '基金代码', value: 'code' })
      filter_json_to_excel(list, data, "指标计算器");
    }
  },
  mounted () {
    this.ismanager = String(this.$route.query.ismanager) == 'true' ? true : false
  },
};
</script>

<style lang="scss" scoped>
.targetChoose {
	.target_background {
		background: #fafafa;
		border-radius: 4px;
	}
	.overflow_ellipsis {
		a {
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
		.el-link {
			justify-content: start;
		}
		::v-deep.el-link--inner {
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
	}
	::v-deep.el-table__body-wrapper::-webkit-scrollbar {
		width: 8px;
	}
}
</style>
