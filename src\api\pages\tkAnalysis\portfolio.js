import { combinationRequest as request, default as analysisRequest } from '@/api/request';
import axios from 'axios';
// 配置策略/组合策略列表
export async function getPolicyList(params) {
	return request({
		url: '/getPolicyList',
		method: 'get',
		params
	});
}
// 组合列表
export async function combinationList(params) {
	return request({
		url: '/combinationList',
		method: 'get',
		params
	});
}

// 创建组合
export async function createCombination(params) {
	return request({
		url: '/createCombination',
		method: 'post',
		data: params
	});
}

// 组合调仓记录列表
export async function getRecodeList(params) {
	return request({
		url: '/combinationAdjRecords',
		method: 'get',
		params
	});
}

// 组合调仓日期列表
export async function getRecodeDateList(params) {
	return request({
		url: '/combinationTimeList',
		method: 'get',
		params
	});
}

// 组合调仓日期列表
export async function adjustHolding(params) {
	return request({
		url: '/adjustHolding',
		method: 'post',
		data: params
	});
}

// 删除调仓记录
export async function deleteAdjustPositionRecord(params) {
	return request({
		url: '/deleteAdjustPositionRecord',
		method: 'get',
		params
	});
}

// 添加组合标签
export async function addCombinationLabel(params) {
	return request({
		url: '/addCombinationLabel',
		method: 'get',
		params
	});
}

// 组合资产权重详情(组合配置情况)
export async function combinationAssetWeightChange(params) {
	return request({
		url: '/combinationAssetWeightChange',
		method: 'get',
		params
	});
}

// 组合调仓日期保存
export async function combinationHoldNav(params) {
	return request({
		url: '/combinationHoldNav',
		method: 'get',
		params
	});
}

// 组合调仓确认
export async function adjustPositionConfirm(params) {
	return request({
		url: '/adjustPositionConfirm',
		method: 'get',
		params
	});
}

// 删除配置策略/组合策略或模拟组合
export function deletePolicy(params) {
	return request({
		url: '/deletePolicy',
		method: 'get',
		params
	});
}

// 组合详情
export async function combinationDetail(params) {
	return request({
		url: '/combinationDetail',
		method: 'get',
		params
	});
}

// 更新组合
export async function updateCombination(data) {
	return request({
		url: '/updateCombination',
		method: 'post',
		data
	});
}

// 生成模拟组合
export async function createImitateCombination(params) {
	return request({
		url: '/createImitateCombination',
		method: 'get',
		params
	});
}

// 组合持仓监控
export async function CombinationPositionMonitor(params) {
	return request({
		url: '/combinationPositionMonitor',
		method: 'get',
		params
	});
}

// 组合风险收益
export async function combinationRiskReturn(params) {
	return request({
		url: '/combinationRiskReturn',
		method: 'get',
		params
	});
}

// 方案业绩表现曲线/持仓净值走势
export async function holdingRateTrend(params) {
	return request({
		url: '/holdingRateTrend',
		method: 'get',
		params
	});
}

// 组合收益贡献度走势
export async function combinationHoldReturn(params) {
	return request({
		url: '/returnContributeAnalysis',
		method: 'get',
		params
	});
}

// 方案业绩表现表格/总体风险收益
export async function overallRiskReturn(params) {
	return request({
		url: '/overallRiskReturn',
		method: 'get',
		params
	});
}

// 基金标签
export async function combinationFundTags(params) {
	return request({
		url: '/combinationFundTags',
		method: 'get',
		params
	});
}

// 报告期前十大集中度
export async function concentrationInfo(params) {
	return request({
		url: '/concentrationInfo',
		method: 'get',
		params
	});
}

// 报告期换手率
export async function turnoverInfo(params) {
	return request({
		url: '/turnoverInfo',
		method: 'get',
		params
	});
}

// 持仓风格
export async function holdStyle(params) {
	return request({
		url: '/holdStyle',
		method: 'get',
		params
	});
}

// 当前权益持仓风格
export async function equityHoldStyle(params) {
	return request({
		url: '/equityHoldStyle',
		method: 'get',
		params
	});
}

// 当前权益持仓风格
export function combinationFinancialIndex(params) {
	return request({
		url: '/combinationFinancialIndex',
		method: 'get',
		params
	});
}

// 组合个股持仓复盘
export async function combinationPositionReview(params) {
	return request({
		url: '/combinationPositionReview',
		method: 'get',
		params
	});
}

// 获取指标列表
export function majorAssetPerformance(params) {
	return request({
		url: '/majorAssetPerformance',
		method: 'get',
		params
	});
}

// 持仓债券分析
export async function BondHoldingMsg(params) {
	return request({
		url: '/bondHoldingMsg',
		method: 'get',
		params
	});
}
// 行业持仓分析
export async function industryPositionAnalysis(params) {
	return request({
		url: '/industryPositionAnalysis',
		method: 'get',
		params
	});
}

// 行业操作复盘
export async function industryOperationsReview(params) {
	return request({
		url: '/industryOperationsReview',
		method: 'get',
		params
	});
}

// Brinson归因
export async function brinsonAttribution(params) {
	return request({
		url: '/brinsonAttribution',
		method: 'get',
		params
	});
}

//刷新组合调仓信息
export async function refreshHoldingDetails(params) {
	return request({
		url: '/refreshHoldingDetails',
		method: 'get',
		params
	});
}

export async function industryInfo(params) {
	return request({
		url: '/industryInfo',
		method: 'get',
		params
	});
}

// 大类资产指数列表
export function getMajorAssetIndexlist(params) {
	return request({
		url: '/getMajorAssetIndexList',
		method: 'get',
		params
	});
}
export function getIndexSearchList(params) {
	return request({
		url: '/Search',
		method: 'get',
		params
	});
}
/**
 * 产品分析-收益走势
 * */
export function getProductTrend(data) {
	return request({
		url: '/productTrend',
		method: 'post',
		data
	});
}
/**
 * 产品分析 产品表现
 * */
export function getProductPerformance(data) {
	return request({
		url: '/productPerformance',
		method: 'post',
		data
	});
}
/**
 * 收益相关系数矩阵
 * */
export function getReturnsCorrelationMatrix(data) {
	return request({
		url: '/correlation',
		method: 'post',
		data
	});
}
/**
 * 配置策略方案列表
 */
export function getPloyPlanlist(params) {
	return request({
		url: '/ployPlanlist',
		method: 'get',
		params
	});
}
/**
 *基金池基金列表
 */
export function getPoolList(params) {
	// return Promise.resolve({
	// 	mtycode: 200,
	// 	message: 'string',
	// 	data: [
	// 		{
	// 			poolId: 'poolId',
	// 			poolName: 'poolName',
	// 			codeList: [
	// 				{
	// 					code: 'code',
	// 					name: 'name'
	// 				}
	// 			]
	// 		}
	// 	]
	// });
	return request({
		url: '/poolList',
		method: 'get',
		params
	});
}
/**
 * 全市场公募基金列表
 */

export function getMarketWindFund(params) {
	return request({
		url: '/marketWindFund',
		method: 'get',
		params
	});
}

/**
 * 资产分析模型默认模版查询
 */
export function getModelTemplate(data) {
	return request({
		url: '/modelTemplateQuery',
		method: 'post',
		data
	});
}

/**
 *策略/组合研究参数详情页
 */
export function getParameterDetails(params) {
	return request({
		url: '/parameterDetails',
		method: 'get',
		params
	});
}
/**
 *交易流水文件/持仓文件导入
 */
 export function uploadTemplate(data) {
  // console.log(data);
	return request({
		url: '/uploadTemplate',
		method: 'post',
		data,
		 headers:{
		 	'Content-Type':'application/form-data'
		 }
	});
}
/**
 * 参数设置-持仓设置 获取持仓设置列表
 */
export function getPositionSettingList(data) {
	// return Promise.resolve({
	// 	code: 200,
	// 	message: 'string',
	// 	data: {
	// 		priorRisk: 'string',
	// 		positionList: [
	// 			{
	// 				price: 'string',
	// 				maxRatio: 'string',
	// 				minRatio: 'string',
	// 				code: 'string',
	// 				name: 'string'
	// 			}
	// 		]
	// 	}
	// });
	return request({
		url: '/positionSettingList',
		method: 'post',
		data
	});
}
/**
 * 搜索
 */
export function getSearchList(params) {
	// return Promise.resolve({
	// 	data: [
	// 		{
	// 			code: 'string',
	// 			name: 'string',
	// 			fundCo: 'string',
	// 			companyCode: 'string',
	// 			flag: 'string',
	// 			startFrom: 'string',
	// 			type: ['string']
	// 		}
	// 	],
	// 	mtycode: 200,
	// 	mtymessage: 'string'
	// });
	return analysisRequest({
		url: '/Search/',
		method: 'get',
		params,
		cancelRequest: true
	});
}
//修改大类资产指数模板
export function updateMajorAssetType(data) {
	return request({
		url: '/updateMajorAssetType',
		method: 'post',
		data
	});
}
