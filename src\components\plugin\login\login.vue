<!--  -->
<template>
  <div v-if="showdialog"
       class="loginBox">
    <el-dialog width="480px"
               class="dialogloginclass"
               :show-close="false"
               :close-on-click-modal="false"
               :visible.sync="showdialog">
      <template slot="title">
        <img style="width: 480px;height: 346px; position: absolute; ;"
             src="../../../assets/img/header.png" />
        <div style="display: flex; align-items: center; justify-content: space-between; height: 10px; padding-left: 16px; padding-right: 16px">
          <div>
            <div v-if="!showGDB"
                 class="ms-title">
              <div>
                <svg width="64"
                     height="37"
                     viewBox="0 0 64 37"
                     fill="none"
                     xmlns="http://www.w3.org/2000/svg"
                     xmlns:xlink="http://www.w3.org/1999/xlink">
                  <rect y="0.984375"
                        width="64"
                        height="36"
                        fill="url(#pattern0)" />
                  <defs>
                    <pattern id="pattern0"
                             patternContentUnits="objectBoundingBox"
                             width="1"
                             height="1">
                      <use xlink:href="#image0_4684_128224"
                           transform="translate(-0.0303888) scale(0.******** 0.********)" />
                    </pattern>

                  </defs>
                </svg>
              </div>
              <!-- <div style>慧捕基捕基能手</div> -->
            </div>
            <div v-if="showGDB"
                 class="ms-title">
              <div>
                <!-- <img style="height: 30px" src="../../../../version/projectGDBank/asset/img/1.png" /> -->
              </div>
            </div>
          </div>
          <div>
            <!-- <i style="cursor: pointer" @click="showdialog = false" class="el-icon-close"></i> -->
          </div>
        </div>
        <div v-if="!showGDB"
             style="
             z-index:999 ;
             position: relative;
						margin-top: 40px;
						font-weight: 600;
						font-size: 30px;
						line-height: 38px;
						color: rgba(0, 0, 0, 0.85);
						width: 100%;
						text-align: center;
					">
          慧捕基
        </div>
        <div v-if="showGDB"
             style="
						margin-top: 40px;
						font-weight: 600;
						font-size: 30px;
						line-height: 38px;
						color: rgba(0, 0, 0, 0.85);
						width: 100%;
						text-align: center;
					">
          慧捕基
        </div>
        <!-- <div style="margin-top: 16px; display: flex; align-items: center; margin-left: 32px; margin-right: 32px">
					<div @click="changeindex(1)" style="flex: 1">
						<div
							:style="
								nowindex == 1
									? 'font-family: PingFang;font-weight: 500;font-size: 16px;line-height: 24px;text-align: center;color: #4096ff;border-bottom:2px solid #4096ff'
									: 'font-family: PingFang;font-weight: 500;font-size: 16px;line-height: 24px;text-align: center;'
							"
						>
							登录
						</div>
					</div>
					<div @click="changeindex(2)" style="flex: 1">
						<div
							:style="
								nowindex == 2
									? 'font-family: PingFang;font-weight: 500;font-size: 16px;line-height: 24px;text-align: center;color: #4096ff;border-bottom:2px solid #4096ff'
									: 'font-family: PingFang;font-weight: 500;font-size: 16px;line-height: 24px;text-align: center;'
							"
						>
							联系我们
						</div>
					</div>
				</div> -->
      </template>
      <div class="ms-login"
           style="margin-top: 20px"
           v-show="nowindex == 1">
        <el-form :model="param"
                 :rules="rules"
                 ref="login"
                 label-width="0px"
                 class="ms-content">
          <el-form-item prop="username">
            <el-input v-model="param.username"
                      tabindex="1"
                      placeholder="用户名">
              <el-button slot="prepend"
                         icon="el-icon-user"></el-button>
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input tabindex="2"
                      :type="passwordtpye"
                      placeholder="密码"
                      v-model="param.password">
              <i slot="suffix"
                 @click="passwordtpyechangge"
                 class="el-input__icon el-icon-view"></i>
              <el-button slot="prepend"
                         icon="el-icon-lock"></el-button>
            </el-input>
          </el-form-item>
          <!-- <div class="loginpx1">
            <div>
              <el-form-item prop="ident">
                <el-input
                  tabindex="3"
                  class="inputboxs"
                  type="text"
                  placeholder="验证码"
                  v-model="param.ident"
                  @keyup.enter.native="submitForm"
                >
                  <el-button slot="prepend" icon="el-icon-key"></el-button>
                </el-input>
              </el-form-item>
            </div>
            <div @click="refreshCode" class="loginpx2">
              <SIdentify
                class="loginpx3"
                :identifyCode="identifyCode"
              ></SIdentify>
            </div>
          </div> -->

          <div class="login-btn"
               style="margin-bottom: 20px;position: relative;">
            <el-button v-loading="loadinglogin"
                       type="primary"
                       :style="loadinglogin ? 'background:#e8e8e8 !important' : ''"
                       :disabled="loadinglogin"
                       @click="submitForm()">登录</el-button>
          </div>
          <!-- <div class="switch">
						还没有账号?
						<el-button class="switch-btn" type="text" @click="switchLogin('toRegister')">注册</el-button>
						成为内测用户
					</div> -->
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import SIdentify from './components/identify';
import { login } from '@/api/common.js';
import store from '@/store/store';
import router from '@/router';
import CryptoJS from 'crypto-js';
export default {
  //import引入的组件需要注入到对象中才能使用
  components: { SIdentify },
  data () {
    //这里存放数据
    return {
      nowindex: 1,
      showdialog: false,
      showGDB: false,
      param: {
        username: '',
        password: '',
        ident: null
      },
      isLogin: true,
      passwordtpye: 'password',
      identifyCodes: '1234567890',
      identifyCode: '',
      rules: {
        username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
        ident: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
      },
      registerRules: {
        username: { required: true, message: '请输入用户名', trigger: 'blur' },
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          {
            type: 'string',
            pattern: /^((\d)+[A-Za-z]+[A-Za-z0-9]*)|([A-Za-z]+(\d)+[A-Za-z0-9]*)$/,
            message: '密码需包含字母和数字,且长度在6~30位之间',
            min: 6,
            max: 30,
            trigger: 'blur'
          }
        ],
        // first_name: { required: true, message: '请输入姓', trigger: 'blur' },
        // last_name: { required: true, message: '请输入名', trigger: 'blur' },
        mobile: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          {
            type: 'string',
            pattern: /^1[3-9]\d{9}$/,
            message: '请输入长度为11位的手机号码',
            trigger: 'blur'
          }
        ],
        wechat: { required: true, message: '请输入微信', trigger: 'blur' },
        // title: { required: true, message: '请输入职位', trigger: 'blur' },
        email: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          {
            type: 'string',
            pattern: /^([\w\._-]+)@([\w\._-]+)\.([\w\._-]+)$/,
            message: '请输入正确的邮箱',
            trigger: 'blur'
          }
        ],
        institute_name: {
          required: true,
          message: '请输入您所属的公司名称',
          trigger: 'blur'
        }
      },
      isLogin: true,
      uploadUrl: '',
      fileList: [],
      loadinglogin: false
    };
  },
  //方法集合
  methods: {
    changeindex (flag) {
      this.nowindex = flag;
    },
    // 加密
    encrypt (word) {
      // word, keyStr第一个参数是加密的字段名字  第二个是key值（16位）
      var key = CryptoJS.enc.Utf8.parse('bbfYTUNoNCQUSDuI');
      var srcs = CryptoJS.enc.Utf8.parse(word);
      var encrypted = CryptoJS.AES.encrypt(srcs, key, { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 });
      return encrypted.toString();
    },
    async submitForm () {
      this.loadinglogin = true;
      var password = this.encrypt(this.param.password).toString();
      //   if (this.param.ident == this.identifyCode) {
      let data = await login({
        username: this.param.username,
        password
      });
      // axios
      // 	.post(that.$baseUrl + '/login/', { username: that.param.username, password: that.param.password })
      // 	.then(async (res) => {
      this.loadinglogin = false;
      if (data?.detail != '没有提供正确的登录信息') {
        /* vue全局事件派发 */
        this.$event.$emit('login', true);
        store.dispatch('changetoken', data.access);
        store.dispatch('changerefreshtoken', data.refresh);
        store.dispatch('changeusername', data.username);
        store.dispatch('changeuserid', data.user_id);
        store.dispatch('changeuserrole', data.roles);
        store.commit('setUserType', data.type);
        this.showdialog = false;
        if (router.history.current.fullPath == '/dashboard?flaglogin=logins') {
          await router.push({
            path: '/dashboard',
            query: { flaglogin: 'login' }
          });
        }
        if (router.history.current.fullPath == '/dashboard?flaglogin=login') {
          await router.push({
            path: '/dashboard',
            query: { flaglogin: 'logins' }
          });
        }
        if (router.history.current.fullPath == '/dashboard' || router.history.current.fullPath == '/') {
          console.log('object');
          await router.push({
            path: '/dashboard',
            query: { flaglogin: 'login' }
          });
        }

        // console.log(router);
        this.$nextTick(() => {
          if (localStorage.getItem('nextPath') && JSON.parse(JSON.parse(localStorage.getItem('nextPath')).value).path != '') {
            router.push({
              path: JSON.parse(JSON.parse(localStorage.getItem('nextPath')).value).path,
              query: {
                ...JSON.parse(JSON.parse(localStorage.getItem('nextPath')).value).query,
                flaglogin: 'login'
              },
              params: JSON.parse(JSON.parse(localStorage.getItem('nextPath')).value).params
            });
            localStorage.setItem('nextPath', '');
          } else {
            router.push({
              path: '/dashboard',
              query: { flaglogin: 'login' }
            });
          }
        });
        // if(localstorage.setItem(localstorage.))
      } else {
        that.$message('登陆失败' + ',' + data?.detail);
      }
      // })
      // .catch((err) => {
      // 	console.log(err);
      // 	that.loadinglogin = false;

      // 	if (err.data?.detail && err.data?.detail == '没有提供正确的登录信息') {
      // 		that.$message('没有提供正确的登录信息');
      // 	} else {
      // 		that.$message('账号密码错误');
      // 	}
      // });
      //   } else {
      //     this.loadinglogin = false;
      //     this.$message("验证码错误");
      //     this.refreshCode();
      //   }
    },
    passwordtpyechangge () {
      if (this.passwordtpye == 'password') {
        this.passwordtpye = 'text';
      } else {
        this.passwordtpye = 'password';
      }
    },
    randomNum (min, max) {
      return Math.floor(Math.random() * (max - min) + min);
    },
    refreshCode () {
      this.identifyCode = '';
      this.makeCode(this.identifyCodes, 4);
    },
    makeCode (o, l) {
      for (let i = 0; i < l; i++) {
        this.identifyCode += this.identifyCodes[this.randomNum(0, this.identifyCodes.length)];
      }
      // ////console.log(this.identifyCode);
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () {
    this.showdialog = true;
    if (window.localStorage.getItem('mty_modulesName') == 'GDBank') {
      this.showGDB = true;
    } else {
      this.showGDB = false;
    }
    this.identifyCode = '';
    this.makeCode(this.identifyCodes, 4);
  },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style>
.ms-login .el-input__inner {
	border-radius: 0 !important;
}
.ms-login .el-input--small .el-input__inner {
	height: 45px !important;
	line-height: 45px !important;
}
.loginpx3 {
	width: 90px;
	height: 60px;
}
.loginpx1 {
	display: flex;
	width: 300px;
}
.loginpx2 {
	width: 90px;
	height: 48px;
}
.ms-login .el-form-item__content {
	margin-left: 0px !important;
}
.dialogloginclass .el-dialog__header {
	padding: 0px !important;
}
.dialogloginclass .el-dialog__body {
	padding: 0px !important;
}
</style>

<style scoped lang="scss">
.cash {
	position: fixed;
	height: 100vh;
	width: 100%;
}

.loginbottom {
	position: fixed;
	bottom: 10px;
	width: 100%;
}
.ms-title {
	width: 100%;
	line-height: 75px;
	text-align: center;
	font-size: 30px;
	color: #fff;
	// border-bottom: 1px solid #ddd;
}
.ms-login {
	width: 425px;
	margin-left: 32px;

	// margin: -285px 0 0 -225px;
	border-radius: 5px;
	// background: rgba(255, 255, 255, 0.3);
	overflow: hidden;
	.switch {
		text-align: center;
		.switch-btn {
			display: inline-block;
			padding: 0 !important;
		}
	}
	.register-form {
		display: flex;
		justify-content: space-between;
	}
}
.inputboxs {
	width: 282px;
}
.ms-content {
	padding: 16px 16px;
}
.login-btn {
	text-align: center;
}
.login-btn button {
	width: 100%;
	height: 45px;
	margin-bottom: 15px;
}
.login-tips {
	font-size: 18px;
	line-height: 45px;
	color: #fff;
}
.upload-box {
	width: 100%;
	::v-deep .el-upload--text {
		display: inline;
		border: none;
		.el-input__inner {
			cursor: pointer;
		}
	}
}

.main {
	width: 100%;
	min-width: 1200px;
	height: 100vh;
	background-color: #fff;
	background: radial-gradient(ellipse at bottom, #54677c 0%, #090a0f 100%);
	overflow: hidden;
	filter: drop-shadow(0 0 10px white);
	position: relative;
	.main-title {
		position: absolute;
		left: 50%;
		top: 70px;
		transform: translateX(-50%);
		font-size: 30px;
		color: rgba(255, 255, 255, 1);
		&::after {
			content: '';
			position: absolute;
			left: 50%;
			transform: translateX(-50%);
			bottom: -28px;
			width: 28px;
			height: 4px;
			background: rgba(244, 54, 63, 1);
			border-radius: 2px;
		}
	}
}
</style>
