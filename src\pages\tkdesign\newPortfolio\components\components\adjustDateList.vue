<template>
	<div style="flex-basis: 130px; border: 1px solid #e9e9e9; margin-right: 8px">
		<!-- <ui style="width: 100%;">
            <li :class="['date-item',active === date ? 'active' : '']" v-for="(date,key) in dateList" :key="key" @click="handleBtnClick(date)">
                <span>
                    {{ date }}
                </span>
               
                
            </li>
        </ui> -->
		<el-tabs :tab-position="'left'" style="height: 460px" @tab-click="handleBtnClick" v-model="active">
			<el-tab-pane v-for="(date, key) in dateList" :key="key" :name="date">
				<template slot="label">
					<span style="text-align: left; width: 100%; display: inline-block">{{ date }} </span>
					<i v-if="active === date" class="el-icon-delete" @click.stop="deleteDate(date)"></i>
				</template>
			</el-tab-pane>
		</el-tabs>
	</div>
</template>
<script>
export default {
	name: 'adjustDateList',
	props: {
		dateList: {
			type: Array,
			default() {
				return [];
			}
		},
		currentDate: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			active: ''
		};
	},
	watch: {
		currentDate(val) {
			this.active = val;
		}
	},
	methods: {
		handleBtnClick(date) {
			console.log('::::date;::::', date.name);
			this.active = date.name;
			this.$emit('click', date.name);
		},
		deleteDate(date) {
			this.$emit('deleteDate', date);
		}
	}
};
</script>
<style lang="scss" scoped>
li {
	margin: 0;
	padding: 0;
	list-style: none;
}
.date-item {
	height: 36px;
	line-height: 36px;
	span {
		padding: 3px 16px;
		align-items: center;
		overflow: hidden;
		color: rgba(0, 0, 0, 0.85);
		width: 116px;
		text-overflow: ellipsis;
		white-space: nowrap;
		/* 四级文字/常规 */
		font-family: PingFang SC;
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
		line-height: 22px; /* 157.143% */
	}
	.el-icon-delete {
		font-size: 14px;
		color: rgba(0, 0, 0, 0.25);
		display: none;
	}
}
.active {
	border-right: 3px solid #4096ff;
	background: #ecf5ff;
	.el-icon-delete {
		display: inline-block;
		font-size: 14px;
		color: rgba(0, 0, 0, 0.25);
	}
}
.date-item:hover {
	cursor: pointer;
}
</style>
