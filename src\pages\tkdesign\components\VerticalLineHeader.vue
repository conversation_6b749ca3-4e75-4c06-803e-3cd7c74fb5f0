<template>
    <div class="vertival-line-header-wrapper">
        <div class="vlhw-title"><span class="vlhw-title-border"></span> {{title}}</div>
        <div class="vlhw-title-right">
            <slot name="right"></slot>
            <el-button v-if="showDownloadBtn" class="download-btn-wrapper" @click="handleDownloadClick">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path d="M7.88736 10.6575C7.90072 10.6745 7.9178 10.6883 7.93729 10.6978C7.95678 10.7073 7.97818 10.7123 7.99986 10.7123C8.02154 10.7123 8.04294 10.7073 8.06243 10.6978C8.08192 10.6883 8.099 10.6745 8.11236 10.6575L10.1124 8.1271C10.1856 8.03424 10.1195 7.89674 9.99986 7.89674H8.67665V1.85389C8.67665 1.77531 8.61236 1.71103 8.53379 1.71103H7.46236C7.38379 1.71103 7.3195 1.77531 7.3195 1.85389V7.89496H5.99986C5.88022 7.89496 5.81415 8.03246 5.88736 8.12532L7.88736 10.6575ZM14.5356 10.0325H13.4641C13.3856 10.0325 13.3213 10.0967 13.3213 10.1753V12.9253H2.67843V10.1753C2.67843 10.0967 2.61415 10.0325 2.53557 10.0325H1.46415C1.38557 10.0325 1.32129 10.0967 1.32129 10.1753V13.711C1.32129 14.0271 1.57665 14.2825 1.89272 14.2825H14.107C14.4231 14.2825 14.6784 14.0271 14.6784 13.711V10.1753C14.6784 10.0967 14.6141 10.0325 14.5356 10.0325Z" fill="black" fill-opacity="0.45"/>
              </svg>
            </el-button>
        </div>
    </div>
</template>
<script>
export default {
    name:'VerticalLineHeader',
    components:{
    },
    props:{
        title:{
            type: String,
            default:''
        },
         //是否展示下载按钮
         showDownloadBtn:{
            type:Boolean,
            default: false,
        },
    },
    methods:{
        handleDownloadClick(){
            this.$emit('downloadClick')
        }
    },
}
</script>
<style lang="scss" scoped>
.vertival-line-header-wrapper {
    display: flex;
    padding: 14px 0px;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    border-bottom: 2px solid #E9E9E9;
    margin-bottom: 20px;
    .vlhw-title {
        color: rgba(0, 0, 0, 0.85);
        font-size: 18px;
        font-style: normal;
        font-weight: 500;
        line-height: 26px;
        white-space: nowrap;
        .vlhw-title-border {
            display: inline-block;
            width: 6px;
            height: 20px;
            border-radius: 35px;
            background:  #4096ff;
            margin-right: 9px;
            vertical-align: middle;
        }
    }
    .vlhw-title-right {
        display: flex;
        align-items: center;
        .title-right-form {
            display: flex;
        }
        ::v-deep .radio-group-wrapper {
                margin-left: 16px;
        }
    }
    ::v-deep .el-form-item {
        margin-bottom: unset;
        .el-form-item__label {
            color: #000000;
        }
    }
    .more-btn-wrapper {
        margin-left: 16px;
    }
    .download-btn-wrapper {
        margin-left: 16px;
        display: flex;
        width: 32px;
        height: 32px;
        align-items: center;
        justify-content: center;
    }
}
</style>