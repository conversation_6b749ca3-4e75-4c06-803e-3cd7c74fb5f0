<template>
    <div class="plate-wrapper fund-performance-board-wrapper">
        <combinationComponentHeader title="组合收益贡献度走势" showMoreBtn @download="exportExcel">
            <template slot="right">
                
                
            </template>
        </combinationComponentHeader>
  
        <pileUpBarChart ref="fund-performance-board-chart-container" @tableData="getTableData"></pileUpBarChart>
    </div>
</template>
<script>
import combinationComponentHeader from './combinationComponentHeader.vue';
import pileUpBarChart from '../chart/pileUpBarChart.vue';
import { filter_to_excel } from "@/utils/exportExcel.js";
export default {
    name:'incomeContributionDegree',
    components:{
        combinationComponentHeader,
        pileUpBarChart
    },
    data(){
        return {
            tableData:[],
            tableHeader:[
                {
                    prop: 'date',
                    label: '净值日期',
                }
                ]
        }
    },
    methods:{
        getData(params){
            let chartDom = this.$refs['fund-performance-board-chart-container'];
            chartDom?.getData(params)
        },
        getTableData(val){
            this.tableHeader = [
                ...this.tableHeader,
                ...val.lengdList
                ]
                val.date_list.forEach((item,index) => {
                    this.tableData.push({
                        date:item
                    })
                    val.data1.forEach((item1,index1) => {
                        this.tableData[index][item1.name] = item1.data[index];
                    })
                    
                })
        },
        exportExcel(){
            let list = this.tableHeader.map((item) => {
				return {
					...item,
					format: ''
				};
			});
			filter_to_excel(list, this.tableData, '组合收益贡献度走势');
        }
    }
    
}
</script>
<style lang="scss" scoped>
.fund-performance-board-wrapper {
    .select-form-wrapper {
        margin-bottom: 16px;
    }
    .content-table-wrapper {
        margin-bottom: 32px;
    }
}

</style>