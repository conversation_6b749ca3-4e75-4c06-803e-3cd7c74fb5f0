<template>
    <div class="plate-wrapper valuation-percentile-wrapper" v-loading="loading">
        <combinationComponentHeader title="总体风险收益" @download="exportExcel">
            <template slot="right">
                <div style="margin-right: 16px;">
                    <FormTimePicker v-model="preset_time" @input="handleFormChange"></FormTimePicker>
                </div>
            </template>
        </combinationComponentHeader>
        <el-table border stripe :data="tableDataNow">
            <el-table-column min-width="233px" align="gotoleft" prop="cum_return1" show-overflow-tooltip label="区间指数">
                
            </el-table-column>
            <el-table-column min-width="160px" align="gotoleft" prop="data1" label="产品">
                <templete slot-scope="scope">{{ scope.row.key1 === 'karma' || scope.row.key1 === 'sharp' || scope.row.key1 === 'information' ||scope.row.key1 === 'sortino' ? fix2(scope.row.data1) : fix2p(scope.row.data1 ) }}</templete>
            </el-table-column>
            <el-table-column min-width="136px" align="gotoleft" prop="nav1"  label="基准">
                <templete slot-scope="scope">{{scope.row.key1 === 'karma' || scope.row.key1 === 'sharp' || scope.row.key1 === 'information' ||scope.row.key1 === 'sortino' ? fix2(scope.row.nav1): fix2p(scope.row.nav1)  }}</templete>
            </el-table-column>
            <el-table-column min-width="112px" align="gotoleft" prop="cum_return2"  label="区间指标">

            </el-table-column>
            <el-table-column min-width="112px" align="gotoleft"  prop="data2" label="产品">
                <templete slot-scope="scope" v-if="scope.row.key2 === 'recoveryLength' || scope.row.key2 === 'winDays' || scope.row.key2 === 'loseDays'">{{ Number(scope.row.data2).toFixed(0) || '--'}}</templete>
                <templete slot-scope="scope" v-else-if="!scope.row.key2"></templete>
                <templete slot-scope="scope" v-else>{{ fix2p(scope.row.data2)  }}</templete>
            </el-table-column>
            <el-table-column min-width="112px" align="gotoleft"  prop="nav2" label="基准">
                <templete slot-scope="scope" v-if="scope.row.key2 === 'recoveryLength' || scope.row.key2 === 'winDays' || scope.row.key2 === 'loseDays'">{{ Number(scope.row.nav2).toFixed(0) || '--'}}</templete>
                <templete slot-scope="scope" v-else-if="!scope.row.key2"></templete>
                <templete slot-scope="scope" v-else>{{ fix2p(scope.row.nav2)  }}</templete>
            </el-table-column>

            <template slot="empty">
                <el-empty image-size="160"></el-empty>
            </template>
        </el-table>
    </div>
</template>
<script>
import combinationComponentHeader from './combinationComponentHeader.vue';
import FormTimePicker from './formTimePicker.vue';
import { overallRiskReturn } from '@/api/pages/tkAnalysis/portfolio.js';
import { filter_to_excel } from "@/utils/exportExcel.js";
const dayjs = require('dayjs');
export default {
    name:'totalRiskReturn',
    components:{
        combinationComponentHeader,
        FormTimePicker
    },
    data(){
        return {
            form:{},
            loading:false,
            tableHeader:[{
                prop:'cum_return1',
                label:'区间指数'

            },{
                prop:'data1',
                label:'产品'

            },{
                prop:'nav1',
                label:'基准'

            },{
                prop:'cum_return2',
                label:'区间指数'

            },{
                prop:'data2',
                label:'产品'

            },{
                prop:'nav2',
                label:'基准'

            }],
            tableDataNow: [
                {
                    cum_return1:'区间收益',
                    cum_return2:'最大回撤',
                    key1:'cumReturn',
                    key2:'maxDrawdown',
                    data1:'',
                    data2:'',
                    nav1:'',
                    nav2:''
                },
                {
                    cum_return1:'年化收益',
                    cum_return2:'最大回撤恢复天数',
                    key1:'aveReturn',
                    key2:'recoveryLength',
                    data1:'',
                    data2:'',
                    nav1:'',
                    nav2:''
                },
                {
                    cum_return1:'波动',
                    cum_return2:'正收益率占比',
                    key1:'volatility',
                    key2:'winRatio',
                    data1:'',
                    data2:'',
                    nav1:'',
                    nav2:''
                },
                {
                    cum_return1:'卡玛率',
                    cum_return2:'负收益率占比',
                    key1:'karma',
                    key2:'loseRatio',
                    data1:'',
                    data2:'',
                    nav1:'',
                    nav2:''
                },
                {
                    cum_return1:'夏普',
                    cum_return2:'最大连续上涨天数',
                    key1:'sharp',
                    key2:'winDays',
                    data1:'',
                    data2:'',
                    nav1:'',
                    nav2:''
                },
                {
                    cum_return1:'信息率',
                    cum_return2:'最大连续下跌天数',
                    key1:'information',
                    key2:'loseDays',
                    data1:'',
                    data2:'',
                    nav1:'',
                    nav2:''
                },
                {
                    cum_return1:'索提诺比率',
                    cum_return2:'',
                    key1:'sortino',
                    key2:'',
                    data1:'',
                    data2:'',
                    nav1:'',
                    nav2:''
                },
            ],
            preset_time: {
                radioValue: '1',
                startDate: dayjs().subtract(1, 'year').format('YYYY-MM-DD'),
                endDate: dayjs().format('YYYY-MM-DD')
            },
            params:{}
        }
    },
    methods:{
        fix2(value) {
			return  value !== '' &&
				value != '--' &&
				value != '- -' &&
				JSON.stringify(value) != '[]' &&
				JSON.stringify(value) != '{}' &&
				value != 'NAN' &&
				value != 'nan' && value !== undefined &&
        value !== null 
				? Number(value).toFixed(2)
				: '--';
		},
        fix2p(value) {
			return value !== '' &&
				value != '--' &&
				value != '- -' &&
				JSON.stringify(value) != '[]' &&
				JSON.stringify(value) != '{}' &&
				value != 'NAN' &&
				value != 'nan' &&
        value !== undefined &&
        value !== null 
				? (Number(value) * 100).toFixed(2) + '%'
				: '--';
				
		},
        exportExcel(){
            let list = this.tableHeader.map((item) => {
				return {
					...item,
					format: ''
				};
			});
			filter_to_excel(list, this.tableDataNow, '总体风险收益');
        },
        async getData(param = {}){
            this.loading = true;
            this.params = param;
           let res = await overallRiskReturn({
                ...this.preset_time,
                ...param});
            if(res.mtycode != 200){
                return;
            }
            this.getTableData(res.data);
            this.loading = false;
        },
        handleFormChange(val) {
            this.preset_time = val;
            this.getData(this.params);
        
		},
        getTableData(data){
            //产品
                this.tableDataNow.forEach(item2=>{
                        item2.data1 = data.productList[item2.key1]||''
                        item2.data2 = data.productList[item2.key2]||""
                })
            //基准
                this.tableDataNow.forEach(item2=>{
                        item2.nav1 = data.referenceList[item2.key1]||''
                        item2.nav2 = data.referenceList[item2.key2]||""
                })
                console.log("this.tableDataNow:::",this.tableDataNow)
                this.$forceUpdate()
        }
    }
}
</script>
<style lang="scss" scoped></style>