import request from '@/api/request';

/**
 *
 * @param {分时段业绩表现} params
 * lag=1&code=110022&type=equity&start_date&end_date&periodname=股票牛熊市场
 * @returns
 */
export function getMarketWindowReturn(params) {
	return request({
		url: '/MarketWindowReturn/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {牛熊市下债券基金表现} params
 * lag=1&code=110022&type=equity&start_date&end_date&periodname=股票牛熊市场
 * @returns
 */
export function getMarketWindowReturnv2(params) {
	return request({
		url: '/MarketWindowReturnv2/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {基准概念列表} params
 * @returns
 */
export function getFundPeriod(params) {
	return request({
		url: '/MacroPeriod/',
		method: 'get',
		params
	});
}
