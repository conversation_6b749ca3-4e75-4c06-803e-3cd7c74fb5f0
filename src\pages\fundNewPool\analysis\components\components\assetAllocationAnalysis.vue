<template>
  <div class="chart_one assetAllocationAnalysis"
       v-if="show">
    <el-tabs v-model="activeName"
             @tab-click="handleClick"
             :before-leave="destroyCharts">
      <el-tab-pane v-for="item in tabs_list"
                   :key="item.value"
                   :label="item.label"
                   :name="item.value">
        <div v-loading="loading"
             v-if="info['code_list']">
          <div v-if="info['code_list'].length > 6">
            <div class="flex_between">
              <!-- <div class="title">{{ name }}</div> -->
              <div class="flex_start">
                <div v-if="data_type == 'pool'"
                     class="mr-12">
                  <el-select v-model="computed_active"
                             @change="handleClick">
                    <el-option v-for="item in computed_list"
                               :key="item.value"
                               :label="item.label"
                               :value="item.value"></el-option>
                  </el-select>
                </div>
                <div class="mr-12"
                     v-if="data_type == 'fund'||data_type =='manager'">
                  <el-radio-group v-model="active_class"
                                  @change="changheActive">
                    <el-radio-button label="全部"></el-radio-button>
                    <el-radio-button label="关注"></el-radio-button>
                  </el-radio-group>
                </div>
                <el-popover v-if="activeName == item.value"
                            placement="top-start"
                            width="220"
                            trigger="manual"
                            v-model="visible">
                  <div>
                    <div v-show="!showOneItem"
                         class="flex_start">
                      <div>
                        <el-dropdown @command="changeOperator">
                          <el-button type="primary"
                                     class="mr-8">
                            {{ operator != '' ? operator : '运算符' }}
                            <i class="el-icon-arrow-down el-icon--right"></i>
                          </el-button>
                          <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item command="大于">大于</el-dropdown-item>
                            <el-dropdown-item command="小于">小于</el-dropdown-item>
                          </el-dropdown-menu>
                        </el-dropdown>
                      </div>
                      <el-input style="width: 120px"
                                v-model="weight"
                                placeholder="配置权重%"></el-input>
                    </div>
                    <div class="mt-12">
                      <div>
                        <el-checkbox v-model="showOneItem"
                                     :label="true">
                          {{
                          '查看单个' + (activeName.includes('申万') ? '行业' : activeName == '主题' ? '主题' : '资产') + '配置'
                          }}
                        </el-checkbox>
                      </div>
                      <div class="mt-8">
                        <el-select :disabled="!showOneItem"
                                   v-model="active_item"
                                   placeholder>
                          <el-option v-for="item in item_list"
                                     :key="item.value"
                                     :label="item.label"
                                     :value="item.value"></el-option>
                        </el-select>
                      </div>
                    </div>
                    <div style="text-align: right; margin: 0; margin-top: 16px">
                      <el-button size="mini"
                                 type="text"
                                 @click="visible = false">取消</el-button>
                      <el-button type="primary"
                                 size="mini"
                                 :disabled="operator == '运算符' || weight == ''"
                                 @click="changeFilterCondition">确定</el-button>
                    </div>
                  </div>

                  <el-button type="text"
                             slot="reference"
                             @click="visible = !visible">自定义</el-button>
                </el-popover>
              </div>
            </div>
            <div class="charts_fill_class"
                 id="container">
              <v-chart v-if="!show_empty && activeName == item.value"
                       :ref="'charts-' + activeName"
                       :options="option"
                       :lazy-update="true"
                       v-loading="barraChartLoading"
                       element-loading-text="正在拼命绘制图形,请稍后..."
                       element-loading-spinner="el-icon-loading"
                       element-loading-background="rgba(239, 239, 239, 0.5)"
                       class="charts_one_class"
                       :style="`height: ${height > 400 ? height : 400}px`"
                       autoresize />
              <el-empty v-if="show_empty"
                        description="暂无数据"></el-empty>
            </div>
          </div>
          <div v-else>
            <div class="flex_between mb-16">
              <!-- <div class="title">{{ name }}</div> -->
              <div v-if="data_type == 'pool'">
                <el-select v-model="computed_active"
                           @change="handleClick">
                  <el-option v-for="item in computed_list"
                             :key="item.value"
                             :label="item.label"
                             :value="item.value"></el-option>
                </el-select>
              </div>
            </div>
            <div class="flex_start"
                 style="flex-wrap: wrap">
              <div v-for="item in chart_list"
                   :key="item.label"
                   class="charts_fill_class"
                   style="width: 25%">
                <v-chart :ref="'charts-' + item.label"
                         :options="item.option"
                         :lazy-update="true"
                         v-loading="barraChartLoading"
                         element-loading-text="正在拼命绘制图形,请稍后..."
                         element-loading-spinner="el-icon-loading"
                         element-loading-background="rgba(239, 239, 239, 0.5)"
                         class="charts_one_class"
                         autoresize />
              </div>
            </div>
            <el-pagination background
                           style="display: flex; justify-content: right; padding-top: 16px; padding-bottom: 24px"
                           @current-change="formatPagination"
                           :current-page.sync="currentPage"
                           :page-size="4"
                           layout="total, prev, pager, next, jumper"
                           :total="all_chart_list.length"></el-pagination>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
// 小提琴图
import { violinOption, boxplotOption } from "@/utils/chartStyle.js";
import {
  getIndustryList
} from "@/api/pages/SystemMixed.js";
import {
  getAllocate,
  postIndustryInfo,
} from "@/api/pages/tools/pool.js";
import { alphamsg } from "@/api/pages/SystemAlpha.js";
import { barChartOption } from "@/utils/chartStyle.js";

export default {
  computed: {
    name () {
      return this.tabs_list.find(item => {
        return item.value == this.activeName;
      })?.label;
    }
  },
  props: {
    data_type: {
      type: String,
      default: "fund"
    },
  },
  data () {
    return {
      ismanager: false,
      show: true,
      visible: false,
      show_empty: false,
      showOneItem: false,
      operator: "大于",
      activeName: "allocation",
      weight: "0",
      tabs_list: [
        {
          label: "大类资产配置",
          value: "allocation"
        },
        {
          label: "债券资产配置",
          value: "bond"
        },
        {
          label: "一级行业权重",
          value: "申万(2021)"
        },
        {
          label: "二级行业权重",
          value: "申万二级(2021)"
        },
        {
          label: "主题占比",
          value: "主题"
        }
      ],
      computed_active: "等权",
      computed_list: [
        {
          label: "简单平均",
          value: "等权"
        },
        {
          label: "加权平均",
          value: "规模加权"
        },
        {
          label: "最小值",
          value: "最小"
        },
        {
          label: "最大值",
          value: "最大"
        },
        {
          label: "中位数",
          value: "中位数"
        }
      ],
      chartData: [],
      specialData: [],
      option: {},
      info: {},
      active_class: "全部",
      list: [],
      loading: true,
      indsutry_list: [],
      height: 600,
      active_item: [],
      item_list: [],
      allocation_data: [],
      industry_data: [],
      chart_list: [],
      all_chart_list: [],
      allocation_list: [],
      bond_list: [],
      currentPage: 1
    };
  },
  methods: {
    getData (info) {
      this.info = info;
      if (this.info["code_list"].length < 2) {
        this.show = false;
        return;
      }
      this.show = true;
      this.getIndustryList();
      this.handleClick();
    },
    // 切换标签钩子函数
    destroyCharts (name, oldName) {
      this.option = {};
      this.show_empty = true;
      this.height = 600;
      this.loading = true;
      this.$refs["charts-" + oldName]?.[0].dispose();
      return true;
    },
    refresInfo (info) {
      this.info = info;
      this.chartData = [];
      if (this.activeName == "allocation" || this.activeName == "bond") {
        this.formatAllocation(this.allocation_data);
      } else {
        this.formatIndustry(this.industry_data);
      }
      if (this.info["code_list"].length > 6) {
        this.draw();
      } else {
        if (this.info["code_list"].length == 2) {
          this.drawBar();
        } else {
          this.drawRadar();
        }
      }
    },
    // 监听tab切换
    handleClick () {
      this.visible = false;
      this.weight = "0";
      this.showOneItem = false;
      this.currentPage = 1;
      if (this.activeName == "allocation" || this.activeName == "bond") {
        this.getAllocate();
      } else {
        this.item_list = this.indsutry_list
          .find(v => v.label == this.activeName)
          ?.value.map(item => {
            return { label: item.label, value: item.label };
          });
        this.active_item = this.item_list?.[0].value;
        this.getIndustryInfo();
      }
    },
    // 获取大类资产/券种数据
    async getAllocate () {
      this.loading = true;
      this.chartData = [];
      this.item_list = [];
      let data = await getAllocate({
        ids: [
          ...this.info["code_list"].map(item => {
            return {
              code: item.code,
              type: this.data_type == 'fund' ? this.ismanager ? 'manager' : 'fund' : this.data_type
            };
          })
        ],
        yearqtr: this.info.quarter,
        insert_time: this.info.date,
        flag: this.info.flag,
        method: this.computed_active,
        status: this.activeName == "bond" ? 2 : 1,
        ismanager: this.ismanager,
        type: this.info.type
      });
      if (data?.mtycode == 200) {
        this.show_empty = false;
        this.allocation_data = data?.data;
        this.formatAllocation(this.allocation_data);
        this.item_list = this.allocation_list;
        this.active_item = this.item_list?.[0].value;
      } else {
        this.show_empty = true;
      }

      if (this.info["code_list"].length > 6) {
        this.draw();
      } else {
        if (this.info["code_list"].length == 2) {
          this.drawBar();
        } else {
          this.drawRadar();
        }
      }

      this.loading = false;
    },
    formatAllocation (data) {
      data.map(obj => {
        for (const key in obj) {
          if (this.foramtEN(key)) {
            let index = this.chartData.findIndex(item => {
              return item.label == this.foramtEN(key);
            });
            let name =
              obj.poolId == this.info.code
                ? this.info.name
                : this.info["code_list"].find(v => v.code == obj.poolId)
                  ?.name;
            let color =
              obj.poolId == this.info.code
                ? "#4096ff"
                : this.info["code_list"].find(v => v.code == obj.poolId)
                  ?.color;
            let flag =
              obj.poolId == this.info.code
                ? 0
                : this.info["code_list"].find(v => v.code == obj.poolId)
                  ?.flag;
            if (index == -1) {
              this.chartData.push({
                label: this.foramtEN(key),
                value: [
                  {
                    date: obj["yearqtr"],
                    value: obj[key] * 1 > 100 ? 100 : obj[key],
                    code: obj.poolId,
                    name,
                    color,
                    flag
                  }
                ]
              });
            } else {
              this.chartData[index].value.push({
                code: obj.poolId,
                date: obj["yearqtr"],
                value: obj[key] * 1 > 100 ? 100 : obj[key],
                name,
                color,
                flag
              });
            }
          }
        }
      });
      this.allocation_list = this.chartData.map(item => {
        return {
          label: item.label,
          value: item.label
        };
      });
    },
    formatIndustry (data) {
      data.map(obj => {
        for (const key in obj) {
          if (
            !["flag", "poolId", "isprior", "yearqtr"].some(v => v == key)
          ) {
            let index = this.chartData.findIndex(item => {
              return item.label == key;
            });
            let name =
              obj.poolId == this.info.code
                ? this.info.name
                : this.info["code_list"].find(v => v.code == obj.poolId)
                  ?.name;
            let color =
              obj.poolId == this.info.code
                ? "#4096ff"
                : this.info["code_list"].find(v => v.code == obj.poolId)
                  ?.color;
            let flag =
              obj.poolId == this.info.code
                ? 0
                : this.info["code_list"].find(v => v.code == obj.poolId)
                  ?.flag;
            if (index == -1) {
              this.chartData.push({
                label: key,
                value: [
                  {
                    date: obj["yearqtr"],
                    value: this.activeName == "主题" ? obj[key] : obj[key],
                    code: obj.poolId,
                    name,
                    color,
                    flag
                  }
                ]
              });
            } else {
              this.chartData[index].value.push({
                date: obj["yearqtr"],
                value: this.activeName == "主题" ? obj[key] : obj[key],
                name,
                color,
                flag
              });
            }
          }
        }
      });
    },
    // 获取行业/主题列表
    async getIndustryList () {
      let res = await alphamsg();
      // 还原格式
      let data = res.data;
      if (res.mtycode == 200) {
        let level1 = [];
        let level2 = [];
        let level3 = [];
        let stockclass = data.stockclass.map(item => {
          return {
            label: item,
            value: item
          };
        });
        data.industry_tree.map(industry1 => {
          //  一级行业
          level1.push({
            label: industry1.industry_name,
            value: industry1.industry_code
          });
          industry1.children.map(industry2 => {
            //  二级行业
            level2.push({
              label: industry2.industry_name,
              value: industry2.industry_code
            });
            industry2.children.map(industry3 => {
              //  二级行业
              level3.push({
                label: industry3.industry_name,
                value: industry3.industry_code
              });
            });
          });
        });
        let alpha = [
          { "申万(2021)": level1 },
          { "申万二级(2021)": level2 },
          { "申万三级(2021)": level3 },
          {
            stockclass,
            恒生一级: data.hkindustry.map(item => {
              return { label: item.industry_name, value: item.industry_code };
            })
          },
          stockclass
        ];
        let jsonString = [{ alpha: alpha }];
        data = jsonString[0];
      }

      this.indsutry_list = [
        {
          label: "申万(2021)",
          value: data?.alpha?.[0]["申万(2021)"]
        },
        {
          label: "申万二级(2021)",
          value: data?.alpha?.[1]["申万二级(2021)"]
        },
        {
          label: "申万三级(2021)",
          value: data?.alpha?.[2]["申万三级(2021)"]
        },
        {
          label: "恒生一级",
          value: data?.alpha?.[3]["恒生一级"]
        },
        {
          label: "主题",
          value: data?.alpha?.[4].map(item => {
            return { label: item.label, value: item.label };
          })
        }
      ];
      // console.log(this.indsutry_list);
    },
    // 获取行业/主题数据
    async getIndustryInfo () {
      this.loading = true;
      this.chartData = [];
      let data = await postIndustryInfo({
        ids: [
          ...this.info["code_list"].map(item => {
            return {
              code: item.code,
              type: this.data_type == 'fund' ? this.ismanager ? 'manager' : 'fund' : this.data_type
              // type: this.data_type
            };
          })
        ],
        yearqtr: this.info.quarter,
        insert_time: this.info.date,
        flag: this.info.flag,
        method: this.computed_active,
        industry_standard: this.activeName,
        ismanager: this.ismanager,
        type: this.info.type
      });
      if (data?.mtycode == 200) {
        this.industry_data = data?.data;
        // this.height = this.industry_data?;
        this.formatIndustry(this.industry_data);
      }
      if (this.info["code_list"].length > 6) {
        this.draw();
      } else {
        if (this.info["code_list"].length == 2) {
          this.drawBar();
        } else {
          this.drawRadar();
        }
      }

      this.loading = false;
    },
    // 监听类型切换
    changheActive (val) {
      this.option = {};
      this.show_empty = true;
      this.height = 600;
      this.loading = true;
      this.active_class = val;
      if (this.$refs["charts-" + this.activeName]?.[0]) {
        this.$refs["charts-" + this.activeName]?.[0].dispose();
      }
      setTimeout(() => {
        this.draw();
      }, 100);
    },
    // 监听运算符切换
    changeOperator (val) {
      this.operator = val;
    },
    changeFilterCondition () {
      this.option = {};
      this.visible = false;
      this.show_empty = true;
      this.height = 600;
      this.loading = true;
      if (this.$refs["charts-" + this.activeName]?.[0]) {
        this.$refs["charts-" + this.activeName]?.[0].dispose();
      }
      setTimeout(() => {
        this.draw();
      }, 100);
    },
    // 画图
    draw () {
      let value = this.chartData.map(item => {
        return {
          ...item,
          value: item.value.map(obj => {
            return {
              ...obj,
              value: obj.value == "--" || isNaN(obj.value) ? 0 : obj.value
            };
          })
        };
      });
      // 全部显示逻辑
      if (!this.showOneItem) {
        let scatter_data = [];
        this.height = value.length * 30;
        if (this.active_class == "全部") {
          value = value.filter(item => {
            if (
              this.operator == "大于" &&
              !isNaN(this.weight) &&
              typeof (this.weight * 1) == "number"
            ) {
              return item.value.some(v => v.value > this.weight);
            } else if (
              this.operator == "小于" &&
              !isNaN(this.weight) &&
              typeof (this.weight * 1) == "number"
            ) {
              return item.value.some(v => v.value < this.weight);
            } else {
              return true;
            }
          });
          if (value.length) {
            this.show_empty = false;
            this.option = boxplotOption(value);
          } else {
            this.show_empty = true;
          }
        } else {
          scatter_data = value
            .filter(item => {
              return item.value.filter(v => v.flag != 0)?.length != 0;
            })
            .map(item => {
              return {
                ...item,
                value: item.value.filter(v => v.flag != 0)
              };
            });
          value = scatter_data.filter(item => {
            if (
              this.operator == "大于" &&
              !isNaN(this.weight) &&
              typeof (this.weight * 1) == "number"
            ) {
              return item.value.some(v => v.value > this.weight);
            } else if (
              this.operator == "小于" &&
              !isNaN(this.weight) &&
              typeof (this.weight * 1) == "number"
            ) {
              return item.value.some(v => v.value < this.weight);
            } else {
              return true;
            }
          });
          if (value.length) {
            this.show_empty = false;
            let option = {
              ...violinOption(value, scatter_data),
              animation: true,
              animationDuration: 1000, // 动画时长
              animationEasing: "cubicOut" // 缓动函数
            };

            this.option = option;
          } else {
            this.show_empty = true;
          }
        }
      }
      // 单选逻辑
      else {
        let new_value = [];
        value
          .filter(v => v.label == this.active_item)
          .map(item => {
            item.value.map(obj => {
              let index = new_value.findIndex(v => v.label == obj.date);
              if (index == -1) {
                new_value.push({
                  label: obj.date,
                  value: [
                    {
                      ...obj,
                      date: item.label
                    }
                  ]
                });
              } else {
                new_value[index].value.push({
                  ...obj,
                  date: item.label
                });
              }
            });
          });
        value = new_value;
        let scatter_data = [];
        this.height = value.length * 30;
        if (this.active_class == "全部") {
          if (value.length) {
            this.show_empty = false;
            this.option = boxplotOption(value);
          } else {
            this.show_empty = true;
          }
        } else {
          scatter_data = value
            .filter(item => {
              return item.value.filter(v => v.flag != 0)?.length != 0;
            })
            .map(item => {
              return {
                ...item,
                value: item.value.filter(v => v.flag != 0)
              };
            });
          if (value.length) {
            this.show_empty = false;
            let option = {
              ...violinOption(value, scatter_data),
              animation: true,
              animationDuration: 1000, // 动画时长
              animationEasing: "cubicOut" // 缓动函数
            };
            this.option = option;
          } else {
            this.show_empty = true;
          }
        }
      }
      // console.log(this.option);
      this.loading = false;
    },
    // 雷达图
    drawRadar2 () {
      let indicator = this.chartData.map(v => {
        return { name: v.label, max: 100 };
      });
      let yearter_list = this.info.quarter;
      let legend = this.info.code_list.map(v => v.name);
      let list = [];
      this.chartData.map(item => {
        item.value.map(obj => {
          let index = list.findIndex(v => v.label == obj.date);
          if (index == -1) {
            list.push({
              label: obj.date,
              value: [{ ...obj, class: item.label }]
            });
          } else {
            list[index].value.push({ ...obj, class: item.label });
          }
        });
      });
      let series = [];
      list.map((item, j) => {
        let data = [];
        legend.map(name => {
          let i = data.findIndex(v => v.name == name);
          if (i == -1) {
            let res = item.value?.find(v => v.name == name);
            data.push({
              name,
              itemStyle: {
                color:
                  res?.flag == 2
                    ? res?.color
                    : res?.flag == 1
                      ? "#FFD600"
                      : "#fafafa"
              },
              value: item.value.filter(v => v.name == name).map(v => v.value)
            });
          }
        });
        let index = series.findIndex(v => v.name == item.label);
        if (index == -1) {
          series.push({
            name: item.label,
            radarIndex: j,
            type: "radar",
            tootip: { trigger: "item" },
            data
          });
        }
      });
      // 假设有4个雷达图
      const numCharts = series.length;

      // 获取页面容器的宽度
      const containerWidth = document.getElementById("container").clientWidth;
      const containerHeight = document.getElementById("container").clientHeight;

      // 计算每个雷达图的宽度
      const chartWidth = containerWidth / numCharts;

      // 存储雷达图的中心点坐标
      const chartCenters = [];

      // 计算每个雷达图的中心点坐标
      for (let i = 0; i < numCharts; i++) {
        const x = (i + 0.5) * chartWidth;
        const y = "50%"; // 设置垂直居中

        chartCenters.push([x, y]);
      }

      let option = {
        legend: {
          data: legend,
          pageIcons: {
            horizontal: [
              "path://M11.7487 6.92214L6.30673 0.634973C6.15096 0.455009 5.85102 0.455009 5.69359 0.634973L0.251579 6.92214C0.049409 7.15658 0.231693 7.5 0.558148 7.5L11.4422 7.5C11.7686 7.5 11.9509 7.15658 11.7487 6.92214Z",
              "path://M0.251255 1.07786L5.69327 7.36503C5.84904 7.54499 6.14898 7.54499 6.30641 7.36503L11.7484 1.07786C11.9506 0.843416 11.7683 0.499999 11.4419 0.5L0.557824 0.5C0.231369 0.5 0.0490849 0.843417 0.251255 1.07786Z"
            ]
          },
          icon: "path://M63.6 489.6h896.7v44.8H63.6z"
        },
        tooltip: {
          trigger: "axis"
        },
        radar: series.map((item, index) => {
          return {
            center: chartCenters[index],
            radius:
              (chartWidth / 4) * 0.75 > containerHeight * 0.75
                ? containerHeight * 0.75
                : (chartWidth / 4) * 0.75,
            indicator
          };
        }),
        series: series
      };
      this.option = option;
    },
    // 雷达图
    drawRadar () {
      // let maxValues = 100
      // try {
      //   maxValues = this.chartData.map(item => {
      //     const values = item.value.map(obj => obj.value);
      //     const maxValue = Math.max(...values.filter(val => typeof val === 'number')); // 排除字符串类型的值
      //     return { label: item.label, maxValue };
      //   });
      // }
      // catch { }
      // console.log(maxValues);
      let chart_list = [];
      let name_list = this.info["code_list"].map(v => v.name);
      let indicator = name_list.map(v => {
        return { name: v, max: 100 };
      });
      let legend = this.info.quarter;
      this.chartData.map(item => {
        let index = chart_list.findIndex(v => v.label == item.label);
        if (index == -1) {
          let data = [];
          item.value.map(obj => {
            let i = data.findIndex(v => v.name == obj.date);
            if (i == -1) {
              data.push({
                name: obj.date,
                value: [{ name: obj.name, value: obj.value }]
              });
            } else {
              data[i].value.push({ name: obj.name, value: obj.value });
            }
          });
          chart_list.push({
            label: item.label,
            value: data.map(obj => {
              return {
                ...obj,
                value: obj.value
                  .sort((a, b) => {
                    return (
                      name_list.indexOf(a.name) - name_list.indexOf(b.name)
                    );
                  })
                  .map(v =>
                    !isNaN(v.value) && typeof (v.value * 1) == "number"
                      ? (v.value * 1).toFixed(2)
                      : 0
                  )
              };
            })
          });
        }
      });
      chart_list = chart_list.map(item => {
        let option = {
          title: { text: item.label },
          tooltip: {
            trigger: "axis"
          },
          legend: [
            {
              data: legend,
              pageIcons: {
                horizontal: [
                  "path://M11.7487 6.92214L6.30673 0.634973C6.15096 0.455009 5.85102 0.455009 5.69359 0.634973L0.251579 6.92214C0.049409 7.15658 0.231693 7.5 0.558148 7.5L11.4422 7.5C11.7686 7.5 11.9509 7.15658 11.7487 6.92214Z",
                  "path://M0.251255 1.07786L5.69327 7.36503C5.84904 7.54499 6.14898 7.54499 6.30641 7.36503L11.7484 1.07786C11.9506 0.843416 11.7683 0.499999 11.4419 0.5L0.557824 0.5C0.231369 0.5 0.0490849 0.843417 0.251255 1.07786Z"
                ]
              },
              icon: "path://M63.6 489.6h896.7v44.8H63.6z"
            }
          ],
          radar: { indicator, radius: "55%" },
          series: [
            {
              type: "radar",
              tooltip: {
                trigger: "item"
              },
              data: item.value
            }
          ]
        };
        return {
          label: item.label,
          option
        };
      });
      this.all_chart_list = chart_list.filter(v =>
        v.option.series?.[0].data.every(n => n.value.some(m => m * 1 > 0))
      );
      this.formatPagination();
    },
    // 柱状图
    drawBar () {
      let name_list = this.info["code_list"].map(v => v.name);
      let chart_list = this.chartData.map(item => {
        let series = [];
        item.value.map(obj => {
          let index = series.findIndex(v => v.name == obj.date);
          let value =
            !isNaN(obj.value) && typeof (obj.value * 1) == "number"
              ? (obj.value * 1).toFixed(2)
              : "--";

          if (index == -1) {
            series.push({
              name: obj.date,
              type: "bar",
              data: [[obj.name, value]]
            });
          } else {
            series[index].data.push([obj.name, value]);
          }
        });
        let option = barChartOption({
          title: { text: item.label },
          tooltip: {
            trigger: "axis"
          },
          toolbox: false,
          legend: { show: true, data: this.info.quarter },
          xAxis: [{ data: name_list, isAlign: "center", rotate: -15 }],
          yAxis: [{ type: "value" }],
          series
        });
        return {
          label: item.label,
          option
        };
      });

      this.all_chart_list = chart_list.filter(v =>
        v.option.series.every(n => n.data.some(m => m[1] * 1 > 0))
      );
      this.formatPagination();
    },
    // 格式化分页
    formatPagination () {
      this.chart_list = this.all_chart_list.slice(
        (this.currentPage - 1) * 4,
        4 * this.currentPage
      );
    },
    // 格式化英文字段
    foramtEN (val) {
      switch (val) {
        case "absWeight":
          return "ABS";
        case "bondWeight":
          return "债券";
        case "cashWeight":
          return "现金";
        case "equityWeight":
          return "股票";
        case "fundWeight":
          return "基金";
        case "optionWeight":
          return "权证";
        case "otherWeight":
          return "其他";
        case "repoWeight":
          return "金融资产";
        case "ABS":
          return "ABS";
        case "中期票据":
          return "中期票据";
        case "买入返售金融资产":
          return "买入返售金融资产";
        case "企业债":
          return "企业债";
        case "其他债券":
          return "其他债券";
        case "其他资产":
          return "其他资产";
        case "同业存单":
          return "同业存单";
        case "短期融资券":
          return "短期融资券";
        case "A股股票":
          return "A股股票";
        case "可转债":
          return "可转债";
        case "国债":
          return "国债";
        case "政策性金融债":
          return "政策性金融债";
        case "现金存款":
          return "现金存款";
        case "金融债":
          return "非政策性金融债";
      }
    }
  },
  mounted () {
    this.ismanager = String(this.$route.query.ismanager) == 'true' ? true : false
  },
};
</script>

<style lang="scss" scoped>
.assetAllocationAnalysis {
	::v-deep .el-radio-button__orig-radio:checked + .el-radio-button__inner {
		color: #4096ff;
		background-color: transparent;
	}
}
</style>
