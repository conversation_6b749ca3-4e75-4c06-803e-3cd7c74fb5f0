<template>
	<div class="condition-main">
		<div>
			<el-input v-model="content" placeholder="请输入搜索内容" @input="changeContent"></el-input>
		</div>
		<div class="condition-list" style="height: 884px; overflow: auto">
			<el-tree :data="data" default-expand-all :props="defaultProps" show-checkbox @check-change="checkChange"></el-tree>
		</div>
	</div>
</template>

<script>
export default {
	data() {
		return {
			data: [],
			content: '',
			defaultProps: {
				children: 'children',
				label: 'fieldName'
			},
			cacheData: []
		};
	},
	methods: {
		getData(data) {
			this.cacheData = [...data];
			this.data = [{ fieldName: '全选', children: data }];
		},
		// 监听输入框内容
		changeContent() {
			if (this.content == '') {
				this.data = [...this.cacheData];
			} else {
				this.data = this.cacheData.filter((v) => v.fieldName.includes(this.content));
			}
		},
		handleNodeClick(item) {
			console.log(item);
			if (!item.children) {
				this.$emit('resolveFather', item);
			}
		},
		checkChange(item, boolean) {
			if (!item.children && boolean) {
				this.$emit('resolveFather', item);
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.condition-main {
	.condition-list {
		overflow-x: hidden;
		overflow-y: auto;
	}

	//   width: 240px;
	//   white-space: nowrap;
	//   text-overflow: ellipsis;
}
.condition-list::-webkit-scrollbar {
	width: 2px;
}
</style>
