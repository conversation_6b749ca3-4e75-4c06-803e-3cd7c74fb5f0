<template>
	<div>
		<div class="flex_card">
			<div v-for="item in templateList" :key="item.value" v-show="item.isshow" :class="item.type">
				<component :is="item.is" :ref="item.value" @resolveFather="item.methods" v-loading="loading"></component>
			</div>
		</div>
	</div>
</template>

<script>
// 持仓股票分析
import positionStockAnalysis from '@/components/components/components/positionStockAnalysis/index.vue';
// 行业配置表现
import industryAllocationPerformance from '@/components/components/industryAllocationPerformance/index.vue';

// 行业配置表现
import { getIndustryInfoCompany, getHoldStockMsg } from '@/api/pages/SystemOther.js';

export default {
	components: {
		positionStockAnalysis,
		industryAllocationPerformance
	},
	data() {
		return {
			templateList: [],
			info: {},
			loading: true,
			requestOver: [],
			requestAll: [],
			industryInfo: null,
			holdStockMsg: null
		};
	},
	methods: {
		// 获取数据
		getData(data) {
			this.info = { ...data, type: 'equityhk' };
			this.loading = true;
			this.requestOver = [];
			this.watch();
			this.formatTemplatList();
		},
		// 添加watch函数式监听(因为watch侦听器在页面切换时失效)
		watch() {
			let unwatch = this.$watch('requestOver', (val, old) => {
				this.loading = false;
				this.$compontentsWatch(val, this);
			});
		},
		// 格式化模板列表
		formatTemplatList() {
			this.requestAll = [];
			let requestList = [];
			this.templateList.map((item) => {
				if (item.methods && typeof item.methods == 'string') {
					item.methods = this?.[item.methods];
				}
				if (
					item.typelist.some((obj) => {
						return this.info.type.indexOf(obj) != -1 || obj == '*';
					})
				) {
					this.requestAll.push(item);
					if (requestList.indexOf(item.getRequestData) == -1) {
						if (item.getRequestData && item.getRequestData !== 'None') {
							requestList.push(item.getRequestData);
							this?.[item.getRequestData]();
						}
					}
				}
			});
		},
		// 接收/返回组件列表
		getTemplateList(list) {
			if (list) {
				// 是光大
				if (this.isGDBank()) {
					this.templateList = list.filter((item) => {
						return item.isshow;
					});
				} else {
					// 不是光大
					this.templateList = list.filter((item) => {
						return item.is !== 'GDBankDetailequity' && item.is !== 'GDBankDetailbond' && item.isshow;
					});
				}
				this.$forceUpdate();
			} else {
				return this.templateList;
			}
		},
		// 判断是否是光大
		isGDBank() {
			if (window.localStorage.getItem('mty_modulesName') == 'GDBank') {
				this.showGD = true;
				return true;
			} else {
				this.showGD = false;
				return false;
			}
		},
		// 获取股票持仓分析
		async getHoldStockMsg() {
			this.requestOver.push('getHoldStockMsg');
		},
		// 获取股票持仓分析类型数据
		getHoldStockMsgData() {
			this.$refs['positionStockAnalysis']?.[0].getDateList(this.info);
		},
		// 获取行业配置表现数据
		async getIndustryInfo() {
			if (this.getCacheData('industryInfo')) {
				this.industryInfo = this.getCacheData('industryInfo');
			} else {
				let data = await getIndustryInfoCompany({
					// flag: this.info.flag,
					code: this.info.code,
					type: this.info.type,
					industry_section: this.info.type == 'equityhk' ? '恒生一级' : '申万(2021)'
				});
				this.industryInfo = data;
				this.setCacheData('industryInfo', data);
				// }
			}
			this.requestOver.push('getIndustryInfo');
		},
		// 行业配置表现
		getIndustryInfoData() {
			let data = this.industryInfo;
			if (data?.mtycode == 200) {
				this.$refs['industryAllocationPerformance']?.[0].getData(data?.data, this.info);
			} else {
				this.$refs['industryAllocationPerformance']?.[0].hideLoading();
			}
		}
	}
};
</script>

<style></style>
