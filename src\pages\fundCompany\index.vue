<template>
	<div class="analysis_main_company">
		<!-- 基金公司 -->
		<div style="display: flex">
			<div style="flex: 1; min-width: 564px; max-width: 564px; margin-right: 24px">
				<fund-company-profile ref="fundCompanyProfile"></fund-company-profile>
			</div>
			<div style="flex: 2; width: 100%">
				<overview-chart ref="OverviewChart"></overview-chart>
			</div>
		</div>
		<div style="display: flex; justify-content: space-between; align-items: center; margin-right: 12px">
			<el-menu :default-active="activeIndex" class="el-menu-demo type_menu" mode="horizontal" @select="handleSelect">
				<el-menu-item v-for="item in activeComponentsList" :key="item.key" :index="item.key">{{ item.label }}</el-menu-item>
			</el-menu>
			<div style="display: flex">
				<!-- <el-button type="primary" @click="openTemplateList">自定义模板</el-button>
				<el-button type="primary" id="printWord" @click="print">打印word</el-button> -->
			</div>
		</div>
		<div class="line"></div>
		<div class="main">
			<div v-for="item in activeComponentsList" :key="item.key" v-show="activeIndex == item.key || printActive == true">
				<component :is="item.key" :ref="item.key" @overRequest="overRequest"></component>
			</div>
		</div>
	</div>
</template>

<script>
import onePagePass from './components/onePagePass';
import equityhk from './components/equityhk';
import equity from './components/equity';
import bond from './components/bond';
import purebond from './components/purebond';
import fundCompanyProfile from './components/fundCompanyProfile.vue';
import OverviewChart from './components/overviewChart.vue';
import fundSpectrum from './components/fundSpectrum.vue';
import hotRecommend from './components/hotRecommend.vue';
import assetAnalysis from './components/assetAnalysis.vue';
import itsFund from './components/itsFund.vue';
import itsFundManager from './components/itsFundManager.vue';
import latestResearchReport from './components/latestResearchReport.vue';

import { companyComponentsList } from '@/utils/componentsClass.js';
// 获取基金公司存在基金类型列表
import { getHoldType } from '@/api/pages/SystemOther.js';

export default {
	name: 'fundCompany',
	components: {
		onePagePass,
		equity,
		equityhk,
		bond,
		purebond,
		fundCompanyProfile,
		OverviewChart,
		fundSpectrum,
		hotRecommend,
		assetAnalysis,
		itsFund,
		itsFundManager,
		latestResearchReport
	},
	data() {
		return {
			id: '',
			name: '',
			activeIndex: 'assetAnalysis',
			activeComponentsList: companyComponentsList,
			info: {}
		};
	},
	mounted() {
		this.init();
	},
	// watch: {
	// 	//监听相同路由下参数变化的时候，从而解决当跳转到同页面不刷新问题
	// 	$route(to, from) {
	// 		this.init();
	// 	}
	// },
	methods: {
		handleSelect(key, keyPath) {
			if (this.activeIndex == key) {
				return;
			}
			this.activeIndex = key;
			this.getData();
		},
		init() {
			this.id = this.$route.query.id;
			this.name = this.$route.query.name;
			this.info = {
				code: this.id,
				name: this.name,
				flag: 3,
				type: []
			};
			this.activeIndex = this.activeComponentsList?.[0]?.key;
			this.getHoldType();
			// this.$refs[this.activeIndex]?.[0].getTemplateList(this.filterTemplateList(this.activeIndex));
			// this.$refs[this.activeIndex]?.[0].getData(this.info);
		},
		filterTemplateList(key) {
			let templateList = this.activeComponentsList.filter((item) => {
				return item.key == key;
			})?.[0]?.templateList;
			return templateList.filter((item) => {
				return item.typelist.some((val) => {
					return this.info.type.indexOf(val) !== -1 || val.indexOf('*') !== -1;
				});
			});
		},
		getData() {
			this.$refs[this.activeIndex]?.[0].getTemplateList(this.filterTemplateList(this.activeIndex));
			this.$refs[this.activeIndex]?.[0].getData(this.info);
		},
		// 获取基金公司下所有基金类型
		async getHoldType() {
			let data = await getHoldType({ code: this.info.code });
			if (data?.mtycode == 200) {
				this.info.type = data?.data
					?.map((item) => {
						return item.type;
					})
					.filter((item) => {
						return (
							item == 'equity' ||
							item == 'equityhk' ||
							item == 'equityindex' ||
							item == 'bond' ||
							item == 'cbond' ||
							item == 'purebond' ||
							item == 'bill' ||
							item == 'fof' ||
							item == 'money'
						);
					})
					.map((item) => {
						let sort = null;
						switch (item) {
							case 'equity':
								sort = 0;
								break;
							case 'equityhk':
								sort = 1;
								break;
							case 'equityindex':
								sort = 2;
								break;
							case 'bond':
								sort = 3;
								break;
							case 'cbond':
								sort = 4;
								break;
							case 'purebond':
								sort = 5;
								break;
							case 'bill':
								sort = 6;
								break;
							case 'fof':
								sort = 7;
								break;
							case 'money':
								sort = 8;
								break;
						}
						return { type: item, sort };
					})
					.sort((a, b) => {
						return a.sort - b.sort;
					})
					.map((item) => {
						return item.type;
					});
				this.$refs['fundCompanyProfile']?.getData(this.info);
				this.$refs['OverviewChart']?.getTypeList(this.info);
				this.getData();
			}
		}
	}
};
</script>

<style lang="scss">
.analysis_main_company {
	.flex_between {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	.flex_start {
		display: flex;
		justify-content: start;
		align-items: center;
	}
	.flex_center {
		display: flex;
		justify-content: center;
		align-items: center;
	}
	$margin-sizes: (
		'4': 4px,
		'8': 8px,
		'12': 12px,
		'16': 16px,
		'24': 24px
	);
	$padding-sizes: (
		'4': 4px,
		'8': 8px,
		'12': 12px,
		'16': 16px,
		'24': 24px
	);

	// 生成动态边距类
	@each $name, $size in $padding-sizes {
		.p-#{$name} {
			padding: $size !important;
		}
		.pt-#{$name} {
			padding-top: $size !important;
		}
		.pr-#{$name} {
			padding-right: $size !important;
		}
		.pb-#{$name} {
			padding-bottom: $size !important;
		}
		.pl-#{$name} {
			padding-left: $size !important;
		}
		.px-#{$name} {
			padding-right: $size !important;
			padding-left: $size !important;
		}
		.py-#{$name} {
			padding-top: $size !important;
			padding-bottom: $size !important;
		}
	}
	@each $name, $size in $margin-sizes {
		.m-#{$name} {
			margin: $size !important;
		}
		.mt-#{$name} {
			margin-top: $size !important;
		}
		.mr-#{$name} {
			margin-right: $size !important;
		}
		.mb-#{$name} {
			margin-bottom: $size !important;
		}
		.ml-#{$name} {
			margin-left: $size !important;
		}
		.mx-#{$name} {
			margin-right: $size !important;
			margin-left: $size !important;
		}
		.my-#{$name} {
			margin-top: $size !important;
			margin-bottom: $size !important;
		}
	}
	.flex_card {
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;
		flex-wrap: wrap;
	}
	.flex_card .card_header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		position: relative;
		padding: 16px 0;
	}
	.flex_card > .small_template {
		flex: 1;
		min-width: 564px;
		height: auto;
		padding: 16px 24px 0 24px;
		margin: 12px;
		background: #ffffff;
		box-shadow: 0px 5px 12px 4px rgba(0, 0, 18, 0.02);
		border-radius: 4px;
	}
	.flex_card > .big_template {
		height: auto;
		width: 100%;
		flex-basis: 100%;
	}
	.flex_card > .small_template .chart_title {
		position: absolute;
		left: 24px;
		top: 16px;
		font-family: 'PingFang';
		font-style: normal;
		font-weight: 500;
		font-size: 16px;
		line-height: 24px;
		color: rgba(0, 0, 0, 0.85);
	}
	.flex_card > .small_template .title {
		font-family: 'PingFang SC';
	}
	.flex_card > div .title {
		font-family: 'PingFang';
		font-style: normal;
		font-weight: 500;
		font-size: 16px;
		line-height: 24px;
		color: rgba(0, 0, 0, 0.85);
	}
	.manager_info_title {
		font-family: 'PingFang';
		font-style: normal;
		font-weight: 500;
		font-size: 18px;
		line-height: 26px;
		color: rgba(0, 0, 0, 0.85);
		margin-left: 12px;
	}
	.chart_one {
		min-width: 1152px;
		/* height: 390px; */
		padding: 16px 24px 24px 24px;
		margin: 12px;
		background: #ffffff;
		box-shadow: 0px 5px 12px 4px rgba(0, 0, 18, 0.02);
		border-radius: 4px;
	}
	.combination .chart_one {
		min-width: 1152px;
		/* height: 390px; */
		padding: 0 24px 24px 24px;
		margin: 12px;
		background: #ffffff;
		box-shadow: 0px 5px 12px 4px rgba(0, 0, 18, 0.02);
		border-radius: 4px;
	}
	.emptyClass .title {
		font-family: 'PingFang';
		font-style: normal;
		font-weight: 500;
		font-size: 16px;
		line-height: 24px;
		color: rgba(0, 0, 0, 0.85);
		background: #ffffff;
	}
	.chart_one .title {
		font-family: 'PingFang';
		font-style: normal;
		font-weight: 500;
		font-size: 16px;
		line-height: 24px;
		color: rgba(0, 0, 0, 0.85);
		background: #ffffff;
	}

	div .charts_one_class {
		// height: 282px;
		position: relative;
		page-break-inside: avoid;
	}
	.charts_fill_class .charts_one_class {
		width: 100%;
	}
	.charts_fill_class .charts_two_class {
		width: 100%;
	}
	.charts_fill_class .charts_analysis_class {
		width: 100%;
	}
	// charts外层div,使用flex将图居中显示
	div .charts_center_class {
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	div .charts_two_class {
		width: 564px;
		height: 210px;
		position: relative;
		page-break-inside: avoid;
	}
	div .charts_analysis_class {
		// width: 100%;
		height: 280px;
		position: relative;
		page-break-inside: avoid;
	}

	.analysis_main {
		background: #f7f9fa;
		padding: 12px;
	}
	.analysis_main_company {
		background: #f7f9fa;
		padding: 24px;
	}
	.backbut {
		position: absolute;
		right: 5px;
		bottom: 10px;
		top: 10px;
		margin: auto;
	}
	.backbuts {
		position: absolute;
		right: 110px;
		bottom: 10px;
		top: 10px;
		margin: auto;
	}
	div .type_menu {
		position: -webkit-sticky;
		position: sticky !important;
		top: 0px;
		z-index: 100;
		background: #f7f9fa !important;
		margin: 12px;
	}
	div .drag_template_list_type_menu {
		display: flex;
		overflow: scroll;
	}
	div .drag_template_list_type_menu::-webkit-scrollbar {
		height: 0;
	}
	.main {
		width: 100%;
		height: 100%;
	}
	.fixed-height-short {
		height: 250px;
	}
	.flex_basic_info {
		flex: 1;
		min-width: 564px;
		max-width: 564px;
		background: #ffffff;
		margin: 12px;
	}
	.flex_return {
		flex: 2;
		width: 100%;
		min-width: 564px;
		padding: 16px 40px 24px 24px;
		background: #ffffff;
		margin: 12px;
	}
}
</style>
