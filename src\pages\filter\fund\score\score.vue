<!--  -->
<template>
  <div v-loading="loading"
       style="background: white"
       class="">
    <!-- body -->
    <div style="display: flex; background: white; margin-top: 1px">
      <div style="width: 200px; box-shadow: 1px 0px 0px #e9e9e9; border-radius: 0px 0px 0px 4px">
        <div style="height: 48px">
          <div style="height: 40px; margin-top: 8px; background: #4096ff; display: flex; justify-content: space-between; align-items: center">
            <div style="margin-left: 16px; font-weight: 500; font-size: 14px; color: white">模板</div>
            <div style="margin-right: 16px">
              <!-- <svg width="14"
                   height="14"
                   viewBox="0 0 14 14"
                   fill="none"
                   xmlns="http://www.w3.org/2000/svg">
                <path d="M3.02638 10.7485C3.05763 10.7485 3.08888 10.7454 3.12013 10.7407L5.74825 10.2798C5.7795 10.2735 5.80919 10.2595 5.83107 10.236L12.4545 3.6126C12.469 3.59814 12.4805 3.58097 12.4883 3.56207C12.4962 3.54317 12.5002 3.52291 12.5002 3.50244C12.5002 3.48198 12.4962 3.46171 12.4883 3.44281C12.4805 3.42391 12.469 3.40674 12.4545 3.39229L9.85763 0.793848C9.82794 0.76416 9.78888 0.748535 9.74669 0.748535C9.7045 0.748535 9.66544 0.76416 9.63576 0.793848L3.01232 7.41729C2.98888 7.44072 2.97482 7.46885 2.96857 7.5001L2.50763 10.1282C2.49243 10.2119 2.49786 10.2981 2.52345 10.3792C2.54905 10.4603 2.59403 10.534 2.6545 10.5938C2.75763 10.6938 2.88732 10.7485 3.02638 10.7485ZM4.0795 8.02354L9.74669 2.35791L10.892 3.50322L5.22482 9.16885L3.83575 9.41416L4.0795 8.02354ZM12.7498 12.061H1.24982C0.973254 12.061 0.749817 12.2845 0.749817 12.561V13.1235C0.749817 13.1923 0.806067 13.2485 0.874817 13.2485H13.1248C13.1936 13.2485 13.2498 13.1923 13.2498 13.1235V12.561C13.2498 12.2845 13.0264 12.061 12.7498 12.061Z"
                      fill="white" />
              </svg> -->
            </div>
          </div>
        </div>
        <el-tree ref="treeScore"
                 :data="treeData"
                 node-key="value"
                 show-checkbox
                 @check-change="handleCheckChange"> </el-tree>
        <div @click="addmodelsnew"
             style="
						font-style: normal;

						font-weight: 400;
						cursor: pointer;
						font-size: 14px;
						line-height: 22px;
						margin-top: 12px;
						margin-left: 40px;
						color: #4096ff;
					">
          +新增模板
        </div>
        <!-- <div v-for="(item, index) in gridData.concat(listar)"
             :key="index">
          <div :style="nowModel == item.model_name ? 'background: rgba(0, 0, 0, 0.04)' : ''"
               @click="chooseModel(item)">
            <div style="display: flex; align-items: center; justify-content: space-between; cursor: pointer"
                 class="boxModel">
              <div style="padding-left: 16px"
                   class="normalFontQ">
                <span>{{ item.model_name }}</span><span v-if="item.types && item.types == 'self'"></span><span v-else>(慧捕基模板)</span>
              </div>
              <div @click="editModel(item)"
                   style="padding-right: 16px"
                   v-show="nowModel == item.model_name">
                <svg width="14"
                     height="14"
                     viewBox="0 0 14 14"
                     fill="none"
                     xmlns="http://www.w3.org/2000/svg">
                  <path d="M3.02638 10.7485C3.05763 10.7485 3.08888 10.7454 3.12013 10.7407L5.74825 10.2798C5.7795 10.2735 5.80919 10.2595 5.83107 10.236L12.4545 3.6126C12.469 3.59814 12.4805 3.58097 12.4883 3.56207C12.4962 3.54317 12.5002 3.52291 12.5002 3.50244C12.5002 3.48198 12.4962 3.46171 12.4883 3.44281C12.4805 3.42391 12.469 3.40674 12.4545 3.39229L9.85763 0.793848C9.82794 0.76416 9.78888 0.748535 9.74669 0.748535C9.7045 0.748535 9.66544 0.76416 9.63576 0.793848L3.01232 7.41729C2.98888 7.44072 2.97482 7.46885 2.96857 7.5001L2.50763 10.1282C2.49243 10.2119 2.49786 10.2981 2.52345 10.3792C2.54905 10.4603 2.59403 10.534 2.6545 10.5938C2.75763 10.6938 2.88732 10.7485 3.02638 10.7485ZM4.0795 8.02354L9.74669 2.35791L10.892 3.50322L5.22482 9.16885L3.83575 9.41416L4.0795 8.02354ZM12.7498 12.061H1.24982C0.973254 12.061 0.749817 12.2845 0.749817 12.561V13.1235C0.749817 13.1923 0.806067 13.2485 0.874817 13.2485H13.1248C13.1936 13.2485 13.2498 13.1923 13.2498 13.1235V12.561C13.2498 12.2845 13.0264 12.061 12.7498 12.061Z"
                        fill="black"
                        fill-opacity="0.65" />
                </svg>
              </div>
            </div>
          </div>
        </div> -->
      </div>
      <div style="flex: 5">
        <div style="height: 55px; display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid #e9e9e9">
          <div style="margin-left: 24px; font-weight: 500; font-size: 16px; line-height: 24px; color: rgba(0, 0, 0, 0.85)">
            {{ tags.length > 0 ? indexForScore : '新建模板' }}
          </div>
          <div style="margin-right: 16px">
            <el-button type="text"
                       @click="savemodelClick()"
                       style="color: #4096ff">保存</el-button>
            <el-button type="text"
                       :disabled="JSON.stringify(nowchosse) == '{}'"
                       @click="delModel"
                       style="color: #4096ff">删除</el-button>
          </div>
        </div>
        <!-- 打分范围 -->
        <range-choose :filter_type="filter_type"
                      @changetype="changetype"
                      ref="rangeChoose"></range-choose>
        <!-- 已选条件-->
        <div style="border-left: 1px solid #e9e9e9; margin-top: 1px; border-bottom: 1px solid #e9e9e9; background: white">
          <div style="padding-top: 24px; padding-bottom: 24px; padding-left: 24px; display: flex; flex-wrap: wrap; align-items: center">
            <div class="normalFont"
                 style="wdith: 70px">已选择条件：</div>
            <div style="display: flex; align-items: center; width: calc(100% - 70px)">
              <div class="newFilterTag"
                   v-for="(item, index) in listSelect"
                   :key="index"
                   style="display: flex; align-items: center; flex-wrap: wrap">
                <div v-for="(items, index2) in item.data"
                     :key="index2">
                  <div>
                    <el-tag @close="closeTags(index, index2)"
                            :class="isDianDao ? 'noDianDaoCalss' : 'isDianDaoCalss'"
                            style="
												margin-top: 8px;
												margin-right: 16px;
												max-height: 32px;
												color: rgba(0, 0, 0, 0.85);
												background: rgba(0, 0, 0, 0.02);
												border: 1px solid #d9d9d9;
												border-radius: 4px;
												font-family: 'PingFang';
												font-style: normal;
												font-weight: 400;
												font-size: 14px;
												line-height: 22px;
												color: rgba(0, 0, 0, 0.85);
											"
                            closable
                            type="">
                      {{ items.labelName }}:
                      <span v-if="item.labelIndex == 'g' || item.labelIndex == 'h'">{{
												JSON.stringify(items.dataResult) == '[]' ||
												JSON.stringify(items.dataResult) == '{}' ||
												JSON.stringify(items.dataResult) == '[{}]'
													? ''
													: items.dataResult[0].date.indexOf('1w') >= 0
													? '近一周'
													: items.dataResult[0].date.indexOf('2w') >= 0
													? '近两周'
													: items.dataResult[0].date.indexOf('1m') >= 0
													? '近一月'
													: items.dataResult[0].date.indexOf('2m') >= 0
													? '近两月'
													: items.dataResult[0].date.indexOf('1q') >= 0
													? '近一季'
													: items.dataResult[0].date.indexOf('2q') >= 0
													? '近半年'
													: items.dataResult[0].date.indexOf('1y') >= 0
													? '近一年'
													: items.dataResult[0].date.indexOf('2y') >= 0
													? '近两年'
													: items.dataResult[0].date.indexOf('3y') >= 0
													? '近三年'
													: items.dataResult[0].date.indexOf('5y') >= 0
													? '近五年'
													: '自' + items.dataResult[0].date[1]
											}}</span>
                      <span>
                        {{ JSON.stringify(items.dataResult) == '[]' ? '' : items.dataResult[0].value + '%' }}
                      </span>
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 过滤指标 -->
        <div>
          <dialogFilter @t1t2="t1t2"
                        ref="dialogScore"
                        :radioType2="radioType"
                        @changeMerge="changeMerge"
                        :radioInput2="radioInput"
                        :isSame2="isSame"
                        :dataIndustry="dataIndustry"
                        :listSelectX="listSelect"
                        @closeDialog="closeDialog"
                        :openFilterFlag="openFilterFlag"></dialogFilter>
        </div>
      </div>
    </div>
    <el-dialog title="存在模板修改，是否保存该模板？"
               :visible.sync="showSaveItem"
               width="30%">
      <div style="width: 100%; display: flex; align-items: center; margin-left: 10px">
        <div style="margin-right: 10px">名称</div>
        <div><el-input v-model="input"
                    placeholder="请输入模板名称"></el-input></div>
      </div>
      <div v-if="showjslogo"
           style="margin-top: 10px; margin-left: 10px">
        <el-checkbox v-model="checked">作为<span style="color: red; font-weight: 600">慧捕基模板</span></el-checkbox>
      </div>
      <div style="text-align: right; margin-right: 10px; margin-top: 10px">
        <el-button type="primary"
                   @click="submitModel2">保存并下一步</el-button>
        <el-button type="primary"
                   @click="submitModel3">不保存直接下一步</el-button>
      </div>
    </el-dialog>
    <el-dialog title="保存模板"
               :visible.sync="showModel"
               width="30%">
      <div style="width: 100%; display: flex; align-items: center; margin-left: 10px">
        <div style="margin-right: 10px">名称</div>
        <div><el-input v-model="input"
                    placeholder="请输入模板名称"></el-input></div>
      </div>
      <div v-if="showjslogo"
           style="margin-top: 10px; margin-left: 10px">
        <el-checkbox v-model="checked">作为<span style="color: red; font-weight: 600">慧捕基模板</span></el-checkbox>
      </div>
      <div style="text-align: right; margin-right: 10px; margin-top: 10px">
        <el-button type="primary"
                   @click="submitModel">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { SelectedScore,AlphaFilterV2 } from '@/api/pages/SystemAlpha.js';
import { SelectedScoreV2,scoreCardList } from '@/api/pages/Filter.js';
import dialogFilter from './componentScore/dialogFilter.vue';
import axios from '@/api/index';
import { alphaGo } from '@/assets/js/alpha_type.js';
import { alphamsg } from '@/api/pages/SystemAlpha.js';
import rangeChoose from '../beta/components/rangeChoose.vue';
export default {
  //import引入的组件需要注入到对象中才能使用
  components: { dialogFilter, rangeChoose },
  data () {
    //这里存放数据
    return {
      kongtre: [],
      treeData: [], //树节点数据
      radio: 'equity', //一级分类
      radioList: [
        { value: 'equity', label: '主动权益' },
        { value: 'equityhk', label: '港股' },
        { value: 'bond', label: '固收+' },
        { value: 'purebond', label: '纯债' },
        { value: 'bill', label: '中短债' },
        { value: 'cbond', label: '可转债' }
      ],
      nowModel: '',
      modelList: [
        { value: '1', label: '1' },
        { value: '2', label: '2' },
        { value: '3', label: '3' },
        { value: '4', label: '4' }
      ],
      isMerge: false,
      pageTotal: 0,
      pageIndex: 1,
      dataList: [],
      dataListAll: [],
      showModelList: false,
      checked: false,
      showjslogo: false,
      input: '',
      clickIndex: 'equity',
      selectFilter: [],
      isDianDao: false,
      openFilterFlag: false,
      dataIndustry: {},
      listSelect: [],
      radioType: 'latest',
      isSame: true,
      showModel: false,
      radioInput: '',
      field104Options: [
        {
          label: '波动率', //波动率：区间数据（默认下届为0）
          value: 'volatility'
        },
        {
          label: '最大回撤', //区间数据（默认下届为0）
          value: 'maxdrawdown'
        },
        {
          label: '平均下行周期', //区间数据（默认下届为0）
          value: 'averagelength'
        },
        {
          label: '平均恢复周期', //区间数据（默认下届为0）
          value: 'averagerecovery'
        },
        {
          label: '最大回撤比', //区间数据（默认下届为0.5，上届为1）
          value: 'maxdrawdown_ratio'
        },
        {
          label: '在险价值', //区间数据
          value: 'VaR05'
        },
        {
          label: '期望损失', //区间数据
          value: 'ES05'
        },
        {
          label: '下行风险', //区间数据
          value: 'downsidevolatility'
        },
        {
          label: '波动率比', //区间数据（默认下届为0.5，上届为1）
          value: 'volatilityratio'
        },
        {
          label: '痛苦指数',
          value: 'painindex'
        }
      ],
      field105Options: [
        {
          label: '年化收益率',
          value: 'ave_return'
        },
        {
          label: '累计收益率',
          value: 'cum_return'
        },
        {
          label: '夏普率（rf==0）',
          value: 'sharpe0'
        },
        {
          label: '夏普率（rf==4%）',
          value: 'sharpe04'
        },
        {
          label: '夏普率（动态rf）',
          value: 'sharpe'
        },
        {
          label: '卡码率',
          value: 'calmar'
        },
        {
          label: '索提诺系数（rf==0）',
          value: 'sortino0'
        },
        {
          label: '索提诺系数（rf==4%）',
          value: 'sortino04'
        },
        {
          label: '索提诺系数（动态rf）',
          value: 'sortino'
        },
        {
          label: '稳定系数',
          value: 'hurstindex'
        },
        {
          label: '凯利系数',
          value: 'kelly'
        },
        {
          label: '信息⽐率',
          value: 'information'
        },
        {
          label: '上攻潜力（周）',
          value: 'upsidepotential'
        },
        {
          label: '月胜率',
          value: 'monthly_win_ratio'
        },
        {
          label: '詹森系数',
          value: 'jensen'
        },
        {
          label: '特诺系数',
          value: 'treynor'
        },
        {
          label: '上行捕获',
          value: 'bullreturn'
        },
        {
          label: '下行捕获',
          value: 'bearreturn'
        },
        {
          label: '择时gamma',
          value: 'gamma'
        },
        {
          label: 'M2',
          value: 'msquared'
        }
      ],
      loading: false,
      listCol: [],
      listar: [], //mty
      gridData: [],
      table1: [],
      table2: [],
      arrtablecol: [],
      model_ids: [],
      tags: [],
      tenantUserId: '',
      checked: '',
      nowChoose: 'score',
      indexForScore: 0,
      nowchosse: {},
      originData: [],
      isequal: [],
      showSaveItem: false,
      selectNumber: 'all'
    };
  },
  props: {
    filter_type: {
      type: String,
      default: 'filter'
    },
    pool_id: {
      type: Number
    }
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {
    listSelect: {
      handler (newVal, oldVal) {
        this.nowChoose = 'score';
      },
      deep: true
    },
    nowChoose (val) {
      if (this.nowChoose == 'score') {
        // this.nowchosse = {}
      } else {
      }
    }
  },
  //方法集合
  methods: {
    savemodelClick () {
      this.showModel = true;
      if (JSON.stringify(this.nowchosse) != '{}' && this.indexForScore != '新建模板') {
        this.input = this.nowchosse.model_name;
      }
    },
    // 去重

    removeDuplicateModelIds (data) {
      const uniqueModelIds = new Set();
      const result = [];
      const filteredChildren = data.filter((child) => {
        if (uniqueModelIds.has(child.model_id)) {
          return false;
        }
        uniqueModelIds.add(child.model_id);
        return true;
      });
      return filteredChildren;
    },
    // 模板选择
    handleCheckChange (data, checkedNode, checked) {
      console.log(data);
      if (data?.children) {
        return -1;
      }
      this.indexForScore = null;
      this.nowchosse = {};
      let i = 0;
      // 获取被选中的节点
      let checkedNodes = this.$refs.treeScore.getCheckedNodes();
      // 过滤掉父节点
      for (let i = 0; i < checkedNodes.length; i++) {
        if (checkedNodes[i]?.children) {
          checkedNodes.splice(i, 1);
          break;
        }
      }
      let checkedLeafNodes = checkedNodes.filter((node) => !node.children);
      if (checkedLeafNodes?.length > 0) {
        this.nowChoose = 'model';
      }
      const lastNode = data;
      // 如果节点是勾选状态，将其设置为已勾选的最后一个节点
      if (checkedLeafNodes.findIndex((item) => item.model_id == lastNode.model_id) >= 0) {
        i = checkedLeafNodes.findIndex((item) => item.model_id == lastNode.model_id);
      } else {
        // 如果节点是取消勾选状态，查找已勾选节点列表中的最后一个节点
        i = checkedLeafNodes.length - 1;
      }
      // if (checkedLeafNodes?.length == i) { i = i - 1 }
      console.log(data, checkedLeafNodes, i);
      this.indexForScore = checkedNodes?.[i]?.model_name || null;
      this.nowchosse = checkedNodes?.[i] || {};
      // 打印只包含叶子节点的数组
      this.tags = checkedLeafNodes;
      this.model_ids = checkedNodes?.[i]?.model_id || null;
      this.listSelect = checkedLeafNodes?.[i]?.model_args.value || [];
      this.$refs['rangeChoose'].getList({
        mtytype: this.tags?.[i]?.model_args?.mtytype || ['all'],
        csrctype: this.tags?.[i]?.model_args?.csrctype || ['all'],
        poollist: this.tags?.[i]?.model_args?.poollist || ['all']
      });
    },
    hasDuplicateItemsAcrossArrays (arrays) {
      const combinedItems = [].concat(...arrays);
      const uniqueItems = new Set(combinedItems);
      return uniqueItems.size !== combinedItems.length;
    },
    t1t2 (e, e1) {
      this.table1 = e;
      this.table2 = e1;
    },
    // 切换模板
    changetype () {
      let chooseList = this.$refs.rangeChoose.resolveList();
      this.nowChoose = 'score';
      // console.log(this.treeData, 'ss')
      if (this.tags.length > 0) {
        for (let i = 0; i < this.treeData.length; i++) {
          for (let j = 0; j < this.treeData[i]?.children?.length || 0; j++) {
            if (this.treeData[i].children[j].model_id == this.model_ids) {
              this.treeData[i].children[j].model_args.csrctype = chooseList.csrctype;
              this.treeData[i].children[j].model_args.mtytype = chooseList.mtytype;
              this.treeData[i].children[j].model_args.poollist = chooseList.poollist;
            }
          }
        }
      }
    },
    changeMerge (e) {
      this.isMerge = e;
    },
    delModel () {
      for (let i = 0; i < this.treeData.length; i++) {
        for (let j = 0; j < this.treeData[i].children.length; j++) {
          if (this.treeData[i].children[j].model_id == this.model_ids) {
            if (i == 0) {
            } else {
              this.$message.error('无权限删除');
              return;
            }
          }
        }
      }
      // let type =
      //   this.gridData.concat(this.listar)[this.gridData.concat(this.listar).findIndex((item) => item.model_name == this.nowModel)]?.types ||
      //   'mty';
      // if (type == 'mty') {
      //   this.$message.error('无权限删除');
      // } else {
      let that = this;
      axios
        .delete(
          that.$baseUrl +
          '/system/alpha/delete_model/?source=score&type=' +
          this.radio +
          '&ismanager=false&model_name=' +
          this.nowModel +
          '&flag=score' +
          '&model_id=' +
          this.model_ids
        )
        .then((res) => {
          that.getmodals();
          that.$message.success('删除成功');
          // that.useraddmodalshow =false
        })
        .catch((err) => {
          //  that.$message('失败')
          ////console.log(err)
          //that.$message('数据缺失')
        });
      // }
    },
    deepCopy (obj) {
      var a = JSON.stringify(obj);
      var newobj = JSON.parse(a);
      return newobj;
    },
    chooseModel (scope) {
      this.nowchosse = {};
      this.nowModel = scope.model_name;
      let scope1 = this.deepCopy(scope);
      this.showModelList = false;
      this.tags = [];
      this.model_ids = null;
      this.listSelect = scope1.model_args.value;
      this.isSame = scope1.model_args.isSame;
      this.radioType = scope1.model_args.radioType;
      this.radioInput = scope1.model_args.radioInput;
    },
    addmodelsnew () {
      this.listSelect = [];
      this.$refs['rangeChoose'].getList({
        mtytype: ['all'],
        csrctype: ['all'],
        poollist: ['all']
      });
      this.nowChoose = 'score';
      this.indexForScore = '新建模板';
      this.$nextTick(() => {
        this.getmodals();
      });
    },
    getmodals () {
      //获取模板[

      let that = this;
      axios
        .get(that.$baseUrl + '/system/alpha/QueryModelV2/?type=' + this.radio + '&ismanager=false&source=score')
        .then((result) => {
          let res = result.data;
          // that.gridData =
          //   res.data.self_data.map((item) => {
          //     return {
          //       ispublic: item.ispublic,
          //       model_args: item.model_args,
          //       model_description: item.model_description,
          //       model_name: item.model_name,
          //       model_time: item.model_time,
          //       user_id: item.user_id,
          //       types: 'self'
          //     };
          //   }) || [];
          // that.listar = res.data.owl_data || [];

          this.treeData = [
            { label: '我的模板', id: '000001', children: [] },
            { label: '共享模板', id: '000002', children: [] }
          ];
          this.treeData[0].children = res.data.data?.self_data?.map((item) => {
            return { ...item, id: item.model_id, label: item.model_name };
          });
          this.treeData[1].children = this.removeDuplicateModelIds(
            res.data.data?.owl_data
              ?.map((item) => {
                return { ...item, id: item.model_id, label: item.model_name };
              })
              .concat(
                res.data.data?.company_data?.map((item) => {
                  return { ...item, id: item.model_id, label: item.model_name };
                }) || []
              )
              .concat(
                res.data.data?.share_data?.map((item) => {
                  return { ...item, id: item.model_id, label: item.model_name };
                }) || []
              ) || []
          );
          this.originData = this.deepCopy(this.treeData);
          // if (that.$route.query.model_name) {
          //   if (that.listar.findIndex((item) => item.model_name == that.$route.query.model_name) >= 0) {
          //     that.chooseModel(that.listar[that.listar.findIndex((item) => item.model_name == that.$route.query.model_name)]);
          //   }
          // }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    // 新建保存模板

    submitModel () {
      let that = this;
      if (JSON.stringify(this.nowchosse) != '{}' && this.indexForScore != '新建模板') {
        for (let i = 0; i < this.originData.length; i++) {
          for (let j = 0; j < this.originData[i].children.length; j++) {
            if (this.originData[i].children[j].model_id == this.nowchosse.model_id) {
              if (this.originData[i].label == '共享模版') {
                this.$message.warning('无修改权限');
                this.treeData = this.originData;
                return false;
              } else {
                let chooseList = this.$refs.rangeChoose.resolveList();
                axios
                  .put(that.$baseUrl + '/system/alpha/UpdateModel/', {
                    type: this.radio,
                    model_id: this.nowchosse.model_id,
                    ismanager: 'false',
                    model_name: this.input,
                    model_description: '',
                    ispublic: this.checked,
                    source: 'score',
                    model_args: {
                      csrctype: chooseList.csrctype,
                      mtytype: chooseList.mtytype,
                      poollist: chooseList.poollist,
                      value: this.listSelect,
                      isSame: this.isSame,
                      radioType: this.radioType,
                      radioInput: this.radioInput
                    },
                    title: '--',
                    user_permission: [],
                    flag: 'score'
                  })
                  .then((res) => {
                    that.$message.success('修改模板成功');
                    that.getmodals();
                    that.showModel = false;
                  })
                  .catch((err) => {
                    //  that.$message('失败')
                    that.$message.error('模板修改失败');
                  });
              }
            }
          }
        }
      } else {
        let chooseList = this.$refs.rangeChoose.resolveList();
        // 先判断拆分模板
        // this.modelApplicable()
        // 在进行保存模板
        if (this.input == null || this.input == '') {
          this.$message('请输入模板名称');
        } else {
          let that = this;
          axios
            .post(that.$baseUrl + '/system/alpha/SaveModelV2/', {
              type: this.radio,
              ismanager: 'false',
              model_name: this.input,
              model_description: '',
              ispublic: this.checked,
              source: 'score',
              model_args: {
                csrctype: chooseList.csrctype,
                mtytype: chooseList.mtytype,
                poollist: chooseList.poollist,
                value: this.listSelect,
                isSame: this.isSame,
                radioType: this.radioType,
                radioInput: this.radioInput
              },
              title: '--',
              user_permission: [],
              flag: 'score'
            })
            .then((res) => {
              that.$message.success('保存模板成功');
              that.getmodals();
              that.showModel = false;
            })
            .catch((err) => {
              //  that.$message('失败')
              that.$message.error('模板保存失败');
            });
        }
      }
    },
    // 判断模板是否使用，不适用创建新模板
    modelApplicable () {
      let chooseList = this.$refs.rangeChoose.resolveList();
      let t1 = []; //含权益
      let t2 = []; //含债券
      let t3 = []; //其他
      let t4 = [];
      // console.log(chooseList, this.listSelect)
      for (let i = 0; i < this.listSelect.length; i++) {
        if (this.listSelect[i].labelIndex == 'a') {
          for (let j = 0; j < this.listSelect[i].data.length; j++) {
            if (
              this.listSelect[i].data[j].labelName == '行业宽度' ||
              this.listSelect[i].data[j].labelName == '交易能力' ||
              this.listSelect[i].data[j].labelName == '股票换手率' ||
              this.listSelect[i].data[j].labelName == '择股能力' ||
              this.listSelect[i].data[j].labelName == '组合能力' ||
              this.listSelect[i].data[j].labelName == '整体行业能力' ||
              this.listSelect[i].data[j].labelName == '股票关注期' ||
              this.listSelect[i].data[j].labelName == '特定行业能力' ||
              this.listSelect[i].data[j].labelName == '股票关注期' ||
              this.listSelect[i].data[j].labelName == '特定主题能力'
            ) {
              t1.push(this.listSelect[i].data[j]);
            } else if (this.listSelect[i].data[j].labelName == '信用能力' || this.listSelect[i].data[j].labelName == '转债能力') {
              t2.push(this.listSelect[i].data[j]);
            } else if (
              this.listSelect[i].data[j].labelName == '胜率稳定性' ||
              this.listSelect[i].data[j].labelName == '收益稳定性' ||
              this.listSelect[i].data[j].labelName == '大类资产能力' ||
              this.listSelect[i].data[j].labelName == '适应性排名' ||
              this.listSelect[i].data[j].labelName == '风控能力' ||
              this.listSelect[i].data[j].labelName == '收益能力' ||
              this.listSelect[i].data[j].labelName == '打新能力' ||
              this.listSelect[i].data[j].labelName == '综合排名'
            ) {
              t3.push(this.listSelect[i].data[j]);
            } else {
              t3.push(this.listSelect[i].data[j]);
            }
          }
        } else {
          // 通用条件
          t4 = t4.concat(this.listSelect[i]);
        }
      }
      //       csrctype: (2) ['债券型', '混合型', __ob__: bt]
      // mtytype: ['all', __ob__: bt]
      // poollist: [__ob__: bt]
    },
    my_desc_sort (name) {
      //  ////console.log(name)
      return function (a, b) {
        if (a[name] === '--' || a[name] === 'nan' || a[name] === '- -' || b[name] === '--' || b[name] === 'nan' || b[name] === '- -') {
          if (a[name] === '--' || a[name] === 'nan' || a[name] === '- -') {
            return 1;
          } else if (b[name] === '--' || b[name] === 'nan' || b[name] === '- -') {
            return -1;
          }
        } else if (Number(a[name]) > Number(b[name])) {
          return -1;
        } else if (Number(a[name]) < Number(b[name])) {
          return 1;
        } else {
          return 0;
        }
      };
    },
    my_asc_sort (name) {
      return function (a, b) {
        if (a[name] === '--' || a[name] === 'nan' || a[name] === '- -' || b[name] === '--' || b[name] === 'nan' || b[name] === '- -') {
          if (a[name] === '--' || a[name] === 'nan' || a[name] === '- -') {
            return 1;
          } else if (b[name] === '--' || b[name] === 'nan' || b[name] === '- -') {
            return -1;
          }
        } else if (Number(a[name]) < Number(b[name])) {
          return -1;
        } else if (Number(a[name]) > Number(b[name])) {
          return 1;
        } else {
          return 0;
        }
      };
    },

    sort_change (column) {
      // ////console.log(column)
      // ////console.log('colum')
      this.pageIndex = 1; // return to the first page after sorting
      if (column.prop === 'code') {
        if (column.order === 'descending') {
          this.dataListAll = this.dataListAll.sort(this.my_desc_sort('code'));
        } else if (column.order === 'ascending') {
          this.dataListAll = this.dataListAll.sort(this.my_asc_sort('code'));
        }
      } else if (column.prop === '1y') {
        if (column.order === 'descending') {
          this.dataListAll = this.dataListAll.sort(this.my_desc_sort('1y'));
        } else if (column.order === 'ascending') {
          this.dataListAll = this.dataListAll.sort(this.my_asc_sort('1y'));
        }
      } else if (column.prop === '1m') {
        if (column.order === 'descending') {
          this.dataListAll = this.dataListAll.sort(this.my_desc_sort('1m'));
        } else if (column.order === 'ascending') {
          this.dataListAll = this.dataListAll.sort(this.my_asc_sort('1m'));
        }
      } else if (column.prop === '1q') {
        if (column.order === 'descending') {
          this.dataListAll = this.dataListAll.sort(this.my_desc_sort('1q'));
        } else if (column.order === 'ascending') {
          this.dataListAll = this.dataListAll.sort(this.my_asc_sort('1q'));
        }
      } else if (column.prop === '1w') {
        if (column.order === 'descending') {
          this.dataListAll = this.dataListAll.sort(this.my_desc_sort('1w'));
        } else if (column.order === 'ascending') {
          this.dataListAll = this.dataListAll.sort(this.my_asc_sort('1w'));
        }
      } else if (column.prop === 'netasset') {
        if (column.order === 'descending') {
          this.dataListAll = this.dataListAll.sort(this.my_desc_sort('netasset'));
        } else if (column.order === 'ascending') {
          this.dataListAll = this.dataListAll.sort(this.my_asc_sort('netasset'));
        }
      } else if (column.prop === 'long_stock_pick') {
        if (column.order === 'descending') {
          this.dataListAll = this.dataListAll.sort(this.my_desc_sort('long_stock_pick'));
        } else if (column.order === 'ascending') {
          this.dataListAll = this.dataListAll.sort(this.my_asc_sort('long_stock_pick'));
        }
      } else if (column.prop === 'short_trade') {
        if (column.order === 'descending') {
          this.dataListAll = this.dataListAll.sort(this.my_desc_sort('short_trade'));
        } else if (column.order === 'ascending') {
          this.dataListAll = this.dataListAll.sort(this.my_asc_sort('short_trade'));
        }
      } else if (column.prop === 'long_adaptive') {
        if (column.order === 'descending') {
          this.dataListAll = this.dataListAll.sort(this.my_desc_sort('long_adaptive'));
        } else if (column.order === 'ascending') {
          this.dataListAll = this.dataListAll.sort(this.my_asc_sort('long_adaptive'));
        }
      } else if (column.prop === 'long_industry_cap') {
        if (column.order === 'descending') {
          this.dataListAll = this.dataListAll.sort(this.my_desc_sort('long_industry_cap'));
        } else if (column.order === 'ascending') {
          this.dataListAll = this.dataListAll.sort(this.my_asc_sort('long_industry_cap'));
        }
      } else if (column.prop === 'long_stockclass_cap') {
        if (column.order === 'descending') {
          this.dataListAll = this.dataListAll.sort(this.my_desc_sort('long_stockclass_cap'));
        } else if (column.order === 'ascending') {
          this.dataListAll = this.dataListAll.sort(this.my_asc_sort('long_stockclass_cap'));
        }
      } else if (column.prop === 'window_score') {
        if (column.order === 'descending') {
          this.dataListAll = this.dataListAll.sort(this.my_desc_sort('window_score'));
        } else if (column.order === 'ascending') {
          this.dataListAll = this.dataListAll.sort(this.my_asc_sort('window_score'));
        }
      } else if (column.prop === 'final_score_industry') {
        if (column.order === 'descending') {
          this.dataListAll = this.dataListAll.sort(this.my_desc_sort('final_score_industry'));
        } else if (column.order === 'ascending') {
          this.dataListAll = this.dataListAll.sort(this.my_asc_sort('final_score_industry'));
        }
      } else if (column.prop === 'stockclass_hold_weight') {
        if (column.order === 'descending') {
          this.dataListAll = this.dataListAll.sort(this.my_desc_sort('stockclass_hold_weight'));
        } else if (column.order === 'ascending') {
          this.dataListAll = this.dataListAll.sort(this.my_asc_sort('stockclass_hold_weight'));
        }
      } else if (column.prop === 'industry_weight') {
        if (column.order === 'descending') {
          this.dataListAll = this.dataListAll.sort(this.my_desc_sort('industry_weight'));
        } else if (column.order === 'ascending') {
          this.dataListAll = this.dataListAll.sort(this.my_asc_sort('industry_weight'));
        }
      } else if (column.prop === 'index_weight') {
        if (column.order === 'descending') {
          this.dataListAll = this.dataListAll.sort(this.my_desc_sort('index_weight'));
        } else if (column.order === 'ascending') {
          this.dataListAll = this.dataListAll.sort(this.my_asc_sort('index_weight'));
        }
      } else {
        for (let i = 0; i < this.listCol.length; i++) {
          if (column.prop == this.listCol[i].value) {
            if (column.order === 'descending') {
              this.dataListAll = this.dataListAll.sort(this.my_desc_sort(column.prop));
            } else if (column.order === 'ascending') {
              this.dataListAll = this.dataListAll.sort(this.my_asc_sort(column.prop));
            }
          }
        }
      }
      this.dataList = this.dataListAll.slice(0, 20); // show only one page
    },

    elcellstyle ({ row, column, rowIndex, columnIndex }) {
      let t = this.listCol.length;

      // ////console.log(row[0])
      if (columnIndex == 3 + t) {
        if (row['1w'] >= 0) {
          return 'color: #E85D2D;';
        } else return 'color: #20995B;';
      }
      if (columnIndex == 4 + t) {
        if (row['1m'] >= 0) {
          return 'color: #E85D2D;';
        } else return 'color: #20995B;';
      }
      if (columnIndex == 5 + t) {
        if (row['1q'] >= 0) {
          return 'color: #E85D2D;';
        } else return 'color: #20995B;';
      }
      if (columnIndex == 6 + t) {
        if (row['1y'] >= 0) {
          return 'color: #E85D2D;';
        } else return 'color: #20995B;';
      }
    },
    godetailP (id, name) {
      //带参进去
      this.$router.push({ path: '/fundmanagerdetail/' + id, hash: '', query: { id: id, name: name } });
    },
    godetail (id, name) {
      //带参进去
      alphaGo(id, name, this.$route.path);
    },
    openModelFilter () {
      this.showModelList = true;
    },
    printconsole () {
      const { export_json_to_excel } = require('@/vendor/Export2Excel');
      var list = [];
      // list.push(this.dataexplain);
      let tHeader = [];
      let tHeader2 = [];
      let filterVal = [];

      tHeader = ['基金名称', '基金经理姓名', '基金代码'];
      for (let i = 0; i < this.listCol.length; i++) {
        tHeader.push(this.listCol[i].label);
      }
      tHeader2 = ['近一周收益', '近一月收益', '近一季收益', '近一年收益', '规模', '重仓股'];
      // ////console.log(this.colums)
      for (let i = 0; i < this.dataListAll.length; i++) {
        list[i] = [];
        list[i][0] = this.dataListAll[i].name;
        list[i][1] = this.dataListAll[i].manager_name;
        list[i][2] = this.dataListAll[i].code;
        for (let j = 0; j < this.listCol.length; j++) {
          list[i][3 + j] = this.dataListAll[i][this.listCol[j].value];
        }
        list[i][3 + this.listCol.length] = this.dataListAll[i]['1w'];
        list[i][4 + this.listCol.length] = this.dataListAll[i]['1m'];
        list[i][5 + this.listCol.length] = this.dataListAll[i]['1q'];
        list[i][6 + this.listCol.length] = this.dataListAll[i]['1y'];
        list[i][7 + this.listCol.length] = (Number(this.dataListAll[i]['netasset']) / 100000000).toFixed(2) + '亿';
        list[i][8 + this.listCol.length] = this.dataListAll[i].bigs;
      }

      export_json_to_excel(tHeader.concat(tHeader2), list, '主动权益筛选结果');
    },
    // 判断对象是否相等
    isArray (value) {
      return Array.isArray(value);
    },
    deepEqual (obj1, obj2) {
      if (obj1 === obj2) {
        return true;
      }

      if (typeof obj1 !== 'object' || obj1 === null || typeof obj2 !== 'object' || obj2 === null) {
        return false;
      }

      if (this.isArray(obj1) && this.isArray(obj2)) {
        if (obj1.length !== obj2.length) {
          return false;
        }
        for (let i = 0; i < obj1.length; i++) {
          if (!this.deepEqual(obj1[i], obj2[i])) {
            return false;
          }
        }
        return true;
      }

      if (this.isArray(obj1) !== this.isArray(obj2)) {
        return false;
      }

      const keys1 = Object.keys(obj1);
      const keys2 = Object.keys(obj2);

      if (keys1.length !== keys2.length) {
        return false;
      }

      for (let key of keys1) {
        if (!keys2.includes(key) || !this.deepEqual(obj1[key], obj2[key])) {
          return false;
        }
      }

      return true;
    },
    async doFilter (datas) {
      // console.log(datas)
      let postData = [];
      // console.log(this.table1.concat(this.table2))
      for (let i = 0; i < datas.length; i++) {
        postData.push({
          codes:[],
          flag:'1',
          mtytype: datas[i]?.model_args?.mtytype || ['all'],
          csrctype: datas[i]?.model_args?.csrctype || ['all'],
          poollist:
            this.filter_type == 'filter'
              ? datas[i]?.model_args?.poollist?.length == 0
                ? ['all']
                : datas[i]?.model_args?.poollist?.filter((item) => item != '全市场基金') || ['all']
              : [this.pool_id],
          model_name: datas[i]?.label || '自定义打分选项',
          item: [],
          selectNumber: this.selectNumber
        });
        for (let j = 0; j < datas[i]?.model_args?.value.length; j++) {
          for (let k = 0; k < datas[i]?.model_args?.value[j].data.length; k++) {
            let t1 =
              this.table1.concat(this.table2)[
                this.table1.concat(this.table2).findIndex((item) => item.name == datas[i]?.model_args?.value?.[j]?.data?.[k]?.labelName)
              ].id || '--';
            let t2 =
              this.table1.concat(this.table2)[
                this.table1.concat(this.table2).findIndex((item) => item.name == datas[i]?.model_args?.value?.[j]?.data?.[k]?.labelName)
              ].ename || '--';
            // let temparray = this.table1.concat(this.table2)
            postData[i].item.push({
              id: t1,
              ename: t2,
              name: datas[i]?.model_args?.value?.[j]?.data?.[k]?.labelName,
              operation: datas[i]?.model_args?.value?.[j]?.data?.[k]?.dataResult[0]?.operation?.mathRange,
              value:
                datas[i]?.model_args?.value?.[j]?.data?.[k]?.labelName == '特定主题能力' ||
                  datas[i]?.model_args?.value?.[j]?.data?.[k]?.labelName == '特定行业能力'
                  ? datas[i]?.model_args?.value?.[j]?.data?.[k]?.dataResult[0]?.value / 100
                  : datas[i]?.model_args?.value?.[j]?.data?.[k]?.dataResult[0]?.value,
              date: datas[i]?.model_args?.value?.[j]?.data?.[k]?.dataResult[0]?.date?.[1] || '--',
              selectoption: datas[i]?.model_args?.value?.[j]?.data?.[k]?.dataResult[0]?.date
            });
          }
        }
      }
      // console.log(postData)
      let resultData = [];
      this.loading = true;
      if (this.filter_type != 'filter') {
        this.$emit('resolveFather', postData);
        this.loading = false;
        return;
      }
      for (let i = 0; i < postData.length; i++) {
        let data = await SelectedScoreV2(postData[i]);
        if (data && data.mtycode == 200) {
          resultData.push({ key: postData[i].model_name, value: data?.data || [] });
        } else if (data.mtycode == 204) {
          this.$notify({
            title: '打分结果为空',
            message: '非常抱歉，基金打分结果为空，请确认打分范围是否正确',
            type: 'error',
            duration: '5000'
          });
          this.showmsgloading = false;
        } else {
          this.$notify({
            title: '基金打分失败',
            message: '非常抱歉，基金打分失败',
            type: 'error',
            duration: '5000'
          });
          this.loading = true;
          this.showmsgloading = false;
        }
      }
      this.loading = false;
      // console.log(resultData, '打分结果')
      this.$emit('scoreResult', this.arrtablecol, resultData, this.selectNumber);
      this.saveScore();
    },
    saveScore () {
      try {
        let chooseList = this.$refs.rangeChoose.resolveList();
        this.localStorage.setItem('mty_score_ListSelect', this.listSelect);
        this.localStorage.setItem('mty_score_types', chooseList);
        this.localStorage.setItem('mty_score_chooseModel', this.tags);
      } catch { }
    },
    hasDuplicate (nums) {
      const numSet = new Set(nums);
      return numSet.size !== nums.length;
    },
    async gotoFilter () {
      this.showSaveItem = false;
      this.isequal = [];
      // console.log(this.tags, this.originData)
      for (let i = 0; i < this.originData.length; i++) {
        for (let j = 0; j < this.originData[i].children?.length; j++) {
          if (this.tags.findIndex((item) => item.model_id == this.originData[i].children[j].model_id) >= 0) {
            if (
              this.deepEqual(
                this.tags[this.tags.findIndex((item) => item.model_id == this.originData[i].children[j].model_id)],
                this.originData[i].children[j]
              )
            ) {
            } else {
              this.isequal.push(this.tags.findIndex((item) => item.model_id == this.originData[i].children[j].model_id));
            }
          }
        }
      }
      // if (this.isequal.length == 0) {
      // 正常进入打分
      if (this.tags.length == 0) {
        let chooseList = this.$refs.rangeChoose.resolveList();
        this.doFilter([{ model_args: { ...chooseList, value: this.listSelect } }]);
      } else {
        let temp1 = [];
        let temp2 = [];
        for (let i = 0; i < this.tags.length; i++) {
          temp1 = temp1.concat(this.tags[i]?.model_args?.mtytype);
          temp2 = temp2.concat(this.tags[i].model_args?.csrctype);
        }
        // console.log(temp1, temp2, this.hasDuplicate(temp1), this.hasDuplicate(temp2))
        if (this.hasDuplicate(temp1)) {
          this.$message.warning('抱歉不能选择具交叉分类的基金进行打分');
          return false;
        } else {
          this.doFilter(this.tags);
        }
      }
      // }
      // else {
      //   console.log('isqut', this.isequal)
      //   this.doFilter(this.tags)
      //   // 提示是否保存模板
      //   // this.showSaveItem = true
      // }
      return false;
      let codeList = [];
      // console.log(this.table1);
      // console.log(this.table2);
      console.log(this.listSelect);
      if (
        this.localStorage.getItem('mty_filter_new_result') != null &&
        this.localStorage.getItem('mty_filter_new_result') != undefined &&
        this.localStorage.getItem('mty_filter_new_result') != 'undefined'
      ) {
        let dataXX = JSON.parse(this.localStorage.getItem('mty_filter_new_result'));
        for (let i = 0; i < dataXX.length; i++) codeList.push(dataXX[i].code);
      } else if (
        this.localStorage.getItem('mty_filter_new_result_code') != null &&
        this.localStorage.getItem('mty_filter_new_result_code') != undefined &&
        this.localStorage.getItem('mty_filter_new_result_code') != 'undefined'
      ) {
        codeList = JSON.parse(this.localStorage.getItem('mty_filter_new_result_code'));
      }
      this.loading = true;
      let temp1 = [];
      let temp2 = [];
      for (let a = 0; a < this.listSelect.length; a++) {
        for (let i = 0; i < this.listSelect[a].data.length; i++) {
          for (let j = 0; j < this.table1.length; j++) {
            if (this.table1[j].name == this.listSelect[a].data[i].labelName) {
              temp1.push({
                id: this.table1[j].id,
                name: this.table1[j].name,
                ename: this.table1[j].ename,
                min: this.listSelect[a].data[i].dataResult[0].value,
                date: this.listSelect[a].data[i].dataResult[0].date,
                rank: '',
                selectoption: ''
              });
            }
          }
          for (let k = 0; k < this.table2.length; k++) {
            if (this.table2[k].name == this.listSelect[a].data[i].labelName) {
              console.log(this.listSelect[a].data[i]);
              if (this.listSelect[a].data[i].labelName == '特定主题能力' || this.listSelect[a].data[i].labelName == '特定行业能力') {
                temp2.push({
                  id: this.table2[k].id,
                  name: this.table2[k].name,
                  ename: this.table2[k].ename,
                  min: 0,
                  date: '',
                  rank: [this.listSelect[a].data[i].dataResult[0].value / 100],
                  selectoption: [this.listSelect[a].data[i].dataResult[0].date]
                });
              } else {
                temp2.push({
                  id: this.table2[k].id,
                  name: this.table2[k].name,
                  ename: this.table2[k].ename,
                  min: this.listSelect[a].data[i].dataResult[0].value,
                  date: '',
                  rank: '',
                  selectoption: ''
                });
              }
            }
          }
        }
      }
      try {
        let data = await SelectedScoreV2({
          scoretable1: temp1,
          scoretable2: temp2,
          code: codeList
        });
        if (data && data.mtycode == 200) {
          try {
            this.localStorage.setItem('mty_scoreListSelect', JSON.stringify(this.listSelect));
          } catch (e) {
            this.localStorage.setItem('mty_scoreListSelect', JSON.stringify([]));
            // this.$message.warning('缓存已满，无法存入');
          }
          this.arrtablecol = [];
          if (temp1.length) {
            for (let i = 0; i < temp1.length; i++) {
              this.arrtablecol.push({ name: temp1[i].name + temp1[i].date, keyName: temp1[i].ename + temp1[i].date, flag: 1 });
            }
          }
          if (temp2.length) {
            for (let j = 0; j < temp2.length; j++) {
              if (temp2[j].selectoption.length) {
                for (let k = 0; k < temp2[j].selectoption.length; k++) {
                  this.arrtablecol.push({ name: temp2[j].selectoption[k], keyName: temp2[j].selectoption[k], flag: 2 });
                }
              } else {
                this.arrtablecol.push({ name: temp2[j].name, keyName: temp2[j].name, flag: 2 });
              }
            }
          }
          this.$emit('scoreResult', this.arrtablecol, data.data);
        } else {
          this.$notify({
            title: '基金打分失败',
            message: '非常抱歉，基金打分失败，错误信息：' + data.mtymessage,
            type: 'error',
            duration: '5000'
          });
        }
        this.showmsgloading = false;
      } catch (err) {
        this.$notify({
          title: '基金打分失败',
          message: '非常抱歉，基金打分失败',
          type: 'error',
          duration: '5000'
        });
        this.showmsgloading = false;
      }
      this.loading = false;
    },
    handlePageChange () {
      this.dataList = this.dataListAll.slice((this.pageIndex - 1) * 20, this.pageIndex * 20 - 1);
    },
    getTime () {
      let flag = 0;
      if (this.radioType == 'latest') {
        flag = 90;
        return this.moment(Date.now() - 86400000 * flag).format('YYYY-MM-DD');
      } else if (this.radioType == '3') {
        flag = 3 * 365;
        return this.moment(Date.now() - 86400000 * flag).format('YYYY-MM-DD');
      } else if (this.radioType == '6') {
        flag = 6 * 365;
        return this.moment(Date.now() - 86400000 * flag).format('YYYY-MM-DD');
      } else if (this.radioType == 'radioSelf') {
        flag = 365 * Number(this.radioInput);
        return this.moment(Date.now() - 86400000 * flag).format('YYYY-MM-DD');
      } else if (this.radioType == 'created') {
        return '2000-01-01';
      }
    },
    closeTags (index, index1) {
      if (this.listSelect[index].data.length == 1) {
        this.listSelect.splice(index, 1);
      } else if (this.listSelect[index].data.length > 1) {
        this.listSelect[index].data.splice(index1, 1);
      }
    },
    closeDialog (e, e2, e3, e4, e5) {
      if (e == '--') {
        this.addmodelsnew();
      } else if (e == 'back') {
        this.$emit('back');
      } else {
        try {
          this.listSelect = e;
          for (let i = this.listSelect.length - 1; i >= 0; i--) {
            for (let j = this.listSelect[i].data.length - 1; j >= 0; j--) {
              // console.log(this.listSelect[i].data[j]);
              // console.log(this.listSelect[i].data[j].havedata);
              if (this.listSelect[i].data[j].dataResult.length == 0 || this.listSelect[i].data[j].dataResult[0]?.havedata == false) {
                this.listSelect[i].data.splice(j, 1);
              }
              if (this.listSelect[i].data.length == 0) {
                this.listSelect.splice(i, 1);
              }
            }
          }
          this.openFilterFlag = false;
          this.radioType = e2;
          this.isSame = e4;
          this.radioInput = e3;
          this.gotoFilter();
          this.selectNumber = e5
        } catch (err) {
          this.$message.error('筛选失败，请确认运算符、数值等条件是否选择');
        }
      }
    },
    async getIndustryInfo () {
      let data = await alphamsg();
      if (data.mtycode == 200) {
        this.dataIndustry = data.data;
      }
    },
    Init () {
      if (
        this.localStorage.getItem('mty_filterType') != null &&
        this.localStorage.getItem('mty_filterType') != undefined &&
        this.localStorage.getItem('mty_filterType') != 'null' &&
        this.localStorage.getItem('mty_filterType') != 'undefined'
      ) {
        this.radio = this.localStorage.getItem('mty_filterType');
      }
      let that = this;
      this.getmodals();
      this.getIndustryInfo();
      this.$refs.dialogScore.getList();
      if (this.$store.state.userType.indexOf('staff') >= 0 || this.$store.state.userType.indexOf('super') >= 0) {
        this.showjslogo = true;
      } else {
        this.showjslogo = false;
      }
      if (that.$route.query.model_name) {
        // this.tabName = '0'
      } else {
        if (
          this.localStorage.getItem('mty_scoreListSelect') != null &&
          this.localStorage.getItem('mty_scoreListSelect') != undefined &&
          this.localStorage.getItem('mty_scoreListSelect') != 'null' &&
          this.localStorage.getItem('mty_scoreListSelect') != 'undefined'
        ) {
          this.$nextTick(() => {
            // this.listCol = JSON.parse(this.localStorage.getItem('mty_scoreListCol'));
            this.listSelect = JSON.parse(this.localStorage.getItem('mty_scoreListSelect'));
          });
        }
      }
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () {
    if (this.localStorage.getItem('mty_score_ListSelect') && this.localStorage.getItem('mty_score_ListSelect')?.length > 0) {
      this.listSelect = this.localStorage.getItem('mty_score_ListSelect');
    }
    if (
      this.localStorage.getItem('mty_score_types')?.csrctype &&
      this.localStorage.getItem('mty_score_types')?.mtytype &&
      this.localStorage.getItem('mty_score_types')?.poollist
    ) {
      // setTimeout(() => {
      this.$refs['rangeChoose'].getList({
        mtytype: this.localStorage.getItem('mty_score_types')?.mtytype || ['all'],
        csrctype: this.localStorage.getItem('mty_score_types')?.csrctype || ['all'],
        poollist: this.localStorage.getItem('mty_score_types')?.poollist || ['all']
      });
      // }, 3000)
    }
    if (this.localStorage.getItem('mty_score_chooseModel') && this.localStorage.getItem('mty_score_chooseModel')?.length > 0) {
      // for(let i =0;i<this.localStorage.getItem('mty_score_chooseModel')?.length;i++){
      this.kongtre = this.localStorage.getItem('mty_score_chooseModel');
      // }
    } else {
      this.kongtre = [];
    }
  },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style>
.newFilterTag .el-tag .el-icon-close {
  color: rgba(0, 0, 0, 0.45) !important;
}
</style>
<style lang="scss" scoped>
.boxModel {
  height: 40px;
  cursor: pointer;
}
//@import url(); 引入公共css类
</style>
