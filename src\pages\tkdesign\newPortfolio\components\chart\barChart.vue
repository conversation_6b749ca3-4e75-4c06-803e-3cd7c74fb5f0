<template>
		<div class="charts_fill_class" v-loading="loading">
			<el-empty image-size="160" v-if="showEmpty"></el-empty>
			<v-chart
				v-else
				ref="companySizeChange"
				:options="option"
				element-loading-text="暂无数据"
				element-loading-spinner="el-icon-document-delete"
				element-loading-background="rgba(239, 239, 239, 0.5)"
				class="charts_one_class"
				autoresize
			></v-chart>
		</div>
</template>

<script>
import VChart from 'vue-echarts';

import { barChartOption } from '@/utils/chartStyle.js';
// import { getTimeingInfo } from '@/api/pages/tkAnalysis/captial-market.js'
export default {
	components: { VChart },
	data() {
		return {
			option: {},
			loading: true,
			showEmpty: true,
		};
	},
	methods: {
		showempty(){
			this.showEmpty = true;
		
		},
		hideempty(){
			this.showEmpty = false;
		},
	
		async getData(param) {
			// let res = await getTimeingInfo({});
			// const {date_list,data1,data2} = this.filterData(res.data);
			this.loading = false;
			this.option = barChartOption({
				toolbox:false,
				color:['#4096ff', '#4096ff', '#7388A9', '#6F80DD', '#6C96F2', '#FD6865', '#83D6AE'],
				tooltip: {
					formatter: function (obj) {
						var value = `<div style="font-size:14px;">` + obj?.[0].axisValue + `</div>`;
						for (let i = 0; i < obj.length; i++) {
							value +=
								`<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
								`<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
								obj?.[i].color +
								`;"></div>` +
								`<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
								(Number(obj?.[i].value) * 1).toFixed(2) +
								'%</div>' +
								`</div>`;
						}
						return `<div style="padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
					}
				},
				legend: [],
				grid:{
					bottom:'10'	
				},
				xAxis: [
					{
						type: 'category',
						boundaryGap: true,
						data: param.chartDate,
						axisPointer: {
							type: 'shadow'
						},
						axisLabel: {
							//x轴文字的配置
							show: true,
							interval: 0,//使x轴文字显示全
						}
					}
				],
				yAxis: [
					{
						type: 'value',
					
					}
					
				],
				series: [
		
					{
						type: 'bar',
						itemStyle: {
							normal: {
								//这里是重点
								color: function(params) {
									console.log(params)
									if(params.value > 0){
										return '#CF1322'
									}else{
										return '#389E0D'
									}
								}
							}
						},
						barMaxWidth:'35',
						data: param.chartData
					},
          
				]
			});
		},
		filterData(data){
			const date_list = data.map((item)=>{
				if(item.flag !== '全年'){
					return item.date;
				}
				
			});
			const data1= data.map((item)=>{
				if(item.flag !== '全年'){
					return item.exponentNav
				}
			});
			const data2 = data.map((item)=>{
				if(item.flag !== '全年'){
					return item.stockValue
				}
			});
			return {date_list,data1,data2}
		}
	}
};
</script>

<style scoped>
.chart_one{
	padding: 0;
	box-shadow: none;
}
</style>
