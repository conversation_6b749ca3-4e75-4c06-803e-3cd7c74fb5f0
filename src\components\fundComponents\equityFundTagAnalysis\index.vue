<!--  -->
<template>
	<div class="chart_one">
		<div style="display: flex; align-items: center; justify-content: space-between">
			<div class="title" style="flex: 1; text-align: left">
				基金标签分析<el-tooltip class="item" effect="dark" content="任意一个时间节点开始买入，与基准进行比较" placement="right-start">
					<svg width="14" height="14" viewBox="0 0 14 14" fill="none">
						<path
							fill-rule="evenodd"
							clip-rule="evenodd"
							d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
							fill="black"
							fill-opacity="0.45"
						/>
					</svg>
				</el-tooltip>
			</div>
			<div>
				比较基准选择：<el-select v-model="benchmarkvalue" @change="changeBenchmark" placeholder="请选择比较基准">
					<el-option v-for="item in benchmarkoptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>
				<el-button icon="el-icon-document-delete" style="margin-left: 16px" @click="exportExcel">导出Excel</el-button>
			</div>
		</div>
		<el-table
			:data="fourdata"
			class="table"
			ref="multipleTable"
			style="width: 99%"
			max-height="400px"
			header-cell-class-name="table-header"
			v-loading="loading"
			:default-sort="{ prop: 'sum_weight', order: 'descending' }"
		>
			<el-table-column
				v-for="(item, index) in columnList"
				:key="index"
				:prop="item.value"
				:label="item.label"
				:formatter="item.format"
				:sortable="item.sortable"
				align="gotoleft"
			>
			</el-table-column>
			<!-- <el-table-column prop="type" :label="benchmarkvaluename" align="gotoleft"> </el-table-column>
			<el-table-column prop="sum_weight" sortable align="gotoleft" label="权重">
				<template slot-scope="scope"
					><span>{{ scope.row.sum_weight | fixp }}</span></template
				>
			</el-table-column>
			<el-table-column prop="fund_number" sortable align="gotoleft" label="数量">
				<template slot-scope="scope"
					><span>{{ scope.row.fund_number }}</span></template
				>
			</el-table-column> -->
		</el-table>
	</div>
</template>

<script>
import { exportTitle, exportTable } from '@/utils/exportWord.js';
import { filter_json_to_excel } from '@/utils/exportExcel.js';
export default {
	data() {
		//这里存放数据
		return {
			notesData: {
				label001: ''
			},
			datatable: [],
			benchmarkvalue: 'type',
			benchmarkvaluename: '类型',
			columnList: [
				{ label: '类型', value: 'type' },
				{ label: '权重', value: 'sum_weight', format: this.fixp, sortable: true },
				{ label: '数量', value: 'fund_number', sortable: true }
			],
			benchmarkoptions: [
				{
					label: '类型',
					value: 'type'
				},
				{
					label: '风格',
					value: 'style'
				},
				{
					label: '行业',
					value: 'industry'
				}
			],
			fourdata: [],
			loading: true
		};
	},
	filters: {},
	//方法集合
	methods: {
		getData(data) {
			this.loading = false;
			this.fourdata = data;
		},
		changeBenchmark(value) {
			this.loading = true;
			if (value == 'type')
				this.columnList = [
					{ label: '类型', value: 'type' },
					{ label: '权重', value: 'sum_weight', format: this.fixp, sortable: true },
					{ label: '数量', value: 'fund_number', sortable: true }
				];
			else if (value == 'style')
				this.columnList = [
					{ label: '风格标签', value: 'description' },
					{ label: '风格', value: 'type' },
					{ label: '权重', value: 'sum_weight', format: this.fixp, sortable: true },
					{ label: '数量', value: 'fund_number', sortable: true }
				];
			else if (value == 'industry')
				this.columnList = [
					{ label: '行业', value: 'type' },
					{ label: '占净值比', value: 'sum_weight', format: this.fixp, sortable: true },
					{ label: '占权益比', value: 'stock_weight', format: this.fixp, sortable: true },
					{ label: '数量', value: 'fund_number', sortable: true }
				];
			this.$emit('resolveFather', value);
		},
		fixp(row, column) {
			let data = row?.[column?.property];
			if (data == '--') return data;
			else {
				return Number(data).toFixed(2) + '%';
			}
		},
		exportExcel() {
			filter_json_to_excel(this.columnList, this.fourdata, '权益基金标签分析');
		},
		createPrintWord() {
			if (this.fourdata.length) {
				return [
					...exportTitle('权益基金标签分析'),
					...exportTable(
						this.columnList.map((item) => {
							return { ...item, format: item.format ? 'fix2b' : undefined };
						}),
						this.fourdata
					)
				];
			} else {
				return [];
			}
		}
	}
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
</style>
