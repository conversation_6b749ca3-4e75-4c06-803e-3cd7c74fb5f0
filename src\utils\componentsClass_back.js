export let componentsList = [
	{
		label: '一页通',
		key: 'onePagePass',
		class: [
			'equity',
			'equitywithhk',
			'equityhk',
			'equityhk-index',
			'equityenhance',
			'equityindex',
			'bond',
			'obond',
			'cbond',
			'bill',
			'purebond'
		],
		templateList: [
			{
				name: '光大银行',
				is: 'GDBankDetailequity',
				value: 'GDBankDetailequity',
				type: 'big_template',
				typelist: ['equity', 'equitywithhk'],
				isshow: true,
				getData: 'getGDBankDetailData',
				getRequestData: undefined
			},
			{
				name: '光大银行',
				is: 'GDBankDetailbond',
				value: 'GDBankDetailbond',
				type: 'big_template',
				typelist: ['bond', 'obond'],
				isshow: true,
				getData: 'getGDBankDetailData',
				getRequestData: undefined
			},
			{
				name: '业绩与能力评价',
				is: 'performanceCapabilityEvaluation',
				value: 'performanceCapabilityEvaluation',
				typelist: [
					'equity',
					'equityhk',
					'equitywithhk',
					'equityhk-index',
					'equityenhance',
					'equityindex',
					'bond',
					'obond',
					'cbond',
					'bill',
					'purebond'
				],
				type: 'small_template',
				isshow: true,
				getData: 'getPerformanceCapabilityEvaluationData',
				getRequestData: 'getPerformanceCapabilityEvaluation'
			},
			{
				name: '除固收外其他债券基金经理评价',
				is: 'evaluateManagers',
				value: 'evaluateManagers',
				typelist: ['cbond', 'bill', 'purebond'],
				type: 'small_template',
				isshow: true,
				getData: 'getEvaluateManagersData',
				getRequestData: 'getEvaluateManagers'
			},
			{
				name: '基金经理评价',
				is: 'fundManagerEvaluation',
				value: 'fundManagerEvaluation',
				typelist: ['equity', 'equityhk', 'equitywithhk', 'bond', 'obond'],
				type: 'small_template',
				isshow: true,
				getData: 'getFundManagerEvaluationData',
				getRequestData: 'getFundManagerEvaluation'
			},
			{
				name: '表现风格',
				is: 'performanceStyle',
				value: 'performanceStyle',
				typelist: ['equity', 'equityhk', 'equitywithhk', 'equityhk-index', 'equityenhance', 'equityindex'],
				type: 'small_template',
				isshow: true,
				getData: 'getPerformanceStyleData',
				getRequestData: 'getPerformanceStyle'
			},
			{
				name: '操盘风格',
				is: 'holdingStyle',
				value: 'holdingStyle',
				typelist: ['equity', 'equityhk', 'equitywithhk', 'equityhk-index', 'equityenhance', 'equityindex', 'bond', 'obond', 'bill'],
				type: 'small_template',
				isshow: true,
				getData: 'getHoldingStyleData',
				getRequestData: 'getHoldingStyle'
			},
			{
				name: '近期公告风格',
				is: 'recentAnnouncementStyle',
				value: 'recentAnnouncementStyle',
				typelist: ['equity', 'equityhk', 'equitywithhk'],
				type: 'small_template',
				isshow: true,
				getData: 'getRecentAnnouncementStyleData',
				getRequestData: 'getRecentAnnouncementStyle'
			},
			{
				name: '近30日因子分析',
				typelist: ['equity', 'equityhk', 'equitywithhk'],
				is: 'factorAnalysis',
				value: 'factorAnalysis',
				type: 'small_template',
				isshow: true,
				getData: 'getFactorAnalysisData',
				getRequestData: 'getFactorAnalysis'
			},
			{
				name: '年度风格',
				typelist: ['bond', 'obond', 'cbond', 'bill', 'purebond'],
				is: 'debtBasedStyle',
				value: 'debtBasedStyle',
				type: 'small_template',
				isshow: true,
				getData: 'getDebtBasedStyleData',
				getRequestData: 'getDebtBasedByLan'
			},
			{
				name: '近期风格',
				typelist: ['bond', 'obond', 'cbond', 'bill', 'purebond'],
				is: 'recentStyle',
				value: 'recentStyle',
				type: 'small_template',
				isshow: true,
				getData: 'getRecentStyleData',
				getRequestData: 'getRecentStyle'
			},
			{
				name: '固收+的加法描述',
				typelist: ['bond', 'obond'],
				is: 'additiveDescription',
				value: 'additiveDescription',
				type: 'small_template',
				isshow: true,
				getData: 'getAdditiveDescriptionData',
				getRequestData: 'getGSWEB'
			},
			{
				name: '市场风格表现',
				typelist: ['bond'],
				is: 'stockDebtSynthesis',
				value: 'stockDebtSynthesis',
				type: 'small_template',
				isshow: true,
				getData: 'getStockDebtSynthesisData',
				getRequestData: 'getMarketStyleBYlan'
			},
			{
				name: '规模及持有人结构',
				typelist: ['equity', 'equityhk', 'equitywithhk'],
				is: 'sizeStructure',
				value: 'sizeStructure',
				type: 'small_template',
				isshow: true,
				getData: 'getSizeStructureData',
				getRequestData: 'getMoneyScale'
			},
			{
				name: '行业评价',
				typelist: ['equity', 'equityhk', 'equitywithhk', 'bond', 'obond'],
				is: 'industryEvaluation',
				value: 'industryEvaluation',
				type: 'big_template',
				isshow: true,
				getData: 'getIndustryEvaluationData',
				getRequestData: 'getIndustryDetlaCapability'
			},
			{
				name: '规模及持有人结构',
				typelist: ['equityhk-index', 'equityenhance', 'equityindex', 'bond', 'obond', 'cbond', 'bill', 'purebond'],
				is: 'sizeStructure',
				value: 'sizeStructure',
				type: 'big_template',
				isshow: true,
				getData: 'getSizeStructureData',
				getRequestData: 'getMoneyScale'
			},
			{
				name: '相同跟踪标的基金',
				typelist: ['equityhk-index', 'equityenhance', 'equityindex'],
				is: 'sameSubjectMatterFund',
				value: 'sameSubjectMatterFund',
				type: 'big_template',
				isshow: true,
				getData: 'getSameSubjectMatterFundData',
				getRequestData: 'getFundBmindex'
			},
			{
				name: '行业能力&&市场适应性',
				is: 'industryCapacityAndMarketAdaptability',
				value: 'industryCapacityAndMarketAdaptability',
				type: 'big_template',
				typelist: ['equity', 'equityhk', 'equitywithhk', 'bond'],
				isshow: true,
				getData: 'getIndustryCapacityAndMarketAdaptabilityData',
				getRequestData: 'getIndustryCapacityAndMarketAdaptability'
			}
		]
	},
	{
		label: '业绩表现',
		key: 'performance',
		class: [
			'equity',
			'equitywithhk',
			'equityhk',
			'equityhk-index',
			'equityenhance',
			'equityindex',
			'bond',
			'obond',
			'cbond',
			'bill',
			'purebond',
			'fof'
		],
		templateList: [
			{
				name: '收益率分布直方图',
				is: 'distributionReturn',
				value: 'distributionReturn',
				type: 'big_template',
				typelist: [
					'equity',
					'equityhk',
					'equitywithhk',
					'equityhk-index',
					'equityenhance',
					'equityindex',
					'bond',
					'obond',
					'cbond',
					'bill',
					'purebond',
					'fof'
				],
				isshow: true,
				getData: 'getDistributionReturnData',
				methods: 'getDistributionData',
				getRequestData: 'getDistributionReturn'
			},
			{
				name: '风险收益指标',
				is: 'riskReturnIndex',
				value: 'riskReturnIndex',
				typelist: [
					'equity',
					'equityhk',
					'equitywithhk',
					'equityhk-index',
					'equityenhance',
					'equityindex',
					'bond',
					'obond',
					'cbond',
					'bill',
					'purebond',
					'fof'
				],
				type: 'big_template',
				isshow: true,
				getData: 'getRiskReturnIndexData',
				getRequestData: 'getRiskFeatureYearly'
			},
			{
				name: '风险收益关系',
				is: 'riskReturnRelationship',
				value: 'riskReturnRelationship',
				typelist: [
					'equity',
					'equityhk',
					'equitywithhk',
					'equityhk-index',
					'equityenhance',
					'equityindex',
					'bond',
					'obond',
					'cbond',
					'bill',
					'purebond',
					'fof'
				],
				type: 'big_template',
				isshow: true,
				getData: 'getBestBenchmarksData',
				getRequestData: 'getBestBenchmarks'
			},
			{
				name: '分时段业绩表现',
				is: 'timePhasedPerformance',
				value: 'timePhasedPerformance',
				typelist: ['equity', 'equityhk', 'equitywithhk', 'equityhk-index', 'equityenhance', 'equityindex', 'fof'],
				type: 'big_template',
				isshow: true,
				getData: 'getMarketWindowReturnData',
				getRequestData: 'getFundPeriod'
			},
			{
				name: '动态4因子分析',
				is: 'dynamicFourFactorAnalysis',
				value: 'dynamicFourFactorAnalysis',
				typelist: ['fof'],
				type: 'small_template',
				isshow: true,
				getData: 'getDynamicFourFactorAnalysisData',
				getRequestData: 'getDynamicAnalysis'
			}
			// {
			// 	name: '动态4因子统计',
			// 	is: 'dynamicFourFactorStatistics',
			// 	value: 'dynamicFourFactorStatistics',
			// 	typelist: ['fof'],
			// 	type: 'small_template',
			// 	isshow: true,
			// 	getData: 'getDynamicFourFactorStatisticsData',
			// 	getRequestData: 'getDynamicStatistics'
			// },
			// {
			// 	name: 'TM模型分析',
			// 	is: 'TMModelAnalysis',
			// 	value: 'TMModelAnalysis',
			// 	typelist: ['fof'],
			// 	type: 'big_template',
			// 	isshow: true,
			// 	getData: 'getTMModelAnalysisData',
			// 	getRequestData: 'getTMStatistics'
			// },
			// {
			// 	name: '同类排名比较',
			// 	is: 'comparisonSimilarRanking',
			// 	value: 'comparisonSimilarRanking',
			// 	typelist: ['fof'],
			// 	type: 'big_template',
			// 	isshow: true,
			// 	getData: 'getComparisonSimilarRankingData',
			// 	getRequestData: 'getFofMeasureSinceRank'
			// },
			// {
			// 	name: '滚动胜率',
			// 	is: 'holdingPressure',
			// 	value: 'holdingPressure',
			// 	typelist: [
			// 		'equity',
			// 		'equityhk',
			// 		'equitywithhk',
			// 		'equityhk-index',
			// 		'equityenhance',
			// 		'equityindex',
			// 		'bond',
			// 		'obond',
			// 		'cbond',
			// 		'bill',
			// 		'purebond',
			// 		'fof'
			// 	],
			// 	type: 'big_template',
			// 	isshow: true,
			// 	getData: 'getHoldingPressureData',
			// 	getRequestData: 'getAnalysisIndex'
			// },
			// {
			// 	name: '基金利润分析',
			// 	is: 'fundProfitAnalysis',
			// 	value: 'fundProfitAnalysis',
			// 	typelist: ['equity', 'equityhk', 'equitywithhk', 'bond', 'obond', 'cbond', 'bill', 'purebond'],
			// 	type: 'big_template',
			// 	isshow: true,
			// 	getData: 'getFundProfitAnalysisData',
			// 	getRequestData: 'getFundProfitAnalysis'
			// },
			// {
			// 	name: '利息收益分析',
			// 	is: 'interestIncomeAnalysis',
			// 	value: 'interestIncomeAnalysis',
			// 	typelist: ['bond', 'obond', 'cbond', 'bill', 'purebond'],
			// 	type: 'big_template',
			// 	isshow: true,
			// 	getData: 'getInterestIncomeAnalysisData',
			// 	getRequestData: 'getInterestIncomeAnalysis'
			// },
			// {
			// 	name: '投资收益分析',
			// 	is: 'investmentIncomeAnalysis',
			// 	value: 'investmentIncomeAnalysis',
			// 	typelist: ['equity', 'equityhk', 'equitywithhk', 'bond', 'obond', 'cbond', 'bill', 'purebond'],
			// 	type: 'big_template',
			// 	isshow: true,
			// 	getData: 'getInvestmentIncomeAnalysisData',
			// 	getRequestData: 'getInvestmentIncomeAnalysis'
			// },
			// {
			// 	name: '波动率收益图',
			// 	is: 'rateReturn',
			// 	value: 'rateReturn',
			// 	typelist: ['bill', 'purebond'],
			// 	type: 'big_template',
			// 	isshow: true,
			// 	methods: 'rateReturnChange',
			// 	getData: 'getRateReturn',
			// 	getRequestData: undefined
			// },
			// {
			// 	name: '基金打新分析',
			// 	is: 'fundInnovation',
			// 	value: 'fundInnovation',
			// 	typelist: ['equity', 'equityhk', 'equitywithhk'],
			// 	type: 'big_template',
			// 	isshow: true,
			// 	getData: 'getFundInnovationData',
			// 	getRequestData: 'getIPOStockAnalysis'
			// },
			// {
			// 	name: '规模及持有人结构',
			// 	typelist: ['fof'],
			// 	is: 'sizeStructure',
			// 	value: 'sizeStructure',
			// 	type: 'big_template',
			// 	isshow: true,
			// 	getData: 'getSizeStructureData',
			// 	getRequestData: 'getMoneyScale'
			// }
		]
	},
	{
		label: '风格分析',
		key: 'styleAnalysis',
		class: ['equity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex'],
		templateList: [
			{
				name: '风格标签',
				is: 'styleLabel',
				value: 'styleLabel',
				type: 'small_template',
				typelist: ['equity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex'],
				isshow: true,
				getData: 'getStyleInfoData',
				getRequestData: 'getStyleInfo'
			},
			{
				name: '持仓风格',
				is: 'positionStyle',
				value: 'positionStyle',
				type: 'small_template',
				typelist: ['equity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex'],
				isshow: true,
				getData: 'getHoldStyleData',
				getRequestData: 'getHoldStyle'
			},
			{
				name: '交易风格',
				is: 'tradingStyle',
				value: 'tradingStyle',
				type: 'big_template',
				typelist: ['equity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex'],
				isshow: true,
				getData: 'getConcentrationInfoData',
				getRequestData: 'getConcentrationInfo'
			},
			{
				name: '动态4因子分析',
				is: 'dynamicFourFactorAnalysis',
				value: 'dynamicFourFactorAnalysis',
				typelist: ['equityhk-index', 'equityenhance', 'equityindex'],
				type: 'small_template',
				isshow: true,
				getData: 'getDynamicFourFactorAnalysisData',
				getRequestData: 'getDynamicAnalysis'
			},
			{
				name: '动态4因子统计',
				is: 'dynamicFourFactorStatistics',
				value: 'dynamicFourFactorStatistics',
				typelist: ['equityhk-index', 'equityenhance', 'equityindex'],
				type: 'small_template',
				isshow: true,
				getData: 'getDynamicFourFactorStatisticsData',
				getRequestData: 'getDynamicStatistics'
			}
		]
	},
	{
		label: '能力分析',
		key: 'abilityAnalysis',
		class: ['equity', 'equitywithhk', 'equityhk'],
		templateList: [
			{
				name: '超额收益归因',
				is: 'excessReturnAttribution',
				value: 'excessReturnAttribution',
				type: 'big_template',
				typelist: ['equity', 'equitywithhk', 'equityhk'],
				isshow: true,
				getData: 'getExcessReturnDecompositionData',
				getRequestData: 'getExcessReturnDecomposition'
			},
			{
				name: '行业配置表现',
				is: 'industryAllocationPerformance',
				value: 'industryAllocationPerformance',
				type: 'big_template',
				typelist: ['equity', 'equitywithhk', 'equityhk'],
				isshow: true,
				getData: 'getIndustryInfoData',
				getRequestData: 'getIndustryInfo'
			},
			{
				name: '动态4因子分析',
				is: 'dynamicFourFactorAnalysis',
				value: 'dynamicFourFactorAnalysis',
				typelist: ['equity', 'equitywithhk', 'equityhk'],
				type: 'small_template',
				isshow: true,
				getData: 'getDynamicFourFactorAnalysisData',
				getRequestData: 'getDynamicAnalysis'
			},
			{
				name: '动态4因子统计',
				is: 'dynamicFourFactorStatistics',
				value: 'dynamicFourFactorStatistics',
				typelist: ['equity', 'equitywithhk', 'equityhk'],
				type: 'small_template',
				isshow: true,
				getData: 'getDynamicFourFactorStatisticsData',
				getRequestData: 'getDynamicStatistics'
			},
			{
				name: 'TM模型分析',
				is: 'TMModelAnalysis',
				value: 'TMModelAnalysis',
				typelist: ['equity', 'equitywithhk', 'equityhk'],
				type: 'big_template',
				isshow: true,
				getData: 'getTMModelAnalysisData',
				getRequestData: 'getTMStatistics'
			},
			{
				name: 'PB-ROE分析',
				is: 'PBROEcharacteristics',
				value: 'PBROEcharacteristics',
				type: 'big_template',
				typelist: ['equity', 'equitywithhk', 'equityhk'],
				isshow: true,
				getData: 'getCharacteristicsData',
				getRequestData: 'getCharacteristics'
			},
			{
				name: '买入特征',
				is: 'buyingCharacteristics',
				value: 'buyingCharacteristics',
				type: 'small_template',
				typelist: ['equity', 'equitywithhk', 'equityhk'],
				isshow: true,
				getData: 'getBuyingCharacteristicsData',
				getRequestData: 'getBuyingCharacteristics'
			},
			{
				name: '卖出特征',
				is: 'sellingCharacteristics',
				value: 'sellingCharacteristics',
				type: 'small_template',
				typelist: ['equity', 'equitywithhk', 'equityhk'],
				isshow: true,
				getData: 'getSellingCharacteristicsData',
				getRequestData: 'getSellingCharacteristics'
			},
			{
				name: '六种买入模式',
				is: 'sixBuyingModes',
				value: 'sixBuyingModes',
				type: 'big_template',
				typelist: ['equity', 'equitywithhk', 'equityhk'],
				isshow: true,
				getData: 'getBuyModInfoData',
				getRequestData: 'getBuyModInfo'
			},
			{
				name: '六种卖出模式',
				is: 'sixSellingModes',
				value: 'sixSellingModes',
				type: 'big_template',
				typelist: ['equity', 'equitywithhk', 'equityhk'],
				isshow: true,
				getData: 'getSellModInfoData',
				getRequestData: 'getSellModInfo'
			},
			{
				name: '调仓节奏',
				is: 'warehouseAdjustmentRhythm',
				value: 'warehouseAdjustmentRhythm',
				type: 'big_template',
				typelist: ['equity', 'equitywithhk', 'equityhk'],
				isshow: true,
				getData: 'getWarehouseAdjustmentData',
				getRequestData: 'getWarehouseAdjustment'
			},
			{
				name: '前十大的进击',
				is: 'topTenAttacks',
				value: 'topTenAttacks',
				type: 'big_template',
				typelist: ['equity', 'equitywithhk', 'equityhk'],
				isshow: true,
				getData: 'getTopTenAttacksData',
				getRequestData: 'getTopTenAttacks'
			}
		]
	},
	{
		label: '权益分析',
		key: 'equityAnalysis',
		class: ['equity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex', 'bond', 'obond'],
		templateList: [
			{
				name: '股票持仓分析',
				is: 'stocksReportPosition',
				value: 'stocksReportPosition',
				type: 'big_template',
				isshow: true,
				typelist: ['equity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex', 'bond', 'obond', 'bill'],
				isshow: true,
				getData: 'getHoldStocks',
				getRequestData: 'getDateList'
			},
			{
				name: '估值分析',
				is: 'valuationAnalysis',
				value: 'valuationAnalysis',
				typelist: ['equity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex', 'bond', 'obond', 'bill'],
				type: 'big_template',
				isshow: true,
				getData: 'getValuationAnalysisData',
				getRequestData: 'getValuationAnalysis'
			},
			{
				name: 'PB-ROE估值',
				is: 'PBROEvaluation',
				value: 'PBROEvaluation',
				typelist: ['equity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex', 'bond', 'obond', 'bill'],
				type: 'big_template',
				isshow: true,
				getData: 'getStocksValuationInfoData',
				getRequestData: 'getStocksValuationInfo'
			},
			{
				name: '报告期持仓统计',
				is: 'fundAssetAllocationAnalysis',
				value: 'fundAssetAllocationAnalysis',
				type: 'big_template',
				typelist: ['equity', 'equitywithhk', 'equityhk'],
				isshow: true,
				getData: 'getIndexListData',
				getRequestData: 'getIndexList'
			},
			{
				name: '长期持股行业分布',
				is: 'longTermShareholdingIndustry',
				value: 'longTermShareholdingIndustry',
				typelist: ['equity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex', 'bond', 'obond', 'bill'],
				type: 'big_template',
				isshow: true,
				getData: 'getStocksLongHoldData',
				getRequestData: 'getStocksLongHold'
			},
			{
				name: '长期持有个股',
				is: 'longTermHoldingShares',
				value: 'longTermHoldingShares',
				typelist: ['equity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex', 'bond', 'obond', 'bill'],
				type: 'big_template',
				isshow: true,
				getData: 'getStocksLongHoldData',
				getRequestData: 'getStocksLongHold'
			},
			{
				name: '持股加权估值水平',
				is: 'shareholdingWeightedValuation',
				value: 'shareholdingWeightedValuation',
				typelist: ['equity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex', 'bond', 'obond', 'bill'],
				type: 'big_template',
				isshow: true,
				getData: 'getStockHistoryHoldData',
				getRequestData: 'getStockHistoryHold'
			},
			{
				name: '持仓加权盈利能力',
				is: 'positionWeightedProfitability',
				value: 'positionWeightedProfitability',
				typelist: ['equity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex', 'bond', 'obond', 'bill'],
				type: 'big_template',
				isshow: true,
				getData: 'getPositionWeightedData',
				getRequestData: 'getPositionWeighted'
			}
		]
	},
	{
		label: '资配分析',
		key: 'assetAllocationAnalysis',
		class: ['bill', 'bond', 'obond', 'cbond', 'purebond', 'fof'],
		templateList: [
			{
				name: '基金资产配置分析',
				is: 'fundAssetAllocationAnalysis',
				value: 'fundAssetAllocationAnalysis',
				type: 'big_template',
				typelist: ['bill', 'bond', 'obond', 'cbond', 'purebond', 'fof'],
				isshow: true,
				methods: 'getStatisticsIndexCode',
				getData: 'getIndexListData',
				getRequestData: 'getIndexList'
			},
			{
				name: '持仓分类',
				is: 'positionClass',
				value: 'positionClass',
				type: 'big_template',
				typelist: ['purebond'],
				isshow: true,
				getData: 'getPositionClassData',
				getRequestData: 'getPositionClass'
			},
			{
				name: '基金持仓分析',
				is: 'fundPositionAnalysis',
				value: 'fundPositionAnalysis',
				type: 'big_template',
				typelist: ['fof'],
				isshow: true,
				getData: 'getFundPositionAnalysisData',
				getRequestData: 'getFundPositionAnalysis'
			},
			{
				name: '最新各类型基金配置情况',
				is: 'newFundAllocation',
				value: 'newFundAllocation',
				type: 'big_template',
				typelist: ['fof'],
				isshow: true,
				getData: 'getNewFundAllocationData',
				getRequestData: 'getNewFundAllocation'
			},
			// 计算不准确、隐藏
			// {
			// 	name: '权益基金alpha/beta/smartbeta分解',
			// 	is: 'equityFundDecompose',
			// 	value: 'equityFundDecompose',
			// 	type: 'big_template',
			// 	typelist: ['fof'],
			// 	isshow: true,
			// 	getData: 'getFofAlphaRankData',
			// 	getRequestData: 'getFofAlphaRank'
			// },
			{
				name: '权益基金标签分析',
				is: 'equityFundTagAnalysis',
				value: 'equityFundTagAnalysis',
				type: 'big_template',
				typelist: ['fof'],
				isshow: true,
				getData: 'getFoFEquityTagData',
				getRequestData: 'getFoFEquityTag'
			},
			{
				name: '权益基金股票持仓分析',
				is: 'equityStockPositionAnalysis',
				value: 'equityStockPositionAnalysis',
				type: 'big_template',
				typelist: ['fof'],
				isshow: true,
				getData: 'getFoFHoldingNewestData',
				getRequestData: 'getFoFHoldingNewest'
			},
			{
				name: '长期持股行业分布',
				is: 'longTermShareholdingIndustry',
				value: 'longTermShareholdingIndustry',
				type: 'big_template',
				typelist: ['fof'],
				isshow: true,
				getData: 'getStocksLongHoldData',
				getRequestData: 'getStocksLongHold'
			},
			{
				name: '长期持有个股',
				is: 'longTermHoldingShares',
				value: 'longTermHoldingShares',
				type: 'big_template',
				typelist: ['fof'],
				isshow: true,
				getData: 'getStocksLongHoldData',
				getRequestData: 'getStocksLongHold'
			},
			// 计算不准确、隐藏
			// {
			// 	name: '直投股票capm分析',
			// 	is: 'capmStockAnalysis',
			// 	value: 'capmStockAnalysis',
			// 	type: 'big_template',
			// 	typelist: ['fof'],
			// 	isshow: true,
			// 	methods: 'getCapmAnalysis',
			// 	getData: 'getCapmBenchmarkData',
			// 	getRequestData: 'getCapmBenchmark'
			// },
			{
				name: '行业配置表现',
				is: 'industryAllocationPerformance',
				value: 'industryAllocationPerformance',
				type: 'big_template',
				typelist: ['bond', 'obond'],
				isshow: true,
				getData: 'getIndustryInfoData',
				getRequestData: 'getIndustryInfo'
			},
			{
				name: '持仓债券分析',
				is: 'bondReportPosition',
				value: 'bondReportPosition',
				type: 'big_template',
				typelist: ['bill', 'bond', 'obond', 'cbond', 'purebond', 'fof'],
				isshow: true,
				getData: 'getBondAnalysise',
				getRequestData: 'getDateList'
			},
			{
				name: '风格择时能力',
				is: 'styleTimingAbility',
				value: 'styleTimingAbility',
				type: 'big_template',
				typelist: ['bill', 'bond', 'obond', 'purebond'],
				isshow: true,
				getData: 'getStyleTimingData',
				getRequestData: 'getStyleTiming'
			},
			{
				name: '权益分析(直投权益+间接投资权益)',
				is: 'fundTypeAnalysis',
				value: 'fundTypeAnalysisequity',
				type: 'big_template',
				typelist: ['fof'],
				getData: 'getIndexBasicMsgEquityData',
				getRequestData: 'getIndexBasicMsgEquity'
			},
			// {
			// 	name: '纯债分析(直投纯债+间接投资纯债)',
			// 	is: 'fundTypeAnalysis',
			// 	value: 'fundTypeAnalysispurebond',
			// 	type: 'big_template',
			// 	typelist: ['fof'],
			// 	isshow: true,
			// 	getData: 'getIndexBasicMsgPureBondData',
			// 	getRequestData: 'getIndexBasicMsgPureBond'
			// },
			{
				name: '转债分析(直投转债+间接投资转债)',
				is: 'fundTypeAnalysis',
				value: 'fundTypeAnalysiscbond',
				type: 'big_template',
				typelist: ['fof'],
				isshow: true,
				getData: 'getIndexBasicMsgCbondData',
				getRequestData: 'getIndexBasicMsgCbond'
			},
			// {
			// 	name: '商品分析(放大权重至100%)',
			// 	is: 'fundTypeAnalysis',
			// 	value: 'fundTypeAnalysiscommodity',
			// 	type: 'big_template',
			// 	typelist: ['fof'],
			// 	isshow: true,
			// 	getData: 'getIndexBasicMsgCommodityData',
			// 	getRequestData: 'getIndexBasicMsgCommodity'
			// },
			// {
			// 	name: '对冲资产分析(放大权重至100%)',
			// 	is: 'fundTypeAnalysis',
			// 	value: 'fundTypeAnalysisneutral',
			// 	type: 'big_template',
			// 	typelist: ['fof'],
			// 	isshow: true,
			// 	getData: 'getIndexBasicMsgNeutralData',
			// 	getRequestData: 'getIndexBasicMsgNeutral'
			// },
			{
				name: '短期流动性管理',
				is: 'mobilityManage',
				value: 'mobilityManage',
				type: 'big_template',
				typelist: ['fof'],
				isshow: true,
				getData: 'getFofLiquidityData',
				getRequestData: 'getFofLiquidity'
			},
			{
				name: 'What-if 假想验证：调仓时的资产配置作对了吗',
				is: 'whatIfAssetAllocation',
				value: 'whatIfAssetAllocation',
				type: 'big_template',
				typelist: ['bond', 'obond'],
				isshow: true,
				getData: 'getWhatIfAssetAllocationData',
				getRequestData: 'getWhatIf'
			},
			{
				name: 'What-if 假想验证：能否打败固定比例再平衡虚拟基准',
				is: 'whatIfBeatFixedProportion',
				value: 'whatIfBeatFixedProportion',
				type: 'big_template',
				typelist: ['bond', 'obond'],
				isshow: true,
				getData: 'getWhatIfBeatFixedProportionData',
				getRequestData: 'getWhatIfBeatFixedProportion'
			}
		]
	},
	{
		label: '债券分析',
		key: 'debtAnalysis',
		class: ['bill', 'bond', 'obond', 'cbond', 'purebond'],
		templateList: [
			{
				name: '转债季度信息',
				is: 'bondQuarterInfo',
				value: 'bondQuarterInfo',
				type: 'big_template',
				typelist: ['bill', 'bond', 'obond', 'cbond', 'purebond'],
				isshow: true,
				getData: 'getBondQuarterInfoData',
				getRequestData: 'getBondQuarterInfo'
			},
			{
				name: '转债PLUS',
				is: 'cbondReturnPlus',
				value: 'cbondReturnPlusCBond',
				type: 'big_template',
				typelist: ['bill', 'bond', 'obond', 'cbond', 'purebond'],
				isshow: true,
				methods: 'getCbondReturnsCBond',
				getData: 'getCbondReturnsCBondData',
				getRequestData: 'getCbondReturnsCBond'
			},
			{
				name: '股票PLUS',
				is: 'cbondReturnPlus',
				value: 'cbondReturnPlusEquity',
				type: 'big_template',
				typelist: ['bill', 'bond', 'obond', 'cbond', 'purebond'],
				isshow: true,
				methods: 'getCbondReturnsEquity',
				getData: 'getCbondReturnsEquityData',
				getRequestData: 'getCbondReturnsEquity'
			},
			{
				name: '转债正股风格',
				is: 'cbondStyle',
				value: 'cbondStyle',
				type: 'big_template',
				typelist: ['bill', 'bond', 'obond', 'cbond', 'purebond'],
				isshow: true,
				getData: 'getcbondStyleData',
				getRequestData: 'getcbondStyle'
			},
			{
				name: '转债股性债性概览',
				is: 'cbondHoldEquityBond',
				value: 'cbondHoldEquityBond',
				type: 'big_template',
				typelist: ['bill', 'bond', 'obond', 'cbond', 'purebond'],
				isshow: true,
				getData: 'getcbondHoldEquityBondData',
				getRequestData: 'getcbondHoldEquityBond'
			},
			{
				name: '六种买入模式',
				is: 'sixBuyingModes',
				value: 'sixBuyingModes',
				type: 'big_template',
				typelist: ['cbond'],
				isshow: true,
				getData: 'getBuyModInfoData',
				getRequestData: 'getBuyModInfo'
			},
			{
				name: '六种卖出模式',
				is: 'sixSellingModes',
				value: 'sixSellingModes',
				type: 'big_template',
				typelist: ['cbond'],
				isshow: true,
				getData: 'getSellModInfoData',
				getRequestData: 'getSellModInfo'
			},
			{
				name: '久期拉长',
				is: 'prolongedDuration',
				value: 'prolongedDuration',
				type: 'big_template',
				isshow: true,
				typelist: ['bill', 'bond', 'obond', 'cbond', 'purebond'],
				isshow: true,
				getData: 'getDurationAnalysisInfoData',
				getRequestData: 'getDurationAnalysisInfo'
			},
			{
				name: '信用挖掘',
				is: 'creditMining',
				value: 'creditMining',
				type: 'big_template',
				isshow: true,
				typelist: ['bill', 'bond', 'obond', 'cbond', 'purebond'],
				isshow: true,
				getData: 'getCreditDownRationData',
				getRequestData: 'getCreditDownRation'
			},
			{
				name: '基金报告期披露信用债评级分布(近年)',
				is: 'creditBondRatingDistribution',
				value: 'creditBondRatingDistribution',
				type: 'big_template',
				isshow: true,
				typelist: ['bill', 'bond', 'obond', 'cbond', 'purebond'],
				isshow: true,
				getData: 'getCreditLevelData',
				getRequestData: 'getCreditLevel'
			}
		]
	},
	// {
	// 	label: '基金筛选能力',
	// 	key: 'fundScreeningAbility',
	// 	class: ['fof'],
	// 	templateList: [
	// 		{
	// 			name: '权益类子基金vs 比较基准',
	// 			is: 'childrenFundBenchmark',
	// 			value: 'childrenFundBenchmarkequity',
	// 			type: 'big_template',
	// 			typelist: ['fof'],
	// 			isshow: true,
	// 			getData: 'getChildrenFundBenchmarkEquityData',
	// 			getRequestData: 'getChildrenFundBenchmarkEquity'
	// 		},
	// 		{
	// 			name: '转债类子基金vs 比较基准',
	// 			is: 'childrenFundBenchmark',
	// 			value: 'childrenFundBenchmarkbond',
	// 			type: 'big_template',
	// 			typelist: ['fof'],
	// 			isshow: true,
	// 			getData: 'getChildrenFundBenchmarkBondData',
	// 			getRequestData: 'getChildrenFundBenchmarkBond'
	// 		},
	// 		{
	// 			name: '纯债类子基金vs 比较基准',
	// 			is: 'childrenFundBenchmark',
	// 			value: 'childrenFundBenchmarkpurebond',
	// 			type: 'big_template',
	// 			typelist: ['fof'],
	// 			isshow: true,
	// 			getData: 'getChildrenFundBenchmarkPurebondData',
	// 			getRequestData: 'getChildrenFundBenchmarkPurebond'
	// 		}
	// 	]
	// },
	{
		label: '声音',
		key: 'voice',
		class: [
			'equity',
			'equitywithhk',
			'equityhk',
			'equityhk-index',
			'equityenhance',
			'equityindex',
			'bond',
			'obond',
			'cbond',
			'bill',
			'purebond',
			'fof'
		],
		templateList: [
			{
				name: '声音',
				is: 'voiceData',
				value: 'voiceData',
				type: 'big_template',
				typelist: [
					'equity',
					'equitywithhk',
					'equityhk',
					'equityhk-index',
					'equityenhance',
					'equityindex',
					'bond',
					'obond',
					'cbond',
					'bill',
					'purebond',
					'fof'
				],
				isshow: true,
				getData: 'getMarketingData',
				getRequestData: 'getMarketing'
			}
		]
	}
];
export let managerComponentsList = [
	{
		label: '一页通',
		key: 'onePagePass',
		class: ['activeequity', 'hkequity', 'bond', 'obond', 'cbond', 'bill', 'purebond'],
		templateList: [
			{
				name: '通用模版',
				is: 'globalTemplate',
				value: 'globalTemplate',
				type: 'big_template',
				typelist: ['activeequity', 'hkequity', 'bond', 'obond', 'cbond', 'bill', 'purebond'],
				isshow: true,
				getData: 'getGlobalTemplateData',
				getRequestData: 'getGlobalTemplate'
			},
			// 主动权益
			{
				name: '主动权益代表基金',
				is: 'representativeFund',
				value: 'representativeFundactiveequity',
				type: 'big_template',
				typelist: ['activeequity'],
				isshow: true,
				getRequestData: 'getRepresentativeFund',
				getData: 'getRepresentativeFundData'
			},
			{
				name: '主动权益概述',
				is: 'activeEquitySummary',
				value: 'activeEquitySummaryactiveequity',
				type: 'small_template',
				typelist: ['activeequity'],
				isshow: true,
				getData: 'getSummaryData',
				getRequestData: 'getSummary'
			},
			{
				name: '业绩与能力评价',
				is: 'performanceCapabilityEvaluation',
				value: 'performanceCapabilityEvaluationactiveequity',
				type: 'small_template',
				typelist: ['activeequity'],
				isshow: true,
				getData: 'getPerformanceCapabilityEvaluationData',
				getRequestData: 'getPerformanceCapabilityEvaluation'
			},
			{
				name: '表现风格',
				is: 'performanceStyle',
				value: 'performanceStyleactiveequity',
				type: 'small_template',
				typelist: ['activeequity'],
				isshow: true,
				getData: 'getPerformanceStyleData',
				getRequestData: 'getPerformanceStyle'
			},
			{
				name: '近期公告风格',
				is: 'recentAnnouncementStyle',
				value: 'recentAnnouncementStyleactiveequity',
				type: 'small_template',
				typelist: ['activeequity'],
				isshow: true,
				getData: 'getRecentAnnouncementStyleData',
				getRequestData: 'getRecentAnnouncementStyle'
			},
			{
				name: '操盘风格',
				is: 'holdingStyle',
				value: 'holdingStyleactiveequity',
				type: 'small_template',
				typelist: ['activeequity'],
				isshow: true,
				getData: 'getholdingStyleData',
				getRequestData: 'getholdingStyle'
			},
			{
				name: '行业能力&&市场适应性',
				is: 'industryCapacityAndMarketAdaptability',
				value: 'industryCapacityAndMarketAdaptabilityactiveequity',
				type: 'big_template',
				typelist: ['activeequity'],
				isshow: true,
				getData: 'getIndustryCapacityAndMarketAdaptabilityData',
				getRequestData: 'getIndustryCapacityAndMarketAdaptability'
			},
			{
				name: '行业评价',
				is: 'industryEvaluation',
				value: 'industryEvaluationactiveequity',
				type: 'big_template',
				typelist: ['activeequity'],
				isshow: true,
				getData: 'getIndustryEvaluationData',
				getRequestData: 'getIndustryDetlaCapability'
			},
			{
				name: '近次披露特征',
				is: 'characteristicsRecentDisclosure',
				value: 'characteristicsRecentDisclosureactiveequity',
				type: 'big_template',
				typelist: ['activeequity'],
				isshow: true,
				getData: 'getCharacteristicsRecentDisclosureData',
				getRequestData: 'getCharacteristicsRecentDisclosure'
			},
			{
				name: '近次披露特征Barra因子分析',
				is: 'characteristicsBarra',
				value: 'characteristicsBarraactiveequity',
				type: 'small_template',
				typelist: ['activeequity'],
				isshow: true,
				getData: 'getCharacteristicsBarraData',
				getRequestData: 'getCharacteristicsBarra'
			},
			{
				name: '近30交易日推算持仓行业占比',
				is: 'industryShareThirty',
				value: 'industryShareThirtyactiveequity',
				type: 'small_template',
				typelist: ['activeequity'],
				isshow: true,
				getData: 'getIndustryShareThirtyData',
				getRequestData: 'getIndustryShareThirty'
			},
			// 港股
			{
				name: '港股代表基金',
				is: 'representativeFund',
				value: 'representativeFundhkequity',
				type: 'big_template',
				typelist: ['hkequity'],
				isshow: true,
				getRequestData: 'getRepresentativeFund',
				getData: 'getRepresentativeFundData'
			},
			{
				name: '港股概述',
				is: 'activeEquitySummary',
				value: 'activeEquitySummaryhkequity',
				type: 'small_template',
				typelist: ['hkequity'],
				isshow: true,
				getData: 'getSummaryData',
				getRequestData: 'getSummary'
			},
			{
				name: '业绩与能力评价',
				is: 'performanceCapabilityEvaluation',
				value: 'performanceCapabilityEvaluationhkequity',
				type: 'small_template',
				typelist: ['hkequity'],
				isshow: true,
				getData: 'getPerformanceCapabilityEvaluationData',
				getRequestData: 'getPerformanceCapabilityEvaluation'
			},
			{
				name: '表现风格',
				is: 'performanceStyle',
				value: 'performanceStylehkequity',
				type: 'small_template',
				typelist: ['hkequity'],
				isshow: true,
				getData: 'getPerformanceStyleData',
				getRequestData: 'getPerformanceStyle'
			},
			{
				name: '近期公告风格',
				is: 'recentAnnouncementStyle',
				value: 'recentAnnouncementStylehkequity',
				type: 'small_template',
				typelist: ['hkequity'],
				isshow: true,
				getData: 'getRecentAnnouncementStyleData',
				getRequestData: 'getRecentAnnouncementStyle'
			},
			{
				name: '操盘风格',
				is: 'holdingStyle',
				value: 'holdingStylehkequity',
				type: 'small_template',
				typelist: ['hkequity'],
				isshow: true,
				getData: 'getholdingStyleData',
				getRequestData: 'getholdingStyle'
			},
			{
				name: '行业能力&&市场适应性',
				is: 'industryCapacityAndMarketAdaptability',
				value: 'industryCapacityAndMarketAdaptabilityhkequity',
				type: 'big_template',
				typelist: ['hkequity'],
				isshow: true,
				getData: 'getIndustryCapacityAndMarketAdaptabilityData',
				getRequestData: 'getIndustryCapacityAndMarketAdaptability'
			},
			{
				name: '行业评价',
				is: 'industryEvaluation',
				value: 'industryEvaluationhkequity',
				type: 'big_template',
				typelist: ['hkequity'],
				isshow: true,
				getData: 'getIndustryEvaluationData',
				getRequestData: 'getIndustryDetlaCapability'
			},
			{
				name: '近次披露特征',
				is: 'characteristicsRecentDisclosure',
				value: 'characteristicsRecentDisclosurehkequity',
				type: 'big_template',
				typelist: ['hkequity'],
				isshow: true,
				getData: 'getCharacteristicsRecentDisclosureData',
				getRequestData: 'getCharacteristicsRecentDisclosure'
			},
			{
				name: '近次披露特征Barra因子分析',
				is: 'characteristicsBarra',
				value: 'characteristicsBarrahkequity',
				type: 'small_template',
				typelist: ['hkequity'],
				isshow: true,
				getData: 'getCharacteristicsBarraData',
				getRequestData: 'getCharacteristicsBarra'
			},
			{
				name: '近30交易日推算持仓行业占比',
				is: 'industryShareThirty',
				value: 'industryShareThirtyhkequity',
				type: 'small_template',
				typelist: ['hkequity'],
				isshow: true,
				getData: 'getIndustryShareThirtyData',
				getRequestData: 'getIndustryShareThirty'
			},
			// 固收+
			{
				name: '固收+代表基金',
				is: 'representativeFund',
				value: 'representativeFundbond',
				type: 'big_template',
				typelist: ['bond'],
				isshow: true,
				getRequestData: 'getRepresentativeFund',
				getData: 'getRepresentativeFundData'
			},
			{
				name: '固收+概述',
				is: 'activeEquitySummary',
				value: 'activeEquitySummarybond',
				type: 'small_template',
				typelist: ['bond'],
				isshow: true,
				getData: 'getSummaryData',
				getRequestData: 'getSummary'
			},
			{
				name: '业绩与能力评价',
				is: 'performanceCapabilityEvaluation',
				value: 'performanceCapabilityEvaluationbond',
				type: 'small_template',
				typelist: ['bond'],
				isshow: true,
				getData: 'getPerformanceCapabilityEvaluationData',
				getRequestData: 'getPerformanceCapabilityEvaluation'
			},
			{
				name: '操盘风格',
				is: 'holdStyle',
				value: 'holdStylebond',
				type: 'small_template',
				typelist: ['bond'],
				isshow: true,
				getData: 'getholdingStyleData',
				getRequestData: 'getholdingStyle'
			},
			{
				name: '表现风格',
				is: 'performanceStyleManager',
				value: 'performanceStyleManagerbond',
				type: 'small_template',
				typelist: ['bond'],
				isshow: true,
				getData: 'getBondManagerMethodsData',
				getRequestData: 'getBondManagerMethods'
			},
			{
				name: '市场风格表现',
				is: 'stockDebtSynthesis',
				value: 'stockDebtSynthesisbond',
				type: 'small_template',
				typelist: ['bond'],
				isshow: true,
				getData: 'getStockDebtSynthesisData',
				getRequestData: 'getStockDebtSynthesis'
			},
			{
				name: '固收+权益仓位',
				is: 'fixedIncomeEquityPosition',
				value: 'fixedIncomeEquityPositionbond',
				type: 'small_template',
				typelist: ['bond'],
				isshow: true,
				getData: 'getFixedIncomeEquityPositionData',
				getRequestData: 'getFixedIncomeEquityPosition'
			},
			{
				name: '权益中枢及标准差',
				is: 'equityCenterStandardDeviation',
				value: 'equityCenterStandardDeviationbond',
				type: 'small_template',
				typelist: ['bond'],
				isshow: true,
				getData: 'getEquityCenterStandardDeviationData',
				getRequestData: 'getEquityCenterStandardDeviation'
			},
			{
				name: '等效ß',
				is: 'equivalentBeta',
				value: 'equivalentBetabond',
				type: 'small_template',
				typelist: ['bond'],
				isshow: true,
				getData: 'getEquivalentBetaData',
				getRequestData: 'getEquivalentBeta'
			},
			{
				name: '固收+的加法描述',
				is: 'additiveDescription',
				value: 'additiveDescriptionbond',
				type: 'small_template',
				typelist: ['bond'],
				isshow: true,
				getData: 'getAdditiveDescriptionData',
				getRequestData: 'getAdditiveDescription'
			},
			// 可转债
			{
				name: '可转债代表基金',
				is: 'representativeFund',
				value: 'representativeFundcbond',
				type: 'big_template',
				typelist: ['cbond'],
				isshow: true,
				getRequestData: 'getRepresentativeFund',
				getData: 'getRepresentativeFundData'
			},
			{
				name: '可转债概述',
				is: 'activeEquitySummary',
				value: 'activeEquitySummarycbond',
				type: 'small_template',
				typelist: ['cbond'],
				isshow: true,
				getData: 'getSummaryData',
				getRequestData: 'getSummary'
			},
			{
				name: '业绩与能力评价',
				is: 'performanceCapabilityEvaluation',
				value: 'performanceCapabilityEvaluationcbond',
				type: 'small_template',
				typelist: ['cbond'],
				isshow: true,
				getData: 'getPerformanceCapabilityEvaluationData',
				getRequestData: 'getPerformanceCapabilityEvaluation'
			},
			{
				name: '操盘风格',
				is: 'holdStyle',
				value: 'holdStylecbond',
				type: 'small_template',
				typelist: ['cbond'],
				isshow: true,
				getData: 'getholdingStyleData',
				getRequestData: 'getholdingStyle'
			},
			// 中短债
			{
				name: '中短债代表基金',
				is: 'representativeFund',
				value: 'representativeFundbill',
				type: 'big_template',
				typelist: ['bill'],
				isshow: true,
				getRequestData: 'getRepresentativeFund',
				getData: 'getRepresentativeFundData'
			},
			{
				name: '中短债概述',
				is: 'activeEquitySummary',
				value: 'activeEquitySummarybill',
				type: 'small_template',
				typelist: ['bill'],
				isshow: true,
				getData: 'getSummaryData',
				getRequestData: 'getSummary'
			},
			{
				name: '业绩与能力评价',
				is: 'performanceCapabilityEvaluation',
				value: 'performanceCapabilityEvaluationbill',
				type: 'small_template',
				typelist: ['bill'],
				isshow: true,
				getData: 'getPerformanceCapabilityEvaluationData',
				getRequestData: 'getPerformanceCapabilityEvaluation'
			},
			{
				name: '操盘风格',
				is: 'holdStyle',
				value: 'holdStylebill',
				type: 'small_template',
				typelist: ['bill'],
				isshow: true,
				getData: 'getholdingStyleData',
				getRequestData: 'getholdingStyle'
			},
			{
				name: '市场风格表现',
				typelist: ['bill'],
				is: 'stockDebtSynthesis',
				value: 'stockDebtSynthesisbill',
				type: 'small_template',
				isshow: true,
				getData: 'getStockDebtSynthesisData',
				getRequestData: 'getStockDebtSynthesis'
			},
			// 纯债
			{
				name: '纯债代表基金',
				is: 'representativeFund',
				value: 'representativeFundpurebond',
				type: 'big_template',
				typelist: ['purebond'],
				isshow: true,
				getRequestData: 'getRepresentativeFund',
				getData: 'getRepresentativeFundData'
			},
			{
				name: '纯债概述',
				is: 'activeEquitySummary',
				value: 'activeEquitySummarypurebond',
				type: 'small_template',
				typelist: ['purebond'],
				isshow: true,
				getData: 'getSummaryData',
				getRequestData: 'getSummary'
			},
			{
				name: '业绩与能力评价',
				is: 'performanceCapabilityEvaluation',
				value: 'performanceCapabilityEvaluationpurebond',
				type: 'small_template',
				typelist: ['purebond'],
				isshow: true,
				getData: 'getPerformanceCapabilityEvaluationData',
				getRequestData: 'getPerformanceCapabilityEvaluation'
			},
			{
				name: '操盘风格',
				is: 'holdStyle',
				value: 'holdStylepurebond',
				type: 'small_template',
				typelist: ['purebond'],
				isshow: true,
				getData: 'getholdingStyleData',
				getRequestData: 'getholdingStyle'
			},
			{
				name: '市场风格表现',
				typelist: ['purebond'],
				is: 'stockDebtSynthesis',
				value: 'stockDebtSynthesispurebond',
				type: 'small_template',
				isshow: true,
				getData: 'getStockDebtSynthesisData',
				getRequestData: 'getStockDebtSynthesis'
			}
		]
	},
	{
		label: '主动权益',
		key: 'activeequity',
		class: ['activeequity'],
		templateList: [
			{
				name: '管理产品',
				is: 'managementProducts',
				value: 'managementProducts',
				type: 'big_template',
				typelist: ['activeequity'],
				isshow: true,
				getData: 'getManagedFundsData',
				getRequestData: 'getManagedFunds'
			},
			{
				name: '收益率分布直方图',
				is: 'distributionReturn',
				value: 'distributionReturn',
				type: 'big_template',
				typelist: ['activeequity'],
				isshow: true,
				getData: 'getDistributionReturnData',
				getRequestData: 'getDistributionReturn'
			},
			// {
			// 	name: '收益与基准比较',
			// 	is: 'revenueVsBenchmark',
			// 	value: 'revenueVsBenchmark',
			// 	type: 'small_template',
			// 	typelist: ['activeequity'],
			// 	isshow: true,
			// 	getData: 'getBondManagerMsgData',
			// 	getRequestData: 'getBondManagerMsg'
			// },
			{
				name: '风险收益关系',
				is: 'riskReturnRelationship',
				value: 'riskReturnRelationship',
				type: 'small_template',
				typelist: ['activeequity'],
				isshow: true,
				getData: 'getBestBenchmarksData',
				getRequestData: 'getBestBenchmarks'
			},
			{
				name: '分时段业绩表现',
				is: 'timePhasedPerformance',
				value: 'timePhasedPerformance',
				type: 'big_template',
				typelist: ['activeequity'],
				isshow: true,
				getData: 'getMarketWindowReturnData',
				getRequestData: 'getFundPeriod'
			},
			{
				name: '股票持仓分析',
				is: 'stocksReportPosition',
				value: 'stocksReportPosition',
				type: 'big_template',
				isshow: true,
				typelist: ['activeequity'],
				isshow: true,
				getData: 'getHoldStocks',
				getRequestData: 'getDateList'
			},
			{
				name: '估值分析',
				is: 'valuationAnalysis',
				value: 'valuationAnalysis',
				type: 'big_template',
				typelist: ['activeequity'],
				isshow: true,
				getData: 'getValuationAnalysisData',
				getRequestData: 'getValuationAnalysis'
			},
			{
				name: 'PB-ROE估值',
				is: 'PBROEvaluation',
				value: 'PBROEvaluation',
				type: 'big_template',
				typelist: ['activeequity'],
				isshow: true,
				getData: 'getStocksValuationInfoData',
				getRequestData: 'getStocksValuationInfo'
			},
			{
				name: '长期持股行业分布',
				is: 'longTermShareholdingIndustry',
				value: 'longTermShareholdingIndustry',
				type: 'big_template',
				typelist: ['activeequity'],
				isshow: true,
				getData: 'getStocksLongHoldData',
				getRequestData: 'getStocksLongHold'
			},
			{
				name: '高频股票',
				is: 'longTermHoldingShares',
				value: 'longTermHoldingShares',
				type: 'big_template',
				typelist: ['activeequity'],
				isshow: true,
				getData: 'getStocksLongHoldData',
				getRequestData: 'getStocksLongHold'
			},
			{
				name: '四因子分析',
				is: 'dynamicFourFactorAnalysis',
				value: 'dynamicFourFactorAnalysis',
				type: 'big_template',
				typelist: ['activeequity'],
				isshow: true,
				getData: 'getDynamicAnalysisData',
				getRequestData: 'getDynamicAnalysis'
			},
			{
				name: '基于回报数据的投资能力分析',
				is: 'investmentCapabilityAnalysis',
				value: 'investmentCapabilityAnalysis',
				type: 'small_template',
				typelist: ['activeequity'],
				isshow: true,
				getData: 'getInvestmentCapabilityAnalysisData',
				getRequestData: 'getInvestmentCapabilityAnalysis'
			},
			{
				name: '动态多因子分析',
				is: 'dynamicMultiFactorAnalysis',
				value: 'dynamicMultiFactorAnalysis',
				type: 'small_template',
				typelist: ['activeequity'],
				isshow: true,
				getData: 'getDynamicMultiFactorAnalysisData',
				getRequestData: 'getDynamicMultiFactorAnalysis'
			},
			{
				name: '超额收益归因',
				is: 'excessReturnAttribution',
				value: 'excessReturnAttribution',
				type: 'big_template',
				typelist: ['activeequity'],
				isshow: true,
				getData: 'getExcessReturnDecompositionData',
				getRequestData: 'getExcessReturnDecomposition'
			},
			{
				name: '行业配置表现',
				is: 'industryAllocationPerformance',
				value: 'industryAllocationPerformance',
				type: 'big_template',
				typelist: ['activeequity'],
				isshow: true,
				getData: 'getIndustryInfoData',
				getRequestData: 'getIndustryInfo'
			},
			{
				name: '交易风格',
				is: 'tradingStyle',
				value: 'tradingStyle',
				type: 'big_template',
				typelist: ['activeequity'],
				isshow: true,
				getData: 'getConcentrationInfoData',
				getRequestData: 'getConcentrationInfo'
			},
			{
				name: '基金经理整体风格描述',
				is: 'overallStyleDescription',
				value: 'overallStyleDescription',
				type: 'small_template',
				typelist: ['activeequity'],
				isshow: true,
				getData: 'getOverallStyleDescriptionData',
				getRequestData: 'getOverallStyleDescription'
			},
			{
				name: '历史持仓股票分析',
				is: 'historicalStockPositionAnalysis',
				value: 'historicalStockPositionAnalysis',
				type: 'small_template',
				typelist: ['activeequity'],
				isshow: true,
				getData: 'getStockHistoryHoldData',
				getRequestData: 'getStockHistoryHold'
			},
			{
				name: '主题特征&轨迹图',
				is: 'thematicFeaturesAndTrackMap',
				value: 'thematicFeaturesAndTrackMap',
				type: 'big_template',
				typelist: ['activeequity'],
				isshow: true,
				getData: 'getThemeAndMapData',
				getRequestData: 'getThemeAndMap'
			},
			{
				name: 'PB-ROE特征',
				is: 'PBROEcharacteristics',
				value: 'PBROEcharacteristics',
				type: 'big_template',
				typelist: ['activeequity'],
				isshow: true,
				getData: 'getCharacteristicsData',
				getRequestData: 'getCharacteristics'
			},
			{
				name: '买入模式',
				is: 'sixBuyingModes',
				value: 'sixBuyingModes',
				type: 'big_template',
				typelist: ['activeequity'],
				isshow: true,
				getData: 'getBuyModInfoData',
				getRequestData: 'getBuyModInfo'
			},
			{
				name: '卖出模式',
				is: 'sixSellingModes',
				value: 'sixSellingModes',
				type: 'big_template',
				typelist: ['activeequity'],
				isshow: true,
				getData: 'getSellModInfoData',
				getRequestData: 'getSellModInfo'
			}
		]
	},
	{
		label: '港股',
		key: 'hkequity',
		class: ['hkequity'],
		templateList: [
			{
				name: '管理产品',
				is: 'managementProducts',
				value: 'managementProducts',
				type: 'big_template',
				typelist: ['hkequity'],
				isshow: true,
				getData: 'getManagedFundsData',
				getRequestData: 'getManagedFunds'
			},
			{
				name: '收益率分布直方图',
				is: 'distributionReturn',
				value: 'distributionReturn',
				type: 'big_template',
				typelist: ['hkequity'],
				isshow: true,
				methods: 'getDistributionData',
				getData: 'getDistributionReturnData',
				getRequestData: 'getDistributionReturn'
			},
			{
				name: '收益与基准比较',
				is: 'revenueVsBenchmark',
				value: 'revenueVsBenchmark',
				type: 'small_template',
				typelist: ['hkequity'],
				isshow: true,
				getData: 'getBondManagerMsgData',
				getRequestData: 'getBondManagerMsg'
			},
			{
				name: '风险收益关系',
				is: 'riskReturnRelationship',
				value: 'riskReturnRelationship',
				type: 'small_template',
				typelist: ['hkequity'],
				isshow: true,
				getData: 'getBestBenchmarksData',
				getRequestData: 'getBestBenchmarks'
			},
			{
				name: '分时段业绩表现',
				is: 'timePhasedPerformance',
				value: 'timePhasedPerformance',
				type: 'big_template',
				typelist: ['hkequity'],
				isshow: true,
				getData: 'getMarketWindowReturnData',
				getRequestData: 'getFundPeriod'
			},
			{
				name: '股票持仓分析',
				is: 'stocksReportPosition',
				value: 'stocksReportPosition',
				type: 'big_template',
				isshow: true,
				typelist: ['hkequity'],
				isshow: true,
				getData: 'getHoldStocks',
				getRequestData: 'getDateList'
			},
			{
				name: '估值分析',
				is: 'valuationAnalysis',
				value: 'valuationAnalysis',
				type: 'big_template',
				typelist: ['hkequity'],
				isshow: true,
				getData: 'getValuationAnalysisData',
				getRequestData: 'getValuationAnalysis'
			},
			{
				name: 'PB-ROE估值',
				is: 'PBROEvaluation',
				value: 'PBROEvaluation',
				type: 'big_template',
				typelist: ['hkequity'],
				isshow: true,
				getData: 'getStocksValuationInfoData',
				getRequestData: 'getStocksValuationInfo'
			},
			{
				name: '长期持股行业分布',
				is: 'longTermShareholdingIndustry',
				value: 'longTermShareholdingIndustry',
				type: 'big_template',
				typelist: ['hkequity'],
				isshow: true,
				getData: 'getStocksLongHoldData',
				getRequestData: 'getStocksLongHold'
			},
			{
				name: '高频股票',
				is: 'longTermHoldingShares',
				value: 'longTermHoldingShares',
				type: 'big_template',
				typelist: ['hkequity'],
				isshow: true,
				getData: 'getStocksLongHoldData',
				getRequestData: 'getStocksLongHold'
			},
			{
				name: '四因子分析',
				is: 'dynamicFourFactorAnalysis',
				value: 'dynamicFourFactorAnalysis',
				type: 'big_template',
				typelist: ['hkequity'],
				isshow: true,
				getData: 'getDynamicAnalysisData',
				getRequestData: 'getDynamicAnalysis'
			},
			{
				name: '基于回报数据的投资能力分析',
				is: 'investmentCapabilityAnalysis',
				value: 'investmentCapabilityAnalysis',
				type: 'small_template',
				typelist: ['hkequity'],
				isshow: true,
				getData: 'getInvestmentCapabilityAnalysisData',
				getRequestData: 'getOtherMsg'
			},
			{
				name: '动态多因子分析',
				is: 'dynamicMultiFactorAnalysis',
				value: 'dynamicMultiFactorAnalysis',
				type: 'small_template',
				typelist: ['hkequity'],
				isshow: true,
				getData: 'getDynamicMultiFactorAnalysisData',
				getRequestData: 'getOtherMsg'
			},
			{
				name: 'what-if调仓做对了吗',
				is: 'warehouseAdjustmentRhythm',
				value: 'warehouseAdjustmentRhythm',
				type: 'big_template',
				typelist: ['hkequity'],
				isshow: true,
				getData: 'getWarehouseAdjustmentData',
				getRequestData: 'getWarehouseAdjustment'
			},
			{
				name: '超额收益归因',
				is: 'excessReturnAttribution',
				value: 'excessReturnAttribution',
				type: 'big_template',
				typelist: ['hkequity'],
				isshow: true,
				getData: 'getExcessReturnDecompositionData',
				getRequestData: 'getExcessReturnDecomposition'
			},
			{
				name: '行业配置表现',
				is: 'industryAllocationPerformance',
				value: 'industryAllocationPerformance',
				type: 'big_template',
				typelist: ['hkequity'],
				isshow: true,
				getData: 'getIndustryInfoData',
				getRequestData: 'getIndustryInfo'
			},

			// {
			// 	name: '行业能力宽度',
			// 	is: 'industryWidth',
			// 	value: 'industryWidth',
			// 	type: 'big_template',
			// 	typelist: ['hkequity'],
			// 	isshow: true,
			// 	getData: 'getIndustryWidthData',
			// 	getRequestData: 'getIndustryWidth'
			// },
			{
				name: '交易风格',
				is: 'tradingStyle',
				value: 'tradingStyle',
				type: 'big_template',
				typelist: ['hkequity'],
				isshow: true,
				getData: 'getConcentrationInfoData',
				getRequestData: 'getConcentrationInfo'
			},
			{
				name: '基金经理整体风格描述',
				is: 'overallStyleDescription',
				value: 'overallStyleDescription',
				type: 'small_template',
				typelist: ['hkequity'],
				isshow: true,
				getData: 'getOverallStyleDescriptionData',
				getRequestData: 'getOtherMsg'
			},
			{
				name: '历史持仓股票分析',
				is: 'historicalStockPositionAnalysis',
				value: 'historicalStockPositionAnalysis',
				type: 'small_template',
				typelist: ['hkequity'],
				isshow: true,
				getData: 'getStockHistoryHoldData',
				getRequestData: 'getStockHistoryHold'
			},
			// {
			// 	name: '主题特征',
			// 	is: 'thematicFeatures',
			// 	value: 'thematicFeatures',
			// 	type: 'small_template',
			// 	typelist: ['hkequity'],
			// 	isshow: true,
			// 	getData: 'getEquityThemeData',
			// 	getRequestData: 'getEquityTheme'
			// },
			// {
			// 	name: '主题特征-轨迹图',
			// 	is: 'themeFeaturesTrackMap',
			// 	value: 'themeFeaturesTrackMap',
			// 	type: 'small_template',
			// 	typelist: ['hkequity'],
			// 	isshow: true,
			// 	methods: 'getThemeCode',
			// 	getData: 'getThemeData',
			// 	getRequestData: 'getOtherMsg'
			// },
			{
				name: '主题特征&轨迹图',
				is: 'thematicFeaturesAndTrackMap',
				value: 'thematicFeaturesAndTrackMap',
				type: 'big_template',
				typelist: ['hkequity'],
				isshow: true,
				methods: 'getThemeCode',
				getData: 'getThemeAndMapData',
				getRequestData: 'getThemeAndMap'
			},
			{
				name: 'PB-ROE特征',
				is: 'PBROEcharacteristics',
				value: 'PBROEcharacteristics',
				type: 'big_template',
				typelist: ['hkequity'],
				isshow: true,
				getData: 'getCharacteristicsData',
				getRequestData: 'getOtherMsg'
			},
			{
				name: '买入模式',
				is: 'sixBuyingModes',
				value: 'sixBuyingModes',
				type: 'big_template',
				typelist: ['hkequity'],
				isshow: true,
				getData: 'getBuyModInfoData',
				getRequestData: 'getBuyModInfo'
			},
			{
				name: '卖出模式',
				is: 'sixSellingModes',
				value: 'sixSellingModes',
				type: 'big_template',
				typelist: ['hkequity'],
				isshow: true,
				getData: 'getSellModInfoData',
				getRequestData: 'getSellModInfo'
			}
		]
	},
	{
		label: '固收+',
		key: 'bond',
		class: ['bond'],
		templateList: [
			{
				name: '管理产品',
				is: 'managementProducts',
				value: 'managementProducts',
				type: 'big_template',
				typelist: ['bond'],
				isshow: true,
				getData: 'getManagedFundsData',
				getRequestData: 'getManagedFunds'
			},
			{
				name: '业绩表现',
				is: 'distributionReturn',
				value: 'distributionReturn',
				type: 'big_template',
				typelist: ['bond'],
				isshow: true,
				getData: 'getDistributionReturnData',
				getRequestData: 'getDistributionReturn'
			},
			// {
			// 	name: '收益与基准比较',
			// 	is: 'revenueVsBenchmark',
			// 	value: 'revenueVsBenchmark',
			// 	type: 'small_template',
			// 	typelist: ['bond'],
			// 	isshow: true,
			// 	getData: 'getBondManagerMsgData',
			// 	getRequestData: 'getBondManagerMsg'
			// },
			{
				name: '风险收益关系',
				is: 'riskReturnRelationship',
				value: 'riskReturnRelationship',
				type: 'small_template',
				typelist: ['bond'],
				isshow: true,
				getData: 'getBestBenchmarksData',
				getRequestData: 'getBestBenchmarks'
			},
			{
				name: '分时段业绩表现',
				is: 'timePhasedPerformance',
				value: 'timePhasedPerformance',
				type: 'big_template',
				typelist: ['bond'],
				isshow: true,
				getData: 'getMarketWindowReturnData',
				getRequestData: 'getFundPeriod'
			},
			{
				name: '大类资产配置情况',
				is: 'fundAssetAllocationAnalysis',
				value: 'fundAssetAllocationAnalysis',
				type: 'big_template',
				typelist: ['bond'],
				isshow: true,
				getData: 'getIndexListData',
				getRequestData: 'getIndexList'
			},
			{
				name: '持仓分类',
				is: 'positionClass',
				value: 'positionClass',
				type: 'big_template',
				typelist: ['bond'],
				isshow: true,
				getData: 'getPositionClassData',
				getRequestData: 'getPositionClass'
			},
			// {
			// 	name: '基金仓位估算变化',
			// 	is: 'fundPositionEstimation',
			// 	value: 'fundPositionEstimation',
			// 	type: 'big_template',
			// 	typelist: ['bond'],
			// 	isshow: true,
			// 	getData: 'getFundPositionEstimationData',
			// 	getRequestData: 'getFundPositionEstimation'
			// }
			{
				name: '行业配置表现',
				is: 'industryAllocationPerformance',
				value: 'industryAllocationPerformance',
				type: 'big_template',
				typelist: ['bond'],
				isshow: true,
				getData: 'getIndustryInfoData',
				getRequestData: 'getIndustryInfo'
			},
			// {
			// 	name: '二级债基权益平均因子暴露',
			// 	is: 'bondEquityDynamic',
			// 	value: 'bondEquityDynamic',
			// 	type: 'big_template',
			// 	typelist: ['bond'],
			// 	isshow: true,
			// 	getData: 'geFactoraData',
			// 	getRequestData: 'geFactora'
			// },
			{
				name: '股票持仓分析',
				is: 'stocksReportPosition',
				value: 'stocksReportPosition',
				type: 'big_template',
				isshow: true,
				typelist: ['bond'],
				isshow: true,
				getData: 'getHoldStocks',
				getRequestData: 'getDateList'
			},
			{
				name: '估值分析',
				is: 'valuationAnalysis',
				value: 'valuationAnalysis',
				type: 'big_template',
				typelist: ['bond'],
				isshow: true,
				getData: 'getValuationAnalysisData',
				getRequestData: 'getValuationAnalysis'
			},
			{
				name: 'PB-ROE估值',
				is: 'PBROEvaluation',
				value: 'PBROEvaluation',
				type: 'big_template',
				typelist: ['bond'],
				isshow: true,
				getData: 'getStocksValuationInfoData',
				getRequestData: 'getStocksValuationInfo'
			},
			{
				name: '久期拉长',
				is: 'prolongedDuration',
				value: 'prolongedDuration',
				type: 'big_template',
				typelist: ['bond'],
				isshow: true,
				getData: 'getDurationAnalysisInfoData',
				getRequestData: 'getDurationAnalysisInfo'
			}
			// {
			// 	name: '久期拉长表格',
			// 	is: 'prolongedDurationTable',
			// 	value: 'prolongedDurationTable',
			// 	type: 'big_template',
			// 	typelist: ['bond'],
			// 	isshow: true,
			// 	getData: 'getProlongedDurationTableData',
			// 	getRequestData: 'getProlongedDurationTable'
			// },
			// {
			// 	name: '基金债券信用挖掘比例变化估计 信用挖掘',
			// 	is: 'bondCreditMining',
			// 	value: 'bondCreditMining',
			// 	type: 'big_template',
			// 	typelist: ['bond'],
			// 	isshow: true,
			// 	getData: 'getBondCreditMiningData',
			// 	getRequestData: 'getBondCreditMining'
			// },
			// {
			// 	name: '风格择时能力',
			// 	is: 'styleTimingAbility',
			// 	value: 'styleTimingAbility',
			// 	type: 'big_template',
			// 	typelist: ['bond'],
			// 	isshow: true,
			// 	getData: 'getStyleTimingData',
			// 	getRequestData: 'getStyleTiming'
			// },
			// {
			// 	name: '牛熊市场表现 基金不同市场表现及其分位',
			// 	is: 'bullBearMarketPerformance',
			// 	value: 'bullBearMarketPerformance',
			// 	type: 'big_template',
			// 	typelist: ['bond'],
			// 	isshow: true,
			// 	getData: 'getBullBearMarketPerformanceData',
			// 	getRequestData: 'getBullBearMarketPerformance'
			// }
		]
	},
	{
		label: '纯债',
		key: 'purebond',
		class: ['purebond'],
		templateList: [
			{
				name: '管理产品',
				is: 'managementProducts',
				value: 'managementProducts',
				type: 'big_template',
				typelist: ['purebond'],
				isshow: true,
				getData: 'getManagedFundsData',
				getRequestData: 'getManagedFunds'
			},
			{
				name: '业绩表现',
				is: 'distributionReturn',
				value: 'distributionReturn',
				type: 'big_template',
				typelist: ['purebond'],
				isshow: true,
				methods: 'getDistributionData',
				getData: 'getDistributionReturnData',
				getRequestData: 'getDistributionReturn'
			},
			{
				name: '收益与基准比较',
				is: 'revenueVsBenchmark',
				value: 'revenueVsBenchmark',
				type: 'small_template',
				typelist: ['purebond'],
				isshow: true,
				getData: 'getBondManagerMsgData',
				getRequestData: 'getBondManagerMsg'
			},
			{
				name: '风险收益关系',
				is: 'riskReturnRelationship',
				value: 'riskReturnRelationship',
				type: 'small_template',
				typelist: ['purebond'],
				isshow: true,
				getData: 'getBestBenchmarksData',
				getRequestData: 'getBestBenchmarks'
			},
			{
				name: '分时段业绩表现',
				is: 'timePhasedPerformance',
				value: 'timePhasedPerformance',
				type: 'big_template',
				typelist: ['purebond'],
				isshow: true,
				getData: 'getMarketWindowReturnData',
				getRequestData: 'getFundPeriod'
			},
			{
				name: '大类资产配置情况',
				is: 'fundAssetAllocationAnalysis',
				value: 'fundAssetAllocationAnalysis',
				type: 'big_template',
				typelist: ['purebond'],
				isshow: true,
				methods: 'getIndexCode',
				getData: 'getIndexListData',
				getRequestData: 'getIndexList'
			},
			{
				name: '持仓分类',
				is: 'positionClass',
				value: 'positionClass',
				type: 'big_template',
				typelist: ['purebond'],
				isshow: true,
				getData: 'getPositionClassData',
				getRequestData: 'getPositionClass'
			},
			{
				name: '基金仓位估算变化',
				is: 'fundPositionEstimation',
				value: 'fundPositionEstimation',
				type: 'big_template',
				typelist: ['purebond'],
				isshow: true,
				getData: 'getFundPositionEstimationData',
				getRequestData: 'getFundPositionEstimation'
			},
			{
				name: '久期拉长',
				is: 'prolongedDuration',
				value: 'prolongedDuration',
				type: 'big_template',
				typelist: ['purebond'],
				isshow: true,
				getData: 'getDurationAnalysisInfoData',
				getRequestData: 'getDurationAnalysisInfo'
			},
			{
				name: '久期拉长表格',
				is: 'prolongedDurationTable',
				value: 'prolongedDurationTable',
				type: 'big_template',
				typelist: ['purebond'],
				isshow: true,
				getData: 'getProlongedDurationTableData',
				getRequestData: 'getProlongedDurationTable'
			},
			{
				name: '基金债券信用挖掘比例变化估计 信用挖掘',
				is: 'bondCreditMining',
				value: 'bondCreditMining',
				type: 'big_template',
				typelist: ['purebond'],
				isshow: true,
				getData: 'getBondCreditMiningData',
				getRequestData: 'getBondCreditMining'
			},
			{
				name: '风格择时能力',
				is: 'styleTimingAbility',
				value: 'styleTimingAbility',
				type: 'big_template',
				typelist: ['purebond'],
				isshow: true,
				getData: 'getStyleTimingData',
				getRequestData: 'getStyleTiming'
			},
			{
				name: '牛熊市场表现 基金不同市场表现及其分位',
				is: 'bullBearMarketPerformance',
				value: 'bullBearMarketPerformance',
				type: 'big_template',
				typelist: ['purebond'],
				isshow: true,
				getData: 'getBullBearMarketPerformanceData',
				getRequestData: 'getBullBearMarketPerformance'
			}
		]
	},
	{
		label: '中短债',
		key: 'bill',
		class: ['bill'],
		templateList: [
			{
				name: '管理产品',
				is: 'managementProducts',
				value: 'managementProducts',
				type: 'big_template',
				typelist: ['bill'],
				isshow: true,
				getData: 'getManagedFundsData',
				getRequestData: 'getManagedFunds'
			},
			{
				name: '业绩表现',
				is: 'distributionReturn',
				value: 'distributionReturn',
				type: 'big_template',
				typelist: ['bill'],
				isshow: true,
				methods: 'getDistributionData',
				getData: 'getDistributionReturnData',
				getRequestData: 'getDistributionReturn'
			},
			{
				name: '收益与基准比较',
				is: 'revenueVsBenchmark',
				value: 'revenueVsBenchmark',
				type: 'small_template',
				typelist: ['bill'],
				isshow: true,
				getData: 'getBondManagerMsgData',
				getRequestData: 'getBondManagerMsg'
			},
			{
				name: '风险收益关系',
				is: 'riskReturnRelationship',
				value: 'riskReturnRelationship',
				type: 'small_template',
				typelist: ['bill'],
				isshow: true,
				getData: 'getBestBenchmarksData',
				getRequestData: 'getBestBenchmarks'
			},
			{
				name: '分时段业绩表现',
				is: 'timePhasedPerformance',
				value: 'timePhasedPerformance',
				type: 'big_template',
				typelist: ['bill'],
				isshow: true,
				getData: 'getMarketWindowReturnData',
				getRequestData: 'getFundPeriod'
			},
			{
				name: '大类资产配置情况',
				is: 'fundAssetAllocationAnalysis',
				value: 'fundAssetAllocationAnalysis',
				type: 'big_template',
				typelist: ['bill'],
				isshow: true,
				methods: 'getIndexCode',
				getData: 'getIndexListData',
				getRequestData: 'getIndexList'
			},
			{
				name: '持仓分类',
				is: 'positionClass',
				value: 'positionClass',
				type: 'big_template',
				typelist: ['bill'],
				isshow: true,
				getData: 'getPositionClassData',
				getRequestData: 'getPositionClass'
			},
			{
				name: '基金仓位估算变化',
				is: 'fundPositionEstimation',
				value: 'fundPositionEstimation',
				type: 'big_template',
				typelist: ['bill'],
				isshow: true,
				getData: 'getFundPositionEstimationData',
				getRequestData: 'getFundPositionEstimation'
			},
			{
				name: '久期拉长',
				is: 'prolongedDuration',
				value: 'prolongedDuration',
				type: 'big_template',
				typelist: ['bill'],
				isshow: true,
				getData: 'getDurationAnalysisInfoData',
				getRequestData: 'getDurationAnalysisInfo'
			},
			{
				name: '久期拉长表格',
				is: 'prolongedDurationTable',
				value: 'prolongedDurationTable',
				type: 'big_template',
				typelist: ['bill'],
				isshow: true,
				getData: 'getProlongedDurationTableData',
				getRequestData: 'getProlongedDurationTable'
			},
			{
				name: '基金债券信用挖掘比例变化估计 信用挖掘',
				is: 'bondCreditMining',
				value: 'bondCreditMining',
				type: 'big_template',
				typelist: ['bill'],
				isshow: true,
				getData: 'getBondCreditMiningData',
				getRequestData: 'getBondCreditMining'
			},
			{
				name: '风格择时能力',
				is: 'styleTimingAbility',
				value: 'styleTimingAbility',
				type: 'big_template',
				typelist: ['bill'],
				isshow: true,
				getData: 'getStyleTimingData',
				getRequestData: 'getStyleTiming'
			},
			{
				name: '牛熊市场表现 基金不同市场表现及其分位',
				is: 'bullBearMarketPerformance',
				value: 'bullBearMarketPerformance',
				type: 'big_template',
				typelist: ['bill'],
				isshow: true,
				getData: 'getBullBearMarketPerformanceData',
				getRequestData: 'getBullBearMarketPerformance'
			}
		]
	},
	{
		label: '可转债',
		key: 'cbond',
		class: ['cbond'],
		templateList: [
			{
				name: '管理产品',
				is: 'managementProducts',
				value: 'managementProducts',
				type: 'big_template',
				typelist: ['cbond'],
				isshow: true,
				getData: 'getManagedFundsData',
				getRequestData: 'getManagedFunds'
			},
			{
				name: '业绩表现',
				is: 'distributionReturn',
				value: 'distributionReturn',
				type: 'big_template',
				typelist: ['cbond'],
				isshow: true,
				methods: 'getDistributionData',
				getData: 'getDistributionReturnData',
				getRequestData: 'getDistributionReturn'
			},
			{
				name: '收益与基准比较',
				is: 'revenueVsBenchmark',
				value: 'revenueVsBenchmark',
				type: 'small_template',
				typelist: ['cbond'],
				isshow: true,
				getData: 'getBondManagerMsgData',
				getRequestData: 'getBondManagerMsg'
			},
			{
				name: '风险收益关系',
				is: 'riskReturnRelationship',
				value: 'riskReturnRelationship',
				type: 'small_template',
				typelist: ['cbond'],
				isshow: true,
				getData: 'getBestBenchmarksData',
				getRequestData: 'getBestBenchmarks'
			},
			{
				name: '分时段业绩表现',
				is: 'timePhasedPerformance',
				value: 'timePhasedPerformance',
				type: 'big_template',
				typelist: ['cbond'],
				isshow: true,
				getData: 'getMarketWindowReturnData',
				getRequestData: 'getFundPeriod'
			},
			{
				name: '大类资产配置情况',
				is: 'fundAssetAllocationAnalysis',
				value: 'fundAssetAllocationAnalysis',
				type: 'big_template',
				typelist: ['cbond'],
				isshow: true,
				methods: 'getIndexCode',
				getData: 'getIndexListData',
				getRequestData: 'getIndexList'
			},
			{
				name: '持仓分类',
				is: 'positionClass',
				value: 'positionClass',
				type: 'big_template',
				typelist: ['cbond'],
				isshow: true,
				getData: 'getPositionClassData',
				getRequestData: 'getPositionClass'
			},
			{
				name: '基金仓位估算变化',
				is: 'fundPositionEstimation',
				value: 'fundPositionEstimation',
				type: 'big_template',
				typelist: ['cbond'],
				isshow: true,
				getData: 'getFundPositionEstimationData',
				getRequestData: 'getFundPositionEstimation'
			},
			// {
			// 	name: '转债股性债性概览',
			// 	is: 'cbondHoldEquityBond',
			// 	value: 'cbondHoldEquityBond',
			// 	type: 'big_template',
			// 	typelist: ['cbond'],
			// 	isshow: true,
			// 	getData: 'getcbondHoldEquityBondData',
			// 	getRequestData: 'getcbondHoldEquityBond'
			// },
			{
				name: '久期拉长',
				is: 'prolongedDuration',
				value: 'prolongedDuration',
				type: 'big_template',
				typelist: ['cbond'],
				isshow: true,
				getData: 'getDurationAnalysisInfoData',
				getRequestData: 'getDurationAnalysisInfo'
			},
			{
				name: '久期拉长表格',
				is: 'prolongedDurationTable',
				value: 'prolongedDurationTable',
				type: 'big_template',
				typelist: ['cbond'],
				isshow: true,
				getData: 'getProlongedDurationTableData',
				getRequestData: 'getProlongedDurationTable'
			},
			{
				name: '基金债券信用挖掘比例变化估计 信用挖掘',
				is: 'bondCreditMining',
				value: 'bondCreditMining',
				type: 'big_template',
				typelist: ['cbond'],
				isshow: true,
				getData: 'getBondCreditMiningData',
				getRequestData: 'getBondCreditMining'
			},
			{
				name: '风格择时能力',
				is: 'styleTimingAbility',
				value: 'styleTimingAbility',
				type: 'big_template',
				typelist: ['cbond'],
				isshow: true,
				getData: 'getStyleTimingData',
				getRequestData: 'getStyleTiming'
			},
			{
				name: '牛熊市场表现 基金不同市场表现及其分位',
				is: 'bullBearMarketPerformance',
				value: 'bullBearMarketPerformance',
				type: 'big_template',
				typelist: ['cbond'],
				isshow: true,
				getData: 'getBullBearMarketPerformanceData',
				getRequestData: 'getBullBearMarketPerformance'
			}
		]
	},
	{
		label: '时间轴',
		key: 'timeAxis',
		class: ['activeequity', 'hkequity', 'bond', 'cbond', 'bill', 'purebond'],
		templateList: [
			{
				name: '时间轴',
				is: undefined,
				value: 'timeAxis',
				type: undefined,
				typelist: ['activeequity', 'hkequity', 'bond', 'cbond', 'bill', 'purebond'],
				isshow: true,
				getData: undefined,
				getRequestData: 'getData'
			}
		]
	}
];
export let companyComponentsList = [
	{
		label: '一页通',
		key: 'onePagePass',
		class: ['*'],
		templateList: [
			{
				name: '所属基金概况',
				is: 'allTypeFundBasicInfo',
				value: 'allTypeFundBasicInfo',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getAllTypeFundBaicInfoData',
				getRequestData: 'getAllTypeFundBaicInfo'
			},
			{
				name: '各类型基金业绩',
				is: 'allTypeFundCumReturn',
				value: 'allTypeFundCumReturn',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getAllTypeFundCumReturnData',
				getRequestData: 'getAllTypeFundCumReturn'
			},
			{
				name: '基金公司规模',
				is: 'fundCompanyNetasset',
				value: 'fundCompanyNetasset',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getFundCompanyNetassetData',
				getRequestData: 'getFundCompanyNetassetList'
			},
			{
				name: '最新基金业绩排名',
				is: 'performanceRanking',
				value: 'performanceRanking',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getPerformanceRankingData',
				getRequestData: 'getPerformanceRankingList'
			},
			{
				name: '基金资产配置分析',
				is: 'allocationAnalysis',
				value: 'allocationAnalysis',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getCompanyAllocationDetailsData',
				getRequestData: 'getCompanyAllocationDetails'
			},
			// {
			// 	name: '全部基金',
			// 	is: 'allFunds',
			// 	value: 'allFunds',
			// 	type: 'big_template',
			// 	typelist: ['*'],
			// 	isshow: true,
			// 	getData: 'getAllFundsData',
			// 	getRequestData: 'getAllFunds'
			// },
			{
				name: '全部基金经理',
				is: 'allFundManagers',
				value: 'allFundManagers',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getAllFundManagersData',
				getRequestData: 'getHoldType'
			}
		]
	},
	{
		label: '主动权益',
		key: 'equity',
		class: ['equity'],
		templateList: [
			{
				name: '持仓股票分析',
				is: 'positionStockAnalysis',
				value: 'positionStockAnalysis',
				type: 'big_template',
				typelist: ['equity'],
				isshow: true,
				getData: 'getHoldStockMsgData',
				getRequestData: 'getHoldStockMsg'
			},
			{
				name: '行业配置表现',
				typelist: ['equity'],
				is: 'industryAllocationPerformance',
				value: 'industryAllocationPerformance',
				type: 'big_template',
				isshow: true,
				getData: 'getIndustryInfoData',
				getRequestData: 'getIndustryInfo'
			}
		]
	},
	{
		label: '港股',
		key: 'equityhk',
		class: ['equityhk'],
		templateList: [
			{
				name: '持仓股票分析',
				is: 'positionStockAnalysis',
				value: 'positionStockAnalysis',
				type: 'big_template',
				typelist: ['equityhk'],
				isshow: true,
				getData: 'getHoldStockMsgData',
				getRequestData: 'getHoldStockMsg'
			},
			{
				name: '行业配置表现',
				typelist: ['equityhk'],
				is: 'industryAllocationPerformance',
				value: 'industryAllocationPerformance',
				type: 'big_template',
				isshow: true,
				getData: 'getIndustryInfoData',
				getRequestData: 'getIndustryInfo'
			}
		]
	},
	{
		label: '固收+',
		key: 'bond',
		class: ['bond'],
		templateList: [
			{
				name: '持仓股票分析',
				is: 'positionStockAnalysis',
				value: 'positionStockAnalysis',
				type: 'big_template',
				typelist: ['bond'],
				isshow: true,
				getData: 'getHoldStockMsgData',
				getRequestData: 'getHoldStockMsg'
			},
			{
				name: '行业配置表现',
				typelist: ['bond'],
				is: 'industryAllocationPerformance',
				value: 'industryAllocationPerformance',
				type: 'big_template',
				isshow: true,
				getData: 'getIndustryInfoData',
				getRequestData: 'getIndustryInfo'
			},
			{
				name: '持仓债券分析',
				is: 'positionBondAnalysis',
				typelist: ['bond'],
				value: 'positionBondAnalysis',
				type: 'big_template',
				isshow: true,
				getData: 'getBondAnalysisData',
				getRequestData: 'getBondAnalysis'
			},
			{
				name: '信用分析',
				is: 'creditAnalysis',
				typelist: ['bond'],
				value: 'creditAnalysis',
				type: 'big_template',
				isshow: true,
				getData: 'getBondCreditAnalysisData',
				getRequestData: 'getBondCreditAnalysis'
			}
		]
	},
	{
		label: '纯债',
		key: 'purebond',
		class: ['purebond'],
		templateList: [
			{
				name: '持仓债券分析',
				is: 'positionBondAnalysis',
				typelist: ['purebond'],
				value: 'positionBondAnalysis',
				type: 'big_template',
				isshow: true,
				getData: 'getBondAnalysisData',
				getRequestData: 'getBondAnalysis'
			},
			{
				name: '信用分析',
				is: 'creditAnalysis',
				typelist: ['purebond'],
				value: 'creditAnalysis',
				type: 'big_template',
				isshow: true,
				getData: 'getBondCreditAnalysisData',
				getRequestData: 'getBondCreditAnalysis'
			}
		]
	}
	// {
	// 	label: '资产分析',
	// 	key: 'assetAnalysis',
	// 	class: ['*'],
	// 	templateList: [
	// 		{
	// 			name: '持仓股票分析',
	// 			is: 'positionStockAnalysis',
	// 			value: 'positionStockAnalysis',
	// 			type: 'big_template',
	// 			typelist: ['equity'],
	// 			isshow: true,
	// 			methods: 'getHoldStockMsg',
	// 			getData: 'getHoldStockMsgData',
	// 			getRequestData: 'getTypeList'
	// 		},
	// 		{
	// 			name: '行业配置表现',
	// 			typelist: ['equity'],
	// 			is: 'industryAllocationPerformance',
	// 			value: 'industryAllocationPerformance',
	// 			type: 'big_template',
	// 			isshow: true,
	// 			getData: 'getIndustryInfoData',
	// 			getRequestData: 'getIndustryInfo'
	// 		},
	// 		{
	// 			name: '配置变化对比',
	// 			is: 'configurationChangeComparison',
	// 			value: 'configurationChangeComparison',
	// 			type: 'big_template',
	// 			typelist: ['equity'],
	// 			isshow: true,
	// 			methods: 'getIndustryChange',
	// 			getData: 'getIndustryChangeData',
	// 			getRequestData: 'getDateList'
	// 		},
	// 		{
	// 			name: '持仓债券分析',
	// 			is: 'positionBondAnalysis',
	// 			value: 'positionBondAnalysis',
	// 			type: 'big_template',
	// 			typelist: ['bond', 'purebond'],
	// 			isshow: true,
	// 			methods: 'getPositionBondAnalysis',
	// 			getData: 'getPositionBondAnalysisData',
	// 			getRequestData: 'getTypeList'
	// 		},
	// 		{
	// 			name: '信用分析',
	// 			is: 'creditAnalysis',
	// 			value: 'creditAnalysis',
	// 			type: 'big_template',
	// 			typelist: ['bond', 'purebond'],
	// 			isshow: true,
	// 			methods: 'getCreditAnalysis',
	// 			getData: 'getCreditAnalysisData',
	// 			getRequestData: 'getTypeList'
	// 		},
	// 		// {
	// 		// 	name: '评价指标',
	// 		// 	is: 'GDBankHf',
	// 		// 	value: 'GDBankHf',
	// 		// 	type: 'big_template',
	// 		// 	typelist: ['hf_equity'],
	// 		// 	isshow: true,
	// 		// 	getData: 'getGDBankHfDetailData',
	// 		// 	getRequestData: undefined
	// 		// },
	// 		{
	// 			name: '产品类型饼状图',
	// 			is: 'fundPieChart',
	// 			value: 'fundPieChart',
	// 			type: 'big_template',
	// 			typelist: ['hf_equity', 'hf_bond'],
	// 			isshow: true,
	// 			getData: 'getFundPieChartData',
	// 			getRequestData: 'getFundPieChart'
	// 		},
	// 		{
	// 			name: '公司规模及人员变动图',
	// 			is: 'companySizeChange',
	// 			value: 'companySizeChange',
	// 			type: 'big_template',
	// 			typelist: ['hf_equity', 'hf_bond'],
	// 			isshow: true,
	// 			getData: 'getCompanySizeChangeData',
	// 			getRequestData: 'getCompanySizeChange'
	// 		},
	// 		{
	// 			name: '存续期间变化',
	// 			is: 'durationChange',
	// 			value: 'durationChange',
	// 			type: 'big_template',
	// 			typelist: ['hf_equity', 'hf_bond'],
	// 			isshow: true,
	// 			getData: 'getDurationChangeData',
	// 			getRequestData: 'getDurationChange'
	// 		},
	// 		{
	// 			name: '收益图',
	// 			is: 'incomeChart',
	// 			value: 'incomeChart',
	// 			type: 'big_template',
	// 			typelist: ['hf_equity', 'hf_bond'],
	// 			isshow: true,
	// 			getData: 'getIncomeChartData',
	// 			getRequestData: 'getIncomeChart'
	// 		},
	// 		{
	// 			name: '回撤图',
	// 			is: 'retracementChart',
	// 			value: 'retracementChart',
	// 			type: 'big_template',
	// 			typelist: ['hf_equity', 'hf_bond'],
	// 			isshow: true,
	// 			getData: 'getRetracementChartData',
	// 			getRequestData: 'getRetracementChart'
	// 		},
	// 		{
	// 			name: '高管变动',
	// 			is: 'managerChange',
	// 			value: 'managerChange',
	// 			type: 'big_template',
	// 			typelist: ['hf_equity', 'hf_bond'],
	// 			isshow: true,
	// 			getData: 'getManagerChangeData',
	// 			getRequestData: 'getManagerChange'
	// 		}
	// 	]
	// },
	// {
	// 	label: '所属基金',
	// 	key: 'itsFund',
	// 	class: ['*'],
	// 	templateList: [
	// 		{
	// 			name: '新发基金',
	// 			is: 'newDevelopmentFund',
	// 			value: 'newDevelopmentFund',
	// 			type: 'big_template',
	// 			typelist: ['*'],
	// 			isshow: true,
	// 			getData: 'getNewDevelopmentFundData',
	// 			getRequestData: 'getNewDevelopmentFund'
	// 		},
	// 		{
	// 			name: '全部基金',
	// 			is: 'allFunds',
	// 			value: 'allFunds',
	// 			type: 'big_template',
	// 			typelist: ['*'],
	// 			isshow: true,
	// 			getData: 'getAllFundsData',
	// 			getRequestData: 'getAllFunds'
	// 		}
	// 	]
	// },
	// {
	// 	label: '所属基金经理',
	// 	key: 'itsFundManager',
	// 	class: ['*'],
	// 	templateList: [
	// 		{
	// 			name: '全部基金经理',
	// 			is: 'allFundManagers',
	// 			value: 'allFundManagers',
	// 			type: 'big_template',
	// 			typelist: ['*'],
	// 			isshow: true,
	// 			methods: 'getAllFundManagers',
	// 			getData: 'getAllFundManagersData',
	// 			getRequestData: 'getHoldType'
	// 		}
	// 	]
	// }
];

export let combinationComponentsList = [
	{
		label: '一页通',
		key: 'onePagePass',
		class: ['*'],
		templateList: [
			{
				name: '业绩对比',
				is: 'performanceComparison',
				value: 'performanceComparison',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getPerformanceComparisonData',
				methods: 'getPerformanceComparisonIndex',
				getRequestData: 'getPerformanceComparison'
			},
			{
				name: '动态回撤',
				is: 'dynamicPullback',
				value: 'dynamicPullback',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getDynamicPullbackData',
				methods: 'getDynamicPullbackIndex',
				getRequestData: 'getDynamicPullback'
			},
			{
				name: '相关系数',
				is: 'correlationCoefficient',
				value: 'correlationCoefficient',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getCorrelationCoefficientData',
				methods: '',
				getRequestData: 'getCorrelationCoefficient'
			},
			{
				name: '组合收益贡献度走势',
				is: 'contributionTrend',
				value: 'contributionTrend',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getContributionTrendData',
				methods: '',
				getRequestData: 'getContributionTrend'
			},
			{
				name: '基金配置走势',
				is: 'allocationTrend',
				value: 'allocationTrend',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getAllocationTrendData',
				methods: 'getAllocationTrendType',
				getRequestData: 'getAllocationTrend'
			},
			// {
			// 	name: '配置穿透',
			// 	is: 'typePieChart',
			// 	value: 'typePieChart',
			// 	type: 'big_template',
			// 	typelist: ['*'],
			// 	isshow: true,
			// 	getData: '',
			// 	getRequestData: 'getTypePieChart'
			// },
			{
				name: '月度收益',
				is: 'monthlyIncome',
				value: 'monthlyIncome',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getMonthlyIncomeData',
				methods: 'getMonthlyIncomeIndex',
				getRequestData: 'getMonthlyIncome'
			}
		]
	},
	{
		label: '业绩表现',
		key: 'performance',
		class: ['*'],
		templateList: [
			{
				name: '收益率分布直方图',
				is: 'distributionReturn',
				value: 'distributionReturn',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getDistributionReturnData',
				methods: 'getDistributionData',
				getRequestData: 'getDistributionReturn'
			},
			{
				name: '风险收益指标&&同类排名比较',
				is: 'comparisonSimilarRankingAndRiskReturnIndex',
				value: 'comparisonSimilarRankingAndRiskReturnIndex',
				typelist: ['*'],
				type: 'big_template',
				isshow: true,
				getData: 'getComparisonSimilarRankingAndRiskReturnIndexData',
				getRequestData: 'getComparisonSimilarRankingAndRiskReturnIndex',
				methods: 'getComparisonSimilarRankingAndRiskReturnIndexTag'
			},
			{
				name: '风险收益关系',
				is: 'riskReturnRelationship',
				value: 'riskReturnRelationship',
				typelist: ['*'],
				type: 'big_template',
				isshow: true,
				getData: 'getBestBenchmarksData',
				getRequestData: 'getBestBenchmarks'
			}
		]
	},
	{
		label: '资配分析',
		key: 'assetAllocationAnalysis',
		class: ['*'],
		templateList: [
			{
				name: '最新各类型基金配置情况',
				is: 'newFundAllocation',
				value: 'newFundAllocation',
				type: 'big_template',
				isshow: true,
				typelist: ['*'],
				getData: 'getNewFundAllocationData',
				getRequestData: 'getFundMessage'
			},
			{
				name: '权益基金标签分析',
				is: 'equityFundTagAnalysis',
				value: 'equityFundTagAnalysis',
				type: 'big_template',
				isshow: true,
				typelist: ['*'],
				methods: 'getEquityTag',
				getData: undefined,
				getRequestData: 'getFoFEquityTag'
			},
			{
				name: '权益基金股票持仓分析',
				is: 'equityStockPositionAnalysis',
				value: 'equityStockPositionAnalysis',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getFoFHoldingNewestData',
				getRequestData: 'getFoFHoldingNewest'
			},
			{
				name: '直投股票capm分析',
				is: 'capmStockAnalysis',
				value: 'capmStockAnalysis',
				type: 'big_template',
				isshow: true,
				typelist: ['*'],
				methods: 'getCapmAnalysisIndex',
				getData: '',
				getRequestData: 'getCapmAnalysis'
			},
			{
				name: '持仓债券分析',
				is: 'positionBondAnalysis',
				value: 'positionBondAnalysis',
				type: 'big_template',
				isshow: true,
				typelist: ['*'],
				getData: 'getBondAnalysiseData',
				getRequestData: 'getBondAnalysise'
			},
			{
				name: '持仓分类',
				is: 'positionClass',
				value: 'positionClass',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getPositionClassData',
				getRequestData: 'getPositionClass'
			},
			{
				name: '权益分析(直投权益+间接投资权益)',
				is: 'fundTypeAnalysis',
				value: 'fundTypeAnalysisequity',
				type: 'big_template',
				isshow: true,
				typelist: ['*'],
				methods: 'getFofImmediateAsset',
				getData: 'getIndexBasicMsgEquityData',
				getRequestData: 'getIndexBasicMsgEquity'
			},
			{
				name: '转债分析(直投转债+间接投资转债)',
				is: 'fundTypeAnalysis',
				value: 'fundTypeAnalysiscbond',
				type: 'big_template',
				isshow: true,
				typelist: ['*'],
				methods: 'getFofImmediateAsset',
				getData: 'getIndexBasicMsgCbondData',
				getRequestData: 'getIndexBasicMsgCbond'
			},
			{
				name: '短期流动性管理',
				is: 'mobilityManage',
				value: 'mobilityManage',
				type: 'big_template',
				isshow: true,
				typelist: ['*'],
				getData: 'getFofLiquidityData',
				getRequestData: 'getFofLiquidity'
			}
		]
	}
];
