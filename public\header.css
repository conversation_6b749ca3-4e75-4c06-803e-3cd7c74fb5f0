@media print {
	.headbox {
		width: 800px;

		padding: 20px;
	}
	.peoplename {
		font-size: 40px;
		font-weight: 600;
		font-family: simsun;
	}
	.github-badge {
		margin-left: 10px;
		display: inline-block;
		border-radius: 4px;
		text-shadow: none;
		font-size: 14px;
		color: #fff;
		margin-top: 13px;
	}
	.marlft {
		margin-left: 60px;
	}
	.github-badge .badge-subject {
		display: inline-block;
		background-color: #4d4d4d;
		padding: 4px 4px 4px 6px;
		border-top-left-radius: 4px;
		border-bottom-left-radius: 4px;
	}
	.github-badge .badge-value {
		display: inline-block;
		padding: 4px 6px 4px 4px;
		border-top-right-radius: 4px;
		border-bottom-right-radius: 4px;
	}
	.github-badge .bg-brightgreen {
		background-color: #4dc820 !important;
	}
	.github-badge .bg-orange {
		background-color: #ffa500 !important;
	}
	.github-badge .bg-yellow {
		background-color: #d8b024 !important;
	}
	.github-badge .bg-blueviolet {
		background-color: #8833d7 !important;
	}
	.github-badge .bg-pink {
		background-color: #f26bae !important;
	}
	.github-badge .bg-red {
		background-color: #e05d44 !important;
	}
	.github-badge .bg-blue {
		background-color: #007ec6 !important;
	}
	.github-badge .bg-lightgrey {
		background-color: #9f9f9f !important;
	}
	.github-badge .bg-grey,
	.github-badge .bg-gray {
		background-color: #555 !important;
	}
	.github-badge .bg-lightgrey,
	.github-badge .bg-lightgray {
		background-color: #9f9f9f !important;
	}
	.marginleft20 {
		margin-left: 20px;
	}
	/* 矩形旁边的三角形 */
	.pentagon {
		margin: 10px 0 0 0;

		position: relative;

		height: 0;

		width: 0;

		border-left: 20px solid #2086fd;

		border-right: 20px solid transparent;

		border-bottom: 30px solid transparent;

		border-top: 30px solid transparent;
	}
	/* shiyingxing */
	.newst .zonghepaimingwai {
		width: 200px;
		height: 60px;
		margin-top: 10px;
		background: #00ccff;
		margin-left: -50px;
		display: table;
	}
	.newst .box {
		width: 80px;
		height: 80px;
		background: #00ccff;
		display: -webkit-box;
		-webkit-box-orient: horizontal;
		-webkit-box-pack: center;
		-webkit-box-align: center;
		/* 警告 (157:3) You should write display: flex by final spec instead of display: box */
		display: -moz-box;
		-moz-box-orient: horizontal;
		-moz-box-pack: center;
		-moz-box-align: center;

		display: -o-box;
		-o-box-orient: horizontal;
		-o-box-pack: center;
		-o-box-align: center;

		display: -ms-box;
		-ms-box-orient: horizontal;
		-ms-box-pack: center;
		-ms-box-align: center;

		display: box;
		box-orient: horizontal;
		box-pack: center;
		box-align: center;
	}
	.newst .pentagon {
		margin: 10px 0 0 0;

		position: relative;

		height: 0;

		width: 0;

		border-left: 20px solid #00ccff;

		border-right: 20px solid transparent;

		border-bottom: 30px solid transparent;

		border-top: 30px solid transparent;
	}
	/*  */
	.widthbox {
		width: 300px;
	}
	/* 原型锯齿 */
	.box {
		width: 80px;
		height: 80px;
		background: #2086fd;
		display: -webkit-box;
		-webkit-box-orient: horizontal;
		-webkit-box-pack: center;
		-webkit-box-align: center;
		/* 警告 (157:3) You should write display: flex by final spec instead of display: box */
		display: -moz-box;
		-moz-box-orient: horizontal;
		-moz-box-pack: center;
		-moz-box-align: center;

		display: -o-box;
		-o-box-orient: horizontal;
		-o-box-pack: center;
		-o-box-align: center;

		display: -ms-box;
		-ms-box-orient: horizontal;
		-ms-box-pack: center;
		-ms-box-align: center;

		display: box;
		box-orient: horizontal;
		box-pack: center;
		box-align: center;
	}
	.clipped {
		clip-path: polygon(
			50% 0%,
			46.93% 3.1%,
			43.47% 0.43%,
			40.83% 3.9%,
			37.06% 1.7%,
			34.89% 5.49%,
			30.87% 3.81%,
			29.21% 7.85%,
			25% 6.7%,
			23.89% 10.92%,
			19.56% 10.33%,
			19.01% 14.66%,
			14.64% 14.64%,
			14.66% 19.01%,
			10.33% 19.56%,
			10.92% 23.89%,
			6.7% 25%,
			7.85% 29.21%,
			3.81% 30.87%,
			5.49% 34.89%,
			1.7% 37.06%,
			3.9% 40.83%,
			0.43% 43.47%,
			3.1% 46.93%,
			0% 50%,
			3.1% 53.07%,
			0.43% 56.53%,
			3.9% 59.17%,
			1.7% 62.94%,
			5.49% 65.11%,
			3.81% 69.13%,
			7.85% 70.79%,
			6.7% 75%,
			10.92% 76.11%,
			10.33% 80.44%,
			14.66% 80.99%,
			14.64% 85.36%,
			19.01% 85.34%,
			19.56% 89.67%,
			23.89% 89.08%,
			25% 93.3%,
			29.21% 92.15%,
			30.87% 96.19%,
			34.89% 94.51%,
			37.06% 98.3%,
			40.83% 96.1%,
			43.47% 99.57%,
			46.93% 96.9%,
			50% 100%,
			53.07% 96.9%,
			56.53% 99.57%,
			59.17% 96.1%,
			62.94% 98.3%,
			65.11% 94.51%,
			69.13% 96.19%,
			70.79% 92.15%,
			75% 93.3%,
			76.11% 89.08%,
			80.44% 89.67%,
			80.99% 85.34%,
			85.36% 85.36%,
			85.34% 80.99%,
			89.67% 80.44%,
			89.08% 76.11%,
			93.3% 75%,
			92.15% 70.79%,
			96.19% 69.13%,
			94.51% 65.11%,
			98.3% 62.94%,
			96.1% 59.17%,
			99.57% 56.53%,
			96.9% 53.07%,
			100% 50%,
			96.9% 46.93%,
			99.57% 43.47%,
			96.1% 40.83%,
			98.3% 37.06%,
			94.51% 34.89%,
			96.19% 30.87%,
			92.15% 29.21%,
			93.3% 25%,
			89.08% 23.89%,
			89.67% 19.56%,
			85.34% 19.01%,
			85.36% 14.64%,
			80.99% 14.66%,
			80.44% 10.33%,
			76.11% 10.92%,
			75% 6.7%,
			70.79% 7.85%,
			69.13% 3.81%,
			65.11% 5.49%,
			62.94% 1.7%,
			59.17% 3.9%,
			56.53% 0.43%,
			53.07% 3.1%
		);
	}
	.zonghefenshu {
		color: #ffb000;
		display: table-cell;
		vertical-align: middle;
		text-align: center;
		vertical-align: middle;
		height: 60px;
		width: 60px;
		font-size: 30px;
		background: #eaf4ff;
		border-radius: 50%;
		border: 1px solid #fff;
	}
	.zonghefenshu2 {
		color: #ffb000;
		display: flex;
		align-items: center;
		text-align: center;
		vertical-align: middle;
		height: 40px;
		width: 40px;
		font-size: 10px;
		background: #eaf4ff;
		border-radius: 50%;
		border: 1px solid #fff;
	}
	.zonghepaimingwai {
		width: 200px;
		height: 60px;
		margin-top: 10px;
		background: #2086fd;
		margin-left: -50px;
		display: table;
	}
	.marginleft60 {
		margin-left: 60px;
	}
	.progress-bar {
		padding-right: 50px;
		width: 240px;
		margin-right: -55px;
		-webkit-box-sizing: border-box;
		box-sizing: border-box;
	}
	.progress-bar,
	.el-progress-bar__inner::after,
	.el-progress-bar__innerText,
	.el-spinner {
		display: inline-block;
		vertical-align: middle;
	}
	.progress-bar__outer {
		height: 6px;
		border-radius: 100px;
		background-color: #ebeef5;
		overflow: hidden;
		position: relative;
		vertical-align: middle;
	}
	.progress-bar__inner {
		position: absolute;
		left: 0;
		top: 0;
		height: 100%;
		background-color: #409eff;
		text-align: right;
		border-radius: 100px;
		line-height: 1;
		white-space: nowrap;
		-webkit-transition: width 0.6s ease;
		transition: width 0.6s ease;
	}
	.progress-bar__innerText {
		color: #fff;
		font-size: 16px;
		margin: 1px 5px;
	}
	.marginleft60 {
		margin-left: 30px;
	}
	.progress-bar {
		padding-right: 50px;
		width: 240px;
		margin-right: -55px;
		-webkit-box-sizing: border-box;
		box-sizing: border-box;
	}
	.progress-bar,
	.el-progress-bar__inner::after,
	.el-progress-bar__innerText,
	.el-spinner {
		display: inline-block;
		vertical-align: middle;
	}
	.progress-bar__outer {
		height: 6px;
		border-radius: 100px;
		background-color: #ebeef5;
		overflow: hidden;
		position: relative;
		vertical-align: middle;
	}
	.progress-bar__inner {
		position: absolute;
		left: 0;
		top: 0;
		height: 100%;
		background-color: #409eff;
		text-align: right;
		border-radius: 100px;
		line-height: 1;
		white-space: nowrap;
		-webkit-transition: width 0.6s ease;
		transition: width 0.6s ease;
	}
	.progress-bar__innerText {
		color: #fff;
		font-size: 16px;
		margin: 1px 5px;
	}
}

.marginleft60 {
	margin-left: 30px;
}
.progress-bar {
	padding-right: 50px;
	width: 240px;
	margin-right: -55px;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
}
.progress-bar,
.el-progress-bar__inner::after,
.el-progress-bar__innerText,
.el-spinner {
	display: inline-block;
	vertical-align: middle;
}
.progress-bar__outer {
	height: 6px;
	border-radius: 100px;
	background-color: #ebeef5;
	overflow: hidden;
	position: relative;
	vertical-align: middle;
}
.progress-bar__inner {
	position: absolute;
	left: 0;
	top: 0;
	height: 100%;
	background-color: #409eff;
	text-align: right;
	border-radius: 100px;
	line-height: 1;
	white-space: nowrap;
	-webkit-transition: width 0.6s ease;
	transition: width 0.6s ease;
}
.progress-bar__innerText {
	color: #fff;
	font-size: 16px;
	margin: 1px 5px;
}
.headbox {
	width: 800px;

	padding: 20px;
}
.peoplename {
	font-size: 40px;
	font-weight: 600;
	font-family: simsun;
}
.github-badge {
	margin-left: 10px;
	display: inline-block;
	border-radius: 4px;
	text-shadow: none;
	font-size: 14px;
	color: #fff;
	margin-top: 13px;
}
.marlft {
	margin-left: 60px;
}
.github-badge .badge-subject {
	display: inline-block;
	background-color: #4d4d4d;
	padding: 4px 4px 4px 6px;
	border-top-left-radius: 4px;
	border-bottom-left-radius: 4px;
}
.github-badge .badge-value {
	display: inline-block;
	padding: 4px 6px 4px 4px;
	border-top-right-radius: 4px;
	border-bottom-right-radius: 4px;
}
.github-badge .bg-brightgreen {
	background-color: #4dc820 !important;
}
.github-badge .bg-orange {
	background-color: #ffa500 !important;
}
.github-badge .bg-yellow {
	background-color: #d8b024 !important;
}
.github-badge .bg-blueviolet {
	background-color: #8833d7 !important;
}
.github-badge .bg-pink {
	background-color: #f26bae !important;
}
.github-badge .bg-red {
	background-color: #e05d44 !important;
}
.github-badge .bg-blue {
	background-color: #007ec6 !important;
}
.github-badge .bg-lightgrey {
	background-color: #9f9f9f !important;
}
.github-badge .bg-grey,
.github-badge .bg-gray {
	background-color: #555 !important;
}
.github-badge .bg-lightgrey,
.github-badge .bg-lightgray {
	background-color: #9f9f9f !important;
}
.marginleft20 {
	margin-left: 20px;
}
/* 矩形旁边的三角形 */
.pentagon {
	margin: 10px 0 0 0;

	position: relative;

	height: 0;

	width: 0;

	border-left: 20px solid #2086fd;

	border-right: 20px solid transparent;

	border-bottom: 30px solid transparent;

	border-top: 30px solid transparent;
}
/* shiyingxing */
.newst .zonghepaimingwai {
	width: 200px;
	height: 60px;
	margin-top: 10px;
	background: #00ccff;
	margin-left: -50px;
	display: table;
}
.newst .box {
	width: 80px;
	height: 80px;
	background: #00ccff;
	display: -webkit-box;
	-webkit-box-orient: horizontal;
	-webkit-box-pack: center;
	-webkit-box-align: center;
	/* 警告 (157:3) You should write display: flex by final spec instead of display: box */
	display: -moz-box;
	-moz-box-orient: horizontal;
	-moz-box-pack: center;
	-moz-box-align: center;

	display: -o-box;
	-o-box-orient: horizontal;
	-o-box-pack: center;
	-o-box-align: center;

	display: -ms-box;
	-ms-box-orient: horizontal;
	-ms-box-pack: center;
	-ms-box-align: center;

	display: box;
	box-orient: horizontal;
	box-pack: center;
	box-align: center;
}
.newst .pentagon {
	margin: 10px 0 0 0;

	position: relative;

	height: 0;

	width: 0;

	border-left: 20px solid #00ccff;

	border-right: 20px solid transparent;

	border-bottom: 30px solid transparent;

	border-top: 30px solid transparent;
}
/*  */
.widthbox {
	width: 300px;
}
/* 原型锯齿 */
.box {
	width: 80px;
	height: 80px;
	background: #2086fd;
	display: -webkit-box;
	-webkit-box-orient: horizontal;
	-webkit-box-pack: center;
	-webkit-box-align: center;
	/* 警告 (157:3) You should write display: flex by final spec instead of display: box */
	display: -moz-box;
	-moz-box-orient: horizontal;
	-moz-box-pack: center;
	-moz-box-align: center;

	display: -o-box;
	-o-box-orient: horizontal;
	-o-box-pack: center;
	-o-box-align: center;

	display: -ms-box;
	-ms-box-orient: horizontal;
	-ms-box-pack: center;
	-ms-box-align: center;

	display: box;
	box-orient: horizontal;
	box-pack: center;
	box-align: center;
}
.clipped {
	clip-path: polygon(
		50% 0%,
		46.93% 3.1%,
		43.47% 0.43%,
		40.83% 3.9%,
		37.06% 1.7%,
		34.89% 5.49%,
		30.87% 3.81%,
		29.21% 7.85%,
		25% 6.7%,
		23.89% 10.92%,
		19.56% 10.33%,
		19.01% 14.66%,
		14.64% 14.64%,
		14.66% 19.01%,
		10.33% 19.56%,
		10.92% 23.89%,
		6.7% 25%,
		7.85% 29.21%,
		3.81% 30.87%,
		5.49% 34.89%,
		1.7% 37.06%,
		3.9% 40.83%,
		0.43% 43.47%,
		3.1% 46.93%,
		0% 50%,
		3.1% 53.07%,
		0.43% 56.53%,
		3.9% 59.17%,
		1.7% 62.94%,
		5.49% 65.11%,
		3.81% 69.13%,
		7.85% 70.79%,
		6.7% 75%,
		10.92% 76.11%,
		10.33% 80.44%,
		14.66% 80.99%,
		14.64% 85.36%,
		19.01% 85.34%,
		19.56% 89.67%,
		23.89% 89.08%,
		25% 93.3%,
		29.21% 92.15%,
		30.87% 96.19%,
		34.89% 94.51%,
		37.06% 98.3%,
		40.83% 96.1%,
		43.47% 99.57%,
		46.93% 96.9%,
		50% 100%,
		53.07% 96.9%,
		56.53% 99.57%,
		59.17% 96.1%,
		62.94% 98.3%,
		65.11% 94.51%,
		69.13% 96.19%,
		70.79% 92.15%,
		75% 93.3%,
		76.11% 89.08%,
		80.44% 89.67%,
		80.99% 85.34%,
		85.36% 85.36%,
		85.34% 80.99%,
		89.67% 80.44%,
		89.08% 76.11%,
		93.3% 75%,
		92.15% 70.79%,
		96.19% 69.13%,
		94.51% 65.11%,
		98.3% 62.94%,
		96.1% 59.17%,
		99.57% 56.53%,
		96.9% 53.07%,
		100% 50%,
		96.9% 46.93%,
		99.57% 43.47%,
		96.1% 40.83%,
		98.3% 37.06%,
		94.51% 34.89%,
		96.19% 30.87%,
		92.15% 29.21%,
		93.3% 25%,
		89.08% 23.89%,
		89.67% 19.56%,
		85.34% 19.01%,
		85.36% 14.64%,
		80.99% 14.66%,
		80.44% 10.33%,
		76.11% 10.92%,
		75% 6.7%,
		70.79% 7.85%,
		69.13% 3.81%,
		65.11% 5.49%,
		62.94% 1.7%,
		59.17% 3.9%,
		56.53% 0.43%,
		53.07% 3.1%
	);
}
.zonghefenshu {
	color: #ffb000;
	display: table-cell;
	vertical-align: middle;
	text-align: center;
	vertical-align: middle;
	height: 60px;
	width: 60px;
	font-size: 30px;
	background: #eaf4ff;
	border-radius: 50%;
	border: 1px solid #fff;
}
.zonghefenshu2 {
	color: #ffb000;
	display: flex;
	align-items: center;
	text-align: center;
	vertical-align: middle;
	height: 40px;
	width: 40px;
	font-size: 10px;
	background: #eaf4ff;
	border-radius: 50%;
	border: 1px solid #fff;
}
.zonghepaimingwai {
	width: 200px;
	height: 60px;
	margin-top: 10px;
	background: #2086fd;
	margin-left: -50px;
	display: table;
}
.marginleft60 {
	margin-left: 60px;
}
.progress-bar {
	padding-right: 50px;
	width: 240px;
	margin-right: -55px;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
}
.progress-bar,
.el-progress-bar__inner::after,
.el-progress-bar__innerText,
.el-spinner {
	display: inline-block;
	vertical-align: middle;
}
.progress-bar__outer {
	height: 6px;
	border-radius: 100px;
	background-color: #ebeef5;
	overflow: hidden;
	position: relative;
	vertical-align: middle;
}
.progress-bar__inner {
	position: absolute;
	left: 0;
	top: 0;
	height: 100%;
	background-color: #409eff;
	text-align: right;
	border-radius: 100px;
	line-height: 1;
	white-space: nowrap;
	-webkit-transition: width 0.6s ease;
	transition: width 0.6s ease;
}
.progress-bar__innerText {
	color: #fff;
	font-size: 16px;
	margin: 1px 5px;
}
