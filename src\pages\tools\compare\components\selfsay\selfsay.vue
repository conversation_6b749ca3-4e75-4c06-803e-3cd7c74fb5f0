<!--  -->
<template>
  <div class="selfsay">
    <div style="display: flex; align-items: center; width: 100%; position: relative">
      <div style="display: flex; align-items: center">
        <div class="TitltCompare">备注</div>
      </div>
      <div style="right: 0px; position: absolute">
        <i @click="showdetailchoose = !showdetailchoose"
           :style="showdetailchoose ? 'font-size:20px;cursor: pointer;color:#4096FF' : 'font-size:20px;cursor: pointer;'"
           class="el-icon-edit"></i>
      </div>
    </div>
    <div v-show="showdetailchoose">
      <div style="height: 10px"></div>
      <el-input v-model="input"
                type="textarea"
                placeholder="请输入备注"></el-input>

      <div style="height: 10px"></div>
      <div style="width: 100%; text-align: right"><el-button @click="submit">提交</el-button></div>
    </div>
    <div style="height: 20px"></div>
    <div style="font-size: 16px; font-weight: 600; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{ input }}</div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { getDescriptionCompare, postDescriptionCompare } from '@/api/pages/tools/compare.js';

export default {
  //import引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    comparetype: {
      type: String,
      default: 'manager' //fund
    },
    id: {
      type: String,
      default: '30189741,30441407'
    },
    type: {
      type: String,
      default: 'equity'
    },
    name: {
      type: String,
      default: '萧楠,胡昕炜'
    }
  },
  data () {
    //这里存放数据
    return {
      input: '',
      showdetailchoose: false
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    async submit () {
      let data = await postDescriptionCompare({ code: this.id, description: this.input });
      if (data) {
        this.$message.success('备注修改成功');
        this.showdetailchoose = false;
      }
    },
    getdata () {
      if (this.comparetype == 'manager') {
        this.getmanagerdata();
      } else {
        this.gefunddata();
      }
    },
    async getmanagerdata () {
      let data = await getDescriptionCompare({ code: this.id });
      if (data) {
        this.input = data.data.description;
      }
    },
    async gefunddata () {
      let data = await getDescriptionCompare({ code: this.id });
      if (data) {
        this.input = data.data.description;
      }
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () { },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
</style>
