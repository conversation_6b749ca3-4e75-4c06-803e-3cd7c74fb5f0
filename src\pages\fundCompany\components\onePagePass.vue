<template>
	<div>
		<div class="flex_card">
			<div v-for="item in templateList" :key="item.value" v-show="item.isshow" :class="item.type">
				<component :is="item.is" :ref="item.value" @resolveFather="item.methods" v-loading="loading"></component>
			</div>
		</div>
	</div>
</template>

<script>
// 所属基金概况
import allTypeFundBasicInfo from '@/components/components/components/allTypeFundBasicInfo/index.vue';
// 全部基金
import allFunds from '@/components/components/components/allFunds/index.vue';
// 全部基金经理
import allFundManagers from '@/components/components/components/allFundManagers/index.vue';
// 各类型基金收益
import allTypeFundCumReturn from '@/components/components/components/allTypeFundCumReturn/index.vue';
// 新发基金
import newDevelopmentFund from '@/components/components/components/newDevelopmentFund/index.vue';
// 基金公司规模
import fundCompanyNetasset from '@/components/components/components/fundCompanyNetasset/index.vue';
// 最新基金业绩排名
import performanceRanking from '@/components/components/components/performanceRanking/index.vue';
// 风险收益对比
import riskReturnCompare from '@/components/components/components/riskReturnCompare/index.vue';
// 行业配置表现
import industryAllocationPerformance from '@/components/components/industryAllocationPerformance/index.vue';
// 基金资产配置分析
import allocationAnalysis from '@/components/components/components/allocationAnalysis/index.vue';

import {
	getNewDevelopmentFund,
	getFundRecentReturn,
	getIndustryInfo,
	getHoldManagerMsg,
	getHoldFundMsg,
	getAllTypeFundBasicInfo,
	getAllTypeFundCumReturn,
	getCompanyAllocationDetails
} from '@/api/pages/SystemOther.js';

export default {
	components: {
		allFunds,
		allFundManagers,
		allTypeFundBasicInfo,
		allTypeFundCumReturn,
		newDevelopmentFund,
		fundCompanyNetasset,
		performanceRanking,
		riskReturnCompare,
		industryAllocationPerformance,
		allocationAnalysis
	},
	data() {
		return {
			templateList: [],
			info: {},
			loading: true,
			requestOver: [],
			requestAll: [],
			industryInfo: null,
			newDevelopmentFund: null,
			allTypeFundCumReturn: null,
			allTypeFundBasicInfo: null,
			fundCompanyNetasset: null,
			performanceRanking: null,
			allocationDetails: null,
			holdFundMsgData: null,
			cumReturnDate: null
		};
	},
	methods: {
		// 获取数据
		getData(data) {
			this.info = data;
			this.loading = true;
			this.requestOver = [];
			this.watch();
			this.formatTemplatList();
		},
		// 添加watch函数式监听(因为watch侦听器在页面切换时失效)
		watch() {
			let unwatch = this.$watch('requestOver', (val, old) => {
				this.loading = false;
				this.$compontentsWatch(val, this);
			});
		},
		// 格式化模板列表
		formatTemplatList() {
			this.requestAll = [];
			let requestList = [];
			this.templateList.map((item) => {
				if (item.methods && typeof item.methods == 'string') {
					item.methods = this?.[item.methods];
				}
				if (
					item.typelist.some((obj) => {
						return this.info.type.indexOf(obj) != -1 || obj == '*';
					})
				) {
					this.requestAll.push(item);
					if (requestList.indexOf(item.getRequestData) == -1) {
						if (item.getRequestData && item.getRequestData !== 'None') {
							requestList.push(item.getRequestData);
							this?.[item.getRequestData]();
						}
					}
				}
			});
		},
		// 接收/返回组件列表
		getTemplateList(list) {
			if (list) {
				// 是光大
				if (this.isGDBank()) {
					this.templateList = list.filter((item) => {
						return item.isshow;
					});
				} else {
					// 不是光大
					this.templateList = list.filter((item) => {
						return item.is !== 'GDBankDetailequity' && item.is !== 'GDBankDetailbond' && item.isshow;
					});
				}
				this.$forceUpdate();
			} else {
				return this.templateList;
			}
		},
		// 判断是否是光大
		isGDBank() {
			if (window.localStorage.getItem('mty_modulesName') == 'GDBank') {
				this.showGD = true;
				return true;
			} else {
				this.showGD = false;
				return false;
			}
		},
		// 获取所属基金概况
		async getAllTypeFundBaicInfo() {
			if (this.getCacheData('allTypeFundBasicInfo')) {
				this.allTypeFundBasicInfo = this.getCacheData('allTypeFundBasicInfo');
				this.requestOver.push('getAllTypeFundBaicInfo');
			} else {
				let data = await getAllTypeFundBasicInfo({ code: this.info.code, type: this.info.type.join(',') });
				if (data?.mtycode == 200) {
					this.allTypeFundBasicInfo = data?.data;
					this.setCacheData('allTypeFundBasicInfo', this.allTypeFundBasicInfo);
				}
				this.requestOver.push('getAllTypeFundBaicInfo');
			}
		},
		// 获取所属基金概况
		getAllTypeFundBaicInfoData() {
			this.$refs['fundCompanyNetasset']?.[0]?.getData(this.allTypeFundBasicInfo?.history);
			this.$refs['allTypeFundBasicInfo']?.[0]?.getData(this.allTypeFundBasicInfo?.newest);
		},
		// 获取各类型基金收益
		getAllTypeFundCumReturn() {
			this.requestOver.push('getAllTypeFundCumReturn');
		},
		// 获取各类型基金收益数据
		async getAllTypeFundCumReturnData() {
			this.$refs['allTypeFundCumReturn']?.[0]?.getAllTypeFundCumReturn(this.info);
		},
		// 获取公募基金规模
		async getFundCompanyNetasset() {
			this.$refs['fundCompanyNetasset']?.[0]?.getFundCompanyNetasset();
		},
		// 获取公募基金规模类型列表
		getFundCompanyNetassetList() {
			this.requestOver.push('getFundCompanyNetassetList');
		},
		// 获取公募基金规模
		getFundCompanyNetassetData() {
			this.$refs['fundCompanyNetasset']?.[0]?.getTypeList(this.info);
		},
		// 获取基金业绩排名基金列表
		getPerformanceRankingList() {
			this.requestOver.push('getPerformanceRankingList');
		},
		// 获取最新基金业绩排名
		getPerformanceRankingData() {
			this.$refs['performanceRanking']?.[0]?.getTypeList(this.info);
		},
		// 获取基金资产配置
		async getCompanyAllocationDetails() {
			if (this.getCacheData('allocationDetails')) {
				this.allocationDetails = this.getCacheData('allocationDetails');
			} else {
				let data = await getCompanyAllocationDetails({
					code: this.info.code,
					start_date: '',
					end_date: ''
				});
				if (data?.mtycode == 200) {
					this.allocationDetails = data?.data;
					this.setCacheData('allocationDetails', this.allocationDetails);
				}
			}
			this.requestOver.push('getCompanyAllocationDetails');
		},
		// 获取基金资产配置数据
		getCompanyAllocationDetailsData() {
			this.$refs['allocationAnalysis']?.[0]?.getData(this.allocationDetails);
		},
		// 获取全部基金
		async getAllFunds() {
			if (this.getCacheData('holdFundMsgData')) {
				this.holdFundMsgData = this.getCacheData('holdFundMsgData');
			} else {
				let data = await getHoldFundMsg({ code: this.info.code, type: 'equity' });
				if (data?.mtycode == 200) {
					this.holdFundMsgData = data?.data;
					this.setCacheData('holdFundMsgData', this.holdFundMsgData);
				} else {
					this.$refs['allFunds']?.[0]?.hideLoading();
				}
			}
			this.requestOver.push('getAllFunds');
		},
		// 获取全部基金数据
		getAllFundsData() {
			this.$refs['allFunds']?.[0]?.getData(this.holdFundMsgData, this.info.type);
		},
		// 获取基金经理类型
		async getHoldType() {
			this.requestOver.push('getHoldType');
		},
		// 获取基金经理数据
		getAllFundManagersData() {
			this.$refs['allFundManagers']?.[0]?.getTypeList(this.info);
		}
	}
};
</script>

<style></style>
