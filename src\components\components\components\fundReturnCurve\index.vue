<template>
	<div
		id="fundReturnCurve"
		style="width: calc(100% - 20px); position: relative"
		:class="info.flag == 2 ? 'manager_return ml-16' : 'ml-16'"
		v-loading="loading"
	>
		<div class="fund_return_curve px-20">
			<div class="flex_between return_curve_title py-20">
				<div class="flex_start">
					<div class="mr-8">{{ info.name }}</div>
					<div class="font_weight_500" :style="cum_return > 0 ? 'color: #cf1322' : cum_return < 0 ? 'color:#389E0D' : ''">
						{{ cum_return }}%
					</div>
				</div>
				<div style="cursor: pointer" @click="downloadImage">
					<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
						<rect x="0.5" y="0.5" width="31" height="31" rx="3.5" fill="white" />
						<path
							d="M15.8874 18.6574C15.9007 18.6744 15.9178 18.6882 15.9373 18.6977C15.9568 18.7072 15.9782 18.7122 15.9999 18.7122C16.0215 18.7122 16.0429 18.7072 16.0624 18.6977C16.0819 18.6882 16.099 18.6744 16.1124 18.6574L18.1124 16.127C18.1856 16.0342 18.1195 15.8967 17.9999 15.8967H16.6766V9.85379C16.6766 9.77522 16.6124 9.71094 16.5338 9.71094H15.4624C15.3838 9.71094 15.3195 9.77522 15.3195 9.85379V15.8949H13.9999C13.8802 15.8949 13.8141 16.0324 13.8874 16.1252L15.8874 18.6574ZM22.5356 18.0324H21.4641C21.3856 18.0324 21.3213 18.0967 21.3213 18.1752V20.9252H10.6784V18.1752C10.6784 18.0967 10.6141 18.0324 10.5356 18.0324H9.46415C9.38557 18.0324 9.32129 18.0967 9.32129 18.1752V21.7109C9.32129 22.027 9.57665 22.2824 9.89272 22.2824H22.107C22.4231 22.2824 22.6784 22.027 22.6784 21.7109V18.1752C22.6784 18.0967 22.6141 18.0324 22.5356 18.0324Z"
							fill="black"
							fill-opacity="0.45"
						/>
						<rect x="0.5" y="0.5" width="31" height="31" rx="3.5" stroke="#D9D9D9" />
					</svg>
				</div>
			</div>
			<div class="flex_between mt-20">
				<div class="flex_start">
					<div>
						<el-select v-model="value" :disabled="selectCheck" @change="changeTime" style="width: 120px; height: 32px">
							<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
						</el-select>
					</div>
					<div class="flex_start">
						<div class="choose_time_checkbox mx-12">
							<el-checkbox v-model="selectCheck" @change="selectCheck = !selectCheck">自定义时间区间</el-checkbox>
						</div>
						<div v-show="selectCheck">
							<el-date-picker
								v-model="value1"
								type="daterange"
								style="width: 240px"
								unlink-panels
								range-separator="-"
								start-placeholder="开始日期"
								end-placeholder="结束日期"
								value-format="yyyy-MM-dd"
								@change="changeTime"
							>
							</el-date-picker>
						</div>
					</div>
				</div>
				<div v-show="currentList.length != 0" class="clear_btn">
					<el-button type="primary" @click="clearMouseDown">清除所有切片</el-button>
				</div>
			</div>
			<div class="return_chart_main" id="return_chart_main">
				<el-empty v-show="empty" class="return_chart_option"></el-empty>
				<v-chart
					v-show="!empty"
					ref="fundReturnCurveChart"
					class="return_chart_option"
					autoresize
					:options="yejioptions"
					@click.native="watchMouseDown"
				/>
				<div class="return_chart_legend flex_center mt-12">
					<div class="flex_start" @click="changeShowHide(info.name)">
						<div class="lengend_left" :style="hideList.includes(info.name) ? '' : 'background: #4096ff;'"></div>
						<div class="pl-12 pr-16 py-5 mr-12 lengend_border" :style="hideList.includes(info.name) ? 'color:rgba(0,0,0,0.45)' : ''">
							<div>{{ info.name }}</div>
						</div>
					</div>

					<div class="mr-12" style="border-left: 6px solid #ffb75a; border-radius: 4px">
						<el-select v-model="activeBenchmark" @change="getRateInfo">
							<el-option v-for="item in benchmarkList" :key="item.code" :label="item.name" :value="item.code"> </el-option>
						</el-select>
					</div>
					<div class="flex_start" @click="changeShowHide('超额收益')">
						<div class="lengend_left" :style="hideList.includes('超额收益') ? '' : 'background: #7388a9'"></div>
						<div
							class="pl-12 pr-16 py-5 mr-12 lengend_border flex_start"
							:style="hideList.includes('超额收益') ? 'color:rgba(0,0,0,0.45)' : ''"
						>
							<div>超额收益</div>
						</div>
					</div>
					<div class="flex_start" @click="changeShowHide('回撤')">
						<div class="lengend_left" :style="hideList.includes('回撤') ? '' : 'background: #6f80dd'"></div>
						<div
							class="pl-12 pr-16 py-5 mr-12 lengend_border flex_start"
							:style="hideList.includes('回撤') ? 'color:rgba(0,0,0,0.45)' : ''"
						>
							<div>回撤</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="return_click_warning px-12 py-12">
			<div class="flex_start">
				<div class="mr-8" style="color: #4096ff">
					<i class="el-icon-warning-outline"></i>
				</div>
				<div>点击任何位置可进行时间切片操作</div>
			</div>
		</div>
	</div>
</template>
<script>
// linear-gradient(180deg, rgba(69, 118, 233, 0.25) 0%, rgba(69, 118, 233, 0.00) 100%);

import { exportChart, exportTitle } from '@/utils/exportWord.js';
import { lineChartOption } from '@/utils/chartStyle.js';
import { getRateInfo, getBenchmarkList } from '@/api/pages/Analysis.js';
import { abnorlmalPoint } from '@/api/pages/SystemAlpha.js';
export default {
	data() {
		return {
			options: [
				{ label: '成立以来', value: 8 },
				{ label: '今年以来', value: 7 },
				{ label: '近五年', value: 6 },
				{ label: '近三年', value: 5 },
				{ label: '近一年', value: 4 },
				{ label: '近半年', value: 3 },
				{ label: '近三月', value: 2 },
				{ label: '近一月', value: 1 }
			],
			selectCheck: false,
			value: 8,
			value1: '',
			radio1: '',
			activeBenchmark: '',
			abnormal: [],
			benchmarkList: [],
			dateList: [],
			yejioptions: {},
			colorList: [
				{
					label: 'self',
					value: '#4096ff'
				},
				{
					label: 'index',
					value: '#ffb75a'
				},
				{
					label: 'down',
					value: '#6F80DD'
				},
				{
					label: 'excee',
					value: '#7388A9'
				}
			],
			info: {},
			current: '',
			currentList: [],
			timer: null,
			series: [],
			start_date: '',
			end_date: '',
			cum_return: 0,
			loading: true,
			hideList: [],
			empty: false
		};
	},
	methods: {
		// 获取父组件传递数据&入口函数
		getData(info) {
			this.loading = true;
			this.requestData = [];
			this.info = info;
			this.abnorlmalPoint();
			this.getBenchmarkList();
		},
		// 获取异常点数据
		async abnorlmalPoint() {
			this.abnormal = [];
			let data = await abnorlmalPoint({ code: this.info.code });
			if (data?.mtycode == 200) {
				this.abnormal = data?.data;
			}
		},
		// 发送请求，获取基准列表
		async getBenchmarkList() {
			let data = await getBenchmarkList({
				code: this.info.code,
				flag: this.info.flag,
				type: this.info.type
			});
			this.benchmarkList = [];
			this.activeBenchmark = '';
			if (data?.mtycode == 200) {
				this.benchmarkList = data?.data
					.sort((a, b) => {
						return b.isdefault - a.isdefault;
					})
					.map((v) => {
						return { code: v.indexCode, name: v.indexName, flag: v.flag };
					});
				this.activeBenchmark = this.benchmarkList?.[0]?.code;
			} else {
				this.benchmarkList = this.COMMON.default_index[this.info.type].map((v) => {
					return { code: v.indexCode, name: v.indexName, flag: '6' };
				});
				this.activeBenchmark = this.benchmarkList?.[0]?.code;
			}
			this.getRateInfo();
		},
		// 发送请求，获取收益数据
		async getRateInfo() {
			this.loading = true;
			let codes = [this.info.code];
			let flag = [this.info.flag];
			if (this.activeBenchmark) {
				codes.push(this.activeBenchmark);
				flag.push(this.benchmarkList.find((v) => v.code == this.activeBenchmark)?.flag);
			}
			let data = await getRateInfo({
				codes,
				type: this.info.type,
				flag,
				start_date: this.start_date || '',
				end_date: this.end_date || ''
			});
			this.loading = false;
			if (data?.mtycode == 200) {
				this.empty = false;
				this.formatRateData(data?.data);
			} else {
				this.empty = true;
			}
		},
		// 切换时间区间
		changeTime() {
			this.loading = true;
			if (this.selectCheck) {
				this.start_date = this.value1?.[0];
				this.end_date = this.value1?.[1];
			} else {
				this.changeTimeSelect();
			}
			this.getRateInfo();
		},
		// 监听事件区间选择
		changeTimeSelect() {
			this.end_date = this.moment(this.moment()).format('YYYY-MM-DD');
			switch (this.value) {
				// 近一月
				case 1:
					this.start_date = this.moment(this.moment()).subtract(1, 'months').format('YYYY-MM-DD');
					break;
				// 近三月
				case 2:
					this.start_date = this.moment(this.moment()).subtract(3, 'months').format('YYYY-MM-DD');
					break;
				// 近半年
				case 3:
					this.start_date = this.moment(this.moment()).subtract(6, 'months').format('YYYY-MM-DD');
					break;
				// 近一年
				case 4:
					this.start_date = this.moment(this.moment()).subtract(1, 'years').format('YYYY-MM-DD');
					break;
				// 近三年
				case 5:
					this.start_date = this.moment(this.moment()).subtract(3, 'years').format('YYYY-MM-DD');
					break;
				// 近五年
				case 6:
					this.start_date = this.moment(this.moment()).subtract(5, 'years').format('YYYY-MM-DD');
					break;
				// 今年以来
				case 7:
					this.start_date = this.moment(this.moment().year() + '-01-01').format('YYYY-MM-DD');
					break;
				// 成立以来
				case 8:
					this.start_date = '';
					this.end_date = '';
					break;
			}
		},
		// 切换显示隐藏
		changeShowHide(val) {
			let index = this.hideList.indexOf(val);
			if (index != -1) {
				this.hideList.splice(index, 1);
			} else {
				this.hideList.push(val);
			}
			let nameList = Array.from(new Set(this.yejioptions.series.map((v) => v.name)));
			let legend = {
				show: false,
				data: nameList,
				selected: {}
			};
			this.hideList.map((v) => {
				legend.selected[v] = false;
			});
			this.yejioptions = {
				...this.yejioptions,
				legend
			};
		},
		// 格式化接口获取的收益数据
		formatRateData(data) {
			// 对返回时间进行去重、排序，获得正序的时间序列
			this.dateList = Array.from(new Set(data.filter((v) => v.code == this.info.code).map((v) => v.date))).sort((a, b) => {
				return this.moment(this.moment(a, 'YYYY-MM-DD').format()).isBefore(this.moment(b, 'YYYY-MM-DD').format()) ? -1 : 1;
			});
			// 定义数据项列表
			let codeList = [this.info.code];
			if (this.activeBenchmark) {
				codeList.push(this.activeBenchmark);
			}
			let result = [];
			this.dateList.map((date) => {
				codeList.map((code) => {
					let index = result.findIndex((v) => v.code == code);
					let value = data.find((v) => v.code == code && v.date == date)?.rate || 0;
					if (index == -1) {
						result.push({
							code,
							name: this.info.code == code ? this.info.name : this.benchmarkList.find((v) => v.code == code)?.name,
							date: [date],
							value: [value]
						});
					} else {
						result[index].date.push(date);
						result[index].value.push(value);
					}
				});
			});
			this.series = result;
			// 将自身和指数数据格式化数据为echart数据格式
			let series = [...this.formatSelfIndexSeries(result), ...this.formatDown(result), ...this.formarExcess(result)];

			this.drawAction(series);
		},
		// 将自身和指数数据格式化数据为echart数据格式
		formatSelfIndexSeries(data) {
			let series = [];
			data.map((item) => {
				let color = '';
				if (item.name == this.info.name) {
					color = '#4096ff';
				} else {
					color = '#ffb75a';
				}
				let items = {
					name: item.name,
					type: 'line',
					symbol: 'none',
					data: this.computedReturn(item.value).map((v, i) => [item.date[i], v]),
					connectNulls: true,
					lineStyle: {
						color
					}
				};
				if (item.name == this.info.name) {
					this.cum_return = (items.data?.[items.data.length - 1]?.[1] * 100).toFixed(2);
					items['areaStyle'] = {
						color: new echarts.graphic.LinearGradient(1, 1, 1, 0, [
							{ offset: 0, color: 'rgba(69, 118, 233, 0.00)' },
							{ offset: 1, color: 'rgba(69, 118, 233, 0.25)' }
						])
					};
				}
				series.push({ ...item, ...items });
			});
			return series;
		},
		/**
		 * 根据自身日涨跌幅数据计算出回撤
		 * 入参data
		 * [{code:'string',name:'string',date:array,value:array}]
		 */
		formatDown(data) {
			let series = [];
			data
				.filter((v) => v.code == this.info.code)
				.map((obj) => {
					let downData = [];
					let max = 0;
					this.computedDown(obj.value).map((item, index) => {
						if (item >= max) {
							max = item;
							downData.push([obj.date[index].slice(0, 10), 0]);
						} else {
							downData.push([obj.date[index].slice(0, 10), ((max - item) / max) * -1]);
						}
					});
					series.push({
						name: '回撤',
						type: 'line',
						yAxisIndex: 1,
						connectNulls: true,
						symbol: 'none',
						lineStyle: {
							width: 0,
							color: 'rgba(111, 128, 221, 0.5)'
						},
						areaStyle: {
							color: 'rgba(111, 128, 221, 0.5)'
						},
						data: downData
					});
				});
			return series;
		},
		/**
		 * 根据自身和基准日涨跌幅数据计算出超额收益
		 * 入参data
		 * [{code:'string',name:'string',date:array,value:array}]
		 * 超额收益算法：cumreturn[n]-indexreturn[n]
		 *
		 * 修改超额收益算法：(cumreturn[n]+1) / (indexreturn[n]+1) - 1
		 */
		formarExcess(data) {
			let series = [];
			let cumData = data.map((v) => {
				return { ...v, value: this.computedReturn(v.value) };
			});
			let selfData = cumData?.filter((v) => v.code == this.info.code);
			let indexData = cumData?.filter((v) => v.code !== this.info.code);
			selfData.map((obj) => {
				let excessData = [];
				obj.date.map((item, i) => {
					let indexItem = indexData.find((v) => v.date.indexOf(item) !== -1);
					let index = indexItem?.date.indexOf(item);
					excessData.push([item.slice(0, 10), (obj.value[i] + 1) / (indexItem.value[index] + 1) - 1]);
				});
				series.push({
					name: '超额收益',
					type: 'line',
					connectNulls: true,
					symbol: 'none',
					lineStyle: {
						width: 0,
						color: 'rgba(115, 136, 169, 0.5)'
					},
					areaStyle: {
						color: 'rgba(115, 136, 169, 0.5)'
					},
					data: excessData
				});
			});

			return series;
		},
		// 画图
		drawAction(data) {
			this.loading = false;
			let series = data;
			if (this.abnormal.length > 0) {
				series[series.findIndex((item) => item.name == this.info.name)]['markPoint'] = {
					data: []
				};
				for (let i = 0; i < this.abnormal.length; i++) {
					if (
						series[series.findIndex((item) => item.name == this.info.name)].data.filter((item) => item[0] == this.abnormal[i].date).length >
						0
					) {
						series[series.findIndex((item) => item.name == this.info.name)]['markPoint'].data.push({
							symbol: 'pin',
							emphasis: {
								disabled: false,
								label: {
									show: true,
									formatter: `异常点:${this.abnormal[i].reason}`,
									position: 'top',
									backgroundColor: 'rgba(0, 0, 0, 0.45)',
									borderRadius: 4,
									padding: [0, 9],
									width: 164,
									height: 26,
									color: 'white',
									lineHeight: 32,
									align: 'center',
									fontFamily: 'PingFang',
									fontStyle: 'normal',
									fontWeight: 400,
									fontSize: '12px'
								}
							},
							coord: [
								this.abnormal[i].date,
								series[series.findIndex((item) => item.name == this.info.name)].data.filter(
									(item) => item[0] == this.abnormal[i].date
								)[0][1]
							],
							symbolSize: 20,
							animation: true,
							label: {
								show: false,
								color: '#000'
							},
							itemStyle: { color: '#4096ff' }
						});
					}
				}
			}

			let that = this;
			let options = lineChartOption({
				color: this.colorList.map((v) => v.value),
				toolbox: 'none',
				grid: {
					top: '16px',
					left: '62px',
					containLabel: false
				},
				tooltip: {
					backgroundColor: '#ffffff',
					formatter: function (obj) {
						that.current = obj?.[0].axisValue;
						var value = `<div style="font-size:14px;">` + obj?.[0].axisValue + `</div>`;
						for (let i = 0; i < obj.length; i++) {
							value +=
								`<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
								`<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
								obj?.[i].color +
								`;"></div>` +
								`<div style="font-family: PingFang SC;">` +
								obj?.[i].seriesName +
								'</div></div>' +
								`<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
								(Number(obj?.[i].value?.[1]) * 100).toFixed(2) +
								'%</div>' +
								`</div>`;
						}
						return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
					}
				},
				xAxis: [
					{
						data: this.dateList,
						axisLabel: {
							interval: function (index, vlaue) {}
						}
					}
				],
				yAxis: [
					{
						min: this.computedMinMax(data).min,
						max: this.computedMinMax(data).max,
						formatter: function (obj) {
							return (obj * 100 > 10 || -(obj * 100) > 10 ? (obj * 100).toFixed(0) : (obj * 100).toFixed(2)) + '%';
						}
					},
					{
						show: false,
						max: 0,
						min: 0 - (this.computedMinMax(data).max - this.computedMinMax(data).min),
						formatter: function (obj) {
							return (obj * 100 > 10 || -(obj * 100) > 10 ? (obj * 100).toFixed(0) : (obj * 100).toFixed(2)) + '%';
						}
					}
				],
				series
			});
			this.yejioptions = options;
			console.log(options);
		},
		// 计算最大最小值
		computedMinMax(data) {
			let value_all = [];
			data
				.filter((v) => v.name != '回撤')
				.map((obj) => {
					obj.data.map((item) => {
						value_all.push(item[1]);
					});
				});
			return { min: Math.min(...value_all), max: Math.max(...value_all) };
		},
		// 累计收益计算
		computedReturn(data) {
			let cum_return = 1;
			let cum = data.map((item) => {
				cum_return = cum_return * (1 + item);
				return cum_return - 1;
			});
			return cum;
		},
		// 回撤计算
		computedDown(data) {
			let cum_return = 1;
			let cum = data.map((item) => {
				cum_return = cum_return * (1 + item);
				return cum_return;
			});
			return cum;
		},
		// 清除所有切片
		clearMouseDown() {
			this.current = '';
			this.currentList = [];
			let series = [...this.formatSelfIndexSeries(this.series), ...this.formatDown(this.series), ...this.formarExcess(this.series)];

			this.drawAction(series);
		},
		// 监听点击图发散
		watchMouseDown(val) {
			this.loading = true;
			if (this.timer || this.currentList.indexOf(this.current?.[0]) !== -1 || !this.current?.[0]) {
				this.currentList.splice(this.currentList.indexOf(this.current?.[0]), 1);
				this.timer = setTimeout(() => {
					this.componentdMouseDown();
				}, 100);
			} else {
				this.currentList.push(this.current);
			}
			this.timer = setTimeout(() => {
				this.componentdMouseDown();
			}, 100);
		},
		// 图发散数据计算
		componentdMouseDown() {
			this.timer = null;
			clearTimeout(this.timer);
			let option = [];
			this.currentList.sort().map((current) => {
				if (current) {
					let series = this.series.map((item) => {
						return {
							...item,
							data: this.computedReturn(item.value).map((v, i) => [item.date[i], v])
						};
					});
					series.map((item) => {
						let index = item.date.indexOf(current);
						// 从当前点开始计算累计收益
						let data = item.date.slice(index).map((val, i) => {
							return [val, this.computedReturn(item.value.slice(index))[i]];
						});
						if (item.name == this.info.name) {
							let oIndex = option.findIndex((obj) => {
								return obj.name == item.name;
							});
							if (oIndex == -1) {
								option.unshift({
									...item,
									data: [...item.data.slice(0, index), ...data],
									markLine: {
										silent: true,
										symbol: 'none',
										data: [
											{
												name: '买入',
												symbol: 'none',
												y: '100%',
												xAxis: current
											}
										]
									}
								});
							} else {
								option[oIndex].data = [...option[oIndex].data.slice(0, index), ...data];
								option[oIndex].markLine.data.push({
									name: '买入',
									symbol: 'none',
									y: '100%',
									xAxis: current
								});
							}
						} else {
							let oIndex = option.findIndex((obj) => {
								return obj.name == item.name;
							});
							if (oIndex == -1) {
								option.push({
									...item,
									data: [...item.data.slice(0, index), ...data]
								});
							} else {
								option[oIndex].data = [...option[oIndex].data.slice(0, index), ...data];
							}
						}
					});
				}
			});
			let currentData = [];
			option.map((item) => {
				this.currentList.map((val, i) => {
					let index = item.data.findIndex((time) => {
						return time[0] == val;
					});
					if (index != -1) {
						if (i == 0) {
							currentData.push({
								...item,
								date: item.date.slice(0, index),
								value: item.value.slice(0, index),
								data: item.data.slice(0, index)
							});
						}
						let indexAfter = item.data.findIndex((time) => {
							return time[0] == this.currentList[i + 1];
						});
						if (indexAfter != -1) {
							currentData.push({
								...item,
								date: item.date.slice(index, indexAfter),
								value: item.value.slice(index, indexAfter),
								data: item.data.slice(index, indexAfter)
							});
						} else {
							currentData.push({
								...item,
								date: item.date.slice(index),
								value: item.value.slice(index),
								data: item.data.slice(index)
							});
						}
					}
				});
			});
			let series = [...this.formatSelfIndexSeries(currentData), ...this.formatDown(currentData), ...this.formarExcess(currentData)];

			this.drawAction(series);
			// this.drawAction(currentData);
		},
		// 导出收益图
		downloadImage() {
			this.exportImage('return_chart_main', this.info.name + '收益曲线.jpg');
		},
		// word导出配置
		async createPrintWord() {
			let item = [];
			let height = document.getElementById('fundReturnCurve')?.clientHeight || 660;
			let width = document.getElementById('fundReturnCurve')?.clientWidth || 1008;
			let canvas = await this.html2canvas(document.getElementById('fundReturnCurve'), { scale: 3 });
			let base64Str = canvas.toDataURL('image/jpg');
			item = [...exportTitle('业绩曲线'), ...exportChart(base64Str, { width, height })];
			return item;
		}
	}
};
</script>
<style lang="scss" scoped>
.manager_return {
	.fund_return_curve {
		height: 700px;
		.return_chart_main {
			.return_chart_option {
				height: 432px;
			}
		}
	}
}
.fund_return_curve {
	width: 100%;
	// max-width: 1008px;
	height: 660px;
	border-radius: 4px;
	border: 1px solid #d9d9d9;
	background: #fff;
	/* 主模块 */
	box-shadow: 0px 1px 2px 0px rgba(0, 0, 18, 0.1);
	.return_curve_title {
		border-bottom: 2px solid #f5f5f5;
		color: rgba(0, 0, 0, 0.85);
		font-family: PingFang SC;
		font-size: 18px;
		font-style: normal;
		font-weight: 400;
		.font_weight_500 {
			font-weight: 500;
		}
	}
	.choose_time_checkbox {
		width: 148px;
		height: 32px;
		border-radius: 4px;
		border: 1px solid #d8dce6;
		background: #fff;
		text-align: center;
		line-height: 32px;
	}
	.clear_btn {
		::v-deep.el-button--small {
			color: #fff;
			text-align: center;
			font-size: 14px;
			font-style: normal;
			font-family: PingFang SC;
			font-weight: 400;
		}
	}
	.return_chart_main {
		width: 100%;
		.return_chart_option {
			width: 100%;
			height: 400px;
			margin-top: 12px;
		}

		.return_chart_legend {
			color: rgba(0, 0, 0, 0.85);
			font-family: PingFang SC;
			font-size: 14px;
			font-style: normal;
			font-weight: 400;
			> .flex_start {
				cursor: pointer;
			}
			.lengend_left {
				width: 6px;
				height: 33px;
				background: #d9d9d9;
				border-radius: 4px 0 0 4px;
			}
			.lengend_border {
				border-top: 1px solid #d9d9d9;
				border-right: 1px solid #d9d9d9;
				border-bottom: 1px solid #d9d9d9;
				border-left: 0px;
				border-radius: 0 4px 4px 0;
			}
			::v-deep.el-input--small .el-input__inner {
				border-radius: 0 4px 4px 0;
				border-left: none;
			}
		}
	}

	// ::v-deep.el-radio-button__orig-radio:checked + .el-radio-button__inner {
	// 	background-color: #fff;
	// 	color: #4096ff;
	// }
}
.return_click_warning {
	position: absolute;
	bottom: 0;
	width: 100%;
	// max-width: 1008px;
	height: 47px;
	border-radius: 0 0 4px 4px;
	border-top: 1px solid #d9d9d9;
	background: #ecf5ff;
	color: rgba(0, 0, 0, 0.65);
	font-family: PingFang SC;
	font-size: 14px;
	font-style: normal;
	font-weight: 400;
}
</style>
