<template>
    <div class="vertival-line-header-wrapper">
        <div class="vlhw-title">
            {{title}}
            <!-- <img src="@/assets/img/tkdesign/question.png" alt="" srcset=""> -->
        </div>
        <div class="vlhw-title-right">
            <slot name="right"></slot>
            <div class="detail-download" v-if="showDownLoadBtn" @click="$emit('download')" style="cursor: pointer;">
                <img src="@/assets/img/tkdesign/download.png" alt="" srcset="">
            </div>
        </div>
    </div>
</template>
<script>
export default {
    name:'combinationComponentHeader',
    components:{
    },
    props:{
        title:{
            type: String,
            default:''
        },
        //是否展示更多按钮
        showMoreBtn:{
            type:Boolean,
            default: false,
        },
        showDownLoadBtn:{
            type:Boolean,
            default: true,
        }
    }
}
</script>
<style lang="scss" scoped>
.vertival-line-header-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 0;
    border-bottom: 2px solid rgba(233, 233, 233, 1);
    margin-bottom: 15px;
    .vlhw-title {
        color: rgba(0, 0, 0, 0.85);
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 24px; /* 150% */
        padding-left: 9px;
        // border-left: 3px solid #4096ff;
        position: relative;
    }
    .vlhw-title::before {
        content: '';
        position: absolute;
        width: 5px;
        height: 20px;
        left: 0px;
        // bottom: 0px;
        // right: 20px;
        top: 3px;
        background-color:rgba(255, 145, 3, 1) ; 
        border-radius: 6px;

    }
    .vlhw-title-right {
        display: flex;
        align-items: center;
        .title-right-form {
            display: flex;
        }
        ::v-deep .radio-group-wrapper {
                margin-left: 16px;
        }
    }
    ::v-deep .el-form-item {
        margin-bottom: unset;
        .el-form-item__label {
            color: #000000;
        }
    }
    .more-btn-wrapper {
        margin-left: 16px;
    }
}
.detail-download{
    width: 32px;
    height: 32px;
    border: 1px solid rgba(217, 217, 217, 1);
    border-radius: 2px;
    line-height: 32px;
    text-align: center;
}
</style>