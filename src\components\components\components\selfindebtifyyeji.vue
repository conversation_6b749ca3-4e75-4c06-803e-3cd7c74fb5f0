<!--  -->
<template>
  <div class="headerbox2">
    <div style="display: flex; flex-direction: row-reverse">
      <div class="boxslefhead marginright202">
        <el-button @click="goback"><i style="color: #4096FF"
             class="el-icon-back"></i></el-button>
      </div>
      <div style="display: flex; flex-direction: row; width: 100%; align-items: center">
        <div class="boxslefhead">
          <el-date-picker v-model="value1"
                          type="daterange"
                          unlink-panels
                          range-separator="-"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期">
          </el-date-picker>
        </div>
        <div class="boxslefhead"
             style="display: flex">
          <!-- <i style="color:#4096FF" class="el-icon-search"></i>添加 -->
          <el-select v-model="values"
                     :remote-method="searchpeople"
                     filterable
                     remote
                     prefix-icon="el-icon-search"
                     :loading="loading"
                     placeholder="输入简拼、代码、名称选择基金/基准">
            <el-option-group v-for="groups in havefundmanager"
                             :key="groups.label"
                             :label="groups.label">
              <el-option v-for="group in groups.options"
                         :key="group.code"
                         :label="group.name"
                         :value="group.code"> </el-option>
            </el-option-group>
          </el-select>
        </div>
        <div class="boxslefhead"
             style="display: flex; align-items: center">
          <div style="display: flex; align-items: center">
            <el-checkbox v-model="radio">去打新收益</el-checkbox>
          </div>
        </div>
      </div>
    </div>
    <div style="display: flex; flex-wrap: wrap"
         class="margintop10">
      <div v-for="(item, index) in list"
           :key="index"
           class="tagselfbox">
        <el-tooltip style="cursor: pointer"
                    v-if="item.name.length > 6"
                    class="item"
                    effect="dark"
                    :content="item.name"
                    placement="right-start">
          <el-tag closable
                  :style="`background:${color[index + 1]}!important;border:0px !important`"
                  @close="handleClose(item.code)"
                  :key="item.code"
                  class="boxlabels">{{ item.name.slice(0, 6) }}</el-tag>
        </el-tooltip>
        <el-tag closable
                :style="`background:${color[index + 1]}!important;border:0px !important`"
                @close="handleClose(item.code)"
                :key="item.code"
                class="boxlabels"
                v-else>{{ item.name }}</el-tag>
      </div>
    </div>
    <div v-show="list.length == 0 || value1 == ''"
         class="height20border"></div>
    <div v-show="list.length == 0 || value1 == ''"
         class="height20border"></div>
    <v-chart v-loading="yejishowflag2"
             element-loading-text="暂无数据"
             element-loading-spinner="el-icon-document-delete"
             element-loading-background="rgba(239, 239, 239, 0.5)"
             style="width: 100%"
             class="height300"
             :options="yejioptions2" />
    <div class="height20border"></div>
    <div v-show="list.length == 0 || value1 == ''"
         class="height20border"></div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { fontSize } from '../../../assets/js/echartsrpxtorem'; //注意路径
// //require('echarts/lib/chart/stepline')
import { getFundOrBase, getBasicFundLine } from '@/api/pages/componenets/yejiheader.js';
import VCharts from 'vue-echarts';
export default {
  //import引入的组件需要注入到对象中才能使用
  components: { 'v-chart': VCharts },
  props: {
    code: {
      type: String
    },
    name: {
      type: String
    },
    firstdata: {
      type: Array
    }
  },
  data () {
    //这里存放数据
    return {
      radio: '1',
      value1: '',
      values: null,
      loading: true,
      yejishowflag2: false,
      yejioptions2: {},
      havefundmanager: [],
      listfront: [],
      color: [
        '#4096FF',
        '#FFB6C1',
        '#DB7093',
        '#DA70D6',
        '#800080',
        '#9370DB',
        '#6A5ACD',
        '#4169E1',
        '#B0C4DE',
        '#4682B4',
        '#5F9EA0',
        '#8FBC8F',
        '#EEE8AA',
        '#FFD700',
        '#FFA500',
        '#FF6347',
        '#CD5C5C',
        '#B22222',
        '#D3D3D3',
        '#A9A9A9',
        '#FA8072',
        '#929694',
        '#40BFDD',
        '#C2B12F',
        '#ffa94d',
        '#fcc419',
        '#94d82d',
        '#94C5DE',
        '#B7A7D7',
        '#FDDBC7',
        '#F3A483',
        '#D45C4E',
        '#409eff',
        '#f39c12',
        '#ff1744',
        '#d500f9',
        '#2979ff',
        '#00e5ff',
        '#ff5722',
        '#ffea00',
        '#ff3d00',
        '#ff8a80',
        '#ff80ab',
        '#b388ff',
        '#8c9eff',
        '#a7ffeb',
        '#ffff00',
        '#ffab40',
        '#ffebee',
        '#e8eaf6',
        '#e1f5fe',
        '#fffde7',
        '#efebe9'
      ],
      list: [], //
      showing: true
    };
  },
  //监听属性 类似于data概念
  computed: {
    watchoptionchangge () {
      let t1 = this.value1;
      let t2 = this.list.length;
      let t3 = this.radio;
      return { t1, t2, t3 };
    }
  },
  //监控data中的数据变化
  watch: {
    values (val) {
      // console.log('dasdasdasdsad')
      let codelist = [];
      for (let k = 0; k < this.list.length > 0; k++) {
        codelist.push(this.list[k].code);
      }
      for (let i = 0; i < this.listfront.length; i++) {
        if (this.listfront[i].code == val) {
          if (codelist.indexOf(val) < 0) {
            this.list.push(this.listfront[i]);
          }
        }
      }
      // console.log(this.list)
    },
    watchoptionchangge () {
      if (this.value1 != '') this.getdatayeji();
    }
  },
  //方法集合
  methods: {
    goback () {
      this.$emit('changgeif');
    },
    //查询基金及收益
    async searchpeople (query) {
      this.loading = false;
      let data = await getFundOrBase({ message: query });
      if (data) {
        let temparr = [
          {
            label: '基金产品',
            options: []
          },
          {
            label: '基金基准',
            options: []
          }
        ];
        for (let i = 0; i < data.length; i++) {
          if (data[i].flag == 'fund') {
            temparr[0].options.push(data[i]);
          } else if (data[i].flag == 'index') {
            temparr[1].options.push(data[i]);
          }
        }
        this.listfront = data;
        this.havefundmanager = temparr;
      }
    },
    // 获取业绩表现信息
    async getdatayeji () {
      let arr = [];
      for (let i = 0; i < this.list.length; i++) {
        arr.push({ name: this.list[i].code, fund_name: this.list[i].name, type: this.list[i].fund_co == '' ? false : true });
      }
      let data = await getBasicFundLine({ list: arr, time: this.value1, cutflag: this.radio, code: this.code });
      this.yejishowflag2 = false;
      if (data) {
        let series = [];
        for (let j = 0; j < data.data.data.length; j++) {
          series.push({
            name: data.data.data[j].name,
            data: data.data.data[j].value,
            type: 'line',
            smooth: true
          });
        }

        this.yejioptions2 = {
          color: this.color,
          tooltip: {
            trigger: 'axis',
            formatter: function (obj) {
              var value = obj[0].axisValue + `<br />`;
              for (let i = 0; i < obj.length; i++) {
                value +=
                  `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:` +
                  obj[i].color +
                  `;"></span>` +
                  obj[i].seriesName +
                  ':' +
                  (Number(obj[i].data) * 100).toFixed(2) +
                  '%' +
                  `<br />`;
              }
              return value;
            }
          },
          grid: {
            left: '5%',
            right: '5%',
            bottom: '10%',
            top: '3%',
            height: fontSize(300),
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: data.data.date[0].value
          },
          yAxis: {
            axisLabel: {
              formatter: function (obj) {
                return (obj * 100).toFixed(0) + '%';
              }
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed'
              }
            },
            type: 'value'
          },
          series: series
        };
      }
    },
    handleClose (val) {
      this.list.splice(this.list.indexOf(val), 1);
    },
    drawpicfirst () {
      this.yejishowflag2 = false;
      let that = this;
      let series = [];
      let colorpan = [];

      series.push({
        name: that.name,
        type: 'line',
        symbol: 'none',
        data: that.firstdata.fund_return_top.fund_ret
      });
      colorpan.push('#4096FF');

      that.yejioptions2 = {
        color: colorpan,
        tooltip: {
          trigger: 'axis',
          formatter: function (obj) {
            var value = obj[0].axisValue + `<br />`;
            for (let i = 0; i < obj.length; i++) {
              value +=
                `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:` +
                obj[i].color +
                `;"></span>` +
                obj[i].seriesName +
                ':' +
                (Number(obj[i].data) * 100).toFixed(2) +
                '%' +
                `<br />`;
            }
            return value;
          }
        },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '3%',
          top: '3%',
          height: fontSize(300),
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: that.firstdata.fund_return_top.date
        },
        yAxis: {
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed'
            }
          },
          min: (value) => {
            return value.min;
          },
          type: 'value',
          axisLabel: {
            formatter: function (obj) {
              // var value = obj.value;
              return (obj * 100).toFixed(0) + '%';
            }
          }
        },
        series: series
      };
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () {
    this.drawpicfirst();
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () { },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style>
.el-date-editor .el-range-separator {
	padding: 0 20px !important;
}
.el-range-separator {
	justify-content: center !important;
	align-items: center !important;
	display: flex !important;
}
.el-input__icon .el-range__icon .el-icon-date {
	display: flex !important;
	align-items: center !important;
}
</style>
<style>
.boxslefhead ::v-deep .el-select--small {
	width: 160px !important;
}
.tagselfbox ::v-deep .el-range-editor--small.el-input__inner {
	height: 32px !important;
}
.tagselfbox ::v-deep .el-date-editor--daterange.el-input,
.el-date-editor--daterange.el-input__inner,
.el-date-editor--timerange.el-input,
.el-date-editor--timerange.el-input__inner {
	width: 200px !important;
}
.tagselfbox ::v-deep .el-tag .el-tag__close {
	color: white !important;
}
.tagselfbox ::v-deep .el-range-editor--small .el-range__close-icon,
.el-range-editor--small .el-range__icon {
	line-height: 24px !important;
}
.tagselfbox ::v-deep .el-date-editor .el-range__icon {
	font-size: 14px !important;
	margin-left: -5px !important;
}
.tagselfbox ::v-deep .el-range-editor--small .el-range-input {
	font-size: 13px !important;
}
.tagselfbox ::v-deep .el-range-editor--small .el-range-separator {
	line-height: 24px !important;
	font-size: 13px !important;
}
</style>
<style scoped>
.marginright202 {
	margin-right: 20px !important;
}
/* .tagselfbox .el-input__icon {
	
    display: flex !important;
    align-items: center !important;

} */
.boxslefhead ::v-deep .el-date-editor .el-range-editor .el-input__inner .el-date-editor--daterange .el-range-editor--small {
	width: 200px !important;
}
.tagselfbox .el-tag {
	background-color: #4096FF !important;
	border-color: #4096FF !important;
	color: white !important;
	font-size: 14px !important;
}

.selfindetifybox {
}
.boxslefhead {
	margin: 10px;
}
.margintop10 {
	/* margin-top:10px */
}
.tagselfbox {
	margin: 10px;
}
.boxlabels {
}
.height300 {
	height: 310px !important;
}
</style>
