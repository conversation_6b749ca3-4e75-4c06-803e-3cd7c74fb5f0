<template>
	<div>
		<div class="flex_start">
			<div class="export-condition">
				<choose-condition ref="chooseCondition" @resolveFather="getConditionList"></choose-condition>
			</div>
			<div class="export-config px-20 py-20">
				<functional-area
					ref="functionalArea"
					:list="list"
					:condition="condition"
					@exportExcel="exportExcel"
					@updateActiveConditionList="updateActiveConditionList"
				></functional-area>
				<key-compute ref="keyCompute" @updateDate="updateDate"></key-compute>
			</div>
		</div>
	</div>
</template>

<script>
// 左侧条件列表
import chooseCondition from './components/chooseCondition.vue';
// 右侧顶部功能区
import functionalArea from './components/functionalArea.vue';
// 右侧字段计算器
import keyCompute from './components/keyCompute.vue';
import { getFieldList, exportToExcel } from '@/api/pages/ApiTaikang.js';
export default {
	components: { chooseCondition, keyCompute, functionalArea },
	data() {
		return {
			list: [],
			all_list: [],
			condition_list: [],
			activeType: '',
			condition: [],
			date: { startDate: '', endDate: '' },
			loading: false
		};
	},
	methods: {
		getData(activeType) {
			this.activeType = activeType;
			this.$refs['functionalArea']?.getData(this.activeType);
			this.getFieldList();
		},
		// 获取字段列表
		async getFieldList() {
			let data = await getFieldList({ fieldName: '', table: this.activeType });
			if (data.code == 200) {
				this.list = data.data;
				this.$refs['chooseCondition']?.getData(this.list);
			}
		},
		// 获取选择时间区间
		updateDate(date) {
			this.date = date;
		},
		// 获取已选条件列表
		updateActiveConditionList(condition) {
			// 更新各个组件内的条件列表
			if (condition) {
				this.$refs['keyCompute']?.setConditionList(condition);
				this.condition = condition;
			} else {
				// 获取各个组件内的条件列表
				this.condition = this.$refs['keyCompute']?.getExportCondition();
			}
		},
		// 导出Excel
		async exportExcel(data) {
			// 筛选条件
			this.updateActiveConditionList();
			let filterFieldList = [];
			this.condition.map((obj) => {
				obj.children.map((item) => {
					let time = {};
					if (obj.fieldType == 'date') {
						time = {
							startDate: item.value?.[0],
							endDate: item.value?.[1]
						};
					}
					filterFieldList.push({
						field: obj.field,
						fieldName: obj.fieldName,
						fieldType: obj.fieldType,
						fieldValue: obj.fieldType == 'date' ? [] : [item.value],
						calculator: item?.mathRange,
						operator: item?.operator,
						table: obj.table,
						tableName: obj.tableName,
						unit: item.unit,
						...time
					});
				});
			});
			// 导出字段
			let exportFieldList = [];
			if (data?.length) {
				exportFieldList = data.map((item) => {
					return {
						field: item.field,
						fieldName: item.fieldName,
						table: item.table,
						tableName: item.tableName
					};
				});
			} else {
				exportFieldList = this.condition.map((item) => {
					return {
						field: item.field,
						fieldName: item.fieldName,
						table: item.table,
						tableName: item.tableName
					};
				});
			}
			this.loading = this.$loading({
				lock: true,
				text: '正在生成数据,请稍等...',
				spinner: 'el-icon-loading',
				background: 'rgba(0, 0, 0, 0.7)'
			});
			let file = await exportToExcel({
				filterFieldList,
				exportFieldList,
				templateType: this.activeType,
				startDate: this.date?.startDate,
				endDate: this.date?.endDate
			});
			this.loading.close();
			if (file.code == 200) {
				let aLink = document.createElement('a');
				aLink.style.display = 'none';
				aLink.href = file.data;
				// 触发点击-然后移除
				document.body.appendChild(aLink);
				aLink.click();
				document.body.removeChild(aLink);
			} else {
				this.$message.warning(file.message);
			}
		},
		getConditionList(item) {
			let index = this.condition_list.findIndex((v) => v.field == item.field);
			if (index == -1) {
				this.condition_list.push({ ...item });
			} else {
				this.condition_list.splice(index, 1);
			}
			this.$refs['keyCompute']?.getCondition(item);
		}
	}
};
</script>

<style lang="scss" scoped>
.export-condition {
	width: 240px;
	height: 919px;
	border-right: 1px solid #d4d8e5;
}
.export-config {
	width: 100%;
	height: 919px;
	div {
		font-size: 14px;
		color: rgba(0, 0, 0, 0.85);
	}
}
</style>
