<template>
  <div>
    <div style="height: 16px"></div>
    <div class="material_center">
      <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
        <el-tab-pane label="快速打印" name="quickPrint">
          <quick-print ref="quickPrint"></quick-print>
        </el-tab-pane>
        <el-tab-pane label="周期订阅" name="periodicSubscription">
          <periodic-subscription ref="periodicSubscription">
          </periodic-subscription>
        </el-tab-pane>
        <!-- <el-tab-pane label="报告模板" name="reportTemplate">
					<report-template ref="reportTemplate"></report-template>
				</el-tab-pane> -->
        <el-tab-pane label="下载历史" name="downloadHistory">
          <download-history ref="downloadHistory"></download-history>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
// 快速打印
import quickPrint from "./quickPrint.vue";
// 周期订阅
import periodicSubscription from "./periodicSubscription.vue";
// 报告模板
import reportTemplate from "./reportTemplate.vue";
// 下载历史
import downloadHistory from "./downloadHistory";
export default {
  components: {
    quickPrint,
    periodicSubscription,
    reportTemplate,
    downloadHistory,
  },
  data() {
    return {
      activeName: "quickPrint",
    };
  },
  mounted() {
    let name = localStorage.getItem("subscriptionActiveMenu");
    if (name) {
      this.activeName = name;
    }
    this.$refs[this.activeName].getData();
  },
  methods: {
    handleClick() {
      localStorage.setItem("subscriptionActiveMenu", this.activeName);
      this.$refs[this.activeName].getData();
    },
  },
};
</script>

<style lang="scss" scoped>
.material_center {
  margin: 0 24px;
  background-color: #fff;
  // height: calc(100vh - 245px);
}
</style>
