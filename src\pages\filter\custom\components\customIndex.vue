<template>
	<div class="custom_index">
		<div>
			<div class="flex_between mb-16">
				<div>在这里你可以创建自定义的基准指数。</div>
				<div><el-button type="primary" @click="openDialog()">创建</el-button></div>
			</div>
			<el-table :data="data" v-loading="loading">
				<el-table-column
					v-for="item in column"
					:key="item.value"
					:prop="item.value"
					:label="item.label"
					:sortable="item.sortable"
					align="gotoleft"
				>
					<template slot-scope="{ row }">
						<div v-if="item.value.includes('is')">
							<el-switch
								v-model="row[item.value]"
								active-color="#4096ff"
								inactive-color="#e7e7e7"
								@change="changeData(row, item)"
							></el-switch>
						</div>
						<div v-else>{{ row[item.value] }}</div>
					</template>
				</el-table-column>
				<el-table-column align="gotoleft" label="操作">
					<template slot-scope="{ row }">
						<div class="flex_start">
							<el-link class="mr-8" @click="openDialog(row)">编辑</el-link>
							<el-link class="mr-8" @click="deleteItem(row)">删除</el-link>
						</div>
					</template>
				</el-table-column>
				<template slot="empty">
					<el-empty></el-empty>
				</template>
			</el-table>
		</div>
		<edit-index ref="editIndex" @resolverFather="getData"></edit-index>
	</div>
</template>

<script>
import { getIndexList, deleteIndexList, getIndexListBase } from '@/api/pages/Tools.js';
import editIndex from '@/pages/filter/custom/components/components/editIndex';
export default {
	components: { editIndex },
	data() {
		return {
			loading: true,
			dialogVisible: false,
			active_data: [],
			defaultProps: {
				children: 'children',
				label: 'label'
			},
			column: [
				{
					label: '基准名称',
					value: 'name'
				},
				{
					label: '基准成分',
					value: 'index_name'
				},
				{
					label: '基期',
					value: 'baseDate',
					sortable: true
				},
				{
					label: '修改日期',
					value: 'create_date',
					sortable: true
				},
				{
					label: '是否默认',
					value: 'isdefault'
				},
				{
					label: '备注',
					value: 'description'
				}
			],
			data: [],
			formInline: { user: '', region: '', resource: '手动添加', desc: '' }
		};
	},
	methods: {
		async getData() {
			this.loading = true;
			let data = await getIndexList();
			if (data?.mtycode == 200) {
				this.data = data?.data.map((v) => {
					return {
						...v,
						isdefault: v.isdefault == 1 ? true : false
					};
				});
			} else {
				this.data = [];
			}
			this.loading = false;
		},
		changeData(row, item) {
			if (item.label == '是否默认') {
				let obj = this.data.find((v) => v.id == row.id);
				if (row[item.value]) {
					let index = this.model.find((v) => v.isdefault == true);
					if (index?.id) {
						index.isdefault = 0;
						this.updateModel(index);
					}
					obj.isdefault = 1;
				} else {
					obj.isdefault = 0;
				}
				this.updateModel(obj);
			}
		},
		updateModel(postData) {},
		async openDialog(val) {
			if (val) {
				let data = await getIndexListBase({ id: val.id });
				if (data?.mtycode == 200) {
					let posstData = {
						id: val.id,
						name: val.name,
						description: val.description,
						list: data?.data
					};
					this.$refs['editIndex'].getData(posstData);
				} else {
					this.$message.warning(data?.mtymessage);
				}
			} else {
				this.$refs['editIndex'].getData();
			}
		},
		async deleteItem(val) {
			let data = await deleteIndexList({ id: val.id });
			if (data?.mtycode == 200) {
				this.$message.success('删除成功');
				this.getData();
			} else {
				this.$message.success(data?.mtymessage);
			}
		},
		chooseIndex() {
			let data = [];
			this.$refs.tree.getCheckedNodes().map((item) => {
				if (!item.children) {
					data.push({
						id: item.id,
						label: item.label,
						name: item.label,
						weight: 0
					});
				}
			});
			this.active_data = data;
		}
	}
};
</script>

<style lang="scss">
.custom_index {
	.el-form-item__label {
		width: auto !important;
	}
	.el-form-item__content {
		margin-left: auto !important;
	}
	// .el-dialog__header {
	// 	padding: 0;
	// }
	// .el-dialog__body {
	// 	padding: 0;
	// }
}
</style>
