import request from '@/utils/request';

const server = '';

/**
 * @param {获取订阅任务列表} params
 * @returns
 */
export function getSubscriptionList(params) {
	return request({
		url: server + '/subscription/list',
		method: 'get',
		params
	});
}
/**
 * @param {新增订阅任务} params
 * @returns
 */
export function postSubscriptionList(data) {
	return request({
		url: server + '/subscription/list',
		method: 'post',
		data
	});
}
/**
 * @param {修改订阅任务} params
 * @returns
 */
export function putSubscriptionList(data) {
	return request({
		url: server + '/subscription/list',
		method: 'put',
		data
	});
}
/**
 * @param {删除订阅任务} params
 * @returns
 */
export function deleteSubscriptionList(params) {
	return request({
		url: server + '/subscription/list',
		method: 'delete',
		params
	});
}
/**
 * @param {获取邮箱列表} params
 * @returns
 */
export function getEmailList(params) {
	return request({
		url: server + '/subscription/email',
		method: 'get',
		params
	});
}
/**
 * @param {新增邮箱} params
 * @returns
 */
export function postEmailItem(data) {
	return request({
		url: server + '/subscription/email',
		method: 'post',
		data
	});
}
/**
 * @param {删除邮箱} params
 * @returns
 */
export function deleteEmailItem(params) {
	return request({
		url: server + '/subscription/email',
		method: 'delete',
		params
	});
}
/**
 * @param {新增邮箱分组} params
 * @returns
 */
export function postEmailGroup(data) {
	return request({
		url: server + '/subscription/group',
		method: 'post',
		data
	});
}
/**
 * @param {修改邮箱分组} params
 * @returns
 */
export function putEmailGroup(data) {
	return request({
		url: server + '/subscription/group',
		method: 'put',
		data
	});
}
/**
 * @param {删除邮箱分组} params
 * @returns
 */
export function deleteEmailGroup(params) {
	return request({
		url: server + '/subscription/group',
		method: 'delete',
		params
	});
}
/**
 * @param {获取报告地址} params
 * @returns
 */
export function getReportUrl(params) {
	return request({
		url: server + '/subscription/report',
		method: 'get',
		params
	});
}
/**
 * @param {获取所有报告列表} params
 * @returns
 */
export function getReportList(params) {
	return request({
		url: server + '/subscription/report/all',
		method: 'get',
		params
	});
}
/**
 * @param {修改定时任务状态} params
 * @returns
 */
export function putTaskStatus(data) {
	return request({
		url: server + '/subscription/task',
		method: 'put',
		data
	});
}
/**
 * @param {获取订阅任务详情} params
 * @returns
 */
export function getTaskDetail(params) {
	return request({
		url: server + '/subscription/detail',
		method: 'get',
		params
	});
}
/**
 * @param {手动发送报告到邮箱} params
 * @returns
 */
export function getSendReport(params) {
	return request({
		url: server + '/subscription/send',
		method: 'get',
		params
	});
}
/**
 * @param {获取报告模板列表} params
 * @returns
 */
export function getReportTemplateList(params) {
	return request({
		url: server + '/subscription/report/list',
		method: 'get',
		params
	});
}
/**
 * @param {新增报告模板} data
 * @returns
 */
export function postReportTemplate(data) {
	return request({
		url: server + '/subscription/report/list',
		method: 'post',
		data
	});
}
/**
 * @param {修改报告模板} data
 * @returns
 */
export function putReportTemplate(data) {
	return request({
		url: server + '/subscription/report/list',
		method: 'put',
		data
	});
}
/**
 * @param {删除报告模板} data
 * @returns
 */
export function deleteReportTemplate(data) {
	return request({
		url: server + '/subscription/report/list',
		method: 'delete',
		data
	});
}
/**
 * @param {获取报告模板详情} params
 * @returns
 */
export function getReportTemplateDetail(params) {
	return request({
		url: server + '/subscription/report/detail',
		method: 'get',
		params
	});
}

/**
 * @param {新增任务列表} data
 * @returns
 */
export function addTaskList(data) {
	return request({
		url: server + '/subscription/taskInfo',
		method: 'post',
		data
	});
}
