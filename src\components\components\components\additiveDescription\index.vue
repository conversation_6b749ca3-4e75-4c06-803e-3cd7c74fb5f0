<template>
	<div id="additiveDescription">
		<analysis-card-title title="固收+的加法描述" image_id="additiveDescription"> </analysis-card-title>
		<el-empty v-if="empty" style="height: 205px"></el-empty>
		<div v-else style="height: 205px" v-loading="loading">
			{{ adddesc }}
		</div>
	</div>
</template>

<script>
import { exportTitle, exportDescripe } from '@/utils/exportWord.js';

// 固收+加法描述TODo
import { getBondDes } from '@/api/pages/Analysis.js';
// 固收+的加法描述
export default {
	name: 'additiveDescription',
	data() {
		return {
			adddesc: '',
			loading: true,
			empty: false,
			info: {}
		};
	},
	methods: {
		// 获取固收+加法描述数据
		async getGSWEB() {
			let data = await getBondDes({
				code: this.info.code,
				flag: this.info.flag,
				type: this.info.type,
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			if (data?.mtycode == 200) {
				return data?.data;
			} else {
				return '';
			}
		},
		// 获取父组件传递数据
		async getData(info) {
			this.info = info;
			let data = await this.getGSWEB();
			this.loading = false;
			this.adddesc = data;
			if (!this.adddesc) {
				this.empty = true;
			} else {
				this.empty = false;
			}
		},
		createPrintWord() {
			if (this.adddesc) {
				return [...exportTitle('固收+的加法描述'), ...exportDescripe(this.adddesc, '000000')];
			} else {
				return [];
			}
		}
	}
};
</script>

<style></style>
