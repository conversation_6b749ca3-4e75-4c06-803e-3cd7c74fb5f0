<!--  -->
<template>
	<div class="boxCzJzOnly">
		<span style="font-weight: 600; margin-left: 10px; margin-right: 10px">{{ haveName }}</span>

		<div style="margin-top: 20px; display: flex; align-items: center">
			<operator @resolveMathRange="resolveMathRange"></operator>
			<el-dropdown @command="command2">
				<el-button type="primary">
					<span>{{ yearqtr == '' ? '选择分位或者值' : quarterList[quarterList.findIndex((item) => item.value == yearqtr)].label }}</span>
					<i class="el-icon-arrow-down el-icon--right"></i>
				</el-button>
				<el-dropdown-menu slot="dropdown">
					<el-dropdown-item v-for="(item, index) in quarterList" :command="item.value" :key="index">{{ item.label }}</el-dropdown-item>
				</el-dropdown-menu>
			</el-dropdown>
			<span style="font-weight: 600; margin-left: 0px; margin-right: 10px"></span>
			<el-dropdown @command="command">
				<el-button type="primary">
					{{ iconFlag != '' ? (iconFlag == 'all' ? '所有' : iconFlag) : '运算符' }}<i class="el-icon-arrow-down el-icon--right"></i>
				</el-button>
				<el-dropdown-menu slot="dropdown">
					<el-dropdown-item command="all">所有</el-dropdown-item>
					<el-dropdown-item command="<">&lt;</el-dropdown-item>
					<el-dropdown-item command="=">=</el-dropdown-item>
					<el-dropdown-item command=">">&gt;</el-dropdown-item>
					<el-dropdown-item command="<=">&lt;=</el-dropdown-item>
					<el-dropdown-item command=">=">&gt;=</el-dropdown-item>
				</el-dropdown-menu>
			</el-dropdown>
			<div v-show="showBox" style="margin-left: 10px; display: flex; align-items: center">
				<!-- <div style="padding:5px;background:#ecf5ff;border:1px #f8f8f8;">
                {{iconFlag=='all'?'所有':iconFlag}}
            </div> -->
				<div style="margin-left: 10px">
					<el-input
						type="number"
						@input="inputChange"
						:placeholder="
							yearqtr == '' ? '选择分位或者值' : yearqtr == '分位' ? '前百分之,如10,表示TOP10%' : yearqtr == '值' ? '指标物理含义值' : ''
						"
						v-model="input"
					>
					</el-input>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import operator from '@/pages/filter/fund/beta/componentsFilter/components/operator.vue';

export default {
	props: {
		haveName: {
			type: String,
			default: ''
		},
		dataX: {
			type: Object,
			default: {}
		},
		placeholder: {
			type: String
		},
		indexFlag: {
			type: Number
		},
		baseIndexFlag: {
			type: Number
		}
	},
	//import引入的组件需要注入到对象中才能使用
	components: { operator },
	data() {
		//这里存放数据
		return {
			field103: '',
			iconFlag: '',
			showBox: false,
			input: '',
			yearqtr: '',
			quarterList: [
				{
					value: '分位',
					label: '分位'
				},
				{
					value: '值',
					label: '值'
				}
			],
			mathRange: { mathRange: 'avg' }
		};
	},
	//监听属性 类似于data概念
	computed: {},
	//监控data中的数据变化
	watch: {
		dataX(val) {
			if (val.dataResult && val.dataResult.length > 0) {
				this.showBox = true;
				this.iconFlag = val.dataResult[0].flag;
				this.input = val.dataResult[0].value;
				this.yearqtr = val.dataResult[0].yearqtr;
				this.field103 = val.dataResult[0].label;
			}
		}
	},
	//方法集合
	methods: {
		resolveMathRange(obj) {
			this.mathRange = obj;
			this.resolveFather();
		},
		resolveFather() {
			this.$emit(
				'boxCzJzOnlyChange',
				this.baseIndexFlag,
				this.indexFlag,
				this.input,
				this.iconFlag,
				this.field103,
				this.yearqtr,
				this.mathRange
			);
		},
		changeNode(e) {
			this.yearqtr = '';
			this.iconFlag = '';
			this.showBox = false;
			this.input = '';
			this.resolveFather();
		},
		command(e) {
			this.iconFlag = e;
			this.showBox = true;
			this.resolveFather();
		},
		command2(e) {
			this.yearqtr = e;
			this.resolveFather();
		},
		inputChange() {
			this.resolveFather();
		}
	},
	//生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	//生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		if (JSON.stringify(this.dataX) != '{}') {
			if (this.dataX.dataResult && this.dataX.dataResult.length > 0) {
				this.showBox = true;
				this.iconFlag = this.dataX.dataResult[0].flag;
				this.input = this.dataX.dataResult[0].value;
				this.yearqtr = this.dataX.dataResult[0].yearqtr;
				this.field103 = this.dataX.dataResult[0].label;
			}
		}
	},
	beforeCreate() {}, //生命周期 - 创建之前
	beforeMount() {}, //生命周期 - 挂载之前
	beforeUpdate() {}, //生命周期 - 更新之前
	updated() {}, //生命周期 - 更新之后
	beforeDestroy() {}, //生命周期 - 销毁之前
	destroyed() {}, //生命周期 - 销毁完成
	activated() {} //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
.boxOnlyYSF {
	display: flex;
	align-items: center;
}
</style>
