<template>
	<div class="chart_one" v-loading="loading">
		<div style="display: flex; align-items: center; justify-content: space-between; position: relative">
			<div class="title" style="margin-bottom: 24px">
				TM模型分析
				<el-tooltip class="item" effect="dark" :content="EXPLAIN.abilityPage['TM模型分析']" placement="right-start">
					<svg width="14" height="14" viewBox="0 0 14 14" fill="none">
						<path
							fill-rule="evenodd"
							clip-rule="evenodd"
							d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
							fill="black"
							fill-opacity="0.45"
						/>
					</svg> </el-tooltip
				>{{ befg + aftg }}
			</div>
			<el-button class="print_show" icon="el-icon-document-delete" @click="exportExcel">导出Excel</el-button>
		</div>

		<el-table :span-method="objectSpanMethod" :data="datas" class="table" ref="multipleTable" header-cell-class-name="table-header">
			<el-table-column prop="hkf" align="gotoleft"> </el-table-column>
			<el-table-column prop="name" align="gotoleft"> </el-table-column>
			<el-table-column prop="value" label="均值  " align="gotoleft">
				<template slot-scope="scope"
					><span>{{ scope.row.value | fix4 }}</span></template
				>
			</el-table-column>
			<el-table-column prop="description" align="gotoleft" label="说明"> </el-table-column>
			<template slot="empty">
				<el-empty image-size="160"></el-empty>
			</template>
		</el-table>
	</div>
</template>

<script>
import { exportTitle, exportTable } from '@/utils/exportWord.js';
import { filter_json_to_excel } from '@/utils/exportExcel.js';
// TM模型分析
export default {
	name: 'TMModelAnalysis',
	data() {
		return {
			befg: '',
			aftg: '',
			datas: [],
			base1: 0,
			base2: 0,
			loading: true,
			info: {}
		};
	},
	filters: {
		fix4(value) {
			if (value == null || value == 'null') {
				return '--';
			} else return parseInt(value * 10000) / 10000;
		}
	},
	methods: {
		getData(data, info) {
			this.loading = false;
			this.info = info;
			this.datasourse = data;
			this.befg = '';
			this.aftg = '';
			this.datas = [];
			this.base1 = 0;
			this.base2 = 0;
			let temp1 = '配置港股';
			let temp2 = '未配置港股';
			if (this.info.type == 'equityhk' || this.info.type == 'hkequity') {
				temp1 = '配置港股';
				temp2 = '配置港股';
			} else if (this.info.type == 'equitywithhk') {
				temp1 = '未配置港股';
				temp2 = '配置港股';
			} else {
				temp1 = '未配置港股';
				temp2 = '未配置港股';
			}
			let t = 0;
			if (data?.mtycode == 200) {
				data = data.data;
			} else {
				return;
			}
			for (const key in data) {
				if (key.indexOf('_explain') == -1) {
					if (key.indexOf('hk') == -1) {
						this.datas.push({
							hkf: temp1,
							name: key == 'c' ? '择时系数' : key,
							value: data[key],
							description: data[key + '_explain']
						});
						this.base1 += 1;
					} else {
						this.datas.push({
							hkf: temp2,
							name: '港股' + (key.split('hk_')?.[1] == 'c' ? '择时系数' : key.split('hk_')?.[1]),
							value: data[key],
							description: data[key + '_explain']
						});
						this.base2 += 1;
					}
				}
			}
		},
		objectSpanMethod(row, column, rowIndex, columnIndex) {
			if (row.columnIndex === 0) {
				if (this.info.type == 'equityhk' || this.info.type == 'hkequity') {
					if (row.row.hkf == '配置港股' && row.rowIndex == 0) {
						return {
							rowspan: this.base1,
							colspan: 1
						};
					} else {
						return {
							rowspan: 0,
							colspan: 0
						};
					}
				} else if (this.info.type == 'equitywithhk') {
					if (row.row.hkf == '配置港股' && row.rowIndex == this.base2) {
						return {
							rowspan: this.base2,
							colspan: 1
						};
					} else if (row.row.hkf == '未配置港股' && row.rowIndex == 0) {
						return {
							rowspan: this.base1,
							colspan: 1
						};
					} else {
						return {
							rowspan: 0,
							colspan: 0
						};
					}
				} else {
					if (row.row.hkf == '未配置港股' && row.rowIndex == 0) {
						return {
							rowspan: this.base1,
							colspan: 1
						};
					} else {
						return {
							rowspan: 0,
							colspan: 0
						};
					}
				}
			}
		},
		exportExcel() {
			let list = [
				{
					label: '',
					fill: 'header',
					value: 'hkf'
				},
				{
					label: '',
					fill: 'header',
					value: 'name'
				},
				{
					label: '均值',
					value: 'value',
					format: 'fix3'
				},
				{
					label: '说明',
					value: 'description'
				}
			];
			filter_json_to_excel(list, this.datas, 'TM模型分析');
		},
		createPrintWord() {
			let list = [
				{
					label: '',
					fill: 'header',
					value: 'hkf'
				},
				{
					label: '',
					fill: 'header',
					value: 'name'
				},
				{
					label: '均值',
					value: 'value',
					format: 'fix3'
				},
				{
					label: '说明',
					value: 'description'
				}
			];
			if (this.datas.length) {
				return [...exportTitle('TM模型分析'), ...exportTable(list, this.datas)];
			} else {
				return [];
			}
		}
	}
};
</script>

<style></style>
