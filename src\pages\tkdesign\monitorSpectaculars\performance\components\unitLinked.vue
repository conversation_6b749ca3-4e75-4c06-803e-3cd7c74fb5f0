<template>
  <div v-loading="loadingLink"
       class="border_table">
    <!-- 头部区域 -->
    <div class="border_table_header">
      <!-- 右侧标题区域 -->
      <div class="border_table_header_title">
        <div class="block" />
        <div>投连业绩看板</div>
        <img alt=""
             class="icon-question"
             src="../../../../../assets/img/question.png">
      </div>
      <!-- 下载 -->
      <div class="border_table_header_upload"
           @click="downloadExcel()">
        <i class="el-icon-download"></i>
      </div>
    </div>
    <!-- 表格区域 -->
    <el-table class='kanbantable'
              :data="tableData"
              border
              stripe>
      <el-table-column fixed
                       align="gotoleft"
                       label="产品类型"
                       sortable
                       :show-overflow-tooltip='true'
                       min-width="120"
                       prop="productType" />
      <el-table-column fixed
                       align="gotoleft"
                       label="产品名称"
                       min-width="120"
                       :show-overflow-tooltip='true'
                       prop="productName"
                       sortable />
      <el-table-column align=""
                       label="期末账面价值(亿)"
                       min-width="160"
                       prop="endingBookValue"
                       sortable><template slot-scope="{row}">
          {{handDataForm(row.endingBookValue,'Y')}}</template> </el-table-column>
      <el-table-column align=""
                       label="期末单位净值"
                       min-width="140"
                       prop="endingNetAssetValue"
                       sortable><template slot-scope="{row}">
          {{handDataForm(row.endingNetAssetValue,'4')}}</template> </el-table-column>
      <el-table-column align="center"
                       label="分析区间">
        <el-table-column align=""
                         label="收益率"
                         min-width="120"
                         prop="intervalRate"
                         sortable><template slot-scope="{row}">
            {{handDataForm(row.intervalRate,'2p')}}</template> </el-table-column>
        <el-table-column align=""
                         label="基准收益率"
                         min-width="120"
                         prop="intervalBenchmarkRate"
                         sortable><template slot-scope="{row}">
            {{handDataForm(row.intervalBenchmarkRate,'2p')}}</template> </el-table-column>
        <el-table-column align=""
                         label="超额收益率"
                         min-width="120"
                         prop="intervalExcessRate"
                         sortable><template slot-scope="{row}">
            {{handDataForm(row.intervalExcessRate,'2p')}}</template> </el-table-column>
      </el-table-column>
      <el-table-column align=""
                       label="当日收益率"
                       min-width="120"
                       prop="dailyRate"
                       sortable><template slot-scope="{row}">
          {{handDataForm(row.dailyRate,'2p')}}</template> </el-table-column>
      <el-table-column align=""
                       label="近1周"
                       min-width="120"
                       prop="oneWeekRate"
                       sortable><template slot-scope="{row}">
          {{handDataForm(row.oneWeekRate,'2p')}}</template> </el-table-column>
      <el-table-column align=""
                       label="近1月"
                       min-width="120"
                       prop="oneMonthRate"
                       sortable><template slot-scope="{row}">
          {{handDataForm(row.oneMonthRate,'2p')}}</template> </el-table-column>
      <el-table-column align=""
                       label="近1年"
                       min-width="120"
                       prop="oneYearRate"
                       sortable><template slot-scope="{row}">
          {{handDataForm(row.oneYearRate,'2p')}}</template> </el-table-column>
      <el-table-column align=""
                       label="近2年"
                       min-width="120"
                       prop="twoYearRate"
                       sortable><template slot-scope="{row}">
          {{handDataForm(row.twoYearRate,'2p')}}</template> </el-table-column>
      <el-table-column align=""
                       label="近3年"
                       min-width="120"
                       prop="threeYearRate"
                       sortable><template slot-scope="{row}">
          {{handDataForm(row.threeYearRate,'2p')}}</template> </el-table-column>
      <el-table-column align=""
                       label="成立以来年化收益率"
                       min-width="120"
                       prop="sinceRate"
                       sortable><template slot-scope="{row}">
          {{handDataForm(row.sinceRate,'2p')}}</template> </el-table-column>
      <el-table-column align=""
                       label="成立时间"
                       min-width="120"
                       prop="inceptionDate"
                       sortable />
      <template slot="empty">
        <el-empty :image-size="160"></el-empty>
      </template>
    </el-table>
  </div>
</template>

<script>
import { getPerformanceBoardLinked } from '@/api/pages/tkdesign/performance'
import { filter_json_to_excel_inside, changColumnToRow, filter_json_to_excel_inside_multiHeader } from '@/utils/exportExcel.js';

export default {
  props: {
    time: {
      type: Array,
    }
  },

  data () {
    return {
      tableData: [], //表格数据源
      loadingLink: false,
    };
  },
  watch: {
    time () {
      this.getPerformanceBoardLinked()
    }
  },
  mounted () {
    // this.getPerformanceBoardLinked()
  },
  methods: {
    handDataForm (data, flag) {
      let result = ''
      if (data === '' || data === 'NaN' || data === undefined) {
        return '--'
      }
      if (flag == '2p') {
        result = (data * 100).toFixed(2) + '%'
      }
      else if (flag == '2') {
        result = (data * 1).toFixed(2)
      }
      else if (flag == '4') {
        result = (data * 1).toFixed(4)
      }
      else if (flag == 'Y') {
        result = (data / 100000000).toFixed(2)
      }
      return result
    },
    /**
     * 给某一单元格添加样式
     */
    cellStyle ({ row, column, rowIndex, columnIndex }) {
      if (rowIndex === 0 && columnIndex === 1)
        return {
          backgroundImage: 'linear-gradient(90deg, rgba(242, 208, 211,1) 0%, rgba(0, 0, 0, 0) 100%)',
        }
      if (rowIndex === 0 && columnIndex === 2)
        return {
          backgroundImage: 'linear-gradient(90deg, rgba(218, 236, 207,1) 0%, rgba(218, 236, 207,0) 100%)',
        }
    },
    downloadExcel () {
      const title1 = ['产品类型', '产品名称', '期末账面价值', '期末单位净值',
        '分析区间', '', '',
        '当日收益率', '近1周', '近1月', '近1年', '近2年', '近3年', '成立以来年化收益率', '成立时间']
      const title2 = ['', '', '', '',
        '收益率', '基准收益率', '超额收益率',
        '', '', '', '', '', '', '', '']
      const merge = ['A1:A2', 'B1:B2', 'C1:C2', 'D1:D2', 'E1:G1', 'H1:H2', 'I1:I2', 'J1:J2', 'K1:K2', 'L1:L2', 'M1:M2', 'N1:N2', 'O1:O2',];
      const format = [
        { value: 'productType', format: '' },
        { value: 'productName', format: '' },
        { value: 'endingBookValue', format: '' },
        { value: 'endingNetAssetValue', format: '' },

        { value: 'intervalRate', format: '' },
        { value: 'intervalBenchmarkRate', format: '' },
        { value: 'intervalExcessRate', format: '' },

        { value: 'dailyRate', format: '' },
        { value: 'oneWeekRate', format: '' },
        { value: 'oneMonthRate', format: '' },
        { value: 'oneYearRate', format: '' },
        { value: 'twoYearRate', format: '' },
        { value: 'threeYearRate', format: '' },
        { value: 'sinceRate', format: '' },
        { value: 'inceptionDate', format: '' },
      ]

      filter_json_to_excel_inside_multiHeader([title1, title2], this.tableData, [], '投连业绩看板', merge, format)
    },

    /**
     * 连投 - 业绩看板
     * @param startDate 时间区间的开始时间：2023-02-02
     * @param endDate 时间区间的结束时间：2023-02-02
     */
    getPerformanceBoardLinked () {
      this.loadingLink = true
      this.tableData = []
      let params = {
        size: 10,
        page: 0,
        startDate: this.moment(this.time[0]).format('YYYY-MM-DD'),
        endDate: this.moment(this.time[1]).format('YYYY-MM-DD'),
      }
      getPerformanceBoardLinked(params).then(res => {
        this.loadingLink = false
        if (res && (res.mtycode == 200 || res.code == 200)) {
          this.tableData = res.data
        }
      })
    }
  },
}
</script>
<style>
.kanbantable .el-table__fixed .cell {
	line-height: 47px !important;
}
</style>
<style lang="scss" scoped>
@import '../../../tkdesign';

.border_table_header_title {
	display: flex;
	align-items: center;

	.block {
		width: 6px;
		height: 20px;
		border-radius: 35px;
		background-color: #4096ff;
		margin-right: 16px;
	}

	.icon-question {
		margin-left: 3px;
	}
}

.border_table_header_upload {
	width: 32px;
	line-height: 30px;
	border-radius: 4px;
	border: 1px solid #d9d9d9;
	text-align: center;
	margin-left: 16px;
}
</style>

