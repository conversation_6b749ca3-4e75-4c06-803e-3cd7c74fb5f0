<template>
	<div class="chart_one" v-show="show">
		<div v-loading="loadyejis">
			<div class="card_header">
				<div class="title">相关系数</div>
				<div>
					<el-button v-show="flage" icon="el-icon-document-delete" style="margin-left: 16px" @click="exportExcel">导出Excel</el-button>
					<el-button v-show="showFlag" @click="flage = !flage" icon="el-icon-refresh"></el-button>
				</div>
			</div>
			<div v-show="flage" style="width: 100%; min-width: 1000px; max-height: 600px; overflow: auto">
				<table>
					<tr class="correlationCoefficient_tr" style="position: sticky; top: 0; background: #ffffff">
						<th v-for="(item, index) in headerList" :key="'th' + index" :style="index == 0 ? 'position: sticky; left: 0' : ''">
							{{ item.label }}
						</th>
					</tr>
					<tr class="correlationCoefficient_tr" v-for="(item, index) in data" :key="'tr' + index">
						<td
							v-for="(obj, i) in headerList"
							:key="'td' + i"
							:style="`${cellStyle(item[obj.value])};${i == 0 ? 'position: sticky; left: 0;background:#ffffff' : ''}`"
						>
							{{ item[obj.value] }}
						</td>
					</tr>
				</table>
			</div>
			<correlation-matrix v-show="!flage" ref="correlationMatrix"></correlation-matrix>
		</div>
	</div>
</template>

<script>
import correlationMatrix from '@/pages/fundNewPool/analysis/components/components/correlationMatrix.vue';
import { filter_json_to_excel } from '@/utils/exportExcel.js';
export default {
	name: 'correlationCoefficient',
	components: { correlationMatrix },
	data() {
		return {
			headerList: [],
			tableData: [],
			data: [],
			show: true,
			loading: true,
			list: [],
			flage: true,
			showFlag: false
		};
	},
	methods: {
		getData(data, list) {
			this.loading = false;
			if (list?.length) {
				this.list = list;
				this.showFlag = true;
				this.getChartData();
			} else {
				this.showFlag = false;
				this.flage = true;
			}
			let headerLst = [];
			if (!data?.length) {
				return;
			}
			if (data?.[0]) {
				let obj = data?.[0];
				for (const key in obj) {
					headerLst.push({ label: key, value: key });
				}
				headerLst.unshift({
					label: '',
					value: 'name'
				});
			}
			this.headerList = headerLst;
			this.data = data.map((item) => {
				let name = '';
				for (const key in item) {
					if (item[key] == 1) {
						name = key;
					}
					item[key] = item[key].toFixed(4);
				}
				return {
					...item,
					name
				};
			});
		},
		hideLoading() {
			this.show = false;
		},
		cellStyle(value) {
			if (value <= 1 && value > 0.75) {
				return 'background:rgba(247, 101, 96, 1)';
			} else if (value <= 0.75 && value > 0.5) {
				return 'background:rgba(247, 101, 96, 0.8)';
			} else if (value <= 0.5 && value > 0.25) {
				return 'background:rgba(247, 101, 96, 0.6)';
			} else if (value <= 0.25 && value > 0) {
				return 'background:rgba(247, 101, 96, 0.4)';
			} else if (value <= 0 && value > -0.25) {
				return 'background:rgba(35, 195, 67, 0.4)';
			} else if (value <= -0.25 && value > -0.5) {
				return 'background:rgba(35, 195, 67, 0.6)';
			} else if (value <= -0.5 && value > -0.75) {
				return 'background:rgba(35, 195, 67, 0.8)';
			} else if (value <= -0.75 && value > -1) {
				return 'background:rgba(35, 195, 67, 1)';
			} else {
				return '';
			}
		},
		exportExcel() {
			let list = [];
			let obj = {};
			this.headerList.map((item) => {
				obj[item.label] = item.value;
				list.push(item);
			});
			let data = this.data.map((item, index) => {
				return {
					...item,
					name: this.headerList[index + 1].value
				};
			});
			filter_json_to_excel(list, data, '相关系数');
		},
		getChartData() {
			this.$refs['correlationMatrix'].getData(
				this.list?.map((item) => {
					return {
						fund_code: item.code,
						name: item.name
					};
				})
			);
		},
		createPrintWord() {
			let list = [];
			let obj = {};
			this.headerList.map((item) => {
				obj[item.label] = item.value;
				list.push(item);
			});
			let data = this.data.map((item, index) => {
				return {
					...item,
					name: this.headerList[index + 1].value
				};
			});
			return [];
			// return [...this.$exportWord.exportTitle('相关系数'), ...this.$exportWord.exportTable(list, data, '', true)];
		}
	}
};
</script>

<style lang="scss" scoped>
table {
	width: 100%;
}
.correlationCoefficient_tr {
	width: 100%;
	// overflow-x: scroll;
	display: flex;
	justify-content: space-around;
	height: 32px;
	align-items: center;
	th {
		flex: 1;
		min-width: 240px;
		height: 100%;
		text-align: center;
		font-family: 'PingFang';
		font-style: normal;
		font-weight: 400;
		font-size: 14px;
		line-height: 32px;
		color: rgba(0, 0, 0, 0.65);
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	td {
		flex: 1;
		min-width: 240px;
		height: 100%;
		text-align: center;
		font-family: 'Helvetica Neue';
		font-style: normal;
		font-weight: 400;
		font-size: 14px;
		line-height: 32px;
		color: rgba(0, 0, 0, 0.65);
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
}
</style>
