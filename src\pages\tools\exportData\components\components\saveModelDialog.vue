import fa from 'element-ui/src/locale/lang/fa';
<template>
	<el-dialog title="保存模版" :visible.sync="dialogVisible" width="400px">
		<div class="pt-20" style="border-top: 1px solid #e9e9e9; border-bottom: 1px solid #e9e9e9">
			<el-form :model="ruleForm" :inline="true" :rules="rules" ref="ruleForm" class="demo-ruleForm">
				<el-form-item label="模版名称" prop="name">
					<el-input v-model="ruleForm.name"></el-input>
				</el-form-item>
			</el-form>
		</div>
		<div slot="footer">
			<el-button @click="dialogVisible = false">取 消</el-button>
			<el-button type="primary" @click="submitForm">确 定</el-button>
		</div>
	</el-dialog>
</template>

<script>
export default {
	data() {
		return {
			dialogVisible: false,
			ruleForm: {
				name: ''
			},
			rules: {
				name: [{ required: true, message: '请输入模版名称', trigger: 'blur' }]
			}
		};
	},
	methods: {
		getData() {
			this.dialogVisible = true;
		},
		submitForm() {
			this.$refs['ruleForm'].validate((valid) => {
				if (valid) {
					this.$emit('submitModel', this.ruleForm);
					this.dialogVisible = false;
				} else {
					return false;
				}
			});
		}
	}
};
</script>

<style></style>
