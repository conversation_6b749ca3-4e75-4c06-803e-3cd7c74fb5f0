import request from '@/utils/request';

/**
 * 获取产品管理记录列表
 * @param params
 * @returns {*}
 */
export function getProductManager(params) {
    return request({
        url: '/api/taikang/product/manager/getProductManager',
        method: 'get',
        params
    });
}

/**
 * 获取全部所有投资经理
 * @returns {*}
 */
export function getManager() {
    return request({
        url: '/api/taikang/product/manager/option/manager',
        method: 'get',
    });
}

/**
 * 保存
 * @param data
 * @returns {*}
 */
export function saveManager(data) {
    return request({
        url: '/api/taikang/product/manager/save',
        method: 'post',
        data
    });
}

/**
 * 删除某一项
 * @param id
 * @returns {*}
 */
export function deleteManager(id) {
    return request({
        url: `/api/taikang/product/manager/del?id=${id}`,
        method: 'post',
    });
}

/**
 *删除全部
 * @returns {*}
 */
export function deleteAll() {
    return request({
        url: `/api/taikang/product/manager/delAll`,
        method: 'post',
    });
}
