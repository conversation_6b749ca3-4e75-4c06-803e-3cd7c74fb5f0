<template>
	<div>
		<div class="flex_card">
			<div v-for="item in templateList" :key="item.value" v-show="item.isshow" :class="item.type">
				<component :is="item.is" :ref="item.value" @resolveFather="item.methods" v-loading="loading" :showDescription="true"></component>
			</div>
		</div>
	</div>
</template>

<script>
// 基金报告期披露信用债评级分布(近年)
import creditBondRatingDistribution from '@/components/components/components/creditBondRatingDistribution/index.vue';
// 基金杠杆率
import leverageLevel from '@/components/components/components/leverageLevel/index.vue';
// 基金平均剩余期限
import averageRemainingMaturity from '@/components/components/components/averageRemainingMaturity/index.vue';
// 基金非交易日变动
import noTradingDayChanges from '@/components/components/components/noTradingDayChanges/index.vue';
export default {
	components: { creditBondRatingDistribution, leverageLevel, averageRemainingMaturity, noTradingDayChanges },
	data() {
		return {
			name: '怎么挣钱',
			info: {},
			templateList: [],
			requestOver: [],
			requestAll: 0,
			loading: true
		};
	},
	props: {
		showEditor: {
			type: Boolean,
			default: false
		}
	},
	methods: {
		// 接收/返回组件列表
		getTemplateList(list) {
			if (list) {
				this.templateList = [...list];
			} else {
				return this.templateList;
			}
		},
		// 获取父组件数据
		getData(data) {
			this.info = data;
			this.loading = true;
			this.requestOver = [];
			this.formatTemplatList();
		},
		// 获取打印数据
		async createPrintWord(info) {
			this.info = info;
			let printData = [];
			this.templateList.map((item) => {
				if (item.isshow) {
					if (this.$refs[item.value]?.[0].createPrintWord) {
						let list = this.$refs[item.value]?.[0].createPrintWord(this.info);
						printData.push(list);
					}
				}
			});
			let data = await Promise.all(printData);
			data.unshift(this.$exportWord.exportFirstTitle(this.name));
			return data;
		},
		// 格式化模板列表
		formatTemplatList() {
			this.$nextTick(() => {
				this.templateList.map((item) => {
					if (item.typelist.indexOf(this.info.type) !== -1) {
						this.$refs[item.value]?.[0]?.getData(this.info);
						this.loading = false;
					}
				});
			});
		}
	}
};
</script>

<style></style>
