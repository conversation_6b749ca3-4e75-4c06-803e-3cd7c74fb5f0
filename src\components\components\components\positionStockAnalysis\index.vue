<template>
	<div class="chart_one">
		<div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px">
			<div class="title">持仓股票分析</div>
			<div>
				<span style="margin-right: 8px">季度选择:</span
				><el-select v-model="value" placeholder="" @change="changeYearQtr">
					<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>
				<el-button style="margin-left: 16px" icon="el-icon-document-delete" @click="exportExcel">导出Excel</el-button>
			</div>
		</div>
		<el-table
			v-loading="topTenLoading"
			class="fund-analysis-table"
			:data="allData"
			@row-click="clickTopTenLine"
			:row-class-name="tableRowClassName"
			height="500"
			:highlight-current-row="true"
			:default-sort="{ prop: 'value', order: 'descending' }"
		>
			<el-table-column
				v-for="item in column"
				:key="item.value"
				:prop="item.value"
				:label="item.label"
				:sortable="item.sortable ? item.sortable : false"
				:show-overflow-tooltip="item.value == 'fund_list' ? true : false"
				:min-width="item.width ? item.width : ''"
				align="gotoleft"
			>
				<template #header>
					<long-table-popover-chart
						v-if="item.popover"
						:data="formatTableData()"
						date_key="stock_name"
						:data_key="item.value"
						:show_name="item.label"
						:formatter="
							function (val) {
								return val;
							}
						"
					>
						<span>{{ item.label }}</span>
					</long-table-popover-chart>
					<span v-else>{{ item.label }}</span>
				</template>
				<template slot-scope="{ row }">
					<span>{{ item.format ? item.format(row[item.value]) : row[item.value] }}</span>
				</template>
			</el-table-column>
			<template slot="empty">
				<el-empty image-size="160"></el-empty>
			</template>
		</el-table>
		<div class="chart_one" v-loading="barraLoading" v-if="info.type != 'equityhk'">
			<div class="title">
				<span style="font-weight: bold">{{
					currentStockCode
						? allData.filter((item) => {
								return item.stock_code == currentStockCode;
						  })[0].stock_name
						: ''
				}}</span>
				<span>股票Barra分析</span>
			</div>
			<div class="charts_fill_class" v-loading="barraLoading">
				<v-chart
					v-show="!emptyShow"
					class="charts_one_class"
					:options="barraOption"
					autoresize
					element-loading-text="暂无数据"
					element-loading-spinner="el-icon-document-delete"
					element-loading-background="rgba(239, 239, 239, 0.5)"
				/>
				<el-empty v-show="emptyShow" image-size="160"></el-empty>
			</div>
		</div>
	</div>
</template>

<script>
// 股票持仓分析
import { getHoldStockMsg, getStockBarraStyle } from '@/api/pages/SystemMixed.js';
// 最新报告持仓分析
import { getDateList } from '@/api/pages/Analysis.js';
// 股票Barra分析
import { barChartOption } from '@/utils/chartStyle.js';
import { filter_json_to_excel } from '@/utils/exportExcel.js';

// 持仓股票分析
export default {
	name: 'positionStockAnalysis',
	data() {
		return {
			emptyShow: true,
			topTenLoading: true,
			fundAnalysisTable: [],
			fundTypeList: [],
			fundType: '',
			currentStockCode: '',
			barraChartLoading: true,
			column: [
				{
					label: '股票名称',
					width: '120px',
					value: 'stock_name',
					popover: false
				},
				{
					label: '股票代码',
					width: '120px',
					value: 'stock_code',
					popover: false
				},
				{
					label: '行业',
					width: '120px',
					value: 'swlevel1',
					popover: false
				},
				{
					label: '持有个股基金数(公司内)',
					width: '220px',
					value: 'fund_list_number',
					sortable: true,
					popover: true
				},
				{
					label: '个股共识度(公司内)',
					width: '220px',
					value: 'company_consensus',
					format: this.fix2p,
					sortable: true,
					popover: true
				},
				{
					label: '持有个股基金数(全公募)',
					width: '220px',
					value: 'number',
					sortable: true,
					popover: true
				},
				{
					label: '个股共识度(全公募)',
					width: '220px',
					value: 'market_consensus',
					format: this.fix2p,
					sortable: true,
					popover: true
				},
				{
					label: '持仓市值(亿)',
					width: '160px',
					value: 'value',
					format: this.fix8,
					sortable: true,
					popover: true
				},
				{
					label: '持仓市值占流动市值比',
					width: '200px',
					value: 'marketValueRatio',
					format: this.fixp,
					sortable: true,
					popover: true
				},
				{
					label: '股票市值(亿)',
					width: '160px',
					value: 'totalmv',
					format: this.fix8,
					sortable: true,
					popover: true
				},
				{
					label: '持股数量(万股)',
					width: '160px',
					value: 'holdings',
					format: this.fix4,
					sortable: true,
					popover: true
				},
				{
					label: '持有基金列表',
					value: 'fund_list',
					popover: false
				}
			],
			show: true,
			barraOption: {},
			barraLoading: true,
			info: {},
			options: [],
			allData: [],
			value: ''
		};
	},
	methods: {
		// 隐藏模块
		hideLoading() {
			this.allData = [];
			this.topTenLoading = false;
		},
		// 切换季度
		changeYearQtr(val) {
			this.value = val;
			this.getHoldStockMsg();
		},
		// 格式化数据
		formatTableData() {
			let data = [];
			this.allData.map((item) => {
				let obj = { ...item };
				for (const key in item) {
					let format = this.column.find((obj) => {
						return obj.value == key;
					})?.format;
					if (format) {
						let val = format(item[key]);
						obj[key] = typeof val == 'string' ? (val.includes('%') ? val?.split('%')?.[0] * 1 : !isNaN(val) ? val * 1 : val) : val;
					}
				}
				data.push(obj);
			});
			return data;
		},
		// 获取报告持仓季度列表
		async getDateList(info) {
			this.info = info;
			let data = await getDateList({ code: this.info.code, type: this.info.type, flag: this.info.flag });
			if (data?.mtycode == 200) {
				this.getDateListData(data?.data);
			}
		},
		// 获取股票持仓分析
		async getHoldStockMsg() {
			this.topTenLoading = true;
			let data = await getHoldStockMsg({ code: this.info.code, number: 10, type: this.info.type, quarter: this.value });
			if (data?.mtycode == 200) {
				this.getData(data?.data);
			} else {
				this.hideLoading();
			}
		},
		// 获取持仓季度列表
		getDateListData(data) {
			this.options = data
				?.sort((a, b) => {
					return this.moment(b).isAfter(a) ? 1 : -1;
				})
				?.map((item) => {
					return { label: item, value: item };
				});
			this.value = this.options?.[0]?.value;
			this.getHoldStockMsg();
		},
		// 获取持仓股票数据
		async getData(data) {
			this.allData = data.map((item) => {
				return {
					...item,
					fund_list_number: item['0'].length,
					fund_list: item['0'].join(','),
					value: item.value * 1,
					totalmv: item.totalmv * 1,
					holdings: item.holdings * 1,
					marketValueRatio:
						String(Number(item.value) / Number(item.totalmv)) == 'NaN' ? '--' : (Number(item.value) / Number(item.totalmv)) * 100,
					company_consensus: item['0'].length / item.company_fund_all_number,
					market_consensus: item.number / item.market_fund_all_number
				};
			});
			this.allData.map((item) => {
				let index = this.options.findIndex((obj) => {
					return obj.value == item.yearqtr;
				});
				if (index == -1) {
					this.options.push({
						label: item.yearqtr,
						value: item.yearqtr
					});
				}
			});
			if (this.info.type != 'equityhk') {
				this.currentStockCode = this.allData?.find((v) => !v.stock_code.includes('HK'))?.stock_code;
				this.getBarraChart();
			}
			this.topTenLoading = false;
		},
		// 点击股票前十大table单行显示对应barra
		clickTopTenLine(row, column, event) {
			if (this.info.type != 'equityhk') {
				this.currentStockCode = row.stock_code;
				this.getBarraChart();
			}
		},
		// 为表格行添加class
		tableRowClassName({ row, rowIndex }) {
			if (this.info.type != 'equityhk') {
				if (row.stock_code == this.currentStockCode) {
					return 'table-even-line';
				}
			}
		},
		// 股票Barra分析chart
		async getBarraChart() {
			this.barraLoading = true;
			this.emptyShow = true;
			let data = await getStockBarraStyle({ stock_code: this.currentStockCode });
			if (data?.mtycode == 200) {
				if (
					this.FUNC.isValidObj(data.data) &&
					this.FUNC.isValidObj(data.data[0]) &&
					this.FUNC.isValidObj(data.data[1]) &&
					this.FUNC.isValidObj(data.data[0].name) &&
					this.FUNC.isValidObj(data.data[1].value)
				) {
					this.barraLoading = false;
					this.emptyShow = false;
					let label_list = data.data[0].name.slice();
					let data_list = data.data[1].value.slice();
					let labelList = [],
						dataList = [];
					label_list.forEach((item, index) => {
						if (this.COMMON.barra_zh_en.en.includes(item)) {
							let zh_index = this.COMMON.barra_zh_en.en.indexOf(item);
							labelList.push(this.COMMON.barra_zh_en.zh[zh_index]);
							dataList.push(data_list[index]);
						}
					});
					let dataMin = Math.min(...dataList);
					let dataMax = Math.max(...dataList);
					let option = barChartOption({
						tooltip: {
							type: 'shadow',
							formatter(params) {
								return `${params[0].marker} ${params[0].name}<br>${params[0].seriesName}: ${
									params[0].value ? params[0].value.toFixed(4) : '--'
								}`;
							}
						},
						visualMap: {
							show: false,
							orient: 'horizontal',
							left: 'center',
							min: dataMin,
							max: dataMax,
							text: ['High Score', 'Low Score'],
							dimension: 0,
							inRange: {
								color: ['#0BBF9B', '#EA5454']
							}
						},
						xAxis: [{ type: 'value' }],
						yAxis: [
							{
								show: false,
								type: 'category',
								data: labelList
							}
						],
						series: [
							{
								name: '因子暴露',
								type: 'bar',
								stack: '总量',
								label: {
									show: true,
									formatter: '{b}',
									fontSize: '14px'
								},
								data: dataList
							}
						]
					});
					this.barraOption = option;
					this.barraLoading = false;
				}
			}
		},
		// 导出excel
		exportExcel() {
			let column = this.column.map((item) => {
				return {
					...item,
					format: ''
				};
			});
			let name = this.options.find((item) => {
				return item.value == this.value;
			})?.label;
			filter_json_to_excel(column, this.allData, this.info.name + '-' + name + '持仓股票分析');
		},
		// 百分化
		fix2p(val) {
			return val * 1 ? (val * 100).toFixed(2) + '%' : '--';
		},
		// 加百分号
		fixp(val) {
			return val * 1 ? (val * 1).toFixed(2) + '%' : '--';
		},
		// 亿元化
		fix8(val) {
			return val * 1 ? (val / 10 ** 8).toFixed(2) : '-';
		},
		// 万元化
		fix4(val) {
			return val * 1 ? (val / 10 ** 4).toFixed(2) : '-';
		}
	}
};
</script>

<style></style>
