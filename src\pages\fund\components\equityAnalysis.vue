<!-- 历史持仓 -->
<template>
	<div>
		<div class="flex_card">
			<div v-for="item in templateList" :key="item.value" v-show="item.isshow" :class="item.type">
				<component :is="item.is" :ref="item.value" @resolveFather="item.methods" v-loading="loading" :showDescription="true"></component>
			</div>
		</div>
	</div>
</template>

<script>
// 已披露行业配置
import industryReportPosition from '@/components/components/components/industryReportPosition/index.vue';
// 行业配置变化半年度堆叠图
import industryPositionChange from '@/components/components/components/industryPositionChange/index.vue';
// 行业配置表现
import industryAllocationPerformance from '@/components/components/components/industryAllocationPerformance/index.vue';
// 行业高低配
import industryEvaluation from '@/components/components/components/industryEvaluation/index.vue';
// 行业能力圈
import industryCapacity from '@/components/components/components/industryCapacity/index.vue';

export default {
	components: {
		industryPositionChange,
		industryAllocationPerformance,
		industryReportPosition,
		industryEvaluation,
		industryCapacity
	},
	data() {
		return {
			name: '行业分析',
			info: {},
			templateList: [],
			requestOver: [],
			requestAll: 0,
			loading: true
		};
	},
	props: {
		showEditor: {
			type: Boolean,
			default: false
		}
	},
	methods: {
		// 接收/返回组件列表
		getTemplateList(list) {
			if (list) {
				this.templateList = [...list];
			} else {
				return this.templateList;
			}
		},
		// 获取父组件数据
		getData(data) {
			this.info = data;
			this.loading = true;
			this.requestOver = [];
			this.formatTemplatList();
		},
		// 获取打印数据
		async createPrintWord(info) {
			this.info = info;
			let printData = [];
			this.templateList.map((item) => {
				if (item.isshow) {
					if (this.$refs[item.value]?.[0].createPrintWord) {
						let list = this.$refs[item.value]?.[0].createPrintWord(this.info);
						printData.push(list);
					}
				}
			});
			let data = await Promise.all(printData);
			data.unshift(this.$exportWord.exportFirstTitle(this.name));
			return data;
		},
		// 格式化模板列表
		formatTemplatList() {
			this.$nextTick(() => {
				this.templateList.map((item) => {
					if (item.typelist.indexOf(this.info.type) !== -1) {
						this.$refs[item.value]?.[0]?.getData(this.info);
						this.loading = false;
					}
				});
			});
		}
	}
};
</script>

<style></style>
