<!-- 产品头部信息 -->
<template>
	<div class="flex_start" style="width: 100%; height: 660px">
		<!-- 基础信息 -->
		<div class="">
			<components :is="'basicInformation' + info.flag" :ref="'basicInformation' + info.flag"></components>
		</div>
		<!-- 走势图 -->
		<div class="" style="flex-grow: 1">
			<fund-return-curve ref="fundReturnCurve"></fund-return-curve>
		</div>
	</div>
</template>

<script>
// 基金基础信息
import basicInformation1 from '@/components/components/components/fundBasicInformation/components/fundInfo.vue';
// 基金经理基础信息
import basicInformation2 from '@/components/components/components/fundBasicInformation/components/managerInfo.vue';
// 走势图
import fundReturnCurve from '@/components/components/components/fundReturnCurve/index.vue';

export default {
	components: {
		basicInformation1,
		basicInformation2,
		fundReturnCurve
	},
	data() {
		return {
			info: {}
		};
	},
	props: {
		showEditor: {
			type: Boolean,
			default: false
		}
	},
	methods: {
		// 获取打印配置
		async createPrintWord() {
			// 基金||基金经理基础信息
			let basicInformation = await this.$refs['basicInformation' + this.info.flag].createPrintWord();
			// 业绩曲线
			let fundReturnCurve = await this.$refs['fundReturnCurve'].createPrintWord();
			return [...basicInformation, ...fundReturnCurve];
		},
		// 父组件调用
		getData(info) {
			this.info = info;
			this.$nextTick(() => {
				console.log(this.$refs);
				this.$refs['basicInformation' + info.flag]?.getData(this.info);
				this.$refs['fundReturnCurve']?.getData(this.info);
			});
		}
	}
};
</script>
