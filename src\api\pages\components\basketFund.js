import request from '@/api/request';
// 获取临时池基金列表
export function basketFund() {
	return request({
		url: '/pool/basket_fund/',
		method: 'get'
	});
}
// 获取正式池列表
export function fundPool() {
	return request({
		url: '/pool/fund_pool/',
		method: 'get'
	});
}
// 创建正式池列表
export function fundPoolC(data) {
	return request({
		url: '/pool/fund_pool/',
		method: 'post',
		data
	});
}
// 获取基金入池列表
export function fundPools(data) {
	return request({
		url: '/pool/pool_funds/',
		method: 'post',
		data
	});
}
