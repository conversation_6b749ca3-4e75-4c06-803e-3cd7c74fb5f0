<template>
	<div class="drop-down-menu-box">
		<el-select
			:loading="loading"
			clearable
			filterable
			v-model="chooseName"
			:placeholder="placeholderName"
			@change="deliverFundGuideName"
			>
			<el-option v-for="item in dropDownData" :key="item.value" :label="item.label" :value="item.value"> </el-option>
		</el-select>
	</div>
</template>

<script>
export default {
	data() {
		return {
			dropDownData: [],
			loading: true,
			chooseName: '基金准则'
		};
	},
	methods: {
		getData(data, loading = true) {
			this.chooseName = '基金准则';
			this.loading = loading;
			this.dropDownData = data;
		},
		deliverFundGuideName(data) {
			this.chooseName = data;
			this.$emit('deliverFundGuideName', data);
		}
	}
};
</script>