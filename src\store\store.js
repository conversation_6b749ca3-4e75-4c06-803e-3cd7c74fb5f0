import Vue from 'vue';

import Vuex from 'vuex';

Vue.use(Vuex);

export default new Vuex.Store({
	state: {
		id: localStorage.getItem('id') || '',
		count: 1002,
		loginfalg: 0,
		token: localStorage.getItem('token') || '',
		username: localStorage.getItem('username') || '',
		retoken: localStorage.getItem('retoken') || '',
		userType: localStorage.getItem('userType') || '',
		role: localStorage.getItem('role') || '',
		cacheData: null,
		ws: null,
		betaFilterPageSize: null,
		voicePageInfo: {},
		isPublic: false,
		pageinfo: ''
	},
	actions: {
		//`异步更改数据`
		changetoken({ commit }, params) {
			commit('setToken', params);
		},
		changeusername({ commit }, params) {
			commit('setUsername', params);
		},
		changeuserid({ commit }, params) {
			commit('setUserid', params);
		},
		changeuserrole({ commit }, params) {
			commit('setUserrole', params);
		},
		changerefreshtoken({ commit }, params) {
			commit('setreToken', params);
		},
		changeCacheData({ commit }, params) {
			commit(`setCacheData`, params);
		},
		changeWs({ commit }, params) {
			commit(`setWs`, params);
		}
	},
	mutations: {
		// increment:state => state.count ++,
		// decrement:state => state.count --,
		setToken: (state, data) => {
			state.token = data;
			localStorage.setItem('token', data);
		},
		setreToken: (state, data) => {
			state.retoken = data;
			localStorage.setItem('retoken', data);
		},
		setUsername: (state, data) => {
			state.username = data;
			localStorage.setItem('username', data);
		},
		setUserid: (state, data) => {
			state.id = data;
			localStorage.setItem('id', data);
		},
		setUserrole: (state, data) => {
			state.role = data;
			localStorage.setItem('role', data);
		},
		setUserType(state, data) {
			state.userType = data;
			localStorage.setItem('userType', data);
		},
		setCacheData(state, data) {
			state.cacheData = data;
		},
		setWs(state, data) {
			state.ws = data;
		},
		setVoicePageInfo(state, data) {
			state.voicePageInfo = data;
		},
		setIsPublic(state, data) {
			state.isPublic = data;
		},
		setPageInfo(state, data) {
			state.pageinfo = data;
		}
	}
});
