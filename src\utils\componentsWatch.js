export function watch(val, that) {
  if (val?.length > 0) {
    val?.map((obj) => {
      let active = that.requestAll.filter((item) => {
        return item.getRequestData == obj;
      });
      if (active?.length) {
        active?.map((item) => {
          if (item?.getData && item?.getData !== "None") {
            that?.[item?.getData]();
          }
          let index = that.requestAll.findIndex((obj) => {
            return obj.value == item.value;
          });
          that.requestAll.splice(index, 1);
        });
      }
    });
  }
  if (that.requestAll.length) {
    if (
      that.requestAll.every((item) => {
        return item?.getRequestData == "None" || !item?.getRequestData;
      })
    ) {
      that.requestAll?.map((item, index) => {
        if (item?.getData && item?.getData !== "None") {
          that?.[item?.getData]();
        }
      });
      that.requestAll = [];
      // that.$emit('overRequest', that.componentsName);
    }
  } else {
    // that.$emit('overRequest', that.componentsName);
  }
}
