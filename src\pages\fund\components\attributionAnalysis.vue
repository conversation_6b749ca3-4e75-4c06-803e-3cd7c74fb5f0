<!-- 能力分析 -->
<template>
	<div>
		<div class="flex_card">
			<div v-for="item in templateList" :key="item.value" v-show="item.isshow" :class="item.type">
				<component :is="item.is" :ref="item.value" @resolveFather="item.methods" v-loading="loading" :showDescription="true"></component>
			</div>
		</div>
	</div>
</template>

<script>
// Brinson归因
import brinsonAttribution from '@/components/components/components/brinsonAttribution/index.vue';
// TM模型分析
import TMModelAnalysis from '@/components/components/fundComponents/TMModelAnalysis/index.vue';
// 交易股票估值-盈利特征统计
import PBROEcharacteristics from '@/components/components/components/PBROEcharacteristics/index.vue';
// 调仓节奏
import warehouseAdjustmentRhythm from '@/components/components/fundComponents/warehouseAdjustmentRhythm/index.vue';
// 分年度类Brinson归因
import excessReturnAttribution from '@/components/components/components/excessReturnAttribution/index.vue';

// 基金收益来源分析
import fundProfitAnalysis from '@/components/components/components/fundProfitAnalysis/index.vue';
// 利息收益分析
import interestIncomeAnalysis from '@/components/components/components/interestIncomeAnalysis/index.vue';
// 投资收益分析
import investmentIncomeAnalysis from '@/components/components/components/investmentIncomeAnalysis/index.vue';

export default {
	components: {
		brinsonAttribution,
		excessReturnAttribution,
		TMModelAnalysis,
		PBROEcharacteristics,
		warehouseAdjustmentRhythm,
		fundProfitAnalysis,
		interestIncomeAnalysis,
		investmentIncomeAnalysis
	},
	data() {
		return {
			name: '归因分析',
			templateList: [],
			info: {},
			requestOver: [],
			requestAll: [],
			loading: true
		};
	},
	props: {
		showEditor: {
			type: Boolean,
			default: false
		}
	},
	methods: {
		// 接收/返回组件列表
		getTemplateList(list) {
			if (list) {
				this.templateList = [...list];
			} else {
				return this.templateList;
			}
		},
		// 获取父组件数据
		getData(data) {
			this.info = data;
			this.loading = true;
			this.requestOver = [];
			this.formatTemplatList();
		},
		// 获取打印数据
		async createPrintWord(info) {
			this.info = info;
			let printData = [];
			this.templateList.map((item) => {
				if (item.isshow) {
					if (this.$refs[item.value]?.[0].createPrintWord) {
						let list = this.$refs[item.value]?.[0].createPrintWord(this.info);
						printData.push(list);
					}
				}
			});
			let data = await Promise.all(printData);
			data.unshift(this.$exportWord.exportFirstTitle(this.name));
			return data;
		},
		// 格式化模板列表
		formatTemplatList() {
			this.$nextTick(() => {
				this.templateList.map((item) => {
					if (item.typelist.indexOf(this.info.type) !== -1) {
						this.$refs[item.value]?.[0]?.getData(this.info);
						this.loading = false;
					}
				});
			});
		}
	}
};
</script>

<style scoped>
div .six_buy_sell_chart {
	width: 300px;
	height: 200px;
}
.charts_three_class {
	width: 100%;
	/* flex:1;
	min-width:752px !important; */
	/* height: 200px; */
	position: relative;
	page-break-inside: avoid;
}
.flex_card .small_template {
	position: relative;
	height: 324px;
	overflow-x: hidden;
}
</style>
