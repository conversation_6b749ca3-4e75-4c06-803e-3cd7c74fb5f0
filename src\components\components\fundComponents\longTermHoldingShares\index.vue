<template>
  <div id="longTermHoldingShares">
    <analysis-card-title title="个股复盘"
                         @downloadExcel="exportExcel"></analysis-card-title>
    <div style="width: 100%"
         v-loading="loading">
      <el-table :data="data"
                style="width: 99%"
                max-height="414px"
                class="table longholdstock"
                ref="multipleTable"
                :default-sort="{ prop: 'times', order: 'descending' }"
                header-cell-class-name="table-header"
                border
                stripe
                @sort-change="sortChange">
        <el-table-column v-for="item in column"
                         :key="item.value"
                         :align="item.align ? item.align : 'gotoleft'"
                         :prop="item.value"
                         :label="item.label"
                         :min-width="item.width"
                         sortable="custom">
          <template #header>
            <long-table-popover-chart v-if="item.popover"
                                      :data="formatTableData()"
                                      date_key="name"
                                      :data_key="item.value"
                                      :show_name="item.label">
              <span>{{ item.label }}</span>
            </long-table-popover-chart>
            <span v-else>{{ item.label }}</span>
          </template>
          <template slot-scope="{ row }">
            <el-link v-if="item.value == 'name'"
                     type="primary"
                     @click="showfundmsg(row)">{{ row.name }}</el-link>
            <span v-else>
              {{ item.format ? item.format(row[item.value]) : row[item.value] }}
            </span>
          </template>
        </el-table-column>
        <template slot="empty">
          <el-empty image-size="160"></el-empty>
        </template>
      </el-table>
    </div>
    <el-dialog title="高频股票分析"
               width="60vw"
               :visible.sync="vishighz">
      <highFrequencyStockAnalysis ref="highFrequencyStockAnalysis"></highFrequencyStockAnalysis>
    </el-dialog>
  </div>
</template>

<script>
// 长期持有个股

import highFrequencyStockAnalysis from './components/highFrequencyStockAnalysis.vue';
import { filter_json_to_excel } from '@/utils/exportExcel.js';

// 长期持有个股
import { getStocksLongHold } from '@/api/pages/Analysis.js';
export default {
  name: 'longTermHoldingShares',
  components: { highFrequencyStockAnalysis },
  data () {
    return {
      loading: true,
      search: '',
      minTimes: '',
      maxTimes: '',
      data: [],
      vishighz: false,
      chooseSharesCode: null,
      info: {},
      column: [
        {
          label: '行业',
          value: 'swlevel1',
          sortable: false,
          popover: false
        },
        {
          label: '名称',
          value: 'name',
          sortable: false,
          popover: false
        },
        {
          label: '次数',
          value: 'times',
          sortable: true,
          popover: true
        },
        {
          label: '个股期间收益',
          value: 'contribution',
          align: 'right',
          format: this.fix25p,
          sortable: true,
          popover: true
        },
        {
          label: '个股对净值收益贡献度',
          value: 'stock_return',
          align: 'right',
          width: '150px',
          format: this.fix2p,
          sortable: true,
          popover: true
        },
        {
          label: '平均权重',
          value: 'weight',
          align: 'right',
          format: this.fixp,
          sortable: true,
          popover: true
        },
        {
          label: '首次公告',
          value: 'first_report',
          sortable: true,
          popover: false
        },
        {
          label: '末次公告',
          value: 'last_report',
          sortable: true,
          popover: false
        }
      ]
    };
  },
  methods: {
    openvideo () {
      window.open('https://www.bilibili.com/video/BV1394y1Q73k?share_source=copy_web');
    },
    // 获取长期持有个股数据
    async getStocksLongHold () {
      this.loading = true;
      let data = await getStocksLongHold({
        flag: this.info.flag,
        code: this.info.code,
        type: this.info.type,
        start_date: this.info.start_date,
        end_date: this.info.end_date,
        template: 'longTermHoldingShares'
      });
      this.loading = false;
      if (data?.mtycode == 200) {
        this.data = data?.data
          .map((item) => {
            return {
              ...item,
              stock_return: item.contribution * item.weight
            };
          })
          ?.sort((a, b) => {
            let key = 'times';
            if (a?.[key] === '--' && b?.[key] !== '--') return 1;
            if (a?.[key] !== '--' && b?.[key] === '--') return -1;
            if (a?.[key] === '--' && b?.[key] === '--') return 0;
            return b?.[key] * 1 - a?.[key] * 1;
          });
      }
    },
    async getData (info) {
      this.info = info;
      if (this.info.flag == 1) {
        if (
          this.column.every((item) => {
            return item.value != 'min_pe' && item.value != 'max_pe';
          })
        ) {
          this.column.push(
            {
              label: '最小市盈率',
              value: 'min_pe',
              align: 'right',
              format: this.fix3,
              sortable: true,
              popover: true
            },
            {
              label: '最大市盈率',
              value: 'max_pe',
              align: 'right',
              format: this.fix3,
              sortable: true,
              popover: true
            }
          );
        }
      }
      await this.getStocksLongHold();
    },
    // 表格排序
    sortChange (val) {
      let data = this.data;
      // 降序
      if (val?.order == 'descending') {
        data = data?.sort((a, b) => {
          if (a?.[val?.prop] === '--' && b?.[val?.prop] !== '--') return 1;
          if (a?.[val?.prop] !== '--' && b?.[val?.prop] === '--') return -1;
          if (a?.[val?.prop] === '--' && b?.[val?.prop] === '--') return 0;
          return b?.[val?.prop] * 1 - a?.[val?.prop] * 1;
        });
      } else if (val?.order == 'ascending') {
        // 升序
        data = data?.sort((a, b) => {
          if (a?.[val?.prop] === '--' && b?.[val?.prop] !== '--') return 1;
          if (a?.[val?.prop] !== '--' && b?.[val?.prop] === '--') return -1;
          if (a?.[val?.prop] === '--' && b?.[val?.prop] === '--') return 0;
          return a?.[val?.prop] * 1 - b?.[val?.prop] * 1;
        });
      }
    },
    // 打开 行业表格 股票分析弹窗
    showfundmsg (row) {
      this.chooseSharesCode = row.stock_code;
      this.vishighz = true;
      this.$nextTick(() => {
        this.$refs['highFrequencyStockAnalysis'].getData({
          ...this.info,
          stock_code: row.stock_code,
          start_date: this.FUNC.earlyAndLateDate([row.first_report]).earlyDate,
          end_date: this.FUNC.earlyAndLateDate([row.last_report]).lateDate
        });
      });
    },
    formatFrequency (key) {
      this[key] = this[key].replace(/\D/g, '');
    },
    // 搜索个股
    searchStock () {
      console.log(this.search, this.maxTimes, this.minTimes);
    },
    formatTableData () {
      let data = [];
      this.data.map((item) => {
        let obj = { ...item };
        for (const key in item) {
          let format = this.column.find((obj) => {
            return obj.value == key;
          })?.format;
          if (format) {
            let val = format(item[key]);
            obj[key] = typeof val == 'string' ? (val.includes('%') ? val?.split('%')?.[0] * 1 : !isNaN(val) ? val * 1 : val) : val;
          }
        }
        data.push(obj);
      });
      return data;
    },
    fixp (val) {
      return val * 1 && !isNaN(val) ? (val * 1).toFixed(2) + '%' : '--';
    },
    fix2p (val) {
      return val * 1 && !isNaN(val) ? (val * 100).toFixed(2) + '%' : '--';
    },
    fix25p (val) {
      return val * 1 && !isNaN(val) ? (val / 100).toFixed(2) + '%' : '--';
    },
    fix3 (val) {
      return val * 1 && !isNaN(val) ? (val * 1).toFixed(3) : '--';
    },
    exportExcel () {
      let list = this.column.map((item) => {
        return { label: item.label, value: item.value };
      });
      let data = this.data.map((item) => {
        let obj = {};
        for (const key in item) {
          let index = this.column.findIndex((v) => v.value == key);
          if (this.column[index]?.format) {
            obj[key] = this.column[index]?.format(item[key]);
          } else {
            obj[key] = item[key];
          }
        }
        return obj;
      });
      filter_json_to_excel(list, data, '个股复盘');
    },
    async createPrintWord (info) {
      await this.getData(info);
      let list = this.column.map((item) => {
        return { label: item.label, value: item.value };
      });
      let data = this.data.map((item) => {
        let obj = {};
        for (const key in item) {
          let index = this.column.findIndex((v) => v.value == key);
          if (this.column[index]?.format) {
            obj[key] = this.column[index]?.format(item[key]);
          } else {
            obj[key] = item[key];
          }
        }
        return obj;
      });
      if (data.length) {
        return [
          ...this.$exportWord.exportTitle('个股复盘'),
          ...this.$exportWord.exportTable(
            list,
            data
              .sort((a, b) => {
                return b.times - a.times;
              })
              .slice(0, 10),
            '',
            true
          )
        ];
      } else {
        return [];
      }
    }
  }
};
</script>

<style></style>
