<template>
	<div>
		<analysis-card-title title="市场风格表现" @downloadExcel="exportExcel"> </analysis-card-title>
		<div>
			<el-table v-loading="loading" :data="table2" height="300px">
				<el-table-column props="description" align="gotoleft" label="描述">
					<template slot-scope="scope">
						<div>{{ scope.row.description }}</div>
					</template>
				</el-table-column>
				<el-table-column props="cum_return" align="gotoleft" label="累计收益">
					<template slot-scope="scope"
						><div>{{ scope.row.cum_return | fix2p }}</div></template
					>
				</el-table-column>
				<el-table-column props="maxdrawdown" align="gotoleft" label="最大回撤">
					<template slot-scope="scope"
						><div>{{ scope.row.maxdrawdown | fix2p }}</div></template
					>
				</el-table-column>
				<el-table-column props="sharpe" align="gotoleft" label="夏普率">
					<template slot-scope="scope"
						><div>{{ scope.row.sharpe | fix3 }}</div></template
					>
				</el-table-column>
			</el-table>
		</div>
	</div>
</template>

<script>
import { exportTitle, exportTable } from '@/utils/exportWord.js';
import { filter_json_to_excel } from '@/utils/exportExcel.js';

// 市场风格表现
import { getMarketStyleBYlan } from '@/api/pages/Analysis.js';

export default {
	name: 'stockDebtSynthesis',
	data() {
		return {
			table2: [],
			table22: [],
			loading: true,
			info: {}
		};
	},
	filters: {
		fix3(value) {
			return parseInt(value * 1000) / 1000;
		},
		fix2p(value) {
			return (value * 100).toFixed(2) + '%';
		}
	},
	methods: {
		// 获取市场风格表现
		async getMarketStyleBYlan() {
			let data = await getMarketStyleBYlan({
				code: this.info.code,
				type: this.info.type,
				flag: this.info.flag,
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			if (data?.mtycode == 200) {
				return data?.data;
			} else {
				return [];
			}
		},
		async getData(info) {
			this.info = info;
			let data = await this.getMarketStyleBYlan();
			this.loading = false;
			this.table2 = data;
		},
		exportExcel() {
			let list = [
				{
					label: '描述',
					value: 'description'
				},
				{
					label: '年化收益',
					value: 'ave_return',
					format: 'fix2p'
				},
				{
					label: '最大回撤',
					value: 'maxdrawdown',
					format: 'fix2p'
				},
				{
					label: '夏普率',
					value: 'sharpe',
					format: 'fix3'
				}
			];
			filter_json_to_excel(list, this.table2, '市场风格表现');
		},
		createPrintWord() {
			let list = [
				{
					label: '描述',
					value: 'description'
				},
				{
					label: '年化收益',
					value: 'ave_return',
					format: 'fix2p'
				},
				{
					label: '最大回撤',
					value: 'maxdrawdown',
					format: 'fix2p'
				},
				{
					label: '夏普率',
					value: 'sharpe',
					format: 'fix3'
				}
			];
			if (this.table2.length) {
				return [...exportTitle('市场风格表现'), ...exportTable(list, this.table2)];
			} else {
				return [];
			}
		}
	}
};
</script>

<style></style>
