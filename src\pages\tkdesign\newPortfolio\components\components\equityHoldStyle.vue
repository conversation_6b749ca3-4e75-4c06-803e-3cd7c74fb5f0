<template>
    <div class="plate-wrapper fund-performance-board-wrapper">
        <combinationComponentHeader title="当前权益持仓风格" showMoreBtn @download="exportExcel">
            <template slot="right">
                <div style="margin-right: 12px;">
                        <el-radio-group class="radio-group-wrapper" v-model="form.holdFlag" size="small" style="margin-left: 0 !important;" @input="radioChange">
                            <el-radio-button label="big10">前十大</el-radio-button>
                            <el-radio-button label="all">全持仓</el-radio-button>
                        </el-radio-group>
                    </div>
                    <div style="margin-right: 12px;">
                        <el-radio-group class="radio-group-wrapper" v-model="form.penetrateFlag" size="small" style="margin-left: 0 !important;" @input="radioChange">
                            <el-radio-button :label="true">穿透fof持仓</el-radio-button>
                            <el-radio-button :label="false">不穿透fof持仓</el-radio-button>
                        </el-radio-group>
                    </div>
                <!-- <el-select v-model="comparisonValue" placeholder="选择报告期" style="margin-right: 12px;">
                    <el-option
                        v-for="item in options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                        </el-option>
                </el-select> -->
                <div style="margin-right: 12px;">
                        <el-radio-group class="radio-group-wrapper" v-model="pageStyle" size="small" style="margin-left: 0 !important;" @input="changePage">
                            <el-radio-button label="图表">图表</el-radio-button>
                            <el-radio-button label="数据">数据</el-radio-button>
                        </el-radio-group>
                    </div>
            </template>
        </combinationComponentHeader>
  
        <div style="margin-top: 20px;">
            <barChartForHoldStyle ref="fund-performance-board-chart-container3" v-show="pageStyle === '图表'"></barChartForHoldStyle>
            <tableForHoldStyle ref="fund-table" v-show="pageStyle === '数据'" @tableData="getTableData"></tableForHoldStyle>
        </div>
    </div>
</template>
<script>
import combinationComponentHeader from './combinationComponentHeader.vue';
import barChartForHoldStyle from '../chart/barChartForHoldStyle.vue';
import tableForHoldStyle from '../table/tableForHoldStyle.vue';
import { filter_to_excel } from "@/utils/exportExcel.js";
import { equityHoldStyle} from '@/api/pages/tkAnalysis/portfolio.js';
export default {
    name:'equityHoldStyle',
    components:{
        combinationComponentHeader,
        barChartForHoldStyle,
        tableForHoldStyle
    },
    data(){
        return {
            form:{
                holdFlag:'big10',
                penetrateFlag:true
            },
            comparisonValue:'',
            options:[{
                label:'行业口径',
                value: '行业口径'
            }],
            param:null,
            pageStyle:'图表',
            tableHeader:[],
            tableData:[]
        }
    },
    methods:{
        radioChange(){
            this.getData(this.param)
        },
        changePage(value){
            console.log("value::::",value);
            this.pageStyle = value;
        },
        getData(param){
            this.param = param;
            this.$nextTick(async ()=>{
                let chartDom3 = this.$refs['fund-performance-board-chart-container3'];
                chartDom3?.showLoading();
                let chartDom = this.$refs['fund-table'];
               
                chartDom?.showLoading();
                let res = await equityHoldStyle({
                    ...param,
                    ...this.form
                });
                
                if(res.mtycode != 200){
                    return;
                }
                console.log('res:::',res,res.mtycode != 200);

                if(res.data.length > 0){
                    chartDom3?.hideempty();
                    chartDom?.getData(res.data);
                    chartDom3?.getData(res.data);
                    return;
                }else{
                    chartDom3?.showempty();
                }


            })
           
        },
        getTableData(val){
            this.tableHeader = val.tableHeader;
            this.tableData = val.tableDataNow;
        },
        exportExcel(){
            let list = this.tableHeader.map((item) => {
				return {
					...item,
					format: ''
				};
			});
			filter_to_excel(list, this.tableData, '当前权益持仓风格');
        }
    },
}
</script>
<style lang="scss" scoped>
.fund-performance-board-wrapper {
    .select-form-wrapper {
        margin-bottom: 16px;
    }
    .content-table-wrapper {
        margin-bottom: 32px;
    }
}

</style>