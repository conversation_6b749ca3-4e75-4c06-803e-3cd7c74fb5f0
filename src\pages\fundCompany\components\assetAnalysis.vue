<template>
	<div>
		<div class="flex_card">
			<div v-for="item in templateList" :key="item.value" v-show="item.isshow" :class="item.type">
				<component :is="item.is" :ref="item.value" @resolveFather="item.methods" v-loading="loading"></component>
			</div>
		</div>
	</div>
</template>

<script>
// 持仓股票分析
import positionStockAnalysis from '@/components/components/components/positionStockAnalysis/index.vue';
// 行业配置表现
import industryAllocationPerformance from '@/components/components/components/industryAllocationPerformance/index.vue';
// 配置变化对比
import configurationChangeComparison from '@/components/components/components/configurationChangeComparison/index.vue';
// 持仓债券分析
import positionBondAnalysis from '@/components/components/components/positionBondAnalysis/index.vue';
// 信用分析
import creditAnalysis from '@/components/components/components/creditAnalysis/index.vue';

// 产品类型饼图
import fundPieChart from '@/components/components/components/fundPieChart/index.vue';
// 公司规模及人员变动图
import companySizeChange from '@/components/components/components/companySizeChange/index.vue';
// 存续期间变化
import durationChange from '@/components/components/components/durationChange/index.vue';
// 收益图
import incomeChart from '@/components/components/components/incomeChart/index.vue';
// 回撤图
import retracementChart from '@/components/components/components/retracementChart/index.vue';
// 高管变动
import managerChange from '@/components/components/components/managerChange/index.vue';

// 获取持仓数据更新时间
import { getDateList } from '@/api/pages/Analysis.js';
import fundAssetAllocation from '@/components/components/components/fundAssetAllocationAnalysis/index.vue';
import {
	getbondclassdetail,
	getIndexList,
	getIndexReturn,
	getIndustryInfoCompany,
	getHoldStockMsg,
	getStockBarraStyle,
	getHoldType,
	getIndustryChange,
	getBondAnalysis,
	getBondCreditAnalysis,
	getPositionStatistics,
	getFofAllocationDetails
} from '@/api/pages/SystemOther.js';

export default {
	components: {
		positionStockAnalysis,
		industryAllocationPerformance,
		configurationChangeComparison,
		positionBondAnalysis,
		creditAnalysis,
		fundPieChart,
		companySizeChange,
		durationChange,
		incomeChart,
		retracementChart,
		managerChange
	},
	data() {
		return {
			templateList: [],
			info: {},
			loading: true,
			requestOver: [],
			requestAll: [],
			typeList: [],
			dateList: [],
			industryInfo: null
		};
	},

	methods: {
		// 获取数据
		getData(data) {
			this.info = data;
			this.loading = true;
			this.requestOver = [];
			this.watch();
			this.formatTemplatList();
		},
		// 添加watch函数式监听(因为watch侦听器在页面切换时失效)
		watch() {
			let unwatch = this.$watch('requestOver', (val, old) => {
				this.loading = false;
				this.$compontentsWatch(val, this);
			});
		},
		// 格式化模板列表
		formatTemplatList() {
			this.requestAll = [];
			let requestList = [];
			this.templateList.map((item) => {
				if (item.methods && typeof item.methods == 'string') {
					item.methods = this?.[item.methods];
				}
				if (
					item.typelist.some((obj) => {
						return this.info.type.indexOf(obj) != -1;
					})
				) {
					this.requestAll.push(item);
					if (requestList.indexOf(item.getRequestData) == -1) {
						if (item.getRequestData && item.getRequestData !== 'None') {
							requestList.push(item.getRequestData);
							this?.[item.getRequestData]();
						}
					}
				}
			});
		},
		// 接收/返回组件列表
		getTemplateList(list) {
			if (list) {
				// 是光大
				if (this.isGDBank()) {
					this.templateList = list.filter((item) => {
						return item.isshow;
					});
				} else {
					// 不是光大
					this.templateList = list.filter((item) => {
						return item.is !== 'GDBankDetailequity' && item.is !== 'GDBankDetailbond' && item.isshow;
					});
				}
				this.$forceUpdate();
			} else {
				return this.templateList;
			}
		},
		// 判断是否是光大
		isGDBank() {
			if (window.localStorage.getItem('mty_modulesName') == 'GDBank') {
				this.showGD = true;
				return true;
			} else {
				this.showGD = false;
				return false;
			}
		},
		// 获取股票持仓分析
		async getHoldStockMsg(type) {
			let data = await getHoldStockMsg({ code: this.info.code, number: 10, type });
			if (data?.mtycode == 200) {
				this.$refs['positionStockAnalysis']?.[0]?.getData(data?.data);
			} else {
				this.$refs['positionStockAnalysis']?.[0]?.hideLoading();
			}
		},
		// 获取股票持仓分析类型数据
		getHoldStockMsgData() {
			this.$refs['positionStockAnalysis']?.[0]?.getTypeList(this.typeList);
		},
		// 获取类型列表
		async getTypeList() {
			if (this.getCacheData('typeList')) {
				this.typeList = this.getCacheData('typeList');
			} else {
				let data = await getHoldType({ code: this.info.code, noType: 'no' });
				if (data?.mtycode == 200) {
					this.typeList = this.formatTypeList(data?.data);

					this.setCacheData('typeList', this.typeList);
				}
			}
			this.requestOver.push('getTypeList');
		},
		// 格式化类型列表
		formatTypeList(data) {
			return data
				.filter((item) => {
					return (
						item.type == 'equity' ||
						item.type == 'equityhk' ||
						item.type == 'bond' ||
						// item.type == 'cbond' ||
						item.type == 'purebond' ||
						// item.type == 'bill' ||
						item.type == 'money'
					);
				})
				.map((item) => {
					let sort = null;
					switch (item.type) {
						case 'equity':
							sort = 0;
							break;
						case 'equityhk':
							sort = 1;
							break;
						case 'bond':
							sort = 2;
							break;
						// case 'cbond':
						// 	sort = 3;
						// 	break;
						case 'purebond':
							sort = 4;
							break;
						// case 'bill':
						// 	sort = 5;
						// 	break;
						case 'money':
							sort = 6;
							break;
					}
					return { type: item.type, name: this.FUNC.textConverter(this.COMMON.fundType_zh_en, item.type, 'en', 'zh'), sort };
				})
				.sort((a, b) => {
					return a.sort - b.sort;
				});
		},
		// 获取行业配置表现数据
		async getIndustryInfo() {
			// 行业配置表现
			let industryInfoData = {
				flag: this.info.flag,
				code: this.info.code,
				type: 'equity',
				industry_section: '申万(2021)'
			};
			if (this.getCacheData('industryInfo')) {
				this.industryInfo = this.getCacheData('industryInfo');
			} else {
				let data = await getIndustryInfoCompany(industryInfoData);
				if (data?.mtycode == 200 && data?.data?.length) {
					this.industryInfo = data?.data;
					this.setCacheData('industryInfo', this.industryInfo);
				} else {
					this.$message.warning('行业配置表现' + (data?.mtymessage || '暂无数据'));
				}
			}
			this.requestOver.push('getIndustryInfo');
		},
		// 获取行业配置
		getIndustryInfoData() {
			let industryInfoData = {
				flag: this.info.flag,
				code: this.info.code,
				type: 'equity',
				industry_section: '申万(2021)'
			};
			if (this.industryInfo) {
				this.$refs['industryAllocationPerformance']?.[0].getData(this.formatIndustryInfoData(this.industryInfo), industryInfoData);
			} else {
				this.$refs['industryAllocationPerformance']?.[0].hideLoading();
			}
		},
		// 格式化行业图数据
		formatIndustryInfoData(data) {
			data.forEach((item) => {
				item.weight = item.weight;
			});
			return data;
		},
		// 获取持仓数据更新时间
		async getDateList() {
			if (this.getCacheData('dateList')) {
				this.dateList = this.getCacheData('dateList');
			} else {
				let data = await getDateList({ code: this.info.code, type: 'equity', flag: this.info.flag });
				if (data?.mtycode == 200) {
					this.dateList = this.formatDateList(data?.data);
					this.setCacheData('dateList', this.dateList);
				}
			}
			this.requestOver.push('getDateList');
		},
		// 传递持仓数据
		getIndustryChangeData() {
			this.$refs['configurationChangeComparison']?.[0]?.getDateList(this.dateList);
		},
		// 获取配置变化比例数据
		async getIndustryChange(val) {
			let data = await getIndustryChange({ ...val, code: this.info.code });
			if (data?.mtycode == 200) {
				this.$refs['configurationChangeComparison']?.[0]?.getData(data?.data);
			} else {
				this.$refs['configurationChangeComparison']?.[0]?.hideLoading();
			}
		},
		// 获取持仓债券分析数据
		async getPositionBondAnalysis(type) {
			let data = await getBondAnalysis({ code: this.info.code, type });
			if (data?.mtycode == 200) {
				this.$refs['positionBondAnalysis']?.[0]?.getData();
			} else {
				this.$refs['positionBondAnalysis']?.[0]?.hideLoading();
			}
			// this.requestOver.push('getPositionBondAnalysisData');
		},
		// 获取持仓债券类型列表
		getPositionBondAnalysisData() {
			this.$refs['positionBondAnalysis']?.[0]?.getTypeList(this.typeList);
		},
		// 获取持仓债券类型列表
		getCreditAnalysisData() {
			this.$refs['creditAnalysis']?.[0]?.getTypeList(this.typeList);
		},
		// 获取信用分析数据
		async getCreditAnalysis(type) {
			let data = await getBondCreditAnalysis({ code: this.info.code, type });
			console.log(data);
			if (data?.mtycode == 200) {
				this.$refs['creditAnalysis']?.[0]?.getData(data?.data);
			} else {
				this.$refs['creditAnalysis']?.[0]?.hideLoading();
			}
		},
		// 获取产品类型饼图
		getFundPieChart() {
			this.requestOver.push('getFundPieChart');
		},
		// 获取产品类型饼图数据
		getFundPieChartData() {
			this.$refs['fundPieChart']?.[0]?.getData();
		},
		// 获取公司规模及人员变动图
		getCompanySizeChange() {
			this.requestOver.push('getCompanySizeChange');
		},
		// 获取公司人员变动图数据
		getCompanySizeChangeData() {
			this.$refs['companySizeChange']?.[0]?.getData();
		},
		// 获取存续期间变化
		getDurationChange() {
			this.requestOver.push('getDurationChange');
		},
		// 获取存续期间变化数据
		getDurationChangeData() {
			this.$refs['durationChange']?.[0]?.getData();
		},
		// 获取收益图
		getIncomeChart() {
			this.requestOver.push('getIncomeChart');
		},
		// 获取收益图数据
		getIncomeChartData() {
			this.$refs['incomeChart']?.[0]?.getData();
		},
		// 获取回撤图
		getRetracementChart() {
			this.requestOver.push('getRetracementChart');
		},
		// 获取回撤图数据
		getRetracementChartData() {
			this.$refs['retracementChart']?.[0]?.getData();
		},
		// 获取高管变动
		getManagerChange() {
			this.requestOver.push('getManagerChange');
		},
		// 获取高管变动数据
		getManagerChangeData() {
			this.$refs['managerChange']?.[0]?.getData();
		},

		addType() {
			let equityType = [];
			let bonftype = [];
			for (let i = 0; i < this.typeList.length; i++) {
				if (this.typeList[i].type.indexOf('equity') >= 0) {
					equityType.push(this.typeList[i]);
				}
				if (this.typeList[i].type.indexOf('bond') >= 0 || this.typeList[i].type.indexOf('bill') >= 0) {
					bonftype.push(this.typeList[i]);
				}
			}
			this.fundTypeList_1 = equityType;
			this.fundTypeList_2 = bonftype;
			if (this.fundTypeList_1.length > 0) {
				this.fundType_1 = this.fundTypeList_1[0].type;
				this.fundType_2 = this.fundTypeList_1[0].type;
			}
			if (this.fundTypeList_2.length > 0) {
				this.fundType_3 = this.fundTypeList_2[0].type;
				this.fundType_4 = this.fundTypeList_2[0].type;
				this.fundType_5 = this.fundTypeList_2[0].type;
			}
		},
		// 大类资产配置列表
		async getIndexList() {
			this.analysisIndexData = {
				type: this.fundType_5,
				index_codes: [],
				start_date: '',
				end_date: ''
			};
			let indexList = null;
			let postData = { type: this.fundType_5 };
			let data = await getIndexList(postData);
			indexList = data;
			this.indexList = indexList;
			this.$refs['fundAssetAllocationAnalysis']?.getIndexList(this.indexList);
		},
		// 获取子组件传递数据
		getAnalysisIndexCode(data) {
			this.analysisIndexData = { ...this.analysisIndexData, ...data };
			this.getbondclassdetail();
		},
		// 获取报告期持仓统计指数收益
		async getIndexReturn(tradstyleData, temp, postData) {
			let { data, mtycode, mtymessage } = await getIndexReturn(postData);
			if (mtycode == 200) {
				this.$refs[temp]?.getDataCompany(tradstyleData, data);
			} else {
				this.$message.warning('报告期持仓统计指数收益' + (mtymessage || '暂无数据'));
			}
		},
		// 大类资产配置
		async getbondclassdetail() {
			let { data, mtycode, mtymessage } = await getbondclassdetail({ code: this.code, type: this.fundType_5 });
			if (mtycode == 200) {
				this.analysisIndexData.start_date = this.FUNC.earlyAndLateDate(data.yearqtr).earlyDate;
				this.analysisIndexData.end_date = this.FUNC.earlyAndLateDate(data.yearqtr).lateDate;
				this.getIndexReturn(data, 'fundAssetAllocationAnalysis', this.analysisIndexData);
			} else {
				this.$message.warning('基金资产配置分析' + (mtymessage || '暂无数据'));
				this.$refs['fundAssetAllocationAnalysis']?.hideLoading();
			}
		},
		// 获取数据类型
		// getTypeList() {
		// 	axios.get(this.$baseUrl + '/Company/HoldType/?code=' + this.code + '&noType=no').then((res) => {
		// 		if (res.status == 200) {
		// 			let arr = [];
		// 			this.typeList = res.data.data
		// 				.filter((item) => {
		// 					return (
		// 						item.type == 'equity' ||
		// 						item.type == 'equityhk' ||
		// 						item.type == 'bond' ||
		// 						// item.type == 'cbond' ||
		// 						item.type == 'purebond' ||
		// 						// item.type == 'bill' ||
		// 						item.type == 'money'
		// 					);
		// 				})
		// 				.map((item) => {
		// 					let sort = null;
		// 					switch (item.type) {
		// 						case 'equity':
		// 							sort = 0;
		// 							break;
		// 						case 'equityhk':
		// 							sort = 1;
		// 							break;
		// 						case 'bond':
		// 							sort = 2;
		// 							break;
		// 						// case 'cbond':
		// 						// 	sort = 3;
		// 						// 	break;
		// 						case 'purebond':
		// 							sort = 4;
		// 							break;
		// 						// case 'bill':
		// 						// 	sort = 5;
		// 						// 	break;
		// 						case 'money':
		// 							sort = 6;
		// 							break;
		// 					}
		// 					return { type: item.type, name: this.FUNC.textConverter(this.COMMON.fundType_zh_en, item.type, 'en', 'zh'), sort };
		// 				})
		// 				.sort((a, b) => {
		// 					return a.sort - b.sort;
		// 				});
		// 			this.getData();
		// 		}
		// 	});
		// },

		// 获取持仓数据更新时间
		// async getDateList() {
		// 	let data = await getDateList({ code: this.info.code, type: this.info.type, flag: this.info.flag });
		// 	if (data?.mtycode == 200) {
		// 		this.formatDateList(data?.data);
		// 	} else {
		// 		this.generateQuarterList();
		// 	}
		// 	this.getIndustryChange();
		// },
		formatDateList(data) {
			let date = [];
			date = data.map((item) => {
				return {
					year: item.split(' ')?.[0],
					quarter: item.split(' ')?.[1]
				};
			});
			let dateObj = [];
			date.map((item) => {
				let index = dateObj.findIndex((obj) => {
					return obj.year == item.year;
				});
				if (index == -1) {
					dateObj.push({
						year: item.year,
						quarter: [item.quarter]
					});
				} else {
					dateObj[index].quarter.push(item.quarter);
				}
			});
			let option = [];
			dateObj.map((item) => {
				option.push({
					label: item.year,
					value: item.year,
					children: item.quarter.map((qtr) => {
						return {
							label: qtr,
							value: qtr
						};
					})
				});
			});
			let quarterList = option;
			let targetQuarter = [option[option.length - 3].value, option[option.length - 3].children[0].value];
			let contrastQuarter = [
				option[option.length - 1].value,
				option[option.length - 1].children[option[option.length - 1].children.length - 1].value
			];
			return { quarterList, targetQuarter, contrastQuarter };
		},
		// 返回今天和三年前时间
		returnDay() {
			let threeYear = new Date(new Date().getTime() - 1000 * 3600 * 24 * 365 * 3).getFullYear();
			let threeMonth = new Date(new Date().getTime() - 1000 * 3600 * 24 * 365 * 3).getMonth() + 1;
			threeMonth = threeMonth > 10 ? threeMonth : '0' + threeMonth;
			let threeDay = new Date(new Date().getTime() - 1000 * 3600 * 24 * 365 * 3).getDate();
			threeDay = threeDay > 10 ? threeDay : '0' + threeDay;
			let threeDate = threeYear + '-' + threeMonth + '-' + threeDay;
			let nowYear = new Date().getFullYear();
			let nowMonth = new Date().getMonth() + 1;
			nowMonth = nowMonth > 10 ? nowMonth : '0' + nowMonth;
			let nowDay = new Date().getDate();
			nowDay = nowDay > 10 ? nowDay : '0' + nowDay;
			let nowDate = nowYear + '-' + nowMonth + '-' + nowDay;
			return [threeDate, nowDate];
		},
		isMock() {
			this.companyCreateDate = '2006-09-10';
			// ...
			// this.fundAnalysisTable.push(
			// 	{
			// 		id: 1,
			// 		name: '易方达test股票',
			// 		code: '110110',
			// 		holdFundNumber: 100,
			// 		proportion: '10%',
			// 		holdSharesNumber: 100,
			// 		marketValue: 10042.0,
			// 		holdFundList: ['基金1', '基金2', '基金3']
			// 	},
			// 	{
			// 		id: 2,
			// 		name: '易方达aaa股票',
			// 		code: '110122',
			// 		holdFundNumber: 100,
			// 		proportion: '10%',
			// 		holdSharesNumber: 100,
			// 		marketValue: 10042.0,
			// 		holdFundList: ['基金1', '基金2', '基金3']
			// 	}
			// );
			// ...
			let obj1 = { name: '汇添富', type: '医药生物', amount: '+10%' };
			let obj2 = { name: '汇添富', type: '医药生物', amount: '+10%' };
			// this.ownIndustryChangeData.push(obj1, obj2);
			// this.ownSharesChangeData.push(obj1, obj2);
			// this.otherIndustryChangeData.push(obj1, obj2);
			// this.otherSharesChangeData.push(obj1, obj2);
			// ...
		},
		// 获取股票前十大table数据
		getTopTenData() {
			let params = {
				code: this.code,
				number: 10,
				type: this.fundType_1
			};
			let url = this.$baseUrl + '/Company/HoldStockMsg/?' + this.FUNC.paramsToString(params);
			this.topTenLoading = true;
			this.barraLoading = true;
			this.getIndustryInfo();
			axios
				.get(url)
				.then((res) => {
					let list = res.data.data;
					this.fundAnalysisTable = list
						.map((item) => {
							let val = (item.value / 10 ** 4 / (item.liqmv / 10 ** 8)) * 100;
							return {
								...item,
								NumberFundHold: item['0'].length,
								marketValueRatio: val == 'NaN' ? '--' : val.toFixed(2),
								rankStockHold:
									String(Number(item.value) / Number(item.totalmv)) == 'NaN'
										? '--'
										: ((Number(item.value) / Number(item.totalmv)) * 100).toFixed(2)
							};
						})
						.sort((a, b) => {
							return b.fund_list_number - a.fund_list_number;
						});
					this.oldTable = this.fundAnalysisTable;
					this.topTenLoading = false;
					this.currentStockCode = this.fundAnalysisTable[0].stock_code;
					this.getBarraChart();
				})
				.catch((err) => {
					this.topTenLoading = false;
					this.barraLoading = false;
				});
		},
		// 点击股票前十大table单行显示对应barra
		clickTopTenLine(row, column, event) {
			this.currentStockCode = row.stock_code;
			this.getBarraChart();
		},
		// 为表格行添加class
		tableRowClassName({ row, rowIndex }) {
			// currentStockCode
			if (row.stock_code == this.currentStockCode) {
				return 'table-even-line';
			}
		},
		// 股票Barra分析chart
		getBarraChart() {
			let url = this.$baseUrl + '/Company/StockBarraStyle/?stock_code=' + this.currentStockCode;
			axios
				.get(url)
				.then((res) => {
					if (res.status == 200) {
						if (
							this.FUNC.isValidObj(res.data.data) &&
							this.FUNC.isValidObj(res.data.data[0]) &&
							this.FUNC.isValidObj(res.data.data[1]) &&
							this.FUNC.isValidObj(res.data.data[0].name) &&
							this.FUNC.isValidObj(res.data.data[1].value)
						) {
							let label_list = res.data.data[0].name.slice();
							let data_list = res.data.data[1].value.slice();
							let labelList = [],
								dataList = [];
							label_list.forEach((item, index) => {
								if (this.COMMON.barra_zh_en.en.includes(item)) {
									let zh_index = this.COMMON.barra_zh_en.en.indexOf(item);
									labelList.push(this.COMMON.barra_zh_en.zh[zh_index]);
									dataList.push(data_list[index]);
								}
							});
							let dataMin = Math.min(...dataList);
							let dataMax = Math.max(...dataList);

							let option = {
								tooltip: {
									textStyle: {
										fontSize: '14px'
									},
									trigger: 'axis',
									axisPointer: {
										// 坐标轴指示器，坐标轴触发有效
										type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
									},
									formatter(params) {
										return `${params[0].marker} ${params[0].name}<br>${params[0].seriesName}: ${params[0].value?.toFixed(4)}`;
									}
								},
								grid: {
									left: 0,
									top: 0,
									right: 0,
									bottom: 0
								},
								visualMap: {
									show: false,
									orient: 'horizontal',
									left: 'center',
									min: dataMin,
									max: dataMax,
									text: ['High Score', 'Low Score'],
									// Map the score column to color
									dimension: 0,
									inRange: {
										color: ['#0BBF9B', '#EA5454']
									}
								},
								xAxis: {
									type: 'value',
									position: 'top',
									splitLine: {
										lineStyle: {
											type: 'dashed'
										}
									},
									axisLabel: { fontSize: fontSize(14) }
								},
								yAxis: {
									type: 'category',
									axisLine: { show: false },
									axisLabel: { show: false, fontSize: fontSize(14) },
									axisTick: { show: false },
									splitLine: { show: false },
									data: labelList
								},
								series: [
									{
										name: '因子暴露',
										type: 'bar',
										stack: '总量',
										label: {
											show: true,
											formatter: '{b}',
											fontSize: fontSize(14)
										},
										data: dataList
									}
								]
							};
							this.barraOption = option;
							this.barraChartLoading = false;
						} else {
							this.barraChartLoading = true;
						}
					}
					this.barraLoading = false;
				})
				.catch((err) => {
					console.error('error: ', err);
					this.barraLoading = false;
					this.barraChartLoading = true;
				});
		},
		// 配置变化对比
		// getIndustryChange() {
		// 	console.log(this.contrastQuarter, this.targetQuarter);
		// 	this.compareQuarterLoading = true;
		// 	let url = this.$baseUrl + '/Company/IndustryChange/';
		// 	let params = {
		// 		code: this.code,
		// 		flag: this.contrastType[0],
		// 		now_yearqtr: this.contrastQuarter.join(' '),
		// 		target_yearqtr: this.targetQuarter.join(' '),
		// 		type: this.fundType_2
		// 	};
		// 	axios
		// 		.get(url, { params })
		// 		.then((res) => {
		// 			if (res.data.mtycode == 200) {
		// 				if (this.contrastType[0] == 'industry') {
		// 					this.compareQuarterData = res.data.data;
		// 				} else {
		// 					this.compareQuarterData = res.data.data;
		// 				}
		// 				// let arr = []
		// 				// res.data.data.map(item=>{
		// 				//     if (arr.findIndex(a=>{return a.})) {

		// 				//     }
		// 				// })
		// 				this.compareQuarterLoading = false;
		// 			} else {
		// 				console.error('error: ', res);
		// 				this.compareQuarterData = [];
		// 				this.compareQuarterLoading = false;
		// 			}
		// 		})
		// 		.catch((err) => {
		// 			console.warn('error: ', err);
		// 			this.compareQuarterData = [];
		// 			this.compareQuarterLoading = false;
		// 		});
		// },
		// 获取债券前十大table
		getBondTopTenData() {
			this.bondTopTenLoading = true;
			axios
				.get(this.$baseUrl + '/Company/BondAnalysis/?code=' + this.code + '&type=' + this.fundType_3)
				.then((res) => {
					if (res.status == 200 && res.data.mtycode == 200) {
						this.bondTopTenData = res.data.data.sort((a, b) => Number(b.value) - Number(a.value));
					} else {
						console.error('error: ', res);
					}
					this.bondTopTenLoading = false;
				})
				.catch((err) => {
					this.bondTopTenLoading = false;
					console.error('error: ', err);
				});
		},
		// getBondTypeData() {
		// 	// TODO:
		// 	axios.get(this.$baseUrl + '/Company/BondTypeAnalysis/?code=' + this.code).then(res => {
		// 		let resData = JSON.parse(res.data.replace(/NaN/g, 'null'));
		// 		if (res.status == 200 && resData.mtycode == 200) {
		// 			//console.log('resData: ', resData);
		// 		}
		// 	});
		// },

		// 信用下沉分析
		getBondCreditData() {
			this.loadingcredit = true;
			axios
				.get(this.$baseUrl + '/Company/BondCreditAnalysis/?code=' + this.code + '&type=' + this.fundType_4)
				.then((res) => {
					this.loadingcredit = false;

					if (res.status == 200 && res.data.mtycode == 200) {
						let resData = res.data.data;
						if (
							!this.FUNC.isValidObj(resData.yearqtr) ||
							(!this.FUNC.isValidObj(resData.downratioinC) && !this.FUNC.isValidObj(resData.downratioinN))
						) {
							this.showBondCredit = false;
						} else {
							this.bondCreditOption = {
								color: ['#4096FF', '#4096ff'],
								// title: {
								// 	textStyle: {
								// 		'font-family': 'PingFang',
								// 		'font-style': 'normal',
								// 		'font-weight': 400,
								// 		'font-size': '14px',
								// 		'line-height': '22px',
								// 		color: ' rgba(0, 0, 0, 0.65)'
								// 	},
								// 	text: '基金债券信用挖掘比例估计'
								// },
								legend: {},
								grid: {
									left: '40px',
									top: '24px',
									right: 0,
									bottom: '24px'
								},
								tooltip: {
									textStyle: {
										fontSize: fontSize(14)
									},
									trigger: 'axis',
									axisPointer: {
										// 坐标轴指示器，坐标轴触发有效
										type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
									},
									formatter: function (obj) {
										var value = obj[0].axisValue + `<br />`;
										for (let i = 0; i < obj.length; i++) {
											value +=
												`<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:` +
												obj[i].color +
												`;"></span>` +
												obj[i].seriesName +
												':' +
												(Number(obj[i].data) * 100).toFixed(2) +
												'%' +
												`<br />`;
										}
										return value;
									}
								},
								xAxis: [
									{
										nameTextStyle: {
											fontSize: fontSize(14)
										},
										axisLabel: {
											show: true,
											textStyle: {
												fontSize: fontSize(14)
											}
										},
										type: 'category',
										data: resData.yearqtr,
										axisTick: {
											alignWithLabel: true
										}
									}
								],
								yAxis: [
									{
										axisLine: {
											show: false
										},
										axisTick: {
											show: false
										},
										splitLine: {
											show: true,
											lineStyle: {
												type: 'dashed'
											}
										},
										nameTextStyle: {
											fontSize: fontSize(14)
										},
										axisLabel: {
											show: true,
											textStyle: {
												fontSize: fontSize(14)
											},
											formatter: function (obj) {
												//   //console.log(obj)
												// var value = obj.value;
												return (obj * 100).toFixed(0) + '%';
											}
										},
										type: 'value'
									}
								],
								series: [
									{
										name: '下沉占信用债比例',
										type: 'line',
										symbol: 'none',
										data: resData.downratioinC
									},
									{
										name: '下沉占净值比',
										type: 'line',

										symbol: 'none',
										data: resData.downratioinN
									}
								]
							};
							this.showBondCredit = true;
						}
					} else {
						this.showBondCredit = false;
					}
				})
				.catch((err) => {
					this.loadingcredit = false;
					this.showBondCredit = false;
				});
		},
		// 久期分析
		getBondDurationData() {
			let start_date = this.bondDurationDate && this.bondDurationDate.length ? this.bondDurationDate[0] : '';
			let end_date = this.bondDurationDate && this.bondDurationDate.length ? this.bondDurationDate[1] : '';
			axios
				.get(
					this.$baseUrl +
						'/Company/BondDurationAnalysis/?code=' +
						this.code +
						'&method=' +
						this.bondDurationTypeValue +
						'&start_date=' +
						start_date +
						'&end_date=' +
						end_date
				)
				.then((res) => {
					if (res.status == 200 && res.data.mtycode == 200) {
						let resData = res.data.data;
						this.formatDurationData(resData);
					}
				});
		},
		// 在给定时间区间内生成连续季度
		generateQuarterList() {
			let option = [];
			let qList = ['Q1', 'Q2', 'Q3', 'Q4'];
			let pre = this.companyCreateDate;
			let now = this.FUNC.transformDate(new Date());

			let preYear = pre.slice(0, 4);
			let nowYear = now.slice(0, 4);
			let preQ = this.FUNC.dateToQuarter(pre).slice(5);
			let nowQ = this.FUNC.dateToQuarter(now).slice(5);

			let yList = Array.from({ length: Math.abs(nowYear - preYear + 1) }, (item, index) => (item = parseInt(preYear) + index));

			for (let y of yList) {
				let yobj = {
					value: y,
					label: y,
					children: []
				};
				if (y == preYear) {
					qList.forEach((q) => {
						if (q >= preQ) {
							yobj.children.push({ value: q, label: q });
						}
					});
				} else if (y == nowYear) {
					qList.forEach((q) => {
						if (q <= nowQ) {
							yobj.children.push({ value: q, label: q });
						}
					});
				} else {
					qList.forEach((q) => yobj.children.push({ value: q, label: q }));
				}
				option.push(yobj);
			}
			this.quarterList = option;
			if (option[option.length - 1].children.length == 1) {
				this.targetQuarter = [option[option.length - 3].value, option[option.length - 3].children[0].value];
				this.contrastQuarter = [
					option[option.length - 2].value,
					option[option.length - 2].children[option[option.length - 2].children.length - 1].value
				];
			} else {
				this.targetQuarter = [option[option.length - 2].value, option[option.length - 2].children[0].value];
				this.contrastQuarter = [
					option[option.length - 1].value,
					option[option.length - 1].children[option[option.length - 1].children.length - 2].value
				];
			}
			// this.contrastQuarter = [option[option.length - 1].value, option[option.length - 1].children[0].value];
			// this.targetQuarter = [option[option.length - 1].value, option[option.length - 1].children[0].value];
			// option示例格式:
			// option = [{
			//   label: 'label',
			//   value: 'value',
			//   children: [{
			//     label: 'label',
			//     value: 'value'
			//   }]
			// }];
		},
		// 格式化久期分析数据
		formatDurationData(data) {
			let arr = [];
			let senKeyLink = [];
			data.map((obj) => {
				obj.map((item, i) => {
					item.label =
						item.yearqtr +
						(item.duration > 0
							? item.duration > 2.5
								? item.duration > 3.5
									? item.duration > 4.5
										? item.duration > 5.5
											? '长期'
											: '中长期'
										: '中期'
									: '中短期'
								: '短期'
							: '');
				});
				arr.push(...obj);
			});
			data.map((obj) => {
				// 绑定走势关系
				obj.map((item, i) => {
					if (i < obj.length - 1) {
						if (item.label !== obj[i + 1].label && item.label.slice(0, 7) !== obj[i + 1].label.slice(0, 7)) {
							senKeyLink.push({ source: item.label, target: obj[i + 1].label, value: obj[i + 1].duration });
						}
					}
				});
			});
			// let senKeyData = Array.from(
			// 	new Set(
			// 		arr.map(item => {
			// 			return item.label;
			// 		})
			// 	)
			// ).sort((a, b) => {
			// 	return a.slice(0, 4) - b.slice(0, 4);
			// });
			let { senKeyData } = this.getDurationYearQtr();
			let { allItem } = this.getDurationYearQtr();
			senKeyData = senKeyData
				.map((item) => {
					// item.label.indexOf('短期') !== -1 && item.label.indexOf('中短期') == -1
					let itemStyle = {};
					// 短期
					if (item.indexOf('短期') !== -1 && item.indexOf('中短期') == -1) {
						itemStyle = { color: '#f18bbf', borderColor: '#f18bbf' };
					} else if (item.indexOf('中短期') !== -1) {
						itemStyle = { color: '#0078D7', borderColor: '#0078D7' };
					} else if (item.indexOf('中期') !== -1) {
						itemStyle = { color: '#3891A7', borderColor: '#3891A7' };
					} else if (item.indexOf('中长期') !== -1) {
						itemStyle = { color: '#0037DA', borderColor: '#0037DA' };
					} else {
						itemStyle = { color: '#C0BEAF', borderColor: '#C0BEAF' };
					}
					return {
						name: item,
						itemStyle
					};
				})
				.sort((a, b) => {
					return a.name.slice(0, 7) - b.name.slice(0, 7);
				});
			this.bondDurationOption = {
				series: {
					type: 'sankey',
					layout: 'none',
					emphasis: {
						show: '',
						focus: 'adjacency'
					},
					label: {
						fontSize: fontSize(14)
					},
					data: senKeyData,
					links: [...senKeyLink, ...allItem]
				}
			};
		},
		// 获取不同久期计算方法中的连续计算季度
		getDurationYearQtr() {
			// {
			// 		value: 'ir-riskexposure',
			// 		label: '利率风险' 2
			// 	},
			// 	{
			// 		value: 'sharpe-regression',
			// 		label: '持仓估计' 1
			// 	},
			// 	{
			// 		value: 'duration-regression',
			// 		label: '回归久期估算法' 1
			// 	},
			// 	{
			// 		value: 'ir-riskanalysis',
			// 		label: '利率灵敏度报告法' 2
			// 	}
			let yearList = [];
			let allItem = [];
			let senKeyData = [];
			switch (this.bondDurationTypeValue) {
				// this.bondDurationDate 时间区间
				case 'ir-riskexposure':
					yearList = this.getDurationYearQtrList(2);
					break;
				case 'sharpe-regression':
					yearList = this.getDurationYearQtrList(1);
					break;
				case 'duration-regression':
					yearList = this.getDurationYearQtrList(1);
					break;
				case 'ir-riskanalysis':
					yearList = this.getDurationYearQtrList(2);
					break;
			}
			yearList.map((item, i) => {
				if (i < yearList.length - 1) {
					allItem.push({ source: item + '短期', target: yearList[i + 1] + '短期', value: 0 });
					allItem.push({ source: item + '中短期', target: yearList[i + 1] + '中短期', value: 0 });
					allItem.push({ source: item + '中期', target: yearList[i + 1] + '中期', value: 0 });
					allItem.push({ source: item + '中长期', target: yearList[i + 1] + '中长期', value: 0 });
					allItem.push({ source: item + '长期', target: yearList[i + 1] + '长期', value: 0 });
				}
			});
			yearList.map((item) => {
				senKeyData.push(item + '短期');
				senKeyData.push(item + '中短期');
				senKeyData.push(item + '中期');
				senKeyData.push(item + '中长期');
				senKeyData.push(item + '长期');
			});
			return { allItem, senKeyData };
		},
		// 获取不同久期计算方法中的连续计算季度列表
		getDurationYearQtrList(type) {
			let m0 = Math.ceil((new Date(this.bondDurationDate[0]).getMonth() + 1) / 4) * 1 + 1;
			let m1 = Math.ceil((new Date(this.bondDurationDate[1]).getMonth() + 1) / 4) * 1 + 1;
			let start = new Date(this.bondDurationDate[0]).getFullYear() + ' Q' + (type == 1 ? m0 : m0 % 2 !== 0 ? (m0 == 3 ? 4 : 2) : m0);
			let end = new Date(this.bondDurationDate[1]).getFullYear() + ' Q' + (type == 1 ? m1 : m1 % 2 !== 0 ? (m1 == 3 ? 4 : 2) : m1);
			let allYear = [];
			for (let index = start.slice(0, 4); index <= end.slice(0, 4); index++) {
				allYear.push(index + ' Q1');
				allYear.push(index + ' Q2');
				allYear.push(index + ' Q3');
				allYear.push(index + ' Q4');
			}
			allYear = allYear.slice(
				allYear.findIndex(
					(year) => {
						return year == start;
					},
					allYear.findIndex((year) => {
						return year == end;
					})
				)
			);
			if (type == 2) {
				allYear = allYear.filter((item) => {
					return item.slice(-1) % 2 == 0;
				});
			}
			return allYear;
		},
		// 点击筛选
		sortChange({ prop, order }) {
			let valueArr = [];
			let strArr = [];
			this.fundAnalysisTable.map((obj) => {
				if (obj[prop] == '--' || obj[prop] == '——' || obj[prop] == 'NaN') {
					strArr.push(obj);
				} else if (obj['marketValueRatio'] == '--' || obj['marketValueRatio'] == '——' || obj['marketValueRatio'] == 'NaN') {
					strArr.push(obj);
				} else {
					valueArr.push(obj);
				}
			});
			valueArr.sort((a, b) => {
				if (prop) {
					// 从大到小排序
					if (order == 'descending') {
						return b[prop] - a[prop];
					} else if (order == 'ascending') {
						return a[prop] - b[prop];
					}
				} else {
					// 从大到小排序
					if (order == 'descending') {
						return b['marketValueRatio'] - a['marketValueRatio'];
					} else if (order == 'ascending') {
						return a['marketValueRatio'] - b['marketValueRatio'];
					}
				}
			});
			strArr.length ? valueArr.push(...strArr) : '';
			this.fundAnalysisTable = order ? valueArr : this.oldTable;
			// this.handlePageChange();
		}
	}
};
</script>

<style scoped lang="scss">
.boxminTitleBoxCompany {
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.header-title {
	font-size: 16px;
}

.card-title-icon {
	display: inline-block;
	margin: 0 10px 0 5px;
	height: 6px;
	width: 6px;
	background: #40AFFF;
	vertical-align: middle;
}
.card-title {
	height: 50px;
	line-height: 50px;
	font-size: 16px;
	font-weight: 400;
	color: #333333;
}
.absolute-title {
	position: absolute;
	left: 0;
	z-index: 10;
}

.card-box {
	margin-bottom: 30px;
	.select-list {
		.select-label {
			margin-left: 16px;
			font-size: 14px;
		}
	}
	.config-change-table-list {
		margin-top: 16px;
		.table-box {
			border-radius: 5px;
			border: 1px solid #e9e9e9;
			border-bottom: none;
		}
	}
}
.flex {
	width: 100%;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
// 与产品沟通后决定不采用UI稿样式，采用全局普通样式
// 为防止决策变动，UI稿通用样式放在main.css文件common-table-card相关样式中
// .card-box {
// 	border: 1px solid #e9e9e9;
// 	border-radius: 5px;
// 	.table-title-icon {
// 		display: inline-block;
// 		margin: 0 10px 0 5px;
// 		height: 6px;
// 		width: 6px;
// 		background: #40AFFF;
// 		vertical-align: middle;
// 	}
// 	.table-title {
// 		height: 50px;
// 		line-height: 50px;
// 		font-size: 16px;
// 		font-weight: 400;
// 		color: #333333;
// 	}
// }
// .fund-analysis-table {
// 	::v-deep
.el-table .table-even-line {
	background: black;
}
// 	.amount-font-color {
// 		color: #0bbf9b;
// 	}
// }
.fund-analysis-table {
	::v-deep .el-table__row {
		cursor: pointer;
	}
}
.column_main {
	width: 100%;
	margin: 0 auto;
	position: relative;
	height: 40px;
	display: flex;
	align-items: center;
	justify-content: flex-end;
}
.column_bg {
	background: rgba(255, 0, 0, 0.1);
	width: 100%;
	height: 40px;
	position: absolute;
	top: 0;
	left: 0;
}
.active_bg {
	background: rgba(255, 0, 0, 0.5);
	height: 40px;
	position: absolute;
	top: 0;
	left: 0;
}
</style>
