.box_Board {
  padding: 0 24px 16px 24px;
  overflow: auto;
  // 面包屑
  .header_box {
    margin-top: 16px;
    margin-bottom: 16px;
    font-size: 14px;

    .header_inactive {
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
      text-align: left;
      color: rgba(0, 0, 0, 0.45);
    }
  }

  // 表格区域
  .border_table {
    padding: 16px 24px;
    background: white;
    // 表格区域头部
    .border_table_header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      .border_table_header_title {
        color: rgba(0, 0, 0, 0.85);
        text-align: center;
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 24px;
      }

    }

    // 分页
    .pagination_board {
      text-align: right;
      margin-top: 16px;
    }
  }


}

.button-color {
  color: #4096ff
}

.flex {
  display: flex;
}

.item-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

//表格的表头背景色
::v-deep .el-table__header .el-table__cell {
  // main.css里有样式
}

//斑马纹颜色
::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {

}

.download {
  cursor:pointer;
}