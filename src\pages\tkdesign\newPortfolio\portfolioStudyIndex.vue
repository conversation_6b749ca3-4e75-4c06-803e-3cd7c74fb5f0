<!--  -->
<template>
	<div class="portfolioSelf">

		<FTB ref="ftb" title="组合策略研究" :tableHeader="tableHeader" :buttonList="buttonList" :pagekey="'combinPloy'" :pageSize="pageSize"></FTB>
	</div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import FTB from './components/firstPageTable.vue';
export default {
	//import引入的组件需要注入到对象中才能使用
	components: { FTB },
	data() {
		//这里存放数据
		return {
			tableHeader: [
				{
					prop: 'name',
					label: '策略名称',
					showTip:true
				},
				{
					prop: 'describe',
					label: '策略描述',
					showTip:true
				},
				{
					prop: 'ispublic',
					label: '是否公共',
				},
				{
					prop: 'createBy',
					label: '创建人',
				},
				{
					prop: 'createTime',
					label: '创建日期',
					width:'120px'
				},
				{
					prop: 'opt',
					label: '操作',
					optList:[
					{ key: '回测', label: '回测'},
					{ key: '生成模拟组合', label: '生成模拟组合'},
					{ key: 'more', label: '更多',moreList:[{key: 'edit', label: '编辑'},{key: 'del', label: '删除'}]},
					]
				}
			],
			buttonList:[{ key: '0', label: '全部' },
				{ key: '1', label: '私有的' },
			{ key: '2', label: '公共的' },],
			pageSize:10

        };
	},
	//监听属性 类似于data概念
	computed: {},
	//监控data中的数据变化
	watch: {},
	//方法集合
	methods: {
        handleSelect(){

        },
		getHeight () {
			// 140 130 50 44 40 40 60 112
			this.$nextTick(()=>{
				console.log("document.getElementsByClassName('content'):::::",parseInt(getComputedStyle(document.getElementsByClassName('content')[0]).height))
				let height = parseInt(getComputedStyle(document.getElementsByClassName('content')[0]).height)
				this.pageSize = Math.floor((height - 140 - 130 - 48 - 44 - 40 - 40 - 40 - 112) / 40);
				
			})
		},
    },
	// created () {
	// 	window.addEventListener('resize', this.getHeight)
	// },
	// destroyed () {
	// 	window.removeEventListener('resize', this.getHeight)
	// },
	//生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.$refs.ftb.getData('我的组合');
		// this.getHeight()
	},
	beforeCreate() {}, //生命周期 - 创建之前
	beforeMount() {}, //生命周期 - 挂载之前
	beforeUpdate() {}, //生命周期 - 更新之前
	updated() {}, //生命周期 - 更新之后
	beforeDestroy() {}, //生命周期 - 销毁之前
	destroyed() {}, //生命周期 - 销毁完成
	activated() {} //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
</style>
