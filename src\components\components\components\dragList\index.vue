<template>
	<div>
		<!-- <el-checkbox-group v-model="checkList" style="width: 85px">
		</el-checkbox-group> -->
		<transition-group name="drag" class="list" tag="div">
			<div
				class="flex_start py-4"
				v-for="(item, index) in list"
				:key="index"
				@dragenter="dragenter($event, index)"
				@dragover="dragover($event, index)"
				@dragstart="dragstart(index)"
				draggable
			>
				<drag-icon class="mr-24"></drag-icon>
				<el-checkbox v-model="item.show" @change="dragover">{{ item.label }}</el-checkbox>
			</div>
		</transition-group>
	</div>
</template>

<script>
// 拖拽图标
import dragIcon from './components/icon.vue';
export default {
	components: { dragIcon },
	data() {
		return {
			checkList: [],
			dragIndex: null
		};
	},
	props: {
		list: {
			type: Object,
			default: []
		}
	},
	methods: {
		dragstart(index) {
			this.dragIndex = index;
		},
		dragover() {
			// console.log(this.list);
			this.$emit('resolveFather');
		},
		dragenter(e, index) {
			e.preventDefault();
			// 避免源对象触发自身的dragenter事件;
			if (this.dragIndex !== index) {
				const moving = this.list[this.dragIndex];
				this.list.splice(this.dragIndex, 1);
				this.list.splice(index, 0, moving);
				// 排序变化后目标对象的索引变成源对象的索引
				this.dragIndex = index;
			}
		}
	}
};
</script>

<style></style>
