<!--  -->
<template>
  <div class="poolCreate">
    <el-dialog title="创建基金池" :visible.sync="updatepool" width="400px" destroy-on-close>
      <div style="margin-bottom: 8px" class="dialogfontsize15">基金池名称:</div>
      <el-input style="margin-bottom: 16px" type="text" v-model="poolname" label="基金代码"></el-input>
      <div style="margin-bottom: 8px" class="dialogfontsize15">基金池说明:</div>
      <el-input style="margin-bottom: 16px" type="textarea" v-model="pooldec" label="基金名称"></el-input>

      <div v-if="type2 != 'pool'" style="display: flex">
        <div style="flex: 1; margin-right: 16px">
          <div>
            <span>定时更新入池范围：</span>
          </div>
          <el-select style="margin-top: 8px" v-model="value2" placeholder="请选择">
            <el-option
              v-for="item in options2"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              :disabled="item.disabled"
            ></el-option>
          </el-select>
        </div>

        <div style="flex: 1">
          <div>
            <span>定时更新频率</span>
            <span>（频率/天）</span>
          </div>
          <el-select style="margin-top: 8px" v-model="value" placeholder="请选择">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              :disabled="item.disabled"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div style="margin-top: 16px; display: flex">
        <div style="flex: 1">
          <div style="border-right: 1px dashed #000; width: 150px">
            <div style="margin-bottom: 8px" class="dialogfontsize15">是否公开:</div>
            <el-radio-group style="margin-top: 8px" v-model="poolradio">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </div>
        </div>
        <div style="flex: 1">
          <div class="dialogfontsize15" style="margin-bottom: 8px">是否对标池:</div>
          <el-radio-group v-model="poolradios" style="margin-top: 8px">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </div>
      </div>
      <div style="margin-top: 16px" v-show="poolradios == 1">
        <div class="dialogfontsize15" style="margin-bottom: 8px">选择对标基金:</div>
        <div class="searchfundormanager">
          <el-select
            style="margin-right: 12px; margin-bottom: 16px"
            v-model="values"
            :remote-method="searchpeople"
            filterable
            remote
            :loading="loading"
            placeholder="输入简拼、代码、名称查询基金/经理"
            ref="select"
            @hook:mounted="cancalReadOnly"
            @visible-change="cancalReadOnly"
          >
            <template slot="prefix">
              <div
                style="width: 24px; height: 100%; display: flex; justify-content: center; align-items: center; matgin-left: 13.1px"
              >
                <i class="el-icon-search" style="color: #00000073"></i>
              </div>
            </template>
            <el-option-group
              v-for="groups in havefundmanager"
              :key="groups.label"
              :label="groups.label"
            >
              <el-option
                v-for="(group, index) in groups.options"
                :key="group.code + ' ' + index"
                :label="
                                      group.flag == 'fund'
                                          ? `${group.code}-${group.name}-${group.fundCo.split('基金')[0]}`
                                          : group.flag == 'manager'
                                          ? `${group.name}-${group.fundCo.split('基金')[0]}`
                                          : group.flag == 'company'
                                          ? group.name
                                          : `${group.name}-${group.code}`
                                  "
                :value="group.code + '|' + group.name + '|' + group.flag"
              ></el-option>
            </el-option-group>
          </el-select>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="updatapooldo()">创建</el-button>
      </span>
    </el-dialog>
  </div>
</template>
  
  <script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import axios from "@/api/index";
import { getPoolList } from "@/api/pages/SystemMixed.js";
export default {
  //import引入的组件需要注入到对象中才能使用
  components: {},
  data() {
    //这里存放数据
    return {
      updatepool: false,
      poolname: "",
      pooldec: "",
      poolradio: 0,
      poolradios: 0,
      value: "0",
      value2: "50",
      options: [
        {
          label: "关闭",
          value: "0"
        },
        {
          label: "1日",
          value: "1"
        },
        {
          label: "7日",
          value: "7"
        },
        {
          label: "30日",
          value: "30"
        },
        {
          label: "60日",
          value: "60"
        },
        {
          label: "180日",
          value: "180"
        },
        {
          label: "365日",
          value: "365"
        }
      ],
      options2: [
        {
          label: "TOP10",
          value: "10"
        },
        {
          label: "TOP50",
          value: "50"
        },
        {
          label: "TOP100",
          value: "100"
        },
        {
          label: "TOP200",
          value: "200"
        },
        {
          label: "全部",
          value: "all"
        }
      ],
      list: [],
      filter: {},
      type2: "",
      values: "",
      havefundmanager: [],
      loading: false
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    searchpeople(query) {
      ////console.log(query)
      ////console.log(this.values)
      let that = this;
      axios
        .get(this.$baseUrl + "/Analysis/Search/?message=" + query)
        .then(res => {
          let data = res?.data?.data || [];
          // //console.log(data);
          let temparr = [
            {
              label: "基金",
              options: []
            }
          ];
          for (let i = 0; i < data.length; i++) {
            if (data[i].flag === "fund") {
              temparr[0].options.push(data[i]);
            }
            // FIXME: 暂不开放股票入口
            // else if (data[i].flag == 'stock') {
            //  temparr[3].options.push(data[i]);
            // }
          }
          that.havefundmanager = temparr;
          that.loading = false;
        })
        .catch(error => {
          //that.$message('数据缺失');
        });
    },
    cancalReadOnly(value) {
      let plat = navigator.userAgent.match(
        // 判断不同端
        /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
      );
      if (plat) {
        this.$nextTick(() => {
          if (!value) {
            const { select } = this.$refs;
            const input = select.$el.querySelector(".el-input__inner");
            input.removeAttribute("readonly");
            // this.$refs.select.blur();  根据tip自行判断是否添加
          }
        });
      }
    },
    show(list, filter, type2, code) {
      if (this.FUNC.isEmpty(code)) {
        this.poolradios = 1;
        this.values = code;
      }
      this.updatepool = true;
      this.list = [];
      for (let i = 0; i < list.length; i++) {
        this.list.push(list[i].code);
      }

      this.filter = filter;
      this.type2 = type2;
    },
    async updatapooldo() {
      let that = this;
      if (this.values == "" && this.poolradios == 1) {
        this.$message.warning("请选择对标基金");
        return false;
      }
      if (this.FUNC.isEmpty(this.poolname)) {
        let { data, mtycode, mtymessage } = await getPoolList({
          status: this.value == "" ? 0 : this.value * 1,
          name: this.poolname,
          description: this.pooldec,
          status3: that.poolradios,
          ispublic: this.poolradio,
          fundlist: this.list,
          filter: this.filter.formData,
          type: this.filter.ftype,
          type2: this.type2,
          range: this.value2,
          compareFund: this.values.split("|")[0]
        });
        if (mtycode == 200) {
          this.updatepool = false;
          this.$message.success("创建成功");
          if (this.poolradios == 1) {
            this.$router.push("/poolcompare");
          } else {
            this.$router.push("/poolnormal");
          }
        } else {
          this.$message.error(mtymessage);
        }
      } else {
        this.$message.error("请输入池子名称");
      }
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  beforeCreate() {}, //生命周期 - 创建之前
  beforeMount() {}, //生命周期 - 挂载之前
  beforeUpdate() {}, //生命周期 - 更新之前
  updated() {}, //生命周期 - 更新之后
  beforeDestroy() {}, //生命周期 - 销毁之前
  destroyed() {}, //生命周期 - 销毁完成
  activated() {} //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
  <style lang="scss" scoped>
//@import url(); 引入公共css类
</style>