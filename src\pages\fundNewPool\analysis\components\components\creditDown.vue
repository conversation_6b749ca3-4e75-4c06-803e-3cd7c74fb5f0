<template>
  <div class="chart_one"
       v-show="show"
       v-loading="loading">
    <div class="flex_between">
      <div class="title">信用下沉</div>
      <!-- <div>
        <el-select v-model="model"
                   placeholder=""
                   multiple
                   collapse-tags>
          <el-option v-for="item in options"
                     :key="item.value"
                     :label="item.label"
                     :value="item.value"> </el-option>
        </el-select>
      </div>-->
    </div>
    <div class="charts_fill_class"
         style="margin-top: 16px">
      <v-chart :options="option"
               v-loading="loading"
               element-loading-text="暂无数据"
               element-loading-spinner="el-icon-document-delete"
               element-loading-background="rgba(239, 239, 239, 0.5)"
               class="charts_one_class"
               autoresize />
    </div>
  </div>
</template>

<script>
import { getCreditDownInfo } from "@/api/pages/tools/pool.js";
import { lineChartOption } from "@/utils/chartStyle.js";
export default {
  props: {
  },
  data () {
    return {
      ismanager: false,
      show: true,
      optione: [],
      model: [],
      loading: true,
      option: {},
      info: {},
      data: []
    };
  },
  methods: {
    async getData (info) {
      this.info = info;
      this.loading = true;
      let data = await getCreditDownInfo({
        ids: this.info["code_list"].map(v => {
          return {
            code: v.code,
            type: this.ismanager
              ? "manager"
              : "fund"
          };
        }),
        insert_time: this.info.date,
        yearqtr: this.info["quarter"],
        method: "等权",
        flag: 5,
        ismanager: this.ismanager,
        type: this.info.type,
      });
      this.loading = false;
      if (data?.mtycode == 200 && data?.data.length > 0) {
        this.show = true;
        this.data = data?.data;
        this.formatData(this.data);
      } else {
        this.show = false;
        return false;
      }
    },
    formatData (data) {
      let series = [];
      let quarter = [];
      let temp = [
        {
          name: data[0].name,
          code: data[0].fund_pool_id,
          data: [[data[0].yearqtr, (data[0].downratioinN * 100).toFixed(2)]]
        }
      ];
      quarter = [data[0].yearqtr];
      for (let i = 1; i < data.length; i++) {
        if (quarter.findIndex(item => item == data[i].yearqtr) < 0)
          quarter.push(data[i].yearqtr);
        if (temp.findIndex(item => item.name == data[i].name) < 0) {
          temp.push({
            name: data[i].name,
            code: data[i].fund_pool_id,
            data: [[data[i].yearqtr, (data[i].downratioinN * 100).toFixed(2)]]
          });
        } else {
          temp[temp.findIndex(item => item.name == data[i].name)].data.push([
            data[i].yearqtr,
            (data[i].downratioinN * 100).toFixed(2)
          ]);
        }
      }
      quarter = quarter.sort((a, b) => {
        if (a > b) return 1;
        else return -1;
      });
      if (data && data?.length > 20) {
        const quarterReturns = {};
        // 遍历所有基金的季度收益率，按照季度分类
        temp.forEach(fund => {
          fund.data.forEach(quarterReturn => {
            const quarter = quarterReturn[0];
            const returnRate = parseFloat(quarterReturn[1]);
            if (quarter in quarterReturns) {
              quarterReturns[quarter].push(returnRate);
            } else {
              quarterReturns[quarter] = [returnRate];
            }
          });
        });
        let temp2 = [];
        let temp3 = [];
        for (const quarter in quarterReturns) {
          const returns = quarterReturns[quarter];
          returns.sort((a, b) => a - b);
          const q2_5 = returns[Math.floor(returns.length * 0.025)];
          const q97_5 = returns[Math.floor(returns.length * 0.975)];
          temp2.push([quarter, q2_5.toFixed(2)]);
          temp3.push([quarter, q97_5.toFixed(2)]);
        }
        series = [
          {
            name: "97.5%分位",
            data: temp3,
            areaStyle: {
              color: "#ccc",
              origin: "start",
              opacity: 0.5
            },
            // areaStyle: {
            //   color: '#000',
            //   origin: 'start',
            //   opacity: 0.1
            // },
            type: "line"
            // Symbol: 'none',
          },
          {
            name: "2.5%分位",
            data: temp2,
            // areaStyle: {
            //   color: '#fff',
            //   origin: 'start',
            //   shadowColor: '#fff',
            //   shadowOffsetX: 1
            // },
            areaStyle: {
              color: "#fff",
              origin: "start",
              shadowColor: "#fff",
              opacity: 1
            },
            type: "line"
            // Symbol: 'none',
          }
          // {
          //   name: '填充区域上折现高',
          //   type: 'line',
          //   areaStyle: {
          //     color: '#0000CD',
          //     origin: 'start',
          //     opacity: 0.5
          //   },
          //   data: tops
          // },
          // {
          //   name: '填充区域下折现高',
          //   type: 'line',
          //   areaStyle: {
          //     color: '#fff',
          //     origin: 'start',
          //     shadowColor: '#F3F3F3',
          //     shadowOffsetX: 1
          //   },
          //   data: bottoms
          // }
        ];
        for (let i = 0; i < this.info.code_list.length; i++) {
          if (this.info.code_list[i].flag != 0) {
            series.push({
              name:
                temp[
                  temp.findIndex(
                    item => item.code == this.info.code_list[i].code
                  )
                ].name,
              data:
                temp[
                  temp.findIndex(
                    item => item.code == this.info.code_list[i].code
                  )
                ].data,
              type: "line",
              symbol: "circle",
              symbolSize: 15
            });
          }
        }
      } else if (data) {
        series = temp.map(item => {
          return {
            name: item.name,
            data: item.data,
            type: "line",
            symbol: "circle",
            symbolSize: 15
          };
        });
      }
      this.option = lineChartOption({
        xAxis: [{ show: true, name: "时间", data: quarter }],
        yAxis: [{ name: "%", type: "value" }],
        legend: series.map(item => {
          return item.name;
        }),
        series: series
      });
    },
    refresInfo (info) {
      this.info = info;
      this.formatData(this.data);
    }
  },
  mounted () {
    this.ismanager = String(this.$route.query.ismanager) == 'true' ? true : false
  },
};
</script>

<style></style>
