<template>
	<div class="charts_fill_class" v-loading="loading">
		<v-chart
			ref="barLineChartComponent"
			class="charts_one_class"
			autoresize
			element-loading-text="暂无数据"
			element-loading-spinner="el-icon-document-delete"
			element-loading-background="rgba(239, 239, 239, 0.5)"
			:options="option"
			@legendselectchanged="legendselectchanged"
		/>
	</div>
</template>

<script>
import vChart from 'vue-echarts';
import { barChartOption } from '@/utils/chartStyle.js';

export default {
	components: { vChart },
	data() {
		return {
			option: {},
			loading: true,
			styleColor: ['#F8931B','#FEC70B','#FDED00','#B5EC30','#08C47C','#00D7E9','#00D7E9','#00D7E9','#984dc1','#984dc1', '#4096ff', '#7388A9', '#6F80DD', '#F6BD16', '#F6BD16', '#E8684A']
		};
	},
	methods: {
		getData({ series, legend, xAxis, dateDayList }) {
			this.loading = false;
			this.$nextTick(() => {
				this.option = barChartOption({
					color: this.styleColor,
					grid: {
						top: '8px',
						left: '48px',
						right: '48px',
						bottom: '96px'
					},
					toolbox: 'none',
					legend: {
						bottom: '0',
						...legend
					},
					tooltip: {
						formatter: (params) => {
							let str = '';
							str += `日期: ${params[0].axisValue} <br />`;
							if (params.length > 0 && params[0].seriesName == '指数') {
								let temp = this.FUNC.dateToQuarter(params[0].axisValue);
								let year = temp.slice(6, 7);
								let queater = temp.slice(0, 4);
								let dates = this.FUNC.getQuarterStartDate(year, queater);

								let indexs = series?.[0].data.findIndex((item) => item[0] == dates);
								for (let i = 0; i < series.length; i++) {
									// str += `${series?.[i]?.name}: ${
									// 	!isNaN(series?.[i]?.data?.[indexs]?.[1]) ? Number(series?.[i]?.data?.[indexs][1]).toFixed(2) + '%' : '--'
									// }<br />`;
									let item = series?.[i]?.data?.[indexs]?.[1];
									str += `<div style="display:flex;justify-content:space-between;align-item:center;">
                                        <div>${series?.[i]?.name}:</div>
                                        <div>
                                            ${!isNaN(item) ? Number(item).toFixed(2) + '%' : '--'}
                                        </div>
                                     </div>`;
								}
							}
							for (let i = params.length - 1; i >= 0; i--) {
								if (params[i].seriesName !== '指数') {
									let dotHtml =
										'<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' +
										params[i].color +
										'"></span>';
									str += `<div style="display:flex;justify-content:space-between;align-item:center;">
                                        <div>${dotHtml}${params[i].seriesName}:</div>
                                        <div>${!isNaN(params[i]?.value?.[1]) ? Number(params[i]?.value?.[1]).toFixed(2) + '%' : '--'}</div>
                                     </div>`;
								}
							}
							return `<div style="padding:12px">${str}</div>`;
						}
					},
					dataZoom: {
						bottom: '48px',
						start: 0,
						end: 100
					},
					xAxis: [
						{ data: xAxis, isAlign: true },
						{ data: dateDayList, show: false }
					],
					yAxis: [
						{
							name: '占净值比',
							min: 0,
							formatter: function (value) {
								return value + '%';
							},
							nameLocation: 'middle', // 设置名称居中
							nameGap: 48, // 控制名称距离轴线的距离
							nameTextStyle: {
								align: 'center'
							}
						},
						{
							name: '累计收益率',
							// show: false,
							formatter: function (value) {
								return value + '%';
							},
							nameLocation: 'middle', // 设置名称居中
							nameGap: 48, // 控制名称距离轴线的距离
							nameRotate: 270,
							nameTextStyle: {
								align: 'center'
							}
						}
					],
					series
				});
				console.log(this.option);
			});
		},
		legendselectchanged(val) {
			this.$emit('legendselectchanged', val.selected);
		},
		createPrintWord() {
			this.$refs['barLineChartComponent'].mergeOptions({
				toolbox: { show: false }
			});
			let chart = this.$refs['barLineChartComponent'].getConnectedDataURL({
				type: 'jpg',
				pixelRatio: 2,
				backgroundColor: '#fff',
				excludeComponents: ['dataZoom']
			});
			this.$refs['barLineChartComponent'].mergeOptions({
				toolbox: { show: true }
			});
			return chart;
		}
	}
};
</script>

<style></style>
