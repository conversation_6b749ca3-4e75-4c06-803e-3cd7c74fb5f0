<!--  -->
<template>
  <div class="returns">
    <div style="display: flex; align-items: center; width: 100%; position: relative; justify-content: space-between">
      <div style="display: flex; align-items: center">
        <div class="TitltCompare">α、β比较</div>
      </div>
      <div class="TitltCompare"
           style="display: flex; justify-content: flex-end; align-items: center; font-size: 14px">
        <div>
          <el-date-picker @change="changgealphabeta"
                          v-model="valuedata"
                          unlink-panels
                          style="width: 216px"
                          type="daterange"
                          range-separator="|"
                          :picker-options="pickerOptions"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期">
          </el-date-picker>
        </div>
        <div style="width: 24px"></div>

        <div>
          背景选择：
          <el-select @change="changgealphabeta"
                     v-model="value1"
                     :remote-method="searchpeople1"
                     filterable
                     remote
                     prefix-icon="el-icon-search"
                     :loading="loading"
                     placeholder="选择比较背景">
            <el-option v-for="item in options"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"> </el-option>
          </el-select>
        </div>
        <div style="width: 24px"></div>

        <div>
          市场基准选择：
          <el-select @change="changgealphabeta"
                     v-model="valuebase"
                     :remote-method="searchpeople"
                     filterable
                     remote
                     prefix-icon="el-icon-search"
                     :loading="loading"
                     placeholder="选择基准">
            <el-option-group v-for="groups in optionsbase"
                             :key="groups.label"
                             :label="groups.label">
              <el-option v-for="group in groups.options"
                         :key="group.code"
                         :label="group.name"
                         :value="group.code"> </el-option>
            </el-option-group>
          </el-select>
        </div>
      </div>
    </div>

    <div v-loading="loadingitem"
         style="page-break-inside: avoid; text-align: left; margin-top: 24px">
      <v-chart ref="basicsmsgRankcompare"
               v-loading="empty1"
               autoresize
               element-loading-text="暂无数据"
               element-loading-spinner="el-icon-document-delete"
               element-loading-background="rgba(239, 239, 239, 0.5)"
               style="page-break-inside: avoid; width: 100%; height: 400px"
               :options="optionpbroe"></v-chart>
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { FundAlphaWithBeta, ManagerAlphaWithBeta } from '@/api/pages/tools/compare.js';
import { getFundOrBase } from '@/api/pages/components/yejiheader.js';
import VCharts from 'vue-echarts';
export default {
  props: {
    comparetype: {
      type: String,
      default: 'manager' //fund
    },
    id: {
      type: String,
      default: '30189741,30441407'
    },
    name: {
      type: String,
      default: '萧楠,胡昕炜'
    },
    type: {
      type: String,
      default: 'equity'
    },
    rankHold: {
      type: Array
    }
  },
  //import引入的组件需要注入到对象中才能使用
  components: { 'v-chart': VCharts },
  data () {
    //这里存放数据
    return {
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一周',
            onClick (picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近一个月',
            onClick (picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近三个月',
            onClick (picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近六个月',
            onClick (picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90 * 2);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近一年',
            onClick (picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 365);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近三年',
            onClick (picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 365 * 3);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近五年',
            onClick (picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 365 * 5);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      },
      empty1: false,
      showdetailchoose: false,
      value1: '',
      value2: '',
      value3: '',
      valuebase: '',
      valuedata: '',
      options: [
        {
          value: '中周期',
          label: '中周期'
        },
        {
          value: '大金融',
          label: '大金融'
        },
        {
          value: '大科技',
          label: '大科技'
        },
        {
          value: '大周期',
          label: '大周期'
        },
        {
          value: '大消费',
          label: '大消费'
        }
      ],
      optionpbroe: {},
      optionsbase: [],
      loadingitem: false
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {
    value (val) {
      this.getmanagerdata();
    },
    rankHold (val) {
      if (val && val != '' && val.length == 2 && val[0] != this.valuebase) {
        let that = this;
        setTimeout(() => {
          that.valuebase = val[0];
          that.optionsbase = val[1];
          that.changgealphabeta();
        }, 200);
      }
    },
    valuebase (val) {
      // console.log("out");
      this.$emit('outBasicRank', [this.valuebase, this.optionsbase]);
    }
  },
  //方法集合
  methods: {
    changgealphabeta () {
      this.getdata();
    },
    // 获取基准
    async searchpeople (query) {
      this.loading = false;
      let { data } = await getFundOrBase({ message: query, flag: 6 });
      if (data) {
        let temparr = [
          {
            label: '参考基准',
            options: []
          }
        ];
        for (let i = 0; i < data.length; i++) {
          if (data[i].flag == 'index') {
            temparr[0].options.push(data[i]);
          }
        }
        this.optionsbase = temparr;
      }
    },
    searchpeople1 () { },
    getdata () {
      // Object.assign(this.$data, this.$options.data());
      this.empty1 = false;
      if (this.comparetype == 'manager') {
        this.getmanagerdata();
      } else {
        this.gefunddata();
      }
    },
    async getmanagerdata () {
      let start_date = '';
      let end_date = '';
      let data = await ManagerAlphaWithBeta({
        fund_name: this.name,
        fund_code: this.id,
        type: this.type,
        start_date: start_date,
        end_date: end_date,
        background: this.value1,
        index_code: this.valuebase
      });
      if (data) {
        if (JSON.stringify(data.data) == '{}' || JSON.stringify(data.data) == '[]' || JSON.stringify(data.data) == '') {
          this.empty1 = true;
        } else {
        }
      }
      // if (data) {
      // this.optionpbroe = {
      //     xAxis: {
      //         type: 'category',
      //         data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
      //     },
      //     yAxis: {
      //         type: 'value'
      //     },
      //     series: [{
      //         data: [150, 230, 224, 218, 135, 147, 260],
      //         type: 'line'
      //     }]
      // }
      // } else {
      //     this.empty1 = true
      // }
    },
    async gefunddata () {
      let start_date = '';
      let end_date = '';
      // //console.log(this.valuedata)
      if (this.valuedata != '') {
        var d = new Date(this.valuedata[0]);
        var d1 = new Date(this.valuedata[1]);
        start_date = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate();
        end_date = d1.getFullYear() + '-' + (d1.getMonth() + 1) + '-' + d1.getDate();
      }
      this.loadingitem = true;
      let data = await FundAlphaWithBeta({
        fund_name: this.name,
        fund_code: this.id,
        type: this.type,
        start_date: start_date,
        end_date: end_date,
        background: this.value1,
        index_code: this.valuebase
      });
      this.loadingitem = false;
      if (data) {
        if (JSON.stringify(data.data) == '{}' || JSON.stringify(data.data) == '[]' || JSON.stringify(data.data) == '') {
          this.empty1 = true;
        } else {
          // //console.log(data)
          // //console.log('dara')
          let dataarr = [];
          let dataarr2 = [];
          for (let i = 0; i < data.data.length; i++) {
            if (this.id.indexOf(data.data[i].code) >= 0) {
              dataarr2.push([
                (Number(data.data[i].alpha) * 100).toFixed(2),
                Number(data.data[i].beta).toFixed(2),
                data.data[i].code,
                data.data[i].name,
                data.data[i].R2
              ]);
            } else {
              dataarr.push([
                (Number(data.data[i].alpha) * 100).toFixed(2),
                Number(data.data[i].beta).toFixed(2),
                data.data[i].code,
                data.data[i].name,
                data.data[i].R2
              ]);
            }
          }
          let seriess = [
            {
              name: '背景参照系',
              type: 'scatter',
              emphasis: {
                focus: 'series'
              },
              data: dataarr
            }
          ];
          for (let i = 0; i < dataarr2.length; i++) {
            seriess.push({
              name: dataarr2[i][3],
              type: 'scatter',
              // emphasis: {
              //     focus: 'series'
              // },
              data: [dataarr2[i]]
            });
          }
          seriess.sort((a, b) => {
            if (this.$route.query.name.split(',').indexOf(a.name) > this.$route.query.name.split(',').indexOf(b.name)) return 1;
            else return -1;
          });
          // //console.log(seriess)
          this.optionpbroe = {
            color: ['#f1f1f1', '#4096ff', '#4096ff', '#7388A9', '#6F80DD', '#929694', '#4096FF', '#e040fb', '#ff3d00'],
            tooltip: {
              trigger: 'axis',
              formatter: function (obj) {
                // //console.log(obj)
                let temp = '';
                for (let i = 0; i < obj.length; i++) {
                  temp += obj[i].data[3] + ':  alpha:' + obj[i].data[0] + '   ,beta:' + obj[i].data[1] + `<br />`;
                }
                // var value = obj.value;
                return temp;
              }
            },
            legend: {},
            grid: {
              left: '3%',
              right: '60px',
              bottom: '3%',
              top: '30px',
              containLabel: true
            },
            xAxis: {
              name: 'alpha',
              type: 'value',
              max: 200,
              min: -40,
              boundaryGap: false,
              axisLabel: {
                formatter: function (obj) {
                  //   //console.log(obj)
                  // var value = obj.value;
                  return obj + '%';
                }
              }
            },
            yAxis: {
              name: 'beta',
              axisLine: {
                show: false
              },
              axisTick: {
                show: false
              },
              splitLine: {
                show: true,
                lineStyle: {
                  type: 'dashed'
                }
              },
              min: 0,
              max: 2,
              type: 'value',
              axisLabel: {
                formatter: function (obj) {
                  //   //console.log(obj)
                  // var value = obj.value;
                  return obj.toFixed(1);
                }
              }
            },
            series: seriess
          };
        }
      }
    },
    createPrintWord () {
      let height = this.$refs['basicsmsgRankcompare']?.$el.clientHeight;
      let width = this.$refs['basicsmsgRankcompare']?.$el.clientWidth;
      let chart = this.$refs['basicsmsgRankcompare'].getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      });
      return [...this.$exportWord.exportFirstTitle('alpha-beta气泡图'), ...this.$exportWord.exportChart(chart, { width, height })];
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () { },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style scoped></style>
