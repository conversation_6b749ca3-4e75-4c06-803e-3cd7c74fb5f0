<template>
	<div>
		<div class="flex_between">
			<div class="flex_start">
				<div style="width: 42px">模板:</div>
				<div class="mr-8">
					<el-select v-model="active_template" placeholder="请选择模版" @change="changeTemplate">
						<el-option v-for="item in template_list" :key="item.value" :label="item.label" :value="item.value"></el-option>
					</el-select>
				</div>
				<div class="mr-8">
					<el-link :disabled="!active_template" @click="save">另存为模板</el-link>
				</div>
				<div>
					<el-link :disabled="!active_template" @click="resetTemplate">新建模版</el-link>
				</div>
			</div>
			<div class="flex_start">
				<div class="mr-12">
					<el-button :disabled="active_template != ''" @click="save">保存</el-button>
				</div>
				<div class="mr-12">
					<el-button :disabled="!active_template" @click="del">删除模版</el-button>
				</div>
				<div>
					<el-popover placement="top-end" width="272" v-model="visible">
						<div>
							<el-radio-group v-model="radio">
								<el-radio :label="0">已选数据导出</el-radio>
								<el-radio :label="1">自定义导出列</el-radio>
							</el-radio-group>
						</div>
						<div style="text-align: right; margin: 0" class="mt-12">
							<el-button size="mini" type="text" @click="visible = false">取消</el-button>
							<el-button type="primary" size="mini" @click="exportExcel">确定</el-button>
						</div>
						<el-button type="primary" slot="reference" icon="el-icon-download">导出Excel</el-button>
					</el-popover>
				</div>
			</div>
		</div>
		<custom-export-column ref="customExportColumn" :templateType="templateType" @returnRows="returnRows"></custom-export-column>
		<save-model-dialog ref="saveModelDialog" @submitModel="submitModel"></save-model-dialog>
	</div>
</template>

<script>
// 自定义导出列弹窗
import customExportColumn from './customExportColumn.vue';
// 保存模版弹窗
import saveModelDialog from './saveModelDialog.vue';
// 保存筛选条件模版
import { addFieldTemplate, getFieldTemplateList, deleteFieldTemplate } from '@/api/pages/ApiTaikang.js';
export default {
	components: { customExportColumn, saveModelDialog },
	data() {
		return {
			visible: false,
			active_template: '',
			template_list: [],
			radio: 0,
			templateType: ''
		};
	},
	props: {
		list: {
			type: Array,
			default: []
		},
		condition: {
			type: Array,
			default: []
		}
	},
	methods: {
		getData(templateType) {
			this.templateType = templateType;
			this.getFieldTemplateList();
		},
		async getFieldTemplateList() {
			let data = await getFieldTemplateList({ templateType: this.templateType });
			if (data.code == 200) {
				this.template_list = data.data.map((item) => {
					return { ...item, label: item.templateName, value: item.id };
				});
			}
		},
		// 导出excel
		exportExcel() {
			if (this.radio == 0) {
				this.visible = false;
				this.$emit('exportExcel');
			} else {
				this.$refs.customExportColumn.getData(this.list);
			}
		},
		// 监听模版选择
		changeTemplate() {
			let list = this.template_list.find((v) => v.value == this.active_template)?.fieldList;
			let condition = [];
			list.map((item) => {
				let index = condition.findIndex((v) => v.field == item.field);
				if (index == -1) {
					condition.push({
						field: item.field,
						fieldName: item.fieldName,
						fieldType: item.fieldType,
						children: item.fieldValue.map((v) => {
							return { value: v, mathRange: item.calculator || 'avg', operator: item.operator, unit: item.unit };
						})
					});
				} else {
					condition[index].children.push(
						...item.fieldValue.map((v) => {
							return { value: v, mathRange: item.calculator, operator: item.operator, unit: item.unit };
						})
					);
				}
			});

			this.$emit('updateActiveConditionList', condition);
		},
		// 获取自定义配置导出列
		returnRows(data) {
			this.$emit('exportExcel', data);
		},
		// 重置模版
		resetTemplate() {
			this.active_template = '';
			this.$emit('updateActiveConditionList', []);
		},
		// 打开保存模版弹窗
		save() {
			this.$refs['saveModelDialog'].getData();
			this.$emit('updateActiveConditionList');
		},
		// 删除模版
		del() {
			this.$confirm('此操作将永久删除该模版, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			})
				.then(async () => {
					let data = await deleteFieldTemplate({ id: this.active_template });
					if (data.code == 200) {
						this.$message({
							type: 'success',
							message: '删除成功!'
						});
						this.resetTemplate();
						this.getFieldTemplateList();
					} else {
						this.$message({
							type: 'warning',
							message: data.message
						});
					}
				})
				.catch(() => {
					this.$message({
						type: 'info',
						message: '已取消删除'
					});
				});
		},
		// 模版传递名称
		submitModel(name) {
			this.addFieldTemplate(name);
		},
		// 保存模版
		async addFieldTemplate(obj) {
			let fieldList = [];
			this.condition.map((obj) => {
				obj.children.map((item) => {
					fieldList.push({
						field: obj.field,
						fieldName: obj.fieldName,
						fieldValue: [item.value],
						operator: item?.operator,
						fieldType: obj?.fieldType,
						table: obj.table,
						tableName: obj.tableName,
						unit: item.unit
					});
				});
			});
			let postData = { templateName: obj.name, templateType: this.templateType, fieldList };
			let data = await addFieldTemplate(postData);
			if (data.code == 200) {
				this.$message.success('保存成功');
			}
			this.getFieldTemplateList();
		}
	}
};
</script>

<style></style>
