<template>
  <div class="returns">
    <div style="display: flex; align-items: center; width: 100%; position: relative; justify-content: space-between">
      <div style="display: flex; align-items: center">
        <div class="TitltCompare">指标比较</div>
      </div>
      <div class="returncomparebox"
           style="text-align: right; margin-top: 10px; display: flex; align-items: center">
        基准选择：
        <el-select v-model="value"
                   :remote-method="searchpeople"
                   filterable
                   remote
                   @change="changgealphabeta"
                   @focus="focusF"
                   prefix-icon="el-icon-search"
                   :loading="loading"
                   placeholder="请选择">
          <el-option v-for="group in options"
                     :key="group.code"
                     :label="group.name"
                     :value="group.code"> </el-option>
        </el-select>
        <div style="width: 24px"></div>
        <div style="display: flex; justify-content: flex-end">
          <el-button style="width: 104px"
                     @click="showCompare = true"
                     type="primary">选择指标</el-button>
          <el-button style="width: 104px"
                     @click="printExcel"
                     type="">导出数据</el-button>
        </div>
      </div>
    </div>

    <div style="margin-top: 16px"
         v-loading="loading_table">
      <div v-for="(item, index) in hold_Array"
           :key="index"
           style="margin-top: 24px">
        <sTable :data="item.list"
                :measure="item.value"
                typeFlag="2"></sTable>
      </div>
    </div>
    <el-dialog title="添加对比"
               :visible.sync="showCompare"
               width="1000px"
               destroy-on-close>
      <div v-loading="loadingitem"
           class="savemodel"
           style="width: 100%">
        <el-row>
          <div style="height: 10px"></div>
          <el-col :span="19"
                  style="border-right: 1px solid #e8e8e8">
            <div style="margin-top: 5px">
              <el-radio v-model="radio"
                        label="1w">近一周</el-radio>
              <el-radio v-model="radio"
                        label="1m">近一月</el-radio>
              <el-radio v-model="radio"
                        label="1q">近一季</el-radio>
              <el-radio v-model="radio"
                        label="2q">近半年</el-radio>
              <el-radio v-model="radio"
                        label="1y">近一年</el-radio>
              <el-radio v-model="radio"
                        label="recent_1y">今年以来</el-radio>
              <el-radio v-model="radio"
                        label="from_date">成立以来</el-radio>
            </div>
            <div style="margin-top: 30px; margin-left: 10px; height: 260px">
              <el-checkbox-group style="display: flex; flex-wrap: wrap"
                                 v-model="checkList">
                <el-checkbox style="width: 111px; text-align: left"
                             v-for="(item, index) in ListCheckBox"
                             :key="index"
                             :label="item.name"></el-checkbox>
              </el-checkbox-group>
            </div>
          </el-col>
          <el-col :span="5">
            <div style="margin-left: 10px">
              <div style="display: flex; justify-content: space-between; align-items: center">
                <div style="font-weight: 600">已选对比({{ endSelect.length }})</div>

                <div><el-button type="text"
                             style="color: red"
                             @click="clear">清空</el-button></div>
              </div>
              <el-scrollbar style="height: 300px">
                <div style="text-align: center; margin: 10px 0 0 0"
                     v-for="(item, index) in endSelect"
                     :key="index">
                  <el-tag style="min-width: 170px"
                          closable
                          @close="closeTags(index)"
                          type="info">{{ item.dateName }}{{ item.rankName }}</el-tag>
                </div>
              </el-scrollbar>
            </div>
          </el-col>
        </el-row>
        <div style="display: flex; justify-content: flex-end; align-items: center; width: 100%; margin-top: 10px">
          <el-button @click="showCompare = false">取消</el-button>
          <el-button @click="gotoCompare"
                     type="primary">确定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { ManagerSinceFeature, FundRiskMsg, ManagedFundReturn, Measure } from '@/api/pages/tools/compare.js';
import sTable from '../SelfTable.vue';
import { getFundOrBase } from '@/api/pages/components/yejiheader.js';
import VCharts from 'vue-echarts';
export default {
  props: {
    comparetype: {
      type: String,
      default: 'manager' //fund
    },
    id: {
      type: String,
      default: '30189741,30441407'
    },
    type: {
      type: String,
      default: 'equity'
    },
    name: {
      type: String,
      default: '萧楠,胡昕炜'
    },
    returnBCompare: {
      type: Array
    }
  },
  filters: {
    fix3 (value) {
      if (value == '--' || value == null || value == '') {
        return value;
      } else {
        return value.toFixed(4);
      }
    },
    fixz (value) {
      return value == '1w'
        ? '近一周'
        : value == '1m'
          ? '近一月'
          : value == '1q'
            ? '近一季'
            : value == '2q'
              ? '近半年'
              : value == '1y'
                ? '近一年'
                : value == 'recent_1y'
                  ? '今年以来'
                  : value == 'from_date'
                    ? '成立以来'
                    : '';
    },
    fix2 (value) {
      return Number(value).toFixed(2) + '亿';
    },
    fixz2 (value) {
      let ListCheckBox = [
        {
          name: '收益',
          value: 'cum_return'
        },
        {
          name: '年化波动',
          value: 'volatility'
        },
        {
          name: '夏普比率',
          value: 'sharpe'
        },
        {
          name: '卡玛比率',
          value: 'calmar'
        },
        {
          name: '索提诺比率',
          value: 'sortino'
        },
        {
          name: '最大回撤',
          value: 'maxdrawdown'
        },
        {
          name: 'alpha',
          value: 'alpha'
        },
        {
          name: 'beta',
          value: 'beta'
        },
        {
          name: '跟踪误差',
          value: 'trackingerroe'
        },
        {
          name: '信息比率',
          value: 'information'
        },
        {
          name: '特诺系数',
          value: 'treynor'
        },
        {
          name: '痛苦指数',
          value: 'painindex'
        },
        {
          name: '上攻潜力',
          value: 'upsidepotential'
        }
      ];
      return ListCheckBox[ListCheckBox.findIndex((values) => values.value == value)].name;
    }
  },
  //import引入的组件需要注入到对象中才能使用
  components: { 'v-chart': VCharts, sTable },
  data () {
    //这里存放数据
    return {
      loadingitem: false,
      showdetailchoose: false,
      value: '000300.SH',
      loading_table: false,
      loading: false,
      options: [{ name: '沪深300', code: '000300.SH' }],
      showCompare: false,
      radio: '1w',
      checkList: [],
      ListCheckBox: [
        {
          name: '收益',
          value: 'cum_return'
        },
        {
          name: '年化波动',
          value: 'volatility'
        },
        {
          name: '夏普比率',
          value: 'sharpe'
        },
        {
          name: '卡玛比率',
          value: 'calmar'
        },
        {
          name: '索提诺比率',
          value: 'sortino'
        },
        {
          name: '最大回撤',
          value: 'maxdrawdown'
        },
        {
          name: 'alpha',
          value: 'alpha'
        },
        {
          name: 'beta',
          value: 'beta'
        },
        {
          name: '跟踪误差',
          value: 'trackingerroe'
        },
        {
          name: '信息比率',
          value: 'information'
        },
        {
          name: '特诺系数',
          value: 'treynor'
        },
        {
          name: '痛苦指数',
          value: 'painindex'
        },
        {
          name: '上攻潜力',
          value: 'upsidepotential'
        }
      ],
      fundholdcolumns: [
        {
          dataIndex: 'name',
          key: 'name',
          title: '基金',
          align: 'left',
          scopedSlots: { customRender: 'name' },
          sorter: (a, b) => {
            if (a.name >= b.name) return 1;
            else return -1;
          }
        },
        {
          dataIndex: 'measure_name',
          key: 'measure_name',
          title: '指标',
          align: 'left',
          scopedSlots: { customRender: 'measure_name' },
          sorter: (a, b) => {
            if (a.measure_name >= b.measure_name) return 1;
            else return -1;
          }
        },
        {
          dataIndex: 'flag',
          key: 'flag',
          title: '区间',
          align: 'left',
          scopedSlots: { customRender: 'flag' },
          sorter: (a, b) => {
            if (a.flag >= b.flag) return 1;
            else return -1;
          }
        },
        {
          dataIndex: 'meter',
          key: 'meter',
          title: '数值',
          align: 'left',
          scopedSlots: { customRender: 'meter' },
          sorter: (a, b) => {
            if (a.meter >= b.meter) return 1;
            else return -1;
          }
        }
      ],
      endSelect: [],
      fund_hold: [],
      hold_Array: []
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {
    returnBCompare (val) {
      if (val && val != '' && val.length == 3) {
        let that = this;
        setTimeout(() => {
          that.value = val[0];
          that.options = val[1];
          that.endSelect = val[2];
          // that.gotoCompare(1);
        }, 100);
      }
    },
    radio (val) {
      this.checkList = [];
      for (let i = 0; i < this.endSelect.length; i++) {
        if (this.endSelect[i].dateValue == val) {
          this.checkList.push(this.endSelect[i].rankName);
        }
      }
    },
    checkList (val) {
      let radioName =
        this.radio == '1w'
          ? '近一周'
          : this.radio == '1m'
            ? '近一月'
            : this.radio == '1q'
              ? '近一季'
              : this.radio == '2q'
                ? '近半年'
                : this.radio == '1y'
                  ? '近一年'
                  : this.radio == 'recent_1y'
                    ? '今年以来'
                    : this.radio == 'from_date'
                      ? '成立以来'
                      : '';
      let temp = [];
      for (let i = 0; i < this.endSelect.length; i++) {
        if (this.endSelect[i].dateValue != this.radio) {
          temp.push(this.endSelect[i]);
        }
      }

      for (let i = 0; i < val.length; i++) {
        temp.push({
          dateName: radioName,
          dateValue: this.radio,
          rankName: val[i],
          rankValue: this.ListCheckBox[this.ListCheckBox.findIndex((item) => item.name == val[i])].value
        });
      }
      // console.log(val);
      this.endSelect = temp;
    }
  },
  //方法集合
  methods: {
    changgealphabeta () {
      this.gotoCompare(1);
    },
    printExcel () {
      const { export_json_to_excel } = require('@/vendor/Export2Excel');
      var list = [];
      let tHeader = [];
      let tHeaderKey = [];
      for (let i = 0; i < this.fundholdcolumns.length; i++) {
        tHeader.push(this.fundholdcolumns[i].title);
        tHeaderKey.push(this.fundholdcolumns[i].dataIndex);
      }
      // console.log(tHeader);
      for (let i = 0; i < this.fund_hold.length; i++) {
        list[i] = [];
        for (let j = 0; j < tHeaderKey.length; j++) {
          list[i][j] = this.fund_hold[i][tHeaderKey[j]];
        }
      }
      // console.log(list);
      export_json_to_excel(tHeader, list, '指标比较');
    },
    closeTags (index) {
      if (this.checkList.findIndex((item) => item == this.endSelect[index].rankName) >= 0) {
        this.checkList.splice(
          this.checkList.findIndex((item) => item == this.endSelect[index].rankName),
          1
        );
      } else {
        this.endSelect.splice(index, 1);
      }
    },
    async gotoCompare (flag) {
      this.loading_table = true;
      if (flag != 1) {
        this.$emit('outBasicBCompare', [this.value, this.options, this.endSelect]);
      }
      // if (this.value == '') {
      // 	this.$message.error('请选择比较基准');
      // } else if (this.endSelect.length == 0) {
      // 	this.$message.error('请选择比较指标');
      // } else if (this.endSelect.length > 0) {
      let temp = [];
      for (let i = 0; i < this.endSelect.length; i++) {
        temp.push({
          measure_name: this.endSelect[i].rankValue,
          flag: this.endSelect[i].dateValue
        });
      }
      this.loadingitem = true;
      try {
        let index_name = this.options[this.options.findIndex((item) => item.code == this.value)].name;
        let data = await Measure({
          type: this.type,
          manager_name: this.name,
          flag: this.comparetype == 'manager' ? '2' : '1',
          code: this.id,
          name: this.name,
          measure_data: temp,
          index_code: this.value,
          index_name: index_name
        });
        this.loading_table = false;
        if (data) {
          let dateKey = [];
          let temp = [];
          let tempall = this.$route.query.id.split(',');
          for (let i = 0; i < data.data.length; i++) {
            if (dateKey.indexOf(data.data[i].flag + '|' + data.data[i].measure_name) < 0) {
              dateKey.push(data.data[i].flag + '|' + data.data[i].measure_name);
            }
            if (temp.indexOf(data.data[i].code) < 0) {
              temp.push(data.data[i].code);
            }
            if (tempall.indexOf(data.data[i].code) < 0) {
              tempall.push(data.data[i].code);
            }
          }
          let t = tempall.filter((item) => !temp.includes(item));
          // console.log('xxxxxx');
          for (let k = 0; k < t.length; k++) {
            for (let j = 0; j < dateKey.length; j++) {
              let arryT = {
                code: t[k],
                flag: dateKey[j].split('|')[0],
                measure_name: dateKey[j].split('|')[1],
                meter: '--',
                name: this.$route.query.name.split(',')[this.$route.query.id.split(',').indexOf(t[k])]
              };
              data.data.push(arryT);
            }
          }

          this.fund_hold = data.data.sort((a, b) => {
            let t = a.measure_name + a.flag;
            let t2 = b.measure_name + b.flag;
            if (t >= t2) return 1;
            else return -1;
            // return  a.measure_name >= b.measure_name ?1: a.flag < b.flag ?1:-1;
          });
          this.duelDate();
          this.showCompare = false;
          this.loadingitem = false;
        }
      } catch (e) {
        console.error(e);
        this.loadingitem = false;
        this.$message.error('服务器错误');
      }
      // }
    },
    clear () {
      this.radio = '1w';
      this.checkList = [];
      this.endSelect = [];
    },
    async getdata () {
      this.loadingitem = true;
      try {
        let data = await Measure({
          type: this.type,
          manager_name: this.name,
          flag: this.comparetype == 'manager' ? '2' : '1',
          code: this.id,
          name: this.name,
          measure_data: [],
          index_code: '',
          index_name: ''
        });
        if (data) {
          let dateKey = [];
          let temp = [];
          let tempall = this.$route.query.id.split(',');
          for (let i = 0; i < data.data.length; i++) {
            if (dateKey.indexOf(data.data[i].flag + '|' + data.data[i].measure_name) < 0) {
              dateKey.push(data.data[i].flag + '|' + data.data[i].measure_name);
            }
            if (temp.indexOf(data.data[i].code) < 0) {
              temp.push(data.data[i].code);
            }
            if (tempall.indexOf(data.data[i].code) < 0) {
              tempall.push(data.data[i].code);
            }
          }
          let t = tempall.filter((item) => !temp.includes(item));
          // console.log('xxxxxx');
          for (let k = 0; k < t.length; k++) {
            for (let j = 0; j < dateKey.length; j++) {
              let arryT = {
                code: t[k],
                flag: dateKey[j].split('|')[0],
                measure_name: dateKey[j].split('|')[1],
                meter: '--',
                name: this.$route.query.name.split(',')[this.$route.query.id.split(',').indexOf(t[k])]
              };
              data.data.push(arryT);
            }
          }
          this.fund_hold = data.data.sort((a, b) => {
            let t = a.measure_name + a.flag;
            let t2 = b.measure_name + b.flag;
            if (t >= t2) return 1;
            else return -1;
            // return  a.measure_name >= b.measure_name ?1: a.flag < b.flag ?1:-1;
          });
          this.duelDate();
          this.showCompare = false;
          this.loadingitem = false;
        }
      } catch (e) {
        this.loadingitem = false;
        this.$message.error('服务器错误');
      }
    },
    duelDate () {
      try {
        this.hold_Array = [];
        let list = [];
        let list2 = [];
        for (let i = 0; i < this.fund_hold.length; i++) {
          if (list.indexOf(this.fund_hold[i].measure_name + this.fund_hold[i].flag) < 0) {
            list.push(this.fund_hold[i].measure_name + this.fund_hold[i].flag);
            list2[list.indexOf(this.fund_hold[i].measure_name + this.fund_hold[i].flag)] = [];
            list2[list.indexOf(this.fund_hold[i].measure_name + this.fund_hold[i].flag)].push(this.fund_hold[i]);
          } else {
            list2[list.indexOf(this.fund_hold[i].measure_name + this.fund_hold[i].flag)].push(this.fund_hold[i]);
          }
        }

        let listName = this.$route.query.id.split(',');
        listName.push(this.value);
        for (let i = 0; i < list2.length; i++) {
          if (list2[i].length < 4) {
            let t = listName.filter((item) => !list2[i].map((items) => items.code).includes(item));
            list2[i].push({ measure_name: list2[i][0].measure_name, flag: list2[i][0].flag, name: '--', code: t[0], meter: '--' });
          }
        }
        for (let i = 0; i < list2.length; i++) {
          list2[i].sort((a, b) => {
            if (listName.indexOf(a.code) > listName.indexOf(b.code)) return 1;
            else return -1;
          });
          this.hold_Array[i] = { value: [], list: [['指标'], ['区间'], ['数值']] };
          for (let j = 0; j < list2[i].length; j++) {
            if (list2[i][j].code != this.value && list2[i][j].code != '000300.SH' && list2[i][j].code != undefined) {
              // console.log(list2[i][j].code);
              if (list2[i][j].measure_name != '--') {
                this.hold_Array[i].list[0].push(this.fixMRisk(list2[i][j].measure_name));
              } else {
                this.hold_Array[i].list[0].push('--');
              }
              if (list2[i][j].flag != '--') {
                this.hold_Array[i].list[1].push(this.fixYear(list2[i][j].flag));
              } else {
                this.hold_Array[i].list[1].push('--');
              }
              if (list2[i][j].meter != '--') {
                this.hold_Array[i].list[2].push(Number(list2[i][j].meter).toFixed(4));
              } else {
                this.hold_Array[i].list[2].push('--');
              }
            } else {
              this.hold_Array[i].value = [
                list2[i][j].name,
                this.fixMRisk(list2[i][j].measure_name),
                this.fixYear(list2[i][j].flag),
                Number(list2[i][j].meter).toFixed(4)
              ];
            }
          }
        }
        // console.log(this.hold_Array);
      } catch (e) {
        console.log(e);
      }
    },
    async searchpeople (query) {
      if (query == '') {
        this.loading = false;
        // let data = await getFundOrBase({'message':"1"})
        this.options = this.COMMON.optionBasic;
        // console.log(this.options);
      } else {
        this.loading = false;
        let data = await getFundOrBase({ message: query });
        if (data) {
          // //console.log(data)
          let temparr = [
            {
              label: '参考基准',
              options: []
            }
          ];
          for (let i = 0; i < data.length; i++) {
            if (data[i].flag == 'index') {
              temparr[0].options.push(data[i]);
            }
          }
          this.options = temparr?.[0].options;
          // //console.log(data)
        }
      }
    },
    fixYear (value) {
      return value == '1w'
        ? '近一周'
        : value == '1m'
          ? '近一月'
          : value == '1q'
            ? '近一季'
            : value == '2q'
              ? '近半年'
              : value == '1y'
                ? '近一年'
                : value == 'recent_1y'
                  ? '今年以来'
                  : value == 'from_date'
                    ? '成立以来'
                    : '';
    },
    fixMRisk (value) {
      let ListCheckBox = [
        {
          name: '收益',
          value: 'cum_return'
        },
        {
          name: '年化波动',
          value: 'volatility'
        },
        {
          name: '夏普比率',
          value: 'sharpe'
        },
        {
          name: '卡玛比率',
          value: 'calmar'
        },
        {
          name: '索提诺比率',
          value: 'sortino'
        },
        {
          name: '最大回撤',
          value: 'maxdrawdown'
        },
        {
          name: 'alpha',
          value: 'alpha'
        },
        {
          name: 'beta',
          value: 'beta'
        },
        {
          name: '跟踪误差',
          value: 'trackingerroe'
        },
        {
          name: '信息比率',
          value: 'information'
        },
        {
          name: '特诺系数',
          value: 'treynor'
        },
        {
          name: '痛苦指数',
          value: 'painindex'
        },
        {
          name: '上攻潜力',
          value: 'upsidepotential'
        }
      ];
      return ListCheckBox[ListCheckBox.findIndex((values) => values.value == value)].name;
    },
    createPrintWord () {
      console.log(this.fund_hold);
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () { },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style></style>
