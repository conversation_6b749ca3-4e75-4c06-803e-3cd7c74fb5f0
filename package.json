{"name": "manage-system", "version": "4.2.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "dev:GDBank": "cross-env PROJECT_NAME=projectGDBank vue-cli-service serve", "dev:Base": "cross-env PROJECT_NAME=projectBase vue-cli-service serve", "dev:Tk": "cross-env PROJECT_NAME=projectTk vue-cli-service serve", "test:Base": "cross-env PROJECT_NAME=projectBase vue-cli-service build --mode test", "test:GDBank": "cross-env PROJECT_NAME=projectGDBank vue-cli-service build --mode test", "build": "vue-cli-service build --mode production", "build:Tk": "cross-env PROJECT_NAME=projectTk vue-cli-service build --mode test", "build:GDBank": "cross-env PROJECT_NAME=projectGDBank vue-cli-service build --mode production", "build:Simple": "cross-env PROJECT_NAME=simpleProduction vue-cli-service build --mode simpleProduction", "build:Base": "cross-env PROJECT_NAME=projectBase vue-cli-service build --mode production"}, "dependencies": {"@antv/g2plot": "^2.4.31", "@babel/polyfill": "^7.12.1", "axios": "^0.18.0", "babel-polyfill": "^6.26.0", "core-js": "^2.6.12", "crypto-js": "^4.2.0", "d3-array": "^2.4.0", "d3-color": "^1.4.0", "d3-scale": "^3.2.0", "d3-shape": "^1.3.5", "dayjs": "^1.11.11", "docx": "^7.3.0", "driver.js": "^1.3.1", "echarts": "^4.8.0", "element-ui": "^2.11.0", "file-saver": "^2.0.2", "html2canvas": "^1.4.1", "module": "^1.2.5", "moment": "^2.29.1", "print-js": "^1.6.0", "qrcodejs2": "0.0.2", "qs": "^6.10.3", "sass": "^1.69.5", "sass-loader": "^9.0.2", "script-loader": "^0.7.2", "vcolorpicker": "^1.1.0", "vue": "2.6.10", "vue-easy-jwt": "^2.0.6", "vue-echarts": "^5.0.0-beta.0", "vue-router": "^3.0.3", "vuex": "^3.5.1", "xlsx": "^0.18.5", "xss": "^1.0.7"}, "devDependencies": {"@babel/plugin-proposal-optional-chaining": "^7.16.7", "@vue/cli-plugin-babel": "^3.9.0", "@vue/cli-service": "^3.9.0", "compression-webpack-plugin": "^5.0.0", "copy-webpack-plugin": "^6.4.1", "cross-env": "^7.0.3", "image-webpack-loader": "^8.1.0", "script-loader": "^0.7.2", "vue-particles": "^1.0.9", "vue-template-compiler": "2.6.10", "webpack": "^4.47.0", "webpack-dev-server": "^3.0.0"}}