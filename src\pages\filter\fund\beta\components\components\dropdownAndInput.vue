<template>
	<div>
		<div>{{ val.value }}</div>
		<div>
			<el-dropdown @command="command">
				<el-button type="primary">
					<span>{{ val.value == '' ? val.placeholder : val.list[val.list.findIndex((v) => v.value == val.value)].label }}</span>
					<i class="el-icon-arrow-down el-icon--right"></i>
				</el-button>
				<el-dropdown-menu slot="dropdown">
					<el-dropdown-item v-for="(v, v_index) in val.list" :command="v.value" :key="v_index">{{ v.label }}</el-dropdown-item>
				</el-dropdown-menu>
			</el-dropdown>
		</div>
		<div>
			<el-input style="width: 200px" v-model="val.value" :placeholder="val.placeholder"></el-input>
		</div>
	</div>
</template>

<script>
export default {};
</script>

<style></style>
