<template>
  <div>
    <el-dialog class="dialogs"
               title="合并基金池"
               v-loading="loading"
               :visible.sync="visible"
               style="font-size: 16px"
               width="608px"
               destroy-on-close>
      <div style="display: flex;margin-top:16px;align-items: center;">
        <div>
          <span style="color: red">*</span>
          <span class="dialogfontsize15"
                style="margin-bottom: 8px; margin-left: 8px">
            <i style="color: red"></i>池子名称:
          </span>
        </div>
        <div>
          <el-input placeholder="请输入池"
                    type="text"
                    style=" width: 473px; margin-left: 9px"
                    v-model="form.name"
                    label="代码"></el-input>
        </div>
      </div>
      <div style="display: flex;align-items: center;margin-top: 24px;">
        <div>
          <span style>选择交并补:</span>
        </div>
        <div class="selectJiaoBingBu"
             style="display: inline-block; margin-left: 9px; width: 473px; border: 1px solid #d9d9d9; border-radius: 4px; min-height: 32px">
          <div :class="
						range_list.length == 4
							? 'leftParts'
							: range_list.length == 3
							? 'leftParts4'
							: range_list.length == 2
							? 'leftParts3'
							: range_list.length == 1
							? 'leftParts2'
							: ''
					"
               style="display: flex; justify-content: space-between; width: 100%">
            <div class="leftPart"
                 style="position: absolute"
                 v-for="index in range_list"
                 :key="index">
              <div class="bar"></div>
              <div style="display: flex; flex-direction: column">
                <el-radio-group v-model="index['range']"
                                size="mini">
                  <el-radio-button label="交"></el-radio-button>
                  <el-radio-button label="并"></el-radio-button>
                  <el-radio-button label="补"></el-radio-button>
                </el-radio-group>
              </div>
              <div class="bar"></div>
            </div>

            <div v-if="list.length"
                 :class="
							list.length == 5
								? 'rightPart'
								: list.length == 4
								? 'rightPart4'
								: list.length == 3
								? 'rightPart3'
								: list.length == 2
								? 'rightPart2'
								: ''
						"
                 style="flex: 2; width: 100%; flex-direction: column; align-items: self-end; display: flex">
              <div class="item"
                   style="line-height: 35px; min-width: 0; height: 35px; background-color: #f5f5f5"
                   v-for="(tt, index) in list"
                   :key="index">
                <span style="margin-left: 6px">{{ tt.name }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- <div style="margin-left: 16px; margin-top: 24px">
        <div style="margin-bottom: 8px; display: inline-block"
             class="dialogfontsize15">选择行业:</div>
        <div style="display: inline-block; margin-left: 6px">
          <el-cascader style="width: 473px; display: inline-block"
                       placeholder="请选择行业"
                       :options="industryList"
                       v-model="form.industry_list"
                       :props="{ multiple: true, emitPath: false }"
                       filterable></el-cascader>
        </div>
      </div> -->

      <!-- <div style="margin-left: 2px; margin-top: 24px">
        <div style="margin-bottom: 8px; display: inline-block"
             class="dialogfontsize15">指定人查看:</div>
        <div style="display: inline-block; margin-left: 6px">
          <el-cascader style="width: 100%"
                       placeholder="选择可查看人员"
                       :options="userList"
                       v-model="form.user_ids"
                       :props="{ multiple: true, emitPath: false }"
                       filterable></el-cascader>
        </div>
      </div> -->

      <div style="display: flex; align-items: center; margin-left: 16px; margin-top: 10px; margin-bottom: 10px">
        <div style="flex: 1"
             display="inline-block">
          <div style="width: 190px">
            <div class="dialogfontsize15"
                 style="margin-bottom: 8px; display: inline-block">是否公开:</div>
            <el-radio-group v-model="form.ispublic"
                            style="margin-left: 8px; margin-top: 17px; margin-bottom: 16px; display: inline-block">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </div>
        </div>
        <div v-show="ismanager"
             style="flex: 1">
          <div style="display:flex;align-items: center;">
            <div class="dialogfontsize15"
                 style="width:100px">经理池类型:</div>
            <el-select size="mini"
                       style="margin-right: 4px;"
                       v-model="selectType"
                       placeholder="请选择">
              <el-option style="width: 190px"
                         v-for="item in options"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value">
              </el-option>
            </el-select>
          </div>
        </div>
      </div>

      <div class="dialogfontsize15"
           style="margin-bottom: 45px; margin-left: 16px; display: inline-block">池子说明:</div>
      <el-input type="textarea"
                style="margin-bottom: 16px; margin-left: 6px; display: inline-block"
                v-model="form.description"
                label="名称"></el-input>

      <span slot="footer"
            class="dialog-footer">
        <el-button type
                   @click="visible = false">取消</el-button>
        <el-button type="primary"
                   @click="submit">创 建</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mergeFundPool } from "@/api/pages/tools/pool.js";
export default {
  data () {
    return {
      visible: false,
      poolname: "",
      list: [],
      range_list: [],
      form: { ispublic: 0 },
      loading: false,
      options: [{ value: 'activeequity', label: '主动权益' },
      { value: 'hkequity', label: '港股权益' },
      { value: 'bond', label: '固收+' },
      { value: 'cbond', label: '可转债' },
      { value: 'purebond', label: '纯债' },
      { value: 'bill', label: '中短债' },
      ],
      selectType: 'activeequity',
    };
  },
  props: {
    userList: {
      type: Object,
      default: []
    },
    industryList: {
      type: Object,
      default: []
    },
    isdb: {
      type: Number,
      default: 0
    },
    ismanager: {
      type: Boolean
    }
  },
  methods: {
    getData (val) {
      this.visible = true;
      this.list = val;
      this.range_list = val.map(v => v).slice(1);
    },
    async submit () {
      if (!this.form.name) {
        this.$message.error("请输入池名称");
        return;
      }
      if (this.range_list.some(v => !v.range)) {
        this.$message.error("请选择交并补");
        return;
      }
      let postData = {
        name: this.form.name,
        description: this.form.description,
        status: this.form.ispublic,
        status3: this.isdb,
        userlist: this.form.user_ids,
        pool_list: this.list.map(v => String(v.id)),
        method: this.range_list.map(v => v.range),
        company_ids: this.form.industry_list || [],
        flag: this.ismanager ? 'manager' : 'fund',
        type: this.selectType
      };
      this.loading = true;
      let data = await mergeFundPool(postData);
      if (data?.mtycode == 200) {
        this.$message.success("创建成功");
        this.visible = false;
        this.$emit("resolveFather");
      } else {
        this.$message.warning("创建失败" + data?.mtymessage);
      }
      this.loading = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.title {
	background: #f3f4f8;
	font-weight: 600;
	padding: 10px 15px;
}
.sub-title {
	font-size: 14px;
	font-weight: 600;
	border-left: 2px solid dodgerblue;
	margin-bottom: 6px;
	padding-left: 3px;
	line-height: 22px;
	height: 22px;
	flex: 1 1 auto;
}
.title-change-fund {
	display: flex;
	align-items: center;
	margin-bottom: 4px;
	label {
		font-size: 14px;
		margin-right: 10px;
	}
}
//勾选5个的情况
.rightPart :nth-child(1) {
	// width: 330px;
	width: 310px;

	margin-right: 20px;
}
.rightPart :nth-child(2) {
	// width: 330px;
	width: 310px;

	margin-right: 20px;
}
.rightPart :nth-child(3) {
	// width: 374px;
	// width: 363px;
	width: 343px;

	margin-right: 20px;
}
.rightPart :nth-child(4) {
	// width: 398px;
	// width: 390px;
	width: 373px;

	margin-right: 20px;
}
.rightPart :nth-child(5) {
	// width: 422px;
	// width: 420px;
	width: 404px;
	margin-right: 20px;
}
//  5个  ↑

//  rightPart 4个  ↓
.rightPart4 :nth-child(1) {
	// width: 330px;
	width: 341px;

	margin-right: 20px;
}
.rightPart4 :nth-child(2) {
	// width: 330px;
	width: 341px;

	margin-right: 20px;
}
.rightPart4 :nth-child(3) {
	// width: 374px;
	// width: 363px;
	width: 374px;

	margin-right: 20px;
}
.rightPart4 :nth-child(4) {
	width: 406px;

	margin-right: 20px;
}
// rightPart 4个   ↑

//  rightPart 3个  ↓
.rightPart3 :nth-child(1) {
	// width: 330px;
	width: 370px;

	margin-right: 20px;
}
.rightPart3 :nth-child(2) {
	// width: 330px;
	width: 370px;

	margin-right: 20px;
}
.rightPart3 :nth-child(3) {
	width: 405px;

	margin-right: 20px;
}
// rightPart 3个   ↑

//  rightPart 2个  ↓
.rightPart2 :nth-child(1) {
	// width: 330px;
	width: 400px;

	margin-right: 20px;
}
.rightPart2 :nth-child(2) {
	// width: 330px;
	width: 400px;

	margin-right: 20px;
}

// rightPart 2个   ↑

// 5个 ↓
.leftParts div:nth-child(1) {
	// left: 195px;
	left: 215px;
}
.leftParts div:nth-child(2) {
	// left: 163px;

	left: 183px;

	// .leftPart {
	.bar {
		// width: 5px;
		height: 27px;
		// background-color: #e9e9e9;
		// margin-left: 8px;
	}
	// }
}
.leftParts div:nth-child(3) {
	// left: 132px;
	left: 152px;

	.bar {
		// width: 5px;
		height: 50px;
		// background-color: #e9e9e9;
		// margin-left: 8px;
	}
}
.leftParts div:nth-child(4) {
	// left: 103px;
	left: 123px;

	.bar {
		// width: 5px;
		height: 80px;
	}
}

// leftParts4 ↓
.leftParts4 div:nth-child(1) {
	// left: 195px;
	left: 181px;
}
.leftParts4 div:nth-child(2) {
	// left: 163px;

	left: 150px;

	// .leftPart {
	.bar {
		// width: 5px;
		height: 27px;
		// background-color: #e9e9e9;
		// margin-left: 8px;
	}
	// }
}
.leftParts4 div:nth-child(3) {
	// left: 132px;
	left: 120px;

	.bar {
		// width: 5px;
		height: 50px;
		// background-color: #e9e9e9;
		// margin-left: 8px;
	}
}

// leftParts4 ↑

// leftParts3 ↓
.leftParts3 div:nth-child(1) {
	// left: 195px;
	left: 150px;
}
.leftParts3 div:nth-child(2) {
	// left: 163px;

	left: 120px;

	// .leftPart {
	.bar {
		// width: 5px;
		height: 27px;
	}
	// }
}

// leftParts3 ↑

// leftParts2 ↓
.leftParts2 div:nth-child(1) {
	// left: 195px;
	left: 120px;
}

// leftParts2 ↑

.leftPart {
	z-index: 9;
	margin-top: 10px;
}
.item {
	margin: 8px 0;
}

::v-deep .el-radio-button--mini .el-radio-button__inner {
	/* ... */
	padding: 7px 4px;
}
::v-deep .leftParts .el-radio-group {
	/* ... */
	width: 25px;
}
::v-deep .leftParts4 .el-radio-group {
	/* ... */
	width: 25px;
}
::v-deep .leftParts3 .el-radio-group {
	/* ... */
	width: 25px;
}
::v-deep .leftParts2 .el-radio-group {
	/* ... */
	width: 25px;
}

::v-deep .leftParts1 .el-radio-group {
	/* ... */
	width: 25px;
}
::v-deep.dialogs .el-input--small {
	/* ... */
	width: 475px;
}

::v-deep .el-dialog__header {
	border-bottom: 1px solid rgb(233, 233, 233);
}

::v-deep .el-radio-button__inner {
	// border-radius: 0;
	border: none;
}
::v-deep .el-radio-button:first-child .el-radio-button__inner {
	border-radius: 0;
}

::v-deep .el-radio-button:last-child .el-radio-button__inner {
	border-radius: 0;
}

::v-deep .el-radio-button:first-child .el-radio-button__inner {
	border-left: none;
}

::v-deep .el-dialog__footer {
	border-top: 1px solid rgb(233, 233, 233);
}

::v-deep .el-radio-button__inner {
	background-color: rgb(233, 233, 233);
}

.bar {
	width: 2px;
	height: 5px;
	background-color: #e9e9e9;
	margin-left: 8px;
}
// .item:nth-child(3) {
// 	width: 374px;
// }
// .item:nth-child(4) {
// 	width: 398px;
// }
// .item:nth-child(5) {
// 	width: 422px;
// }
</style>
