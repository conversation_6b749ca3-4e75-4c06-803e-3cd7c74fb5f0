<template>
    <div class="box_Board">
    <div class="header_box"><span class="header_unactive">市场分析&nbsp;/&nbsp;</span>基金市场配置<span></span></div>
    <ThePositionTimingChartPlate></ThePositionTimingChartPlate>
    <TheIndustryConfigurationChangePlate></TheIndustryConfigurationChangePlate>
    <TheIndustryConfigurationPlate ></TheIndustryConfigurationPlate>
    <TheHeavyPositionStocksPlate ></TheHeavyPositionStocksPlate>
    <TheHeavyFundPlate ></TheHeavyFundPlate>
  </div>
</template>
<script>
import '@/pages/assets/css/page-container.scss';
import ThePositionTimingChartPlate from './component/ThePositionTimingChartPlate.vue';
import TheIndustryConfigurationChangePlate from './component/TheIndustryConfigurationChangePlate.vue';
import TheIndustryConfigurationPlate from './component/TheIndustryConfigurationPlate.vue';
import TheHeavyPositionStocksPlate from './component/TheHeavyPositionStocksPlate.vue';
import TheHeavyFundPlate from './component/TheHeavyFundPlate.vue';
export default {
    components:{
        ThePositionTimingChartPlate,
        TheIndustryConfigurationChangePlate,
        TheIndustryConfigurationPlate,
        TheHeavyPositionStocksPlate,
        TheHeavyFundPlate
    }
}
</script>
<style scoped>
</style>