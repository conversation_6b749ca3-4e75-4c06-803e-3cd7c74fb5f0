<template>
  <el-dialog :visible.sync="dialogVisible"
             width="50%"
             top="20vh"
             @close="dialogVisible = false"
             :close-on-click-modal="false"
             :title="title"
             :modal="false">
    <div class="border_table main">
      <div>
        <!-- 右侧标题区域 -->
        <div class="border_table_header">
          <div class="border_table_header_title">
            <div class="block" />
            <div>{{ title }}</div>
          </div>
        </div>
        <div class="area-body">
          <div class="table">
            <el-table v-loading="tableLoading"
                      :data="tableData"
                      border
                      stripe
                      @sort-change="sortData">
              <el-table-column align="gotoleft"
                               label="名称"
                               prop="name" />
              <el-table-column align="gotoleft"
                               label="申万一级"
                               prop="sw1level" />
              <el-table-column align="gotoleft"
                               label="申万二级"
                               prop="sw2level" />
              <el-table-column align="gotoleft"
                               label="申万三级"
                               prop="sw3level" />
              <el-table-column align="gotoleft"
                               label="泰康内部"
                               prop="tklevel" />
              <el-table-column align="gotoleft"
                               label="区间涨跌幅">
                <div slot-scope="scope">
                  {{ (scope.row.dataArr[0][scope.row.dataArr[0].length - 1]['股价走势特征'] * 100).toFixed(2) }}%
                </div>
              </el-table-column>
              <el-table-column align="gotoleft"
                               label="指数收益率">
                <div slot-scope="scope">
                  {{ (scope.row.dataArr[0][scope.row.dataArr[0].length - 1]['行业指数'] * 100).toFixed(2) }}%
                </div>
              </el-table-column>
              <el-empty :image-size="180" />
            </el-table>
          </div>
        </div>
      </div>

      <div>
        <div class="border_table_header">
          <div class="border_table_header_title">
            <div class="block" />
            <div>股价走势特征</div>
          </div>
          <div class="border_table_header_filter">
            <div class="border_table_header_upload">
              <i class="el-icon-download"></i>
            </div>
          </div>
        </div>
        <div class="area-body">
          <div class="chart">
            <v-chart autoresize
                     id="1"
                     element-loading-text="暂无数据"
                     element-loading-spinner="el-icon-document-delete"
                     element-loading-background="rgba(239, 239, 239, 0.5)"
                     style="height: 340px; width: 100% !important"
                     :options="performance.options" />
          </div>
        </div>
      </div>
      <div>
        <div class="border_table_header">
          <div class="border_table_header_title">
            <div class="block" />
            <div>盈利与估值</div>
          </div>
          <div class="border_table_header_filter">
            <div class="border_table_header_upload">
              <i class="el-icon-download"></i>
            </div>
          </div>
        </div>
        <div class="area-body">
          <div class="chart">
            <v-chart autoresize
                     id="1"
                     element-loading-text="暂无数据"
                     element-loading-spinner="el-icon-document-delete"
                     element-loading-background="rgba(239, 239, 239, 0.5)"
                     style="height: 340px; width: 100% !important"
                     :options="particle.options" />
          </div>
        </div>
        <Person ref="person" />
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { lineChartOption, barChartOption } from "@/utils/chartStyle";
import { dateList, testData0, testData1, testData2, testData3 } from "@/utils/date";
import { getObjectStockInfo } from '@/api/pages/analysis/report'
import Person from "./person.vue";

export default {
  components: { Person },
  data () {
    return {
      date: '',
      params: {},
      dialogVisible: false,
      title: '',
      tableData: [],// 页面表格数据源
      oldTableData: [],
      select: [
        {
          value: '选项1',
          label: '整体'
        },
        {
          value: '选项2',
          label: '分析对象1'
        },
        {
          value: '选项3',
          label: '分析对象2'
        },
      ],
      value: '',
      account: '',
      selectAccount: [
        {
          value: '选项1',
          label: '整体'
        },
        {
          value: '选项2',
          label: '分析对象1'
        },
        {
          value: '选项3',
          label: '分析对象2'
        },
      ],
      promptMessage: false,
      year: '',
      performance: {
        options: {}, // 业绩表现图表数据源
      },
      particle: {
        options: {}, // 业绩表现图表数据源
      },
      tableLoading: false,
    }
  },
  mounted () {
    this.oldTableData = this.tableData
  },
  methods: {
    /**
     * 排序
     */
    sortData ({ column, prop, order }) {
      let arr = JSON.parse(JSON.stringify(this.tableData.filter(v => v[prop] !== 'nan' && v[prop] !== 'NaN')))
      let noArr = this.tableData.filter(v => v[prop] === 'nan' || v[prop] === 'NaN')
      if (order === 'ascending') {
        this.tableData = noArr.concat(arr.sort((a, b) => Number(a[prop]) - Number(b[prop])))
      }
      if (order === 'descending') {
        this.tableData = arr.sort((a, b) => Number(b[prop]) - Number(a[prop])).concat(noArr)
      }
      if (order === null) {
        this.tableData = this.oldTableData
      }
    },
    /**
     * 数据整理
     */
    arrangeData (arr) {
      arr.forEach((item) => {
        item.data.dataArr = []
        eval(item.data.date).forEach((citem, cindex) => {
          item.data.dataArr.push({
            date: citem,
            '基准指数': eval(item.data['基准指数'])[cindex],
            '行业指数': eval(item.data['行业指数'])[cindex],
            '股价走势特征': eval(item.data['301365.SZ'])[cindex],
            'pb': eval(item.data['pb'])[cindex],
            'roe': eval(item.data['roe'])[cindex],
            '股票披露权重': eval(item.data['weightInNetasset'])[cindex],
          })
        })
      })
      arr.forEach(item => {
        item.data.dataArr.sort((a, b) => a.date.localeCompare(b.date))
      })
      return arr
    },

    /**
     * 数据格式
     */
    formatter (row, column, cellValue, index) {
      // 期末规模
      if (column.label === '期末规模（亿）') {
        if (cellValue === 'nan' || cellValue === 'NaN' || cellValue === undefined || !cellValue) {
          return '--'
        }
        if (Number((Number(cellValue) / 100000000).toFixed(2)) !== 0) {
          return (Number(cellValue) / 100000000).toFixed(2)
        } else {
          return '0.00（' + (Number(cellValue) / 10000).toFixed(2) + '万）'
        }
      }
      // 净买入
      if (column.label === '市值收益（万）' || column.label === '累计浮盈（万）') {
        if (cellValue === 'nan' || cellValue === 'NaN' || cellValue === undefined || !cellValue) {
          return '--'
        }
        return (Number(cellValue) / 10000).toFixed(2)
      }

      if (column.label === '持仓权重') {
        if (cellValue === 'nan' || cellValue === 'NaN' || cellValue === undefined || !cellValue) {
          return '--'
        }
        return (Number(cellValue) * 100).toFixed(2) + '%'
      }
    },

    showDialog (obj) {
      this.dialogVisible = true
      this.title = obj.name
      this.tableData = [obj]
      this.getChartData()
    },

    /**
     * 表格数据生成
     */
    getChartData () {
      console.log(this.tableData, 'nbnb');
      const line1 = [
        {
          name: '行业指数',
          type: 'line',
          symbol: 'none',
          lineStyle: {
            width: 3
          },
          data: this.tableData[0].dataArr[0].map(v => Number((v['行业指数'] * 100).toFixed(2)))
        },
        {
          name: '沪深300',
          type: 'line',
          symbol: 'none',
          lineStyle: {
            width: 3
          },
          data: this.tableData[0].dataArr[0].map(v => Number((v['基准指数'] * 100).toFixed(2)))
        },
        {
          name: '股价走势特征',
          type: 'line',
          symbol: 'none',
          lineStyle: {
            width: 3
          },
          data: this.tableData[0].dataArr[0].map(v => Number((v['股价走势特征'] * 100).toFixed(2)))
        },
        {
          name: '股票披露权重',
          type: 'bar',
          barWidth: 15,
          data: this.tableData[0].dataArr[0].map(v => Number((v['股票披露权重'] * 100).toFixed(2)))
        }
      ]
      this.performance.options = barChartOption({
        grid: { top: '24px', bottom: '36px' }, // 位置
        toolbox: false,
        tooltip: {
          formatter: function (obj) {
            let value = `<div style="font-size:14px;">` + obj?.[0].axisValue + `</div>`;
            for (let i = 0; i < obj.length; i++) {
              value +=
                `<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
                `<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
                obj?.[i].color +
                `;"></div>` +
                `<div style="font-family: PingFang SC;">` +
                obj?.[i].seriesName +
                '</div></div>' +
                `<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
                (Number(obj?.[i].value)) +
                '%</div>' +
                `</div>`;
            }
            return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
          }
        },
        legend: {
          data: ['行业指数', '沪深300', '股价走势特征', '股票披露权重'],
        },
        xAxis: [
          {
            name: '日期',
            data: this.tableData[0].dataArr[0].map(v => v['date'])
          },
        ],
        series: line1,
        yAxis: [
          {
            formatter: function (value) {
              return value + '%';
            }
          }
        ],
      })
      const line2 = [
        {
          name: 'pb',
          type: 'line',
          symbol: 'none',
          yAxisIndex: 0,
          lineStyle: {
            width: 3
          },
          data: this.tableData[0].dataArr[1].map(v => ((v?.pe * 1).toFixed(2)))
        },
        {
          name: 'roe',
          yAxisIndex: 1,
          type: 'line',
          symbol: 'none',
          lineStyle: {
            width: 3
          },
          data: this.tableData[0].dataArr[1].map(v => ((v?.roe * 1).toFixed(2)))
        },

      ]
      this.particle.options = lineChartOption({
        grid: { left: '24px', right: '48px', top: '24px', bottom: '36px' }, // 位置
        dataZoom: false,
        toolbox: false,
        tooltip: {
          formatter: function (obj) {
            let value = `<div style="font-size:14px;">` + obj?.[0].axisValue + `</div>`;
            for (let i = 0; i < obj.length; i++) {
              value +=
                `<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
                `<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
                obj?.[i].color +
                `;"></div>` +
                `<div style="font-family: PingFang SC;">` +
                obj?.[i].seriesName +
                '</div></div>' +
                `<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
                (Number(obj?.[i].value)) + `` + (obj?.[i].seriesName == 'roe' ? '%' : '') +
                '</div>' +
                `</div>`;
            }
            return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
          }
        },
        legend: {
          data: ['pe', 'roe'],
        },
        xAxis: [
          {
            name: '日期',
            data: this.tableData[0].dataArr[1].map(v => v['date'])
          }
        ],
        series: line2,
        yAxis: [
          {
            name: 'pe',
            type: 'value',
            formatter: function (value, index) {  //Y轴的自定义刻度值，对应上图
              return `${value}`;
            },
          }, {
            name: 'roe',
            type: 'value',
            formatter: function (value, index) {  //Y轴的自定义刻度值，对应上图
              return `${value}%`;
            },
          },
        ],
      });
    },

  },
}
</script>

<style lang="scss" scoped>
@import '../../../../tkdesign';

.main {
	height: 60vh;
	overflow-y: auto;
}

.border_table_header {
	padding-bottom: 16px;
	border-bottom: 1px solid #ccc;

	.border_table_header_title {
		display: flex;
		align-items: center;

		.block {
			width: 6px;
			height: 20px;
			border-radius: 35px;
			background-color: #4096ff;
			margin-right: 16px;
		}

		i {
			margin-left: 3px;
		}
	}

	.border_table_header_filter {
		display: flex;
		align-items: center;
		font-size: 14px;

		.border_table_header_radio {
			display: flex;
			align-items: center;
		}

		.border_table_header_select {
			margin-left: 16px;
		}

		.border_table_header_upload {
			width: 32px;
			line-height: 30px;
			border-radius: 4px;
			border: 1px solid #d9d9d9;
			text-align: center;
			margin-left: 16px;
		}
	}
}

.search-security {
	width: 210px;
	margin-right: 16px;
}
</style>
