<template>
	<div id="warehouseAdjustmentRhythm">
		<analysis-card-title title="调仓效果" image_id="warehouseAdjustmentRhythm"></analysis-card-title>
		<div id="warehouseAdjustmentRhythmMain">
			<div class="charts_fill_class" v-loading="loading">
				<v-chart
					ref="warehouseAdjustmentRhythm"
					v-loading="loading"
					element-loading-text="暂无数据"
					element-loading-spinner="el-icon-document-delete"
					element-loading-background="rgba(239, 239, 239, 0.5)"
					class="charts_analysis_class"
					style="height: 400px"
					autoresize
					:options="barGraphqs"
				/>
			</div>
			<div class="mt-20" v-if="showDescription">
				<analysis-description :is_column="true" :description="description"></analysis-description>
			</div>
		</div>
	</div>
</template>

<script>
// 模型使用说明
import analysisDescription from '@/components/components/components/analysisDescription/index.vue';
import { lineChartOption } from '@/utils/chartStyle.js';

// 调仓效果接口有问题，暂时使用前十大接口代替调试页面
import { getImaginaryNav } from '@/api/pages/Analysis.js';
// 调仓效果
export default {
	name: 'warehouseAdjustmentRhythm',
	components: { analysisDescription },
	props: {
		showDescription: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			loading: true,
			barGraphqs: {},
			tabledata: [],
			alphaOption: {},
			info: {}
		};
	},
	computed: {
		description() {
			return `以公开持仓保持不动，每季度做再平衡，其表现与原始表现对比。在这一部分，我们主 要做了这样一个假设: 假如维持上一季末的持仓不变，与实际的组合表现（季中做过调仓） 进行比较，是否调仓能够获得超额收益。`;
		}
	},
	methods: {
		async getData(info) {
			this.info = info;
			await this.getImaginaryNav();
		},
		// 获取调仓效果数据
		async getImaginaryNav() {
			let topTenAttacks = null;
			this.loading = true;
			let data = await getImaginaryNav({
				code: this.info.code,
				type: this.info.type,
				flag: this.info.flag,
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			this.loading = false;
			if (data?.mtycode == 200) {
				topTenAttacks = data?.data;
			}

			this.getChartData(topTenAttacks?.imaginary_big10);
		},
		getChartData(data) {
			// 绘制图表
			if (data.length == 0) {
				this.loading = true;
				this.barGraphqs = null;
			} else {
				let result = data.sort((a, b) => {
					return this.moment(this.moment(a.date, 'YYYY-MM-DD').format()).isBefore(this.moment(b.date, 'YYYY-MM-DD').format()) ? -1 : 1;
				});
				this.barGraphqs = lineChartOption({
					toolbox: 'none',
					color: ['#F8931B','#FEC70B','#FDED00','#B5EC30','#08C47C','#00D7E9','#00D7E9','#00D7E9','#984dc1','#984dc1','#4096ff', '#4096ff', '#7388A9'],
					legend: {
						bottom: '0',
						data: [
							{ name: '假想组合收益', icon: 'line' },
							{ name: '基金累计收益', icon: 'line' },
							{ name: '超额收益', icon: 'bar' }
						]
					},
					grid: {
						top: '12px',
						bottom: '38px'
					},
					xAxis: [{ data: result.map((v) => v.date) }],
					yAxis: [
						{
							type: 'value'
						}
					],
					tooltip: {
						formatter: function (obj) {
							var value = `<div style="font-size:14px;">` + obj?.[0].axisValue + `</div>`;
							for (let i = 0; i < obj.length; i++) {
								value +=
									`<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
									`<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
									obj?.[i].color +
									`;"></div>` +
									`<div style="font-family: PingFang SC;">` +
									obj?.[i].seriesName +
									'</div></div>' +
									`<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
									(Number(obj?.[i].value?.[1]) * 100).toFixed(2) +
									'%</div>' +
									`</div>`;
							}
							return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
						}
					},
					series: [
						{
							name: '假想组合收益',
							type: 'line',
							data: result.map((v) => [v.date, v.big10_cumreturn]),
							connectNulls: true,
							symbol: 'none',
							lineStyle: {
								color: '#4096ff'
							}
						},
						{
							name: '基金累计收益',
							type: 'line',
							data: result.map((v) => [v.date, v.cum_return]),
							connectNulls: true,
							symbol: 'none',
							lineStyle: {
								color: '#F8BE15'
							}
						},
						{
							name: '超额收益',
							type: 'line',
							data: result.map((v) => [v.date, v.cum_return - v.big10_cumreturn]),
							connectNulls: true,
							symbol: 'none',
							lineStyle: {
								width: 0,
								color: 'rgba(115, 136, 169, 0.15)'
							},
							areaStyle: {
								color: 'rgba(115, 136, 169, 0.15)'
							}
						}
					]
				});
				this.loading = false;
			}
		},
		async createPrintWord(info) {
			await this.getData(info);
			let key = 'warehouseAdjustmentRhythmMain';
			return await new Promise((resolve, reject) => {
				this.$nextTick(async () => {
					let height = document.getElementById(key).clientHeight;
					let width = document.getElementById(key).clientWidth;
					let canvas = await this.html2canvas(document.getElementById(key), {
						scale: 3
					});
					let print_word = [
						...this.$exportWord.exportTitle('调仓效果'),
						...this.$exportWord.exportChart(canvas.toDataURL('image/jpg'), {
							width,
							height
						})
					];
					resolve(print_word);
				});
			});
		}
	}
};
</script>

<style></style>
