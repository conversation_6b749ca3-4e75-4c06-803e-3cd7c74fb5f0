export default [
	{
		path: '/poolnormal',
		component: () => import(/* webpackChunkName: "pool" */ '../../pages/fundNewPool/manage/index.vue'),
		meta: { title: '基金池', tagShow: false }
	},
	{
		path: '/poolcompare',
		component: () => import(/* webpackChunkName: "pool" */ '../../pages/fundNewPool/manage/index.vue'),
		meta: { title: '对标池', tagShow: false }
	},
	{
		path: '/poolDetail/:id',
		component: () => import(/* webpackChunkName: "pool" */ '../../pages/fundNewPool/analysis/index.vue'),
		meta: { title: '池详情', tagShow: false, keepAlive: true }
	},
	{
		path: '/managerpoolnormal',
		component: () => import(/* webpackChunkName: "pool" */ '../../pages/fundNewPool/manage/index.vue'),
		meta: { title: '基金经理池', tagShow: false }
	},
	{
		path: '/managerpoolcompare',
		component: () => import(/* webpackChunkName: "pool" */ '../../pages/fundNewPool/manage/index.vue'),
		meta: { title: '基金经理对标池', tagShow: false }
	},
	{
		path: '/managerpoolDetail/:id',
		component: () => import(/* webpackChunkName: "pool" */ '../../pages/fundNewPool/analysis/index.vue'),
		meta: { title: '基金经理池详情', tagShow: false, keepAlive: true }
	}
];
