<template>
	<div class="analysis_main">
		<header-info @updatePortOut="updatePortOut" ref="headerInfo" @getFundList="getFundList"></header-info>
		<div style="display: flex; justify-content: space-between; align-items: center" class="analysis_menu">
			<el-menu :default-active="activeIndex" class="el-menu-demo type_menu" mode="horizontal" @select="handleSelect">
				<el-menu-item v-for="item in activeComponentsList" :key="item.key" :index="item.key">{{ item.label }}</el-menu-item>
			</el-menu>
			<div style="margin-right: 12px; display: flex; align-items: center">
				<span
					style="
						font-family: 'PingFang';
						font-style: normal;
						font-weight: 400;
						font-size: 14px;
						line-height: 22px;
						color: rgba(0, 0, 0, 0.85);
						margin-right: 8px;
					"
				>
					基准搜索:
				</span>
				<search-components type="index" @resolveFather="getIndexInfo"></search-components>
				<el-button type="primary" id="printWord" @click="print">打印word</el-button>
			</div>
		</div>
		<div class="line"></div>
		<div class="main">
			<div v-for="item in activeComponentsList" :key="item.key" v-show="activeIndex == item.key || printActive == true">
				<component
					:is="item.key"
					:ref="item.key"
					:list="list"
					@overRequest="overRequest"
					:indexInfo="indexInfo"
					:active="activeIndex"
				></component>
			</div>
		</div>
	</div>
</template>

<script>
import headerInfo from './components/header.vue';
import onePagePass from './components/onePagePass';
import performance from './components/performance';
import assetAllocationAnalysis from './components/assetAllocationAnalysis';
import searchComponents from '@/components/components/components/search/index.vue';

import { combinationComponentsList } from '@/utils/componentsClass';
export default {
	components: { headerInfo, onePagePass, performance, assetAllocationAnalysis, searchComponents },
	data() {
		return {
			activeIndex: 'onePagePass',
			activeComponentsList: [],
			info: {
				code: '53',
				name: '组合',
				type: 'portfolio',
				classType: 'portfolio',
				flag: 4
			},
			indexInfo: {
				id: '000300.SH',
				name: '沪深300',
				flag: 'index'
			},
			list: [],
			printActive: false
		};
	},
	beforeRouteEnter(to, from, next) {
		next();
		if (from.path !== to.path && from.path !== '/') {
			location.reload();
		}
	},
	mounted() {
		this.init();
	},
	watch: {
		//监听相同路由下参数变化的时候，从而解决当跳转到同页面不刷新问题
		$route(to, from) {
			this.init();
		}
	},
	methods: {
		updatePortOut() {
			this.init();
		},
		init() {
			this.info = { ...this.info, code: this.$route.query.id, name: this.$route.query.name };
			this.activeComponentsList = combinationComponentsList;
			this.$nextTick(() => {
				this.$refs['headerInfo']?.getData(this.info);
			});
		},
		filterTemplateList(key) {
			return this.activeComponentsList.filter((item) => {
				return item.key == key;
			})?.[0]?.templateList;
		},
		// 监听menu的切换
		handleSelect(key) {
			if (this.activeIndex == key) {
				return;
			}
			this.activeIndex = key;
			this.getData();
		},
		getIndexInfo(info) {
			this.indexInfo = info;
		},
		getData() {
			switch (this.activeIndex) {
				case 'onePagePass':
					this.$refs['onePagePass']?.[0].getTemplateList(this.filterTemplateList('onePagePass'));
					this.$refs['onePagePass']?.[0].getData(this.info);
					break;
				case 'performance':
					this.$refs['performance']?.[0].getTemplateList(this.filterTemplateList('performance'));
					this.$refs['performance']?.[0].getData(this.info);
					break;
				case 'assetAllocationAnalysis':
					this.$refs['assetAllocationAnalysis']?.[0].getTemplateList(this.filterTemplateList('assetAllocationAnalysis'));
					this.$refs['assetAllocationAnalysis']?.[0].getData(this.info);
					break;
				default:
					break;
			}
		},
		// 获取组合内持仓基金列表
		getFundList(list) {
			this.list = list;
			this.getData();
		},
		// 等待组件数据请求完毕
		overRequest(components) {
			if (this.printActive) {
				let index = this.overComponents.findIndex((item) => {
					return item == components;
				});
				this.overComponents.splice(index, 1);
				// this.downloadWord();
				if (this.overComponents.length == 0) {
					this.downloadWord();
					this.overComponents = this.activeComponentsList
						.map((item) => {
							if (this.$refs[item.key]?.[0].createPrintWord) {
								return item.key;
							}
						})
						.filter((item) => {
							return item !== undefined;
						});
				}
			}
		},
		// 执行word导出
		downloadWord() {
			this.$nextTick(() => {
				setTimeout(async () => {
					let header = await this.$refs['headerInfo'].createPrintWord();
					let current = 2;
					let downloadList = [...header];
					this.activeComponentsList.map((item) => {
						if (this.$refs[item.key]?.[0].createPrintWord) {
							if (item.key !== 'onePagePass') {
								switch (current) {
									case 2:
										downloadList.push(...this.$exportWord.exportFirstTitle('二、' + item.label));
										break;
									case 3:
										downloadList.push(...this.$exportWord.exportFirstTitle('三、' + item.label));
										break;
									case 4:
										downloadList.push(...this.$exportWord.exportFirstTitle('四、' + item.label));
										break;
									case 5:
										downloadList.push(...this.$exportWord.exportFirstTitle('五、' + item.label));
										break;
									case 6:
										downloadList.push(...this.$exportWord.exportFirstTitle('六、' + item.label));
										break;
									case 7:
										downloadList.push(...this.$exportWord.exportFirstTitle('七、' + item.label));
										break;
									case 8:
										downloadList.push(...this.$exportWord.exportFirstTitle('八、' + item.label));
										break;
									case 9:
										downloadList.push(...this.$exportWord.exportFirstTitle('九、' + item.label));
										break;
									case 10:
										downloadList.push(...this.$exportWord.exportFirstTitle('十、' + item.label));
										break;
								}
								current = current + 1;
							}

							downloadList.push(...this.$refs[item.key]?.[0].createPrintWord());
						}
					});

					let imgType = 'image/png';
					var xhr = new XMLHttpRequest();
					xhr.responseType = 'arraybuffer';
					xhr.open('GET', 'https://cdn.owl-portfolio.com/img/logoForWord.png', true);
					xhr.onload = () => {
						var result = xhr.response;
						var file = new File([result], 'foo.' + imgType.match(/\/([A-Za-z]+)/)[1], {
							type: imgType
						});
						var reader = new FileReader();
						reader.onload = (evt) => {
							// callBack(evt.target.result);
							this.$exportWord.downloadWord(this.info.name, [...downloadList], evt.target.result);
							this.loading.close();
							this.printActive = false;
						};
						reader.readAsDataURL(file);
					};
					xhr.send(null);
				}, 2000);
			});
		},
		// 点击打印
		async print() {
			this.loading = this.$loading({
				lock: true,
				text: '正在生成word报告,请稍等...',
				spinner: 'el-icon-loading',
				background: 'rgba(0, 0, 0, 0.7)'
			});
			this.printActive = true;
			let that = this;
			if (this.printTimeOut) {
				this.printTimeOut = null;
				clearTimeout(this.printTimeOut);
			}
			// 一分钟超时中断
			this.printTimeOut = setTimeout(() => {
				if (that.printActive) {
					that.loading.close();
					that.printActive = false;
					that.$message.warning('打印超时,请检查网络后重试');
				}
			}, 60000);
			this.overComponents = this.activeComponentsList
				.map((item) => {
					if (this.$refs[item.key]?.[0].createPrintWord) {
						return item.key;
					}
				})
				.filter((item) => {
					return item !== undefined;
				});
			console.log(this.overComponents);
			this.overComponents.map((item) => {
				this.$refs[item]?.[0].getTemplateList(this.filterTemplateList(item));
				this.$refs[item]?.[0].getData(this.info);
			});
		}
	}
};
</script>

<style scoped>
.analysis_menu .el-menu.el-menu--horizontal {
	/* width: 100%; */
	border-bottom: none;
}
</style>
