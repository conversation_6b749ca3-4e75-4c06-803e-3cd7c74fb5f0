<!--  -->
<template>
  <div v-show="visible_newHolding">
    <div class="chart_one">
      <div style="display: flex; align-items: center; justify-content: space-between">
        <div class="title" style="margin-bottom: 24px">权益基金股票{{ newtime }}持仓分析</div>
        <el-button
          icon="el-icon-document-delete"
          style="margin-left: 16px"
          @click="exportExcel"
        >导出Excel</el-button>
      </div>
      <el-table
        :data="newholding"
        :default-sort="{ prop: 'weight', order: 'descending' }"
        ref="multipleTable"
        style="width: 99%"
        height="400px"
      >
        <el-table-column sortable prop="name" label="名称" align="gotoleft"></el-table-column>
        <el-table-column sortable prop="stock_code" label="代码" align="gotoleft"></el-table-column>
        <el-table-column sortable prop="flag" label="持有方式" align="gotoleft"></el-table-column>
        <el-table-column sortable prop="weight" label="占权益比" align="gotoleft">
          <template slot-scope="scope">{{ scope.row.weight | fixp }}</template>
        </el-table-column>
        <el-table-column sortable prop="pb" label="PB" align="gotoleft">
          <template slot-scope="scope">{{ scope.row.pb | fix3 }}</template>
        </el-table-column>
        <el-table-column sortable prop="pb_rank" label="PB 分位" align="gotoleft">
          <template slot-scope="scope">{{ scope.row.pb_rank | fix2p }}</template>
        </el-table-column>
        <el-table-column sortable prop="pe" label="PE" align="gotoleft">
          <template slot-scope="scope">{{ scope.row.pe | fix3 }}</template>
        </el-table-column>
        <el-table-column sortable prop="pe_rank" label="PE 分位" align="gotoleft">
          <template slot-scope="scope">{{ scope.row.pe_rank | fix2p }}</template>
        </el-table-column>
        <el-table-column
          sortable
          prop="swname"
          :show-overflow-tooltip="true"
          :width="300"
          label="行业"
          align="gotoleft"
        ></el-table-column>
      </el-table>
    </div>

    <div class="chart_one">
      <div class="flex_card">
        <div class="small_template" style="box-shadow: none">
          <el-table
            :data="industrypeizhi"
            :default-sort="{ prop: 'weight', order: 'descending' }"
            ref="multipleTable"
            height="215px"
            header-cell-class-name="table-header"
          >
            <el-table-column sortable prop="swname" label="行业" align="gotoleft"></el-table-column>
            <el-table-column sortable prop="weight" label="占已披露股票行业比" align="gotoleft">
              <template slot-scope="scope">{{ scope.row.weight | fixp }}</template>
            </el-table-column>
          </el-table>
        </div>
        <div class="small_template" style="box-shadow: none">
          <div style="display: flex">
            <!-- <el-switch v-model="model" active-text="未披露行业" @change="changeCharts"></el-switch> -->
            <div class="charts_center_class">
              <v-chart
                ref="equityStockPositionAnalysistype"
                v-loading="bondTopTenLoading"
                element-loading-text="暂无数据"
                element-loading-spinner="el-icon-document-delete"
                element-loading-background="rgba(239, 239, 239, 0.5)"
                class="charts_analysis_class"
                style="width: 300px; height: 300px"
                autoresize
                :options="optiontype"
                @click="clickPieChart"
              ></v-chart>
            </div>
            <div class="charts_center_class">
              <v-chart
                ref="equityStockPositionAnalysis"
                v-loading="emptybaogao"
                element-loading-text="暂无数据"
                element-loading-spinner="el-icon-document-delete"
                element-loading-background="rgba(239, 239, 239, 0.5)"
                class="charts_analysis_class"
                style="width: 300px; height: 300px"
                autoresize
                :options="optionbaogao"
              ></v-chart>
            </div>
          </div>
        </div>
        <!-- <div class="small_template" style="box-shadow: none">
					
        </div>-->
      </div>
    </div>
  </div>
</template>

<script>
import { exportTitle, exportChart, exportTable } from "@/utils/exportWord.js";
import { filter_json_to_excel } from "@/utils/exportExcel.js";
// 权益基金股票持仓分析
import VCharts from "vue-echarts";
export default {
  name: "equityStockPositionAnalysis",
  components: { "v-chart": VCharts },
  data() {
    //这里存放数据
    return {
      model: true,
      notesData: {
        cc001: ""
      },
      loadingitem: true,
      emptybaogao: true,
      temppicdata: null,
      pbroe: [],
      industrypeizhi: [],
      visible_newHolding: true,
      loadingpbroe: true,
      revflag: 0,
      newtime: "",
      newholding: [],
      newhold: [],
      data: [],
      optionbaogao: {},
      color: [
        "#4096ff",
        "#4096ff",
        "#7388A9",
        "#6F80DD",
        "#6C96F2",
        "#FD6865",
        "#83D6AE",
        "#88C9E9",
        "#ED589D",
        "#FA541C"
      ],
      optionData: {},
      optiontype: {},
      activeName: "",
      series: null
    };
  },
  filters: {
    fix2p(value) {
      return value * 100 ? (value * 100).toFixed(2) + "%" : value;
      // if (value == '--') return value;
      // else return (value * 100).toFixed(2) + '%';
    },
    fixY(value) {
      if (value == "--") return value;
      else {
        return (Number(value) / 100000000).toFixed(2) + "亿";
      }
    },

    fix3(value) {
      if (value == "--") {
        return value;
      } else return parseInt(value * 1000) / 1000;
    },
    fixp(value) {
      if (!Number(value)) return value;
      else {
        return Number(value).toFixed(2) + "%";
      }
    }
  },
  //方法集合
  methods: {
    // 点击饼图
    clickPieChart(e) {
      this.activeName = e?.name;
      this.setOption(this.optionData);
    },
    changeCharts() {
      this.setOption(this.optionData);
    },
    getData(data) {
      this.loadingitem = false;
      this.optionData = data;
      if (!this.FUNC.isValidObj(data.new_holdings) && !data.swname.length) {
        this.visible_newHolding = false;
      } else {
        this.visible_newHolding = true;
        this.emptybaogao = false;
        this.newholding = data.new_holdings;

        this.newtime = data.yearqtr;
        this.industrypeizhi = data.swname;
        this.series = this.formatData(data.swname);
        this.optiontype = this.returnPieChart(
          this.series?.map(item => {
            return { name: item.name, value: item.value };
          })
        );
        this.activeName = this.series?.[1]?.name;
        this.setOption();
      }
    },
    setOption() {
      this.optionbaogao = {
        color: this.color,
        tooltip: {
          trigger: "item",
          formatter: function(val) {
            return (
              val?.marker + " " + val?.name + " " + val?.value?.toFixed(2) + "%"
            );
          }
        },
        legend: {
          orient: "vertical",
          left: "right"
        },
        toolbox: {
          feature: {
            saveAsImage: { pixelRatio: 3 }
          },
          top: -4,
          width: 104
        },
        series: {
          type: "sunburst",
          radius: ["30%", "70%"],
          emphasis: {
            focus: "ancestor",
            label: {
              show: true,
              fontSize: "40",
              fontWeight: "bold"
            }
          },
          data: this.series?.filter(item => {
            return item.name == this.activeName;
          })?.[0]?.children,
          // radius: [0, '90%'],
          label: {
            show: true,
            rotate: "radial"
          }
        }
      };
    },
    returnPieChart(data) {
      return {
        color: this.color,
        tooltip: {
          trigger: "item",
          formatter: function(val) {
            return (
              val?.marker + " " + val?.name + " " + val?.value?.toFixed(2) + "%"
            );
          }
        },
        legend: {
          orient: "vertical",
          left: "left",
          type: "scroll",
          data: data
            ?.sort((a, b) => {
              return b.value - a.value;
            })
            ?.map(item => {
              return item.name;
            }),
          pageIcons: {
            horizontal: [
              "path://M11.7487 6.92214L6.30673 0.634973C6.15096 0.455009 5.85102 0.455009 5.69359 0.634973L0.251579 6.92214C0.049409 7.15658 0.231693 7.5 0.558148 7.5L11.4422 7.5C11.7686 7.5 11.9509 7.15658 11.7487 6.92214Z",
              "path://M0.251255 1.07786L5.69327 7.36503C5.84904 7.54499 6.14898 7.54499 6.30641 7.36503L11.7484 1.07786C11.9506 0.843416 11.7683 0.499999 11.4419 0.5L0.557824 0.5C0.231369 0.5 0.0490849 0.843417 0.251255 1.07786Z"
            ]
          }
        },
        toolbox: {
          feature: {
            saveAsImage: { pixelRatio: 3 }
          },
          top: -4,
          width: 104
        },
        series: {
          type: "pie",
          radius: ["30%", "70%"],
          center: ["63%", "50%"],
          emphasis: {
            focus: "ancestor",
            label: {
              show: true,
              fontSize: "14",
              fontWeight: "bold"
            }
          },
          data,
          // radius: [0, '90%'],
          label: {
            show: false,
            position: "center"
          }
        }
      };
    },
    // 格式化数据
    formatData(data) {
      let industryList = [];
      let all = 0;
      this.newholding.map(item => {
        all = all + item.weight;
      });
      data.map(item => {
        let swname = item.swname.split("-");
        industryList.push({
          sw1: swname?.[0],
          sw2: swname?.[1],
          sw3: swname?.[2],
          weight: item.weight * (all / 100)
        });
      });
      if (this.model) {
        industryList.unshift({
          sw1: "未披露行业",
          sw2: "未披露行业",
          sw3: "未披露行业",
          weight: 100 - all
        });
      }
      let series = [];
      industryList.map(item => {
        // 一级行业是否相同
        let sw1Index = series.findIndex(obj => {
          return obj.name == item.sw1;
        });
        if (sw1Index == -1) {
          series.push({
            name: item.sw1,
            itemStyle:
              item.sw1 == "未披露行业"
                ? {
                    color: "#BFBFBF"
                  }
                : {},
            children: [
              {
                name: item.sw2,
                itemStyle:
                  item.sw1 == "未披露行业"
                    ? {
                        color: "#BFBFBF"
                      }
                    : {},
                children: [
                  {
                    name: item.sw3,
                    itemStyle:
                      item.sw1 == "未披露行业"
                        ? {
                            color: "#BFBFBF"
                          }
                        : {},
                    value: item.weight
                  }
                ]
              }
            ]
          });
        } else {
          // 二级行业是否相同
          let sw2Index = series[sw1Index].children.findIndex(obj => {
            return obj.name == item.sw2;
          });
          if (sw2Index == -1) {
            series[sw1Index].children.push({
              name: item.sw2,
              children: [
                {
                  name: item.sw3,
                  label: { show: false },
                  value: item.weight
                }
              ]
            });
          } else {
            // 三级行业是否相同
            let sw3Index = series[sw1Index].children[
              sw2Index
            ].children.findIndex(obj => {
              return obj.name == item.sw3;
            });
            if (sw3Index == -1) {
              series[sw1Index].children[sw2Index].children.push({
                name: item.sw3,
                label: { show: false },
                value: item.weight
              });
            } else {
              series[sw1Index].children[sw2Index].children[sw3Index].value =
                series[sw1Index].children[sw2Index].children[sw3Index].value +
                item.weight;
            }
          }
        }
      });
      series = series?.map(item => {
        let all = 0;
        item?.children?.map(obj => {
          obj?.children?.map(val => {
            all = all + val.value * 1;
          });
        });
        let obj = {};
        obj["value"] = all;
        return {
          ...item,
          value: all
        };
      });
      return series;
    },
    hideLoading() {
      this.visible_newHolding = false;
    },
    exportExcel() {
      let list1 = [
        {
          label: "名称",
          value: "name"
        },
        {
          label: "代码",
          value: "stock_code"
        },
        {
          label: "持有方式",
          value: "flag"
        },
        {
          label: "占权益比",
          value: "weight",
          format: "fixp"
        },
        {
          label: "PB",
          value: "pb",
          format: "fix2"
        },
        {
          label: "PB 分位",
          value: "pb_rank",
          format: "fix2p"
        },
        {
          label: "PE",
          value: "pe",
          format: "fix2p"
        },
        {
          label: "PE 分位",
          value: "pe_rank",
          format: "fix2p"
        },
        {
          label: "行业",
          value: "swname"
        }
      ];
      filter_json_to_excel(list1, this.newholding, "权益基金股票持仓分析");
    },
    createPrintWord() {
      let list1 = [
        {
          label: "名称",
          value: "name"
        },
        {
          label: "代码",
          value: "stock_code"
        },
        {
          label: "持有方式",
          value: "flag"
        },
        {
          label: "占权益比",
          value: "weight",
          format: "fix2b"
        },
        {
          label: "PB",
          value: "pb",
          format: "fix2"
        },
        {
          label: "PB 分位",
          value: "pb_rank",
          format: "fix2p"
        },
        {
          label: "PE",
          value: "pe",
          format: "fix2p"
        },
        {
          label: "PE 分位",
          value: "pe_rank",
          format: "fix2p"
        },
        {
          label: "行业",
          value: "swname"
        }
      ];
      let list2 = [
        {
          label: "行业",
          value: "swname"
        },
        {
          label: "占含股基金已披露行业比",
          value: "weight",
          format: "fix2b"
        }
      ];
      this.$refs["equityStockPositionAnalysistype"].mergeOptions({
        toolbox: { show: false }
      });
      let height = this.$refs["equityStockPositionAnalysistype"].$el
        .clientHeight;
      let width = this.$refs["equityStockPositionAnalysistype"].$el.clientWidth;
      let chart = this.$refs["equityStockPositionAnalysistype"].getDataURL({
        type: "png",
        pixelRatio: 2,
        backgroundColor: "#fff"
      });
      this.$refs["equityStockPositionAnalysistype"].mergeOptions({
        toolbox: { show: true }
      });
      if (this.newholding.length && this.industrypeizhi.length) {
        return [
          ...exportTitle("权益基金股票持仓分析"),
          ...exportTable(
            list1,
            this.newholding
              .sort((a, b) => {
                return b.weight - a.weight;
              })
              .slice(0, 20),
            "",
            true
          ),
          ...exportTitle("权益基金股票行业占比分析"),
          ...exportTable(
            list2,
            this.industrypeizhi
              .sort((a, b) => {
                return b.weight - a.weight;
              })
              .slice(0, 20),
            "",
            true
          ),
          ...exportChart(chart, { width, height })
        ];
      } else {
        return [];
      }
    }
  }
};
</script>
<style>
.tablex {
  height: 400px !important;
}
.echalf {
  width: 100% !important;
  height: 400px !important;
}
</style>
