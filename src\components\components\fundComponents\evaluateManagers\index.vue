<template>
	<div v-loading="loadiung" class="noTableBorder">
		<analysis-card-title title="基金经理详情">
			<span v-for="(item, index) in Managerdata" :key="index" style="margin-right: 10px"
				><span
					@click="changge(index)"
					:style="
						active == index
							? 'font-size:16px;font-weight:600;margin-right5px;color:#4096ff;cursor:pointer'
							: ';margin-right:5px;color:rgba(0,0,0,0.45);cursor:pointer'
					"
					>{{ item.name }}&nbsp;<span v-if="active == index">{{ size }}</span></span
				></span
			>
		</analysis-card-title>
		<el-table :data="table3" height="300px">
			<el-table-column prop="name" :show-overflow-tooltip="true" align="gotoleft" label="基金名称">
				<template slot-scope="scope">
					<div style="color: #4096ff; cursor: pointer" @click="gotoDetail(scope.row.code, scope.row.name)">{{ scope.row.name }}</div>
				</template>
			</el-table-column>
			<el-table-column prop="type" :show-overflow-tooltip="true" sortable align="gotoleft" label="类型">
				<template slot-scope="scope">
					<div>{{ scope.row.type }}</div>
				</template>
			</el-table-column>
			<el-table-column prop="netasset" sortable align="gotoleft" label="规模">
				<template slot-scope="scope">
					<div>{{ scope.row.netasset | fixY }}</div>
				</template>
			</el-table-column>
			<el-table-column prop="ave_return" sortable align="gotoleft">
				<template slot="header">
					<div style="text-align: left">
						<div style="display: flex; align-items: center">
							年化收益率<el-tooltip
								class="item"
								effect="dark"
								content="计算时间范围为近三年，若管理不足三年则以管理开始日期计算"
								placement="right-start"
								><svg width="14" height="14" viewBox="0 0 14 14" fill="none">
									<path
										fill-rule="evenodd"
										clip-rule="evenodd"
										d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
										fill="black"
										fill-opacity="0.45"
									/>
								</svg>
							</el-tooltip>
						</div>
						<!-- <div style="color:#a6a6a6">(近三年)</div> -->
					</div>
				</template>
				<template slot-scope="scope">
					<div>{{ scope.row.ave_return | fix2p }}</div>
				</template>
			</el-table-column>
			<el-table-column prop="ave_vol" sortable align="gotoleft">
				<template slot="header">
					<div style="text-align: left">
						<div style="display: flex; align-items: center">
							年化波动率<el-tooltip
								class="item"
								effect="dark"
								content="计算时间范围为近三年，若管理不足三年则以管理开始日期计算"
								placement="right-start"
								><svg width="14" height="14" viewBox="0 0 14 14" fill="none">
									<path
										fill-rule="evenodd"
										clip-rule="evenodd"
										d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
										fill="black"
										fill-opacity="0.45"
									/>
								</svg>
							</el-tooltip>
						</div>
						<!-- <div style="color:#a6a6a6">(近三年)</div> -->
					</div>
				</template>
				<template slot-scope="scope">
					<div>{{ scope.row.volatility | fix2p }}</div>
				</template>
			</el-table-column>
		</el-table>
	</div>
</template>

<script>
import { alphaGo } from '@/assets/js/alpha_type.js';

// 基金经理评价表格数据
import { getEvaluateManagers, getBasicInfo } from '@/api/pages/Analysis.js';

export default {
	name: 'monetaryCycle',
	data() {
		return {
			table3: [],
			Managerdata: [],
			active: 0,
			size: '',
			loadiung: true,
			info: {}
		};
	},
	filters: {
		fix3(value) {
			return parseInt(value * 1000) / 1000;
		},
		fixY(value) {
			if (value && value != '' && value != '--' && value != '- -' && value != 'nan') return (value * 1).toFixed(2) + '亿';
			else return '--';
		},
		fix2p(value) {
			return (value * 100).toFixed(2) + '%';
		}
	},
	methods: {
		gotoDetail(id, name) {
			alphaGo(id, name, this.$route.path);
		},
		// 获取基金经理评价表格数据
		async getEvaluateManagers() {
			let data = await getEvaluateManagers({
				code: this.Managerdata[this.active].code,
				flag: 2,
				type: this.info.type,
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			this.loadiung = false;
			if (data?.mtycode == 200) {
				// this.size = Number(data?.data?.size).toFixed(2) + '亿';
				this.table3 = data?.data;
			}
		},
		// 获取基金经理code
		async getBasicInfo() {
			let data = await getBasicInfo({
				code: this.info.code,
				type: this.info.type,
				flag: this.info.flag,
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			if (data?.mtycode == 200) {
				let name = data?.data?.manager_name.split(',');
				let code = data?.data?.manager_code.split(',');
				this.Managerdata = name.map((v, i) => {
					return { name: v, code: code[i] };
				});
				this.active = 0;
			}
			this.getEvaluateManagers();
		},
		// 获取父组件传递数据
		getData(info) {
			this.info = info;
			this.getBasicInfo();
		},
		changge(data) {
			this.loadiung = true;
			this.size = '';
			this.active = data;
			this.getEvaluateManagers();
		}
	}
};
</script>

<style></style>
