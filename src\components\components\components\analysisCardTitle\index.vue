<template>
	<div class="py-14 card_top flex_between mb-20">
		<div class="flex_start">
			<div class="card_top_border mr-12"></div>
			<div class="title">{{ title }}</div>
		</div>
		<div class="flex_start">
			<slot></slot>
			<div @click="downloadImage" style="height: 32px; cursor: pointer" class="ml-12">
				<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
					<rect x="0.5" y="0.5" width="31" height="31" rx="3.5" fill="white" />
					<path
						d="M15.8874 18.6575C15.9007 18.6746 15.9178 18.6884 15.9373 18.6979C15.9568 18.7074 15.9782 18.7123 15.9999 18.7123C16.0215 18.7123 16.0429 18.7074 16.0624 18.6979C16.0819 18.6884 16.099 18.6746 16.1124 18.6575L18.1124 16.1271C18.1856 16.0343 18.1195 15.8968 17.9999 15.8968H16.6766V9.85392C16.6766 9.77535 16.6124 9.71106 16.5338 9.71106H15.4624C15.3838 9.71106 15.3195 9.77535 15.3195 9.85392V15.895H13.9999C13.8802 15.895 13.8141 16.0325 13.8874 16.1253L15.8874 18.6575ZM22.5356 18.0325H21.4641C21.3856 18.0325 21.3213 18.0968 21.3213 18.1753V20.9253H10.6784V18.1753C10.6784 18.0968 10.6141 18.0325 10.5356 18.0325H9.46415C9.38557 18.0325 9.32129 18.0968 9.32129 18.1753V21.7111C9.32129 22.0271 9.57665 22.2825 9.89272 22.2825H22.107C22.4231 22.2825 22.6784 22.0271 22.6784 21.7111V18.1753C22.6784 18.0968 22.6141 18.0325 22.5356 18.0325Z"
						fill="black"
						fill-opacity="0.45"
					/>
					<rect x="0.5" y="0.5" width="31" height="31" rx="3.5" stroke="#D9D9D9" />
				</svg>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		title: { type: String, default: '' },
		image_id: { type: String, default: '' }
	},
	methods: {
		downloadImage() {
			if (this.image_id) {
				this.exportImage(this.image_id, this.title);
			} else {
				this.$emit('downloadExcel');
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.card_top {
	border-bottom: 2px solid #e9e9e9;
	::v-deep.el-input {
		width: 176px;
	}
	.card_top_border {
		width: 6px;
		height: 20px;
		border-radius: 35px;
		background: #4096ff;
		.title {
			color: rgba(0, 0, 0, 0.85);
			font-family: PingFang SC;
			font-size: 18px;
			font-style: normal;
			font-weight: 500;
		}
	}
}
</style>
