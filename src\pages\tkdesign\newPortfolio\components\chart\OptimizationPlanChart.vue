<template>
    <div v-show="show">
      <div style="page-break-inside: avoid; position: relative">
        <div v-loading="industryrequestflag">
          <div style="page-break-inside: avoid; position: relative">
            <div class="charts_fill_class">
              <v-chart v-show="!industryrequestflag"
                       class="charts_one_class"
                       ref="ValuationPercentileChart"
                       autoresize
                       v-loading="industryrequestflag"
                       :options="industryoption"
                       @legendselectchanged="legendselectchanged" />
              <el-empty v-show="industryrequestflag"
                        description="暂无数据"></el-empty>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script>
  // 行业评价
  import VChart from 'vue-echarts';
  import { lineChartOption } from '../../../components/chart/chartStyle.js'
  export default {
    name: 'ValuationPercentileChart',
    components: {
      VChart
    },
    data () {
      let legendName={
                'medicine':{name:'医药',key:'medicine'} ,
                'hushen300':{name:'沪深300',key:'hushen300'},
        }
      return {
        industryrequestflag: true,
        show: true,
        info: {},
        legendName : legendName,
        industryoption:null,
        selected:{
        },
      };
    },
    methods: {
      // 获取父组件传递数据
      getData (data, info) {
        this.show = true;
        this.info = info;
        this.industryrequestflag = false;
        this.industryoption=this.getIndustryoption(data,info);
      },
      getIndustryoption(chartData,info={}){
        let data = chartData;
        let currentLegend=[]
          currentLegend=[
            { name: this.legendName.medicine.name, icon: 'line' },
            { name: this.legendName.hushen300.name, icon: 'line' },
          ]
        return lineChartOption({
          toolbox:'none',
          color: ['#4096ff', '#4096ff', '#7388A9', '#7388A9','#389E0D'],
          legend: {
           selected:this.selected,
           data:[...currentLegend]
          },
          tooltip: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          },
          xAxis: [
            {
              data: data.map((item) => {
                return item.time;
              }),
              isAlign: true,
              axisLabelRotate: -45
            }
          ],
          yAxis: [
            {
              type: 'value',
              formatter (value) {
                return value + '%';
              }
            },
            {
              type: 'value',
              splitLine: false
            }
          ],
          series: [
            {
              name: this.legendName.medicine.name,
              type: 'line',
              symbol:'none',
              data: data.map((item) => {
                return item[this.legendName.medicine.key]||'--'
              })
            },
            {
              name: this.legendName.hushen300.name,
              type: 'line',
              symbol:'none',
              areaStyle:{},
              lineStyle:{
                opacity:0
             },
              data: data.map((item) => {
                //
                //todo: typeLegendName
                return item[this.legendName.hushen300.key]||'--'
              })
            }
          ]
        })
      },
      // 数据获取失败
      hideLoading () {
        this.industryrequestflag = false;
        this.show = false;
      },
    }
  };
  </script>
  
  <style></style>
  