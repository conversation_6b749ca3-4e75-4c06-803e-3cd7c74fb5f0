<!--  -->
<template>
  <div v-loading="loading"
       class="holdindustry">
    <div style="display: flex; align-items: center; width: 100%; position: relative">
      <div style="display: flex; align-items: center">
        <div class="TitltCompare">因子暴露</div>
      </div>
    </div>
    <div>
      <div style="page-break-inside: avoid; margin-top: 16px">
        <v-chart ref="barraAvg"
                 v-loading="empty1"
                 element-loading-text="暂无数据"
                 element-loading-spinner="el-icon-document-delete"
                 element-loading-background="rgba(239, 239, 239, 0.5)"
                 style="page-break-inside: avoid; width: 100%; height: 400px"
                 :options="optionpbroe"></v-chart>
      </div>
      <div style="page-break-inside: avoid; margin-top: 16px">
        <v-chart ref="barraNew"
                 v-loading="empty2"
                 element-loading-text="暂无数据"
                 element-loading-spinner="el-icon-document-delete"
                 element-loading-background="rgba(239, 239, 239, 0.5)"
                 style="page-break-inside: avoid; width: 100%; height: 400px"
                 :options="optionpbroe2"></v-chart>
      </div>
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { BondFundIndexFactor } from '@/api/pages/tools/compare.js';
import VCharts from 'vue-echarts';
export default {
  //import引入的组件需要注入到对象中才能使用
  components: { 'v-chart': VCharts },
  props: {
    comparetype: {
      type: String,
      default: 'manager' //fund
    },
    id: {
      type: String,
      default: '30189741,30441407'
    },
    type: {
      type: String,
      default: 'equity'
    },
    name: {
      type: String,
      default: '萧楠,胡昕炜'
    }
  },
  filters: {
    fix3 (value) {
      if (value == '--' || value == null || value == '') {
        return value;
      } else {
        return (value * 100).toFixed(2) + '%';
      }
    },
    fix2 (value) {
      return Number(value).toFixed(2) + '亿';
    }
  },
  data () {
    //这里存放数据
    return {
      dicbarr: [
        { value: 'beta', label: '贝塔因子' },
        { value: 'bp', label: '估值因子' },
        { value: 'earningyield', label: '盈利因子' },
        { value: 'growth', label: '成长因子' },
        { value: 'leverage', label: '杠杆因子' },
        { value: 'liquidity', label: '流动性因子' },
        { value: 'momentum', label: '动量因子' },
        { value: 'residualvolatility', label: '残差波动性' },
        { value: 'size', label: '市值因子' }
      ],
      optionpbroe: {},
      optionpbroe2: {},
      empty1: false,
      empty2: false,
      loading: false
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    getdata () {
      Object.assign(this.$data, this.$options.data());
      this.loading = true;
      this.empty1 = false;
      this.empty2 = false;
      if (this.comparetype == 'manager') {
        this.getmanager();
      } else {
        this.gefunddata();
      }
    },
    async getmanager (val) {
      //  let data = await BondFundIndexFactor({manager_code:this.id,type:this.type,manager_name:this.name,flag:this.radio,yearqtr:val})
      //  if(data){
      //  }
    },
    async gefunddata () {
      let data = await BondFundIndexFactor({ fund_code: this.id, type: this.type, fund_name: this.name });
      this.loading = false;
      if (data) {
        if (JSON.stringify(data.data) == '{}' || JSON.stringify(data.data) == '[]' || JSON.stringify(data.data) == '') {
          this.empty1 = true;
          this.empty2 = true;
        } else {
          let seriess = [];
          let tempa2 = [];

          for (let j = 0; j < data.data.ave.length; j++) {
            seriess.push({
              name: data.data.ave[j].fund_name,
              type: 'bar',
              label: {
                show: true,
                formatter: '{b}'
              },
              data: []
            });
            for (let k = 0; k < this.dicbarr.length; k++) {
              seriess[j].data.push([Number(data.data.ave[j][this.dicbarr[k].value]).toFixed(2), this.dicbarr[k].label]);
            }
          }
          for (let k = 0; k < this.dicbarr.length; k++) {
            tempa2.push(this.dicbarr[k].label);
          }
          this.optionpbroe = {
            color: ['#4096ff', '#4096ff', '#7388A9', '#6F80DD', '#4096FF', '#929694', '#f4d1ff', '#e91e63', '#64dd17'],
            title: {
              text: '平均因子暴露'
            },
            // tooltip: {
            // 	textStyle: {
            // 		fontSize: 14
            // 	},
            // 	trigger: 'axis',
            // 	axisPointer: {
            // 		// 坐标轴指示器，坐标轴触发有效
            // 		type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
            // 	}
            // },
            grid: {
              top: '90px',
              left: '2%',
              right: '2%',
              bottom: '2%'
            },
            legend: {
              top: '20px'
            },
            xAxis: {
              nameTextStyle: {
                fontSize: 14
              },
              axisLabel: {
                show: true,
                textStyle: {
                  fontSize: 14
                }
              },
              type: 'value',
              position: 'top',
              splitLine: {
                lineStyle: {
                  type: 'dashed'
                }
              }
            },
            yAxis: {
              type: 'category',
              axisLine: {
                show: false
              },
              axisLabel: {
                show: false
              },
              axisTick: {
                show: false
              },
              splitLine: {
                show: false
              },
              data: tempa2
            },
            series: seriess
          };
          let tempa = [];
          let seriess2 = [];
          for (let j = 0; j < data.data.now.length; j++) {
            seriess2.push({
              name: data.data.now[j].fund_name,
              type: 'bar',
              label: {
                show: true,
                formatter: '{b}'
              },
              data: []
            });
            for (let k = 0; k < this.dicbarr.length; k++) {
              seriess2[j].data.push([Number(data.data.now[j][this.dicbarr[k].value]).toFixed(2), this.dicbarr[k].label]);
            }
          }
          for (let k = 0; k < this.dicbarr.length; k++) {
            tempa.push(this.dicbarr[k].label);
          }
          this.optionpbroe2 = {
            color: ['#4096ff', '#4096FF', '#929694', '#f4d1ff', '#e91e63', '#64dd17'],
            title: {
              text: '最新因子暴露'
            },
            // tooltip: {
            // 	textStyle: {
            // 		fontSize: 14
            // 	},
            // 	trigger: 'axis',
            // 	axisPointer: {
            // 		// 坐标轴指示器，坐标轴触发有效
            // 		type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
            // 	}
            // },
            grid: {
              top: '12%',
              left: '2%',
              right: '2%',
              bottom: '2%'
            },
            legend: {},
            // visualMap: {
            // 	show: false,
            // 	orient: 'horizontal',
            // 	left: 'center',
            // 	min: min,
            // 	max: max,
            // 	text: ['High Score', 'Low Score'],
            // 	// Map the score column to color
            // 	dimension: 0,
            // 	inRange: {
            // 		color: ['#D7DA8B', '#E15457']
            // 	}
            // },
            xAxis: {
              nameTextStyle: {
                fontSize: 14
              },
              axisLabel: {
                show: true,
                textStyle: {
                  fontSize: 14
                }
              },
              type: 'value',
              position: 'top',
              splitLine: {
                lineStyle: {
                  type: 'dashed'
                }
              }
            },
            yAxis: {
              type: 'category',
              axisLine: {
                show: false
              },
              axisLabel: {
                show: false
              },
              axisTick: {
                show: false
              },
              splitLine: {
                show: false
              },
              data: tempa
            },
            series: seriess2
          };
        }
      } else {
        this.empty1 = true;
        this.empty2 = true;
      }
    },
    createPrintWord () {
      let height1 = this.$refs['barraAvg']?.$el.clientHeight;
      let width1 = this.$refs['barraAvg']?.$el.clientWidth;
      let chart1 = this.$refs['barraAvg'].getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      });
      let height2 = this.$refs['barraNew']?.$el.clientHeight;
      let width2 = this.$refs['barraNew']?.$el.clientWidth;
      let chart2 = this.$refs['barraNew'].getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      });
      return [
        ...this.$exportWord.exportTitle('因子暴露'),
        ...this.$exportWord.exportChart(chart1, { width: width1, height: height1 }),
        ...this.$exportWord.exportChart(chart2, { width: width2, height: height2 })
      ];
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () { },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
</style>
<style>
.holdindustry .el-input__inner {
	/* padding-left: 30px !important; */
}
</style>
