<template>
	<div style="width: 100%; position: relative; page-break-inside: avoid" id="performanceCapabilityEvaluation">
		<analysis-card-title title="业绩评价" image_id="performanceCapabilityEvaluation"></analysis-card-title>
		<div v-loading="loading" class="flex_start fund-performance-container">
			<!-- 雷达图部分 -->
			<div class="charts_center_class" style="flex: 1" v-loading="loading">
				<v-chart
					style="width: 100%; height: 300px"
					ref="barraReturnLine"
					autoresize
					v-loading="loading"
					element-loading-text="暂无数据"
					element-loading-spinner="el-icon-document-delete"
					element-loading-background="rgba(239, 239, 239, 0.5)"
					:options="option"
				/>
			</div>
			<!-- 表格部分 -->
			<div class="indicators-table" style="flex: 1">
				<el-table :data="list" style="width: 100%">
					<el-table-column prop="name" label="指标" width="120px" align="center"> </el-table-column>
					<el-table-column prop="content" label="描述" align="gotoleft"> </el-table-column>
				</el-table>
			</div>
		</div>
	</div>
</template>

<script>
import { getCapabilityInfo } from '@/api/pages/Analysis.js';
// 业绩与能力评价
export default {
	name: 'performancethis',
	data() {
		return {
			yeji_quanshishouyi: '',
			yeji_shouyiwen: '',
			yeji_shouyi: '',
			yeji_zeshi: '',
			yeji_fengkong: '',
			yeji_quanshihuiche: '',
			yeji_dongtai: '',
			show: true,
			loading: true,
			list: [],
			all_list: [
				{
					name: '近期收益',
					key: '收益能力',
					flag: 1,
					content: this.EXPLAIN.performance['近期收益']
				},
				{
					name: '成立以来收益',
					key: '全时收益',
					flag: 1,
					content: this.EXPLAIN.performance['管理收益']
				},
				{
					name: '管理以来收益',
					key: '全时收益',
					flag: 2,
					content: this.EXPLAIN.performance['管理收益']
				},
				{
					name: '胜率稳定性',
					key: '胜率稳定性',
					flag: 1,
					content: this.EXPLAIN.performance['胜率稳定性']
				},
				{
					name: '最大回撤',
					key: '全时回撤',
					flag: 1,
					content: this.EXPLAIN.performance['管理回撤']
				}
				// { name: '风控', key: 'yeji_fengkong', content: this.EXPLAIN.performance['风控'] }
				// {
				// 	name: '择时',
				// 	key: 'yeji_zeshi',
				// 	content: this.EXPLAIN.performance['择时']
				// },
				// { name: '信用调节', key: 'yeji_xinyong', content: this.EXPLAIN.performance['信用调节'] },
				// { name: '久期调节', key: 'yeji_jiuqi', content: this.EXPLAIN.performance['久期调节'] }
			],
			performanceData: {},
			activeList: [],
			info: {},
			data: [],
			option: {}
		};
	},
	methods: {
		openvideo() {
			window.open('https://www.bilibili.com/video/BV1pS4y1J76r?share_source=copy_web');
		},
		async getCapabilityInfo() {
			let data = await getCapabilityInfo({
				codes: [this.info.code],
				type: this.info.type,
				flag: [this.info.flag],
				start_date: this.info.start_date,
				end_date: this.info.end_date,
				item: ['收益能力', '全时收益', '胜率稳定性', '全时回撤']
			});
			this.loading = false;
			if (data?.mtycode == 200) {
				this.data = [];
				this.list.map((item) => {
					let index = data?.data?.findIndex((v) => v.item == item.key);
					if (index != -1) {
						this.data.push({ ...item, ...data?.data[index] });
					} else {
						this.data.push({ ...item, description: '暂无数据', relRank: '--' });
					}
				});
				this.updateChart();
			}
		},
		updateChart() {
			// 将数据转换为雷达图所需格式
			const indicators = this.list.map((item) => {
				return { name: item.name, max: 100 };
			});
			const fundValues = this.data.map((item) => {
				// 将relRank转换为0-100的值
				return item.relRank && item.relRank !== '--' ? Math.round(item.relRank * 100) : 50;
			});
			// 同类平均值 - 实际应用中应该使用从API获取的数据
			const avgValues = this.data.map(() => 50); // 假设同类平均值为50

			this.option = {
				grid: { top: 0, left: 0, bottom: 0, right: 0 },
				color: ['#3A7BBE', '#91CC75'],
				legend: {},
				legend: {
					data: [this.info.name, '同类平均'],
					itemStyle: {
						decal: { symbol: 'circle' }
					}
					// left: 'right'
				},
				radar: {
					indicator: indicators,
					radius: '65%',
					splitNumber: 4,
					axisName: {
						color: '#333',
						fontSize: 12
					},
					splitArea: {
						areaStyle: {
							color: ['#F5F5F5', '#E8E8E8', '#DDDDDD', '#D0D0D0'],
							shadowColor: 'rgba(0, 0, 0, 0.05)',
							shadowBlur: 10
						}
					},
					axisLine: {
						lineStyle: {
							color: 'rgba(0, 0, 0, 0.2)'
						}
					},
					splitLine: {
						lineStyle: {
							color: 'rgba(0, 0, 0, 0.1)'
						}
					}
				},
				series: [
					{
						name: this.info.name,
						type: 'radar',
						data: [
							{
								value: fundValues,
								name: this.info.name,
								areaStyle: {
									color: 'rgba(58, 123, 190, 0.6)'
								},
								lineStyle: {
									width: 2
								},
								symbolSize: 6
							}
						]
					},
					{
						name: '同类平均',
						type: 'radar',
						data: [
							{
								value: avgValues,
								name: '同类平均',
								areaStyle: {
									color: 'rgba(145, 204, 117, 0.4)'
								},
								lineStyle: {
									width: 2,
									type: 'dashed'
								},
								symbolSize: 6
							}
						]
					}
				]
			};
		},
		async getData(info) {
			this.info = info;
			this.list = this.all_list
				.filter((v) => v.flag == this.info.flag)
				.map((v) => {
					return { ...v, content: v.content[v.flag == 1 ? 'fund' : 'manager'] };
				});
			await this.getCapabilityInfo();
		},
		async createPrintWord(info) {
			await this.getData(info);
			return await new Promise((resolve, reject) => {
				this.$nextTick(async () => {
					if (this.list.length) {
						let list = [
							{ label: '能力项', value: 'name', size: 20 },
							{ label: '能力项评价', value: 'value', size: 20 },
							{ label: '能力项注释', value: 'descripe', size: 60 }
						];
						let data = this.list.map((item) => {
							return {
								name: item.name,
								value: this.performanceData[item.value],
								descripe: item.content
							};
						});
						resolve([...this.$exportWord.exportTitle('业绩评价'), ...this.$exportWord.exportTable(list, data)]);
					} else {
						resolve([]);
					}
				});
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.fund-performance-container {
	width: 100%;
	.indicators-table {
		::v-deep .cell {
			color: rgba(0, 0, 0, 0.85);
			font-family: PingFang SC;
			font-size: 14px;
			font-style: normal;
			font-weight: 400;
		}
	}
}
.evaluation_item {
	flex: 1;
	width: 100%;
	max-width: 392px;
	min-width: 285px;
	height: 150px;
	// border-bottom: 1px solid #D9D9D9;
	background: linear-gradient(180deg, #ecf5ff 0%, rgba(255, 145, 3, 0) 100%);
	padding: 0 20px 0 0;
	.evaluation_description {
		position: relative;
		flex: 1;
		min-width: 180px;
		// max-width: 174px;
		height: 98px;
		.svg_bg_css {
			position: absolute;
			top: -8px;
		}
		.title_main_position {
			width: 100%;
			height: 100%;
			.item_name {
				color: rgba(0, 0, 0, 0.85);
				font-family: PingFang SC;
				font-size: 16px;
				font-style: normal;
				font-weight: 400;
			}
			.item_description {
				color: #4096ff;
				font-family: PingFang SC;
				font-size: 20px;
				font-style: normal;
				font-weight: 500;
			}
		}
	}

	.evaluation_item_rank {
		margin: 0 auto;
		max-width: 130px;
		height: 20px;
		border-radius: 4px;
		border: 1px solid #d9d9d9;
		background: #fafafa;
	}
}
</style>
