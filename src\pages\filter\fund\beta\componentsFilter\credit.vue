<!--  -->
<template>
	<div class="industryTheme">
		<operator v-if="is_range" ref="operator" @resolveMathRange="resolveMathRange"></operator>
		<el-dropdown @command="command2" class="mr-16">
			<el-button type="primary">
				<span>{{ active_name }}</span> <i class="el-icon-arrow-down el-icon--right"></i>
			</el-button>
			<el-dropdown-menu class="industry_theme_drapDown2" slot="dropdown">
				<el-dropdown-item v-for="(item, index) in list" :command="item.value" :key="index">{{ item.label }}</el-dropdown-item>
			</el-dropdown-menu>
		</el-dropdown>
		<el-dropdown @command="command">
			<el-button type="primary">
				{{ iconFlag != '' ? (iconFlag == 'all' ? '所有' : iconFlag) : '运算符' }}<i class="el-icon-arrow-down el-icon--right"></i>
			</el-button>
			<el-dropdown-menu slot="dropdown">
				<el-dropdown-item command="all">所有</el-dropdown-item>
				<el-dropdown-item command="<">&lt;</el-dropdown-item>
				<el-dropdown-item command="=">=</el-dropdown-item>
				<el-dropdown-item command=">">&gt;</el-dropdown-item>
				<el-dropdown-item command="<=">&lt;=</el-dropdown-item>
				<el-dropdown-item command=">=">&gt;=</el-dropdown-item>
			</el-dropdown-menu>
		</el-dropdown>
		<div v-show="showBox" style="margin-left: 0px; display: flex; align-items: center">
			<div style="margin-left: 16px">
				<el-input type="number" @input="inputChange" :placeholder="placeholder" v-model="input"></el-input>
			</div>
		</div>
	</div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import operator from '@/pages/filter/fund/beta/componentsFilter/components/operator.vue';

export default {
	props: {
		is_range: {
			type: Boolean,
			default: false
		},
		haveName: {
			type: String,
			default: ''
		},
		dataX: {
			type: Object,
			default: {}
		},
		placeholder: {
			type: String,
			default: '输入50,即持仓占比为50%'
		},
		indexFlag: {
			type: Number
		},
		baseIndexFlag: {
			type: Number
		},
		dataIndustry: {
			type: Object
		}
	},
	//import引入的组件需要注入到对象中才能使用
	components: { operator },
	data() {
		//这里存放数据
		return {
			iconFlag: '',
			showBox: false,
			input: '',
			list: [
				{
					label: 'AAA',
					value: 'AAA'
				},
				{
					label: 'AAA以下',
					value: 'AAA以下'
				},
				{
					label: 'AAA以下含未评级',
					value: 'AAA以下含未评级'
				},
				{
					label: '未评级',
					value: '未评级'
				}
			],
			active_name: 'AAA',
			industry_nameT: '',
			mathRange: { mathRange: 'avg' }
		};
	},
	//监控data中的数据变化
	watch: {
		dataX(val) {
			// console.log(val);
			if (val.dataResult && val.dataResult.length > 0) {
				this.showBox = true;
				this.iconFlag = val.dataResult[0].flag;
				this.input = val.dataResult[0].value;
				this.active_name = val.dataResult[0].industryValue;
				this.industry_nameT = val.dataResult[0].industryName;
				if (this.$refs['operator']) {
					this.$refs['operator'].getFlag(val.dataResult[0].mathRange);
				}
			}
		}
	},
	//方法集合
	methods: {
		resolveMathRange(obj) {
			this.mathRange = obj;
			this.resolveFather();
		},
		resolveFather() {
			this.$emit(
				'industryThemeChange',
				this.baseIndexFlag,
				this.indexFlag,
				this.input,
				this.iconFlag,
				this.active_name,
				this.list.findIndex((item) => item.value == this.active_name) >= 0
					? this.list[this.list.findIndex((item) => item.value == this.active_name)]['label']
					: '',
				this.FUNC.isEmpty(this.active_name) && this.FUNC.isEmpty(this.input) && this.FUNC.isEmpty(this.iconFlag),
				this.mathRange
			);
		},
		command(e) {
			this.iconFlag = e;
			this.industry_nameT =
				this.list.findIndex((item) => item.value == this.active_name) >= 0
					? this.list[this.list.findIndex((item) => item.value == this.active_name)]['label']
					: '';
			this.showBox = true;
			this.resolveFather();
		},
		command2(e) {
			this.active_name = e;
			this.resolveFather();
		},
		command3(e) {
			this.yearqtr = e;
			this.resolveFather();
		},
		inputChange() {
			this.resolveFather();
		}
	},
	//生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		if (JSON.stringify(this.dataX) != '{}') {
			if (this.dataX.dataResult && this.dataX.dataResult.length > 0) {
				this.showBox = true;
				this.iconFlag = this.dataX.dataResult[0].flag;
				this.input = this.dataX.dataResult[0].value;
				this.industry_nameT = this.dataX.dataResult[0].industryName;
				this.active_name = this.dataX.dataResult[0].industryValue;
				if (this.$refs['operator']) {
					this.$refs['operator'].getFlag(this.dataX.dataResult[0].mathRange);
				}
			}
		}
	}
};
</script>
<style>
.industry_theme_drapDown2 {
	overflow: auto !important;
}
</style>
<style lang="scss" scoped>
//@import url(); 引入公共css类
.industryTheme {
	display: flex;
	align-items: center;
}
</style>
