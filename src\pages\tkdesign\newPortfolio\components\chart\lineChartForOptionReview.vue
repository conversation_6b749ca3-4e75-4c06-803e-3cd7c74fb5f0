<template>
    <div class="charts_fill_class" v-loading="loading">
        <el-empty image-size="160" v-if="showEmpty"></el-empty>
			<v-chart
				v-else
            ref="companySizeChange"
            :options="option"
            element-loading-text="暂无数据"
            element-loading-spinner="el-icon-document-delete"
            element-loading-background="rgba(239, 239, 239, 0.5)"
            class="charts_one_class"
            autoresize
            @legendselectchanged="handleLegendSelectChanged"
				@zr:dblclick="handleDblClick"
        ></v-chart>
    </div>
</template>

<script>
import VChart from 'vue-echarts';
import { industryOperationsReview} from '@/api/pages/tkAnalysis/portfolio.js';
import { lineChartOption } from '@/utils/chartStyle.js';
export default {
components: { VChart },
data() {
    return {
        option: {},
        loading: true,
			showEmpty: true,
            doubleClick:false,
			legendChanged:false,
			legendData:{}
    };
},
watch: {
		doubleClick: {
			handler(val) {
				if(val && this.legendChanged){
					const chart = this.$refs.companySizeChange;
					let legendWai = this.legendData.name;
					for (const element in this.legendData.selected) {
						//显示当前legent 关闭非当前legent
						if (legendWai == element) {
							chart.dispatchAction({
								type: 'legendSelect',
								name: element
							});
						} else {
							chart.dispatchAction({
								type: 'legendUnSelect',
								name: element
							});
						}
					}
					this.doubleClick = false;
					this.legendChanged = false;
				}
			},
			immediate: true,
		},
	},
methods: {
    handleDblClick(){
			this.doubleClick = true;
		},
		handleLegendSelectChanged (params)  {
			this.legendChanged = true;
			this.legendData = params;
		
			
		},
    getChartData(data){
        let dateList = [],
        data1 = [],
        name = '沪深300',
        data3 = [],
        data2 = [];
        dateList = data.industryList?.map(item=>item.date)
        data.indexList?.forEach(item=>{dateList.push(item.date)})
        dateList = [...new Set(dateList)]
        dateList = dateList.sort()
       
        data1 = dateList.map((v)=>{
            
            let obj = data.industryList?.find((item) => item.date == v);
            return obj ? parseInt(obj.rate * 10000)/100 : '0';
        })
        data2 = dateList.map((v)=>{
            
            let obj = data.indexList?.find((item) => item.date == v);
            return obj ? parseInt(obj.rate * 10000)/100 : '0';
        })
        data3 = dateList.map((v)=>{
            
            let obj = data.industryList?.find((item) => item.date == v);
            return obj ? parseInt(obj.weight * 10000)/100 : '0';
        })
        name = data.indexList?.length >0 ? data.indexList[0].name : '沪深300';
        return {dateList,data1,data2,data3,name}
    },
   async getData(param) {
        this.loading = true;
    
        let res = await industryOperationsReview(param);
        this.loading = false;
        if(res.mtycode != 200){
            return;
        }
        if(res.data?.industryList?.length > 0 || res.data?.indexList?.length > 0){
            this.showEmpty = false;
        
        }else{
            this.showEmpty = true;
        }
        const {dateList,data1,data2,data3,name} = this.getChartData(res.data);
        this.$emit('tableData',{dateList,data1,data2,data3,name});
        this.option = lineChartOption({
            tooltip: {
					backgroundColor: '#ffffff',
					formatter: function (obj) {
						var value = `<div style="font-size:14px;">` + obj?.[0].axisValue + `</div>`;
						for (let i = 0; i < obj.length; i++) {
							value +=
								`<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
								`<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
								obj?.[i].color +
								`;"></div>` +
								`<div style="font-family: PingFang SC;">` +
								obj?.[i].seriesName +
								'</div></div>' +
								`<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
								(Number(obj?.[i].value) * 1).toFixed(2) +
								'%</div>' +
								`</div>`;
						}
						return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
					}
				},
            // legend: {
            //     bottom:'-2%',
            //     data: [
            //         {
            //             name: '组合累计净值',
                    
            //         },
            //         {
            //             name: '沪深300',
                        
            //         },
            //         {
            //             name: '回撤',
            //             icon: 'rect',
            //             backgroundColor:'#7388A9'
            //         },
            //         {
            //             name: '超额收益',
            //             icon: 'rect',
            //             backgroundColor:'#6F80DD'
            //         },
            //     ]
            // },
            xAxis: [{
                type: 'category',
                boundaryGap: false,
                data: dateList,
                axisLabel: {

                    // interval 可以定义成数字，但这也会有一个弊端，在不确定数据量的时候，间隔数不好定义，不能随心所欲的控制所要展示的内容 
                    interval:2
                }, 
            }],
            yAxis: 
               [ {
                    type: 'value',
                    name:'组合累计净值',
                    axisLine:{
                        show:false
                    },
                    axisTick:{
                        show:false
                    },
                    splitLine:{
                        lineStyle:{
                            type:'dashed'
                        }
                    }   
                },{
                    type: 'value',
                    name:'权重',
                    axisLine:{
                        show:false
                    },
                    axisTick:{
                        show:false
                    },
                    splitLine:{
                        lineStyle:{
                            type:'dashed'
                        }
                    }   
                }],
           
            series: [
                {
                    name: '组合累计净值',
                    type: 'line',
                    lineStyle:{
                        color:'#4096ff'
                    },
                    symbol: 'none',
                    data: data1,
                    yAxisIndex: 0, 
                },
                {
                    name,
                    type: 'line',
                    symbol: 'none',
                    lineStyle:{
                        color:'#4096ff'
                    },
                    data: data2,
                },
                {
                    name: '权重',
                    type: 'line',
                    symbol: 'none',
                    lineStyle:{
                        opacity:0
                    },
                    areaStyle: {
                        color:'#4096ff',
                        opacity:0.25,
                    },
                    data: data3,
                    yAxisIndex: 1, 
                },
                // {
                //     name: '超额收益',
                //     type: 'line',
                //     symbol: 'none',
                //     lineStyle:{
                //         opacity:0
                //     },
                //     areaStyle: {
                //         color:'#6F80DD',
                //         opacity:0.25,
                //     },
                //     data: data4,
                // },
            ]
        });
    }
}
};
</script>

<style scoped>
.chart_one{
padding: 0;
box-shadow: none;
}
.charts_fill_class{
    .echarts{
    height: 248px;

    }
}
</style>
