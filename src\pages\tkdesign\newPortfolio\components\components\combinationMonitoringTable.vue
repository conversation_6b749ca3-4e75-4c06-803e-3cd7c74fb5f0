<template>
    <div class="plate-wrapper fund-performance-board-wrapper" v-loading="loading">
        <combinationComponentHeader title="组合持仓监控" showMoreBtn @download="exportExcel">
            <template slot="right">
                <div class="block" style="margin-right: 12px;">
                    <span class="demonstration">截止日期：</span>
                    <el-date-picker
                    v-model="deadline"
                    align="right"
                    type="date"
                    @change="getData(form)"
                    placeholder="选择日期"
                    :picker-options="pickerOptions"
                    >
                    </el-date-picker>
                </div>
                
            </template>
        </combinationComponentHeader>
  
        <el-table border stripe :data="tableDataNow">
            <el-table-column min-width="100px" align="gotoleft" prop="flag" show-overflow-tooltip label="分类">
                
            </el-table-column>
            <el-table-column min-width="120px" align="gotoleft" prop="code" show-overflow-tooltip label="资产代码"></el-table-column>
            <el-table-column min-width="200px" align="gotoleft" prop="name" sortable label="资产名称"></el-table-column>
            <el-table-column min-width="100px" align="gotoleft" prop="rate" sortable label="涨跌幅">
                <templete slot-scope="scope">{{ scope.row.rate | fix2p }}</templete>
            </el-table-column>
            <el-table-column min-width="160px" align="gotoleft" sortable prop="position" label="持仓市值">
                <templete slot-scope="scope">{{ scope.row.position | fix2 }}</templete>
            </el-table-column>
            <el-table-column min-width="160px" align="gotoleft" sortable prop="share" label="持仓数量">
                <templete slot-scope="scope">{{ scope.row.share  | fix2 }}</templete>
            </el-table-column>
            <!-- <el-table-column min-width="160px" align="gotoleft" sortable prop="holdingCost" label="持仓成本">
                <templete slot-scope="scope">{{ scope.row.holdingCost  | fix2  }}</templete>
            </el-table-column> -->
            <el-table-column min-width="160px" align="gotoleft" sortable prop="profitAndLoss" label="当日盈亏">
                <templete slot-scope="scope">{{ scope.row.profitAndLoss | fix2 }}</templete>
            </el-table-column>
            <el-table-column min-width="160px" align="gotoleft" sortable prop="profitAndLossSum" label="浮动盈亏">
                <templete slot-scope="scope">{{ scope.row.profitAndLossSum | fix2 }}</templete>
            </el-table-column>
            <el-table-column min-width="160px" align="gotoleft" sortable prop="accumulateSum" label="累计盈亏">
                <templete slot-scope="scope">{{ scope.row.accumulateSum | fix2 }}</templete>
            </el-table-column>
            <el-table-column min-width="160px" align="gotoleft" sortable prop="weight" label="持仓权重">
                <templete slot-scope="scope">{{ scope.row.weight | fix2p }}</templete>
            </el-table-column>
            <el-table-column min-width="160px" align="gotoleft" sortable prop="nav" label="当前价格">
                <templete slot-scope="scope">{{ scope.row.nav |fix2 }}</templete>
            </el-table-column>
            <el-table-column min-width="160px" align="gotoleft" sortable prop="beginNav" label="成本价格">
                <templete slot-scope="scope">{{ scope.row.beginNav |fix2 }}</templete>
            </el-table-column>
            <el-table-column min-width="120px" align="gotoleft" sortable prop="date" label="时间">
            </el-table-column>

            <template slot="empty">
                <el-empty image-size="160"></el-empty>
            </template>
        </el-table>
        <el-pagination
            background
            style="display: flex; justify-content: right; padding-top: 16px; padding-bottom: 24px"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="currentPage"
            :page-sizes="[10, 20, 40, 60, 80, 100]"
            :page-size="pageSIze"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
        >
        </el-pagination>
    </div>
</template>
<script>
import { filter_to_excel } from "@/utils/exportExcel.js";
import combinationComponentHeader from './combinationComponentHeader.vue';
import { CombinationPositionMonitor} from '@/api/pages/tkAnalysis/portfolio.js';
export default {
    name:'combinationMonitoringTable',
    components:{
        combinationComponentHeader
    },
    props: {
		endDate:{
			type: String,
			default:""
		}
	},
    filters: {
		fix6(value) {
			return value.substring(0, 10);
		},
		fix3(value) {
			return parseInt(value * 1000) / 1000;
		},
		fix5(value) {
			return parseInt(value * 100000) / 100000;
		},
		fix2p(value) {
			return value !== '' &&
				value != '--' &&
				value != '- -' &&
				JSON.stringify(value) != '[]' &&
				JSON.stringify(value) != '{}' &&
				value != 'NAN' &&
				value != 'nan' &&
        value !== undefined &&
        value !== null 
				? (Number(value) * 100).toFixed(2) + '%'
				: '--';
				
		},
		fix2(value) {
			return  value !== '' &&
				value != '--' &&
				value != '- -' &&
				JSON.stringify(value) != '[]' &&
				JSON.stringify(value) != '{}' &&
				value != 'NAN' &&
				value != 'nan' && value !== undefined &&
        value !== null 
				? Number(value).toFixed(2)
				: '--';
		},
	},
    data(){
        return {
            form:{},
            pageSize: 10,
			currentPage: 1,
			total:0,
            deadline:new Date().setTime(new Date().getTime() - 3600 * 1000 * 24),
            allTableData:[],
            IndexStyleOption:[],
            tableData:[],
            legendName : {
                'indicatorPoints':'指标点位',
                'ttm':'TTM',
                'dividedIntoPoints':'分为点',
                'positiveStandardDeviation':'标准差（+1）',
                'negativeStandardDeviation':'标准差（-1）',
                'average':"平均值"
            },
            tableDataNow: [],
            indexCode:'',
            loading:false,
            tableHeader:[{
                prop:'flag',
                label:'分类'
            },{
                prop:'name',
                label:'资产名称'
            },{
                prop:'rate',
                label:'涨跌幅'
            },{
                prop:'position',
                label:'持仓市值'
            },{
                prop:'share',
                label:'持仓数量'
            },{
                prop:'holdingCost',
                label:'持仓成本'
            },{
                prop:'profitAndLoss',
                label:'当日盈亏'
            },{
                prop:'profitAndLossSum',
                label:'浮动盈亏'
            },{
                prop:'weight',
                label:'持仓权重'
            },{
                prop:'nav',
                label:'当前价格'
            },{
                prop:'beginNav',
                label:'成本价格'
            },{
                prop:'date',
                label:'时间'
            }],
            pickerOptions: {
				disabledDate(time) {
					return false;
				},
			}	
        }
    },
    watch: {
		endDate:{
			handler(val){
				if(val.trim()){
					this.pickerOptions.disabledDate = (time) => {
						return time.getTime() > (new Date(val).getTime());
					}
                    this.deadline = val;
				}
			},
			immediate: true
		},
    }, 
    methods:{
        handleSizeChange(val) {
			this.pageSize = val;
			this.currentPage = 1;
			this.handleCurrentChange(1);
		},
		handleCurrentChange(val) {
			this.currentPage = val;
            this.tableDataNow = this.allTableData.slice((this.currentPage-1)*this.pageSize,this.currentPage*this.pageSize)
		},
        async getData(param){
            //获取组合详情
            this.form = param;
            this.loading = true;
			let {mtycode,data} = await CombinationPositionMonitor({
                ...param,
				id:param.combinationId,
                deadline:this.moment(this.deadline).format('YYYY-MM-DD')
			});
			if (mtycode == 200) {
				this.allTableData = data || [];
                this.total = data.length;
                this.handleCurrentChange(1);
			} 
            this.loading = false;
        },
        exportExcel(){
            let list = this.tableHeader.map((item) => {
				return {
					...item,
					format: ''
				};
			});
			filter_to_excel(list, this.allTableData, '组合持仓监控');
        }
    },
    mounted(){
       
    },
}
</script>
<style lang="scss" scoped>
.fund-performance-board-wrapper {
    .select-form-wrapper {
        margin-bottom: 16px;
    }
    .content-table-wrapper {
        margin-bottom: 32px;
    }
}

</style>