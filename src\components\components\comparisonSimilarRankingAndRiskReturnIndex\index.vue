<template>
	<div class="chart_one">
		<div class="title" style="display: flex; justify-content: space-between; align-items: center">
			<span>风险收益指标&nbsp;&nbsp;{{ description }} </span>
			<div style="display: flex; align-items: center">
				<el-select v-model="model" placeholder="" @change="changeTag">
					<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>
				<el-button icon="el-icon-document-delete" @click="exportExcel" style="margin-left: 16px">导出Excel</el-button>
			</div>
		</div>
		<el-table
			:data="df_measure"
			v-loading="loading"
			style="margin-top: 24px"
			class="table"
			:cell-style="elcellstyle"
			ref="multipleTable"
			header-cell-class-name="table-header"
		>
			<el-table-column sortable prop="year" label="年份" align="gotoleft"> </el-table-column>
			<el-table-column sortable prop="cum_return" label="年累计收益" align="gotoleft">
				<template slot-scope="scope"
					><span>{{ scope.row.cum_return | fix2p }}</span></template
				>
			</el-table-column>
			<el-table-column sortable prop="cum_return_rank" label="排名" align="gotoleft">
				<template slot-scope="scope"
					><span>{{ scope.row.cum_return_rank | fix2pfu }}</span></template
				>
			</el-table-column>
			<el-table-column
				v-if="fundindextype == 'equityindex' || fundindextype == 'equityenhance'"
				sortable
				prop="trackingerror"
				label="跟踪误差"
				align="gotoleft"
			>
				<template slot-scope="scope"
					><span>{{ scope.row.trackingerror | fix2p }}</span></template
				>
			</el-table-column>
			<el-table-column
				v-if="fundindextype == 'equityindex' || fundindextype == 'equityenhance'"
				sortable
				prop="trackingerror_rank"
				label="排名"
				align="gotoleft"
			>
				<template slot-scope="scope"
					><span>{{ scope.row.trackingerror_rank | fix2pfu }}</span></template
				>
			</el-table-column>
			<el-table-column v-if="fundindextype == 'equityenhance'" sortable prop="information" label="信息比率" align="gotoleft">
				<template slot-scope="scope"
					><span>{{ scope.row.information | fix2p }}</span></template
				>
			</el-table-column>
			<el-table-column v-if="fundindextype == 'equityenhance'" sortable prop="information_rank" align="gotoleft">
				<template slot-scope="scope"
					><span>{{ scope.row.information_rank | fix2pfu }}</span></template
				>
			</el-table-column>
			<el-table-column sortable prop="maxdrawdown" align="gotoleft" label="最大回撤">
				<template slot-scope="scope"
					><span>{{ scope.row.maxdrawdown | fix2p }}</span></template
				>
			</el-table-column>
			<el-table-column sortable prop="cum_return" label="排名" align="gotoleft">
				<template slot-scope="scope"
					><span>{{ scope.row.maxdrawdown_rank | fix2pfu }}</span></template
				>
			</el-table-column>
			<el-table-column sortable prop="maxdrawdown_rank" align="gotoleft" label="波动率">
				<template slot-scope="scope"
					><span>{{ scope.row.volatility | fix2p }}</span></template
				>
			</el-table-column>
			<el-table-column sortable prop="volatility_rank" label="排名" align="gotoleft">
				<template slot-scope="scope"
					><span>{{ scope.row.volatility_rank | fix2pfu }}</span></template
				>
			</el-table-column>
			<el-table-column sortable prop="sharpe" align="gotoleft" label="夏普率">
				<template slot-scope="scope"
					><span>{{ scope.row.sharpe | fix3 }}</span></template
				>
			</el-table-column>
			<el-table-column sortable prop="sharpe_rank" label="排名" align="gotoleft">
				<template slot-scope="scope"
					><span>{{ scope.row.sharpe_rank | fix2pfu }}</span></template
				>
			</el-table-column>
		</el-table>
		<div style="display: flex; align-items: center; justify-content: space-between; position: relative">
			<div class="title" style="margin-bottom: 24px; margin-top: 16px">同类排名比较</div>
			<el-button icon="el-icon-document-delete" @click="exportExcel1">导出Excel</el-button>
		</div>
		<div style="width: 100%">
			<el-table :data="datatable" v-loading="loading" class="table" ref="multipleTable" header-cell-class-name="table-header">
				<el-table-column prop="year" label="年份" align="gotoleft"> </el-table-column>
				<el-table-column prop="factor" label="收益排名" align="gotoleft">
					<template slot-scope="scope"
						><span>{{ scope.row.cum_return }}</span></template
					>
				</el-table-column>
				<el-table-column prop="std" align="gotoleft" label="波动排名">
					<template slot-scope="scope"
						><span>{{ scope.row.volatility }}</span></template
					>
				</el-table-column>
				<el-table-column prop="gainorloss" label="回撤排名" align="gotoleft">
					<template slot-scope="scope"
						><span>{{ scope.row.maxdrawdown }}</span></template
					>
				</el-table-column>
				<el-table-column prop="adaptivity" align="gotoleft" label="夏普排名">
					<template slot-scope="scope"
						><span>{{ scope.row.sharpe }}</span></template
					>
				</el-table-column>
			</el-table>
		</div>
	</div>
</template>

<script>
import { exportTitle, exportTable } from '@/utils/exportWord.js';
import { filter_json_to_excel } from '@/utils/exportExcel.js';
// 风险收益指标
export default {
	name: 'riskReturnIndex',
	data() {
		return {
			description: null,
			df_measure: [],
			columns: [
				{
					title: '年份',
					dataIndex: 'year',
					sorter: true
				},
				{
					title: '年累计收益',
					dataIndex: 'cum_return',
					sorter: true
				},
				{
					title: '排名',
					dataIndex: 'cum_return_rank',
					sorter: true
				},
				{
					title: '跟踪误差',
					dataIndex: 'trackingerror',
					sorter: true
				},
				{
					title: '排名',
					dataIndex: 'trackingerror_rank',
					sorter: true
				},
				{
					title: '信息比率',
					dataIndex: 'information',
					sorter: true
				},
				{
					title: '排名',
					dataIndex: 'information_rank',
					sorter: true
				},
				{
					title: '最大回撤',
					dataIndex: 'maxdrawdown',
					sorter: true
				},
				{
					title: '排名',
					dataIndex: 'maxdrawdown_rank',
					sorter: true
				},
				{
					title: '波动率',
					dataIndex: 'volatility',
					sorter: true
				},
				{
					title: '排名',
					dataIndex: 'volatility_rank',
					sorter: true
				},
				{
					title: '夏普率',
					dataIndex: 'sharpe',
					sorter: true
				},
				{
					title: '排名',
					dataIndex: 'sharpe_rank',
					sorter: true
				}
			],
			datatable: [],
			notesData: {
				bj001: ''
			},
			loading: true,
			model: 'fof',
			options: [
				{
					label: 'FOF',
					value: 'fof'
				},
				{
					label: '纯债',
					value: 'purebond'
				},
				{
					label: '二级债',
					value: 'bond'
				}
			]
		};
	},
	filters: {
		fix2p(value) {
			return (value * 100 ? (value * 100).toFixed(2) : '--') + '%';
		},
		fix2pfu(value) {
			return (Number(value * 100) ? Number(value * 100).toFixed(2) : '--') + '%';
		},
		fix3(value) {
			return parseInt(value * 1000) / 1000 ? parseInt(value * 1000) / 1000 : '--';
		}
	},
	methods: {
		// 获取数据
		getData(data, comparison, info) {
			this.loading = false;
			this.datatable = comparison;
			this.fundindextype = info?.type;
			let arr = [];
			data.map((item) => {
				let index = arr.findIndex((obj) => {
					return obj.year == item.year;
				});
				let obj = {};
				obj[item.measure] = item.meter;
				obj[item.measure + '_rank'] = item.measure == 'maxdrawdown' || item.measure == 'volatility' ? 1 - item.rank : item.rank;
				obj[item.measure + '_description'] = item.description;
				if (index == -1) {
					arr.push({
						year: item.year,
						data: obj
					});
				} else {
					arr[index].data = { ...arr[index].data, ...obj };
				}
			});

			this.df_measure = arr.map((item) => {
				return { year: item.year, ...item.data };
			});
		},
		changeTag(val) {
			this.loading = true;
			this.$emit('resolveFather', val);
		},
		// 隐藏loading
		hideLoading() {
			this.loading = false;
		},
		// 行样式
		elcellstyle({ row, column, rowIndex, columnIndex }) {
			if (columnIndex == 1) {
				if (row['cum_return'] >= 0) {
					return 'color: #E85D2D;';
				} else return 'color: #18C2A0;';
			}
		},
		exportExcel1() {
			let list = [
				{
					label: '年份',
					fill: 'header',
					value: 'year'
				},
				{
					label: '收益排名',
					value: 'cum_return'
				},
				{
					label: '波动排名',
					value: 'volatility'
				},
				{
					label: '回撤排名',
					value: 'maxdrawdown'
				},
				{
					label: '夏普排名',
					value: 'sharpe'
				}
			];
			filter_json_to_excel(list, this.datatable, '同类排名比较');
		},
		// 导出excel
		exportExcel() {
			let list = [
				{ label: '年份', value: 'year', fill: 'header' },
				{ label: '年累计收益', value: 'cum_return', format: 'fix2p', fill: 'red_or_green' },
				{ label: '排名', value: 'cum_return_rank', format: 'fix2p' },
				{ label: '最大回撤', value: 'maxdrawdown', format: 'fix2p' },
				{ label: '排名', value: 'maxdrawdown_rank', format: 'fix2p' },
				{ label: '波动率', value: 'volatility', format: 'fix2p' },
				{ label: '排名', value: 'volatility_rank', format: 'fix2p' },
				{ label: '夏普率', value: 'sharpe', format: 'fix2p' },
				{ label: '排名', value: 'sharpe_rank', format: 'fix2p' }
			];
			let indexList = [
				{ label: '跟踪误差', value: 'trackingerror', format: 'fix2p' },
				{ label: '排名', value: 'trackingerror_rank', format: 'fix2p' }
			];
			let enhanceList = [
				{ label: '信息比率', value: 'information', format: 'fix2p' },
				{ label: '排名', value: 'information_rank', format: 'fix2p' }
			];
			if ((this.fundindextype = 'equityindex' || this.fundindextype == 'equityenhance')) {
				list.push(...indexList);
			} else if ((this.fundindextype = 'equityenhance')) {
				list.push(...enhanceList);
			}
			filter_json_to_excel(list, this.df_measure, '风险收益指标');
		},
		// 导出
		createPrintWord() {
			let list = [
				{ label: '年份', value: 'year', fill: 'header' },
				{ label: '年累计收益', value: 'cum_return', format: 'fix2p', fill: 'red_or_green' },
				{ label: '排名', value: 'cum_return_rank', format: 'fix2p' },
				{ label: '最大回撤', value: 'maxdrawdown', format: 'fix2p' },
				{ label: '排名', value: 'maxdrawdown_rank', format: 'fix2p' },
				{ label: '波动率', value: 'volatility', format: 'fix2p' },
				{ label: '排名', value: 'volatility_rank', format: 'fix2p' },
				{ label: '夏普率', value: 'sharpe', format: 'fix2p' },
				{ label: '排名', value: 'sharpe_rank', format: 'fix2p' }
			];
			let indexList = [
				{ label: '跟踪误差', value: 'trackingerror', format: 'fix2p' },
				{ label: '排名', value: 'trackingerror_rank', format: 'fix2p' }
			];
			let enhanceList = [
				{ label: '信息比率', value: 'information', format: 'fix2p' },
				{ label: '排名', value: 'information_rank', format: 'fix2p' }
			];
			if ((this.fundindextype = 'equityindex' || this.fundindextype == 'equityenhance')) {
				list.push(...indexList);
			} else if ((this.fundindextype = 'equityenhance')) {
				list.push(...enhanceList);
			}
			return [...exportTitle('风险收益指标'), ...exportTable(list, this.df_measure)];
		}
	}
};
</script>

<style></style>
