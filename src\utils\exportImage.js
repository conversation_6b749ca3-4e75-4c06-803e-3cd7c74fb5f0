const html2canvas = require('html2canvas');

/**
 *
 * @param {String} id 对应dom的id
 * @param {String} name  导出图片的名称
 */
export default function downloadImage(id, name) {
	this.$nextTick(async () => {
		try {
			let canvas = await html2canvas(document.getElementById(id), { scale: 3 });
			let base64Str = canvas.toDataURL('image/png');
			let aLink = document.createElement('a');
			aLink.style.display = 'none';
			aLink.href = base64Str;
			aLink.download = name + '.jpg';
			// 触发点击-然后移除
			document.body.appendChild(aLink);
			aLink.click();
			document.body.removeChild(aLink);
		} catch {}
	});
}
