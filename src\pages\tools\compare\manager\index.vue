<!--  -->
<template>
  <div class="HolePageBox"
       id="pdfDom1">
    <div class="boxTitleHeader">
      <span class="headerFontSmall">工具箱/</span>
      <span class="headerFontSmall">比较/</span>
      <span class="headerFontBig">基金经理比较</span>
    </div>
    <div v-loading="loading"
         class="compareFixHeader">
      <div style="width: 218px">
        <div style="position: absolute; display: flex; flex-direction: column">
          <el-button style="width: 90px; margin-bottom: 10px"
                     type="primary"
                     @click="toGetPdf">导出PDF</el-button>
          <!-- <el-button style="margin-left: 0px !important; width: 90px; margin-bottom: 10px"
                     type="primary"
                     @click="exportWord">导出Word</el-button> -->
          <el-button style="margin-left: 0px !important; width: 90px; margin-bottom: 10px"
                     @click="showSave = true"
                     type="primary">保存</el-button>
          <el-button style="margin-left: 0px !important; width: 90px; margin-bottom: 10px"
                     @click="showbox = true"
                     type="primary">添加对比</el-button>
        </div>
      </div>
      <div style="flex: 1; position: relative"
           v-for="(item, index) in fund_hold"
           :key="index"
           class="compareBox">
        <div class="coloriconFlag"
             :style="'background:' + colorList[index]"></div>
        <div style="padding: 16px">
          <div class="name">{{ item.name_x }}</div>
          <div class="company">{{ item.glr }}</div>
          <div class="date"
               style="margin-top: 16px">任职日期：{{ item.founddate }}</div>
          <div class="nav">累计收益：{{ Number(item.cnav * 100).toFixed(2) }}%</div>
        </div>
        <i @click="deleteSelect(item.code, item.name)"
           style="color: rgba(0, 0, 0, 0.45); width: 10px; height: 10px; position: absolute; right: 10px; top: 10px; cursor: pointer"
           class="el-icon-close"></i>
      </div>
    </div>
    <div v-loading="loading"
         class="contentCompare">
      <tablerank @deleteSelect="deleteSelect"
                 :fund_hold="fund_holdS"
                 ref="tablerank"
                 :comparetype="comparetype"
                 :name="name"
                 :id="id"
                 :type="type"></tablerank>
    </div>
    <div class="contentCompare">
      <returns :indexLists="indexLists"
               @outBasic="outBasic"
               ref="returns"
               :comparetype="comparetype"
               :name="name"
               :id="id"
               :type="type"></returns>
    </div>
    <!-- <div class="contentCompare">
      <basicCompare @outBasicBCompare="outBasicBCompare"
                    :returnBCompare="returnBCompare"
                    ref="basicCompare"
                    :comparetype="comparetype"
                    :name="name"
                    :id="id"
                    :type="type"></basicCompare> -->
    <!-- </div> -->
    <div class="secondTitileCompare">慧捕基打分卡</div>
    <div class="contentCompare2">
      <div v-if="type == 'equity'">
        <equityrank ref="equityrank"
                    :comparetype="comparetype"
                    :name="name"
                    :id="id"
                    :type="type"></equityrank>
      </div>
      <div v-if="type == 'bond'">
        <bondrank ref="bondrank"
                  :comparetype="comparetype"
                  :name="name"
                  :id="id"
                  :type="type"></bondrank>
        <!-- 债券类模板TODO -->
      </div>
      <div v-if="type == 'bill' || type == 'purebond' || type == 'cbond'">
        <otherbondrank ref="otherbondrank"
                       :comparetype="comparetype"
                       :name="name"
                       :id="id"
                       :type="type"></otherbondrank>
        <!-- 债券类模板TODO -->
      </div>
    </div>
    <div class="secondTitileCompare">仓位披露与比较</div>
    <div class="contentCompare">
      <holdstockweight ref="holdstockweight"
                       :comparetype="comparetype"
                       :name="name"
                       :id="id"
                       :type="type"></holdstockweight>
    </div>
    <div class="secondTitileCompare">代表基金收益比较</div>
    <div class="contentCompare">
      <normalfund @changefund="changefund"
                  ref="normalfund"
                  :comparetype="comparetype"
                  :name="name"
                  :id="id"
                  :type="type"></normalfund>
    </div>
    <div class="contentCompare">
      <tablefundrank ref="tablefundrank"
                     :comparetype="comparetype"
                     :name="name"
                     :id="id"
                     :type="type"></tablefundrank>
    </div>
    <div v-show="type == 'bill' || type == 'purebond' || type == 'cbond' || type == 'bond'"
         class="contentCompare">
      <bigmoney ref="bigmoney"
                :comparetype="comparetype"
                :name="name"
                :id="id"
                :type="type"></bigmoney>
    </div>
    <div v-show="type == 'bill' || type == 'purebond' || type == 'cbond' || type == 'bond'"
         class="contentCompare">
      <detailmoney ref="detailmoney"
                   :comparetype="comparetype"
                   :name="name"
                   :id="id"
                   :type="type"></detailmoney>
    </div>
    <div v-if="type == 'equity' || type == 'bond'"
         class="secondTitileCompare">持仓分析</div>
    <div v-if="type == 'equity' || type == 'bond'"
         class="contentCompare">
      <sameholdstock ref="sameholdstock"
                     :comparetype="comparetype"
                     :name="name"
                     :id="id"
                     :type="type"></sameholdstock>
    </div>
    <div v-if="type == 'equity' || type == 'bond'"
         class="contentCompare">
      <holdstock ref="holdstock"
                 :comparetype="comparetype"
                 :name="name"
                 :id="id"
                 :type="type"></holdstock>
    </div>
    <div v-if="type == 'equity' || type == 'bond'"
         class="contentCompare">
      <stockmanaged ref="stockmanaged"
                    :comparetype="comparetype"
                    :name="name"
                    :id="id"
                    :type="type"></stockmanaged>
    </div>
    <div v-if="type == 'equity' || type == 'bond'">
      <sameholdindustry ref="sameholdindustry"
                        class="contentCompare"
                        :comparetype="comparetype"
                        :name="name"
                        :id="id"
                        :type="type"></sameholdindustry>
    </div>
    <div v-if="type == 'equity' || type == 'bond'">
      <diffholdindustry ref="diffholdindustry"
                        class="contentCompare"
                        :comparetype="comparetype"
                        :name="name"
                        :id="id"
                        :type="type"></diffholdindustry>
    </div>
    <div v-if="type == 'equity' || type == 'bond'"
         class="contentCompare">
      <holdindustry ref="holdindustry"
                    :comparetype="comparetype"
                    :name="name"
                    :id="id"
                    :type="type"></holdindustry>
    </div>
    <div v-if="type == 'equity' || type == 'bond'"
         class="contentCompare">
      <industryability ref="industryability"
                       :comparetype="comparetype"
                       :name="name"
                       :id="id"
                       :type="type"></industryability>
    </div>
    <div v-if="type == 'equity' || type == 'bond'"
         class="contentCompare">
      <industryscore ref="industryscore"
                     :comparetype="comparetype"
                     :name="name"
                     :id="id"
                     :type="type"></industryscore>
    </div>
    <div v-if="type == 'equity' || type == 'bond'"
         class="contentCompare">
      <buysells ref="buysells"
                :comparetype="comparetype"
                :name="name"
                :id="id"
                :type="type"> </buysells>
    </div>
    <!-- 债券位置 -->
    <div v-if="type == 'bill' || type == 'purebond' || type == 'cbond' || type == 'bond'"
         class="secondTitileCompare">债券持仓分析</div>
    <div v-if="type == 'bill' || type == 'purebond' || type == 'cbond' || type == 'bond'"
         class="contentCompare">
      <holdbond ref="holdbond"
                :comparetype="comparetype"
                :name="name"
                :id="id"
                :type="type"> </holdbond>
    </div>
    <div v-if="type == 'bill' || type == 'purebond' || type == 'cbond' || type == 'bond'"
         class="contentCompare">
      <holdbondtype ref="holdbondtype"
                    :comparetype="comparetype"
                    :name="name"
                    :id="id"
                    :type="type"></holdbondtype>
    </div>
    <div v-if="type == 'bill' || type == 'purebond' || type == 'cbond' || type == 'bond'"
         class="contentCompare">
      <longdurations ref="longdurations"
                     :comparetype="comparetype"
                     :name="name"
                     :id="id"
                     :type="type"></longdurations>
    </div>
    <div v-if="type == 'bill' || type == 'purebond' || type == 'cbond' || type == 'bond'"
         class="contentCompare">
      <creditdown ref="creditdown"
                  :comparetype="comparetype"
                  :name="name"
                  :id="id"
                  :type="type"></creditdown>
    </div>
    <div v-if="type == 'equity'"
         class="contentCompare">
      <ManagerBuySellMod ref="ManagerBuySellMod"
                         :comparetype="comparetype"
                         :name="name"
                         :id="id"
                         :type="type"></ManagerBuySellMod>
    </div>
    <div v-if="type == 'equity'"
         class="contentCompare">
      <timescore ref="timescore"
                 :comparetype="comparetype"
                 :name="name"
                 :id="id"
                 :type="type"></timescore>
    </div>
    <!--牛熊一下位置  -->
    <div v-if="type == 'bond'"
         class="contentCompare">
      <niuxiong ref="niuxiong"
                :comparetype="comparetype"
                :name="name"
                :id="id"
                :type="type"> </niuxiong>
    </div>
    <div v-if="type == 'bond'"
         class="contentCompare">
      <barra ref="barra"
             :comparetype="comparetype"
             :name="name"
             :id="id"
             :type="type"> </barra>
    </div>
    <div v-if="type == 'equity'"
         class="contentCompare">
      <theme ref="theme"
             :comparetype="comparetype"
             :name="name"
             :id="id"
             :type="type"></theme>
    </div>
    <!-- <div class="contentCompare">
      <selfsay ref="selfsay"
               :comparetype="comparetype"
               :name="name"
               :id="id"
               :type="type"></selfsay>
    </div> -->

    <el-dialog title="选择比较的类型"
               :visible.sync="showitem"
               width="30%"
               destroy-on-close>
      <div class="savemodel"
           style="width: 100%">
        <el-form>
          <el-form-item>
            <el-select v-model="valuex"
                       placeholder="请选择比较类型">
              <el-option v-for="item in options"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value"> </el-option>
            </el-select>
          </el-form-item>
          <div class="height10border"></div>
          <div style="text-align: right"
               class="demo-drawer__footer">
            <el-button type="primary"
                       style="background: #d7dbe0 !important; color: balck !important; border: 1px solid #d7dbe0 !important"
                       @click="showitem = fasle">取消</el-button>
            <el-button type="primary"
                       @click="submitmodal">进入比较</el-button>
          </div>
        </el-form>
      </div>
    </el-dialog>
    <el-dialog title="添加对比"
               :visible.sync="showbox"
               width="800px"
               destroy-on-close>
      <div class="savemodel"
           style="width: 100%">
        <el-row>
          <div style="height: 10px"></div>
          <el-col :span="16"
                  style="border-right: 1px solid #e8e8e8">
            <el-input style="width: 200px; margin-bottom: 10px"
                      v-model="values"
                      @input="changeSelect"></el-input>
            <el-table style="padding-right: 20px"
                      v-loading="loadingSearch"
                      ref="multipleTables"
                      :height="300"
                      @selection-change="handleSelectionChange"
                      :data="select1">
              <el-table-column type="selection"
                               :width="55"> </el-table-column>
              <el-table-column align="gotoleft"
                               prop="name"
                               :show-overflow-tooltip="true"
                               label="基金名称"> </el-table-column>
              <el-table-column align="gotoleft"
                               prop="fund_co"
                               :show-overflow-tooltip="true"
                               label="基金公司"> </el-table-column>
              <!-- <el-table-column align="gotoleft"
                               prop="type"
                               :show-overflow-tooltip="true"
                               label="类型"> </el-table-column> -->
            </el-table>
          </el-col>
          <el-col :span="8">
            <div style="margin-left: 10px">
              <div style="display: flex; justify-content: space-between; align-items: center">
                <div style="font-weight: 600">已选对比</div>
                <div><el-button type="text"
                             style="color: red"
                             @click="clear">清空</el-button></div>
              </div>
              <el-scrollbar style="height: 300px">
                <div style="text-align: center; margin: 10px 0 0 0"
                     v-for="(item, index) in endSelect"
                     :key="index">
                  <el-tag style="min-width: 200px"
                          closable
                          @close="closeTags(index)"
                          type="info">{{ item.name }}</el-tag>
                </div>
              </el-scrollbar>
            </div>
          </el-col>
        </el-row>
        <div style="display: flex; justify-content: flex-end; align-items: center; width: 100%; margin-top: 10px">
          <el-button @click="showbox = false">取消</el-button>
          <el-button @click="gotoCompare"
                     type="primary">确定</el-button>
        </div>
      </div>
    </el-dialog>

    <el-dialog title="保存对比"
               :visible.sync="showSave"
               width="400px"
               destroy-on-close>
      <div v-loading="loadingSave"
           class="savemodel"
           style="width: 100%">
        <el-row>
          <span>对比组名称 &nbsp;&nbsp;</span>
          <el-input style="margin-top: 10px"
                    v-model="valuesX"
                    placeholder="请输入对比组名称"></el-input>
        </el-row>
        <div style="display: flex; justify-content: flex-end; align-items: center; width: 100%; margin-top: 10px">
          <el-button @click="showSave = false">取消</el-button>
          <el-button @click="saveTemplate"
                     type="primary">确定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import printJS from 'print-js';
import { ManagerBasicMsg, FundBasicMsg } from '@/api/pages/tools/compare.js';
import { search_all, TypeMsg, Templater, TemplaterGet } from '@/api/pages/tools/compare.js';
import { getManagerBenchmark } from '@/api/pages/components/fundCumReturn.js';

import basicCompare from '../components/basicsmsg/basicCompare';

import tablerank from '../components/basicsmsg/tablerank';
import returns from '../components/basicsmsg/return';
import rankcompare from '../components/basicsmsg/rankcompare';
import equityrank from '../components/mtyrank/equityrank';
import bondrank from '../components/mtyrank/bondrank';
import otherbondrank from '../components/mtyrank/otherbondrank';
import normalfund from '../components/normalfund/normalfund';
import tablefundrank from '../components/normalfund/tablefundrank';
import holdstock from '../components/holdstock/hlodstcok';
import holdindustry from '../components/holdstock/holdindusrty';
import industryability from '../components/holdstock/industryability';
import stockmanaged from '../components/holdstock/stockmanaged';
import industryscore from '../components/industryscore/industryscore';
import timescore from '../components/timescore/timescore';
import selfsay from '../components/selfsay/selfsay';
import theme from '../components/theme/theme';
import buysells from '../components/buysell/buyselldws';
import ManagerBuySellMod from '../components/buysell/buysellmod';
import holdstockweight from '../components/holdstockweight/holdstockweight';
import sameholdstock from '../components/holdstock/sameholdstock';
import sameholdindustry from '../components/holdstock/sameholdindustry';
import diffholdindustry from '../components/holdstock/diffholdindustry';
// bond
import bigmoney from '../components/moneydo/bigmoney';
import detailmoney from '../components/moneydo/detailmoney';

import holdbond from '../components/holdbond/holdbond';
import holdbondtype from '../components/holdbond/holdbondtype';
import longdurations from '../components/holdbond/longdurations';
import creditdown from '../components/holdbond/creditdown';
import niuxiong from '../components/niuxiong/niuxiong';
import barra from '../components/barra/barra';
export default {
  //import引入的组件需要注入到对象中才能使用
  components: {
    basicCompare,
    tablerank,
    returns,
    rankcompare,
    equityrank,
    normalfund,
    tablefundrank,
    holdstock,
    holdindustry,
    stockmanaged,
    industryscore,
    timescore,
    selfsay,
    theme,
    buysells,
    ManagerBuySellMod,
    bondrank,
    holdbond,
    holdbondtype,
    longdurations,
    creditdown,
    niuxiong,
    barra,
    otherbondrank,
    bigmoney,
    detailmoney,
    holdstockweight,
    sameholdstock,
    diffholdindustry,
    sameholdindustry,
    industryability
  },
  data () {
    //这里存放数据
    return {
      loading: false,
      indexLists: [],
      fund_hold: [],
      fund_holdS: [],
      showbox: false,
      valuesX: '',
      showSave: false,
      colorList: ['#4096ff', '#4096ff', '#7388A9', '#6F80DD'],
      comparetype: 'manager',
      id: '',
      type: '',
      name: '',
      printLoading: false,
      loadingSearch: false,
      values: '',
      select1: [],
      select1All: [],
      startSelect: [],
      endSelect: [],
      loadingSave: false,
      model_flag: 0,
      // 保存基准
      returnsHold: [],
      rankHold: [],
      returnBCompare: [],
      options: [],
      showitem: false,
      valuex: ''
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {
    $route (to, from) {
      Object.assign(this.$data, this.$options.data());
      this.id = this.$route.query.id;
      this.name = this.$route.query.name;
      this.type = this.$route.query.type;
      if (this.$route.query.model_flag) {
        this.model_flag = this.$route.query.model_flag;
      }
      this.$nextTick(() => {
        this.getDatas();
      });
      if (this.model_flag != 0) {
        this.getMemory();
      }
      let t1 = this.id.split(',');
      let t2 = this.name.split(',');
      this.endSelect = [];
      for (let i = 0; i < t1.length; i++) {
        this.endSelect.push({ name: t2[i], code: t1[i] });
      }
    }
  },
  //方法集合
  methods: {
    changefund (val) {
      this.$refs.tablefundrank.getdata(val);
    },
    // 打印pdf
    toGetPdf () {
      document.getElementById('pdfDom1').style = 'width:1200px';
      setTimeout(() => {
        const style1 =
          '@page {  } ' +
          '@media print { .el-card{border: 0px !important;} .el-card__header{border-bottom: 0px !important;font-weight: 1000; }  .el-card.is-always-shadow, .el-card.is-hover-shadow:focus, .el-card.is-hover-shadow:hover {-webkit-box-shadow: 0 0 0 0  !important; box-shadow: 0 0 0 0 !important;} .points{width:6px;height:16px;background:#F56C6C;-webkit-print-color-adjust: exact;box-shadow: 3px 3px 1px #ffb300;  margin-right:5px;margin-top:4px}  .el-table thead {display: table-header-group !important;} .el-table th, .el-table tr {page-break-after: always !important;}  } ';
        printJS({
          printable: 'pdfDom1',
          style: style1,
          scanStyles: false,
          type: 'html',
          css: [
            'https://unpkg.com/ant-design-vue@1.0.3/dist/antd.min.css',
            './ve-table.css',
            './printCompare.css',
            'https://cdn.bootcss.com/element-ui/2.4.5/theme-chalk/index.css'
          ],
          title: '慧捕基基金经理对比报告',
          onPrintDialogClose: () => {
            document.getElementById('pdfDom1').style = 'width:100%';
          }
        });
      }, 1000);
    },
    async getIndexBasic () {
      let postData = { code: this.id.split(',')[0], type: this.type };
      let { data } = await getManagerBenchmark(postData);
      this.indexLists = [
        {
          label: '参考基准',
          options: data.map((item) => {
            return {
              ...item,
              code: item.index_code,
              flag: 'index',
              company_code: '--',
              fund_co: '--',
              num: 4
            };
          })
        }
      ];
    },
    getDatas () {
      try {
        this.getIndexBasic();
      } catch (err) {
        //console.log(err);
        this.indexLists = this.COMMON.optionBasic;
      }
      try {
        this.gefunddata();
        this.$refs.tablerank.getdata();
        this.$refs.returns.getdata();
        // this.$refs.basicCompare.getdata();
        // this.$refs.rankcompare.getdata()
        if (this.type == 'equity') {
          this.$refs.equityrank.getdata();
          // //console.log(this.$refs.holdstockweight)
          this.$refs.sameholdstock.getdata();

          this.$refs.holdstockweight.getdata();
        } else if (this.type == 'bond') {
          this.$refs.bondrank.getdata();
          this.$refs.barra.getdata();
        } else if (this.type == 'bill' || this.type == 'purebond' || this.type == 'cbond') {
          this.$refs.otherbondrank.getdata();
        }
        this.$refs.normalfund.getdata();
        this.$refs.tablefundrank.getdata();
        if (this.type == 'equity' || this.type == 'bond') {
          this.$refs.holdstock.getdata();
          this.$refs.holdindustry.getdata();
          this.$refs.industryability.getdata();
          this.$refs.stockmanaged.getdata();
          this.$refs.industryscore.getdata();
          this.$refs.sameholdindustry.getdata();
          this.$refs.diffholdindustry.getdata();
          this.$refs.buysells.getdata();
        }
        if (this.type == 'bill' || this.type == 'purebond' || this.type == 'cbond' || this.type == 'bond') {
          this.$refs.holdbond.getdata();
          this.$refs.holdbondtype.getdata();
          this.$refs.longdurations.getdata();
          this.$refs.creditdown.getdata();
          this.$refs.niuxiong.getdata();

          this.$refs.bigmoney.getdata();
          this.$refs.detailmoney.getdata();
        }
        if (this.type == 'equity') {
          this.$refs.timescore.getdata();
          this.$refs.theme.getdata();
          this.$refs.ManagerBuySellMod.getdata();
        }
        this.$refs.selfsay.getdata();
      } catch (err) {
        //console.log("xxxxx");
        //console.log(err)
      }
    },
    async saveTemplate () {
      let temp = {};
      if (this.returnsHold.length > 0 && this.returnsHold[0] != '') {
        temp['returnsHold'] = this.returnsHold;
      }
      if (this.rankHold.length > 0 && this.rankHold[0] != '') {
        temp['rankHold'] = this.rankHold;
      }
      if (this.returnBCompare.length > 0 && this.returnBCompare[0] != '') {
        temp['returnBCompare'] = this.returnBCompare;
      }
      try {
        this.loadingSave = true;

        let data = await Templater({
          model_name: this.id, //
          model_description: this.name,
          type: this.type,
          ismanager: true,
          template: temp,
          title: this.valuesX
        });
        if (data) {
          this.loadingSave = false;
          this.$message.success('模板保存成功');
          this.showSave = false;
        }
      } catch (e) {
        this.loadingSave = false;

        this.$message.error('模板保存失败');
      }
    },
    outBasicRank (e) {
      this.rankHold = e;
    },
    outBasic (e) {
      this.returnsHold = e;
    },
    outBasicBCompare (e) {
      this.returnBCompare = e;
    },
    clear () {
      this.startSelect = [];
      this.endSelect = [];
      this.$refs.multipleTables.clearSelection();
    },
    async changeSelect () {
      this.loadingSearch = true;
      let that = this;
      let { data } = await search_all({
        message: that.values,
        flag: 2
      });
      if (data) {
        let temparr = [];
        for (let i = 0; i < data.length; i++) {
          if (data[i].flag == 'manager') {
            temparr.push(data[i]);
          }
        }
        // if(temparr.length > 0) {
        that.select1All = temparr;
        that.select1 = temparr.slice(0, 10);
        // }

        // //console.log(that.havefundmanager)
        that.loading = false;
      }
      this.loadingSearch = false;
    },
    handleSelectionChange (val) {
      // console.log("xxxobject");
      let that = this;
      if (this.select1All.length > 0 && val.length > 0) {
        if (val.length >= this.startSelect.length) {
          for (let i = 0; i < val.length; i++) {
            if (this.endSelect.findIndex((item) => item.code == val[i].code) < 0) {
              this.endSelect.push(val[i]);
            }
          }
        } else {
          let x = 0;
          let flags = false;
          for (let i = 0; i < this.startSelect.length; i++) {
            if (val.findIndex((item) => item.code == this.startSelect[i].code) < 0) {
              x = i;
            } else {
              flags = true;
            }
          }

          if (
            this.endSelect.findIndex((item) => {
              if (item.code == this.startSelect[x].code) return true;
              item.code == this.startSelect[x].code;
            }) >= 0
          ) {
            this.endSelect.splice(
              this.endSelect.findIndex((item) => {
                if (item.code == this.startSelect[x].code) return true;
                item.code == this.startSelect[x].code;
              }),
              1
            );
          }
        }
      }
      this.startSelect = val;
      if (this.endSelect.length > 4) this.$message.error('比较数限制小于五个');
    },
    closeTags (index) {
      let t = this.select1.findIndex((item) => {
        if (item.code == this.endSelect[index].code) return true;
        item.code == this.endSelect[index].code;
      });

      let temp = [];
      for (let i = 0; i < this.endSelect.length; i++) {
        if (i != index) {
          temp.push(this.endSelect[i]);
        }
      }
      // console.log(this.endSelect);
      //   console.log(index);
      // console.log(temp);
      this.endSelect = temp;
      setTimeout(() => {
        if (t > -1) {
          this.$refs.multipleTables.toggleRowSelection(this.select1[t]);
        }
      }, 500);
    },
    deleteSelect (e) {
      let temp = this.id.split(',');
      let temp2 = this.name.split(',');
      let s = temp.indexOf(e);
      temp.splice(s, 1);
      temp2.splice(s, 1);

      this.id = temp.join(',');
      this.name = temp2.join(',');
      this.$route.query.name = this.name;
      this.$route.query.id = this.id;
      this.$nextTick(() => {
        this.getDatas();
      });
    },
    async gotoCompare () {
      let tempcode = '';
      let tempname = '';
      let temptype = '';
      for (let i = 0; i < this.endSelect.length; i++) {
        tempname += this.endSelect[i].name + ',';
        tempcode += this.endSelect[i].code + ',';
      }
      tempcode = tempcode.slice(0, tempcode.length - 1);
      tempname = tempname.slice(0, tempname.length - 1);
      let data = await TypeMsg({ code: tempcode });
      if (data) {
        // //console.log(data)
        if (data.data) {
          if (data.data.length == 0) {
            this.$message.warning('无相同管理类型，请选择同类基金经理比较');
          } else if (data.data.length == 1) {
            temptype = data.data[0];
            if (temptype == 'equity') {
              this.id = tempcode;
              this.name = tempname;
              this.type = temptype;
              this.$route.query.name = this.name;
              this.$route.query.id = this.id;
              this.$route.query.type = this.type;
              this.$nextTick(() => {
                this.getDatas();
              });
              this.showbox = false;
            } else {
              this.$message('对于基金经理的比较我们只提供主动权益类型的比较，债券类型的比较以具体产品比较更优');
            }
          } else if (data.data.length > 1) {
            this.showitem = true;
            this.options = [];
            for (let i = 0; i < data.data.length; i++) {
              this.options.push({ value: data.data[i], label: data.data[i] });
            }
          }
        }
      }
    },
    async getMemory () {
      let data = await TemplaterGet({ model_name: this.id });
      if (data && data.mtycode != 204) {
        if (data.length > 0) {
          if (data[0].model_args.returnsHold && data[0].model_args.returnsHold.length > 0 && data[0].model_args.returnsHold[0] != '') {
            this.returnsHold = data[0].model_args.returnsHold;
          }
          if (data[0].model_args.rankHold && data[0].model_args.rankHold.length > 0 && data[0].model_args.rankHold[0] != '') {
            this.rankHold = data[0].model_args.rankHold;
          }
          if (
            data[0].model_args.returnBCompare &&
            data[0].model_args.returnBCompare.length > 0 &&
            data[0].model_args.returnBCompare[0] != ''
          ) {
            this.returnBCompare = data[0].model_args.returnBCompare;
          }
        }
      }
    },
    async gefunddata () {
      this.loading = true;
      this.fund_hold = [];
      this.fund_holdS = [];
      let data = await ManagerBasicMsg({ manager_code: this.id, type: this.type, manager_name: this.name });
      this.loading = false;
      if (data) {
        this.fund_hold = data.data.sort((a, b) => {
          if (this.$route.query.id.split(',').indexOf(a.code) > this.$route.query.id.split(',').indexOf(b.code)) return 1;
          else return -1;
        });
        let temp = [['管理年限'], ['管理规模'], ['年化收益'], ['代表基金']];
        for (let i = 0; i < this.fund_hold.length; i++) {
          temp[0].push(Number(this.fund_hold[i].managed_year).toFixed(2) + '年');
          temp[1].push(Number(this.fund_hold[i].netasset / 100000000).toFixed(2) + '亿');
          temp[2].push(Number(this.fund_hold[i].avg_return).toFixed(2) + '%');
          temp[3].push(this.fund_hold[i].fund_name);
        }
        this.fund_holdS = temp;
      }
    },
    submitmodal () {
      if (this.endSelect.length <= 1 || this.endSelect.length > 4) {
        this.$message.error('请选择对比项目大于一支且小于五支基金');
      } else {
        let tempcode = '';
        let tempname = '';

        if (this.valuex == 'equity') {
          for (let i = 0; i < this.endSelect.length; i++) {
            tempcode = tempcode + this.endSelect[i].code + ',';
            tempname = tempname + this.endSelect[i].name + ',';
          }
          tempcode = tempcode.slice(0, tempcode.length - 1);
          tempname = tempname.slice(0, tempname.length - 1);
          this.$router.push({
            path: '/managercompare',
            query: {
              id: tempcode,
              type: this.valuex,
              name: tempname
            }
          });
        } else {
          this.$message('对于基金经理的比较我们只提供主动权益类型的比较，债券类型的比较以具体产品比较更优');
        }
      }
    },
    // 打印word
    async exportWord () {
      this.printLoading = this.$loading({
        lock: true,
        text: '正在打印word报告，请稍后...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      let managerData = [];
      this.fund_holdS.map((item, i) => {
        let list = [];
        if (i == 0) {
          item.map((val, index) => {
            if (index == 0) {
              list.push('');
            } else {
              if (this.endSelect[index - 1]) {
                list.push(this.endSelect[index - 1].name);
              }
            }
          });
          managerData.push(list, item);
        } else {
          managerData.push(item);
        }
      });
      let title = this.endSelect
        .map((item) => {
          return item.name;
        })
        .join(' vs ');
      let basicInfoList = [
        { label: '基金经理名称', value: 'name_x' },
        { label: '基金公司', value: 'glr' },
        { label: '任职日期', value: 'founddate' },
        { label: '累计收益', value: 'cnav', format: 'fix2p' }
      ];
      let basicInfoData = this.fund_hold;
      let downloadList = [
        ...this.$exportWord.exportFirstTitle('慧捕基深度比较: ' + title),
        ...this.$exportWord.exportFirstTitle('基础信息'),
        ...this.$exportWord.exportTitle('基金基础信息'),
        ...this.$exportWord.exportTable(basicInfoList, basicInfoData),
        ...this.$exportWord.exportTitle('基金经理基础信息'),
        ...this.$exportWord.exportCompareTable(managerData),
        ...this.$refs['returns'].createPrintWord()
      ];
      // 权益基金对比
      if (this.type == 'equity') {
        downloadList.push(
          ...(await this.$refs['equityrank'].createPrintWord()),
          ...this.$refs['holdstockweight'].createPrintWord(),
          ...this.$refs['normalfund'].createPrintWord(),
          ...this.$refs['tablefundrank'].createPrintWord(),
          ...this.$refs['sameholdstock'].createPrintWord(),
          ...this.$refs['holdstock'].createPrintWord(),
          ...this.$refs['stockmanaged'].createPrintWord(),
          ...this.$refs['sameholdindustry'].createPrintWord(),
          ...this.$refs['diffholdindustry'].createPrintWord(),
          ...this.$refs['holdindustry'].createPrintWord(),
          ...this.$refs['industryability'].createPrintWord(),
          ...(await this.$refs['industryscore'].createPrintWord()),
          ...this.$refs['buysells'].createPrintWord(),
          ...this.$refs['ManagerBuySellMod'].createPrintWord(),
          ...(await this.$refs['timescore'].createPrintWord()),
          ...(await this.$refs['theme'].createPrintWord())
        );
      } else if (this.type == 'bond') {
        downloadList.push(
          ...(await this.$refs['bondrank'].createPrintWord()),
          ...this.$refs['holdstockweight'].createPrintWord(),
          ...this.$refs['normalfund'].createPrintWord(),
          ...this.$refs['tablefundrank'].createPrintWord(),
          ...this.$refs['bigmoney'].createPrintWord(),
          ...this.$refs['detailmoney'].createPrintWord(),
          ...this.$refs['sameholdstock'].createPrintWord(),
          ...this.$refs['holdstock'].createPrintWord(),
          ...this.$refs['stockmanaged'].createPrintWord(),
          ...this.$refs['sameholdindustry'].createPrintWord(),
          ...this.$refs['diffholdindustry'].createPrintWord(),
          ...this.$refs['holdindustry'].createPrintWord(),
          ...this.$refs['industryability'].createPrintWord(),
          ...(await this.$refs['industryscore'].createPrintWord()),
          ...this.$refs['buysells'].createPrintWord(),
          ...this.$refs['holdbond'].createPrintWord(),
          ...this.$refs['longdurations'].createPrintWord(),
          ...this.$refs['creditdown'].createPrintWord(),
          ...(await this.$refs['niuxiong'].createPrintWord()),
          ...this.$refs['barra'].createPrintWord()
        );
      } else if (this.type == 'bill' || this.type == 'purebond' || this.type == 'cbond') {
        downloadList.push(
          ...(await this.$refs['otherbondrank'].createPrintWord()),
          ...this.$refs['holdstockweight'].createPrintWord(),
          ...this.$refs['normalfund'].createPrintWord(),
          ...this.$refs['tablefundrank'].createPrintWord(),
          ...this.$refs['bigmoney'].createPrintWord(),
          ...this.$refs['detailmoney'].createPrintWord(),
          ...this.$refs['holdbond'].createPrintWord(),
          ...this.$refs['longdurations'].createPrintWord(),
          ...this.$refs['creditdown'].createPrintWord()
        );
      }
      let imgType = 'image/png';
      var xhr = new XMLHttpRequest();
      xhr.responseType = 'arraybuffer';
      xhr.open('GET', 'https://cdn.owl-portfolio.com/img/logoForWord.png', true);
      xhr.onload = () => {
        var result = xhr.response;
        var file = new File([result], 'foo.' + imgType.match(/\/([A-Za-z]+)/)[1], {
          type: imgType
        });
        var reader = new FileReader();
        reader.onload = (evt) => {
          // callBack(evt.target.result);
          this.$exportWord.downloadWord(title, [...downloadList], evt.target.result);
          this.printLoading.close();
        };
        reader.readAsDataURL(file);
      };
      xhr.send(null);
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () {
    this.id = this.$route.query.id;
    this.name = this.$route.query.name;
    this.type = this.$route.query.type;
    if (this.$route.query.model_flag) {
      this.model_flag = this.$route.query.model_flag;
    }
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () {
    this.getDatas();
    if (this.model_flag != 0) {
      this.getMemory();
    }
    let t1 = this.id.split(',');
    let t2 = this.name.split(',');
    this.endSelect = [];
    for (let i = 0; i < t1.length; i++) {
      this.endSelect.push({ name: t2[i], code: t1[i] });
    }
  },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scope>
.compareBox {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: flex-start;
	padding: 0px;
	gap: 26px;
	background: #fafafa;
	border-radius: 4px;
	flex: none;
	order: 0;
	flex-grow: 0;
	flex: 1;
	margin-left: 10px;
	.name {
		font-style: normal;
		font-weight: 500;
		font-size: 16px;
		line-height: 24px;
		color: rgba(0, 0, 0, 0.85);
		flex: none;
		order: 0;
		flex-grow: 0;
	}
	.company {
		font-style: normal;
		font-weight: 400;
		font-size: 14px;
		line-height: 22px;
		color: rgba(0, 0, 0, 0.65);
		flex: none;
		order: 1;
		flex-grow: 0;
	}
	.date {
		font-style: normal;
		font-weight: 400;
		font-size: 14px;
		line-height: 22px;
		color: rgba(0, 0, 0, 0.45);
	}
	.nav {
		font-style: normal;
		font-weight: 400;
		font-size: 14px;
		line-height: 22px;
		color: rgba(0, 0, 0, 0.45);
	}
}
.contentCompare {
	margin-top: 16px;
	background: #fff;
	padding: 16px 24px;
}
.contentCompare2 {
	margin-top: -8px;
	margin-left: -24px;
	// padding: 16px 24px;
}
.coloriconFlag {
	width: 4px;
	height: 24px;
	position: absolute;
	border-radius: 15px;
	flex: none;
	order: 0;
	flex-grow: 0;
	top: 20px;
}

.compareFixHeader {
	width: 100%;
	height: 178px;
	background: #fff;
	box-shadow: 0px 5px 12px 4px rgba(0, 0, 18, 0.02);
	position: -webkit-sticky;
	position: sticky !important;
	top: 0px;
	z-index: 100;
	margin-bottom: 16px;
	display: flex;
	padding: 16px 24px;
}
.comparemanager .ant-table-tbody > tr > td,
.ant-table-thead > tr > th {
	padding: 3px !important;
}
</style>
