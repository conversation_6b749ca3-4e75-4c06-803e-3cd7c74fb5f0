<!--  -->
<template>
  <div class="dialogFilter">
    <el-dialog
      class="dialogFilerD"
      :visible.sync="openFilterFlag"
      width="1000px"
      :close-on-click-modal="false"
      :show-close="false"
    >
      <div style="width: 100%">
        <el-row :gutter="10">
          <el-col :span="7">
            <div style="width: 100%; text-align: left">
              <h3 style="font-weight: 600; margin-bottom: 10px">筛选条件</h3>
              <el-scrollbar style="height: 60vh">
                <el-tree :data="data" :props="defaultProps" @node-click="handleNodeClick"></el-tree>
              </el-scrollbar>
            </div>
          </el-col>
          <el-col :span="17">
            <div style="width: 100%; text-align: left">
              <h3 style="font-weight: 600; margin-bottom: 10px">条件内容</h3>
              <el-scrollbar style="height: 60vh">
                <div>
                  <!-- 写死的 -->
                  <div class="boxItemDetail">
                    <div class="titleContent">计算区间</div>
                    <div class="contentBoxFilter">
                      <div style="display: flex; align-items: center" class="contentItem">
                        <el-radio v-model="radio" label="latest">最近一期持仓</el-radio>
                        <el-radio v-model="radio" label="3">最近3年</el-radio>
                        <el-radio v-model="radio" label="6">最近6年</el-radio>
                        <el-radio v-model="radio" label="created">成立以来</el-radio>
                        <el-radio v-model="radio" label="radioSelf">
                          <span>自定义</span>
                          <el-input v-model="radioSelf" placeholder="年" style="width: 60px"></el-input>
                        </el-radio>
                      </div>
                      <div class="contentDel">
                        <!-- <i class="el-icon-error" style="font-size:20px;color:#f56c6c;"></i> -->
                      </div>
                    </div>
                  </div>
                  <div class="boxItemDetail">
                    <div style="display: flex; align-items: center" class="contentItem">
                      <el-radio v-model="radioFilterOrOut" :label="true">
                        以下条件作为
                        <span style="color: red">筛选条件</span>
                      </el-radio>
                      <el-radio v-model="radioFilterOrOut" :label="false">
                        以下条件作为
                        <span style="color: red">排除条件</span>
                      </el-radio>
                    </div>
                  </div>
                  <!-- 动态数据部分 -->
                  <div v-for="(item, index) in listSelect" :key="index">
                    <div v-if="item.labelIndex == 'a'">
                      <div class="boxItemDetail">
                        <div class="titleContent">基金经理规模</div>
                        <div
                          v-for="(items, index1) in item.data"
                          :key="index1"
                          class="contentBoxFilter"
                        >
                          <div style="display: flex; align-items: center" class="contentItem">
                            <boxOnlyYSF
                              :ref="'boxOnlyYSF' + index + '_' + index1"
                              @boxOnlyYSFChange="boxOnlyYSFChange"
                              :indexFlag="index1"
                              :baseIndexFlag="index"
                              placeholder="亿"
                              :dataX="items"
                            ></boxOnlyYSF>
                          </div>
                          <div class="contentDel" @click="del(index, index1)">
                            <i
                              class="el-icon-error"
                              style="cursor: pointer; font-size: 20px; color: #f56c6c"
                            ></i>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div v-else-if="item.labelIndex == 'b'">
                      <div class="boxItemDetail">
                        <div class="titleContent">基金经理管理经验</div>
                        <div
                          v-for="(items, index1) in item.data"
                          :key="index1"
                          class="contentBoxFilter"
                        >
                          <div style="display: flex; align-items: center" class="contentItem">
                            <boxOnlyYSF
                              :ref="'boxOnlyYSF' + index + '_' + index1"
                              @boxOnlyYSFChange="boxOnlyYSFChange"
                              :indexFlag="index1"
                              :baseIndexFlag="index"
                              placeholder="年"
                              :dataX="items"
                            ></boxOnlyYSF>
                          </div>
                          <div class="contentDel" @click="del(index, index1)">
                            <i
                              class="el-icon-error"
                              style="cursor: pointer; font-size: 20px; color: #f56c6c"
                            ></i>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div v-else-if="item.labelIndex == 'c'">
                      <div class="boxItemDetail">
                        <div class="titleContent">基金规模</div>
                        <div
                          v-for="(items, index1) in item.data"
                          :key="index1"
                          class="contentBoxFilter"
                        >
                          <div style="display: flex; align-items: center" class="contentItem">
                            <boxOnlyYSF
                              :ref="'boxOnlyYSF' + index + '_' + index1"
                              @boxOnlyYSFChange="boxOnlyYSFChange"
                              :indexFlag="index1"
                              :baseIndexFlag="index"
                              placeholder="亿"
                              :dataX="items"
                            ></boxOnlyYSF>
                          </div>
                          <div class="contentDel" @click="del(index, index1)">
                            <i
                              class="el-icon-error"
                              style="cursor: pointer; font-size: 20px; color: #f56c6c"
                            ></i>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div v-else-if="item.labelIndex == 'd'">
                      <div class="boxItemDetail">
                        <div class="titleContent">可申购金额</div>
                        <div
                          v-for="(items, index1) in item.data"
                          :key="index1"
                          class="contentBoxFilter"
                        >
                          <div style="display: flex; align-items: center" class="contentItem">
                            <boxOnlyYSF
                              :ref="'boxOnlyYSF' + index + '_' + index1"
                              @boxOnlyYSFChange="boxOnlyYSFChange"
                              :indexFlag="index1"
                              :baseIndexFlag="index"
                              placeholder="十万"
                              :dataX="items"
                            ></boxOnlyYSF>
                          </div>
                          <div class="contentDel" @click="del(index, index1)">
                            <i
                              class="el-icon-error"
                              style="cursor: pointer; font-size: 20px; color: #f56c6c"
                            ></i>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div v-else-if="item.labelIndex == 'e'">
                      <div class="boxItemDetail">
                        <div class="titleContent">基金分类</div>
                        <div
                          v-for="(items, index1) in item.data"
                          :key="index1"
                          class="contentBoxFilter"
                        >
                          <div style="display: flex; align-items: center" class="contentItem">
                            <fundCateOnly
                              @fundCateOnlyChange="fundCateOnlyChange"
                              :fundType="fundType"
                              :ref="'fundCateOnly' + index + '_' + index1"
                              :indexFlag="index1"
                              :baseIndexFlag="index"
                              :dataX="items"
                            ></fundCateOnly>
                          </div>
                          <div class="contentDel" @click="del(index, index1)">
                            <i
                              class="el-icon-error"
                              style="cursor: pointer; font-size: 20px; color: #f56c6c"
                            ></i>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div v-else-if="item.labelIndex == 'f'">
                      <div class="boxItemDetail">
                        <div class="titleContent">基金持有人特征</div>
                        <div
                          v-for="(items, index1) in item.data"
                          :key="index1"
                          class="contentBoxFilter"
                        >
                          <div style="display: flex; align-items: center" class="contentItem">
                            <holdPersonOnly
                              @fundCateOnlyChange="fundCateOnlyChange"
                              :fundType="fundType"
                              :ref="'holdPersonOnly' + index + '_' + index1"
                              :indexFlag="index1"
                              :baseIndexFlag="index"
                              :dataX="items"
                            ></holdPersonOnly>
                          </div>
                          <div class="contentDel" @click="del(index, index1)">
                            <i
                              class="el-icon-error"
                              style="cursor: pointer; font-size: 20px; color: #f56c6c"
                            ></i>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div v-else-if="item.labelIndex == 'g'">
                      <div class="boxItemDetail">
                        <div class="titleContent">风险特征</div>
                        <div
                          v-for="(items, index1) in item.data"
                          :key="index1"
                          class="contentBoxFilter"
                        >
                          <div style="display: flex; align-items: center" class="contentItem">
                            <boxNameYSF
                              :haveName="items.labelName"
                              @boxOnlyYSFNameChange="boxOnlyYSFNameChange"
                              placeholder="前百分之,如10,表示TOP10%"
                              :fundType="fundType"
                              :ref="'boxNameYSF' + index + '_' + index1"
                              :indexFlag="index1"
                              :baseIndexFlag="index"
                              :dataX="items"
                            ></boxNameYSF>
                          </div>
                          <div class="contentDel" @click="del(index, index1)">
                            <i
                              class="el-icon-error"
                              style="cursor: pointer; font-size: 20px; color: #f56c6c"
                            ></i>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div v-else-if="item.labelIndex == 'h'">
                      <div class="boxItemDetail">
                        <div class="titleContent">风险收益特征</div>
                        <div
                          v-for="(items, index1) in item.data"
                          :key="index1"
                          class="contentBoxFilter"
                        >
                          <div style="display: flex; align-items: center" class="contentItem">
                            <boxNameYSF
                              :haveName="items.labelName"
                              @boxOnlyYSFNameChange="boxOnlyYSFNameChange"
                              placeholder="前百分之,如10,表示TOP10%"
                              :fundType="fundType"
                              :ref="'boxNameYSF' + index + '_' + index1"
                              :indexFlag="index1"
                              :baseIndexFlag="index"
                              :dataX="items"
                            ></boxNameYSF>
                          </div>
                          <div class="contentDel" @click="del(index, index1)">
                            <i
                              class="el-icon-error"
                              style="cursor: pointer; font-size: 20px; color: #f56c6c"
                            ></i>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div v-else-if="item.labelIndex == 'i'">
                      <div class="boxItemDetail">
                        <div class="titleContent">风格偏好</div>
                        <div
                          v-for="(items, index1) in item.data"
                          :key="index1"
                          class="contentBoxFilter"
                        >
                          <div style="display: flex; align-items: center" class="contentItem">
                            <boxCzJzOnly
                              :haveName="items.labelName"
                              @boxCzJzOnlyChange="boxCzJzOnlyChange"
                              placeholder="前百分之,如10,表示TOP10%"
                              :fundType="fundType"
                              :ref="'boxCzJzOnly' + index + '_' + index1"
                              :indexFlag="index1"
                              :baseIndexFlag="index"
                              :dataX="items"
                            ></boxCzJzOnly>
                          </div>
                          <!-- <div v-if="items.labelName=='估值'" style="display:flex;align-items:center" class='contentItem'>
                                                      <boxCzJzOnly2 :haveName='items.labelName' @boxCzJzOnlyChange='boxCzJzOnlyChange' placeholder='前百分之,如10,表示TOP10%' :fundType="fundType" :ref="'boxCzJzOnly2'+index+'_'+index1"  :indexFlag='index1' :baseIndexFlag='index'    :dataX="items" ></boxCzJzOnly2>
                          </div>-->
                          <div class="contentDel" @click="del(index, index1)">
                            <i
                              class="el-icon-error"
                              style="cursor: pointer; font-size: 20px; color: #f56c6c"
                            ></i>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div v-else-if="item.labelIndex == 'j'">
                      <div class="boxItemDetail">
                        <div class="titleContent">行业判断</div>
                        <div
                          v-for="(items, index1) in item.data"
                          :key="index1"
                          class="contentBoxFilter"
                        >
                          <div
                            v-if="items.labelName != '大行业'"
                            style="display: flex; align-items: center"
                            class="contentItem"
                          >
                            <industryTheme
                              :dataIndustry="dataIndustry"
                              :haveName="items.labelName"
                              @industryThemeChange="industryThemeChange"
                              placeholder="前百分之,如10,表示TOP10%"
                              :fundType="fundType"
                              :ref="'industryTheme' + index + '_' + index1"
                              :indexFlag="index1"
                              :baseIndexFlag="index"
                              :dataX="items"
                            ></industryTheme>
                          </div>
                          <div
                            v-if="items.labelName == '大行业'"
                            style="display: flex; align-items: center"
                            class="contentItem"
                          >
                            <industryBig
                              :dataIndustry="dataIndustry"
                              :haveName="items.labelName"
                              @industryThemeChange="industryThemeChange"
                              placeholder="前百分之,如10,表示TOP10%"
                              :fundType="fundType"
                              :ref="'industryBig' + index + '_' + index1"
                              :indexFlag="index1"
                              :baseIndexFlag="index"
                              :dataX="items"
                            ></industryBig>
                          </div>
                          <div class="contentDel" @click="del(index, index1)">
                            <i
                              class="el-icon-error"
                              style="cursor: pointer; font-size: 20px; color: #f56c6c"
                            ></i>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div v-else-if="item.labelIndex == 'k'">
                      <div class="boxItemDetail">
                        <div class="titleContent">主题判断</div>
                        <div
                          v-for="(items, index1) in item.data"
                          :key="index1"
                          class="contentBoxFilter"
                        >
                          <div style="display: flex; align-items: center" class="contentItem">
                            <industryTheme
                              :dataIndustry="dataIndustry"
                              :haveName="items.labelName"
                              @industryThemeChange="industryThemeChange"
                              placeholder="前百分之,如10,表示TOP10%"
                              :fundType="fundType"
                              :ref="'industryTheme' + index + '_' + index1"
                              :indexFlag="index1"
                              :baseIndexFlag="index"
                              :dataX="items"
                            ></industryTheme>
                          </div>
                          <div class="contentDel" @click="del(index, index1)">
                            <i
                              class="el-icon-error"
                              style="cursor: pointer; font-size: 20px; color: #f56c6c"
                            ></i>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div v-else-if="item.labelIndex == 'l'">
                      <div class="boxItemDetail">
                        <div class="titleContent">指数匹配</div>
                        <div
                          v-for="(items, index1) in item.data"
                          :key="index1"
                          class="contentBoxFilter"
                        >
                          <div style="display: flex; align-items: center" class="contentItem">
                            <indexOnly
                              :dataIndustry="dataIndustry"
                              :haveName="items.labelName"
                              @indexOnlyChange="indexOnlyChange"
                              placeholder="前百分之,如10,表示TOP10%"
                              :fundType="fundType"
                              :ref="'indexOnly' + index + '_' + index1"
                              :indexFlag="index1"
                              :baseIndexFlag="index"
                              :dataX="items"
                            ></indexOnly>
                          </div>
                          <div class="contentDel" @click="del(index, index1)">
                            <i
                              class="el-icon-error"
                              style="cursor: pointer; font-size: 20px; color: #f56c6c"
                            ></i>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div v-else-if="item.labelIndex == 'm'">
                      <div class="boxItemDetail">
                        <div class="titleContent">其他</div>
                        <div
                          v-for="(items, index1) in item.data"
                          :key="index1"
                          class="contentBoxFilter"
                        >
                          <div style="display: flex; align-items: center" class="contentItem">
                            <boxOnlyYSF
                              :haveName="items.labelName"
                              @boxOnlyYSFChange="boxOnlyYSFChange"
                              :placeholder="items.labelName == 'ROE' ? 'ROE的值' : '前百分之,如10,表示TOP10%'"
                              :fundType="fundType"
                              :ref="'boxNameYSF' + index + '_' + index1"
                              :indexFlag="index1"
                              :baseIndexFlag="index"
                              :dataX="items"
                            ></boxOnlyYSF>
                          </div>
                          <div class="contentDel" @click="del(index, index1)">
                            <i
                              class="el-icon-error"
                              style="cursor: pointer; font-size: 20px; color: #f56c6c"
                            ></i>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-scrollbar>
            </div>
          </el-col>
        </el-row>
        <div class="height10border"></div>
        <div style="text-align: right" class="demo-drawer__footer">
          <el-button @click="out(1)" type>取消</el-button>
          <el-button type="primary" @click="out(2)">确定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Holdperson from "../../../tools/alphafof/analysis/components/holdperson.vue";
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import boxOnlyYSF from "./boxOnlyYSF";
import fundCateOnly from "./fundCateOnly";
import holdPersonOnly from "./holdPersonOnly";
import boxNameYSF from "./boxNameYSF";
import industryTheme from "./industryTheme";
import indexOnly from "./indexOnly";
import boxCzJzOnly from "./boxCzJzOnly";
import boxCzJzOnly2 from "./boxCzJzOnly2";
import industryBig from "./industryBig";
export default {
  props: {
    openFilterFlag: {
      type: Boolean,
      default: false
    },
    dataIndustry: {
      type: Object
    },
    listSelectX: {
      type: Array
    },
    radioType2: {
      type: String,
      default: "latest"
    },
    radioInput2: {
      type: String,
      default: ""
    },
    isSame2: {
      type: Boolean,
      default: true
    }
  },
  //import引入的组件需要注入到对象中才能使用
  components: {
    Holdperson,
    boxOnlyYSF,
    fundCateOnly,
    holdPersonOnly,
    boxNameYSF,
    industryTheme,
    indexOnly,
    boxCzJzOnly,
    boxCzJzOnly2,
    industryBig
  },
  data() {
    //这里存放数据
    return {
      fundType: "equity",
      listSelect: [],
      filterData: {},
      radioFilterOrOut: true,
      radioSelf: null,
      radio: "latest",
      data: [
        {
          label: "基金经理规模",
          typeCate: "1", //1为运算符
          type: "a"
        },
        {
          label: "基金经理管理经验",
          typeCate: "1",
          type: "b"
        },
        {
          label: "基金规模",
          typeCate: "1",
          type: "c"
        },
        {
          label: "可申购金额",
          typeCate: "1",
          type: "d"
        },
        {
          label: "基金分类",
          typeCate: "2", //2为基金分类特有
          type: "e"
        },
        {
          label: "基金持有人特征",
          typeCate: "3",
          type: "f" //3为持有人特有
        },
        {
          label: "风险特征",
          children: [
            {
              label: "波动率",
              typeCate: "4", //4 name+运算符
              type: "g"
            },
            {
              label: "最大回撤",
              typeCate: "4", //4 name+运算符
              type: "g"
            },
            {
              label: "平均下行周期",
              typeCate: "4", //4 name+运算符
              type: "g"
            },
            {
              label: "平均恢复周期",
              typeCate: "4", //4 name+运算符
              type: "g"
            },
            {
              label: "最大回撤比",
              typeCate: "4", //4 name+运算符
              type: "g"
            },
            {
              label: "在险价值",
              typeCate: "4", //4 name+运算符
              type: "g"
            },
            {
              label: "期望损失",
              typeCate: "4", //4 name+运算符
              type: "g"
            },
            {
              label: "下行风险",
              typeCate: "4", //4 name+运算符
              type: "g"
            },
            {
              label: "波动率比",
              typeCate: "4", //4 name+运算符
              type: "g"
            },
            {
              label: "痛苦指数",
              typeCate: "4", //4 name+运算符
              type: "g"
            }
          ]
        },
        {
          label: "风险收益特征",
          children: [
            {
              label: "年化收益率",
              typeCate: "4", //4 name+运算符
              type: "h"
            },
            {
              label: "累计收益率",
              typeCate: "4", //4 name+运算符
              type: "h"
            },
            {
              label: "夏普率（rf==0）",
              typeCate: "4", //4 name+运算符
              type: "h"
            },
            {
              label: "夏普率（rf==4%）",
              typeCate: "4", //4 name+运算符
              type: "h"
            },
            {
              label: "夏普率（动态rf）",
              typeCate: "4", //4 name+运算符
              type: "h"
            },
            {
              label: "卡码率",
              typeCate: "4", //4 name+运算符
              type: "h"
            },
            {
              label: "索提诺系数（rf==0）",
              typeCate: "4", //4 name+运算符
              type: "h"
            },
            {
              label: "索提诺系数（rf==4%）",
              typeCate: "4", //4 name+运算符
              type: "h"
            },
            {
              label: "索提诺系数（动态rf）",
              typeCate: "4", //4 name+运算符
              type: "h"
            },
            {
              label: "稳定系数",
              typeCate: "4", //4 name+运算符
              type: "h"
            },
            {
              label: "凯利系数",
              typeCate: "4", //4 name+运算符
              type: "h"
            },
            {
              label: "信息比率",
              typeCate: "4", //4 name+运算符
              type: "h"
            },
            {
              label: "上攻潜力（周）",
              typeCate: "4", //4 name+运算符
              type: "h"
            },
            {
              label: "月胜率",
              typeCate: "4", //4 name+运算符
              type: "h"
            },
            {
              label: "詹森系数",
              typeCate: "4", //4 name+运算符
              type: "h"
            },
            {
              label: "特诺系数",
              typeCate: "4", //4 name+运算符
              type: "h"
            },
            {
              label: "上行捕获",
              typeCate: "4", //4 name+运算符
              type: "h"
            },
            {
              label: "下行捕获",
              typeCate: "4", //4 name+运算符
              type: "h"
            },
            {
              label: "择时gamma",
              typeCate: "4", //4 name+运算符
              type: "h"
            },
            {
              label: "M2",
              typeCate: "4", //4 name+运算符
              type: "h"
            }
          ]
        },
        {
          label: "风格偏好",
          children: [
            {
              label: "成长",
              typeCate: "5", //5 特有单选
              type: "i"
            },
            {
              label: "价值",
              typeCate: "6", //6 特有单选
              type: "i"
            },
            {
              label: "估值",
              typeCate: "6", //6 特有单选
              type: "i"
            }
          ]
        },
        {
          label: "行业判断",
          children: [
            {
              label: "大行业",
              typeCate: "10", //行业
              type: "j"
            },
            {
              label: "申万一级行业",
              typeCate: "7", //行业
              type: "j"
            },
            {
              label: "申万二级行业",
              typeCate: "7", //行业
              type: "j"
            },
            {
              label: "申万三级行业",
              typeCate: "7", //行业
              type: "j"
            },
            {
              label: "恒生一级行业",
              typeCate: "7", //行业
              type: "j"
            }
          ]
        },
        {
          label: "主题判断",
          typeCate: "7", //行业
          type: "k"
        },
        {
          label: "指数匹配",
          typeCate: "8", //特有
          type: "l"
        },
        {
          label: "其他",
          children: [
            {
              label: "前十大集中度",
              typeCate: "4", //name+运算符
              type: "m"
            },
            {
              label: "ROE",
              typeCate: "4", //name+运算符
              type: "m"
            },
            {
              label: "换手率",
              typeCate: "4", //name+运算符
              type: "m"
            }
          ]
        }
      ],
      defaultProps: {
        children: "children",
        label: "label"
      }
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {
    listSelectX(val) {
      // console.log("0000");
      this.listSelect = this.listSelectX;
    }
  },
  //方法集合
  methods: {
    boxOnlyYSFNameChange(e1, e2, e3, e4, e5, e6) {
      this.listSelect[e1].data[e2].dataResult = [
        { flag: e4, value: e3, date: e5, option: e6 }
      ];
    },
    industryThemeChange(e1, e2, e3, e4, e5, e6) {
      this.listSelect[e1].data[e2].dataResult = [
        { flag: e4, value: e3, industryValue: e5, industryName: e6 }
      ];
    },
    bigOnlyChange(e1, e2, e3) {
      this.listSelect[e1].data[e2].dataResult = [{ value: e3 }];
    },
    indexOnlyChange(e1, e2, e3, e4, e5, e6) {
      this.listSelect[e1].data[e2].dataResult = [
        { flag: e4, value: e3, index_code: e5, index_code_options: e6 }
      ];
      console.log(this.listSelect);
    },
    boxOnlyYSFChange(e1, e2, e3, e4) {
      this.listSelect[e1].data[e2].dataResult = [{ flag: e4, value: e3 }];
    },
    boxCzJzOnlyChange(e1, e2, e3, e4, e5, e6, e7) {
      this.listSelect[e1].data[e2].dataResult = [
        { flag: e4, value: e3, label: e5, yearqtr: e6, rank_value: e7 }
      ];
    },
    fundCateOnlyChange(e1, e2, e3) {
      this.listSelect[e1].data[e2].dataResult = [{ value: e3 }];
    },
    del(item, index) {
      if (this.listSelect[item].data.length > 1) {
        this.listSelect[item].data.splice(index, 1);
      } else {
        this.listSelect.splice(item, 1);
      }
    },
    out(flag) {
      console.log(this.listSelect);
      if (flag == 1) {
        this.$emit("closeDialog", []);
      } else {
        let flagX = 0;
        for (let i = 0; i < this.listSelect.length; i++) {
          let temp = [];
          let tempName = [];

          for (let j = 0; j < this.listSelect[i].data.length; j++) {
            if (this.listSelect[i].data[j].dataResult[0].flag) {
              if (
                this.listSelect[i].data[j].dataResult[0].flag == "all" ||
                this.listSelect[i].data[j].dataResult[0].flag == ">" ||
                this.listSelect[i].data[j].dataResult[0].flag == "<" ||
                this.listSelect[i].data[j].dataResult[0].flag == "<=" ||
                this.listSelect[i].data[j].dataResult[0].flag == ">=" ||
                this.listSelect[i].data[j].dataResult[0].flag == "="
              ) {
                if (this.listSelect[i].data[j].dataResult[0].industryName) {
                  if (
                    tempName.indexOf(
                      this.listSelect[i].data[j].dataResult[0].industryName
                    ) < 0
                  ) {
                    tempName.push(
                      this.listSelect[i].data[j].dataResult[0].industryName
                    );
                    temp[tempName.length - 1] = [];
                    temp[tempName.length - 1].push(
                      this.listSelect[i].data[j].dataResult[0]
                    );
                  } else {
                    temp[
                      tempName.indexOf(
                        this.listSelect[i].data[j].dataResult[0].industryName
                      )
                    ].push(this.listSelect[i].data[j].dataResult[0]);
                  }
                } else if (
                  this.listSelect[i].data[j].dataResult[0].index_code
                ) {
                  if (
                    tempName.indexOf(
                      this.listSelect[i].data[j].dataResult[0].index_code
                    ) < 0
                  ) {
                    tempName.push(
                      this.listSelect[i].data[j].dataResult[0].index_code
                    );
                    temp[tempName.length - 1] = [];
                    temp[tempName.length - 1].push(
                      this.listSelect[i].data[j].dataResult[0]
                    );
                  } else {
                    temp[
                      tempName.indexOf(
                        this.listSelect[i].data[j].dataResult[0].index_code
                      )
                    ].push(this.listSelect[i].data[j].dataResult[0]);
                  }
                } else if (this.listSelect[i].data[j].dataResult[0].date) {
                  if (
                    tempName.indexOf(
                      this.listSelect[i].data[j].labelName +
                        this.listSelect[i].data[j].dataResult[0].date
                    ) < 0
                  ) {
                    tempName.push(
                      this.listSelect[i].data[j].labelName +
                        this.listSelect[i].data[j].dataResult[0].date
                    );
                    temp[tempName.length - 1] = [];
                    temp[tempName.length - 1].push(
                      this.listSelect[i].data[j].dataResult[0]
                    );
                  } else {
                    temp[
                      tempName.indexOf(
                        this.listSelect[i].data[j].labelName +
                          this.listSelect[i].data[j].dataResult[0].date
                      )
                    ].push(this.listSelect[i].data[j].dataResult[0]);
                  }
                } else if (this.listSelect[i].data[j].dataResult[0].yearqtr) {
                  console.log(
                    this.listSelect[i].data[j].labelName +
                      this.listSelect[i].data[j].dataResult[0].yearqtr +
                      this.listSelect[i].data[j].dataResult[0].rank_value
                  );
                  if (
                    tempName.indexOf(
                      this.listSelect[i].data[j].labelName +
                        this.listSelect[i].data[j].dataResult[0].yearqtr +
                        this.listSelect[i].data[j].dataResult[0].rank_value
                    ) < 0
                  ) {
                    tempName.push(
                      this.listSelect[i].data[j].labelName +
                        this.listSelect[i].data[j].dataResult[0].yearqtr +
                        this.listSelect[i].data[j].dataResult[0].rank_value
                    );
                    temp[tempName.length - 1] = [];
                    temp[tempName.length - 1].push(
                      this.listSelect[i].data[j].dataResult[0]
                    );
                  } else {
                    temp[
                      tempName.indexOf(
                        this.listSelect[i].data[j].labelName +
                          this.listSelect[i].data[j].dataResult[0].yearqtr +
                          this.listSelect[i].data[j].dataResult[0].rank_value
                      )
                    ].push(this.listSelect[i].data[j].dataResult[0]);
                  }
                } else {
                  if (
                    tempName.indexOf(this.listSelect[i].data[j].labelName) < 0
                  ) {
                    tempName.push(this.listSelect[i].data[j].labelName);
                    temp[tempName.length - 1] = [];
                    temp[tempName.length - 1].push(
                      this.listSelect[i].data[j].dataResult[0]
                    );
                  } else {
                    temp[
                      tempName.indexOf(this.listSelect[i].data[j].labelName)
                    ].push(this.listSelect[i].data[j].dataResult[0]);
                  }
                }
              }
            }
          }
          console.log(temp);
          let tempAll = [];
          for (let x = 0; x < temp.length; x++) {
            if (temp[x].length > 1) {
              let min = [-9999999999];
              let max = [999999999];
              let DY = [];
              for (let p = 0; p < temp[x].length; p++) {
                if (
                  (temp[x][p].flag == "<" || temp[x][p].flag == "<=") &&
                  (temp[x][p].value != "" || temp[x][p].value != null)
                )
                  max.push(temp[x][p].value);
                if (
                  (temp[x][p].flag == ">" || temp[x][p].flag == ">=") &&
                  (temp[x][p].value != "" || temp[x][p].value != null)
                )
                  min.push(temp[x][p].value);
                if (
                  temp[x][p].flag == "=" &&
                  (temp[x][p].value != "" || temp[x][p].value != null)
                )
                  DY.push(temp[x][p].value);
              }
              if (DY.length > 1) {
                flagX = 1;
                this.$message.error(
                  tempName[x] + "算数逻辑有误，不能存在两个等于。"
                );
              } else if (Math.min(...max) < Math.max(...min)) {
                flagX = 1;
                this.$message.error(
                  tempName[x] + "算数逻辑有误，大于对应的值比小于对应的值小了。"
                );
              } else if (DY.length == 1) {
                if (DY[0] < Math.max(...min) || DY[0] > Math.min(...max)) {
                  flagX = 1;
                  this.$message.error(
                    tempName[x] + "算数逻辑有误，等于的值在大于小于的范围之外。"
                  );
                } else {
                  tempAll.push(
                    this.listSelect[i].data[
                      this.listSelect[i].data.findIndex(
                        item =>
                          item.dataResult[0].flag == "=" &&
                          item.dataResult[0].value == DY[0]
                      )
                    ]
                  );
                }
              } else {
                flagX = 0;
                let t1 = {};
                let t2 = {};
                if (Math.max(...min) != -9999999999) {
                  t1 = this.listSelect[i].data[
                    this.listSelect[i].data.findIndex(
                      item =>
                        (item.dataResult[0].flag == ">" ||
                          item.dataResult[0].flag == ">=") &&
                        item.dataResult[0].value == Math.max(...min)
                    )
                  ];
                }
                if (Math.min(...max) != 999999999) {
                  t2 = this.listSelect[i].data[
                    this.listSelect[i].data.findIndex(
                      item =>
                        (item.dataResult[0].flag == "<" ||
                          item.dataResult[0].flag == "<=") &&
                        item.dataResult[0].value == Math.min(...max)
                    )
                  ];
                }
                // this.listSelect[i].data= []
                if (JSON.stringify(t1) != "{}") tempAll.push(t1);
                if (JSON.stringify(t2) != "{}") tempAll.push(t2);
              }
            } else {
              console.log("object");

              console.log(
                this.listSelect[i].data[
                  this.listSelect[i].data.findIndex(
                    item =>
                      tempName[x].indexOf(item.labelName) >= 0 &&
                      item.dataResult[0].flag == temp[x][0].flag &&
                      item.dataResult[0].value == temp[x][0].value
                  )
                ]
              );
              //  this.listSelect[i].data.push(temp[x])
              // this.listSelect[i].data[this.listSelect[i].data.findIndex(item => (item.dataResult[0].flag=='<'||item.dataResult[0].flag=='<=')&&item.dataResult[0].value==Math.min(...max))]
              tempAll.push(
                this.listSelect[i].data[
                  this.listSelect[i].data.findIndex(
                    item =>
                      tempName[x].indexOf(item.labelName) >= 0 &&
                      item.dataResult[0].flag == temp[x][0].flag &&
                      item.dataResult[0].value == temp[x][0].value
                  )
                ]
              );
            }
          }
          this.listSelect[i].data = tempAll;
        }
        console.log("xxxx");
        console.log(this.listSelect);
        if (flagX == 0) {
          this.$emit(
            "closeDialog",
            this.listSelect,
            this.radio,
            this.radioSelf,
            this.radioFilterOrOut
          );
        }
      }
    },
    isEqual(objA, objB) {
      //相等
      if (objA === objB) return objA !== 0 || 1 / objA === 1 / objB;
      //空判断
      if (objA == null || objB == null) return objA === objB;
      //类型判断
      if (
        Object.prototype.toString.call(objA) !==
        Object.prototype.toString.call(objB)
      )
        return false;

      switch (Object.prototype.toString.call(objA)) {
        case "[object RegExp]":
        case "[object String]":
          //字符串转换比较
          return "" + objA === "" + objB;
        case "[object Number]":
          //数字转换比较,判断是否为NaN
          if (+objA !== +objA) {
            return +objB !== +objB;
          }

          return +objA === 0 ? 1 / +objA === 1 / objB : +objA === +objB;
        case "[object Date]":
        case "[object Boolean]":
          return +objA === +objB;
        case "[object Array]":
          //判断数组
          for (let i = 0; i < objA.length; i++) {
            if (!this.isEqual(objA[i], objB[i])) return false;
          }
          return true;
        case "[object Object]":
          //判断对象
          let keys = Object.keys(objA);
          for (let i = 0; i < keys.length; i++) {
            if (!this.isEqual(objA[keys[i]], objB[keys[i]])) return false;
          }

          keys = Object.keys(objB);
          for (let i = 0; i < keys.length; i++) {
            if (!this.isEqual(objA[keys[i]], objB[keys[i]])) return false;
          }

          return true;
        default:
          return false;
      }
    },
    handleNodeClick(e) {
      if (
        this.listSelect.length == 0 &&
        e.type &&
        e.type != null &&
        e.type != undefined
      ) {
        this.listSelect.push({
          labelIndex: e.type,
          data: [
            {
              labelName: e.label,
              typeCate: e.typeCate,
              dataResult: []
            }
          ]
        });
      } else {
        if (e.type && e.type != null && e.type != undefined) {
          if (
            this.listSelect.findIndex(item => item.labelIndex == e.type) >= 0
          ) {
            this.listSelect[
              this.listSelect.findIndex(item => item.labelIndex == e.type)
            ].data.push({
              labelName: e.label,
              typeCate: e.typeCate,
              dataResult: []
            });
          } else {
            this.listSelect.push({
              labelIndex: e.type,
              data: [
                {
                  labelName: e.label,
                  typeCate: e.typeCate,
                  dataResult: []
                }
              ]
            });
          }
        }
      }
      // 分类 push for循环 if判断类型
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.listSelect = this.listSelectX;
    this.radioSelf = this.radioInput2;
    this.radio = this.radioType2;
    this.radioFilterOrOut = this.isSame2;
  },
  beforeCreate() {}, //生命周期 - 创建之前
  beforeMount() {}, //生命周期 - 挂载之前
  beforeUpdate() {}, //生命周期 - 更新之前
  updated() {}, //生命周期 - 更新之后
  beforeDestroy() {}, //生命周期 - 销毁之前
  destroyed() {}, //生命周期 - 销毁完成
  activated() {} //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
.dialogFilerD {
  .boxItemDetail {
    margin-top: 10px;
    margin-bottom: 15px;
  }
  ::v-deep .el-dialog__header {
    padding: 0px !important ;
  }
  .titleContent {
    font-size: 15px;
    font-weight: 600;
  }
  .contentBoxFilter {
    margin-top: 10px;
    display: flex;
    width: 100%;
    // height:40px;
    align-items: center;
    justify-content: start;
    .contentItem {
      padding: 15px;
      background: #f5f5f5;
      flex: 9;
    }
    .contentDel {
      flex: 1;
      margin-right: 10px;
      justify-content: end;
      display: flex;
    }
  }
}
</style>
