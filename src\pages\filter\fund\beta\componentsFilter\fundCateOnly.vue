<!--  -->
<template>
  <div class="fundCateOnly">
    <div v-show="haveName != ''"
         style="
				font-weight: 400;
				font-size: 14px;
				line-height: 22px;
				color: rgba(0, 0, 0, 0.85);
				margin-left: 0px;
				margin-right: 16px;
				margin-bottom: 4px;
			">
      {{ haveName }}
    </div>
    <div style="display: flex; align-items: center">
      <operator v-if="is_range"
                ref="operator"
                @resolveMathRange="resolveMathRange"></operator>
      <el-cascader v-model="equitytype"
                   @change="changeNode"
                   placeholder="请选择基金分类"
                   :options="radios == 'equity' ? optionstype : optionstypebond"
                   :props="{ expandTrigger: 'hover' }">
      </el-cascader>
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import operator from '@/pages/filter/fund/beta/componentsFilter/components/operator.vue';
export default {
  components: { operator },
  props: {
    is_range: {
      type: Boolean,
      default: false
    },
    haveName: {
      type: String,
      default: ''
    },
    dataX: {
      type: Object,
      default: {}
    },
    placeholder: {
      type: String
    },
    indexFlag: {
      type: Number
    },
    baseIndexFlag: {
      type: Number
    },
    fundType: {
      type: String,
      default: 'equity'
    },
    radios: {
      type: String,
      default: 'equity'
    },
    conditions: {
      default: {}
    }
  },
  data () {
    //这里存放数据
    return {
      equitytype: '',
      optionstype: [
        {
          value: '证监会分类',
          label: '证监会分类',
          children: [
            {
              value: '股票型基金',
              label: '股票型基金'
            },
            {
              value: '混合型基金',
              label: '混合型基金'
            }
          ]
        },
        {
          value: '慧捕基分类',
          label: '慧捕基分类',
          children: [
            {
              value: 'A股基金',
              label: 'A股基金'
            },
            {
              value: '沪港深基金',
              label: '沪港深基金'
            }
          ]
        },
        {
          value: '二级债',
          label: '二级债',
          children: [
            {
              label: '不限',
              value: '不限'
            },
            {
              label: '开放',
              value: '开放'
            },
            {
              label: '定开',
              value: '定开'
            },
            {
              label: '封闭',
              value: '封闭'
            }
          ]
        },
        {
          value: '偏债混合',
          label: '偏债混合',
          children: [
            {
              label: '不限',
              value: '不限'
            },
            {
              label: '开放',
              value: '开放'
            },
            {
              label: '定开',
              value: '定开'
            },
            {
              label: '封闭',
              value: '封闭'
            }
          ]
        },
        {
          value: '聚源二级分类',
          label: '聚源二级分类',
          children: [
            {
              label: '不限',
              value: '不限'
            }
          ]
        },
        {
          value: '泰康分类',
          label: '泰康分类',
          children: [
            {
              label: '不限',
              value: '不限'
            }
          ]
        }
      ],
      optionstypebond: [],
      math_range: {}
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {
    dataX (val) {
      if (val.dataResult && val.dataResult.length > 0) {
        this.equitytype = val.dataResult[0].value;
        if (this.$refs['operator']) {
          this.$refs['operator'].getFlag(val.dataResult[0].mathRange);
        }
      }
    }
  },
  //方法集合
  methods: {
    addType () {
      if (this.conditions?.tkCate) {
        this.optionstype = this.optionstype.push({ value: '泰康二级分类', label: '泰康二级分类', children: this.conditions?.tkCate })
        this.optionstypebond = this.optionstype.push({ value: '泰康二级分类', label: '泰康二级分类', children: this.conditions?.tkCate })
      }

      if (this.conditions?.jyCate) {
        this.optionstype = this.optionstype.push({ value: '聚源分类', label: '聚源分类', children: this.conditions?.jyCate })
        this.optionstypebond = this.optionstype.push({ value: '聚源分类', label: '聚源分类', children: this.conditions?.jyCate })
      }
    },
    resolveMathRange (obj) {
      this.math_range = obj;
      this.resolveFather();
    },
    changeNode (e) {
      this.resolveFather();
    },
    resolveFather () {
      this.$emit(
        'fundCateOnlyChange',
        this.baseIndexFlag,
        this.indexFlag,
        this.equitytype,
        this.FUNC.isEmpty(this.equitytype),
        this.math_range
      );
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () {
    if (JSON.stringify(this.dataX) != '{}') {
      if (this.dataX.dataResult && this.dataX.dataResult.length > 0) {
        this.equitytype = this.dataX.dataResult[0].value;
        if (this.$refs['operator']) {
          this.$refs['operator'].getFlag(this.dataX.dataResult[0].mathRange);
        }
      }
    }
    this.addType()
  },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
</style>
