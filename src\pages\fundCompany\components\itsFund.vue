<template>
	<div>
		<div class="flex_card">
			<div v-for="item in templateList" :key="item.value" v-show="item.isshow" :class="item.type">
				<component :is="item.is" :ref="item.value" @resolveFather="item.methods" v-loading="loading"></component>
			</div>
		</div>
	</div>
</template>

<script>
// 新发基金
import newDevelopmentFund from '@/components/components/components/newDevelopmentFund/index.vue';
// 全部基金
import allFunds from '@/components/components/components/allFunds/index.vue';

// 新发基金,全部基金
import { getNewFund, getHoldFundMsg } from '@/api/pages/SystemOther.js';

export default {
	components: { newDevelopmentFund, allFunds },
	data() {
		return {
			templateList: [],
			info: {},
			loading: true,
			requestOver: [],
			requestAll: [],
			typeList: [],
			dateList: [],
			industryInfo: null,
			newDevelopmentFundData: null,
			holdFundMsgData: null
		};
	},
	methods: {
		// 获取数据
		getData(data) {
			this.info = data;
			this.loading = true;
			this.requestOver = [];
			this.watch();
			this.formatTemplatList();
		},
		// 添加watch函数式监听(因为watch侦听器在页面切换时失效)
		watch() {
			let unwatch = this.$watch('requestOver', (val, old) => {
				this.loading = false;
				this.$compontentsWatch(val, this);
			});
		},
		// 格式化模板列表
		formatTemplatList() {
			this.requestAll = [];
			let requestList = [];
			this.templateList.map((item) => {
				if (item.methods && typeof item.methods == 'string') {
					item.methods = this?.[item.methods];
				}
				if (
					item.typelist.some((obj) => {
						return this.info.type.indexOf(obj) != -1 || obj == '*';
					})
				) {
					this.requestAll.push(item);
					if (requestList.indexOf(item.getRequestData) == -1) {
						if (item.getRequestData && item.getRequestData !== 'None') {
							requestList.push(item.getRequestData);
							this?.[item.getRequestData]();
						}
					}
				}
			});
		},
		// 接收/返回组件列表
		getTemplateList(list) {
			if (list) {
				// 是光大
				if (this.isGDBank()) {
					this.templateList = list.filter((item) => {
						return item.isshow;
					});
				} else {
					// 不是光大
					this.templateList = list.filter((item) => {
						return item.is !== 'GDBankDetailequity' && item.is !== 'GDBankDetailbond' && item.isshow;
					});
				}
				this.$forceUpdate();
			} else {
				return this.templateList;
			}
		},
		// 判断是否是光大
		isGDBank() {
			if (window.localStorage.getItem('mty_modulesName') == 'GDBank') {
				this.showGD = true;
				return true;
			} else {
				this.showGD = false;
				return false;
			}
		},
		// 获取新发基金
		async getNewDevelopmentFund() {
			console.log('newDevelopmentFundData');
			if (this.getCacheData('newDevelopmentFundData')) {
				this.newDevelopmentFundData = this.getCacheData('newDevelopmentFundData');
			}
			let data = await getNewFund({ code: this.info.code });
			if (data?.mtycode == 200) {
				this.newDevelopmentFundData = data?.data;
				this.setCacheData('newDevelopmentFundData', this.newDevelopmentFundData);
			}
			this.requestOver.push('getNewDevelopmentFund');
		},
		// 获取新发基金数据
		getNewDevelopmentFundData() {
			this.$refs['newDevelopmentFund']?.[0]?.getData(this.newDevelopmentFundData);
		},
		// 获取全部基金
		async getAllFunds() {
			if (this.getCacheData('holdFundMsgData')) {
				this.holdFundMsgData = this.getCacheData('holdFundMsgData');
			} else {
				let data = await getHoldFundMsg({ code: this.info.code });
				if (data?.mtycode == 200) {
					this.holdFundMsgData = data?.data;
					this.setCacheData('holdFundMsgData', this.holdFundMsgData);
				}
			}
			this.requestOver.push('getAllFunds');
		},
		// 获取全部基金数据
		getAllFundsData() {
			this.$refs['allFunds']?.[0]?.getData(this.holdFundMsgData);
		}
	}
};
</script>

<style scoped lang="scss">
.flex {
	width: 100%;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
</style>
