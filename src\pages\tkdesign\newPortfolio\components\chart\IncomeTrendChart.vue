<template>
	<div>
		<div style="page-break-inside: avoid; position: relative">
			<div>
				<div style="page-break-inside: avoid; position: relative">
					<div class="color_legends_wrapper">
						<div v-for="(yearItem, index) in yearsName" class="legend_item">
							<div :style="{ backgroundColor: yearItem.color }" class="legend_color_box"></div>
							{{ yearItem.name }}
						</div>
					</div>
				</div>
				<div class="charts_fill_class">
					<v-chart
						v-show="!industryrequestflag"
						class="charts_one_class"
						ref="ValuationPercentileChart"
						autoresize
						v-loading="industryrequestflag"
						:options="industryoption"
						@legendselectchanged="legendselectchanged"
					/>
					<el-empty v-show="industryrequestflag" description="暂无数据"></el-empty>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
// 行业评价
import VChart from 'vue-echarts';
import { lineChartOption } from '../../../components/chart/chartStyle.js';
export default {
	name: 'ValuationPercentileChart',
	components: {
		VChart
	},
	data() {
		return {
			industryrequestflag: true,
			info: {},
			industryoption: null,
			selected: {},
			yearsName: []
		};
	},
	methods: {
		// 获取父组件传递数据
		getData(data, info) {
			this.info = info;
			if (data.length > 0) {
				this.industryrequestflag = false;
				this.industryoption = this.getIndustryoption(data, info);
			}
		},
		getIndustryoption(chartData, info = {}) {
			let data = chartData || [];
			let currentLegend = [];
			currentLegend = data.map((item) => {
				return {
					name: item.name,
					icon: 'line'
				};
			});
			//以数据的名称作为系列的名称
			let seriesName = new Set();
			//数组的全部年份
			let yearsName = new Set();
			//查找数组的全部日期
			let dateList = new Set();
			data = data.map((element) => {
				//以数据的名称作为系列的名称
				seriesName.add(element.name);
				let year = new Date(element.date).getFullYear();
				yearsName.add(year);

				dateList.add(element.date);
				return element;
			});
			//背景颜色数据处理 标记对应年份区间在数组中的起始位置
			let markAreaData = [...yearsName].map((item) => {
				let index = [...dateList].findLastIndex((dataItem) => {
					let year = new Date(dataItem).getFullYear();
					return year === item;
				});
				return {
					index,
					year: item
				};
			});
			let colors = [
				'rgba(246, 189, 22, 0.25)',
				'rgba(55, 120, 246, 0.25)',
				'rgba(255, 145, 3, 0.25)',
				'rgba(115, 136, 169, 0.25)',
				'rgba(111, 128, 221, 0.25)',
				'rgba(253, 156, 255, 0.25)',
				'rgba(174, 201, 254, 0.25)',
				'rgba(169, 244, 208, 0.25)',
				'rgba(154, 137, 255, 0.25)',
				'rgba(219, 174, 255, 0.25)',
				'rgba(159, 212, 253, 0.25)',
				'rgba(208, 232, 255, 0.25)',
				'rgba(251, 227, 142, 0.25)',
				'rgba(253, 208, 159, 0.25)',
				'rgba(254, 174, 174, 0.25)',
				'rgba(254, 208, 238, 0.25)'
			];
			const legendColors = [
				'rgba(246, 189, 22, 1)',
				'rgba(55, 120, 246, 1)',
				'rgba(255, 145, 3,1)',
				'rgba(115, 136, 169,1)',
				'rgba(111, 128, 221, 1)',
				'rgba(253, 156, 255,1)',
				'rgba(174, 201, 254, 1)',
				'rgba(169, 244, 208, 1)',
				'rgba(154, 137, 255, 1)',
				'rgba(219, 174, 255, 1)',
				'rgba(159, 212, 253, 1)',
				'rgba(208, 232, 255,1)',
				'rgba(251, 227, 142,1)',
				'rgba(253, 208, 159, 1)',
				'rgba(254, 174, 174, 1)',
				'rgba(254, 208, 238, 1)'
			];
			//用于展示上方年份标识
			this.yearsName = [...yearsName].map((item, index) => {
				return {
					name: item,
					color: legendColors[index]
				};
			});
			//生成多个横坐标 为每个横坐标设置不同的区域颜色（每一年的区域颜色不一样）
			const xAxisConfigList = markAreaData.map((item, index) => {
				let leftIndex = markAreaData[index - 1] ? markAreaData[index - 1].index + 1 : 0;
				let rightIndex = item.index + 1;
				return {
					data: [...dateList],
					show: true,
					min: 'unset',
					max: 'unset',
					type: 'category',
					boundaryGap: false,
					axisLine: {
						show: false,
						lineStyle: {
							show: false
						}
					},
					axisTick: {
						show: false
					},
					axisLabel: {
						show: index === 0
					},
					splitArea: {
						interval: (valueIndex, valueValue) => {
							return valueIndex >= leftIndex && valueIndex <= rightIndex;
						},
						show: true,
						areaStyle: {
							color: colors[index]
						}
					}
				};
			});
			//已产品名称作为唯一标识，将相同产品名称的数据处理在一个series
			let series = [...seriesName].map((item, index) => {
				let dataArr = [];
				data.forEach((dataItem) => {
					if (item === dataItem.name) {
						dataArr.push(dataItem.rate);
					}
				});

				let result = {
					name: item,
					type: 'line',
					symbol: 'none',
					data: dataArr
				};
				return result;
			});
			console.log('series', series);
			return lineChartOption({
				toolbox: 'none',
				// color: ['#4096ff', '#4096ff', '#7388A9', '#7388A9', '#389E0D'],
				legend: {
					selected: this.selected,
					data: [...currentLegend]
				},
				// tooltip: {
				// 	// 坐标轴指示器，坐标轴触发有效
				// 	type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
				// },
				tooltip: {
					backgroundColor: '#ffffff',
					formatter: function (obj) {
						var value = `<div style="font-size:14px;">` + obj?.[0].axisValue + `</div>`;
						for (let i = 0; i < obj.length; i++) {
							value +=
								`<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
								`<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
								obj?.[i].color +
								`;"></div>` +
								`<div style="font-family: PingFang SC;">` +
								obj?.[i].seriesName +
								'</div></div>' +
								`<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
								(Number(obj?.[i].value) * 100).toFixed(2) +
								'%</div>' +
								`</div>`;
						}
						return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
					}
				},
				xAxis: xAxisConfigList,
				yAxis: [
					{
						type: 'value',
						formatter(value) {
							return (Number(value) * 100).toFixed(2) + '%';
						}
					}
					// {
					// 	type: 'value',
					// 	splitLine: false
					// }
				],
				series: series
			});
		}
	}
};
</script>

<style scoped lang="scss">
.color_legends_wrapper {
	display: flex;
	justify-content: center;
	.legend_item {
		display: flex;
		align-items: center;
		&:not(:nth-last-child(1)) {
			margin-right: 40px;
		}
		.legend_color_box {
			width: 8px;
			height: 8px;
			margin-right: 8px;
		}
	}
}
</style>
