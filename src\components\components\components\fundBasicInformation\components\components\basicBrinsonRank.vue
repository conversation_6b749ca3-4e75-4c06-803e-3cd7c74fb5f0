<template>
	<div class="basic_brinson_rank mt-12 px-8 py-8">
		<div class="flex_start">
			<div v-for="(item, index) in itemList" :key="item" :class="index == 0 ? 'rank_card px-8 py-8 ' : 'rank_card px-8 py-8 ml-8 '">
				<div>{{ item.label }}</div>
				<div>
					<el-progress :percentage="data[item.value]" style="width: 127px" :format="format" color="rgb(64, 150, 255)"></el-progress>
				</div>
			</div>
		</div>
		<div class="flex_between benchmark_description mt-8">
			<div>比较基准:沪深300</div>
			<div>同类基金比较</div>
		</div>
	</div>
</template>

<script>
import { getCapabilityInfo } from '@/api/pages/Analysis.js';
export default {
	data() {
		return {
			itemList: [
				{ label: '大类资产配置', value: 'allocation' },
				{ label: '行业选择收益', value: 'industry' },
				{ label: '选股收益', value: 'stock' },
				{ label: '其他收益', value: 'other' }
			],
			data: { allocation: 80, industry: 80, stock: 80, other: 80 },
			info: {}
		};
	},
	methods: {
		getData(info) {
			this.info = info;
			this.getCapabilityInfo();
		},
		async getCapabilityInfo() {
			let data = await getCapabilityInfo({
				codes: [this.info.code],
				type: this.info.type,
				flag: [this.info.flag],
				item: ['大类资产能力', '行业能力', '择股能力', '择时能力']
			});
			if (data?.mtycode == 200) {
				let allocation = data.data?.find((v) => v.item == '大类资产能力')?.relRank * 100;
				let industry = data.data?.find((v) => v.item == '行业能力')?.relRank * 100;
				let stock = data.data?.find((v) => v.item == '择股能力')?.relRank * 100;
				let other = data.data?.find((v) => v.item == '择时能力')?.relRank * 100;
				this.data = { allocation, industry, stock, other };
			}
		},
		format(val) {
			return '';
		},
		createPrintWord() {
			return [...this.$exportWord.exportDoubleColumnTable(this.itemList, this.data)];
		}
	}
};
</script>

<style lang="scss" scoped>
.basic_brinson_rank {
	border-radius: 4px;
	border: 1px solid #d9d9d9;
	.rank_card {
		width: 142px;
		height: 48px;
		border-radius: 4px;
		background: #ecf5ff;
		color: #1c8dd4;
		font-family: PingFang SC;
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
		::v-deep.el-progress-bar {
			padding-right: 0;
		}
	}
	.benchmark_description {
		color: rgba(0, 0, 0, 0.25);
		// font-family: PingFang SC;
		font-size: 12px;
		font-style: normal;
		font-weight: 400;
	}
}
</style>
