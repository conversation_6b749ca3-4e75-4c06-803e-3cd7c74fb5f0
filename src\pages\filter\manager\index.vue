<!--  -->
<template>
  <div style=""
       class="alphafilter">
    <el-menu :default-active="activeIndex"
             class="el-menu-demo"
             mode="horizontal"
             @select="handleSelect">
      <el-menu-item index="1">主动权益</el-menu-item>
      <el-menu-item index="2">港股</el-menu-item>
    </el-menu>
    <div class="line2"></div>
    <zhudong ref="equity"
             type="activeequity"
             v-if="zhudong"></zhudong>
    <zhudong ref="equity"
             type="hkequity"
             v-if="ganggu"></zhudong>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import zhudong from './components/alphaFundmanagerpool.vue';
export default {
  //import引入的组件需要注入到对象中才能使用
  components: {
    zhudong
  },
  data () {
    //这里存放数据
    return {
      activeIndex: '1',
      zhudong: true,
      ganggu: false
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    handleSelect (key, keyPath) {
      if (key == '1') {
        this.zhudong = true;
        this.ganggu = false;
        try {
          window.localStorage.setItem('keepactiveIndexM', JSON.stringify(key));
        } catch (err) { }
        if (this.FUNC.isEmpty(keyPath)) {
          this.$refs.equity.changeTab('equity');
        }
      } else {
        this.zhudong = false;
        this.ganggu = true;
        try {
          window.localStorage.setItem('keepactiveIndexM', JSON.stringify(key));
        } catch (err) { }
        if (this.FUNC.isEmpty(keyPath)) {
          this.$refs.equity.changeTab('equityhk');
        }
      }
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () {
    if (
      localStorage.getItem('keepactiveIndexM') != null &&
      localStorage.getItem('keepactiveIndexM') != undefined &&
      localStorage.getItem('keepactiveIndexM') != 'undefined'
    ) {
      //  //console.log(localStorage.getItem('keepactiveIndex'))
      this.activeIndex = JSON.parse(window.localStorage.getItem('keepactiveIndexM'));
    }
    // //console.log(this.activeIndex)
    if (this.activeIndex != '1') {
      this.handleSelect(this.activeIndex);
    }
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () { }
};
</script>
<style>
/* .line2 {
	width: 100%;
	height: 1px;
	margin-bottom: -9px;
	background: #e9e9e9;
} */

.alphafilter .line2 {
	margin-bottom: -9px;
	width: 100%;
	height: 1px;
	background: #e9e9e9;
}
.alphafilter .el-menu {
	background: #ffffff !important;
	background-color: #ffffff !important;
}
.alphafilter .el-menu.el-menu--horizontal {
	border-bottom: 0px !important;
}
.alphafilter .el-menu-item .is-active {
	background: #ffffff !important;
}
.alphafilter .el-menu-item:hover {
	background: #ffffff !important;
}
</style>
