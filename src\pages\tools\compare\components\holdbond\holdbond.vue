<!--  -->
<template>
  <div v-loading="loading"
       class="holdstock">
    <div style="display: flex; align-items: center; width: 100%; position: relative; justify-content: space-between">
      <div style="display: flex; align-items: center">
        <div class="TitltCompare">近一年公布重仓</div>
      </div>
    </div>
    <div style="display: flex; justify-content: flex-end">
      <el-button @click="outexcel()"
                 icon="el-icon-download"></el-button><el-button :style="nowindex == item ? 'color:white;background:#4096FF' : ''"
                 v-for="(item, index) in yearqtrlist"
                 :key="index"
                 @click="changgeyearqtr(item)">{{ item }}</el-button>
    </div>
    <div>
      <div style="height: 16px"></div>
      <sTable :data="hold_stock_msg"
              typeFlag="splice1"></sTable>
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { BondFundRecent1y } from '@/api/pages/tools/compare.js';
import sTable from '../SelfTable.vue';
import VCharts from 'vue-echarts';
export default {
  props: {
    comparetype: {
      type: String,
      default: 'manager' //fund
    },
    id: {
      type: String,
      default: '30189741,30441407'
    },
    type: {
      type: String,
      default: 'equity'
    },
    name: {
      type: String,
      default: '萧楠,胡昕炜'
    }
  },
  //import引入的组件需要注入到对象中才能使用
  components: {
    'v-chart': VCharts,
    sTable
  },
  data () {
    //这里存放数据
    return {
      hold_stock_msg: [],
      loading: false,
      options1: [],
      value1: '',
      value2: '',
      nowindex: '',
      showdetailchoose: false,
      stock_holdcolumns: [],
      stock_holdcolumnsfund: [],
      fund_hold: [],
      stock_hold: [],
      empty1: false,
      empty2: false,
      optionpbroe: {},
      optionpbroe2: {},
      yearqtrlist: [],
      datatable: [],
      stocksamelist: [],
      colorlist: [
        '#c92a2a',
        '#a61e4d',
        '#862e9c',
        '#5f3dc4',
        '#364fc7',
        '#1864ab',
        '#0b7285',
        '#087f5b',
        '#2b8a3e',
        '#5c940d',
        '#e67700',
        '#d9480f',
        '#ffe3e3',
        '#ffdeeb',
        '#f3d9fa',
        '#e5dbff',
        '#edf2ff',
        '#d0ebff',
        '#c5f6fa',
        '#d3f9d8',
        '#e9fac8',
        '#fff3bf',
        '#ffe8cc',
        '#ffa8a8',
        '#faa2c1',
        '#e599f7',
        '#eebefa',
        '#b197fc',
        '#91a7ff',
        '#74c0fc',
        '#66d9e8',
        '#63e6be',
        '#8ce99a',
        '#c0eb75',
        '#ffe066',
        '#ffc078',
        '#fa5252',
        '#f06595',
        '#4096FF',
        '#FFB6C1',
        '#DB7093',
        '#DA70D6',
        '#800080',
        '#9370DB',
        '#6A5ACD',
        '#4169E1',
        '#B0C4DE',
        '#4682B4',
        '#5F9EA0',
        '#8FBC8F',
        '#EEE8AA',
        '#FFD700',
        '#FFA500',
        '#FF6347',
        '#CD5C5C',
        '#B22222',
        '#D3D3D3',
        '#A9A9A9',
        '#FA8072',
        '#929694',
        '#40BFDD',
        '#C2B12F',
        '#ffa94d',
        '#fcc419',
        '#94d82d',
        '#94C5DE',
        '#B7A7D7',
        '#FDDBC7',
        '#F3A483',
        '#D45C4E',
        '#409eff',
        '#f39c12',
        '#ff1744',
        '#d500f9',
        '#2979ff',
        '#00e5ff',
        '#ff5722',
        '#ffea00',
        '#ff3d00',
        '#ff8a80',
        '#ff80ab',
        '#b388ff',
        '#8c9eff',
        '#a7ffeb',
        '#ffff00',
        '#ffab40',
        '#ffebee',
        '#e8eaf6',
        '#e1f5fe',
        '#fffde7',
        '#efebe9'
      ]
    };
  },
  filters: {
    fix3 (value) {
      if (value == '--' || value == null || value == '') {
        return value;
      } else {
        return (value * 100).toFixed(2) + '%';
      }
    },
    fix3z (value) {
      if (value == '--' || value == null || value == '') {
        return value;
      } else {
        return value.toFixed(2) + '%';
      }
    },
    fix2 (value) {
      return Number(value).toFixed(2) + '亿';
    }
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    // 股票详情分析

    //导出excel
    outexcel () {
      const { export_json_to_excel } = require('@/vendor/Export2Excel');
      var list = [];
      list.push(this.dataexplain);
      let tHeader = [];
      let filterVal = [];

      tHeader = ['名称', '债券名称', '债券权重', '所持季度'];
      filterVal = ['fund_name', 'bond_name', 'weight', 'yearqtr'];
      // //console.log(this.datatable)
      let temparr = [];
      for (let i = 0; i < this.datatable.length; i++) {
        temparr = temparr.concat(this.datatable[i]);
      }
      // //console.log(temparr)
      for (let i = 0; i < temparr.length; i++) {
        list[i] = [];
        list[i][0] = temparr[i].fund_name;
        list[i][1] = temparr[i].bond_name;
        list[i][2] = temparr[i].weight;
        list[i][3] = temparr[i].yearqtr;
      }

      export_json_to_excel(tHeader, list, '比较近一年公布重仓');
    },
    changgeyearqtr (val) {
      //console.log(this.datatable)
      this.nowindex = val;
      this.stock_hold = [];
      let templength = [];
      let tempdataarr = [];
      for (let i = 0; i < this.datatable.length; i++) {
        let item2 = [];
        for (let j = 0; j < this.datatable[i].length; j++) {
          if (this.datatable[i][j].yearqtr == val) {
            item2.push(this.datatable[i][j]);
          }
        }
        tempdataarr.push(item2);
      }

      for (let i = 0; i < tempdataarr.length; i++) {
        templength.push(tempdataarr[i].length);
      }
      let index = templength.indexOf(Math.max(...templength));
      this.stock_hold = [];
      for (let i = 0; i < templength[index]; i++) {
        let item = {};
        for (let j = 0; j < tempdataarr.length; j++) {
          if (tempdataarr[j][i]) {
            // //console.log('i'+i)
            // //console.log('j'+j)
            if (tempdataarr[j][i].yearqtr == val) {
              item['name' + j] = tempdataarr[j][i].bond_name;
              item['weight' + j] = tempdataarr[j][i].weight;
            }
          }
        }
        // //console.log(item)
        if (JSON.stringify(item) != '{}') {
          this.stock_hold.push(item);
        }
      }
      this.hold_stock_msg = [['季度']];
      for (let i = 0; i < this.$route.query.id.split(',').length; i++) {
        this.hold_stock_msg[0].push('债券/权重');
      }

      for (let i = 0; i < this.stock_hold.length; i++) {
        this.hold_stock_msg.push([]);
        this.hold_stock_msg[i + 1].push(this.nowindex);

        for (let j = 0; j < this.$route.query.id.split(',').length; j++) {
          this.hold_stock_msg[i + 1].push(
            (this.FUNC.isEmpty(this.stock_hold[i]['name' + j]) ? this.stock_hold[i]['name' + j] : '--') +
            '/' +
            (this.FUNC.isEmpty(this.stock_hold[i]['weight' + j])
              ? this.comparetype == 'manager'
                ? (Number(this.stock_hold[i]['weight' + j]) * 100).toFixed(2)
                : Number(this.stock_hold[i]['weight' + j]).toFixed(2)
              : '--') +
            '%'
          );
        }
      }
      this.stocksamelist = [];
      for (let i = 1; i < this.datatable.length; i++) {
        for (let j = 0; j < this.datatable[i].length; j++) {
          for (let k = 0; k < this.datatable[i - 1].length; k++) {
            if (
              this.datatable[i - 1][k].bond_name == this.datatable[i][j].bond_name &&
              this.stocksamelist.indexOf(this.datatable[i - 1][k].bond_name) < 0
            ) {
              this.stocksamelist.push(this.datatable[i - 1][k].bond_name);
            }
          }
        }
      }
      // //console.log(this.stocksamelist)
    },
    getdata () {
      Object.assign(this.$data, this.$options.data());
      this.loading = true;
      if (this.comparetype == 'manager') {
        // this.getmanager()
      } else {
        this.gefunddata();
      }
    },
    async getmanager () {
      let data = await ManagerHoldRecent1y({ manager_code: this.id, type: this.type, manager_name: this.name });
      if (data) {
        // //console.log('chigu1y')
        // //console.log(data)
        this.yearqtrlist = [];
        if (data.data.length > 0) {
          for (let i = 0; i < data.data[0].length; i++) {
            if (this.yearqtrlist.indexOf(data.data[0][i].yearqtr) < 0) {
              {
                this.yearqtrlist.push(data.data[0][i].yearqtr);
              }
            }
          }
        }
        this.datatable = data.data;
        this.stock_holdcolumns = [];
        for (let i = 0; i < this.datatable.length; i++) {
          if (this.datatable[i].length > 0) {
            this.stock_holdcolumns.push({
              title: this.datatable[i][0].manager_name,
              children: [
                {
                  dataIndex: 'name' + i,
                  key: 'Name' + i,
                  title: '股票名称',
                  scopedSlots: {
                    customRender: 'Name' + i
                  }
                },
                {
                  dataIndex: 'weight' + i,
                  key: 'Weight' + i,
                  title: '权重',
                  sorter: (a, b) => a['weight' + i] - b['weight' + i],
                  scopedSlots: {
                    customRender: 'Weight' + i
                  }
                },
                {
                  dataIndex: 'rank' + i,
                  key: 'Rank' + i,
                  title: '抱团度',
                  scopedSlots: {
                    customRender: 'Rank' + i
                  },
                  sorter: (a, b) => a['rank' + i] - b['rank' + i]
                }
              ]
            });
          }
        }
        let max = this.yearqtrlist[0];
        for (let i = 0; i < this.yearqtrlist.length - 1; i++) {
          max = max < this.yearqtrlist[i + 1] ? yearqtrlist[i + 1] : max;
        }
        this.changgeyearqtr(max);
      }
    },

    async gefunddata () {
      let data = await BondFundRecent1y({ fund_code: this.id, type: this.type, fund_name: this.name });
      this.loading = false;

      if (data) {
        //console.log('chigu1y')
        //console.log(data)
        this.yearqtrlist = [];
        if (data.data.length > 0) {
          for (let i = 0; i < data.data[0].length; i++) {
            if (this.yearqtrlist.indexOf(data.data[0][i].yearqtr) < 0) {
              {
                this.yearqtrlist.push(data.data[0][i].yearqtr);
              }
            }
          }
        }
        this.datatable = data.data;
        this.stock_holdcolumnsfund = [];
        for (let i = 0; i < this.datatable.length; i++) {
          if (this.datatable[i].length > 0) {
            this.stock_holdcolumnsfund.push({
              title: this.datatable[i][0].fund_name,
              children: [
                {
                  dataIndex: 'name' + i,
                  key: 'Name' + i,
                  title: '债券名称',
                  scopedSlots: {
                    customRender: 'Name' + i
                  }
                },
                {
                  dataIndex: 'weight' + i,
                  key: 'Weight' + i,
                  title: '权重',
                  sorter: (a, b) => a['weight' + i] - b['weight' + i],
                  scopedSlots: {
                    customRender: 'Weight' + i
                  }
                }
              ]
            });
          }
        }
        let max = this.yearqtrlist[0];
        for (let i = 0; i < this.yearqtrlist.length - 1; i++) {
          max = max < this.yearqtrlist[i + 1] ? this.yearqtrlist[i + 1] : max;
        }
        this.changgeyearqtr(max);
      }
    },
    // 获取季度
    getquarter (val) {
      let temp = null;
      var myDate = new Date();
      let year = myDate.getFullYear();
      let month = myDate.getMonth() + 1;
      let quarter = Math.floor(month % 3 == 0 ? month / 3 : month / 3 + 1);
      if (quarter - val - 1 <= 0) {
        return year - 1 + ' Q' + (4 + quarter - val - 1);
      } else {
        return year + ' Q' + (quarter - val - 1);
      }
    }, //
    createPrintWord () {
      // hold_stock_msg
      let name = this.name.split(',');
      let data = [['', ...name], ...this.hold_stock_msg];
      return [
        ...this.$exportWord.exportFirstTitle('债券持仓分析'),
        ...this.$exportWord.exportTitle('近一年公布重仓'),
        ...this.$exportWord.exportCompareTable(data, [], true)
      ];
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () { },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
</style>
