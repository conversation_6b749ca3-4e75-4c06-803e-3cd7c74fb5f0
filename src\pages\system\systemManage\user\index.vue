<template>
	<!--用户权限管理页面-->
	<div class="user-management">
		<div class="container">
			<!--新增用户按钮页面-->
			<div style="text-align: right"><el-button class="add-user" type="primary" @click="openUserDrawer(1)">新增用户</el-button></div>
			<!--三个条件的搜索-->

			<el-form ref="form" class="flex" :model="form" label-width="100%" :inline="true">
				<el-form-item>
					<el-input v-model="form.username" placeholder="输入用户名账号搜索" />
				</el-form-item>
				<el-form-item>
					<el-input v-model="form.name" placeholder="输入姓名搜索" />
				</el-form-item>

				<el-form-item>
					<el-input v-model="form.mobile" placeholder="输入联系电话搜索" />
				</el-form-item>

				<el-form-item>
					<el-input v-model="form.email" placeholder="输入邮箱搜索" />
				</el-form-item>
				<!--点击查询后会根据条件筛选表格-->
				<el-form-item>
					<el-button type="primary" @click="onSubmit">查询</el-button>
				</el-form-item>
			</el-form>

			<!--界面用户表格-->
			<!--v-loading绑定一个布尔值 ture -->
			<el-table v-loading="tableDataLoading" :data="tableDataNow" class="user-table" style="width: 100%" height="calc(100vh - 535px)">
				<!-- <el-table-column prop="username" label="用户名账号" align="gotoleft" show-overflow-tooltip></el-table-column> -->
				<el-table-column prop="username" label="用户名账号" align="gotoleft" show-overflow-tooltip>
					<template slot="header"> </template>
				</el-table-column>
				<el-table-column prop="last_name" label="姓名" :width="120" align="gotoleft">
					<template slot-scope="{ row }">
						{{ row.last_name + row.first_name }}
					</template>
				</el-table-column>
				<!-- <el-table-column prop="first_name" label="名" :width="(120)" align="gotoleft"></el-table-column> -->
				<el-table-column prop="title" label="职位" align="gotoleft" show-overflow-tooltip></el-table-column>
				<el-table-column prop="is_active" label="激活状态" :width="100" align="gotoleft">
					<template slot-scope="{ row }">
						<!-- <span :style="{ color: row.is_active ? '#20995B' : '#E85D2D' }">{{ row.is_active ? '已激活' : '未激活' }}</span> -->
						<el-switch
							v-model="row.is_active"
							:disabled="!(userType == 'superuser' || !row.is_superuser)"
							@change="changeAccountStatus(row)"
						>
						</el-switch>
					</template>
				</el-table-column>
				<el-table-column label="账号类型" align="gotoleft" :width="150">
					<template slot-scope="{ row }">
						<span>{{ row.is_superuser ? '超级用户' : row.is_staff ? '员工用户' : '普通用户' }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="mobile" label="联系电话" align="gotoleft"></el-table-column>
				<el-table-column prop="email" label="邮箱" align="gotoleft" :min-width="150" show-overflow-tooltip></el-table-column>
				<el-table-column prop="wechat" label="微信" align="gotoleft" show-overflow-tooltip></el-table-column>
				<!-- <el-table-column prop="institute" label="部门外键"></el-table-column> -->
				<el-table-column label="操作" :width="300" align="gotoleft">
					<template slot-scope="{ row }">
						<!-- <el-button type="text" v-if="userType == 'superuser' || !row.is_superuser"> -->
						<!-- <span style="color: #606266">{{ row.is_active ? '停用' : '激活' }}</span> -->

						<!-- </el-button> -->
						<div class="user-management-button">
							<el-button @click="openUserDrawer(3, row)" type="text">查看</el-button>
							<el-button @click="openUserDrawer(2, row)" type="text" v-if="userType == 'superuser' || !row.is_superuser">编辑</el-button>
							<el-button @click="userDelete(row.id)" v-if="userType == 'superuser' || !row.is_superuser" type="text" style="color: #f56c6c"
								>删除</el-button
							>
						</div>
					</template>
				</el-table-column>
			</el-table>
			<div v-show="paginationShow">
				<el-pagination
					background
					style="display: flex; justify-content: right; padding-top: 16px; padding-bottom: 24px"
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
					:current-page.sync="currentPage"
					:page-sizes="[10, 20, 40, 60, 80, 100]"
					:page-size="pageSIze"
					layout="total, sizes, prev, pager, next, jumper"
					:total="tableData.length"
				>
				</el-pagination>
			</div>

			<div class="user-drawer">
				<el-drawer :visible.sync="userDrawer" :with-header="false" :wrapperClosable="false" size="40%">
					<div class="container">
						<div class="drawer-header">
							<div class="header-title">
								<span>{{ !userForm.id ? '新增用户' : userDrawerIscheck ? '查看用户' : '编辑用户' }}</span>
							</div>
							<div class="close-btn" @click="userCancel">
								<i class="el-icon-close"></i>
							</div>
						</div>
						<!-- 新增/编辑用户 -->
						<el-form ref="userForm" :model="userForm" :rules="userRules" class="edit-user">
							<el-row :gutter="20">
								<el-col :span="12">
									<el-form-item prop="username" label="用户名账号">
										<el-input style="" v-model="userForm.username" :disabled="userDrawerIscheck" :placeholder="placeholderText"></el-input>
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item v-if="!userForm.id" prop="password" label="密码">
										<el-input v-model="userForm.password" :disabled="userDrawerIscheck" :placeholder="placeholderText"></el-input>
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item prop="last_name" label="姓名">
										<el-input v-model="userForm.last_name" :disabled="userDrawerIscheck" :placeholder="placeholderText"></el-input>
									</el-form-item>
								</el-col>
								<!-- <el-col :span="12">
									<el-form-item prop="first_name" label="名">
										<el-input v-model="userForm.first_name" :disabled="userDrawerIscheck" :placeholder="placeholderText"></el-input>
									</el-form-item>
								</el-col> -->
								<el-col :span="12">
									<el-form-item prop="title" label="职位">
										<el-input v-model="userForm.title" :disabled="userDrawerIscheck" :placeholder="placeholderText"></el-input>
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item prop="mobile" label="联系电话">
										<el-input v-model="userForm.mobile" :disabled="userDrawerIscheck" :placeholder="placeholderText"></el-input>
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item prop="email" label="邮箱">
										<el-input v-model="userForm.email" :disabled="userDrawerIscheck" :placeholder="placeholderText"></el-input>
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item prop="wechat" label="微信">
										<el-input v-model="userForm.wechat" :disabled="userDrawerIscheck" :placeholder="placeholderText"></el-input>
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item prop="institute" label="所属部门">
										<el-select v-model="userForm.institute" filterable :disabled="userDrawerIscheck" placeholder="请选择">
											<el-option v-for="item of institueList" :key="item.id" :value="item.id" :label="item.name">
												<span style="float: left">{{ item.name }}</span>
												<span v-if="item.serviceplans.length == 0" style="float: right">该部门未添加销售计划表</span>
											</el-option>
										</el-select>
									</el-form-item>
								</el-col>
								<el-col :span="24" v-show="userForm.institute == 1 && userForm.roles.length == 0">
									<el-form-item prop="accountType" label="账号类型" class="label-oneline-item">
										<el-checkbox v-model="userForm.is_superuser" :disabled="userDrawerIscheck || userType !== 'superuser'"
											>超级用户</el-checkbox
										>
										<span v-if="!userDrawerIscheck && userType !== 'superuser'" style="color: #ccc; font-size: 14px"
											>(非超级用户无权创建超级用户)</span
										>
									</el-form-item>
								</el-col>
								<el-col :span="24" v-show="userForm.institute == 1 && !userForm.is_superuser">
									<el-form-item prop="roles" label="角色" class="label-oneline-item">
										<el-checkbox-group v-model="userForm.roles" :disabled="userDrawerIscheck">
											<el-checkbox v-for="role of rolesList" :key="role.id" :label="role.id">{{ role.description }}</el-checkbox>
										</el-checkbox-group>
									</el-form-item>
								</el-col>
								<el-col :span="24">
									<el-form-item prop="is_active" label="激活状态" class="label-oneline-item">
										<el-switch v-model="userForm.is_active" :disabled="userDrawerIscheck"></el-switch>
									</el-form-item>
								</el-col>
							</el-row>

							<div v-if="!userDrawerIscheck">
								<!-- <p class="add-institute-tip">
									没有找到想要的部门?点击<el-button type="text" @click="companyDrawer = true">新增部门</el-button>创建新部门。
								</p> -->
								<el-form-item class="submit-btn">
									<el-button type="primary" @click="userSubmitForm()">保存</el-button>
									<el-button @click="userCancel()">取消</el-button>
								</el-form-item>
							</div>
							<div v-else>
								<el-form-item class="submit-btn">
									<el-button @click="userCancel()">关闭</el-button>
								</el-form-item>
							</div>
						</el-form>
					</div>
					<!-- 新增部门 -->
					<el-drawer :append-to-body="true" :with-header="false" :visible.sync="companyDrawer" :wrapperClosable="false" size="30%">
						<div class="container">
							<institute-form :visible="companyDrawer" @companyclose="closeCompanyDrawer" :id="editCompanyId"></institute-form>
						</div>
					</el-drawer>
				</el-drawer>
			</div>
		</div>
	</div>
</template>

<script>
import instituteForm from './components/instituteForm.vue';
import {
	refreshUserGroups,
	getUsers,
	getUsersRoles,
	patchUsers,
	postUsers,
	deleteUsers,
	getInstituesList
} from '@/api/pages/SystemOther.js';
export default {
	data() {
		return {
			title: '用户管理',
			tableData: [],
			tableDataNow: [],
			// 分页在只有一页的时候隐藏
			paginationShow: true,
			// 分页
			currentPage: 1,
			pageSIze: 10,
			// 根据条件自动筛选 双向数据绑定
			form: {
				username: '',
				name: '',
				mobile: '',
				email: ''
			},
			// 新增用户-抽屉
			userDrawer: false,
			userDrawerIscheck: false,
			placeholderText: '',
			userForm: {
				id: '',
				username: '',
				password: '',
				last_name: '',
				first_name: '',
				title: '',
				is_active: true,
				// is_staff: false,
				is_superuser: false,
				roles: [],
				mobile: '',
				email: '',
				wechat: '',
				institute: ''
			},
			editUserOldMobile: '',
			// 新增部门-抽屉
			companyDrawer: false,
			// 选择下拉列表
			positionList: [],
			institueList: [],
			rolesList: [],
			// imageUrl: '',
			userRules: {
				username: { required: true, message: '用户名账号不能为空', trigger: 'blur' },
				password: [
					{ required: true, message: '密码不能为空', trigger: 'blur' },
					{
						type: 'string',
						pattern: /^((\d)+[A-Za-z]+[A-Za-z0-9]*)|([A-Za-z]+(\d)+[A-Za-z0-9]*)$/,
						message: '密码需包含字母和数字,且长度在6~30位之间',
						min: 6,
						max: 30,
						trigger: 'blur'
					}
				],
				// 20210910 需求: 职位, 姓, 名 非必填 // 20211011 需求: 因后端逻辑问题 职位,姓,名 暂时设为必填
				last_name: { required: true, message: '姓名不能为空', trigger: 'blur' },
				// first_name: { required: true, message: '名不能为空', trigger: 'blur' },
				title: { required: true, message: '职位不能为空', trigger: 'blur' },
				mobile: [
					{ required: true, message: '联系电话不能为空', trigger: 'blur' },
					{ type: 'string', pattern: /^1[3-9]\d{9}$/, message: '请输入长度为11位的手机号码', trigger: 'blur' }
				],
				email: [{ required: true, message: '邮箱不能为空', trigger: 'blur' }],
				// wechat: [{ required: true, message: '联系电话不能为空', trigger: 'blur' }],
				institute: { required: true, message: '部门外键不能为空', trigger: 'blur' }
				// roles: { required: this.userForm.institute == 1 && !this.userForm.is_superuser, message: '部门外键不能为空', trigger: 'blur' }
			},
			haveDebounce: false,
			userType: this.$store.state.userType,
			activeList: [
				{ label: '全部状态', value: '0' },
				{ label: '激活', value: '1' },
				{ label: '停用', value: '2' }
			],
			userList: [
				{ label: '所有用户', value: '0' },
				{ label: '超级用户', value: '1' },
				{ label: '员工用户', value: '2' },
				{ label: '普通用户', value: '3' }
			],
			allData: [],
			titleList: [],
			tableDataLoading: true
		};
	},
	components: {
		instituteForm
	},
	created() {
		this.getUserList();
		this.getRoleList();
	},
	mounted() {
		// window.syncPost({
		// 	title: 'Ractor 下多线程 Ruby 程序指南',
		// 	desc: '什么是 Ractor?Ractor 是 Ruby 3 新引入的特性。Ractor 顾名思义是 Ruby 和',
		// 	content: 'hello world',
		// 	thumb:
		// 		'http://mmbiz.qpic.cn/mmbiz_jpg/CJcVm4ThlNOeib5w5A6MYk4Eg9ErnzZ73dEicribs3gPPUB4cCxiaeRm2ZfNOibHfl4TIo8h6VlFZeBRmLoMKgibvPdw/0?wx_fmt=jpeg'
		// });
	},
	//监听属性 类似于data概念
	computed: {},
	//监控data中的数据变化
	watch: {},
	methods: {
		// 输入搜索-加防抖
		// inputSearchUser(val) {
		// 	if (this.haveDebounce) return;
		// 	this.haveDebounce = true;
		// 	setTimeout(() => {
		// 		this.getUserList(val);
		// 		this.haveDebounce = false;
		// 	}, 500);
		// },

		// 分页
		handleSizeChange(val) {
			this.pageSIze = val;
			this.changePageAndSize();
		},
		handleCurrentChange(val) {
			this.currentPage = val;
			this.changePageAndSize();
		},
		changePageAndSize() {
			this.tableDataNow = this.tableData.slice((this.currentPage - 1) * this.pageSIze, this.currentPage * this.pageSIze);
		},
		// 获取用户列表
		async getUserList() {
			this.tableDataLoading = true;
			let data = await getUsers();
			this.tableDataLoading = false;
			this.allData = data;
			this.allData.map((item) => {
				item.activeType = item.is_active ? '激活' : '停用';
				item.userType = item.is_superuser ? '超级用户' : item.is_staff ? '员工用户' : '普通用户';
			});
			this.tableData = this.allData;
			this.changePageAndSize();
			this.getCondition(this.tableData);
		},
		// 提取筛选条件数组
		getCondition(data) {
			// 所有状态
			this.activeList = Array.from(
				new Set(
					data.map((item) => {
						return item.is_active ? '激活' : '停用';
					})
				)
			).map((item, index) => {
				return { label: item, value: index + 1 };
			});
			this.activeList.unshift({ label: '所有状态', value: 0 });
			// 所有用户类型
			this.userList = Array.from(
				new Set(
					data.map((item) => {
						return item.is_superuser ? '超级用户' : item.is_staff ? '员工用户' : '普通用户';
					})
				)
			).map((item, index) => {
				return { label: item, value: index + 1 };
			});
			this.userList.unshift({ label: '所有用户', value: 0 });
			// 所有职位
			this.titleList = Array.from(
				new Set(
					data.map((item) => {
						return item.title;
					})
				)
			).map((item, index) => {
				return { label: item, value: index + 1 };
			});
			this.titleList.unshift({ label: '所有用户', value: 0 });
		},
		async getRoleList() {
			let data = await getUsersRoles();
			this.rolesList = data.data;
		},
		// 用户
		userSubmitForm() {
			this.$refs.userForm.validate(async (valid) => {
				if (valid) {
					// tip:
					// 1. 由于接口设计问题,如果修改用户时若没有修改mobile,将原mobile给后端,后端接口报错,为规避该问题,故使用editUserOldMobile处理.
					// 2. 由于接口继承问题,在新增用户的时候需要传一个空salescontracts字段,否则后端报错.(由于接口设计问题,新增用户接口需要传入JSON对象)
					let data = Object.assign({}, this.userForm);
					// data.is_staff = data.institute == 1;
					// if (!data.is_superuser) {
					// 	data.roles = data.roles.map((item) => (item = { id: item }));
					// }
					if (data.institute == 1) {
						// data.is_staff = true;
						if (!data.is_superuser && data.roles.length == 0) {
							this.$message.error('请选择账号类型或角色');
							return;
						}
						data.roles = data.roles.map((item) => (item = { id: item }));
					} else {
						// data.is_staff = false;
						data.is_superuser = false;
						data.roles = [];
					}

					let result = {};
					if (!!this.userForm.id) {
						data.first_name = data.last_name.slice(1, data.last_name.length);
						data.last_name = data.last_name.slice(0, 1);
						if (data.mobile == this.editUserOldMobile) {
							delete data.mobile;
						}
						result = await patchUsers(this.userForm.id, data);
					} else {
						data.salescontracts = [];
						data = JSON.parse(JSON.stringify(data));
						data.first_name = data.last_name.slice(1, data.last_name.length);
						data.last_name = data.last_name.slice(0, 1);
						data.wechat = data.wechat ? data.wechat : data.mobile;
						result = await postUsers(data);
					}
					if (result.mytstatus == 200 || result.id) {
						this.$message.success('成功');
						this.userDrawer = false;
						this.getUserList();
						this.resetUserForm();
						this.callRefresh();
					} else {
						this.$message.error('保存失败: ' + result.mobile);
					}
				}
			});
		},
		userCancel() {
			this.resetUserForm();
			this.userDrawer = false;
		},
		openUserDrawer(editType, data) {
			// editType 1-新增 2-编辑 3-查看
			// //console.log('openUserDrawer', editType, data);
			this.getInstitueList();
			if (editType === 2 || editType === 3) {
				// 编辑时不显示password字段,后端get接口中也没有给该字段
				this.userForm = {
					id: data.id,
					username: data.username,
					last_name: data.last_name + data.first_name,
					first_name: '',
					title: data.title,
					is_active: data.is_active,
					is_staff: data.is_staff,
					roles: data.roles.map((item) => item.id),
					is_superuser: data.is_superuser,
					mobile: data.mobile,
					email: data.email,
					wechat: data.wechat,
					institute: data.institute
				};
				this.editUserOldMobile = data.mobile;
			}
			this.userDrawerIscheck = editType === 3;
			this.placeholderText = editType === 3 ? '' : '请输入';
			this.userDrawer = true;
		},
		userDelete(id) {
			this.$confirm('确认删除该用户?', '提示', {
				distinguishCancelAndClose: true,
				confirmButtonText: '确认',
				cancelButtonText: '取消'
			})
				.then(async () => {
					let data = await deleteUsers(id);
					if (data.mtycode == 200) {
						this.$message.success('删除成功');
						this.getUserList();
						this.callRefresh();
					} else {
						this.$message.error('删除失败', data.mtymessage);
					}
				})
				.catch(() => {
					//console.log('cancel userDelete');
				});
		},
		changeAccountStatus(item) {
			item.is_active = !item.is_active;
			this.$confirm(`确认${item.is_active ? '停用' : '启用'}该用户?`, '提示', {
				distinguishCancelAndClose: true,
				confirmButtonText: '确认',
				cancelButtonText: '取消'
			})
				.then(async () => {
					let data = { is_active: !item.is_active };
					let result = await patchUsers(item.id, data);
					if (result.mytstatus == 200) {
						this.$message.success(`${item.is_active ? '停用' : '激活'}成功`);
						this.getUserList();
						this.callRefresh();
					} else {
						this.tableDataLoading = false;
						this.$message.error(`${data.mtymessage}`);
					}
				})
				.catch(() => {
					//console.log('cancel changeAccountStatus');
				});
		},
		resetUserForm() {
			this.userForm = {
				username: '',
				password: '',
				last_name: '',
				first_name: '',
				title: '',
				is_active: true,
				// is_staff: false,
				is_superuser: false,
				roles: [],
				mobile: '',
				email: '',
				wechat: '',
				institute: ''
			};
			this.editUserOldMobile = '';
			this.userDrawerIscheck = false;
			this.$refs.userForm.resetFields();
		},
		// 部门
		closeCompanyDrawer(haveEdit) {
			this.companyDrawer = false;
			if (haveEdit) {
				this.getInstitueList();
			}
		},
		async getInstitueList() {
			let data = await getInstituesList();
			this.institueList = data;
		},
		async callRefresh() {
			await refreshUserGroups();
			// let data =
			// if (data) {
			// 	this.$message.error('error: ' + data.mtymessage);
			// }
		},
		goToTable() {},
		// 根据激活状态过滤用户
		onSubmit() {
			this.tableData = this.allData.filter((item) => {
				let result = [];
				if (this.form.name && item.name) {
					result.push(item.name.includes(this.form.name));
				} else {
					result.push(true);
				}
				if (this.form.username && item.username) {
					result.push(item.username.includes(this.form.username));
				} else {
					result.push(true);
				}
				if (this.form.mobile && item.mobile) {
					result.push(item.mobile.includes(this.form.mobile));
				} else {
					result.push(true);
				}
				if (this.form.email && item.email) {
					result.push(item.email.includes(this.form.email));
				} else {
					result.push(true);
				}
				return result.every((v) => v == true);
			});
			this.paginationShow = true;
			this.changePageAndSize();
		},
		// 监听姓名搜索
		onSearchUser(val) {
			this.tableDataNow = this.allData.filter((obj) => {
				if (obj.last_name && obj.first_name) {
					console.log(obj.last_name);
					console.log(obj.first_name);
					return obj.last_name.indexOf(val) !== -1 || obj.first_name.indexOf(val) !== -1;
				}
			});
			if (this.tableDataNow.length == 1) this.paginationShow = false;
			else this.paginationShow = true;
		},
		// 监听联系电话搜索
		onSearchMobile(val) {
			this.tableDataNow = this.allData.filter((obj) => {
				if (obj.mobile && obj.mobile) {
					return obj.mobile.indexOf(val) !== -1 || obj.mobile.indexOf(val) !== -1;
				}
			});
			if (this.tableDataNow.length == 1) this.paginationShow = false;
			else this.paginationShow = true;
		},
		// 监听微信搜索
		onSearchWechat(val) {
			this.tableDataNow = this.allData.filter((obj) => {
				if (obj.wechat && obj.wechat) {
					return obj.wechat.indexOf(val) !== -1 || obj.wechat.indexOf(val) !== -1;
				}
			});
			if (this.tableDataNow.length == 1) this.paginationShow = false;
			else this.paginationShow = true;
		}
	}
};
</script>

<style lang="scss" scoped>
.user-management {
	::v-deep.el-form-item__content {
		margin-left: auto !important;
	}
	.user-table {
		margin: 50px 0;
	}
	// 用户新增抽屉
	.user-drawer {
		.el-select {
			width: 100%;
		}
		.el-form-item {
			padding: 10px 0;
		}
		.add-institute-tip {
			color: #999;
		}
	}
}

// 部门新增抽屉
// .edit-institute {
// 	.business-card-upload,
// 	.el-form-item {
// 		padding: 10px 0;
// 	}
// }

// 图片上传框样式
.avatar-uploader .el-upload {
	border: 1px dashed #d9d9d9;
	border-radius: 6px;
	cursor: pointer;
	position: relative;
	overflow: hidden;
}
.avatar-uploader .el-upload:hover {
	border-color: #409eff;
}
.avatar-uploader-icon {
	font-size: 28px;
	color: #8c939d;
	width: 178px;
	height: 178px;
	line-height: 178px;
	text-align: center;
}
.avatar {
	width: 178px;
	height: 178px;
	display: block;
}

.user-drawer {
	.submit-btn {
		display: flex;
		justify-content: flex-end;
	}
}

// 抽屉自定义表单头样式
.drawer-header {
	margin-bottom: 20px;
	display: flex;
	justify-content: space-between;
	font-size: 22px;
	.header-title {
		border-bottom: 2px solid #f68136;
	}
	.close-btn {
		cursor: pointer;
	}
}
.label-oneline-item ::v-deep .el-form-item__label {
	width: 100%;
}
.flex {
	display: flex;
	align-items: center;
}
.user-management-button .el-button--small {
	padding: 12px 20px;
}
</style>
<style>
.edit-user .el-form-item__label {
	width: 95px !important;
}
.edit-user .el-form-item__content {
	margin-left: 95px !important;
}
/*.el-input__inner {*/
/*  width: 150px ;*/
/*}*/
</style>
