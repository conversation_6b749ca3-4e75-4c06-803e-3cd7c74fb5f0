// 项目常量
const COMMON = {
  // option基准默认选项列表
  optionBasic: [
    {
      label: "参考基准",
      options: [
        {
          code: "000300.SH",
          company_code: "--",
          flag: "index",
          fund_co: "--",
          name: "沪深300",
          num: 4,
        },
        {
          code: "000905.SH",
          company_code: "--",
          flag: "index",
          fund_co: "--",
          name: "中证500",
          num: 4,
        },
        {
          code: "000001.SH",
          company_code: "--",
          flag: "index",
          fund_co: "--",
          name: "上证综指",
          num: 4,
        },
        {
          code: "399106.SZ",
          company_code: "--",
          flag: "index",
          fund_co: "--",
          name: "深证综指",
          num: 4,
        },
        {
          code: "399005.SZ",
          company_code: "--",
          flag: "index",
          fund_co: "--",
          name: "中小板指",
          num: 4,
        },
        {
          code: "399006.SZ",
          company_code: "--",
          flag: "index",
          fund_co: "--",
          name: "创业板指",
          num: 4,
        },
      ],
    },
  ],
  // 常用季度数据: quarter-季度;month-包含月份;startDate-季度开始日期;endDate-季度结束日期;
  quarterData: [
    {
      quarter: "Q1",
      month: ["01", "02", "03"],
      startDate: "01-01",
      endDate: "03-31",
    },
    {
      quarter: "Q2",
      month: ["04", "05", "06"],
      startDate: "04-01",
      endDate: "06-30",
    },
    {
      quarter: "Q3",
      month: ["07", "08", "09"],
      startDate: "07-01",
      endDate: "09-30",
    },
    {
      quarter: "Q4",
      month: ["10", "11", "12"],
      startDate: "10-01",
      endDate: "12-31",
    },
  ],
  // 行业分类
  industries: [
    "采掘",
    "化工",
    "钢铁",
    "有色金属",
    "建筑材料",
    "建筑装饰",
    "电气设备",
    "机械设备",
    "国防军工",
    "汽车",
    "家用电器",
    "纺织服装",
    "轻工制造",
    "商业贸易",
    "农林牧渔",
    "食品饮料",
    "休闲服务",
    "医药生物",
    "公用事业",
    "交通运输",
    "房地产",
    "电子",
    "计算机",
    "传媒",
    "通信",
    "银行",
    "非银金融",
    "综合",
  ],
  // 基金产品中英文对照表
  fundType_zh_en: [
    { en: "equity", zh: "主动权益" },
    { en: "equityhk", zh: "港股基金" },
    { en: "equityindex", zh: "被动权益指数" },
    { en: "equitywithhk", zh: "含港股主动权益" },
    { en: "equityenhance", zh: "指数增强" },
    { en: "equityhk-index", zh: "港股指数" },
    { en: "bond", zh: "固收+" },
    { en: "cbond", zh: "可转债" },
    { en: "purebond", zh: "纯债" },
    { en: "bill", zh: "中短债" },
    { en: "money", zh: "货币" },
    { en: "obond", zh: "其他债券" },
    { en: "bondindex", zh: "二级债基指数" },
    { en: "fof", zh: "FOF" },
    { en: "fof:target-date", zh: "目标日期型fof" },
    { en: "fof:target-risk", zh: "目标风险型fof" },
    { en: "QDII", zh: "QDII" },
    { en: "reits", zh: "地产基建类基金" },
    { en: "neutral", zh: "对冲类基金" },
    { en: "other", zh: "其他" },
  ],
  // barra因子中英文对照表
  barra_zh_en: {
    en: [
      "beta",
      "momentum",
      "size",
      "growth",
      "bp",
      "leverage",
      "liquidity",
      "nonlinearsize",
      "earningyield",
      "residualvolatility",
    ],
    zh: [
      "贝塔因子",
      "动量因子",
      "市值因子",
      "成长因子",
      "估值因子",
      "杠杆因子",
      "流动性因子",
      "非线性市值因子",
      "盈利因子",
      "残差波动率因子",
    ],
  },
  // 不同类型默认基准
  default_index: {
    equity: [{ indexCode: "000300.SH", indexName: "沪深300" }],
    equityhk: [{ indexCode: "HSCI.HI", indexName: "恒生综合" }],
    equityindex: [],
    equitywithhk: [
      { indexCode: "000300.SH", indexName: "沪深300" },
      { indexCode: "HSCI.HI", indexName: "恒生综合" },
    ],
    equityenhance: [],
    "equityhk-index": [],
    bond: [{ indexCode: "B80E20", indexName: "股债80:20" }],
    cbond: [
      { indexCode: "931078.CSI", indexName: "中证可转债及可交换债券指数" },
    ],
    purebond: [
      { indexCode: "CBA00201.CS", indexName: "中债综合财富(总值)指数" },
    ],
    bill: [{ indexCode: "H11015.CSI", indexName: "中证短债" }],
    obond: [{ indexCode: "B80E20", indexName: "股债80:20" }],
    bondindex: [],
    fof: [{ indexCode: "B80E20", indexName: "股债80:20" }],
    "fof:target-date": [{ indexCode: "B80E20", indexName: "股债80:20" }],
    "fof:target-risk": [{ indexCode: "B80E20", indexName: "股债80:20" }],
    QDII: [],
    reits: [],
    neutral: [{ indexCode: "B90E10", indexName: "股债90:10" }],
    other: [],
  },
  // 跳转对应组件ID
  jump_template_id: [
    { name: "基金规模", id: "sizeStructure", menu: "onePagePass" },
    { name: "机构占比", id: "sizeStructure", menu: "onePagePass" },
    { name: "擅长风格", id: "positionStyle", menu: "styleAnalysis" },
    { name: "相对擅长风格", id: "positionStyle", menu: "styleAnalysis" },
    { name: "擅长行业", id: "industryReportPosition", menu: "equityAnalysis" },
    {
      name: "擅长选股行业",
      id: "industryReportPosition",
      menu: "equityAnalysis",
    },
  ],
};

export default COMMON;
