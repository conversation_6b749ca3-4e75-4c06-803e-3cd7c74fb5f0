<!--  -->
<template>
  <div class="chart_one">
    <div style="display: flex; align-items: center; justify-content: space-between">
      <div class="title" style="margin-bottom: 24px">最新各类型基金配置情况</div>
      <el-button
        icon="el-icon-document-delete"
        style="margin-left: 16px"
        @click="exportExcel"
      >导出Excel</el-button>
    </div>
    <div style="display: flex" v-loading="loading">
      <el-table
        :data="fourdata1"
        class="table"
        :default-sort="{ prop: 'fof_weight', order: 'descending' }"
        ref="multipleTable"
        header-cell-class-name="table-header"
        max-height="400px"
      >
        <el-table-column
          v-for="(item, index) in columnList"
          :key="index"
          :prop="item.value"
          :label="item.label"
          align="gotoleft"
        ></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { exportTitle, exportTable } from "@/utils/exportWord.js";
import { filter_json_to_excel } from "@/utils/exportExcel.js";
export default {
  //import引入的组件需要注入到对象中才能使用
  data() {
    //这里存放数据
    return {
      companyCreateDate: "",
      quarterList: [],
      targetQuarter: "",
      datatable: [],
      benchmarkvalue: "",
      benchmarkvaluename: "",
      benchmarkoptions: [],
      fourdata1: [],
      fourdata2: [],
      notesData: {
        fholdfundetail: ""
      },
      info: {},
      loading: true,
      columnList: [
        {
          label: "基金类型",
          value: "csrctype"
        },
        {
          label: "仓位",
          value: "weight",
          format: "fix2p"
        },
        {
          label: "A类仓位",
          value: "A"
        },
        {
          label: "C类仓位",
          value: "C"
        },
        {
          label: "内部公司权重",
          value: "company_weight"
        },
        {
          label: "基金经理平均管理时间",
          value: "managed_time"
        }
      ]
    };
  },
  filters: {
    fix2p(value) {
      if (value == "--") return value;
      else return (value * 100).toFixed(2) + "%";
    },
    fixY(value) {
      if (value == "--") return value;
      else {
        return (Number(value) / *********).toFixed(2) + "亿";
      }
    },
    fixt(value) {
      if (value == "--") return value;
      else {
        return Number(value).toFixed(2) + "年";
      }
    },
    fixp(value) {
      if (value == "--") return value;
      else {
        return Number(value).toFixed(2);
      }
    }
  },
  //方法集合
  methods: {
    fix2p(value) {
      if (value == "--") return value;
      else return (value * 100).toFixed(2) + "%";
    },
    fixt(value) {
      if (value == "--") return value;
      else {
        return Number(value).toFixed(2) + "年";
      }
    },
    getData(data, info) {
      this.info = info;
      this.loading = false;
      if (info.type == "portfolio") {
        this.columnList.splice(
          this.columnList.findIndex(item => {
            return item.value == "company_weight";
          }),
          1
        );
      }
      this.fourdata1 = data.map(item => {
        return {
          ...item,
          weight: this.fix2p(item.weight),
          A: this.fix2p(item.A),
          C: this.fix2p(item.C),
          company_weight: this.fix2p(item.company_weight),
          managed_time: this.fixt(item.managed_time)
        };
      });
    },
    changgedate() {
      this.resolveData();
    },
    resolveData() {
      this.$emit("resolveFather", this.targetQuarter.join(" "));
    },
    generateQuarterList() {
      let option = [];
      let qList = ["Q1", "Q2", "Q3", "Q4"];
      let pre = this.companyCreateDate;
      let now = this.FUNC.transformDate(new Date());

      let preYear = pre.slice(0, 4);
      let nowYear = now.slice(0, 4);
      let preQ = this.FUNC.dateToQuarter(pre).slice(5);
      let nowQ = this.FUNC.dateToQuarter(now).slice(5);

      let yList = Array.from(
        { length: Math.abs(nowYear - preYear + 1) },
        (item, index) => (item = parseInt(preYear) + index)
      );

      for (let y of yList) {
        let yobj = {
          value: y,
          label: y,
          children: []
        };
        if (y == preYear) {
          qList.forEach(q => {
            if (q >= preQ) {
              yobj.children.push({ value: q, label: q });
            }
          });
        } else if (y == nowYear) {
          qList.forEach(q => {
            if (q <= nowQ) {
              yobj.children.push({ value: q, label: q });
            }
          });
        } else {
          qList.forEach(q => yobj.children.push({ value: q, label: q }));
        }
        option.push(yobj);
      }
      this.quarterList = option;
      if (option[option.length - 1].children.length == 1) {
        this.targetQuarter = [
          option[option.length - 2].value,
          option[option.length - 2].children[
            option[option.length - 2].children.length - 1
          ].value
        ];
      } else {
        this.targetQuarter = [
          option[option.length - 1].value,
          option[option.length - 1].children[
            option[option.length - 1].children.length - 2
          ].value
        ];
      }
      this.resolveData();
    },
    getTime(data) {
      if (data.funds_manager && data.funds_manager.length > 0) {
        this.companyCreateDate = data.fund_message[0]["成立日期"] || "暂无数据";
      } else {
        this.companyCreateDate = "2008-01-01";
      }
      this.generateQuarterList();
    },
    exportExcel() {
      let list = [
        {
          label: "基金类型",
          value: "csrctype"
        },
        {
          label: "仓位",
          value: "weight",
          format: "fix2p"
        },
        {
          label: "A类仓位",
          value: "A",
          format: "fix2p"
        },
        {
          label: "C类仓位",
          value: "C",
          format: "fix2p"
        },
        {
          label: "内部公司权重",
          value: "company_weight",
          format: "fix2p"
        },
        {
          label: "基金经理平均管理时间",
          value: "managed_time",
          format: "fix2p"
        }
      ];
      filter_json_to_excel(list, this.fourdata1, "最新各类型基金配置情况");
    },
    createPrintWord() {
      if (this.fourdata1.length) {
        return [
          ...exportTitle("最新各类型基金配置情况"),
          ...exportTable(
            this.columnList.map(item => {
              return { ...item, format: undefined };
            }),
            this.fourdata1
          )
        ];
      } else {
        return [];
      }
    }
  }
};
</script>
