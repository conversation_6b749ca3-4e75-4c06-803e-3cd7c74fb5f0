.margintop5 {
	margin-top: 5px;
}
.fontsize30 {
	font-size: 30px;
}
.fontsize24 {
	font-size: 24px;
}
.headernamesize {
	font-size: 30px;
	display: flex;
}
.headercodesize {
	font-size: 22px;
	color: #999999;
	bottom: 3px;
}
.headerfont {
	font-size: 16px;
}
.fontsize12 {
	font-size: 12px;
}
.fontsize6 {
	font-size: 6px;
}
.height30 {
	height: 30px;
}
.shiyingxingpingfen {
	display: flex;
	flex-wrap: wrap;
	margin-left: 80px;
}
.fontsize20 {
	font-size: 20px;
}
.marginleft20 {
	margin-left: 20px;
}
.zonghefenshu {
	color: #ffb000;
	display: table-cell;
	vertical-align: middle;
	text-align: center;
	vertical-align: middle;
	height: 60px;
	width: 60px;
	font-size: 30px;
	background: #eaf4ff;
	border-radius: 50%;
	border: 1px solid #fff;
}
.zonghefenshu2 {
	color: #ffb000;
	display: flex;
	align-items: center;
	text-align: center;
	vertical-align: middle;
	height: 40px;
	width: 40px;
	font-size: 10px;
	background: #eaf4ff;
	border-radius: 50%;
	border: 1px solid #fff;
}
.zonghepaimingwai {
	width: 200px;
	height: 60px;
	margin-top: 10px;
	background: #2086fd;
	margin-left: -50px;
	display: table;
}
.marginleft60 {
	margin-left: 60px;
}
.paddingright20 {
	padding-right: 20px;
}
.cbond .progrsser {
	padding-right: 20px;
	width: 200px;
	font-size: 12px;
}

.progrsser {
	padding-right: 20px;
	width: 300px;
	font-size: 12px;
}
.danweijin {
	padding-right: 20px;
}
.nianhua {
	padding-right: 20px;
}

/* 矩形旁边的三角形 */
.pentagon {
	margin: 10px 0 0 0;

	position: relative;

	height: 0;

	width: 0;

	border-left: 20px solid #2086fd;

	border-right: 20px solid transparent;

	border-bottom: 30px solid transparent;

	border-top: 30px solid transparent;
}
/* shiyingxing */
.newst .zonghepaimingwai {
	width: 200px;
	height: 60px;
	margin-top: 10px;
	background: #00ccff;
	margin-left: -50px;
	display: table;
}
.newst .box {
	width: 80px;
	height: 80px;
	background: #00ccff;
	display: -webkit-box;
	-webkit-box-orient: horizontal;
	-webkit-box-pack: center;
	-webkit-box-align: center;
	/* 警告 (157:3) You should write display: flex by final spec instead of display: box */
	display: -moz-box;
	-moz-box-orient: horizontal;
	-moz-box-pack: center;
	-moz-box-align: center;

	display: -o-box;
	-o-box-orient: horizontal;
	-o-box-pack: center;
	-o-box-align: center;

	display: -ms-box;
	-ms-box-orient: horizontal;
	-ms-box-pack: center;
	-ms-box-align: center;

	display: box;
	box-orient: horizontal;
	box-pack: center;
	box-align: center;
}
.newst .pentagon {
	margin: 10px 0 0 0;

	position: relative;

	height: 0;

	width: 0;

	border-left: 20px solid #00ccff;

	border-right: 20px solid transparent;

	border-bottom: 30px solid transparent;

	border-top: 30px solid transparent;
}
/*  */
.widthbox {
	width: 300px;
}
.github-badge {
	margin-left: 10px;
	display: inline-block;
	border-radius: 4px;
	text-shadow: none;
	font-size: 14px;
	color: #fff;
	margin-top: 13px;
	min-width: 120px;
}
.github-badge .badge-subject {
	display: inline-block;
	background-color: #4d4d4d;
	padding: 4px 4px 4px 6px;
	border-top-left-radius: 4px;
	border-bottom-left-radius: 4px;
}
.github-badge .badge-value {
	display: inline-block;
	padding: 4px 6px 4px 4px;
	border-top-right-radius: 4px;
	border-bottom-right-radius: 4px;
}
.github-badge .bg-brightgreen {
	background-color: #4dc820 !important;
}
.github-badge .bg-orange {
	background-color: #ffa500 !important;
}
.github-badge .bg-yellow {
	background-color: #d8b024 !important;
}
.github-badge .bg-blueviolet {
	background-color: #8833d7 !important;
}
.github-badge .bg-pink {
	background-color: #f26bae !important;
}
.github-badge .bg-red {
	background-color: #e05d44 !important;
}
.github-badge .bg-blue {
	background-color: #007ec6 !important;
}
.github-badge .bg-lightgrey {
	background-color: #9f9f9f !important;
}
.github-badge .bg-grey,
.github-badge .bg-gray {
	background-color: #555 !important;
}
.github-badge .bg-lightgrey,
.github-badge .bg-lightgray {
	background-color: #9f9f9f !important;
}
