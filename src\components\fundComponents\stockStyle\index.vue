<template>
	<div class="chart_one">
		<div class="flex_card">
			<div v-loading="loading" class="small_template">
				<div class="title">
					股票风格特征<el-tooltip
						class="item"
						effect="dark"
						content="根据持仓权重计算在各个 Barra 因子上的风险敞口，分析债券基金的持股风格。"
						placement="right-start"
					>
						<svg width="14" height="14" viewBox="0 0 14 14" fill="none">
							<path
								fill-rule="evenodd"
								clip-rule="evenodd"
								d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
								fill="black"
								fill-opacity="0.45"
							/>
						</svg>
					</el-tooltip>
				</div>
				<div class="charts_fill_class">
					<v-chart
						ref="stockStyle1"
						class="charts_analysis_class"
						v-loading="loading"
						autoresize
						element-loading-text="暂无数据"
						element-loading-spinner="el-icon-document-delete"
						element-loading-background="rgba(239, 239, 239, 0.5)"
						:options="peizhibar3"
					/>
				</div>
			</div>
			<div v-loading="loading" class="small_template">
				<div class="title">股票风格特征</div>
				<div class="charts_fill_class">
					<v-chart
						ref="stockStyle2"
						class="charts_analysis_class"
						v-loading="loading"
						autoresize
						element-loading-text="暂无数据"
						element-loading-spinner="el-icon-document-delete"
						element-loading-background="rgba(239, 239, 239, 0.5)"
						:options="peizhibar4"
					/>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { exportTitle, exportChart } from '@/utils/exportWord.js';
// 股票风格特征
import { barChartOption } from '@/utils/chartStyle.js';

import VChart from 'vue-echarts';
export default {
	name: 'stockStyle',
	components: { VChart },
	data() {
		return {
			loading: true,
			peizhibar3: {},
			peizhibar4: {}
		};
	},
	methods: {
		getData(data, info) {
			this.loading = false;
			if (
				data.style_avg == '数据缺失，请联系数据工程师' ||
				data.style_avg[0] == null ||
				data.style_avg[0] == [] ||
				data.style_avg[0] == '' ||
				data.style_avg[0] == {}
			) {
				this.loading = true;
			} else {
				let temps1 = [
					data.style_avg[0].earningyield.toFixed(2),
					data.style_avg[0].size.toFixed(2),
					data.style_avg[0].liquidity.toFixed(2),
					data.style_avg[0].bp.toFixed(2),
					data.style_avg[0].leverage.toFixed(2),
					data.style_avg[0].nonlinearsize.toFixed(2),
					data.style_avg[0].momentum.toFixed(2),
					data.style_avg[0].growth.toFixed(2),
					data.style_avg[0].residualvolatility.toFixed(2),
					data.style_avg[0].beta.toFixed(2)
				];
				let max = temps1[0];
				let min = temps1[0];
				let len = temps1.length;
				for (var i = 1; i < len; i++) {
					if (temps1[i] > max) {
						max = temps1[i];
					}
					if (temps1[i] < min) {
						min = temps1[i];
					}
				}
				// 绘制图表
				this.peizhibar3 = barChartOption({
					tooltip: { type: 'shadow' },
					visualMap: {
						show: false,
						orient: 'horizontal',
						left: 'center',
						min: min,
						max: max,
						text: ['High Score', 'Low Score'],
						dimension: 0,
						inRange: {
							color: ['#4096ff', '#4096ff']
						}
					},
					xAxis: [{ type: 'value' }],
					yAxis: [
						{
							type: 'category',
							data: [
								'盈利因子',
								'市值因子',
								'流动性因子',
								'估值因子',
								'杠杆因子',
								'非线性市值因子',
								'动量因子',
								'成长因子',
								'残差波动率因子',
								'贝塔因子'
							],
							show: false
						}
					],
					series: [
						{
							name: '因子暴露',
							type: 'bar',
							stack: '总量',
							label: {
								show: true,
								textStyle: {
									fontSize: '12px',
									color: 'rgba(0, 0, 0, 0.65)',
									fontWeight: 400,
									fontStyle: 'normal'
								},
								formatter: '{b}'
							},
							data: [
								data.style_avg[0].earningyield.toFixed(2),
								data.style_avg[0].size.toFixed(2),
								data.style_avg[0].liquidity.toFixed(2),
								data.style_avg[0].bp.toFixed(2),
								data.style_avg[0].leverage.toFixed(2),
								data.style_avg[0].nonlinearsize.toFixed(2),
								data.style_avg[0].momentum.toFixed(2),
								data.style_avg[0].growth.toFixed(2),
								data.style_avg[0].residualvolatility.toFixed(2),
								data.style_avg[0].beta.toFixed(2)
							]
						}
					]
				});
			}
			if (
				data.style_newest == '数据缺失，请联系数据工程师' ||
				data.style_newest[0] == null ||
				data.style_newest[0] == [] ||
				data.style_newest[0] == '' ||
				data.style_newest[0] == {}
			) {
				this.loading = true;
			} else {
				let temps2 = [
					data.style_avg[0].earningyield.toFixed(2),
					data.style_avg[0].size.toFixed(2),
					data.style_avg[0].liquidity.toFixed(2),
					data.style_avg[0].bp.toFixed(2),
					data.style_avg[0].leverage.toFixed(2),
					data.style_avg[0].nonlinearsize.toFixed(2),
					data.style_avg[0].momentum.toFixed(2),
					data.style_avg[0].growth.toFixed(2),
					data.style_avg[0].residualvolatility.toFixed(2),
					data.style_avg[0].beta.toFixed(2)
				];
				let max2 = temps2[0];
				let min2 = temps2[0];
				let len2 = temps2.length;
				for (var i = 1; i < len2; i++) {
					if (temps2[i] > max2) {
						max2 = temps2[i];
					}
					if (temps2[i] < min2) {
						min2 = temps2[i];
					}
				}
				// 绘制图表
				this.peizhibar4 = barChartOption({
					tooltip: { type: 'shadow' },
					visualMap: {
						show: false,
						orient: 'horizontal',
						left: 'center',
						min: min2,
						max: max2,
						text: ['High Score', 'Low Score'],
						dimension: 0,
						inRange: {
							color: ['#4096ff', '#4096ff']
						}
					},
					xAxis: [{ type: 'value' }],
					yAxis: [
						{
							type: 'category',
							data: [
								'盈利因子',
								'市值因子',
								'流动性因子',
								'估值因子',
								'杠杆因子',
								'非线性市值因子',
								'动量因子',
								'成长因子',
								'残差波动率因子',
								'贝塔因子'
							],
							show: false
						}
					],
					series: [
						{
							name: '因子暴露',
							type: 'bar',
							stack: '总量',
							label: {
								show: true,
								textStyle: {
									fontSize: '12px',
									color: 'rgba(0, 0, 0, 0.65)',
									fontWeight: 400,
									fontStyle: 'normal'
								},
								formatter: '{b}'
							},
							data: [
								data.style_newest[0].earningyield.toFixed(2),
								data.style_newest[0].size.toFixed(2),
								data.style_newest[0].liquidity.toFixed(2),
								data.style_newest[0].bp.toFixed(2),
								data.style_newest[0].leverage.toFixed(2),
								data.style_newest[0].nonlinearsize.toFixed(2),
								data.style_newest[0].momentum.toFixed(2),
								data.style_newest[0].growth.toFixed(2),
								data.style_newest[0].residualvolatility.toFixed(2),
								data.style_newest[0].beta.toFixed(2)
							]
						}
					]
				});
			}
		},
		createPrintWord() {
			let chart1 = this.$refs['stockStyle1'].getDataURL({
				type: 'png',
				pixelRatio: 1,
				backgroundColor: '#fff'
			});
			let chart2 = this.$refs['stockStyle1'].getDataURL({
				type: 'png',
				pixelRatio: 1,
				backgroundColor: '#fff'
			});
			return [...exportTitle('股票风格特征'), ...exportChart(chart1), ...exportChart(chart2)];
		}
	}
};
</script>

<style scoped>
.flex_card > div {
	height: 350px;
}
</style>
