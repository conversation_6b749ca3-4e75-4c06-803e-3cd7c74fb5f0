<template>
    <div class="charts_fill_class" v-loading="loading">
        <el-empty image-size="160" v-if="showEmpty"></el-empty>

        <v-chart
        v-else
            ref="companySizeChange"
            :options="option"
            element-loading-text="暂无数据"
            element-loading-spinner="el-icon-document-delete"
            element-loading-background="rgba(239, 239, 239, 0.5)"
            class="charts_one_class"
            autoresize
            @legendselectchanged="handleLegendSelectChanged"
				@zr:dblclick="handleDblClick"
        ></v-chart>
    </div>
</template>

<script>
import VChart from 'vue-echarts';
import { holdingRateTrend} from '@/api/pages/tkAnalysis/portfolio.js';
import { lineChartOption } from '@/utils/chartStyle.js';
export default {
components: { VChart },
data() {
    return {
        option: {},
        loading: true,
        showEmpty: true,
        doubleClick:false,
			legendChanged:false,
			legendData:{}
    };
},
watch: {
		doubleClick: {
			handler(val) {
				if(val && this.legendChanged){
					const chart = this.$refs.companySizeChange;
					let legendWai = this.legendData.name;
					for (const element in this.legendData.selected) {
						//显示当前legent 关闭非当前legent
						if (legendWai == element) {
							chart.dispatchAction({
								type: 'legendSelect',
								name: element
							});
						} else {
							chart.dispatchAction({
								type: 'legendUnSelect',
								name: element
							});
						}
					}
					this.doubleClick = false;
					this.legendChanged = false;
				}
			},
			immediate: true,
		},
	},
methods: {
    handleDblClick(){
			this.doubleClick = true;
		},
		handleLegendSelectChanged (params)  {
			this.legendChanged = true;
			this.legendData = params;
		
			
		},
    getChartData(data){
        let dateList = [],
            data1 = [],
            data2 = [],
            name='沪深300',
            data3 = [],
            data4 = [];
        data.forEach(element => {
            dateList.push(element.date);
            data1.push(element.returnCum * 100);
            data2.push(element.contrastReturn * 100);
            data3.push(element.drawdown * 100);
            data4.push(element.excess * 100);
        });
        name = data.length >0 ? data[0].indexName : '沪深300';
        return {dateList,data1,data2,data3,data4,name}
    },
   async getData(param) {
    this.loading = true;
        let res = await holdingRateTrend(param);
        
        if(res.mtycode != 200){
            return;
        }
        if(res.data.length >0){
            this.showEmpty = false;
        }else{
            this.showEmpty = true;
            return;
        }
        const {dateList,data1,data2,data3,data4,name} = this.getChartData(res.data);

        this.$emit('tableData',res.data,name);

        this.option = lineChartOption({
            grid:{
                left:'3%'
            },
            tooltip: {
					backgroundColor: '#ffffff',
                    
					formatter: function (obj) {
                        //数据排序
                        let list = obj;
						list.sort((a,b)=>{
							if(a.value-b.value < 0){
								return 1;
							}else{
								return -1;
							}
						})
						var value = `<div style="font-size:14px;">` + list?.[0].axisValue + `</div>`;
						for (let i = 0; i < list.length; i++) {
							value +=
								`<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
								`<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
                                    list?.[i].color +
								`;"></div>` +
								`<div style="font-family: PingFang SC;">` +
                                    list?.[i].seriesName +
								'</div></div>' +
								`<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
								(Number(list?.[i].value) * 1).toFixed(2) +
								'%</div>' +
								`</div>`;
						}
						return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
					}
				},
            legend: {
                bottom:'-2%',
                data: [
                    {
                        name: '组合累计净值',
                    
                    },
                    {
                        name,
                        
                    },
                    {
                        name: '回撤',
                        icon: 'rect',
                        backgroundColor:'#7388A9'
                    },
                    {
                        name: '超额收益',
                        icon: 'rect',
                    },
                ]
            },
            xAxis: [{
                type: 'category',
                boundaryGap: false,
                data: dateList,
                axisLabel: {

                    // interval 可以定义成数字，但这也会有一个弊端，在不确定数据量的时候，间隔数不好定义，不能随心所欲的控制所要展示的内容 
                    interval:2
                }, 
            }],
            yAxis: 
               [ {
                    type: 'value',
                    axisLine:{
                        show:false
                    },
                    axisTick:{
                        show:false
                    },
                    axisLabel: {show:true,formatter: '{value}%'},
                    name:'收益率',
                    formatter: '{value}%',
                    nameLocation: 'center' ,
                    nameGap:50,
                    splitLine:{
                        lineStyle:{
                            type:'dashed'
                        }
                    }   
                },{
                    type: 'value',
                    axisLine:{
                        show:false
                    },
                    axisTick:{
                        show:false
                    },
                    name:'回撤',
                    nameGap:50,
                    nameLocation: 'center' ,
                    axisLabel: {show:true,},
                    formatter: '{value}%',
                    nameTextStyle:{
                        fontSize: 12,
                        color: "rgba(0, 0, 0, 0.65)"
                    }  
                }],
           
            series: [
                {
                    name: '组合累计净值',
                    type: 'line',
                    lineStyle:{
                    },
                    symbol: 'none',
                    data: data1,
                    yAxisIndex:0
                },
                {
                    name,
                    type: 'line',
                    symbol: 'none',
                    lineStyle:{
                    },
                    data: data2,
                    yAxisIndex:0
                },
                {
                    name: '回撤',
                    type: 'line',
                    symbol: 'none',
                    lineStyle:{
                        opacity:0
                    },
                    areaStyle: {
                    },
                    data: data3,
                    yAxisIndex:1
                },
                {
                    name: '超额收益',
                    type: 'line',
                    symbol: 'none',
                    lineStyle:{
                        opacity:0
                    },
                    areaStyle: {
                    },
                    data: data4,
                    yAxisIndex:0
                },
            ]
        });
        this.loading = false;
    }
}
};
</script>

<style scoped>
.chart_one{
padding: 0;
box-shadow: none;
}
.charts_fill_class{
    .echarts{
    height: 248px;

    }
}
</style>
