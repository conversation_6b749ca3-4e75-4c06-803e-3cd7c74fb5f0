<template>
    <div v-show="show">
      <div style="page-break-inside: avoid; position: relative">
        <div>
          <div style="page-break-inside: avoid; position: relative">
            <div class="charts_fill_class">
              <v-chart v-show="!industryrequestflag"
                       class="charts_one_class"
                       ref="industryEvaluation"
                       autoresize
                       v-loading="industryrequestflag"
                       @legendselectchanged="legendselectchanged"
                       :options="industryoption" />
              <el-empty v-show="industryrequestflag"
                        description="暂无数据"></el-empty>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script>
  // 行业评价
  import VChart from 'vue-echarts';
  import { exportTitle, exportChart } from '@/utils/exportWord.js';
  import { lineChartOption } from '../../../components/chart/chartStyle.js'
  export default {
    name: 'FundManagersPerformanceChart',
    components: {
      VChart
    },
    data () {
      let legendName={
                'returnCum':{name:'拟合曲线',key:'returnCum'} ,
                'contrastReturn':{name:'',key:'contrastReturn'},
                'excessReturn':{name:'超额收益曲线',key:'excess'},
                'maximumDrawdown':{name:'回撤',key:'drawdown'}
      }
      return {
        legendName:legendName,
        industryrequestflag: true,
        industryoption: {},
        show: true,
        info: {},
        selected:{
        },
      };
    },
    methods: {
      openvideo () {
        window.open('https://www.bilibili.com/video/BV1Cd4y1D7wx?share_source=copy_web');
      },
      // 获取父组件传递数据
      getData (data, info) {
        this.show = true;
        this.info = info;
        this.legendName.contrastReturn.name=info.contrastReturnName
        // data = this.formatData(data);
        this.industryrequestflag = false;
        this.industryoption = lineChartOption({
          toolbox:'none',
          color: ['#4096ff', '#4096ff', '#7388A9', '#7388A9','#389E0D'],
          legend: {
            selected:this.selected,
            data:[
            { name: this.legendName.returnCum.name, icon: 'line' },
            { name: this.legendName.contrastReturn.name, icon: 'line' },
            { name: this.legendName.excessReturn.name, icon: 'rect' },
            { name: this.legendName.maximumDrawdown.name, icon: 'line' }
          ]},
          tooltip: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          },
          xAxis: [
            {
              data: data.map((item) => {
                return item.date;
              }),
              isAlign: true,
              axisLabelRotate: -45
            }
          ],
          yAxis: [
            {
              type: 'value',
              formatter (value) {
                return value ;
              }
            },
            {
              type: 'value',
              splitLine: false
            }
          ],
          series: [
            {
              name: this.legendName.returnCum.name,
              type: 'line',
              symbol:'none',
              data: data.map((item) => {
                return item[this.legendName.returnCum.key]||'--';
              })
            },
            {
              name: this.legendName.contrastReturn.name,
              type: 'line',
              symbol:'none',
              data: data.map((item) => {
                return item[this.legendName.contrastReturn.key]||'--';
              })
            },
            // {
            //   name: this.legendName.zhongzhen1000.name,
            //   type: 'line',
            //   symbol:'none',
            //   data: data.map((item) => {
            //     return item[this.legendName.zhongzhen1000.value]||'--';
            //   })
            // },
            {
              name: this.legendName.excessReturn.name,
              type: 'line',
              yAxisIndex: 1,
              symbol:'none',
              lineStyle:{
                opacity:0
             },
              areaStyle:{},
              data: data.map((item) => {
                return item[this.legendName.excessReturn.key]||'--';
              })
            },
            {
              name: this.legendName.maximumDrawdown.name,
              type: 'line',
              symbol:'none',
              yAxisIndex: 1,
              data: data.map((item) => {
                return item[this.legendName.maximumDrawdown.key]||'--';
              })
            }
          ]
        });
      },
      // // 格式化接收数据
      // formatData (data) {
      //   let errData = [];
      //   let infoData = [];
      //   data.map((item) => {
      //     if (item.industry_rank == '--') {
      //       errData.push(item);
      //     } else {
      //       infoData.push(item);
      //     }
      //   });
      //   infoData = infoData
      //     .sort((a, b) => {
      //       return b.industry_rank - a.industry_rank;
      //     })
      //     .map((item) => {
      //       if (this.info.flag == 2) {
      //         return {
      //           ...item,
      //           weight_top: item.weight_top * 100,
      //           weight_other: item.weight_other * 100,
      //           weight_bottom: item.weight_bottom * 100
      //         };
      //       } else {
      //         return { ...item };
      //       }
      //     });
      //   let allData = [...infoData].filter((item) => {
      //     return item.industry_name !== '--';
      //   });
      //   if (allData.length == 0) {
      //     return [...infoData];
      //   } else {
      //     return allData;
      //   }
      // },
      // 数据获取失败
      hideLoading () {
        this.industryrequestflag = false;
        this.show = false;
      },
      createPrintWord () {
        this.$refs['industryEvaluation'].mergeOptions({ toolbox: { show: false } });
        let height = this.$refs['industryEvaluation'].$el.clientHeight;
        let width = this.$refs['industryEvaluation'].$el.clientWidth;
        let chart = this.$refs['industryEvaluation'].getDataURL({
          type: 'png',
          pixelRatio: 2,
          backgroundColor: '#fff'
        });
        this.$refs['industryEvaluation'].mergeOptions({ toolbox: { show: true } });
        return this.show ? [...exportTitle('行业评价'), ...exportChart(chart, { width, height })] : [];
      },
      legendselectchanged(params){
        this.selected = params.selected
      }
    }
  };
  </script>
  
  <style></style>
  