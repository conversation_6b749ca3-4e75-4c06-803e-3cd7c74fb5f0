<template>
	<div>
		<el-dialog :visible.sync="dialogVisible" width="100%">
			<div>
				<div class="flex_between">
					<div class="flex_start mb-24">
						<div>权重选择：</div>
						<el-input v-model="weight" style="width: 200px" placeholder="请输入权重大于"></el-input>
						<el-button class="ml-12" type="primary" @click="submit">查询</el-button>
					</div>
					<div>
						<el-button type="primary" @click="exportExcel">导出为Excel</el-button>
					</div>
				</div>

				<el-table :data="data" :span-method="objectSpanMethod" border style="width: 100%" :cell-style="cellClassName">
					<el-table-column
						v-for="item in column"
						:key="item.value"
						:prop="item.value"
						:label="item.label"
						:min-width="item.width"
						align="gotoleft"
					>
						<template #header>
							<div :style="`min-width:calc(${item.width} - 20px); white-space: nowrap; overflow: hidden; text-overflow: ellipsis`">
								{{ item.label }}
							</div>
						</template>
					</el-table-column>
				</el-table>
			</div>
			<div slot="footer">
				<el-button @click="dialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="dialogVisible = false">确 定</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import { filter_json_to_excel } from '@/utils/exportExcel.js';
export default {
	data() {
		return {
			dialogVisible: false,
			column: [],
			industry_list: [],
			data: [],
			rowspans: [],
			active_industry: [],
			one_industry: [],
			two_industry: [],
			three_industry: [],
			weight: 0,
			cache_data: [],
			color_list: [
				// '#4096ff',
				'#4096ff',
				'#FD6865',
				'#946DFF',
				'#7388A9',
				'#6F80DD',
				'#CD8150',
				'#F6C243',
				'#83303D',
				'#6C96F2',
				'#FA541C',
				'#6F29C1',
				'#8FBA9C',
				'#D94F84',
				'#FD6865',
				'#88C9E9',
				'#ED589D',
				'#EE7C62',
				'#51A77D',
				'#4247AC',
				'#4990F7',
				'#4C97CC',
				'#CD72CA',
				'#377C96',
				'#773A84',
				'#ADD950',
				'#C28459',
				'#8C8C50',
				'#E94C1D'
			]
		};
	},
	props: {
		info: {
			type: Object,
			default: {}
		}
	},
	methods: {
		getData(industry, data) {
			console.log(industry);
			this.industry_list = industry.map((item, index) => {
				return {
					...item,
					color: this.color_list[index]
				};
			});
			this.cache_data = data;
			this.submit();
			this.dialogVisible = true;
		},
		submit() {
			this.formatColumn();
			this.formatData(this.cache_data.filter((v) => v.code_list.some((n) => n.ratioinN >= this.weight)));
		},
		formatColumn() {
			let column = [
				{ label: '', value: 'industry_1', width: '120px' },
				{ label: '', value: 'industry_2', width: '120px' },
				{ label: '', value: 'industry_3', width: '120px' }
			];
			this.info.code_list.map((item) => {
				column.push({ label: item.name, value: item.code, width: '150px' });
			});
			this.column = column;
		},
		formatData(data) {
			console.log(data);
			let industry = [];
			this.industry_list.map((item) => {
				if (item.children?.length != 0) {
					item.children.map((obj) => {
						if (obj.children?.length != 0) {
							obj.children.map((tem) => {
								if (industry.findIndex((v) => v.industry_3_code == tem.value) == -1) {
									industry.push({
										industry_1: item.label,
										industry_2: obj.label,
										industry_3: tem.label,
										industry_1_code: item.value,
										industry_2_code: obj.value,
										industry_3_code: tem.value,
										colspan: 1
									});
								}
							});
						} else {
							if (industry.findIndex((v) => v.industry_2_code == obj.value) == -1) {
								industry.push({
									industry_1: item.label,
									industry_2: obj.label,
									industry_1_code: item.value,
									industry_2_code: obj.value,
									colspan: 2
								});
							}
						}
					});
				} else {
					if (industry.findIndex((v) => v.industry_1_code == item.value) == -1) {
						industry.push({
							industry_1: item.label,
							industry_1_code: item.value,
							colspan: 3
						});
					}
				}
			});
			let res = [];
			data.map((item) => {
				industry.map((obj) => {
					// 三级行业
					if (obj.industry_3_code) {
						if (obj.industry_3_code == item.swlevel3code) {
							let value = {};
							item.code_list.map((v) => {
								if (v.ratioinN >= this.weight) {
									value[v.code] = item.name + `(${v.ratioinN})`;
								}
							});
							res.push({ ...obj, ...value });
						}
					} else if (obj.industry_2_code) {
						if (obj.industry_2_code == item.swlevel2code) {
							let value = {};
							item.code_list.map((v) => {
								if (v.ratioinN >= this.weight) {
									value[v.code] = item.name + `(${v.ratioinN})`;
								}
							});
							res.push({ ...obj, ...value });
						}
					} else {
						if (obj.industry_1_code == item.swlevel1code) {
							let value = {};
							item.code_list.map((v) => {
								if (v.ratioinN >= this.weight) {
									value[v.code] = item.name + `(${v.ratioinN})`;
								}
							});
							res.push({ ...obj, ...value });
						}
					}
				});
			});
			this.active_industry = [];
			res.map((item) => {
				if (item.industry_3_code) {
					let index = this.active_industry.findIndex((v) => v.value == item.industry_3_code);
					if (index == -1) {
						this.active_industry.push({
							label: item.industry_3,
							value: item.industry_3_code,
							num: 1,
							use: false
						});
						this.active_industry.push({
							label: item.industry_2,
							value: item.industry_2_code,
							num: 1,
							use: false
						});
						this.active_industry.push({
							label: item.industry_1,
							value: item.industry_1_code,
							num: 1,
							use: false
						});
					} else {
						this.active_industry[index].num += 1;
						let index1 = this.active_industry.findIndex((v) => v.value == item.industry_1_code);
						this.active_industry[index1].num += 1;
						let index2 = this.active_industry.findIndex((v) => v.value == item.industry_2_code);
						this.active_industry[index2].num += 1;
					}
				} else if (item.industry_2_code) {
					let index = this.active_industry.findIndex((v) => item.industry_2_code == v.value);
					if (index == -1) {
						this.active_industry.push({
							label: item.industry_2,
							value: item.industry_2_code,
							num: 1,
							use: false
						});
						this.active_industry.push({
							label: item.industry_1,
							value: item.industry_1_code,
							num: 1,
							use: false
						});
					} else {
						this.active_industry[index].num += 1;
						let index1 = this.active_industry.findIndex((v) => v.value == item.industry_1_code);
						this.active_industry[index1].num += 1;
					}
				} else {
					let index = this.active_industry.findIndex((v) => item.industry_1_code == v.value);
					if (index == -1) {
						this.active_industry.push({
							label: item.industry_1,
							value: item.industry_1_code,
							num: 1,
							use: false
						});
					} else {
						this.active_industry[index].num += 1;
					}
				}
			});
			console.log(this.active_industry);
			let empty_data = this.markDuplicates(this.sortData(res));
			this.removeDataEmpty(empty_data);
		},
		removeDataEmpty(data) {
			let new_data = data.map((obj) => {
				let new_obj = { ...obj };
				this.info.code_list.map((item) => {
					if (!obj[item.code]) {
						new_obj[item.code] = '';
					}
				});
				return new_obj;
			});
			console.log(new_data);
			let sw = [];
			new_data.map((item) => {
				if (item.industry_3_code) {
					let index = sw.findIndex((v) => v.industry_3_code == item.industry_3_code);
					if (index == -1) {
						sw.push({ industry_3_code: item.industry_3_code, children: [...item] });
					} else {
						sw[index].children.push({ ...item });
					}
				} else if (item.industry_2_code) {
					let index = sw.findIndex((v) => v.industry_2_code == item.industry_2_code);
					if (index == -1) {
						sw.push({ industry_2_code: item.industry_2_code, children: [...item] });
					} else {
						sw[index].children.push({ ...item });
					}
				} else {
					let index = sw.findIndex((v) => v.industry_1_code == item.industry_1_code);
					if (index == -1) {
						sw.push({ industry_1_code: item.industry_1_code, children: [...item] });
					} else {
						sw[index].children.push({ ...item });
					}
				}
			});
			console.log(sw);
			sw.map((item) => {
				this.alignTop(item.children);
			});
		},
		// 填充空白
		alignTop(val) {
			let data = val;
			console.log(data);
			// 遍历数组
			for (let i = 0; i < data.length; i++) {
				const currentItem = data[i];

				// 遍历当前项的每个属性
				for (const key in currentItem) {
					if (currentItem[key] === '') {
						// 在其他项中查找对应字段不为空的值，并赋值给当前项
						for (let j = 0; j < data.length; j++) {
							const otherItem = data[j];
							if (otherItem[key] !== '') {
								currentItem[key] = otherItem[key];
								delete otherItem[key];
								break;
							}
						}
					}
				}
			}
			console.log(data);
			// return data;
		},
		markDuplicates(array) {
			let obj = {};
			array.map((item) => {
				if (item.industry_3_code) {
					if (!obj[item.industry_3_code]) {
						obj[item.industry_3_code] = 1;
					} else {
						obj[item.industry_3_code]++;
					}
				}
				if (item.industry_2_code) {
					if (!obj[item.industry_2_code]) {
						obj[item.industry_2_code] = 1;
					} else {
						obj[item.industry_2_code]++;
					}
				}
				if (!obj[item.industry_1_code]) {
					obj[item.industry_1_code] = 1;
				} else {
					obj[item.industry_1_code]++;
				}
			});
			let new_arr = array.map((item) => {
				return { ...item, rowspan: 0, rowspan1: 0, rowspan2: 0 };
			});
			for (const key in obj) {
				let index = new_arr.findIndex((v) => v.industry_1_code == key);
				if (index != -1) {
					new_arr[index].rowspan = obj[key];
				}
				let index1 = new_arr.findIndex((v) => v.industry_2_code == key);
				if (index1 != -1) {
					new_arr[index1].rowspan1 = obj[key];
				}
				let index2 = new_arr.findIndex((v) => v.industry_3_code == key);
				if (index2 != -1) {
					new_arr[index2].rowspan2 = obj[key];
				}
			}
			return new_arr;
		},
		sortData(array) {
			// 自定义比较函数
			function compare(a, b) {
				if (a.industry_1 < b.industry_1) {
					return -1;
				} else if (a.industry_1 > b.industry_1) {
					return 1;
				} else {
					if (a.industry_2 < b.industry_2) {
						return -1;
					} else if (a.industry_2 > b.industry_2) {
						return 1;
					} else {
						if (a.industry_3 < b.industry_3) {
							return -1;
						} else if (a.industry_3 > b.industry_3) {
							return 1;
						} else {
							return 0;
						}
					}
				}
			}

			// 使用比较函数对数组进行排序
			const sortedArray = array.sort(compare);

			return sortedArray;
		},
		objectSpanMethod({ row, column, rowIndex, columnIndex }) {
			let { rowspan, rowspan1, rowspan2 } = row;
			if (columnIndex == 0) {
				if (row.colspan == 3) {
					return {
						rowspan: rowspan,
						colspan: 3
					};
				} else {
					return {
						rowspan: rowspan,
						colspan: 1
					};
				}
			} else if (columnIndex == 1) {
				if (row.colspan == 3) {
					return {
						rowspan: rowspan1,
						colspan: 0
					};
				} else if (row.colspan == 2) {
					return {
						rowspan: rowspan1,
						colspan: row.colspan || 1
					};
				} else {
					return {
						rowspan: rowspan1,
						colspan: 1
					};
				}
			} else if (columnIndex == 2) {
				if (row.colspan == 3) {
					return {
						rowspan: rowspan2,
						colspan: 0
					};
				} else if (row.colspan == 2) {
					return {
						rowspan: rowspan2,
						colspan: 0
					};
				} else {
					return {
						rowspan: rowspan2,
						colspan: 1
					};
				}
			} else {
				return {
					rowspan: 1,
					colspan: 1
				};
			}
			// if (row.colspan == 3) {
			// 	if (columnIndex == 0) {
			// 		return {
			// 			rowspan: row.columnspan || 1,
			// 			colspan: row.colspan || 1
			// 		};
			// 	} else if (columnIndex > 0 && columnIndex <= 2) {
			// 		return {
			// 			rowspan: 0,
			// 			colspan: 0
			// 		};
			// 	} else {
			// 		return {
			// 			rowspan: 1,
			// 			colspan: 1
			// 		};
			// 	}
			// } else if (row.colspan == 2) {
			// 	if (columnIndex == 1) {
			// 		return {
			// 			rowspan: row.columnspan || 1,
			// 			colspan: row.colspan || 1
			// 		};
			// 	} else if (columnIndex == 2) {
			// 		return {
			// 			rowspan: 0,
			// 			colspan: 0
			// 		};
			// 	} else {
			// 		return {
			// 			rowspan: 1,
			// 			colspan: 1
			// 		};
			// 	}
			// } else if (row.colspan == 1) {
			// 	let index = this.active_industry.findIndex((v) => v.value == row.industry_1_code);
			// 	return {
			// 		rowspan: 1,
			// 		colspan: 1
			// 	};
			// }
		},
		cellClassName({ row, column, rowIndex, columnIndex }) {
			return 'background:' + this.industry_list.find((v) => row.industry_1_code == v.value)?.color + '30';
		},
		exportExcel() {
			filter_json_to_excel(this.column, this.data);
		}
	}
};
</script>

<style></style>
