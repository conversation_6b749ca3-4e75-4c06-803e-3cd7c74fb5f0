<template>
	<div id="TMModelAnalysis">
		<analysis-card-title title="TM模型分析" image_id="TMModelAnalysis"></analysis-card-title>
		<div class="flex_start" v-loading="loading">
			<el-table
				:span-method="objectSpanMethod"
				:data="datas"
				class="mr-20"
				height="420px"
				border
				stripe
				ref="multipleTable"
				header-cell-class-name="table-header"
				style="flex: 1"
			>
				<el-table-column prop="hkf" align="gotoleft" label="大类"> </el-table-column>
				<el-table-column prop="name" align="gotoleft" label="名称"> </el-table-column>
				<el-table-column prop="value" label="均值" align="gotoleft">
					<template slot-scope="scope"
						><span>{{ scope.row.value | fix4 }}</span></template
					>
				</el-table-column>
				<el-table-column prop="description" align="gotoleft" label="说明"> </el-table-column>
				<template slot="empty">
					<el-empty image-size="160"></el-empty>
				</template>
			</el-table>
			<div style="width: 602px" v-if="showDescription" id="TMModelAnalysisDescription">
				<analysis-description title="TM模型分析" :description="description"></analysis-description>
			</div>
		</div>
	</div>
</template>

<script>
import analysisDescription from '@/components/components/components/analysisDescription/index.vue';
import { filter_json_to_excel } from '@/utils/exportExcel.js';

// TM模型分析
import { getTMStatistics } from '@/api/pages/Analysis.js';
// TM模型分析
export default {
	name: 'TMModelAnalysis',
	components: { analysisDescription },
	props: {
		showDescription: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			befg: '',
			aftg: '',
			datas: [],
			base1: 0,
			base2: 0,
			loading: true,
			info: {}
		};
	},
	computed: {
		description() {
			return `经典 TM 模型，统计显著性的置信度为 0.95 统计显著固然很好，统计不显著不见得就 不存在，只是稳定性稍差。是否呈现能力取决于是否真有能力和市场是否配合能力展现两个 方面。均值为年化数据。`;
		}
	},
	filters: {
		fix4(value) {
			if (value == null || value == 'null') {
				return '--';
			} else return parseInt(value * 10000) / 10000;
		}
	},
	methods: {
		// 获取TM模型分析数据
		async getTMStatistics() {
			this.loading = true;
			let data = await getTMStatistics({
				code: this.info.code,
				type: this.info.type,
				flag: this.info.flag,
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			let temp1 = '配置港股';
			let temp2 = '未配置港股';
			if (this.info.type == 'equityhk' || this.info.type == 'hkequity') {
				temp1 = '配置港股';
				temp2 = '配置港股';
			} else if (this.info.type == 'equitywithhk') {
				temp1 = '未配置港股';
				temp2 = '配置港股';
			} else {
				temp1 = '未配置港股';
				temp2 = '未配置港股';
			}
			let t = 0;
			this.loading = false;
			if (data?.mtycode == 200) {
				data = data.data?.[0];
			} else {
				return;
			}
			for (const key in data) {
				if (key.indexOf('Explain') == -1 && key != 'code') {
					if (key.indexOf('hk') == -1) {
						this.datas.push({
							hkf: temp1,
							name: key == 'c' ? '择时系数' : key,
							value: data[key],
							description: data[key + 'Explain']
						});
						this.base1 += 1;
					} else {
						this.datas.push({
							hkf: temp2,
							name: '港股' + (key.split('hk')?.[1] == 'c' ? '择时系数' : key.split('hk')?.[1]),
							value: data[key],
							description: data[key + 'Explain']
						});
						this.base2 += 1;
					}
				}
			}
		},
		async getData(info) {
			this.info = info;
			this.befg = '';
			this.aftg = '';
			this.datas = [];
			this.base1 = 0;
			this.base2 = 0;
			await this.getTMStatistics();
		},
		objectSpanMethod(row, column, rowIndex, columnIndex) {
			if (row.columnIndex === 0) {
				if (this.info.type == 'equityhk' || this.info.type == 'hkequity') {
					if (row.row.hkf == '配置港股' && row.rowIndex == 0) {
						return {
							rowspan: this.base1,
							colspan: 1
						};
					} else {
						return {
							rowspan: 0,
							colspan: 0
						};
					}
				} else if (this.info.type == 'equitywithhk') {
					if (row.row.hkf == '配置港股' && row.rowIndex == this.base2) {
						return {
							rowspan: this.base2,
							colspan: 1
						};
					} else if (row.row.hkf == '未配置港股' && row.rowIndex == 0) {
						return {
							rowspan: this.base1,
							colspan: 1
						};
					} else {
						return {
							rowspan: 0,
							colspan: 0
						};
					}
				} else {
					if (row.row.hkf == '未配置港股' && row.rowIndex == 0) {
						return {
							rowspan: this.base1,
							colspan: 1
						};
					} else {
						return {
							rowspan: 0,
							colspan: 0
						};
					}
				}
			}
		},
		exportExcel() {
			let list = [
				{
					label: '',
					fill: 'header',
					value: 'hkf'
				},
				{
					label: '',
					fill: 'header',
					value: 'name'
				},
				{
					label: '均值',
					value: 'value',
					format: 'fix3'
				},
				{
					label: '说明',
					value: 'description'
				}
			];
			filter_json_to_excel(list, this.datas, 'TM模型分析');
		},
		async createPrintWord(info) {
			await this.getData(info);
			let list = [
				{
					label: '',
					fill: 'header',
					value: 'hkf'
				},
				{
					label: '',
					fill: 'header',
					value: 'name'
				},
				{
					label: '均值',
					value: 'value',
					format: 'fix3'
				},
				{
					label: '说明',
					value: 'description'
				}
			];
			let id = 'TMModelAnalysisDescription';
			let height = document.getElementById(id).clientHeight || 196;
			let width = document.getElementById(id).clientWidth || 1642;
			let canvas = await this.html2canvas(document.getElementById(id), {
				scale: 3
			});
			if (this.datas.length) {
				return [
					...this.$exportWord.exportTitle('TM模型分析'),
					...this.$exportWord.exportTable(list, this.datas),
					...this.$exportWord.exportChart(canvas.toDataURL('image/jpg'), {
						width,
						height
					})
				];
			} else {
				return [];
			}
		}
	}
};
</script>

<style></style>
