<script>
import { deleteRule } from "../../../../api/pages/tkdesign/monitorWarning";

export default {
  props: ['details'],
  data () {
    return {
      tableList: [
        {
          type: 1,
          tableData: [
            {
              label: '触发条件',
              prop: 'compareType',
              width: '200',
            }, {
              label: '日历日跨度',
              prop: 'valueType',
              width: '200',
            }, {
              label: '收益率阈值',
              prop: 'threshold',
              width: '200',
            }, {
              label: '触发项说明',
              prop: 'description',
            }]
        },
        {
          type: 2,
          tableData: [
            {
              label: '触发条件',
              prop: 'compareType',
              width: '150',
            }, {
              label: '日历日跨度',
              prop: 'valueType',
              width: '150',
            }, {
              label: '比较基准',
              prop: 'indexName',
              width: '200',
            }, {
              label: '超额收益率阈值',
              prop: 'threshold',
              width: '200',
            }, {
              label: '触发项说明',
              prop: 'description',
            }]
        },
        {
          type: 3,
          tableData: [
            {
              label: '触发条件',
              prop: 'compareType',
              width: '150',
            }, {
              label: '回撤比阈值',
              prop: 'threshold',
              width: '150',
            }, {
              label: '日历日跨度',
              prop: 'valueType',
              width: '150',
            }, {
              label: '触发项说明',
              prop: 'description',
            }]
        },
        {
          type: 4,
          tableData: [
            {
              label: '监控目标',
              prop: 'srcType',
              width: '150',
            }, {
              label: '触发条件',
              prop: 'compareType',
              width: '150',
            }, {
              label: '占比阈值',
              prop: 'threshold',
              width: '150',
            },
            // {
            //   label: '参照目标',
            //   prop: 'valueType',
            //   width: '150',
            // }, 
            {
              label: '触发项说明',
              prop: 'description',
            }]
        },
        {
          type: 5,
          tableData: [
            {
              label: '类型',
              prop: 'srcType',
              width: '150',
            }, {
              label: '触发条件',
              prop: 'compareType',
              width: '150',
            }, {
              label: '占比阈值',
              prop: 'threshold',
              width: '150',
            }, {
              label: '参照目标',
              prop: 'valueType',
              width: '150',
            }, {
              label: '触发项说明',
              prop: 'description',
            },]
        },
        {
          type: 6,
          tableData: [
            {
              label: '资产类别',
              prop: 'srcType',
              width: '150',
            }, {
              label: '触发条件',
              prop: 'compareType',
              width: '150',
            }, {
              label: '占比阈值',
              prop: 'threshold',
              width: '150',
            },
            // {
            //   label: '参照目标',
            //   prop: 'valueType',
            //   width: '150',
            // }, 
            {
              label: '触发项说明',
              prop: 'description',
            }]
        },
        {
          type: 7,
          tableData: [
            // {
            //   label: '交易市场',
            //   prop: 'srcType',
            //   width: '150',
            // }, 
            {
              label: '触发条件',
              prop: 'compareType',
              width: '150',
            }, {
              label: '集中度阈值',
              prop: 'threshold',
              width: '150',
            }, {
              label: '集中度',
              prop: 'valueTypeC',
              width: '150',
            },
            //  {
            //   label: '参照目标',
            //   prop: 'valueType2',
            //   width: '150',
            // }, 
            {
              label: '触发项说明',
              prop: 'description',
            }]
        },
        {
          type: 8,
          tableData: [
            // {
            //   label: '交易市场',
            //   prop: 'srcType',
            //   width: '150',
            // }, 
            {
              label: '触发条件',
              prop: 'compareType',
              width: '150',
            }, {
              label: '集中度阈值',
              prop: 'threshold',
              width: '150',
            },
            {
              label: '行业标准',
              prop: 'valueType',
              width: '150',
            },
            {
              label: '集中度',
              prop: 'valueTypeC',
              width: '150',
            },
            // {
            //   label: '参照目标',
            //   prop: 'valueType2',
            //   width: '150',
            // },
            {
              label: '触发项说明',
              prop: 'description',
            }]
        },
        {
          type: 9,
          tableData: [
            {
              label: '触发条件',
              prop: 'compareType',
              width: '150',
            }, {
              label: '持仓比例阈值',
              prop: 'threshold',
              width: '150',
            }, {
              label: '触发项说明',
              prop: 'description',
            }]
        },
        {
          type: 10,
          tableData: [
            {
              label: '触发条件',
              prop: 'compareType',
              width: '150',
            }, {
              label: '持仓比例阈值',
              prop: 'threshold',
              width: '150',
            }, {
              label: '触发项说明',
              prop: 'description',
            }]
        },
        {
          type: 11,
          tableData: [
            {
              label: '监控目标',
              prop: 'srcType',
              width: '150',
            }, {
              label: '触发条件',
              prop: 'compareType',
              width: '150',
            }, {
              label: '规模阈值',
              prop: 'threshold',
              width: '150',
            }, {
              label: '日历日跨度',
              prop: 'valueType',
              width: '150',
            }, {
              label: '触发项说明',
              prop: 'description',
            }]
        },
        {
          type: 12,
          tableData: [
            {
              label: '监控目标',
              prop: 'srcType',
              width: '150',
            }, {
              label: '触发条件',
              prop: 'compareType',
              width: '150',
            }, {
              label: '规模阈值',
              prop: 'threshold',
              width: '150',
            }, {
              label: '日历日跨度',
              prop: 'valueType',
              width: '150',
            }, {
              label: '触发项说明',
              prop: 'description',
            }]
        },
        {
          type: 13,
          tableData: [
            {
              label: '监控目标',
              prop: 'srcType',
              width: '150',
            }, {
              label: '触发条件',
              prop: 'compareType',
              width: '150',
            }, {
              label: '规模阈值',
              prop: 'threshold',
              width: '150',
            }, {
              label: '日历日跨度',
              prop: 'valueType',
              width: '150',
            }, {
              label: '触发项说明',
              prop: 'description',
            }]
        }
      ],
      formData: {},
      warnTypes: [
        { label: '组合收益率', value: 1 },
        { label: '组合超额收益率', value: 2 },
        { label: '组合回撤', value: 3 },
        { label: '组合单个资产持仓占比', value: 4 },//单证券市值占比
        { label: '组合单个资产持仓份额占市场份额比', value: 5 },//单股票持仓占股本比
        { label: '组合大类资产持仓占比', value: 6 },//证券资产占比
        { label: '个股集中度', value: 7 },
        { label: '行业集中度', value: 8 },
        { label: '黑名单占比', value: 9 },
        { label: '白名单占比', value: 10 },
        { label: '组合大类资产净买入金额', value: 11 },//净买入
        { label: '组合单个资产净买入金额', value: 12 },//新增
        { label: '组合单个资产买入金额', value: 13 }//新增
      ],
      targetOptions: [
        { value: 1, label: "股票" },
        { value: 2, label: "债券" },
        { value: 3, label: "基金" },
        { value: 4, label: "期货" },
      ],
      secuAreaOptions: [
        { label: '个股', value: 1 },
        { label: "基金", value: 2 }
      ],
      assetTypeOptions: [
        { label: "现金", value: 1 },
        { label: "股票市值", value: 2 },
        { label: "债券市值", value: 3 },
        { label: "期货市值", value: 4 },
        { label: "逆回购市值", value: 5 },
        { label: "正回购市值", value: 6 },
      ],
      marketTypeOptions: [
        { label: "全部", value: 1 },
        { label: "A股", value: 2 },
        { label: "港股", value: 3 },
        { label: "其他", value: 4 },
      ],
      compareTypeOptions: ['>=', '>', '<', '<='],
    }
  },
  computed () {

  },
  methods: {
    async querySearchAsync (queryString, cb) {
      let result = []
      //todo 记得更换接口
      result = await this.$axios.get(this.$baseUrl + '/Analysis/Search/', {
        params: {
          flag: 6,
          message: queryString
        },
        headers: { 'authorization': 'Bearer ' + this.$store.state.token }
      })
      let array = []
      for (let i = 0; i < 10; i++) {
        if (result.data.data[i]) {
          array.push({
            value: result.data.data[i].code,
            label: `${result.data.data[i].name}-${result.data.data[i].code}`
          })
        }
      }
      cb(array)
    },
    getTableData (type) {
      return this.tableList.filter(v => v.type === type)[0].tableData
    },
    warnTypeValue (type) {
      return this.warnTypes.filter(v => v.value === type)[0].label
    },
    targetValue (target) {
      return this.targetOptions.filter(v => v.value === target)[0].label
    },
    secuAreaValue (secuArea) {
      return this.secuAreaOptions.filter(v => v.value === secuArea)[0].label
    },
    assetTypeValue (assetType) {
      return this.assetTypeOptions.filter(v => v.value === assetType)[0].label
    },
    marketTypeValue (marketType) {
      return this.marketTypeOptions.filter(v => v.value === marketType)[0].label
    },
    addRow () {
      const row = {
        warnType: 1,
      }
      this.formData.push(row)
    },
    getDaysFromNewYear (period) {
      const today = new Date();
      let days = 365
      let startDate;
      switch (period) {
        case 'a':
          // startDate = new Date(today.getFullYear(), 0, 1);
          days = -1;
          break;
        case 'b':
          days = 183
          break;
        case 'c':
          days = 365
          break;
        case 'd':
          days = 365 * 2

          break;
        case 'e':
          days = 365 * 3

          break;
        default:
          throw new Error('Invalid period');
      }
      return days;

    },
    buildDescription (detail, row) {
      const warnName = this.getWarnTypeName(detail.warnType);
      let compareName;
      if (row.compareType === '>=') compareName = '大于等于';
      if (row.compareType === '>') compareName = '超过';
      if (row.compareType === '<=') compareName = '小于等于';
      if (row.compareType === '<') compareName = '低于';
      if (!compareName) compareName = '?';

      let text;
      if (detail.warnType === 1 || detail.warnType === 2 || detail.warnType === 3) {
        text = `告警，该${this.warnTypeValue(detail.warnType)}在${row.valueType ? row.valueType == -1 ? '年初至今' : row.valueType + '日历日' : '?日历日'}${compareName}${row.threshold ? row.threshold : '?'}%`
      } else if (detail.warnType === 9 || detail.warnType === 10) {
        text = `告警，该产品持有${compareName}${row.threshold ? row.threshold : '?'}%${detail.warnType === 9 ? '黑' : '白'}名单个股！`
      } else if (detail.warnType === 4) {
        text = `告警，单只${row.srcType ? this.targetValue(row.srcType) : '?'}持仓占比${compareName}${row.valueType ? row.valueType == 1 ? '产品净资产' : row.valueType == 2 ? '产品总资产' : row.valueType == 3 ? '目标市值' : row.valueType == 4 ? '基金份额' : row.valueType == 5 ? '基金份额（合计）' : '' : ''}${row.threshold ? row.threshold :
          '?'}%`
      } else if (detail.warnType === 5) {
        text = `告警，单只${row.srcType ? this.secuAreaValue(row.srcType) : '?'}持仓占比${compareName}${row.valueType ? row.valueType == 1 ? '总股本' : row.valueType == 2 ? '流通股本' : row.valueType == 3 ? '基金份额' : row.valueType == 4 ? '基金份额（合计）' : '' : '?'}的${row.threshold ? row.threshold :
          '?'}%`
      } else if (detail.warnType === 6) {
        text = `告警，${row.srcType ? this.assetTypeValue(row.srcType) : '?'}占比${compareName}${row.valueType ? row.valueType == 1 ? '产品净资产' : row.valueType == 2 ? '产品总资产' : row.valueType == 3 ? '目标市值' : row.valueType == 4 ? '基金份额' : row.valueType == 5 ? '基金份额（合计）' : '' : '组合'}的${row.threshold ? row.threshold :
          '?'}% `
      } else if (detail.warnType === 7 || detail.warnType === 8) {
        text = `告警，该产品持有的${detail.warnType === 7 ? '个股' : '行业'}${row.valueType2 ? row.valueType2 == 1 ? '产品净资产' : row.valueType2 == 2 ? '产品总资产' : row.valueType2 == 3 ? '股票市值' : '' : ''}前${row.valueTypeC ? row.valueTypeC : '?'}大集中度${row.warnType === 8 ? row.valueType ?
          row.valueType == 2 ? '在TK内部一级行业' : '在申万一级行业' : '?' : ''
          }${compareName}${row.threshold ? row.threshold : '?'}% `
      } else if (detail.warnType == 11 || detail.warnType == 12 || detail.warnType == 13) {

        let targetName = detail.warnType == 11 ? '组合大类资产净买入' : detail.warnType == 12 ? '组合单个资产净买入金额' : detail.warnType == 13 ? '组合单个资产买入金额' : '';
        // if (row.srcType) {

        //   if (row.srcType == 1) return '股票';
        //   if (row.srcType == 2) return '基金';
        //   if (row.srcType == 3) return '债券';
        //   if (row.srcType == 4) return '保险资管';
        //   if (row.srcType == 5) return '单一资管计划';


        // }
        // console.log(row);
        text = `告警，${row.valueType ? row.valueType == -1 ? '年初至今' : row.valueType + '日历日内' : '?日历日'}${row.srcType == 1 ? '股票' : row.srcType == 2 ? '基金' : row.srcType == 3 ? '债券' : row.srcType == 4 ? '保险资管' : row.srcType == 5 ? '单一资管计划' : ''}在${targetName}中规模${compareName}阈值${row.threshold || '?'}亿`;
      }


      // 告警，{监控名称}中{监控对象}触发{风险监控类型}：{监控目标}规模阈值{值}
      detail.description = text;
      return text;
    },
    getWarnTypeName (value) {
      for (let index = 0; index < this.warnTypes.length; index++) {
        const element = this.warnTypes[index];
        if (element.value == value) {
          return element.label;
        }
      }
    },
    /**
     * 删除一条规则设置
     */
    deleteSet (detail) {
      const index = this.formData.indexOf(detail)
      this.$confirm('确定删除么?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.formData.splice(index, 1)
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    handleSelect (item, row) {
      console.log('item', item, row);
      row.indexCode = item.value;
      row.indexName = item.label;
    },
    handleWarnType11Change (row, array) {
      // if (row && array && array.length > 0) {
      //   const str = array.join(',');
      //   row.srcTypeMulti = str;
      // }
    },
    warnTypeChange (row, value) {
      const array = Object.keys(row);
      array.forEach(element => {
        row[element] = undefined;
      });
      row.warnType = value;
      console.log('item123', row, value);
    },
  },

  watch: {
    details: {
      handler (newVal) {
        // console.log(newVal);
        this.formData = newVal
      },
      deep: true
    },
    formData: {
      handler (newVal) {
        console.log('formData', newVal)
        this.$emit('updateData', newVal)
      },
      deep: true
    }
  },
  mounted () {
    this.formData = this.details?.map(item => {
      if (item.warnType == 7 || item.warnType == 8) {
        return {
          ...item,
          valueTypeC: item?.srcType || 10
        }
      } else { return { ...item } }
    })
  }
}
</script>

<template>
  <div class="body overflow-hidden">
    <div v-for="(detail,indexAll) in formData"
         :key="indexAll">
      <div class="table_header">
        <div>
          风险监控类型
          <el-select v-model="detail.warnType"
                     class="table_header_select"
                     @change="value => warnTypeChange(detail, value)">
            <el-option v-for="(item,indexItem) in warnTypes"
                       :label="item.label"
                       :value="item.value"
                       :key="indexItem">
            </el-option>
          </el-select>
        </div>
        <div class="table_header_delete">
          <i class="el-icon-delete"
             @click="deleteSet(detail)"></i>
        </div>
      </div>
      <el-table :data="[detail]"
                border
                class="tableClass">
        <el-table-column v-for="(item, indexs) in getTableData(detail.warnType)"
                         :key="indexs"
                         :label="item.label"
                         :prop="item.prop"
                         :width="item.width"
                         min-width="120"
                         align="gotoleft">
          <template v-if="item.label === '日历日跨度'"
                    slot="header">
            <!-- 自定义表头内容 -->
            <div class="custom-header">
              <!-- 你可以在这里添加任何自定义的HTML或组件 -->
              {{ item.label }}
              <el-dropdown @command="(e)=>{
                  detail.valueType = getDaysFromNewYear(e)
                }">
                <span style="margin-right:16px"
                      class="el-dropdown-link">
                  <i class="el-icon-arrow-down"></i>
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="a">年初至今</el-dropdown-item>
                  <el-dropdown-item command="b">近半年</el-dropdown-item>
                  <el-dropdown-item command="c">近一年</el-dropdown-item>
                  <el-dropdown-item command="d">近两年</el-dropdown-item>
                  <el-dropdown-item command="e">近三年</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </template>
          <template slot-scope="{row}">
            <div v-show="item.prop === 'compareType'">
              <el-select v-model="row.compareType"
                         class="table_select">
                <el-option v-for="(item, index) in compareTypeOptions"
                           :key="index"
                           :label="item"
                           :value="item" />
              </el-select>
            </div>
            <div v-show="detail.warnType === 1 || detail.warnType === 2|| detail.warnType === 12|| detail.warnType === 11|| detail.warnType === 13">
              <div v-show="item.prop === 'valueType'">

                <el-input-number v-model="row.valueType"
                                 class="table_input_number"
                                 controls-position="right" />

              </div>
            </div>
            <!-- @select="((item)=>{handleSelect(item, row)})" -->
            <div v-show="detail.warnType === 2">
              <div v-show="item.prop === 'indexName'">
                <el-autocomplete v-model="row.indexName"
                                 :fetch-suggestions="querySearchAsync"
                                 class="search-stock"
                                 value-key="label"
                                 clearable
                                 @select="((item)=>{handleSelect(item, row)})"
                                 placeholder="搜索代码或名称">
                  <i slot="prefix"
                     class="el-input__icon"></i>
                </el-autocomplete>
              </div>
            </div>
            <div v-show="item.prop === 'valueType'">
              <div v-show="detail.warnType === 3">
                <el-input-number v-model="row.valueType"
                                 class="table_input_number"
                                 controls-position="right" />
              </div>
              <div v-show="detail.warnType === 4">
                <el-select v-model="row.valueType">
                  <el-option label="产品净资产"
                             :value=1></el-option>
                  <el-option label="产品总资产"
                             :value=2></el-option>
                  <el-option label="目标市值"
                             :value=3></el-option>
                  <el-option label="基金份额"
                             :value=4></el-option>
                  <el-option label="基金份额（合计）"
                             :value=5></el-option>
                </el-select>
              </div>
              <div v-show="detail.warnType === 6">
                <el-select v-model="row.valueType">
                  <el-option label="产品净资产"
                             :value=1></el-option>
                  <el-option label="产品总资产"
                             :value=2></el-option>
                </el-select>
              </div>
              <div v-show="detail.warnType === 5">
                <el-select v-model="row.valueType">
                  <el-option label="总股本"
                             :value="1"></el-option>
                  <el-option label="流通股本"
                             :value="2"></el-option>
                  <el-option label="基金份额"
                             :value="3"></el-option>
                  <el-option label="基金份额（合计）"
                             :value="4"></el-option>
                </el-select>
              </div>

              <div v-show="detail.warnType === 8">

                <el-select v-model="row.valueType">
                  <el-option label="TK内部一级行业"
                             :value="2"></el-option>
                  <el-option label="申万一级行业"
                             :value="1"></el-option>
                </el-select>
              </div>
            </div>
            <div v-show="item.prop === 'valueTypeC'">
              <div v-show="detail.warnType === 7||detail.warnType === 8">

                <el-select v-model="row.valueTypeC">
                  <el-option v-for="(item,index) in [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20]"
                             :key="index"
                             :label="'前' + item + '大'"
                             :value="item"></el-option>
                </el-select>
              </div>
            </div>
            <div v-show="item.prop === 'valueType2'">
              <div v-show="detail.warnType === 7 || detail.warnType === 8">
                <el-select v-model="row.valueType2">
                  <el-option label="产品净资产"
                             :value="1"></el-option>
                  <el-option label="产品总资产"
                             :value="2"></el-option>
                  <el-option label="股票市值"
                             :value="3"></el-option>
                </el-select>
              </div>
            </div>
            <div v-show="item.prop === 'threshold'">
              <div v-show="detail.warnType === 11||detail.warnType === 12||detail.warnType === 13">
                <el-input v-model="row.threshold"
                          class="table_input" />
                亿
              </div>
              <div v-show="detail.warnType !== 11&&detail.warnType !== 12&&detail.warnType !== 13">
                <el-input v-model="row.threshold"
                          class="table_input" />
                %
              </div>
              <!-- <div v-show="detail.warnType == 11&&detail.warnType == 12&&detail.warnType == 13">
                <el-input v-model="row.threshold"
                          class="table_input" />
                %
              </div> -->
            </div>
            <div v-show="item.prop === 'srcType'">
              <div v-show="detail.warnType === 4">
                <el-select v-model="row.srcType">
                  <el-option v-for="(item, index) in targetOptions"
                             :key=index
                             :label="item.label"
                             :value="item.value" />
                </el-select>
              </div>
              <div v-show="detail.warnType === 5">
                <el-select v-model="row.srcType">
                  <el-option v-for="(item, index) in secuAreaOptions"
                             :key=index
                             :label="item.label"
                             :value="item.value" />
                </el-select>
              </div>
              <div v-show="detail.warnType === 6">
                <el-select v-model="row.srcType">
                  <el-option v-for="(item, index) in assetTypeOptions"
                             :key=index
                             :label="item.label"
                             :value="item.value" />
                </el-select>
              </div>
              <div v-show="detail.warnType === 7 || detail.warnType === 8">
                <el-select v-model="row.srcType">
                  <el-option v-for="(item, index) in marketTypeOptions"
                             :key=index
                             :label="item.label"
                             :value="item.value" />
                </el-select>
              </div>
              <div v-show="detail.warnType === 11||detail.warnType === 12||detail.warnType === 13">
                <el-select v-model="row.srcType"
                           @change="value => handleWarnType11Change(row, value)">
                  <el-option label="股票"
                             :value="1"></el-option>
                  <el-option label="基金"
                             :value="2"></el-option>
                  <el-option label="债券"
                             :value="3"></el-option>
                  <el-option label="保险资管"
                             :value="4"></el-option>
                  <el-option label="单一资产管理计划"
                             :value="5"></el-option>
                </el-select>
              </div>
            </div>

            <div v-show="item.prop === 'description'">
              {{ buildDescription(detail, row) }}
            </div>

          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-button class="add_button"
               icon="el-icon-plus"
               @click="addRow">新增</el-button>
  </div>
</template>

<style lang="scss" scoped>
@import '../../tkdesign';

.body {
	overflow: auto;
	scrollbar-width: none !important;

	position: relative;
	height: calc(100vh - 506px);

	padding-bottom: 112px;
	font-size: 14px;

	.table_header {
		display: flex;
		justify-content: space-between;
		padding: 16px;
		background-color: #fafafa;
		border: 1px solid #ebeef5;
		border-bottom: 0;

		.table_header_delete {
			cursor: pointer;
		}

		.table_header_select {
			margin-left: 16px;
		}
	}

	.tableClass {
		margin-bottom: 20px;

		.table_select {
			width: 100px;
		}

		.table_input {
			width: 70px;
		}

		.table_input_number {
			width: 100px;
		}
	}

	.add_button {
		width: 100%;
		border-radius: 4px;
		border: 1px dashed #4096ff;
		color: #4096ff;
	}
}

.body::-webkit-scrollbar {
	width: 4px;
}
</style>
