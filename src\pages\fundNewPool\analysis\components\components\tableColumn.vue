<template>
  <el-popover popper-class="custom-table-checkbox"
              trigger="hover">
    <div class="flex_between pb-12"
         style="border-bottom: 1px solid #e9e9e9; width: 240px">
      <div>
        <el-checkbox :indeterminate="isIndeterminate"
                     v-model="checkAll"
                     @change="handleCheckAllChange">列展示</el-checkbox>
      </div>
      <div>
        <!-- <el-link class="mr-12"
                 @click="submitColumnChange">确定</el-link>
        <el-link>重置</el-link> -->
      </div>
    </div>
    <drag-list style="height: 416px; overflow: auto"
               :list="sort_column_list"
               @resolveFather="getSortList"></drag-list>
    <template #reference>
      <span class="el-dropdown-link"
            style=""><i class="el-icon-setting"></i> </span>
    </template>
  </el-popover>
</template>

<script>
// 拖拽组件
import dragList from '@/components/components/components/dragList/index.vue';
export default {
  components: { dragList },
  data () {
    return {
      isIndeterminate: true,
      checkAll: false,
      sort_column_list: []
    };
  },
  props: {
    ismanager: {
      type: Boolean,
    }
  },
  methods: {
    // 获取列表数据
    getData (active_list) {
      this.sort_column_list = active_list.map((item) => {
        return item;
      });
    },
    // 获取排序后列表
    getSortList () {
      // console.log(this.sort_column_list);
      this.submitColumnChange();
    },
    // 确定列展示修改
    submitColumnChange () {
      this.$emit('resolveFather', this.sort_column_list);
    },
    // 监听列选择全选
    handleCheckAllChange (val) {
      this.sort_column_list = val
        ? this.sort_column_list.map((item) => {
          return { ...item, show: true };
        })
        : this.sort_column_list.map((item) => {
          return { ...item, show: false };
        });
      this.isIndeterminate = false;
    }
  }
};
</script>

<style></style>
