// import tkRequest from "@/api/request";
import { tkRequest as request } from '@/api/request';
import { combinationRequest as requestCon } from '@/api/request';
// 分析兑现管理列表
export function modelCalculate(data) {
  return requestCon({
    url: "/modelCalculate",
    method: "post",
    data
  });
}
// 回测曲线
export function holdingRateTrend(params) {
  return requestCon({
    url: "/holdingRateTrend",
    method: "get",
    params
  });
}
// 回测曲线下表格
export function overallRiskReturn(params) {
  return requestCon({
    url: "/overallRiskReturn",
    method: "get",
    params
  });
}
// 回测资产配置权重
export function assetAllocationWeight(data) {
  return requestCon({
    url: "/assetAllocationWeight",
    method: "post",
    data
  });
}
// 回测方案表现近期
// 年度表现用这个 getCustomMeasure "@/api/pages/tools/pool.js";
export function planIndexPerformance(data) {
  return requestCon({
    url: "/productPerformance",
    method: "post",
    data
  });
}

// 收益贡献分析
export function returnContributeAnalysis(params) {
  return requestCon({
    url: "/returnContributeAnalysis",
    method: "get",
    params
  });
}
// 保存方案/组合
export function CreatePloyOrCombination(data) {
  return requestCon({
    url: "/createPloyOrCombination",
    method: "post",
    data
  });
}
// 已有方案获取模型数据
export function retestDetails(params) {
  return requestCon({
    url: "/retestDetails",
    method: "get",
    params
  });
}
// 生成组合回测
export function calPortfolioBack(data) {
  return requestCon({
    url: "/calPortfolioBack",
    method: "post",
    data
  });
}