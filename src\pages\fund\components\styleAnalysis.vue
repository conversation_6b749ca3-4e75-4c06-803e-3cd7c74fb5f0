<!-- 风格分析 -->
<template>
	<div>
		<div class="flex_card">
			<div v-for="item in templateList" :key="item.value" v-show="item.isshow" :class="item.type">
				<component :is="item.is" :ref="item.value" @resolveFather="item.methods" v-loading="loading"></component>
			</div>
		</div>
	</div>
</template>

<script>
// 基金资产配置分析
import fundAssetAllocationAnalysis from '@/components/components/components/fundAssetAllocationAnalysis/index.vue';
// 全持仓风格分析
import positionStyle from '@/components/components/fundComponents/positionStyle/index.vue';
// 当前权益持仓风格
import equityStyle from '@/components/components/components/equityStyle/index.vue';
// 权益分年度持仓风格
import styleLabel from '@/components/components/fundComponents/styleLabel/index.vue';
// 整体财务指标
import financialIndex from '@/components/components/components/financialIndex/index.vue';
// 报告期换手率
import reportTurnover from '@/components/components/components/reportTurnover/index.vue';
// 持仓集中度
import holdStockConcentration from '@/components/components/components/holdStockConcentration/index.vue';

// 债券资产配置
import positionClass from '@/components/components/components/positionClass/index.vue';
// FOF最新各类型基金配置情况
import newFundAllocation from '@/components/components/components/newFundAllocation/index.vue';
// FOF基金持仓分析
import fundPositionAnalysis from '@/components/components/components/fundPositionAnalysis/index.vue';
// 短期流动性管理
import mobilityManage from '@/components/components/components/mobilityManage/index.vue';
export default {
	components: {
		fundAssetAllocationAnalysis,
		equityStyle,
		financialIndex,
		reportTurnover,
		holdStockConcentration,
		styleLabel,
		positionStyle,
		positionClass,
		newFundAllocation,
		fundPositionAnalysis,
		mobilityManage
	},
	data() {
		return {
			name: '资产配置与风格',
			info: {},
			templateList: [],
			requestOver: [],
			requestAll: 0,
			loading: true
		};
	},
	props: {
		showEditor: {
			type: Boolean,
			default: false
		}
	},
	methods: {
		// 接收/返回组件列表
		getTemplateList(list) {
			if (list) {
				this.templateList = [...list];
			} else {
				return this.templateList;
			}
		},
		// 获取父组件数据
		getData(data) {
			this.info = data;
			this.loading = true;
			this.requestOver = [];
			this.formatTemplatList();
		},
		// 获取打印数据
		async createPrintWord(info) {
			this.info = info;
			let printData = [];
			this.templateList.map((item) => {
				if (item.isshow) {
					if (this.$refs[item.value]?.[0].createPrintWord) {
						let list = this.$refs[item.value]?.[0].createPrintWord(this.info);
						printData.push(list);
					}
				}
			});
			let data = await Promise.all(printData);
			data.unshift(this.$exportWord.exportFirstTitle(this.name));
			return data;
		},
		// 格式化模板列表
		formatTemplatList() {
			this.$nextTick(() => {
				this.templateList.map((item) => {
					if (item.typelist.indexOf(this.info.type) !== -1) {
						this.$refs[item.value]?.[0]?.getData(this.info);
						this.loading = false;
					}
				});
			});
		}
	}
};
</script>
<style scoped>
.flex_card .small_template {
	height: 324px;
}
</style>
