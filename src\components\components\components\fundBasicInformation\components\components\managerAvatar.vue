<template>
	<div class="basic_avatar flex_between">
		<div class="flex_start">
			<div class="manager_avatar mr-12">
				<img id="imc" :src="base64Image" />
			</div>
			<div class="manager_name">
				<div>{{ info.name }}</div>
				<div style="font-weight: 400; font-size: 16px">{{ info.company }}</div>
			</div>
		</div>
		<div class="flex_start">
			<!-- style="color: #4576e9" -->
			<el-button
				v-for="v in info.type_list"
				:key="v.value"
				type="primary"
				:plain="v.value == info.type ? false : true"
				@click="changeType(v.value)"
				>{{ v.label }}</el-button
			>
		</div>
	</div>
</template>

<script>
import { getBasicInfo } from '@/api/pages/Analysis.js';
export default {
	data() {
		return {
			info: {
				name: '',
				company: '',
				type: '',
				type_list: []
			},
			base64Image: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
		};
	},
	methods: {
		getData(info) {
			this.info = info;
			this.getBasicInfo();
		},
		async getBasicInfo() {
			let data = await getBasicInfo({ code: this.info.code, type: this.info.type, flag: this.info.flag });
			if (data.mtycode == 200) {
				this.base64Image = data.data?.url;
			}
		},
		// 切换基金经理分析类型
		changeType(type) {
			if (type != this.info.type) {
				this.$event.$emit('changeManagerAnalysisType', type);
			}
		},
		createPrintWord() {
			return [...this.$exportWord.exportManagerInfo(this.info)];
		}
	}
};
</script>
<style lang="scss" scoped>
.basic_avatar {
	border-radius: 4px;
	background: linear-gradient(266deg, #ffb75a 14.58%, #4096ff 100%);
	box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.15);
	padding: 16px;
	.manager_avatar {
		width: 64px;
		height: 64px;
		/* background:grey; */
		img {
			width: 64px;
			height: 64px;
			background: #ffffff;
			border-radius: 50%;
		}
	}
	.manager_name {
		color: #fff;
		font-family: PingFang SC;
		font-size: 20px;
		font-style: normal;
		font-weight: 500;
	}
}
</style>
