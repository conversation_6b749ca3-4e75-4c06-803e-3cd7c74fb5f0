<template>
    <div class="charts_fill_class" v-loading="loading">
        <el-empty image-size="160" v-if="showEmpty"></el-empty>
			<v-chart
				v-else
            ref="companySizeChange"
            :options="option"
            element-loading-text="暂无数据"
            element-loading-spinner="el-icon-document-delete"
            element-loading-background="rgba(239, 239, 239, 0.5)"
            class="charts_one_class"
            autoresize
        ></v-chart>
    </div>
</template>

<script>
import VChart from 'vue-echarts';
import { concentrationInfo,turnoverInfo} from '@/api/pages/tkAnalysis/portfolio.js';
import { lineChartOption } from '@/utils/chartStyle.js';
export default {
components: { VChart },
props:{
    name:{
        type: String,
        default:'turnoverInfo'
    }
},
data() {
    return {
        option: {},
        loading: true,
			showEmpty: true,
    };
},
methods: {
    getChartData(data){
        let dateList = [],
            data1 = [],
            data2 = [],
            data3 = [];
            this.name === 'turnoverInfo' && data.forEach(element => {
                dateList.push(element.year);
                data1.push(element.turnover*100);
                // data2.push(element.turnoverMiddle);
                // data3.push(element.percentRank);
            });
            if(this.name !== 'turnoverInfo'){
                dateList = data.concentrationData.yearqtr;
                data1 = data.concentrationData.top10Concentration.map( item => item*100);
               
                // data2 = data.middleConcentrationRank.top10Concentration;
                // data3 = data.concentrationData.top10ConcentrationRank;
            }
        return {dateList,data1,data2,data3}
    },
   async getData(param) {
        this.showEmpty = true;
        this.loading = true;
        let res = this.name === 'turnoverInfo' ? await turnoverInfo(param)   : await concentrationInfo(param);
        this.loading = false;
        if(res.mtycode != 200 && !res.data){
            return;
        }
        if(this.name === 'turnoverInfo' && res.data.length >0){
            this.showEmpty = false;
        }
        if(this.name !== 'turnoverInfo' && res.data?.concentrationData?.top10Concentration?.length >0){
            this.showEmpty = false;
        }
        const {dateList,data1,data2,data3} = this.getChartData(res.data);
        this.$emit('tableData',{dateList,data1,data2,data3});
        this.option = lineChartOption({
            tooltip: {
					backgroundColor: '#ffffff',
					formatter: function (obj) {
                        //数据排序
                        let list = obj;
						list.sort((a,b)=>{
							if(a.value-b.value < 0){
								return 1;
							}else{
								return -1;
							}
						})
						var value = `<div style="font-size:14px;">` + list?.[0].axisValue + `</div>`;
						for (let i = 0; i < list.length; i++) {
							value +=
								`<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
								`<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
                                    list?.[i].color +
								`;"></div>` +
								`<div style="font-family: PingFang SC;">` +
                                    list?.[i].seriesName +
								'</div></div>' +
								`<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
								(Number(list?.[i].value) * 1).toFixed(2) +
								// `${list[i].seriesName === '换手率'||list[i].seriesName === '换手率中位数'?'倍': '%'}`+'</div>' +
                                `%`+'</div>' +
								`</div>`;
						}
						return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
					}
				},
            // title: {
            //     show:true,
            //     text: this.name === 'turnoverInfo' ? '报告期换手率' : '报告期前十大集中度',
            //     left: 'left',
            //     textStyle: {
            //         fontSize: 14
            //     },
            // },
            color:['#4096ff', '#4096ff', '#7388A9'],
            grid:{
					bottom:30,
					left:40,
					top:20
			},
            legend: {
                bottom:'0%',
                data: this.name === 'turnoverInfo' ?[
                    {
                        name: '换手率',
                    
                    },
                    // {
                    //     name: '换手率中位数',
                        
                    // },
                    // {
                    //     name: '全市场排名分位',
                    // },
                ]:[
                    {
                        name: '前十大集中度',
                    
                    },
                    // {
                    //     name: '前十大集中度中位数',
                        
                    // },
                    // {
                    //     name: '全市场排名分位',
                    // },
                ]
            },
            xAxis: [{
                type: 'category',
                boundaryGap: true,
                data: dateList,
                axisLabel: {

                    // interval 可以定义成数字，但这也会有一个弊端，在不确定数据量的时候，间隔数不好定义，不能随心所欲的控制所要展示的内容 
                    interval:2
                }, 
            }],
            yAxis: 
            [ 
                {
                    type: 'value',
                    axisLine:{
                        show:false
                    },
                    axisTick:{
                        show:false
                    },
                    splitLine:{
                        lineStyle:{
                            type:'dashed'
                        }
                    },
                    name:this.name === 'turnoverInfo' ?'换手率(%)' :'集中度（%）',
                    nameLocation: 'middle' ,
                    nameGap: 40,
                    nameTextStyle:{
                        fontSize: 12,
                        color: "rgba(0, 0, 0, 0.65)"
                    }  
                },
                {
                    type: 'value',
                    axisLine:{
                        show:false
                    },
                    axisTick:{
                        show:false
                    },
                    name:'全市场排名分位',
                    nameLocation: 'middle' ,
                    nameGap: 40 ,
                    nameTextStyle:{
                        fontSize: 12,
                        color: "rgba(0, 0, 0, 0.65)"
                    }  
                }
            ],
           
            series: [
                {
                    name: this.name === 'turnoverInfo'?'换手率':'前十大集中度',
                    type: 'line',
                    symbol: 'none',
                    data: data1,
                    yAxisIndex:0,
                },
                // {
                //     name: this.name === 'turnoverInfo'?'换手率中位数':'前十大集中度中位数',
                //     type: 'line',
                //     symbol: 'none',
                //     data: data2,
                //     yAxisIndex:0,
                // },
                // {
                //     name: '全市场排名分位',
                //     type: 'line',
                //     symbol: 'none',
                //     data: data3,
                //     yAxisIndex:1,
                // },
            ]
        });
    }
}
};
</script>

<style scoped>
.chart_one{
padding: 0;
box-shadow: none;
}
.charts_fill_class{
    .echarts{
    height: 248px;

    }
}
</style>
