<!-- 风格分析 -->
<template>
	<div>
		<div class="flex_card">
			<div v-for="item in templateList" :key="item.value" v-show="item.isshow" :class="item.type">
				<component
					v-for="v in fundtype_list"
					:key="v.value"
					:is="item.is"
					:ref="item.value + v.value"
					@resolveFather="item.methods"
					v-loading="loading"
				></component>
			</div>
		</div>
	</div>
</template>

<script>
// FOF子资产分析
import fundTypeAnalysis from '@/components/components/components/fundTypeAnalysis/index.vue';
export default {
	components: {
		fundTypeAnalysis
	},
	data() {
		return {
			name: 'FOF持仓穿透',
			info: {},
			templateList: [],
			requestOver: [],
			requestAll: 0,
			loading: true,
			fundtype_list: [
				{ label: '权益分析(直投权益+间接投资权益)', value: 'equity' },
				{ label: '纯债分析(直投纯债+间接投资纯债)', value: 'purebond' },
				{ label: '转债分析(直投转债+间接投资转债)', value: 'cbond' },
				{ label: '商品分析(放大权重至100%)', value: 'commodity' },
				{ label: '对冲资产分析(放大权重至100%)', value: 'neutral' }
			]
		};
	},
	props: {
		showEditor: {
			type: Boolean,
			default: false
		}
	},
	methods: {
		// 接收/返回组件列表
		getTemplateList(list) {
			if (list) {
				this.templateList = [...list];
			} else {
				return this.templateList;
			}
		},
		// 获取父组件数据
		getData(data) {
			this.info = data;
			this.loading = true;
			this.requestOver = [];
			this.formatTemplatList();
		},
		// 获取打印数据
		async createPrintWord(info) {
			this.info = info;
			let printData = [];
			this.templateList.map((item) => {
				if (item.isshow) {
					if (this.$refs[item.value]?.[0].createPrintWord) {
						let list = this.$refs[item.value]?.[0].createPrintWord(this.info);
						printData.push(list);
					}
				}
			});
			let data = await Promise.all(printData);
			data.unshift(this.$exportWord.exportFirstTitle(this.name));
			return data;
		},
		// 格式化模板列表
		formatTemplatList() {
			this.$nextTick(() => {
				this.templateList.map((item) => {
					if (item.typelist.indexOf(this.info.type) !== -1) {
						this.fundtype_list.map((v) => {
							this.$refs[item.value + v.value]?.[0]?.getData(this.info, v.value);
						});
						this.loading = false;
					}
				});
			});
		}
	}
};
</script>
<style scoped>
.flex_card .small_template {
	height: 324px;
}
</style>
