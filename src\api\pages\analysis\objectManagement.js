import request from '@/utils/request';

/**
 *获取列表
 * @param params
 * @returns {*}
 */
export function getList(params) {
    return request({
        url: '/api/taikang/analytic/list',
        method: 'get',
        params
    });
}

/**
 * 获取分析对象树
 * @param params
 * @returns {*}
 */
export function getTree(params) {
    return request({
        url: '/api/taikang/analytic/getTree',
        method: 'get',
        params
    });
}

/**
 * 分析对象保存
 * @param data
 * @returns {*}
 */
export function saveObject(data) {
    return request({
        url: '/api/taikang/analytic/save',
        method: 'post',
        data
    })
}
/**
 * 分析对象删除
 * @param data
 * @returns {*}
 */
export function deleteObject(id, data) {
    return request({
        url: `/api/taikang/analytic/del?id=${id}`,
        method: 'post',
        data
    })
}

export function saveReport(params) {
    return request({
        url: '/api/taikang/report/save',
        method: 'post',
        params
    })
}


