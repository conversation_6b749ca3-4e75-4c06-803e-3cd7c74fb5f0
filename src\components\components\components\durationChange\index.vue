<template>
	<div class="chart_one">
		<div>
			<div class="title">存续期间变化</div>
		</div>
		<div class="charts_fill_class" v-loading="loading">
			<v-chart
				ref="durationChange"
				:options="option"
				element-loading-text="暂无数据"
				element-loading-spinner="el-icon-document-delete"
				element-loading-background="rgba(239, 239, 239, 0.5)"
				class="charts_one_class"
				autoresize
			></v-chart>
		</div>
	</div>
</template>

<script>
import VChart from 'vue-echarts';

import { lineChartOption } from '@/utils/chartStyle.js';
export default {
	components: { VChart },
	data() {
		return {
			option: {},
			loading: true
		};
	},
	methods: {
		getData() {
			this.loading = false;
			this.option = lineChartOption({
				legend: {
					data: ['Evaporation', 'Precipitation', 'Temperature']
				},
				xAxis: [
					{
						type: 'category',
						data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
						axisPointer: {
							type: 'shadow'
						}
					}
				],
				yAxis: [
					{
						type: 'value',
						name: 'Precipitation',
						min: 0,
						max: 250,
						interval: 50,
						axisLabel: {
							formatter: '{value} ml'
						}
					},
					{
						type: 'value',
						name: 'Temperature',
						min: 0,
						max: 25,
						interval: 5,
						axisLabel: {
							formatter: '{value} °C'
						}
					}
				],
				series: [
					{
						name: 'Evaporation',
						type: 'bar',
						tooltip: {
							valueFormatter: function (value) {
								return value + ' ml';
							}
						},
						data: [2.0, 4.9, 7.0, 23.2, 25.6, 76.7, 135.6, 162.2, 32.6, 20.0, 6.4, 3.3]
					},
					{
						name: 'Precipitation',
						type: 'bar',
						tooltip: {
							valueFormatter: function (value) {
								return value + ' ml';
							}
						},
						data: [2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 175.6, 182.2, 48.7, 18.8, 6.0, 2.3]
					},
					{
						name: 'Temperature',
						type: 'line',
						yAxisIndex: 1,
						tooltip: {
							valueFormatter: function (value) {
								return value + ' °C';
							}
						},
						data: [2.0, 2.2, 3.3, 4.5, 6.3, 10.2, 20.3, 23.4, 23.0, 16.5, 12.0, 6.2]
					}
				]
			});
		}
	}
};
</script>

<style></style>
