<!--  -->
<template>
	<div id="managedFundAll" v-show="loaax">
		<analysis-card-title title="管理产品概况" image_id="managedFundAll"></analysis-card-title>
		<div v-loading="loaa">
			<v-chart
				ref="managedFundAll"
				autoresize
				element-loading-text="暂无数据"
				element-loading-spinner="el-icon-document-delete"
				element-loading-background="rgba(239, 239, 239, 0.5)"
				style="page-break-inside: avoid; width: 100%; height: 300px"
				:options="Ttradpic5"
			/>
		</div>
	</div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { exportChart, exportTitle } from '@/utils/exportWord.js';
import { getEvaluateManagers } from '@/api/pages/Analysis.js';
export default {
	data() {
		//这里存放数据
		return {
			search: '',
			loaax: true,
			Ttradpic5: {},
			loaa: true,
			showitem: true,
			tableData: [],
			info: {}
		};
	},
	filters: {
		filts(value) {
			if (value == '--' || value == null || value == '' || value == -1000000) {
				return '--';
			} else return value;
		},
		fixY(value) {
			if (value == '--' || value == null || value == '' || value == -1000000) {
				return '--';
			} else return Number(value).toFixed(2) + '亿';
		},
		fix2Y(value) {
			if (value == '--' || value == null || value == '' || value == -1000000) {
				return '--';
			} else return (Number(value) * 100).toFixed(2) + '%';
		}
	},
	//方法集合
	methods: {
		getData(info) {
			this.loaa = true;
			this.info = info;
			this.getmanagedFundAll();
		},
		async getmanagedFundAll() {
			let data = await getEvaluateManagers({ code: this.info.code, flag: this.info.flag });
			if (data?.mtycode == 200) {
				this.getChartData(data?.data);
			}
		},
		getChartData(data) {
			this.loaa = false;
			let maxp = 0;
			let maxdata = 0;
			let mindata = 0;
			if (data) {
				let arr = [];
				for (let i = 0; i < data.length; i++) {
					// 最大点权重
					if (Number(data[i].netasset) > maxp) maxp = Number(data[i].netasset);
					// 日期换成长度
					let arr2 = '';
					let datetemp = null;
					if (data[i].founddate && data[i].founddate != '' && data[i].founddate != undefined && data[i].founddate != null) {
						datetemp = data[i].founddate.split('-');
						for (let j = 0; j < datetemp.length; j++) {
							arr2 += datetemp[j];
						}
					} else {
						arr2 = '';
					}
					if (i == 0) {
						maxdata = arr2;
						mindata = arr2;
					}
					if (Number(arr2) > maxdata) maxdata = arr2;
					if (Number(arr2) < mindata) mindata = arr2;
					// //console.log(arr2)
					// 空权重置为0
					let tempnow = data[i].netasset;
					if (tempnow == '--') {
						tempnow = 0;
					}
					arr.push([
						(Number(data[i].volatility) * 100).toFixed(2),
						(Number(data[i].ave_return) * 100).toFixed(2),
						tempnow,
						arr2,
						data[i].name,
						data[i].type
					]);
				}
				let seris = [];
				let cate = [];
				for (let i = 0; i < arr.length; i++) {
					if (cate.indexOf(arr[i][5]) < 0) {
						cate.push(arr[i][5]);
						seris.push({
							type: 'scatter',
							data: [arr[i]],
							name: arr[i][5]
						});
					} else {
						seris[cate.indexOf(arr[i][5])].data.push(arr[i]);
					}
				}
				this.Ttradpic5 = {
					color: ['#4096ff', '#4096ff', '#7388A9', '#6F80DD', '#6C96F2', '#FD6865', '#83D6AE', '#88C9E9', '#ED589D', '#FA541C'],
					grid: { top: '30px', left: '50px', right: '100px', bottom: '20px' },
					legend: {
						data: cate
					},
					// toolbox: {
					// 	feature: {
					// 		saveAsImage: { pixelRatio: 3 }
					// 	},
					// 	top: -4,
					// 	width: 104
					// },
					tooltip: {
						trigger: 'item',
						textStyle: {
							fontSize: 14
						},
						formatter(params) {
							let temp2 = Number(params.data[2]);
							if (Number(params.data[2]) == 0) {
								temp2 = '--';
							}
							// //console.log(params)
							let temp = null;
							temp =
								params.data[4] +
								`，波动率：` +
								params.data[0] +
								`%，收益率：` +
								params.data[1] +
								'%' +
								`,起始管理日期：` +
								params.data[3] +
								',规模：' +
								Number(temp2).toFixed(2) +
								'亿';
							return temp;
						}
					},
					xAxis: {
						//月份
						nameTextStyle: {
							fontSize: 12
						},
						splitLine: {
							show: true,
							lineStyle: {
								type: 'dashed'
							}
						},
						axisLabel: {
							show: true,
							fontSize: 12,
							lineStyle: {
								type: 'dashed'
							}
						},
						axisLine: {
							show: false
						},
						name: '年化波动率(%)',
						// data: res.data.nav_rate_daily_return.date,
						type: 'value'
					},
					yAxis: {
						//该项在相应月份的数值
						axisLine: {
							show: false
						},
						axisTick: {
							show: false
						},
						splitLine: {
							show: true,
							lineStyle: {
								type: 'dashed'
							}
						},
						nameTextStyle: {
							fontSize: 12
						},
						axisLabel: {
							show: true,
							textStyle: {
								fontSize: 11
							}
						},
						name: '年化收益率(%)',
						type: 'value',
						scale: true
					},
					visualMap: [
						{
							show: false,
							right: '0px',
							top: '150px',
							dimension: 2,
							min: 0,
							max: maxp,
							itemWidth: 30,
							itemHeight: 120,
							precision: 1,
							text: ['配置权重%'],
							textGap: 5,
							textStyle: {
								color: 'black',
								fontSize: 10
							},
							inRange: {
								symbolSize: [10, 30]
							},
							outOfRange: {
								symbolSize: [10]
							},
							controller: {
								inRange: {
									color: ['black']
								}
							}
						}
					],
					series: seris
				};
			} else {
				this.loaax = false;
				this.loaa = false;
			}
		},
		createPrintWord() {
			this.$refs['managedFundAll'].mergeOptions({ toolbox: { show: false } });
			let height = this.$refs['managedFundAll'].$el.clientHeight;
			let width = this.$refs['managedFundAll'].$el.clientWidth;
			let item = [
				...exportTitle('管理产品概况'),
				...exportChart(
					this.$refs['managedFundAll'].getDataURL({
						type: 'png',
						pixelRatio: 1,
						backgroundColor: '#fff'
					}),
					{ width, height }
				)
			];
			this.$refs['managedFundAll'].mergeOptions({ toolbox: { show: true } });
			return item;
		}
	}
};
</script>
<style scoped>
.treepicbox {
	margin: 20px;
}
.fonts18 {
	font-size: 18px;
}
</style>
