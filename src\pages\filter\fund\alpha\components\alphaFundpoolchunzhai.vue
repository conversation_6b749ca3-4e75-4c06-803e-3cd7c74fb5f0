<template>
  <div class="homebodyfontsize">
    <tempbasket ref="tempbasketfund"
                type="fund"></tempbasket>
    <div>
      <div style="margin-top: 10px; padding-bottom: 20px; background: white">
        <div class="title"
             style="display: flex; background: white !important">
          <div class="pointssearch"></div>
          筛选条件
        </div>
        <div style="margin-top: 10px">
          <!-- <div style="display:flex;width:100%;maring-top:10px" >
            <div style="display:flex;margin-left:30px">
            <el-input  v-model="fundname" placeholder="请输入基金名称" style="width:100%;margin-right:10px" class="fundnameinput"></el-input>
            <el-button  type="" icon="el-icon-search" @click="handleSearch">搜索</el-button>    
            </div> 
        </div> -->
          <div style="margin-left: 30px; margin-top: 10px">
            <alphachoosepool @changepool="changepool"></alphachoosepool>
            <el-form ref="elForm"
                     :model="formData"
                     size="medium"
                     label-width="100px">
              <el-form-item>
                <template slot="label">
                  基金规模<el-tooltip class="item"
                              effect="dark"
                              content="请输入数字，如0-2，表示0亿-2亿范围"
                              placement="right-start">
                    <svg width="14"
                         height="14"
                         viewBox="0 0 14 14"
                         fill="none">
                      <path fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
                            fill="black"
                            fill-opacity="0.45" />
                    </svg>
                  </el-tooltip>
                </template>
                <div class="tiptablebox">
                  <div>
                    <el-input type="number"
                              placeholder="e.g. 0"
                              v-model="formData.scale.from"
                              class="inputbox"><i slot="suffix"><i class="yifont">亿</i></i></el-input>
                  </div>
                  <div>&nbsp; ~ &nbsp;</div>
                  <div>
                    <el-input type="number"
                              placeholder="e.g. 500"
                              v-model="formData.scale.end"
                              class="inputbox"><i slot="suffix"><i class="yifont">亿</i></i></el-input>
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="是否信用挖掘(2015年后)">
                <el-radio-group v-model="formData.issink"
                                size="medium">
                  <el-radio v-for="(item, index) in field103Options"
                            :key="index"
                            :label="item.value"
                            @click.native="cancelchoose2">{{
										item.label
									}}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="当前久期">
                <el-cascader placeholder="请选择久期"
                             class="width160"
                             v-model="formData.nowdurelong"
                             :options="optionsdure"> </el-cascader>
              </el-form-item>
              <el-form-item label="当前信用">
                <el-cascader placeholder="请选择信用"
                             class="width160"
                             v-model="formData.nowsinkdown"
                             :options="optionssinkdown">
                </el-cascader>
              </el-form-item>
              <!-- <el-form-item label="可接受信用下沉程度" >
        <el-radio-group v-model="formData.sink" size="medium">
          <el-radio v-for="(item, index) in field102Options" :key="index" :label="item.value"
           @click.native ='cancelchoose'
           >{{item.label}}</el-radio>
        </el-radio-group>
      </el-form-item> -->
              <el-form-item label="久期操作（含杠杆）">
                <el-slider v-model="formData.sinkdo"
                           style="width: 300px"
                           :format-tooltip="formatTooltip2"
                           :min="0"
                           :max="3"
                           :step="1"></el-slider>
                <!-- <el-checkbox-group v-model="formData.sinkdo" size="medium">
									<el-checkbox v-for="(item, index) in sinkdoOptions" :key="index" :label="item.value">{{ item.label }}</el-checkbox>
								</el-checkbox-group> -->
              </el-form-item>
              <el-form-item label="基金分类">
                <el-checkbox-group v-model="formData.fundchara"
                                   size="medium">
                  <el-checkbox v-for="(item, index) in fundcharaOptions"
                               :key="index"
                               :label="item.value">{{ item.label }}</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item>
                <template slot="label">
                  基金可申购金额<el-tooltip class="item"
                              effect="dark"
                              content="请输入数字单位为万元，如5000，表示5000万元及以上"
                              placement="right-start">
                    <svg width="14"
                         height="14"
                         viewBox="0 0 14 14"
                         fill="none">
                      <path fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
                            fill="black"
                            fill-opacity="0.45" />
                    </svg>
                  </el-tooltip>
                </template>
                <div class="tiptablebox">
                  <div>
                    <el-input type="number"
                              placeholder="e.g. 10"
                              v-model="formData.maxmoney"
                              class="inputbox"><i slot="suffix"><i class="yifont">万</i></i></el-input>
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="⻛险收益特征">
                <template slot="label">
                  风险收益特征<el-tooltip class="item"
                              effect="dark"
                              content="请输入风险特征。例如年化收益率，输入0.1-0.6，表示其范围为10%-60%"
                              placement="right-start">
                    <svg width="14"
                         height="14"
                         viewBox="0 0 14 14"
                         fill="none">
                      <path fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
                            fill="black"
                            fill-opacity="0.45" />
                    </svg>
                  </el-tooltip>
                </template>
                <el-checkbox-group v-model="temp1"
                                   size="medium">
                  <el-checkbox v-for="(item, index) in field104Options"
                               :key="index"
                               :label="item.value">{{ item.label }}</el-checkbox>
                </el-checkbox-group>
                <div>
                  <div style="display: flex; margin: 5px; background: #f1f1f5; max-width: 75vw; flex-wrap: wrap">
                    <div v-if="showfengxian"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="收益"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     class="width160"
                                     v-model="formData.insuranceincome[0].date"
                                     :options="option1">
                        </el-cascader>
                      </div>
                      <selecterinputboxsignal name="收益"
                                              @setvalue1="setvalue1"
                                              @setvalue2="setvalue2"
                                              indexid="1.1"
                                              @setrank="setrank"
                                              :rankvalue="formData.insuranceincome[0].rank"
                                              :valuef="formData.insuranceincome[0].value1"
                                              :smallorbig="true"
                                              :buttonarray="selectvalue1"></selecterinputboxsignal>
                      <!-- <div style="display:flex">
                      <div class="tiptablebox"> 
                               <div><el-input type="number" placeholder='e.g. 0.2' v-model="formData.insuranceincome[0].value1" class="inputbox"></el-input></div>
                        &nbsp;~&nbsp;
                         <div><el-input type="number" placeholder='e.g. 0.6' v-model="formData.insuranceincome[0].value2" class="inputbox"></el-input></div>
                         </div>
                     </div> -->
                    </div>
                    <div v-if="showfengxian1"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="最大回撤"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     class="width160"
                                     v-model="formData.insuranceincome[1].date"
                                     :options="option2">
                        </el-cascader>
                      </div>
                      <selecterinput name="波动率比"
                                     @setvalue1="setvalue1"
                                     @setvalue2="setvalue2"
                                     indexid="1.2"
                                     @setrank="setrank"
                                     :rankvalue="formData.insuranceincome[1].rank"
                                     :valuef="formData.insuranceincome[1].value1"
                                     :valueb="formData.insuranceincome[1].value2"
                                     :smallorbig="false"
                                     :buttonarray="selectvalue2"></selecterinput>

                      <!-- <div style="display:flex">
                      <div class="tiptablebox"> &nbsp;小于&nbsp;
                         <div><el-input type="number" placeholder='e.g. 0.3' v-model="formData.insuranceincome[1].value1" class="inputbox"></el-input></div>
                         </div>
                     </div> -->
                    </div>
                    <div v-if="showfengxian2"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="波动率"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     class="width160"
                                     v-model="formData.insuranceincome[2].date"
                                     :options="option3">
                        </el-cascader>
                      </div>
                      <selecterinput name="波动率比"
                                     @setvalue1="setvalue1"
                                     @setvalue2="setvalue2"
                                     indexid="1.3"
                                     @setrank="setrank"
                                     :rankvalue="formData.insuranceincome[2].rank"
                                     :smallorbig="false"
                                     :valuef="formData.insuranceincome[2].value1"
                                     :valueb="formData.insuranceincome[2].value2"
                                     :buttonarray="selectvalue3"></selecterinput>

                      <!-- <div style="display:flex">
                      <div class="tiptablebox"> &nbsp;小于&nbsp;
                         <div><el-input type="number" placeholder='e.g. 0.1' v-model="formData.insuranceincome[2].value1" class="inputbox"></el-input></div>
                         </div>
                     </div> -->
                    </div>
                    <div v-if="showfengxian3"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="夏普率"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     class="width160"
                                     v-model="formData.insuranceincome[3].date"
                                     :options="option4">
                        </el-cascader>
                      </div>
                      <selecterinputboxsignal name="波动率比"
                                              @setvalue1="setvalue1"
                                              @setvalue2="setvalue2"
                                              indexid="1.4"
                                              @setrank="setrank"
                                              :rankvalue="formData.insuranceincome[3].rank"
                                              :valuef="formData.insuranceincome[3].value1"
                                              :smallorbig="true"
                                              :buttonarray="selectvalue4"></selecterinputboxsignal>

                      <!-- <div style="display:flex">
                      <div class="tiptablebox"> &nbsp;大于&nbsp;
                         <div><el-input type="number" placeholder='e.g. 1' v-model="formData.insuranceincome[3].value1" class="inputbox"></el-input></div>
                         </div>
                     </div> -->
                    </div>
                    <div v-if="showfengxian4"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="卡码率"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     class="width160"
                                     v-model="formData.insuranceincome[4].date"
                                     :options="option5">
                        </el-cascader>
                      </div>
                      <selecterinputboxsignal name="波动率比"
                                              @setvalue1="setvalue1"
                                              @setvalue2="setvalue2"
                                              indexid="1.5"
                                              @setrank="setrank"
                                              :rankvalue="formData.insuranceincome[4].rank"
                                              :valuef="formData.insuranceincome[4].value1"
                                              :smallorbig="true"
                                              :buttonarray="selectvalue5"></selecterinputboxsignal>

                      <!-- <div style="display:flex">
                      <div class="tiptablebox"> &nbsp;大于&nbsp;
                         <div><el-input type="number" placeholder='e.g. 30' v-model="formData.insuranceincome[4].value1" class="inputbox"></el-input></div>
                         </div>
                     </div> -->
                    </div>
                    <div v-if="showfengxian5"
                         class="choosebox">
                      <div>
                        <el-input class="width170"
                                  disabled
                                  value="胜率"></el-input>
                      </div>
                      <div>
                        <el-cascader placeholder="请选择时间范围"
                                     class="width160"
                                     v-model="formData.insuranceincome[5].date"
                                     :options="option6">
                        </el-cascader>
                      </div>
                      <selecterinputboxsignal name="胜率"
                                              @setvalue1="setvalue1"
                                              @setvalue2="setvalue2"
                                              indexid="1.6"
                                              @setrank="setrank"
                                              :rankvalue="formData.insuranceincome[5].rank"
                                              :valuef="formData.insuranceincome[5].value1"
                                              :smallorbig="true"
                                              :buttonarray="selectvalue6"></selecterinputboxsignal>

                      <!-- <div style="display:flex">
                      <div class="tiptablebox"> &nbsp;大于&nbsp;
                         <div><el-input type="number" placeholder='e.g. 30' v-model="formData.insuranceincome[4].value1" class="inputbox"></el-input></div>
                         </div>
                     </div> -->
                    </div>
                  </div>
                </div>
              </el-form-item>

              <div>
                <el-divider content-position="left">市场判断</el-divider>
              </div>
              <el-form-item label="alpha筛选器-风格判断"
                            prop="field107">
                <template slot="label">
                  市场判断<el-tooltip class="item"
                              effect="dark"
                              content="请在输入框中输入数字单位为%，请确保选择的元素加起来为100"
                              placement="right-start">
                    <svg width="14"
                         height="14"
                         viewBox="0 0 14 14"
                         fill="none">
                      <path fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M7.0002 0.700195C10.4793 0.700195 13.3002 3.52113 13.3002 7.0002C13.3002 10.4793 10.4793 13.3002 7.0002 13.3002C3.52113 13.3002 0.700195 10.4793 0.700195 7.0002C0.700195 3.52113 3.52113 0.700195 7.0002 0.700195ZM7.0002 1.76895C4.11176 1.76895 1.76895 4.11176 1.76895 7.0002C1.76895 9.88863 4.11176 12.2314 7.0002 12.2314C9.88863 12.2314 12.2314 9.88863 12.2314 7.0002C12.2314 4.11176 9.88863 1.76895 7.0002 1.76895ZM7.0002 9.53145C7.31086 9.53145 7.5627 9.78328 7.5627 10.0939C7.5627 10.4046 7.31086 10.6564 7.0002 10.6564C6.68954 10.6564 6.4377 10.4046 6.4377 10.0939C6.4377 9.78328 6.68954 9.53145 7.0002 9.53145ZM7.0002 3.68145C7.59082 3.68145 8.1477 3.88395 8.56957 4.25379C9.00832 4.6377 9.2502 5.15379 9.2488 5.70645C9.2488 6.51926 8.71301 7.25051 7.88332 7.56973C7.62316 7.66957 7.44879 7.92269 7.44879 8.19973V8.51895C7.44879 8.58082 7.39816 8.63145 7.33629 8.63145H6.66129C6.59941 8.63145 6.54879 8.58082 6.54879 8.51895V8.2166C6.54879 7.89176 6.64441 7.57113 6.82863 7.30394C7.01004 7.04238 7.26316 6.8427 7.56129 6.72879C8.04082 6.54457 8.3502 6.14379 8.3502 5.70645C8.3502 5.08629 7.7441 4.58145 7.0002 4.58145C6.25629 4.58145 5.6502 5.08629 5.6502 5.70645V5.81332C5.6502 5.8752 5.59957 5.92582 5.5377 5.92582H4.8627C4.80082 5.92582 4.7502 5.8752 4.7502 5.81332V5.70645C4.7502 5.15379 4.99207 4.6377 5.43082 4.25379C5.8527 3.88535 6.40957 3.68145 7.0002 3.68145Z"
                            fill="black"
                            fill-opacity="0.45" />
                    </svg>
                  </el-tooltip>
                </template>
                <el-checkbox-group v-model="temp4"
                                   size="medium">
                  <el-checkbox v-for="(item, index) in field107Options"
                               :key="index"
                               :label="item.value"
                               :disabled="item.disabled">{{
										item.label
									}}</el-checkbox>
                </el-checkbox-group>
                <div>
                  <div style="display: flex; margin: 5px; background: #f1f1f5; max-width: 75vw; flex-wrap: wrap">
                    <div v-if="czshow"
                         class="choosebox">
                      <el-checkbox-group v-model="czchoose">
                        <el-checkbox style="margin-right: 0"
                                     label="松货币"></el-checkbox>
                        <el-input @mousewheel.native.prevent
                                  onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
                                  :disabled="czchoose.indexOf('松货币') < 0"
                                  style="margin-right: 20px"
                                  type="number"
                                  v-model="formData.Growth.value1"
                                  class="inputbox"></el-input>
                        <el-checkbox style="margin-right: 0"
                                     label="紧货币"></el-checkbox>
                        <el-input @mousewheel.native.prevent
                                  onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
                                  :disabled="czchoose.indexOf('紧货币') < 0"
                                  style="margin-right: 20px"
                                  type="number"
                                  v-model="formData.Growth.value2"
                                  class="inputbox"></el-input>
                      </el-checkbox-group>
                    </div>

                    <div v-if="dpshow"
                         class="choosebox">
                      <el-checkbox-group v-model="dpchoose">
                        <el-checkbox style="margin-right: 0"
                                     label="松信用"></el-checkbox>
                        <el-input @mousewheel.native.prevent
                                  onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
                                  :disabled="dpchoose.indexOf('松信用') < 0"
                                  style="margin-right: 20px"
                                  type="number"
                                  v-model="formData.bigsmall.value1"
                                  class="inputbox"></el-input>
                        <el-checkbox style="margin-right: 0"
                                     label="紧信用"></el-checkbox>
                        <el-input @mousewheel.native.prevent
                                  onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode || event.which))) || event.which === 8"
                                  :disabled="dpchoose.indexOf('紧信用') < 0"
                                  style="margin-right: 20px"
                                  type="number"
                                  v-model="formData.bigsmall.value2"
                                  class="inputbox"></el-input>
                      </el-checkbox-group>
                    </div>
                  </div>
                </div>
              </el-form-item>
            </el-form>
            <div style="display: flex; margin-top: 20px">
              <el-button style="background: #4096FF; color: white"
                         type=""
                         size="medium"
                         @click="FUNC.throttleFunc(submitForm, 3000)()">提交</el-button>
              <el-button style="background: #40AFFF; color: white"
                         size="medium"
                         @click="redoForm">清除筛选条件</el-button>
              <el-button size="medium"
                         @click="resetForm">存为模板</el-button>
              <el-button size="medium"
                         @click="vismodel = true">选择模板</el-button>
            </div>
          </div>
        </div>
      </div>
      <div class="line2"
           style="margin-top: 5px; margin-bottom: 5px"></div>
      <div style="padding-bottom: 20px; background: white">
        <div style="display: flex">
          <div class="title"
               style="display: flex; background: white !important">
            <div class="pointssearch"></div>
            筛选结果
          </div>
          <div style="justify-content: flex-end; display: flex"
               class="marginight20px">
            <el-button size="medium"
                       :disabled="fundlisttemp.length > 4 || fundlisttemp.length <= 1"
                       @click="gotocompare">比较</el-button>
            <el-button size="medium"
                       @click="cretezuhequ">创建基金池</el-button>
            <el-button size="medium"
                       :disabled="fundlisttemp.length < 1 && alldata.length > 50"
                       @click="creteportfolio">创建组合</el-button>
            <div style="margin-left: 16px"></div>
            <el-button size="medium"
                       style="background: #4096FF; color: white"
                       @click="printconsole">导出筛选结果</el-button>
          </div>
        </div>
        <div v-loading="showmsgsloading"
             class="tablemargin">
          <el-table :default-sort="{ prop: '1w', order: 'descending' }"
                    :data="tableData"
                    @sort-change="sort_change"
                    :cell-style="elcellstyle"
                    class="table"
                    ref="multipleTable"
                    @selection-change="handleSelectzuhe"
                    :row-key="getRowKey"
                    header-cell-class-name="table-header">
            <el-table-column type="selection"
                             align="gotoleft"
                             :reserve-selection="true"
                             :width="getfontSize(55)"> </el-table-column>
            <el-table-column :show-overflow-tooltip="true"
                             prop="name"
                             :width="getfontSize(240)"
                             align="gotoleft"
                             label="基金名称">
              <template slot-scope="scope"><a style="border-bottom: 1px solid #4096ff"
                   @click="godetail(scope.row.code, scope.row.name)">{{
									scope.row.name | isDefault
								}}</a></template>
            </el-table-column>
            <el-table-column sortable="custom"
                             prop="code"
                             label="基金代码"
                             align="gotoleft"> </el-table-column>
            <el-table-column sortable="custom"
                             prop="manager_name"
                             label="基金经理"
                             align="gotoleft">
              <template slot-scope="scope"><a style="border-bottom: 1px solid #4096ff"
                   @click="godetailP(scope.row.manager_code.split(',')[0], scope.row.manager_name.split(',')[0])">{{ scope.row.manager_name.split(',')[0] | isDefault }}</a><span v-if="scope.row.manager_code.split(',').length >= 2">,<a style="border-bottom: 1px solid #4096ff"
                     @click="godetailP(scope.row.manager_code.split(',')[1], scope.row.manager_name.split(',')[1])">{{ scope.row.manager_name.split(',')[1] | isDefault }}</a></span></template>
            </el-table-column>
            <el-table-column prop="1w"
                             sortable="custom"
                             label="近一周收益"
                             align="gotoleft">
              <template slot-scope="scope">{{ scope.row['1w'] | fix2p }}</template>
            </el-table-column>
            <el-table-column prop="1m"
                             sortable="custom"
                             label="近一月收益"
                             align="gotoleft">
              <template slot-scope="scope">{{ scope.row['1m'] | fix2p }}</template>
            </el-table-column>
            <el-table-column prop="1q"
                             sortable="custom"
                             label="近一季收益"
                             align="gotoleft">
              <template slot-scope="scope">{{ scope.row['1q'] | fix2p }}</template>
            </el-table-column>
            <el-table-column prop="1y"
                             sortable="custom"
                             label="近一年收益"
                             align="gotoleft">
              <template slot-scope="scope">{{ scope.row['1y'] | fix2p }}</template>
            </el-table-column>
            <el-table-column prop="finally_score"
                             sortable="custom"
                             label="得分"
                             align="gotoleft">
              <template slot-scope="scope">{{ scope.row['finally_score'] | fix2p }}</template>
            </el-table-column>
            <!-- <el-table-column label="查看详情" width="100" align="gotoleft">
                    <template slot-scope="scope"><div @click="godetail(scope.row.code,scope.row.name)"><i  class="el-icon-tickets icon_color"></i></div></template>  
                </el-table-column> -->
            <el-table-column label="关注"
                             width="100"
                             align="gotoleft">
              <template slot-scope="scope">
                <div @click="addpool(scope.row.code, scope.row.name)"><i class="el-icon-circle-plus icon_color"></i></div>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <el-pagination background
                           layout="total, sizes, prev, pager, next"
                           :current-page.sync="pageIndex"
                           :page-size="pageSize"
                           :total="pageTotal"
                           @size-change="handleSizeChange"
                           @current-change="handlePageChange"></el-pagination>
          </div>
        </div>
      </div>
    </div>
    <!-- <createdcomb :addzuheflag="addzuheflag" :fundlist="fundlisttemp" :dataobj="dataobj"></createdcomb> -->

    <el-drawer style="overflow-x: hidden"
               :visible.sync="vismodel"
               direction="rtl"
               :show-close="false"
               size="600px">
      <div style="text-align: left"
           class="marginleft20">
        <span class="recommodfont borderbottom2px">推荐模板&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<el-button style="color: #b4bed0 !important; border: 1px solid #b4bed0; border-radius: 2px; background: #white !important"
                     @click="changepos"
                     icon="el-icon-refresh">换一换</el-button></span>
      </div>
      <div style="height: 20px"></div>
      <div v-if="showmodel"
           style="width: 600px; display: flex; flex-wrap: wrap">
        <div v-for="(items, index) in listar"
             :key="index"
             style="cursor: pointer"
             @click="setshuju(items.model_args)"
             class="modelbox">
          <el-tooltip class="item"
                      effect="dark"
                      :content="items.model_description"
                      placement="right-start">
            <div style="text-align: center">
              <span class="modelnamebox">{{ items.model_name }}</span>
            </div>
          </el-tooltip>
        </div>
      </div>
      <div style="height: 50px"></div>
      <div style="text-align: left"
           class="marginleft20"><span class="recommodfont borderbottom2px">我的模板</span></div>
      <div style="height: 20px"></div>
      <div v-for="(item1, index) in gridData"
           :key="index">
        <div class="selfmodelbox">
          <div class="margin20px"></div>
          <div style="cursor: pointer"
               @click="setshuju(item1.model_args)"
               class="selfmodelde1">
            模型名称：<span class="selfmodelde2"
                  style="color: #4096ff">{{ item1.model_name }}</span>
          </div>
          <div style="cursor: pointer"
               @click="setshuju(item1.model_args)"
               class="selfmodelde1">
            创建日期：<span class="selfmodelde2">{{ item1.model_time }}</span>
          </div>
          <div style="cursor: pointer"
               class="selfmodelde1">
            编辑操作：<span class="selfmodelde2">
              <el-button @click="deletemodel(item1.model_name)"
                         type="danger">删除</el-button>
            </span>
          </div>
          <div style="cursor: pointer"
               @click="setshuju(item1.model_args)"
               class="selfmodelde1">
            模型描述：<span class="selfmodelde2">{{ item1.model_description }}</span>
          </div>
          <div class="margin20px"></div>
        </div>
      </div>
    </el-drawer>
    <el-dialog title="保存为模板"
               :visible.sync="useraddmodalshow"
               width="30%"
               destroy-on-close>
      <div class="savemodel"
           style="width: 100%">
        <el-form :model="usermodal">
          <el-form-item label="模板名称">
            <el-input v-model="usermodal.name"
                      autocomplete="off"></el-input>
          </el-form-item>
          <div class="height10border"></div>
          <el-form-item label="模板描述">
            <el-input type="textarea"
                      :rows="2"
                      placeholder="请输入内容"
                      v-model="usermodal.textarea"> </el-input>
          </el-form-item>
          <div class="height10border"></div>
          <el-form-item label="是否公开模板">
            <template>
              <el-radio v-model="usermodal.ispublic"
                        label="true">是</el-radio>
              <el-radio v-model="usermodal.ispublic"
                        label="false">否</el-radio>
            </template>
          </el-form-item>
          <div class="height10border"></div>
          <div style="text-align: right"
               class="demo-drawer__footer">
            <el-button type="primary"
                       style="background: #d7dbe0 !important; color: balck !important; border: 1px solid #d7dbe0 !important"
                       @click="useraddmodalshow = fasle">取消</el-button>
            <el-button type="primary"
                       @click="submitmodal">确认提交</el-button>
          </div>
        </el-form>
      </div>
    </el-dialog>
    <el-dialog title="选择添加的基金池"
               :visible.sync="addfundvis"
               width="20%"
               destroy-on-close>
      基金代码:<br />
      <el-input type="text"
                :disabled="true"
                :value="choosefundid"
                label="基金代码"></el-input> 基金名称:<br />
      <el-input type="text"
                :disabled="true"
                :value="choosefundname"
                label="基金名称"></el-input>
      基金池：<br />
      <el-select style="width: 100%"
                 v-model="choosedpool"
                 placeholder="请选择您的基金池">
        <el-option v-for="item in options"
                   :key="item.value"
                   :label="item.label"
                   :value="item.value"> </el-option>
      </el-select>
      <br />理由:<br />
      <el-input type="textarea"
                v-model="choosereason"
                label="选择的理由"></el-input>
      <span slot="footer"
            class="dialog-footer">
        <el-button type="primary"
                   @click="saveEdit(form)">提 交</el-button>
      </span>
    </el-dialog>
    <poolC ref="poolC"></poolC>
    <portC ref="portC"></portC>
  </div>
</template>
<script>
import { alphaGo } from '@/assets/js/alpha_type.js';
import alphachoosepool from './components/alphachoosepool.vue';
import { fontSize } from '@/assets/js/echartsrpxtorem'; //注意路径
import selecterinput from './components/selecterinpubox';
import selecterinputboxsignal from './components/selecterinpuboxsignal';
import tempbasket from './components/tempbasket';
import axios from '@/api/index';
import poolC from '@/components/components/components/poolcreate/index.vue';
import { TypeMsg } from '@/api/pages/SystemAlpha.js';
import portC from '@/components/components/components/portfoliocreat/index.vue';
import createdcomb from './components/createzuhealpha';
export default {
  components: { tempbasket, selecterinput, selecterinputboxsignal, createdcomb, alphachoosepool, poolC, portC },

  props: [],
  data () {
    return {
      addzuheflag: false,
      fundlist: [],
      dataobj: [],
      fundlisttemp: [],
      useraddmodalshow: false,
      vismodel: false,
      usermodal: {
        name: '',
        textarea: '',
        ispublic: 'false'
      },
      listar: [],
      showmodel: true,
      gridData: [],
      options: [
        {
          value: '选项1',
          label: '池子1'
        },
        {
          value: '选项2',
          label: '池子2'
        },
        {
          value: '选项3',
          label: '池子3'
        },
        {
          value: '选项4',
          label: '池子4'
        },
        {
          value: '选项5',
          label: '池子5'
        }
      ],
      showfengxian: false,
      showfengxian1: false,
      showfengxian2: false,
      showfengxian3: false,
      showfengxian4: false,
      showfengxian5: false,
      optionssinkdown: [
        {
          value: '算法估计',
          label: '算法估计',
          children: [
            {
              value: '不下沉',
              label: '不下沉'
            },
            {
              value: '稍许下沉',
              label: '稍许下沉'
            },
            {
              value: '下沉',
              label: '下沉'
            }
          ]
        },
        {
          value: '报告披露',
          label: '报告披露',
          children: [
            {
              value: '不下沉',
              label: '不下沉'
            },
            {
              value: '稍许下沉',
              label: '稍许下沉'
            },
            {
              value: '下沉',
              label: '下沉'
            }
          ]
        }
      ],
      optionsdure: [
        {
          value: '算法估计',
          label: '算法估计',
          children: [
            {
              value: '短',
              label: '短（1.5年以下）'
            },
            {
              value: '中',
              label: '中（1.5年-4年）'
            },
            {
              value: '长',
              label: '长（4年以上）'
            }
          ]
        },
        {
          value: '报告披露',
          label: '报告披露',
          children: [
            {
              value: '短',
              label: '短（1.5年以下）'
            },
            {
              value: '中',
              label: '中（1.5年-4年）'
            },
            {
              value: '长',
              label: '长（4年以上）'
            }
          ]
        }
      ],
      dureOptions: [
        {
          value: '短',
          label: '短'
        },
        {
          value: '中',
          label: '中'
        },
        {
          value: '长',
          label: '长'
        }
      ],
      showej: false,
      temp1: [],
      temp3: [],
      temp4: [],
      temp5: [],
      hgchoose: [],
      czchoose: [],
      dpchoose: [],
      swchoose: [],
      czshow: false,
      dpshow: false,
      swshow: false,
      hangyeshow: false,
      fenggeshow: false,
      kuanjishow: true,
      choosedpool: null,
      choosereason: null,
      choosefundid: null,
      choosefundname: null,
      option: [
        {
          value: 'now',
          label: '近期表现',
          children: [
            {
              value: '1m',
              label: '一月'
            },
            {
              value: '3m',
              label: '三月'
            },
            {
              value: '6m',
              label: '六月'
            },
            {
              value: '1y',
              label: '一年'
            },
            {
              value: '2y',
              label: '两年'
            }
          ]
        }
      ],
      addfundvis: false,
      alldata: [],
      tableData: [],
      pageTotal: null,
      pageIndex: 1,
      pageSize: 20,
      showmsgsloading: false,
      selectvalue1: [],
      selectvalue2: [],
      selectvalue3: [],
      selectvalue4: [],
      selectvalue5: [],
      selectvalue6: [],
      fundname: null,
      formData: {
        nowdurelong: null,
        nowsinkdown: null,
        scale: {
          from: null,
          end: null
        },
        long: {
          from: null,
          end: null
        },
        fundchara: [],
        sinkdo: 0,
        sink: null,
        issink: null,
        maxmoney: null,
        insuranceincome: [
          {
            id: 1,
            date: ['now', '3y'],
            value1: null,
            rank: '100'
          },
          {
            id: 2,
            date: ['now', '3y'],
            value1: null,
            value2: null,
            rank: '100'
          },
          {
            id: 3,
            date: ['now', '3y'],
            value1: null,
            value2: null,
            rank: '100'
          },
          {
            id: 4,
            date: ['now', '3y'],
            value1: null,
            rank: '100'
          },
          {
            id: 5,
            date: ['now', '3y'],
            value1: null,
            rank: '100'
          },
          {
            id: 6,
            date: ['now', '3y'],
            value1: null,
            rank: '100'
          }
        ],

        Marketcycle: {
          value1: 0,
          value2: 0,
          value3: 0,
          value4: 0
        },
        Growth: {
          value1: 0,
          value2: 0
        },
        bigsmall: {
          value1: 0,
          value2: 0
        },
        shenwan: {
          value1: 0,
          value2: 0,
          value3: 0,
          value4: 0,
          value5: 0,
          value6: 0,
          value7: 0,
          value8: 0,
          value9: 0,
          value10: 0,
          value11: 0,
          value12: 0,
          value13: 0,
          value14: 0,
          value15: 0,
          value16: 0,
          value17: 0,
          value18: 0,
          value19: 0,
          value20: 0,
          value21: 0,
          value22: 0,
          value23: 0,
          value24: 0,
          value25: 0,
          value26: 0,
          value27: 0,
          value28: 0
        }
      },
      poollist: [],
      field102Options: [
        {
          label: '不下沉',
          value: '不下沉'
        },
        {
          label: '轻度下沉',
          value: '轻度下沉'
        },
        {
          label: '灵活下沉',
          value: '灵活下沉'
        },
        {
          label: '下沉',
          value: '下沉'
        }
      ],
      fundcharaOptions: [
        {
          label: '不限',
          value: '不限'
        },
        {
          label: '开放',
          value: '开放'
        },
        {
          label: '定开',
          value: '定开'
        },
        {
          label: '封闭',
          value: '封闭'
        }
      ],
      sinkdoOptions: [
        {
          label: '稳定',
          value: '稳定'
        },
        {
          label: '温和变化',
          value: '温和变化'
        },
        {
          label: '大调整',
          value: '大调整'
        }
      ],
      field103Options: [
        {
          label: '是',
          value: '是'
        },
        {
          label: '否',
          value: '否'
        }
      ],
      option1: [
        { value: 'now', label: '近期表现', children: [] },
        {
          value: 'from',
          label: '从那时起',
          children: [
            {
              value: '2015-08-26',
              label: '2015-08-26'
            }
          ]
        }
      ],
      option2: [
        { value: 'now', label: '近期表现', children: [] },
        {
          value: 'from',
          label: '从那时起',
          children: [
            {
              value: '2015-08-26',
              label: '2015-08-26'
            }
          ]
        }
      ],
      option3: [
        { value: 'now', label: '近期表现', children: [] },
        {
          value: 'from',
          label: '从那时起',
          children: [
            {
              value: '2015-08-26',
              label: '2015-08-26'
            }
          ]
        }
      ],
      option4: [
        { value: 'now', label: '近期表现', children: [] },
        {
          value: 'from',
          label: '从那时起',
          children: [
            {
              value: '2015-08-26',
              label: '2015-08-26'
            }
          ]
        }
      ],
      option5: [
        { value: 'now', label: '近期表现', children: [] },
        {
          value: 'from',
          label: '从那时起',
          children: [
            {
              value: '2015-08-26',
              label: '2015-08-26'
            }
          ]
        }
      ],
      option6: [
        { value: 'now', label: '近期表现', children: [] },
        {
          value: 'from',
          label: '从那时起',
          children: [
            {
              value: '2015-08-26',
              label: '2015-08-26'
            }
          ]
        }
      ],
      field104Options: [
        {
          label: '收益', //波动率：区间数据（默认下届为0）
          value: 1
        },
        {
          label: '最⼤回撤', //区间数据（默认下届为0）
          value: 2
        },
        {
          label: '波动率', //区间数据（默认下届为0）
          value: 3
        },
        {
          label: '夏普率', //区间数据（默认下届为0）
          value: 4
        },
        {
          label: '卡码率', //区间数据（默认下届为0.5，上届为1）
          value: 5
        },
        {
          label: '胜率', //区间数据（默认下届为0.5，上届为1）
          value: 6
        }
      ],
      field105Options: [
        {
          label: '二级市场周期',
          value: 1
        }
      ],
      field107Options: [
        {
          label: '松货币vs紧货币',
          value: 1
        },
        {
          label: '松信用vs紧信用',
          value: 2
        }
      ],
      field108Options: [
        {
          label: '申万一级行业',
          value: 1
        }
      ],
      is_request_active: false
    };
  },
  filters: {
    fix6 (value) {
      return value.substring(0, 10);
    },
    fix3 (value) {
      return parseInt(value * 1000) / 1000;
    },
    fix2p (value) {
      if (value === '' || value == null || value == 'nan' || value == '--' || value == 'None') {
        return '—';
      } else {
        return (value * 100).toFixed(2) + '%';
      }
    },
    isDefault (value) {
      return value == '--' ? '' : value;
    }
  },
  computed: {},
  watch: {
    swchoose (val) {
      if (val.indexOf('非银金融') < 0) {
        this.$set(this.formData.shenwan, 'value1', 0);
      }
      if (val.indexOf('农林牧渔') < 0) {
        this.$set(this.formData.shenwan, 'value2', 0);
      }
      if (val.indexOf('国防军工') < 0) {
        this.$set(this.formData.shenwan, 'value3', 0);
      }
      if (val.indexOf('采掘') < 0) {
        this.$set(this.formData.shenwan, 'value4', 0);
      }
      if (val.indexOf('传媒') < 0) {
        this.$set(this.formData.shenwan, 'value5', 0);
      }
      if (val.indexOf('电子') < 0) {
        this.$set(this.formData.shenwan, 'value6', 0);
      }
      if (val.indexOf('休闲服务') < 0) {
        this.$set(this.formData.shenwan, 'value7', 0);
      }
      if (val.indexOf('房地产') < 0) {
        this.$set(this.formData.shenwan, 'value8', 0);
      }
      if (val.indexOf('电子设备') < 0) {
        this.$set(this.formData.shenwan, 'value9', 0);
      }
      if (val.indexOf('钢铁') < 0) {
        this.$set(this.formData.shenwan, 'value10', 0);
      }
      if (val.indexOf('通信') < 0) {
        this.$set(this.formData.shenwan, 'value11', 0);
      }
      if (val.indexOf('建筑材料') < 0) {
        this.$set(this.formData.shenwan, 'value12', 0);
      }
      if (val.indexOf('综合') < 0) {
        this.$set(this.formData.shenwan, 'value13', 0);
      }
      if (val.indexOf('公用事业') < 0) {
        this.$set(this.formData.shenwan, 'value14', 0);
      }
      if (val.indexOf('机械设备') < 0) {
        this.$set(this.formData.shenwan, 'value15', 0);
      }
      if (val.indexOf('商业贸易') < 0) {
        this.$set(this.formData.shenwan, 'value16', 0);
      }
      if (val.indexOf('纺织服装') < 0) {
        this.$set(this.formData.shenwan, 'value17', 0);
      }
      if (val.indexOf('化工') < 0) {
        this.$set(this.formData.shenwan, 'value18', 0);
      }
      if (val.indexOf('建筑装饰') < 0) {
        this.$set(this.formData.shenwan, 'value19', 0);
      }
      if (val.indexOf('银行') < 0) {
        this.$set(this.formData.shenwan, 'value20', 0);
      }
      if (val.indexOf('食品饮料') < 0) {
        this.$set(this.formData.shenwan, 'value21', 0);
      }
      if (val.indexOf('交通运输') < 0) {
        this.$set(this.formData.shenwan, 'value22', 0);
      }
      if (val.indexOf('汽车') < 0) {
        this.$set(this.formData.shenwan, 'value23', 0);
      }
      if (val.indexOf('轻工制造') < 0) {
        this.$set(this.formData.shenwan, 'value24', 0);
      }
      if (val.indexOf('医药生物') < 0) {
        this.$set(this.formData.shenwan, 'value25', 0);
      }
      if (val.indexOf('有色金属') < 0) {
        this.$set(this.formData.shenwan, 'value26', 0);
      }
      if (val.indexOf('计算机') < 0) {
        this.$set(this.formData.shenwan, 'value27', 0);
      }
      if (val.indexOf('家用电器') < 0) {
        this.$set(this.formData.shenwan, 'value28', 0);
      }
    },
    czchoose (val) {
      if (val) {
        if (val.indexOf('松货币') < 0) {
          this.$set(this.formData.Growth, 'value1', 0);
        }
        if (val.indexOf('紧货币') < 0) {
          this.$set(this.formData.Growth, 'value2', 0);
        }
      }
    },
    dpchoose (val) {
      if (val) {
        if (val.indexOf('松信用') < 0) {
          this.$set(this.formData.bigsmall, 'value1', 0);
        }
        if (val.indexOf('紧信用') < 0) {
          this.$set(this.formData.bigsmall, 'value2', 0);
        }
      }
    },
    hgchoose (val) {
      if (val) {
        if (val.indexOf('牛市') < 0) {
          this.$set(this.formData.Marketcycle, 'value1', 0);
        }
        if (val.indexOf('熊市') < 0) {
          this.$set(this.formData.Marketcycle, 'value2', 0);
        }
        if (val.indexOf('震荡') < 0) {
          this.$set(this.formData.Marketcycle, 'value3', 0);
        }
        if (val.indexOf('无观点') < 0) {
          this.$set(this.formData.Marketcycle, 'value4', 0);
        }
      }
    },
    temp1 (val) {
      this.showfengxian = false;
      this.showfengxian1 = false;
      this.showfengxian2 = false;
      this.showfengxian3 = false;
      this.showfengxian4 = false;
      this.showfengxian5 = false;
      ////console.log(val)
      if (val.indexOf(1) > -1) {
        this.showfengxian = true;
        //////console.log('波动率')
      } else {
        this.$set(this.formData.insuranceincome[0], 'date', ['now', '3y']);
        this.$set(this.formData.insuranceincome[0], 'value1', null);
        this.$set(this.formData.insuranceincome[0], 'rank', '100');
      }
      if (val.indexOf(2) > -1) {
        this.showfengxian1 = true;
        //////console.log('波动率')
      } else {
        this.$set(this.formData.insuranceincome[1], 'date', ['now', '3y']);
        this.$set(this.formData.insuranceincome[1], 'value1', null);
        this.$set(this.formData.insuranceincome[1], 'value2', null);
        this.$set(this.formData.insuranceincome[1], 'rank', '100');
      }
      if (val.indexOf(3) > -1) {
        this.showfengxian2 = true;
        //////console.log('波动率')
      } else {
        this.$set(this.formData.insuranceincome[2], 'date', ['now', '3y']);
        this.$set(this.formData.insuranceincome[2], 'value1', null);
        this.$set(this.formData.insuranceincome[2], 'value2', null);
        this.$set(this.formData.insuranceincome[2], 'rank', '100');
      }
      if (val.indexOf(4) > -1) {
        this.showfengxian3 = true;
        //////console.log('波动率')
      } else {
        this.$set(this.formData.insuranceincome[3], 'date', ['now', '3y']);
        this.$set(this.formData.insuranceincome[3], 'value1', null);
        this.$set(this.formData.insuranceincome[3], 'rank', '100');
      }
      if (val.indexOf(5) > -1) {
        this.showfengxian4 = true;
        //////console.log('波动率')
      } else {
        this.$set(this.formData.insuranceincome[4], 'date', ['now', '3y']);
        this.$set(this.formData.insuranceincome[4], 'value1', null);
        this.$set(this.formData.insuranceincome[4], 'rank', '100');
      }
      if (val.indexOf(6) > -1) {
        this.showfengxian5 = true;
        //////console.log('波动率')
      } else {
        this.$set(this.formData.insuranceincome[5], 'date', ['now', '3y']);
        this.$set(this.formData.insuranceincome[5], 'value1', null);
        this.$set(this.formData.insuranceincome[5], 'rank', '100');
      }
    },
    temp3 (val) {
      this.showej = false;

      if (val.indexOf(1) > -1) {
        this.showej = true;
      } else {
        this.hgchoose = [];
        this.$set(this.formData.Marketcycle, 'value1', 0);
        this.$set(this.formData.Marketcycle, 'value2', 0);
        this.$set(this.formData.Marketcycle, 'value3', 0);
        this.$set(this.formData.Marketcycle, 'value4', 0);
      }
    },
    temp4 (val) {
      this.czshow = false;
      this.dpshow = false;
      if (val) {
        if (val.indexOf(1) > -1) {
          this.czshow = true;
        } else {
          this.czchoose = [];
          this.$set(this.formData.Growth, 'value1', 0);
          this.$set(this.formData.Growth, 'value2', 0);
        }
        if (val.indexOf(2) > -1) {
          this.dpshow = true;
        } else {
          this.dpchoose = [];
          this.$set(this.formData.bigsmall, 'value1', 0);
          this.$set(this.formData.bigsmall, 'value2', 0);
        }
      }
    },
    temp5 (val) {
      this.swshow = false;
      if (val.indexOf(1) > -1) {
        this.swshow = true;
      } else {
        this.swchoose = [];
        this.$set(this.formData.shenwan, 'value1', 0);
        this.$set(this.formData.shenwan, 'value2', 0);
        this.$set(this.formData.shenwan, 'value3', 0);
        this.$set(this.formData.shenwan, 'value4', 0);
        this.$set(this.formData.shenwan, 'value5', 0);
        this.$set(this.formData.shenwan, 'value6', 0);
        this.$set(this.formData.shenwan, 'value7', 0);
        this.$set(this.formData.shenwan, 'value8', 0);
        this.$set(this.formData.shenwan, 'value9', 0);
        this.$set(this.formData.shenwan, 'value10', 0);
        this.$set(this.formData.shenwan, 'value11', 0);
        this.$set(this.formData.shenwan, 'value12', 0);
        this.$set(this.formData.shenwan, 'value13', 0);
        this.$set(this.formData.shenwan, 'value14', 0);
        this.$set(this.formData.shenwan, 'value15', 0);
        this.$set(this.formData.shenwan, 'value16', 0);
        this.$set(this.formData.shenwan, 'value17', 0);
        this.$set(this.formData.shenwan, 'value18', 0);
        this.$set(this.formData.shenwan, 'value19', 0);
        this.$set(this.formData.shenwan, 'value20', 0);
        this.$set(this.formData.shenwan, 'value21', 0);
        this.$set(this.formData.shenwan, 'value22', 0);
        this.$set(this.formData.shenwan, 'value23', 0);
        this.$set(this.formData.shenwan, 'value24', 0);
        this.$set(this.formData.shenwan, 'value25', 0);
        this.$set(this.formData.shenwan, 'value26', 0);
        this.$set(this.formData.shenwan, 'value27', 0);
        this.$set(this.formData.shenwan, 'value28', 0);
      }
    }
  },
  created () {
    this.getmodals();
    this.getselectoptions();
    if (JSON.stringify(this.$route.query) != '{}') {
      if (this.$route.query.args != null) {
        axios
          .get(that.$baseUrl + '/system/alpha/HomePage/modelquery/?id=' + this.$route.query.args)
          .then((res) => {
            //console.log(res.data);
            // that.flagargs = 1

            that.setshuju(res.data.data.model_query[0].model_args);
          })
          .catch((error) => {
            //that.$message('数据缺失');
          });
      }
    } else {
      if (this.localStorage.getItem('temp4purebond') != null && this.localStorage.getItem('temp4purebond') != 'null') {
        this.temp4 = JSON.parse(this.localStorage.getItem('temp4purebond'));
      }
      if (this.localStorage.getItem('czchoosepurebond') != null && this.localStorage.getItem('czchoosepurebond') != 'null') {
        this.czchoose = JSON.parse(this.localStorage.getItem('czchoosepurebond'));
      }
      if (this.localStorage.getItem('dpchoosepurebond') != null && this.localStorage.getItem('dpchoosepurebond') != 'null') {
        this.dpchoose = JSON.parse(this.localStorage.getItem('dpchoosepurebond'));
      }
      if (this.localStorage.getItem('formDatapurebond') != null && this.localStorage.getItem('formDatapurebond') != 'null') {
        this.formData = JSON.parse(this.localStorage.getItem('formDatapurebond'));
      }
      if (this.localStorage.getItem('temp1purebond') != null && this.localStorage.getItem('temp1purebond') != 'null') {
        this.temp1 = JSON.parse(this.localStorage.getItem('temp1purebond'));
      }
      let that = this;
      if (this.localStorage.getItem('alldatapurebond') != null && this.localStorage.getItem('alldatapurebond') != 'null') {
        this.alldata = JSON.parse(this.localStorage.getItem('alldatapurebond'));
        that.tableData = this.alldata.slice(0, 19);
        that.pageTotal = this.alldata.length;
      } else {
        // let that = this
        that.showmsgsloading = true;
        if (that.is_request_active) return;
        that.is_request_active = true;
        axios
          .post(this.$baseUrl + '/system/alpha/other_bond_filter/', { fund_type: 'purebond' })
          .then((res) => {
            that.is_request_active = false;
            that.showmsgsloading = false;
            that.alldata = res.data.data;
            that.tableData = res.data.data.slice(0, 19);
            that.pageTotal = res.data.data.length;
            try {
              this.localStorage.setItem('formDatapurebond', JSON.stringify(that.formData));
              this.localStorage.setItem('temp1purebond', JSON.stringify(that.temp1));
              this.localStorage.setItem('alldatapurebond', JSON.stringify(that.alldata));
              this.localStorage.setItem('temp4purebond', JSON.stringify(that.temp4));
              this.localStorage.setItem('czchoosepurebond', JSON.stringify(that.czchoose));
              this.localStorage.setItem('dpchoosepurebond', JSON.stringify(that.dpchoose));
            } catch (err) { }
          })
          .catch((err) => {
            that.$message('筛选失败');
          });
      }
    }
    let that = this;
    axios
      .get(that.$baseUrl + '/system/alpha/filter_risk_future/')
      .then((result) => {
        let res = result.data;
        for (let i = 0; i < res.data.volatility.length; i++) {
          if (res.data.volatility[i] == '1w') that.option3[0].children.push({ value: '1w', label: '一周' });
          if (res.data.volatility[i] == '2w') that.option3[0].children.push({ value: '2w', label: '两周' });
          if (res.data.volatility[i] == '1m') that.option3[0].children.push({ value: '1m', label: '一月' });
          if (res.data.volatility[i] == '2m') that.option3[0].children.push({ value: '2m', label: '两月' });
          if (res.data.volatility[i] == '1q') that.option3[0].children.push({ value: '1q', label: '一季' });
          if (res.data.volatility[i] == '2q') that.option3[0].children.push({ value: '2q', label: '两季' });
          if (res.data.volatility[i] == '1y') that.option3[0].children.push({ value: '1y', label: '一年' });
          if (res.data.volatility[i] == '2y') that.option3[0].children.push({ value: '2y', label: '两年' });
          if (res.data.volatility[i] == '3y') that.option3[0].children.push({ value: '3y', label: '三年' });
          if (res.data.volatility[i] == '5y') that.option3[0].children.push({ value: '5y', label: '五年' });
        }
        for (let i = 0; i < res.data.maxdrawdown.length; i++) {
          if (res.data.maxdrawdown[i] == '1w') that.option2[0].children.push({ value: '1w', label: '一周' });
          if (res.data.maxdrawdown[i] == '2w') that.option2[0].children.push({ value: '2w', label: '两周' });
          if (res.data.maxdrawdown[i] == '1m') that.option2[0].children.push({ value: '1m', label: '一月' });
          if (res.data.maxdrawdown[i] == '2m') that.option2[0].children.push({ value: '2m', label: '两月' });
          if (res.data.maxdrawdown[i] == '1q') that.option2[0].children.push({ value: '1q', label: '一季' });
          if (res.data.maxdrawdown[i] == '2q') that.option2[0].children.push({ value: '2q', label: '两季' });
          if (res.data.maxdrawdown[i] == '1y') that.option2[0].children.push({ value: '1y', label: '一年' });
          if (res.data.maxdrawdown[i] == '2y') that.option2[0].children.push({ value: '2y', label: '两年' });
          if (res.data.maxdrawdown[i] == '3y') that.option2[0].children.push({ value: '3y', label: '三年' });
          if (res.data.maxdrawdown[i] == '5y') that.option2[0].children.push({ value: '5y', label: '五年' });
        }
        for (let i = 0; i < res.data.cum_return.length; i++) {
          if (res.data.cum_return[i] == '1w') that.option1[0].children.push({ value: '1w', label: '一周' });
          if (res.data.cum_return[i] == '2w') that.option1[0].children.push({ value: '2w', label: '两周' });
          if (res.data.cum_return[i] == '1m') that.option1[0].children.push({ value: '1m', label: '一月' });
          if (res.data.cum_return[i] == '2m') that.option1[0].children.push({ value: '2m', label: '两月' });
          if (res.data.cum_return[i] == '1q') that.option1[0].children.push({ value: '1q', label: '一季' });
          if (res.data.cum_return[i] == '2q') that.option1[0].children.push({ value: '2q', label: '两季' });
          if (res.data.cum_return[i] == '1y') that.option1[0].children.push({ value: '1y', label: '一年' });
          if (res.data.cum_return[i] == '2y') that.option1[0].children.push({ value: '2y', label: '两年' });
          if (res.data.cum_return[i] == '3y') that.option1[0].children.push({ value: '3y', label: '三年' });
          if (res.data.cum_return[i] == '5y') that.option1[0].children.push({ value: '5y', label: '五年' });
        }
        for (let i = 0; i < res.data.sharpe.length; i++) {
          if (res.data.sharpe0[i] == '1w') that.option4[0].children.push({ value: '1w', label: '一周' });
          if (res.data.sharpe0[i] == '2w') that.option4[0].children.push({ value: '2w', label: '两周' });
          if (res.data.sharpe0[i] == '1m') that.option4[0].children.push({ value: '1m', label: '一月' });
          if (res.data.sharpe0[i] == '2m') that.option4[0].children.push({ value: '2m', label: '两月' });
          if (res.data.sharpe0[i] == '1q') that.option4[0].children.push({ value: '1q', label: '一季' });
          if (res.data.sharpe0[i] == '2q') that.option4[0].children.push({ value: '2q', label: '两季' });
          if (res.data.sharpe0[i] == '1y') that.option4[0].children.push({ value: '1y', label: '一年' });
          if (res.data.sharpe0[i] == '2y') that.option4[0].children.push({ value: '2y', label: '两年' });
          if (res.data.sharpe0[i] == '3y') that.option4[0].children.push({ value: '3y', label: '三年' });
          if (res.data.sharpe0[i] == '5y') that.option4[0].children.push({ value: '5y', label: '五年' });
        }

        for (let i = 0; i < res.data.calmar.length; i++) {
          if (res.data.calmar[i] == '1w') that.option5[0].children.push({ value: '1w', label: '一周' });
          if (res.data.calmar[i] == '2w') that.option5[0].children.push({ value: '2w', label: '两周' });
          if (res.data.calmar[i] == '1m') that.option5[0].children.push({ value: '1m', label: '一月' });
          if (res.data.calmar[i] == '2m') that.option5[0].children.push({ value: '2m', label: '两月' });
          if (res.data.calmar[i] == '1q') that.option5[0].children.push({ value: '1q', label: '一季' });
          if (res.data.calmar[i] == '2q') that.option5[0].children.push({ value: '2q', label: '两季' });
          if (res.data.calmar[i] == '1y') that.option5[0].children.push({ value: '1y', label: '一年' });
          if (res.data.calmar[i] == '2y') that.option5[0].children.push({ value: '2y', label: '两年' });
          if (res.data.calmar[i] == '3y') that.option5[0].children.push({ value: '3y', label: '三年' });
          if (res.data.calmar[i] == '5y') that.option5[0].children.push({ value: '5y', label: '五年' });
        }
        for (let i = 0; i < res.data.calmar.length; i++) {
          if (res.data.calmar[i] == '1w') that.option6[0].children.push({ value: '1w', label: '一周' });
          if (res.data.calmar[i] == '2w') that.option6[0].children.push({ value: '2w', label: '两周' });
          if (res.data.calmar[i] == '1m') that.option6[0].children.push({ value: '1m', label: '一月' });
          if (res.data.calmar[i] == '2m') that.option6[0].children.push({ value: '2m', label: '两月' });
          if (res.data.calmar[i] == '1q') that.option6[0].children.push({ value: '1q', label: '一季' });
          if (res.data.calmar[i] == '2q') that.option6[0].children.push({ value: '2q', label: '两季' });
          if (res.data.calmar[i] == '1y') that.option6[0].children.push({ value: '1y', label: '一年' });
          if (res.data.calmar[i] == '2y') that.option6[0].children.push({ value: '2y', label: '两年' });
          if (res.data.calmar[i] == '3y') that.option6[0].children.push({ value: '3y', label: '三年' });
          if (res.data.calmar[i] == '5y') that.option6[0].children.push({ value: '5y', label: '五年' });
        }
      })
      .catch((error) => { });
  },
  mounted () { },
  methods: {
    async gotocompare () {
      let tempcode = '';
      let tempname = '';
      let temptype = null;
      for (let i = 0; i < this.fundlisttemp.length; i++) {
        tempcode = tempcode + this.fundlisttemp[i].code + ',';
        tempname = tempname + this.fundlisttemp[i].name + ',';
      }
      tempcode = tempcode.slice(0, tempcode.length - 1);
      tempname = tempname.slice(0, tempname.length - 1);
      let data = await TypeMsg({ code: tempcode });
      if (data) {
        // //console.log(data)
        if (data.data) {
          if (data.data.length == 0) {
            this.$router.push({
              path: '/fundcompareDiff',
              query: {
                id: tempcode,
                type: '',
                types: data.type.join(','),
                name: tempname
              }
            });
            // this.$message.error('请选择具有相同类型的基金进行比较');
          } else if (data.data.length == 1) {
            temptype = data.data[0];
            if (
              temptype == 'bond' ||
              temptype == 'cbond' ||
              temptype == 'purebond' ||
              temptype == 'bill' ||
              temptype.indexOf('equity') >= 0 ||
              temptype == 'obond'
            ) {
              this.$router.push({
                path: '/fundcompare',
                query: {
                  id: tempcode,
                  type: temptype,
                  name: tempname
                }
              });
            } else {
              this.$message('暂时只提供主动权益，二级债，债券类产品的比较');
            }
          } else if (data.data.length > 1) {
            this.$router.push({
              path: '/fundcompareDiff',
              query: {
                id: tempcode,
                type: '',
                types: data.type.join(','),
                name: tempname
              }
            });
            // this.$message.error('请选择具有相同类型的基金进行比较');
            // this.showitem = true
            // this.options = []
            // for(let i = 0; i < data.data.length; i++){
            //     this.options.push({value:data.data[i],label:data.data[i]})
            // }
          }
        }
      }
    },
    creteportfolio () {
      let temp = [];
      if (this.fundlisttemp?.length == 0) {
        temp = this.alldata;
      } else {
        temp = this.fundlisttemp;
      }
      this.fundlist = this.tablelistzuhe;
      this.$refs.portC.show(temp, 'alpha');
    },
    cretezuhequ () {
      //console.log('22321');
      let temp = [];
      if (this.fundlisttemp?.length == 0) {
        temp = this.alldata;
      } else {
        temp = this.fundlisttemp;
      }
      // this.addzuheflag = true;
      this.fundlist = this.tablelistzuhe;
      this.dataobj = {
        clicktype: true,
        ftype: 'bill',
        ismanager: false,
        formData: this.formData,
        temp1: this.temp1,
        czchoose: this.czchoose,
        dpchoose: this.dpchoose,
        temp4: this.temp4
      };
      // console.log('xx');
      this.$refs.poolC.show(temp, this.dataobj, 'alpha');
    },
    getfontSize (val) {
      return fontSize(val);
    },
    getRowKey: function (row) {
      return row.code;
    },
    handleSelectzuhe (val) {
      if (val.length > 0) {
        this.disabledfalg = false;
      } else {
        this.disabledfalg = true;
      }

      this.fundlisttemp = val;
    },
    changepool (val) {
      this.poollist = val;
    },
    // cretezuhequ() {
    // 	// //console.log('22321')
    // 	this.addzuheflag = true;
    // 	this.fundlist = this.tablelistzuhe;
    // 	this.dataobj = {
    // 		clicktype: true,
    // 		ftype: 'purebond',
    // 		ismanager: false,
    // 		formData: this.formData,
    // 		temp1: this.temp1,
    // 		czchoose: this.czchoose,
    // 		dpchoose: this.dpchoose,
    // 		temp4: this.temp4
    // 	};
    // },
    deletemodel (val) {
      let that = this;
      axios
        .delete(that.$baseUrl + '/Tools/delete_model/?type=purebond&ismanager=false&model_name=' + val)
        .then((res) => {
          that.getmodals();
          that.$message('删除成功');
          // that.useraddmodalshow =false
        })
        .catch((err) => {
          //  that.$message('失败')
          ////console.log(err)
          //that.$message('数据缺失')
        });
    },
    changepos () {
      // //console.log('herein')
      let temparr = this.listar;
      let i = temparr.length;
      while (i) {
        let j = Math.floor(Math.random() * i--);
        [temparr[j], temparr[i]] = [temparr[i], temparr[j]];
      }
      this.listar = temparr;

      this.showmodel = false;
      this.showmodel = true;
      // //console.log(this.listar)
    },
    getmodals () {
      //获取模板
      let that = this;
      axios
        .get(that.$baseUrl + '/Tools/query_model/?type=purebond&ismanager=false')
        .then((res) => {
          //console.log('模板');
          //console.log(res.data);
          that.gridData = res.data.data.self_data;
          //  that.gridData =  res.data.owl_data
          that.listar = res.data.data.owl_data;
          if (that.listar.length > 6) {
            that.listar = that.listar.slice(0, 6);
          }
          // that.useraddmodalshow =false
          // let listar = []
          // for(let i = 0 ; i <6 ; i++){
          //     let m  = parseInt(Math.random()*(res.data.owl_data.length-0+1) + 6);
          //     while(listar.indexOf(m)>=0){
          //         m  = parseInt(Math.random()*(res.data.owl_data.length-0+1) + 6);
          //     }
          //     listar.push(m)
          // }
          // //console.log(listar)
        })
        .catch((err) => {
          //  that.$message('失败')
          ////console.log(err)
          //that.$message('数据缺失')
        });
    },
    setshuju (val) {
      // //console.log('GOGOGO')
      this.formData = val.formData;
      this.temp1 = val.temp1;
      this.czchoose = val.czchoose;
      this.dpchoose = val.dpchoose;
      this.temp4 = val.temp4;
      this.vismodel = false;
      this.submitForm();
    },
    submitmodal () {
      if (this.usermodal.name == null || this.usermodal.name == '') {
        that.$message('请输入模板名称');
      } else {
        let temp = null;
        let that = this;
        let formData = this.formData;
        for (const key in formData) {
          if (formData[key] == null || formData[key] == 'None') {
            formData[key] = '';
          } else {
            if (typeof formData[key] == 'object') {
              if (formData[key]?.length) {
                for (let index = 0; index < formData[key].length; index++) {
                  for (const key2 in formData[key][index]) {
                    if (formData[key][index][key2] == null || formData[key][index][key2] == 'None') {
                      formData[key][index][key2] = '';
                    }
                  }
                }
              } else {
                for (const key1 in formData[key]) {
                  if (formData[key][key1] == null || formData[key][key1] == 'None') {
                    formData[key][key1] = '';
                  }
                }
              }
            }
          }
        }
        temp = {
          formData,
          temp1: this.temp1,
          czchoose: this.czchoose,
          dpchoose: this.dpchoose,
          temp4: this.temp4
        };
        axios
          .post(that.$baseUrl + '/Tools/save_model/', {
            type: 'purebond',
            ismanager: 'false',
            model_name: this.usermodal.name,
            model_description: this.usermodal.textarea,
            ispublic: this.usermodal.ispublic,
            model_args: temp
          })
          .then((res) => {
            that.$message('保存模板成功');
            that.getmodals();
            that.useraddmodalshow = false;
          })
          .catch((err) => {
            //  that.$message('失败')
            ////console.log(err)
            //that.$message('数据缺失')
          });
      }
    },
    getselectoptions () {
      let that = this;
      axios
        .get(that.$baseUrl + '/system/alpha/measure_section_range/?type=purebond')
        .then((result) => {
          let res = result.data;
          //console.log(res.data);
          //console.log('listdown');
          that.selectvalue1 = [];
          that.selectvalue2 = [];
          that.selectvalue3 = [];
          that.selectvalue4 = [];
          that.selectvalue5 = [];
          that.selectvalue6 = [];
          for (let i = 0; i < res.data.length; i++) {
            if (res.data[i].measure_id == '30') {
              that.selectvalue1.push(res.data[i]);
            } else if (res.data[i].measure_id == '34') {
              that.selectvalue2.push(res.data[i]);
            } else if (res.data[i].measure_id == '31') {
              that.selectvalue3.push(res.data[i]);
            } else if (res.data[i].measure_id == '56') {
              that.selectvalue4.push(res.data[i]);
            } else if (res.data[i].measure_id == '37') {
              that.selectvalue5.push(res.data[i]);
            } else if (res.data[i].measure_id == '37') {
              that.selectvalue6.push(res.data[i]);
            }
          }
        })
        .catch((err) => {
          //  that.$message('失败')
          ////console.log(err)
          //that.$message('数据缺失')
        });
    },
    setrank (val, index) {
      // //console.log(val)
      // //console.log(index)
      // //console.log('her1')
      if (index == 1.1) {
        this.$set(this.formData.insuranceincome[0], 'rank', val);
      } else if (index == 1.2) {
        this.$set(this.formData.insuranceincome[1], 'rank', val);
      } else if (index == 1.3) {
        this.$set(this.formData.insuranceincome[2], 'rank', val);
      } else if (index == 1.4) {
        this.$set(this.formData.insuranceincome[3], 'rank', val);
      } else if (index == 1.5) {
        this.$set(this.formData.insuranceincome[4], 'rank', val);
      } else if (index == 1.6) {
        this.$set(this.formData.insuranceincome[5], 'rank', val);
      }
      //  //console.log(this.formData.insuranceincome)
    },
    setvalue1 (val, index) {
      // //console.log(val)
      // //console.log(index)
      // //console.log('her1')
      if (index == 1.1) {
        this.$set(this.formData.insuranceincome[0], 'value1', val);
      } else if (index == 1.2) {
        this.$set(this.formData.insuranceincome[1], 'value1', val);
      } else if (index == 1.3) {
        this.$set(this.formData.insuranceincome[2], 'value1', val);
      } else if (index == 1.4) {
        this.$set(this.formData.insuranceincome[3], 'value1', val);
      } else if (index == 1.5) {
        this.$set(this.formData.insuranceincome[4], 'value1', val);
      } else if (index == 1.6) {
        this.$set(this.formData.insuranceincome[5], 'value1', val);
      }
      //console.log(this.formData.insuranceincome);
    },
    setvalue2 (val, index) {
      //console.log(val);
      //console.log(index);
      if (index == 1.1) {
        this.$set(this.formData.insuranceincome[0], 'value2', val);
      } else if (index == 1.2) {
        this.$set(this.formData.insuranceincome[1], 'value2', val);
      } else if (index == 1.3) {
        this.$set(this.formData.insuranceincome[2], 'value2', val);
      } else if (index == 1.4) {
        this.$set(this.formData.insuranceincome[3], 'value2', val);
      } else if (index == 1.5) {
        this.$set(this.formData.insuranceincome[4], 'value2', val);
      } else if (index == 1.6) {
        this.$set(this.formData.insuranceincome[5], 'value2', val);
      }
    },
    my_desc_sort (name) {
      //  ////console.log(name)
      return function (a, b) {
        if (a[name] > b[name]) {
          return -1;
        } else if (a[name] < b[name]) {
          return 1;
        } else {
          return 0;
        }
      };
    },
    my_asc_sort (name) {
      return function (a, b) {
        if (a[name] < b[name]) {
          return -1;
        } else if (a[name] > b[name]) {
          return 1;
        } else {
          return 0;
        }
      };
    },

    sort_change (column) {
      // ////console.log(column)
      // ////console.log('colum')
      this.pageIndex = 1; // return to the first page after sorting
      if (column.prop === 'code') {
        if (column.order === 'descending') {
          this.alldata = this.alldata.sort(this.my_desc_sort('code'));
        } else if (column.order === 'ascending') {
          this.alldata = this.alldata.sort(this.my_asc_sort('code'));
        }
      } else if (column.prop === '1y') {
        if (column.order === 'descending') {
          this.alldata = this.alldata.sort(this.my_desc_sort('1y'));
        } else if (column.order === 'ascending') {
          this.alldata = this.alldata.sort(this.my_asc_sort('1y'));
        }
      } else if (column.prop === '1m') {
        if (column.order === 'descending') {
          this.alldata = this.alldata.sort(this.my_desc_sort('1m'));
        } else if (column.order === 'ascending') {
          this.alldata = this.alldata.sort(this.my_asc_sort('1m'));
        }
      } else if (column.prop === '1q') {
        if (column.order === 'descending') {
          this.alldata = this.alldata.sort(this.my_desc_sort('1q'));
        } else if (column.order === 'ascending') {
          this.alldata = this.alldata.sort(this.my_asc_sort('1q'));
        }
      } else if (column.prop === '1w') {
        if (column.order === 'descending') {
          this.alldata = this.alldata.sort(this.my_desc_sort('1w'));
        } else if (column.order === 'ascending') {
          this.alldata = this.alldata.sort(this.my_asc_sort('1w'));
        }
      } else if (column.prop === 'finally_score') {
        if (column.order === 'descending') {
          this.alldata = this.alldata.sort(this.my_desc_sort('finally_score'));
        } else if (column.order === 'ascending') {
          this.alldata = this.alldata.sort(this.my_asc_sort('finally_score'));
        }
      }
      this.tableData = this.alldata.slice(0, this.pageSize); // show only one page
    },
    elcellstyle ({ row, column, rowIndex, columnIndex }) {
      // ////console.log(row[0])

      // ////console.log(row[0])
      if (columnIndex == 4) {
        if (row['1w'] >= 0) {
          return 'color: #E85D2D;';
        } else return 'color: #20995B;';
      }
      if (columnIndex == 5) {
        if (row['1m'] >= 0) {
          return 'color: #E85D2D;';
        } else return 'color: #20995B;';
      }
      if (columnIndex == 6) {
        if (row['1q'] >= 0) {
          return 'color: #E85D2D;';
        } else return 'color: #20995B;';
      }
      if (columnIndex == 7) {
        if (row['1y'] >= 0) {
          return 'color: #E85D2D;';
        } else return 'color: #20995B;';
      }
    },
    cancelchoose (event) {
      //取消单线
      if (event.target.tagName === 'INPUT')
        // console. log(this radio,000"event. target);
        // this.radio= this.radio ? '' :'1'
        this.formData.sink = this.formData.sink ? '' : '1';
    },
    cancelchoose2 (event) {
      //取消单线
      if (event.target.tagName === 'INPUT')
        // console. log(this radio,000"event. target);
        // this.radio= this.radio ? '' :'1'
        this.formData.issink = this.formData.issink ? '' : '1';
    },
    cancelchoose4 (event) {
      //取消单线
      if (event.target.tagName === 'INPUT')
        // console. log(this radio,000"event. target);
        // this.radio= this.radio ? '' :'1'
        this.formData.sinkdo = this.formData.sinkdo ? '' : '1';
    },
    formatTooltip2 (val) {
      if (val == 0) return '不限';
      else if (val == 1) return '稳定';
      else if (val == 2) return '温和变化';
      else if (val == 3) return '大调整';
    },
    godetail (id, name) {
      //带参进去
      alphaGo(id, name, this.$route.path);
    },
    addpool (id, name) {
      let that = this;
      axios
        .post(that.$baseUrl + '/system/alpha/pool/basket_fund/', { fund_code: id })
        .then((res) => {
          that.$message('新增成功' + '  ' + id + ' ' + name);
        })
        .catch((err) => {
          //  that.$message('失败')
          ////console.log(err)
          //that.$message('数据缺失')
        });
    },
    submitForm () {
      this.submitflag = true;
      if (this.formData.scale.from != null) {
        if (this.formData.scale.from < 0) {
          this.submitflag = false;
          this.$message('基金当前规模前置范围不能小于0');
        }
      }
      if (this.formData.scale.end != null) {
        if (this.formData.scale.end < 0) {
          this.submitflag = false;
          this.$message('基金当前规模后置范围不能小于0');
        }
      }
      if (
        this.formData.scale.from != null &&
        this.formData.scale.end != null &&
        this.formData.scale.from != '' &&
        this.formData.scale.end != ''
      ) {
        if (Number(this.formData.scale.from) > Number(this.formData.scale.end)) {
          this.submitflag = false;
          this.$message('基金当前规模的前置范围大于后置范围');
        }
      }

      if (this.formData.long.from != null) {
        if (this.formData.long.from < 0) {
          this.submitflag = false;
          this.$message('估测久期前置范围不能小于0');
        }
      }
      if (this.formData.long.end != null) {
        if (this.formData.long.end < 0) {
          this.submitflag = false;
          this.$message('估测久期后置范围不能小于0');
        }
      }
      if (
        this.formData.long.from != null &&
        this.formData.long.end != null &&
        this.formData.long.from != '' &&
        this.formData.long.end != ''
      ) {
        if (this.formData.long.from > this.formData.long.end) {
          this.submitflag = false;
          this.$message('估测久期的前置范围不能大于后置范围');
        }
      }

      if (this.formData.maxmoney != null) {
        if (this.formData.maxmoney < 0) {
          this.$message('申购额度不能小于0');
          this.submitflag = false;
        }
      }

      if (this.formData.insuranceincome[1].value1 != null) {
        if (this.formData.insuranceincome[1].date == null) {
          this.submitflag = false;
          this.$message('风险收益特征中，最大回撤的时间范围未选择');
        }
      }
      if (this.formData.insuranceincome[2].value1 != null) {
        if (this.formData.insuranceincome[2].date == null) {
          this.submitflag = false;
          this.$message('风险收益特征中，波动率的时间范围未选择');
        }
      }
      if (this.formData.insuranceincome[3].value1 != null) {
        if (this.formData.insuranceincome[3].date == null) {
          this.submitflag = false;
          this.$message('风险收益特征中，夏普率的时间范围未选择');
        }
      }
      if (this.formData.insuranceincome[4].value1 != null) {
        if (this.formData.insuranceincome[4].date == null) {
          this.submitflag = false;
          this.$message('风险收益特征中，卡码率的时间范围未选择');
        }
      }
      if (this.formData.insuranceincome[5].value1 != null) {
        if (this.formData.insuranceincome[5].date == null) {
          this.submitflag = false;
          this.$message('风险收益特征中，胜率的时间范围未选择');
        }
      }
      if (this.temp4) {
        if (this.temp4.indexOf(1) > -1) {
          let czsum = 0;
          if (this.czchoose.indexOf('松货币') < 0) {
            this.$set(this.formData.Growth, 'value1', 0);
          } else if (this.czchoose.indexOf('松货币') > -1) {
            czsum += Number(this.formData.Growth.value1);
          }

          if (this.czchoose.indexOf('紧货币') < 0) {
            this.$set(this.formData.Growth, 'value2', 0);
          } else if (this.czchoose.indexOf('紧货币') > -1) {
            czsum += Number(this.formData.Growth.value2);
          }

          if (czsum != 100) {
            if (this.czchoose.indexOf('松货币') < 0 && this.czchoose.indexOf('紧货币') < 0) {
            } else {
              this.$message('市场判断中货币筛选条件值加起来不为100，无法提交');
              this.submitflag = false;
            }
          }
        }

        if (this.temp4.indexOf(2) > -1) {
          let dpsum = 0;
          if (this.dpchoose.indexOf('松信用') < 0) {
            this.$set(this.formData.bigsmall, 'value1', 0);
          } else if (this.dpchoose.indexOf('松信用') > -1) {
            dpsum += Number(this.formData.bigsmall.value1);
          }

          if (this.dpchoose.indexOf('紧信用') < 0) {
            this.$set(this.formData.bigsmall, 'value2', 0);
          } else if (this.dpchoose.indexOf('紧信用') > -1) {
            dpsum += Number(this.formData.bigsmall.value2);
          }

          if (dpsum != 100) {
            if (this.dpchoose.indexOf('松信用') < 0 && this.dpchoose.indexOf('紧信用') < 0) {
            } else {
              this.$message('市场判断信用筛选条件值加起来不为100，无法提交');
              this.submitflag = false;
              ////console.log('库存周期'+dpsum)
            }
          }
        }
      }
      if (this.temp3.indexOf(1) > -1) {
        let zqsum = 0;
        if (this.hgchoose.indexOf('牛市') < 0) {
          this.$set(this.formData.Marketcycle, 'value1', 0);
        } else if (this.hgchoose.indexOf('牛市') > -1) {
          zqsum += Number(this.formData.Marketcycle.value1);
        }

        if (this.hgchoose.indexOf('熊市') < 0) {
          this.$set(this.formData.Marketcycle, 'value2', 0);
        } else if (this.hgchoose.indexOf('熊市') > -1) {
          zqsum += Number(this.formData.Marketcycle.value2);
        }

        if (this.hgchoose.indexOf('震荡') < 0) {
          this.$set(this.formData.Marketcycle, 'value3', 0);
        } else if (this.hgchoose.indexOf('震荡') > -1) {
          zqsum += Number(this.formData.Marketcycle.value3);
        }

        if (this.hgchoose.indexOf('无观点') < 0) {
          this.$set(this.formData.Marketcycle, 'value4', 0);
        } else if (this.hgchoose.indexOf('无观点') > -1) {
          zqsum += Number(this.formData.Marketcycle.value4);
        }

        if (zqsum != 100) {
          if (
            this.hgchoose.indexOf('牛市') < 0 &&
            this.hgchoose.indexOf('熊市') < 0 &&
            this.hgchoose.indexOf('震荡') < 0 &&
            this.hgchoose.indexOf('无观点')
          ) {
          } else {
            this.$message('二级市场周期的筛选条件值加起来不为100，无法提交');
            this.submitflag = false;
            ////console.log('成长周期'+zqsum)
          }
        }
      }

      if (this.submitflag == true) {
        if (this.formData.maxmoney == null || this.formData.maxmoney == '') {
          this.formData.maxmoney = '0';
        }
        ////console.log("发送请求")
        ////console.log(this.formData)
        let tempformdata = this.formData;
        if (tempformdata.scale.from == '') tempformdata.scale.from = null;
        if (tempformdata.scale.end == '') tempformdata.scale.end = null;
        if (tempformdata.maxmoney == '') tempformdata.maxmoney = null;

        if (tempformdata.cbondstyle == '') tempformdata.cbondstyle = null;
        if (tempformdata.fundchara == '') tempformdata.fundchara = [];
        if (tempformdata.sink == '') tempformdata.sink = null;
        //  if(tempformdata.cz.value1){

        for (let i = 0; i < tempformdata.insuranceincome.length; i++) {
          //  //console.log(i)
          // //console.log(tempformdata.insuranceincome[i].value2)
          if (tempformdata.insuranceincome[i].value1 == '') this.$set(tempformdata.insuranceincome[i], 'value1', null);

          // //console.log("发送请求")
          if (tempformdata.insuranceincome[i].value2 == '') {
            //  //console.log("发送请求")
            // tempformdata.insuranceincome[i].value2=null
            this.$set(tempformdata.insuranceincome[i], 'value2', null);
          }
        }
        let that = this;
        this.showmsgsloading = true;
        if (that.is_request_active) return;
        that.is_request_active = true;
        axios
          .post(this.$baseUrl + '/system/alpha/other_bond_filter/', { fund_type: 'purebond', filter_condition: tempformdata, poollist: this.poollist })
          .then((res) => {
            that.is_request_active = false;
            that.showmsgsloading = false;
            that.alldata = res.data.data.sort(that.my_desc_sort('finally_score'));
            that.tableData = that.alldata.slice(0, 19);
            that.pageTotal = res.data.data.length;
            that.pageIndex = 1;
            try {
              this.localStorage.setItem('formDatapurebond', JSON.stringify(that.formData));
              this.localStorage.setItem('temp1purebond', JSON.stringify(that.temp1));
              this.localStorage.setItem('alldatapurebond', JSON.stringify(that.alldata));
              this.localStorage.setItem('temp4purebond', JSON.stringify(that.temp4));
              this.localStorage.setItem('czchoosepurebond', JSON.stringify(that.czchoose));
              this.localStorage.setItem('dpchoosepurebond', JSON.stringify(that.dpchoose));
            } catch (err) { }
          })
          .catch((err) => {
            //console.log(err);
            that.$message('筛选失败');
            that.alldata = [];
            that.tableData = [];
            that.pageTotal = 0;
            that.pageIndex = 1;
          });
        axios.post(that.$baseUrl + '/system/alpha/buy_point_alpha/', { filter: that.formData, type: 'purebond', ismanager: false }).then((res) => {
          //埋点成功
        });
      }
    },
    printconsole () {
      const { export_json_to_excel } = require('@/vendor/Export2Excel');
      var list = [];
      let tHeader = [];
      let filterVal = [];

      tHeader = ['基金名称', '基金经理姓名', '基金代码', '近一周收益', '近一月收益', '近一季收益', '近一年收益'];
      filterVal = ['name', 'manager', 'code', '1w', '1m', '1q', '1y'];
      ////console.log(this.alldata)
      for (let i = 0; i < this.alldata.length; i++) {
        list[i] = [];
        list[i][0] = this.alldata[i].name;
        list[i][1] = this.alldata[i].manager_name;
        list[i][2] = this.alldata[i].code;
        list[i][3] = this.alldata[i]['1w'];
        list[i][4] = this.alldata[i]['1m'];
        list[i][5] = this.alldata[i]['1q'];
        list[i][6] = this.alldata[i]['1y'];
      }

      export_json_to_excel(tHeader, list, '固收+筛选结果');
    },
    redoForm () {
      this.formData = {
        fundchara: [],
        scale: {
          from: null,
          end: null
        },
        long: {
          from: null,
          end: null
        },
        sinkdo: 0,
        sink: null,
        issink: null,
        nowdurelong: null,
        nowsinkdown: null,
        maxmoney: null,
        insuranceincome: [
          {
            id: 1,
            date: ['now', '3y'],
            value1: null,
            rank: '100'
          },
          {
            id: 2,
            date: ['now', '3y'],
            value1: null,
            value2: null,
            rank: '100'
          },
          {
            id: 3,
            date: ['now', '3y'],
            value1: null,
            value2: null,
            rank: '100'
          },
          {
            id: 4,
            date: ['now', '3y'],
            value1: null,
            rank: '100'
          },
          {
            id: 5,
            date: ['now', '3y'],
            value1: null,
            rank: '100'
          },
          {
            id: 6,
            date: ['now', '3y'],
            value1: null,
            rank: '100'
          }
        ],

        Marketcycle: {
          value1: 0,
          value2: 0,
          value3: 0,
          value4: 0
        },
        Growth: {
          value1: 0,
          value2: 0
        },
        bigsmall: {
          value1: 0,
          value2: 0
        },
        shenwan: {
          value1: 0,
          value2: 0,
          value3: 0,
          value4: 0,
          value5: 0,
          value6: 0,
          value7: 0,
          value8: 0,
          value9: 0,
          value10: 0,
          value11: 0,
          value12: 0,
          value13: 0,
          value14: 0,
          value15: 0,
          value16: 0,
          value17: 0,
          value18: 0,
          value19: 0,
          value20: 0,
          value21: 0,
          value22: 0,
          value23: 0,
          value24: 0,
          value25: 0,
          value26: 0,
          value27: 0,
          value28: 0
        }
      };
      this.temp1 = [];
      this.temp4 = [];
      this.czchoose = [];
      this.dpchoose = [];
    },
    godetailP (id, name) {
      //带参进去
      this.$router.push({ path: '/fundmanagerdetail/' + id, hash: '', query: { id: id, name: name } });
    },
    handlePageChange () {
      this.tableData = this.alldata.slice((this.pageIndex - 1) * this.pageSize, this.pageIndex * this.pageSize);
    },
    handleSizeChange (val) {
      this.pageSize = val;
      this.tableData = this.alldata.slice((this.pageIndex - 1) * this.pageSize, this.pageIndex * this.pageSize);
    },
    resetForm () {
      this.useraddmodalshow = true;
    }
  }
};
</script>
<style>
.choosebox {
	display: flex;
	align-items: center;
	background: #f1f1f5;
	margin: 20px;
}
.tiptablebox {
	display: flex;
}
.inputbox {
	border: 0px;
	width: 80px !important;
	outline: medium;
	text-align: center;

	padding: 0;
	-webkit-appearance: none;
	appearance: none;
	margin: 0;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
	-webkit-appearance: none !important;
	-moz-appearance: none !important;
	-o-appearance: none !important;
	-ms-appearance: none !important;
	appearance: none !important;
	margin: 0;
}
input[type='number'] {
	-webkit-appearance: textfield;
	-moz-appearance: textfield;
	-o-appearance: textfield;
	-ms-appearance: textfield;
	appearance: textfield;
}
.el-checkbox-group {
	margin-left: 0 !important;
}
/* input[readonly]{
background-color: #f1f1f5
} */
.el-form-item {
	margin-bottom: 0;
}
.homebodyfontsize .el-form-item__label {
	width: 200px !important;
	text-align: left;
}
.homebodyfontsize .el-form-item__content {
	margin-left: 200px !important;
}
</style>
<style lang="scss" scoped>
.managerDetailPage {
	margin-left: 2%;
	width: 96%;
	background: #d0d7df;
	padding: 20px;
}

.row {
	margin: -10px;
	display: flex;
}

.left {
	width: 155px;
	flex: 0 0 auto;
	margin-right: 10px;
}

.right {
	position: relative;
	flex: 1 1 100px;
}
</style>
<style lang="scss">
.comment-section {
	padding: 5px 15px 0 15px;
}

.comment {
	background: linear-gradient(90deg, #3b64f2, #1b8eff);
	color: white;
	font-size: 12px;
	padding: 12px 24px;
}

.comment.center {
	text-align: center;
}

.section {
	padding: 15px 15px 0 15px;
}

.double-table {
	display: flex;
	flex-basis: 10px;
	justify-content: space-between;

	.single-table {
		flex: 1;
	}

	.cell {
		font-size: 14px !important;
		font-weight: 400 !important;
		text-align: center !important;
		padding: 0 !important;
	}

	th {
		padding: 5px 0 !important;
	}
}

.split-cell {
	display: flex;
	align-items: center;
	justify-content: center;

	div {
		width: 40px;
	}
}
</style>
<style lang="scss" scoped>
.title {
	width: 99%;
	font-weight: 600;
	padding: 10px 15px;
}
.sub-title {
	font-size: 14px;
	font-weight: 600;
	border-left: 2px solid dodgerblue;
	margin-bottom: 6px;
	padding-left: 3px;
	line-height: 22px;
	height: 22px;
	flex: 1 1 auto;
}
.title-change-fund {
	display: flex;
	align-items: center;
	margin-bottom: 4px;
	label {
		font-size: 14px;
		margin-right: 10px;
	}
}
</style>
