const config = require("./config/projectsConfig.js");
let projectName = process.env.PROJECT_NAME;

const CopyWebpackPlugin = require("copy-webpack-plugin");
const CompressionWebpackPlugin = require("compression-webpack-plugin");
const productionGzipExtensions = ["js", "css"];
const webpack = require("webpack");

let publicPath = "./";
if (projectName == "projectGDBank") {
  publicPath = "https://cdn.owl-portfolio.com/owl_dashboard_front_GDBank/";
} else if (projectName == "projectBase") {
  if (process.env.NODE_ENV === "production") {
    publicPath = "https://cdn.owl-portfolio.com/owl_dashboard_front/";
  } else if (process.env.NODE_ENV === "simpleProduction") {
    publicPath = "https://cdn.owl-portfolio.com/simple_owltools/";
  }
}

let plugins = [];
if (process.env.NODE_ENV !== "development") {
  plugins.push(
    // Ignore all locale files of moment.js
    new webpack.IgnorePlugin({
      resourceRegExp: /^\.\/locale$/,
      contextRegExp: /moment$/,
    }),
    // new webpack.optimize.LimitChunkCountPlugin({
    // 	maxChunks: 10,
    // 	minChunkSize: 10240
    // }),
    // 配置compression-webpack-plugin压缩
    new CompressionWebpackPlugin({
      algorithm: "gzip",
      test: new RegExp("\\.(" + productionGzipExtensions.join("|") + ")$"),
      threshold: 10240,
      minRatio: 0.8,
    })
  );
}
if (projectName == "projectTk") {
  plugins.push(
    new CopyWebpackPlugin({
      patterns: [
        {
          from: "version/lib", // 源文件目录
          to: "lib", // 打包后的文件目录中的子文件夹
          toType: "dir", // 表示 to 是一个目录
          // noErrorOnMissing: true, // 如果源文件目录不存在不会报错
          // globOptions: {
          // 	ignore: [
          // 		// 忽略所有的 `.txt` 文件（举例）
          // 		'**/*.txt'
          // 	]
          // }
        },
      ],
    })
  );
}
module.exports = {
  pages: config[projectName || "project"].pages,
  publicPath: publicPath,
  productionSourceMap:
    process.env.NODE_ENV === "production" ||
    process.env.NODE_ENV === "test" ||
    process.env.NODE_ENV === "simpleProduction" ||
    process.env.NODE_ENV === "uat"
      ? false
      : true,
  css: {
    extract:
      process.env.NODE_ENV === "production" ||
      process.env.NODE_ENV === "test" ||
      process.env.NODE_ENV === "simpleProduction" ||
      process.env.NODE_ENV === "uat"
        ? {
            ignoreOrder: true,
          }
        : false,
  },
  pwa: {
    iconPaths: {
      favicon32: "/src/assets/img/owls2.png",
      favicon16: "/src/assets/img/owls2.png",
      appleTouchIcon: "/src/assets/img/owls2.png",
      maskIcon: "/src/assets/img/owls2.png",
      msTileImage: "/src/assets/img/owls2.png",
    },
  },
  // assetsDir: 'lib',
  devServer: {
    host: "0.0.0.0", //host改成自己的IP
    // port: 8080,
    // allowedHosts: 'all',
    // disableHostCheck: true,
    port: 8085,
    // 查阅 https://github.com/vuejs/vue-doc-zh-cn/vue-cli/cli-service.md#配置代理
    proxy: null,
  },
  // transpileDependencies: ['element-ui'],
  chainWebpack: (config) => {
    // config.entry('main').add('babel-polyfill'),
    // config.resolve.alias.set('@', resolve('src'));
    //d导致v-html无法解析
    config.module
      .rule("vue")
      .use("vue-loader")
      .loader("vue-loader")
      .tap((options) => {
        options.compilerOptions.directives = {
          html(node, directiveMeta) {
            (node.props || (node.props = [])).push({
              name: "innerHTML",
              value: `xss(_s(${directiveMeta.value}))`,
            });
          },
        };
        return options;
      })
      .end();

    // config.output.filename('js/[name].[contenthash:8].js').chunkFilename('js/[name].[contenthash:8]/.js')

    // config.module.rule('images').use('image-webpack-loader').loader('image-webpack-loader').options({ bypassOnDebug: true }).end();
  },
  configureWebpack: {
    output: {
      filename: "js/[name].js",
      chunkFilename: "js/[name].js",
    },
    plugins,
    externals: {
      vue: "Vue",
      "vue-router": "VueRouter",
      "element-ui": "ELEMENT",
      echarts: "echarts",
      axios: "axios",
      "vue-antd-ui": "antd",
      "vue-echarts": "VueECharts",
    },
  },
};
