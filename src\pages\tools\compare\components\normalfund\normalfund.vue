<!--  -->
<template>
  <div class="normalfund">
    <div style="display: flex; align-items: center; width: 100%; position: relative; justify-content: space-between">
      <div style="display: flex; align-items: center">
        <div class="TitltCompare">业绩曲线</div>
      </div>
      <div style="display: flex; justify-content: flex-end; align-items: center; font-size: 14px">
        <div>
          <el-date-picker v-model="value1"
                          type="daterange"
                          align="right"
                          style="width: 216px"
                          unlink-panels
                          @change="changedata()"
                          range-separator="|"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                          :picker-options="pickerOptions">
          </el-date-picker>
        </div>
        <div style="margin-left: 16px">
          <el-select v-model="value2"
                     :remote-method="searchpeople2"
                     @focus="focusF"
                     filterable
                     @change="changedata()"
                     remote
                     prefix-icon="el-icon-search"
                     :loading="loading"
                     placeholder="选择参考基准曲线">
            <el-option-group v-for="groups in options2"
                             :key="groups.label"
                             :label="groups.label">
              <el-option v-for="group in groups.options"
                         :key="group.code"
                         :label="group.name"
                         :value="group.code"> </el-option>
            </el-option-group>
          </el-select>
        </div>
        <div style="display: flex">
          <div v-for="(item, index) in nameList"
               :key="index"
               style="margin-left: 16px">
            <el-select v-model="item.value"
                       @change="changedata(item.value)"
                       :placeholder="'请选择' + item.name + '管理基金'">
              <el-option v-for="item in item.option"
                         :key="item.code"
                         :label="item.name"
                         :value="item.code"> </el-option>
            </el-select>
          </div>
        </div>
      </div>
    </div>

    <div style="page-break-inside: avoid; margin-top: 16px">
      <v-chart ref="normalfund"
               v-loading="empty1"
               autoresize
               element-loading-text="暂无数据"
               element-loading-spinner="el-icon-document-delete"
               element-loading-background="rgba(239, 239, 239, 0.5)"
               style="page-break-inside: avoid; width: 100%; height: 400px"
               :options="optionpbroe"></v-chart>
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import VCharts from 'vue-echarts';
import { ManagedFundReturn, ManagerManagedFund } from '@/api/pages/tools/compare.js';
import { getFundOrBase } from '@/api/pages/components/yejiheader.js';
export default {
  //import引入的组件需要注入到对象中才能使用
  props: {
    comparetype: {
      type: String,
      default: 'manager' //fund
    },
    id: {
      type: String,
      default: '30189741,30441407'
    },
    type: {
      type: String,
      default: 'equity'
    },
    name: {
      type: String,
      default: '萧楠,胡昕炜'
    }
  },
  components: { 'v-chart': VCharts },
  computed: {
    nameList () {
      let list = this.name.split(',');
      let data = [];
      list.forEach((item) => {
        data.push({ name: item, value: '', option: [], fundname: '' });
      });
      return data;
    }
  },
  data () {
    //这里存放数据
    return {
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一周',
            onClick (picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近一个月',
            onClick (picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近三个月',
            onClick (picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近半年',
            onClick (picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 180);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近一年',
            onClick (picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 365);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近三年',
            onClick (picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 365 * 3);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      },
      showdetailchoose: false,
      optionpbroe: {},
      empty1: false,
      value1: '', //日期选择器
      value2: '', //基准选择
      options2: [], //基准选择项
      valuemanager: [],
      optionsmanager: [],
      flagshow: false,
      arrdata: [],
      basepersonfund: '',
      baseindexlist: ''
    };
  },
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    // 前往比较基金
    gotoco () {
      // //console.log(this.nameList)
      let tempcode = '';
      let tempname = '';
      for (let i = 0; i < this.nameList.length; i++) {
        tempcode = tempcode + this.nameList[i].value + ',';
        tempname = tempname + this.nameList[i].fundname + ',';
      }
      tempcode = tempcode.slice(0, tempcode.length - 1);
      tempname = tempname.slice(0, tempname.length - 1);
      this.$router.push({
        path: '/fundcompare',
        query: {
          id: tempcode,
          type: 'equity',
          name: tempname
        }
      });
    },
    focusF () {
      if (this.value2 == '') {
        this.searchpeople2('');
      }
    },
    // 获取基准
    async searchpeople2 (query) {
      if (query == '') {
        this.loading = false;
        // let data = await getFundOrBase({'message':"1"})
        this.options2 = this.COMMON.optionBasic;
        // console.log(this.options);
      } else {
        this.loading = false;
        let data = await getFundOrBase({ message: query });
        if (data) {
          let temparr = [
            {
              label: '参考基准',
              options: []
            }
          ];
          for (let i = 0; i < data.length; i++) {
            if (data[i].flag == 'index') {
              temparr[0].options.push(data[i]);
            }
          }
          this.options2 = temparr;
        }
      }
    },
    getdata () {
      this.empty1 = false;
      if (this.comparetype == 'manager') {
        this.getmanager();
      } else {
        this.gefunddata();
      }
    },
    changedata (id) {
      if (id) {
        for (let i = 0; i < this.arrdata.data[0].length; i++) {
          for (let k = 0; k < this.arrdata.data[0][i].length; k++) {
            if (this.arrdata.data[0][i][k].code == id) {
              for (let j = 0; j < this.nameList.length; j++) {
                if (this.nameList[j].value == id) {
                  this.$set(this.nameList[j], 'fundname', this.arrdata.data[0][i][k].name);
                  this.nameList[j].fundname == this.arrdata.data[0][i][k].name;
                }
              }
            }
          }
        }
        // this.$emit('changefund',this.nameList)
      }
      if (this.comparetype == 'manager') {
        this.getmanagerdata();
      } else {
        // this.gefunddata()
      }
    }, //
    async getmanagerdata () {
      this.empty1 = false;
      let _this = this;
      let temp = '';
      if (this.nameList.length > 0) {
        temp = this.nameList[0].value;
        for (let i = 1; i < this.nameList.length; i++) {
          temp = temp + ',' + this.nameList[i].value;
        }
      }
      let data = await ManagedFundReturn({
        fund_code: temp,
        start_date: this.value1[0],
        end_date: this.value1[1],
        manager_code: this.id,
        type: this.type,
        manager_name: this.name,
        index_code: this.value2
      });
      if (data) {
        this.$emit('changefund', this.nameList);
        let datatemp = [];
        let dataarr = [];
        let indexlist = [];
        let indexnum = [];
        for (let i = 0; i < data.data.length; i++) {
          if (data.data[i].name == 'date') {
            datatemp = data.data[i].value;
          } else {
            dataarr.push(data.data[i]);
            indexlist.push(0);
            indexnum.push(0);
          }
        }
        if (dataarr.length > 0) {
          for (let k = 0; k < dataarr[0].value.length; k++) {
            for (let j = 0; j < dataarr.length; j++) {
              if (dataarr[j].value[k] == 'nan') {
                if (k - indexlist[j] > 1) {
                  dataarr[j].value[k] = dataarr[j].value[k - 1];
                } else {
                  indexlist[j] = k;
                }
              } else {
                if (indexlist[j] != 0) {
                  dataarr[j].value[k] = Number(dataarr[j].value[k]) + Number(dataarr[indexlist.indexOf(0)].value[indexlist[j]]);
                }
              }
            }
          }
        }

        this.basepersonfund = dataarr[indexlist.indexOf(0)].name;

        this.baseindexlist = [];
        for (let i = 0; i < indexlist.length; i++) {
          if (indexlist[i] == 0) this.baseindexlist.push({ name: dataarr[i].name, minpoint: 0 });
          else this.baseindexlist.push({ name: dataarr[i].name, minpoint: dataarr[i].value[indexlist[i] + 1] });
        }
        // //console.log(this.arrdata)
        // //console.log('arrdata')
        for (let i = 0; i < this.arrdata.data[0].length; i++) {
          for (let j = 0; j < this.arrdata.data[0][i].length; j++) {
            if (this.basepersonfund == this.arrdata.data[0][i][j].code) this.basepersonfund = this.arrdata.data[0][i][j].name;
            for (let k = 0; k < this.baseindexlist.length; k++) {
              if (this.baseindexlist[k].name == this.arrdata.data[0][i][j].code) {
                this.$set(this.baseindexlist[k], 'name', this.arrdata.data[0][i][j].name);
              }
            }
          }
        }
        //
        let seriesss = [];
        // dataarr.sort((a, b) => {
        // 	if (this.$route.query.id.split(',').indexOf(a.name) > this.$route.query.id.split(',').indexOf(b.name)) return 1;
        // 	else return -1;
        // });
        for (let i = 0; i < dataarr.length; i++) {
          if (dataarr[i].name == this.value2) {
            for (let j = 0; j < this.options2[0].options.length; j++) {
              if (this.options2[0].options[j].code == this.value2) {
                seriesss.push({
                  name: this.options2[0].options[j].name,
                  type: 'line',
                  symbol: 'none',
                  data: dataarr[i].value
                });
              }
            }
          }
          for (let k = 0; k < this.nameList.length; k++) {
            if (this.nameList[k].value == dataarr[i].name) {
              seriesss.push({
                name: this.nameList[k].fundname,
                type: 'line',
                symbol: 'none',
                data: dataarr[i].value
              });
            }
          }
        }
        this.optionpbroe = {
          color: ['#ff9003', '#4096ff', '#7388A9', '#6F80DD', '#4096FF', '#929694', '#f4d1ff', '#e91e63', '#64dd17'],
          tooltip: {
            trigger: 'axis',
            formatter: function (obj) {
              //   //console.log(obj)
              var value = obj[0].axisValue + `<br />`;
              for (let i = 0; i < obj.length; i++) {
                if (obj[i].seriesName == _this.baseperson) {
                  value +=
                    `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:` +
                    obj[i].color +
                    `;"></span>` +
                    obj[i].seriesName +
                    ':' +
                    (Number(obj[i].data) * 100).toFixed(2) +
                    '%' +
                    `<br />`;
                } else {
                  let temp = 0;
                  for (let k = 0; k < _this.baseindexlist.length; k++) {
                    if (_this.baseindexlist[k].name == obj[i].seriesName) temp = _this.baseindexlist[k].minpoint;
                  }
                  value +=
                    `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:` +
                    obj[i].color +
                    `;"></span>` +
                    obj[i].seriesName +
                    ':' +
                    ((Number(obj[i].data) - Number(temp)) * 100).toFixed(2) +
                    '%' +
                    `<br />`;
                }
              }
              return value;
            }
          },
          legend: {},
          grid: {
            left: '10px',
            right: '3%',
            bottom: '3%',
            top: '30px',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: datatemp
          },
          yAxis: {
            axisLine: { show: false },
            axisTick: { show: false },
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed'
              }
            },
            min: (value) => {
              //  //console.log(value)
              //  //console.log('123')
              return value.min;
            },
            type: 'value',
            axisLabel: {
              formatter: function (obj) {
                //   //console.log(obj)
                // var value = obj.value;
                return (obj * 100).toFixed(1) + '%';
              }
            }
          },
          series: seriesss
        };
      }
    },
    gefunddata () { },
    // 查找管理基金 只有人的方法要
    async getmanager () {
      this.value1 = [this.convertToLateDate(24 * 60 * 60 * 1000 * 365 * 3), this.convertToLateDate(0)];
      let data = await ManagerManagedFund({ manager_code: this.id, type: this.type, manager_name: this.name });
      if (data) {
        this.arrdata = data;
        //console.log(this.arrdata)
        // //console.log(object);
        for (let i = 0; i < data.data[0].length; i++) {
          for (let k = 0; k < this.nameList.length; k++) {
            if (this.nameList[k].name == data.data[0][i][0].manager_name) {
              this.nameList[k].value = data.data[0][i][0].code;
              this.nameList[k].fundname = data.data[0][i][0].name;
              this.nameList[k].option = data.data[0][i];
            }
          }
        }
        this.getmanagerdata();
      }
    },
    // 格式化时间
    convertToLateDate (val) {
      var data = new Date();
      var Da = new Date(data.getTime() - val);
      // 以上两行代码为关键代码，若想要返回一天后的时间，则可以将第二行代码更换为下面代码
      // var Da = new Date(data.getTime() + 24 * 60 * 60 * 1000);
      // 若是想要返回值为当前时间，则上面两行代码可以直接修改为下面代码即可。
      // var Da = new Date()
      var y = Da.getFullYear();
      var m = Da.getMonth() + 1;
      var d = Da.getDate();
      m = m < 10 ? '0' + m : m;
      d = d < 10 ? '0' + d : d;
      return y + '-' + m + '-' + d + ' ';
    },
    createPrintWord () {
      let height = this.$refs['normalfund']?.$el.clientHeight;
      let width = this.$refs['normalfund']?.$el.clientWidth;
      let chart = this.$refs['normalfund'].getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff'
      });
      return [
        ...this.$exportWord.exportFirstTitle('代表基金收益比较'),
        ...this.$exportWord.exportTitle('业绩曲线'),
        ...this.$exportWord.exportChart(chart, { width, height })
      ];
    }
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () { },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { } //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style>
.normalfund .el-range-editor--small .el-range-separator {
	line-height: 24px;
	font-size: 13px;
}
.normalfund .el-date-editor .el-range-separator {
	width: 16px;
}
</style>
