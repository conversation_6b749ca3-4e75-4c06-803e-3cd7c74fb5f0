import request from '@/utils/request';

const server = '/system/mixed';

/**
@/api/pages/filter/custom.js
 */

// 获取自定义分类列表
export function getCustomTags(params) {
	return request({
		url: server + '/fund/pool/page',
		method: 'get',
		params
	});
}

// 新增或修改自定义分类
export function saveCustomTags(data) {
	return request({
		url: server + '/fund/pool/save',
		method: 'post',
		data
	});
}

// 手动导入基金信息
export function importTagByHand(data) {
	return request({
		url: server + '/fund/pool/importByHand',
		method: 'post',
		data
	});
}

// 手动导入基金信息
export function importTagByFile(params) {
	return request({
		url: server + '/fund/pool/importByFile',
		method: 'get',
		params
	});
}

// 获取分类下的基金代码
export function getFundCode(params) {
	return request({
		url: server + '/fund/pool/getFundCode',
		method: 'get',
		params
	});
}

// 获取分类下的基金代码
export function deleteTag(params) {
	return request({
		url: server + '/fund/pool/delete',
		method: 'post',
		params
	});
}

// 获取用户列表
export function getUserList(params) {
	return request({
		url: server + '/pool/userlist/',
		method: 'get',
		params
	});
}
/**
 * @param {获取池子更新时间} params
 * @returns
 */
export function getPoolDateList(params) {
	return request({
		url: server + '/Combination/DateList/',
		method: 'get',
		params
	});
}
/**
 * @param {更改池子内基金状态} data
 * id   池子ID
 * code 基金code
 * flag
 * @returns
 */
export function updateStatus(data) {
	return request({
		url: server + '/Combination/UpdateStatus/',
		method: 'post',
		data
	});
}
/**
 * @param {池子新增基金} data
 * id   池子ID
 * code 基金code
 * flag
 * @returns
 */
export function postFunds(data) {
	return request({
		url: server + '/Report/getfile/',
		method: 'post',
		data
	});
}

/**
 * @param {删除池子内基金} params
 * @returns
 */
export function deletePoolFund(params) {
	return request({
		url: server + '/pool/pool_funds/',
		method: 'delete',
		params
	});
}
/**
 * @param {获取池子基础信息} data
 * @returns
 */
export function getPoolInfo(data) {
	return request({
		url: server + '/pool/PoolBasicInfo/',
		method: 'post',
		data
	});
}
/**@/api/pages/fundPool/index.js */
/**
 * @param {获取基金池列表} params
 * @returns
 */
export function getPoolList(params) {
	return request({
		url: server + '/pool/fund_pool/',
		method: 'get',
		params
	});
}
// 基金池内基金列表
export function getPoolDetail(params) {
	return request({
		url: server + '/pool/pool_funds/',
		method: 'get',
		params
	});
}
/**
 * @param {新增基金池信息} data
 * @returns
 */
export function postPoolInfo(data) {
	return request({
		url: server + '/pool/fund_pool/',
		method: 'post',
		data
	});
}

/**
 * @param {编辑基金池信息} data
 * @returns
 */
export function putPoolInfo(data) {
	return request({
		url: server + '/pool/fund_pool/',
		method: 'put',
		data
	});
}

/**
 * @param {删除基金池信息} params
 * @returns
 */
export function deletePool(params) {
	return request({
		url: server + '/pool/fund_pool/',
		method: 'delete',
		params
	});
}

/**
 * @param {合并基金池} data
 * @returns
 */
export function mergeFundPool(data) {
	return request({
		url: server + '/pool/fund_pool/portfolio_fund/',
		method: 'post',
		data
	});
}

// 获取组合列表
export function getListAll(params) {
	return request({
		url: server + '/Combination/CombinationList/',
		method: 'get',
		params
	});
}
// 获取组合详情
export function getList(params) {
	return request({
		url: server + '/Combination/CombinationInfo/',
		method: 'get',
		params
	});
}
// 创建组合
export function createCom(data) {
	return request({
		url: server + '/Combination/CombinationInfo/',
		method: 'post',
		data
	});
}
// 打通创建组合
export function createCom2(data) {
	return request({
		url: server + '/Combination/CombinationPool/',
		method: 'post',
		data
	});
}
// 修改
export function upCom(data) {
	return request({
		url: server + '/Combination/CombinationInfo/',
		method: 'put',
		data
	});
}
// 删除组合
export function delCom(params) {
	return request({
		url: server + '/Combination/CombinationInfo/',
		method: 'delete',
		params
	});
}
/*组合构建end*/
/** 组合详情日期列表 */
export function getComQuaList(params) {
	return request({
		url: server + '/Combination/CombinationTimeList/',
		method: 'get',
		params
	});
}
/** 组合详情持仓 */
export function getQuaDetail(params) {
	return request({
		url: server + '/Combination/CombinationHolding/',
		method: 'get',
		params
	});
}
// 获取昨日成交价格
// TODO
export function fundPrice(params) {
	return request({
		url: server + '/Combination/CombinationHoldNav/',
		method: 'get',
		params
	});
}
// 提交（手动修改&excel）
export function CombinationHolding(data) {
	return request({
		url: server + '/Combination/CombinationHolding/',
		method: 'post',
		data
	});
}
// 等权重调仓
export function ComEquityWeight(data) {
	return request({
		url: server + '/Combination/CombinationHoldingEquity/',
		method: 'post',
		data
	});
}
// 获取基金配置走势
export function getCombinationFundWeight(params) {
	return request({
		url: server + '/Combination/CombinationFundWeight/',
		method: 'get',
		params
	});
}

// 获取组合收益贡献度走势
export function getCombinationHoldReturn(params) {
	return request({
		url: server + '/Combination/CombinationHoldReturn/',
		method: 'get',
		params
	});
}
// 获取组合内基金列表
export function getCombinationHolding(params) {
	return request({
		url: server + '/Combination/CombinationBasicInfo/',
		method: 'get',
		params
	});
}

// 获取未调仓说明列表
export function getCombinationDescription(params) {
	return request({
		url: server + '/Combination/CombinationDescription/',
		method: 'get',
		params
	});
}
// 设置未调仓说明列表
// combination_id,date,description
export function postCombinationDescription(data) {
	return request({
		url: server + '/Combination/CombinationDescription/',
		method: 'post',
		data
	});
}
// 修改未调仓说明列表
export function putCombinationDescription(data) {
	return request({
		url: server + '/Combination/CombinationDescription/',
		method: 'put',
		data
	});
}
// 获取月度收益表格
export function getCombinationPartReturn(params) {
	return request({
		url: server + '/Combination/CombinationPartReturn/',
		method: 'get',
		params
	});
}
/**
 * @param {自定义指标计算} data
 * ids  Array
 * insert_time  String
 * yearqtr  Array
 * start_date  String 开始时间
 * end_date   String 结束时间
 * cut_flag String 时间切分
 * measure  Array 计算指标
 * flag 5:基金池
 * @returns
 */
export function getCustomMeasure(data) {
	return request({
		url: server + '/Combination/Measure/',
		method: 'post',
		data
	});
}

/**
 * @param {获取基金池持仓信息} data
 * ids  Array
 * insert_time  String
 * yearqtr  Array
 * isbond   0:股票  1:债券
 * isnew    0:全部  1:最新
 * flag 5:基金池
 * @returns
 */
export function getCombinationHoldStock(data) {
	return request({
		url: server + '/Combination/CombinationHoldStock/',
		method: 'post',
		data
	});
}
/**
 * @param {获取转债股性债性} data
 * ids  Array
 * insert_time  String
 * yearqtr  Array
 * method   等权
 * flag 5:基金池
 * @returns
 */
export function getCbondConvert(data) {
	return request({
		url: server + '/Combination/CbondConvert/',
		method: 'post',
		data
	});
}
/**
 * @param {获取基金池信用下沉} data
 * ids  Array
 * insert_time  String
 * yearqtr  Array
 * method   等权
 * flag 5:基金池
 * @returns
 */
export function getCreditDownInfo(data) {
	return request({
		url: server + '/Combination/CreditDownInfo/',
		method: 'post',
		data
	});
}
/**
 * @param {获取基金池久期分析} data
 * ids  Array
 * insert_time  String
 * yearqtr  Array
 * method   等权
 * flag 5:基金池
 * @returns
 */
export function getDurationInfo(data) {
	return request({
		url: server + '/Combination/DurationInfo/',
		method: 'post',
		data
	});
}
/**
 * @param {获取基金池基金行业能力} data
 * @returns
 */
export function getIndustryRankInfo(data) {
	return request({
		url: server + '/Combination/IndustryRankInfo/',
		method: 'post',
		data
	});
}
/**
 * @param {获取基金池基金行业能力} data
 * @returns
 */
export function getMarketRankInfo(data) {
	return request({
		url: server + '/Combination/MarketRankInfo/',
		method: 'post',
		data
	});
}

/**
 * @param {获取池子类型划分} params
 * @returns
 */
export function getCombinationType(data) {
	return request({
		url: server + '/Combination/CombinationType/',
		method: 'post',
		data
	});
}
/**
 * @param {获取池子中基金分年度波动收益} params
 * @returns
 */
export function getCombinationRisk(data) {
	return request({
		url: server + '/Combination/CombinationAvereturnVolatility/',
		method: 'post',
		data
	});
}

/**
 * @param {获取池子中基金行业能力统计} params
 * @returns
 */
export function getCombinationIndustry(data) {
	return request({
		url: server + '/Combination/CombinationIndustry/',
		method: 'post',
		data
	});
}
/**
 * @param {获取池子中基金行业能力统计} params
 * @returns
 */
export function getCombinationIndustryTop(params) {
	return request({
		url: server + '/Combination/IndustryTop/',
		method: 'get',
		params
	});
}
/**
 * @param {获取池子中基金风格能力统计} params
 * @returns
 */
export function getCombinationMarket(data) {
	return request({
		url: server + '/Combination/CombinationMarket/',
		method: 'post',
		data
	});
}
/**
 * @param {获取池子中基金风格能力统计} params
 * @returns
 */
export function getCombinationMarketTop(params) {
	return request({
		url: server + '/Combination/MarketTop/',
		method: 'get',
		params
	});
}
/**
 * @param {获取池子中alphabeta} params
 * @returns
 */
export function getCombinationAB(data) {
	return request({
		url: server + '/Combination/CombinationAlphaBeta/',
		method: 'post',
		data
	});
}

/**
 * @param {获取前10大} params
 * @returns
 */
export function getCombinationB10(data) {
	return request({
		url: server + '/Combination/CombinationBig10/',
		method: 'post',
		data
	});
}
/**
 * @param {获取季度大行业权重} params
 * @returns
 */
export function getCombinationQuarterWeight(data) {
	return request({
		url: server + '/Combination/CombinationQuarterWeight/',
		method: 'post',
		data
	});
}
// new
/**
 * @param {获取子分类统计数据} params
 * @returns
 */
export function getChildrenCategory(data) {
	return request({
		url: server + '/Combination/getNetasset/',
		method: 'post',
		data
	});
}

/**
 * @param {获取收益统计数据} params
 * @returns
 */
export function getReturns(params) {
	return request({
		url: server + '/Combination/getReturns/',
		method: 'get',
		params
	});
}
/**
 * @param {获取风格统计数据} params
 * @returns
 */
export function getstyle(data) {
	return request({
		url: server + '/Combination/getStyle/',
		method: 'post',
		data
	});
}
/**
 * @param {获取前10大2} params
 * @returns
 */
export function getCombinationB102(data) {
	return request({
		url: server + '/Combination/CombinationBig102/',
		method: 'post',
		data
	});
}
/**
 * @param {大行业} params
 * @returns
 */
export function bigIndustry(data) {
	return request({
		url: server + '/Combination/getBigIndustry/',
		method: 'post',
		data
	});
}
export function swIndustry(data) {
	return request({
		url: server + '/Combination/getSWIndustry/',
		method: 'post',
		data
	});
}
/**
 * @param {获取前5大} params
 * @returns
 */
export function getCombinationB5(data) {
	return request({
		url: server + '/Combination/getCombinationB5/',
		method: 'post',
		data
	});
}

/**
 * @param {大类资产配置} params
 * @returns
 */
export function getAllocate(data) {
	return request({
		url: server + '/Combination/AllocationDetails/',
		method: 'post',
		data
	});
}
/**
 * @param {获取基金池行业信息} data
 * ids  Array
 * insert_time  String
 * yearqtr  Array
 * industry_standard   申万(2021) 申万二级
 * industry_code
 * method   等权
 * flag 5:基金池
 * @returns
 */
export function getIndustryInfo(data) {
	return request({
		url: server + '/Combination/IndustryInfo/',
		method: 'post',
		data
	});
}
/**
 * @param {获取基金池表现风格} data
 * ids  Array
 * insert_time  String
 * yearqtr  Array
 * flag 5:基金池
 * @returns
 */
export function getStyleInfo(data) {
	return request({
		url: server + '/Combination/StyleInfo/',
		method: 'post',
		data
	});
}
/**
 * @param {信用} params
 * @returns
 */
export function getCredit(params) {
	return request({
		url: server + '/Combination/CreditDown/',
		method: 'get',
		params
	});
}
/**
 * @param {久期} params
 * @returns
 */
export function getDuration(params) {
	return request({
		url: server + '/Combination/DurationInfo/',
		method: 'get',
		params
	});
}
/**
 * @param {生成子池} params
 * @returns
 */
export function createdChildren(data) {
	return request({
		url: server + '/pool/ChildPool/',
		method: 'post',
		data
	});
}
/**
 * @param {选择对标项} params
 * @returns
 */
export function doDB(params) {
	return request({
		url: server + '/Combination/doDB/',
		method: 'get',
		params
	});
}
// 获得对标项
export function getDB(params) {
	return request({
		url: server + '/Combination/getDB/',
		method: 'get',
		params
	});
}
/**
 * @param {获取相似矩阵} params
 * @returns
 */
export function getCombinationSimilar(data) {
	return request({
		url: server + '/Combination/CombinationSimilar/',
		method: 'post',
		data
	});
}

/**
 * @param {获取相似系数} params
 * @returns
 */
export function getFofReturnRelevat(data) {
	return request({
		url: server + '/Combination/CorrelationAnalysis/',
		method: 'post',
		data
	});
}

// 基金公司
/**
 *
 * @param {基金公司大类资产配置} params
 * @returns
 */
export function getCompanyAllocationDetails(params) {
	return request({
		url: server + '/Company/AllocationDetails/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {所属基金概况} params
 * @returns
 */
export function getAllTypeFundBasicInfo(params) {
	return request({
		url: server + '/Company/NetassetAndNumber/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {各类型基金收益} params
 * @returns
 */
export function getAllTypeFundCumReturn(params) {
	return request({
		url: server + '/Company/Since1yReturn/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {获取行业配置变化比例} params
 * @returns
 */
export function getIndustryChange(params) {
	return request({
		url: server + '/Company/IndustryChange/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {信用分析} params
 * @returns
 */
export function getBondCreditAnalysis(params) {
	return request({
		url: server + '/Company/BondCreditAnalysis/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {基金公司存在基金类型} params
 * @returns
 */
export function getHoldType(params) {
	return request({
		url: server + '/Company/HoldType/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {基金公司基础信息} params
 * @returns
 */
export function getBasicMsg(params) {
	return request({
		url: server + '/Company/BasicMsg/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {基金公司收益曲线} params
 * @returns
 */
export function getReturnWithAsset(params) {
	return request({
		url: server + '/Company/ReturnWithAsset/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {新发基金} params
 * @returns
 */
export function getNewFund(params) {
	return request({
		url: server + '/Company/NewFund/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {全部基金} params
 * @returns
 */
export function getHoldFundMsg(params) {
	return request({
		url: server + '/Company/HoldFundMsg/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {全部基金经理} params
 * @returns
 */
export function getHoldManagerMsg(params) {
	return request({
		url: server + '/Company/HoldManagerMsg/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {新发基金} params
 * @returns
 */
export function getNewDevelopmentFund(params) {
	return request({
		// url: '/Company/NewFund/',
		url: server + '/Company/Event/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {基金业绩排名} params
 * @returns
 */
export function getFundRecentReturn(params) {
	return request({
		url: server + '/Company/FundRecentReturn/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {获取持仓债券分析} params
 * @returns
 */
export function getBondAnalysis(params) {
	return request({
		url: server + '/Company/BondAnalysis/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {持仓股票分析} params
 * @returns
 */
export function getHoldStockMsg(params) {
	return request({
		url: server + '/Company/HoldStockMsg/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {股票Barra分析} params
 * @returns
 */
export function getStockBarraStyle(params) {
	return request({
		url: server + '/Company/StockBarraStyle/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {公司行业配置表现} params
 * @returns
 */
export function getIndustryInfoCompany(params) {
	return request({
		url: server + '/Company/IndustryDetails/',
		method: 'get',
		params
	});
}

/**
 *
 * @param {获取机构列表} params
 * @returns
 */
export function getInstituesList(params) {
	return request({
		url: server + '/institues',
		method: 'get',
		params
	});
}
export function getUsersSalesman(params) {
	return request({
		url: server + '/users/salesman',
		method: 'get',
		params
	});
}
export function getUsersGroups(params) {
	return request({
		url: server + '/groups',
		method: 'get',
		params
	});
}
export function refreshUserGroups() {
	return request({
		url: server + '/refresh-user-groups/',
		method: 'post'
	});
}
export function getInstitues(id) {
	return request({
		url: server + '/institues/' + id,
		method: 'get'
	});
}
export function patchInstitues(id, data) {
	return request({
		url: server + '/institues/' + id,
		method: 'patch',
		data
	});
}
export function postInstitues(data) {
	return request({
		url: server + '/institues/',
		method: 'post',
		data
	});
}

// 删除机构
export function deleteInstitues(id) {
	return request({
		url: server + '/institues/' + id,
		method: 'delete'
	});
}
export function getServiceplans(id) {
	return request({
		url: server + '/serviceplans/' + id,
		method: 'get'
	});
}
export function patchServiceplans(id, data) {
	return request({
		url: server + '/serviceplans/' + id,
		method: 'patch',
		data
	});
}
export function postServiceplans(data) {
	return request({
		url: server + '/serviceplans/',
		method: 'post',
		data
	});
}
export function getUsers() {
	return request({
		url: server + '/users',
		method: 'get'
	});
}
export function getUsersRoles() {
	return request({
		url: server + '/users/roles',
		method: 'get'
	});
}
export function patchUsers(id, data) {
	return request({
		url: server + '/users/' + id,
		method: 'patch',
		data
	});
}
export function postUsers(data) {
	return request({
		url: server + '/users',
		method: 'post',
		data
	});
}
export function deleteUsers(id) {
	return request({
		url: server + '/users/' + id,
		method: 'delete'
	});
}
