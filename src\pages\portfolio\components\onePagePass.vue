<template>
	<div class="combination">
		<div class="flex_card">
			<div v-for="item in templateList" :key="item.value" v-show="item.isshow" :class="item.type">
				<component :is="item.is" :ref="item.value" :indexInfo="indexInfo" @resolveFather="item.methods" v-loading="loading"></component>
			</div>
		</div>
	</div>
</template>

<script>
// 业绩对比
import performanceComparison from '@/components/components/performanceComparison/index.vue';
// 动态回撤
import dynamicPullback from '@/components/components/dynamicFallback/index.vue';
// 相关系数
import correlationCoefficient from '@/components/components/correlationCoefficient/index.vue';
// 收益贡献度走势
import contributionTrend from '@/components/components/contributionTrend/index.vue';
// 基金配置走势
import allocationTrend from '@/components/components/allocationTrend/index.vue';
// 月度收益
import monthlyIncome from '@/components/components/monthlyIncome/index.vue';
// 类型穿透饼图
import typePieChart from './components/typePieChart.vue';

// 业绩对比

import {
	getFofReturnRelevat,
	getCombinationFundWeight,
	getCombinationHoldReturn,
	getCombinationPartReturn
} from '@/api/pages/SystemMixed.js';

import {
	getBasicInfo,
	getFofAllocationDetails,
	getFundOrManagerReturn,
	getIndexReturnInfo,
	getFofDrawdown
} from '@/api/pages/SystemAlpha.js';

export default {
	components: {
		performanceComparison,
		dynamicPullback,
		correlationCoefficient,
		contributionTrend,
		allocationTrend,
		monthlyIncome,
		typePieChart
	},
	data() {
		return {
			info: {},
			distributionPostData: {},
			templateList: [],
			requestOver: 0,
			requestAll: 0,
			loading: true,
			correlationCoefficient: null,
			dynamicPullback: null,
			combinationHoldReturn: null,
			combinationHoldReturnData: {},
			combinationPartReturn: null,
			combinationPartReturnData: {},
			codes: [],
			start_date: '',
			end_date: '',
			fundOrManagerReturn: null,
			basicInfo: null,
			indexReturnInfo: null,
			activeIndex: 'fund',
			combinationFundWeight: null,
			fofAllocationDetails: null,
			returnInfoIndex: [],
			fofDrawdown: {}
		};
	},
	props: {
		indexInfo: {
			type: Object,
			default: {}
		},
		active: {
			type: String,
			default: ''
		},
		// 组合内当前持仓列表
		list: {
			type: Array,
			dafault: []
		}
	},
	watch: {
		indexInfo(n, o) {
			if (this.active == 'onePagePass') {
				this.getPerformanceComparisonIndex();
			}
		}
	},
	methods: {
		// 获取打印数据
		createPrintWord() {
			let printData = [];
			this.templateList.map((item) => {
				if (item.isshow) {
					if (this.$refs[item.value]?.[0].createPrintWord) {
						printData.push(...this.$refs[item.value]?.[0].createPrintWord());
					}
				}
			});
			return printData;
		},
		// 接收/返回组件列表
		getTemplateList(list) {
			if (list) {
				this.templateList = [...list];
			} else {
				return this.templateList;
			}
		},
		// 获取父组件数据
		getData(data) {
			this.info = data;
			this.codes = [this.info.code];
			this.loading = true;
			this.watch();
			this.requestOver = 0;
			this.requestAll = 0;
			this.initPostData();
			this.formatTemplatList();
		},
		// 添加watch函数式监听(因为watch侦听器在页面切换时失效)
		watch() {
			let unwatch = this.$watch('requestOver', (val, old) => {
				if (val == this.requestAll) {
					this.templateList.map((item) => {
						if (item.getData && item.getData !== 'None') {
							this?.[item.getData]();
						}
					});
					this.loading = false;
					this.$emit('overRequest', 'onePagePass');
					unwatch();
				}
			});
		},
		// 获取打印数据
		createPrintWord() {
			let printData = [];
			this.templateList.map((item) => {
				if (item.isshow) {
					if (this.$refs[item.value]?.[0].createPrintWord) {
						printData.push(...this.$refs[item.value]?.[0].createPrintWord());
					}
				}
			});
			return printData;
		},
		// 格式化模板列表
		formatTemplatList() {
			let requestList = [];
			this.requestAll = 0;
			this.templateList.map((item) => {
				if (item.methods && typeof item.methods == 'string') {
					item.methods = this?.[item.methods];
				}
				if (item.typelist.indexOf(this.info.type) !== -1 || item.typelist.indexOf('*') !== -1) {
					if (requestList.indexOf(item.getRequestData) == -1) {
						if (item.getRequestData && item.getRequestData !== 'None') {
							this.$nextTick(() => {
								this?.[item.getRequestData]();
								requestList.push(item.getRequestData);
								this.requestAll = this.requestAll + 1;
							});
						}
					}
				}
			});
		},
		// 初始化请求数据
		initPostData() {
			// 组合贡献度走势
			this.combinationHoldReturnData = {
				flag: 'month',
				combination_id: this.info.code,
				start_date: ''
			};
			// 月度收益表格
			this.combinationPartReturnData = {
				flag: 'month',
				combination_id: this.info.code
			};
			this.fofDrawdown = {
				code: this.info.code,
				type: this.info.type,
				index_code: this.indexInfo.id
			};
			this.returnInfoIndex = [this.indexInfo.id];
		},
		// 获取业绩对比数据
		async getPerformanceComparison() {
			let postData = {
				codes: this.codes,
				type: this.info.type,
				flag: this.info.flag,
				start_date: this.start_date || '',
				end_date: this.end_date || ''
			};
			let data = await getFundOrManagerReturn(postData);
			if (data?.mtycode == 200) {
				this.fundOrManagerReturn = data?.data?.data;
			}
			let getBasicInfoPostData = {
				code: this.info.code,
				flag: this.info.flag,
				type: this.info.type,
				benchmark: this.indexInfo.id
			};
			let info = await getBasicInfo(getBasicInfoPostData);
			if (info?.mtycode == 200) {
				this.basicInfo = info?.data?.data;
			}
			this.requestOver = this.requestOver + 1;
		},
		// 获取业绩对比基准数据
		async getIndexPerformanceComparison() {
			let data = await getIndexReturnInfo({
				index_codes: this.returnInfoIndex,
				type: this.info.type,
				start_date: this.fundOrManagerReturn?.[0].date?.[0] || '',
				end_date: this.fundOrManagerReturn?.[0].date?.[this.fundOrManagerReturn?.[0].date.length - 1] || ''
			});
			if (data?.mtycode == 200) {
				this.indexReturnInfo = data?.data?.data;
				// this.$nextTick(() => {
				this.$refs['performanceComparison']?.[0]?.getIndexData(this.indexReturnInfo);
				// });
			}
		},
		// 业绩对比数据
		getPerformanceComparisonData() {
			this.$refs['performanceComparison']?.[0]?.getData(this.fundOrManagerReturn, this.info);
			this.$refs['performanceComparison']?.[0]?.getInfoData(this.basicInfo);
			this.getIndexPerformanceComparison();
		},
		// 获取业绩对比基准变化数据
		getPerformanceComparisonIndex() {
			this.returnInfoIndex = [this.indexInfo?.id];
			this.fofDrawdown.index_code = this.indexInfo?.id;
			this.getIndexPerformanceComparison();
			this.getDynamicPullback(this.indexInfo);
		},
		// 获取动态回撤数据
		async getDynamicPullback() {
			let data = await getFofDrawdown(this.fofDrawdown);
			// if (data?.mtycode == 200) {
			// this.dynamicPullback = data?.data;
			this.$refs['dynamicPullback']?.[0]?.getData(data?.data?.data, this.info, this.indexInfo);
			// } else {
			// this.$message.warning('动态回撤' + data?.mtymessage);
			// }
			this.requestOver = this.requestOver + 1;
		},
		// 动态回撤数据
		getDynamicPullbackData() {},
		// 动态回撤
		getDynamicPullbackIndex(index) {
			// this.fofDrawdown.index_code = index;
			// this.getDynamicPullback();
		},
		// 获取下穿类型饼图
		getTypePieChart() {
			this.$refs['typePieChart']?.[0]?.getData(
				this.list?.map((item) => {
					return item.code;
				})
			);
			this.requestOver = this.requestOver + 1;
		},
		// 获取相关系数数据
		async getCorrelationCoefficient() {
			if (this.getCacheData('correlationCoefficient')) {
				this.correlationCoefficient = this.getCacheData('correlationCoefficient');
				this.requestOver = this.requestOver + 1;
			} else {
				let data = await getFofReturnRelevat({
					code: this.info.code,
					index_code: this.indexInfo.id,
					type: this.info.type,
					start_date: '',
					end_date: ''
				});
				if (data?.mtycode == 200) {
					this.correlationCoefficient = data?.data;
				} else {
					this.$refs['correlationCoefficient']?.[0].hideLoading();
				}
				this.requestOver = this.requestOver + 1;
			}
		},
		// 相关系数数据
		getCorrelationCoefficientData() {
			this.$refs['correlationCoefficient']?.[0].getData(this.correlationCoefficient, this.list);
		},
		// 获取组合收益贡献度走势数据
		async getContributionTrend() {
			let data = await getCombinationHoldReturn(this.combinationHoldReturnData);
			if (data?.mtycode == 200) {
				this.combinationHoldReturn = data?.data;
			}
			this.requestOver = this.requestOver + 1;
		},
		// 组合收益贡献度走势数据
		getContributionTrendData() {
			this.$refs['contributionTrend']?.[0].getData(this.combinationHoldReturn);
		},
		// 获取基金配置走势数据
		async getAllocationTrend() {
			if (this.activeIndex == 'fund') {
				// 基金配置走势
				let data = await getCombinationFundWeight({
					combination_id: this.info.code
				});
				if (data?.mtycode == 200) {
					this.combinationFundWeight = data?.data;
					this.$refs['allocationTrend']?.[0].getFundData(this.combinationFundWeight);
					this.requestOver = this.requestOver + 1;
				}
			} else {
				// 资产配置走势
				let data = await getFofAllocationDetails({
					fund_code: this.info.code,
					type: this.info.type,
					flag: this.info.flag
				});
				if (data?.mtycode == 200) {
					this.fofAllocationDetails = data?.data;
					this.$refs['allocationTrend']?.[0].getAllocationData(this.fofAllocationDetails);
					this.requestOver = this.requestOver + 1;
				}
			}
		},
		// 基金配置走势数据
		getAllocationTrendData() {
			// if (this.activeIndex == 'fund') {
			// 	this.$refs['allocationTrend']?.[0].getFundData(this.combinationFundWeight);
			// } else {
			// 	this.$refs['allocationTrend']?.[0].getAllocationData(this.fofAllocationDetails);
			// }
		},
		// 获取基金配置走势子组件传递数据
		getAllocationTrendType(val) {
			this.activeIndex = val;
			this.getAllocationTrend();
		},
		// 获取月度收益数据
		async getMonthlyIncome() {
			let data = await getCombinationPartReturn(this.combinationPartReturnData);
			if (data?.mtycode == 200) {
				this.combinationPartReturn = data?.data;
				this.$refs['monthlyIncome']?.[0]?.getData(this.combinationPartReturn, this.info);
			}
			this.requestOver = this.requestOver + 1;
		},
		// 月度收益基准
		getMonthlyIncomeIndex(val) {
			this.combinationPartReturnData = {
				...this.combinationPartReturnData,
				flag: val
			};
			this.getMonthlyIncome();
		},
		// 月度收益数据
		getMonthlyIncomeData() {}
	}
};
</script>

<style></style>
