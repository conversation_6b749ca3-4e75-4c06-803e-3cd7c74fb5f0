<!--  -->
<template>
  <div class=''>
    <div class="flex item-center justify-between">
      <div class="area-title">长期持股行业分布(FOF)</div>
      <img alt=""
           class="download"
           src="../../../../../assets/img/download.png"
           @click="downloadExcel('长期持股行业分布')" />
    </div>
    <el-divider></el-divider>
    <div class="area-body">
      <div class="area-chart"
           v-loading="shareholding.loading">
        <div class="chart-card_half">
          <div class="chart-card_title">长期持股行业次数（次）</div>
          <v-chart autoresize
                   ref="ChartComponent_cqcghycs2"
                   v-show="!shareholding.countEmpty"
                   :options="shareholding.numberChart"
                   element-loading-background="rgba(239, 239, 239, 0.5)"
                   element-loading-spinner="el-icon-document-delete"
                   element-loading-text="暂无数据"
                   style="height: 340px; width: 100% !important; padding-top: 20px" />
          <el-empty v-show="shareholding.countEmpty"
                    :image-size="200"></el-empty>
        </div>
        <div class="chart-card_half">
          <div class="chart-card_title">长期持股行业权重（%）</div>
          <v-chart autoresize
                   ref="ChartComponent_cqcghyqz2"
                   v-if="!shareholding.weightEmpty"
                   :options="shareholding.weightChart"
                   element-loading-background="rgba(239, 239, 239, 0.5)"
                   element-loading-spinner="el-icon-document-delete"
                   element-loading-text="暂无数据"
                   style="height: 340px; width: 100% !important; padding-top: 20px" />
          <el-empty v-else
                    :image-size="200"></el-empty>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import {
  getObjectIndustryStat,
} from '@/api/pages/analysis/report';
import { filter_json_to_excel_inside, changColumnToRow, filter_json_to_excel_inside_multiHeader } from '@/utils/exportExcel.js';
import { export_json_to_excel_multiHeader } from '@/vendor/Export2Excel.js';

export default {
  //import引入的组件需要注入到对象中才能使用
  components: {},
  data () {
    //这里存放数据
    return {
      // 持股行业分布
      shareholding: {
        numberChart: null, // 长期持股行业次数
        weightChart: null, // 长期持股行业权重
        loading: false,
        countEmpty: true,
        weightEmpty: true,
        params: {}
      },
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    /**
 * 参数写入
 */
    getParams () {
      let reportID = Number(this.$route.query.id);
      let industryStandard = 3;
      let startFrom = Number(this.moment(this.$route.query.startDate).format('YYYYMMDD'));
      let endTo = Number(this.moment(this.$route.query.endDate).format('YYYYMMDD'));
      this.shareholding.params = {
        reportID,
        industryStandard,
        startFrom,
        endTo,
        selectedCuts: this.$route.query.graininess,
        reportTemplate: "FOF"
      };
    },
    downloadExcel (name) {
      if (name === '混合行业持仓分析') {
        const type = this.position.industryStandard == 2 ? '泰康一级行业' : '申万一级行业';
        const [nameArray, column] = this.buildExcelData(this.position.allTableData, 'yearqtr', 'industryName', 'weight');
        const format = [
          '',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p'
        ];
        const data = changColumnToRow(column, format);
        export_json_to_excel_multiHeader([nameArray], null, data, `${name}(${type})`);
      } else if (name === 'FOF行业持仓分析') {
        let kldName = '';
        const code = this.FOFPosition.code;
        this.FOFPosition.codeList.map((item) => {
          if (item.value === code) {
            kldName = item.label;
          }
        });

        let arr =
          this.FOFPosition.code === 'all'
            ? this.FOFPosition.allTableData
            : this.FOFPosition.tableData.filter((v) => v.data.code === this.FOFPosition.code);
        const type = this.FOFPosition.industryStandard == 2 ? '泰康一级行业' : '申万一级行业';
        const [nameArray, column] = this.buildExcelData(arr, 'date', 'industryName', 'weight');
        const format = [
          '',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p'
        ];
        const data = changColumnToRow(column, format);
        export_json_to_excel_multiHeader([nameArray], null, data, `${name}(${type})-颗粒度${kldName}`);
      } else if (name === 'MOM行业持仓分析') {
        let kldName = '';
        const code = this.MOMPosition.code;
        this.MOMPosition.codeList.map((item) => {
          if (item.value === code) {
            kldName = item.label;
          }
        });
        let arr =
          this.MOMPosition.code === 'all'
            ? this.MOMPosition.allTableData.filter((v) => v.data.code === this.MOMPosition.code)
            : this.MOMPosition.tableData.filter((v) => v.data.code === this.MOMPosition.code);
        const type = this.MOMPosition.industryStandard == 2 ? '泰康一级行业' : '申万一级行业';
        const [nameArray, column] = this.buildExcelData(arr, 'date', 'industryName', 'weight');
        const format = [
          '',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p'
        ];
        const data = changColumnToRow(column, format);
        export_json_to_excel_multiHeader([nameArray], null, data, `${name}(${type})-颗粒度${kldName}`);
      } else if (name === '长期持股行业分布') {
        const title = [
          { label: '行业名称', value: 'swlevel1' },
          { label: '长期持股行业次数', value: 'number' },
          { label: '长期持股行业权重（%）', value: 'weight', format: 'fix4p' }
        ];
        filter_json_to_excel_inside(title, this.pieChartData, ['data'], name);
      } else if (name === '整体组合行业Brison归因') {
        const type = this.brison.params.industryStandard == 2 ? '泰康一级行业' : '申万一级行业';
        const title = [
          { label: '行业', value: 'industry_name' },
          { label: '市值收益(亿元)', value: 'marketValueGainLoss', format: '' },
          { label: '净买入(亿元)', value: 'total_buy', format: '' },
          { label: '实际配置权重', value: 'meanWeight', format: '' },
          { label: '超欠配', value: 'changeWeightMean', format: '' },
          { label: '行业配置贡献', value: 'industry_hold_return', format: '' },
          { label: '股票选择贡献', value: 'stock_return', format: '' },
          { label: '行业持仓收益率', value: 'rp', format: '' },
          { label: '行业基准收益率', value: 'industry_index_return', format: '' }
        ];
        filter_json_to_excel_inside(title, this.brison.oldTableData, ['data'], name + '(' + type + ')');
      } else if (name === 'MOM行业操作复盘') {
        let kldName = '';
        const code = this.MOMData.code;
        this.MOMData.codeList.map((item) => {
          if (item.value === code) {
            kldName = item.label;
          }
        });
        let arr =
          this.MOMData.code === 'all' ? this.MOMData.allTableData : this.MOMData.tableData.filter((v) => v.data.code === this.MOMData.code);
        const type = this.MOMData.industryStandard == 2 ? '泰康一级行业' : '申万一级行业';
        const [nameArray, column] = this.buildExcelData(arr, 'date', 'industryName', 'index_rate');
        const format = [
          '',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p'
        ];
        const data = changColumnToRow(column, format);
        export_json_to_excel_multiHeader([nameArray], null, data, `${name}(${type})-颗粒度${kldName}`);
      } else if (name === 'FOF行业操作复盘') {
        let kldName = '';
        const code = this.FOFData.code;
        this.FOFData.codeList.map((item) => {
          if (item.value === code) {
            kldName = item.label;
          }
        });
        let arr =
          this.FOFData.code === 'all' ? this.FOFData.allTableData : this.FOFData.tableData.filter((v) => v.data.code === this.FOFData.code);
        const type = this.FOFData.industryStandard == 2 ? '泰康一级行业' : '申万一级行业';
        const [nameArray, column] = this.buildExcelData(arr, 'date', 'industryName', 'weight');
        const format = [
          '',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p',
          'fix4p'
        ];
        const data = changColumnToRow(column, format);
        export_json_to_excel_multiHeader([nameArray], null, data, `${name}(${type})-颗粒度${kldName}`);
      } else if (name === '行业配置表现') {
        if (this.performance.allTableData) {
          const temp = {};
          this.performance.allTableData.map((item) => {
            if (Object.keys(temp).includes(item.data.industryName)) {
              temp[item.data.industryName].push(item);
            } else {
              temp[item.data.industryName] = [item];
            }
          });
          let array = [];
          Object.keys(temp).map((key) => {
            array = array.concat(temp[key]);
          });

          const type = this.performance.industryStandard == 2 ? '泰康一级行业' : '申万一级行业';
          const title = [
            { label: '时间', value: 'date', format: '' },
            { label: '行业', value: 'industryName', format: '' },
            { label: '权重', value: 'weight', format: 'fix4p' },
            { label: '行业估算收益率', value: 'industry_return', format: 'fix4p' }
          ];
          filter_json_to_excel_inside(title, array, ['data'], name + '(' + type + ')');
        }
      }
    },
    /**
 * 长期持股行业分布数据
 */
    getIndustryShareholdingData () {
      this.getParams()
      this.shareholding.loading = true;
      return getObjectIndustryStat(this.shareholding.params).then((res) => {
        this.shareholding.loading = false;
        if (res.code === 200) {
          this.shareholding.countEmpty = false;
          this.shareholding.weightEmpty = false;
          this.pieChartData = res.data.rows;
          this.getIndustryShareholdingChart(res.data.rows);
        } else {
          this.shareholding.countEmpty = true;
          this.shareholding.weightEmpty = true;
        }
        return res;
      });
    },
    /**
 * 获取长期持股行业分布
 */
    getIndustryShareholdingChart (arr) {
      this.shareholding.numberChart = {
        grid: { left: '24px', right: '48px', top: '24px', bottom: '36px' },
        tooltip: {
          trigger: 'item',
          backgroundColor: '#ffffff',
          textStyle: {
            color: '#333333'
          }
        },
        legend: {
          show: false,
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            type: 'pie',
            radius: '80%',
            data: arr.map((item) => {
              return { value: item.data.number, name: item.data.swlevel1 };
            }),
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      };
      this.shareholding.weightChart = {
        grid: { left: '24px', right: '48px', top: '50px', bottom: '36px' },
        tooltip: {
          trigger: 'item',
          backgroundColor: '#ffffff',
          textStyle: {
            color: '#333333'
          }
        },
        legend: {
          show: false,
          orient: 'vertical',
          left: 'left'
        },

        series: [
          {
            type: 'pie',
            radius: '80%',
            data: arr.map((item) => {
              return { value: (Number(item.data.weight) * 100).toFixed(4), name: item.data.swlevel1 };
            }),
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      };
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () {

  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () {
  },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { }, //如果页面有keep-alive缓存功能，这个函数会触发
}
</script>
<style lang="scss" scoped>
@import '../../../tkdesign';

.download {
	padding-left: 25px;
}

.border_table_header_search {
	display: flex;
	justify-content: flex-end;
	position: relative;

	.selector {
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
		line-height: 22px;
		margin-top: 5px;
		color: rgba(0, 0, 0, 0.85);
	}

	.search-security {
		width: 250px;
		margin-right: 25px;
	}
}

.table {
	margin-top: 16px;
}

.area-chart {
	display: flex;
	justify-content: space-between;
	flex-wrap: wrap;

	.chart-card_half {
		width: calc(50% - 8px);
		padding: 0 20px 20px;
		border: 1px solid #d9d9d9;
		border-radius: 4px;

		.chart-card_title {
			display: flex;
			justify-content: space-between;
			color: rgba(0, 0, 0, 0.85);
			font-family: PingFang;
			height: 46px;
			line-height: 46px;
			font-size: 14px;
			font-style: normal;
			font-weight: 400;
			border-bottom: 1px solid #d9d9d9;
		}
	}
}

.chart-card {
	.chart-card_title {
		display: flex;
		justify-content: space-between;
		color: rgba(0, 0, 0, 0.85);
		font-family: PingFang;
		height: 46px;
		line-height: 46px;
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
		border-bottom: 1px solid #d9d9d9;
	}

	.chart-card_body {
		display: flex;

		.sidebar {
			width: 120px;
			padding: 20px 0px;
			gap: 40px;
			box-shadow: 19px 0px 20px 0px rgba(0, 0, 0, 0.04);
			background-color: #ffffff;
			height: 708px;

			.card {
				width: 12px;
				height: 8px;
				margin-right: 5px;
			}
		}
	}
}

.chart-card_header_bg {
	display: flex;
	flex-direction: row;
	align-items: center;
	border-bottom: 1px solid #d9d9d9;
	justify-content: space-between;
}

.charts_one_class {
	height: 680px;
}

.granularityDialog .dialog-body_footer {
	display: flex;
	justify-content: flex-end;
	padding: 0 20px;
}

.el-button--small {
	border-radius: 0 3px 3px 0;
}
</style>

<style lang="scss">
.granularityDialog .el-dialog__header {
	padding-bottom: 20px;
	border-bottom: 1px #e9e9e9 solid;
}

.granularityDialog .el-dialog__body {
	padding: 24px 12px !important;

	.dialog-body {
		padding: 0 20px;
		display: flex;

		.dialog-body_title {
			white-space: nowrap;
		}
	}
}

.el-checkbox-group > .el-checkbox-button:last-child .el-checkbox-button__inner {
	border-radius: 0;
	border-right: 0;
}

.performance-dialog-checkbox {
	.is-checked {
		.el-checkbox-button__inner {
			background-color: rgb(255, 244, 230) !important;
			color: #fc9004 !important;
			border-color: #fc9004 !important;
			box-shadow: none !important;
		}
	}

	.el-checkbox-button__inner {
		border: 1px solid #dcdfe6 !important;
		border-radius: 4px !important;
		margin-right: 10px;
	}
}
</style>