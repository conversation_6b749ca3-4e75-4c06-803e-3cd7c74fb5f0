<template>
	<div id="holdingStyle">
		<analysis-card-title title="操盘风格" image_id="holdingStyle"></analysis-card-title>
		<div class="charts_center_class" v-loading="loading">
			<v-chart
				style="width: 100%; height: 300px"
				ref="holdingStyle"
				autoresize
				v-loading="loading"
				element-loading-text="暂无数据"
				element-loading-spinner="el-icon-document-delete"
				element-loading-background="rgba(239, 239, 239, 0.5)"
				:options="optioncaopan"
			/>
		</div>
	</div>
</template>

<script>
// 操盘风格
import { lineChartOption } from '@/utils/chartStyle.js';
import { getCapabilityInfo } from '@/api/pages/Analysis.js';

export default {
	name: 'holdingStyle',
	data() {
		return {
			visible_caopanStyle: false,
			loading: true,
			optioncaopan: {},
			info: {}
		};
	},
	methods: {
		openvideo() {
			window.open('https://www.bilibili.com/video/BV1tN4y1T7BM?share_source=copy_web');
		},
		async getCapabilityInfo() {
			let data = await getCapabilityInfo({
				codes: [this.info.code],
				type: this.info.type,
				flag: [this.info.flag],
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			if (data?.mtycode == 200) {
				let holdingStyle = {};
				holdingStyle.caopan_touzi = data?.data?.find((v) => v.item == '股票集中度')?.score || '暂无';
				holdingStyle.caopan_touzi_rank = (Number(data?.data?.find((v) => v.item == '股票集中度')?.relRank) * 100)?.toFixed(2);
				holdingStyle.caopan_hangye = data?.data?.find((v) => v.item == '行业集中度')?.score || '暂无';
				holdingStyle.caopan_hangye_rank = (Number(data?.data?.find((v) => v.item == '行业集中度')?.relRank) * 100)?.toFixed(2);
				holdingStyle.caopan_huanshou = data?.data?.find((v) => v.item == '股票换手率')?.score || '暂无';
				holdingStyle.caopan_huanshou_rank = (Number(data?.data?.find((v) => v.item == '股票换手率')?.relRank) * 100)?.toFixed(2);
				holdingStyle.caopan_guanzhuqi = data?.data?.find((v) => v.item == '股票关注期')?.score || '暂无';
				holdingStyle.caopan_guanzhuqi_rank = (Number(data?.data?.find((v) => v.item == '股票关注期')?.relRank) * 100)?.toFixed(2);
				this.getChartData(holdingStyle);
			}
		},
		async getData(info) {
			this.info = info;
			await this.getCapabilityInfo();
		},
		getChartData(data) {
			this.loading = false;
			let {
				caopan_hangye_rank,
				caopan_touzi_rank,
				caopan_guanzhuqi_rank,
				caopan_huanshou_rank,
				caopan_hangye,
				caopan_touzi,
				caopan_guanzhuqi,
				caopan_huanshou
			} = data;
			let caopandata1 = [caopan_hangye_rank, caopan_touzi_rank, caopan_guanzhuqi_rank, caopan_huanshou_rank];
			let caopandata2 = [caopan_hangye, caopan_touzi, caopan_guanzhuqi, caopan_huanshou];
			for (let i = 0; i < caopandata1.length; i++) {
				if (caopandata1[i] == '暂无' || caopandata1[i] == 'NaN') caopandata1[i] = 0;
			}
			let tempcaopan1 = ['行业集中度', '个股集中度', ' 个股关注期', '平均换手率'];
			this.optioncaopan = lineChartOption({
				toolbox: 'none',
				grid: {
					top: '8px'
				},
				tooltip: {
					formatter: function (value) {
						let str = `<div style="display:flex;align-items:center;"><div style="margin-right:4px;">${value[0].name}排名:</div><div>${value[0].data}%</div></div>`;
						let res = str;
						// if (value[0].name == '行业集中度') {
						// 	res =
						// 		`<div style="margin-bottom:12px">` +
						// 		str +
						// 		`</div>` +
						// 		'<div style="display:flex;align-items:center;"><div style="margin-right:4px;">行业集中度：</div><div>' +
						// 		(!isNaN(caopandata2[0]) ? Number(caopandata2[0]).toFixed(2) : caopandata2[0]) +
						// 		'</div></div>';
						// } else if (value[0].name == '个股集中度') {
						// 	res =
						// 		`<div style="margin-bottom:12px">` +
						// 		str +
						// 		`</div>` +
						// 		'<div style="display:flex;align-items:center;"><div style="margin-right:4px;">个股集中度：</div><div>' +
						// 		(!isNaN(caopandata2[1]) ? Number(caopandata2[1]).toFixed(2) : caopandata2[1]) +
						// 		'</div></div>';
						// } else if (value[0].name == ' 个股关注期') {
						// 	res =
						// 		`<div style="margin-bottom:12px">` +
						// 		str +
						// 		`</div>` +
						// 		'<div style="display:flex;align-items:center;"><div style="margin-right:4px;">个股关注期：</div><div>' +
						// 		(!isNaN(caopandata2[2]) ? Number(caopandata2[2]).toFixed(2) : caopandata2[2]) +
						// 		'</div></div>';
						// } else if (value[0].name == '平均换手率') {
						// 	res =
						// 		`<div style="margin-bottom:12px">` +
						// 		str +
						// 		`</div>` +
						// 		'<div style="display:flex;align-items:center;"><div style="margin-right:4px;">平均换手率：</div><div>' +
						// 		(!isNaN(caopandata2[3]) ? Number(caopandata2[3]).toFixed(2) : caopandata2[3]) +
						// 		'</div></div>';
						// } else {
						// 	res =
						// 		`<div style="display:flex;align-items:center;"><div style="margin-right:4px;">` +
						// 		value[0].axisValue +
						// 		'</div><div>:' +
						// 		value[0].data +
						// 		'%</div></div>';
						// }
						return `<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${res}</div>`;
					}
				},
				xAxis: [{ data: tempcaopan1, boundaryGap: true, isAlign: true }],
				yAxis: [
					{
						name: '排名分位',
						type: 'value',
						nameLocation: 'middle', // 设置名称居中
						nameGap: 48, // 控制名称距离轴线的距离
						nameTextStyle: {
							align: 'center'
						},
						min: Math.min(...caopandata1.map((v) => v * 1)) - 10 > 0 ? Math.floor(Math.min(...caopandata1.map((v) => v * 1)) - 10) : 0,
						formatter: function (value) {
							if (value == 0 || value == '0') {
								return '低(0%)';
							} else if (value == 100 || value == '100') {
								return '高(100%)';
							} else return value + '%';
						}
					}
				],
				series: [
					{
						barWidth: '48px',
						data: caopandata1,
						itemStyle: {
							color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
								{ offset: 0, color: '#4096ff' }, // 渐变起始颜色
								{ offset: 1, color: '#ecf5ff' } // 渐变结束颜色
							])
						},
						type: 'bar',
						symbol: 'none'
					}
				]
			});
			this.loading_caopanStyle = false;
		},
		async createPrintWord(info) {
			await this.getData(info);
			return await new Promise((resolve, reject) => {
				this.$nextTick(async () => {
					let height = this.$refs['holdingStyle'].$el.clientHeight || 402;
					let width = this.$refs['holdingStyle'].$el.clientWidth || 828;
					let chart = this.$refs['holdingStyle'].getDataURL({
						type: 'jpg',
						pixelRatio: 3,
						backgroundColor: '#fff'
					});
					resolve([...this.$exportWord.exportTitle('操盘风格'), ...this.$exportWord.exportChart(chart, { width, height })]);
				});
			});
		}
	}
};
</script>
