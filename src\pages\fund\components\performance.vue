<!-- 业绩表现 -->
<template>
	<div>
		<div class="flex_card">
			<div v-for="item in templateList" :key="item.value" v-show="item.isshow" :class="item.type">
				<component :is="item.is" :ref="item.value" @resolveFather="item.methods" v-loading="loading" :showDescription="true"></component>
			</div>
		</div>
	</div>
</template>

<script>
// 总体业绩表现
import allReturnPerformance from '@/components/components/components/allReturnPerformance/index.vue';
// 滚动胜率
import holdingPressure from '@/components/components/fundComponents/holdingPressure/index.vue';
// 分时段业绩表现
import timePhasedPerformance from '@/components/components/components/timePhasedPerformance/index.vue';
// 市场适应性 marketAdaptabilityAndDescription
import marketAdaptabilityAndDescription from '@/components/components/components/marketAdaptability/index.vue';

// 收益率分布直方图
import distributionReturn from '@/components/components/components/distributionReturn/index.vue';
// 风险收益指标
import riskReturnIndex from '@/components/components/components/riskReturnIndex/index.vue';
// 风险收益关系
import riskReturnRelationship from '@/components/components/components/riskReturnRelationship/index.vue';

export default {
	components: {
		marketAdaptabilityAndDescription,
		allReturnPerformance,
		timePhasedPerformance,
		holdingPressure,
		distributionReturn,
		riskReturnIndex,
		riskReturnRelationship
	},
	data() {
		return {
			name: '业绩与适应性分析',
			info: {},
			templateList: [],
			requestOver: [],
			requestAll: 0,
			loading: true
		};
	},
	props: {
		showEditor: {
			type: Boolean,
			default: false
		}
	},
	methods: {
		// 接收/返回组件列表
		getTemplateList(list) {
			if (list) {
				this.templateList = [...list];
			} else {
				return this.templateList;
			}
		},
		// 获取父组件数据
		getData(data) {
			this.info = data;
			this.loading = true;
			this.requestOver = [];
			this.formatTemplatList();
		},
		// 获取打印数据
		async createPrintWord(info) {
			this.info = info;
			let printData = [];
			this.templateList.map((item) => {
				if (item.isshow) {
					if (this.$refs[item.value]?.[0].createPrintWord) {
						let list = this.$refs[item.value]?.[0].createPrintWord(this.info);
						printData.push(list);
					}
				}
			});
			let data = await Promise.all(printData);
			data.unshift(this.$exportWord.exportFirstTitle(this.name));
			return data;
		},
		// 格式化模板列表
		formatTemplatList() {
			this.$nextTick(() => {
				this.templateList.map((item) => {
					if (item.typelist.indexOf(this.info.type) !== -1) {
						this.$refs[item.value]?.[0]?.getData(this.info);
						this.loading = false;
					}
				});
			});
		}
	}
};
</script>

<style></style>
