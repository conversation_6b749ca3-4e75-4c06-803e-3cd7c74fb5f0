# Diff Details

Date : 2022-04-01 14:21:08

Directory f:\owl-tools\vue-manage-system-master\src\components\page\alphafilter

Total : 9 files,  1823 codes, 99 comments, 77 blanks, all 1999 lines

[summary](results.md) / [details](details.md) / [diff summary](diff.md) / diff details

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [src/components/page/alphaFilter/components/boxCzJzOnly.vue](/src/components/page/alphaFilter/components/boxCzJzOnly.vue) | Vue | 246 | 10 | 12 | 268 |
| [src/components/page/alphaFilter/components/boxNameYSF.vue](/src/components/page/alphaFilter/components/boxNameYSF.vue) | Vue | 0 | 0 | 1 | 1 |
| [src/components/page/alphaFilter/components/boxOnlyYSF.vue](/src/components/page/alphaFilter/components/boxOnlyYSF.vue) | Vue | 102 | 10 | 5 | 117 |
| [src/components/page/alphaFilter/components/dialogFilter.vue](/src/components/page/alphaFilter/components/dialogFilter.vue) | Vue | 654 | 21 | 18 | 693 |
| [src/components/page/alphaFilter/components/fundCateOnly.vue](/src/components/page/alphaFilter/components/fundCateOnly.vue) | Vue | 96 | 10 | 6 | 112 |
| [src/components/page/alphaFilter/components/holdPersonOnly.vue](/src/components/page/alphaFilter/components/holdPersonOnly.vue) | Vue | 85 | 10 | 6 | 101 |
| [src/components/page/alphaFilter/components/indexOnly.vue](/src/components/page/alphaFilter/components/indexOnly.vue) | Vue | 158 | 18 | 8 | 184 |
| [src/components/page/alphaFilter/components/industryTheme.vue](/src/components/page/alphaFilter/components/industryTheme.vue) | Vue | 154 | 10 | 9 | 173 |
| [src/components/page/alphaFilter/index.vue](/src/components/page/alphaFilter/index.vue) | Vue | 328 | 10 | 12 | 350 |

[summary](results.md) / [details](details.md) / [diff summary](diff.md) / diff details