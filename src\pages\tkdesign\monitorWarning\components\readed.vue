<!--  -->
<template>
  <div class=''>
    <!-- 头部区域 -->
    <div class="border_table_header">
      <!-- 左侧搜索区域 -->
      <div class="border_table_header_search">
        <el-input v-model="formData.name"
                  class="search"
                  clearable
                  @clear="getList(formData)"
                  placeholder="请输入产品名称或代码"
                  prefix-icon="el-icon-search" />
        <el-button type="primary"
                   @click="getList(formData)">查询</el-button>
      </div>
      <!-- 右侧筛选区域 -->
      <!-- <div @click="exportExcel()"><i class=""><svg xmlns="http://www.w3.org/2000/svg"
                     width="16"
                     height="16"
                     viewBox="0 0 16 16"
                     fill="none">
                  <path d="M7.88736 10.6575C7.90072 10.6745 7.9178 10.6883 7.93729 10.6978C7.95678 10.7073 7.97818 10.7123 7.99986 10.7123C8.02154 10.7123 8.04294 10.7073 8.06243 10.6978C8.08192 10.6883 8.099 10.6745 8.11236 10.6575L10.1124 8.1271C10.1856 8.03424 10.1195 7.89674 9.99986 7.89674H8.67665V1.85389C8.67665 1.77531 8.61236 1.71103 8.53379 1.71103H7.46236C7.38379 1.71103 7.3195 1.77531 7.3195 1.85389V7.89496H5.99986C5.88022 7.89496 5.81415 8.03246 5.88736 8.12532L7.88736 10.6575ZM14.5356 10.0325H13.4641C13.3856 10.0325 13.3213 10.0967 13.3213 10.1753V12.9253H2.67843V10.1753C2.67843 10.0967 2.61415 10.0325 2.53557 10.0325H1.46415C1.38557 10.0325 1.32129 10.0967 1.32129 10.1753V13.711C1.32129 14.0271 1.57665 14.2825 1.89272 14.2825H14.107C14.4231 14.2825 14.6784 14.0271 14.6784 13.711V10.1753C14.6784 10.0967 14.6141 10.0325 14.5356 10.0325Z"
                        fill="black"
                        fill-opacity="0.45" />
                </svg></i></div> -->
      <div>
        预警日期：
        <el-radio-group v-model="formData.type"
                        @input="getList(formData)">
          <el-radio-button v-for="(item, index) in radioOptions"
                           :label="index + 1"
                           :key="index">
            {{ item }}
          </el-radio-button>
        </el-radio-group>
      </div>
    </div>
    <!-- 表格区域 -->
    <el-table :data="tableData"
              v-loadong="loading.listLoading"
              height="calc(100vh - 500px)">

      <el-table-column align="gotoleft"
                       :show-overflow-tooltip="true"
                       label="预警规则名称"
                       prop="ruleName" />
      <el-table-column align="gotoleft"
                       :show-overflow-tooltip="true"
                       label="目标值"
                       prop="targetValue" />
      <el-table-column align="gotoleft"
                       :show-overflow-tooltip="true"
                       label="触发值"
                       prop="triggerValue" />
      <el-table-column align="gotoleft"
                       :show-overflow-tooltip="true"
                       label="关联产品"
                       prop="fundName" />
      <el-table-column align="gotoleft"
                       label="触发日期"
                       :show-overflow-tooltip="true"
                       prop="triggerTime"
                       sortable />
      <el-table-column align="gotoleft"
                       label="预警信息"
                       :show-overflow-tooltip="true"
                       width='200'
                       prop="description">
        <!-- <template slot-scope="scope">
                <el-popover placement="top-start"
                            width="500"
                            trigger="hover">
                  <div style="max-height: 400px;overflow: auto;padding:16px">
                    <div v-for="(item,index) in scope.row.description.split('<br />')"
                         :key="index">
                      <div>{{ item }}</div>
                    </div>

                  </div>
                  <div type="text"
                       slot="reference">{{scope.row.description.slice(0,10) + '...'  }}</div>
                </el-popover>
              </template> -->
      </el-table-column>
      <el-table-column align="gotoleft"
                       label="条件类型"
                       :show-overflow-tooltip="true"
                       prop="warnType"
                       v-slot="{ row }">
        <template>
          {{ getWarnType(row.warnType) }}
        </template>
      </el-table-column>
      <el-table-column align="gotoleft"
                       :show-overflow-tooltip="true"
                       label="触发标的"
                       prop="indexName" />
      <el-table-column align="gotoleft"
                       :show-overflow-tooltip="true"
                       label="超限日期"
                       prop="notifyTimes" />
      <template slot="empty">
        <el-empty :image-size="160"></el-empty>
      </template>
    </el-table>
    <!-- 分页器 -->
    <div class="pagination_board">
      <el-pagination :current-page.sync="pagination.pageIndex"
                     :page-size="pagination.pageSize"
                     :total="pagination.total"
                     background
                     layout="total, sizes, prev, pager, next"
                     @size-change="sizeChange"
                     @current-change="currentChange" />
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { getList } from "../../../../api/pages/tkdesign/monitorWarning";

export default {
  //import引入的组件需要注入到对象中才能使用
  components: {},
  data () {
    //这里存放数据
    return {
      pagination: {
        pageIndex: 1,// 当前页码
        pageSize: 10,// 页面显示几条数据
        total: 0,
      },
      loading: {
        listLoading: false,
        ruleListLoading: false,
        setLoading: false
      },
      radioOptions: ['全部', '近一周', '近一月', '近三月'],
      tableData: [],// 预警记录表格数据源

      // 首页搜索区域input绑定的数据
      formData: {
        name: '',
        type: '1',
      },
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    // 每页条数改变时触发的回调
    sizeChange (value) {
      this.pagination.pageSize = value
      this.getList(this.formData)
    }
    ,
    // 当前页数改变时触发的回调
    currentChange (value) {
      this.pagination.pageIndex = value
      this.getList(this.formData)
    },
    /**
     *获取条件类型
     * @param type
     * @returns {string|*}
     */
    getWarnType (type) {
      console.log(type);
      const list = [
        { label: '组合收益率', value: 1 },
        { label: '组合超额收益率', value: 2 },
        { label: '组合回撤', value: 3 },
        { label: '组合单个资产持仓占比', value: 4 },//单证券市值占比
        { label: '组合单个资产持仓份额占市场份额比', value: 5 },//单股票持仓占股本比
        { label: '组合大类资产持仓占比', value: 6 },//证券资产占比
        { label: '个股集中度', value: 7 },
        { label: '行业集中度', value: 8 },
        { label: '黑名单占比', value: 9 },
        { label: '白名单占比', value: 10 },
        { label: '组合大类资产净买入金额', value: 11 },//净买入
        { label: '组合单个资产净买入金额', value: 12 },//净买入
        { label: '组合单个资产买入金额', value: 13 }//新增//新增
      ]
      return list.filter(v => v.value === type)[0].label
    },
    /**
     * 获取列表
     * @param data
     */
    getList (data) {
      this.loading.listLoading = true
      const params = {
        ...data,
        current: this.pagination.pageIndex,
        pageSize: this.pagination.pageSize,
        flag: '2'
      }
      getList(params).then((res) => {
        this.loading.listLoading = false
        if (res.code === 200) {
          // console.log(res.data);
          this.tableData = res.data
          this.pagination.total = res.total
        } else {
          this.tableData = []
          this.pagination.total = 0
        }
      })
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created () {

  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted () {
    this.getList(this.formData)

  },
  beforeCreate () { }, //生命周期 - 创建之前
  beforeMount () { }, //生命周期 - 挂载之前
  beforeUpdate () { }, //生命周期 - 更新之前
  updated () { }, //生命周期 - 更新之后
  beforeDestroy () { }, //生命周期 - 销毁之前
  destroyed () { }, //生命周期 - 销毁完成
  activated () { }, //如果页面有keep-alive缓存功能，这个函数会触发
}
</script>
<style lang='scss' scoped>
@import '../../tkdesign';
.border_table_header_search {
	display: flex;

	.el-button {
		margin-left: 10px;
	}
}
//@import url(); 引入公共css类
</style>