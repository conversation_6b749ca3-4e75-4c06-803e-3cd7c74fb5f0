<template>
	<div id="cbondHoldEquityBond" v-show="show">
		<analysis-card-title title="转债股性债性概览" image_id="cbondHoldEquityBond"></analysis-card-title>
		<div class="charts_fill_class" v-loading="cardLoading">
			<v-chart
				ref="valuationAnalysis"
				v-loading="empty1"
				element-loading-text="暂无数据"
				element-loading-spinner="el-icon-document-delete"
				element-loading-background="rgba(239, 239, 239, 0.5)"
				class="charts_one_class"
				autoresize
				:options="optionpbroe"
			></v-chart>
		</div>
	</div>
</template>

<script>
import { exportTitle, exportChart } from '@/utils/exportWord.js';
// cbonf股性债性概览
import { getcbondHoldEquityBond } from '@/api/pages/Analysis.js';
export default {
	data() {
		return {
			empty1: true,
			cardLoading: true,
			optionpbroe: {},
			show: true,
			info: {}
		};
	},
	methods: {
		openvideo() {
			window.open('https://www.bilibili.com/video/BV1PN4y1T7Nk?share_source=copy_web');
		},
		getData(info) {
			this.info = info;
			this.getcbondHoldEquityBond();
		},
		async getcbondHoldEquityBond() {
			let data = await getcbondHoldEquityBond({
				flag: this.info.flag,
				code: this.info.code,
				type: this.info.type,
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			if (data?.mtycode == 200) {
				this.getChartData(data?.data, data?.rsquared);
			} else {
				this.hideLoading();
			}
		},
		getChartData(data, rsquared) {
			this.show = true;
			this.cardLoading = false;
			let datas = [];
			let datas2 = [];
			datas.push(data?.fund_pb_roe?.value);
			datas.push(data?.date);
			let maxpb = 30;
			let maxroe = 40;
			if (data?.fund_pb_roe?.date) {
				for (let i = 0; i < data.fund_pb_roe?.date?.length; i++) {
					if (Number(rsquared?.slope[i]) < 0 && rsquared?.intercept[i]) {
						datas[0][i].push([0, rsquared.intercept[i], '2017 Q4', '散点', '估值self']);
						datas[0][i].push([-Number(rsquared.intercept[i]) / Number(rsquared.slope[i]), 0, '2017 Q4', '散点', '估值self']);
					} else {
						datas[0][i].push([0, rsquared?.intercept[i], '2017 Q4', '散点', '估值self']);
						datas[0][i].push([
							maxroe,
							Number(maxroe) * Number(rsquared?.slope[i]) + Number(rsquared?.intercept[i]),
							'2017 Q4',
							'散点',
							'估值self'
						]);
					}
					datas2.push(
						'市场均衡线：PB=(' +
							rsquared?.intercept[i] +
							') + (' +
							rsquared?.slope[i] +
							') * roe            tip:R^2=' +
							Number(rsquared?.rsquared[i] * 100).toFixed(2) +
							'%'
					);
				}
				this.loading = false;
			} else {
				this.loading = true;
			}
			let max = 0;
			let templist = [];
			for (let i = 0; i < data.fund_pb_roe.value.length; i++) {
				for (let j = 0; j < data.fund_pb_roe.value[i].length; j++) {
					if (max < Number(data.fund_pb_roe.value[i][j][4])) max = Number(data.fund_pb_roe.value[i][j][4]);
				}
			}
			this.drawvalua(datas, datas2, Number(maxpb).toFixed(0), Number(maxroe).toFixed(0), max);
		},
		hideLoading() {
			this.show = false;
		},
		drawvalua(data, square, maxpb, maxroe, max) {
			// console.log('in?');
			// console.log(data);
			let tempaa = '';
			if (this.fundtype == 'hkequity' || this.fundtype == 'equityhk') {
				tempaa = '港股分布（基于一致ROE）';
			} else {
				tempaa = '转债股性债性';
			}
			if (
				data[0] == null ||
				data[0] == {} ||
				data[0] == [] ||
				data[0] == '数据缺失' ||
				data[0] == '' ||
				data[1] == null ||
				data[1] == {} ||
				data[1] == [] ||
				data[0] == '数据缺失' ||
				data[0] == ''
			) {
				this.empty1 = true;
			} else {
				//每个点的样式
				var itemStyle = {
					color: '#4096FF',
					opacity: 0.8,
					shadowBlur: 5,
					shadowOffsetX: 0,
					shadowOffsetY: 0,
					shadowColor: 'rgba(0, 0, 0, 0.5)'
				};
				//每个点的大小
				var sizeFunction = function (x) {
					var y = Math.sqrt(x / 5e8) + 0.1;
					return y * 80;
				};
				// Schema:tooptip提示插件包含的元素
				var schema = [
					{
						name: 'Income',
						index: 0,
						text: 'roe_est',
						unit: ''
					},
					{
						name: 'LifeExpectancy',
						index: 1,
						text: 'pb',
						unit: ''
					},
					{
						name: 'Population',
						index: 2,
						text: '点大小',
						unit: ''
					},
					{
						name: 'Country',
						index: 3,
						text: '估值',
						unit: ''
					}
				];

				// 绘制图表
				let option = {
					baseOption: {
						timeline: {
							axisType: 'category',
							orient: 'horizontal',
							autoPlay: true,
							inverse: true, //顺序 从小到大 1800->2015
							playInterval: 5000, //数据点渲染间隔
							left: null,
							left: 0,
							bottom: 0,
							height: '40px',
							width: '98%',
							label: {
								fontSize: '14px',
								color: '#000' //时间（标签）节点的样式
							},
							symbol: 'none',
							lineStyle: {
								color: '#000' //分隔线样式
							},
							checkpointStyle: {
								//当前时间节点样式（所在目标点样式）
								color: '#fff',
								borderColor: '#000',
								borderWidth: 2
							},
							controlStyle: {
								showNextBtn: false, //上一个
								showPrevBtn: false, //下一个
								color: '#666',
								borderColor: '#666'
							},
							emphasis: {
								label: {
									color: '#409eff'
								},
								controlStyle: {
									color: '#aaa',
									borderColor: '#aaa'
								}
							},
							data: []
						},
						backgroundColor: '#ffffff',
						// title: [
						// 	{
						// 		text: data[1][0],
						// 		textAlign: 'center',
						// 		left: '63%',
						// 		top: '65%',
						// 		textStyle: {
						// 			fontSize: '18px',
						// 			color: 'rgba(0, 0, 0, 0.2)'
						// 		}
						// 	},
						// 	{
						// 		text: tempaa,
						// 		left: 'center',
						// 		top: 10,
						// 		textStyle: {
						// 			color: '#000',
						// 			fontWeight: 'normal',
						// 			fontSize: '18px'
						// 		}
						// 	}
						// ],
						tooltip: {
							padding: 5,
							backgroundColor: '#222',
							borderColor: '#777',
							borderWidth: 1,
							textStyle: {
								fontSize: '18px'
							},
							formatter: function (obj) {
								var value = obj.value;
								return (
									'转债名称:' +
									value[3] +
									'<br>' +
									'季度:' +
									value[2] +
									'<br>' +
									'平底溢价率:' +
									(value[0] * 1).toFixed(2) +
									'<br>' +
									'转股溢价率:' +
									(value[1] * 1).toFixed(2) +
									'<br>' +
									'规模:' +
									(value[4] * 1).toFixed(2) +
									'亿<br>'
								);
							}
						},
						grid: {
							top: '32px',
							// containLabel: true
							left: '32px',
							right: '80px',
							bottom: '64px'
						},
						xAxis: {
							margin: 12,
							scale: true,
							type: 'value',
							name: '平底溢价率',
							// min: 0,
							// nameGap: 25,
							// nameLocation: 'middle',
							nameTextStyle: {
								'font-family': 'Helvetica Neue',
								'font-style': 'normal',
								'font-weight': 400,
								'font-size': '12px',
								'line-height': '20px',
								color: 'rgba(0, 0, 0, 0.65)'
							},
							splitLine: {
								show: true,
								lineStyle: {
									type: 'dashed'
								}
							},
							axisLine: {
								show: false,
								lineStyle: {
									color: '#e9e9e9'
								}
							},
							axisLabel: {
								fontSize: '18px',
								color: 'rgba(0,0,0,0.65)'
							}
						},
						yAxis: {
							margin: 16,
							// scale: true,
							type: 'value',
							name: '转股溢价率',
							// min: 0,
							nameTextStyle: {
								'font-family': 'Helvetica Neue',
								'font-style': 'normal',
								'font-weight': 400,
								'font-size': '12px',
								'line-height': '20px',
								color: 'rgba(0, 0, 0, 0.65)'
							},
							axisLine: {
								show: false,
								lineStyle: {
									color: '#e9e9e9'
								}
							},
							splitLine: {
								show: true,
								lineStyle: {
									type: 'dashed'
								}
							},

							axisLabel: {
								fontSize: '16px',
								color: 'rgba(0,0,0,0.65)'
							}
						},
						visualMap: [
							// {
							// 	show: false,
							// 	// categories: ['估值', '估值self'],
							// 	calculable: true,
							// 	precision: 0.1,
							// 	textGap: 30,
							// 	textStyle: {
							// 		fontSize: '18px',
							// 		color: '#ccc'
							// 	},
							// 	inRange: {
							// 		fontSize: '18px',
							// 		color:
							// 			//  ['#4096ff', '#4096ff', '#FD6865']
							// 			(function (val) {
							// 				console.log(val);
							// 				var colors = ['#4096ff']; //[ '#9dc5c8','#edc1a5', '#e88f70','#bcd3bb',  '#e1e8c8', '#7b7c68', '#e5b5b5', '#f0b489', '#928ea8', '#bda29a'];
							// 				return colors.concat(colors);
							// 			})()
							// 	}
							// },
							{
								show: false,
								right: '0px',
								top: '150px',
								min: 0,
								max: max,
								dimension: 4,
								itemWidth: 30,
								itemHeight: 120,
								precision: 0.1,
								text: ['配置权重%'],
								textGap: 0.0,
								textStyle: {
									color: 'black',
									fontSize: 10
								},
								inRange: {
									symbolSize: [10, 20]
								},
								controller: {
									inRange: {
										color: ['black']
									}
								}
							}
						],
						series: [
							{
								type: 'scatter',
								itemStyle: itemStyle,
								data: data[0][0]
							}
						],
						animationDurationUpdate: 1000,
						animationEasingUpdate: 'quinticInOut'
					},
					options: []
				};
				for (var n = 0; n < data[1].length; n++) {
					option.baseOption.timeline.data.push(data[1][n][0]);
					option.options.push({
						// title: {
						// 	show: true,
						// 	text: data[1][n] + '',
						// 	textStyle: {
						// 		fontSize: '18px'
						// 	}
						// },
						graphic: [
							{
								type: 'group',
								left: 'center',
								top: '50',
								children: [
									{
										type: 'text',
										z: 100,
										left: 'center',
										top: '50',
										style: {
											fill: '#333',
											text: data[1][n] + '',
											font: '14px' + ' Microsoft YaHei',
											colors: '#4096FF'
										}
									}
								]
							}
						],
						series: [
							// {
							// 	name: data[1][1],
							// 	type: 'line',
							// 	symbol: 'none',
							// 	itemStyle: {
							// 		color: '#409eff'
							// 	},
							// 	data: data[0][n].slice(data[0][n].length - 2, data[0][n].length)
							// },
							{
								name: data[1][n],
								type: 'scatter',
								itemStyle: itemStyle,
								data: data[0][n].slice(0, data[0][n].length).map((item) => {
									return {
										value: item,
										itemStyle: {
											color: item[0] * 1 > 20 ? '#4096ff' : item[0] * 1 > -20 ? '#4096ff' : '#FD6865'
										}
									};
								})
							}
						]
					});
				}
				console.log(option);
				this.optionpbroe = option;
				this.empty1 = false;
			}
		},
		createPrintWord() {
			this.$refs['valuationAnalysis'].mergeOptions({ toolbox: { show: false } });
			let height = this.$refs['valuationAnalysis'].$el.clientHeight;
			let width = this.$refs['valuationAnalysis'].$el.clientWidth;
			let chart = this.$refs['valuationAnalysis'].getDataURL({
				type: 'png',
				pixelRatio: 1,
				backgroundColor: '#fff'
			});
			this.$refs['valuationAnalysis'].mergeOptions({ toolbox: { show: true } });
			return [...exportTitle('估值分析'), ...exportChart(chart, { width, height })];
		}
	}
};
</script>

<style></style>
