<!--  -->
<template>
	<div>
		<div
			v-show="haveName != ''"
			style="
				font-weight: 400;
				font-size: 14px;
				line-height: 22px;
				color: rgba(0, 0, 0, 0.85);
				margin-left: 0px;
				margin-right: 16px;
				margin-bottom: 4px;
			"
		>
			{{ haveName }}
		</div>
		{{ address }}
		<div class="boxOnlyYSF">
			<div>
				<div>
					<operator :operator="operator" @resolveMathRange="resolveMathRange"></operator>
				</div>
			</div>
			<div v-show="showTime" style="margin-right: 16px">
				<el-cascader @change="change" placeholder="请选择时间范围" style="width: 100px" v-model="date" :options="option"> </el-cascader>
			</div>
			<div style="display: flex; align-items: center">
				<div>
					<el-input type="number" @input="inputChange" placeholder="权重（%），例60" v-model="input"></el-input>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import axios from '@/api/index';
import operator from '@/pages/filter/fund/beta/componentsFilter/components/operator2.vue';
export default {
	props: {
		showTime: {
			type: Boolean,
			default: true
		},
		haveName: {
			type: String,
			default: ''
		},
		dataX: {
			type: Object,
			default: {}
		},
		placeholder: {
			type: String
		},
		indexFlag: {
			type: Number
		},
		baseIndexFlag: {
			type: Number
		},
		optionsselect: {
			type: Array,
			default: []
		}
	},
	//import引入的组件需要注入到对象中才能使用
	components: { operator },
	data() {
		//这里存放数据
		return {
			iconFlag: '',
			showBox: false,
			mathRange: { mathRange: 'line' },
			input: '',
			date: '',
			operator: '',
			option: [
				{ value: 'now', label: '近期表现', children: [] }
				// {
				// 	value: 'from',
				// 	label: '从那时起',
				// 	children: [
				// 		{
				// 			value: '2015-08-26',
				// 			label: '2015-08-26'
				// 		}
				// 	]
				// }
			]
		};
	},
	//监听属性 类似于data概念
	computed: {
		address() {
			if (this.FUNC.isEmpty(this.optionsselect)) {
				this.option = this.optionsselect;
			}
		}
	},

	//监控data中的数据变化
	watch: {
		dataX(val) {
			if (val.dataResult && val.dataResult.length > 0) {
				this.showBox = true;
				this.iconFlag = val.dataResult[0].flag;
				this.input = val.dataResult[0].value;
				this.date = val.dataResult[0].date;
				this.option = val.dataResult[0].option;
				this.operator = val?.dataResult?.[0]?.operation?.mathRange || 'rank(0-100)';
				// this.getDate();
			}
		}
	},
	//方法集合
	methods: {
		resolveMathRange(obj) {
			this.mathRange = obj;
			this.resolveFather();
		},
		resolveFather() {
			this.$emit(
				'boxOnlyYSFNameChange',
				this.baseIndexFlag,
				this.indexFlag,
				this.input,
				this.iconFlag,
				this.date,
				this.option,
				this.showTime ? this.FUNC.isEmpty(this.input) && this.FUNC.isEmpty(this.date) : this.FUNC.isEmpty(this.input),
				this.mathRange
			);
		},
		change() {
			this.$emit(
				'boxOnlyYSFNameChange',
				this.baseIndexFlag,
				this.indexFlag,
				this.input,
				this.iconFlag,
				this.date,
				this.option,
				this.showTime ? this.FUNC.isEmpty(this.input) && this.FUNC.isEmpty(this.date) : this.FUNC.isEmpty(this.input),
				this.mathRange
			);
		},

		inputChange() {
			this.$emit(
				'boxOnlyYSFNameChange',
				this.baseIndexFlag,
				this.indexFlag,
				this.input,
				this.iconFlag,
				this.date,
				this.option,
				this.showTime ? this.FUNC.isEmpty(this.input) && this.FUNC.isEmpty(this.date) : this.FUNC.isEmpty(this.input),
				this.mathRange
			);
		},
		getDate() {
			let that = this;
			axios
				.get(that.$baseUrl + '/system/alpha/filter_risk_future/?file=boxName')
				.then((result) => {
          let res = result.data;
					if (this.haveName == '最大回撤比') {
						for (let i = 0; i < res.data.maxdrawdown_ratio.length; i++) {
							if (res.data.maxdrawdown_ratio[i] == '1w') that.option[0].children.push({ value: '1w', label: '一周' });
							if (res.data.maxdrawdown_ratio[i] == '2w') that.option[0].children.push({ value: '2w', label: '两周' });
							if (res.data.maxdrawdown_ratio[i] == '1m') that.option[0].children.push({ value: '1m', label: '一月' });
							if (res.data.maxdrawdown_ratio[i] == '2m') that.option[0].children.push({ value: '2m', label: '两月' });
							if (res.data.maxdrawdown_ratio[i] == '1q') that.option[0].children.push({ value: '1q', label: '一季' });
							if (res.data.maxdrawdown_ratio[i] == '2q') that.option[0].children.push({ value: '2q', label: '两季' });
							if (res.data.maxdrawdown_ratio[i] == '1y') that.option[0].children.push({ value: '1y', label: '一年' });
							if (res.data.maxdrawdown_ratio[i] == '2y') that.option[0].children.push({ value: '2y', label: '两年' });
							if (res.data.maxdrawdown_ratio[i] == '3y') that.option[0].children.push({ value: '3y', label: '三年' });
							if (res.data.maxdrawdown_ratio[i] == '5y') that.option[0].children.push({ value: '5y', label: '五年' });
						}
					}
					if (this.haveName == '波动率比') {
						for (let i = 0; i < res.data.volatilityratio.length; i++) {
							if (res.data.volatilityratio[i] == '1w') that.option[0].children.push({ value: '1w', label: '一周' });
							if (res.data.volatilityratio[i] == '2w') that.option[0].children.push({ value: '2w', label: '两周' });
							if (res.data.volatilityratio[i] == '1m') that.option[0].children.push({ value: '1m', label: '一月' });
							if (res.data.volatilityratio[i] == '2m') that.option[0].children.push({ value: '2m', label: '两月' });
							if (res.data.volatilityratio[i] == '1q') that.option[0].children.push({ value: '1q', label: '一季' });
							if (res.data.volatilityratio[i] == '2q') that.option[0].children.push({ value: '2q', label: '两季' });
							if (res.data.volatilityratio[i] == '1y') that.option[0].children.push({ value: '1y', label: '一年' });
							if (res.data.volatilityratio[i] == '2y') that.option[0].children.push({ value: '2y', label: '两年' });
							if (res.data.volatilityratio[i] == '3y') that.option[0].children.push({ value: '3y', label: '三年' });
							if (res.data.volatilityratio[i] == '5y') that.option[0].children.push({ value: '5y', label: '五年' });
						}
					}
					if (this.haveName == '年化波动率') {
						for (let i = 0; i < res.data.volatility.length; i++) {
							if (res.data.volatility[i] == '1w') that.option[0].children.push({ value: '1w', label: '一周' });
							if (res.data.volatility[i] == '2w') that.option[0].children.push({ value: '2w', label: '两周' });
							if (res.data.volatility[i] == '1m') that.option[0].children.push({ value: '1m', label: '一月' });
							if (res.data.volatility[i] == '2m') that.option[0].children.push({ value: '2m', label: '两月' });
							if (res.data.volatility[i] == '1q') that.option[0].children.push({ value: '1q', label: '一季' });
							if (res.data.volatility[i] == '2q') that.option[0].children.push({ value: '2q', label: '两季' });
							if (res.data.volatility[i] == '1y') that.option[0].children.push({ value: '1y', label: '一年' });
							if (res.data.volatility[i] == '2y') that.option[0].children.push({ value: '2y', label: '两年' });
							if (res.data.volatility[i] == '3y') that.option[0].children.push({ value: '3y', label: '三年' });
							if (res.data.volatility[i] == '5y') that.option[0].children.push({ value: '5y', label: '五年' });
						}
					}
					if (this.haveName == '最大回撤') {
						for (let i = 0; i < res.data.maxdrawdown.length; i++) {
							if (res.data.maxdrawdown[i] == '1w') that.option[0].children.push({ value: '1w', label: '一周' });
							if (res.data.maxdrawdown[i] == '2w') that.option[0].children.push({ value: '2w', label: '两周' });
							if (res.data.maxdrawdown[i] == '1m') that.option[0].children.push({ value: '1m', label: '一月' });
							if (res.data.maxdrawdown[i] == '2m') that.option[0].children.push({ value: '2m', label: '两月' });
							if (res.data.maxdrawdown[i] == '1q') that.option[0].children.push({ value: '1q', label: '一季' });
							if (res.data.maxdrawdown[i] == '2q') that.option[0].children.push({ value: '2q', label: '两季' });
							if (res.data.maxdrawdown[i] == '1y') that.option[0].children.push({ value: '1y', label: '一年' });
							if (res.data.maxdrawdown[i] == '2y') that.option[0].children.push({ value: '2y', label: '两年' });
							if (res.data.maxdrawdown[i] == '3y') that.option[0].children.push({ value: '3y', label: '三年' });
							if (res.data.maxdrawdown[i] == '5y') that.option[0].children.push({ value: '5y', label: '五年' });
						}
					}
					if (this.haveName == '平均下行周期') {
						for (let i = 0; i < res.data.averagelength.length; i++) {
							if (res.data.averagelength[i] == '1w') that.option[0].children.push({ value: '1w', label: '一周' });
							if (res.data.averagelength[i] == '2w') that.option[0].children.push({ value: '2w', label: '两周' });
							if (res.data.averagelength[i] == '1m') that.option[0].children.push({ value: '1m', label: '一月' });
							if (res.data.averagelength[i] == '2m') that.option[0].children.push({ value: '2m', label: '两月' });
							if (res.data.averagelength[i] == '1q') that.option[0].children.push({ value: '1q', label: '一季' });
							if (res.data.averagelength[i] == '2q') that.option[0].children.push({ value: '2q', label: '两季' });
							if (res.data.averagelength[i] == '1y') that.option[0].children.push({ value: '1y', label: '一年' });
							if (res.data.averagelength[i] == '2y') that.option[0].children.push({ value: '2y', label: '两年' });
							if (res.data.averagelength[i] == '3y') that.option[0].children.push({ value: '3y', label: '三年' });
							if (res.data.averagelength[i] == '5y') that.option[0].children.push({ value: '5y', label: '五年' });
						}
					}
					if (this.haveName == '平均恢复周期') {
						for (let i = 0; i < res.data.averagerecovery.length; i++) {
							if (res.data.averagerecovery[i] == '1w') that.option[0].children.push({ value: '1w', label: '一周' });
							if (res.data.averagerecovery[i] == '2w') that.option[0].children.push({ value: '2w', label: '两周' });
							if (res.data.averagerecovery[i] == '1m') that.option[0].children.push({ value: '1m', label: '一月' });
							if (res.data.averagerecovery[i] == '2m') that.option[0].children.push({ value: '2m', label: '两月' });
							if (res.data.averagerecovery[i] == '1q') that.option[0].children.push({ value: '1q', label: '一季' });
							if (res.data.averagerecovery[i] == '2q') that.option[0].children.push({ value: '2q', label: '两季' });
							if (res.data.averagerecovery[i] == '1y') that.option[0].children.push({ value: '1y', label: '一年' });
							if (res.data.averagerecovery[i] == '2y') that.option[0].children.push({ value: '2y', label: '两年' });
							if (res.data.averagerecovery[i] == '3y') that.option[0].children.push({ value: '3y', label: '三年' });
							if (res.data.averagerecovery[i] == '5y') that.option[0].children.push({ value: '5y', label: '五年' });
						}
					}
					if (this.haveName == '在险价值') {
						for (let i = 0; i < res.data.volatility.length; i++) {
							if (res.data.VaR05[i] == '1w') that.option[0].children.push({ value: '1w', label: '一周' });
							if (res.data.VaR05[i] == '2w') that.option[0].children.push({ value: '2w', label: '两周' });
							if (res.data.VaR05[i] == '1m') that.option[0].children.push({ value: '1m', label: '一月' });
							if (res.data.VaR05[i] == '2m') that.option[0].children.push({ value: '2m', label: '两月' });
							if (res.data.VaR05[i] == '1q') that.option[0].children.push({ value: '1q', label: '一季' });
							if (res.data.VaR05[i] == '2q') that.option[0].children.push({ value: '2q', label: '两季' });
							if (res.data.VaR05[i] == '1y') that.option[0].children.push({ value: '1y', label: '一年' });
							if (res.data.VaR05[i] == '2y') that.option[0].children.push({ value: '2y', label: '两年' });
							if (res.data.VaR05[i] == '3y') that.option[0].children.push({ value: '3y', label: '三年' });
							if (res.data.VaR05[i] == '5y') that.option[0].children.push({ value: '5y', label: '五年' });
						}
					}
					if (this.haveName == '期望损失') {
						for (let i = 0; i < res.data.ES05.length; i++) {
							if (res.data.ES05[i] == '1w') that.option[0].children.push({ value: '1w', label: '一周' });
							if (res.data.ES05[i] == '2w') that.option[0].children.push({ value: '2w', label: '两周' });
							if (res.data.ES05[i] == '1m') that.option[0].children.push({ value: '1m', label: '一月' });
							if (res.data.ES05[i] == '2m') that.option[0].children.push({ value: '2m', label: '两月' });
							if (res.data.ES05[i] == '1q') that.option[0].children.push({ value: '1q', label: '一季' });
							if (res.data.ES05[i] == '2q') that.option[0].children.push({ value: '2q', label: '两季' });
							if (res.data.ES05[i] == '1y') that.option[0].children.push({ value: '1y', label: '一年' });
							if (res.data.ES05[i] == '2y') that.option[0].children.push({ value: '2y', label: '两年' });
							if (res.data.ES05[i] == '3y') that.option[0].children.push({ value: '3y', label: '三年' });
							if (res.data.ES05[i] == '5y') that.option[0].children.push({ value: '5y', label: '五年' });
						}
					}
					if (this.haveName == '下行风险') {
						for (let i = 0; i < res.data.downsidevolatility.length; i++) {
							if (res.data.downsidevolatility[i] == '1w') that.option[0].children.push({ value: '1w', label: '一周' });
							if (res.data.downsidevolatility[i] == '2w') that.option[0].children.push({ value: '2w', label: '两周' });
							if (res.data.downsidevolatility[i] == '1m') that.option[0].children.push({ value: '1m', label: '一月' });
							if (res.data.downsidevolatility[i] == '2m') that.option[0].children.push({ value: '2m', label: '两月' });
							if (res.data.downsidevolatility[i] == '1q') that.option[0].children.push({ value: '1q', label: '一季' });
							if (res.data.downsidevolatility[i] == '2q') that.option[0].children.push({ value: '2q', label: '两季' });
							if (res.data.downsidevolatility[i] == '1y') that.option[0].children.push({ value: '1y', label: '一年' });
							if (res.data.downsidevolatility[i] == '2y') that.option[0].children.push({ value: '2y', label: '两年' });
							if (res.data.downsidevolatility[i] == '3y') that.option[0].children.push({ value: '3y', label: '三年' });
							if (res.data.downsidevolatility[i] == '5y') that.option[0].children.push({ value: '5y', label: '五年' });
						}
					}
					if (this.haveName == '痛苦指数') {
						for (let i = 0; i < res.data.painindex.length; i++) {
							if (res.data.painindex[i] == '1w') that.option[0].children.push({ value: '1w', label: '一周' });
							if (res.data.painindex[i] == '2w') that.option[0].children.push({ value: '2w', label: '两周' });
							if (res.data.painindex[i] == '1m') that.option[0].children.push({ value: '1m', label: '一月' });
							if (res.data.painindex[i] == '2m') that.option[0].children.push({ value: '2m', label: '两月' });
							if (res.data.painindex[i] == '1q') that.option[0].children.push({ value: '1q', label: '一季' });
							if (res.data.painindex[i] == '2q') that.option[0].children.push({ value: '2q', label: '两季' });
							if (res.data.painindex[i] == '1y') that.option[0].children.push({ value: '1y', label: '一年' });
							if (res.data.painindex[i] == '2y') that.option[0].children.push({ value: '2y', label: '两年' });
							if (res.data.painindex[i] == '3y') that.option[0].children.push({ value: '3y', label: '三年' });
							if (res.data.painindex[i] == '5y') that.option[0].children.push({ value: '5y', label: '五年' });
						}
					}
					if (this.haveName == '年化收益率') {
						for (let i = 0; i < res.data.ave_return.length; i++) {
							if (res.data.ave_return[i] == '1w') that.option[0].children.push({ value: '1w', label: '一周' });
							if (res.data.ave_return[i] == '2w') that.option[0].children.push({ value: '2w', label: '两周' });
							if (res.data.ave_return[i] == '1m') that.option[0].children.push({ value: '1m', label: '一月' });
							if (res.data.ave_return[i] == '2m') that.option[0].children.push({ value: '2m', label: '两月' });
							if (res.data.ave_return[i] == '1q') that.option[0].children.push({ value: '1q', label: '一季' });
							if (res.data.ave_return[i] == '2q') that.option[0].children.push({ value: '2q', label: '两季' });
							if (res.data.ave_return[i] == '1y') that.option[0].children.push({ value: '1y', label: '一年' });
							if (res.data.ave_return[i] == '2y') that.option[0].children.push({ value: '2y', label: '两年' });
							if (res.data.ave_return[i] == '3y') that.option[0].children.push({ value: '3y', label: '三年' });
							if (res.data.ave_return[i] == '5y') that.option[0].children.push({ value: '5y', label: '五年' });
						}
					}
					if (this.haveName == '累计收益率') {
						for (let i = 0; i < res.data.cum_return.length; i++) {
							if (res.data.cum_return[i] == '1w') that.option[0].children.push({ value: '1w', label: '一周' });
							if (res.data.cum_return[i] == '2w') that.option[0].children.push({ value: '2w', label: '两周' });
							if (res.data.cum_return[i] == '1m') that.option[0].children.push({ value: '1m', label: '一月' });
							if (res.data.cum_return[i] == '2m') that.option[0].children.push({ value: '2m', label: '两月' });
							if (res.data.cum_return[i] == '1q') that.option[0].children.push({ value: '1q', label: '一季' });
							if (res.data.cum_return[i] == '2q') that.option[0].children.push({ value: '2q', label: '两季' });
							if (res.data.cum_return[i] == '1y') that.option[0].children.push({ value: '1y', label: '一年' });
							if (res.data.cum_return[i] == '2y') that.option[0].children.push({ value: '2y', label: '两年' });
							if (res.data.cum_return[i] == '3y') that.option[0].children.push({ value: '3y', label: '三年' });
							if (res.data.cum_return[i] == '5y') that.option[0].children.push({ value: '5y', label: '五年' });
						}
					}
					if (this.haveName == '夏普率（rf=0）') {
						for (let i = 0; i < res.data.sharpe0.length; i++) {
							if (res.data.sharpe0[i] == '1w') that.option[0].children.push({ value: '1w', label: '一周' });
							if (res.data.sharpe0[i] == '2w') that.option[0].children.push({ value: '2w', label: '两周' });
							if (res.data.sharpe0[i] == '1m') that.option[0].children.push({ value: '1m', label: '一月' });
							if (res.data.sharpe0[i] == '2m') that.option[0].children.push({ value: '2m', label: '两月' });
							if (res.data.sharpe0[i] == '1q') that.option[0].children.push({ value: '1q', label: '一季' });
							if (res.data.sharpe0[i] == '2q') that.option[0].children.push({ value: '2q', label: '两季' });
							if (res.data.sharpe0[i] == '1y') that.option[0].children.push({ value: '1y', label: '一年' });
							if (res.data.sharpe0[i] == '2y') that.option[0].children.push({ value: '2y', label: '两年' });
							if (res.data.sharpe0[i] == '3y') that.option[0].children.push({ value: '3y', label: '三年' });
							if (res.data.sharpe0[i] == '5y') that.option[0].children.push({ value: '5y', label: '五年' });
						}
					}
					if (this.haveName == '夏普率（rf=4%）') {
						// console.log("夏普率（rf==4%");
						for (let i = 0; i < res.data.sharpe04.length; i++) {
							if (res.data.sharpe04[i] == '1w') that.option[0].children.push({ value: '1w', label: '一周' });
							if (res.data.sharpe04[i] == '2w') that.option[0].children.push({ value: '2w', label: '两周' });
							if (res.data.sharpe04[i] == '1m') that.option[0].children.push({ value: '1m', label: '一月' });
							if (res.data.sharpe04[i] == '2m') that.option[0].children.push({ value: '2m', label: '两月' });
							if (res.data.sharpe04[i] == '1q') that.option[0].children.push({ value: '1q', label: '一季' });
							if (res.data.sharpe04[i] == '2q') that.option[0].children.push({ value: '2q', label: '两季' });
							if (res.data.sharpe04[i] == '1y') that.option[0].children.push({ value: '1y', label: '一年' });
							if (res.data.sharpe04[i] == '2y') that.option[0].children.push({ value: '2y', label: '两年' });
							if (res.data.sharpe04[i] == '3y') that.option[0].children.push({ value: '3y', label: '三年' });
							if (res.data.sharpe04[i] == '5y') that.option[0].children.push({ value: '5y', label: '五年' });
						}
						console.log(object);
					}
					if (this.haveName == '夏普率（动态rf）' || this.haveName == '夏普率') {
						for (let i = 0; i < res.data.sharpe.length; i++) {
							if (res.data.sharpe[i] == '1w') that.option[0].children.push({ value: '1w', label: '一周' });
							if (res.data.sharpe[i] == '2w') that.option[0].children.push({ value: '2w', label: '两周' });
							if (res.data.sharpe[i] == '1m') that.option[0].children.push({ value: '1m', label: '一月' });
							if (res.data.sharpe[i] == '2m') that.option[0].children.push({ value: '2m', label: '两月' });
							if (res.data.sharpe[i] == '1q') that.option[0].children.push({ value: '1q', label: '一季' });
							if (res.data.sharpe[i] == '2q') that.option[0].children.push({ value: '2q', label: '两季' });
							if (res.data.sharpe[i] == '1y') that.option[0].children.push({ value: '1y', label: '一年' });
							if (res.data.sharpe[i] == '2y') that.option[0].children.push({ value: '2y', label: '两年' });
							if (res.data.sharpe[i] == '3y') that.option[0].children.push({ value: '3y', label: '三年' });
							if (res.data.sharpe[i] == '5y') that.option[0].children.push({ value: '5y', label: '五年' });
						}
					}
					if (this.haveName == '卡码率') {
						for (let i = 0; i < res.data.calmar.length; i++) {
							if (res.data.calmar[i] == '1w') that.option[0].children.push({ value: '1w', label: '一周' });
							if (res.data.calmar[i] == '2w') that.option[0].children.push({ value: '2w', label: '两周' });
							if (res.data.calmar[i] == '1m') that.option[0].children.push({ value: '1m', label: '一月' });
							if (res.data.calmar[i] == '2m') that.option[0].children.push({ value: '2m', label: '两月' });
							if (res.data.calmar[i] == '1q') that.option[0].children.push({ value: '1q', label: '一季' });
							if (res.data.calmar[i] == '2q') that.option[0].children.push({ value: '2q', label: '两季' });
							if (res.data.calmar[i] == '1y') that.option[0].children.push({ value: '1y', label: '一年' });
							if (res.data.calmar[i] == '2y') that.option[0].children.push({ value: '2y', label: '两年' });
							if (res.data.calmar[i] == '3y') that.option[0].children.push({ value: '3y', label: '三年' });
							if (res.data.calmar[i] == '5y') that.option[0].children.push({ value: '5y', label: '五年' });
						}
					}
					if (this.haveName == '索提诺系数（rf=0）') {
						for (let i = 0; i < res.data.sortino0.length; i++) {
							if (res.data.sortino0[i] == '1w') that.option[0].children.push({ value: '1w', label: '一周' });
							if (res.data.sortino0[i] == '2w') that.option[0].children.push({ value: '2w', label: '两周' });
							if (res.data.sortino0[i] == '1m') that.option[0].children.push({ value: '1m', label: '一月' });
							if (res.data.sortino0[i] == '2m') that.option[0].children.push({ value: '2m', label: '两月' });
							if (res.data.sortino0[i] == '1q') that.option[0].children.push({ value: '1q', label: '一季' });
							if (res.data.sortino0[i] == '2q') that.option[0].children.push({ value: '2q', label: '两季' });
							if (res.data.sortino0[i] == '1y') that.option[0].children.push({ value: '1y', label: '一年' });
							if (res.data.sortino0[i] == '2y') that.option[0].children.push({ value: '2y', label: '两年' });
							if (res.data.sortino0[i] == '3y') that.option[0].children.push({ value: '3y', label: '三年' });
							if (res.data.sortino0[i] == '5y') that.option[0].children.push({ value: '5y', label: '五年' });
						}
					}
					if (this.haveName == '索提诺系数（rf=0.04）') {
						for (let i = 0; i < res.data.sortino04.length; i++) {
							if (res.data.sortino04[i] == '1w') that.option[0].children.push({ value: '1w', label: '一周' });
							if (res.data.sortino04[i] == '2w') that.option[0].children.push({ value: '2w', label: '两周' });
							if (res.data.sortino04[i] == '1m') that.option[0].children.push({ value: '1m', label: '一月' });
							if (res.data.sortino04[i] == '2m') that.option[0].children.push({ value: '2m', label: '两月' });
							if (res.data.sortino04[i] == '1q') that.option[0].children.push({ value: '1q', label: '一季' });
							if (res.data.sortino04[i] == '2q') that.option[0].children.push({ value: '2q', label: '两季' });
							if (res.data.sortino04[i] == '1y') that.option[0].children.push({ value: '1y', label: '一年' });
							if (res.data.sortino04[i] == '2y') that.option[0].children.push({ value: '2y', label: '两年' });
							if (res.data.sortino04[i] == '3y') that.option[0].children.push({ value: '3y', label: '三年' });
							if (res.data.sortino04[i] == '5y') that.option[0].children.push({ value: '5y', label: '五年' });
						}
					}
					if (this.haveName == '索提诺系数（动态rf）') {
						for (let i = 0; i < res.data.sortino.length; i++) {
							if (res.data.sortino[i] == '1w') that.option[0].children.push({ value: '1w', label: '一周' });
							if (res.data.sortino[i] == '2w') that.option[0].children.push({ value: '2w', label: '两周' });
							if (res.data.sortino[i] == '1m') that.option[0].children.push({ value: '1m', label: '一月' });
							if (res.data.sortino[i] == '2m') that.option[0].children.push({ value: '2m', label: '两月' });
							if (res.data.sortino[i] == '1q') that.option[0].children.push({ value: '1q', label: '一季' });
							if (res.data.sortino[i] == '2q') that.option[0].children.push({ value: '2q', label: '两季' });
							if (res.data.sortino[i] == '1y') that.option[0].children.push({ value: '1y', label: '一年' });
							if (res.data.sortino[i] == '2y') that.option[0].children.push({ value: '2y', label: '两年' });
							if (res.data.sortino[i] == '3y') that.option[0].children.push({ value: '3y', label: '三年' });
							if (res.data.sortino[i] == '5y') that.option[0].children.push({ value: '5y', label: '五年' });
						}
					}
					if (this.haveName == '稳定系数') {
						for (let i = 0; i < res.data.hurstindex.length; i++) {
							if (res.data.hurstindex[i] == '1w') that.option[0].children.push({ value: '1w', label: '一周' });
							if (res.data.hurstindex[i] == '2w') that.option[0].children.push({ value: '2w', label: '两周' });
							if (res.data.hurstindex[i] == '1m') that.option[0].children.push({ value: '1m', label: '一月' });
							if (res.data.hurstindex[i] == '2m') that.option[0].children.push({ value: '2m', label: '两月' });
							if (res.data.hurstindex[i] == '1q') that.option[0].children.push({ value: '1q', label: '一季' });
							if (res.data.hurstindex[i] == '2q') that.option[0].children.push({ value: '2q', label: '两季' });
							if (res.data.hurstindex[i] == '1y') that.option[0].children.push({ value: '1y', label: '一年' });
							if (res.data.hurstindex[i] == '2y') that.option[0].children.push({ value: '2y', label: '两年' });
							if (res.data.hurstindex[i] == '3y') that.option[0].children.push({ value: '3y', label: '三年' });
							if (res.data.hurstindex[i] == '5y') that.option[0].children.push({ value: '5y', label: '五年' });
						}
					}
					if (this.haveName == '凯利系数') {
						for (let i = 0; i < res.data.kelly.length; i++) {
							if (res.data.kelly[i] == '1w') that.option[0].children.push({ value: '1w', label: '一周' });
							if (res.data.kelly[i] == '2w') that.option[0].children.push({ value: '2w', label: '两周' });
							if (res.data.kelly[i] == '1m') that.option[0].children.push({ value: '1m', label: '一月' });
							if (res.data.kelly[i] == '2m') that.option[0].children.push({ value: '2m', label: '两月' });
							if (res.data.kelly[i] == '1q') that.option[0].children.push({ value: '1q', label: '一季' });
							if (res.data.kelly[i] == '2q') that.option[0].children.push({ value: '2q', label: '两季' });
							if (res.data.kelly[i] == '1y') that.option[0].children.push({ value: '1y', label: '一年' });
							if (res.data.kelly[i] == '2y') that.option[0].children.push({ value: '2y', label: '两年' });
							if (res.data.kelly[i] == '3y') that.option[0].children.push({ value: '3y', label: '三年' });
							if (res.data.kelly[i] == '5y') that.option[0].children.push({ value: '5y', label: '五年' });
						}
					}
					if (this.haveName == '信息比率') {
						for (let i = 0; i < res.data.information.length; i++) {
							if (res.data.information[i] == '1w') that.option[0].children.push({ value: '1w', label: '一周' });
							if (res.data.information[i] == '2w') that.option[0].children.push({ value: '2w', label: '两周' });
							if (res.data.information[i] == '1m') that.option[0].children.push({ value: '1m', label: '一月' });
							if (res.data.information[i] == '2m') that.option[0].children.push({ value: '2m', label: '两月' });
							if (res.data.information[i] == '1q') that.option[0].children.push({ value: '1q', label: '一季' });
							if (res.data.information[i] == '2q') that.option[0].children.push({ value: '2q', label: '两季' });
							if (res.data.information[i] == '1y') that.option[0].children.push({ value: '1y', label: '一年' });
							if (res.data.information[i] == '2y') that.option[0].children.push({ value: '2y', label: '两年' });
							if (res.data.information[i] == '3y') that.option[0].children.push({ value: '3y', label: '三年' });
							if (res.data.information[i] == '5y') that.option[0].children.push({ value: '5y', label: '五年' });
						}
					}
					if (this.haveName == '上攻潜力') {
						for (let i = 0; i < res.data.upsidepotential.length; i++) {
							if (res.data.upsidepotential[i] == '1w') that.option[0].children.push({ value: '1w', label: '一周' });
							if (res.data.upsidepotential[i] == '2w') that.option[0].children.push({ value: '2w', label: '两周' });
							if (res.data.upsidepotential[i] == '1m') that.option[0].children.push({ value: '1m', label: '一月' });
							if (res.data.upsidepotential[i] == '2m') that.option[0].children.push({ value: '2m', label: '两月' });
							if (res.data.upsidepotential[i] == '1q') that.option[0].children.push({ value: '1q', label: '一季' });
							if (res.data.upsidepotential[i] == '2q') that.option[0].children.push({ value: '2q', label: '两季' });
							if (res.data.upsidepotential[i] == '1y') that.option[0].children.push({ value: '1y', label: '一年' });
							if (res.data.upsidepotential[i] == '2y') that.option[0].children.push({ value: '2y', label: '两年' });
							if (res.data.upsidepotential[i] == '3y') that.option[0].children.push({ value: '3y', label: '三年' });
							if (res.data.upsidepotential[i] == '5y') that.option[0].children.push({ value: '5y', label: '五年' });
						}
					}
					if (this.haveName == '月胜率') {
						for (let i = 0; i < res.data.monthly_win_ratio.length; i++) {
							if (res.data.monthly_win_ratio[i] == '1w') that.option[0].children.push({ value: '1w', label: '一周' });
							if (res.data.monthly_win_ratio[i] == '2w') that.option[0].children.push({ value: '2w', label: '两周' });
							if (res.data.monthly_win_ratio[i] == '1m') that.option[0].children.push({ value: '1m', label: '一月' });
							if (res.data.monthly_win_ratio[i] == '2m') that.option[0].children.push({ value: '2m', label: '两月' });
							if (res.data.monthly_win_ratio[i] == '1q') that.option[0].children.push({ value: '1q', label: '一季' });
							if (res.data.monthly_win_ratio[i] == '2q') that.option[0].children.push({ value: '2q', label: '两季' });
							if (res.data.monthly_win_ratio[i] == '1y') that.option[0].children.push({ value: '1y', label: '一年' });
							if (res.data.monthly_win_ratio[i] == '2y') that.option[0].children.push({ value: '2y', label: '两年' });
							if (res.data.monthly_win_ratio[i] == '3y') that.option[0].children.push({ value: '3y', label: '三年' });
							if (res.data.monthly_win_ratio[i] == '5y') that.option[0].children.push({ value: '5y', label: '五年' });
						}
					}
					if (this.haveName == '詹森系数') {
						for (let i = 0; i < res.data.jensen.length; i++) {
							if (res.data.jensen[i] == '1w') that.option[0].children.push({ value: '1w', label: '一周' });
							if (res.data.jensen[i] == '2w') that.option[0].children.push({ value: '2w', label: '两周' });
							if (res.data.jensen[i] == '1m') that.option[0].children.push({ value: '1m', label: '一月' });
							if (res.data.jensen[i] == '2m') that.option[0].children.push({ value: '2m', label: '两月' });
							if (res.data.jensen[i] == '1q') that.option[0].children.push({ value: '1q', label: '一季' });
							if (res.data.jensen[i] == '2q') that.option[0].children.push({ value: '2q', label: '两季' });
							if (res.data.jensen[i] == '1y') that.option[0].children.push({ value: '1y', label: '一年' });
							if (res.data.jensen[i] == '2y') that.option[0].children.push({ value: '2y', label: '两年' });
							if (res.data.jensen[i] == '3y') that.option[0].children.push({ value: '3y', label: '三年' });
							if (res.data.jensen[i] == '5y') that.option[0].children.push({ value: '5y', label: '五年' });
						}
					}
					if (this.haveName == '特诺系数') {
						for (let i = 0; i < res.data.treynor.length; i++) {
							if (res.data.treynor[i] == '1w') that.option[0].children.push({ value: '1w', label: '一周' });
							if (res.data.treynor[i] == '2w') that.option[0].children.push({ value: '2w', label: '两周' });
							if (res.data.treynor[i] == '1m') that.option[0].children.push({ value: '1m', label: '一月' });
							if (res.data.treynor[i] == '2m') that.option[0].children.push({ value: '2m', label: '两月' });
							if (res.data.treynor[i] == '1q') that.option[0].children.push({ value: '1q', label: '一季' });
							if (res.data.treynor[i] == '2q') that.option[0].children.push({ value: '2q', label: '两季' });
							if (res.data.treynor[i] == '1y') that.option[0].children.push({ value: '1y', label: '一年' });
							if (res.data.treynor[i] == '2y') that.option[0].children.push({ value: '2y', label: '两年' });
							if (res.data.treynor[i] == '3y') that.option[0].children.push({ value: '3y', label: '三年' });
							if (res.data.treynor[i] == '5y') that.option[0].children.push({ value: '5y', label: '五年' });
						}
					}
					if (this.haveName == '上行捕获') {
						for (let i = 0; i < res.data.bullreturn.length; i++) {
							if (res.data.bullreturn[i] == '1w') that.option[0].children.push({ value: '1w', label: '一周' });
							if (res.data.bullreturn[i] == '2w') that.option[0].children.push({ value: '2w', label: '两周' });
							if (res.data.bullreturn[i] == '1m') that.option[0].children.push({ value: '1m', label: '一月' });
							if (res.data.bullreturn[i] == '2m') that.option[0].children.push({ value: '2m', label: '两月' });
							if (res.data.bullreturn[i] == '1q') that.option[0].children.push({ value: '1q', label: '一季' });
							if (res.data.bullreturn[i] == '2q') that.option[0].children.push({ value: '2q', label: '两季' });
							if (res.data.bullreturn[i] == '1y') that.option[0].children.push({ value: '1y', label: '一年' });
							if (res.data.bullreturn[i] == '2y') that.option[0].children.push({ value: '2y', label: '两年' });
							if (res.data.bullreturn[i] == '3y') that.option[0].children.push({ value: '3y', label: '三年' });
							if (res.data.bullreturn[i] == '5y') that.option[0].children.push({ value: '5y', label: '五年' });
						}
					}
					if (this.haveName == '下行捕获') {
						for (let i = 0; i < res.data.bearreturn.length; i++) {
							if (res.data.bearreturn[i] == '1w') that.option[0].children.push({ value: '1w', label: '一周' });
							if (res.data.bearreturn[i] == '2w') that.option[0].children.push({ value: '2w', label: '两周' });
							if (res.data.bearreturn[i] == '1m') that.option[0].children.push({ value: '1m', label: '一月' });
							if (res.data.bearreturn[i] == '2m') that.option[0].children.push({ value: '2m', label: '两月' });
							if (res.data.bearreturn[i] == '1q') that.option[0].children.push({ value: '1q', label: '一季' });
							if (res.data.bearreturn[i] == '2q') that.option[0].children.push({ value: '2q', label: '两季' });
							if (res.data.bearreturn[i] == '1y') that.option[0].children.push({ value: '1y', label: '一年' });
							if (res.data.bearreturn[i] == '2y') that.option[0].children.push({ value: '2y', label: '两年' });
							if (res.data.bearreturn[i] == '3y') that.option[0].children.push({ value: '3y', label: '三年' });
							if (res.data.bearreturn[i] == '5y') that.option[0].children.push({ value: '5y', label: '五年' });
						}
					}
					if (this.haveName == '择时gamma') {
						for (let i = 0; i < res.data.gamma.length; i++) {
							if (res.data.gamma[i] == '1w') that.option[0].children.push({ value: '1w', label: '一周' });
							if (res.data.gamma[i] == '2w') that.option[0].children.push({ value: '2w', label: '两周' });
							if (res.data.gamma[i] == '1m') that.option[0].children.push({ value: '1m', label: '一月' });
							if (res.data.gamma[i] == '2m') that.option[0].children.push({ value: '2m', label: '两月' });
							if (res.data.gamma[i] == '1q') that.option[0].children.push({ value: '1q', label: '一季' });
							if (res.data.gamma[i] == '2q') that.option[0].children.push({ value: '2q', label: '两季' });
							if (res.data.gamma[i] == '1y') that.option[0].children.push({ value: '1y', label: '一年' });
							if (res.data.gamma[i] == '2y') that.option[0].children.push({ value: '2y', label: '两年' });
							if (res.data.gamma[i] == '3y') that.option[0].children.push({ value: '3y', label: '三年' });
							if (res.data.gamma[i] == '5y') that.option[0].children.push({ value: '5y', label: '五年' });
						}
					}
					if (this.haveName == 'M2') {
						for (let i = 0; i < res.data.msquared.length; i++) {
							if (res.data.msquared[i] == '1w') that.option[0].children.push({ value: '1w', label: '一周' });
							if (res.data.msquared[i] == '2w') that.option[0].children.push({ value: '2w', label: '两周' });
							if (res.data.msquared[i] == '1m') that.option[0].children.push({ value: '1m', label: '一月' });
							if (res.data.msquared[i] == '2m') that.option[0].children.push({ value: '2m', label: '两月' });
							if (res.data.msquared[i] == '1q') that.option[0].children.push({ value: '1q', label: '一季' });
							if (res.data.msquared[i] == '2q') that.option[0].children.push({ value: '2q', label: '两季' });
							if (res.data.msquared[i] == '1y') that.option[0].children.push({ value: '1y', label: '一年' });
							if (res.data.msquared[i] == '2y') that.option[0].children.push({ value: '2y', label: '两年' });
							if (res.data.msquared[i] == '3y') that.option[0].children.push({ value: '3y', label: '三年' });
							if (res.data.msquared[i] == '5y') that.option[0].children.push({ value: '5y', label: '五年' });
						}
					}
				})
				.catch((err) => {});
		}
	},
	//生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		// this.getDate();
		if (JSON.stringify(this.dataX) != '{}') {
			if (this.dataX.dataResult && this.dataX.dataResult.length > 0) {
				this.showBox = true;
				this.iconFlag = this.dataX.dataResult[0].flag;
				this.input = this.dataX.dataResult[0].value;
				this.date = this.dataX.dataResult[0].date;
				this.option = this.dataX.dataResult[0].option;
				this.operator = this.dataX?.dataResult[0]?.operation?.mathRange || 'rank(0-100)';
			}
		}
	}
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
.boxOnlyYSF {
	display: flex;
	align-items: center;
}
</style>
