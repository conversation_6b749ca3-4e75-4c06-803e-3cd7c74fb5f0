<template>
  <div id="styleLabel">
    <analysis-card-title
      title="权益分年度持仓风格"
      @downloadExcel="exportExcel"
    >
    </analysis-card-title>
    <el-table v-loading="loading" border stripe :data="data" max-height="460px">
      <el-table-column
        v-for="item in column"
        :key="item.value"
        :prop="item.value"
        :label="item.label"
        align="gotoleft"
      ></el-table-column>
      <template slot="empty">
        <el-empty image-size="160"></el-empty>
      </template>
    </el-table>
  </div>
</template>

<script>
import { filter_json_to_excel } from "@/utils/exportExcel.js";
// 权益分年度持仓风格
import { getStyleInfo } from "@/api/pages/Analysis.js";
// 权益分年度持仓风格
export default {
  name: "styleLabel",
  data() {
    return {
      data: [],
      column: [
        {
          label: "年份",
          value: "year",
        },
        {
          label: "成长价值",
          value: "valuegrowth",
        },
        {
          label: "大小盘",
          value: "bigsmall",
        },
        {
          label: "个股择时",
          value: "timing",
        },
        {
          label: "进攻防守",
          value: "volatility",
        },
        {
          label: "大行业",
          value: "industrysector",
        },
      ],
      loading: true,
      info: {},
    };
  },
  methods: {
    // 获取权益分年度持仓风格数据
    async getStyleInfo() {
      this.loading = true;
      let data = await getStyleInfo({
        code: this.info.code,
        type: this.info.type,
        flag: this.info.flag,
        start_date: this.info.start_date,
        end_date: this.info.end_date,
      });
      this.loading = false;
      if (data?.mtycode == 200) {
        this.data = data?.data.sort((a, b) => {
          return b.year - a.year;
        });
      } else {
        this.data = [];
      }
    },
    async getData(info) {
      this.info = info;
      await this.getStyleInfo();
    },
    exportExcel() {
      let list = this.column;
      filter_json_to_excel(list, this.data, "权益分年度持仓风格");
    },
    async createPrintWord(info) {
      await this.getData(info);
      let list = this.column;
      if (this.data.length) {
        return [
          ...this.$exportWord.exportTitle("权益分年度持仓风格"),
          ...this.$exportWord.exportTable(list, this.data, "", true),
        ];
      } else {
        return [];
      }
    },
  },
};
</script>

<style></style>
