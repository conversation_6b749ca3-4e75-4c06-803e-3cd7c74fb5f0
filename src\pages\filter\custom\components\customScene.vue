<template>
	<div class="custom_scene">
		<div>
			<div class="flex_between mb-16">
				<div>在这里你可以创建自定义的情景</div>
				<div><el-button type="primary" @click="openCustomTag">创建</el-button></div>
			</div>
			<el-table :data="data" v-loading="loading" height="calc(100vh - 373px)">
				<el-table-column
					v-for="item in column"
					:key="item.value"
					:prop="item.value"
					:label="item.label"
					:width="item.width"
					align="gotoleft"
				>
					<template slot-scope="{ row }">
						<div v-if="item.value == 'color'" :style="`width:25px;height:25px;background:${row[item.value]}`"></div>
						<div v-else>{{ item.format ? item.format(row[item.value]) : row[item.value] }}</div>
					</template>
				</el-table-column>
				<el-table-column align="gotoleft" label="操作" width="200px">
					<template slot-scope="{ row }">
						<div class="flex_start">
							<el-link class="mr-8" @click="openCustomTag(row)">编辑</el-link>
							<el-link class="mr-8" @click="deleteCustomScene(row)">删除</el-link>
						</div>
					</template>
				</el-table-column>
				<template slot="empty">
					<el-empty></el-empty>
				</template>
			</el-table>
			<div class="flex_between mt-16">
				<div style="font-family: 'PingFang'; font-style: normal; font-weight: 400; font-size: 14px; color: rgba(0, 0, 0, 0.65)">
					共{{ total }}条数据
				</div>
				<div class="flex_start">
					<el-select
						v-model="pageSize"
						filterable
						allow-create
						default-first-option
						placeholder=""
						style="width: 60px"
						@change="handleSizeChange"
					>
						<el-option v-for="item in size_list" :key="item.value" :label="item.labe" :value="item.value"> </el-option>
					</el-select>
					<span style="margin-left: 8px; font-size: 13px">条/页</span>
					<el-pagination
						@page-size-change="handleSizeChange"
						@current-change="handlePageChange"
						:current-page.sync="pageNum"
						:page-size="pageSize"
						layout="prev, pager, next, jumper"
						:total="total"
					>
					</el-pagination>
				</div>
			</div>
		</div>
		<edit-scene ref="editScene" @resolveFather="postSceneList"></edit-scene>
	</div>
</template>

<script>
import { getSceneList, postSceneList, deleteCustomScene } from '@/api/pages/SystemAlpha.js';
import editScene from './components/editScene.vue';
export default {
	components: { editScene },
	data() {
		return {
			total: 0,
			pageNum: 0,
			pageSize: 10,
			size_list: [
				{
					label: '10',
					value: '10'
				},
				{
					label: '20',
					value: '20'
				},
				{
					label: '50',
					value: '50'
				},
				{
					label: '100',
					value: '100'
				}
			],
			loading: false,
			column: [
				{
					label: '情景名称',
					value: 'name',
					width: '200px'
				},
				{
					label: '情景描述',
					value: 'description'
				},
				{
					label: '时间段',
					value: 'date'
				},
				{
					label: '配色',
					value: 'color'
				},
				{
					label: '是否公共',
					value: 'publicType',
					format: this.formatPublic
				}
			],
			data: []
		};
	},
	methods: {
		async getData() {
			this.loading = true;
			let data = await getSceneList({ pageNum: this.pageNum, pageSize: this.pageSize });
			this.loading = false;
			if (data?.mtycode == 200) {
				this.total = data?.data.total;
				this.data = data?.data?.records.map((item) => {
					return {
						...item,
						date: item.startDate.slice(0, 10) + '~' + item.endDate.slice(0, 10),
						date_list: [item.startDate, item.endDate]
					};
				});
			}
		},
		openCustomTag(row) {
			if (row.name) {
				this.$refs['editScene'].getData(row);
			} else {
				this.$refs['editScene'].getData();
			}
		},
		handlePageChange(val) {
			this.pageNum = val;
			this.getData();
		},
		handleSizeChange(val) {
			this.pageSize = val;
			this.getData();
		},
		// 删除自定义场景
		async deleteCustomScene(row) {
			let data = await deleteCustomScene({ id: row.id });
			if (data?.mtycode == 200) {
				this.$message.success('删除成功');
			}
			this.getData();
		},
		// 新增或修改自定义场景
		async postSceneList(postData) {
			let data = await postSceneList(postData);
			if (data?.mtycode == 200) {
				this.$message.success('操作成功');
			}
			this.getData();
		},
		formatPublic(val) {
			return val == 0 ? '仅自己可见' : val == 1 ? '公司内可见' : '公共';
		}
	}
};
</script>

<style></style>
