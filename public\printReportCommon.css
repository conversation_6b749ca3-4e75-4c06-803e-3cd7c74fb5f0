@media print {
	.headernamesize {
		font-size: 30px;
		display: flex;
	}
	#burst-12 {
		background: #8d1ebd;
		width: 40px;
		height: 40px;
		position: relative;
		text-align: center;
	}
	#burst-12:before,
	#burst-12:after {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		height: 40px;
		width: 40px;
		background: #8d1ebd;
	}
	#burst-12:before {
		-webkit-transform: rotate(30deg);
		-moz-transform: rotate(30deg);
		-ms-transform: rotate(30deg);
		-o-transform: rotate(30deg);
	}
	#burst-12:after {
		-webkit-transform: rotate(60deg);
		-moz-transform: rotate(60deg);
		-ms-transform: rotate(60deg);
		-o-transform: rotate(60deg);
	}
	.zonghefenshu2 {
		color: #ffb000;
		display: flex;
		align-items: center;
		text-align: center;
		vertical-align: middle;
		height: 40px;
		width: 40px;
		font-size: 10px;
		background: #eaf4ff;
		border-radius: 50%;
		border: 1px solid #fff;
	}
	.fontsize12 {
		font-size: 12px;
	}
	.headercodesize {
		font-size: 22px;
		color: #999999;
		bottom: 3px;
	}
	.github-badge {
		margin-left: 10px;
		display: inline-block;
		border-radius: 4px;
		text-shadow: none;
		font-size: 14px;
		color: #fff;
		margin-top: 13px;
	}
	.github-badge .badge-subject {
		display: inline-block;
		background-color: #4d4d4d;
		padding: 4px 4px 4px 6px;
		border-top-left-radius: 4px;
		border-bottom-left-radius: 4px;
	}
	.github-badge .badge-value {
		display: inline-block;
		padding: 4px 6px 4px 4px;
		border-top-right-radius: 4px;
		border-bottom-right-radius: 4px;
	}
	.github-badge .bg-brightgreen {
		background-color: #4dc820 !important;
	}
	.github-badge .bg-orange {
		background-color: #ffa500 !important;
	}
	.github-badge .bg-yellow {
		background-color: #d8b024 !important;
	}
	.github-badge .bg-blueviolet {
		background-color: #8833d7 !important;
	}
	.github-badge .bg-pink {
		background-color: #f26bae !important;
	}
	.github-badge .bg-red {
		background-color: #e05d44 !important;
	}
	.github-badge .bg-blue {
		background-color: #007ec6 !important;
	}
	.github-badge .bg-lightgrey {
		background-color: #9f9f9f !important;
	}
	.github-badge .bg-grey,
	.github-badge .bg-gray {
		background-color: #555 !important;
	}
	.github-badge .bg-lightgrey,
	.github-badge .bg-lightgray {
		background-color: #9f9f9f !important;
	}
	.el-icon-info:before {
		content: '\e7a4';
	}
	.icon_color {
		color: rgba(0, 0, 0, 0.45);;
	}
	.github-badge .bg-blueviolet {
		background-color: #8833d7 !important;
	}
	.fontsize24 {
		font-size: 24px;
	}
	/* 数字大小区分颜色 */
	.Numbercomputedgreen {
		color: #36c461;
		font-weight: 900;
	}
	.Numbercomputedred {
		color: #c01717;
		font-weight: 900;
	}
	.margin15 {
		font-weight: 400;
		color: #333333;
		margin-top: 15px;
		font-family: PingFangSC-Regular, PingFang;
	}
	.detailmsg {
		margin-left: 10px;
	}
	.marginleft20 {
		margin-left: 20px;
	}
	.scorebox1 {
		width: 220px !important;
		height: 60px;
		background: #fafafa;
		border-radius: 2px;
		margin-top: 15px;
	}
	.sirclepro {
		width: 60px;
		height: 40px;
		margin-left: 5px;
		margin-top: 3px;
	}
	.circlenumber {
		display: flex;
		flex-direction: column;
		justify-content: center;
		text-align: center;
		width: 120px;
		height: 60px;
		font-size: 13px;
		font-family: PingFangSC-Regular, PingFang;
		font-weight: 400;
		color: #5c6e8f;
		line-height: 18px;
	}
	.frontrank {
		font-size: 17px;
		font-family: HelveticaNeue-CondensedBold, HelveticaNeue;
		font-weight: normal;
		font-weight: 600;
		color: #40AFFF;
	}
	.scorebox2 {
		/* width: 481px; */
		/* height: 135px; */
		background: #fafafa;
		border-radius: 2px;
		margin-top: 15px;
		margin-left: 15px;
	}
	.progrsserhead .el-progress-bar__outer {
		height: 6px !important;
	}
	.progrsserhead {
		width: 200px;
		margin-left: 10px;
	}
	.guanzhubox {
		width: 452px;
		/* height: 43px; */
		/* background: #4096FF; */
		border-radius: 4px;
		margin-top: 20px;
		color: white;
	}
	.el-button .guanzhubox .el-button--default .el-button--small {
		background: #4096FF !important;
		color: white;
		font-size: 22px;
		height: 30px;
		font-family: PingFangSC-Regular, PingFang;
		font-weight: 400;
	}
	.points {
		width: 6px;
		height: 16px;
		background: red;
		box-shadow: 3px 3px 1px #ffb300;

		margin-right: 5px;
		margin-top: 4px;
	}
	.content-box {
		/* position: absolute; */
		/* left: 240px; */
		/* right: 0; */
		/* top: 70px; */
		/* bottom: 0; */
		padding-bottom: 30px;
		-webkit-transition: left 0.3s ease-in-out;
		transition: left 0.3s ease-in-out;
		background: #fff;
	}

	.content {
		width: auto;
		height: 100%;
		padding: 10px;
		/* overflow-y: scroll; */
		box-sizing: border-box;
	}

	.content-collapse {
		left: 65px;
	}
	.content {
		padding: 10px 0 0;
		font-size: 12px;
		color: #606266;
	}
	.content .award {
		display: inline-block;
	}
	.content .info {
		display: inline-block;
	}
	.stylebackfull {
		width: 42px;
		margin: auto;
		height: 15.4px;
		background: #4096FF;
		border: 1px solid #4096FF;
	}
	.stylebackfull4096ff {
		width: 42px;
		margin: auto;
		height: 15.4px;
		background: #4096ff;
		border: 1px solid #4096ff;
	}
	.stylebackfull9013fe {
		width: 42px;
		margin: auto;
		height: 15.4px;
		background: #9013fe;
		border: 1px solid #9013fe;
	}
	.stylebacknull {
		width: 42px;
		margin: auto;
		height: 15.4px;
		background: #ecf3fb;
		border: 1px solid #d2e7ff;
	}
	.marginleft21 {
		margin-left: 3px;
	}
	.textsizeyejibond {
		width: 57px;
		font-size: 14px;
		font-family: PingFangSC-Regular, PingFang;
		font-weight: 400;
		color: #5c6e8f;
		line-height: 18px;
		margin-top: 5px;
		margin-bottom: 10px;
	}
	.textsizeyeji {
		width: 70px;
		font-size: 14px;
		font-family: PingFangSC-Regular, PingFang;
		font-weight: 400;
		color: #5c6e8f;
		line-height: 18px;
		margin-top: 5px;
		margin-bottom: 10px;
	}
	.marginbottom10px {
		margin-bottom: 10px;
	}
	.managerbox1 {
		width: 267px;
		height: 200px;
	}
	.box3x2 {
		width: 106px;
		height: 50px;
		background: #f5f5f5;
		margin-right: 10px;
		text-align: center;
		margin-bottom: 10px;
		display: flex;
		flex-direction: column;
		justify-content: center;
	}
	.width150 {
		width: 130px;
	}
	.picmanager {
		width: 200px;
		height: 200px;
	}
	.boxstyle1 {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 104px;
		height: 30px;
		background: rgba(11, 191, 155, 0.1);
		border: 1px solid #0bbf9b;
		text-align: center;
		margin: 5px;
	}
	.boxstyle2 {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 104px;
		height: 30px;
		background: rgba(232, 93, 45, 0.1);
		border: 1px solid #e85d2d;
		text-align: center;
		margin: 5px;
	}
	.boxstyle11 {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 150px;
		height: 30px;
		background: rgba(11, 191, 155, 0.1);
		border: 1px solid #0bbf9b;
		text-align: center;
		margin: 5px;
	}
	.boxstyle22 {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 150px;
		height: 30px;
		background: rgba(232, 93, 45, 0.1);
		border: 1px solid #e85d2d;
		text-align: center;
		margin: 5px;
	}
	.el-date-editor .el-range__icon {
		font-size: 14px !important;
		margin-left: -5px !important;
	}
	.boxstyle3 {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 104px;
		height: 30px;
		background: rgba(32, 118, 255, 0.1);
		border: 1px solid #4096FF;
		text-align: center;
		margin: 5px;
	}
	.boxclass1 {
		width: 100px;
		height: 14px;
		font-size: 14px;
		font-family: PingFangSC-Medium, PingFang;
		font-weight: 500;
		color: #0bbf9b;
		line-height: 14px;
	}
	.boxclass2 {
		width: 100px;
		height: 14px;
		font-size: 14px;
		font-family: PingFangSC-Medium, PingFang;
		font-weight: 500;
		color: #e85d2d;
		line-height: 14px;
	}
	.boxclass11 {
		width: 70px;
		height: 14px;
		font-size: 14px;
		font-family: PingFangSC-Medium, PingFang;
		font-weight: 500;
		color: #0bbf9b;
		line-height: 14px;
	}
	.boxclass22 {
		width: 70px;
		height: 14px;
		font-size: 14px;
		font-family: PingFangSC-Medium, PingFang;
		font-weight: 500;
		color: #e85d2d;
		line-height: 14px;
	}
	.boxclass3 {
		width: 100px;
		height: 14px;
		font-size: 14px;
		font-family: PingFangSC-Medium, PingFang;
		font-weight: 500;
		color: #4096FF;
		line-height: 14px;
	}
	.margin40px {
		height: 40px;
	}
	.headerbox1 {
		width: 723px;
		margin-left: 10px;
		margin-right: 10px;
	}
	.marginleft211 {
		margin-left: 6px;
	}
	.marginleft22 {
		margin-left: 12px;
	}
	.marginleft2111 {
		margin-left: 26px;
	}
	.marginright1 {
		margin-right: 46px;
	}
	.progrsserhead .el-progress-bar__outer {
		height: 6px !important;
	}

	.height400 {
		height: 400px !important;
	}
	.headerbox2 {
		width: 838px;
		box-shadow: 2px 6px 25px 0px rgba(239, 239, 239, 0.5);
		border-radius: 2px;
	}
	.detailmsg {
		margin-left: 10px;
	}
	.margin15 {
		font-weight: 400;
		color: #333333;
		margin-top: 15px;
		font-family: PingFangSC-Regular, PingFang;
	}
	.scorebox1 {
		width: 220px !important;
		height: 60px;
		background: #fafafa;
		border-radius: 2px;
		margin-top: 15px;
	}
	.scorebox2 {
		/* width: 481px; */
		/* height: 135px; */
		background: #fafafa;
		border-radius: 2px;
		margin-top: 15px;
		margin-left: 15px;
	}
	.sirclepro {
		width: 60px;
		height: 40px;
		margin-left: 5px;
		margin-top: 3px;
	}
	.circlenumber {
		display: flex;
		flex-direction: column;
		justify-content: center;
		text-align: center;
		width: 120px;
		height: 60px;
		font-size: 13px;
		font-family: PingFangSC-Regular, PingFang;
		font-weight: 400;
		color: #5c6e8f;
		line-height: 18px;
	}
	.frontrank {
		font-size: 17px;
		font-family: HelveticaNeue-CondensedBold, HelveticaNeue;
		font-weight: normal;
		font-weight: 600;
		color: #40AFFF;
	}
	.ql-editor {
		font-size: 14px !important;
	}
	.progrsserhead {
		width: 200px;
		margin-left: 10px;
	}
	.width80pxheaderstyle {
		width: 80px !important;
		margin-left: 10px;
	}
	.echartsoneline {
		width: 100% !important;
		height: 500px !important;
	}
	.echartshalfline {
		width: 100% !important;
		height: 350px !important;
	}
	.guanzhubox {
		width: 452px;
		/* height: 43px; */
		/* background: #4096FF; */
		border-radius: 4px;
		margin-top: 20px;
		color: white;
	}
	.el-button .guanzhubox .el-button--default .el-button--small {
		background: #4096FF !important;
		color: white;
		font-size: 22px;
		height: 30px;
		font-family: PingFangSC-Regular, PingFang;
		font-weight: 400;
	}
	.margintop20 {
		margin-top: 20px;
	}
	.datebox {
		width: 838px;
		height: 60px;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		align-self: center;
	}
	.linecolum {
		width: 1px;
		height: 8px;
		border: 1px solid #b7b7b7;
	}
	.linebox {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		margin-left: 5px;
		margin-right: 5px;
	}
	.yearbutton {
		font-size: 14px !important;
		font-family: PingFangSC-Regular, PingFang;
		font-weight: 400 !important;
		color: #333333;
	}

	.yearbuttonact {
		font-size: 14px !important;
		font-family: PingFangSC-Regular, PingFang;
		font-weight: 400 !important;
		color: #4096ff !important;
	}
	.height60 {
		margin-top: 10px;
	}
	.benchmarkline1 {
		width: 23px;
		height: 2px;
		background: #4096FF;
	}
	.benchmarkline2 {
		width: 23px;
		height: 2px;
		background: #929694;
	}
	.benchmarkline3 {
		width: 23px;
		height: 2px;
		background: #40bfdd;
	}
	.benchmarkline4 {
		width: 23px;
		height: 2px;
		background: #c2b12f;
	}
	.fonst {
		width: 55px;
		height: 14px;
		font-size: 12px;
		font-family: PingFangSC-Regular, PingFang;
		font-weight: 400;
		color: #666666;
		line-height: 14px;
	}
	.headbenchmarkbox {
		display: flex;
		margin-right: 10px;
		width: 180px;
		cursor: pointer;
		justify-content: center;
		align-items: center;
	}
	.headbenchmarkbox2 {
		display: flex;
		margin-right: 10px;
		width: 200px;
		justify-content: center;
		align-items: center;
	}
	.headwidth160 {
		width: 100px;
		margin-left: 5px;
	}
	.el-menu .el-menu--inline {
		overflow-x: hidden !important;
	}
	.headwidth160 .el-input__inner {
		font-size: 12px !important;
	}
	.avgbox {
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	/* .el-checkbox-group{
        display:flex;align-items:center;flex-wrap:wrap
    } */
	.el-popover {
		font-size: 14px !important;
	}
	.selfde {
		margin-right: 40px;
		font-size: 14px;
		font-family: PingFangSC-Regular, PingFang;
		font-weight: 400;
		color: #666666;
		line-height: 20px;
	}
	.avgde {
		margin-right: 40px;
		font-size: 14px;
		font-family: PingFangSC-Regular, PingFang;
		font-weight: 400;
		color: #666666;
		line-height: 20px;
	}
	.height20border {
		height: 20px;
	}
	.height10border {
		height: 10px;
	}
	.height40border {
		height: 40px;
	}
	.borderbottom2px {
		font-size: 16px;
	}
	.fs14 {
		font-size: 14px;
	}
	.borderbottom2px::after {
		content: '';
		width: 70px;
		height: 1px;
		display: block;

		/* border-bottom: 2px solid #40AFFF ; */
	}
	.borderbottom2px2::after {
		content: 'dashdashdksajdasdj';
		width: 70px;
		height: 1px;
		display: block;

		border-bottom: 2px solid #40AFFF;
	}
	.savemodel {
		border-top: 1px solid #e5e5e5;
		border-bottom: 1px solid #e5e5e5;
		padding: 20px;
	}
	.recommodfont {
		width: 190px;
		height: 16px;
		font-size: 20px;
		font-family: PingFangSC-Medium, PingFang;
		font-weight: 500;
		color: #333333;
		line-height: 16px;
		/* -webkit-background-clip: text;
        -webkit-text-fill-color: transparent; */
	}
	.modelbox {
		margin-left: 20px;
		width: 160px;
		height: 60px;
		text-align: center;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 20px;

		background: rgba(180, 190, 208, 0.12);
		border-radius: 3px;
		border: 1px solid #e6e6e6;
	}
	.modelnamebox {
		font-size: 16px;
		font-family: PingFangSC-Regular, PingFang;
		font-weight: 400;
		color: #333333;
	}
	.selfmodelbox {
		margin: 20px;
		width: 520px;
		background: rgba(255, 255, 255, 0.07);
		box-shadow: 0px 2px 8px 0px rgba(224, 224, 224, 0.5);
		border-radius: 3px;
	}
	.selfmodelde1 {
		font-size: 16px;
		font-family: PingFangSC-Regular, PingFang;
		font-weight: 400;
		color: #5c6e8f;
		margin-top: 10px;
		margin-left: 40px;
	}
	.selfmodelde2 {
		margin-left: 10px;
		font-size: 16px;
		font-family: PingFangSC-Regular, PingFang;
		font-weight: 400;
		color: #2e384a;
	}
	/**
      * 全局使用flexible.js自适应,
      * 目前所有的elementUI选框布局需要important强制覆盖
      * 推荐使用其他布局方式改进 
      **/
	.el-date-editor {
		height: 32px !important;
	}

	/**
      * 通用表格样式
      * (根据UI基金公司设计图表格的样式)
      **/
	.common-table-card {
		border: 1px solid #e9e9e9;
		border-radius: 5px;
	}
	.common-table-card .card-title-icon {
		display: inline-block;
		margin: 0 10px 0 5px;
		height: 6px;
		width: 6px;
		background: #40AFFF;
		vertical-align: middle;
	}
	.common-table-card .card-title-text {
		height: 50px;
		line-height: 50px;
		font-size: 16px;
		font-weight: 400;
		color: #333333;
	}
	.common-table-card .amount-font-color {
		color: #0bbf9b;
	}
	.el-dropdown-menu--medium .el-dropdown-menu__item {
		line-height: 30px !important;
		padding: 0 17px !important;
		font-size: 14px !important;
	}
}
