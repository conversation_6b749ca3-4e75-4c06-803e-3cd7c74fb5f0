<template>
	<div>
		<div class="flex_card">
			<div v-for="item in templateList" :key="item.value" v-show="item.isshow" :class="item.type">
				<component :is="item.is" :ref="item.value" @resolveFather="item.methods" v-loading="loading"></component>
			</div>
		</div>
	</div>
</template>

<script>
// 全部基金经理
import allFundManagers from '@/components/components/components/allFundManagers/index.vue';
// 基金类型
import { getHoldType, getHoldManagerMsg } from '@/api/pages/SystemOther.js';

export default {
	components: { allFundManagers },
	data() {
		return {
			allFundManagerData: [],
			templateList: [],
			info: {},
			loading: true,
			requestOver: [],
			requestAll: [],
			typeList: null
		};
	},
	methods: {
		// 获取数据
		getData(data) {
			this.info = data;
			this.loading = true;
			this.requestOver = [];
			this.watch();
			this.formatTemplatList();
		},
		// 添加watch函数式监听(因为watch侦听器在页面切换时失效)
		watch() {
			let unwatch = this.$watch('requestOver', (val, old) => {
				this.loading = false;
				this.$compontentsWatch(val, this);
			});
		},
		// 格式化模板列表
		formatTemplatList() {
			this.requestAll = [];
			let requestList = [];
			this.templateList.map((item) => {
				if (item.methods && typeof item.methods == 'string') {
					item.methods = this?.[item.methods];
				}
				if (
					item.typelist.some((obj) => {
						return this.info.type.indexOf(obj) != -1 || obj == '*';
					})
				) {
					this.requestAll.push(item);
					if (requestList.indexOf(item.getRequestData) == -1) {
						if (item.getRequestData && item.getRequestData !== 'None') {
							requestList.push(item.getRequestData);
							this?.[item.getRequestData]();
						}
					}
				}
			});
		},
		// 接收/返回组件列表
		getTemplateList(list) {
			if (list) {
				// 是光大
				if (this.isGDBank()) {
					this.templateList = list.filter((item) => {
						return item.isshow;
					});
				} else {
					// 不是光大
					this.templateList = list.filter((item) => {
						return item.is !== 'GDBankDetailequity' && item.is !== 'GDBankDetailbond' && item.isshow;
					});
				}
				this.$forceUpdate();
			} else {
				return this.templateList;
			}
		},
		// 判断是否是光大
		isGDBank() {
			if (window.localStorage.getItem('mty_modulesName') == 'GDBank') {
				this.showGD = true;
				return true;
			} else {
				this.showGD = false;
				return false;
			}
		},
		// 获取基金经理
		async getHoldManagerMsg(type) {
			let data = await getHoldManagerMsg({ code: this.info.code, type });
			if (data?.mtycode == 200) {
				this.$refs['allFundManagers']?.[0]?.getData(data?.data);
			}
		},
		// 获取基金经理数据
		getHoldManagerMsgData() {
			this.$refs['allFundManagers']?.[0]?.getTypeList(this.typeList);
		},
		// 获取基金经理类型
		async getHoldType() {
			if (this.getCacheData('typeList')) {
				this.typeList = this.getCacheData('typeList');
			} else {
				let data = await getHoldType({ code: this.info.code });
				if (data?.mtycode == 200) {
					this.typeList = data?.data;
				}
			}
			this.requestOver.push('getHoldType');
		},

		getFundList() {
			this.allManagerLoading = true;
			axios.get(this.$baseUrl + '/Company/HoldType/?code=' + this.code + '&noType=no').then((res) => {
				if (res.status == 200) {
					let arr = [];
					this.fundTypeList = res.data.data
						.filter((item) => {
							return (
								item.type == 'equity' ||
								item.type == 'equityhk' ||
								item.type == 'bond' ||
								// item.type == 'cbond' ||
								item.type == 'purebond' ||
								// item.type == 'bill' ||
								item.type == 'money'
							);
						})
						.map((item) => {
							let sort = null;
							switch (item.type) {
								case 'equity':
									sort = 0;
									break;
								case 'equityhk':
									sort = 1;
									break;
								case 'bond':
									sort = 2;
									break;
								// case 'cbond':
								// 	sort = 3;
								// 	break;
								case 'purebond':
									sort = 4;
									break;
								// case 'bill':
								// 	sort = 5;
								// 	break;
								case 'money':
									sort = 6;
									break;
							}
							return { type: item.type, name: this.FUNC.textConverter(this.COMMON.fundType_zh_en, item.type, 'en', 'zh'), sort };
						})
						.sort((a, b) => {
							return a.sort - b.sort;
						});
					if (this.fundTypeList.length > 0) {
						this.fundType = this.fundTypeList[0].type;
					}
					this.getManagerList();
				}
			});
		},
		getManagerList() {
			let params = {
				code: this.code,
				type: this.fundType
			};
			let url = this.$baseUrl + '/Company/HoldManagerMsg/?' + this.FUNC.paramsToString(params);
			this.allManagerLoading = true;
			this.allFundManagerData = [];
			axios
				.get(url)
				.then((res) => {
					if (res.status == 200) {
						this.allFundManagerData = res.data.data.slice();
						this.allFundManagerDataSort = res.data.data.slice();
					} else {
						console.error('error: ', res.data.data);
					}
					this.allManagerLoading = false;
				})
				.catch((err) => {
					console.error('error: ', err);
					this.allManagerLoading = false;
				});
		},
		sort_change(sortVal) {
			let order = sortVal.order;
			let key = sortVal.prop;
			if (!order) {
				this.allFundManagerDataSort = this.allFundManagerData.slice();
			} else if (order == 'ascending' && key) {
				let haveValList = this.allFundManagerDataSort.filter((item) => !isNaN(parseFloat(item[key])));
				let noValList = this.allFundManagerDataSort.filter((item) => isNaN(parseFloat(item[key])));
				haveValList.sort((a, b) => a[key] - b[key]);
				this.allFundManagerDataSort = [...haveValList, ...noValList];
			} else if (order == 'descending' && key) {
				let haveValList = this.allFundManagerDataSort.filter((item) => !isNaN(parseFloat(item[key])));
				let noValList = this.allFundManagerDataSort.filter((item) => isNaN(parseFloat(item[key])));
				haveValList.sort((a, b) => b[key] - a[key]);
				this.allFundManagerDataSort = [...haveValList, ...noValList];
			}
		},
		getfontSize(val) {
			return fontSize(val);
		},
		// 前往详情页
		goDetail(row) {
			alphaGo(row.manager_code, row.name_x, this.$route.path);
		},
		// 跳转
		godetail(code, name) {
			//带参进去
			alphaGo(code, name, this.$route.path);
		}
	}
};
</script>

<style scoped lang="scss">
.header-manager {
	margin: 10px 0;
	display: flex;
	justify-content: space-between;
	.header-title {
		font-size: 16px;
		font-weight: bold;
	}
}
</style>
