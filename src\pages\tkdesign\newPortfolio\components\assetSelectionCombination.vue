<template>
	<div class="asset-selection-wrapper">
		<el-tabs class="type-tab-wrapper" v-model="activeName" type="card" @tab-click="handleTabChange">
			<el-tab-pane v-loading="searchLoading" label="基金产品" name="product">
				<div class="asset-selection-content">
					<div class="selection-box-wrapper">
						<TheTransferBox
							key="product"
							:optionList="optionList"
							v-model="checkoutList"
							:searchValue.sync="searchValue"
							:searchInputConfig="searchInputConfig"
							@searchInput="handleSearchInput"
							:resultTableHeader="resultTableHeader"
							navTitle="全市场公募基金"
						></TheTransferBox>
						<div>*请配置大类资产下的基金产品</div>
					</div>
				</div>
			</el-tab-pane>
			<el-tab-pane v-loading="searchLoading" label="基金池" name="pool">
				<div class="asset-selection-content">
					<div class="selection-box-wrapper">
						<TheTransferBox
							:optionList="optionList"
							v-model="checkoutList"
							:searchValue.sync="searchValue"
							:searchInputConfig="searchInputConfig"
							:resultTableHeader="resultTableHeader"
							@searchInput="handleSearchInput"
							navTitle="基金池"
						></TheTransferBox>
						<div>*请配置大类资产下的基金产品</div>
					</div>
				</div>
			</el-tab-pane>
			<el-tab-pane v-loading="searchLoading" label="配置策略" name="strategy">
				<div class="asset-selection-content">
					<div class="asset-selection-header">
						<span>配置策略方案：</span>
						<el-select v-model="currentPloy" filterable placeholder="请选择" @change="handleStrategyChange">
							<el-option v-for="item in strategyList" :key="item.ployId" :label="item.ployName" :value="item.ployId"> </el-option>
						</el-select>
						<!-- <el-input style="width: 240px" v-model="strategyInput" placeholder="请搜索配置策略方案" @input="handleConfigSearch"></el-input> -->
					</div>
					<div>
						<el-tabs v-model="currentStrategy" @tab-click="handleStrategyIndexChange">
							<el-tab-pane v-for="strategy in strategyListSearch" :label="strategy.tabLabel" :name="strategy.tabName"></el-tab-pane>
						</el-tabs>
						<div class="selection-box-wrapper" v-if="currentStrategy">
							<TheTransferBox
								key="strategy"
								:optionList="optionList"
								v-model="checkoutList"
								:searchValue.sync="searchValue"
								:searchInputConfig="searchInputConfig"
								:resultTableHeader="resultTableHeader"
								@searchInput="handleSearchInput"
								navTitle="全市场公募基金"
							></TheTransferBox>
							<div>*请配置大类资产下的基金产品</div>
						</div>
						<el-empty v-else image-size="160"></el-empty>
					</div>
				</div>
			</el-tab-pane>
		</el-tabs>
		<div class="asset-selection-content">
			<div class="asset-selection-footer">
				<el-button type="text" @click="handleDialogAsset">基金产品分析预览<i class="el-icon-arrow-right"></i></el-button>
			</div>
			<el-dialog title="基金产品分析预览" width="62.5%" :visible.sync="dialogAssetAnalysisVisible">
				<TheAssetAnalysis v-if="dialogAssetAnalysisVisible" :codeList="codeList" marketType="fund" flag="1"></TheAssetAnalysis>
			</el-dialog>
		</div>
		<div class="btn-footer-wrapper">
			<el-button style="margin-top: 12px" @click="handleBack">取消</el-button>
			<el-button type="primary" style="margin-top: 12px" @click="handleNext">下一步</el-button>
		</div>
	</div>
</template>
<script>
import TheAssetAnalysis from './TheAssetAnalysis.vue';
import TheTransferBox from './components/TheTransferBox.vue';
import TheAssetsConfig from './components/TheAssetsConfig.vue';
import { getPloyPlanlist, getMarketWindFund, getPoolList } from '@/api/pages/tkAnalysis/portfolio.js';
export default {
	name: 'assetSelectionConfiguration',
	components: {
		TheAssetAnalysis,
		TheTransferBox,
		TheAssetsConfig
	},
	data() {
		return {
			searchValue: '',
			dialogAssetAnalysisVisible: false,
			currentPloy: '',
			/** 配置策略 'strategy' 基金池 'pool' 基金产品 'product'*/
			activeName: 'product',
			//当前选择的策略 的指数code
			currentStrategy: '',
			//配置策略方案列表
			strategyList: [],
			// strategyListSearch: [],
			optionList: [],
			checkoutList: [],
			searchInputConfig: {
				label: '搜索基金：',
				placeholder: '请输入名称搜索基金',
				searchName: 'code',
				custom: true
			},
			searchLoading: false
		};
	},
	computed: {
		codeList() {
			let list =
				this.checkoutList.map((item) => {
					return item.code;
				}) || [];
			//去重处理
			list = Array.from(new Set(list));
			return list;
		},
		strategyListSearch() {
			let index = 0;
			if (this.currentPloy) {
				let searchIndex = this.strategyList.findIndex((item) => {
					return item.ployId === this.currentPloy;
				});
				if (searchIndex >= 0) {
					index = searchIndex;
				}
			}
			let { dataList = [], ployId = '', ployName = '' } = this.strategyList[index] || {};
			let result = dataList?.map((item) => {
				return {
					ployId: ployId,
					ployName: ployName,
					...item,
					tabName: item.code,
					tabLabel: `${item.name}`
				};
			});
			return result;
		},
		//右侧列表展示头部和属性
		resultTableHeader() {
			//todo
			if (this.activeName === 'strategy') {
				return [
					{ prop: 'typeLabel', label: '基金类别' },
					{ prop: 'name', label: '基金名称' },
					{ prop: 'indexName', label: '所属策略' }
				];
			}
			if (this.activeName === 'pool') {
				return [
					{ prop: 'typeLabel', label: '基金类别' },
					{ prop: 'name', label: '基金名称' }
					// { prop: 'ployName', label: '所属策略' }
				];
			}
			return [
				{ prop: 'typeLabel', label: '基金类别' },
				{ prop: 'name', label: '基金名称' }
				// { prop: 'ployName', label: '所属策略' }
			];
		}
	},
	methods: {
		//由外部初始化调用
		initData() {
			this.getData();
		},
		// 百分化
		fix2p(val) {
			return val * 1 ? (val * 100).toFixed(2) + '%' : '--';
		},
		handleDialogAsset() {
			if (this.codeList && this.codeList.length > 0) {
				this.dialogAssetAnalysisVisible = true;
				return;
			}
			this.$message.warning('请选择基金');
		},
		//配置策略方案查询
		// handleConfigSearch() {
		// 	if (!this.strategyInput) {
		// 		this.strategyListSearch = this.FUNC.deepClone(this.strategyList);
		// 		this.currentStrategy = this.strategyListSearch[0]?.tabName;
		// 		return;
		// 	}
		// 	this.strategyListSearch = this.strategyList.filter((item) => {
		// 		let str = item['name'];
		// 		if (str && str.indexOf(this.strategyInput) < 0) {
		// 			return false;
		// 		}
		// 		return true;
		// 	});
		// 	this.currentStrategy = this.strategyListSearch[0]?.tabName;
		// },
		//切换分类时重新调用接口 清除数据
		handleTabChange() {
			this.currentPloy = '';
			this.searchValue = '';
			this.getData();
		},
		//搜索基金
		handleSearchInput(value) {
			this.FUNC.debounceFunc(() => {
				return this.getData(true, value);
			}, 1000)();
		},
		//isSearchFund 是否为搜索基金时调用
		async getData(isSearchFund) {
			const keyWord = this.searchValue;
			this.searchLoading = true;
			//搜索时不需要清空列表
			if (!isSearchFund) {
				this.optionList = [];
				this.checkoutList = [];
			}
			//如果选中的是配置策略
			if (this.activeName === 'strategy') {
				let params = {
					indexCode: this.currentStrategy,
					keyWord
				};
				//如果选中的是配置策略tab需要先获取配置策略方案列表 搜索基金的时候不需要单独调用
				if (!isSearchFund) {
					await this.getPloyPlanlist();
				}
				await this.getFundList(params);
				this.searchLoading = false;
				return;
			}
			let params = {
				keyWord
			};
			//基金池
			if (this.activeName === 'pool') {
				await this.getPoolList(params);
				this.searchLoading = false;
				return;
			}
			await this.getFundList(params);
			this.searchLoading = false;
		},
		//当前选中所属策略变化时
		handleStrategyChange() {
			this.checkoutList = [];
			this.currentStrategy = this.strategyListSearch[0].code;
			this.handleStrategyIndexChange();
		},
		//当前选中所属策略指数变化
		async handleStrategyIndexChange() {
			this.searchLoading = true;
			this.setStrategyInfo();
			let params = {
				indexCode: this.currentStrategy
			};
			await this.getFundList(params);
			this.searchLoading = false;
		},
		//设置列表选中时 右侧当前所属策略展示
		setStrategyInfo() {
			let index = this.strategyListSearch.findIndex((item) => {
				return item?.tabName === this.currentStrategy;
			});
			let indexCode = index >= 0 ? this.strategyListSearch[index].code : '';
			let indexName = index >= 0 ? this.strategyListSearch[index].name : '';
			let weight = index >= 0 ? this.strategyListSearch[index].weight : '';
			this.optionList = this.optionList.map((item) => {
				item.children?.forEach((childrenItem) => {
					childrenItem.value.indexCode = indexCode;
					childrenItem.value.indexName = indexName;
					childrenItem.value.weight = weight;
				});
				return item;
			});
			console.log(this.optionList);
		},
		//获取全市场公募基金列表
		async getFundList(params = {}) {
			let reqData = await getMarketWindFund(params);
			let { data = [], mtycode, mtymessage } = reqData || {};
			if (mtycode == 200) {
				this.optionList = data.map((item) => {
					item = {
						...item,
						typeId: item.name,
						typeLabel: item.name
					};
					item.children = item.codeList || [];
					item.children?.forEach((childrenItem) => {
						childrenItem.childrenId = childrenItem.code;
						childrenItem.childrenLabel = childrenItem.name;
						childrenItem.tag = childrenItem.flag;
						childrenItem.value = {
							//将外层字段放进选中结果的value中
							typeId: item.name,
							typeLabel: item.name,
							...childrenItem
						};
					});
					return item;
				});
				this.setStrategyInfo();
			} else {
				this.$message.warning(mtymessage);
			}
		},
		//获取基金池基金列表
		async getPoolList(params = {}) {
			// typeLabel: '一级分类展示文案',
			// 			typeId: '一级分类唯一标识',
			// 			children: [
			// 				{
			// 					childrenLabel: '子节点展示文案',
			// 					childrenId: '子节点唯一标识',
			// 					value: {} //注意：该值将决定右侧列表展示的数据 子节点本身的数据值，用户接口传输
			// 				}
			// 			]
			let reqData = await getPoolList(params);
			let { data = [], mtycode, mtymessage } = reqData || {};
			if (mtycode == 200) {
				this.optionList = data.map((item) => {
					item = {
						...item,
						typeId: item.poolId,
						typeLabel: item.poolName
					};
					item.children = item.codeList || [];
					item.children?.forEach((childrenItem) => {
						childrenItem.childrenId = childrenItem.code;
						childrenItem.childrenLabel = childrenItem.name;
						childrenItem.value = {
							//将外层字段放进选中结果的value中
							typeId: item.poolId,
							typeLabel: item.poolName,
							...childrenItem
						};
					});
					return item;
				});
			} else {
				this.$message.warning(mtymessage);
			}
			console.log('optionList', this.optionList);
		},
		/**
		 * 获取配置策略方案
		 * */
		async getPloyPlanlist(params = {}) {
			let reqData = await getPloyPlanlist(params);
			let { data = [], mtycode, mtymessage } = reqData || {};
			if (mtycode == 200) {
				this.strategyList = data;
				//设置相应的默认值
				this.currentPloy = this.strategyList[0]?.ployId;
				this.currentStrategy = this.strategyList[0]?.dataList[0]?.code;
				this.handleStrategyIndexChange();
			} else {
				this.$message.warning(mtymessage);
			}
		},
		handleNext() {
			if (this.codeList && this.codeList.length > 0) {
				let ployProject = this.checkoutList.map((item) => {
					return {
						code: item.indexCode,
						weight: item.weight,
						fundCode: item.code
					};
				});
				console.log(ployProject, 'this.otherInfo?.ployProject');
				this.$emit('nextStep', {
					selectList: this.checkoutList,
					ployProject,
					ployId: this.currentPloy,
					constructionDate: this.strategyListSearch[0]?.date
				});
				return;
			}
			this.$message.warning('请选择基金');
		},
		handleBack() {
			this.$emit('backStep');
		}
	}
};
</script>
<style lang="scss" scoped>
.transfer-box-wrapper {
	display: flex;
	.first-type-wrapper {
		::v-deep .el-tabs__nav-scroll {
			padding: 0 16px;
			.el-tabs__nav-wrap {
				&::after {
					content: unset;
				}
			}
		}
	}
	.transfer-left {
		width: 480px;
		border: 1px solid #e9e9e9;
		border-radius: 4px;
		.left-nav-wrapper {
			::v-deep .el-tabs__header {
				.el-tabs__item {
					text-align: left;
				}
			}
		}
		.transfer-left-title {
			display: flex;
			padding: 8px 16px;
			align-items: center;
			border-bottom: 1px solid #e9e9e9;
			.label {
				color: rgba(0, 0, 0, 0.85);
				font-size: 14px;
				font-style: normal;
				font-weight: 400;
				line-height: 22px; /* 157.143% */
				word-break: keep-all;
			}
		}
	}
	.transfer-center {
		display: flex;
		flex-direction: column;
		padding: 0 20px;
		justify-content: center;
		align-items: center;
	}
	.transfer-right {
		border: 1px solid #e9e9e9;
		border-radius: 4px;
		.transfer-right-title {
			padding: 10px 16px;
			color: #000;
			font-size: 14px;
			font-style: normal;
			font-weight: 400;
			line-height: 22px; /* 157.143% */
		}
	}
}
.asset-selection-wrapper {
	padding: 20px;
	background-color: #ffffff;
	border-radius: 4px;
	.type-tab-wrapper {
		::v-deep > .el-tabs__header {
			.el-tabs__nav-wrap {
				margin: unset;
			}
			.el-tabs__nav {
				background-color: none;
				border: none;
				.el-tabs__item {
					margin-right: 4px;
					background-color: #fafafa;
					border-radius: 4px 4px 0px 0px;
					border: 1px solid #f0f0f0;
					border-bottom: unset;
					&.is-active {
						background-color: #ffffff;
					}
				}
			}
		}
	}
	.selection-box-wrapper {
		display: flex;
		justify-content: space-between;
	}
	.asset-selection-content {
		// padding: 0 24px;
	}
	.asset-selection-header {
		color: rgba(0, 0, 0, 0.85);
		font-size: 16px;
		font-style: normal;
		font-weight: 500;
		line-height: 24px; /* 150% */
		padding: 16px 0;
		display: flex;
		.header-btn {
			margin-left: 20px;
		}
	}
}
.asset-selection-footer {
	display: flex;
	justify-content: flex-end;
	padding: 8px 0;
	color: rgba(0, 0, 0, 0.45);
	font-size: 12px;
	font-weight: 400;
	line-height: 20px; /* 166.667% */
}
.btn-footer-wrapper {
	padding: 16px 24px;
	border-top: 1px solid #e9e9e9;
}
</style>
