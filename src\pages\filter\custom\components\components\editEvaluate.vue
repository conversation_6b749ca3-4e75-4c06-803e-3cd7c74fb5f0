<template>
	<div>
		<el-dialog title="新建模板" :visible.sync="dialogVisible">
			<div>
				<div class="flex_start mb-16">
					<div class="mr-16">模版名称</div>
					<div><el-input v-model="form.name" placeholder="请输入模板名称"></el-input></div>
				</div>
				<div class="mb-16">
					<div>筛选范围</div>
					<div><range-choose ref="rangeChoose"></range-choose></div>
				</div>
				<div class="flex_start mb-16">
					<div class="mr-16">自定义标签</div>
					<div>
						<el-switch v-model="form.is_tag" active-color="#4096ff" inactive-color="#E7E7E7"></el-switch>
					</div>
				</div>
				<div class="flex_start mb-16">
					<div class="mr-16">自定义评分</div>
					<div>
						<el-switch v-model="form.is_evaluate" active-color="#4096ff" inactive-color="#E7E7E7"></el-switch>
					</div>
				</div>
				<div class="mb-16">
					<div class="mb-8">自定义能力项</div>
					<div>
						<div class="flex_start mb-8" v-for="(item, index) in form.item" :key="index">
							<div class="mr-24">
								<div class="mb-4"><el-input v-model="item.name" placeholder="输入自定义能力项名称"></el-input></div>
								<div>
									<el-input
										v-model="item.description"
										style="width: 400px"
										type="textarea"
										:rows="2"
										placeholder="输入自定义能力项描述"
									></el-input>
								</div>
							</div>
							<div class="flex_center">
								<div class="mr-8" v-show="index == form.item.length - 1" @click="addCapabilityItem(index)"><el-link>新增</el-link></div>
								<div @click="deleteItem(index)"><el-link>删除</el-link></div>
							</div>
						</div>
					</div>
				</div>
				<div class="flex_start">
					<div class="mr-24">量化评分</div>
					<div @click="openScoreCondition"><el-button type="primary">选择打分条件</el-button></div>
				</div>
			</div>
			<div slot="footer">
				<el-button @click="dialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="submit">确 定</el-button>
			</div>
		</el-dialog>
		<el-dialog :visible.sync="visible" width="80%">
			<score-components ref="scoreComponents" filter_type="evaluate" pool_id="all" @resolveFather="getScoreCondition"></score-components>
		</el-dialog>
	</div>
</template>

<script>
// 筛选范围
import rangeChoose from '@/pages/filter/fund/beta/components/rangeChoose';
// 量化评分
import scorePoolFund from '@/pages/fundNewPool/analysis/components/components/scorePoolFund.vue';
import scoreComponents from '@/pages/filter/fund/score/score.vue';

import { saveModel } from '@/api/pages/Tools.js';

export default {
	components: { rangeChoose, scoreComponents },
	data() {
		return {
			dialogVisible: false,
			visible: false,
			form: {
				name: '',
				is_tag: true,
				is_evaluate: true,
				item: [{ name: '', description: '' }],
				rate: []
			}
		};
	},
	methods: {
		getData() {
			this.dialogVisible = true;
			this.$nextTick(() => {
				this.$refs['rangeChoose'].getTypeList();
			});
		},
		openScoreCondition() {
			this.visible = true;
			this.$nextTick(() => {
				this.$refs['scoreComponents'].Init();
			});
		},
		getScoreCondition(val) {
			this.visible = false;
			this.form.rate = val?.[0].item;
		},
		addCapabilityItem(index) {
			this.form.item.push({ name: '', description: '' });
		},
		deleteItem(index) {
			this.form.item.splice(index, 1);
		},
		async submit() {
			let postData = {
				model_name: this.form.name,
				model_description: '',
				ispublic: false,
				ismanager: false,
				source: 'evaluate',
				type: 'equity',
				title: '--',
				user_permission: [],
				model_args: [{ ...this.form, rate: { ...this.$refs['rangeChoose'].resolveList(), item: this.form.rate } }]
			};
			let data = await saveModel(postData);
			if (data?.mtycode == 200) {
				this.dialogVisible = false;
				this.$message.success('提交成功');
				this.$emit('resolveFather');
			} else {
				this.$message.warning(data?.mtymessage);
			}
		}
	}
};
</script>

<style></style>
