<template>
    <div class="charts_fill_class" v-loading="loading">
        <v-chart
            ref="companySizeChange"
            :options="option"
            element-loading-text="暂无数据"
            element-loading-spinner="el-icon-document-delete"
            element-loading-background="rgba(239, 239, 239, 0.5)"
            class="charts_one_class"
            autoresize
        ></v-chart>
    </div>
</template>

<script>
import VChart from 'vue-echarts';

import { lineChartOption } from '@/utils/chartStyle.js';
export default {
components: { VChart },
data() {
    return {
        option: {},
        loading: true
    };
},
methods: {
    getData() {
        this.loading = false;
        this.option = lineChartOption({
            toolbox:'none',
            legend: {
                data: [
                {
                        name: '行业指数',
                    
                    },,
                    {
                        name: '股价走势特征',
                    
                    },
                    {
                        name: '沪深300',
                        
                    },
                    {
                        name: '股票披露权重',
                        icon: 'rect',
                        backgroundColor:'#4096ff'
                    },
                ]
            },
            xAxis: [{
                type: 'category',
                boundaryGap: false,
                data: ['2024-06-03', '2024-06-03', '2024-06-03', '2024-06-03', '2024-06-03', '2024-06-03', '2024-06-03'],
                axisLabel: {

                    // interval 可以定义成数字，但这也会有一个弊端，在不确定数据量的时候，间隔数不好定义，不能随心所欲的控制所要展示的内容 
                    interval:2
                }, 
            }],
            yAxis: 
               [ {
                    type: 'value',
                    name: '股票披露权重(%)',
                    axisLine:{
                        show:false
                    },
                    axisTick:{
                        show:false
                    },
                    splitLine:{
                        lineStyle:{
                            type:'dashed'
                        }
                    }   
                },{
                    type: 'value',
                    name: '收益率(%)',
                    axisLine:{
                        show:false
                    },
                    axisTick:{
                        show:false
                    },
                    splitLine:{
                        lineStyle:{
                            type:'dashed'
                        }
                    }   
                }],
           
            series: [
                {
                    name: '行业指数',
                    type: 'line',
                    yAxisIndex: 1,

                    lineStyle:{
                        color:'##4096ff'
                    },
                    symbol: 'none',
                    data: [1320, 1132, 601, 234, 120, 90, 20],
                },
                {
                    name: '沪深300',
                    type: 'line',
                    yAxisIndex: 1,

                    symbol: 'none',
                    lineStyle:{
                        color:'#4096ff'
                    },
                    data: [1320, 1132, 21, 54, 260, 830, 710],
                },
                {
                    name: '股价走势特征',
                    type: 'line',
                    yAxisIndex: 1,

                    symbol: 'none',
                  
                    data: [100, 130, 130, 450, 560, 570, 560],
                },
                {
                    name: '股票披露权重',
                    type: 'bar',
                    barWidth: 30,
                    symbol: 'none',
                    lineStyle:{
                        opacity:0
                    },
                  
                    data: [100, 130, 130, 450, 560, 570, 560],
                },
            ]
        });
    }
}
};
</script>

<style scoped>
.chart_one{
padding: 0;
box-shadow: none;
}
.charts_fill_class{
    .echarts{
    height: 248px;

    }
}
</style>
