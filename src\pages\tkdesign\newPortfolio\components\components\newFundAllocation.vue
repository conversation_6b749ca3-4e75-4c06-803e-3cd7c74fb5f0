<template>
  <div class="plate-wrapper fund-performance-board-wrapper">
    <combinationComponentHeader title="最新各类型基金配置情况" showMoreBtn>
      <template slot="right"></template>
    </combinationComponentHeader>

    <el-table border stripe :data="tableDataNow">
      <el-table-column align="gotoleft" prop="name" show-overflow-tooltip label="基金类型"></el-table-column>
      <el-table-column align="gotoleft" prop="date" show-overflow-tooltip label="仓位"></el-table-column>
      <el-table-column align="gotoleft" prop="nav" sortable label="A类仓位"></el-table-column>
      <el-table-column align="gotoleft" prop="nav" sortable label="C类仓位">
        <templete slot-scope="scope">{{ scope.row.rate | fix2p }}</templete>
      </el-table-column>
      <el-table-column align="gotoleft" sortable prop="cum_return" label="基金经理平均管理年限">
        <templete slot-scope="scope">{{ scope.row.cum_return | fix2p }}</templete>
      </el-table-column>

      <template slot="empty">
        <el-empty image-size="160"></el-empty>
      </template>
    </el-table>
    <!-- <div id="fund-performance-board-chart-container" style="height: 355px;"></div> -->
  </div>
</template>
<script>
import combinationComponentHeader from "./combinationComponentHeader.vue";
export default {
  name: "newFundAllocation",
  components: {
    combinationComponentHeader
  },
  data() {
    return {
      form: {},
      IndexStyleOption: [],
      tableData: [],
      legendName: {
        indicatorPoints: "指标点位",
        ttm: "TTM",
        dividedIntoPoints: "分为点",
        positiveStandardDeviation: "标准差（+1）",
        negativeStandardDeviation: "标准差（-1）",
        average: "平均值"
      },
      tableDataNow: []
    };
  },
  mounted() {
    // let chartDom = document.getElementById('fund-performance-board-chart-container');
    // this.myChart = echarts.init(chartDom);
    let option;
    option = {
      title: {},
      tooltip: {
        trigger: "axis"
      },
      legend: {
        // textStyle:{
        //     color:rgba(0, 0, 0, 0.65)
        // },
        selected: {
          // 不选中'TTM'
          TTM: false
        },
        data: [
          {
            name: this.legendName.indicatorPoints
            // 强制设置图形为圆。
            // icon: 'none',
            // 设置文本为红色
            // textStyle: {
            // color: 'red'
            // }
          },
          {
            name: this.legendName.ttm,
            icon: "rect"
            // 设置文本为红色
            // textStyle: {
            // color: 'red'
            // }
          },
          {
            name: this.legendName.dividedIntoPoints,
            icon: "rect",
            backgroundColor: "#4096ff"
          },
          {
            name: this.legendName.positiveStandardDeviation
            // 强制设置图形为圆。
            // icon: 'circle',
            // 设置文本为红色
            // textStyle: {
            // color: 'red'
            // }
          },
          {
            name: this.legendName.average
            // 强制设置图形为圆。
            // icon: 'circle',
            // 设置文本为红色
            // textStyle: {
            // color: 'red'
            // }
          },
          {
            name: this.legendName.negativeStandardDeviation
            // 强制设置图形为圆。
            // icon: 'circle',
            // 设置文本为红色
            // textStyle: {
            // color: 'red'
            // }
          }
        ]
      },
      xAxis: {
        type: "category",
        boundaryGap: false,
        data: ["2024-06-03", "2024-06-03", "2024-06-03"]
      },
      yAxis: [
        {
          name: this.legendName.dividedIntoPoints,
          type: "value",
          interval: 25
        },
        {
          name: this.legendName.indicatorPoints,
          type: "value",
          interval: 355,
          scale: true
        }
      ],
      series: [
        {
          name: this.legendName.indicatorPoints,
          type: "line",
          lineStyle: {
            color: "#4096ff"
          },
          symbol: "none",
          data: [1320, 1132, 601, 234, 120, 90, 20],
          yAxisIndex: 1
        },
        {
          name: this.legendName.ttm,
          type: "line",
          symbol: "none",
          lineStyle: {
            opacity: 0
          },
          areaStyle: {},
          data: [1320, 1132, 21, 54, 260, 830, 710],
          yAxisIndex: 1
        },
        {
          name: this.legendName.dividedIntoPoints,
          type: "line",
          symbol: "none",
          lineStyle: {
            opacity: 0
          },
          areaStyle: {
            color: "#4096ff",
            opacity: 0.25
          },
          data: [10, 13, 13, 45, 56, 57, 56],
          yAxisIndex: 0
        },
        {
          name: this.legendName.positiveStandardDeviation,
          type: "line",
          symbol: "none",
          lineStyle: {
            type: "dashed"
          },
          data: [100, 100, 100, 100, 100, 100, 100],
          yAxisIndex: 1
        },
        {
          name: this.legendName.average,
          type: "line",
          symbol: "none",
          lineStyle: {
            type: "dashed"
          },
          data: [30, 182, 434, 791, 390, 30, 10],
          yAxisIndex: 1
        },
        {
          name: this.legendName.negativeStandardDeviation,
          type: "line",
          symbol: "none",
          lineStyle: {
            type: "dashed"
          },
          data: [30, 16, 13, 791, 390, 30, 10],
          yAxisIndex: 1
        }
      ]
    };
    // option && this.myChart.setOption(option);
  }
};
</script>
<style lang="scss" scoped>
.fund-performance-board-wrapper {
  .select-form-wrapper {
    margin-bottom: 16px;
  }
  .content-table-wrapper {
    margin-bottom: 32px;
  }
}
</style>