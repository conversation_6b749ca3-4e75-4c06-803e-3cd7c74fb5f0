<template>
  <div>
    <el-dialog title="新增池"
               :visible.sync="visible"
               width="450px"
               destroy-on-close>
      <div class="dialogfontsize15"
           style="margin-bottom: 8px">池名称:</div>
      <el-input type="text"
                style="margin-bottom: 16px"
                v-model="form.name"
                label="池名称"></el-input>
      <div class="dialogfontsize15"
           style="margin-bottom: 8px">池说明:</div>
      <el-input type="textarea"
                style="margin-bottom: 16px"
                v-model="form.description"
                label="池说明"></el-input>
      <div style="display: flex; align-items: center">
        <div style="flex: 1">
          <div>
            <div class="dialogfontsize15"
                 style="margin-bottom: 8px">是否公开:</div>
            <el-radio-group v-model="form.ispublic"
                            style="margin-bottom: 16px">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </div>
        </div>
        <div v-show="ismanager"
             style="flex: 1">
          <div>
            <div class="dialogfontsize15"
                 style="margin-bottom: 8px">选择基金经理池类型:</div>
            <el-select v-model="selectType"
                       placeholder="请选择">
              <el-option v-for="item in options"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value">
              </el-option>
            </el-select>
          </div>
        </div>
      </div>

      <div>
        <div style="margin-bottom: 8px"
             class="dialogfontsize15">基准选择:</div>
        <div>
          <search-component ref="searchComponentIndex"
                            type="index"
                            select-style="width: 100%"
                            placeholder="输入简拼、代码、名称查询指数"
                            @resolveFather="getIndexInfo"></search-component>
        </div>
      </div>
      <div v-show="isdb"
           class="mt-8">
        <div style="margin-bottom: 8px"
             class="dialogfontsize15">对标标的选择:</div>
        <div>
          <search-component ref="searchComponentFund"
                            :type="ismanager?'manager':'fund'"
                            select-style="width: 100%"
                            placeholder="输入简拼、代码、名称查询"
                            @resolveFather="getFundInfo"></search-component>
        </div>
      </div>
      <!-- <div class="mt-8">
        <div style="margin-bottom: 8px"
             class="dialogfontsize15">指定人查看:</div>
        <div>
          <el-cascader style="width: 100%"
                       placeholder="选择可查看人员"
                       :options="userList"
                       v-model="form.user_ids"
                       :props="{ multiple: true, emitPath: false }"
                       filterable></el-cascader>
        </div>
      </div> -->

      <span slot="footer"
            class="dialog-footer">
        <el-button type="primary"
                   @click="submit">新 增</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import { postPoolInfo } from "@/api/pages/SystemMixed.js";
import {
  postPoolInfo
} from "@/api/pages/tools/pool.js";
import searchComponent from "@/components/components/components/search/index.vue";
export default {
  components: { searchComponent },
  data () {
    return {
      visible: false,
      options: [{ value: 'activeequity', label: '主动权益' },
      { value: 'hkequity', label: '港股权益' },
      { value: 'bond', label: '固收+' },
      { value: 'cbond', label: '可转债' },
      { value: 'purebond', label: '纯债' },
      { value: 'bill', label: '中短债' },
      ],
      selectType: 'activeequity',
      form: {
        description: '',
        ispublic: 0,
        index_code: "000300.SH",
        compareFund: "",

      }
    };
  },
  props: {
    userList: {
      type: Object,
      default: []
    },
    isdb: {
      type: Number,
      default: 0
    },
    ismanager: {
      type: Boolean
    }
  },
  methods: {
    getData () {
      this.visible = true;
      this.$nextTick(() => {
        this.$refs["searchComponentIndex"].setDefaultValue("沪深300");
      });
    },
    getIndexInfo (val) {
      this.form.index_code = val.id;
    },
    getFundInfo (val) {
      this.form.compareFund = val.id;
    },
    async submit () {
      if (!this.form.name) {
        this.$message.warning("请输入池名称");
        return;
      }
      if (this.isdb) {
        if (!this.form.compareFund) {
          this.$message.warning('请选择对标标的');
          return;
        }
      }
      let postData = {
        name: this.form.name,
        description: this.form.description,
        status: 0,
        ispublic: this.form.ispublic,
        status3: this.isdb,
        index_code: this.form.index_code,
        userlist: this.form.user_ids,
        flag: this.ismanager ? 'manager' : 'fund',//GD,
        time_window: 0,
        type: this.ismanager ? this.selectType : 'equity',
      };
      if (this.isdb == 1) {
        postData["compareFund"] = this.form.compareFund;
      }
      else {
        postData["compareFund"] = '';
      }
      let data = await postPoolInfo(postData);
      if (data?.mtycode == 200) {
        this.$message.success("创建成功");
        this.visible = false;
        this.$emit("resolveFather");
      } else {
        this.$message.warning(data?.message || "创建失败");
      }
    }
  }
};
</script>

<style></style>
