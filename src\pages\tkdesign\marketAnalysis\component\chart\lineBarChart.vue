<template>
	<div class="charts_fill_class" v-loading="loading">
		<v-chart
			ref="companySizeChange"
			:options="option"
			element-loading-text="暂无数据"
			element-loading-spinner="el-icon-document-delete"
			element-loading-background="rgba(239, 239, 239, 0.5)"
			class="charts_one_class"
			autoresize
		></v-chart>
	</div>
</template>

<script>
import VChart from 'vue-echarts';
import FUNC from '@/utils/commonFunc';
import { barChartOption } from '@/utils/chartStyle.js';
import stringTool from '@/pages/tkdesign/components/string.tool';
import { getPositionConfiguration } from '@/api/pages/tkAnalysis/captial-market.js';
export default {
	components: { VChart },
	data() {
		return {
			option: {},
			loading: true
		};
	},
	methods: {
		async getData(params) {
			this.loading = true;
			let res = await getPositionConfiguration(params);
			this.loading = false;

			if (res.code !== 200) {
				return {};
			}
			this.$emit('getAver', res.data.averageEquityPosition);
			const { date_list, data1, data2, date_list2 } = this.filterData(res.data);
			this.option = barChartOption({
				toolbox: false,
				color: [
					{
						type: 'linear',
						x: 0,
						y: 0,
						x2: 0,
						y2: 1,
						colorStops: [
							{
								offset: 0,
								color: 'rgb(255, 169, 58)' // 0% 处的颜色
							},
							{
								offset: 1,
								color: 'rgb(255, 224, 184)' // 100% 处的颜色
							}
						],
						global: false // 缺省为 false
					}
				],
				legend: {
					top: '90%',
					data: [{ name: '权益仓位' }, { name: '指数', icon: 'line' }]
				},
				tooltip: {
					backgroundColor: '#ffffff',
					formatter: (params) => {
						let str = ``;
						for (let i = 0; i < params.length; i++) {
							let value = params[i].value[1] * 1 && !isNaN(params[i].value[1]) ? (params[i].value[1] * 1).toFixed(2) + '%' : '--';
							let value2 = params[i].value * 1 && !isNaN(params[i].value) ? params[i].value.toFixed(2) + '%' : '--';
							str += `<div style="margin-bottom:8px;display:flex;align-items:center;justify-content:space-between;"><div style="display:flex;align-items:center;"><div>${
								params[i].axisValue
							}      ${params[i].seriesName}:</div></div><div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">${
								params[i].seriesName === '指数' ? value2 : value
							}</div></div>`;
						}
						return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${str}</div>`;
					}
				},
				grid: {
					bottom: '58px'
				},
				// tooltip: {
				// 	formatter: function (value) {
				// 		console.log(value)
				// 				return stringTool.fix2pxx(value);
				// 			}
				// 		},
				xAxis: [
					// {
					// 	type: 'category',
					// 	boundaryGap: true,
					// 	show: false,
					// 	data: date_list,
					// 	axisPointer: {
					// 		type: 'shadow'
					// 	}
					// },
					{
						type: 'category',
						boundaryGap: true,

						data: date_list2,
						axisPointer: {
							type: 'shadow'
						}
					}
				],
				yAxis: [
					{
						type: 'value'
					},
					{
						type: 'value',
						min: 0,
						max: 100,
						show: true
					}
				],
				series: [
					{
						name: '指数',
						type: 'line',
						yAxisIndex: 0,
						// xAxisIndex: 1,
						itemStyle: {
							color: 'rgba(55, 120, 246, 1)'
						},
						data: data2,
						symbol: 'none'
					},
					{
						name: '权益仓位',
						type: 'bar',
						yAxisIndex: 1,
						barWidth: 40,
						// barMaxWidth: 40,
						// itemStyle:{
						// 	color:[{
						// 		offset: 0, color: 'rgb(255, 224, 184)' // 0% 处的颜色
						// 	}, {
						// 		offset: 1, color: 'rgb(255, 169, 58)' // 100% 处的颜色
						// 	}]
						// },
						data: data1
					}
				]
			});
		},
		filterData(data) {
			const date_list = data.dataList.map((item) => {
				const arr = item.date.split(' ');
				if (arr.length === 2) {
					const [year, qauter] = arr;
					const num = qauter.split('Q')[1];
					// return FUNC.getQuarterEndDate(num, year);
					return FUNC.returnQuarter(item.date, 'end', 'quarter');
				}
				return item.date;
			});
			const date_list2 = data.fitTrendList.map((item) => {
				const arr = item.date.split(' ');
				if (arr.length === 2) {
					const [year, qauter] = arr;
					const num = qauter.split('Q')[1];
					return FUNC.getQuarterEndDate(num, year);
					// return FUNC.returnQuarter(item.date, 'end', 'quarter');
				}
				return item.date;
			});
			// const date_list = [...new Set([...date1,...date2])];
			const data1 = data.dataList.map((item) => {
				return [FUNC.returnQuarter(item.date, 'end', 'quarter'), item.stockPosition];
			});
			const data2 = data.fitTrendList.map((item) => (item.point || 0) * 100);
			this.$emit('getChartData', { date_list, data1, data2, date_list2 });
			return { date_list, data1, data2, date_list2 };
		}
	}
};
</script>

<style>
.chart_one {
	padding: 0;
	box-shadow: none;
}
</style>
