<template>
	<div class="charts_fill_class" v-loading="loading">
		<v-chart
			ref="companySizeChange"
			:options="option"
			element-loading-text="暂无数据"
			element-loading-spinner="el-icon-document-delete"
			element-loading-background="rgba(239, 239, 239, 0.5)"
			class="charts_one_class"
			autoresize
		></v-chart>
	</div>
</template>

<script>
import VChart from 'vue-echarts';

import { lineChartOption } from '@/utils/chartStyle.js';
export default {
	components: { VChart },
	data() {
		return {
			option: {},
			loading: true
		};
	},
	methods: {
		filtData(list) {
			let dateList = list.map((item) => item.date);
			let data = list.map((item) => ((item.rate || 0) * 100).toFixed(2));
			return { data, dateList };
		},
		getData(list) {
			this.loading = false;
			let { data, dateList } = this.filtData(list);
			this.option = lineChartOption({
				toolbox: 'none',
				xAxis: [
					{
						type: 'category',
						data: dateList,

						formatter: (val, i) => {
							if (i === 0 || i === data.length - 1 || i === Math.ceil(data.length / 2)) {
								return val;
							}
							return '';
						}
					}
				],
				grid: {
					right: '10px'
				},
				tooltip: {
					backgroundColor: '#ffffff',
					formatter: function (obj) {
						var value = `<div style="font-size:14px;">` + obj?.[0].axisValue + `</div>`;
						for (let i = 0; i < obj.length; i++) {
							value +=
								`<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
								`<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
								obj?.[i].color +
								`;"></div>` +
								`<div style="font-family: PingFang SC;">` +
								'累计收益' +
								'</div></div>' +
								`<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
								Number(obj?.[i].value) +
								'%</div>' +
								`</div>`;
						}
						return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
					}
				},
				yAxis: [
					{
						type: 'value',
						formatter: '{value}%',
						min: function (value) {
							//取最小值向下取整为最小刻度
							return Math.floor(value.min);
						},
						max: function (value) {
							//取最大值向上取整为最大刻度
							return Math.ceil(value.max);
						}
					}
				],
				series: [
					{
						type: 'line',
						tooltip: {
							valueFormatter: function (value) {
								return value + ' °C';
							}
						},
						itemStyle: {
							color: 'rgba(55, 120, 246, 1)'
						},
						data: data,

						symbol: 'none'
					}
				]
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.chart_one {
	padding: 0;
	box-shadow: none;
}
.charts_fill_class {
	.echarts {
		height: 248px;
	}
}
</style>
