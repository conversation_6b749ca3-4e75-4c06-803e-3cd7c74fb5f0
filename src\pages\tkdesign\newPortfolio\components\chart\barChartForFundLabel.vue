<template>
		<div class="charts_fill_class" v-loading="loading">
			<el-empty image-size="160" v-if="showEmpty"></el-empty>
			<v-chart
				v-else
				ref="companySizeChange"
				:options="option"
				element-loading-text="暂无数据"
				element-loading-spinner="el-icon-document-delete"
				element-loading-background="rgba(239, 239, 239, 0.5)"
				class="charts_one_class"
				autoresize
				@legendselectchanged="handleLegendSelectChanged"
				@zr:dblclick="handleDblClick"
			></v-chart>
		</div>
</template>

<script>
import VChart from 'vue-echarts';

import { barChartOption } from '@/utils/chartStyle.js';
import { combinationFundTags} from '@/api/pages/tkAnalysis/portfolio.js';
export default {
	components: { VChart },
	data() {
		return {
			option: {},
			loading: true,
			showEmpty: true,
			doubleClick:false,
			legendChanged:false,
			legendData:{}
		};
	},
	watch: {
		doubleClick: {
			handler(val) {
				if(val && this.legendChanged){
					const chart = this.$refs.companySizeChange;
					let legendWai = this.legendData.name;
					for (const element in this.legendData.selected) {
						//显示当前legent 关闭非当前legent
						if (legendWai == element) {
							chart.dispatchAction({
								type: 'legendSelect',
								name: element
							});
						} else {
							chart.dispatchAction({
								type: 'legendUnSelect',
								name: element
							});
						}
					}
					this.doubleClick = false;
					this.legendChanged = false;
				}
			},
			immediate: true,
		},
	},
	methods: {
		handleDblClick(){
			this.doubleClick = true;
		},
		handleLegendSelectChanged (params)  {
			this.legendChanged = true;
			this.legendData = params;
		
			
		},
		fix2p(value) {
			return (parseInt(value * 10000) / 100).toFixed(2);
		 },
		
				
		async getData(param) {
			this.loading = true;
			let res = await combinationFundTags(param);
			this.loading = false;
			if(res.mtycode != 200){
				return;
			}
			if(res.data.length >0){
				this.showEmpty = false;
			}else{
				this.showEmpty = true;
			}
			const {date_list,legendList,series} = this.filterData(res.data);
			this.$emit('tableData',{date_list,legendList,series});
			this.option = barChartOption({
				toolbox:false,
				color:['#4096ff', '#4096ff', '#7388A9', '#6F80DD'],
				tooltip: {
					formatter: function (obj) {
                        //数据排序
                        let list = obj;
						list.sort((a,b)=>{
							if(a.value-b.value < 0){
								return 1;
							}else{
								return -1;
							}
						})
						var value = `<div style="font-size:14px;">` + list?.[0].axisValue + `</div>`;
						for (let i = 0; i < list.length; i++) {
							if(Number(list[i].value) == '0'){
								continue;
							}
							let currentFund = series.find((item)=>{
								return item.name == list[i].seriesName;
							})
							
							value +=
								`<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
								`<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
                                    list?.[i].color +
								`;"></div>` +
								`<div style="font-family: PingFang SC;">` +
                                    list?.[i].seriesName + `<span style="font-family: PingFang SC;">  ${currentFund.fundNum[list[i].dataIndex]}只</span>` +
								'</div></div>' +
								`<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
								(Number(list?.[i].value) * 1).toFixed(2) +
								'%</div>' +
								`</div>`;
						}
						return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
					}
				},

				legend: {
					bottom: '0px',
					data: legendList
				},
				grid:{
					bottom:70,
				},
				dataZoom: {
					bottom: 30,
				},
				xAxis: [
					{
						type: 'category',
						boundaryGap: true,
						data: date_list,
						axisPointer: {
							type: 'shadow'
						}
					}
				],
				yAxis: [
					{
						type: 'value',
						min:0
					}
					
				],
				series: series
			});
		},
		filterData(data){
			const date_list = data.map((item)=>item.date);
			let legendList = [];
			data.forEach((item)=>{
				item.dateList.forEach((item)=>{
					legendList.push({name:item.type})
				})
			
			})
			legendList = legendList.map((item)=>JSON.stringify(item));
			legendList = [...new Set(legendList)].map((item)=>JSON.parse(item));
			const series = legendList.map((item,index)=>{
				return {
					name:item.name,
					type:'bar',
					data:data.map((item2)=>{
						let sum = '0';
						item2.dateList.forEach((item3)=>{
							if(item3.type == item.name){
								sum = this.fix2p(item3.sumWeight);
							}
						})
						return sum;
					}),
					fundNum:data.map((item2)=>{
						let fundNum = 0;
						item2.dateList.forEach((item3)=>{
							if(item3.type == item.name){
								fundNum = item3.fundNumber;
							}
						})
						return fundNum;
					})
				}
			})
			return {date_list,legendList,series}
		}
	}
};
</script>

<style scoped>
.chart_one{
	padding: 0;
	box-shadow: none;
}
</style>
