<template>
	<div class="custom_tag">
		<div>
			<div class="flex_between mb-16">
				<div>在这里你可以创建自定义的基金分类标签，在基金筛选页面可以选择使用</div>
				<div><el-button type="primary" @click="openCustomTag">添加分类</el-button></div>
			</div>
			<el-table :data="data" height="calc(100vh - 425px)">
				<el-table-column
					v-for="item in column"
					:key="item.value"
					:prop="item.value"
					:label="item.label"
					:width="item.width"
					align="gotoleft"
				>
					<template slot-scope="{ row }">
						<div v-if="item.value == 'second_type'">
							<span v-for="v in row[item.value]" :key="v.id" class="mr-8">{{ v.name }}</span>
						</div>
						<div v-else>{{ row[item.value] }}</div>
					</template>
				</el-table-column>
				<el-table-column align="gotoleft" label="操作" width="200px">
					<template slot-scope="{ row }">
						<div class="flex_start">
							<el-link class="mr-8" @click="openCustomTag(row)">编辑</el-link>
							<el-link class="mr-8" @click="openSetFunds(row)">配置产品</el-link>
							<el-link class="mr-8" @click="deleteTag(row)">删除</el-link>
						</div>
					</template>
				</el-table-column>
				<template slot="empty">
					<el-empty></el-empty>
				</template>
			</el-table>
			<div class="flex_between mt-16">
				<div style="font-family: 'PingFang'; font-style: normal; font-weight: 400; font-size: 14px; color: rgba(0, 0, 0, 0.65)">
					共{{ total }}条数据
				</div>
				<div class="flex_start">
					<el-select
						v-model="pageSize"
						filterable
						allow-create
						default-first-option
						placeholder=""
						style="width: 60px"
						@change="handleSizeChange"
					>
						<el-option v-for="item in size_list" :key="item.value" :label="item.labe" :value="item.value"> </el-option>
					</el-select>
					<span style="margin-left: 8px; font-size: 13px">条/页</span>
					<el-pagination
						@page-size-change="handleSizeChange"
						@current-change="handlePageChange"
						:current-page.sync="pageNum"
						:page-size="pageSize"
						layout="prev, pager, next, jumper"
						:total="total"
					>
					</el-pagination>
				</div>
			</div>
		</div>
		<edit-tag ref="editTag" @resolveFather="saveCustomTags"></edit-tag>
		<set-tag-funds ref="setTagFunds" @resolveFather="setTagFunds"></set-tag-funds>
	</div>
</template>

<script>
import editTag from '@/pages/filter/custom/components//components/editTag';

import setTagFunds from './components/setTagFunds.vue';

import { getCustomTags, saveCustomTags, importTagByHand, deleteTag } from '@/api/pages/SystemMixed.js';

export default {
	components: { editTag, setTagFunds },
	data() {
		return {
			total: 0,
			pageNum: 0,
			pageSize: 10,
			size_list: [
				{
					label: '10',
					value: '10'
				},
				{
					label: '20',
					value: '20'
				},
				{
					label: '50',
					value: '50'
				},
				{
					label: '100',
					value: '100'
				}
			],
			column: [
				{
					label: '一级分类',
					value: 'first_type',
					width: '200px'
				},
				{
					label: '二级分类',
					value: 'second_type'
				}
			],
			data: []
		};
	},
	methods: {
		async getData() {
			let data = await getCustomTags({ pageNum: 0, pageSize: 10 });
			if (data?.mtycode == 200) {
				this.data = data?.data?.records
					.filter((v) => v.children.length != 0)
					.map((item) => {
						return {
							id: item.id,
							first_type: item.name,
							second_type: item.children
						};
					});
			}
		},
		openCustomTag(data) {
			if (data?.first_type) {
				this.$refs['editTag'].getData(data);
			} else {
				this.$refs['editTag'].getData();
			}
		},
		openSetFunds(data) {
			this.$refs['setTagFunds']?.getData(data);
		},
		// 删除自定义分类
		async deleteTag(row) {
			let data = await deleteTag({ categoryId: row.id });
		},
		// 保存自定义标签
		async saveCustomTags(postData) {
			let data = await saveCustomTags(postData);
			if (data?.mtycode == 200) {
				this.$message.success('操作成功');
			}
			this.getData();
		},
		handlePageChange(val) {
			this.pageNum = val;
			this.getData();
		},
		handleSizeChange(val) {
			this.pageSize = val;
			this.getData();
		}
	}
};
</script>

<style lang="scss" scoped>
.custom_tag {
	.el-form-item__label {
		width: auto !important;
	}
	.el-form-item__content {
		margin-left: auto !important;
	}
}
</style>
