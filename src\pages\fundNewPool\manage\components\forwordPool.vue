<template>
  <el-dialog title="转发"
             :visible.sync="visible"
             width="400px"
             destroy-on-close>
    <div style="margin-bottom: 8px"
         class="dialogfontsize15">{{ '选择转发人员' }}:</div>
    <div>
      <el-select style="width: 100%"
                 :placeholder="'选择转发人员'"
                 v-model="user_ids"
                 multiple
                 filterable>
        <el-option v-for="item in userList"
                   :key="item.value"
                   :label="item.label"
                   :value="item.value">
        </el-option>
      </el-select>
    </div>

    <span slot="footer"
          class="dialog-footer">
      <el-button type="primary"
                 @click="submit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { putPoolInfo } from '@/api/pages/tools/pool.js';
export default {
  data () {
    return {
      visible: false,
      user_ids: [],
      item_info: {}
    };
  },
  props: {
    userList: {
      type: Object,
      default: []
    },
    ismanager: {
      type: <PERSON><PERSON><PERSON>,
    }
  },
  methods: {
    getData (val) {
      console.log(this.userList);
      this.visible = true;
      this.user_ids = val.user_list;
      this.item_info = val;
    },
    async submit () {
      let data = await putPoolInfo({
        user_permission: this.user_ids,
        id: String(this.item_info.id),
        name: this.item_info.name,
        description: this.item_info.description,
        status: this.item_info.status == '--' ? 0 : this.item_info.status,
        index_code: this.item_info.indexCode,
        status3: this.item_info.status3,
        userlist: this.item_info.user_ids,
        ispublic: this.item_info.ispublic,
        ismanager: this.ismanager,
        // user_permission: [],
      });
      this.$message.success('编辑成功');
      this.visible = false;
      this.$emit('resolveFather');
    }
  }
};
</script>

<style></style>
