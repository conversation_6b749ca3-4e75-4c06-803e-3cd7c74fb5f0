<!--  -->
<template>
	<div class="chart_one" v-if="show">
		<div style="display: flex; align-items: center; justify-content: space-between">
			<div class="title" style="margin-bottom: 24px">权益基金alpha/beta/smartbeta分解</div>
			<el-button icon="el-icon-document-delete" @click="exportExcel">导出Excel</el-button>
		</div>
		<el-table :data="datatable" class="table" ref="multipleTable" max-height="400px" header-cell-class-name="table-header">
			<el-table-column prop="year" label="年份" sortable align="gotoleft"> </el-table-column>
			<el-table-column prop="alpha" label="alpha" sortable align="gotoleft">
				<template slot-scope="scope"
					><span>{{ scope.row.alpha | fix4 }}</span></template
				>
			</el-table-column>
			<el-table-column prop="factor" label="alpha排名" sortable align="gotoleft">
				<template slot-scope="scope"
					><span>{{ scope.row.alpha_rank | fix2p }}</span></template
				>
			</el-table-column>
			<el-table-column prop="std" align="gotoleft" sortable label="beta">
				<template slot-scope="scope"
					><span>{{ scope.row.beta | fix4 }}</span></template
				>
			</el-table-column>
			<el-table-column prop="gainorloss" label="beta排名" sortable align="gotoleft">
				<template slot-scope="scope"
					><span>{{ scope.row.beta_rank | fix2p }}</span></template
				>
			</el-table-column>
			<el-table-column prop="adaptivity" align="gotoleft" sortable label="smartbeta">
				<template slot-scope="scope"
					><span>{{ scope.row.smart | fix4 }}</span></template
				>
			</el-table-column>
			<el-table-column prop="gainorloss" label="smartbeta排名" sortable align="gotoleft">
				<template slot-scope="scope"
					><span>{{ scope.row.smart_rank | fix2p }}</span></template
				>
			</el-table-column>
		</el-table>
	</div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { exportTitle, exportTable } from '@/utils/exportWord.js';
import { filter_json_to_excel } from '@/utils/exportExcel.js';
export default {
	//import引入的组件需要注入到对象中才能使用
	filters: {
		fix2p(value) {
			if (value == '--') return value;
			else return (value * 100).toFixed(2) + '%';
		},
		fixY(value) {
			if (value == '--') return value;
			else {
				return (Number(value) / 100000000).toFixed(2) + '亿';
			}
		},

		fix4(value) {
			if (value == '--') return value;
			else {
				return Number(value).toFixed(4);
			}
		},
		fixp(value) {
			if (value == '--') return value;
			else {
				return Number(value).toFixed(2) + '%';
			}
		}
	},
	data() {
		return {
			datatable: [],
			notesData: {
				abs001: ''
			},
			show: true
		};
	},
	methods: {
		getData(data) {
			this.show = true;
			this.datatable = data;
		},
		hideLoading() {
			this.show = false;
		},
		exportExcel() {
			let list = [
				{
					label: '年份',
					value: 'year'
				},
				{
					label: 'alpha',
					value: 'alpha',
					format: 'fix4'
				},
				{
					label: 'alpha排名',
					value: 'fix2p'
				},
				{
					label: 'beta',
					value: 'beta',
					format: 'fix4'
				},
				{
					label: 'beta排名',
					value: 'beta_rank',
					format: 'fix2p'
				},
				{
					label: 'smartbeta',
					value: 'smart',
					format: 'fix4'
				},
				{
					label: 'smartbeta排名',
					value: 'smart_rank',
					format: 'fix2p'
				}
			];
			filter_json_to_excel(list, this.datatable, '权益基金alpha/beta/smartbeta分解');
		},
		createPrintWord() {
			let list = [
				{
					label: '年份',
					value: 'year'
				},
				{
					label: 'alpha',
					value: 'alpha',
					format: 'fix4'
				},
				{
					label: 'alpha排名',
					value: 'fix2p'
				},
				{
					label: 'beta',
					value: 'beta',
					format: 'fix4'
				},
				{
					label: 'beta排名',
					value: 'beta_rank',
					format: 'fix2p'
				},
				{
					label: 'smartbeta',
					value: 'smart',
					format: 'fix4'
				},
				{
					label: 'smartbeta排名',
					value: 'smart_rank',
					format: 'fix2p'
				}
			];
			if (this.datatable.length) {
				return [...exportTitle('权益基金alpha/beta/smartbeta分解'), ...exportTable(list, this.datatable)];
			} else {
				return [];
			}
		}
	}
};
</script>
