<template>
	<div>
		<el-dialog title="打分" :visible.sync="visible" width="80%">
			<div>
				<score-components
					ref="scoreComponents"
					filter_type="pool"
					@resolveFather="getScoreCondition"
					:pool_id="info.code"
				></score-components>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import scoreComponents from '@/pages/filter/fund/score/score.vue';
export default {
	components: { scoreComponents },
	data() {
		return {
			visible: false,
			info: {}
		};
	},
	methods: {
		getData(info) {
			this.info = info;
			this.visible = true;
			this.$nextTick(() => {
				this.$refs['scoreComponents'].Init();
			});
		},
		getScoreCondition(val) {
			this.visible = false;
			// let item = [];
			// val.map((v) => {
			// 	item.push(...v.item);
			// });
			this.$emit('resolveFather', val);
		}
	}
};
</script>

<style></style>
