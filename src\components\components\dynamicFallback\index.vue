<!-- 动态回撤 -->
<template>
	<div class="chart_one">
		<div v-loading="loadyejis">
			<div class="card_header">
				<div class="title">动态回撤</div>
			</div>
			<div class="charts_fill_class" v-loading="loading">
				<v-chart
					element-loading-text="暂无数据"
					element-loading-spinner="el-icon-document-delete"
					element-loading-background="rgba(239, 239, 239, 0.5)"
					class="charts_one_class"
					style="height: 442px"
					ref="dynamicFallback"
					autoresize
					:options="option"
				/>
			</div>
		</div>
	</div>
</template>

<script>
import VChart from 'vue-echarts';
import { lineChartOption } from '@/utils/chartStyle';
import searchComponents from '@/components/components/components/search/index.vue';

export default {
	name: 'dynamicFallback',
	components: { VChart, searchComponents },
	data() {
		return {
			options: [
				{
					label: '总回报',
					value: 'all'
				}
			],
			model: '总回报',
			option: {},
			indexInfo: {
				id: '000300.SH',
				name: '沪深300',
				flag: 'index'
			},
			info: {},
			loading: true
		};
	},
	methods: {
		getData(data, info, indexInfo) {
			this.indexInfo = indexInfo ? indexInfo : this.indexInfo;
			this.loading = false;
			if (!data) {
				return;
			}
			this.info = info;
			let legend = [this.info.name, this.indexInfo.name];
			let index_date = data?.index_date.slice(data?.index_date.indexOf(data?.date.sort()[0]));
			let index_drawdown = data?.index_drawdown.slice(data?.index_date.indexOf(data?.date.sort()[0]));
			let xAxis = Array.from(new Set([...data?.date, ...index_date])).sort();
			let series = [
				{
					name: this.info.name,
					type: 'line',
					data: data.date.map((item, index) => {
						return [item, (data.daily_drawdown[index] == 0 ? 0 : -1 * data.daily_drawdown[index] * 100)?.toFixed(2) || '--'];
					}),
					symbol: 'none',
					areaStyle: {}
				},
				{
					name: this.indexInfo.name,
					type: 'line',
					data: index_date.map((item, index) => {
						return [item, (index_drawdown[index] == 0 ? 0 : -1 * index_drawdown[index] * 100)?.toFixed(2) || '--'];
					}),
					symbol: 'none'
				}
			];
			this.option = lineChartOption({
				legend,
				xAxis: [{ data: xAxis }],
				yAxis: [
					{
						type: 'value',
						name: '回撤值',
						scale: true,
						formatter: function (val) {
							return val + '%';
						}
					}
				],
				tooltip: {
					formatter: function (obj) {
						var value = obj[0].axisValue + `<br />`;
						for (let i = 0; i < obj.length; i++) {
							value +=
								`<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:` +
								obj[i].color +
								`;"></span>` +
								obj[i].seriesName +
								':' +
								Number(obj[i].data[1]).toFixed(2) +
								'%' +
								`<br />`;
						}
						return value;
					}
				},
				dataZoom: true,
				series
			});
		},
		getIndexInfo(info) {
			this.indexInfo = info;
			this.loading = true;
			this.$emit('resolveFather', info.id);
		},
		createPrintWord() {
			this.$refs['dynamicFallback'].mergeOptions({ toolbox: { show: false } });
			let height = this.$refs['dynamicFallback']?.$el.clientHeight;
			let width = this.$refs['dynamicFallback']?.$el.clientWidth;
			let chart = this.$refs['dynamicFallback'].getDataURL({
				type: 'png',
				pixelRatio: 3,
				backgroundColor: '#fff'
			});
			this.$refs['dynamicFallback'].mergeOptions({ toolbox: { show: true } });
			return [...this.$exportWord.exportTitle('动态回撤'), ...this.$exportWord.exportChart(chart, { width, height })];
		}
	}
};
</script>

<style></style>
