import request from '@/api/request';

/**
 *
 * @param {获取自定义显示模版列表} params
 * @returns
 */
export function getUserConfig(params) {
	return request({
		url: '/UserConfig/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {获取自定义模版详情} params
 * @returns
 */
export function getUserConfigInfo(params) {
	return request({
		url: '/UserConfigInfo/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {新增自定义模版} params
 * @returns
 */
export function postUserConfigInfo(data) {
	return request({
		url: '/UserConfigInfo/',
		method: 'post',
		data
	});
}
/**
 *
 * @param {修改自定义模版} params
 * @returns
 */
export function putUserConfigInfo(data) {
	return request({
		url: '/UserConfigInfo/',
		method: 'put',
		data
	});
}
/**
 *
 * @param {删除自定义模版详} params
 * @returns
 */
export function deleteUserConfigInfo(params) {
	return request({
		url: '/UserConfigInfo/',
		method: 'delete',
		params
	});
}
