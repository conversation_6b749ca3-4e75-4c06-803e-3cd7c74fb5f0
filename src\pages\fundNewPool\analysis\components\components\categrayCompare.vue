<template>
  <div class="categray_compare"
       v-show="show">
    <div>
      <div class="flex_card">
        <div v-for="item in templateList"
             :key="item.value"
             v-show="item.isshow"
             :class="item.type">
          <component :is="item.is"
                     :ismanager="ismanager"
                     :ref="item.value"
                     data_type="pool"
                     types="equity"
                     @resolveFather="item.methods"></component>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// 指标选择器
import targetChoose from '@/pages/fundNewPool/analysis/components/components/targetChoose.vue';
// 相关系数
import correlationCoefficient from '@/pages/fundNewPool/analysis/components/components/correlationCoefficient.vue';
// 资产配置分析
import assetAllocationAnalysis from '@/pages/fundNewPool/analysis/components/components/assetAllocationAnalysis.vue';
// 前十大持仓
import holdTop10 from '@/pages/fundNewPool/analysis/components/components/holdTop10.vue';
// 行业能力
import industryCapability from '@/pages/fundNewPool/analysis/components/components/industryCapability.vue';
// 市场适应性
import marketCapability from '@/pages/fundNewPool/analysis/components/components/marketCapability.vue';
export default {
  components: { targetChoose, correlationCoefficient, assetAllocationAnalysis, holdTop10, industryCapability, marketCapability },
  data () {
    return {
      show: false,
      info: {},
      templateList: [
        {
          name: '指标选择器',
          is: 'targetChoose',
          value: 'targetChoose',
          type: 'big_template',
          isshow: true
        },
        {
          name: '相关系数',
          is: 'correlationCoefficient',
          value: 'correlationCoefficient',
          type: 'big_template',
          isshow: true
        },
        {
          name: '资产配置分析',
          is: 'assetAllocationAnalysis',
          value: 'assetAllocationAnalysis',
          type: 'big_template',
          isshow: true
        },
        {
          name: '前十大持仓',
          is: 'holdTop10',
          value: 'holdTop10',
          type: 'big_template',
          isshow: true
        },
        {
          name: '行业能力',
          is: 'industryCapability',
          value: 'industryCapability',
          class: ['equity'],
          type: 'big_template',
          isshow: true,
          is_request: false,
          getData: 'getIndustryCapabilityData',
          getRequestData: 'getIndustryCapability'
        },
        {
          name: '市场适应性',
          is: 'marketCapability',
          value: 'marketCapability',
          class: ['equity'],
          type: 'big_template',
          isshow: true,
          is_request: false,
          getData: 'getMarketCapabilityData',
          getRequestData: 'getMarketCapability'
        }
      ]
    };
  },
  props: {
    ismanager: {
      type: Boolean
    }
  },
  methods: {
    getData (info) {
      this.info = info;
      this.show = true;
      this.$nextTick(() => {
        this.$refs['targetChoose']?.[0].getData({ ...this.info, code_list: this.info.children_pool_list });
        this.$refs['correlationCoefficient']?.[0].getData({ ...this.info, code_list: this.info.children_pool_list });
        this.$refs['assetAllocationAnalysis']?.[0].getData({ ...this.info, code_list: this.info.children_pool_list });
        this.$refs['holdTop10']?.[0].getData(this.info);
        this.$refs['industryCapability']?.[0].getData({ ...this.info, code_list: this.info.children_pool_list });
        this.$refs['marketCapability']?.[0].getData({ ...this.info, code_list: this.info.children_pool_list });
      });
    },
    refresInfo (info) {
      this.info = info;
      this.show = true;
    },
    hideAnalysis () {
      this.show = false;
    }
  }
};
</script>

<style></style>
