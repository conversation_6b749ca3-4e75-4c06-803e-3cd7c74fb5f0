<!--  -->
<template>
	<div class="portfoliocreat">
		<el-dialog v-loading="loading" class="FTBdialog" width="686px" height="444px" :visible.sync="openPortfolio">
			<div slot="title">
				<span
					style="
						font-family: 'PingFang';
						font-style: normal;
						font-weight: 500;
						font-size: 16px;
						line-height: 24px;
						color: rgba(0, 0, 0, 0.85);
						width: 100%;
					"
					>快速创建组合</span
				>
			</div>
			<div style="width: 100%; height: 1px; background: rgba(0, 0, 0, 0.06); margin-bottom: 16px"></div>
			<div style="display: flex; margin-bottom: 16px">
				<div style="flex: 1">组合名称：<el-input v-model="Pname" style="width: 216px" placeholder="请输入"></el-input></div>
				<div style="flex: 1">
					<span style="margin-left: 50px">创建人：</span
					><el-input disabled v-model="creater" style="width: 216px" placeholder="请输入"></el-input>
				</div>
			</div>
			<div style="display: flex; margin-bottom: 16px">
				<div style="flex: 1">创建日期：<el-input disabled v-model="createdate" style="width: 216px" placeholder="请输入"></el-input></div>
				<div style="flex: 1">
					<span style="margin-left: 36px">成立日期：</span>
					<el-date-picker v-model="founddate" type="date" value-format="yyyy-MM-dd" style="width: 216px" placeholder="请选择">
					</el-date-picker>
				</div>
			</div>
			<div style="display: flex; margin-bottom: 16px">
				<div style="flex: 1">
					结束日期：
					<el-date-picker v-model="enddate" type="date" value-format="yyyy-MM-dd" style="width: 216px" placeholder="请选择">
					</el-date-picker>
				</div>

				<div style="flex: 1">
					<span style="margin-left: 36px">调仓日期：</span>
					<el-date-picker v-model="founddate" type="date" disabled value-format="yyyy-MM-dd" style="width: 216px" placeholder="请选择">
					</el-date-picker>
				</div>
			</div>
			<div style="display: flex; margin-bottom: 24px">
				<div style="flex: 1">
					<span>分红处理：</span
					><el-radio-group v-model="moneyDo">
						<el-radio :label="true">提取现金</el-radio>
						<el-radio :label="false">分红再投资</el-radio>
					</el-radio-group>
				</div>
				<div style="flex: 1">
					<span style="margin-left: 36px">是否置顶：</span
					><el-radio-group v-model="istop">
						<el-radio :label="true">是</el-radio>
						<el-radio :label="false">否</el-radio>
					</el-radio-group>
				</div>
			</div>
			<div style="margin-bottom: 16px">
				<div style="margin-bottom: 8px">组合说明:</div>
				<el-input v-model="description" :autosize="{ minRows: 4, maxRows: 8 }" placeholder="请输入" type="textarea"></el-input>
			</div>
			<div style="text-align: right">
				<el-button type="" @click="openPortfolio = false">取消</el-button>
				<el-button type="primary" @click="CrePort()">确认</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';

import { getList, createCom2, delCom, upCom, getListAll } from '@/api/pages/SystemMixed.js';
export default {
	//import引入的组件需要注入到对象中才能使用
	components: {},
	data() {
		//这里存放数据
		return {
			list: [],
			openPortfolio: false,
			Pname: '',
			creater: '',
			createdate: '',
			founddate: '',
			enddate: '',
			moneyDo: true,
			istop: false,
			description: '',
			loading: false
		};
	},
	//监听属性 类似于data概念
	computed: {},
	//监控data中的数据变化
	watch: {},
	//方法集合
	methods: {
		show(list, filter, type2) {
			this.openPortfolio = true;
			this.list = [];
			for (let i = 0; i < list.length; i++) {
				this.list.push(list[i].code);
			}
			// this.list = list;
			this.creater = this.$store.state.username;
			this.createdate = this.moment(new Date()).format('YYYY-MM-DD');
		},
		async CrePort() {
			this.loading = true;
			if (this.FUNC.isEmpty(this.founddate)) {
			} else {
				this.$message.error('请选择组合成立日期');
				return false;
			}
			if (this.FUNC.isEmpty(this.Pname)) {
			} else {
				this.$message.error('请输入组合名称');
				return false;
			}
			let { data, mtymessage, mtycode } = await createCom2({
				name: this.Pname,
				create_date: this.createdate,
				date: this.founddate,
				end_date: this.enddate,
				flag: this.moneyDo,
				ispublic: 1,
				isshow: this.istop,
				description: this.description,
				codeList: this.list
			});
			if (mtycode == 200) {
				// try {
				// 	window.localStorage.setItem('mty_portfolio_founddate', JSON.stringify(this.founddate));
				// } catch (e) {
				// 	// this.$message.warning('缓存已满，无法存入');
				// 	// window.localStorage.setItem('mty_portfolio_founddate', JSON.stringify(''));
				// }
				this.$message.success('创建成功');
				this.openPortfolio = false;
				this.$router.push('/portfolioSelf');
			} else {
				this.$message.warning('创建失败，' + mtymessage);
			}
			this.loading = false;
		}
	},
	//生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	//生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	beforeCreate() {}, //生命周期 - 创建之前
	beforeMount() {}, //生命周期 - 挂载之前
	beforeUpdate() {}, //生命周期 - 更新之前
	updated() {}, //生命周期 - 更新之后
	beforeDestroy() {}, //生命周期 - 销毁之前
	destroyed() {}, //生命周期 - 销毁完成
	activated() {} //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
</style>
