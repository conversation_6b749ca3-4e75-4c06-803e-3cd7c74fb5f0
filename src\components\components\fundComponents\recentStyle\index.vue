<template>
	<div v-loading="loading" id="recentStyle">
		<div>
			<analysis-card-title title="近期风格" image_id="recentStyle">
				<!-- <QuickTimePicker v-model="preset_time" @change="changeTimePicker"></QuickTimePicker> -->
			</analysis-card-title>
			<div>
				<div style="width: 100%; display: flex; flex-wrap: wrap; justify-content: center">
					<div v-if="fundtype == 'bond' && isNull(pilu_gupiaoT)" style="display: flex; margin-right: 24px; align-items: center">
						<div style="color: #5c6e8f"><span>股票仓位</span><span style="color: #a9a9a9">(报告)</span>：</div>
						<div class="fzRecent" style="height: none; flex: 1">{{ Number(pilu_gupiaoT).toFixed(0) }}%</div>
					</div>
					<div v-if="fundtype != 'cbond' && isNull(pilu_gupiao)" style="display: flex; align-items: center; margin-right: 24px">
						<div style="color: #5c6e8f"><span>债券仓位</span><span style="color: #a9a9a9">(报告)</span>：</div>
						<div class="fzRecent" style="height: none; flex: 1">{{ Number(pilu_gupiao).toFixed(0) }}%</div>
					</div>
					<div
						v-if="JSON.stringify(info.type3) == 'multibondclass' && info.type != 'cbond' && isNull(maxbondweight)"
						style="display: flex; align-items: center"
					>
						<div style="color: #5c6e8f">最大占比券种：{{ maxbond }}</div>
						<div class="fzRecent" style="height: none; flex: 1">{{ Number(maxbondweight).toFixed(0) }}%</div>
					</div>
					<div v-if="info.type == 'cbond' && isNull(cbondweight)" style="display: flex; align-items: center">
						<div style="color: #5c6e8f">转债仓位：</div>
						<div class="fzRecent" style="height: none; flex: 1">{{ Number(cbondweight).toFixed(0) }}%</div>
					</div>
					<div v-if="info.type == 'cbond' && isNull(pilu_gupiaoT)" style="display: flex; align-items: center">
						<div style="color: #5c6e8f">权益仓位：</div>
						<div class="fzRecent" style="height: none; flex: 1">{{ Number(pilu_gupiaoT).toFixed(0) }}%</div>
					</div>
				</div>
				<div class="margin20px"></div>
				<div style="display: flex; text-align: center">
					<div v-show="info.type != 'cbond' && info.type != 'bond' && isNull(pilu_ganggan)" class="flex_recent">
						<div style="color: #4096FF" class="marginbottom10px">{{ (Number(pilu_ganggan) * 100).toFixed(2) }}%</div>
						<div :class="pilu_ganggan > 1.3 ? 'stylebackfull' : 'stylebacknull'"></div>
						<div :class="pilu_ganggan > 1.2 && pilu_ganggan < 1.3 ? 'stylebackfull' : 'stylebacknull'"></div>
						<div :class="pilu_ganggan > 1.1 && pilu_ganggan < 1.2 ? 'stylebackfull' : 'stylebacknull'"></div>
						<div :class="pilu_ganggan > 1.0 && pilu_ganggan < 1.1 ? 'stylebackfull' : 'stylebacknull'"></div>
						<div :class="pilu_ganggan > 0 && pilu_ganggan < 1.0 ? 'stylebackfull' : 'stylebacknull'"></div>
						<div class="textsizeyeji">
							<div>杠杆率</div>
							<div style="color: white">报告</div>
						</div>
					</div>
					<div v-show="info.type == 'cbond' && isNull(pilu_guzhi)" class="flex_recent">
						<div style="color: #4096FF" class="marginbottom10px">{{ pilu_guzhi | fixgz }}</div>
						<div :class="pilu_guzhi > 1 ? 'stylebackfull' : 'stylebacknull'"></div>
						<div :class="pilu_guzhi <= 1 && pilu_guzhi > 0 ? 'stylebackfull' : 'stylebacknull'"></div>
						<div :class="false ? 'stylebackfull' : 'stylebacknull'"></div>
						<div :class="pilu_guzhi <= 0 && pilu_guzhi > -1 ? 'stylebackfull' : 'stylebacknull'"></div>
						<div :class="pilu_guzhi < -1 ? 'stylebackfull' : 'stylebacknull'"></div>
						<div class="textsizeyeji">估值</div>
					</div>
					<div v-show="info.type == 'cbond' && isNull(pilu_jiage)" class="flex_recent">
						<div style="color: #4096FF" class="marginbottom10px">{{ pilu_jiage }}</div>
						<div :class="pilu_jiage == '股性' ? 'stylebackfull' : 'stylebacknull'"></div>
						<div :class="pilu_jiage == '均衡(哑铃)' ? 'stylebackfull' : 'stylebacknull'"></div>
						<div :class="pilu_jiage == '均衡(梯子)' ? 'stylebackfull' : 'stylebacknull'"></div>
						<div :class="pilu_jiage == '均衡(子弹)' ? 'stylebackfull' : 'stylebacknull'"></div>
						<div :class="pilu_jiage == '债性' ? 'stylebackfull' : 'stylebacknull'"></div>
						<div class="textsizeyeji">调性</div>
					</div>
					<!-- <div v-show="info.type=='bond'"  class="flex_recent">
						<div style="color: #4096FF" class="marginbottom10px">{{ pilu_dxp }}</div>
						<div :class="pilu_dxp == '重度下沉' ? 'stylebackfull' : 'stylebacknull'"></div>
						<div :class="pilu_dxp == '中度下沉' ? 'stylebackfull' : 'stylebacknull'"></div>
						<div :class="pilu_dxp == '轻度下沉' ? 'stylebackfull' : 'stylebacknull'"></div>
						<div :class="pilu_dxp == '不下沉' ? 'stylebackfull' : 'stylebacknull'"></div>
						<div :class="pilu_dxp == '不下沉' ? 'stylebackfull' : 'stylebacknull'"></div>
						<div class="textsizeyeji"><div>大小盘</div><div>程度</div></div>
					</div> TODO临时隐藏等数据-->
					<div v-show="show1 && isNull(pilu_jiuqib)" class="flex_recent">
						<div style="color: #4096FF" class="marginbottom10px">{{ pilu_jiuqib | fixDur }}</div>
						<div :class="pilu_jiuqib > 7 ? 'stylebackfull' : 'stylebacknull'"></div>
						<div :class="pilu_jiuqib < 7 && pilu_jiuqib >= 5 ? 'stylebackfull' : 'stylebacknull'"></div>
						<div :class="pilu_jiuqib < 5 && pilu_jiuqib >= 3 ? 'stylebackfull' : 'stylebacknull'"></div>
						<div :class="pilu_jiuqib < 3 && pilu_jiuqib >= 1.5 ? 'stylebackfull' : 'stylebacknull'"></div>
						<div :class="pilu_jiuqib < 1.5 ? 'stylebackfull' : 'stylebacknull'"></div>
						<div class="textsizeyeji">债券久期(估算)</div>
					</div>
					<div v-show="show2 && isNull(pilu_jiuqibb)" class="flex_recent">
						<div style="color: #4096FF" class="marginbottom10px">{{ pilu_jiuqibb | fixDur }}</div>
						<div :class="pilu_jiuqibb > 7 ? 'stylebackfull' : 'stylebacknull'"></div>
						<div :class="pilu_jiuqibb < 7 && pilu_jiuqibb >= 5 ? 'stylebackfull' : 'stylebacknull'"></div>
						<div :class="pilu_jiuqibb < 5 && pilu_jiuqibb >= 3 ? 'stylebackfull' : 'stylebacknull'"></div>
						<div :class="pilu_jiuqibb < 3 && pilu_jiuqibb >= 1.5 ? 'stylebackfull' : 'stylebacknull'"></div>
						<div :class="pilu_jiuqibb < 1.5 ? 'stylebackfull' : 'stylebacknull'"></div>
						<div class="textsizeyeji">债券久期(报告)</div>
					</div>

					<div v-show="show4 && isNull(pilu_xinyongbb)" class="flex_recent">
						<div style="color: #4096FF" class="marginbottom10px">{{ pilu_xinyongbb }}</div>
						<div :class="pilu_xinyongb == '不下沉' ? 'stylebackfull' : 'stylebacknull'"></div>
						<div :class="pilu_xinyongb == '轻度下沉' ? 'stylebackfull' : 'stylebacknull'"></div>
						<div :class="pilu_xinyongb == '灵活下沉' ? 'stylebackfull' : 'stylebacknull'"></div>
						<div :class="pilu_xinyongb == '灵活下沉' ? 'stylebackfull' : 'stylebacknull'"></div>
						<div :class="pilu_xinyongb == '下沉' ? 'stylebackfull' : 'stylebacknull'"></div>
						<div class="textsizeyeji">信用挖掘(报告)</div>
					</div>

					<div v-show="info.type == 'bond' && isNull(pilu_chengzhang)" class="flex_recent">
						<div style="color: #4096FF" class="marginbottom10px">{{ pilu_chengzhang | fixczjz }}</div>
						<div :class="pilu_chengzhang > 1 ? 'stylebackfull' : 'stylebacknull'"></div>
						<div :class="pilu_chengzhang <= 1 && pilu_chengzhang > 0 ? 'stylebackfull' : 'stylebacknull'"></div>
						<div :class="false ? 'stylebackfull' : 'stylebacknull'"></div>
						<div :class="pilu_chengzhang <= 0 && pilu_chengzhang > -1 ? 'stylebackfull' : 'stylebacknull'"></div>
						<div :class="pilu_chengzhang < -1 ? 'stylebackfull' : 'stylebacknull'"></div>
						<div class="textsizeyeji">
							<div>成长价值</div>
							<div>程度</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
// 近期风格
import { getRecentStyleInfo } from '@/api/pages/Analysis.js';
export default {
	name: 'recentStyle',
	filters: {
		fixDur(val) {
			if (typeof val == 'number') {
				if (val < 1.5) return '超短久期';
				else if (val < 3) return '短久期';
				else if (val < 5) return '中久期';
				else if (val < 7) return '中长久期';
				else return '长久期';
			} else {
				return '暂无数据';
			}
		},
		fixgz(val) {
			if (val) {
				if (val > 1) return '高估值';
				else if (val <= 1 && val > 0) return '较高估值';
				else if (val <= 0 && val > -1) return '较低估值';
				else return '低估值';
			} else {
				return '暂无数据';
			}
		},
		fixczjz(val) {
			if (val) {
				if (val > 1) return '高成长';
				else if (val <= 1 && val > 0) return '较高成长';
				else if (val <= 0 && val > -1) return '较低成长';
				else return '低成长';
			} else {
				return '暂无数据';
			}
		}
	},
	data() {
		return {
			table11: [],
			table22: [],
			table33: [],
			pilu_jiuqib: '',
			pilu_xinyongb: '',
			pilu_gupiao: '',
			pilu_gupiaoT: '',
			fundtype: '',
			maxbond: '',
			maxbondweight: '',
			cbondweight: '',
			loading: true,
			show1: true,
			show2: true,
			show3: true,
			show4: true,
			info: {},
			isbookvalue: false,
			pilu_ganggan: '',
			pilu_chengzhang: '',
			pilu_jiage: '',
			pilu_guzhi: ''
		};
	},
	methods: {
		// 获取近期风格
		async getRecentStyle() {
			let data = await getRecentStyleInfo({
				code: this.info.code,
				type: this.info.type,
				flag: this.info.flag,
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			if (data?.mtycode == 200) {
				return data?.data;
			} else {
				return [];
			}
		},

		getShow() {
			if (this.info.type == 'purebond' || this.info.type == 'bill') {
				if (JSON.stringify(this.info.isbookvalue) == 'true') {
					if (this.info.type3 == 'interest') {
						this.show1 = false;
						this.show2 = true;
						this.show3 = false;
						this.show4 = false;
					} else {
						this.show1 = false;
						this.show2 = true;
						this.show3 = false;
						this.show4 = true;
					}
				} else if (this.info.type3 == 'interest') {
					this.show1 = true;
					this.show2 = true;
					this.show3 = false;
					this.show4 = false;
				} else {
					this.show1 = true;
					this.show2 = true;
					this.show3 = true;
					this.show4 = true;
				}
			} else if (this.info.type == 'cbond') {
				this.show1 = false;
				this.show2 = false;
				this.show3 = false;
				this.show4 = false;
			} else if (this.info.type == 'bond') {
				this.show1 = false;
				this.show2 = true;
				this.show3 = false;
				this.show4 = true;
			}
		},
		// 获取报告数据
		async getData(info) {
			this.info = info;
			this.fundtype = this.info.type;
			let data = await this.getRecentStyle();
			if (JSON.stringify(info.isbookvalue) == '"true"') {
				this.isbookvalue = info.isbookvalue;
			} else {
				this.isbookvalue = false;
			}
			this.info = info;
			this.getShow();
			this.loading = false;
			this.pilu_gupiaoT = data?.equity_weight || '暂无数据';
			this.pilu_gupiao = data?.bond_weight || '暂无数据';
			this.maxbond = data.max_bond || '暂无数据';
			this.maxbondweight = data.max_bond_weight || '暂无数据';
			this.cbondweight = data.cbond_weight || '暂无数据';
			this.pilu_ganggan = data.leverage || '暂无数据';
			this.pilu_guzhi = data.value || '暂无数据';
			this.pilu_jiuqib = data?.guess_duration || '暂无数据';
			this.pilu_jiuqibb = data?.report_duration || '暂无数据';
			this.pilu_xinyongb =
				data?.credit_down == 0
					? '不下沉'
					: data?.credit_down < 0.1
					? '轻度下沉'
					: data?.credit_down < 0.8
					? '灵活下沉'
					: data?.credit_down < 1
					? '下沉'
					: '暂无数据';
			this.pilu_xinyongbb =
				data?.credit_down == 0
					? '不下沉'
					: data?.credit_down < 0.1
					? '轻度下沉'
					: data?.credit_down < 0.8
					? '灵活下沉'
					: data?.credit_down < 1
					? '下沉'
					: '暂无数据';
			this.pilu_jiage = data.cbond_style || '暂无数据';
			this.pilu_dxp = data?.dxp || '暂无数据'; //todo
			this.pilu_chengzhang = data?.growth || '暂无数据';
		},
		// 债券仓位(报告)
		for7(percentage) {
			return this.pilu_gupiao + '%';
		},
		// 股票仓位(报告)
		for8(percentage) {
			return this.pilu_gupiaoT + '%';
		},
		// 判空
		isNull(val) {
			if (val == '暂无数据' || val == '--' || val == undefined || val == '') {
				return false;
			} else {
				return true;
			}
		},
		exportImage() {
			this.html2canvas(document.getElementById('recentStyle'), { scale: 3 }).then(function (canvas) {
				let base64Str = canvas.toDataURL('image/png');
				let aLink = document.createElement('a');
				aLink.style.display = 'none';
				aLink.href = base64Str;
				aLink.download = '近期风格.jpg';
				// 触发点击-然后移除
				document.body.appendChild(aLink);
				aLink.click();
				document.body.removeChild(aLink);
			});
		}
	}
};
</script>

<style scoped>
.fzRecent {
	display: flex;
	align-items: center;
	font-family: 'PingFang';
	font-style: normal;
	font-weight: 400;
	font-size: 14px;
	line-height: 22px;
	color: rgba(0, 0, 0, 0.85);
}
.flex_recent {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}
</style>
