<template>
	<div id="performanceStyle">
		<div v-loading="loading">
			<analysis-card-title title="表现风格" image_id="performanceStyle"></analysis-card-title>
			<div>
				<div class="flex_start item_bg px-20 py-20">
					<div class="mr-20">近一年</div>
					<div class="box_style1 mr-20" style="flex: 1">
						{{ oneyear }}
					</div>
					<div class="box_style2 mr-20" style="flex: 1">
						{{ oneyear1 }}
					</div>
					<div class="box_style3" style="flex: 1">
						{{ oneyear11 }}
					</div>
				</div>
				<div class="flex_start item_bg px-20 py-20" style="border-radius: 0px">
					<div class="mr-20">近三年</div>
					<div class="box_style1 mr-20" style="flex: 1">
						{{ twoyear }}
					</div>
					<div class="box_style2 mr-20" style="flex: 1">
						{{ twoyear2 }}
					</div>
					<div class="box_style3" style="flex: 1">
						{{ twoyear22 }}
					</div>
				</div>
				<div class="flex_start item_bg px-20 py-20" style="border-bottom: 1px solid #d9d9d9; border-radius: 0px 0px 4px 4px">
					<div class="mr-20">全时段</div>
					<div class="box_style1 mr-20" style="flex: 1">
						{{ threeyear }}
					</div>
					<div class="box_style2 mr-20" style="flex: 1">
						{{ threeyear3 }}
					</div>
					<div class="box_style3" style="flex: 1">
						{{ threeyear33 }}
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
// 表现风格
import { getPerformanceStyle } from '@/api/pages/Analysis.js';
// 表现风格
export default {
	name: 'performanceStyle',
	data() {
		return {
			oneyear: '',
			oneyear1: '',
			oneyear11: '',
			twoyear: '',
			twoyear2: '',
			twoyear22: '',
			threeyear: '',
			threeyear3: '',
			threeyear33: '',
			loading: true,
			info: {}
		};
	},
	methods: {
		openvideo() {
			window.open('https://www.bilibili.com/video/BV1bY4y177CV?share_source=copy_web');
		},
		// 获取表现风格数据
		async getPerformanceStyle() {
			let data = await getPerformanceStyle({
				code: this.info.code,
				type: this.info.type,
				flag: this.info.flag,
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			if (data?.mtycode == 200) {
				return data?.data;
			} else {
				return {};
			}
		},
		async getData(info) {
			this.info = info;
			let data = await this.getPerformanceStyle();
			this.loading = false;
			this.oneyear = data?.bigsmall_now || '暂无';
			this.oneyear1 = data?.valuegrowth_now || '暂无';
			this.oneyear11 = data?.industry_now || '暂无';
			this.twoyear = data?.bigsmall_three || '暂无';
			this.twoyear2 = data?.valuegrowth_three || '暂无';
			this.twoyear22 = data?.industry_three || '暂无';
			this.threeyear = data?.bigsmall || '暂无';
			this.threeyear3 = data?.valuegrowth || '暂无';
			this.threeyear33 = data?.industry || '暂无';
		},
		exportImage() {
			this.html2canvas(document.getElementById('performanceStyle'), {
				scale: 3
			}).then(function (canvas) {
				let base64Str = canvas.toDataURL('image/png');
				let aLink = document.createElement('a');
				aLink.style.display = 'none';
				aLink.href = base64Str;
				aLink.download = '表现风格.jpg';
				// 触发点击-然后移除
				document.body.appendChild(aLink);
				aLink.click();
				document.body.removeChild(aLink);
			});
		},
		async createPrintWord(info) {
			await this.getData(info);
			return await new Promise((resolve, reject) => {
				this.$nextTick(async () => {
					let list = [
						{
							label: '时间',
							value: 'date'
						},
						{
							label: '大小盘',
							value: 'big'
						},
						{
							label: '成长/价值',
							value: 'value'
						},
						{
							label: '大行业',
							value: 'industry'
						}
					];
					let data = [
						{
							date: '近一年',
							big: this.oneyear,
							value: this.oneyear1,
							industry: this.oneyear11
						},
						{
							date: '近三年',
							big: this.twoyear,
							value: this.twoyear2,
							industry: this.twoyear22
						},
						{
							date: '全时段',
							big: this.threeyear,
							value: this.threeyear3,
							industry: this.threeyear33
						}
					];
					resolve([...this.$exportWord.exportTitle('表现风格'), ...this.$exportWord.exportTable(list, data)]);
				});
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.item_bg {
	max-width: 790px;
	min-width: 570px;
	height: 100px;
	border-radius: 4px 4px 0px 0px;
	border-top: 1px solid #d9d9d9;
	border-right: 1px solid #d9d9d9;
	border-left: 1px solid #d9d9d9;
	background: linear-gradient(180deg, #ecf5ff 0%, rgba(255, 145, 3, 0) 100%);
	> div {
		font-family: PingFang SC;
		font-size: 18px;
		font-style: normal;
		font-weight: 500;
		line-height: 26px; /* 144.444% */
		text-align: center;
	}
	.box_style1 {
		// padding: 11px 54px;
		height: 50px;
		line-height: 50px;
		border-radius: 4px;
		border: 1px solid #18c2a0;
		color: #18c2a0;
		color: #18c2a0;
		background: rgba(24, 194, 160, 0.15);
	}
	.box_style2 {
		// padding: 11px 54px;
		height: 50px;
		line-height: 50px;
		height: 50px;
		border-radius: 4px;
		border: 1px solid #e85d2d;
		color: #e85d2d;
		background: rgba(232, 93, 45, 0.15);
	}
	.box_style3 {
		// padding: 11px 54px;
		height: 50px;
		line-height: 50px;
		height: 50px;
		border-radius: 4px;
		border: 1px solid #4576e9;
		color: #4576e9;
		background: rgba(69, 118, 233, 0.15);
	}
}
</style>
