<template>
  <div class="wrapper"
       style="font-family: '<PERSON>Fang'">
    <v-head></v-head>
    <div style="width: 100%; display: flex; flex-wrap: wrap">
      <div class="content-box"
           style="height: calc(100vh - 54px); position: relative; width: 100%">
        <v-tags ref="tag"
                id="tag"></v-tags>
        <div class="flex_start mt-16"
             style="font-size: 14px; height: 22px; line-height: 22px; max-width: 1720px; margin: 0 auto">
          <div class="flex_start pl-12 pr-12 mr-16"
               @click="goBack"
               style="cursor: pointer; border-right: 1px solid #d9d9d9">
            <div>
              <svg width="14"
                   height="12"
                   viewBox="0 0 14 12"
                   fill="none"
                   xmlns="http://www.w3.org/2000/svg">
                <path d="M9.60131 1.21926C9.75897 1.21926 9.86408 1.27181 9.96918 1.37691C10.1794 1.58712 10.1794 1.90245 9.96918 2.11265L5.08179 7.00004L9.96918 11.8349C10.1794 12.0451 10.1794 12.3604 9.96918 12.5706C9.75897 12.7808 9.44365 12.7808 9.23345 12.5706L3.97819 7.31536C3.76798 7.10515 3.76798 6.78983 3.97819 6.57962L9.23346 1.32437C9.28601 1.27181 9.44367 1.21926 9.60132 1.21926L9.60131 1.21926Z"
                      fill="black"
                      fill-opacity="0.45" />
              </svg>
            </div>
            <div>返回</div>
          </div>
          <div>{{ title }}</div>
        </div>
        <div class="content"
             style="position: relative; min-height: calc(100% - 54px)">
          <div >
            <transition name="move"
                        mode="out-in">
              <!-- <template v-if="/fundCompany/.test($route.path)">
								<router-view :key="$route.query.id"></router-view>
              </template>-->
              <template>
                <router-view :key="$route.query.id"></router-view>
              </template>
            </transition>
            <el-backtop target=".content"></el-backtop>
          </div>
          
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import vHead from './Header.vue';
import vTags from './Tags.vue';
export default {
  data () {
    return {
      tagsList: [],
      mainHeight: null,
      title: ''
    };
  },
  components: {
    vHead,
    vTags
  },
  // computed: {
  // 	mainHeight() {
  // 		this.$nextTick(() => {
  // 			let tagHeight = this.$refs.tag.$el.clientHeight;
  // 			return `height: calc(100vh-${tagHeight || '96px'})`;
  // 		});
  // 	}
  // },
  mounted () {
    let tagHeight = this.$refs.tag.$el.clientHeight;
    this.mainHeight = `height: calc(100vh-${tagHeight + 56 || 96}px)`;
    //创建一个监听实例
    // var eleResize = new ElementResize('#tag');
    // eleResize.listen(function (e) {
    // 	console.log('监听', e);
    // });
  },
  methods: {
    goBack () {
      this.$router.go(-1);
    }
  },
  created () {
    this.title = this.$route.meta.title;
    this.$event.$on('collapse-content', (msg) => {
      this.collapse = msg;
    });

    // 只有在标签页列表里的页面才使用keep-alive，即关闭标签之后就不保存到内存中了。
    this.$event.$on('tags', (msg) => {
      let arr = [];
      for (let i = 0, len = msg.length; i < len; i++) {
        msg[i].name && arr.push(msg[i].name);
      }
      this.tagsList = arr;
    });
  }
};
</script>
<style>
.pageimg {
	margin-left: 10px;
}
.bottombanben {
	position: absolute;
	/* position: fixed; */
	z-index: 1000;
	bottom: 0;
	padding-top: 24px;
	padding-bottom: 24px;
	width: 100%;
	background: #f9f5f9;
	/* margin-top: 24px; */
	border-top: 1px solid #f5f5f5;
	text-align: center;
}
</style>
