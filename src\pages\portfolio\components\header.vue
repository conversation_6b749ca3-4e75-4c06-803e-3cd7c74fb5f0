<!--  -->
<template>
  <div v-loading="loading" class="comDetailPage">
    <assetDetails
      @updatePort="updatePort"
      :show="showDetails"
      ref="assetDetails"
      :comId="showDetailsID"
    ></assetDetails>
    <div class="comMainTitle">
      <span
        @click="goback"
        style="color: rgba(0, 0, 0, 0.46); font-size: 14px; cursor: pointer"
      >组合管理&nbsp;/&nbsp;</span>
      <span>组合详情</span>
    </div>
    <div class="comBox">
      <div class="comBoxContent" style="padding-top: 24px; padding-bottom: 24px; margin-top: 0px">
        <div
          style="
            font-family: 'PingFang';
            font-style: normal;
            font-weight: 500;
            font-size: 16px;
            line-height: 24px;
            color: rgba(0, 0, 0, 0.85);
          "
        >
          {{ info.name }}
          (调仓日期:{{ time }})
          <span>
            <i
              style="color: #4096ff; margin-left: 16px; cursor: pointer"
              class="el-icon-s-tools"
              @click="changeFund"
            ></i>
          </span>
        </div>
        <div style="display: flex; align-items: center">
          <!-- <div>调整日期：</div>
					<div style="margin-right: 24px">
						<el-select @change="getCombinationHolding" v-model="time" style="width: 144px" placeholder="请选择">
							<el-option v-for="item in options" :key="item" :label="item" :value="item"> </el-option>
						</el-select>
          </div>-->
          <v-chart
            ref="equityStockPositionAnalysisindustry"
            element-loading-text="暂无数据"
            element-loading-spinner="el-icon-document-delete"
            element-loading-background="rgba(239, 239, 239, 0.5)"
            class="charts_analysis_class"
            style="width: 100%; height: 100px"
            autoresize
            :options="option"
            @click.native="getChangeTime"
          ></v-chart>
        </div>

        <div
          style="
            display: flex;
            margin-top: 20px;
            font-size: 16px;
            font-family: 'PingFang SC';
            font-style: normal;
            font-weight: 400;
            font-size: 14px;
            line-height: 22px;
            margin-bottom: 16px;
            color: rgba(0, 0, 0, 0.65);
          "
        >
          <div style="width: 80px">组合说明：</div>
          <div>{{ description }}</div>
        </div>
        <el-table
          :data="dataList"
          style="width: 100%"
          :height="
            theadList.length >= 8
              ? '486px'
              : theadList.length <= 4
              ? '243px'
              : theadList.length * 54 + 'px'
          "
        >
          <el-table-column
            v-for="(item, index) in theadList"
            :key="index"
            :prop="item.value"
            :label="item.label"
            :sortable="item.sort"
            :show-overflow-tooltip="true"
            :formatter="item.format"
            align="gotoleft"
            :width="item.width ? item.width + 'px' : ''"
          >
            <template slot="header" slot-scope="{ column }">
              {{ column.label }}
              <span v-show="item.content && item.content !== ''">
                <el-tooltip effect="dark" :content="item.content" placement="top">
                  <svg
                    width="14"
                    height="14"
                    viewBox="0 0 13 13"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M7.0002 0.699951C10.4793 0.699951 13.3002 3.52089 13.3002 6.99995C13.3002 10.479 10.4793 13.3 7.0002 13.3C3.52113 13.3 0.700195 10.479 0.700195 6.99995C0.700195 3.52089 3.52113 0.699951 7.0002 0.699951ZM7.0002 1.7687C4.11176 1.7687 1.76895 4.11151 1.76895 6.99995C1.76895 9.88839 4.11176 12.2312 7.0002 12.2312C9.88863 12.2312 12.2314 9.88839 12.2314 6.99995C12.2314 4.11151 9.88863 1.7687 7.0002 1.7687ZM7.0002 9.5312C7.31086 9.5312 7.5627 9.78304 7.5627 10.0937C7.5627 10.4044 7.31086 10.6562 7.0002 10.6562C6.68954 10.6562 6.4377 10.4044 6.4377 10.0937C6.4377 9.78304 6.68954 9.5312 7.0002 9.5312ZM7.0002 3.6812C7.59082 3.6812 8.1477 3.8837 8.56957 4.25355C9.00832 4.63745 9.2502 5.15354 9.2488 5.7062C9.2488 6.51901 8.71301 7.25026 7.88332 7.56948C7.62316 7.66933 7.44879 7.92245 7.44879 8.19948V8.5187C7.44879 8.58058 7.39816 8.6312 7.33629 8.6312H6.66129C6.59941 8.6312 6.54879 8.58058 6.54879 8.5187V8.21636C6.54879 7.89151 6.64441 7.57089 6.82863 7.3037C7.01004 7.04214 7.26316 6.84245 7.56129 6.72854C8.04082 6.54433 8.3502 6.14354 8.3502 5.7062C8.3502 5.08604 7.7441 4.5812 7.0002 4.5812C6.25629 4.5812 5.6502 5.08604 5.6502 5.7062V5.81308C5.6502 5.87495 5.59957 5.92558 5.5377 5.92558H4.8627C4.80082 5.92558 4.7502 5.87495 4.7502 5.81308V5.7062C4.7502 5.15354 4.99207 4.63745 5.43082 4.25355C5.8527 3.88511 6.40957 3.6812 7.0002 3.6812Z"
                      fill="black"
                      fill-opacity="0.85"
                    />
                  </svg>
                </el-tooltip>
              </span>
            </template>
            <template slot-scope="{ row }">
              <span v-show="item.value !== 'name'">
                {{
                item.format(row[item.value])
                }}
              </span>
              <el-link v-show="item.value == 'name'" @click="goDetail(row)">
                {{
                row[item.value]
                }}
              </el-link>
            </template>
          </el-table-column>
        </el-table>
        <div style="background: #fafafa; height: 40px">
          <div
            style="
              padding: 9px 24px;
              font-family: 'PingFang';
              font-style: normal;
              font-weight: 500;
              font-size: 14px;
              line-height: 22px;
              color: rgba(0, 0, 0, 0.85);
            "
          >剩余现金: {{ fix2m(money) }}</div>
        </div>
      </div>
    </div>
    <el-dialog :title="current" width="30%" :rows="4" :visible.sync="dialogVisible">
      <div>
        <el-input type="textarea" v-model="changeDescription" placeholder="请输入不调仓说明"></el-input>
      </div>
      <div slot="footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="writeDescription">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getCombinationHolding,
  getCombinationDescription,
  postCombinationDescription,
  putCombinationDescription,
  getComQuaList
} from "@/api/pages/SystemMixed.js";
import { alphaGo } from "@/assets/js/alpha_type.js";
import assetDetails from "./assetDetails";
import VCharts from "vue-echarts";
export default {
  components: { "v-chart": VCharts, assetDetails },
  data() {
    //这里存放数据
    return {
      showDetailsID: "",
      showDetails: false,
      id: "",
      tableData: [],
      tableDataAll: [],
      pageSIze: "10",
      money: 0,
      nameCom: "慧捕基组合1",
      createdName: "慧捕基",
      createdTime: "2022-01-01",
      description: "",
      loading: true,
      loadingTable: false,
      dialogVisible: false,
      changeDescription: "",
      theadList: [
        {
          label: "证券代码",
          value: "code",
          format: this.formatDefault,
          width: "100"
        },
        {
          label: "证券简称",
          format: this.formatDefault,
          value: "name",
          width: "200"
        },
        {
          label: "最新价",
          format: this.formatDefault,
          value: "nav",
          sort: true
        },
        {
          label: "当日涨跌",
          value: "fluctuation", // rate*nav
          sort: true,
          format: this.fix5
          // content: '----------'
        },
        {
          label: "当日涨跌幅",
          format: this.fix2p,
          value: "rate",
          sort: true
        },
        {
          label: "持仓数量(万)",
          format: this.fix4,
          value: "share",
          sort: true
        },
        {
          label: "持仓金额",
          value: "now_nav",
          format: this.fix2m,
          sort: true
        },
        {
          label: "买入金额",
          value: "totalmv",
          format: this.fix2m,
          sort: true
        },
        {
          label: "权重",
          format: this.fix2p,
          value: "weight",
          sort: true
          // content: '----------'
        },
        {
          label: "当日盈亏",
          format: this.fix2m,
          value: "profit_loss", // rate*totalmv
          sort: true
          // content: '----------'
        },
        {
          label: "累计盈亏",
          format: this.fix2m,
          value: "cumulative_profit_loss", // cum_return * totalmv
          sort: true
        },
        {
          label: "持有收益率",
          format: this.fix2p,
          value: "cum_return",
          sort: true
        },
        // {
        // 	label: '利息收入',
        // 	format: this.formatDefault,
        // 	value: 'interest_income',
        // 	sort: true
        // },
        // {
        // 	label: '持仓成本',
        // 	format: this.formatDefault,
        // 	value: 'holding_cost',
        // 	sort: true
        // },
        {
          label: "成本价格",
          format: this.formatDefault,
          value: "begin_nav",
          sort: true
        },
        {
          label: "末次买入时间",
          format: this.formatDefault,
          value: "max_date",
          sort: true
        },
        {
          label: "基金说明",
          format: this.formatDefault,
          value: "description",
          sort: true
        }
        // {
        // 	label: '操作',
        // 	value: 'setting'
        // }
      ],
      dataList: [],
      info: {},
      options: [],
      desList: [],
      time: "",
      option: {},
      current: ""
    };
  },
  //方法集合
  methods: {
    updatePort() {
      this.$emit("updatePortOut");
    },
    changeFund() {
      this.showDetails = true;
      this.$refs.assetDetails.showdialog(this.info.code, this.time);
      this.showDetailsID = this.info.code;
    },
    goback() {
      this.$router.push("/portfolioSelf");
    },
    getData(info) {
      this.info = info;
      this.getCombinationDescription();
      this.getHoldList();
    },
    // 获取不调仓说明列表
    async getCombinationDescription(type) {
      let data = await getCombinationDescription({
        combination_id: this.info.code
      });
      if (data?.mtycode == 200) {
        this.desList = data?.data;
        if (type) {
          this.filterChartData();
        }
      }
    },
    // 添加不调仓说明
    async postCombinationDescription() {
      let data = await postCombinationDescription({
        combination_id: this.info.code,
        description: this.changeDescription,
        date: this.current
      });
      if (data?.mtycode == 200) {
        this.$message.success("添加成功");
      }
      this.dialogVisible = false;
      this.getCombinationDescription(1);
      this.changeDescription = "";
    },
    // 修改不调仓说明
    async putCombinationDescription() {
      let data = await putCombinationDescription({
        combination_id: this.info.code,
        description: this.changeDescription,
        date: this.current
      });
      if (data?.mtycode == 200) {
        this.$message.success("修改成功");
      }
      this.dialogVisible = false;
      this.getCombinationDescription(1);
      this.changeDescription = "";
    },
    handleSizeChange(val) {
      this.pageSIze = val;
      this.currentPage = 1;
      this.handleCurrentChange(1);
    },
    handleCurrentChange(val) {
      this.tableData = this.tableDataAll.slice(
        (val - 1) * this.pageSIze,
        val * this.pageSIze
      );
    },
    // 添加不调仓说明
    writeDescription() {
      if (this.changeDescription) {
        let index = this.desList.findIndex(item => {
          return item.date == this.current;
        });
        if (index == -1) {
          this.postCombinationDescription();
        } else {
          this.putCombinationDescription();
        }
      }
    },
    // 查看基金详情
    goDetail(val) {
      alphaGo(val.code, val.name, this.$route.path);
    },
    fix5(val) {
      return Number(val) ? val.toFixed(5) : "--";
    },
    fix2m(val) {
      if (val * 1) {
        // let val = value * 1 >= 0 ? value * 1 : value * -1;
        return val >= 10 ** 4 || val <= -1 * 10 ** 4
          ? (val / 10 ** 4).toFixed(2) + "万元"
          : val >= 10 ** 8 || val <= -1 * 10 ** 8
          ? (val / 10 ** 8).toFixed(2) + "亿元"
          : (val * 1).toFixed(2) + "元";
      } else {
        return "--";
      }
      // return (Number(val) ? val.toFixed(2) : '--') + '元';
    },
    fix2p(val) {
      return val * 100 ? (val * 100).toFixed(2) + "%" : "--";
    },
    // 万
    fix4(val) {
      return Number(val) ? (val / 10000).toFixed(2) : "--";
    },
    // 默认
    formatDefault(val) {
      return val;
    },
    getChangeTime(val) {
      if (this.current) {
        let index = this.options.indexOf(this.current);
        if (index == -1) {
          let i = this.desList.findIndex(item => {
            return item.date == this.current;
          });
          if (i != -1) {
            this.changeDescription = this.desList[i].description;
          }
          this.dialogVisible = true;
        } else {
          this.time = this.current;
          this.getCombinationHolding();
          this.filterChartData();
        }
      }
    },
    async getCombinationHolding() {
      this.loading = true;
      let data = await getCombinationHolding({
        combination_id: this.info.code,
        date: this.time
      });
      if (data?.mtycode == 200) {
        this.description = data?.description;
        this.dataList = data?.data?.map(item => {
          return {
            ...item,
            fluctuation: Number(item.rate * item.nav)
              ? item.rate * item.nav
              : "--", // 涨跌
            profit_loss: item.rate * item.totalmv, // 当日盈亏
            cumulative_profit_loss: item.cum_return * item.totalmv, // 累计盈亏
            now_nav: item.cum_return * item.totalmv + item.totalmv // 持仓金额
          };
        });
        this.$emit("getFundList", data?.data);
        this.money = data?.surplus_asset;
        this.description = data?.description;
      }
      this.loading = false;
    },
    filterChartData() {
      let xAxis = this.FUNC.generateDateList(
        this.moment(this.options?.[0]).format("YYYY-MM-DD"),
        this.moment().format("YYYY-MM-DD")
      );
      let series = [];
      xAxis.map((item, index) => {
        if (this.options.indexOf(item) == -1) {
          if (
            this.desList.findIndex(obj => {
              return obj.date == item;
            }) == -1
          ) {
            series.push({
              data: [
                {
                  value: [item, 0],
                  symbolSize: "6",
                  itemStyle: {
                    color: "#ffffff"
                  },
                  emphasis: {
                    itemStyle: {
                      color: "#4096ff"
                    }
                  }
                }
              ],
              type: "line"
            });
          } else {
            series.push({
              data: [
                {
                  value: [item, 0],
                  symbol: "none"
                  // symbolSize: '10',
                  // itemStyle: {
                  // 	color: '#4096ff'
                  // },
                  // emphasis: {
                  // 	itemStyle: {
                  // 		color: '#4096ff'
                  // 	}
                  // }
                }
              ],
              type: "line",
              markPoint: {
                data: [
                  {
                    type: "max",
                    name: "Max",
                    // symbol: 'path://M6.98831 11L0.926135 0.499999L13.0505 0.5L6.98831 11Z',
                    symbolSize: 10,
                    symbolOffset: [0, 0],
                    itemStyle: {
                      color: "#4096ff"
                    },
                    label: {
                      show: true,
                      formatter: item,
                      color: "transparent"
                    }
                  }
                ]
              }
            });
          }
        } else {
          series.push({
            data: [{ value: [item, 0], symbol: "none" }],
            type: "line",
            markPoint: {
              emphasis: {
                disabled: false,
                label: {
                  show: true,
                  position: "top",
                  formatter: `调仓日期:${item}`,
                  backgroundColor: "rgba(0, 0, 0, 0.45)",
                  borderRadius: 4,
                  padding: [0, 9],
                  width: 164,
                  height: 26,
                  color: "white",
                  lineHeight: 32,
                  align: "center",
                  fontFamily: "PingFang",
                  fontStyle: "normal",
                  fontWeight: 400,
                  fontSize: "12px"
                }
              },
              data: [
                {
                  type: "max",
                  name: "Max",
                  symbol:
                    "path://M6.98831 11L0.926135 0.499999L13.0505 0.5L6.98831 11Z",
                  symbolSize: 14,
                  symbolOffset: [0, "-50%"],
                  itemStyle: {
                    color: item == this.time ? "red" : "#4096ff"
                  },
                  label: {
                    show: true,
                    formatter: item,
                    color: "transparent"
                  }
                }
              ]
            }
          });
        }
      });
      this.option = {
        xAxis: {
          type: "category",
          data: xAxis,
          axisTick: { show: false },
          axisLine: { symbol: ["none", "arrow"], symbolSize: [7, 7] },
          axisLabel: { fontSize: 12 }
        },
        tooltip: {
          trigger: "axis",
          formatter: val => {
            this.current = val?.[0].name;
            if (this.options.indexOf(val?.[0].name) == -1) {
              let index = this.desList.findIndex(obj => {
                return obj.date == val?.[0].name;
              });
              if (index == -1) {
                return `${val?.[0].name}<br />点击设置不调仓说明`;
                // return `<div style="width:200px">${val?.[0].name}<br />点击设置不调仓说明</div>`;
              } else {
                // return `<div style="width:200px;height:100px">${val?.[0].name}:<br />${this.desList[index]?.description}`;
                return `${val?.[0].name}:<br />${this.desList[index]?.description}`;
              }
            }
          }
        },
        grid: {
          left: "8px",
          right: "8px",
          bottom: "0",
          containLabel: true
        },
        yAxis: {
          type: "value",
          show: false,
          min: 0
        },
        series
      };
    },
    async getHoldList() {
      let data = await getComQuaList({ combination_id: this.info.code });
      if (data?.mtycode == 200) {
        this.options = data?.data;
        this.time = data?.data[data?.data.length - 1];
      }
      this.getCombinationHolding();
      this.filterChartData();
    },
    createPrintWord() {
      let list = [
        {
          label: "证券代码",
          value: "code"
        },
        {
          label: "证券简称",
          value: "name"
        },
        {
          label: "最新价",
          value: "nav"
        },
        {
          label: "涨跌",
          value: "fluctuation", // rate*nav
          format: "fix4"
        },
        {
          label: "涨跌幅",
          format: "fix2p",
          value: "rate"
        },
        {
          label: "持仓数量",
          value: "share",
          format: "fix2m"
        },
        {
          label: "持仓金额",
          value: "now_nav",
          format: "fix2m"
        },
        {
          label: "买入金额",
          value: "totalmv",
          format: "fix2m"
        },
        {
          label: "权重",
          format: "fix2p",
          value: "weight"
        },
        {
          label: "当日盈亏",
          format: "fix2m",
          value: "profit_loss" // rate*totalmv
        },
        {
          label: "累计盈亏",
          format: "fix2m",
          value: "cumulative_profit_loss" // cum_return * totalmv
        },
        {
          label: "持有收益率",
          format: "fix2p",
          value: "cum_return"
        },

        {
          label: "成本价格",
          value: "begin_nav"
        },
        {
          label: "末次买入时间",
          value: "max_date"
        },
        {
          label: "基金说明",
          value: "description"
        }
      ];
      return [
        ...this.$exportWord.exportPortfolioInfo(this.info, this.time),
        ...this.$exportWord.exportDescripe(`调仓说明:${this.description}`),
        ...this.$exportWord.exportTable(list, this.dataList, "", true)
      ];
    }
  }
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
.comDetailPage {
  // padding-bottom: 24px;
  background: #f7f9fa;
}
</style>
