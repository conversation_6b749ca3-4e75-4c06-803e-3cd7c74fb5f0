<template>
	<div id="holdStockConcentration">
		<analysis-card-title title="持股集中度" image_id="holdStockConcentration">
			<el-select v-model="active" placeholder="集中度">
				<el-option v-for="item in list" :key="item.name" :label="item.name" :value="item.name"> </el-option>
			</el-select>
		</analysis-card-title>
		<div
			v-for="(item, index) in list"
			v-show="item.name == active"
			:key="item.name"
			:id="'tradingStyle' + index"
			class="echarts_main mb-20"
			v-loading="loading"
		>
			<div class="flex_between title">
				<div>{{ item.name }}</div>
				<div @click="downloadImage('tradingStyle' + index, name)">
					<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path
							d="M7.88736 10.6575C7.90072 10.6746 7.9178 10.6884 7.93729 10.6979C7.95678 10.7074 7.97818 10.7123 7.99986 10.7123C8.02154 10.7123 8.04294 10.7074 8.06243 10.6979C8.08192 10.6884 8.099 10.6746 8.11236 10.6575L10.1124 8.12713C10.1856 8.03427 10.1195 7.89677 9.99986 7.89677H8.67665V1.85392C8.67665 1.77535 8.61236 1.71106 8.53379 1.71106H7.46236C7.38379 1.71106 7.3195 1.77535 7.3195 1.85392V7.89499H5.99986C5.88022 7.89499 5.81415 8.03249 5.88736 8.12535L7.88736 10.6575ZM14.5356 10.0325H13.4641C13.3856 10.0325 13.3213 10.0968 13.3213 10.1753V12.9253H2.67843V10.1753C2.67843 10.0968 2.61415 10.0325 2.53557 10.0325H1.46415C1.38557 10.0325 1.32129 10.0968 1.32129 10.1753V13.7111C1.32129 14.0271 1.57665 14.2825 1.89272 14.2825H14.107C14.4231 14.2825 14.6784 14.0271 14.6784 13.7111V10.1753C14.6784 10.0968 14.6141 10.0325 14.5356 10.0325Z"
							fill="black"
							fill-opacity="0.45"
						/>
					</svg>
				</div>
			</div>
			<div class="charts_fill_class">
				<v-chart
					:ref="'holdStockConcentration' + index"
					v-loading="loading"
					element-loading-text="暂无数据"
					element-loading-spinner="el-icon-document-delete"
					element-loading-background="rgba(239, 239, 239, 0.5)"
					style="width: 100%; height: 400px"
					autoresize
					:options="item.option"
				/>
			</div>
		</div>
	</div>
</template>

<script>
import { getConcentrationInfo } from '@/api/pages/Analysis.js';
import { barChartOption } from '@/utils/chartStyle.js';
export default {
	data() {
		return {
			active: '报告期前十大集中度',
			loading: true,
			list: [],
			info: {}
		};
	},
	methods: {
		async getData(info) {
			this.info = info;
			await this.getConcentrationInfo();
		},
		// 获取交易风格数据
		async getConcentrationInfo() {
			this.loading = true;
			let postData = {
				code: this.info.code,
				flag: this.info.flag,
				type: this.info.type,
				start_date: this.info.start_date,
				end_date: this.info.end_date
			};
			// 集中度
			let data = await getConcentrationInfo({ ...postData, mtyhints: '' });

			this.loading = false;
			if (data?.mtycode == 200) {
				this.getConcentration(data?.data);
			}
		},
		getConcentration(data) {
			let sort_data = data.sort((a, b) => {
				return this.moment(this.moment(a.yearqtr, 'YYYY QQ').format()).isBefore(this.moment(b.yearqtr, 'YYYY QQ').format()) ? -1 : 1;
			});
			let date_list = sort_data.map((v) => v.yearqtr);
			this.list = [
				{
					name: '报告期前十大集中度',
					option: this.formatChartData({
						date_list,
						sort_data: sort_data.map((v) => {
							return {
								...v,
								data: v.top10Concentration,
								rank: v.top10ConcentrationRank
							};
						}),
						name: '前十大'
					})
				},
				{
					name: '报告期个股前五大占比',
					option: this.formatChartData({
						date_list,
						sort_data: sort_data.map((v) => {
							return {
								...v,
								data: v.top5Concentration,
								rank: v.top5ConcentrationRank
							};
						}),
						name: '前五大'
					})
				},
				{
					name: '报告期个股前三大占比',
					option: this.formatChartData({
						date_list,
						sort_data: sort_data.map((v) => {
							return {
								...v,
								data: v.top3Concentration,
								rank: v.top3ConcentrationRank
							};
						}),
						name: '前三大'
					})
				}
			];
		},
		// 处理数据，返回图option数据
		formatChartData(data) {
			return barChartOption({
				toolbox: 'none',
				color: ['#4096ff', '#4096ff', '#E8684A'],
				grid: {
					top: '8px',
					left: '48px',
					right: '48px',
					bottom: '36px'
				},
				tooltip: {
					trigger: 'axis',
					formatter: function (obj) {
						var value = obj[0].axisValue + `年<br />`;
						for (let i = 0; i < obj.length; i++) {
							if (obj[i].seriesName == '全市场排名分位') {
								value +=
									`<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:` +
									obj[i].color +
									`;"></span>` +
									obj[i].seriesName +
									':' +
									Number(obj[i].data * 100).toFixed(2) +
									'%' +
									`<br />`;
							} else {
								value +=
									`<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:` +
									obj[i].borderColor +
									`;"></span>` +
									obj[i].seriesName +
									':' +
									Number(obj[i].data).toFixed(2) +
									'%' +
									`<br />`;
							}
						}
						return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
					}
				},
				legend: {
					bottom: '0',
					data: [data.name + '集中度', , { name: '全市场排名分位', icon: 'line' }]
				},
				xAxis: [{ type: 'category', data: data.date_list }],
				yAxis: [
					{
						name: data.name + '集中度',
						type: 'value',
						nameLocation: 'middle', // 设置名称居中
						nameGap: 48, // 控制名称距离轴线的距离
						nameTextStyle: {
							align: 'center'
						}
					},
					{
						name: '全市场排名分位',
						type: 'value',
						nameLocation: 'middle', // 设置名称居中
						nameGap: 48, // 控制名称距离轴线的距离
						nameRotate: 270,
						nameTextStyle: {
							align: 'center'
						},
						formatter: function (val) {
							return val * 100 + '%';
						}
					}
				],
				series: [
					{
						name: data.name + '集中度',
						type: 'bar',
						data: data.sort_data.map((v) => v.data),
						barGap: 0,
						itemStyle: {
							borderColor: '#4096ff',
							color: new echarts.graphic.LinearGradient(
								0,
								0,
								0,
								1, // 渐变方向，这里表示从上到下
								[
									{ offset: 0, color: '#4096ff' }, // 渐变起始颜色
									{ offset: 1, color: '#85AEFF' } // 渐变结束颜色
								]
							)
						}
					},
					// 苟煜说不要了
					// {
					// 	name: data.name + '集中度中位数',
					// 	type: 'bar',
					// 	data: tempdata2,
					// 	barGap: 0,
					// 	itemStyle: {
					// 		borderColor: '#FFAB3E',
					// 		color: new echarts.graphic.LinearGradient(
					// 			0,
					// 			0,
					// 			0,
					// 			1, // 渐变方向，这里表示从上到下
					// 			[
					// 				{ offset: 0, color: '#FFAB3E' }, // 渐变起始颜色
					// 				{ offset: 1, color: '#FFC67D' } // 渐变结束颜色
					// 			]
					// 		)
					// 	}
					// },
					{
						name: '全市场排名分位',
						type: 'line',
						symbol: 'none',
						yAxisIndex: 1,
						data: data.sort_data.map((v) => v.rank),
						itemStyle: {
							color: '#E8684A'
						},
						lineStyle: {
							color: '#E8684A'
						}
					}
				]
			});
		},
		// 导出单个图
		downloadImage(id, name) {
			this.exportImage(id, name + '.jpg');
		},
		async createPrintWord(info) {
			await this.getData(info);
			let list = [];
			return await new Promise((resolve, reject) => {
				this.$nextTick(async () => {
					this.list.map((item, index) => {
						let height = this.$refs['holdStockConcentration' + index]?.[0].$el.clientHeight;
						let width = this.$refs['holdStockConcentration' + index]?.[0].$el.clientWidth;
						let chart = this.$refs['holdStockConcentration' + index]?.[0].getDataURL({
							type: 'jpg',
							pixelRatio: 3,
							backgroundColor: '#fff'
						});
						list.push(...this.$exportWord.exportTitle(item.name), ...this.$exportWord.exportChart(chart, { width, height }));
					});
					resolve(list);
				});
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.echarts_main {
	padding: 0px 20px 20px 20px;
	border-radius: 4px;
	border: 1px solid #d9d9d9;
	background: #fff;
	.title {
		border-bottom: 1px solid #e9e9e9;
		margin-bottom: 20px;
		padding: 14px 0;
	}
}
</style>
