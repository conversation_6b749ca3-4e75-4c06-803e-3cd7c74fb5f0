<template>
  <div>
    <div class="header">
      <div style="width: 100%; display: flex; align-items: center; justify-content: space-between">
        <div class="logo-nav-container" style="display: flex; align-items: center; flex: 1; overflow: hidden;">
          <div class="logo" style="display: flex; align-items: center; min-width: 140px;">
            <div>
              <div v-if="showGDB">
                <!-- <div class="imsg"><img class="imgss" src="../../../version/projectGDBank/asset/img/1.png" /></div> -->
              </div>
              <div v-else>
                <div v-if="!showjslogo" class="imsg">
                  <img class="imgss" src="../../assets/img/aaa.jpg" />
                </div>
                <div v-if="showjslogo" style="display: flex">
                  <div class="imsg">
                   
                  </div>
                  <div style="display: none" class="imsg2">
                    <div>
                      
                    </div>
                    <div>
                      <div>
                     
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div v-if="!showGDB && !showjslogo"
                class="platform-title"
                style="
                  line-height: 56px;
                  font-family: 'Roboto';
                  font-style: normal;
                  font-weight: 700;
                  font-size: 16px;
                  line-height: 32px;
                  color: #ffffff;
                ">
              慧捕基
            </div>
          </div>

          <!-- 顶部导航菜单 -->
          <div class="top-menu-container">
            <el-menu
              class="top-menu"
              mode="horizontal"
              :default-active="onRoutes"
              background-color="#4096ff"
              text-color="#ffffff"
              active-text-color="#40AFFF">
              <template v-for="item in items">
                <el-submenu v-if="item.subs && item.subs.length" :index="item.index" :key="item.index">
                  <template slot="title">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path v-for="(d, index) in item.icon" :key="index" :d="d" fill-rule="evenodd" clip-rule="evenodd" fill="white" fill-opacity="0.85" />
                    </svg>
                    <span style="margin-left: 5px">{{ item.title }}</span>
                  </template>
                  <template v-for="subItem in item.subs">
                    <el-submenu v-if="subItem.subs" :index="subItem.index" :key="subItem.index">
                      <template slot="title">{{ subItem.title }}</template>
                      <el-menu-item v-for="(threeItem, i) in subItem.subs" :key="i" :index="threeItem.index" @click="navigateToSubItem(threeItem.index)">
                        {{ threeItem.title }}
                      </el-menu-item>
                    </el-submenu>
                    <el-menu-item v-else :index="subItem.index" :key="subItem.index + '1'" @click="navigateToSubItem(subItem.index)">
                      {{ subItem.title }}
                    </el-menu-item>
                  </template>
                </el-submenu>
                <el-menu-item v-else :index="item.index" :key="item.index" @click="navigateToSubItem(item.index)">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path v-for="(d, index) in item.icon" :key="index" :d="d" fill-rule="evenodd" clip-rule="evenodd" fill="white" fill-opacity="0.85" />
                  </svg>
                  <span style="margin-left: 5px">{{ item.title }}</span>
                </el-menu-item>
              </template>
            </el-menu>
          </div>
        </div>
        <div style="display: flex; align-items: center">
          <div class="searchfundormanager"
               v-if="searchShow">
            <!-- <el-select style="margin-right: 12px"
                       v-model="values"
                       :remote-method="searchpeople"
                       filterable
                       remote
                       prefix-icon="el-icon-search"
                       :loading="loading"
                       placeholder="输入简拼、代码、名称查询基金/经理"
                       ref="select"
                       @hook:mounted="cancalReadOnly"
                       @visible-change="cancalReadOnly"> -->
              <template slot="prefix">
                <div style="width: 24px; height: 100%; display: flex; justify-content: center; align-items: center; matgin-left: 13.1px">
                  <i class="el-icon-search"
                     style="color: #00000073"></i>
                </div>
              </template>
              <el-option-group v-for="groups in havefundmanager"
                               :key="groups.label"
                               :label="groups.label">
                <el-option v-for="(group, index) in groups.options"
                           :key="group.code + ' ' + index"
                           :label="
										group.flag == 'fund'
											? `${group.code}-${group.name}-${group.fundCo.split('基金')[0]}`
											: group.flag == 'manager'
											? `${group.name}-${group.fundCo.split('基金')[0]}`
											: group.flag == 'company'
											? group.name
											: `${group.name}-${group.code}`
									"
                           :value="group.code + '|' + group.name + '|' + group.flag"></el-option>
              </el-option-group>
            </el-select>
          </div>
          <div class="header-user-con">
            <!-- 搜索 -->
            <div class="btn-bell"
                 v-if="searchShow == false"
                 @click="searchShow = true">
              <el-tooltip effect="dark"
                          content="搜索"
                          placement="bottom">
                <svg width="16"
                     height="16"
                     viewBox="0 0 16 16"
                     fill="none"
                     xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M10.5521 3.95264C8.7297 2.13019 5.77493 2.13019 3.95249 3.95264C2.13004 5.77509 2.13004 8.72986 3.95249 10.5523C5.77493 12.3748 8.7297 12.3748 10.5521 10.5523C12.3746 8.72986 12.3746 5.77509 10.5521 3.95264ZM3.00968 3.00983C5.35282 0.666685 9.15181 0.666685 11.495 3.00983C13.6788 5.19364 13.8273 8.64202 11.9405 10.9978L14.3234 13.3807L13.3806 14.3235L10.9977 11.9406C8.64187 13.8274 5.19348 13.6789 3.00968 11.4951C0.666533 9.15197 0.666533 5.35298 3.00968 3.00983Z"
                        fill="white" />
                </svg>
              </el-tooltip>
            </div>
            <!-- 帮助中心 -->
            <!-- <div class="btn-bell"
                 @click="goHelpCenter">
              <el-tooltip effect="dark"
                          content="帮助中心"
                          placement="bottom">
                <svg width="16"
                     height="16"
                     viewBox="0 0 16 16"
                     fill="none"
                     xmlns="http://www.w3.org/2000/svg">
                  <g clip-path="url(#clip0_3353_12128)">
                    <path fill-rule="evenodd"
                          clip-rule="evenodd"
                          d="M8.00005 0.799988C11.9761 0.799988 15.2 4.02392 15.2 7.99999C15.2 11.9761 11.9761 15.2 8.00005 15.2C4.02398 15.2 0.800049 11.9761 0.800049 7.99999C0.800049 4.02392 4.02398 0.799988 8.00005 0.799988ZM8.00005 2.02142C4.69898 2.02142 2.02148 4.69892 2.02148 7.99999C2.02148 11.3011 4.69898 13.9786 8.00005 13.9786C11.3011 13.9786 13.9786 11.3011 13.9786 7.99999C13.9786 4.69892 11.3011 2.02142 8.00005 2.02142ZM8.00005 10.8928C8.35509 10.8928 8.64291 11.1807 8.64291 11.5357C8.64291 11.8907 8.35509 12.1786 8.00005 12.1786C7.64501 12.1786 7.35719 11.8907 7.35719 11.5357C7.35719 11.1807 7.64501 10.8928 8.00005 10.8928ZM8.00005 4.20713C8.67505 4.20713 9.31148 4.43856 9.79362 4.86124C10.295 5.29999 10.5715 5.88981 10.5699 6.52142C10.5699 7.45034 9.95755 8.28606 9.00933 8.65088C8.71201 8.76499 8.51273 9.05427 8.51273 9.37088V9.7357C8.51273 9.80642 8.45487 9.86427 8.38416 9.86427H7.61273C7.54201 9.86427 7.48416 9.80642 7.48416 9.7357V9.39017C7.48416 9.01892 7.59344 8.65249 7.80398 8.34713C8.0113 8.0482 8.30058 7.81999 8.6413 7.68981C9.18933 7.47927 9.54291 7.02124 9.54291 6.52142C9.54291 5.81267 8.85023 5.2357 8.00005 5.2357C7.14987 5.2357 6.45719 5.81267 6.45719 6.52142V6.64356C6.45719 6.71427 6.39933 6.77213 6.32862 6.77213H5.55719C5.48648 6.77213 5.42862 6.71427 5.42862 6.64356V6.52142C5.42862 5.88981 5.70505 5.29999 6.20648 4.86124C6.68862 4.44017 7.32505 4.20713 8.00005 4.20713Z"
                          fill="white" />
                  </g>
                  <defs>
                    <clipPath id="clip0_3353_12128">
                      <rect width="16"
                            height="16"
                            fill="white" />
                    </clipPath>
                  </defs>
                </svg>
              </el-tooltip>
            </div> -->
            <!-- 消息中心 -->
            <!-- <div class="btn-bell">
              <el-tooltip effect="light"
                          offset="100"
                          placement="bottom-end"
                          popper-class="slot_tooltip">
                <div slot="content"
                     class="aaa">
                  <div style="width: 336px; height: 406px; background: #ffffff; padding: 0">
                    <el-menu :default-active="activeIndex"
                             mode="horizontal"
                             style="display: flex; justify-content: center; align-items: center"
                             @select="menuSelect">
                      <el-menu-item index="unread"
                                    :style="`flex: 1; text-align: center;  ${activeIndex == 'unread' ? 'color:#4096ff' : ''}`">未读消息</el-menu-item>
                      <el-menu-item index="read"
                                    :style="`flex: 1; text-align: center;  ${activeIndex == 'read' ? 'color:#4096ff' : ''}`">已读消息</el-menu-item>
                    </el-menu>
                    <div style="height: 314px; overflow: scroll">
                      <div v-for="item in messageList"
                           :key="item._id"
                           @click="goDetail"
                           style="
													height: 53px;
													padding: 16px 24px;
													border-bottom: 1px solid #e9e9e9;
													cursor: pointer;
													font-family: 'PingFang';
													font-style: normal;
													font-weight: 500;
													font-size: 14px;
													line-height: 22px;
												">
                        <div :style="
														activeIndex == 'unread'
															? 'color: rgba(0, 0, 0, 0.85);overflow: hidden;white-space: nowrap;text-overflow: ellipsis;'
															: 'color: rgba(0, 0, 0, 0.45);overflow: hidden;white-space: nowrap;text-overflow: ellipsis;'
													">
                          {{ `[${item.type_name}]${item.message}` }}
                        </div>
                      </div>
                    </div>

                    <div style="display: flex; justify-content: center; align-items: center; position: absolute; bottom: 0">
                      <div v-show="activeIndex == 'unread'"
                           @click="readAll"
                           style="
													width: 168px;
													height: 46px;
													line-height: 46px;
													text-align: center;
													border: 1px solid #e9e9e9;
													border-left: none;
													border-bottom: none;
													cursor: pointer;
												">
                        清空未读
                      </div>
                      <div v-show="activeIndex == 'read'"
                           @click="deleteAll"
                           style="
													width: 168px;
													height: 46px;
													line-height: 46px;
													text-align: center;
													border: 1px solid #e9e9e9;
													border-left: none;
													border-bottom: none;
													cursor: pointer;
												">
                        清空已读
                      </div>
                      <div @click="goDetail"
                           style="
													width: 168px;
													height: 46px;
													line-height: 46px;
													text-align: center;
													border-top: 1px solid #e9e9e9;
													cursor: pointer;
												">
                        查看更多
                      </div>
                    </div>
                  </div>
                </div>
                <svg width="16"
                     height="16"
                     viewBox="0 0 16 16"
                     fill="none"
                     xmlns="http://www.w3.org/2000/svg">
                  <g clip-path="url(#clip0_3353_12133)">
                    <path d="M12.653 11.9165H12.2856V6.71241C12.2856 4.55271 10.6892 2.76802 8.61216 2.47108V1.87567C8.61216 1.53741 8.33818 1.26343 7.99991 1.26343C7.66165 1.26343 7.38767 1.53741 7.38767 1.87567V2.47108C5.31063 2.76802 3.7142 4.55271 3.7142 6.71241V11.9165H3.34685C3.07593 11.9165 2.85706 12.1354 2.85706 12.4063V12.8961C2.85706 12.9634 2.91216 13.0185 2.9795 13.0185H6.28563C6.28563 13.9644 7.05399 14.7328 7.99991 14.7328C8.94583 14.7328 9.7142 13.9644 9.7142 13.0185H13.0203C13.0877 13.0185 13.1428 12.9634 13.1428 12.8961V12.4063C13.1428 12.1354 12.9239 11.9165 12.653 11.9165ZM7.99991 13.7532C7.5943 13.7532 7.26522 13.4241 7.26522 13.0185H8.73461C8.73461 13.4241 8.40553 13.7532 7.99991 13.7532ZM4.81624 11.9165V6.71241C4.81624 5.86139 5.14685 5.06241 5.74838 4.46088C6.34991 3.85935 7.14889 3.52873 7.99991 3.52873C8.85093 3.52873 9.64991 3.85935 10.2514 4.46088C10.853 5.06241 11.1836 5.86139 11.1836 6.71241V11.9165H4.81624Z"
                          fill="white" />
                  </g>
                  <defs>
                    <clipPath id="clip0_3353_12133">
                      <rect width="13.7143"
                            height="13.7143"
                            fill="white"
                            transform="translate(1.14282 1.14285)" />
                    </clipPath>
                  </defs>
                </svg>
              </el-tooltip>
              <span class="btn-bell-badge"
                    v-show="messageTip"></span>
            </div> -->
            <!-- 全屏显示 -->
            <div class="btn-fullscreen"
                 @click="handleFullScreen">
              <el-tooltip effect="dark"
                          :content="fullscreen ? `取消全屏` : `全屏`"
                          placement="bottom">
                <svg width="16"
                     height="16"
                     viewBox="0 0 16 16"
                     fill="none"
                     xmlns="http://www.w3.org/2000/svg">
                  <path d="M4.60179 3.7813L5.27373 3.10936C5.28992 3.0931 5.30121 3.07261 5.30634 3.05025C5.31146 3.02788 5.3102 3.00452 5.3027 2.98284C5.29521 2.96115 5.28178 2.942 5.26394 2.92757C5.2461 2.91313 5.22457 2.904 5.20179 2.9012L2.74975 2.61191C2.67169 2.60273 2.60435 2.66854 2.61353 2.74814L2.90281 5.20018C2.91506 5.3012 3.03904 5.34405 3.11098 5.27212L3.77986 4.60324L5.82628 6.64814C5.87373 6.69558 5.95179 6.69558 5.99924 6.64814L6.64822 6.00069C6.69567 5.95324 6.69567 5.87518 6.64822 5.82773L4.60179 3.7813ZM10.0003 6.64814C10.0477 6.69558 10.1258 6.69558 10.1732 6.64814L12.2197 4.60324L12.8885 5.27212C12.9048 5.2883 12.9253 5.2996 12.9476 5.30472C12.97 5.30984 12.9934 5.30858 13.0151 5.30109C13.0367 5.29359 13.0559 5.28016 13.0703 5.26232C13.0848 5.24448 13.0939 5.22295 13.0967 5.20018L13.386 2.74967C13.3952 2.67161 13.3293 2.60426 13.2498 2.61344L10.7977 2.90273C10.6967 2.91497 10.6538 3.03895 10.7258 3.11089L11.3977 3.78283L9.35128 5.8262C9.3285 5.84921 9.31571 5.88029 9.31571 5.91268C9.31571 5.94506 9.3285 5.97614 9.35128 5.99916L10.0003 6.64814ZM13.0967 10.7992C13.0844 10.6981 12.9605 10.6553 12.8885 10.7272L12.2197 11.3961L10.1732 9.3512C10.1502 9.32841 10.1191 9.31563 10.0867 9.31563C10.0544 9.31563 10.0233 9.32841 10.0003 9.3512L9.35128 9.99865C9.3285 10.0217 9.31571 10.0527 9.31571 10.0851C9.31571 10.1175 9.3285 10.1486 9.35128 10.1716L11.3977 12.218L10.7258 12.89C10.7096 12.9062 10.6983 12.9267 10.6932 12.9491C10.6881 12.9715 10.6893 12.9948 10.6968 13.0165C10.7043 13.0382 10.7177 13.0573 10.7356 13.0718C10.7534 13.0862 10.7749 13.0953 10.7977 13.0981L13.2498 13.3874C13.3278 13.3966 13.3952 13.3308 13.386 13.2512L13.0967 10.7992ZM5.99924 9.3512C5.97623 9.32841 5.94515 9.31563 5.91276 9.31563C5.88038 9.31563 5.8493 9.32841 5.82628 9.3512L3.77986 11.3961L3.11098 10.7272C3.09471 10.711 3.07423 10.6997 3.05186 10.6946C3.0295 10.6895 3.00614 10.6908 2.98445 10.6982C2.96277 10.7057 2.94362 10.7192 2.92918 10.737C2.91475 10.7548 2.90562 10.7764 2.90281 10.7992L2.61353 13.2497C2.60435 13.3277 2.67016 13.3951 2.74975 13.3859L5.20179 13.0966C5.30281 13.0844 5.34567 12.9604 5.27373 12.8884L4.60179 12.218L6.64822 10.1731C6.69567 10.1257 6.69567 10.0476 6.64822 10.0002L5.99924 9.3512Z"
                        fill="white" />
                </svg>
              </el-tooltip>
            </div>
            <!-- 用户头像 -->
            <!-- <div class="user-avator"
                 style="cursor: pointer"
                 @click="goUserCenter">
              <el-image :src="avatar"
                        style="width: 24px; height: 24px; border-radius: 50%">
                <div slot="error"
                     class="image-slot">
                  <el-image src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"
                            style="width: 24px; height: 24px; border-radius: 50%"></el-image>
                </div>
              </el-image>
            </div> -->
            <!-- 用户名下拉菜单 -->
            <el-dropdown v-if="username == '请登录'"
                         class="user-name"
                         style="margin-right: 24px"
                         trigger="click"
                         @command="handleCommand">
              <span v-if="username == '请登录'"
                    @click="gotologin"
                    class="el-dropdown-link">{{ username }}</span>
              <span v-else
                    class="el-dropdown-link">{{ username }}</span>
            </el-dropdown>
            <el-dropdown v-else
                         class="user-name"
                         style="margin-right: 24px"
                         trigger="click"
                         @command="handleCommand">
              <span v-if="username == '请登录'"
                    @click="gotologin"
                    class="el-dropdown-link">{{ username }}</span>
              <span v-else
                    class="el-dropdown-link">{{ username }}</span>
              <el-dropdown-menu slot="dropdown">
                <!-- <a href="http://www.owl-portfolio.com/"
                   target="_blank">
                  <el-dropdown-item>公司官网</el-dropdown-item>
                </a> -->
                <!-- <el-dropdown-item divided
                                  command="userCenter">用户中心</el-dropdown-item> -->
                <el-dropdown-item divided
                                  command="changePassword">修改密码</el-dropdown-item>
                <el-dropdown-item divided
                                  command="userPrivateConfig">账户设置</el-dropdown-item>
                <!-- <el-dropdown-item divided
                                  command="helpcenter">帮助中心</el-dropdown-item> -->
                <el-dropdown-item v-if="!showjslogo"
                                  divided
                                  command="loginout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      </div>
    </div>
    <el-dialog title="修改密码"
               :visible.sync="passwordVisible"
               width="20%"
               :before-close="resetPasswordForm">
      <el-form :model="passwordForm"
               ref="passwordForm">
        <el-form-item label="原密码："
                      prop="oldPassword"
                      :rules="[{ required: true, message: '请输入原密码', trigger: 'blur' }]">
          <el-input v-model="passwordForm.oldPassword"
                    show-password></el-input>
        </el-form-item>
        <el-form-item label="新密码："
                      prop="newPassword"
                      :rules="[
						{ required: true, message: '请输入新密码', trigger: 'blur' },
						{
							type: 'string',
							pattern: /^((\d)+[A-Za-z]+[A-Za-z0-9]*)|([A-Za-z]+(\d)+[A-Za-z0-9]*)$/,
							message: '密码需包含字母和数字,且长度在6~30位之间',
							min: 6,
							max: 30,
							trigger: 'blur'
						}
					]">
          <el-input v-model="passwordForm.newPassword"
                    show-password></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer"
           class="dialog-footer">
        <el-button @click="resetPasswordForm">取 消</el-button>
        <el-button type="primary"
                   @click="submitChangePassword">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import axioss from 'axios'; //引入axios
import axios from '../../api/index';
import { alphaGo } from '../../assets/js/alpha_type.js';
// import {
//   getMessageList,
//   postMessageRead,
//   postMessageDelete
// } from "@/api/pages/home/<USER>";
// import { getUserInfo } from "@/api/pages/systemManage/userManage.js";
import { VueEasyJwt } from 'vue-easy-jwt';

export default {
  data () {
    return {
      showGDB: false,
      showjslogo: false,
      collapse: false,
      fullscreen: false,
      searchShow: true,
      name: '请登录',
      message: 0, //2
      values: null,
      loading: true,
      havefundmanager: null,
      // 修改密码
      passwordVisible: false,
      passwordForm: {
        oldPassword: '',
        newPassword: ''
      },
      messageList: [],
      activeIndex: 'unread',
      title: [],
      readTip: false,
      is_read: false,
      is_delete: false,
      timeout: null,
      avatar: '',
      username: '',
      // 菜单相关
      items: [],
      pathList: []
    };
  },
  computed: {
    onRoutes () {
      return this.$route.path.replace('/', '');
    }
  },
  created () {
    // //console.log("??????????????????????????????????????????????????????????????????????");
    // //console.log(this.$store.state.role);
    if (this.$store.state.userType.indexOf('jscustomer') >= 0) {
      this.showjslogo = true;
    } else {
      this.showjslogo = false;
    }
    if (window.localStorage.getItem('mty_modulesName') == 'GDBank') {
      this.showGDB = true;
    } else {
      this.showGDB = false;
    }

    // 获取菜单数据
    this.getmenu();

    // 监听窗口大小变化
    window.addEventListener('resize', (e) => {
      this.collapse = e.currentTarget.innerWidth < 1920 ? true : false;
    });
  },
  watch: {
    values (val) {
      this.havefundmanager = null;
      //console.log('val', val)
      if (val == '') {
      } else {
        let id = val.split('|')[0];
        let name = val.split('|')[1];
        let flag = val.split('|')[2];

        // 若为基金公司或股票,不需要再请求接口,直接跳转即可
        if (flag == 'company') {
          this.$router.push({
            path: '/fundCompany/' + id,
            hash: '',
            query: {
              id: id,
              name: name
            }
          });
          this.values = '';
          return;
        } else if (flag == 'stock') {
          this.$router.push({
            path: '/equitychoose/' + id,
            hash: '',
            query: {
              id: id,
              name: name
            }
          });
          this.values = '';
          return;
        }
        console.log(id, name, this.$route.path);
        alphaGo(id, name, this.$route.path);
      }
    },
    $route (to, from) {
      if (to.query.flaglogin == 'login' || to.query.flaglogin == 'logins') {
        this.username = localStorage.getItem('username') ? localStorage.getItem('username') : this.name;
        this.getmenu(); // 重新获取菜单数据
      }
    }
  },
  computed: {
    // avatar() {
    // 	return localStorage.getItem('avatar');
    // },
    messageTip () {
      return this.is_read == false && this.messageList?.length !== 0;
    }
  },
  methods: {
    // 导航到子菜单项
    navigateToSubItem (hash) {
      this.$router.push({ path: '/' + hash, hash: '' });
    },

    // 获取菜单数据
    async getmenu () {
      // 使用与Sidebar.vue相同的完整菜单数据
      this.items = [
        // {
        //   index: 'marketAnalysys',
        //   title: '市场分析',
        //   icon: 'M14.714 13.0006H2.42829V1.8577C2.42829 1.77913 2.36401 1.71484 2.28544 1.71484H1.28544C1.20686 1.71484 1.14258 1.77913 1.14258 1.8577V14.1434C1.14258 14.222 1.20686 14.2863 1.28544 14.2863H14.714C14.7926 14.2863 14.8569 14.222 14.8569 14.1434V13.1434C14.8569 13.0648 14.7926 13.0006 14.714 13.0006ZM3.99972 11.572H4.99972C5.07829 11.572 5.14258 11.5077 5.14258 11.4291V8.8577C5.14258 8.77913 5.07829 8.71484 4.99972 8.71484H3.99972C3.92115 8.71484 3.85686 8.77913 3.85686 8.8577V11.4291C3.85686 11.5077 3.92115 11.572 3.99972 11.572ZM6.71401 11.572H7.71401C7.79258 11.572 7.85687 11.5077 7.85687 11.4291V5.71484C7.85687 5.63627 7.79258 5.57199 7.71401 5.57199H6.71401C6.63544 5.57199 6.57115 5.63627 6.57115 5.71484V11.4291C6.57115 11.5077 6.63544 11.572 6.71401 11.572ZM9.42829 11.572H10.4283C10.5069 11.572 10.5712 11.5077 10.5712 11.4291V7.1077C10.5712 7.02913 10.5069 6.96484 10.4283 6.96484H9.42829C9.34972 6.96484 9.28544 7.02913 9.28544 7.1077V11.4291C9.28544 11.5077 9.34972 11.572 9.42829 11.572ZM12.1426 11.572H13.1426C13.2212 11.572 13.2854 11.5077 13.2854 11.4291V4.28627C13.2854 4.2077 13.2212 4.14342 13.1426 4.14342H12.1426C12.064 4.14342 11.9997 4.2077 11.9997 4.28627V11.4291C11.9997 11.5077 12.064 11.572 12.1426 11.572Z',
        //   subs: [
        //     {
        //       index: 'captial-market-conditions',
        //       title: '资本市场分析',
        //       subs: null
        //     },
        //     {
        //       index: 'fund-market-performance',
        //       title: '基金市场业绩',
        //       subs: null
        //     },
        //     {
        //       index: 'fund-market-allocation',
        //       title: '基金市场配置',
        //       subs: null
        //     }
        //   ]
        // },
        // {
        //   index: 'dashboard',
        //   title: '画像',
        //   icon: 'M14.7247 7.88569L8.74446 1.90243L8.34361 1.50117C8.25228 1.41035 8.12877 1.35938 8.00003 1.35938C7.87129 1.35938 7.74778 1.41035 7.65645 1.50117L1.2754 7.88569C1.18182 7.97901 1.10785 8.09015 1.05788 8.21255C1.00791 8.33495 0.982935 8.46614 0.984439 8.59836C0.99063 9.1437 1.4441 9.57904 1.98888 9.57904H2.64664V14.625H13.3534V9.57904H14.0251C14.2898 9.57904 14.5389 9.47524 14.7262 9.28778C14.8184 9.19577 14.8915 9.08637 14.9412 8.9659C14.9908 8.84543 15.0161 8.71629 15.0156 8.58596C15.0156 8.32259 14.9119 8.07316 14.7247 7.88569ZM8.86672 13.5095H7.13333V10.349H8.86672V13.5095ZM12.2391 8.46357V13.5095H9.85723V9.9772C9.85723 9.63482 9.5802 9.3575 9.23816 9.3575H6.76189C6.41986 9.3575 6.14282 9.63482 6.14282 9.9772V13.5095H3.76096V8.46357H2.2752L8.00158 2.73594L8.35909 3.09382L13.7264 8.46357H12.2391Z',
        //   subs: null
        // },
        {
          index: 'Header',
          title: '画像与筛选',
          icon: 'M0.574741 2.87207L5.36571 10.1406L5.36572 13.4531C5.36572 13.7638 5.61756 14.0156 5.92822 14.0156H10.0845C10.3951 14.0156 10.647 13.7638 10.647 13.4531L10.647 10.1406L15.4268 2.87155C15.6728 2.49751 15.4045 2 14.9568 2H1.04439C0.59646 2 0.328225 2.49807 0.574741 2.87207ZM13.9137 3.125H2.08886L5.97159 9.01562H10.0403L13.9137 3.125ZM9.52197 10.1406H6.49072V12.8906H9.52197V10.1406Z',
          subs: [
          {
          index: 'dashboard',
          title: '画像',
          icon: 'M14.7247 7.88569L8.74446 1.90243L8.34361 1.50117C8.25228 1.41035 8.12877 1.35938 8.00003 1.35938C7.87129 1.35938 7.74778 1.41035 7.65645 1.50117L1.2754 7.88569C1.18182 7.97901 1.10785 8.09015 1.05788 8.21255C1.00791 8.33495 0.982935 8.46614 0.984439 8.59836C0.99063 9.1437 1.4441 9.57904 1.98888 9.57904H2.64664V14.625H13.3534V9.57904H14.0251C14.2898 9.57904 14.5389 9.47524 14.7262 9.28778C14.8184 9.19577 14.8915 9.08637 14.9412 8.9659C14.9908 8.84543 15.0161 8.71629 15.0156 8.58596C15.0156 8.32259 14.9119 8.07316 14.7247 7.88569ZM8.86672 13.5095H7.13333V10.349H8.86672V13.5095ZM12.2391 8.46357V13.5095H9.85723V9.9772C9.85723 9.63482 9.5802 9.3575 9.23816 9.3575H6.76189C6.41986 9.3575 6.14282 9.63482 6.14282 9.9772V13.5095H3.76096V8.46357H2.2752L8.00158 2.73594L8.35909 3.09382L13.7264 8.46357H12.2391Z',
          subs: null
        },
        {
          index: 'alphaHeader',
          title: '捕获',
          icon: 'M0.574741 2.87207L5.36571 10.1406L5.36572 13.4531C5.36572 13.7638 5.61756 14.0156 5.92822 14.0156H10.0845C10.3951 14.0156 10.647 13.7638 10.647 13.4531L10.647 10.1406L15.4268 2.87155C15.6728 2.49751 15.4045 2 14.9568 2H1.04439C0.59646 2 0.328225 2.49807 0.574741 2.87207ZM13.9137 3.125H2.08886L5.97159 9.01562H10.0403L13.9137 3.125ZM9.52197 10.1406H6.49072V12.8906H9.52197V10.1406Z',
          subs: null
        }, {
          index: 'Comparefundormanager',
          title: '比较',
          icon: "M1.4375 1.03125V12.0156H2.5625V6.57812H6.66406C8.19579 6.57812 9.4375 5.33641 9.4375 3.80469C9.4375 2.27296 8.19579 1.03125 6.66406 1.03125H1.4375ZM6.66406 2.15625H2.5625V5.45312H6.66406C7.57447 5.45312 8.3125 4.71509 8.3125 3.80469C8.3125 2.89428 7.57447 2.15625 6.66406 2.15625Z&&M7.29688 7.20312H6.17188V14.9844H7.29688V10.9559L8.11928 10.1335L12.9701 14.9844H14.5611L8.91477 9.33803L13.3636 4.88925L11.7868 4.875L7.29688 9.36494V7.20312Z",
          subs: null
        },
          ]
        },
        // {
        //   index: 'Comparefundormanager',
        //   title: '比较',
        //   icon: "M1.4375 1.03125V12.0156H2.5625V6.57812H6.66406C8.19579 6.57812 9.4375 5.33641 9.4375 3.80469C9.4375 2.27296 8.19579 1.03125 6.66406 1.03125H1.4375ZM6.66406 2.15625H2.5625V5.45312H6.66406C7.57447 5.45312 8.3125 4.71509 8.3125 3.80469C8.3125 2.89428 7.57447 2.15625 6.66406 2.15625Z&&M7.29688 7.20312H6.17188V14.9844H7.29688V10.9559L8.11928 10.1335L12.9701 14.9844H14.5611L8.91477 9.33803L13.3636 4.88925L11.7868 4.875L7.29688 9.36494V7.20312Z",
        //   subs: null
        // },
        {
          index: 'fundPool',
          title: '投资池',
          icon: 'M14.6408 6.92037L14.6372 6.90723L12.6402 2.22413C12.5512 1.95967 12.2859 1.77734 11.9851 1.77734H3.88997C3.58738 1.77734 3.31861 1.96296 3.23317 2.23071L1.36602 6.86616L1.36068 6.87766L1.35712 6.8908C1.33398 6.97129 1.32686 7.05342 1.33932 7.13391C1.33754 7.16019 1.33576 7.18647 1.33576 7.21275V13.2231C1.33623 13.4878 1.4504 13.7416 1.65325 13.9288C1.8561 14.116 2.13109 14.2214 2.41796 14.2218H13.5817C14.178 14.2218 14.6639 13.7734 14.6657 13.2231V7.21275C14.6657 7.1914 14.6657 7.17005 14.6639 7.15198C14.6711 7.07149 14.6639 6.99429 14.6408 6.92037ZM9.37574 6.21404L9.3704 6.47193C9.35616 7.20947 8.80438 7.70554 7.99807 7.70554C7.6047 7.70554 7.26652 7.58891 7.02266 7.36716C6.77881 7.14541 6.64532 6.8366 6.6382 6.47193L6.63286 6.21404H2.96974L4.38479 3.03887H11.4903L12.9445 6.21404H9.37574ZM2.70097 7.47557H5.50082C5.93334 8.41351 6.85357 8.96707 7.99985 8.96707C8.59969 8.96707 9.15681 8.81266 9.60714 8.52027C10.0023 8.26403 10.3102 7.90594 10.5096 7.47557H13.2952V12.9603H2.70097V7.47557Z',
          subs: [
            {
              index: 'poolnormal',
              title: '基金池',
              subs: null
            },
            {
              index: 'poolcompare',
              title: '基金对标池',
              subs: null
            },
            {
              index: 'managerpoolnormal',
              title: '基金经理池',
              subs: null
            },
            {
              index: 'managerpoolcompare',
              title: '基金经理对标池',
              subs: null
            }
          ]
        },
        // {
        //   index: 'board',
        //   title: '投后',
        //   icon: 'M4.43279 10.6779C4.48259 10.6281 4.48754 10.549 4.44434 10.4934C3.65771 9.48062 3.18785 8.20857 3.18785 6.82614C3.18785 3.52463 5.86405 0.848427 9.16556 0.848427C12.4671 0.848427 15.1433 3.52463 15.1433 6.82614C15.1433 10.1276 12.4671 12.8039 9.16556 12.8039C7.78312 12.8039 6.51108 12.334 5.4983 11.5474C5.44267 11.5042 5.36358 11.5091 5.31378 11.5589L1.75182 15.1209C1.74824 15.1245 1.74315 15.1268 1.73693 15.1268C1.73071 15.1268 1.72562 15.1245 1.72204 15.1209L0.871316 14.2702C0.867349 14.2661 0.86499 14.2605 0.86499 14.2548C0.86499 14.2491 0.867218 14.2436 0.871193 14.2396L4.43279 10.6779ZM9.16556 11.554C10.4279 11.554 11.6165 11.0624 12.5081 10.1689C13.4018 9.27732 13.8934 8.08863 13.8934 6.82614C13.8934 5.5637 13.4018 4.37506 12.5082 3.48348C11.6166 2.58988 10.428 2.09832 9.16556 2.09832C7.90312 2.09832 6.71448 2.58988 5.8229 3.48348C4.9293 4.37506 4.43774 5.5637 4.43774 6.82614C4.43774 8.08853 4.92926 9.27711 5.82278 10.1687C6.71438 11.0624 7.90306 11.554 9.16556 11.554Z',
        //   subs: [
        //     {
        //       index: 'monitorWarning',
        //       title: '监控预警',
        //       subs: null
        //     },
        //     {
        //       index: 'monitorBoard',
        //       title: '投后监控看板',
        //       subs: [
        //         {
        //           index: 'performance',
        //           title: '业绩看板',
        //           subs: null
        //         },
        //         {
        //           index: 'structure',
        //           title: '结构看板',
        //           subs: null
        //         },
        //         {
        //           index: 'pivotTable',
        //           title: '数据透视表',
        //           subs: null
        //         },
        //         {
        //           index: 'information',
        //           title: '重点个股信息',
        //           subs: null
        //         }
        //       ]
        //     },
        //     {
        //       index: 'management',
        //       title: '投后分析',
        //       subs: [
        //         {
        //           index: 'objectManagement',
        //           title: '分析对象管理',
        //           subs: null
        //         },
        //         {
        //           index: 'reportManagement',
        //           title: '分析报告管理',
        //           subs: null
        //         }
        //       ]
        //     },
        //     {
        //       index: 'mapping',
        //       title: '映射管理',
        //       subs: [
        //         {
        //           index: 'industryMap',
        //           title: '行业映射管理列表',
        //           subs: null
        //         },
        //         {
        //           index: 'productMap',
        //           title: '产品映射管理列表',
        //           subs: null
        //         },
        //         {
        //           index: 'Tl4Map',
        //           title: 'TL4映射列表',
        //           subs: null
        //         },
        //         {
        //           index: 'productRecord',
        //           title: '产品管理记录',
        //           subs: null
        //         },
        //         {
        //           index: 'customMarketDivision',
        //           title: '自定义市场划分',
        //           subs: null
        //         },
        //         {
        //           index: 'whitelist',
        //           title: '白名单管理列表',
        //           subs: null
        //         },
        //         {
        //           index: 'blacklist',
        //           title: '黑名单管理列表',
        //           subs: null
        //         }
        //       ]
        //     }
        //   ]
        // },
        {
          index: 'portfolio',
          title: '组合管理',
          icon: 'M5.125 7.85938C5.125 8.17004 4.87316 8.42188 4.5625 8.42188C4.25184 8.42188 4 8.17004 4 7.85938C4 7.54871 4.25184 7.29688 4.5625 7.29688C4.87316 7.29688 5.125 7.54871 5.125 7.85938Z&&M6.57812 7.29688C6.26746 7.29688 6.01562 7.54871 6.01562 7.85938C6.01562 8.17004 6.26747 8.42188 6.57812 8.42188H11.4375C11.7482 8.42188 12 8.17004 12 7.85938C12 7.54871 11.7482 7.29688 11.4375 7.29688H6.57812Z&&M5.125 10.1094C5.125 10.42 4.87316 10.6719 4.5625 10.6719C4.25184 10.6719 4 10.42 4 10.1094C4 9.79871 4.25184 9.54688 4.5625 9.54688C4.87316 9.54688 5.125 9.79871 5.125 10.1094Z&&M6.57812 9.54688C6.26746 9.54688 6.01562 9.79871 6.01562 10.1094C6.01562 10.42 6.26747 10.6719 6.57812 10.6719H11.4375C11.7482 10.6719 12 10.42 12 10.1094C12 9.79871 11.7482 9.54688 11.4375 9.54688H6.57812Z&&M0.984375 2.5625C0.984375 2.25184 1.23621 2 1.54688 2H6.59877C6.84263 2 7.05872 2.15714 7.13388 2.38913L7.63552 3.9375H14.4531C14.7638 3.9375 15.0156 4.18934 15.0156 4.5V13.4531C15.0156 13.7638 14.7638 14.0156 14.4531 14.0156H1.54687C1.23621 14.0156 0.984375 13.7638 0.984375 13.4531V2.5625ZM2.10938 3.9375V3.125H6.18972L6.45295 3.9375H2.10938ZM2.10938 5.0625V12.8906H13.8906V5.0625H2.10938Z',
          subs: [
            {
              index: 'configurationStrategyIndex',
              title: '配置策略研究',
              subs: null
            },
            {
              index: 'portfolioStudyIndex',
              title: '组合策略研究',
              subs: null
            },
            {
              index: 'portfolioList',
              title: '模拟组合管理',
              subs: null
            }
          ]
        },
        {
          index: 'dataAndReport',
          title: '数据与报告',
          icon: 'M2 13.3333C2 13.5101 2.07024 13.6797 2.19526 13.8047C2.32029 13.9298 2.48986 14 2.66667 14H13.3333C13.5101 14 13.6797 13.9298 13.8047 13.8047C13.9298 13.6797 14 13.5101 14 13.3333V8.33333H12.6667V12.6667H3.33333V3.33333H7.66667V2H2.66667C2.48986 2 2.32029 2.07024 2.19526 2.19526C2.07024 2.32029 2 2.48986 2 2.66667V13.3333Z&&M10.3333 1.66663H9.66667V2.99996H12.1143L7.25234 7.86196L6.78101 8.33329L7.72401 9.27629L8.19534 8.80463L13 3.99996V6.33329H14.3333V2.33329C14.3333 2.15648 14.2631 1.98691 14.1381 1.86189C14.0131 1.73686 13.8435 1.66663 13.6667 1.66663H10.3333Z',
          subs: [
            {
              index: 'subscriptionCenter',
              title: '订阅中心',
              subs: null
            },
            {
              index: 'exportData',
              title: '数据导出',
              sub: null
            }
          ]
        },
        // {
        //   index: 'system',
        //   title: '系统管理',
        //   icon: 'M15.0078 13.5078C15.0078 13.1972 14.756 12.9453 14.4453 12.9453L13.0391 12.9453L13.0391 11.0078L13.0388 11.0078C13.039 11.0026 13.0391 10.9974 13.0391 10.9922C13.0391 10.6815 12.7872 10.4297 12.4766 10.4297L8.57031 10.4297L8.57031 9.03906L9.97656 9.03906C10.2872 9.03906 10.5391 8.78722 10.5391 8.47656L10.5391 4.53906C10.5391 4.2284 10.2872 3.97656 9.97656 3.97656L6.03906 3.97656C5.7284 3.97656 5.47656 4.2284 5.47656 4.53906L5.47656 8.47656C5.47656 8.78722 5.7284 9.03906 6.03906 9.03906L7.44531 9.03906L7.44531 10.4297L3.52344 10.4297C3.21278 10.4297 2.96094 10.6815 2.96094 10.9922C2.96094 10.9974 2.96101 11.0026 2.96115 11.0078L2.96094 11.0078L2.96094 12.9453L1.55469 12.9453C1.24403 12.9453 0.992187 13.1972 0.992187 13.5078L0.992187 17.4453C0.992187 17.756 1.24403 18.0078 1.55469 18.0078L5.49219 18.0078C5.80285 18.0078 6.05469 17.756 6.05469 17.4453L6.05469 13.5078C6.05469 13.1972 5.80285 12.9453 5.49219 12.9453L4.08594 12.9453L4.08594 11.5547L11.9141 11.5547L11.9141 12.9453L10.5078 12.9453C10.1972 12.9453 9.94531 13.1972 9.94531 13.5078L9.94531 17.4453C9.94531 17.756 10.1972 18.0078 10.5078 18.0078L14.4453 18.0078C14.756 18.0078 15.0078 17.756 15.0078 17.4453L15.0078 13.5078ZM13.8828 16.8828L13.8828 14.0703L11.0703 14.0703L11.0703 16.8828L13.8828 16.8828ZM9.41406 5.10156L6.60156 5.10156L6.60156 7.91406L9.41406 7.91406L9.41406 5.10156ZM2.11719 16.8828L2.11719 14.0703L4.92969 14.0703L4.92969 16.8828L2.11719 16.8828Z',
        //   subs: [
        //     {
        //       index: 'userrolecontrol',
        //       title: '用户权限管理',
        //       subs: null
        //     }
        //   ]
        // }
      ].map((item) => {
        return {
          ...item,
          icon: item.icon.split('&&')
        };
      });
    },

    getusername () {
      // console.log(process.env.VUE_APP_BASE_URL);
      try {
        const jwt = new VueEasyJwt();
        console.log(jwt.isExpired(this.$store.state.retoken), this.$store.state.retoken);
        if (jwt.isExpired(this.$store.state.retoken)) {
          localStorage.setItem('username', '请登录');
          this.username = '请登录';
        } else {
          console.log(jwt.isExpired(this.$store.state.token), this.$store.state.token);
          if (jwt.isExpired(this.$store.state.token)) {
            axioss
              .post(this.$baseUrl + '/oauth/api-token-refresh', {
                refresh: this.$store.state.retoken
              })
              .then((res) => {
                console.log(res);
                if (res && res.data && res.data.access) {
                  let username = localStorage.getItem('username');
                  this.username = username ? username : this.name;
                } else {
                  localStorage.setItem('username', '请登录');
                  this.username = '请登录';
                }
              })
              .catch((e) => {
                // console.log('weqweqwe4');
                // localStorage.setItem('username', '请登录');
                // this.username = '请登录';
              });
          } else {
            let username = localStorage.getItem('username');
            this.username = username ? username : this.name;
          }
        }
      } catch (e) {
        // console.log('????????')
        console.log(e, 'weqweqwe23');
        localStorage.setItem('username', '请登录');
        this.username = '请登录';
      }
    },
    //图片损毁时触发
    imgOnError (e) {
      let img = e.srcElement;
      img.src = '../../assets/img/people.png';
      img.onerror = null; //防止闪图
    },
    gotologin () {
      this.$logins();
    },
    cancalReadOnly (value) {
      let plat = navigator.userAgent.match(
        // 判断不同端
        /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
      );
      if (plat) {
        this.$nextTick(() => {
          if (!value) {
            const { select } = this.$refs;
            const input = select.$el.querySelector('.el-input__inner');
            input.removeAttribute('readonly');
            // this.$refs.select.blur();  根据tip自行判断是否添加
          }
        });
      }
    },
    // 前往帮助中心
    goHelpCenter () {
      window.open('https://help.owl-portfolio.com/');
    },
    // 前往用户中心
    goUserCenter () {
      this.$router.push('/userCenter');
    },
    // 获取消息通知列表
    async getMessageList () {
      let data = await getMessageList({
        is_read: this.is_read,
        is_delete: this.is_delete,
        user_id: localStorage.getItem('id')
      });
      if (data?.mtycode == 200) {
        this.messageList = data?.data?.data;
        if (this.is_read == false && this.is_delete == false) {
          this.messageList
            .filter((item) => {
              return item.is_strong;
            })
            .map((item, index) => {
              this.$notify.warning({
                title: item.type_name,
                message: item.message,
                duration: 10000,
                offset: 100 * (index + 1)
              });
              this.postMessageRead([item._id], true);
            });
          // this.getMessageList();
        }
      }
    },
    // 切换已读/未读
    menuSelect (val) {
      this.activeIndex = val;
      if (val == 'read') {
        this.is_read = true;
      } else {
        this.is_read = false;
      }
      this.getMessageList();
    },
    // 全部标记为已读
    readAll () {
      let ids = this.messageList.map((item) => {
        return item._id;
      });
      this.postMessageRead(ids);
    },
    // 全部将已读移入回收站
    async deleteAll () {
      let ids = this.messageList.map((item) => {
        return item._id;
      });
      this.postMessageDelete(ids);
    },
    // 标记为已读
    async postMessageRead (ids, bool) {
      if (bool) {
        await postMessageRead({ ids, user_id: localStorage.getItem('id') });
      } else {
        await postMessageRead({ ids, user_id: localStorage.getItem('id') });
        this.getMessageList();
      }
    },
    // 移入回收站
    async postMessageDelete (ids) {
      await postMessageDelete({ ids, user_id: localStorage.getItem('id') });
      this.getMessageList();
    },
    // 查看更多
    goDetail () {
      this.$router.push('/tabs');
    },
    searchpeople (query) {
      this.havefundmanager = null;
      clearTimeout(this.timeout);
      this.timeout = setTimeout(() => {
        this.getResultsList(query);
      }, 500);
    },
    getResultsList (query) {
      axios
        .get(this.$baseUrl + '/Analysis/Search/?message=' + query + '&flag=1,2,3')
        .then((res) => {
          console.log(res.data);
          let data = res?.data?.data || [];
          let temparr = [
            {
              label: '基金产品',
              options: []
            },
            {
              label: '基金经理',
              options: []
            },
            {
              label: '基金公司',
              options: []
            }
            // FIXME: 暂不开放股票入口
            // {
            // 	label: '股票',
            // 	options: []
            // }
          ];
          for (let i = 0; i < data.length; i++) {
            if (data[i].flag === 'fund') {
              temparr[0].options.push(data[i]);
            } else if (data[i].flag == 'manager') {
              temparr[1].options.push(data[i]);
            } else if (data[i].flag == 'company') {
              temparr[2].options.push(data[i]);
            }
            // FIXME: 暂不开放股票入口
            // else if (res.data[i].flag == 'stock') {
            // 	temparr[3].options.push(res.data[i]);
            // }
          }
          console.log(temparr, data);
          this.havefundmanager = temparr;
          this.loading = false;
        })
        .catch((error) => {
          //that.$message('数据缺失');
        });
    },
    // 用户名下拉菜单选择事件
    handleCommand (command) {
      if (command == 'loginout') {
        axios
          .post(this.$baseUrl + '/logout/', { all: true })
          .then((res) => {
            localStorage.removeItem('username');
            localStorage.removeItem('token');
            localStorage.removeItem('id');
            localStorage.removeItem('retoken');
            localStorage.removeItem('userType');
            this.$store.dispatch('changerefreshtoken', '');
            this.$store.dispatch('changetoken', '');
            this.$store.dispatch('changeusername', '');
            this.$store.dispatch('changeuserid', '');
            this.$store.dispatch('changeuserrole', '');
            this.$router.push({ path: '/dashboard' });
            this.$nextTick(() => {
              setTimeout(() => {
                location.reload();
              }, 3000);
            });
          })
          .catch((err) => {
            localStorage.removeItem('username');
            localStorage.removeItem('token');
            localStorage.removeItem('id');
            localStorage.removeItem('retoken');
            localStorage.removeItem('userType');
            this.$store.dispatch('changerefreshtoken', '');
            this.$store.dispatch('changetoken', '');
            this.$store.dispatch('changeusername', '');
            this.$store.dispatch('changeuserid', '');
            this.$store.dispatch('changeuserrole', '');
            this.$router.push({ path: '/dashboard' });
            this.$nextTick(() => {
              setTimeout(() => {
                location.reload();
              }, 3000);
            });
          });
      } else if (command == 'changePassword') {
        this.passwordVisible = true;
      } else if (command == 'userPrivateConfig') {
        this.$router.push('/userPrivateConfig');
      } else if (command == 'helpcenter') {
        window.open('https://help.owl-portfolio.com/');
      } else if (command == 'userCenter') {
        this.goUserCenter();
      }
    },
    // 侧边栏折叠
    collapseChage () {
      this.collapse = !this.collapse;
      this.$event.$emit('collapse', this.collapse);
    },
    // 全屏事件
    handleFullScreen () {
      let element = document.documentElement;
      if (this.fullscreen) {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.webkitCancelFullScreen) {
          document.webkitCancelFullScreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }
      } else {
        if (element.requestFullscreen) {
          element.requestFullscreen();
        } else if (element.webkitRequestFullScreen) {
          element.webkitRequestFullScreen();
        } else if (element.mozRequestFullScreen) {
          element.mozRequestFullScreen();
        } else if (element.msRequestFullscreen) {
          // IE11
          element.msRequestFullscreen();
        }
      }
      this.fullscreen = !this.fullscreen;
    },
    submitChangePassword () {
      if (this.passwordForm.oldPassword === this.passwordForm.newPassword) {
        this.$message('提示：新密码不能与原密码一样');
        return;
      }
      this.$refs.passwordForm.validate((valid) => {
        if (valid) {
          let params = {
            old_password: this.passwordForm.oldPassword,
            new_password: this.passwordForm.newPassword
          };
          axios
            .post(`${this.$baseUrl}/users/${this.$store.state.id}/change-password`, params)
            .then((res) => {
              if (res.status == 200) {
                this.passwordVisible = false;
                this.resetPasswordForm();
                this.$message.success('修改密码成功');
              }
            })
            .catch((err) => {
              let message = err.data && err.data.mtymessage ? err.data.mtymessage : err.statusText ? err.statusText : err;
              this.$message.error('修改密码失败: ' + message);
            });
        }
      });
    },
    // handleClose() {},
    resetPasswordForm () {
      this.passwordVisible = false;
      this.$refs.passwordForm.resetFields();
    },
    // websoket连接
    connectWebsocket () {
      var ws = new WebSocket(this.$webSocket);
      this.$store.commit('setWs', ws);
      ws.onmessage = (evt) => {
        let reader = new FileReader();
        reader.addEventListener('load', (e) => {
          let readerres = reader.result;
          let parseObj = typeof readerres == 'string' ? JSON?.parse?.(readerres) : {};
          if (parseObj?.is_strong) {
            this.$notify.warning({
              title: parseObj.type_name,
              message: parseObj.message,
              duration: 0,
              offset: 100
            });
            this.postMessageRead([parseObj._id], true);
          }
          // this.getMessageList();
        });
        if (typeof file === 'object' && file instanceof Blob) {
          // Call readAsText() with the file object
          reader.readAsText(evt.data, 'utf-8');
          //   reader.readAsText(file);
        } else {
          try {
            let parseObj = JSON.parse(evt.data);
            if (parseObj?.type == 'print') {
              this.$notify.warning({
                title: parseObj.type_name,
                message: parseObj.message,
                duration: 0,
                offset: 100
              });
              this.$event.$emit('printOver', true);
            }
          } catch { }
        }
      };
      setTimeout(() => {
        console.log(localStorage.getItem('id'));
        ws.send(localStorage.getItem('id'));
      }, 1000);
    }
  },
  async mounted () {
    this.getusername();
    // this.getMessageList();

    this.connectWebsocket();
    // let data = await getUserInfo({ user_id: localStorage.getItem('id') });
    // if (data?.mtycode == 200) {
    // 	this.avatar = data?.data?.user_avatar;
    // }
  }
};
</script>
<style>
.searchfundormanager .el-input__inner {
	padding: 0 24px !important;
	border-radius: 4px !important;
	background: #ffffff !important;
	border: 1px solid #d9d9d9;
	width: 300px !important;
}
.slot_tooltip {
	border: 1px solid #e9e9e9 !important;
	box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);
	border-radius: 4px;
	padding: 0 !important;
}
</style>
<style scoped>
.imgss2x {
	width: 200px;
	height: 30px;
}
.imgss2x2 {
	width: 350px;
	height: 30px;
}
.fzpb {
	font-size: 12px;
	line-height: 18px;
	color: rgb(39, 35, 35);
	font-weight: 400;
	font-family: Fantasy;
}
.selsss ::v-deep .el-input__inner {
	color: #000000 !important;
}
.marin409 {
	margin-left: 409px;
	display: flex;
}
.selsss {
	margin-left: 1vw;
	width: 200px;
}
.selsss .el-select-dropdown__wrap {
	max-height: 700px !important;
}
.header {
	font-family: 'PingFang';
	position: relative;
	box-sizing: border-box;
	width: 100%;
	height: 56px;
	font-size: 22px;
	color: white;
	background: #4096ff;
}
.collapse-btn {
	float: left;
	padding: 0 21px;
	cursor: pointer;
	line-height: 56px;
}
.header .logo {
	display: flex;
	float: left;
	max-width: 1200px;
	line-height: 56px;
}
.text {
	width: 168px;
	height: 25px;
	font-size: 24px;
	/* //STHupo; */
	font-family: ruifont;
	font-weight: 400;
	color: #4096ff;
	line-height: 56px;
	margin-left: 74px;
}
.imsg {
	height: 56px;
}
.imsg2 {
	margin-left: 20px;
	height: 56px;
	justify-content: center;
	line-height: 0;
	display: flex;
	flex-direction: column;
	align-items: center;
}
.imgss {
	margin-left: 16px;
	height: 31.02px;
	margin-right: 12px;
}
.imgss2 {
	margin-top: 1px;
	height: 31.02px;
	margin-right: 12px;
}
.header-right {
	float: right;
	padding-right: 50px;
}
.header-user-con {
	display: flex;
	height: 56px;
	align-items: center;
}
.btn-fullscreen {
	/* transform: rotate(45deg); */
	margin-left: 4px;
	font-size: 24px;
	line-height: 30px;
}
.btn-bell,
.btn-fullscreen {
	position: relative;
	width: 30px;
	height: 30px;
	text-align: center;
	border-radius: 15px;
	cursor: pointer;
}
.btn-bell-badge {
	position: absolute;
	right: 0;
	top: -2px;
	width: 8px;
	height: 8px;
	border-radius: 4px;
	background: #f56c6c;
	color: white;
}
.btn-bell .el-icon-bell {
	color: white;
}
.el-tooltip__popper {
	padding: 0;
}
.user-name {
	margin-left: 10px;
	color: white;
}
.user-avator {
	display: flex;
	justify-content: center;
	align-items: center;
	margin-left: 12px;
	margin-right: 8px;
}
.user-avator img {
	display: block;
	width: 24px;
	height: 24px;
	border-radius: 50%;
}
.el-dropdown-link {
	color: white;
	cursor: pointer;
}
.el-dropdown-menu__item {
	text-align: center;
}

/* 顶部菜单样式 */
.top-menu-container {
  overflow-y: hidden;
  margin-left: 20px;
  flex: 1;
  overflow-x: auto;
  display: flex;
}

.top-menu {
  border-bottom: none !important;
  background-color: #4096ff !important;
  display: flex;
  flex-wrap: nowrap;
  width: 100%;
}

.top-menu .el-menu-item {
  height: 56px !important;
  line-height: 56px !important;
  color: #ffffff !important;
  font-size: 14px;
  padding: 0 15px !important;
  white-space: nowrap;
}

.top-menu .el-menu-item:hover,
.top-menu .el-submenu__title:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}
/* .top-menu .el-menu-item .is-active */
.top-menu .el-menu-item.is-active {
  color: #ffffff !important;
  /* background: #ffffff !important; */
  border-bottom: 2px solid #ffffff !important;
}

.top-menu .el-submenu__title {
  height: 56px !important;
  line-height: 56px !important;
  color: #ffffff !important;
  font-size: 14px;
  padding: 0 15px !important;
  white-space: nowrap;
}

.top-menu .el-submenu.is-active .el-submenu__title {
  color: #ffffff !important;
  border-bottom: 2px solid #ffffff !important;
}

/* 下拉菜单样式 */
.el-menu--horizontal .el-menu {
  max-height: 400px;
  overflow-y: auto;
}

.el-menu--horizontal .el-menu .el-menu-item,
.el-menu--horizontal .el-menu .el-submenu__title {
  background-color: #ffffff;
  color: rgba(0, 0, 0, 0.65);
  height: 40px !important;
  line-height: 40px !important;
}

.el-menu--horizontal .el-menu .el-menu-item:hover,
.el-menu--horizontal .el-menu .el-submenu__title:hover {
  background-color: #f5f5f5;
}

.el-menu--horizontal .el-menu .el-menu-item.is-active {
  color: #4096ff;
}

/* 隐藏水平滚动条但保持功能 */
.top-menu-container::-webkit-scrollbar {
  height: 0;
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  .top-menu-container {
    margin-left: 10px;
  }

  .top-menu .el-menu-item,
  .top-menu .el-submenu__title {
    padding: 0 10px !important;
    font-size: 13px;
  }

  .logo .imgss {
    margin-left: 8px;
    height: 28px;
  }
}
</style>
