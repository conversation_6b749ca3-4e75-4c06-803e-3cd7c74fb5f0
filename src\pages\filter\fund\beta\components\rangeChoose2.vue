<template>
  <div>
    <div style="padding-top: 24px; margin-left: 24px; display: flex">
      <span class="normalFont"
            style="flex: 1; max-width: 100px">证监会分类：</span>
      <el-checkbox-group @change="changetype('out')"
                         style="flex: 1"
                         v-model="outType">
        <el-checkbox v-for="(item, index) in outTypeList"
                     :key="index"
                     :label="item.value">{{ item.label }}</el-checkbox>
      </el-checkbox-group>
    </div>
    <div style="padding-top: 24px; margin-left: 24px; display: flex">
      <span class="normalFont"
            style="flex: 1; max-width: 100px">慧捕基分类：</span>
      <el-checkbox-group style="flex: 1"
                         @change="changetype('mty')"
                         v-model="mtyType">
        <el-checkbox v-for="(item, index) in mtyTypeList"
                     :key="index"
                     :label="item.value">{{ item.label }}</el-checkbox>
      </el-checkbox-group>
    </div>
    <div>
      <alphachoosepool @changepool="changepool"
                       ref="alphachoosepool"
                       typeFilter="beta"></alphachoosepool>
    </div>
  </div>
</template>

<script>
import alphachoosepool from '@/components/components/alphafilter/alphachoosepool.vue';

import { getTypeList } from '@/api/pages/SystemAlpha.js';
export default {
  components: { alphachoosepool },
  data () {
    return {
      isIndeterminateOut: false,
      isIndeterminateMty: false,
      checkOut: true,
      checkMty: true,
      mtyType: ['主动权益'], //一级分类
      mtyTypeList: [
        {
          "label": "全部类型",
          "value": "all"
        },
        {
          "label": "主动权益",
          "value": "主动权益"
        },
        {
          "label": "港股",
          "value": "港股"
        },
        {
          "label": "固收+",
          "value": "固收+"
        },
        {
          "label": "可转债",
          "value": "可转债"
        },
        {
          "label": "纯债",
          "value": "纯债"
        },
        {
          "label": "中短债",
          "value": "中短债"
        },
        {
          "label": "指数",
          "value": "指数"
        },
        {
          "label": "指数增强",
          "value": "指数增强"
        },
        {
          "label": "QDII",
          "value": "QDII"
        },
        {
          "label": "货币",
          "value": "货币"
        },
        {
          "label": "fof",
          "value": "fof"
        }
      ],
      outType: ['all'],
      outTypeList: [
        {
          "label": "全部类型",
          "value": "all"
        },
        {
          "label": "混合型基金",
          "value": "混合型基金"
        },
        {
          "label": "债券型基金",
          "value": "债券型基金"
        },
        {
          "label": "短期理财债券型",
          "value": "短期理财债券型"
        },
        {
          "label": "股票型基金",
          "value": "股票型基金"
        },
        {
          "label": "货币式基金",
          "value": "货币式基金"
        },
        {
          "label": "商品基金",
          "value": "商品基金"
        },
        {
          "label": "基金中基金(FOF)",
          "value": "基金中基金(FOF)"
        },
        {
          "label": "QDII",
          "value": "QDII"
        }
      ],
      poollist: []
    };
  },
  mounted () {
    this.getTypeList();
  },
  methods: {
    resolveList () {
      return { mtytype: this.mtyType, csrctype: this.outType, poollist: this.poollist };
    },
    getList ({ mtytype, csrctype, poollist }) {
      // console.log(mtytype, csrctype, poollist, 'sssssssssss')
      this.mtyType = mtytype || ['all'];
      this.outType = csrctype || ['all'];
      this.poollist = poollist || ['all'];
      // console.log(this.mtyType, this.outType, this.poollist, 'sssssssssss')

      this.$refs['alphachoosepool'].getData(this.poollist);
    },
    changepool (val) {
      this.poollist = val;
      this.$emit('changetype', '')

    },
    async getTypeList () {
      let data = await getTypeList();
      if (data?.mtycode == 200) {
        this.outTypeList = data?.data.csrctype.map((item) => {
          return {
            label: item,
            value: item
          };
        });
        this.outTypeList.unshift({
          label: '全部类型',
          value: 'all'
        });
        this.outType = this.outType?.length > 0 ? this.outType : ['all'];
        this.mtyTypeList = data?.data.mtytype.map((item) => {
          return {
            label: item,
            value: item
          };
        });
        this.mtyTypeList.unshift({
          label: '全部类型',
          value: 'all'
        });
        this.mtyType = this.mtyType?.length > 0 ? this.mtyType : ['all'];

      }
    },
    handleCheckOutChange (val) {
      this.checkedOut = val
        ? this.outTypeList.map((item) => {
          return item.label;
        })
        : [];
      this.isIndeterminate = false;
    },
    handleCheckMtyChange (val) {
      this.checkMty = val
        ? mtyTypeList.map((item) => {
          return item.label;
        })
        : [];
      this.isIndeterminate = false;
    },
    changetype (val) {
      if (val == 'out') {
        if (this.outType[this.outType.length - 1] == 'all') {
          this.outType = ['all'];
        } else {
          if (this.outType.includes('all')) {
            this.outType.splice(this.outType.indexOf('all'), 1);
          }
        }
      } else {
        if (this.mtyType[this.mtyType.length - 1] == 'all') {
          this.mtyType = ['all'];
        } else {
          if (this.mtyType.includes('all')) {
            this.mtyType.splice(this.mtyType.indexOf('all'), 1);
          }
        }
      }
      this.$emit('changetype', '')

    }
  }
};
</script>

<style></style>
