<template>
  <div class="plate-wrapper valuation-percentile-wrapper">
    <VerticalLineHeader title="仓位时序图"
                        showDownloadBtn
                        @downloadClick="exportExcel">
      <template slot="right">
        <el-select v-model="fundValue"
                   clearable
                   placeholder="请选择基金类型"
                   style="margin-right: 12px"
                   @change="fundChange">
          <el-option v-for="item in fundOptions"
                     :key="item.value"
                     :label="item.label"
                     :value="item.value"> </el-option>
        </el-select>
        <el-select v-model="comparisonValue"
                   clearable
                   placeholder="请选择基金公司"
                   style="margin-right: 12px"
                   @change="companyChange"
                   filterable>
          <el-option v-for="item in options"
                     :key="item.value"
                     :label="item.label"
                     :value="item.value"> </el-option>
        </el-select>
        <div style="margin-right: 12px">
          <el-radio-group class="radio-group-wrapper"
                          v-model="weight"
                          size="small"
                          style="margin-left: 0 !important"
                          @input="weightChange">
            <el-radio-button label="scaleWeighted">规模加权</el-radio-button>
            <el-radio-button label="equivalency">等权</el-radio-button>
          </el-radio-group>
        </div>
        <QuickTimePicker v-model="preset_time"
                         @change="handleFormChange"></QuickTimePicker>
      </template>
    </VerticalLineHeader>
    <div class="select-form-wrapper">
      <div class="select-form-wrapper">
        <RadioGroup ref="RadioGroup"
                    class="radio-group-wrapper"
                    :defaultValue="defaultValue"
                    :configList="configList"
                    selectOnClick
                    @change="handleTypeChange"></RadioGroup>
      </div>
      <!-- <el-button type="info"
                 plain>平均持有仓位：{{ average }}%</el-button> -->
    </div>
    <lineBarChart ref="fund-performance-board-chart-container"
                  @getAver="getAver"
                  @getChartData="getChartData"></lineBarChart>
  </div>
</template>
<script>
import VerticalLineHeader from './VerticalLineHeader.vue';
import RadioGroup from './RadioGroup.vue';
import lineBarChart from './chart/lineBarChart.vue';
import QuickTimePicker from './QuickTimePicker.vue';
import { getFundType, getFundCompany, getFundCode, getPositionConfiguration } from '../../../../api/pages/tkAnalysis/captial-market';
import { filter_json_to_excel } from '@/utils/exportExcel.js';
export default {
  name: 'ThePositionTimingChartPlate',
  components: {
    RadioGroup,
    VerticalLineHeader,
    lineBarChart,
    QuickTimePicker
  },
  data () {
    return {
      average: 0,
      weight: 'scaleWeighted',
      preset_time: {
        radioValue: '1'
      },
      fundValue: '',
      options: [],
      fundOptions: [],
      defaultValue: {
        radioValue: 'industry'
        // selectValue:{name:'动态市盈率',value:'pe'}
      },
      subType: '',
      comparisonValue: '',

      configList: [
        // { type: 'select', value: '', label: 'type', text: '类型', option: [{ label: '类型' }] },
        { type: 'select', value: '', label: 'industry', text: '行业', option: [{ label: '全部行业', value: [] }] },
        { type: 'select', value: '', label: 'theme', text: '主题', option: [{ label: '全部主题', value: [] }] },
        { type: 'select', value: '', label: 'optionalPool', text: '自选池', option: [{ label: '全部自选池', value: [] }] },
        { type: 'select', value: '', label: 'taikang', text: '泰康分类', option: [{ label: '全部泰康分类', value: [] }] },
        { type: 'select', value: '', label: 'style', text: '风格', option: [{ label: '全部风格', value: [] }] }
      ],
      selectVal: {
        industry: [],
        theme: [],
        type: [],
        scale: [],
        TKIndex: [],
        style: []
      },
      fundTypeKeyList: {
        industryList: {
          key: 'industry',
          label: '行业'
        },
        themeList: {
          key: 'theme',
          label: '主题'
        },
        typeList: {
          key: 'type',
          label: '类型'
        },
        scaleList: {
          key: 'scale',
          label: '规模'
        },
        TKIndexList: {
          key: 'taikang',
          label: '泰康自定义行业'
        },
        styleList: {
          key: 'style',
          label: '风格'
        }
      },
      legendName: {
        indicatorPoints: '组合累计净值',
        ttm: '沪深300',
        dividedIntoPoints: '超额收益累计',
        positiveStandardDeviation: '标准差（+1）',
        negativeStandardDeviation: '标准差（-1）',
        average: '平均值'
      },
      tableHeader: [
        {
          prop: 'date',
          label: '日期',
          formatter: (val) => {
            return stringTool.fix2pxx(val);
          }
        },
        {
          prop: 'stockPosition',
          label: '权益仓位'
        },
        {
          prop: 'point',
          label: '指数',
          formatter: (val) => {
            return stringTool.fix2pxx(val);
          }
        }
      ],
      tableData: []
    };
  },
  async mounted () {
    await this.getFundCode();
    await this.getFundType('industry', '');
    if (this.localStorage.getItem('ThePositionTimingChartPlate')) {
      let key_list = ['firstType', 'subType', ' fundValue', 'comparisonValue', 'weight', 'preset_time', 'defaultValue'];
      for (let key of key_list) {
        this[key] = this.localStorage.getItem('ThePositionTimingChartPlate')?.[key] || this[key];
      }
      let index = this.configList.findIndex((v) => v.label == this.defaultValue.radioValue);
      this.$set(this.configList, index, { ...this.configList[index], value: this.defaultValue.selectValue });
      this.$refs['RadioGroup'].setValue(this.defaultValue);
    }

    this.getPositionConfiguration();
  },
  methods: {
    getChartData ({ date_list, data1, data2, date_list2 }) {
      let arr1 = date_list.map((item, index) => {
        return {
          date: item,
          stockPosition: data1[index]
        };
      });
      let arr2 = date_list2.map((item, index) => {
        return {
          date: item,
          point: data2[index]
        };
      });
      this.tableData = [...arr1, ...arr2];
    },
    // 导出excel
    exportExcel () {
      let list = this.tableHeader.map((item) => {
        return {
          ...item,
          value: item.prop,
          format: ''
        };
      });
      filter_json_to_excel(list, this.tableData, '仓位时序图');
    },
    getAver (val) {
      this.average = val || '';
    },
    selectChange (val, key) {
      for (const key2 in this.selectVal) {
        if (key2 !== key) {
          this.selectVal[key2] = [];
        }
      }
      this.firstType = key;
      this.subType = val;
      this.fundValue = '';
      this.comparisonValue = '';
      this.getFundType();
      this.getPositionConfiguration();
    },
    handleTypeChange (value) {
      this.defaultValue = value;
      //重新设置chart
      console.log(value);
      this.firstType = value.radioValue;
      this.subType = [value?.selectValue?.value];
      // this.form.type = value.radioValue;
      // this.form.subType = [value?.selectValue?.value];
      this.getPositionConfiguration();
    },
    fundChange () {
      // this.getFundCompany();
      this.getPositionConfiguration();
    },
    companyChange () {
      this.getPositionConfiguration();
    },
    weightChange () {
      this.getPositionConfiguration();
    },
    getPositionConfiguration () {
      this.$nextTick(() => {
        let optDate = this.preset_time?.radioValue === 'custom' ? '' : this.preset_time?.radioValue;
        let { startDate, endDate } = this.preset_time || {};
        let chartDom = this.$refs['fund-performance-board-chart-container'];
        this.localStorage.setItem('ThePositionTimingChartPlate', {
          firstType: this.firstType,
          subType: this.subType,
          fundValue: this.fundValue,
          comparisonValue: this.comparisonValue,
          weight: this.weight,
          preset_time: this.preset_time,
          defaultValue: this.defaultValue
        });
        chartDom?.getData({
          type: this.firstType || 'industry',
          subType: this.subType || [],
          windType: this.fundValue,
          company: this.comparisonValue,
          weight: this.weight,
          optDate,
          startDate,
          endDate
        });
      });
    },
    async getFundCode () {
      let params = {
        deadline: ''
      };
      let req = await getFundCode(params);
      let { data, code, message } = req || {};
      if (code == 200) {
        // {type:'select',value:'',label:'type',text:'类型',option:[{label:'类型'}]},
        // {type:'select',value:'',label:'industry',text:'行业',option:[{label:'行业'}]},
        // {type:'select',value:'',label:'theme',text:'主题',option:[{label:'主题'}]},
        // {type:'select',value:'',label:'pool',text:'自选池',option:[{label:'自选池'}]},
        // {type:'select',value:'',label:'taikang',text:'泰康分类',option:[{label:'泰康分类'}]},
        // {type:'select',value:'',label:'style',text:'风格',option:[{label:'风格'}]}],
        this.configList = this.configList.map((item) => {
          let dataList = data[item.label + 'List'] || [];
          let curOption = [];
          if (item.label == 'optionalPool') {
            curOption = dataList.map((item) => {
              return {
                label: item.name,
                value: { name: item.name, value: item.id }
              };
            });
          } else {
            curOption = this.dulConfigOption(dataList);
          }
          item.option.push(...curOption);
          return item;
        });
      } else {
      }
    },
    dulConfigOption (dataList) {
      // {label:'动态市盈率',value:{name:'动态市盈率',value:'pe'}},
      // {label:'静态市盈率',value:{name:'静态市盈率',value:'staticState_pe'}},
      // {label:'滚动市盈率',value:{name:'滚动市盈率',value:'trends_pe'}},
      return dataList.map((item) => {
        return {
          label: item,
          value: { name: item, value: item }
        };
      });
    },
    async getFundType (type, subType) {
      await getFundType({
        type: this.firstType || 'industry',
        subType: this.subType || ''
      })
        .then((res) => {
          if (res.code === 200) {
            this.fundOptions =
              res.data.windType.map((v) => {
                return { label: v, value: v };
              }) || [];
            this.getFundCompany();
          }
        })
        .catch(() => { });
    },
    getFundCompany (type, subType) {
      getFundCompany({
        type: this.firstType || 'industry',
        subType: this.subType || '',
        windType: this.fundValue || ''
      })
        .then((res) => {
          if (res.code === 200) {
            this.options =
              res.data.company.map((v) => {
                return { label: v, value: v };
              }) || [];
          }
        })
        .catch(() => { });
    },
    handleFormChange (val) {
      this.getPositionConfiguration();
    }
  }
};
</script>
<style lang="scss" scoped>
.detail-download {
	width: 32px;
	height: 32px;
	line-height: 32px;
	text-align: center;
	border: 1px solid rgba(217, 217, 217, 1);
	border-radius: 2px;
	box-sizing: border-box;
	vertical-align: middle;
	img {
		// display: block;
	}
}
.select-form-wrapper {
	display: flex;
	justify-content: space-between;
}
</style>
