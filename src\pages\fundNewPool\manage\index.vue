<template>
  <div class="chart_one">
    <div class="box_Board">
      <div class="header_box">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>池管理</el-breadcrumb-item>
          <el-breadcrumb-item>{{ isdb == 0 ? '基金' + blank + '池' : '对标池' }}</el-breadcrumb-item>
        </el-breadcrumb>
        <div class="border_table">
          <div class="flex_start"
               style="margin-bottom: 16px">
            <el-button :style="blank == '经理' ? 'margin-right: 12px' : 'margin-right: 12px'"
                       type="primary"
                       @click="addFundPool">新增基金{{ blank }}池</el-button>
            <el-tooltip v-if="blank != '经理'"
                        :disabled="active_item.length != 0"
                        content="至少勾选一个池子"
                        placement="top"
                        effect="light">
              <!-- <div>
                <el-button :disabled="active_item.length == 0"
                           class="mr-12"
                           type="primary"
                           @click="createPortfolio">创建组合</el-button>
              </div> -->
            </el-tooltip>
            <el-tooltip :disabled="active_item.length != 0"
                        content="至少勾选一个池子"
                        placement="top"
                        effect="light">
              <div>
                <el-popover placement="bottom"
                            width="160">
                  <p>请选择前往的筛选器</p>
                  <div style="text-align: right; margin: 0">
                    <el-button size="mini"
                               type="primary"
                               @click="gotoFilter('alpha')">alpha</el-button>
                    <el-button type="primary"
                               size="mini"
                               @click="gotoFilter('beta')">beta</el-button>
                  </div>
                  <el-button :disabled="active_item.length == 0"
                             class="mr-12"
                             type="primary"
                             slot="reference">基金{{ blank }}筛选</el-button>
                </el-popover>
              </div>
            </el-tooltip>
            <el-tooltip :disabled="active_item.length > 2 && active_item.length < 5"
                        content="勾选2-5个池子"
                        placement="top"
                        effect="light">
              <div>
                <el-button :disabled="active_item.length < 2 || active_item.length > 5"
                           class="mr-12"
                           type="primary"
                           @click="mergePool">合并基金{{ blank }}池</el-button>
              </div>
            </el-tooltip>
          </div>
          <el-table v-loading="loading"
                    :data="data"
                    class="managed-pool"
                    style="width: 100%; min-height: calc(100vh - 421px)"
                    @selection-change="handleSelectionChange"
                    :row-key="getRowKey">
            <el-table-column label
                             align="center"
                             width="50px">
              <template slot-scope="{ row }">
                <div style="border-radius: 50%; border: 1px solid; width: 20px; height: 20px; line-height: 20px; text-align: center"
                     v-if="active_item.findIndex((v) => v.id == row.id) + 1 != 0">
                  {{ active_item.findIndex((v) => v.id == row['id']) + 1 }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="勾选比较"
                             type="selection"
                             align="center"
                             width="55px"
                             :reserve-selection="true"></el-table-column>

            <el-table-column v-for="item in allColumnList"
                             :key="item.value"
                             :prop="item.value"
                             :label="item.label"
                             :min-width="item.width"
                             align="gotoleft"
                             :show-overflow-tooltip="item.tooltip">
              <template slot-scope="{ row }">
                <el-link v-if="item.value == 'name'"
                         @click="goPoolDetail(row)">{{ row[item.value] }}</el-link>
                <span v-else-if="item.value == 'flags'"
                      :style="`${
										item.format(row[item.value]) == '是'
											? 'color:green'
											: item.format(row[item.value]) == '否'
											? 'color:red'
											: 'color:black'
									}`">{{ item.format ? item.format(row[item.value]) : row[item.value] }}</span>
                <span v-else>{{ item.format ? item.format(row[item.value]) : row[item.value] }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作"
                             align="gotoleft"
                             width="200px">
              <template slot-scope="{ row }">
                <div class="flex_start">
                  <!-- <el-link v-if="user_id == row.userId" class="mr-12" @click="setTags(row)">打标签</el-link> -->
                  <el-link v-if="user_id == row.userId"
                           class="mr-12"
                           @click="forwordPool(row)">转发</el-link>
                  <el-link v-if="user_id == row.userId"
                           class="mr-12"
                           @click="editPoolInfo(row)">编辑</el-link>
                  <el-popconfirm v-if="user_id == row.userId"
                                 title="确定删除吗？"
                                 @confirm="deletePool(row.id)">
                    <el-link slot="reference"
                             class="mr-12"
                             style="margin-top: -1px">删除</el-link>
                  </el-popconfirm>
                </div>
              </template>
            </el-table-column>
            <template slot="empty">
              <el-empty image-size="160"></el-empty>
            </template>
          </el-table>
          <el-pagination background
                         style="display: flex; justify-content: right; padding-top: 16px; padding-bottom: 24px"
                         @size-change="handleSizeChange"
                         @current-change="handleCurrentChange"
                         :current-page.sync="currentPage"
                         :page-sizes="[10, 20, 40, 60, 80, 100]"
                         :page-size="pageSIze"
                         layout="total, sizes, prev, pager, next, jumper"
                         :total="allData.length"></el-pagination>
        </div>
        <merge-pool ref="mergePool"
                    :ismanager="isMangerPool"
                    :isdb="isdb"
                    :userList="userList"
                    :industryList="industryList"
                    @resolveFather="getPoolList"></merge-pool>
        <add-fund-pool ref="addFundPool"
                       :ismanager="isMangerPool"
                       :isdb="isdb"
                       :userList="userList"
                       @resolveFather="getPoolList"></add-fund-pool>
        <create-portfolio ref="createPortfolio"
                          v-if="!ismanager"
                          :isdb="isdb"
                          :userList="userList"
                          @resolveFather="getPoolList"></create-portfolio>
        <edit-pool-info ref="editPoolInfo"
                        :ismanager="isMangerPool"
                        :isdb="isdb"
                        :userList="userList"
                        @resolveFather="getPoolList"></edit-pool-info>
        <forword-pool ref="forwordPool"
                      :ismanager="isMangerPool"
                      :isdb="isdb"
                      :userList="userList"
                      @resolveFather="getPoolList"></forword-pool>
      </div>
    </div>
  </div>
</template>

<script>
// 新建基金池
import addFundPool from './components/addFundPool.vue';
// 创建组合
import createPortfolio from './components/createPortfolio.vue';
// 合并基金池
import mergePool from './components/mergePool.vue';
// 编辑基金池
import editPoolInfo from './components/editPoolInfo.vue';
// 转发基金池
import forwordPool from './components/forwordPool.vue';
// /pool/fund_pool/
import { getUserList } from '@/api/pages/Tools.js';

import { getPoolList, deletePool, postPoolInfo } from '@/api/pages/tools/pool.js';

import { alphamsg } from '@/api/pages/SystemAlpha.js';

export default {
  components: {
    mergePool,
    addFundPool,
    createPortfolio,
    editPoolInfo,
    forwordPool
  },

  data () {
    return {
      loading: false,
      allData: [],
      currentPage: 1,
      pageSIze: 10,
      user_id: localStorage.getItem('id'),
      data: [],
      active_item: [],
      allColumnList: [
        {
          label: '名称',
          value: 'name',
          tooltip: true
        },
        {
          label: '创建时间',
          value: 'startDate',
          format: this.fix_empty,
          tooltip: true
        },
        {
          label: '更新时间',
          value: 'endDate',
          format: this.fix_empty,
          tooltip: true
        },
        {
          label: '说明',
          value: 'description',
          tooltip: true
        },
        {
          label: '创建人',
          value: 'userName',
          tooltip: true
        },
        {
          label: '公开',
          value: 'ispublic',
          format: this.fix_is
        },
        // {
        //   label: "是否对标池",
        //   value: "status3",
        //   format: this.fix_is
        // },
        {
          label: '定更状态',
          value: 'status',
          format: this.fix_is
        },
        {
          label: '定时更新跨度',
          value: 'flags',
          format: this.fix_empty
        }
      ],
      userList: [],
      industryList: [],
      isdb: 0,
      isMangerPool: false,
      blank: ''
    };
  },
  watch: {
    //监听相同路由下参数变化的时候，从而解决当跳转到同页面不刷新问题
    $route (to, from) {
      this.init();
    }
  },
  mounted () {
    this.init();
  },
  methods: {
    setTags (row) {
      this.$prompt('请输入标签名称', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(({ value }) => {
          this.$axios({
            url: this.$baseUrl + '/Analysis/PoolTag/',
            method: 'post',
            data: { pool_id: row.id, tag: value },
            headers: { 'Content-Type': 'application/json' }
          }).then((res) => {
            if (res.data.mtycode == 200) {
              this.$message({
                type: 'success',
                message: '设置成功'
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '取消输入'
          });
        });
      // 这里没有服务器供大家尝试，可将下面上传接口替换为你自己的服务器接口
    },
    // 初始化
    init () {
      this.isdb = this.$route.path == '/poolnormal' || this.$route.path == '/managerpoolnormal' ? 0 : 1;
      if (this.$router?.history?.current?.path) {
        if (this.$router?.history?.current?.path.indexOf('manage') < 0) {
          this.isMangerPool = false;
          this.blank = '';
        } else {
          this.isMangerPool = true;
          this.blank = '经理';
        }
      }
      this.getPoolList();
      this.getUserList();
      this.getAlphamsg();
    },
    // 获取行业列表
    async getAlphamsg () {
      let data = await alphamsg();
      let level1 = [];
      let level2 = [];
      let level3 = [];
      data.data.industry_tree.map((industry1) => {
        //  一级行业
        level1.push({
          label: industry1.industry_code,
          value: industry1.industry_name
        });
        industry1.children.map((industry2) => {
          //  二级行业
          level2.push({
            label: industry2.industry_code,
            value: industry2.industry_name
          });
          industry2.children.map((industry3) => {
            //  二级行业
            level3.push({
              label: industry3.industry_name,
              value: industry3.industry_code
            });
          });
        });
      });
      this.industryList = [
        { label: '申万(2021)', children: level1 },
        { label: '申万二级(2021)', children: level2 },
        { label: '申万三级(2021)', children: level3 }
      ];
    },
    // 获取用户列表
    async getUserList () {
      let data = await getUserList();
      if (data?.mtycode == 200) {
        this.formatUserList(data?.data);
      }
    },
    // 格式化用户列表
    formatUserList (data) {
      let userList = [];
      data?.map((item) => {
        let index = userList.findIndex((obj) => {
          return obj.value == item.institute_id;
        });
        if (index == -1) {
          userList.push({
            value: item.institute_id,
            label: item.name,
            children: [
              {
                value: item.id,
                label: item.username
              }
            ]
          });
        } else {
          let i = userList[index].children.findIndex((obj) => {
            return obj.value == item.id;
          });
          if (i == -1) {
            userList[index].children.push({
              value: item.id,
              label: item.username
            });
          }
        }
      });
      this.userList = data.map((item) => {
        return {
          value: item.id,
          label: item.name
        };
      }); //userList;
    },
    // 获取基金池列表
    async getPoolList () {
      this.loading = true;
      let data = await getPoolList({ flag: 5, ismanager: this.isMangerPool }); //GD
      if (data?.mtycode == 200) {
        if (this.isdb == 1) {
          // this.allData = ;
          this.$set(
            this,
            'allData',
            data.data.filter((v) => v.status3 === 1)
          );
          // console.log(data.data.filter(v => v.status3 === 1))
        } else {
          this.$set(
            this,
            'allData',
            data.data.filter((v) => v.status3 === 0)
          );
          // this.allData = ;
          // console.log(data.data.filter(v => v.status3 === 0))
        }
        if (this.isMangerPool) {
          this.allData = this.allData.filter((v) => v.flag == 'manager');
        } else {
          this.allData = this.allData.filter((v) => v.flag == 'fund');
        }
        // console.log(this.allData, this.isdb);
        this.filterData();
      }
      this.loading = false;
    },
    // 数据分页处理
    filterData () {
      this.data = this.allData.slice((this.currentPage - 1) * this.pageSIze, this.currentPage * this.pageSIze);
    },
    // 删除基金池
    async deletePool (id) {
      this.active_item.splice(
        this.active_item.findIndex((v) => v.id == id),
        1
      );
      let data = await deletePool({ id: id, ismanager: this.isMangerPool }); //GD
      this.$message.success('删除成功');
      this.getPoolList();
    },
    // 新增基金池
    addFundPool () {
      this.$refs['addFundPool'].getData();
    },
    // 创建组合
    createPortfolio () {
      this.$refs['createPortfolio'].getData(this.active_item);
    },
    // 转发基金池
    forwordPool (row) {
      this.$refs['forwordPool'].getData(row);
    },
    // 编辑基金池
    editPoolInfo (row) {
      this.$refs['editPoolInfo'].getData(row);
    },
    // 合并基金池
    mergePool () {
      this.$refs['mergePool'].getData(this.active_item);
    },
    // 前往池子详情
    goPoolDetail (row) {
      this.localStorage.setItem('NowPoolMSG', JSON.stringify({ datas: row }));
      // localStorage.setItem(JSON.stringify({ 'datas': row }), 'NowPoolMSG')
      this.$router.replace({
        path: '/poolDetail/' + row.id,
        hash: '',
        query: {
          id: row.id,
          ismanager: this.isMangerPool, //GD
          name: row.name,
          ListLength: row.userId,
          isdb: row.status3,
          index_code: row.indexCode,
          children: row.children == undefined ? 1 : 0,
          type: row?.type || ''
        }
      });
    },
    // 前往筛选
    gotoFilter (val) {
      let idlist = [];
      for (let i = 0; i < this.active_item.length; i++) {
        idlist.push(this.active_item[i].id);
      }
      this.$router.push({
        path: this.isMangerPool ? 'alphaHeadermanager' : '/alphaHeader',
        query: {
          code: idlist,
          type: val
        }
      });
    },
    // 监听表格勾选
    handleSelectionChange (val) {
      this.active_item = val;
    },
    getRowKey (row) {
      return row.id;
    },
    handleSizeChange (val) {
      this.pageSIze = val;
      this.filterData();
    },
    handleCurrentChange (val) {
      this.currentPage = val;
      this.filterData();
    },
    fix_time (val) {
      return val.slice(0, 10);
    },
    fix_is (val) {
      return val ? '是' : '否';
    },
    fix_empty (val) {
      return val;
    }
  }
};
</script>

<style lang="scss" scoped>
.box_Board {
	padding: 0 24px 16px 24px;
	overflow: auto;
	// 面包屑
	.header_box {
		margin-top: 16px;
		margin-bottom: 16px;
		font-size: 14px;

		.header_inactive {
			font-size: 14px;
			font-weight: 400;
			line-height: 22px;
			text-align: left;
			color: rgba(0, 0, 0, 0.45);
		}
	}

	// 表格区域
	.border_table {
		margin-top: 16px;
		padding: 16px 24px;
		background: white;
		// 表格区域头部
		.border_table_header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 20px;

			.border_table_header_title {
				color: rgba(0, 0, 0, 0.85);
				text-align: center;
				font-size: 16px;
				font-style: normal;
				font-weight: 500;
				line-height: 24px;
			}
		}

		// 分页
		.pagination_board {
			text-align: right;
			margin-top: 16px;
		}
	}
}

// .managed-pool {
// 	::v-deep.cell {
// 		display: flex;
// 	}
// }
</style>
