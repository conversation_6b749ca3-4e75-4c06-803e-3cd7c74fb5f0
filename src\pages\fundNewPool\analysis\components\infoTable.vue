<template>
  <div class=" info_table"
       style="position: relative">
    <div class="flex_between mb-16">
      <div title>{{ info.name }}</div>
    </div>
    <div class="flex_between mb-16">
      <div class="flex_start">
        <div v-if="is_self_manage">
          <el-dropdown @command="onCommandFunction">
            <el-button type="primary"
                       class="mr-12">自动生成子池</el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-if="!ismanager"
                                command="type">按类型</el-dropdown-item>
              <el-dropdown-item command="company">按公司</el-dropdown-item>
              <el-dropdown-item command="valuegrowth">按成长价值</el-dropdown-item>
              <el-dropdown-item command="bigsmall">按大小盘</el-dropdown-item>
              <el-dropdown-item command="industrysection">按大行业</el-dropdown-item>
              <el-dropdown-item command="filter">使用筛选模板</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
        <div v-for="item in btn_list"
             :key="item.label"
             class="mr-12"
             v-show="is_self_manage ? true : item.is_self_manage">
          <el-button v-if="item.show_type == 'button'"
                     type="primary"
                     @click="item.method"
                     :disabled="item.disabled">
            {{
            item.label
            }}
          </el-button>
          <div v-else>
            <span v-if="item.label == '季度选择'">持仓季度：</span>
            <el-select v-model="form[item.value]"
                       :key="'select' + item.label"
                       :placeholder="item.label"
                       :multiple="item.multiple"
                       :collapse-tags="item.multiple"
                       @change="item.method">
              <el-option v-for="obj in item.options"
                         :key="obj.value"
                         :label="obj.label"
                         :value="obj.value"></el-option>
            </el-select>
          </div>
        </div>
      </div>
      <div class="flex_start">
        <div v-show="is_self_manage"
             class="flex_start mr-12">
          <div style="width: 80px">基准选择</div>
          <search-components ref="searchComponent"
                             type="index"
                             select-style="width: 100%"
                             placeholder="输入简拼、代码、名称查询指数"
                             @resolveFather="getIndexInfo"
                             :disabled="!is_self_manage"></search-components>
        </div>
        <div v-if="is_self_manage"
             class="mr-12">
          <search-components :type="ismanager?'manager':'fund'"
                             select-style="width: 216px"
                             @resolveFather="getFundInfo"></search-components>
        </div>
        <div class="mr-12"
             v-if="is_self_manage&&!ismanager">
          <el-button @click="putManyFunds">批量导入</el-button>
        </div>
        <el-dropdown>
          <span class="el-dropdown-link"
                style>
            <i class="el-icon-more"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <div @click="exportExcel">导出Excel</div>
            </el-dropdown-item>
            <!-- <el-dropdown-item>导出Word</el-dropdown-item> -->
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
    <div v-if="info['isdb'] == 1"
         class="flex_start mb-16">
      <span>
        对标基金{{ismanager?'经理':''}}
        <i class="el-icon-edit"></i>:
      </span>
      <div class="flex_start ml-12"
           v-for="v in all_data.filter((a) => a.flag == 1)"
           :key="v.code">
        <div :style="`width:12px;height:12px;background:#4096ff`"
             class="mr-8"></div>
        <div style="width: 94px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap">{{ v.name }}</div>
      </div>
    </div>
    <div class="flex_start mb-16">
      <span style="min-width: 64px">
        关注基金{{ismanager?'经理':''}}
        <i class="el-icon-edit"></i>:
      </span>
      <div class="flex_start ml-12"
           style="flex-wrap: wrap">
        <div class="flex_start mr-8"
             v-for="v in care_fund"
             :key="v.code">
          <div :style="`width:12px;height:12px;background:${v['color']}`"
               class="mr-8"></div>
          <div style="width: 94px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap">{{ v.name }}</div>
        </div>
      </div>
    </div>
    <el-table ref="multipleTablePool"
              class="drag-container"
              v-loading="loading"
              :data="data"
              style="width: 100%"
              :height="`${height}px`"
              row-key="code"
              @selection-change="handleSelectionChange"
              @sort-change="sortChange">
      <el-table-column label="勾选比较"
                       type="selection"
                       align="center"
                       width="55px"
                       :reserve-selection="true"></el-table-column>
      <el-table-column v-for="item in column"
                       :key="item.value"
                       :prop="item.value"
                       :label="item.label"
                       :min-width="item.width"
                       align="gotoleft"
                       :sortable="item.sortable">
        <el-table-column v-show="item.children"
                         v-for="obj in item.children"
                         :key="obj.value"
                         :prop="obj.value"
                         :label="obj.label"
                         :min-width="item.width"
                         align="gotoleft"
                         :sortable="obj.sortable">
          <template #header>
            <long-table-popover-chart v-if="obj.popover"
                                      :data="formatTableData()"
                                      date_key="code"
                                      :data_key="obj.value"
                                      :show_name="obj.label">
              <div>{{ obj.label }}</div>
            </long-table-popover-chart>
            <div v-else>{{ obj.label }}</div>
          </template>
          <template slot-scope="{ row }">
            <div v-html="obj.format ? obj.format(row[obj.value]) : row[obj.value]"></div>
          </template>
        </el-table-column>
        <template #header>
          <long-table-popover-chart v-if="item.popover"
                                    :data="formatTableData()"
                                    date_key="code"
                                    :data_key="item.value"
                                    :show_name="item.label">
            <div v-if="item.value == '适应性排名'">
              <div class="flex_start">
                <span>适应性排名</span>
                <!-- <span>
                  <i class="el-icon-warning-outline"></i>
                </span>
                <span @click.stop="openDialog">
                  <i class="el-icon-setting"></i>
                </span> -->
              </div>
            </div>
            <div v-else-if="item.value == '持仓抱团度'">
              <div class="flex_start">
                <span>持仓抱团度</span>
                <span>
                  <i class="el-icon-warning-outline"></i>
                </span>
              </div>
            </div>
            <div v-else>{{ item.label }}</div>
          </long-table-popover-chart>
          <div v-else>{{ item.label }}</div>
        </template>
        <template slot-scope="{ row }">
          <div v-if="item.value == 'name'"
               class="overflow_ellipsis flex_start">
            <div v-show="row['flag'] == 2"
                 :style="`width:12px;height:12px;background:${row['color']}`"
                 class="mr-8"></div>
            <div v-show="row['flag'] == 1"
                 class="flex_start">
              <div :style="`width:12px;height:12px;background:#4096ff`"
                   class="mr-8"></div>
              <div class="mr-8 flex_start">
                <svg width="14"
                     height="14"
                     viewBox="0 0 14 14"
                     fill="none"
                     xmlns="http://www.w3.org/2000/svg">
                  <path d="M12.4156 4.82735L8.94433 4.32286L7.39258 1.17696C7.35019 1.09083 7.28047 1.0211 7.19433 0.978716C6.97832 0.872075 6.71582 0.960943 6.60781 1.17696L5.05605 4.32286L1.58476 4.82735C1.48906 4.84102 1.40156 4.88614 1.33457 4.9545C1.25358 5.03774 1.20895 5.14973 1.21049 5.26586C1.21203 5.38199 1.25961 5.49276 1.34277 5.57383L3.8543 8.02247L3.26094 11.4801C3.24702 11.5605 3.25592 11.6432 3.28663 11.7189C3.31733 11.7945 3.36862 11.86 3.43466 11.908C3.50071 11.9559 3.57887 11.9845 3.66029 11.9903C3.74171 11.9961 3.82313 11.9789 3.89531 11.9408L7.00019 10.3084L10.1051 11.9408C10.1898 11.9859 10.2883 12.001 10.3826 11.9846C10.6205 11.9436 10.7805 11.718 10.7395 11.4801L10.1461 8.02247L12.6576 5.57383C12.726 5.50684 12.7711 5.41934 12.7848 5.32364C12.8217 5.08438 12.6549 4.8629 12.4156 4.82735Z"
                        fill="#FFD600" />
                </svg>
              </div>
            </div>
            <el-link @click="alphaGo(row.code, row.name)">{{ row[item.value] }}</el-link>
          </div>
          <div v-else-if="item.value == 'manager_name'">
            <el-link @click="alphaGo(row.manager_code, row.manager_name)">{{ row[item.value] }}</el-link>
          </div>
          <div v-else>{{ item.format ? item.format(row[item.value]) : row[item.value] }}</div>
        </template>
      </el-table-column>
      <el-table-column align="gotoleft"
                       width="128px">
        <template #header>
          <div class="flex_between"
               style="width: 128px">
            <div>操作</div>
            <div>
              <table-column ref="tableColumn"
                            @resolveFather="getChangeColumnList"></table-column>
            </div>
          </div>
        </template>
        <template slot-scope="scope">
          <div class="flex_start">
            <el-link v-if="scope.row.flag == 0"
                     class="mr-16"
                     @click="updateStatus(scope.row, 2)">设为关注</el-link>
            <el-link v-else-if="scope.row.flag == 2"
                     class="mr-16"
                     @click="updateStatus(scope.row, 0)">取消关注</el-link>
            <div v-if="scope.row.flag != 1">
              <el-popconfirm title="确定删除吗？"
                             @confirm="deletePoolFund(scope)">
                <el-link slot="reference">删除</el-link>
              </el-popconfirm>
            </div>
          </div>
        </template>
      </el-table-column>
      <template slot="empty">
        <el-empty image-size="160"></el-empty>
      </template>
    </el-table>
    <div class="flex_between">
      <div class="pagination_text">共{{ all_data.length }}条数据</div>
      <div>
        <el-pagination background
                       style="display: flex; justify-content: right; padding-top: 16px; padding-bottom: 24px"
                       @size-change="handleSizeChange"
                       @current-change="handleCurrentChange"
                       :current-page.sync="pagination.page"
                       :page-sizes="[10, 20, 40, 60, 80, 100]"
                       :page-size="pagination.size"
                       layout="sizes, prev, pager, next, jumper"
                       :total="all_data.length"></el-pagination>
      </div>
    </div>
    <div class="drag-handle"
         ref="targeDiv"
         @mousedown="dragLine"></div>
    <alphaownpool v-if="!ismanager"
                  @submitExcel='postFunds'
                  @refrshtable="refrshtable"
                  ref="alphaownpoolrefs"></alphaownpool>
    <port-c ref="portC"
            :ismanager="ismanager"></port-c>
    <filter-pool-fund :ismanager="ismanager"
                      ref="filterPoolFund"
                      @resolveFather="createdSelf"></filter-pool-fund>
    <score-pool-fund :ismanager="ismanager"
                     ref="scorePoolFund"
                     @resolveFather="getScoreCondition"></score-pool-fund>
  </div>
</template>

<script>
import {
  getPoolInfo,
  postFunds,
  createdChildren,
  updateStatus,
  getPoolDateList,
  deletePoolFund
} from "@/api/pages/tools/pool.js";;

import { TypeMsg } from "@/api/pages/SystemAlpha.js";

import { filter_json_to_excel } from "@/utils/exportExcel.js";
import { alphaGo } from "@/assets/js/alpha_type.js";

import alphaownpool from "./components/alphaownpool";
import tableColumn from "./components/tableColumn.vue";
import searchComponents from "@/components/components/components/search/index.vue";
import portC from "@/components/components/components/portfoliocreat/index.vue";
import filterPoolFund from "./components/filterPoolFund.vue";
import scorePoolFund from "./components/scorePoolFund.vue";
export default {
  components: {
    tableColumn,
    alphaownpool,
    searchComponents,
    portC,
    filterPoolFund,
    scorePoolFund
  },
  data () {
    return {
      loading: true,
      height: 603,
      visible: false,
      isIndeterminate: true,
      info: {},
      form: { update_time: "" },
      choose_list: [],
      btn_list: [
        // {
        // 	label: '自动生成子池',
        // 	method: this.clickChildren,
        // 	disabled: false,
        // 	show_type: 'button'
        // },
        {
          label: "手动生成子池",
          method: this.handleCreatedSelf,
          disabled: true,
          is_self_manage: true,
          show_type: "button"
        },
        // {
        //   is_self_manage: false,
        //   label: "前往比较",
        //   method: this.goCompare,
        //   disabled: true,
        //   show_type: "button"
        // },
        // {
        //   label: "创建组合",
        //   method: this.createPool,
        //   disabled: true,
        //   show_type: "button"
        // },
        // {
        // 	label: '池子更新记录',
        // 	value: 'update_time',
        // 	method: this.changeTime,
        // 	disabled: false,
        // 	show_type: 'select',
        // 	options: [],
        // 	multiple: false
        // },
        {
          label: "季度选择",
          value: "quarter",
          // method: this.changeQuarter,
          disabled: false,
          show_type: "select",
          options: [],
          multiple: true
        },
        {
          label: "确认",
          method: this.changeQuarter,
          disabled: false,
          show_type: "button"
        }
      ],
      default_column_list: [
        {
          label: "基金名称",
          value: "name",
          show: true,
          is_default: true,
          have_data: true,
          width: "200px"
        },
        {
          label: "基金经理",
          width: "100px",
          show: false,
          is_default: false,
          have_data: false,
          value: "managerName"
        },
        {
          label: "成立日期",
          value: "founddate",
          show: true,
          is_default: true,
          have_data: true,
          width: "150px",
          sortable: "custom"
        },
        {
          label: "综合排名",
          value: "综合排名",
          show: true,
          width: "100px",
          is_default: true,
          have_data: true,
          sortable: "custom",
          popover: true,
          format: this.fix2p
        },
        {
          label: "适应性排名",
          value: "适应性排名",
          show: false,
          width: "150px",
          is_default: false,
          have_data: false,
          popover: true,
          sortable: "custom",
          format: this.fix2p
        },
        {
          label: "任职时长",
          value: "managed_time",
          show: true,
          width: "100px",
          is_default: true,
          have_data: true,
          popover: true,
          sortable: "custom",
          format: this.fix_year
        },
        {
          label: "基金规模",
          value: "netasset",
          show: true,
          width: "100px",
          is_default: true,
          have_data: true,
          popover: true,
          sortable: "custom",
          format: this.fix_money
        },
        {
          label: "持仓抱团度",
          value: "持仓抱团度",
          show: false,
          width: "120px",
          is_default: false,
          have_data: false,
          popover: true,
          sortable: "custom",
          format: this.fix2p
        },
        // {
        // 	label: '前五大集中度',
        // 	value: 'top5_concentration_rank',
        // 	show: false,
        // 	width: '120px',
        // 	is_default: false,
        // 	have_data: false,
        // 	popover: true,
        // 	sortable: 'custom',
        // 	format: this.fix2p
        // },
        {
          label: "前十大集中度",
          value: "top10ConcentrationRank",
          show: false,
          width: "120px",
          is_default: false,
          have_data: false,
          popover: true,
          sortable: "custom",
          format: this.fix2p
        },
        // {
        // 	label: '超中证800月胜率',
        // 	value: 'equity_win_ratio',
        // 	show: false,
        // 	width: '120px',
        // 	is_default: false,
        // 	have_data: false,
        // 	popover: true,
        // 	sortable: 'custom',
        // 	format: this.fix2p
        // },
        {
          label: "风险等级",
          value: "风险等级",
          show: false,
          width: "120px",
          is_default: false,
          have_data: false,
          sortable: "custom"
        },
        {
          label: "超指数胜率",
          value: "monthly_win_ratio",
          show: false,
          width: "120px",
          is_default: false,
          have_data: false,
          popover: true,
          sortable: "custom",
          format: this.fix2p
        }
      ],
      default_column_list_manager: [
        {
          label: "基金经理",
          width: "100px",
          show: true,
          is_default: false,
          have_data: false,
          value: "name"
        },
        {
          label: "基金经理代码",
          width: "150px",
          show: false,
          is_default: false,
          have_data: false,
          value: "code"
        },
        {
          label: "从业日期",
          value: "date",
          show: true,
          is_default: true,
          have_data: true,
          width: "150px",
          sortable: "custom"
        },
        {
          label: "综合排名",
          value: "综合排名",
          show: true,
          width: "100px",
          is_default: true,
          have_data: true,
          sortable: "custom",
          popover: true,
          format: this.fix2p
        },
        {
          label: "适应性排名",
          value: "适应性排名",
          show: false,
          width: "150px",
          is_default: false,
          have_data: false,
          popover: true,
          sortable: "custom",
          format: this.fix2p
        },
        {
          label: "任职时长",
          value: "managedTime",
          show: true,
          width: "100px",
          is_default: true,
          have_data: true,
          popover: true,
          sortable: "custom",
          format: this.fix_year
        },
        {
          label: "管理基金规模",
          value: "netasset",
          show: true,
          width: "100px",
          is_default: true,
          have_data: true,
          popover: true,
          sortable: "custom",
          format: this.fix_money
        },
        {
          label: "持仓抱团度",
          value: "持仓抱团度",
          show: false,
          width: "120px",
          is_default: false,
          have_data: false,
          popover: true,
          sortable: "custom",
          format: this.fix2p
        },
        // {
        // 	label: '前五大集中度',
        // 	value: 'top5_concentration_rank',
        // 	show: false,
        // 	width: '120px',
        // 	is_default: false,
        // 	have_data: false,
        // 	popover: true,
        // 	sortable: 'custom',
        // 	format: this.fix2p
        // },
        {
          label: "前十大集中度",
          value: "top10ConcentrationRank",
          show: false,
          width: "120px",
          is_default: false,
          have_data: false,
          popover: true,
          sortable: "custom",
          format: this.fix2p
        },
        // {
        // 	label: '超中证800月胜率',
        // 	value: 'equity_win_ratio',
        // 	show: false,
        // 	width: '120px',
        // 	is_default: false,
        // 	have_data: false,
        // 	popover: true,
        // 	sortable: 'custom',
        // 	format: this.fix2p
        // },
        // {
        //   label: "风险等级",
        //   value: "风险等级",
        //   show: false,
        //   width: "120px",
        //   is_default: false,
        //   have_data: false,
        //   sortable: "custom"
        // },
        {
          label: "超指数胜率",
          value: "monthly_win_ratio",
          show: false,
          width: "120px",
          is_default: false,
          have_data: false,
          popover: true,
          sortable: "custom",
          format: this.fix2p
        }
      ],
      pagination: {
        page: 1,
        size: 10
      },
      color_list: [
        // '#4096ff',
        "#4096ff",
        "#FD6865",
        "#946DFF",
        "#7388A9",
        "#6F80DD",
        "#CD8150",
        "#F6C243",
        "#83303D",
        "#6C96F2",
        "#FA541C",
        "#6F29C1",
        "#8FBA9C",
        "#D94F84",
        "#FD6865",
        "#88C9E9",
        "#ED589D",
        "#EE7C62",
        "#51A77D",
        "#4247AC",
        "#4990F7",
        "#4C97CC",
        "#CD72CA",
        "#377C96",
        "#773A84",
        "#ADD950",
        "#C28459",
        "#8C8C50",
        "#E94C1D"
      ],
      all_data: [],
      data: [],
      column: [],
      multipleSelection: [],
      care_fund: [],
      is_self_manage: false,
      isDragging: false,
      scrollHeight: 0,
      ismanager: false,
    };
  },
  props: {

  },
  methods: {
    dragLine (event) {
      this.isDragging = true;
      // this.height = event.clientY;
      // 添加全局事件监听
      window.addEventListener("mousemove", this.handleDrag);
      window.addEventListener("mouseup", this.stopDrag);
    },
    handleDrag (event) {
      if (this.isDragging) {
        let dom = document.querySelector(".content-box");
        this.scrollHeight = dom.scrollTop;
        this.height = this.scrollHeight + event.clientY - 329;
      }
    },
    stopDrag () {
      this.isDragging = false;
      // 移除全局事件监听
      window.removeEventListener("mousemove", this.handleDrag);
      window.removeEventListener("mouseup", this.stopDrag);
    },
    // 获取数据
    getData (info) {
      this.info = info;
      this.is_self_manage = (this.info.user_id == this.$store.state.id);
      this.$refs["searchComponent"].setDefaultValue(this.info.index_code);
      //   this.is_self_manage = this.info.user_id == localStorage.getItem("id");
      this.getDateList();
      // this.getPoolDateList();
      this.computedColumn();
      this.getPoolInfo();
      this.$nextTick(() => {
        this.$refs["tableColumn"]?.getData(
          this.ismanager
            ? this.default_column_list_manager
            : this.default_column_list
        );
      });
    },
    // 获取池子更新时间
    async getPoolDateList () {
      let data = await getPoolDateList({
        id: this.info.code,
        ismanager: this.ismanager
      });
      if (data?.mtycode == 200) {
        let index = this.btn_list.findIndex(item => {
          return item.value == "update_time";
        });
        this.$set(this.btn_list, index, {
          ...this.btn_list[index],
          options: data?.data.map(item => {
            return { label: "更新于" + item, value: item };
          })
        });
        this.form["update_time"] = data?.data[data.data?.length - 1];
        this.$emit("getInsertTime", data?.data[data.data?.length - 1]);
      }
    },
    // 获取持仓数据更新时间
    async getDateList () {
      let index = this.btn_list.findIndex(item => {
        return item.value == "quarter";
      });
      this.$set(this.btn_list, index, {
        ...this.btn_list[index],
        options: this.info.quarter_list
          .map(item => {
            return { label: item, value: item };
          })
          .reverse()
      });
      this.form["quarter"] = this.info.quarter;
    },
    // 动态判断列显示
    computedColumn () {
      this.column = [];
      this.$nextTick(() => {
        this.column = this.deepCopy(
          this[
            this.ismanager
              ? "default_column_list_manager"
              : "default_column_list"
          ]
            .filter(item => {
              return item.show;
            })
            .sort((a, b) => {
              return a.orderBy - b.orderBy;
            })
        );
      });
    },
    formatTableData () {
      let data = [];
      this.all_data.map(item => {
        let obj = { ...item };
        for (const key in item) {
          let format = this.column.find(obj => {
            return obj.value == key;
          })?.format;
          if (format) {
            let val = format(item[key]);
            obj[key] =
              typeof val == "string"
                ? val.includes("%")
                  ? val?.split("%")?.[0] * 1
                  : val.includes("年")
                    ? val?.split("年")?.[0] * 1
                    : val.includes("亿")
                      ? val?.split("亿")?.[0] * 1
                      : !isNaN(val)
                        ? val * 1
                        : val
                : val;
          }
        }
        data.push(obj);
      });
      return data;
    },
    // 获取池子信息
    async getPoolInfo (condition, list) {
      let item_list = condition?.length ? condition : [];
      this.loading = true;
      let data = await getPoolInfo({
        id: this.info.code,
        item_list,
        flag: 5,
        item: list,
        type: this.info.type || '',
        insert_time: this.form.update_time,
        ismanager: this.ismanager
      });
      if (data?.mtycode == 200) {
        const mergedArray = Object.values(
          data?.data.reduce((acc, curr) => {
            acc[curr.code] = curr;
            return acc;
          }, {})
        ).map(obj => {
          const matchingObj = this.all_data.find(o => o.code === obj.code);
          return matchingObj ? Object.assign(matchingObj, obj) : obj;
        });
        this.setCareFund(mergedArray);
        if (!condition && !list) {
          this.$emit("getCodeList", this.all_data);
        }
      } else {
        this.all_data = [];
      }
      if (condition?.length) {
        this[
          this.ismanager
            ? "default_column_list_manager"
            : "default_column_list"
        ] = this[
          this.ismanager
            ? "default_column_list_manager"
            : "default_column_list"
        ].map(item => {
          return {
            ...item,
            have_data: item.is_default
              ? true
              : condition.indexOf(item.label) != -1
          };
        });
      }

      this.formatData();

      this.loading = false;
    },
    // 关注基金颜色分配
    setCareFund (mergedArray) {
      this.care_fund = mergedArray
        .filter(v => v.flag == 2)
        .map((item, index) => {
          if (index < this.color_list.length - 1) {
            return {
              ...item,
              color: this.color_list[index]
            };
          } else {
            let num = (index % this.color_list.length) - 1;
            return {
              ...item,
              color: this.color_list[num] || this.color_list[0]
            };
          }
        });
      this.all_data = [
        ...mergedArray.filter(v => v.flag != 2),
        ...this.care_fund
      ].sort((a, b) => {
        const sortOrder = [1, 2, 0];
        return sortOrder.indexOf(a.flag) - sortOrder.indexOf(b.flag);
      });
    },
    // 数据分页操作
    formatData () {
      let data = this.deepCopy(this.all_data);
      this.data = data.slice(
        (this.pagination.page - 1) * this.pagination.size,
        this.pagination.page * this.pagination.size
      );
    },
    // 获取动态列数据
    async getChangeColumnList (list) {
      let have = [];
      this[
        this.ismanager
          ? "default_column_list_manager"
          : "default_column_list"
      ] = list.map(item => {
        let index = this[
          this.ismanager
            ? "default_column_list_manager"
            : "default_column_list"
        ].findIndex(obj => {
          return obj.label == item.label;
        });
        // 判断是否显示
        if (item.show) {
          // 判断是否有数据
          if (
            !this[
              this.ismanager
                ? "default_column_list_manager"
                : "default_column_list"
            ][index].is_default
          ) {
            have.push(
              this[
                this.ismanager
                  ? "default_column_list_manager"
                  : "default_column_list"
              ][index].label
            );
          }
        }
        return item;
      });

      if (
        have.length &&
        !have.every(item => {
          return this.choose_list.includes(item);
        })
      ) {
        this.choose_list = have;
        await this.getPoolInfo([have?.[have.length - 1]]);
      }
      this.computedColumn();
    },
    // 更改基金状态
    async updateStatus (val, status) {
      this.loading = true;
      let data = await updateStatus({
        id: this.info.code,
        codes: [val.code],
        status,
        flag: '2',
        ismanager: this.ismanager
      });
      if (data?.mtycode == 200) {
        this.$message.success("设置成功");
        let i = this.all_data.findIndex(v => v.code == val.code);
        let j = this.data.findIndex(v => v.code == val.code);
        this.$set(this.all_data, i, { ...this.all_data[i], flag: status });
        this.$set(this.data, j, { ...this.data[j], flag: status });
        this.setCareFund(this.all_data);
        this.formatData();
        this.$emit("changeCareFund", this.all_data);
        this.loading = false;
      } else {
        this.$message.warning("设置失败");
        this.loading = false;
      }
    },
    // 监听池子勾选
    handleSelectionChange (val) {
      this.multipleSelection = val;
      this.btnIsDisabled();
    },
    sortChange (sortVal) {
      let order = sortVal.order;
      let key = sortVal.prop;
      if (order == "ascending" && key) {
        let haveValList = this.all_data.filter(
          item => !isNaN(parseFloat(item[key]))
        );
        let noValList = this.all_data.filter(item =>
          isNaN(parseFloat(item[key]))
        );
        haveValList.sort((a, b) => a[key] - b[key]);
        this.all_data = [...haveValList, ...noValList];
      } else if (order == "descending" && key) {
        let haveValList = this.all_data.filter(
          item => !isNaN(parseFloat(item[key]))
        );
        let noValList = this.all_data.filter(item =>
          isNaN(parseFloat(item[key]))
        );
        haveValList.sort((a, b) => b[key] - a[key]);
        this.all_data = [...haveValList, ...noValList];
      }
      this.formatData();
    },
    // 动态判断按钮禁用状态
    btnIsDisabled () {
      // 手动生成子池子
      if (this.multipleSelection.length >= 1) {
        let index = this.btn_list.findIndex(item => {
          return item.label == "手动生成子池";
        });
        if (this.btn_list[index].disabled) {
          this.$set(this.btn_list, index, {
            ...this.btn_list[index],
            disabled: false
          });
        }
      } else {
        let index = this.btn_list.findIndex(item => {
          return item.label == "手动生成子池";
        });
        if (!this.btn_list[index].disabled) {
          this.$set(this.btn_list, index, {
            ...this.btn_list[index],
            disabled: true
          });
        }
      }
      // if (!this.ismanager) {
      //   // 创建组合
      //   if (this.multipleSelection.length >= 1) {
      //     let index = this.btn_list.findIndex(item => {
      //       return item.label == "创建组合";
      //     });
      //     if (this.btn_list[index].disabled) {
      //       this.$set(this.btn_list, index, {
      //         ...this.btn_list[index],
      //         disabled: false
      //       });
      //     }
      //   } else {
      //     let index = this.btn_list.findIndex(item => {
      //       return item.label == "创建组合";
      //     });
      //     if (!this.btn_list[index].disabled) {
      //       this.$set(this.btn_list, index, {
      //         ...this.btn_list[index],
      //         disabled: true
      //       });
      //     }
      //   }
      // }
      // 前往比较
      // if (
      //   this.multipleSelection.length >= 2 &&
      //   this.multipleSelection.length <= 4
      // ) {
      //   let index = this.btn_list.findIndex(item => {
      //     return item.label == "前往比较";
      //   });
      //   // console.log(index);
      //   if (this.btn_list[index].disabled) {
      //     this.$set(this.btn_list, index, {
      //       ...this.btn_list[index],
      //       disabled: false
      //     });
      //   }
      // } else {
      //   let index = this.btn_list.findIndex(item => {
      //     return item.label == "前往比较";
      //   });
      //   if (!this.btn_list[index].disabled) {
      //     this.$set(this.btn_list, index, {
      //       ...this.btn_list[index],
      //       disabled: true
      //     });
      //   }
      // }
    },
    // 前往比较GD修改比较方法为基金/基金经理比较
    async goCompare () {
      const loading = this.$loading({
        lock: true,
        text: "正在获比较信息",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)"
      });
      let data = await TypeMsg({
        code: this.multipleSelection.map(v => v.code).join(","),
        flag: this.ismanager ? 2 : 1,

      });
      loading.close();
      let temptype;
      if (data) {
        if (data.data) {
          if (data.data.length == 0) {
            this.$router.push({
              path: "/fundcompareDiff",
              query: {
                id: this.multipleSelection.map(v => v.code).join(","),
                type: "",
                types: data.type.join(","),
                name: this.multipleSelection.map(v => v.name).join(",")
              }
            });
          } else if (data.data.length == 1) {
            temptype = data.data[0];
            if (
              temptype == "bond" ||
              temptype == "cbond" ||
              temptype == "purebond" ||
              temptype == "bill" ||
              temptype.indexOf("equity") >= 0 ||
              temptype == "obond"
            ) {
              this.$router.push({
                path: "/fundcompare",
                query: {
                  id: this.multipleSelection.map(v => v.code).join(","),
                  type: temptype,
                  name: this.multipleSelection.map(v => v.name).join(",")
                }
              });
            } else {
              this.$message("暂时只提供主动权益，二级债，债券类产品的比较");
            }
          } else if (data.data.length > 1) {
            this.$router.push({
              path: "/fundcompareDiff",
              query: {
                id: this.multipleSelection.map(v => v.code).join(","),
                type: "",
                types: data.type.join(","),
                name: this.multipleSelection.map(v => v.name).join(",")
              }
            });
          }
        }
      }
    },
    // 删除基金
    async deletePoolFund (scope) {
      console.log(scope);
      this.visible = false;
      let data = await deletePoolFund({
        codes: scope.row.code,
        flag: 1,
        id: this.info.code,
        ismanager: this.ismanager
      });
      if (data?.mtycode == 200) {
        this.$message.success("删除成功");
        this.getPoolInfo();
      } else {
        this.$message.error("删除失败");
      }
    },
    // 创建组合
    createPool () {
      this.$refs.portC.show(this.multipleSelection);
    },
    // 监听用户选择季度
    changeQuarter (val) {
      this.$emit("resolveFather", {
        quarter: this.form.quarter,
        date: this.form.update_time
      });
    },
    // 监听用户选择更新时间
    changeTime () {
      this.getPoolInfo();
    },
    // 批量导入
    putManyFunds () {
      this.$refs.alphaownpoolrefs.showitem(this.info.code);
    },
    // 跳转基金详情
    alphaGo (id, name) {
      alphaGo(id, name, this.$route.path);
    },
    // 监听选择自动生成子池
    onCommandFunction (val) {
      if (val == "filter") {
        this.$refs["filterPoolFund"]?.getData(this.info);
      } else {
        this.clickChildren(val);
      }
    },
    // 自动生成子池子
    async clickChildren (val) {
      this.loading = true;
      let data = await createdChildren({
        id: this.info.code,
        types: "auto",
        flag: val,
        name: "",
        codes: [],
        ismanager: this.ismanager,
        type: this.info.type
      });
      this.loading = false;

      if (data?.mtycode == 200) {
        this.$refs.multipleTablePool.clearSelection();
        this.multipleSelection = [];
        this.$message.success("生成成功");
        this.$emit("poolCreated");
      } else {
        this.$message.error("生成失败");
      }
    },
    handleCreatedSelf () {
      this.createdSelf();
    },
    // 手动生成子池子
    async createdSelf (code_list) {
      console.log(code_list);
      this.$prompt("请输入池子名称", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消"
      })
        .then(async ({ value }) => {
          this.showPopPool = false;
          this.loading = true;
          let data = await createdChildren({
            id: this.info.code,
            codes: code_list
              ? code_list
              : this.multipleSelection.map(item => {
                return item.code;
              }),
            types: "hands",
            description: "",
            ispublic: false,
            name: value,
            ismanager: this.ismanager,
            flag: '',
            type: this.info.type
          });
          this.loading = false;

          if (data?.mtycode == 200) {
            this.$message.success("生成成功");
            this.showChildren = false;
            this.multipleSelection = [];
            this.$refs.multipleTablePool.clearSelection();
            this.$emit("poolCreated");
          } else {
            this.$message.error("生成失败");
          }
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "取消输入"
          });
        });
    },
    // 重新获取数据
    refrshtable () {
      // this.flags = 1;
      this.getPoolInfo();
    },
    // 获取搜索信息
    getFundInfo (val) {
      this.postFunds(val.id);
    },
    // 获取指数信息
    getIndexInfo (val) {
      this.info["index_code"] = val.id;
      this.$emit("changeIndexCode", val.id);
    },
    // 新增基金
    async postFunds (code) {
      this.loading = true;
      let data = await postFunds({
        id: this.info.code,
        codes: typeof (code) == "string" ? [code] : code,
        flag: "1",
        ismanager: this.ismanager
      });
      if (data?.mtycode == 200) {
        this.getPoolInfo();
        this.$message.success("新增成功");
        this.$refs?.alphaownpoolrefs?.closeDialog()
      } else {
        this.$message.warning("新增失败");
        // this.$refs.alphaownpoolrefs.closeDialog()
      }
      this.loading = false;
    },
    deepCopy (obj) {
      // 只拷贝对象
      if (typeof obj !== "object") return;
      // 根据obj的类型判断是新建一个数组还是一个对象
      let newObj = obj instanceof Array ? [] : {};
      for (let key in obj) {
        // 遍历obj,并且判断是obj的属性才拷贝
        if (obj.hasOwnProperty(key)) {
          // 判断属性值的类型，如果是对象递归调用深拷贝
          newObj[key] =
            typeof obj[key] === "object" ? this.deepCopy(obj[key]) : obj[key];
        }
      }
      return newObj;
    },
    // 监听分页size切换
    handleSizeChange (val) {
      this.pagination.size = val;
      this.formatData();
    },
    // 监听分页page切换
    handleCurrentChange (val) {
      this.pagination.page = val;
      this.formatData();
    },

    // 格式化表格数据
    fix2p (val) {
      return !isNaN(val) && typeof (val * 1) == "number"
        ? (val * 100).toFixed(2) + "%"
        : "--";
    },
    fix_year (val) {
      return !isNaN(val) && typeof (val * 1) == "number"
        ? val.toFixed(2) + "年"
        : "--";
    },
    fix_money (val) {
      if (!isNaN(val) && typeof (val * 1) == "number") {
        // return val >= 10 ** 8 || val <= -1 * 10 ** 8
        // 	? (val / 10 ** 8).toFixed(2) + '亿'
        // 	: val >= 10 ** 4 || val <= -1 * 10 ** 4
        // 	? (val / 10 ** 4).toFixed(2) + '万'
        // 	: (val * 1).toFixed(2) + '元';
        return (val * 1).toFixed(2) + "亿";
      } else {
        return "--";
      }
    },
    // 导出excel
    exportExcel () {
      let list = [];
      this.column.map(item => {
        if (item.children) {
          item.children.map(obj => {
            list.push({
              label: item.label + obj.label,
              value: obj.value
            });
          });
        } else {
          list.push({
            label: item.label,
            value: item.value
          });
        }
      });
      list.unshift({ label: "基金代码", value: "code" });
      let data = this.all_data;
      filter_json_to_excel(list, data);
    },
    // 获取适应性评分打分条件
    getScoreCondition (val) {
      this.$refs["multipleTablePool"].clearSort();
      this.getPoolInfo(undefined, val);
    },
    // 打开适应性评分弹窗
    openDialog () {
      this.$refs["scorePoolFund"]?.getData(this.info);
    }
  },
  mounted () {
    this.ismanager = String(this.$route.query.ismanager) == 'true' ? true : false
    this.btn_list = this.ismanager
      ? [
        // {
        // 	label: '自动生成子池',
        // 	method: this.clickChildren,
        // 	disabled: false,
        // 	show_type: 'button'
        // },
        {
          label: "手动生成子池",
          method: this.handleCreatedSelf,
          disabled: true,
          is_self_manage: false,
          show_type: "button"
        },
        // {
        //   label: "前往比较",
        //   method: this.goCompare,
        //   is_self_manage: true,
        //   disabled: true,
        //   show_type: "button"
        // },
        {
          label: "季度选择",
          value: "quarter",
          // method: this.changeQuarter,
          disabled: false,
          show_type: "select",
          is_self_manage: false,
          options: [],
          multiple: true
        },
        {
          label: "确认",
          method: this.changeQuarter,
          is_self_manage: false,
          disabled: false,
          show_type: "button"
        }
      ]
      : [
        // {
        // 	label: '自动生成子池',
        // 	method: this.clickChildren,
        // 	disabled: false,
        // 	show_type: 'button'
        // },
        {
          label: "手动生成子池",
          method: this.handleCreatedSelf,
          disabled: true,
          is_self_manage: false,
          show_type: "button"
        },
        // {
        //   label: "前往比较",
        //   method: this.goCompare,
        //   is_self_manage: true,
        //   disabled: true,
        //   show_type: "button"
        // },
        // {
        //   label: "创建组合",
        //   method: this.createPool,
        //   disabled: true,
        //   show_type: "button"
        // },
        // {
        // 	label: '池子更新记录',
        // 	value: 'update_time',
        // 	method: this.changeTime,
        // 	disabled: false,
        // 	show_type: 'select',
        // 	options: [],
        // 	multiple: false
        // },
        {
          label: "季度选择",
          is_self_manage: false,
          value: "quarter",
          // method: this.changeQuarter,
          disabled: false,
          show_type: "select",
          options: [],
          multiple: true
        },
        {
          label: "确认",
          method: this.changeQuarter,
          is_self_manage: false,
          disabled: false,
          show_type: "button"
        }
      ];
  }
};
</script>
<style lang="scss" scoped>
.drag-handle {
	height: 10px;
	width: 100%;
	background: transparent;
	opacity: 0.3;
	cursor: ns-resize;
	position: absolute;
	bottom: 0;
	left: 0;
}
.drag-container {
	transition: all 0.1;
}
.pagination_text {
	font-style: normal;
	font-weight: 400;
	font-size: 14px;
	line-height: 22px;
	color: rgba(0, 0, 0, 0.65);
}
.overflow_ellipsis {
	a {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	.el-link {
		justify-content: start;
	}
	::v-deep.el-link--inner {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
}
</style>
