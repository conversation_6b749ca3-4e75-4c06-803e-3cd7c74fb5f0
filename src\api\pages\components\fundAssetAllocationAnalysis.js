import request from '@/api/request';

/**
 *
 * @param {报告期持仓统计指数列表} params
 * type
 * @returns
 */
export function getIndexList(params) {
	return request({
		url: '/BenchmarkList/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {报告期持仓统计指数收益} params
 * code
 * @returns
 */

export function getIndexReturn(data) {
	return request({
		url: '/RateInfo/',
		method: 'post',
		data
	});
}

export function getAllocationDetails(params) {
	return request({
		url: '/AllocationDetails/',
		method: 'get',
		params
	});
}
export function getFofAllocationDetails(params) {
	return request({
		url: '/FofAllocationDetails/',
		method: 'get',
		params
	});
}
// 基金公司大类资产配置
export function getCompanyAllocationDetails(params) {
	return request({
		url: '/Company/AllocationDetails/',
		method: 'get',
		params
	});
}
// 债券配置
export function getBondClassDetails(params) {
	return request({
		url: '/BondClassDetails/',
		method: 'get',
		params
	});
}
