<template>
	<div id="brinsonAttribution" class="brinsonAttribution plate-wrapper fund-performance-board-wrapper"  v-loading="loading">
		<combinationComponentHeader title="Brinson归因" showMoreBtn @download="exportExcel">
			<template slot="right">
                <div style="margin-right: 16px;">
                        <el-radio-group class="radio-group-wrapper" v-model="form.penetrateFlag" size="small" style="margin-left: 0 !important;" @input="radioChange">
                            <el-radio-button :label="true">穿透fof持仓</el-radio-button>
                            <el-radio-button :label="false">不穿透fof持仓</el-radio-button>
                            
                        </el-radio-group>
                    </div>
                <el-select v-model="form.industryStandard" placeholder="选择行业口径" style="margin-right: 16px;" @input="radioChange">
                    <el-option
                        v-for="item in options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                        </el-option>
                </el-select>
                <!-- <el-select v-model="comparisonValue" placeholder="沪深300" style="margin-right: 16px;">
                    <el-option
                        v-for="item in options2"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                        </el-option>
                </el-select> -->
                <div style="margin-right: 16px;">
                    <FormTimePicker v-model="preset_time" @input="handleFormChange"></FormTimePicker>
                </div>
            </template>
	</combinationComponentHeader>
<div>
			<el-table :data="data" style="width: 100%" border stripe>
				<el-table-column
					v-for="item in column"
					:key="item.value"
					:prop="item.value"
					:label="item.label"
					:min-width="item.width"
					:align="item.align ? item.align : 'gotoleft'"
				>
					<template slot-scope="{ row }">
						<div v-if="item.flag == 'chart'">
							<v-chart :options="returnMock(row[item.value])" style="width: 163px; height: 46px" :key="item.value"></v-chart>
						</div>
						<div v-else-if="item.flag == 'background'">
							<div
								v-if="item.color == 'yellow'"
								class="pr-8"
								:style="`background: linear-gradient(90deg, rgba(255, 145, 3, 0.2) 0%, rgba(255, 145, 3, 0.03) ${item.format(
									row[item.value]
								)}`"
							>
								{{ item.format ? item.format(row[item.value]) : row[item.value] }}
							</div>
							<div
								v-else
								class="pr-8"
								:style="
									row[item.value] > 0
										? `background: linear-gradient(90deg, rgba(207, 19, 34, 0.2) 0%, rgba(207, 19, 34, 0.03) ${item.format(
												row[item.value]
										  )}`
										: `background: linear-gradient(90deg, rgba(56, 158, 13, 0.2)  0%, rgba(56, 158, 13, 0.03) ${item.format(
												row[item.value] * -1
										  )}`
								"
							>
								{{ item.format ? item.format(row[item.value]) : row[item.value] }}
							</div>
						</div>
						<div class="ml-8" v-else>
							{{ item.format ? item.format(row[item.value]) : row[item.value] }}
						</div>
					</template>
				</el-table-column>
				<template slot="empty">
                <el-empty image-size="160"></el-empty>
            </template>
			</el-table>
		</div>
	</div>
</template>

<script>
// 模型使用说明
import combinationComponentHeader from './combinationComponentHeader.vue';
import { brinsonAttribution } from '@/api/pages/tkAnalysis/portfolio.js';
import FormTimePicker from './formTimePicker.vue';
const dayjs = require('dayjs');
import { filter_to_excel } from "@/utils/exportExcel.js";

export default {
	name: 'brinsonAttribution',
	components: { combinationComponentHeader,FormTimePicker },
	data() {
		return {
			form:{
                penetrateFlag:true,
                industryStandard:'sw',
                startDate: dayjs().subtract(1, 'year').format('YYYY-MM-DD'),
                endDate: dayjs().format('YYYY-MM-DD')
            },
            comparisonValue:'沪深300',
            options:[{
                label:'申万一级行业',
                value: 'sw'
            },{
                label:'泰康一级行业',
                value: 'tk'
            }],
			options2:[{
                label:'沪深300',
                value: '沪深300'
            }],
			preset_time: {
                radioValue: '1',
                startDate: dayjs().subtract(1, 'year').format('YYYY-MM-DD'),
                endDate: dayjs().format('YYYY-MM-DD')
            },
			param: null,
			loading: true,
			data: [],
			activeBenchmark: '',
			benchmarkList: [],
			column: [
				{
					label: '行业',
					value: 'industryName'
				},
				{
					label: '配置比例',
					value: 'ratioList',
					flag: 'chart',
					align: 'right'
				},
				{
					label: '平均配置比例',
					value: 'aveRatio',
					align: 'right',
					flag: 'background',
					color: 'yellow',
					format: this.fix2p
				},
				{
					label: '超欠配比例',
					value: 'overRatioList',
					flag: 'chart',
					align: 'right'
				},
				{
					label: '平均超欠配比例',
					value: 'aveOverRatio',
					align: 'right',
					flag: 'background',
					color: 'red_or_green',
					format: this.fix2p
				},
				{
					label: '总超额收益',
					value: 'sumExcess',
					align: 'right',
					flag: 'background',
					color: 'red_or_green',
					format: this.fix2p
				},
				{
					label: '行业配置收益',
					value: 'industryIncome',
					align: 'right',
					flag: 'background',
					color: 'red_or_green',
					format: this.fix2p
				},
				{
					label: '证券选择收益',
					value: 'bondIncome',
					align: 'right',
					flag: 'background',
					color: 'red_or_green',
					format: this.fix2p
				},
			
			],
			info: {}
		};
	},
	methods: {
		handleFormChange(val) {
            this.preset_time = val;
            this.form.startDate = val.startDate;
            this.form.endDate = val.endDate;
            this.getData(this.param);
		},
		async getData(param) {
			this.loading = true;
			this.param = param;
			this.data = [];
			let res = await brinsonAttribution({
				...param,
				...this.form
			})
			if(res.mtycode == 200){
				res.data.forEach(item=>{
					this.data.push({
						industryName: item.industryName,
						ratioList: item.ratioList.map(item=>(item * 10000 / 100).toFixed(2)),
						aveRatio: item.aveRatio,
						overRatioList: item.overRatioList.map(item=>(item * 10000 / 100 ).toFixed(2)),
						aveOverRatio: item.aveOverRatio,
						sumExcess: item.sumExcess,
						industryIncome: item.industryIncome,
						bondIncome: item.bondIncome,
						otherIncome: item.otherIncome
					})
				})
			}
			this.loading = false;
		},
		fix2p(val) {
			return val * 1 && !isNaN(val) ? (val * 100).toFixed(2) + '%' : '--';
		},
		returnMock(data) {
			let xdata = data.map((item, index) => {
				return index + 1;
			});
			return {
				xAxis: {
					show: false,
					type: 'category',
					boundaryGap: false,
					data: xdata
				},
				grid: {
					top: '16',
					left: 0,
					right: 0,
					bottom: 0,
					containLabel: true
				},
				yAxis: {
					show: false,
					type: 'value',
				},
				series: [
					{
						data,
						type: 'line',
						symbol: 'none',
						lineStyle: {
							color: '#64A8FF'
						},
						areaStyle: {
							color: new echarts.graphic.LinearGradient(1, 1, 1, 0, [
								{ offset: 0, color: 'rgba(69, 118, 233, 0.00)' },
								{ offset: 1, color: 'rgba(69, 118, 233, 0.25)' }
							])
						}
					}
				]
			};
		},
		radioChange(){
            this.getData(this.param)
        },
		exportExcel(){
          // 将表头数据进行遍历，生成新的数组list，每个元素包含原表头数据和format字段
          let list = this.column.map((item) => {
            return {
              ...item,
			  prop: item.value,
              format: ''
            };
          });
          // 调用filter_to_excel函数，传入list、表格数据this.tableData和文件名'基金标签'
          filter_to_excel(list, this.data, 'Brinson归因');
        },
	},
};
</script>
<style lang="scss" scoped>
.brinsonAttribution {
	::v-deep.el-table__body {
		.el-table__row {
			.el-table__cell {
				.cell {
					padding: 0;
					> div {
						height: 46px;
						line-height: 46px;
					}
				}
			}
		}
	}
}
</style>
