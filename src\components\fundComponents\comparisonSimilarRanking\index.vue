<template>
	<div class="chart_one" v-loading="loading">
		<div style="display: flex; align-items: center; justify-content: space-between; position: relative">
			<div class="title" style="margin-bottom: 24px">同类排名比较</div>
			<el-button icon="el-icon-document-delete" @click="exportExcel">导出Excel</el-button>
		</div>
		<div style="width: 100%">
			<el-table :data="datatable" class="table" ref="multipleTable" header-cell-class-name="table-header">
				<el-table-column prop="year" label="年份" align="gotoleft"> </el-table-column>
				<el-table-column prop="factor" label="收益排名" align="gotoleft">
					<template slot-scope="scope"
						><span>{{ scope.row.cum_return }}</span></template
					>
				</el-table-column>
				<el-table-column prop="std" align="gotoleft" label="波动排名">
					<template slot-scope="scope"
						><span>{{ scope.row.volatility }}</span></template
					>
				</el-table-column>
				<el-table-column prop="gainorloss" label="回撤排名" align="gotoleft">
					<template slot-scope="scope"
						><span>{{ scope.row.maxdrawdown }}</span></template
					>
				</el-table-column>
				<el-table-column prop="adaptivity" align="gotoleft" label="夏普排名">
					<template slot-scope="scope"
						><span>{{ scope.row.sharpe }}</span></template
					>
				</el-table-column>
				<template slot="empty">
					<el-empty image-size="160"></el-empty>
				</template>
			</el-table>
		</div>
	</div>
</template>

<script>
import { exportTitle, exportTable } from '@/utils/exportWord.js';
import { filter_json_to_excel } from '@/utils/exportExcel.js';
// 同类排名比较
export default {
	name: 'comparisonSimilarRanking',

	data() {
		//这里存放数据
		return {
			datatable: [],
			notesData: {
				bj001: ''
			},
			loading: true
		};
	},
	//方法集合
	methods: {
		async getData(data) {
			this.loading = false;
			this.datatable = data;
		},
		hideLoading() {
			this.loading = false;
		},
		exportExcel() {
			let list = [
				{
					label: '年份',
					fill: 'header',
					value: 'year'
				},
				{
					label: '收益排名',
					value: 'cum_return'
				},
				{
					label: '波动排名',
					value: 'volatility'
				},
				{
					label: '回撤排名',
					value: 'maxdrawdown'
				},
				{
					label: '夏普排名',
					value: 'sharpe'
				}
			];
			filter_json_to_excel(list, this.datatable, '同类排名比较');
		},
		createPrintWord() {
			let list = [
				{
					label: '年份',
					fill: 'header',
					value: 'year'
				},
				{
					label: '收益排名',
					value: 'cum_return'
				},
				{
					label: '波动排名',
					value: 'volatility'
				},
				{
					label: '回撤排名',
					value: 'maxdrawdown'
				},
				{
					label: '夏普排名',
					value: 'sharpe'
				}
			];
			if (this.datatable.length) {
				return [...exportTitle('同类排名比较'), ...exportTable(list, this.datatable)];
			} else {
				return [];
			}
		}
	}
};
</script>

<style></style>
