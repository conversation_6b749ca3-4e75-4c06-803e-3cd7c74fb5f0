import { mockRequest, tkRequest as request } from '@/api/request';
// 业绩与评价指标数据列表
export function getPerformanceEvaluationList(params) {
	return request({
		url: '/getPerformanceEvaluationList',
		method: 'get',
		params
	});
}
//估值分位数据列表
export function getValuationPercentileList(params) {
	return request({
		url: '/market/analysis/getValuationPercentileList',
		method: 'get',
		params
	});
}
//指数间运算模板保存
export function addIndicatorsOperationTemplate(data) {
	return request({
		url: '/market/analysis/addIndicatorsOperationTemplate',
		method: 'post',
		data
	});
}

//复合指标运算模板查询
export function getIndicatorsOperationTemplate(params) {
	return request({
		url: '/market/analysis/getIndicatorsOperationTemplate',
		method: 'get',
		params
	});
}
//复合指标运算表格数据
export function getIndicatorsOperationData(params) {
	return request({
		url: '/market/analysis/indicatorsOperation',
		method: 'get',
		params
	});
}
//指数查询
export function getIndexCode(params) {
	return request({
		url: '/market/analysis/getIndexCode',
		method: 'get',
		params
	});
}
/**
 * 获取全部基金类型
 * @param {*} params
 * @returns
 */
export function getFundAllType(params) {
	return request({
		url: '/getFundAllType',
		method: 'get',
		params
	});
}
/**
 * 获取全部基金公司
 * @param {*} params
 * @returns
 */
export function getAllFundCompany(params) {
	return request({
		url: '/getAllFundCompany',
		method: 'get',
		params
	});
}
/**
 * 获取季度时间
 * @param {*} params
 * @returns
 */
export function getTimeDate(params) {
	return request({
		url: '/getTimeDate',
		method: 'get',
		params
	});
}
/**
 * 获取全部基金池
 * @param {*} params
 * @returns
 */
export function getFundCode(params) {
	return request({
		url: '/getFundCode',
		method: 'get',
		params
	});
}
/**
 * 获取仓位时序图数据
 * @param {*} params
 * @returns
 */
export function getPositionConfiguration(params) {
	return request({
		url: '/positionConfiguration',
		method: 'post',
		data: params
	});
}
/**
 * 行业配置持仓变化
 * @param {*} params
 * @returns
 */
export function getIndustryConfigurationChange(params) {
	return request({
		url: '/industryConfigurationChange',
		method: 'post',
		data: params
	});
}

/**
 * 行业配置\重仓股票\重仓基金
 * @param {*} params
 * @returns
 */
export function getMarketConfiguration(params) {
	return request({
		url: '/getMarketConfiguration?apifoxApiId=*********',
		method: 'post',
		data: params
	});
}
/**
 * 重仓股票\重仓基金 单只股票和基金的收益
 * @param {*} params
 * @returns
 */
export function getRateLastYear(params) {
	return request({
		url: '/getRateLastYear',
		method: 'post',
		params
	});
}

/**
 * 获取全部基金公司
 * @param {*} params
 * @returns
 */
export function getFundCompany(params) {
	return request({
		url: '/getFundCompany',
		method: 'get',
		params
	});
}
//查询基金类型
export function getFundType(params) {
	return request({
		url: '/getFundType',
		method: 'get',
		params
	});
}

// 各行业财务指标列表
export function getFinancialIndexList(data) {
	return request({
		url: '/market/analysis/financialIndexList',
		method: 'post',
		data
	});
}
/** 业绩类型 */
export const PerformanceType = {
	FundGlobal: {
		name: '基金整体拟合业绩',
		value: 'fund'
	},
	FundQuantile: {
		name: '基金的分位数业绩',
		value: 'fund'
	},
	FundQuantileByTime: {
		name: '按不同时段排序的基金业绩',
		value: 'fund'
	},
	FundManagerClobal: {
		name: '基金经理整体拟合业绩',
		value: 'manager'
	}
};
/**是否需要分页展示 */
export const PaginationFlag = {
	Y: {
		name: '分页请求',
		value: '1'
	},
	N: {
		name: '非分页请求',
		value: '0'
	}
};
//市场分析/主动权益基金或基金经理的整体拟合业绩
export function getFitPerformance(params) {
	return request({
		url: '/getFitperformance',
		method: 'get',
		params
	});
}
//主动权益基金或基金经理的整体拟合业绩曲线图-getSubFitperformance
export function getSubFitperformance(params) {
	return request({
		url: '/getSubFitperformance',
		method: 'get',
		params
	});
}
//市场分析/主动权益基金或基金经理的分位数业绩
export function getQuantilePerformance(data) {
	return request({
		url: '/getQuantilePerformance',
		method: 'post',
		data
	});
}
//按不同时段排序的基金或基金经理业绩
export function performanceSort(data) {
	return request({
		url: '/performanceSort',
		method: 'post',
		data
	});
}
//按不同时间段排序的基金公司业绩
export function companyPerformance(data) {
	return request({
		url: '/companyPerformance',
		method: 'post',
		data
	});
}
//行业配置变化信息
export function getIndustryInfo(data) {
	return mockRequest({
		url: '/getIndustryInfo?apipost_id=8e82d2',
		method: 'post',
		data
	});
}
//仓位时序图信息
export function getTimeingInfo(data) {
	return mockRequest({
		url: '/getTimeingInfo?apipost_id=b7d0cc',
		method: 'post',
		data
	});
}
//获取字典接口
export function getDictList(params) {
	return request({
		url: '',
		method: 'get',
		params
	});
}
//获取基金经理整体业绩列表
export function getManagerPerfermanceList(data) {
	return mockRequest({
		url: '/getManagerPerfermanceList?apipost_id=d9f873',
		method: 'post',
		data
	});
}
//获取整体基金经理在不同时间段的分位数业绩列表
export function getManagerAreaPerfermanceList(data) {
	return mockRequest({
		url: '/getManagerAreaPerfermanceList?apipost_id=30e401',
		method: 'post',
		data
	});
}
//获取整体基金经理在不同时间段的分位数业绩列表
export function getCompanyAreaPerfermanceList(data) {
	return mockRequest({
		url: '/getCompanyAreaPerfermanceList?apipost_id=dd7c8c',
		method: 'post',
		data
	});
}
