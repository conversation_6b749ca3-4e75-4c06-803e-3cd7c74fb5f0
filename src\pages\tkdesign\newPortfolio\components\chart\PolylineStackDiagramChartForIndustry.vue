<template>
	<div class="position_style">
		<div class="flex_start">
			<div class="left_legend py-20">
				<div v-for="item in itemList" :key="item.label" class="mb-4 flex_start legend_item" @click="changeShow(item.label)">
					<div class="item_icon mr-8" :style="item.show ? `background-color:${item.color}` : `background-color:#D9D9D9`"></div>
					<div :style="item.show ? `color:rgba(0, 0, 0, 0.85);` : `color:rgba(0, 0, 0, 0.45);`">{{ item.label }}</div>
				</div>
			</div>
			<div class="charts_fill_class ml-20" style="flex: 1" v-loading="loading">
				<el-empty v-show="!show" :image-size="160"></el-empty>
				<v-chart
					v-show="show"
					ref="positionStyle"
					v-loading="loading"
					style="width: 100%; height: 668px"
					autoresize
					element-loading-text="暂无数据"
					element-loading-spinner="el-icon-document-delete"
					element-loading-background="rgba(239, 239, 239, 0.5)"
					:options="optionex"
				/>
			</div>
		</div>
	</div>
</template>

<script>
import { exportTitle, exportChart } from '@/utils/exportWord.js';
import { lineChartOption } from '@/utils/chartStyle.js';

// 行业配置表现
import { industryPositionAnalysis } from '@/api/pages/tkAnalysis/portfolio.js'

// 持仓风格
export default {
	name: 'industryPositionChange',
	data() {
		return {
			optionex: {},
			loading: true,
			show: true,
			info: {},
			value1:true,
			color: [
				'#4096ff',
				'#89B3FA',
				'#D0DFF9',
				'#4096ff',
				'#FFB462',
				'#F8D3AB',
				'#83BE57',
				'#A5D084',
				'#D0ECBA',
				'#904371',
				'#BF5694',
				'#DCA1C4',
				'#E8684A',
				'#EF907A',
				'#F9C5B9',
				'#F6BD16',
				'#CADAF2',
				'#EB2F96',
				'#F66DB8',
				'#FFBAE0',
				'#13C2C2',
				'#68E4DD',
				'#B5F5EC',
				'#B43438',
				'#D16063',
				'#EE9799',
				'#722ED1',
				'#B37FEB'
			],
			data: [],
			itemList: []
		};
	},
	methods: {
		// 获取父组件传递数据
		getData(param) {
			this.getIndustryInfo(param);
		},
		// 获取行业配置数据
		async getIndustryInfo(param) {
			this.loading = true;
			let data = await industryPositionAnalysis(param);
			this.loading = false;
			this.show = true;
			if (data?.mtycode == 200) {
				this.filterItemList(data?.data);
				this.drawLine(data?.data);
				if(data?.data?.length  === 0){
            this.show = false;
        }
			} else {
				this.hideLoading();
			}
		},

		// 切换具体图例的显隐
		changeShow(name) {
			let selected = {};
			// 获取当前点击的索引
			let index = this.itemList.findIndex((v) => v.label == name);
			// 修改数组中当前图例的显示状态
			this.$set(this.itemList, index, { ...this.itemList[index], show: !this.itemList[index].show });
			// 将数组转化成echart接收格式
			this.itemList.map((item) => {
				selected[item.label] = item.show;
			});
			this.optionex = { ...this.optionex, legend: { ...this.optionex.legend, selected } };
			console.log(this.optionex);
		},
		// 画图
		drawLine(data) {
			let { series, date_list } = this.filterData(data);
			this.$emit('tableData',{date_list,series,itemList:this.itemList});
			this.optionex = lineChartOption({
				toolbox: 'none',
				tooltip: {
					formatter: (params) => {
						 //数据排序
			
						 let list = params.map((item)=>JSON.stringify({
							seriesType:item.seriesType,
							seriesName:item.seriesName,
							axisValue:item.axisValue,
							value:item.value
						}));
						 list = [...new Set(list)].map((item)=>JSON.parse(item));
						list.sort((a,b)=>{
							if(list[0].seriesType ==='scatter'){
								if(a.value[1]-b.value[1] < 0){
									return 1;
								}else{
									return -1;
								}
							}else{
								if(a.value-b.value < 0){
									return 1;
								}else{
									return -1;
								}
							}
							
						})
					
						let str = `<div style="display:flex;align-items:center;maring-bottom:8px;"><div style="margin-right:4px">时间:</div><div>${list[0].axisValue}</div></div>`;
						for (let i = 0; i < list.length; i++) {
							let realVal =list[0].seriesType ==='scatter' ? list[i].value[1] : list[i].value;
							
							let value = realVal * 1 && !isNaN(realVal) ? (realVal * 1).toFixed(2) + '%' : '--';
							let dotHtml = `<div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:${
								this.itemList.find((v) => v.label == list[i].seriesName)?.color
							}"></div>`;
							str += `<div style="margin-bottom:8px;display:flex;align-items:center;justify-content:space-between;"><div style="display:flex;align-items:center;">${dotHtml}<div>${list[i].seriesName}:</div></div><div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">${value}</div></div>`;
						}
						return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${str}</div>`;
					}
				},
				dataZoom: true,
				xAxis: [{ type: 'category', data: date_list }],
				yAxis: [
					{
						type: 'value',
						max: 100,
						formatter: function (val) {
							return val + '%';
						}
					}
				],
				series,
				grid:{
					top: 62
				}
			});
		},
		// 过滤图例
		filterItemList(data) {
			// let fakeData = ['门诊人数','急诊人数','住院人数','在院人数','在院人数2','在院人数3','在院人数4']
			// let list = Array.from(new Set(data.map((v) => v.industry_name)));
			this.itemList = data.dataList.map((v, i) => {
				return { label: v.name, color: this.color[i], show: true };
			});
		},
		// 隐藏模块
		hideLoading() {
			this.show = false;
		},
		exportImage() {
			let chart = this.$refs['positionStyle'].getDataURL({
				type: 'png',
				pixelRatio: 3,
				backgroundColor: '#fff',
				excludeComponents: ['dataZoom']
			});
			let aLink = document.createElement('a');
			aLink.style.display = 'none';
			aLink.href = chart;
			aLink.download = '持仓风格.jpg';
			// 触发点击-然后移除
			document.body.appendChild(aLink);
			aLink.click();
			document.body.removeChild(aLink);
		},
		// 过滤接收数据
		filterData(data) {
			let date = []
			data.dataList.forEach((v) => {
				v.proportionList.forEach(item=>{
					date.push(item.yearqtr)
				})
			});
			
			let date_list  = [...new Set(date)];
			// let date_list = [];
			let series = [];
			this.itemList.map((item) => {
				let arr = data.dataList.filter((v) => v.name == item.label);
				if(date_list.length > 1){
					series.push({
						name: item.label,
						type: 'line',
						stack: '总量',
						symbol: 'none',
						lineStyle: {
							color: item.color
						},
						areaStyle: {
							color: item.color,
							opacity: 0.25
						},
						data:date_list.map((v)=>{
							let obj = arr[0].proportionList.find((item) => item.yearqtr == v);
							return obj ? parseInt(obj.proportion*10000)/100 : '--';
						})
					});
				}else{
					series.push({
						name: item.label,
						type: 'scatter' ,
						symbol: 'circle',
						data:arr[0].proportionList.map((v)=>[date_list[0],parseInt(v.proportion*10000)/100])
					});
				}
			
			});
			return { date_list, series };
		},

		createPrintWord() {
			let height = this.$refs['positionStyle'].$el.clientHeight;
			let width = this.$refs['positionStyle'].$el.clientWidth;
			let chart = this.$refs['positionStyle'].getDataURL({
				type: 'png',
				pixelRatio: 2,
				backgroundColor: '#fff',
				excludeComponents: ['dataZoom']
			});
			return this.show ? [...exportTitle('持仓风格'), ...exportChart(chart, { width, height })] : [];
		}
	}
};
</script>

<style lang="scss" scoped>
.position_style {
	height: 720px;
	.left_legend {
		width: 120px;
		height: 720px;
		padding-top:20px;
		padding-left:20px;
		margin-bottom: -20px;
		background: #fff;
		box-shadow: 8px 0px 20px 0px rgba(0, 0, 0, 0.08);
		.legend_item {
			cursor: pointer;
			color: rgba(0, 0, 0, 0.65);
			font-family: PingFang SC;
			font-size: 12px;
			font-style: normal;
			font-weight: 400;
			.item_icon {
				width: 12px;
				height: 8px;
			}
		}
	}
	.charts_fill_class{
		position: relative;
	}
}
</style>
