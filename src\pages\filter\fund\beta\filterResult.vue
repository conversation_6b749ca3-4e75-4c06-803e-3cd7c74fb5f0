<!--  -->
<template>
	<div class="filterNewResult">
		<div style="background: white; padding: 16px 24px 0px 24px">
			<div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 8px">
				<div
					style="
						font-family: 'PingFang';
						font-style: normal;
						font-weight: 500;
						font-size: 16px;
						line-height: 24px;
						text-align: center;
						color: rgba(0, 0, 0, 0.85);
					"
				>
					筛选结果(共{{ pageTotal }}条结果)
				</div>
				<div>
					<el-popover placement="bottom" width="240" trigger="click">
						<drag-list style="height: 416px; overflow: auto" :list="sort_column_list" @resolveFather="getSortList"></drag-list>
						<!-- <table-column ref="tableColumn"></table-column> -->
						<el-button slot="reference" @click="showChooseRow">选择显示列</el-button>
					</el-popover>
					<el-button :disabled="fundlisttemp.length > 4 || fundlisttemp.length <= 1" @click="gotocompare" class="ml-12">比较</el-button>
					<el-button :disabled="disabledfalg" @click="cretezuhequ">创建基金池</el-button>
					<el-button :disabled="disabledfalg" @click="creteportfolio">创建组合</el-button>
					<el-button @click="outExcel" type="">导出过滤结果</el-button>
				</div>
			</div>
			<el-table
				v-loading="loading"
				:data="filterDataList"
				@sort-change="sort_change"
				:cell-style="elcellstyle"
				class="table"
				style="min-height: calc(100vh - 673px)"
				ref="multipleTable"
				:default-sort="{ prop: 'netasset', order: 'descending' }"
				header-cell-class-name="table-header"
				@selection-change="handleSelectzuhe"
				:row-key="getRowKey"
			>
				<el-table-column type="selection" align="gotoleft" :reserve-selection="true" :width="55"> </el-table-column>
				<el-table-column :show-overflow-tooltip="true" prop="name" :min-width="240" align="gotoleft" label="基金名称">
					<template slot-scope="scope"
						><a style="border-bottom: 1px solid #4096ff" @click="godetail(scope.row.code, scope.row.name)">{{
							scope.row.name | isDefault
						}}</a></template
					>
				</el-table-column>
				<el-table-column prop="code" :min-width="100" label="基金代码" align="gotoleft"> </el-table-column>
				<!-- <el-table-column prop="type" :min-width="130" label="基金类型" align="gotoleft">
					<template slot-scope="{ row }">
						<div>{{ fix_type(row.type) }}</div>
					</template>
				</el-table-column> -->
				<el-table-column :min-width="128" prop="manager_name" label="基金经理" align="gotoleft">
					<template slot-scope="scope"
						><a
							style="border-bottom: 1px solid #4096ff"
							@click="godetailP(scope.row.manager_code.split(',')[0], scope.row.manager_name.split(',')[0])"
							>{{ scope.row.manager_name.split(',')[0] | isDefault }}</a
						><span v-if="scope.row.manager_code.split(',').length >= 2"
							>,<a
								style="border-bottom: 1px solid #4096ff"
								@click="godetailP(scope.row.manager_code.split(',')[1], scope.row.manager_name.split(',')[1])"
								>{{ scope.row.manager_name.split(',')[1] | isDefault }}</a
							></span
						></template
					>
				</el-table-column>

				<el-table-column
					v-for="(item, index) in listCol"
					:key="index"
					:prop="item.value"
					:show-overflow-tooltip="true"
					:min-width="item.width ? item.width : 168"
					:sortable="item.children ? false : 'custom'"
					:label="item.label"
					align="gotoleft"
				>
					<el-table-column
						v-show="item.children"
						v-for="obj in item.children"
						:key="item.value + obj.value"
						:prop="obj.value"
						:show-overflow-tooltip="true"
						:min-width="obj.width ? obj.width : 168"
						sortable="custom"
						:label="obj.label"
						align="gotoleft"
					>
						<template slot-scope="{ row }">{{ obj.format ? obj.format(row[obj.value], item.flag, obj.value) : row[obj.value] }}</template>
					</el-table-column>
					<template v-show="!item.children" slot-scope="{ row }">{{
						item.format ? item.format(row[item.value], item.flag, item.value) : row[item.value]
					}}</template>
				</el-table-column>
			</el-table>
			<div class="flex_between mt-16">
				<div style="font-family: 'PingFang'; font-style: normal; font-weight: 400; font-size: 14px; color: rgba(0, 0, 0, 0.65)">
					共{{ pageTotal }}条数据
				</div>
				<div class="flex_start">
					<el-select
						v-model="ps"
						filterable
						allow-create
						default-first-option
						placeholder=""
						style="width: 60px"
						@change="handleSizeChange"
					>
						<el-option v-for="item in ps_list" :key="item.value" :label="item.labe" :value="item.value"> </el-option>
					</el-select>
					<span style="margin-left: 8px; font-size: 13px">条/页</span>
					<el-pagination
						@page-size-change="handleSizeChange"
						@current-change="handlePageChange"
						layout="prev, pager, next, jumper"
						:page-size="pageSize"
						:current-page="pageIndex"
						:total="pageTotal"
					>
					</el-pagination>
				</div>
			</div>
		</div>
		<div style="margin-top: 16px; width: 100%; height: 1px; background: #e9e9e9"></div>
		<div style="text-align: right; margin-right: 24px; padding-top: 24px; padding-bottom: 24px" class="demo-drawer__footer">
			<!-- <el-button @click="changeStep(1)" type="">上一步</el-button> -->
			<!-- <el-button type="primary" @click="changeStep(2)">下一步</el-button> -->
		</div>
		<poolC ref="poolC"></poolC>
		<portC ref="portC"></portC>
	</div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { alphaGo } from '@/assets/js/alpha_type.js';
import poolC from '@/components/components/components/poolcreate/index.vue';
import { TypeMsg } from '@/api/pages/SystemAlpha.js';
import { filter_json_to_excel } from '@/utils/exportExcel.js';
import { AlphaFilterV2 } from '@/api/pages/SystemAlpha.js';
import { BetaFilter } from '@/api/pages/SystemBeta.js';
import portC from '@/components/components/components/portfoliocreat/index.vue';

import dragList from '@/components/components/components/dragList/index.vue';
import tableColumn from '@/pages/fundNewPool/analysis/components/components/tableColumn';

export default {
	//import引入的组件需要注入到对象中才能使用
	components: { poolC, portC, dragList },
	data() {
		//这里存放数据
		return {
			filterData: [],
			filterDataList: [],
			pageIndex: 1,
			pageTotal: 0,
			pageSize: 10,
			ps: 10,
			ps_list: [
				{
					value: 10,
					label: 10
				},
				{
					value: 20,
					label: 20
				},
				{
					value: 50,
					label: 50
				},
				{
					value: 100,
					label: 100
				}
			],
			isMerge: false,
			listCol: [],
			cacheList: [],
			field104Options: [
				{
					label: '波动率', //波动率：区间数据（默认下届为0）
					value: 'volatility'
				},
				{
					label: '最大回撤', //区间数据（默认下届为0）
					value: 'maxdrawdown'
				},
				{
					label: '平均下行周期(天)', //区间数据（默认下届为0）
					value: 'averagelength'
				},
				{
					label: '平均恢复周期(天)', //区间数据（默认下届为0）
					value: 'averagerecovery'
				},
				{
					label: '最大回撤比', //区间数据（默认下届为0.5，上届为1）
					value: 'maxdrawdown_ratio'
				},
				{
					label: '在险价值', //区间数据
					value: 'VaR05'
				},
				{
					label: '期望损失', //区间数据
					value: 'ES05'
				},
				{
					label: '下行风险', //区间数据
					value: 'downsidevolatility'
				},
				{
					label: '波动率比', //区间数据（默认下届为0.5，上届为1）
					value: 'volatilityratio'
				},
				{
					label: '痛苦指数',
					value: 'painindex'
				}
			],
			field105Options: [
				{
					label: '年化收益率',
					value: 'ave_return'
				},
				{
					label: '累计收益率',
					value: 'cum_return'
				},
				{
					label: '夏普率（rf==0）',
					value: 'sharpe0'
				},
				{
					label: '夏普率（rf==4%）',
					value: 'sharpe04'
				},
				{
					label: '夏普率（动态rf）',
					value: 'sharpe'
				},
				{
					label: '卡码率',
					value: 'calmar'
				},
				{
					label: '索提诺系数（rf==0）',
					value: 'sortino0'
				},
				{
					label: '索提诺系数（rf==4%）',
					value: 'sortino04'
				},
				{
					label: '索提诺系数（动态rf）',
					value: 'sortino'
				},
				{
					label: '稳定系数',
					value: 'hurstindex'
				},
				{
					label: '凯利系数',
					value: 'kelly'
				},
				{
					label: '信息比率',
					value: 'information'
				},
				{
					label: '跟踪误差',
					value: 'trackingerror'
				},
				{
					label: '上攻潜力（周）',
					value: 'upsidepotential'
				},
				{
					label: '月胜率',
					value: 'monthly_win_ratio'
				},
				{
					label: '詹森系数',
					value: 'jensen'
				},
				{
					label: '特诺系数',
					value: 'treynor'
				},
				{
					label: '上行捕获',
					value: 'bullreturn'
				},
				{
					label: '下行捕获',
					value: 'bearreturn'
				},
				{
					label: '择时gamma',
					value: 'gamma'
				},
				{
					label: 'M2',
					value: 'msquared'
				}
			],
			disabledfalg: true,
			fundlisttemp: [],
			listSelect: [],
			sort_column_list: [],
			loading: false,
			postData: {}
		};
	},
	//监听属性 类似于data概念
	computed: {
		// sort_column_list() {
		// 	return this.cacheList.map((v) => v.label);
		// }
	},
	//监控data中的数据变化
	watch: {},
	//方法集合
	methods: {
		showChooseRow() {
			// this.sort_column_list = this.cacheList.map((v) => {
			// 	return { ...v, show: true };
			// });
		},
		getSortList() {
			console.log('his.sort_column_list',this.sort_column_list)
			this.listCol = [];
			let timeoutId;
			clearTimeout(timeoutId);
			timeoutId = setTimeout(() => {
				this.listCol = this.sort_column_list.filter((v) => v.show);
			}, 300);
		},
		getRowKey: function (row) {
			return row.code;
		},
		handleSelectzuhe(val) {
			if (val.length > 0) {
				this.disabledfalg = false;
			} else {
				this.disabledfalg = true;
			}

			this.fundlisttemp = val;
		},
		async gotocompare() {
			let tempcode = '';
			let tempname = '';
			let temptype = null;
			for (let i = 0; i < this.fundlisttemp.length; i++) {
				tempcode = tempcode + this.fundlisttemp[i].code + ',';
				tempname = tempname + this.fundlisttemp[i].name + ',';
			}
			tempcode = tempcode.slice(0, tempcode.length - 1);
			tempname = tempname.slice(0, tempname.length - 1);
			let data = await TypeMsg({ code: tempcode });
			if (data) {
				// //console.log(data)
				if (data.data) {
					if (data.data.length == 0) {
						this.$router.push({
							path: '/fundcompareDiff',
							query: {
								id: tempcode,
								type: '',
								types: data.type.join(','),
								name: tempname
							}
						});
						// this.$message.error('请选择具有相同类型的基金进行比较');
					} else if (data.data.length == 1) {
						temptype = data.data[0];
						if (
							temptype == 'bond' ||
							temptype == 'cbond' ||
							temptype == 'purebond' ||
							temptype == 'bill' ||
							temptype.indexOf('equity') >= 0 ||
							temptype == 'obond'
						) {
							this.$router.push({
								path: '/fundcompare',
								query: {
									id: tempcode,
									type: temptype,
									name: tempname
								}
							});
						} else {
							this.$message('暂时只提供主动权益，二级债，债券类产品的比较');
						}
					} else if (data.data.length > 1) {
						this.$router.push({
							path: '/fundcompareDiff',
							query: {
								id: tempcode,
								type: '',
								types: data.type.join(','),
								name: tempname
							}
						});
						// this.$message.error('请选择具有相同类型的基金进行比较');
						// this.showitem = true
						// this.options = []
						// for(let i = 0; i < data.data.length; i++){
						//     this.options.push({value:data.data[i],label:data.data[i]})
						// }
					}
				}
			}
		},
		creteportfolio() {
			this.$refs.portC.show(this.fundlisttemp, 'beta');
		},
		cretezuhequ() {
			this.listSelect = JSON.parse(this.localStorage.getItem('mty_filterListSelect')) || [];
			let formData = {};
			formData['recent_time'] = this.getTime();
			formData['isUpDown'] = this.isSame;
			formData['fund_netasset'] = [];
			formData['manager_netasset'] = [];
			formData['managed_time'] = [];
			formData['isbigforpid'] = [];
			formData['equitytype'] = [];
			formData['fund_year'] = [];
			formData['field103'] = [];
			formData['risk_feature'] = [];
			formData['risk_return_feature'] = [];
			formData['style'] = [];
			formData['industry'] = [];
			formData['stock_class'] = [];
			formData['index_code'] = [];
			formData['conceration'] = [];
			formData['roe'] = [];
			formData['turnover'] = [];
			formData['deltaweight'] = [];
			formData['managed_year'] = [];
			formData['closed_or_open'] = [];
			formData['constraint_equity_or_bond'] = { equity: [], bond: [] };

			for (let i = 0; i < this.listSelect.length; i++) {
				for (let j = 0; j < this.listSelect[i].data.length; j++) {
					if (this.listSelect[i].data[j].labelName == '基金规模') {
						formData['fund_netasset'].push({
							value: this.listSelect[i].data[j].dataResult[0].value,
							operation: this.listSelect[i].data[j].dataResult[0].flag
						});
					} else if (this.listSelect[i].data[j].labelName == '基金经理规模') {
						formData['manager_netasset'].push({
							value: this.listSelect[i].data[j].dataResult[0].value,
							operation: this.listSelect[i].data[j].dataResult[0].flag
						});
					} else if (this.listSelect[i].data[j].labelName == '基金经理管理经验') {
						formData['managed_time'].push({
							value: this.listSelect[i].data[j].dataResult[0].value,
							operation: this.listSelect[i].data[j].dataResult[0].flag
						});
					} else if (this.listSelect[i].data[j].labelName == '基金存续时长') {
						formData['fund_year'].push({
							value: this.listSelect[i].data[j].dataResult[0].value,
							operation: this.listSelect[i].data[j].dataResult[0].flag
						});
					} else if (this.listSelect[i].data[j].labelName == '基金经理任职时长') {
						formData['managed_year'].push({
							value: this.listSelect[i].data[j].dataResult[0].value,
							operation: this.listSelect[i].data[j].dataResult[0].flag
						});
					} else if (this.listSelect[i].data[j].labelName == '股票') {
						formData['constraint_equity_or_bond']['equity'].push({
							value: this.listSelect[i].data[j].dataResult[0].value,
							operation: this.listSelect[i].data[j].dataResult[0].flag
						});
					} else if (this.listSelect[i].data[j].labelName == '债券') {
						formData['constraint_equity_or_bond']['bond'].push({
							value: this.listSelect[i].data[j].dataResult[0].value,
							operation: this.listSelect[i].data[j].dataResult[0].flag
						});
					} else if (this.listSelect[i].data[j].labelName == '可申购金额') {
						formData['isbigforpid'].push({
							value: this.listSelect[i].data[j].dataResult[0].value,
							operation: this.listSelect[i].data[j].dataResult[0].flag
						});
					} else if (this.listSelect[i].data[j].labelName == '基金分类') {
						formData['equitytype'].push({ value: this.listSelect[i].data[j].dataResult[0].value });
					} else if (this.listSelect[i].data[j].labelName == '基金持有人特征') {
						formData['field103'].push({ value: this.listSelect[i].data[j].dataResult[0].value });
					} else if (this.field104Options.findIndex((item) => item.label == this.listSelect[i].data[j].labelName) >= 0) {
						formData['risk_feature'].push({
							value: Number(this.listSelect[i].data[j].dataResult[0].value / 100),
							name: this.field104Options[this.field104Options.findIndex((item) => item.label == this.listSelect[i].data[j].labelName)]
								.value,
							operation: this.listSelect[i].data[j].dataResult[0].flag,
							recent_time: this.listSelect[i].data[j].dataResult[0].date[1]
						});
					} else if (this.field105Options.findIndex((item) => item.label == this.listSelect[i].data[j].labelName) >= 0) {
						formData['risk_return_feature'].push({
							value: Number(this.listSelect[i].data[j].dataResult[0].value / 100),
							name: this.field105Options[this.field105Options.findIndex((item) => item.label == this.listSelect[i].data[j].labelName)]
								.value,
							operation: this.listSelect[i].data[j].dataResult[0].flag,
							recent_time: this.listSelect[i].data[j].dataResult[0].date[1]
						});
					} else if (this.listSelect[i].data[j].labelName == '成长' || this.listSelect[i].data[j].labelName == '价值') {
						formData['style'].push({
							name: this.listSelect[i].data[j].labelName,
							recent_time: this.listSelect[i].data[j].dataResult[0].yearqtr,
							rank_value: this.listSelect[i].data[j].dataResult[0].rank_value,
							value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
							operation: this.listSelect[i].data[j].dataResult[0].flag
						});
					} else if (this.listSelect[i].data[j].labelName == '估值') {
						formData['style'].push({
							name: this.listSelect[i].data[j].labelName,
							recent_time: this.listSelect[i].data[j].dataResult[0].yearqtr,
							rank_value: this.listSelect[i].data[j].dataResult[0].rank_value,
							value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
							operation: this.listSelect[i].data[j].dataResult[0].flag
						});
					} else if (this.listSelect[i].data[j].labelName == '申万一级行业') {
						formData['industry'].push({
							intersection: this.isMerge,
							industry_section: '申万(2021)',
							industry_code: this.listSelect[i].data[j].dataResult[0].industryValue,
							value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
							operation: this.listSelect[i].data[j].dataResult[0].flag
						});
					} else if (this.listSelect[i].data[j].labelName == '申万二级行业') {
						formData['industry'].push({
							intersection: this.isMerge,
							industry_section: '申万二级(2021)',
							industry_code: this.listSelect[i].data[j].dataResult[0].industryValue,
							value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
							operation: this.listSelect[i].data[j].dataResult[0].flag
						});
					} else if (this.listSelect[i].data[j].labelName == '申万三级行业') {
						formData['industry'].push({
							intersection: this.isMerge,
							industry_section: '申万三级(2021)',
							industry_code: this.listSelect[i].data[j].dataResult[0].industryValue,
							value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
							operation: this.listSelect[i].data[j].dataResult[0].flag
						});
					} else if (this.listSelect[i].data[j].labelName == '恒生一级行业') {
						formData['industry'].push({
							intersection: this.isMerge,
							industry_section: '恒生一级',
							industry_code: this.listSelect[i].data[j].dataResult[0].industryValue,
							value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
							operation: this.listSelect[i].data[j].dataResult[0].flag
						});
					} else if (this.listSelect[i].data[j].labelName == '行业超低配') {
						formData['industry'].push({
							value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
							operation: this.listSelect[i].data[j].dataResult[0].flag
						});
					} else if (this.listSelect[i].data[j].labelName == '大行业') {
						formData['industry'].push({
							intersection: this.isMerge,
							industry_section: '大行业',
							industry_code: this.listSelect[i].data[j].dataResult[0].industryValue,
							value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
							operation: this.listSelect[i].data[j].dataResult[0].flag
						});
					} else if (this.listSelect[i].data[j].labelName == '主题判断') {
						formData['stock_class'].push({
							stockclass: this.listSelect[i].data[j].dataResult[0].industryValue,
							value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
							operation: this.listSelect[i].data[j].dataResult[0].flag
						});
					} else if (this.listSelect[i].data[j].labelName == '指数匹配') {
						formData['index_code'].push({
							index_code: this.listSelect[i].data[j].dataResult[0].index_code,
							value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
							operation: this.listSelect[i].data[j].dataResult[0].flag
						});
					} else if (this.listSelect[i].data[j].labelName == '前十大集中度') {
						formData['conceration'].push({
							value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
							operation: this.listSelect[i].data[j].dataResult[0].flag
						});
					} else if (this.listSelect[i].data[j].labelName == 'ROE') {
						formData['roe'].push({
							value: this.listSelect[i].data[j].dataResult[0].value,
							operation: this.listSelect[i].data[j].dataResult[0].flag
						});
					} else if (this.listSelect[i].data[j].labelName == '换手率') {
						formData['turnover'].push({
							value: Number(this.listSelect[i].data[j].dataResult[0].value) / 100,
							operation: this.listSelect[i].data[j].dataResult[0].flag
						});
					}
				}
			}
			let dataobj = {
				clicktype: true,
				ftype: 'equity',
				ismanager: false,
				formData: formData
			};
			this.$refs.poolC.show(this.fundlisttemp, dataobj, 'beta');
		},
		getTime() {
			let radioType = JSON.parse(this.localStorage.getItem('mty_filterradioType'));
			let radioInput = JSON.parse(this.localStorage.getItem('mty_filterradioInput'));
			let flag = 0;
			if (radioType == 'latest') {
				flag = 90;
				return this.moment(Date.now() - 86400000 * flag).format('YYYY-MM-DD');
			} else if (radioType == '3') {
				flag = 3 * 365;
				return this.moment(Date.now() - 86400000 * flag).format('YYYY-MM-DD');
			} else if (radioType == '6') {
				flag = 6 * 365;
				return this.moment(Date.now() - 86400000 * flag).format('YYYY-MM-DD');
			} else if (radioType == 'radioSelf') {
				flag = 365 * Number(radioInput);
				return this.moment(Date.now() - 86400000 * flag).format('YYYY-MM-DD');
			} else if (radioType == 'created') {
				return '2000-01-01';
			} else {
				flag = 90;
				return this.moment(Date.now() - 86400000 * flag).format('YYYY-MM-DD');
			}
		},
		godetail(id, name) {
			//带参进去
			alphaGo(id, name, this.$route.path);
		},
		godetailP(id, name) {
			//带参进去
			alphaGo(id, name, this.$route.path);
		},
		// 获取请求条件
		getRequestPostData(postData) {
			this.postData = postData;
		},
		// 重新获取数据
		async getRequest() {
			console.log(this.listCol);
			this.loading = true;
			let data = await BetaFilter(this.postData);
			this.loading = false;
			this.getData(data, 'refresh');
		},
		getData({ data, total }, type) {
			this.pageTotal = total;
			try {
				this.localStorage.setItem('mty_filter_new_result', JSON.stringify(data));
			} catch (e) {
				this.localStorage.setItem('mty_filter_new_result', JSON.stringify([]));
			}
			this.cacheList = [];
			let flag = 0;
			let tempKey = [];

			if (data.length > 0) {
				for (var key in data[0]) {
					if (key.includes('_mathRange_')) {
						if (key.split('_').length >= 4) {
							let label = key.split('_')[key.split('_').length - 1];
							if (label == '轮动值' || label == '相似度') {
								this.formatColumnList({
									key,
									label: key.replace(/.*_(.*)_(.*).*$/, '$1$2'),
									value: key.split('_mathRange_')?.[1],
									flag: 4
								});
							} else {
								if (key.includes('重仓') || key.includes('全持仓')) {
									this.formatColumnList({
										key,
										label: key.replace(/.*_(.+)_+(.+).*/, '$1$2占比'),
										value: key.split('_mathRange_')?.[1],
										flag: 4
									});
								} else {
									this.formatColumnList({
										key,
										label: key.replace(/.*_(.*)_+(.*).*$/, '$1占$2值比'),
										value: key.split('_mathRange_')?.[1],
										flag: 4
									});
								}
							}
						} else if (key.split('_').length == 3) {
							this.formatColumnList({
								key,
								label: key.replace(/.*(.*)_(.*)$/, '$2'),
								value: key.split('_mathRange_')?.[1],
								flag: 4
							});
						}
					} else if (key.indexOf('scale') >= 0) {
					} else if (key.indexOf('index_name') >= 0) {
						this.formatColumnList({ key, label: '跟踪标的', value: 'index_name', flag: 3 });
					} else if (key.indexOf('managed_year') >= 0) {
						this.formatColumnList({ key, label: '基金经理任职时长', value: 'managed_year' });
					} else if (key.indexOf('managed_time') >= 0) {
						this.formatColumnList({ key, label: '基金经理管理经验', value: 'managed_time' });
					} else if (key.indexOf('fund_year') >= 0) {
						this.formatColumnList({ key, label: '基金存续时长', value: 'fund_year' });
					} else if (key.indexOf('largeapplyingmax') >= 0) {
						this.formatColumnList({ key, label: '申购（万）', value: 'largeapplyingmax' });
					} else if (key.indexOf('value_rank') >= 0) {
						this.formatColumnList({ key, label: '价值', value: 'value_rank' });
					} else if (key.indexOf('growth_rank') >= 0) {
						this.formatColumnList({ key, label: '成长', value: 'growth_rank' });
					} else if (key.indexOf('bp_rank') >= 0) {
						this.formatColumnList({ key, label: '估值', value: 'bp_rank' });
					} else if (key.indexOf('momentum_rank') >= 0) {
						this.formatColumnList({ key, label: '动量', value: 'momentum_rank' });
					} else if (key.indexOf('earningyield_rank') >= 0) {
						this.formatColumnList({ key, label: '盈利', value: 'earningyield_rank' });
					} else if (key.indexOf('section_weight') >= 0) {
						let label = key.replace(/.*_mathRange_(申万(.*)_季度)_section_weight/, '$1景气度');
						this.formatColumnList({ key, label, value: key.split('_mathRange_')?.[1] });
					} else if (key.indexOf('wheel_dynamic_weight') >= 0) {
						let label = key.replace(/avg_mathRange_(.*)_申万((.*))_wheel_dynamic_weight/, '申万$2$1轮动值');
						this.formatColumnList({ key, label, value: key.split('_mathRange_')?.[1] });
					} else if (key.indexOf('FdurationReport') >= 0) {
						this.formatColumnList({ key, label: '估算报告久期', value: key, flag: 4 });
					} else if (key.indexOf('durationReport') >= 0) {
						this.formatColumnList({ key, label: '报告久期', value: key, flag: 4 });
					} else if (key.indexOf('credit_down') >= 0) {
						this.formatColumnList({ key, label: '信用挖掘', value: key, flag: 3 });
					} else if (key.indexOf('beta_rank') >= 0) {
						this.formatColumnList({ key, label: '贝塔', value: 'beta_rank' });
					} else if (key.indexOf('mean_conceration') >= 0) {
						this.formatColumnList({ key, label: '前十大集中度', value: 'mean_conceration' });
					} else if (key.indexOf('mean_turnover') >= 0) {
						this.formatColumnList({ key, label: '换手率', value: 'mean_turnover' });
					} else if (key.indexOf('行业超低配') >= 0) {
						this.formatColumnList({ key, label: '行业超低配', value: '行业超低配' });
					} else if (key.indexOf('mean_roe') >= 0) {
						this.formatColumnList({ key, label: 'ROE', value: 'mean_roe' });
					} else if (key.indexOf('股票关注期') >= 0) {
						this.formatColumnList({ key, value: '股票关注期', label: '股票关注期', flag: 1, type: 'first' });
					} else if (key.indexOf('monthly_win_ratio') >= 0) {
						this.formatColumnList({
							key,
							value: 'monthly_win_ratio',
							label: '胜率',
							flag: key.indexOf('percent') >= 0 ? 1 : 0,
							type: 'first'
						});
					} else if (key.indexOf('持有股票抱团度') >= 0) {
						this.formatColumnList({ key, label: '持有股票抱团度', value: '持有股票抱团度' });
					}
					else if (key=='A股') {
						this.formatColumnList({ key, label: 'A股', value: 'A股' });
					} 
					else if (key=='港股') {
						this.formatColumnList({ key, label: '港股', value: '港股' });
					} 
					else if (key=='美股') {
						this.formatColumnList({ key, label: '美股', value: '美股' });
					} 
					else if (key=='台股') {
						this.formatColumnList({ key, label: '台股', value: '台股' });
					} 
					else if (key.indexOf('lose_ratio') >= 0) {
						this.formatColumnList({ key, label: '赔率', value: 'lose_ratio' });
					} else if (key.indexOf('win_ratio') >= 0) {
						this.formatColumnList({ key, label: '胜率', value: 'win_ratio' });
					} else if (key.indexOf('买入') >= 0) {
						this.formatColumnList({ key, label: key, value: key, flag: 1 });
					} else if (key.indexOf('卖出') >= 0) {
						this.formatColumnList({ key, label: key, value: key, flag: 1 });
					} else if (key.indexOf('hkequity_weight_avg') >= 0) {
						this.formatColumnList({ key, label: '港股仓位中枢', value: key, flag: 4 });
					} else if (key.indexOf('hkequity_weight_chg') >= 0) {
						this.formatColumnList({ key, label: '港股仓位变化', value: key, flag: 3 });
					} else if (key.indexOf('hkequity_weight') >= 0) {
						this.formatColumnList({ key, label: '港股仓位占比', value: key, flag: 4 });
					} else if (key.indexOf('equity_weight_avg') >= 0) {
						this.formatColumnList({ key, label: 'A股仓位中枢', value: key, flag: 4 });
					} else if (key.indexOf('equity_weight_chg') >= 0) {
						this.formatColumnList({ key, label: 'A股仓位变化', value: key, flag: 3 });
					} else if (key.indexOf('equity_weight') >= 0) {
						if (key.includes('equity_weight_asset')) {
							this.formatColumnList({ key, label: '股票(总)仓位占比', value: 'equity_weight_asset', flag: 4 });
						} else {
							this.formatColumnList({ key, label: '股票(净)仓位占比', value: 'equity_weight', flag: 4 });
						}
					} else if (key.indexOf('interest_weight_avg') >= 0) {
						this.formatColumnList({ key, label: '利率债仓位中枢', value: key, flag: 4 });
					} else if (key.indexOf('interest_weight_chg') >= 0) {
						this.formatColumnList({ key, label: '利率债仓位变化', value: key, flag: 3 });
					} else if (key.indexOf('interest_weight') >= 0) {
						if (key.includes('interest_weight_asset')) {
							this.formatColumnList({ key, label: '利率债(总)仓位占比', value: 'interest_weight_asset', flag: 4 });
						} else {
							this.formatColumnList({ key, label: '利率债(净)仓位占比', value: 'interest_weight', flag: 4 });
						}
					} else if (key.indexOf('cbond_weight_avg') >= 0) {
						this.formatColumnList({ key, label: '转债仓位中枢', value: key, flag: 4 });
					} else if (key.indexOf('cbond_weight_chg') >= 0) {
						this.formatColumnList({ key, label: '转债仓位变化', value: key, flag: 3 });
					} else if (key.indexOf('cbond_weight') >= 0) {
						if (key.includes('cbond_weight_asset')) {
							this.formatColumnList({ key, label: '转债(总)仓位占比', value: 'cbond_weight_asset', flag: 4 });
						} else {
							this.formatColumnList({ key, label: '转债(净)仓位占比', value: 'cbond_weight', flag: 4 });
						}
					} else if (key.indexOf('cbond_style') >= 0) {
						this.formatColumnList({ key, label: '转债类型', value: key, flag: 3 });
					} else if (key.indexOf('bond_weight_avg') >= 0) {
						this.formatColumnList({ key, label: '债券仓位中枢', value: key, flag: 4 });
					} else if (key.indexOf('bond_weight') >= 0) {
						if (key.includes('bond_weight_asset')) {
							this.formatColumnList({ key, label: '债券(总)仓位占比', value: 'bond_weight_asset', flag: 4 });
						} else {
							this.formatColumnList({ key, label: '债券(净)仓位占比', value: 'bond_weight', flag: 4 });
						}
					} else if (key.indexOf('fund_weight') >= 0) {
						if (key.includes('fund_weight_asset')) {
							this.formatColumnList({ key, label: '基金(总)仓位占比', value: 'fund_weight_asset', flag: 4 });
						} else {
							this.formatColumnList({ key, label: '基金(净)仓位占比', value: 'fund_weight', flag: 4 });
						}
					} else if (key.indexOf('option_weight') >= 0) {
						if (key.includes('option_weight_asset')) {
							this.formatColumnList({ key, label: '权证(总)仓位占比', value: 'option_weight_asset', flag: 4 });
						} else {
							this.formatColumnList({ key, label: '权证(净)仓位占比', value: 'option_weight', flag: 4 });
						}
					} else if (key.indexOf('repo_weight') >= 0) {
						if (key.includes('repo_weight_asset')) {
							this.formatColumnList({ key, label: '质押式回购(总)仓位占比', value: 'repo_weight_asset', flag: 4 });
						} else {
							this.formatColumnList({ key, label: '质押式回购(净)仓位占比', value: 'repo_weight', flag: 4 });
						}
					} else if (key.indexOf('cash_weight') >= 0) {
						if (key.includes('cash_weight_asset')) {
							this.formatColumnList({ key, label: '现金及等价物(总)仓位占比', value: 'cash_weight_asset', flag: 4 });
						} else {
							this.formatColumnList({ key, label: '现金及等价物(净)仓位占比', value: 'cash_weight', flag: 4 });
						}
					} else if (key.indexOf('abs_weight') >= 0) {
						if (key.includes('abs_weight_asset')) {
							this.formatColumnList({ key, label: '资产支持证券(总)仓位占比', value: 'abs_weight_asset', flag: 4 });
						} else {
							this.formatColumnList({ key, label: '资产支持证券(净)仓位占比', value: 'abs_weight', flag: 4 });
						}
					} else if (key.indexOf('active_weight') >= 0) {
						if (key.includes('active_weight_asset')) {
							this.formatColumnList({ key, label: '主动投资占比(总)仓位占比', value: 'active_weight_asset', flag: 4 });
						} else {
							this.formatColumnList({ key, label: '主动投资占比(净)仓位占比', value: 'active_weight', flag: 4 });
						}
					} else if (key.indexOf('passive_weight') >= 0) {
						if (key.includes('passive_weight_asset')) {
							this.formatColumnList({ key, label: '被动投资占比(净)仓位占比', value: 'passive_weight_asset', flag: 4 });
						} else {
							this.formatColumnList({ key, label: '被动投资占比(净)仓位占比', value: 'passive_weight', flag: 4 });
						}
					} else if (key.indexOf('other_weight') >= 0) {
						if (key.includes('other_weight_asset')) {
							this.formatColumnList({ key, label: '其他占比(净)仓位占比', value: 'other_weight_asset', flag: 4 });
						} else {
							this.formatColumnList({ key, label: '其他占比(净)仓位占比', value: 'other_weight', flag: 4 });
						}
					} else if (key.indexOf('similar_rank') >= 0) {
						this.formatColumnList({ key, label: key.replace(/.*_(.*)_(.*)_.*$/, '$1相似度排名'), value: key, flag: 4 });
						// _similar_rank
					} else if (key.includes('_净') || key.includes('_总') || key.includes('_债')) {
						if (key.includes('_净')) {
							this.formatColumnList({
								key,
								label: key.replace(/.*_(.*)_(.*)_.*$/, '$1占净值比'),
								value: key.replace(/.*_(.*)_(.*)_.*$/, '$1占净值比'),//key.split('_mathRange_')?.[1],
								flag: 4
							});
						} else if (key.includes('_总')) {
							this.formatColumnList({
								key,
								label: key.replace(/.*_(.*)_(.*)_.*$/, '$1占总资产比'),
								value: key.replace(/.*_(.*)_(.*)_.*$/, '$1占总资产比'), //key.split('_mathRange_')?.[1],
								flag: 4
							});
						} else if (key.includes('_债')) {
							this.formatColumnList({
								key,
								label: key.replace(/.*_(.*)_(.*)_.*$/, '$1占债券比'),
								value:  key.replace(/.*_(.*)_(.*)_.*$/, '$1占总资产比'),//key.split('_mathRange_')?.[1],
								flag: 4
							});
						}
					} else if (key.includes('new_mathRange_') && key.includes('_weight')) {
						if (key.includes('_hold_weight')) {
							this.formatColumnList({
								key,
								value: key.split('_mathRange_')?.[1],
								label: key.replace(/^.*_(.*)_.*_.*$/, '最近一期$1仓位占比'),
								flag: flag ? flag : key.indexOf('percent') >= 0 ? 1 : 0,
								type: 'first'
							});
						} else {
							this.formatColumnList({
								key,
								value: key.split('_mathRange_')?.[1],
								label: key.replace(/^.*_(.*)_.*$/, '最近一期$1仓位占比'),
								flag: flag ? flag : key.indexOf('percent') >= 0 ? 1 : 0,
								type: 'first'
							});
						}
					} else if (key.includes('_mathRange_') && key.includes('_weight')) {
						if (key.includes('_hold_weight') || key.includes('_weight_全持仓') || key.includes('_weight_重仓')) {
							this.formatColumnList({ key, label: key.replace(/^.*_(.*)_.*_.*$/, '$1仓位占比'), value: key.split('_mathRange_')?.[1] });
						} else {
							this.formatColumnList({ key, label: key.replace(/^.*_(.*)_.*$/, '$1仓位占比'), value: key.split('_mathRange_')?.[1] });
						}
					} else if (key.indexOf('_weight') >= 0) {
						this.formatColumnList({ key, label: key.replace('_weight', '') + '匹配度', value: key.split('_mathRange_')?.[1] });
						flag = 1;
						tempKey.push(key);
					} else if (key.indexOf('mean_top1_conceration_rank') >= 0) {
						this.formatColumnList({ key, label: '前一大个股集中度', value: key });
					} else if (key.indexOf('mean_top3_conceration_rank') >= 0) {
						this.formatColumnList({ key, label: '前三大个股集中度', value: key });
					} else if (key.indexOf('mean_top5_conceration_rank') >= 0) {
						this.formatColumnList({ key, label: '前五大个股集中度', value: key });
					} else if (key.indexOf('_rank') >= 0) {
						if (this.field104Options.concat(this.field105Options).findIndex((item) => item.value == key.replace('_rank', '')) >= 0) {
							this.formatColumnList({
								key,
								label: this.field104Options.concat(this.field105Options)[
									this.field104Options.concat(this.field105Options).findIndex((item) => item.value == key.replace('_rank', ''))
								].label,
								value: key
							});
						}
					} else if (
						key != '1w' &&
						key != '1m' &&
						key != '1q' &&
						key != '1y' &&
						key != 'code' &&
						key != 'manager_code' &&
						key != 'manager_name' &&
						key != 'name' &&
						key != 'netasset'
					) {
						if (this.field104Options.concat(this.field105Options).findIndex((item) => item.value == key) >= 0) {
							this.formatColumnList({
								key,
								label: this.field104Options.concat(this.field105Options)[
									this.field104Options.concat(this.field105Options).findIndex((item) => item.value == key)
								].label,
								value: key.includes('_mathRange_') ? key.split('_mathRange_')?.[1] : key
							});
						}
					}
				}
			}
			this.filterDataList = data;
			if (type == 'refresh') {
				return;
			} else {
				// 首次进入
				this.pageIndex = 1;
				this.pageSize = 10;
				this.cacheList.push(
					{
						label: '近一周收益率',
						value: '1w',
						format: this.fix2p,
						width: '128px'
					},
					{
						label: '近一月收益率',
						value: '1m',
						format: this.fix2p,
						width: '128px'
					},
					{
						label: '近一季收益率',
						value: '1q',
						format: this.fix2p,
						width: '128px'
					},
					{
						label: '近一年收益率',
						value: '1y',
						format: this.fix2p,
						width: '128px'
					},
					{
						label: '近三年收益率',
						value: '3y',
						format: this.fix2p,
						width: '128px'
					},
					{
						label: '今年以来收益率',
						value: 'this_year',
						format: this.fix2p,
						width: '128px'
					},
					{
						label: '规模',
						value: 'netasset',
						format: this.fix10Y,
						width: '100px'
					},
					{
						label: '重仓',
						value: 'bigs',
						width: '328px',
						tooltip: true
					}
				);
				console.log(this.cacheList)
				this.listCol = [];
				this.$nextTick(() => {
					this.listCol = this.cacheList;
					this.sort_column_list = this.cacheList.map((v) => {
						return { ...v, show: true };
					});
				});
			}
		},
		handleSizeChange(val) {
			this.pageSize = val * 1;
			this.postData = { ...this.postData, page_size: this.pageSize };
			this.getRequest();
		},
		handlePageChange(val) {
			this.pageIndex = val;
			this.postData = { ...this.postData, page: this.pageIndex };
			this.getRequest();
		},
		// 清空数据
		clearTableData() {
			this.filterDataList = [];
			this.pageIndex = 1;
			this.pageSize = 10;
			this.pageTotal = 0;
		},
		// 格式化表头
		formatColumnList({ key, label, value, flag, type }) {
			let index = this.cacheList.findIndex((item) => {
				return item.value == value;
			});
			if (index == -1) {
				if (type == 'first') {
					this.cacheList.push({
						value: key,
						label: label,
						format: this.fix2pa,
						flag: flag ? flag : key.indexOf('percent') >= 0 ? 1 : 0
					});
				} else {
					if (key.includes('_mathRange')) {
						this.cacheList.push({
							value: value,
							label: label,
							flag: flag ? flag : key.indexOf('percent') >= 0 ? 1 : 0,
							children: [
								{
									label: this.formatKey(key.split('_mathRange')?.[0]),
									value: key,
									format: this.fix2pa,
									flag: key.indexOf('percent') >= 0 ? 1 : 0
								}
							]
						});
					} else {
						this.cacheList.push({
							value: value,
							label: label,
							format: this.fix2pa,
							flag: flag ? flag : key.indexOf('percent') >= 0 ? 1 : 0
						});
					}
				}
			} else {
				let i = this.cacheList[index].children.findIndex((item) => {
					return item.value == key;
				});
				if (i == -1) {
					this.cacheList[index].children.push({
						label: this.formatKey(key.split('_mathRange')?.[0]),
						value: key,
						format: this.fix2pa,
						flag: flag ? flag : key.indexOf('percent') >= 0 ? 1 : 0
					});
				}
			}
		},
		// 格式化key
		formatKey(val) {
			switch (val) {
				case 'avg':
					return '平均';
				case 'max':
					return '最大';
				case 'min':
					return '最小';
				case 'new':
					return '最新';
				case 'range':
					return '范围';
				// case 'all':
				// 	return '平均';
				case 'prod':
					return '平均';
			}
		},
		fix2pa(value, type, valueType) {
			if (
				valueType.includes('maxdrawdown_ratio') ||
				valueType.includes('volatilityratio') ||
				valueType.includes('jensen') ||
				valueType == 'averagelength' ||
				valueType == 'averagerecovery' ||
				valueType == 'painindex' ||
				valueType == 'sharpe' ||
				valueType == 'sharpe0' ||
				valueType == 'sharpe04' ||
				valueType == 'calmar' ||
				valueType == 'sortino0' ||
				valueType == 'sortino04' ||
				valueType == 'sortino' ||
				valueType == 'hurstindex' ||
				valueType == 'kelly' ||
				valueType == 'information' ||
				valueType == 'upsidepotential' ||
				valueType == 'gamma' ||
				valueType.includes('ROE')
			) {
				return (value * 1 && !isNaN(value * 1)) || value * 1 == 0 ? (value * 1).toFixed(2) : '--';
			}
			if (
				valueType.includes('conceration_rank') ||
				valueType.includes('hold_weight') ||
				valueType.includes('equity_weight') ||
				valueType.includes('bond_weight') ||
				valueType.includes('fund_weight') ||
				valueType.includes('option_weight') ||
				valueType.includes('repo_weight') ||
				valueType.includes('cash_weight') ||
				valueType.includes('abs_weight') ||
				valueType.includes('active_weight') ||
				valueType.includes('passive_weight') ||
				valueType.includes('other_weight') ||
				valueType.includes('cbond_weight') ||
				valueType.includes('equity_weight_avg') ||
				valueType.includes('bond_weight_avg') ||
				valueType.includes('cbond_weight_avg') ||
				valueType.includes('new_mathRange') ||
				valueType.includes('A股') ||
				valueType.includes('港股') ||
				valueType.includes('美股') ||
				valueType.includes('台股') ||
				valueType.includes('政策性金融债') ||
				valueType.includes('金融债') ||
				valueType.includes('国债') ||
				valueType.includes('同业存单') ||
				valueType.includes('央行票据') ||
				valueType.includes('地方政府债') ||
				valueType.includes('公司债') ||
				valueType.includes('短期融资券') ||
				valueType.includes('中期票据') ||
				valueType.includes('中小企业私募债') ||
				valueType.includes('可转债') ||
				valueType.includes('企业债') ||
				valueType.includes('其他债券')
				// valueType.includes('mathRange') ||
				// 基金大类资产约束
				// valueType.includes('_净') ||
				// valueType.includes('_总') ||
				// valueType.includes('_债')
			) {
				if (value == 'nan' || value == '--' || value == '- -' || value == '') return '--';
				else return (value * 1 && !isNaN(value * 1)) || value * 1 == 0 ? (value * 1).toFixed(2) + '%' : '--';
			} else if (
				valueType == 'treynor' ||
				valueType.includes('msquared') ||
				valueType.includes('bullreturn') ||
				valueType.includes('bearreturn') ||
				valueType.includes('mathRange') ||
				valueType.includes('ave_return') ||
				valueType.includes('similar_rank') ||
				valueType.includes('cum_return') ||
				valueType.includes('volatility') ||
				valueType.includes('maxdrawdown') ||
				valueType.includes('VaR05') ||
				valueType.includes('ES05') ||
				valueType.includes('downsidevolatility') ||
				valueType.includes('monthly_win_ratio') ||
				valueType.includes('mean_conceration') ||
				valueType.includes('win_ratio') ||
				valueType.includes('lose_ratio') ||
				valueType.includes('trackingerror') ||
				valueType.includes('value_rank') ||
				valueType.includes('growth_rank') ||
				valueType.includes('bp_rank') ||
				valueType.includes('momentum_rank') ||
				valueType.includes('earningyield_rank') ||
				valueType.includes('beta_rank') ||
				valueType.includes('持有股票抱团度') ||
				valueType.includes('股票关注期') ||
				valueType.includes('股票换手率') ||
				valueType.indexOf('买入') !== -1 ||
				valueType.indexOf('卖出') !== -1 ||
				valueType == '行业超低配' ||
				(valueType.indexOf('_weight') >= 0 &&
					valueType != 'equity_weight_chg' &&
					valueType != 'hkequity_weight_chg' &&
					valueType != 'cbond_weight_chg' &&
					valueType != 'interest_weight_chg' &&
					valueType != 'bond_weight' &&
					valueType != 'bond_weight_avg')
			) {
				return (value * 1 && !isNaN(value * 1)) || value * 1 == 0 ? (value * 100).toFixed(2) + '%' : '--';
			}

			if (valueType.indexOf('year') != -1 || valueType == 'managed_time') {
				if (value == 'nan' || value == '--' || value == '- -' || value == '') return '--年';
				else return Number(value).toFixed(2) + '年';
			}

			if (type && type == 1) {
				return (value * 1 && !isNaN(value * 1)) || value * 1 == 0 ? (value * 100).toFixed(2) + '%' : '--';
			} else if (type && type == 3) {
				return value;
			} else {
				return (value * 1 && !isNaN(value * 1)) || value * 1 == 0 ? (value * 1).toFixed(2) : '--';
			}
		},
		fix_type(value) {
			let list = [
				{ en: 'equity', zh: '主动权益' },
				{ en: 'equityhk', zh: '港股基金' },
				{ en: 'equityindex', zh: '被动权益指数' },
				{ en: 'equitywithhk', zh: '含港股主动权益' },
				{ en: 'equityenhance', zh: '指数增强' },
				{ en: 'equityhk-index', zh: '港股指数' },
				{ en: 'bond', zh: '固收+' },
				{ en: 'cbond', zh: '可转债' },
				{ en: 'purebond', zh: '纯债' },
				{ en: 'bill', zh: '中短债' },
				{ en: 'money', zh: '货币' },
				{ en: 'obond', zh: '其他债券' },
				{ en: 'bondindex', zh: '二级债基指数' },
				{ en: 'fof', zh: 'FOF' },
				{ en: 'fof:target-date', zh: '目标日期型fof' },
				{ en: 'fof:target-risk', zh: '目标风险型fof' },
				{ en: 'QDII', zh: 'QDII' },
				{ en: 'other', zh: '其他' }
			];
			return list.find((v) => v.en == value)?.zh || value;
		},
		fix2p(val) {
			return (val * 1 && !isNaN(val * 1)) || val * 1 == 0 ? (val * 100).toFixed(2) + '%' : '--';
		},
		fix10Y(value) {
			return (Number(value) / 100000000).toFixed(2) + '亿';
		},
		sort_change(column) {
			this.postData = {
				...this.postData,
				sort: column.prop,
				ascend: column.order === 'descending' ? 0 : column.order === 'ascending' ? 1 : 2,
				page: 1
			};
			this.pageIndex = 1;
			this.getRequest();
		},
		elcellstyle({ row, column, rowIndex, columnIndex }) {
			let t = this.listCol.length + 1;
			if (column.property == '1w') {
				if (row['1w'] >= 0) {
					return 'color: #E85D2D;';
				} else return 'color: #20995B;';
			}
			if (column.property == '1m') {
				if (row['1m'] >= 0) {
					return 'color: #E85D2D;';
				} else return 'color: #20995B;';
			}
			if (column.property == '1q') {
				if (row['1q'] >= 0) {
					return 'color: #E85D2D;';
				} else return 'color: #20995B;';
			}
			if (column.property == '1y') {
				if (row['1y'] >= 0) {
					return 'color: #E85D2D;';
				} else return 'color: #20995B;';
			}
            if (column.property == '3y') {
				if (row['3y'] >= 0) {
					return 'color: #E85D2D;';
				} else return 'color: #20995B;';
			}
            if (column.property == 'this_year') {
				if (row['this_year'] >= 0) {
					return 'color: #E85D2D;';
				} else return 'color: #20995B;';
			}
		},
		changeStep(val) {
			this.fundlisttemp = [];
			this.$refs.multipleTable.clearSelection();
			if (val == 1) {
				this.$emit('changeStep', 1);
			} else {
				this.$emit('changeStep', 2);
			}
		},
		outExcel() {
			let tHeader = [];
			let tHeader2 = [];
			let filterVal = [];

			tHeader = [
				{ label: '基金名称', value: 'name' },
				{ label: '基金经理姓名', value: 'manager_name' },
				{ label: '基金代码', value: 'code' }
			];

			tHeader2 = [
				{ label: '近一周收益', value: '1w' },
				{ label: '近一月收益', value: '1m' },
				{ label: '近一季收益', value: '1q' },
				{ label: '近一年收益', value: '1y' },
				{ label: '规模', value: 'netasset' },
				{ label: '重仓股', value: 'bigs' }
			];
			let data = this.fundlisttemp.length ? this.fundlisttemp : this.filterData;
			let list = [];
			this.listCol.map((item) => {
				if (item.children?.length) {
					item.children.map((obj) => {
						list.push({
							// ...obj,
							label: item.label + '(' + obj.label + ')',
							value: obj.value
						});
					});
				} else {
					list.push({ label: item.label, value: item.value });
				}
			});
			filter_json_to_excel([...tHeader, ...list, ...tHeader2], data, '筛选结果');
		}
	}
};
</script>
<style lang="scss" scoped>
.filterNewResult {
	margin-top: 1px;
	background: white;
}
//@import url(); 引入公共css类
</style>
