<!--  -->
<template>
	<div class="portfolioSelf">

		<FTB ref="ftb" title="模拟组合管理" :tableHeader="tableHeader" :pageSize="pageSize"> </FTB>
	</div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import FTB from './components/firstPageTable.vue';
import stringTool from '@/pages/tkdesign/components/string.tool.js';
export default {
	//import引入的组件需要注入到对象中才能使用
	components: { FTB },
	data() {
		//这里存放数据
		return {
			tableHeader: [
				{
					prop: 'combinationName',
					label: '组合名称',
					showTip:true
				},
				{
					prop: 'netDate',
					label: '净值日期',
					width:'120px'
				},
				{
					prop: 'net',
					label: '最新净值',
					format: stringTool.fix4
				},
				{
					prop: 'sumNet',
					label: '累计收益率',
					format: stringTool.fix2px
				},
				{
					prop: 'rate',
					label: '涨跌幅',
					format: this.formatter,
					width:'120'
				},
				{
					prop: 'lastWeek',
					label: '近1周收益',
					format: this.formatter
				},
				{
					prop: 'lastMonth',
					label: '近1月收益',
					format: this.formatter
				},
				{
					prop: 'lastSeason',
					label: '近3月收益',
					format: this.formatter
				},
				{
					prop: 'yearToDate',
					label: '年初至今',
					format: this.formatter
				},
				{
					prop: 'aveReturn',
					label: '成立以来年化收益',
					format: this.formatter
				},
				{
					prop: 'establishDate',
					label: '成立日期',
					width:'120px'
				},
				{
					prop: 'opt',
					label: '操作',
					optList:[
					{ key: 'edit', label: '编辑'},
					{ key: 'more', label: '更多',moreList:[{key: 'change', label: '调仓'},{key: 'del', label: '删除'}]},
					]
				}
			],
			pageSize:10,
        };
	},
	//监听属性 类似于data概念
	computed: {},
	//监控data中的数据变化
	watch: {},
	//方法集合
	methods: {
        handleSelect(){

        },
		formatter(val){
			return stringTool.fix2px(val);
		},
		getHeight () {
			// 140 130 50 44 40 40 60 112
			this.$nextTick(()=>{
				console.log("document.getElementsByClassName('content'):::::",parseInt(getComputedStyle(document.getElementsByClassName('content')[0]).height))
				let height = parseInt(getComputedStyle(document.getElementsByClassName('content')[0]).height)
				this.pageSize = Math.floor((height - 140 - 130 - 48 - 44 - 40 - 40 - 40 - 112) / 40);
				
			})
		},
    },
	// created () {
	// 	window.addEventListener('resize', this.getHeight)
	// },
	// destroyed () {
	// 	window.removeEventListener('resize', this.getHeight)
	// },
	// mounted() {
	// 	this.getHeight()
	// }, //生命周期 - 挂载完成
	beforeCreate() {}, //生命周期 - 创建之前
	beforeMount() {}, //生命周期 - 挂载之前
	beforeUpdate() {}, //生命周期 - 更新之前
	updated() {}, //生命周期 - 更新之后
	beforeDestroy() {}, //生命周期 - 销毁之前
	destroyed() {}, //生命周期 - 销毁完成
	activated() {} //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
.portfolioSelf{
	height: 100%;
}
</style>
