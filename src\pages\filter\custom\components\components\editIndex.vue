<template>
	<div>
		<el-dialog title="" :visible.sync="dialogVisible">
			<div class="selfIndex" style="overflow: hidden">
				<div style="padding: 16px 1px 16px 12px; background: white">
					<div class="selfIndexFont">指数信息</div>
					<div style="margin-top: 16px; display: flex; align-items: center">
						<div class="selfIndexFontInput"><span style="color: red">*</span>自定义指数名称：</div>
						<div>
							<el-input style="width: 240px" v-model="name" placeholder="请输入指数名称"></el-input>
						</div>
						<div style="margin-left: 36px" class="selfIndexFontInput">作用类型：</div>
						<div>
							<!-- <el-input style="width: 240px" v-model="description" placeholder="请输入备注"></el-input> -->
							<el-select v-model="type" placeholder="">
								<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
							</el-select>
						</div>
					</div>
				</div>
				<div style="margin-top: 16px; display: flex; width: 100%">
					<div class="selfindexchoose">
						<div style="display: flex; justify-content: space-between; align-items: center">
							<div class="selfIndexFont">选择指数</div>
							<div>
								<searchComponent type="index" select-style="width: 250px" @resolveFather="getFundInfo"></searchComponent>
							</div>
						</div>
						<div style="margin-top: 10px; width: 100%">
							<el-table height="380px" ref="filterTable" @filter-change="filterChange" :data="dataList">
								<el-table-column prop="name" align="gotoleft" label="指数名称"></el-table-column>
								<el-table-column prop="code" align="gotoleft" label="指数代码"></el-table-column>
								<el-table-column align="gotoleft" label="操作">
									<template slot-scope="scope"
										><el-button type="text" @click="choose(scope.row, scope.$index)">选择</el-button>
										<el-button type="text" class="ml-8" @click="del(scope.$index)">删除</el-button></template
									>
								</el-table-column>
							</el-table>
						</div>
					</div>
					<div class="selfindexchoose">
						<div style="display: flex; justify-content: space-between; align-items: center">
							<div class="selfIndexFont">自定义指数</div>
							<div>
								<el-button
									type=""
									@click="
										activeData = activeData.map((item) => {
											return {
												name: item.name,
												code: item.code,
												category: item.category,
												weight: (100 / activeData.length).toFixed(2)
											};
										})
									"
									>一键等权</el-button
								>
								<el-button type="" @click="activeData = []">重置</el-button>
							</div>
						</div>
						<div style="margin-top: 10px">
							<el-table height="380px" :data="activeData">
								<el-table-column align="gotoleft" prop="name" label="指数名称"></el-table-column>
								<el-table-column align="gotoleft" prop="code" label="指数代码"></el-table-column>
								<el-table-column align="gotoleft" prop="weight" label="权重">
									<template slot-scope="scope"><el-input v-model="scope.row.weight"></el-input></template>
								</el-table-column>
								<el-table-column align="gotoleft" label="操作">
									<template slot-scope="scope"><el-button type="text" @click="delchoose(scope.row)">删除</el-button></template>
								</el-table-column>
							</el-table>
						</div>
					</div>
				</div>
			</div>
			<div slot="footer">
				<el-button @click="dialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="submit">确 定</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import { postIndexList } from '@/api/pages/Tools.js';
import searchComponent from '@/components/components/components/search/index.vue';
export default {
	//import引入的组件需要注入到对象中才能使用
	components: { searchComponent },
	data() {
		//这里存放数据
		return {
			dialogVisible: false,
			id: '',
			name: '',
			description: '',
			dataList: [
				{
					name: '沪深300',
					code: '000300.SH'
				},
				{
					name: '上证50',
					code: '000016.SH'
				},
				{
					name: '中证500',
					code: '000905.SH'
				},
				{
					name: '中证800',
					code: '000906.SH'
				},
				{
					name: '上证综指',
					code: '000001.SH'
				},
				{
					name: '深证成指',
					code: '399001.SZ'
				},
				{
					name: '中证全债',
					code: 'H11001.CSI'
				}
			],
			options: [
				{
					label: '主动权益',
					value: 'equity'
				},
				{
					label: '固收+',
					value: 'bond'
				},
				{
					label: '可转债',
					value: 'cbond'
				},
				{
					label: '纯债',
					value: 'purebond'
				},
				{
					label: '中短债',
					value: 'bill'
				},
				{
					label: '指数增强',
					value: 'equityenhance'
				},
				{
					label: 'fof',
					value: 'fof'
				}
			],
			type: 'equity',
			activeData: []
		};
	},
	//方法集合
	methods: {
		getData(data) {
			if (data?.id) {
				this.id = data?.id;
				this.name = data?.name;
				this.description = data?.description;
				this.activeData = data?.list;
			}
			this.dialogVisible = true;
		},
		del(index) {
			this.dataList.splice(index, 1);
		},
		// 选择指数
		choose(row, index) {
			if (this.activeData.findIndex((item) => item.code == row.code) < 0) {
				this.activeData.push({
					name: row.name,
					code: row.code,
					weight: 0,
					category: row.category
				});
				this.dataList.splice(index, 1);
			} else {
				this.$message.warning('已存在指数');
			}
		},
		// 删除code
		delchoose(row) {
			this.activeData.splice(
				this.activeData.findIndex((item) => item.code == row.code),
				1
			);
		},
		submit() {
			if (!this.FUNC.isEmpty(this.name)) {
				this.$message.warning('名称未输入');
				return false;
			}
			if (!this.FUNC.isEmpty(this.activeData)) {
				this.$message.warning('指数未定义');
				return false;
			}
			if (this.id) {
				this.putIndexList();
			} else {
				this.postIndexList();
			}
		},
		async postIndexList() {
			let data = await postIndexList({
				name: this.name,
				description: this.description,
				type: this.type,
				isdefault: 1,
				list: this.activeData.map((item) => {
					return {
						code: item.code,
						weight: item.weight
					};
				})
			});
			if (data?.mtycode == 200) {
				this.$message.success('创建成功');
				this.dialogVisible = false;
				this.$emit('resolverFather');
			} else {
				this.$message.warning('创建失败');
			}
		},
		async putIndexList() {
			let data = await putIndexList({
				name: this.name,
				description: this.description,
				type: this.type,
				list: this.activeData.map((item) => {
					return {
						code: item.code,
						weight: item.weight
					};
				})
			});
			if (data?.mtycode == 200) {
				this.$message.success('修改成功');
				this.dialogVisible = false;
				this.$emit('resolverFather');
			} else {
				this.$message.warning('创建失败');
			}
		},
		getFundInfo(val) {
			if (this.dataList.findIndex((v) => v.code == val.id) == -1) {
				this.dataList.unshift({ code: val.id, name: val.name });
			} else {
				this.$message.warning('该基准列表中已存在');
			}
		}
	}
};
</script>
<style lang="scss" scoped>
.selfIndex {
	.selfIndexFont {
		font-style: normal;
		font-weight: 500;
		font-size: 16px;
		line-height: 24px;
		color: rgba(0, 0, 0, 0.85);
	}
	.selfIndexFontInput {
		font-style: normal;
		font-weight: 400;
		font-size: 14px;
		line-height: 22px;
		color: rgba(0, 0, 0, 0.85);
	}
	.selfindexchoose {
		padding: 0 12px;
		background: white;
		flex: 1;
	}
}
</style>
