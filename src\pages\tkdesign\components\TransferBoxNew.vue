<template>
  <div class="asset-selection-wrapper">
    <div class="asset-selection-content">
      <div>
        <div class="transfer-box-wrapper">
          <div class="transfer-left">
            <div class="transfer-left-title">
              <span class="label">搜索指标：</span><el-input v-model="searchInput"
                        placeholder="请输入名称搜索指数"></el-input>
            </div>
            <el-tabs v-model="activeName1"
                     tab-position="left"
                     style="height: 405px"
                     class="left-nav-wrapper">
              <el-tab-pane v-for="leftItem in optionList"
                           :key="leftItem.value"
                           :label="leftItem.label"
                           :name="leftItem.value">
                <div v-for="checkItem in leftItem.children"
                     :key="checkItem.value">
                  <el-checkbox v-if="checkItem.hide !== true"
                               v-model="checkItem.checked"
                               :label="checkItem.value">{{ checkItem.value }} {{ checkItem.label }}
                  </el-checkbox>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
          <div class="transfer-center">
            <el-button style="width: 82px; margin-bottom: 8px"
                       @click="handleAdd">添加<i class="el-icon-arrow-right"></i></el-button>
            <el-button style="width: 82px; margin-bottom: 8px; margin-left: unset"
                       @click="handleRemove"><i class="el-icon-arrow-left"></i>删除</el-button>
            <el-button style="width: 82px; margin-left: unset"
                       @click="handleClear">清空已选</el-button>
          </div>
          <div class="transfer-right">
            <div class="transfer-right-title">已选列</div>
            <el-table ref="multipleTable"
                      :data="selectedData"
                      tooltip-effect="dark"
                      style="width: 480px"
                      :highlight-selection-row="true"
                      row-key="fund_code"
                      @row-click="handleSelectionClick"
                      @selection-change="handleSelectionChange">
              <el-table-column type="selection"
                               width="55"> </el-table-column>
              <el-table-column prop="value"
                               align="gotoleft"
                               label="指数代码"
                               width="185"> </el-table-column>
              <el-table-column label="指数名称"
                               align="gotoleft"
                               width="190">
                <template slot-scope="scope">{{ scope.row.label }}</template>
              </el-table-column>
              <el-table-column align="gotoleft"
                               label=""
                               width="50">
                <template slot-scope="scope">
                  <svg @click.stop="handleRemoveOne(scope.row)"
                       xmlns="http://www.w3.org/2000/svg"
                       width="14"
                       height="14"
                       viewBox="0 0 14 14"
                       fill="none">
                    <path d="M4.62476 1.87402H4.49976C4.56851 1.87402 4.62476 1.81777 4.62476 1.74902V1.87402H9.37476V1.74902C9.37476 1.81777 9.43101 1.87402 9.49976 1.87402H9.37476V2.99902H10.4998V1.74902C10.4998 1.19746 10.0513 0.749023 9.49976 0.749023H4.49976C3.94819 0.749023 3.49976 1.19746 3.49976 1.74902V2.99902H4.62476V1.87402ZM12.4998 2.99902H1.49976C1.22319 2.99902 0.999756 3.22246 0.999756 3.49902V3.99902C0.999756 4.06777 1.05601 4.12402 1.12476 4.12402H2.06851L2.45444 12.2959C2.47944 12.8287 2.92007 13.249 3.45288 13.249H10.5466C11.081 13.249 11.5201 12.8303 11.5451 12.2959L11.931 4.12402H12.8748C12.9435 4.12402 12.9998 4.06777 12.9998 3.99902V3.49902C12.9998 3.22246 12.7763 2.99902 12.4998 2.99902ZM10.4263 12.124H3.57319L3.19507 4.12402H10.8044L10.4263 12.124Z"
                          fill="black"
                          fill-opacity="0.25" />
                  </svg>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getIndexCode } from '@/api/pages/tkAnalysis/captial-market.js';
export default {
  components: {},
  data () {
    return {
      dialogAssetAnalysisVisible: false,
      searchInput: '',
      activeName: '',
      activeName1: '',
      checkedCities: [],
      optionList: {},
      // options: {
      //   'zhinan':{
      //   value: 'zhinan',
      //   label: '股票与指数',
      //   children: {
      //     'shejiyuanze':{
      //     value: 'shejiyuanze',
      //     label: '设计原则'
      //   },
      //   'daohang':{
      //     value: 'daohang',
      //     label: '导航',
      //   }}
      // },
      // 'zujian':{
      //   value: 'zujian',
      //   label: '基金产品',
      //   children: {
      //     'basic':{
      //     value: 'basic',
      //     label: '全市场公募基金',
      //     children:{
      //     'shejiyuanze1':{
      //     value: 'shejiyuanze1',
      //     label: '基金111',
      //     fund_code:'1002',
      //     fund_name:'基金1002',
      //     fund_type_name:'混合型',
      //     establishment_date:'2022-01-01',
      //     checked: false,
      //     hide:false,
      //     },
      //     'shejiyuanze':
      //     {
      //     value: 'shejiyuanze',
      //     label: '基金11',
      //     fund_code:'1001',
      //     fund_name:'基金1001',
      //     fund_type_name:'混合型',
      //     establishment_date:'2022-01-01',
      //     checked: false,
      //     hide:false,
      //     }}
      //   }}
      // }},
      selectedData: [],
      multipleSelection: []
    };
  },
  watch: {
    selectedData (newValue, oldValue) {
      this.$emit('selectChange', newValue);
    }
  },
  // created() {
  // 	this.getIndexOptions();
  // },
  methods: {
    initData () {
      //需要将数据返回处理成想要的结构
    },
    setValue (data) {
      this.getIndexOptions();
      // console.log(data);
      // console.log(this.optionList);
    },
    async getIndexOptions () {
      let params = {
        ...this.form
      };
      let reqData = await getIndexCode(params);
      console.log(reqData);
      let { data = [], code, message } = reqData || {};
      if (code == 200) {
        data.forEach((item) => {
          //   'zhinan':{
          //   value: 'zhinan',
          //   label: '股票与指数',
          //   children: {
          //     'shejiyuanze':{
          //     value: 'shejiyuanze',
          //     label: '设计原则'
          //   },
          let children = {};
          item.dataList.forEach((childrenItem) => {
            children[childrenItem.code] = {
              label: childrenItem.name,
              value: childrenItem.code
            };
          });
          let value = {
            value: item.indexType,
            label: item.indexName,
            children
          };
          // Object.assign(this.optionList, value);
          // console.log(this.optionList, item.indexType, value);
          this.$set(this.optionList, item.indexType, value);
        });
        console.log('**', this.optionList);
      } else {
        this.$message.warning(message);
      }
    },
    handleAdd () {
      //第一分类
      // for(let key in this.options){
      //   let item=key&&this.options[key]
      //   if(item&&item.children){
      //第二分类
      for (let key1 in this.optionList) {
        let item1 = this.optionList[key1];
        if (item1 && item1.children) {
          for (let key2 in item1.children) {
            //最后选择值
            let item2 = item1.children[key2];
            //将item2内值为true的转移到右侧
            if (item2.checked) {
              this.selectedData.push({
                secondType: key1,
                ...item2
              });
              this.optionList[key1].children[key2].hide = true;
              this.optionList[key1].children[key2].checked = false;
            }
          }
        }
      }
    },
    handleClick (tab, event) {
      console.log(tab, event);
    },
    handleSelectionClick (row) {
      this.toggleSelection(row);
    },
    toggleSelection (row) {
      this.$refs.multipleTable.toggleRowSelection(row);
    },
    handleSelectionChange (val) {
      console.log(val);
      this.multipleSelection = val;
    },
    handleRemoveOne (item) {
      let index = this.selectedData.findIndex((selectedItem) => {
        return selectedItem.fund_code === item.fund_code;
      });
      if (index >= 0) {
        this.selectedData.splice(index, 1);
        this.optionList[item.secondType].children[item.value].hide = false;
      }
    },
    handleRemove () {
      this.multipleSelection.forEach((element) => {
        this.handleRemoveOne(element);
      });
      this.multipleSelection = [];
    },
    handleClear () {
      //第二分类
      for (let key1 in this.optionList) {
        let item1 = this.optionList[key1];
        if (item1 && item1.children) {
          for (let key2 in item1.children) {
            //最后选择值
            let item2 = item1.children[key2];
            //将item2内值为true的转移到右侧
            this.optionList[key1].children[key2].hide = false;
            this.optionList[key1].children[key2].checked = false;
          }
        }
      }

      this.selectedData = [];
    }
  }
};
</script>
<style lang="scss" scoped>
.transfer-box-wrapper {
	display: flex;
	.first-type-wrapper {
		::v-deep .el-tabs__nav-scroll {
			padding: 0 16px;
			.el-tabs__nav-wrap {
				&::after {
					content: unset;
				}
			}
		}
	}
	.transfer-left {
		width: 480px;
		border: 1px solid #e9e9e9;
		border-radius: 4px;
		.left-nav-wrapper {
			::v-deep .el-tabs__header {
				.el-tabs__item {
					text-align: left;
				}
			}
		}
		.transfer-left-title {
			display: flex;
			padding: 8px 16px;
			align-items: center;
			border-bottom: 1px solid #e9e9e9;
			.label {
				color: rgba(0, 0, 0, 0.85);
				font-size: 14px;
				font-style: normal;
				font-weight: 400;
				line-height: 22px; /* 157.143% */
				word-break: keep-all;
			}
		}
	}
	.transfer-center {
		display: flex;
		flex-direction: column;
		padding: 0 20px;
		justify-content: center;
		align-items: center;
	}
	.transfer-right {
		border: 1px solid #e9e9e9;
		border-radius: 4px;
		.transfer-right-title {
			padding: 10px 16px;
			color: #000;
			font-size: 14px;
			font-style: normal;
			font-weight: 400;
			line-height: 22px; /* 157.143% */
		}
	}
}
.asset-selection-wrapper {
	background-color: #ffffff;
	border-radius: 4px;
	border-top: 1px solid #e9e9e9;
	.asset-selection-content {
		padding: 0 24px;
	}
	.asset-selection-header {
		color: rgba(0, 0, 0, 0.85);
		font-size: 16px;
		font-style: normal;
		font-weight: 500;
		line-height: 24px; /* 150% */
		padding: 16px 0;
	}
}
.asset-selection-footer {
	display: flex;
	justify-content: space-between;
	padding: 8px 0;
	color: rgba(0, 0, 0, 0.45);
	font-size: 12px;
	font-weight: 400;
	line-height: 20px; /* 166.667% */
}
.btn-footer-wrapper {
	padding: 16px 24px;
	border-top: 1px solid #e9e9e9;
}
</style>
