<template>
    <div class="plate-wrapper fund-performance-board-wrapper">
        <combinationComponentHeader title="基金标签" showMoreBtn @download="exportExcel">
            <template slot="right">
                <el-select v-model="comparisonValue" placeholder="选择比较基准" style="margin-right: 12px;" @change="comparisonChange">
                    <el-option
                        v-for="item in options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                        </el-option>
                </el-select>
                
            </template>
        </combinationComponentHeader>
  
        <!-- <el-table border stripe :data="tableDataNow">
            <el-table-column  align="gotoleft" prop="name" show-overflow-tooltip label="类型">
                
            </el-table-column>
            <el-table-column  align="gotoleft" prop="date" show-overflow-tooltip label="权重"></el-table-column>
            <el-table-column  align="gotoleft" prop="nav" sortable label="数量"></el-table-column>
           

            <template slot="empty">
                <el-empty image-size="160"></el-empty>
            </template>
        </el-table> -->
        <!-- <div id="fund-performance-board-chart-container" style="height: 355px;"></div> -->
        <barChartForFundLabel ref="fund-label-chart" @tableData="getTableData"></barChartForFundLabel>
    </div>
</template>
<script>
import combinationComponentHeader from './combinationComponentHeader.vue';
import barChartForFundLabel from '../chart/barChartForFundLabel.vue';
import { filter_to_excel } from "@/utils/exportExcel.js";
export default {
    name:'fundLabelAnalysis',
    components:{
        combinationComponentHeader,
        barChartForFundLabel
    },
    data(){
        return {
            form:{},
            IndexStyleOption:[],
            tableData:[],
            options:[{
                    label:'行业',
                    value: 'industry'
                },
                {
                    label:'风格',
                    value: 'style'
                },
                {
                    label:'类型',
                    value: 'type'
                }
            ],
            comparisonValue:'industry',
            legendName : {
                'indicatorPoints':'指标点位',
                'ttm':'TTM',
                'dividedIntoPoints':'分为点',
                'positiveStandardDeviation':'标准差（+1）',
                'negativeStandardDeviation':'标准差（-1）',
                'average':"平均值"
            },
            param:null,
            tableHeader:[{
                prop:'name',
                label:'基金标签'
            }],
            tableData:[]
        }
    },
    mounted(){
        
    },
    methods:{
        comparisonChange(){
            this.getData(this.param);
        },
        getData(param){
            this.param = param;
            let chartDom = this.$refs['fund-label-chart'];
            chartDom?.getData({
                ...param,
                tag: this.comparisonValue
            })
        },
       // 获取表格数据的方法
       getTableData(val) {
         // 遍历日期列表，将每个日期作为表头添加到表头数组中
         val.date_list.forEach(item => {
           this.tableHeader.push({
             prop: item, // 使用日期作为表头属性名
             label: item // 使用日期作为表头显示名称
           });
         });
         // 遍历图例列表，为每个图例创建一个表格行
         val.legendList.forEach((item, index) => {
           this.tableData.push({
             name: item.name // 将图例名称作为表格行的 name 属性
           });
           // 遍历日期列表，将每个日期对应的数据填充到表格行中
           val.date_list.forEach((item, index2) => {
             this.tableData[index][item] = val.series[index]['data'][index2]; // 将数据填充到表格行中
           });
         });
       },
      exportExcel(){
          // 将表头数据进行遍历，生成新的数组list，每个元素包含原表头数据和format字段
          let list = this.tableHeader.map((item) => {
            return {
              ...item,
              format: ''
            };
          });
          // 调用filter_to_excel函数，传入list、表格数据this.tableData和文件名'基金标签'
          filter_to_excel(list, this.tableData, '基金标签');
        }
    }
}
</script>
<style lang="scss" scoped>
.fund-performance-board-wrapper {
    .select-form-wrapper {
        margin-bottom: 16px;
    }
    .content-table-wrapper {
        margin-bottom: 32px;
    }
}

</style>