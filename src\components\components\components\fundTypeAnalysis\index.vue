<!--  -->
<template>
	<div :id="'equityStockPositionAnalysis' + fundtype">
		<analysis-card-title :title="title" :image_id="'equityStockPositionAnalysis' + fundtype">
			<div class="flex_start">
				<div class="mr-16">
					<el-date-picker
						@change="changgealphabeta"
						v-model="valuedata"
						unlink-panels
						value-format="yyyy-MM-dd"
						type="daterange"
						range-separator="至"
						:picker-options="pickerOptions"
						start-placeholder="开始日期"
						end-placeholder="结束日期"
					>
					</el-date-picker>
				</div>
				<div>
					比较基准选择：<el-select v-model="benchmarkvalue" placeholder="请选择比较基准" @change="changeSelect">
						<el-option v-for="item in benchmarkoptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
					</el-select>
				</div>
			</div>
		</analysis-card-title>

		<div class="charts_fill_class" v-loading="loading">
			<v-chart
				v-if="!empty"
				:ref="'equityStockPositionAnalysis' + fundtype"
				class="charts_one_class"
				autoresize
				v-loading="loading"
				element-loading-text="暂无数据"
				element-loading-spinner="el-icon-document-delete"
				element-loading-background="rgba(239, 239, 239, 0.5)"
				:options="changingOption"
			/>
			<div v-else>
				<el-empty></el-empty>
			</div>
		</div>
	</div>
</template>

<script>
import { exportTitle, exportChart } from '@/utils/exportWord.js';
import { lineChartOption } from '@/utils/chartStyle.js';
// 产品分析
import { getBenchmarkList, getFofImmediateAsset } from '@/api/pages/Analysis.js';
export default {
	name: 'fundTypeAnalysis',
	data() {
		//这里存放数据
		return {
			pickerOptions: {
				shortcuts: [
					{
						text: '最近一周',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
							picker.$emit('pick', [start, end]);
						}
					},
					{
						text: '最近一个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
							picker.$emit('pick', [start, end]);
						}
					},
					{
						text: '最近三个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
							picker.$emit('pick', [start, end]);
						}
					},
					{
						text: '最近六个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 90 * 2);
							picker.$emit('pick', [start, end]);
						}
					},
					{
						text: '最近一年',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 365);
							picker.$emit('pick', [start, end]);
						}
					},
					{
						text: '最近三年',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 365 * 3);
							picker.$emit('pick', [start, end]);
						}
					},
					{
						text: '最近五年',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setTime(start.getTime() - 3600 * 1000 * 24 * 365 * 5);
							picker.$emit('pick', [start, end]);
						}
					}
				]
			},
			empty: false,
			fundtype: '',
			info: {},
			cangweibianhuaempty: false,
			datatable: [],
			benchmarkvalue: '',
			benchmarkoptions: [],
			changingOption: {},
			showitems: true,
			start_date: '',
			end_date: '',
			valuedata: '',
			loading: true,
			notesData: {
				fxequity: '',
				fxpurebond: '',
				fxcbond: '',
				fxcommodity: '',
				fxneutral: ''
			}
		};
	},
	computed: {
		title() {
			let list = [
				{ label: '纯债分析(直投纯债+间接投资纯债)', value: 'purebond' },
				{ label: '权益分析(直投权益+间接投资权益)', value: 'equity' },
				{ label: '转债分析(直投转债+间接投资转债)', value: 'cbond' },
				{ label: '商品分析(放大权重至100%)', value: 'commodity' },
				{ label: '对冲资产分析(放大权重至100%)', value: 'neutral' }
			];
			return list.find((v) => v.value == this.fundtype)?.label;
		}
	},
	//方法集合
	methods: {
		changgealphabeta() {
			this.loading = true;
			if (this.valuedata.length == 2) {
				this.start_date = this.valuedata[0];
				this.end_date = this.valuedata[1];
				this.getFofImmediateAsset();
			}
		},
		changeSelect() {
			this.loading = true;
			this.getFofImmediateAsset();
		},
		// 获取权益基金分析基准列表
		async getBenchmarkList() {
			let data = await getBenchmarkList({ type: this.fundtype, code: this.info.code, flag: this.info.flag, template: '' });
			if (data?.mtycode == 200) {
				this.benchmarkoptions = data?.data.map((v) => {
					return { label: v.indexName, value: v.indexCode };
				});
				this.loading = true;
				if (this.benchmarkoptions.length > 0) {
					this.benchmarkvalue = this.benchmarkoptions[0].value;
				} else {
					this.benchmarkvalue = '';
				}
				this.getFofImmediateAsset();
			} else {
				this.hideLoading();
			}
		},
		hideLoading() {
			this.empty = true;
			this.loading = false;
		},
		getData(info, type) {
			this.info = info;
			this.fundtype = type;
			this.getBenchmarkList();
		},
		// 获取基金分析数据
		async getFofImmediateAsset() {
			let data = await getFofImmediateAsset({
				code: this.info.code,
				type: this.info.type,
				flag: this.info.flag,
				start_date: this.start_date || this.info.start_date,
				end_date: this.end_date || this.info.end_date,
				status: this.fundtype
			});
			if (data?.mtycode == 200) {
				this.getChartData(data?.data);
			} else {
				this.hideLoading();
			}
		},
		getChartData(data) {
			if (data.date && data.date != []) {
				this.loading = false;
				this.changingOption = lineChartOption({
					color: ['#4096FF', '#4096ff'],
					tooltip: {
						formatter: (params) => {
							if (params.length == 2) {
								let str = `时间: ${params[0].axisValue}<br />`;
								let dotHtml =
									'<div style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' +
									params[0].color +
									'"></div>';
								str += dotHtml + `累计收益率: ${(Number(params[0].value) * 100).toFixed(2)}%<br />`;
								let dotHtml2 =
									'<div style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' +
									params[1].color +
									'"></div>';
								str += dotHtml2 + `${params[1].seriesName}: ${(Number(params[1].value) * 100).toFixed(2)}%<br />`;

								return str;
							} else {
								let str = `时间: ${params[0].axisValue}<br />`;
								let dotHtml =
									'<div style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' +
									params[0].color +
									'"></div>';
								str += dotHtml + `累计收益率: ${(Number(params[0].value) * 100).toFixed(2)}%<br />`;
								return str;
							}
						}
					},
					xAxis: [{ data: data.date }],
					yAxis: [
						{
							formatter: function (obj) {
								return (obj * 100).toFixed(1) + '%';
							},
							scale: true
						}
					],
					series: [
						{
							name:
								(this.fundtype == 'equity'
									? '直投权益+间接投资权益'
									: this.fundtype == 'cbond'
									? '直投转债+间接投资转债'
									: this.fundtype == 'purebond'
									? '直投纯债+间接投资纯债'
									: '') + '累计收益',
							type: 'line',
							symbol: 'none',
							data: data.cum_return
						},
						{
							name: '基准累计收益',
							type: 'line',
							symbol: 'none',
							data: data.index_cum_return
						}
					]
				});
			} else {
				this.hideLoading();
			}
		},
		createPrintWord() {
			let height = this.$refs['equityStockPositionAnalysis' + this.fundtype].$el.clientHeight;
			let width = this.$refs['equityStockPositionAnalysis' + this.fundtype].$el.clientWidth;
			let chart = this.$refs['equityStockPositionAnalysis' + this.fundtype].getDataURL({
				type: 'png',
				pixelRatio: 1,
				backgroundColor: '#fff'
			});
			let title = this.title;

			return !this.empty ? [...exportTitle(title), ...exportChart(chart, { width, height })] : [];
		}
	}
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
</style>
