<template>
	<div>
		<table class="owl_table">
			<tr class="owl_table_th">
				<th class="owl_table_td" v-for="(item, index) in theadList" :key="'thead' + index">
					<div style="display: flex; align-items: center">
						<span>
							{{ item.label }}
						</span>
						<span v-show="item.content && item.content !== ''">
							<el-tooltip effect="dark" :content="item.content" placement="top">
								<svg width="14" height="14" viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path
										fill-rule="evenodd"
										clip-rule="evenodd"
										d="M7.0002 0.699951C10.4793 0.699951 13.3002 3.52089 13.3002 6.99995C13.3002 10.479 10.4793 13.3 7.0002 13.3C3.52113 13.3 0.700195 10.479 0.700195 6.99995C0.700195 3.52089 3.52113 0.699951 7.0002 0.699951ZM7.0002 1.7687C4.11176 1.7687 1.76895 4.11151 1.76895 6.99995C1.76895 9.88839 4.11176 12.2312 7.0002 12.2312C9.88863 12.2312 12.2314 9.88839 12.2314 6.99995C12.2314 4.11151 9.88863 1.7687 7.0002 1.7687ZM7.0002 9.5312C7.31086 9.5312 7.5627 9.78304 7.5627 10.0937C7.5627 10.4044 7.31086 10.6562 7.0002 10.6562C6.68954 10.6562 6.4377 10.4044 6.4377 10.0937C6.4377 9.78304 6.68954 9.5312 7.0002 9.5312ZM7.0002 3.6812C7.59082 3.6812 8.1477 3.8837 8.56957 4.25355C9.00832 4.63745 9.2502 5.15354 9.2488 5.7062C9.2488 6.51901 8.71301 7.25026 7.88332 7.56948C7.62316 7.66933 7.44879 7.92245 7.44879 8.19948V8.5187C7.44879 8.58058 7.39816 8.6312 7.33629 8.6312H6.66129C6.59941 8.6312 6.54879 8.58058 6.54879 8.5187V8.21636C6.54879 7.89151 6.64441 7.57089 6.82863 7.3037C7.01004 7.04214 7.26316 6.84245 7.56129 6.72854C8.04082 6.54433 8.3502 6.14354 8.3502 5.7062C8.3502 5.08604 7.7441 4.5812 7.0002 4.5812C6.25629 4.5812 5.6502 5.08604 5.6502 5.7062V5.81308C5.6502 5.87495 5.59957 5.92558 5.5377 5.92558H4.8627C4.80082 5.92558 4.7502 5.87495 4.7502 5.81308V5.7062C4.7502 5.15354 4.99207 4.63745 5.43082 4.25355C5.8527 3.88511 6.40957 3.6812 7.0002 3.6812Z"
										fill="black"
										fill-opacity="0.85"
									/>
								</svg>
							</el-tooltip>
						</span>
					</div>
					<div v-show="item.sort == true" style="cursor: pointer">
						<svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path
								d="M2.75598 4.72035L5.77213 1.08201C5.89796 0.941414 6.11835 0.941414 6.24418 1.08201L9.24545 4.72035C9.42712 4.92418 9.28267 5.24703 9.00943 5.24703H2.99125C2.71875 5.24703 2.57356 4.92418 2.75598 4.72035Z"
								fill="black"
								fill-opacity="0.85"
							/>
							<path
								d="M2.75598 7.27965L5.77213 10.918C5.89796 11.0586 6.11835 11.0586 6.24418 10.918L9.24545 7.27965C9.42712 7.07582 9.28267 6.75297 9.00943 6.75297H2.99125C2.71875 6.75297 2.57356 7.07582 2.75598 7.27965Z"
								fill="black"
								fill-opacity="0.85"
							/>
						</svg>
					</div>
				</th>
			</tr>
			<tr v-for="(obj, index) in dataList" :key="'data' + index" class="owl_table_tr">
				<td v-for="item in theadList" :key="'data' + item.value" class="owl_table_td">{{ obj[item.value] }}</td>
			</tr>
		</table>
	</div>
</template>

<script>
export default {
	props: {
		theadList: [],
		dataList: []
	}
};
</script>

<style lang="scss" scoped>
.owl_table {
	width: 100%;
	.owl_table_th {
		display: flex;
		align-items: center;
		font-family: 'PingFang';
		background: #fafafa;
		border-radius: 4px 4px 0px 0px;
		font-style: normal;
		font-weight: 500;
		font-size: 14px;
		line-height: 22px;
		color: rgba(0, 0, 0, 0.65);
		border-bottom: 1px solid #e9e9e9;
		.owl_table_td {
			position: relative;
			display: flex;
			justify-content: space-between;
			align-items: center;
			flex: 1;
			height: 57px;
			line-height: 57px;
			margin-left: 8px;
			padding-right: 8px;
		}
		.owl_table_td:first-child {
			margin-left: 24px;
		}
		.owl_table_td:not(:last-child):before {
			position: absolute;
			top: 1.3em;
			height: 20px;
			right: 0px;
			content: '';
			width: 2px;
			background: rgba(0, 0, 0, 0.06);
			border-radius: 0px;
		}
	}
	.owl_table_tr {
		display: flex;
		align-items: center;
		font-family: 'PingFang';
		// background: #fafafa;
		border-radius: 4px 4px 0px 0px;
		font-style: normal;
		font-weight: 500;
		font-size: 14px;
		line-height: 22px;
		color: rgba(0, 0, 0, 0.65);
		border-bottom: 1px solid #e9e9e9;
		.owl_table_td {
			position: relative;
			display: flex;
			justify-content: space-between;
			align-items: center;
			flex: 1;
			height: 57px;
			line-height: 57px;
			margin-left: 8px;
			padding-right: 8px;
		}
		.owl_table_td:first-child {
			margin-left: 24px;
		}
	}
	.owl_table_tr:hover {
		background-color: #f2f3f5;
		transition: all 0.2s;
	}
}
</style>
