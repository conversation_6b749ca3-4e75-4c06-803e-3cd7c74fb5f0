<template>
	<el-dialog :title="title" :visible.sync="dialogVisible" width="56%">
		<div style="border-top: 1px solid #e9e9e9; padding: 10px 24px">
			<div class="text" v-for="(item, index) in honrousList" :key="index">{{ item }}</div>
		</div>
	</el-dialog>
</template>

<script>
export default {
	data() {
		return {
			honrousList: [],
			dialogVisible: false,
			title: ''
		};
	},
	methods: {
		getData(honrousList, title) {
			this.title = title;
			this.dialogVisible = true;
			this.honrousList = honrousList;
		}
	}
};
</script>

<style scoped>
.text {
	font-family: 'PingFang';
	font-style: normal;
	font-weight: 400;
	font-size: 14px;
	line-height: 22px;
	color: rgba(0, 0, 0, 0.65);
	margin: 6px;
}
</style>
