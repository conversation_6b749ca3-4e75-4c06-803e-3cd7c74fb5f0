<template>
	<div>
		<div>
			<analysis-card-title title="持仓债券分析" @downloadExcel="exportExcel">
				<div>
					<span
						style="
							margin-right: 8px;
							font-family: 'PingFang';
							font-style: normal;
							font-weight: 400;
							font-size: 14px;
							color: rgba(0, 0, 0, 0.85);
						"
						>季度:</span
					>
					<el-select v-model="quarter" placeholder="" @change="changeQuarter">
						<el-option v-for="item in quarterList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
					</el-select>
				</div>
			</analysis-card-title>
			<el-table
				class="fund-analysis-table"
				style="width: 99%"
				:data="datatable"
				v-loading="loading"
				max-height="400px"
				:default-sort="{ prop: 'creditrating', order: 'descending' }"
			>
				<el-table-column
					v-for="item in column"
					:key="item.value"
					align="gotoleft"
					:show-overflow-tooltip="true"
					:prop="item.value"
					:label="item.label"
					:sortable="item.sortable"
				>
					<template #header>
						<long-table-popover-chart
							v-if="item.popover"
							:data="formatTableData()"
							date_key="bondName"
							:data_key="item.value"
							:show_name="item.label"
						>
							<span>{{ item.label }}</span>
						</long-table-popover-chart>
						<span v-else>{{ item.label }}</span>
					</template>
					<template slot-scope="{ row }">
						<span>{{ item.format ? item.format(row[item.value]) : row[item.value] }}</span>
					</template>
				</el-table-column>
				<template slot="empty">
					<el-empty image-size="160"></el-empty>
				</template>
			</el-table>
		</div>
		<div class="chart_one" v-loading="loading">
			<div class="flex_card">
				<div class="small_template pt-20" style="box-shadow: none; height: 320px">
					<div class="charts_center_class">
						<v-chart
							ref="equityStockPositionAnalysistype"
							v-loading="loading"
							element-loading-text="暂无数据"
							element-loading-spinner="el-icon-document-delete"
							element-loading-background="rgba(239, 239, 239, 0.5)"
							class="charts_analysis_class"
							autoresize
							:options="optiontype"
						></v-chart>
					</div>
				</div>
				<div class="small_template pt-20" style="box-shadow: none; height: 320px">
					<div class="charts_center_class">
						<v-chart
							ref="equityStockPositionAnalysisindustry"
							v-loading="loading"
							element-loading-text="暂无数据"
							element-loading-spinner="el-icon-document-delete"
							element-loading-background="rgba(239, 239, 239, 0.5)"
							class="charts_analysis_class"
							autoresize
							:options="optionindustry"
						></v-chart>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { exportTitle, exportTable } from '@/utils/exportWord.js';
import { filter_json_to_excel } from '@/utils/exportExcel.js';
// 持仓债券分析
import { getBondAnalysise, getDateList } from '@/api/pages/Analysis.js';

export default {
	name: 'bondReportPosition',
	data() {
		return {
			bondTopTenLoading: true,
			datatable: [],
			info: {},
			industrypeizhi: [],
			optiontype: {},
			optionindustry: {},
			color: ['#4096ff', '#4096ff', '#7388A9', '#6F80DD', '#6C96F2', '#FD6865', '#83D6AE', '#88C9E9', '#ED589D', '#FA541C'],
			quarterList: [],
			quarter: '',
			loading: true,
			column: [
				{
					label: '债券名称',
					value: 'bondName',
					sortable: false,
					popover: false
				},
				{
					label: '债券代码',
					value: 'bondCode',
					sortable: false,
					popover: false
				},
				{
					label: '个券类型',
					value: 'exchangenature',
					sortable: true,
					popover: false
				},
				{
					label: '行业',
					value: 'firstindustryname',
					sortable: true,
					popover: false
				},
				{
					label: '个券评级',
					value: 'creditrating',
					sortable: true,
					popover: false
				},
				{
					label: '占净资产比',
					value: 'ratioinN',
					sortable: true,
					format: this.fix2p,
					popover: true
				},
				{
					label: '持券数量(万)',
					value: 'holdings',
					sortable: true,
					format: this.fix2,
					popover: true
				},
				{
					label: '持仓市值(亿)',
					value: 'value',
					sortable: true,
					format: this.fix2,
					popover: true
				}
			]
		};
	},
	methods: {
		// 获取债券持仓列表
		async getDateList() {
			let data = await getDateList({
				code: this.info.code,
				type: this.info.type,
				flag: this.info.flag,
				template: 'bond_position',
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			if (data?.mtycode == 200) {
				this.quarterList = data?.data
					?.sort((a, b) => {
						return this.moment(this.moment(a, 'YYYY QQ').format()).isAfter(this.moment(b, 'YYYY QQ').format()) ? -1 : 1;
					})
					?.map((item) => {
						return { label: item, value: item };
					});
				this.quarter = this.quarterList?.[0]?.value;
				this.getBondAnalysise();
			}
		},
		// 获取持仓债券数据
		async getBondAnalysise() {
			let data = await getBondAnalysise({
				code: this.info.code,
				type: this.info.type,
				flag: this.info.flag,
				yearqtr: this.quarter
			});
			if (data?.mtycode == 200) {
				this.loading = false;
				let result = [];
				data.data.map((item) => {
					let index = result.findIndex((v) => v.bondCode == item.bondCode);
					if (index == -1) {
						result.push({
							...item,
							holdings: parseFloat(item.holdings / 10 ** 4),
							value: parseFloat(item.value / 10 ** 8),
							ratioinN: this.info.type == 'portfolio' ? item.ratioinN : item.ratioinN / 100
						});
					}
				});
				this.datatable = result;
				this.getIndustryData(this.datatable);
			}
		},
		// 切换季度
		changeQuarter() {
			this.loading = true;
			this.getBondAnalysise();
		},
		// 获取数据
		getData(info) {
			this.info = info;
			this.getDateList();
		},
		getIndustryData(data) {
			let type = [];
			let industry = [];
			let typeWeight = 0;
			if (data?.length && typeof data == 'object') {
				data?.map((item) => {
					typeWeight = typeWeight + item.ratioinN;
					// 个券类型
					let typeIndex = type.findIndex((obj) => {
						return obj.name == item.exchangenature;
					});
					if (typeIndex == -1) {
						type.push({
							name: item.exchangenature,
							value: item.ratioinN
						});
					} else {
						type[typeIndex].value = type[typeIndex].value + item.ratioinN;
					}
				});
			}

			let industryWeight = 0;
			if (data?.length && typeof data == 'object') {
				data?.map((item) => {
					industryWeight = industryWeight + item.ratioinN;
					// 个券行业
					let industryIndex = industry.findIndex((obj) => {
						return obj.name == item.firstindustryname;
					});
					if (industryIndex == -1) {
						industry.push({
							name: item.firstindustryname,
							value: item.ratioinN
						});
					} else {
						industry[industryIndex].value = industry[industryIndex].value + item.ratioinN;
					}
				});
			}
			type.unshift({
				name: '未披露个券',
				value: 100 - typeWeight,
				itemStyle: {
					color: '#BFBFBF'
				}
			});
			industry.unshift({
				name: '未披露个券',
				value: 100 - industryWeight,
				itemStyle: {
					color: '#BFBFBF'
				}
			});
			console.log(type, industry);
			this.optiontype = this.returnPieChart(type);
			this.optionindustry = this.returnPieChart(industry);
		},
		returnPieChart(data) {
			return {
				color: this.color,
				tooltip: {
					trigger: 'item',
					formatter: function (val) {
						return val?.marker + ' ' + val?.name + ' ' + val?.value?.toFixed(2) + '%';
					}
				},
				legend: {
					orient: 'vertical',
					left: 'left',
					type: 'scroll',
					data: data
						?.sort((a, b) => {
							return b.value - a.value;
						})
						?.map((item) => {
							return item.name;
						}),
					pageIcons: {
						horizontal: [
							'path://M11.7487 6.92214L6.30673 0.634973C6.15096 0.455009 5.85102 0.455009 5.69359 0.634973L0.251579 6.92214C0.049409 7.15658 0.231693 7.5 0.558148 7.5L11.4422 7.5C11.7686 7.5 11.9509 7.15658 11.7487 6.92214Z',
							'path://M0.251255 1.07786L5.69327 7.36503C5.84904 7.54499 6.14898 7.54499 6.30641 7.36503L11.7484 1.07786C11.9506 0.843416 11.7683 0.499999 11.4419 0.5L0.557824 0.5C0.231369 0.5 0.0490849 0.843417 0.251255 1.07786Z'
						]
					}
				},
				toolbox: {
					feature: {
						saveAsImage: { pixelRatio: 3 }
					},
					top: -4,
					width: 104
				},
				series: {
					type: 'pie',
					radius: ['30%', '70%'],
					center: ['63%', '50%'],
					emphasis: {
						focus: 'ancestor',
						label: {
							show: true,
							fontSize: '14',
							fontWeight: 'bold'
						}
					},
					data,
					// radius: [0, '90%'],
					label: {
						show: false,
						position: 'center'
					}
				}
			};
		},
		formatTableData() {
			let data = [];
			this.datatable.map((item) => {
				let obj = { ...item };
				for (const key in item) {
					let format = this.column.find((obj) => {
						return obj.value == key;
					})?.format;
					if (format) {
						let val = format(item[key]);
						obj[key] = typeof val == 'string' ? (val.includes('%') ? val?.split('%')?.[0] * 1 : !isNaN(val) ? val : val) : val;
					}
				}
				data.push(obj);
			});
			return data;
		},
		fix2(val) {
			return val * 1 && !isNaN(val) ? (val * 1).toFixed(2) : '--';
		},
		fix2p(val) {
			return val * 1 && !isNaN(val) ? (val * 100).toFixed(2) + '%' : '--';
		},
		exportExcel() {
			let list = [
				{
					label: '债券名称',
					value: 'bond_name'
				},
				{
					label: '债券代码',
					value: 'bond_code'
				},
				{
					label: '个券类型',
					value: 'exchangenature'
				},
				{
					label: '行业',
					value: 'firstindustryname'
				},

				{
					label: '个券评级',
					value: 'creditrating'
				},
				{
					label: '占总净值比例',
					value: 'ratioinN',
					format: 'fix2b'
				},
				{
					label: '持券数量(万)',
					value: 'holdings',
					format: 'fix2b'
				},
				{
					label: '持仓市值(亿)',
					value: 'value',
					format: 'fix2b'
				}
			];
			filter_json_to_excel(list, this.datatable, '持仓债券分析');
		},
		createPrintWord() {
			let list = [
				{
					label: '债券名称',
					value: 'bond_name'
				},
				{
					label: '债券代码',
					value: 'bond_code'
				},
				{
					label: '个券类型',
					value: 'exchangenature'
				},
				{
					label: '行业',
					value: 'firstindustryname'
				},

				{
					label: '个券评级',
					value: 'creditrating'
				},
				{
					label: '占总资产比',
					value: 'ratioinN',
					format: 'fix2b'
				},
				{
					label: '持券数量(万)',
					value: 'holdings',
					format: 'fix2b'
				},
				{
					label: '持仓市值(亿)',
					value: 'value',
					format: 'fix2b'
				}
			];
			if (this.datatable.length) {
				return [
					...exportTitle('持仓债券分析'),
					...exportTable(
						list,
						this.datatable
							.sort((a, b) => {
								return b.ratioinN - a.ratioinN;
							})
							.slice(0, 10)
					)
				];
			} else {
				return [];
			}
		}
	}
};
</script>

<style></style>
