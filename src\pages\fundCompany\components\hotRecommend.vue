<template>
	<div>
		<span>热门推荐</span>
		<!-- test echart -->
		<div id="hotRecommendMain" style="width: 800px; height: 240px"></div>
	</div>
</template>

<script>
export default {
	data() {
		return {};
	},
	created() {},
	mounted() {
		this.generateChart();
	},
	methods: {
		generateChart() {
			let id = 'hotRecommendMain';
			if (document.getElementById(id) == null) {
				//console.log('error: 不存在该DOM');
				return;
			}
			echarts.dispose(document.getElementById(id));
			let myChart = echarts.init(document.getElementById(id));
			let option = {
				xAxis: {
					type: 'category',
					data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
				},
				yAxis: {
					type: 'value'
				},
				series: [
					{
						data: [150, 230, 224, 218, 135, 147, 260],
						type: 'line'
					}
				]
			};

			myChart.setOption(option, true);
		}
	}
};
</script>

<style>
</style>