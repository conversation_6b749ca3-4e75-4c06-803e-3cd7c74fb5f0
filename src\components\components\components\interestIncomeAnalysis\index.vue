<template>
	<div>
		<analysis-card-title title="利息收益分析" @downloadExcel="exportExcel">
			<el-button @click="flage = !flage" icon="el-icon-refresh"></el-button>
		</analysis-card-title>
		<div v-loading="loading">
			<div v-show="flage">
				<el-table
					v-loading="loading"
					:data="cunkuanshouyi"
					stripe
					height="400"
					ref="multipleTable"
					header-cell-class-name="table-header"
					:cell-style="cellStyle"
				>
					<el-table-column
						v-for="item in column"
						:key="item.value"
						:prop="item.value"
						:align="item.align ? item.align : 'gotoleft'"
						:label="item.label"
						border
						stripe
						sortable
					>
						<template #header>
							<long-table-popover-chart
								v-if="item.popover"
								:data="cunkuanshouyi"
								date_key="终止日"
								:data_key="item.value"
								:show_name="item.label"
							>
								<span>{{ item.label }}</span>
							</long-table-popover-chart>
							<span v-else>{{ item.label }}</span>
						</template>
						<template slot-scope="{ row }">
							<span>
								{{ item.format ? item.format(row[item.value]) : row[item.value] }}
							</span>
						</template>
					</el-table-column>
					<template slot="empty">
						<el-empty image-size="160"></el-empty>
					</template>
				</el-table>
			</div>
			<div v-show="!flage">
				<bar-chart-component ref="barChartComponent" />
			</div>
		</div>
	</div>
</template>

<script>
import { exportTitle, exportChart } from '@/utils/exportWord.js';
//  利息收益分析
import barChartComponent from '@/components/components/fundComponents/chartComponent/barChart.vue';
import { filter_json_to_excel } from '@/utils/exportExcel.js';

// 基金收益来源分析
import { getProfitAnalysis } from '@/api/pages/Analysis.js';
export default {
	name: 'interestIncomeAnalysis',
	components: {
		barChartComponent
	},
	data() {
		return {
			flage: false,
			cunkuanshouyi: [],
			touzifenxioption3: {},
			loading: true,
			column: [
				{ label: '起始日', value: '起始日' },
				{ label: '终止日', value: '终止日' },
				{ label: '存款收益率', value: '存款收益率', format: this.fix2p, fill: 'red_or_green', popover: true },
				{ label: '债息收益率', value: '债息收益率', format: this.fix2p, fill: 'red_or_green', popover: true },
				{ label: 'abs利息收益', value: 'abs利息收益', format: this.fix2p, fill: 'red_or_green', popover: true },
				{ label: '回购利息收益', value: '回购利息收益', format: this.fix2p, fill: 'red_or_green', popover: true },
				{ label: '其他利息收益', value: '其他利息收益', format: this.fix2p, fill: 'red_or_green', popover: true }
			]
		};
	},
	methods: {
		// 基金收益来源分析
		async getProfitAnalysis() {
			this.loading = true;
			let data = await getProfitAnalysis({
				code: this.info.code,
				start_date: this.info.start_date,
				end_date: this.info.end_date,
				flag: this.info.flag,
				template: 'interestIncomeAnalysis'
			});
			this.loading = false;
			if (data?.mtycode == 200) {
				this.picdata = data?.data;
				this.drawtouzi();
				this.cunkuanshouyi = data?.data?.map((item, index) => {
					return {
						起始日: item?.startdate,
						终止日: item?.enddate,
						存款收益率: item?.depositinterestincome,
						债息收益率: item?.bondinterestincome,
						abs利息收益率: item?.absinterestincome,
						回购利息收益率: item?.sellbackassetsincome,
						其他利息收益率: item?.otherinterestincome
					};
				}); //利息收益分析
			}
		},
		getData(info) {
			this.info = info;
			this.getProfitAnalysis();
		},
		drawtouzi() {
			let data = {};
			data.legend = ['存款收益率', '债息收益率', 'abs利息收益率', '回购利息收益率', '其他利息收益率'];
			data.xAxis = this.picdata.map((v) => v.enddate);
			data.series = [
				{
					name: '存款收益率',
					type: 'bar',
					stack: '总量',
					barWidth: '100%',
					data: this.picdata.map((v) => v.depositinterestincome)
				},
				{
					name: '债息收益率',
					type: 'bar',
					stack: '总量',
					barWidth: '100%',
					data: this.picdata.map((v) => v.bondinterestincome)
				},
				{
					name: 'abs利息收益率',
					type: 'bar',
					stack: '总量',
					barWidth: '100%',
					data: this.picdata.map((v) => v.absinterestincome)
				},
				{
					name: '回购利息收益率',
					type: 'bar',
					stack: '总量',
					barWidth: '100%',
					data: this.picdata.map((v) => v.sellbackassetsincome)
				},
				{
					name: '其他利息收益率',
					type: 'bar',
					stack: '总量',
					barWidth: '100%',
					data: this.picdata.map((v) => v.otherinterestincome)
				}
			];
			this.$refs['barChartComponent'].getData(data);
		},
		fix2p(val) {
			return val * 1 && !isNaN(val) ? (val * 100).toFixed(2) + '%' : '--';
		},
		// 行样式
		cellStyle({ row, column, rowIndex, columnIndex }) {
			let obj = this.column.find((v) => v.value == column.property);
			if (obj?.fill == 'red_or_green') {
				if (row[obj.value] > 0) {
					return {
						color: '#CF1322'
					};
				} else if (row[obj.value] < 0) {
					return {
						color: '#389E0D'
					};
				}
			}
		},
		// 导出为Excel
		exportExcel() {
			let list = [
				{
					label: '起始日',
					value: '起始日'
				},
				{
					label: '终止日',
					value: '终止日'
				},
				{
					label: '存款收益率',
					value: '存款收益率',
					format: 'fix2p'
				},
				{
					label: '债息收益率',
					value: '债息收益率',
					format: 'fix2p'
				},
				{
					label: 'abs利息收益率',
					value: 'abs利息收益率',
					format: 'fix2p'
				},
				{
					label: '回购利息收益率',
					value: '回购利息收益率',
					format: 'fix2p'
				},
				{
					label: '其他利息收益率',
					value: '其他利息收益率',
					format: 'fix2p'
				}
			];
			filter_json_to_excel(list, this.cunkuanshouyi, '利息收益分析');
		},
		createPrintWord() {
			let height = this.$refs['barChartComponent'].$el.clientHeight;
			let width = this.$refs['barChartComponent'].$el.clientWidth;
			let chart = this.$refs['barChartComponent'].createPrintWord();
			return [...exportTitle('利息收益分析'), ...exportChart(chart, { width, height })];
		}
	}
};
</script>

<style></style>
