<template>
  <div class="box_Board">
    <div class="header_box"><span class="header_inactive">投后&nbsp;/&nbsp;映射管理&nbsp;/&nbsp;<ins style="text-decoration:none;cursor: pointer"
             @click="gotoPage">自定义市场划分</ins> /</span>
      {{ $route.query.detail.marketName }}
    </div>
    <!-- 表格区域 -->
    <div class="border_table">
      <div class="border_table_header">
        <div class="border_table_header_title">{{ $route.query.detail.marketName }}</div>
        <div>
          <el-button type="primary"
                     @click="showDialog=true">
            上传文件
          </el-button>
        </div>
      </div>
      <!-- 表格 -->
      <el-table :data="tableData"
                border
                stripe>
        <el-table-column align="center"
                         label="序号"
                         type="index"
                         width="50">
        </el-table-column>
        <el-table-column align="gotoleft"
                         label="起始日期"
                         prop="startData">
          <template slot-scope="scope">
            <div v-if="scope.row.addFlag">
              <el-date-picker v-model="scope.row.startDate"
                              type="date"
                              clearable
                              placeholder="请选择日期"
                              value-format="yyyyMMdd"></el-date-picker>
            </div>
            <div v-else>{{ scope.row.startDate }}</div>
          </template>
        </el-table-column>
        <el-table-column align="gotoleft"
                         label="结束日期"
                         prop="endData">
          <template slot-scope="scope">
            <div v-if="scope.row.addFlag">
              <el-date-picker v-model="scope.row.endDate"
                              type="date"
                              placeholder="请选择日期"
                              clearable
                              value-format="yyyyMMdd" />
            </div>
            <div v-else>{{ scope.row.endDate }}</div>
          </template>
        </el-table-column>
        <el-table-column align="gotoleft"
                         label="阶段名称"
                         prop="desc">
          <template slot-scope="scope">
            <div v-if="scope.row.addFlag">
              <el-input v-model="scope.row.desc"
                        clearable
                        placeholder="请输入内容" />
            </div>
            <div v-else>{{ scope.row.desc }}</div>
          </template>
        </el-table-column>
        <el-table-column align="gotoleft"
                         label="操作">
          <template slot-scope="scope">
            <div v-if="!scope.row.addFlag"
                 class="flex">
              <el-button class="button-color"
                         type="text"
                         @click="edit(scope.row)">编辑
              </el-button>
              <el-button class="button-color"
                         type="text"
                         @click="deleteRow(scope.row)">
                删除
              </el-button>
            </div>
            <div v-else
                 class="flex">
              <el-button class="button-color"
                         type="text"
                         @click="save(scope.row)">保存
              </el-button>
              <el-button class="button-color"
                         type="text"
                         @click="cancel(scope.row)">取消
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页器 -->
      <div class="pagination_board">
        <el-pagination :current-page.sync="pagination.pageIndex"
                       :page-size="pagination.pageSize"
                       :total="pagination.total"
                       background
                       layout="total, sizes, prev, pager, next"
                       @size-change="sizeChange"
                       @current-change="currentChange">
        </el-pagination>
      </div>
    </div>
    <!-- 上传白名单弹框 -->
    <el-dialog :visible.sync="showDialog"
               title="上传白名单"
               width="900">
      <ExcelPort excelUrl="market_segment.xlsx"
                 :path="`/cloud/api/taikang/market/segment/upload`"
                 :data="{ id: id}"
                 @refrshtable="uploadSuccess" />
    </el-dialog>

  </div>
</template>

<script>
import ExcelPort from '../component/map/alphaownpool.vue'
import { saveRow, getById } from "../../../../api/pages/tkdesign/customMarketDivision";

export default {
  components: {
    ExcelPort
  },
  data () {
    return {
      tableData: [],// 页面表格数据源

      oldData: {},
      id: '',

      pagination: {
        pageIndex: 1,// 当前页码
        pageSize: 10,// 页面显示几条数据
        total: 0,
      },


      showDialog: false,// 绑定上传黑名单的dialog
    };
  },
  methods: {
    /**
     * 获取页面数据
     */
    getById () {
      getById(this.id).then((res) => {
        if (res.code === 200) {
          this.tableData = JSON.parse(res.data.timeInterval.value)
          this.pagination.total = res.total
        } else {
          this.tableData = []
        }
      })
    },
    // 上传成功
    uploadSuccess () {
      this.showDialog = false
      this.getById()
    },
    /**
     * 点击面包屑返回上一页
     */
    gotoPage () {
      this.$router.replace('/customMarketDivision')
    },
    // 每页条数改变时触发的回调
    sizeChange (value) {
      this.pagination.pageSize = value
      this.getById()
    },


    // 当前页数改变时触发的回调
    currentChange (value) {
      this.pagination.pageIndex = value
      this.getById()
    },


    /**
     * 刷新表格addFlag状态
     * @param row
     */
    splice (row, data) {
      const number = this.tableData.indexOf(row)
      this.tableData.splice(number, 1)
      this.tableData.splice(number, 0, data)
    },
    /**
     * 编辑
     * @param row
     */
    async edit (row) {
      for await (let item of this.tableData) {
        if (item.addFlag)
          return
      }
      this.oldData = JSON.parse(JSON.stringify(row))
      row.addFlag = true
      this.splice(row, row)
    },
    /**
     *取消
     * @param row
     */
    cancel (row) {
      this.splice(row, this.oldData)
    },
    /**
     * 保存
     * @param row
     */
    save (row) {
      row.addFlag = false
      this.splice(row, row)
      const data = {
        ...this.$route.query.detail,
        timeInterval: this.tableData
      }
      saveRow(data).then((res) => {
        if (res.code === 200) {
          this.$message.success('上传成功')
        } else {
          this.$message.error('上传失败')
        }
      })
    },
    /**
     * 删除
     * @param row
     */
    async deleteRow (row) {
      for await (let item of this.tableData) {
        if (item.addFlag)
          return
      }
      this.$confirm('确定删除么?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        row.addFlag = false
        const number = this.tableData.indexOf(row)
        this.tableData.splice(number, 1)
        const data = {
          ...this.$route.query.detail,
          timeInterval: this.tableData
        }
        saveRow(data).then((res) => {
          if (res.code === 200) {
            this.$message.success('删除成功')
          } else {
            this.$message.error('删除失败')
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
  },
  mounted () {
    this.id = this.$route.query.detail.id
    this.getById()
  },
}
</script>
<style lang="scss" scoped>
@import '../../tkdesign';
</style>

[
    {
        "rowId": 3,
        "parentRowId": -1,
        "data": {
            "eversincevolatility": "0.14516807853130503",
            "eversincecum_return": "0.07685016421188506",
            "eversinceexcess_maxdrawdown": "-0.27001902690471163",
            "netasset_list": "[3489445.14, 4834375.64, 4815472.02, 4791101.41, 4791101.41, 4791101.41, 6247789.99, 7848920.69, 7981893.24, 7990610.73, 7990835.99, 7990835.99, 7990835.99, 7804293.73, 7772814.27, 7777195.25, 7695908.98, 9039888.79, 9039888.79, 9039888.79, 10281759.85, 10534390.3, 9942008.72, 9761847.11, 9792310.53, 9792310.53, 9792310.53, 9734940.2, 9418433.55, 9407644.79, 9351203.4, 9371038.45, 9371038.45, 9371038.45, 9425345.85, 9530333.6, 9483451.52, 9460164.71, 9393695.57, 9393695.57, 9393695.57, 10321746.65, 10119001.67, 10430428.16, 10595578.58, 10719441.38, 10719441.38, 10719441.38, 10529311.82, 10683131.32, 11975918.09, 12310864.17, 12183762.03, 12183762.03, 12183762.03, 12220711.5, 12354184.06, 12687830.95, 12875514.08, 12851635.54, 12851635.54, 12851635.54, 12669402.95, 12830861.22, 12647882.06, 12338204.72, 12294595.22, 12294595.22, 12294595.22, 12292693.16, 12234910.77, 12254948.45, 12616897.62, 12940731.56, 12940731.56, 12940731.56, 13021682.76, 13248541.95, 13104364.42, 13192361.48, 12846137.95, 12846137.95, 12846137.95, 12876557.1, 12785883.15, 12821908.2, 10216632.63, 10233783.52, 10233783.52, 10233783.52, 9986786.7, 10012615.35, 10014163.37, 10091045.97, 10126954.64, 10126954.64, 10126954.64, 10207710.0, 10018252.09, 10020967.08, 10161933.17, 10112743.9, 10112743.9, 10112743.9, 10093589.77, 10006198.02, 10140331.07, 10139335.69, 10181295.37, 10181295.37, 10181295.37, 10208447.47, 10200952.55, 10247714.7, 10790451.78, 6007860.86, 6007860.86, 6007860.86, 5957202.77]",
            "netasset": "nan",
            "index_cum_return": "0.15906841758632595",
            "weight_list": "[0.0329705498339027, 0.028119357792713323, 0.025833653055236917, 0.025659517350449787, 0.025659517350449787, 0.025659517350449787, 0.02971307554352354, 0.029213066559006594, 0.027210445362684427, 0.026814254978458817, 0.02683642163165552, 0.02683642163165552, 0.02683642163165552, 0.026273467219507105, 0.02543208340658513, 0.02509164096231426, 0.0249899789453846, 0.029003416815731684, 0.029003416815731684, 0.029003416815731684, 0.03247211186416748, 0.032849027654477565, 0.031215958126984408, 0.030789613823976943, 0.030483142629038396, 0.030483142629038396, 0.030483142629038396, 0.030392137782002004, 0.028980395411245644, 0.028690215257781326, 0.028306199773176614, 0.02869649024129118, 0.02869649024129118, 0.02869649024129118, 0.028531470475375187, 0.027929951641744687, 0.026320818684749135, 0.024925794575256628, 0.02405666119032071, 0.02405666119032071, 0.02405666119032071, 0.02506967583864865, 0.024809592324646055, 0.025668735293132107, 0.025748099548701456, 0.025290429711970593, 0.025290429711970593, 0.025290429711970593, 0.0246099442111146, 0.02447062404288839, 0.025178422671028156, 0.025483220595191283, 0.024948235076659723, 0.024948235076659723, 0.024948235076659723, 0.024702844412153667, 0.024816734422582885, 0.024941699461303073, 0.02674752951752498, 0.026619281125693974, 0.026619281125693974, 0.026619281125693974, 0.026254376228252208, 0.026208447956710956, 0.025790292493709382, 0.024940644380333, 0.024630602404336035, 0.024630602404336035, 0.024630602404336035, 0.02459801589871718, 0.02462375404363526, 0.024965320688599246, 0.025826630171849038, 0.024388362636975892, 0.024388362636975892, 0.024388362636975892, 0.022592451386504883, 0.022644637275523405, 0.022036839279888813, 0.022111738880619108, 0.021148723570834374, 0.021148723570834374, 0.021148723570834374, 0.021289663238130644, 0.02098230225162435, 0.020481520765354942, 0.01627457808010044, 0.01613997820704142, 0.01613997820704142, 0.01613997820704142, 0.015750538700320945, 0.01576422644543055, 0.01575751971426306, 0.015775523553298337, 0.015813129178732115, 0.015813129178732115, 0.015813129178732115, 0.01590765925863254, 0.015579553179022473, 0.015587593790844982, 0.015660654571136606, 0.015597318387670155, 0.015597318387670155, 0.015597318387670155, 0.015570240875852973, 0.015372655794596785, 0.015515527155809101, 0.015463686627812036, 0.014660757904822932, 0.014660757904822932, 0.014660757904822932, 0.014350311932325405, 0.014285200168557353, 0.014233174696498426, 0.014889724443920997, 0.008241260712627823, 0.00838182462167798, 0.00838182462167798, 0.008152811038543413]",
            "date": "nan",
            "name": "基金",
            "eversincemaxdrawdown": "-0.12574097462706882",
            "index_name": "沪深300",
            "financialIncomeChange": "nan",
            "marketValueGainLossChange": "nan",
            "total_buy": "nan",
            "eversinceexcess_cum_return": "-0.08221825337444089",
            "date_list": "['2024-03-19', '2024-03-20', '2024-03-21', '2024-03-22', '2024-03-23', '2024-03-24', '2024-03-25', '2024-03-26', '2024-03-27', '2024-03-28', '2024-03-29', '2024-03-30', '2024-03-31', '2024-04-01', '2024-04-02', '2024-04-03', '2024-04-04', '2024-04-05', '2024-04-06', '2024-04-07', '2024-04-08', '2024-04-09', '2024-04-10', '2024-04-11', '2024-04-12', '2024-04-13', '2024-04-14', '2024-04-15', '2024-04-16', '2024-04-17', '2024-04-18', '2024-04-19', '2024-04-20', '2024-04-21', '2024-04-22', '2024-04-23', '2024-04-24', '2024-04-25', '2024-04-26', '2024-04-27', '2024-04-28', '2024-04-29', '2024-04-30', '2024-05-01', '2024-05-02', '2024-05-03', '2024-05-04', '2024-05-05', '2024-05-06', '2024-05-07', '2024-05-08', '2024-05-09', '2024-05-10', '2024-05-11', '2024-05-12', '2024-05-13', '2024-05-14', '2024-05-15', '2024-05-16', '2024-05-17', '2024-05-18', '2024-05-19', '2024-05-20', '2024-05-21', '2024-05-22', '2024-05-23', '2024-05-24', '2024-05-25', '2024-05-26', '2024-05-27', '2024-05-28', '2024-05-29', '2024-05-30', '2024-05-31', '2024-06-01', '2024-06-02', '2024-06-03', '2024-06-04', '2024-06-05', '2024-06-06', '2024-06-07', '2024-06-08', '2024-06-09', '2024-06-10', '2024-06-11', '2024-06-12', '2024-06-13', '2024-06-14', '2024-06-15', '2024-06-16', '2024-06-17', '2024-06-18', '2024-06-19', '2024-06-20', '2024-06-21', '2024-06-22', '2024-06-23', '2024-06-24', '2024-06-25', '2024-06-26', '2024-06-27', '2024-06-28', '2024-06-29', '2024-06-30', '2024-07-01', '2024-07-02', '2024-07-03', '2024-07-04', '2024-07-05', '2024-07-06', '2024-07-07', '2024-07-08', '2024-07-09', '2024-07-10', '2024-07-11', '2024-07-12', '2024-07-13', '2024-07-14', '2024-07-15']",
            "code": "基金"
        }
    },
    {
        "rowId": 2,
        "parentRowId": -1,
        "data": {
            "eversincevolatility": "0.001197309520954422",
            "eversincecum_return": "0.0012041343921556091",
            "eversinceexcess_maxdrawdown": "-0.2700190269047113",
            "netasset_list": "[14196580.15, 14198012.05, 14200132.0, 14202246.62, 14204271.42, 14204271.42, 14204271.42, 14196725.63, 28373213.23, 28382152.25, 28379532.16, 28379532.16, 28379532.16, 28399614.36, 28412917.85, 28415457.7]",
            "netasset": "nan",
            "index_cum_return": "0.15906841758632595",
            "weight_list": "[0.034480953122201854, 0.03481043904016988, 0.03494577824075964, 0.034512590041776536, 0.033512206021062516, 0.033512206021062516, 0.033512206021062516, 0.03318171516880775, 0.05965244166429899, 0.0587504367496449, 0.058111709499081926, 0.058111709499081926, 0.058111709499081926, 0.05740674386268303, 0.05707506323603492, 0.055859020253479205]",
            "date": "nan",
            "name": "债券",
            "eversincemaxdrawdown": "-0.0005312338471412469",
            "index_name": "沪深300",
            "financialIncomeChange": "nan",
            "marketValueGainLossChange": "nan",
            "total_buy": "nan",
            "eversinceexcess_cum_return": "-0.15786428319417034",
            "date_list": "['2024-04-29', '2024-04-30', '2024-05-01', '2024-05-02', '2024-05-03', '2024-05-04', '2024-05-05', '2024-05-06', '2024-05-08', '2024-05-09', '2024-05-10', '2024-05-11', '2024-05-12', '2024-05-13', '2024-05-14', '2024-05-15']",
            "code": "债券"
        }
    },
    {
        "rowId": 1,
        "parentRowId": -1,
        "data": {
            "eversincevolatility": "0.09190194515984892",
            "eversincecum_return": "0.12931829117053484",
            "eversinceexcess_maxdrawdown": "-0.2650546248085475",
            "netasset_list": "[102345767.12, 167089025.87, 181587589.51, 181927196.7, 181927196.7, 181927196.7, 204022936.81, 260829503.54, 285357415.75, 290007999.95, 289770024.22, 289770024.22, 289770024.22, 289236582.71, 297857445.97, 302174444.09, 300263893.5, 302643691.37, 302643691.37, 302643691.37, 306351783.82, 310156694.1, 308549215.53, 307288154.42, 311444598.97, 311444598.97, 311444598.97, 310576196.51, 315574839.19, 318496649.6, 321007639.36, 317185915.77, 317185915.77, 317185915.77, 320923763.15, 331692369.62, 350818848.86, 370072960.36, 381088403.99, 381088403.99, 381088403.99, 387204055.11, 383549489.59, 381717007.84000003, 386711325.6, 396733681.38, 396733681.38, 396733681.38, 400952381.99, 420380721.05, 429803157.99, 435967952.15, 441361410.49, 441361410.49, 441361410.49, 447586919.34000003, 450601592.37, 461140792.33, 461687657.29, 463081616.03000003, 463081616.03000003, 463081616.03000003, 463049058.76, 470272382.71, 471332784.32, 474146813.64, 478372776.14, 478372776.14, 478372776.14, 478959933.32, 476080913.76, 470217828.76, 467395961.12, 509315956.1, 509315956.1, 509315956.1, 554751166.04, 563178582.45, 572382320.67, 574282616.9399999, 585091797.71, 585091797.71, 585091797.71, 582442774.68, 587216463.35, 603485114.88, 607907141.01, 614151546.84, 614151546.84, 614151546.84, 614401874.06, 615656515.86, 616022074.72, 619994861.7, 620896409.32, 620896409.32, 620896409.32, 621935169.72, 623278863.37, 622727960.01, 628657199.92, 628238183.57, 628238183.57, 628238183.57, 628062936.62, 631055874.02, 633364708.29, 635493319.79, 674247434.4399999, 674247434.4399999, 674247434.4399999, 690769043.66, 693285588.38, 699516478.8, 703485663.29, 712458188.66, 700232833.93, 700232833.93, 714106936.86, 729682422.71, 718347587.46, 719392423.37, 748606853.15, 748606853.15, 748606853.15, 779466118.2, 791736485.94, 780375697.2, 786704097.12, 856572775.45, 856572775.45, 856572775.45, 882919810.29, 907416822.06, 923054880.43, 926886271.32, 895818758.12, 895818758.12, 895818758.12, 884021030.26, 950758398.55, 1028410250.67, 1051650150.34, 1056930697.37, 1056930697.37, 1056930697.37, 1093384675.78, 1140748285.2, 1185515250.43, 1222858943.23, 1232554049.22, 1232554049.22, 1232554049.22, 1241003169.33, 1236966133.2, 1246659518.97, 1305086460.96, 1323701594.51, 1323701594.51, 1323701594.51, 1322166300.14, 1325752698.38, 1335753584.92, 1342157965.41, 1348054576.61, 1348054576.61, 1348054576.61, 1343713838.91, 1327174246.26, 1323452362.61, 1315300340.64, 1301847560.97, 1301847560.97, 1301847560.97, 1312971841.22, 1352985384.37, 1372921823.4, 1393797728.15, 1402582406.25, 1402582406.25, 1402582406.25, 1404023500.17, 1410635042.91, 1403775297.39, 1433684044.59, 1418364997.77, 1418364997.77, 1418364997.77, 1416978986.17, 1418690159.19, 1408377295.0, 1425224960.44, 1427365768.62, 1427365768.62, 1427365768.62, 1420173214.29, 1412873272.13, 1412873272.13, 1412873272.13, 1397034354.74, 1397034354.74, 1397034354.74, 1409698506.94, 1417324345.17, 1422921690.57, 1421085777.85, 1427279247.78, 1427279247.78, 1427279247.78, 1437380413.67, 1430462108.76, 1430848108.65, 1435291396.8, 1430653019.0, 1430653019.0, 1430653019.0, 1421324916.27, 1434152167.59, 1425515038.81, 1428301444.09, 1453986954.88, 1453986954.88, 1453986954.88, 1443990121.25, 1462081224.17, 1459738952.89, 1445882522.14, 1469847546.22, 1469847546.22, 1469847546.22, 1470750048.64, 1481515889.07, 1507694777.94, 1521641398.23, 1518246579.69, 1518246579.69, 1518246579.69, 1525101485.67, 1514817189.26, 1517407369.99, 1517407369.99]",
            "netasset": "1517407369.99",
            "index_cum_return": "0.15906841758632595",
            "weight_list": "[0.9670294501660973, 0.9718806422072868, 0.974166346944763, 0.9743404826495502, 0.9743404826495502, 0.9743404826495502, 0.9702869244564765, 0.9707869334409933, 0.9727895546373155, 0.9731857450215411, 0.9731635783683444, 0.9731635783683444, 0.9731635783683444, 0.9737265327804928, 0.9745679165934149, 0.9749083590376857, 0.9750100210546153, 0.9709965831842683, 0.9709965831842683, 0.9709965831842683, 0.9675278881358325, 0.9671509723455224, 0.9687840418730155, 0.9692103861760231, 0.9695168573709617, 0.9695168573709617, 0.9695168573709617, 0.969607862217998, 0.9710196045887544, 0.9713097847422186, 0.9716938002268235, 0.9713035097587088, 0.9713035097587088, 0.9713035097587088, 0.9714685295246247, 0.9720700483582553, 0.9736791813152509, 0.9750742054247434, 0.9759433388096793, 0.9759433388096793, 0.9759433388096793, 0.9404493710391495, 0.9403799686351841, 0.9393854864661082, 0.9397393104095221, 0.9360156866040182, 0.9360156866040182, 0.9360156866040182, 0.9371377655797651, 0.9629179190594174, 0.903627220551148, 0.9024441618116112, 0.9037592982822424, 0.9037592982822424, 0.9037592982822424, 0.9047484697900932, 0.9051556941300128, 0.9065091659060948, 0.9591076647124627, 0.9591697245752462, 0.9591697245752462, 0.9591697245752462, 0.9595609397539214, 0.9605831640141059, 0.9610945383441722, 0.9584479534444994, 0.9583568583856791, 0.9583568583856791, 0.9583568583856791, 0.9584119526378776, 0.9581515996046263, 0.9579100994496836, 0.9567536327256155, 0.9598670814376196, 0.9598670814376196, 0.9598670814376196, 0.9624861073151806, 0.9625945835438672, 0.9625417000769405, 0.9625560434124707, 0.9632423956128648, 0.9632423956128648, 0.9632423956128648, 0.9629911498951538, 0.9636528956656071, 0.9639979259871267, 0.9683652716225614, 0.9685951009659797, 0.9685951009659797, 0.9685951009659797, 0.9689964135242565, 0.9693120517829384, 0.9693251076671453, 0.9692497262176574, 0.969522968771596, 0.969522968771596, 0.969522968771596, 0.9692215747572726, 0.9692714966642048, 0.9686520677440882, 0.9688297577738437, 0.9689586792000991, 0.9689586792000991, 0.9689586792000991, 0.9688417531525039, 0.9694995760705415, 0.9690992594983, 0.9692025051495756, 0.9708959464430911, 0.9708959464430911, 0.9708959464430911, 0.9710341635048945, 0.9708626087065133, 0.9715668846479341, 0.9707385650016861, 0.9773118613125868, 0.9769248897588186, 0.9769248897588186, 0.9773007806367873, 0.9857558842100838, 0.985540975425365, 0.9856264150945565, 0.9859289947047666, 0.9859289947047666, 0.9859289947047666, 0.9861637936839972, 0.9855765271756418, 0.9851979565918759, 0.9851665944744303, 0.9860230799870736, 0.9860230799870736, 0.9860230799870736, 0.9864147860381581, 0.9867006067025217, 0.9868614726316367, 0.9870608113088096, 0.9867721799721928, 0.9867721799721928, 0.9867721799721928, 0.9869064676055261, 0.9879886630652563, 0.9879685385362981, 0.9879684584207274, 0.9879306928634134, 0.9879306928634134, 0.9879306928634134, 0.9884303681465842, 0.9889119872227581, 0.9885108994086815, 0.9885627178112496, 0.9886613955974846, 0.9886613955974846, 0.9886613955974846, 0.9884187185707466, 0.9886846686943802, 0.9884829761251436, 0.9902208274349652, 0.9894921582569718, 0.9894921582569718, 0.9894921582569718, 0.9894601957269036, 0.9893410116096816, 0.9898208146858959, 0.9898372199452344, 0.989916976619959, 0.989916976619959, 0.989916976619959, 0.9898983898694929, 0.9900691523060021, 0.9898392661213455, 0.9899251984654467, 0.9899580737014274, 0.9899580737014274, 0.9899580737014274, 0.9900359555565853, 0.9904490460953661, 0.990366773038414, 0.990624579750198, 0.9906899020281216, 0.9906899020281216, 0.9906899020281216, 0.9907637243798525, 0.9900173279360954, 0.9900325737083224, 0.9898748123522916, 0.9898860032151179, 0.9898860032151179, 0.9898860032151179, 0.9896906478810148, 0.9888851965966876, 0.9888644827817864, 0.988439143016037, 0.9881917228769771, 0.9881917228769771, 0.9881917228769771, 0.9881459139399961, 0.987691495662072, 0.987691495662072, 0.987691495662072, 0.9868305172976142, 0.9868305172976142, 0.9868305172976142, 0.9872973811669858, 0.987899548567635, 0.9886441314067225, 0.9887277895542903, 0.9887444990494804, 0.9887444990494804, 0.9887444990494804, 0.9892419185743005, 0.9895472788280617, 0.9873328924115455, 0.9876912091029862, 0.9872309454762038, 0.9872309454762038, 0.9872309454762038, 0.9873734706068609, 0.9874811672664754, 0.9875544094194277, 0.9877492245877173, 0.9878679639815261, 0.9878679639815261, 0.9878679639815261, 0.9875448470748475, 0.9875154105269167, 0.9877205095691718, 0.9876570657290598, 0.988082068243111, 0.988082068243111, 0.988082068243111, 0.9880899553310877, 0.9880380993580786, 0.9882731962909125, 0.9881036556618584, 0.9887516372371148, 0.9887516372371148, 0.9887516372371148, 0.987090814803055, 0.9868367324699657, 0.9872097461972381, 0.9872097461972381]",
            "date": "2024-11-14",
            "name": "股票",
            "eversincemaxdrawdown": "-0.05504714132962214",
            "index_name": "沪深300",
            "financialIncomeChange": "56970446.03",
            "marketValueGainLossChange": "136420378.55",
            "total_buy": "1359969982.7",
            "eversinceexcess_cum_return": "-0.02975012641579111",
            "date_list": "['2024-03-19', '2024-03-20', '2024-03-21', '2024-03-22', '2024-03-23', '2024-03-24', '2024-03-25', '2024-03-26', '2024-03-27', '2024-03-28', '2024-03-29', '2024-03-30', '2024-03-31', '2024-04-01', '2024-04-02', '2024-04-03', '2024-04-04', '2024-04-05', '2024-04-06', '2024-04-07', '2024-04-08', '2024-04-09', '2024-04-10', '2024-04-11', '2024-04-12', '2024-04-13', '2024-04-14', '2024-04-15', '2024-04-16', '2024-04-17', '2024-04-18', '2024-04-19', '2024-04-20', '2024-04-21', '2024-04-22', '2024-04-23', '2024-04-24', '2024-04-25', '2024-04-26', '2024-04-27', '2024-04-28', '2024-04-29', '2024-04-30', '2024-05-01', '2024-05-02', '2024-05-03', '2024-05-04', '2024-05-05', '2024-05-06', '2024-05-07', '2024-05-08', '2024-05-09', '2024-05-10', '2024-05-11', '2024-05-12', '2024-05-13', '2024-05-14', '2024-05-15', '2024-05-16', '2024-05-17', '2024-05-18', '2024-05-19', '2024-05-20', '2024-05-21', '2024-05-22', '2024-05-23', '2024-05-24', '2024-05-25', '2024-05-26', '2024-05-27', '2024-05-28', '2024-05-29', '2024-05-30', '2024-05-31', '2024-06-01', '2024-06-02', '2024-06-03', '2024-06-04', '2024-06-05', '2024-06-06', '2024-06-07', '2024-06-08', '2024-06-09', '2024-06-10', '2024-06-11', '2024-06-12', '2024-06-13', '2024-06-14', '2024-06-15', '2024-06-16', '2024-06-17', '2024-06-18', '2024-06-19', '2024-06-20', '2024-06-21', '2024-06-22', '2024-06-23', '2024-06-24', '2024-06-25', '2024-06-26', '2024-06-27', '2024-06-28', '2024-06-29', '2024-06-30', '2024-07-01', '2024-07-02', '2024-07-03', '2024-07-04', '2024-07-05', '2024-07-06', '2024-07-07', '2024-07-08', '2024-07-09', '2024-07-10', '2024-07-11', '2024-07-12', '2024-07-13', '2024-07-14', '2024-07-15', '2024-07-16', '2024-07-17', '2024-07-18', '2024-07-19', '2024-07-20', '2024-07-21', '2024-07-22', '2024-07-23', '2024-07-24', '2024-07-25', '2024-07-26', '2024-07-27', '2024-07-28', '2024-07-29', '2024-07-30', '2024-07-31', '2024-08-01', '2024-08-02', '2024-08-03', '2024-08-04', '2024-08-05', '2024-08-06', '2024-08-07', '2024-08-08', '2024-08-09', '2024-08-10', '2024-08-11', '2024-08-12', '2024-08-13', '2024-08-14', '2024-08-15', '2024-08-16', '2024-08-17', '2024-08-18', '2024-08-19', '2024-08-20', '2024-08-21', '2024-08-22', '2024-08-23', '2024-08-24', '2024-08-25', '2024-08-26', '2024-08-27', '2024-08-28', '2024-08-29', '2024-08-30', '2024-08-31', '2024-09-01', '2024-09-02', '2024-09-03', '2024-09-04', '2024-09-05', '2024-09-06', '2024-09-07', '2024-09-08', '2024-09-09', '2024-09-10', '2024-09-11', '2024-09-12', '2024-09-13', '2024-09-14', '2024-09-15', '2024-09-16', '2024-09-17', '2024-09-18', '2024-09-19', '2024-09-20', '2024-09-21', '2024-09-22', '2024-09-23', '2024-09-24', '2024-09-25', '2024-09-26', '2024-09-27', '2024-09-28', '2024-09-29', '2024-09-30', '2024-10-01', '2024-10-02', '2024-10-03', '2024-10-04', '2024-10-05', '2024-10-06', '2024-10-07', '2024-10-08', '2024-10-09', '2024-10-10', '2024-10-11', '2024-10-12', '2024-10-13', '2024-10-14', '2024-10-15', '2024-10-16', '2024-10-17', '2024-10-18', '2024-10-19', '2024-10-20', '2024-10-21', '2024-10-22', '2024-10-23', '2024-10-24', '2024-10-25', '2024-10-26', '2024-10-27', '2024-10-28', '2024-10-29', '2024-10-30', '2024-10-31', '2024-11-01', '2024-11-02', '2024-11-03', '2024-11-04', '2024-11-05', '2024-11-06', '2024-11-07', '2024-11-08', '2024-11-09', '2024-11-10', '2024-11-11', '2024-11-12', '2024-11-13', '2024-11-14']",
            "code": "股票"
        }
    },
    {
        "rowId": 0,
        "parentRowId": -1,
        "data": {
            "eversincevolatility": "0.30336949186252343",
            "eversincecum_return": "-0.18931596546752605",
            "eversinceexcess_maxdrawdown": "-0.3456629043908887",
            "netasset_list": "[2196273.08, 2196273.08, 2196273.08, 2169434.65, 5505779.09, 5489820.86, 6435903.9, 6436976.72, 6436976.72, 6436976.72, 6501432.72, 6447974.49, 6455455.3, 6808914.66, 6860986.16, 6860986.16, 6860986.16, 6845010.37, 6466426.17, 6431843.02, 8217705.91, 8491967.83, 8491967.83, 8491967.83, 8490654.06, 8558484.25, 8406094.44, 8509938.7, 8354233.32, 8354233.32, 8354233.32, 8600318.38, 8635987.4, 9170482.100000001, 9147546.440000001, 9481131.83, 9481131.83, 9481131.83, 9507384.17, 9362774.48, 9716227.19, 9642585.63, 9678940.899999999, 9678940.899999999, 9678940.899999999, 9671347.6, 9478770.57, 9480236.049999999, 9578819.520000001, 9390973.110000001, 9390973.110000001, 9390973.110000001, 9542350.899999999, 9741357.78, 10132021.34, 10063908.41, 10013339.3, 10013339.3, 10013339.3, 10105106.2, 9846798.469999999, 10055163.15, 10054176.129999999, 10030276.75, 10030276.75, 10030276.75, 10397113.0, 10605836.469999999, 10223786.59, 10415051.69, 10531742.14, 10531742.14, 10531742.14, 10628960.19, 10543869.01, 10538988.919999998, 10491041.959999999, 10683985.41, 10683985.41, 10683985.41, 10936169.120000001, 11586710.290000001, 11724704.53, 11845205.64, 12141956.32, 12141956.32, 12141956.32, 12159848.68, 12230754.82, 12289041.719999999, 12150372.32, 12008576.600000001, 12008576.600000001, 12008576.600000001, 11728525.83, 11558715.09, 12523959.84, 12807061.2, 12912263.28, 12912263.28, 12912263.28, 12798127.8, 12790452.26, 13778810.1, 14147997.450000001, 14135722.129999999, 14135722.129999999, 14135722.129999999, 14540808.150000002, 14156871.3, 14525093.290000001, 12888706.6, 14056955.129999999, 14056955.129999999, 14056955.129999999, 14083814.67, 14283429.530000001, 13736711.81, 13780100.33, 13730914.95, 13730914.95, 13730914.95, 13712188.51, 13312166.399999999, 13585283.71, 13386253.739999998, 13205667.6, 13205667.6, 13205667.6, 13214176.42, 13046911.49, 13354312.649999999, 13191111.66, 13180895.04, 13180895.04, 13180895.04, 13088840.16, 14223899.559999999, 14132895.4, 14664803.870000001, 14491910.16, 14491910.16, 14491910.16, 14760304.49, 15945695.48, 15859614.629999999, 16669536.06, 17056134.11, 17056134.11, 17056134.11, 17036811.33, 17607073.54, 17607073.54, 17607073.54, 18643748.29, 18643748.29, 18643748.29, 18137253.419999998, 17360332.26, 16344113.34, 16201403.58, 16247618.01, 16247618.01, 16247618.01, 15631621.79, 15110163.9, 18357240.07, 17886867.39, 18504369.71, 18504369.71, 18504369.71, 18175899.36, 18181522.54, 17964961.09, 17714820.5, 17856457.29, 17856457.29, 17856457.29, 18211950.41, 18484252.16, 18147694.95, 18069463.13, 17728833.78, 17728833.78, 17728833.78, 17727838.12, 17936298.08, 17890236.0, 18319909.990000002, 17272070.81, 17272070.81, 17272070.81, 19945295.029999997, 20205919.849999998, 19659475.06, 19659475.06]",
            "netasset": "19659475.06",
            "index_cum_return": "0.15906841758632595",
            "weight_list": "[0.005181677662948623, 0.005181677662948623, 0.005181677662948623, 0.005070575040312456, 0.012611456897694126, 0.011541915113524903, 0.013322180843552587, 0.013180757142016017, 0.013180757142016017, 0.013180757142016017, 0.013141941935070229, 0.01295250821136943, 0.01269011437912294, 0.014144805770012297, 0.0142109942990599, 0.0142109942990599, 0.0142109942990599, 0.014184684017826417, 0.013208388029183183, 0.013115169162118441, 0.01661140217516757, 0.017012539209984846, 0.017012539209984846, 0.017012539209984846, 0.016990031463405338, 0.017224646351738468, 0.017124579861717094, 0.01741973710253549, 0.015744555925404503, 0.015744555925404503, 0.015744555925404503, 0.014921441298314539, 0.014760779180609416, 0.015421460643170765, 0.015332217706910267, 0.01560888081630083, 0.01560888081630083, 0.01560888081630083, 0.015719186866715657, 0.015364802082768528, 0.015520553247518283, 0.015360150297338187, 0.015264920826978941, 0.015264920826978941, 0.015264920826978941, 0.015253047775422707, 0.014923721771631105, 0.014917372618591737, 0.014974750229044282, 0.014663902049671832, 0.014663902049671832, 0.014663902049671832, 0.014870765984094918, 0.015148950156772737, 0.015760338465066873, 0.015509587655019644, 0.015444002412230592, 0.015444002412230592, 0.015444002412230592, 0.01558800597164306, 0.015127768134861701, 0.015385213345890884, 0.015333808222612261, 0.014443295652085986, 0.014443295652085986, 0.014443295652085986, 0.01461552456278012, 0.014852191124929377, 0.01419994065556762, 0.014371710554392904, 0.014446877974785333, 0.014693285619503435, 0.014693285619503435, 0.01454640832466921, 0.014244115789916243, 0.01445902457463501, 0.014373584905443385, 0.014071005295233443, 0.014071005295233443, 0.014071005295233443, 0.013836206316002745, 0.014423472824358232, 0.014802043408124101, 0.014833405525569682, 0.01397692001292628, 0.01397692001292628, 0.01397692001292628, 0.013585213961841978, 0.013299393297478266, 0.013138527368363248, 0.012939188691190316, 0.01322782002780715, 0.01322782002780715, 0.01322782002780715, 0.013093532394473865, 0.012011336934743615, 0.012031461463701874, 0.012031541579272524, 0.012069307136586617, 0.012069307136586617, 0.012069307136586617, 0.011569631853415837, 0.011088012777241927, 0.01148910059131851, 0.011437282188750411, 0.011338604402515376, 0.011338604402515376, 0.011338604402515376, 0.011581281429253344, 0.01131533130561984, 0.011517023874856456, 0.00977917256503488, 0.010507841743028159, 0.010507841743028159, 0.010507841743028159, 0.010539804273096403, 0.010658988390318466, 0.0101791853141041, 0.01016278005476566, 0.010083023380040922, 0.010083023380040922, 0.010083023380040922, 0.010101610130507187, 0.00993084769399784, 0.010160733878654429, 0.010074801534553281, 0.010041926298572685, 0.010041926298572685, 0.010041926298572685, 0.009964044443414616, 0.009550953904633844, 0.00963322696158591, 0.00937542024980193, 0.009310097971878472, 0.009310097971878472, 0.009310097971878472, 0.009236275620147396, 0.009982672063904654, 0.009967426291677517, 0.010125187647708487, 0.01011399678488201, 0.01011399678488201, 0.01011399678488201, 0.010309352118985173, 0.011114803403312324, 0.011135517218213464, 0.011560856983963112, 0.011808277123022994, 0.011808277123022994, 0.011808277123022994, 0.011854086060003976, 0.012308504337928041, 0.012308504337928041, 0.012308504337928041, 0.01316948270238585, 0.01316948270238585, 0.01316948270238585, 0.012702618833014137, 0.012100451432365027, 0.011355868593277597, 0.011272210445709772, 0.011255500950519652, 0.011255500950519652, 0.011255500950519652, 0.010758081425699464, 0.010452721171938198, 0.012667107588454527, 0.012308790897013672, 0.012769054523796121, 0.012769054523796121, 0.012769054523796121, 0.012626529393139168, 0.012518832733524589, 0.01244559058057234, 0.012250775412282668, 0.012132036018473923, 0.012132036018473923, 0.012132036018473923, 0.012455152925152434, 0.012484589473083245, 0.012279490430828167, 0.01234293427094018, 0.011917931756889015, 0.011917931756889015, 0.011917931756889015, 0.011910044668912446, 0.011961900641921378, 0.011726803709087567, 0.011896344338141518, 0.011248362762885242, 0.011248362762885242, 0.011248362762885242, 0.01290918519694502, 0.013163267530034325, 0.0127902538027619, 0.0127902538027619]",
            "date": "2024-11-14",
            "name": "存托凭证",
            "eversincemaxdrawdown": "-0.2529949727275338",
            "index_name": "沪深300",
            "financialIncomeChange": "121246.67",
            "marketValueGainLossChange": "-2913715.8600000003",
            "total_buy": "22573190.92",
            "eversinceexcess_cum_return": "-0.348384383053852",
            "date_list": "['2024-05-03', '2024-05-04']",
            "code": "存托凭证"
        }
    }
]