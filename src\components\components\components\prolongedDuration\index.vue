<template>
	<div id="prolongedDuration" v-show="show">
		<analysis-card-title title="久期拉长" image_id="prolongedDuration"></analysis-card-title>
		<div class="charts_fill_class">
			<v-chart
				ref="prolongedDuration"
				v-loading="empytzichanpeizh"
				element-loading-text="暂无数据"
				element-loading-spinner="el-icon-document-delete"
				element-loading-background="rgba(239, 239, 239, 0.5)"
				class="charts_one_class"
				autoresize
				:options="holdchangingOption"
			/>
		</div>

		<!-- <div>整体上，久期均值为 2.28 年，最高 3.96(大致在 2012-04-06)，最低 1.03 年（大致在 2018-09-27） ; 须知，久期的变化，除了投资者在不同成熟期的债券之间选择，杠杆也是影响久期的重要乘数因子，杠 杆率平均 1.09，最大 1.53（大致在 2014-12-23），最低 0.95（大致在 2010-04-13）。久期变化中 9.72% 是由于杠杆率的变化产生的。</div> -->
	</div>
</template>
<script>
import { exportTitle, exportChart } from '@/utils/exportWord.js';
import { lineChartOption } from '@/utils/chartStyle.js';

// 久期拉长
import { getDurationAnalysisInfo } from '@/api/pages/Analysis.js';

export default {
	name: 'prolongedDuration',
	data() {
		return {
			empytzichanpeizh: true,
			cardLoading: true,
			holdchangingOption: {},
			flag: 0,
			show: true,
			info: {}
		};
	},
	methods: {
		godetail(id) {
			//进入基金经理详情
		},
		hideLoading() {
			this.cardLoading = false;
			this.show = false;
		},
		getData(info) {
			this.info = info;
			this.getDurationAnalysisInfo();
		},
		// 获取久期拉长数据
		async getDurationAnalysisInfo() {
			let data = await getDurationAnalysisInfo({
				flag: this.info.flag,
				code: this.info.code,
				type: this.info.type,
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			if (data?.mtycode == 200) {
				this.getChartData(data?.data);
			} else {
				this.hideLoading();
			}
		},
		getChartData(data) {
			this.show = true;
			this.cardLoading = false;
			let arr1 = [];
			let arr2 = [];
			let arr3 = [];
			let arr4 = [];
			let arr5 = [];
			let arr6 = [];

			data
				.filter((item) => {
					return item.method == 'ir-riskexposure';
				})
				?.map((item) => {
					arr1.push([item.date, Number(item.duration).toFixed(2)]);
				});
			data
				.filter((item) => {
					return item.method == 'sharpe-regression';
				})
				?.map((item) => {
					arr2.push([item.date, Number(item.duration).toFixed(2)]);
				});
			data
				.filter((item) => {
					return item.method == 'duration-regression';
				})
				?.map((item) => {
					arr3.push([item.date, Number(item.duration).toFixed(2)]);
				});
			data
				.filter((item) => {
					return item.method == 'ir-riskanalysis';
				})
				?.map((item) => {
					arr4.push([item.date, Number(item.duration).toFixed(2)]);
				});
			data
				.filter((item) => {
					return item.method == '多券种组合';
				})
				?.map((item) => {
					arr5.push([item.date, Number(item.duration).toFixed(2)]);
				});
			data
				.filter((item) => {
					return item.method == '信息融合';
				})
				?.map((item) => {
					arr6.push([item.date, Number(item.duration).toFixed(2)]);
				});
			let series = [
				{
					name: '持仓估计',
					type: 'line',
					symbol: 'none',
					connectNulls: true,
					data: arr2
				},
				{
					name: '利率风险',
					type: 'line',
					symbol: 'none',
					connectNulls: true,
					data: arr1
				},
				{
					name: '回归久期估计法',
					type: 'line',
					symbol: 'none',
					connectNulls: true,
					data: arr3
				},
				{
					name: '利率灵敏度报告法',
					type: 'line',
					symbol: 'none',
					connectNulls: true,
					data: arr4
				},
				{
					name: '多券种组合',
					type: 'line',
					symbol: 'none',
					connectNulls: true,
					data: arr5
				},
				{
					name: '信息融合',
					type: 'line',
					symbol: 'none',
					connectNulls: true,
					data: arr6
				}
			].filter((item) => {
				return item.data.length !== 0;
			});

			if (arr1 == [] && arr2 == [] && arr3 == [] && arr4 == [] && arr5 == []) {
				this.empytzichanpeizh = true;
			} else {
				this.empytzichanpeizh = false;
				this.holdchangingOption = lineChartOption({
					toolbox: 'none',
					legend: series?.map((item) => {
						return item.name;
					}),
					xAxis: [
						{
							data: data.map((item) => {
								return item.date;
							})
						}
					],
					yAxis: [{ type: 'value', scale: true }],
					series
				});
			}
		},
		createPrintWord() {
			if (this.empytzichanpeizh) {
				return [];
			} else {
				this.$refs['prolongedDuration'].mergeOptions({ toolbox: { show: false } });
				let height = this.$refs['prolongedDuration'].$el.clientHeight;
				let width = this.$refs['prolongedDuration'].$el.clientWidth;
				let chart = this.$refs['prolongedDuration'].getDataURL({
					type: 'png',
					pixelRatio: 2,
					backgroundColor: '#fff'
				});
				this.$refs['prolongedDuration'].mergeOptions({ toolbox: { show: true } });
				return [...exportTitle('久期拉长'), ...exportChart(chart, { width, height })];
			}
		}
	}
};
</script>
