import request from '@/api/request';

/**
 * 
 * @param {基础信息} params 
 *  基金,基金经理:
      code: str (基金 基金经理code)
      flag: int(是否为基金)
          1: fund
          2:manager
      type: str基金(基金经理类型)
    基金公司:
      code:str (基金公司)
 * @returns 
 */

// 获取基金/基金经理基础信息
export function getReturnInfo(params) {
	return request({
		url: '/ReturnInfo/',
		method: 'get',
		params
	});
}
// 获取基金基础信息
export function getFundBasicInfo(params) {
	return request({
		url: '/BasicInfo/',
		method: 'get',
		params
	});
}
// 获取基金基础信息
export function getProductIndex(params) {
	return request({
		url: '/productindex/',
		method: 'get',
		params
	});
}
// 获取基金基础信息_2
export function getBasicInfo(params) {
	return request({
		url: '/BasicInfo/',
		method: 'get',
		params
	});
}

// 获取基金经理头部能力项（新）
export function getNewCapInfoByLan(params) {
	return request({
		url: '/bondfundopcharacteristics/',
		method: 'get',
		params
	});
}
export function getFundMessage(params) {
	return request({
		url: '/fund_message/',
		method: 'get',
		params
	});
}
// 获取被动权益跟踪标的
export function getFundBmindexName(params) {
	return request({
		url: '/FundBmindexName/',
		method: 'get',
		params
	});
}

// 获取fof择基能力 code,type,flag
export function getFofChooseFund(params) {
	return request({
		url: '/FofChooseFund/',
		method: 'get',
		params
	});
}
// 获取基金公司基础信息
export function getCompanyBasicInfo(params) {
	return request({
		url: '/BasicInfoCompany/',
		method: 'get',
		params
	});
}
// 基金/基金经理能力项
export function getCapabilityInfo(data) {
	return request({
		url: '/CapabilityInfo/',
		method: 'post',
		data
	});
}
// 基金经理基础信息
// code
export function getManagerType(params) {
	return request({
		url: '/TypeInfo/',
		method: 'get',
		params
	});
}
export function getFundApiTop(params) {
	return request({
		url: '/fundapitop/',
		method: 'get',
		params
	});
}
// 基金经理基础信息
// manager_code
export function getFundManagerBasicInfo(params) {
	return request({
		url: '/managertopmsg/',
		method: 'get',
		params
	});
}
// 是否关注
export function getIsExists(params) {
	return request({
		url: '/pool/exists/',
		method: 'get',
		params
	});
}
// 是否关注
export function deleteIsExists(params) {
	return request({
		url: '/pool/exists/',
		method: 'delete',
		params
	});
}
// 是否关注
export function postIsExists(data) {
	return request({
		url: '/pool/exists/',
		method: 'post',
		data
	});
}
// 固收+股债分管
export function getBondFundTop(params) {
	return request({
		url: '/bondfundtop/',
		method: 'get',
		params
	});
}
// 获取
export function getMrl(params) {
	return request({
		url: '/mtytalks/latest',
		method: 'get',
		params
	});
}
// 获取fof类型
export function getFofType(params) {
	return request({
		url: '/FofType/',
		method: 'get',
		params
	});
}
