<template>
  <div class="box_Board">
    <div class="header_box">
      <span class="header_inactive">投后&nbsp;/&nbsp;映射管理&nbsp;/&nbsp; </span>
      白名单管理列表
    </div>
    <!-- 搜索/表格/分页器 -->
    <div class="border_table">
      <!-- 搜索 -->
      <div class="border_table_header">
        <div class="border_table_header_title">白名单管理列表</div>
        <div class="border_table_header_search">
          <el-input v-model="form.name"
                    @clear="reset"
                    class="search-security"
                    clearable
                    placeholder="请输入证券代码或者简称"
                    style="width: 240px"
          >
            <i slot="prefix" class="el-input__icon el-icon-search"></i>
          </el-input>
          <el-button type="primary" class="search_button" @click="getWhiteList(form)">查询</el-button>
          <el-button type="primary" @click="showDialog = true">
            上传白名单
          </el-button>
          <el-button @click="showRecord">
            修改记录
          </el-button>
        </div>
      </div>
      <!-- 表格 -->
      <el-table
          :data="tableData"
          v-loading="loading.tableLoading"
          border
          stripe>
        <el-table-column align="gotoleft" label="证券代码" prop="code"/>
        <el-table-column align="gotoleft" label="证券简称" prop="name"/>
        <el-table-column align="gotoleft" label="入池时间" prop="insertDate"/>
        <el-empty :image-size="160"/>
      </el-table>
      <!--分页器-->
      <div class="pagination_board">
        <el-pagination :current-page.sync="pagination.pageIndex"
                       :page-size="pagination.pageSize"
                       :total="pagination.total"
                       background
                       layout="total, sizes, prev, pager, next"
                       @size-change="sizeChange"
                       @current-change="currentChange"/>
      </div>
    </div>
    <!-- 上传白名单 -->
    <el-dialog :visible.sync="showDialog" title="上传白名单" width="900px">
      <ExcelPort :excelUrl="`blacklist.xlsx`" :path="`/api/taikang/blackWhite/upload`"
                 :data="{type:2}" @refrshtable="uploadSuccess"/>
    </el-dialog>
    <!-- 修改记录 -->
    <el-dialog :visible.sync="showDialogRecord" title="修改记录" width="1200px">
      <!-- 表格 -->
      <el-table
          :data="tableDataRecord"
          v-loading="loading.dialogLoading"
          height="400">
        <el-table-column align="gotoleft" label="证券代码" prop="context.code"></el-table-column>
        <el-table-column align="gotoleft" label="证券简称" prop="context.name"></el-table-column>
        <el-table-column align="gotoleft" label="修改人 ID" prop="context.id"></el-table-column>
        <el-table-column align="gotoleft" label="操作类别" prop="actionType">
          <template slot-scope="scope">
            {{ scope.row.actionType === 1 ? '新增' : scope.row.actionType === 2 ? '删除' : '修改' }}
          </template>
        </el-table-column>
        <el-table-column align="gotoleft" label="修改时间" prop="context.insertDate" width="250"/>
        <el-empty :image-size="160"/>
      </el-table>
      <!-- 分页器 -->
      <div class="pagination_board">
        <el-pagination :current-page.sync="paginationRecord.pageIndex"
                       :page-size="paginationRecord.pageSize"
                       :total="paginationRecord.total"
                       background
                       layout="total, sizes, prev, pager, next"
                       @size-change="sizeChangeRecord"
                       @current-change="currentChangeRecord"/>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ExcelPort from './component/map/alphaownpool.vue';
import { getBWList } from '../../../api/pages/tkdesign/BWList';
import { getRecordList } from '../../../api/pages/tkdesign/historyRecord';

export default {
	components: {
		ExcelPort
	},
	data() {
		return {
			tableData: [], // 页面表格数据源
			tableDataRecord: [], // 修改记录表格数据源

			pagination: {
				pageIndex: 1, // 当前页码
				pageSize: 10, // 页面显示几条数据
				total: 0
			},

			paginationRecord: {
				pageIndex: 1, // paginationRecord
				pageSize: 10, // 页面显示几条数据
				total: 0
			},

			showDialog: false, // 绑定上传黑名单的dialog
			showDialogRecord: false, // 绑定修改记录的dialog

			form: {
				name: ''
			},
			loading: {
				tableLoading: false,
				dialogLoading: false
			}
		};
	},
	methods: {
		// 上传成功
		uploadSuccess() {
			this.showDialog = false;
			this.getWhiteList();
		},

		// 每页条数改变时触发的回调
		sizeChange(value) {
			this.pagination.pageSize = value;
			this.getWhiteList();
		},
		sizeChangeRecord(value) {
			this.paginationRecord.pageSize = value;
			this.getRecordList();
		},

		// 当前页数改变时触发的回调
		currentChange(value) {
			this.pagination.pageIndex = value;
			this.getWhiteList();
		},
		currentChangeRecord(value) {
			this.paginationRecord.pageIndex = value;
			this.getRecordList();
		},

		/**
		 * 重置数据
		 */
		reset() {
			this.form = {
				name: ''
			};
			this.getWhiteList();
		},

		/**
		 * 显示修改记录
		 */
		showRecord() {
			this.showDialogRecord = true;
			this.getRecordList();
		},

		/**
		 * 获取白名单列表
		 */
		getWhiteList(data) {
			this.loading.tableLoading = true;
			let params = {
				white: true,
				current: this.pagination.pageIndex,
				pageSize: this.pagination.pageSize,
				...data
			};
			getBWList(params).then((res) => {
				this.loading.tableLoading = false;
				if (res.code === 200) {
					this.tableData = res.data;
					this.pagination.total = res.total;
				} else {
					this.tableData = [];
					this.pagination.total = 0;
				}
			});
		},

		/**
		 * 获取历史修改记录
		 */
		getRecordList() {
			this.loading.dialogLoading = true;
			const params = {
				type: 5,
				current: this.paginationRecord.pageIndex,
				pageSize: this.paginationRecord.pageSize
			};
			getRecordList(params).then((res) => {
				this.loading.dialogLoading = false;
				if (res.code === 200) {
					this.tableDataRecord = res.data.map((item) => {
						return {
							...item,
							context: JSON.parse(item.context.value)
						};
					});
					this.paginationRecord.total = res.total;
				} else {
					this.tableDataRecord = [];
					this.paginationRecord.total = 0;
				}
			});
		}
	},
	mounted() {
		this.getWhiteList();
	}
};
</script>
<style lang="scss" scoped>
@import '../tkdesign';

.border_table_header_search {
	display: flex;

	.search-security {
		width: 250px;
		margin-right: 10px;
	}
}
</style>
<style>
.search-security .el-input__inner {
	padding: 0 30px !important;
}
</style>
