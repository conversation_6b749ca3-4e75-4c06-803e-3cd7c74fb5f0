<template>
	<component ref="combinations" :is="assetSelectionCom" @nextStep="handleNextStep" @backStep="handleBackStep"></component>
</template>
<script>
import assetSelectionCombination from './assetSelectionCombination.vue';
import assetSelectionConfiguration from './assetSelectionConfiguration.vue';
export default {
	components: {
		assetSelectionCombination,
		assetSelectionConfiguration
	},
	computed: {
		//资产选择和分析使用的组件
		assetSelectionCom() {
			if (this.$route.path === '/configurationStrategySteps') {
				return 'assetSelectionConfiguration';
			}
			return 'assetSelectionCombination';
		}
	},
	data() {
		return {};
	},
	methods: {
		initData() {
			this.$refs['combinations'].initData && this.$refs['combinations'].initData();
		},
		//数组去重
		handleNextStep(value) {
			//去除code一样的数组
			let result = value ? value : {};
			// let
			let selectList = result.selectList || [];
			// 数组去重
			const map = new Map();
			selectList = selectList.filter((key) => !map.has(key.code) && map.set(key.code, 1));
			result.selectList = selectList;
			this.$emit('nextStep', result);
		},
		handleBackStep() {
			this.$emit('backStep');
		}
	}
};
</script>
<style lang="scss" scoped></style>
