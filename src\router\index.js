import Vue from "vue";
import Router from "vue-router";
import basic from "./modules/basic";
import fund from "./modules/fund";
import filter from "./modules/filter";
import pool from "./modules/pool";
import manager from "./modules/manager";
import system from "./modules/system";
import tools from "./modules/tools";
import analysis from "./modules/tkdesign/analysis";
import mapManage from "./modules/tkdesign/mapManage";
import tkdesign from "./modules/tkDesign";
import portfolio from "./modules/portfolio";
import monitorSpectaculars from "./modules/tkdesign/monitorSpectaculars";
import monitorWarning from "./modules/tkdesign/monitorWarning";
Vue.use(Router);

const router = new Router({
  mode: "hash",
  routes: [
    {
      path: "/",
      redirect: "/dashboard",
    },
    {
      path: "/",
      component: () =>
        import(
          /* webpackChunkName: "dashboard" */ "../components/common/Home.vue"
        ),
      meta: { title: "自述文件" },
      children: [
        ...basic,
        ...fund,
        ...filter,
        ...pool,
        ...manager,
        ...system,
        ...tools,
        ...analysis,
        ...mapManage,
        ...monitorSpectaculars,
        ...monitorWarning,
        ...tkdesign,
        ...portfolio,
      ],
    },
    {
      path: "*",
      redirect: "/404",
    },
  ],
});

// export default new Router({
//     mode: 'history',
//     routes: routerss
// });
export default router;
