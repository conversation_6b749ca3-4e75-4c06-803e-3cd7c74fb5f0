<template>
	<div class="px-20 py-20 basic_info" id="managerInfo">
		<manager-avatar ref="managerAvatar"></manager-avatar>
		<!-- <association-pool></association-pool> -->
		<basic-return-info ref="basicReturnInfo"></basic-return-info>
		<basic-info-fund ref="basicInfoFund"></basic-info-fund>
		<basic-fund-capability ref="basicFundCapability"></basic-fund-capability>
		<manager-resume ref="managerResume"></manager-resume>
		<basic-brinson-rank ref="basicBrinsonRank"></basic-brinson-rank>
	</div>
</template>

<script>
// 基金经理头像
import managerAvatar from './components/managerAvatar.vue';
// 关联的基金池
import associationPool from './components/associationPool.vue';
// 基础收益率
import basicReturnInfo from './components/basicReturnInfo.vue';
// 基金基础信息
import basicInfoFund from './components/basicInfoFund.vue';
// 基金基础能力项
import basicFundCapability from './components/basicFundCapability.vue';
// 基础信息Brinson排名
import basicBrinsonRank from './components/basicBrinsonRank.vue';
// 基金经理个人简介
import managerResume from './components/managerResume.vue';
export default {
	components: {
		managerAvatar,
		associationPool,
		basicReturnInfo,
		basicInfoFund,
		basicFundCapability,
		basicBrinsonRank,
		managerResume
	},
	data() {
		return {
			info: {}
		};
	},
	methods: {
		async getData(info) {
			this.info = info;
			this.$refs['managerAvatar']?.getData(this.info);
			this.$refs['basicInfoFund']?.getData(this.info);
			this.$refs['basicReturnInfo']?.getData(this.info);
			this.$refs['basicFundCapability']?.getData(this.info);
			this.$refs['basicBrinsonRank']?.getData(this.info);
			this.$refs['managerResume']?.getData(this.info);
		},
		createPrintWord() {
			let downloadList = [
				...(this.$refs['managerAvatar']?.createPrintWord() || []),
				...(this.$refs['basicReturnInfo']?.createPrintWord() || []),
				...(this.$refs['basicInfoFund']?.createPrintWord() || []),
				...(this.$refs['basicFundCapability']?.createPrintWord() || []),
				...(this.$refs['basicBrinsonRank']?.createPrintWord() || []),
				...(this.$refs['managerResume']?.createPrintWord() || [])
			];
			return downloadList;
		}
	}
};
</script>
<style lang="scss" scoped>
.basic_info {
	width: 652px;
	height: 700px;
	border: 1px solid #d9d9d9;
	border-radius: 4px;
	background-color: #ffffff;
	box-shadow: 0px 1px 2px 0px rgba(0, 0, 18, 0.1);
	font-family: Monospaced Number, Chinese Quote, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, PingFang SC, Hiragino Sans GB,
		Microsoft YaHei, Helvetica Neue, Helvetica, Arial, sans-serif;
}
</style>
