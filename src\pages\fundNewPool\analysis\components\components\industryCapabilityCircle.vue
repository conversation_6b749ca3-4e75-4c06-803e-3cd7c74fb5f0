<template>
  <div v-show="show"
       class="chart_one industryCapabilityCircle">
    <div class="flex_between">
      <div class="title">{{ name }}</div>
      <div>
        <el-select v-model="model"
                   placeholder=""
                   multiple
                   style="width: 260px"
                   collapse-tags
                   @change="changeShowFund">
          <el-option v-for="item in options"
                     :key="item.value"
                     :label="item.label"
                     :value="item.value"> </el-option>
        </el-select>
      </div>
    </div>
    <div class="mt-16">
      <div class="flex_center">
        <el-popover v-model="visible"
                    placement="bottom"
                    width="500"
                    trigger="hover">
          <div class="px-8">
            <el-slider v-model="rank"
                       :marks="marks"
                       @change="changeRank"> </el-slider>
          </div>
          <div slot="reference"
               class="flex_start mr-24"
               style="cursor: pointer"
               @mouseenter="visible = true"
               @click="changeOptionShow('pool')">
            <div class="line mr-8"
                 :style="`background: ${pool_show ? '#4096ff' : '#f9f9f9'}`"></div>
            <div>{{ info.name }}</div>
            <div class="ml-8">
              <svg width="10"
                   height="10"
                   viewBox="0 0 10 10"
                   fill="none"
                   xmlns="http://www.w3.org/2000/svg">
                <path d="M0.89391 2.91276L4.78106 7.40359C4.89233 7.53214 5.10657 7.53214 5.21902 7.40359L9.10617 2.91276C9.25058 2.7453 9.12038 2.5 8.88719 2.5L1.11289 2.5C0.879706 2.5 0.749503 2.7453 0.89391 2.91276Z"
                      fill="black"
                      fill-opacity="0.45" />
              </svg>
            </div>
          </div>
        </el-popover>

        <div v-for="item in fund_list"
             :key="name + 'name' + item.code"
             class="flex_start mr-24"
             style="cursor: pointer"
             @click="changeOptionShow(item.code)">
          <div class="line mr-8"
               :style="`background: ${item.show ? item.color : '#f9f9f9'}`"></div>
          <div>{{ item.name }}</div>
        </div>
      </div>
      <div class="flex_start"
           style="flex-wrap: wrap">
        <div class="charts_fill_class"
             style="width: 25%"
             v-for="item in fund_list"
             :key="name + 'charts' + item.code">
          <v-chart v-if="item.serise"
                   :options="formatOption(item.serise, item.show)"
                   v-loading="loading"
                   element-loading-text="暂无数据"
                   element-loading-spinner="el-icon-document-delete"
                   element-loading-background="rgba(239, 239, 239, 0.5)"
                   class="charts_one_class"
                   autoresize />
        </div>
      </div>
      <el-pagination background
                     style="display: flex; justify-content: right; padding-top: 16px; padding-bottom: 24px"
                     @current-change="formatPagination"
                     :current-page.sync="currentPage"
                     :page-size="pageSIze"
                     layout="total, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    name: {
      type: String
    },
    types: {},
    data_type: {
      type: String,
      default: 'fund'
    },
    ismanager: {
      type: Boolean,
    }
  },
  data () {
    return {
      show: true,
      total: 0,
      currentPage: 1,
      pageSIze: 4,
      loading: false,
      pool_show: true,
      model: [],
      options: [],
      all_fund_list: [],
      fund_list: [],
      option: {},
      cache_option: {},
      info: {},
      rank: 66,
      visible: false,
      marks: {
        0: '0%',
        20: '20%',
        40: '40%',
        60: '60%',
        80: '80%',
        100: {
          style: {
            width: '70px'
          },
          label: this.$createElement('strong', '100%')
        }
      }
    };
  },
  methods: {
    getData (data, info) {
      this.info = info;
      // 下拉框
      this.options = this.info?.['code_list'].map((item) => {
        return { label: item.name, value: item.code };
      });
      if (data?.length > 0) {
        this.show = true;
      } else {
        this.show = false;
      }
      this.model = this.info['code_list']
        .filter((v) => v.flag != 0)
        .map((item) => {
          return item.code;
        });
      if (this.model.length == 0) {
        this.model = this.info['code_list'].slice(0, 8).map((item) => {
          return item.code;
        });
      }
      // 以池子图例为固定列表，基金没有补零
      let indicator = data
        .find((v) => v.code == this.info.code)
        ?.children.map((item) => {
          return { name: item.industry_name, code: item.industry_code, rank: item.industry_rank, max: 1 };
        });
      // console.log(data);
      // 各个关注基金的信息
      this.all_fund_list = data
        .filter((v) => v.flag == 'fund')
        .map((item) => {
          let serise = {
            value: indicator.map((obj) => {
              let index = item.children.findIndex((v) => v.industry_code == obj.code);
              return index == -1 ? 0 : item.children[index].industry_rank;
            }),
            name: item.name,
            symbol: 'none',
            lineStyle: {
              color: item.type == 1 ? '#FFD600' : item.color
            }
          };
          return { name: item.name, code: item.code, show: true, color: item.type == 1 ? '#FFD600' : item.color, serise };
        });
      this.formatPoolChart(indicator);
      this.cache_option = this.deepCopy(this.option);
      this.formatPagination();
    },
    // 格式化池子能力圈
    formatPoolChart (indicator) {
      // indicator = indicator.filter(item => item.rank >= 0.1)
      // console.log(indicator, 's');
      this.option = {
        toolbox: {
          show: true,
          feature: {
            saveAsImage: { pixelRatio: 3 }
          }
        },
        radar: {
          // shape: 'circle',
          indicator,
          radius: '65%',
          name: {
            color: 'rgba(0, 0, 0, 0.65)', // 字体颜色
            fontStyle: 'normal', // 字体样式
            fontWeight: '400' // 字体粗细
          },
          splitArea: {
            show: true,
            areaStyle: {
              color: '#ffffff' // 设置为统一的颜色，例如白色
            }
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(0, 0, 0, 0.12 )', // 线条颜色
              width: 1 // 线条宽度
            }
          },
          splitLine: {
            lineStyle: {
              type: 'dashed',
              color: 'rgba(0, 0, 0, 0.12)' // 设置为统一的颜色，例如白色
            }
          }
        },
        series: [
          {
            name: '行业能力圈',
            type: 'radar',
            data: [
              {
                value: indicator.map((v) => v.rank),
                name: this.info.name,
                symbol: 'none',
                lineStyle: {
                  color: '#4096ff'
                }
              }
            ]
          }
        ]
      };
    },
    // 基金池能力圈
    getPoolData (data) {
      // 以池子图例为固定列表，基金没有补零
      let indicator = data
        .find((v) => v.code == this.info.code)
        ?.children.map((item) => {
          return { name: item.industry_name, code: item.industry_code, rank: item.industry_rank, max: 1 };
        });
      this.formatPoolChart(indicator);
      this.cache_option = this.deepCopy(this.option);
    },
    // 格式化分页
    formatPagination () {
      this.fund_list = this.all_fund_list
        .filter((v) => this.model.some((n) => n == v.code))
        .slice((this.currentPage - 1) * 4, 4 * this.currentPage);

      if (this.fund_list.length == 0) {
        this.fund_list = this.all_fund_list.slice((this.currentPage - 1) * 4, 4 * this.currentPage);
        this.model = this.all_fund_list.map((v) => v.code);
      }
      this.total = this.all_fund_list
        .filter((v) => this.model.some((n) => n == v.code)).length;
    },
    // 监听选择关注基金
    changeShowFund (val) {
      this.formatPagination();
    },
    // 监听分位选择
    changeRank () {
      this.visible = false;
      this.$emit('resolveFather', this.rank / 100);
    },
    // 格式化图显示
    formatOption (data, show) {
      if (show) {
        return {
          ...this.option,
          series: [
            {
              ...this.option.series[0],
              data: [...this.option.series[0].data, data]
            }
          ]
        };
      } else {
        return {
          ...this.option,
          series: [
            {
              ...this.option.series[0]
            }
          ]
        };
      }
    },
    // 点击图例切换显示隐藏
    changeOptionShow (code) {
      if (code == 'pool') {
        this.option = {
          ...this.option,
          series: [
            {
              ...this.option.series[0],
              data: this.option.series[0].data.length == 0 ? this.cache_option.series[0].data : []
            }
          ]
        };
      } else {
        let index = this.fund_list.findIndex((v) => v.code == code);
        this.$set(this.fund_list, index, { ...this.fund_list[index], show: !this.fund_list[index].show });
      }
    },
    deepCopy (obj) {
      // 只拷贝对象
      if (typeof obj !== 'object') return;
      // 根据obj的类型判断是新建一个数组还是一个对象
      let newObj = obj instanceof Array ? [] : {};
      for (let key in obj) {
        // 遍历obj,并且判断是obj的属性才拷贝
        if (obj.hasOwnProperty(key)) {
          // 判断属性值的类型，如果是对象递归调用深拷贝
          newObj[key] = typeof obj[key] === 'object' ? this.deepCopy(obj[key]) : obj[key];
        }
      }
      return newObj;
    }
  }
};
</script>

<style lang="scss" scoped>
.industryCapabilityCircle {
	.line {
		width: 32px;
		height: 2px;
	}
}
</style>
