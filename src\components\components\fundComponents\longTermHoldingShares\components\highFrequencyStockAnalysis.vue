<template>
	<div class="high-frequency-stock-analysis">
		<div style="margin-bottom: 20px">
			<el-table v-loading="loadingAna" :data="ana">
				<el-table-column prop="name" align="gotoleft" :label="info.flag == '2' ? '名称' : '股票名称'"></el-table-column>
				<el-table-column prop="swlevel1" align="gotoleft" label="申万一级"></el-table-column>
				<el-table-column prop="swlevel2" align="gotoleft" label="申万二级"></el-table-column>
				<el-table-column prop="swlevel3" align="gotoleft" label="申万三级"></el-table-column>
				<el-table-column align="gotoleft" prop="cum_ret" label="区间涨跌幅">
					<template slot-scope="{ row }">
						<span :style="row.cum_ret > 0 ? 'color:red' : 'color:green'">{{ (parseFloat(row.cum_ret) * 100).toFixed(2) + '%' }}</span>
					</template>
				</el-table-column>
				<el-table-column align="gotoleft" prop="cum_excess_ret" label="指数收益率">
					<template slot-scope="{ row }">
						<span :style="row.cum_excess_ret > 0 ? 'color:red' : 'color:green'">
							{{ (parseFloat(row.cum_excess_ret) * 100).toFixed(2) + '%' }}
						</span>
					</template>
				</el-table-column>
				<!-- <el-table-column align="gotoleft" prop="maxdd" label="最大回撤">
					<template slot-scope="{ row }">
						<span>{{ parseFloat(row.maxdd).toFixed(3) + '%' }}</span>
					</template>
        </el-table-column>-->
			</el-table>
		</div>
		<div class="stock-trend" v-loading="loadingStock">
			<div class="stock-trend-header">
				<div class="header-title">股价走势特征</div>
				<el-radio-group class="select-weight-type" v-model="weightType">
					<el-radio-button label="weight">占权益比</el-radio-button>
					<el-radio-button label="weight_in_netasset">占净资产比</el-radio-button>
				</el-radio-group>
			</div>
			<v-chart
				v-loading="loadingStock"
				element-loading-text="暂无数据"
				element-loading-spinner="el-icon-document-delete"
				element-loading-background="rgba(239, 239, 239, 0.5)"
				class="stock-trend-chart high_frequency_stock_h400"
				autoresize
				:options="optionStock"
			/>
		</div>
		<div class="profit-valuation" v-loading="loadingStock">
			<div class="profit-valuation-header">
				<div class="header-title">盈利与估值</div>
			</div>
			<v-chart
				v-loading="loadingOption2"
				element-loading-text="暂无数据"
				element-loading-spinner="el-icon-document-delete"
				element-loading-background="rgba(239, 239, 239, 0.5)"
				class="profit-valuation-chart high_frequency_stock_h400"
				autoresize
				:options="option2"
			/>
		</div>
	</div>
</template>

<script>
import { getRateInfo } from '@/api/pages/Analysis.js';
import { getStocksDetail } from '@/api/pages/Analysis.js';
export default {
	data() {
		return {
			ana: [],
			option1: null,
			optionStock: null,
			option2: null,
			weightType: 'weight',
			loadingAna: true,
			loadingStock: true,
			loadingOption2: true,
			info: {},
			data: {},
			index_data: {}
		};
	},
	watch: {
		weightType() {
			this.loadingStock = true;
			this.drawStockTrendFeature(this.data);
		}
	},
	methods: {
		getData(info) {
			this.info = info;
			this.getIndexReturn();
		},
		// 获取父组件传递数据
		getVoluation(data) {
			this.drawProfitValuation(data);
		},
		async getIndexReturn() {
			let data = await getRateInfo({
				codes: ['000002.SH'],
				type: this.info.type,
				flag: [6],
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			if (data?.mtycode == 200) {
				this.index_data = data?.data;
			}
			this.getStocksDetail();
		},
		async getStocksDetail() {
			let data = await getStocksDetail({
				code: this.info.code,
				type: this.info.type,
				flag: this.info.flag + ',6',
				stock_code: this.info.stock_code,
				start_date: this.info.start_date,
				end_date: this.info.end_date
			});
			this.loadingAna = false;
			if (data?.mtycode == 200) {
				this.ana = [
					{
						...data?.data?.basic_info,
						cum_ret: data?.data?.basic_info?.stock_cum_return,
						cum_excess_ret: data?.data?.basic_info?.index_cum_return
					}
				];
				this.drawStockTrendFeature(data?.data);
				this.drawProfitValuation(data?.data);
			}
			// this.ana = data.index;
			// this.data = data;
			// this.drawStockTrendFeature(data);
		},
		// loading
		showLoading(info) {
			this.info = info;
			this.loadingAna = true;
			this.loadingOption2 = true;
			this.loadingStock = true;
		},
		getStockMsg(data) {
			this.loadingAna = false;
			this.ana = data.stock_day.index;
			this.data = data.stock_day;
			this.drawStockTrendFeature(data.stock_day);
			this.drawProfitValuation(data.voluation);
		},
		// 补全日期数组的函数
		fillDates(dates) {
			const filledDates = [];
			const startDate = new Date(dates[0]);
			const endDate = new Date(dates[dates.length - 1]);

			for (let d = startDate; d <= endDate; d.setDate(d.getDate() + 1)) {
				filledDates.push(d.toISOString().slice(0, 10));
			}
			console.log(filledDates);
			return filledDates;
		},
		// 绘制股价走势特征-需求更改后
		drawStockTrendFeature(resData) {
			console.log(resData);
			if (resData?.weight_info?.yearqtr?.length && resData?.rate_info?.date?.length) {
				this.loadingStock = false;
				let dateDayList = [], // 每日日期
					stockRetList = [], // 每日值-股价走势 ret
					dateQuarterList = [], // 季度日期
					stockWeightList = [], // 季度值-股票披露权重
					stockBenchList = [], // 每日值-A指股价走势 bench
					stock_date_q = resData.weight_info?.yearqtr;

				// 生成"连续日期"
				dateDayList = this.FUNC.generateDateList(
					this.FUNC.returnQuarter(resData?.rate_info.date?.[0], 'start'),
					this.FUNC.returnQuarter(resData?.rate_info.date[resData?.rate_info.date.length - 1], 'end')
				);
				// 根据连续日期生成"连续季度"
				dateQuarterList = this.FUNC.dateGenerateQuarterList2(dateDayList?.[0], dateDayList[dateDayList.length - 1]);
				// 根据季度日期生成"季度值"-股票披露权重
				let multiplier = 1;
				dateQuarterList.forEach((item) => {
					if (stock_date_q.includes(item)) {
						let index = stock_date_q.indexOf(item);
						let value = (resData[this.weightType]?.[index] * multiplier).toFixed(2);
						stockWeightList.push(value);
					} else {
						stockWeightList.push(null);
					}
				});

				let stock_data = [];
				stockRetList = [];
				stockBenchList = [];
				// 根据每日日期生成"每日值"
				let date = resData?.rate_info.date
					.slice()
					.map((v) => v.slice(0, 10))
					.map((v, i) => {
						stock_data.push([v, resData?.rate_info.rate[i]]);
					});
				dateDayList.map((item) => {
					let index = stock_data.findIndex((v) => v[0] == item);
					if (index == -1) {
						stockRetList.push([item, 0]);
					} else {
						stockRetList.push(stock_data[index]);
					}
					let index1 = this.index_data.findIndex((v) => v.date == item);
					if (index1 == -1) {
						stockBenchList.push([item, 0]);
					} else {
						stockBenchList.push([item, this.index_data[index1]?.rate]);
					}
				});

				/* 固定y轴刻度 */
				// 1.获取y轴数据中的最大最小值
				const miny0 = Math.min(...stockWeightList.filter((item) => !isNaN(parseFloat(item))));
				const maxy0 = Math.max(...stockWeightList.filter((item) => !isNaN(parseFloat(item))));
				const miny1 = Math.min(
					...stockBenchList.filter((item) => !isNaN(parseFloat(item))),
					...stockRetList.filter((item) => !isNaN(parseFloat(item[1])))
				);
				const maxy1 = Math.max(
					...stockBenchList.filter((item) => !isNaN(parseFloat(item))),
					...stockRetList.filter((item) => !isNaN(parseFloat(item[1])))
				);
				// 2.计算比例尺-ratio;
				// PS: (grid-最大最小值区间10%; +-grid是为了不让最大最小值顶格显示,y0保留2位小数是因为y0轴数值过小,会出现小于1的值)
				const ratio = (maxy0 - miny0) / (maxy1 - miny1);
				const y0Grid = (maxy0 - miny0) / 10;
				const y1Grid = (maxy1 - miny1) / 10;
				// 3.获取包含比例尺的最大最小值
				const y0Min = miny0 < miny1 * ratio ? miny0 : miny1 * ratio;
				const y0Max = maxy0 > maxy1 * ratio ? maxy0 : maxy1 * ratio;
				const y1Min = miny0 < miny1 * ratio ? miny0 / ratio : miny1;
				const y1Max = maxy0 > maxy1 * ratio ? maxy0 / ratio : maxy1;

				// 判断指数名称
				let indexName = '上证A指走势特征';

				this.optionStock = {
					color: ['#409eff', '#c23531', '#BDE1EC'],
					tooltip: {
						textStyle: {
							fontSize: '14px'
						},
						trigger: 'axis',
						formatter: (params) => {
							let str = '';

							if (params.length >= 2 && params[0].componentSubType == 'bar') {
								str = `日期: ${params[1].axisValue} 季度: ${params[0].axisValue} <br />`;
							} else if (params.length == 1 && params[0].componentSubType == 'bar') {
								str = `季度: ${params[0].axisValue} <br />`;
							} else if (params.length == 1 && params[0].componentSubType == 'line') {
								str = `日期: ${params[0].axisValue} <br />`;
							}
							// let str = `日期: ${params[1].axisValue} 季度: ${params[0].axisValue} <br />`;

							for (let i = params.length - 1; i >= 0; i--) {
								if (params[i].value != undefined && params[i].value != null && params[i].value != '') {
									let dotHtml =
										'<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' +
										params[i].color +
										'"></span>';
									str += dotHtml + `${params[i].seriesName}: ${params[i].value + '%'}<br />`;
								}
							}
							return str;
						}
					},
					toolbox: {
						feature: {
							saveAsImage: { pixelRatio: 3 }
						},
						top: -4,
						width: 104
					},
					legend: {
						textStyle: {
							fontSize: '14px'
						},
						data: [indexName, '股价走势特征', '股票披露权重']
					},
					xAxis: [
						{
							margin: 12,
							// 季度
							nameTextStyle: {
								fontSize: '14px',
								color: 'rgba(0,0,0,0.65)'
							},
							axisTick: {
								show: true,
								inside: true,
								alignWithLabel: true,
								lineStyle: {
									color: '#e9e9e9'
								}
							},
							axisLabel: {
								show: true,
								textStyle: {
									fontSize: '10px',
									color: 'rgba(0,0,0,0.65)'
								}
								// interval: 0,
								// rotate: 90
							},
							axisLine: {
								lineStyle: {
									color: '#e9e9e9'
								}
							},
							data: dateQuarterList,
							type: 'category'
						},
						{
							margin: 12,
							//每日
							nameTextStyle: {
								fontSize: '14px',
								color: 'rgba(0,0,0,0.65)'
							},
							axisTick: {
								show: false
							},
							axisLabel: {
								show: false
							},
							data: dateDayList,
							type: 'category'
						}
					],
					yAxis: [
						{
							margin: 16,
							// 该项在相应季度的数值
							nameTextStyle: {
								fontSize: '14px',
								color: 'rgba(0,0,0,0.65)'
							},
							axisLabel: {
								show: true,
								textStyle: {
									fontSize: '14px',
									color: 'rgba(0,0,0,0.65)'
								}
							},
							axisLine: {
								show: false
							},
							name: '股票披露权重(%)',
							type: 'value',
							// min: 0
							scale: true,
							splitLine: {
								show: true,
								lineStyle: {
									type: 'dashed',
									color: '#e9e9e9'
								}
							},
							min: (y0Min - y0Grid).toFixed(2),
							max: (y0Max + y0Grid).toFixed(2)
						},
						{
							//该项在相应每日的数值
							nameTextStyle: {
								fontSize: '14px',
								color: 'rgba(0,0,0,0.65)'
							},
							axisLabel: {
								show: true,
								textStyle: {
									fontSize: '14px',
									color: 'rgba(0,0,0,0.65)'
								}
							},
							name: '收益率(%)',
							type: 'value',
							scale: true,
							splitLine: {
								show: false
							}
						}
					],
					series: [
						{
							name: indexName,
							type: 'line',
							symbol: 'none',
							data: this.computedReturn(stockBenchList.map((v) => v[1])).map((v) => (v * 100).toFixed(2)),
							yAxisIndex: 1,
							xAxisIndex: 1
						},
						{
							name: '股价走势特征',
							type: 'line',
							symbol: 'none',
							data: this.computedReturn(stockRetList.map((v) => v[1])).map((v) => (v * 100).toFixed(2)),
							yAxisIndex: 1,
							xAxisIndex: 1
						},
						{
							name: '股票披露权重',
							type: 'bar',
							symbol: 'none',
							barWidth: '20%',
							data: stockWeightList,
							yAxisIndex: 0,
							xAxisIndex: 0
						}
					]
				};
				console.warn(this.optionStock);
			}
		},
		// 累计收益计算
		computedReturn(data) {
			let cum_return = 1;
			let cum = data.map((item) => {
				cum_return = cum_return * (1 + item * 1);
				return cum_return - 1;
			});
			return cum;
		},
		// 绘制盈利与估值
		drawProfitValuation(resData) {
			if (
				!this.FUNC.isValidObj(resData.finance_info?.yearqtr) ||
				(!this.FUNC.isValidObj(resData.pe) && !this.FUNC.isValidObj(resData.roe))
			) {
				this.loadingOption2 = true;
				return;
			}
			this.loadingOption2 = false;
			this.option2 = {
				color: ['#409eff', '#c23531'],
				tooltip: {
					textStyle: {
						fontSize: '14px'
					},
					trigger: 'axis'
				},
				toolbox: {
					feature: {
						saveAsImage: { pixelRatio: 3 }
					},
					top: -4,
					width: 104
				},
				xAxis: {
					margin: 12,
					//月份
					nameTextStyle: {
						fontSize: '14px',
						color: 'rgba(0,0,0,0.65)'
					},
					axisLabel: {
						show: true,
						textStyle: {
							fontSize: '14px',
							color: 'rgba(0,0,0,0.65)'
						}
					},
					axisLine: {
						lineStyle: {
							color: '#e9e9e9'
						}
					},
					axisTick: {
						lineStyle: {
							color: '#e9e9e9'
						}
					},
					data: resData.finance_info?.yearqtr,
					type: 'category'
				},
				yAxis: [
					{
						margin: 16,
						//该项在相应月份的数值
						axisLine: {
							lineStyle: {
								color: '#e9e9e9'
							}
						},
						nameTextStyle: {
							fontSize: '14px',
							color: 'rgba(0,0,0,0.65)'
						},
						axisLabel: {
							show: true,
							textStyle: {
								fontSize: '14px',
								color: 'rgba(0,0,0,0.65)'
							}
						},
						type: 'value',
						scale: true,
						splitLine: {
							show: false
						}
					},
					{
						margin: 16,
						//该项在相应月份的数值
						axisLine: {
							lineStyle: {
								color: '#e9e9e9'
							}
						},
						nameTextStyle: {
							fontSize: '14px',
							color: 'rgba(0,0,0,0.65)'
						},
						axisLabel: {
							show: true,
							textStyle: {
								fontSize: '14px',
								color: 'rgba(0,0,0,0.65)'
							}
						},
						type: 'value',
						scale: true,
						splitLine: {
							show: false
						}
					}
				],
				legend: {
					textStyle: {
						fontSize: '14px'
					}
				},
				series: [
					{
						name: 'pe',
						symbol: 'none',
						type: 'line',
						data: resData.pe
					},
					{
						type: 'line',
						name: 'roe',
						symbol: 'none',
						yAxisIndex: 1,
						data: resData.roe
					}
				]
			};
		}
	}
};
</script>

<style scoped lang="scss">
.high-frequency-stock-analysis {
	.profit-valuation-header,
	.stock-trend-header {
		margin: 10px 0;
		display: flex;
		justify-content: space-between;
		.header-title {
			font-size: 16px;
			font-weight: bold;
		}
	}
}
.high_frequency_stock_h400 {
	width: 100%;
	height: 400px;
}
</style>
