export default [
	{
		path: '/portfolioSelf',
		component: () => import(/* webpackChunkName: "portfolio" */ '../../pages/portfolio/index.vue'),
		meta: { title: '我的组合', tagShow: false }
	},
	{
		path: '/portfolioCompany',
		component: () => import(/* webpackChunkName: "portfolio" */ '../../pages/portfolio/indexCompany.vue'),
		meta: { title: '公司组合', tagShow: false }
	},
	{
		path: '/portfolioPublic',
		component: () => import(/* webpackChunkName: "portfolio" */ '../../pages/portfolio/indexPublic.vue'),
		meta: { title: '公开组合', tagShow: false }
	},
	{
		path: '/portfolioAnalysis/:id',
		component: () => import(/* webpackChunkName: "portfolio" */ '../../pages/portfolio/analysis.vue'),
		meta: { title: '组合详情', tagShow: true, keepAlive: true }
	}
	// {
	// 	path: '/assetAnalysis',
	// 	component: () => import(/* webpackChunkName: "dashboard" */ '../../pages/assetAllocation/assetAnalysis/index.vue'),
	// 	meta: { title: '资产分析', tagShow: true }
	// },
	// {
	// 	path: '/configurationPolicy',
	// 	component: () => import(/* webpackChunkName: "dashboard" */ '../../pages/assetAllocation/configurationPolicy/index.vue'),
	// 	meta: { title: '配置策略研究', tagShow: true }
	// },
	// {
	// 	path: '/portfolioStrategy',
	// 	component: () => import(/* webpackChunkName: "dashboard" */ '../../pages/assetAllocation/portfolioStrategy/index.vue'),
	// 	meta: { title: '组合策略研究', tagShow: true }
	// }
];
