<template>
	<div >
		<div v-for="item in template" :key="item.value" v-show="item.isshow" :class="item.type">
			<component :is="item.is" :ref="item.value" :indexInfo="indexInfo" v-loading="loading" style="padding-bottom: 20px;"></component>
		</div>
	</div>
</template>

<script>
import incomeContributionDegree from './components/incomeContributionDegree.vue';
import monthIncome from './components/monthIncome.vue';
import totalRiskReturn from './components/totalRiskReturn.vue';

export default {
	components: {
		incomeContributionDegree,
		monthIncome,
		totalRiskReturn,
		// contributionTrend,
		// allocationTrend,
		// monthlyIncome,
		// typePieChart
	},
	data() {
		return {
			info: {},
			distributionPostData: {},
			templateList: [],
			requestOver: 0,
			requestAll: 0,
			loading: false,
			correlationCoefficient: null,
			dynamicPullback: null,
			combinationHoldReturn: null,
			combinationHoldReturnData: {},
			combinationPartReturn: null,
			combinationPartReturnData: {},
			codes: [],
			start_date: '',
			end_date: '',
			fundOrManagerReturn: null,
			basicInfo: null,
			indexReturnInfo: null,
			activeIndex: 'fund',
			combinationFundWeight: null,
			fofAllocationDetails: null,
			returnInfoIndex: [],
			fofDrawdown: {}
		};
	},
	props: {
		indexInfo: {
			type: Object,
			default: {}
		},
		active: {
			type: String,
			default: ''
		},
		// 组合内当前持仓列表
		list: {
			type: Array,
			dafault: []
		},
		template:{
			type: Array,
			dafault: []
		}
	},

	methods: {
		// 获取打印数据
		createPrintWord() {
			let printData = [];
			this.templateList.map((item) => {
				if (item.isshow) {
					if (this.$refs[item.value]?.[0].createPrintWord) {
						printData.push(...this.$refs[item.value]?.[0].createPrintWord());
					}
				}
			});
			return printData;
		},

		// 触发各子组件获取数据
		getData() {
			this.template.forEach((item) => {
				this.$refs[item.value]?.[0].getData({
					combinationId:this.$route.query.id,
                	indexCode:this.indexInfo.id,
				});
			});
		},
	}
};
</script>

<style>
.big_template{
	margin-bottom: 20px;
}
</style>
