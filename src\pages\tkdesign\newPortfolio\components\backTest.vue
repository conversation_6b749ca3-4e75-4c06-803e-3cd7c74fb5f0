<template>
  <div v-loading="loadingall"
       class="back-test-content">
    <!-- 部分1：模型预览 -->
    <div class="box-container"
         style="margin-bottom: 16px">
      <el-menu :default-active="activeIndex"
               style="width: 100%; display: flex;overflow-x: auto;"
               class="el-menu-demo"
               mode="horizontal"
               @select="handleSelect">
        <el-menu-item v-for="(item,index) in quarterList"
                      :key="index"
                      :index="index">{{ item }}</el-menu-item>

      </el-menu>
      <div style="display: flex;margin-top:12px">
        <div style="flex:1">
          <el-table height="400px"
                    :data="tableData">
            <el-table-column prop="name"
                             align="gotoleft"
                             label="名称"
                             :show-overflow-tooltip="true"></el-table-column>
            <el-table-column prop="code"
                             align="gotoleft"
                             :show-overflow-tooltip="true"
                             label="代码"></el-table-column>
            <el-table-column prop="price"
                             align="gotoleft"
                             label="节点日期价格"></el-table-column>
            <el-table-column prop="position"
                             align="gotoleft"
                             label="原仓位(%)">
            </el-table-column>
            <el-table-column prop="weight"
                             align="gotoleft"
                             label="优化仓位(%)">
              <template slot-scope="scope">
                <el-input @change="changeStatus(scope.row)"
                          v-model="scope.row.weight"
                          type="number"></el-input>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div style="flex:1;margin-left:20px">
          <div>
            <!-- @mouseleave="filterData([{},{},tableDataOrigin])" -->
            <v-chart style="width: 100%; height: 400px"
                     ref="backTestLine"
                     @click="handleMouseOver"
                     autoresize
                     v-loading="loading"
                     element-loading-text="暂无数据"
                     element-loading-spinner="el-icon-document-delete"
                     element-loading-background="rgba(239, 239, 239, 0.5)"
                     :options="option" />
          </div>
        </div>
      </div>
    </div>
    <!-- 部分2：模型回测 -->
    <div v-loading="showTestLoading"
         class="box-container"
         style="margin-bottom: 16px">
      <!-- 条件部分 -->
      <div style="display:flex;align-items: center;padding-top:20px;padding-bottom: 20px;">
        <div><span>比较基准：</span>
          <el-select v-model="indexSelected"
                     :remote-method="getBasicIndex"
                     filterable
                     remote
                     prefix-icon="el-icon-search"
                     :loading="loading"
                     placeholder="输入简拼、代码、名称选择基金/基准"
                     @change="selectData">
            <el-option v-for="groups in havefundmanager"
                       :key="groups.code"
                       :value="groups.code"
                       :label="groups.name">
            </el-option>
          </el-select>
        </div>
        <div style="margin-left:24px;display: flex;align-items: center;">
          <div>回测区间：</div>
          <div> <el-date-picker v-model="date"
                            unlink-panels
                            value-format="yyyy-MM-dd"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期">
            </el-date-picker></div>
          <div style="margin-left:12px;color: #4096ff;cursor: pointer;"
               @click="changeTimeSelect(3)">近6月</div>
          <div style="margin-left:12px;color: #4096ff;cursor: pointer;"
               @click="changeTimeSelect(4)">近1年</div>
          <div style="margin-left:12px;color: #4096ff;cursor: pointer;"
               @click="changeTimeSelect(5)">近2年</div>
          <div style="margin-left:12px;color: #4096ff;cursor: pointer;"
               @click="changeTimeSelect(6)">近3年</div>
          <div style="margin-left:12px;color: #4096ff;cursor: pointer;"
               @click="changeTimeSelect(0)">成立以来</div>
          <div style="margin-left:12px"><el-button type="primary"
                       :disabled="canBackTest"
                       @click="backTest()">回测</el-button></div>

        </div>
      </div>
      <!-- 方案业绩表现 -->
      <div class="back-test-content-performance">
        <div class="back-test-content-performance-box">
          <div style="display:flex;justify-content: space-between;"
               class="
               back-test-content-performance-box-title">
            <div>方案业绩表现</div>
            <div><i @click="outExcel1"
                 style="cursor: pointer;"
                 class="el-icon-download
            "></i></div>
          </div>
          <div class="chart">
            <v-chart ref="barLineChartComponent"
                     autoresize
                     v-show="!showEmpty"
                     id="1"
                     element-loading-text="暂无数据"
                     element-loading-spinner="el-icon-document-delete"
                     element-loading-background="rgba(239, 239, 239, 0.5)"
                     style="height: 340px; width: 100% !important"
                     :options="optionsPerformance" />
            <el-empty v-show="showEmpty"
                      :image-size="200"></el-empty>
          </div>
          <div class="table">
            <div style="display: flex;">
              <el-table :data="tableDataPerformance.slice(0,5)"
                        border
                        stripe
                        v-loading="loadingPerformance">
                <el-table-column align="gotoleft"
                                 label="区间指数"
                                 prop="key" />
                <el-table-column align="right"
                                 label="产品">
                  <template slot-scope="scope">
                    <div>{{
                      scope.row.format ? ((scope.row.key === '夏普率' || scope.row.key === '信息率') && scope.row.allValue ? `${Number(scope.row.allValue).toFixed(2)}%` : handleData(scope.row.allValue)) : scope.row.allValue
                    }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="right"
                                 label="基准">
                  <template slot-scope="scope">
                    <div>{{
                      scope.row.format ? ((scope.row.key === '夏普率' || scope.row.key === '信息率') && scope.row.value ? `${Number(scope.row.value).toFixed(2)}%` : handleData(scope.row.value)) : scope.row.value
                    }}
                    </div>
                  </template>
                </el-table-column>
                <el-empty :image-size="180" />
              </el-table>
              <el-table :data="tableDataPerformance.slice(5,10)"
                        border
                        stripe
                        v-loading="loadingPerformance">
                <el-table-column align="gotoleft"
                                 label="区间指数"
                                 prop="key" />
                <el-table-column align="right"
                                 label="产品">
                  <template slot-scope="scope">
                    <div>{{
                      scope.row.format ? ((scope.row.key === '夏普率' || scope.row.key === '信息率') && scope.row.allValue ? `${Number(scope.row.allValue).toFixed(2)}%` : handleData(scope.row.allValue)) : scope.row.allValue
                    }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="right"
                                 label="基准">
                  <template slot-scope="scope">
                    <div>{{
                      scope.row.format ? ((scope.row.key === '夏普率' || scope.row.key === '信息率') && scope.row.value ? `${Number(scope.row.value).toFixed(2)}%` : handleData(scope.row.value)) : scope.row.value
                    }}
                    </div>
                  </template>
                </el-table-column>
                <el-empty :image-size="180" />
              </el-table>
            </div>
          </div>
        </div>
      </div>
      <!-- 资产配置权重 -->
      <div class="back-test-content-performance">
        <div class="back-test-content-performance-box">
          <div style="display:flex;justify-content: space-between;"
               class="back-test-content-performance-box-title">

            <div>资产配置权重</div>
            <div><i @click="outExcel2"
                 style="cursor: pointer;"
                 class="el-icon-download"></i></div>
          </div>
          <div>
            <div class="chart-card_body">
              <v-chart autoresize
                       ref="ChartComponent_ccfgXX"
                       v-if="!showStyleEmpty"
                       element-loading-text="暂无数据"
                       element-loading-spinner="el-icon-document-delete"
                       element-loading-background="rgba(239, 239, 239, 0.5)"
                       style="height: 484px; width: 100% !important"
                       :options="styleOptions" />
              <el-empty style="width: 100%"
                        v-else
                        :image-size="200"></el-empty>
            </div>
          </div>
        </div>
      </div>
      <!-- 方案表现 -->
      <div class="back-test-content-performance">
        <div class="back-test-content-performance-box">
          <div class="back-test-content-performance-box-title"
               style="display:flex;align-items: center;justify-content: space-between;">
            <div>方案表现</div>
            <div style="display:flex;align-items: center;">
              <div>
                <!-- <el-radio-group style="display:flex;align-items: center;"
                              class="radio-group-wrapper"
                              v-model="dateFlag"
                              size="small"
                              @input="intervalChange">
                <el-radio-button label="0">近期业绩</el-radio-button> -->
                <el-dropdown @command="handleCommand"
                             label="1">
                  <el-button type="primary">
                    {{selectStep}}<i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="近期业绩">近期业绩</el-dropdown-item>
                    <el-dropdown-item command="月度">月度</el-dropdown-item>
                    <el-dropdown-item command="季度">季度</el-dropdown-item>
                    <el-dropdown-item command="半年">半年</el-dropdown-item>
                    <el-dropdown-item command="年">年</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
                <!-- <el-select label="1"
                           v-model="selectStep"
                           placeholder="">
                  <el-option v-for="item,index in optionStepList"
                             :label="item.label"
                             :value="item.value"
                             :key="index"></el-option>
                </el-select> -->
                <!-- </el-radio-group> -->
              </div>
              <div style="margin-left: 16px;"><i @click="outExcel4"
                   style="cursor: pointer;"
                   class="el-icon-download"></i></div>
            </div>
          </div>
          <div>
            <el-table :data="dataPerformance">
              <el-table-column v-for="item,index in columnList"
                               :key="index"
                               align="gotoleft"
                               :prop="item.value"
                               :label="item.name"></el-table-column>
            </el-table>
          </div>
        </div>
      </div>
      <!-- 收益贡献分析 -->
      <div class="back-test-content-performance">
        <div class="back-test-content-performance-box">
          <div style="display:flex;align-items: center;justify-content: space-between;"
               class="back-test-content-performance-box-title">
            <div> 收益贡献分析
            </div>

            <div><i @click="outExcel3"
                 style="cursor: pointer;"
                 class="el-icon-download"></i></div>
          </div>
          <div>
            <div class="chart-card_body">
              <v-chart autoresize
                       ref="ChartComponent_ccfgX"
                       v-if="!showPerEmpty"
                       element-loading-text="暂无数据"
                       element-loading-spinner="el-icon-document-delete"
                       element-loading-background="rgba(239, 239, 239, 0.5)"
                       style="height: 484px; width: 100% !important"
                       :options="PerOptions" />
              <el-empty style="width: 100%"
                        v-else
                        :image-size="200"></el-empty>
            </div>
          </div>
        </div>
      </div>
      <div style='padding-top:16px;border-top: 1px solid #E9E9E9;display: flex;align-items: center;'>
        <div v-show='$route.query.active!=2'><el-button type=""
                     @click="()=>{$emit('backStep')}">上一步</el-button></div>
        <div v-if="isPortfolio"
             style="margin-left:12px"
             @click="()=>{
            showDialog = true
            }"><el-button type="primary">保存方案并创建组合</el-button></div>
        <div v-else
             style="margin-left:12px"
             @click="()=>{
            showDialog = true
            }"><el-button type="primary">保存方案</el-button></div>
      </div>
    </div>
    <el-dialog v-loading='loadingSave'
               class="FTBdialog"
               width="736px"
               height="444px"
               :visible.sync="showDialog">
      <div slot="title">
        <span style="
            font-family: 'PingFang';
            font-style: normal;
            font-weight: 500;
            font-size: 16px;
            line-height: 24px;
            color: rgba(0, 0, 0, 0.85);
            width: 100%;
          ">
          <div v-if="isPortfolio">保存方案并创建组合</div>
          <div v-else>保存方案</div>
        </span>
      </div>
      <div style="
          width: 100%;
          height: 1px;
          background: rgba(0, 0, 0, 0.06);
          margin-bottom: 16px;
        "></div>
      <div style="display: flex; margin-bottom: 16px">
        <div style="flex: 1">
          方案名称：
          <el-input v-model="name"
                    style="width: 216px"
                    placeholder="请输入"></el-input>
        </div>
        <div style="flex: 1">
          <span style="margin-left: 50px">创建人：</span>
          <el-input disabled
                    v-model="creater"
                    style="width: 216px"
                    placeholder="请输入"></el-input>
        </div>
      </div>
      <div style="display: flex; margin-bottom: 16px">
        <div style="flex: 1">
          创建日期：
          <el-input disabled
                    v-model="createdate"
                    style="width: 216px"
                    placeholder="请输入"></el-input>
        </div>
        <div style="flex: 1">
          <span style="margin-left: 36px">成立日期：</span>
          <el-date-picker v-model="founddate"
                          type="date"
                          value-format="yyyy-MM-dd"
                          style="width: 216px"
                          placeholder="请选择"></el-date-picker>
        </div>
      </div>
      <div style="display: flex; margin-bottom: 16px">
        <div style="flex: 1">
          结束日期：
          <el-date-picker v-model="enddate"
                          type="date"
                          value-format="yyyy-MM-dd"
                          style="width: 216px"
                          placeholder="请选择"></el-date-picker>
        </div>
        <div style="flex: 1">
          <div style="margin-top:6px"
               v-show="isPortfolio">
            <span style="margin-left: 36px">分红处理：</span>
            <el-radio-group v-model="moneyDo">
              <el-radio :label="true">提取现金</el-radio>
              <el-radio :label="false">分红再投资</el-radio>
            </el-radio-group>
          </div>
        </div>
      </div>
      <div style="display: flex; margin-bottom: 24px">
        <div style="flex: 1">
          方案分类：
          <el-radio-group v-model="categray">
            <el-radio :label="1">私有</el-radio>
            <el-radio :label="2">公共</el-radio>
            <el-radio v-if="
                $store.state.userType == 'superuser' ||
                $store.state.userType == 'staff'
              "
                      :label="0">全部</el-radio>
          </el-radio-group>
        </div>
        <div v-show="false"
             style="flex: 1">
          <span style="margin-left: 36px">是否置顶：</span>
          <el-radio-group v-model="istop">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </div>
      </div>
      <div style="margin-bottom: 16px">
        <div style="margin-bottom: 8px">方案说明:</div>
        <el-input v-model="description"
                  :autosize="{ minRows: 4, maxRows: 8 }"
                  placeholder="请输入方案描述"
                  type="textarea"></el-input>
      </div>
      <div style="text-align: right">
        <el-button type
                   @click="showDialog = false">取消</el-button>
        <el-button type="primary"
                   @click="addCom()">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { filter_json_to_excel_inside, changColumnToRow, filter_json_to_excel_inside_multiHeader } from '@/utils/exportExcel.js';
import { lineChartOption } from '@/utils/chartStyle.js';
import {
  modelCalculate, holdingRateTrend, overallRiskReturn, assetAllocationWeight, planIndexPerformance, returnContributeAnalysis, retestDetails, CreatePloyOrCombination, calPortfolioBack
} from '@/api/pages/tkAnalysis/backTest.js';
import { Search } from '@/api/pages/Analysis.js';
import {
  getCustomMeasure,
} from "@/api/pages/tools/pool.js";
export default {
  props: {
    parameterJson: {},
    listItemInfo: {}
  },
  components: {

  },
  data () {
    return {
      showTestLoading: false,
      // 保存方案
      showDialog: false,
      name: "",
      creater: "",
      createdate: "",
      founddate: "",
      enddate: "",
      moneyDo: false,
      categray: "",
      istop: false,
      description: "",
      // 解
      date: '',
      quarterList: [],
      activeIndex: '',
      tableData: [],
      tableDataOrigin: [],
      filterChooses: {},
      result: [],
      resultOrigin: [],
      option: {},
      indexSelected: '000300.SH',
      loading: false,
      havefundmanager: [
        { name: '沪深300', code: '000300.SH' },
        { name: '深证100', code: '399330.SZ' },
        { name: '中证1000', code: '000852.SH' },
        { name: '创业板指', code: '399006.SZ' },
        { name: '恒生综合', code: 'HSCI.HI' },
        { name: '上证180', code: '000010.SH' },
        { name: '上证A指', code: '000002.SH' },
        { name: '股债50:50', code: 'B50E50' },
        { name: '股债60:40', code: 'B40E60' },
        { name: '股债70:30', code: 'B30E70' }
      ],
      // 回测
      loadingSave: false,
      showEmpty: false,
      PerOptions: {},
      optionsPerformance: {},
      tableDataPerformance: [],
      loadingPerformance: false,
      isPortfolio: false,
      combinationId: '',
      styleOptions: '',
      showStyleEmpty: false,
      canBackTest: false,
      showPerEmpty: false,
      dateFlag: '0',
      selectStep: '近期业绩',
      dataPerformance: [],
      columnList: [{
        value: 'name',
        name: '指标名称'
      }, {
        value: 'lastWeek',
        name: '近一周'
      }, {
        value: 'lastMounth',
        name: '近一月'
      }, {
        value: 'lastSeason',
        name: '近一季'
      }, {
        value: 'lastHalfYears',
        name: '近半年'
      }, {
        value: 'lastYear',
        name: '近一年'
      }, {
        value: 'lastThreeYear',
        name: '近三年'
      }, {
        value: 'lastFiveYear',
        name: '近五年'
      }, {
        value: 'customTime',
        name: '区间内'
      }, {
        value: 'measure',
        name: '指标名称'
      }],
      optionStepList: [{ value: 'monthly', label: '月度' }, { value: "quarterly", label: '季度' }, { value: 'halfyearly', label: '半年' }, { value: 'yearly', label: '年' }],
      loadingall: false,
    };
  },
  computed: {

  },
  mounted () {
    // console.log(this.parameterJson, this.listItemInfo);
    this.filterChooses = this.parameterJson//this.$route.params?.filterChooses;//上一页李琦传来的模型参数
    if (this.listItemInfo?.id) {
      this.filterFlag = this.listItemInfo?.id//this.$route.params?.id;//是否已有方案回测,此字段代表方案id
    }
    else {
      if (this.$route.query?.listItemInfo) this.filterFlag = JSON.parse(this.$route.query?.listItemInfo)?.id;//是否已有方案回测,此字段代表方案id
      else this.filterFlag = undefined
    }
    // console.log(this.filterFlag);
    this.changeTimeSelect(4)
    this.isPortfolio = this.$route.path.indexOf('configurationStrategySteps') >= 0 ? false : true
    this.createdate = this.moment(new Date()).format("YYYY-MM-DD");
    this.creater = this.$store.state.username;
    this.categray = 1
    // console.log(this.$route.query?.active, this.$route.query?.active == 2);
    if (this.filterFlag && this.$route.query?.active == 2) {
      this.getResultHave()
      this.setPortName()
    }
    else {
      this.getResult()
      this.clearPortName()

    }
    // tempuse 
    // this.combinationId = '266'
    // this.showTestLoading = false
    // 获取回测数据
    // this.getHoldingRateTrend()//曲线数据
    // this.getOverallRiskReturn()//曲线下表格数据
    // this.getAllocation()//资产配置权重
    // // this.getPerFormanceCombination()//方案表现
    // this.returnAnalysis()//收益贡献分析
    // this.handleCommand('近期业绩')//方案表现入口默认近期
  },
  methods: {
    // 方案业绩表现
    outExcel1 () {
      const title = [
        { label: '日期', value: 'date', format: '' },
        { label: '分析对象', value: 'value1', format: '' },
        { label: '基准收益', value: 'value2', format: '' },
        { label: '超额收益', value: 'value3', format: '' },
        { label: '回撤', value: 'value4', format: '' },
      ];
      let datas = []
      // console.log(this.optionsPerformance.xAxis[0].data);
      for (let i = 0; i < this.optionsPerformance.xAxis[0].data.length; i++) {
        datas.push([])
        datas[i]['date'] = this.optionsPerformance.xAxis[0].data[i]?.value ? this.optionsPerformance.xAxis[0].data[i]?.value : this.optionsPerformance.xAxis[0].data[i]
        for (let j = 0; j < this.optionsPerformance.series.length; j++) {
          if (this.optionsPerformance.series[j].name == '分析对象') {
            datas[i]['value1'] = this.optionsPerformance.series[j].data[i]
          }
          if (this.optionsPerformance.series[j].name == '基准收益') {
            datas[i]['value2'] = this.optionsPerformance.series[j].data[i]
          }
          if (this.optionsPerformance.series[j].name == '超额收益') {
            datas[i]['value3'] = this.optionsPerformance.series[j].data[i]
          }
          if (this.optionsPerformance.series[j].name == '回撤') {
            datas[i]['value4'] = this.optionsPerformance.series[j].data[i]
          }
        }
      }
      filter_json_to_excel_inside(title, datas, [], '方案业绩表现折线数据');

      const title2 = [
        { label: '指标', value: 'key', format: '' },
        { label: '分析对象', value: 'allValue', format: '' },
        { label: '基准', value: 'value', format: '' },
      ];
      filter_json_to_excel_inside(title2, this.tableDataPerformance, [], '方案业绩表现表格数据');
    },
    outExcel2 () {
      console.log(this.styleOptions);
      const title = [
        { label: '日期', value: 'date', format: '' },

      ];
      let datas = []
      console.log(this.styleOptions.xAxis[0].data);
      for (let i = 0; i < this.styleOptions.xAxis[0].data.length; i++) {
        datas.push([])
        datas[i]['date'] = this.styleOptions.xAxis[0].data[i]?.value ? this.styleOptions.xAxis[0].data[i]?.value : this.styleOptions.xAxis[0].data[i]
        for (let j = 0; j < this.styleOptions.series.length; j++) {
          if (title.findIndex(item => item.label == this.styleOptions.series[j].name) < 0) {
            title.push({ label: this.styleOptions.series[j].name, value: 'value' + j, format: '' })
          }
          datas[i]['value' + j] = this.styleOptions.series[j].data[i]?.[1] || this.styleOptions.series[j].data[i] || ''
          // if (this.styleOptions.series[j].name == '分析对象') {
          //   datas[i]['value1'] = this.styleOptions.series[j].data[i]
          // }
          // if (this.styleOptions.series[j].name == '基准收益') {
          //   datas[i]['value2'] = this.styleOptions.series[j].data[i]
          // }
          // if (this.styleOptions.series[j].name == '超额收益') {
          //   datas[i]['value3'] = this.styleOptions.series[j].data[i]
          // }
          // if (this.styleOptions.series[j].name == '回撤') {
          //   datas[i]['value4'] = this.styleOptions.series[j].data[i]
          // }
        }
      }
      filter_json_to_excel_inside(title, datas, [], '资产配置权重');
    },
    outExcel3 () {
      // console.log(this.styleOptions);
      const title = [
        { label: '日期', value: 'date', format: '' },

      ];
      let datas = []
      console.log(this.PerOptions.xAxis[0].data);
      for (let i = 0; i < this.PerOptions.xAxis[0].data.length; i++) {
        datas.push([])
        datas[i]['date'] = this.PerOptions.xAxis[0].data[i]?.value ? this.PerOptions.xAxis[0].data[i]?.value : this.PerOptions.xAxis[0].data[i]
        for (let j = 0; j < this.PerOptions.series.length; j++) {
          if (title.findIndex(item => item.label == this.PerOptions.series[j].name) < 0) {
            title.push({ label: this.PerOptions.series[j].name, value: 'value' + j, format: '' })
          }
          datas[i]['value' + j] = this.PerOptions.series[j].data[i] || ''
          // if (this.styleOptions.series[j].name == '分析对象') {
          //   datas[i]['value1'] = this.styleOptions.series[j].data[i]
          // }
          // if (this.styleOptions.series[j].name == '基准收益') {
          //   datas[i]['value2'] = this.styleOptions.series[j].data[i]
          // }
          // if (this.styleOptions.series[j].name == '超额收益') {
          //   datas[i]['value3'] = this.styleOptions.series[j].data[i]
          // }
          // if (this.styleOptions.series[j].name == '回撤') {
          //   datas[i]['value4'] = this.styleOptions.series[j].data[i]
          // }
        }
      }
      filter_json_to_excel_inside(title, datas, [], '收益贡献分析');
    },
    outExcel4 () {
      filter_json_to_excel_inside(this.columnList.map(item => { return { ...item, label: item.name, value: item.value, format: '' } }), this.dataPerformance, [], '方案表现');
    },
    handleMouseOver (params) {
      // console.log(params);
      if (params.seriesType === 'scatter') {
        console.log('Hovered over point: ', params.data);
        this.filterData(params?.data || [])

        // this.customMethod(params.data);
      }
    },
    setPortName () {
      let data = {}
      if (JSON.stringify(this.listItemInfo) == '{}') data = JSON.parse(this.$route.query?.listItemInfo)
      else data = this.listItemInfo
      this.name = data.name
      this.createdate = data.createTime
      this.categray = data.ispublic == '全部' ? 0 : data.ispublic == '公共' ? 2 : 1
    },
    clearPortName () {
      this.name = ''
    },
    // 保存方案/组合,成功跳转
    async addCom () {
      this.loadingSave = true
      let { data, mtycode, message } = await CreatePloyOrCombination({
        type: this.isPortfolio ? 'combinPloy' : 'ploy',
        name: this.name,
        createDate: this.createdate,
        createBy: this.creater,
        establishDate: this.founddate,
        endDate: this.enddate,
        ispublic: this.categray,
        indexCode: this.indexSelected,
        description: this.description,
        flag: !this.moneyDo,
        parameterJson: this.filterChooses,
        planList: this.result,
        id: this.filterFlag
      })
      this.loadingSave = false
      if (mtycode == '200') {
        this.showDialog = false
        this.$message.success('保存成功')
        if (this.isPortfolio) {
          this.$router.push('/portfolioList')
        }
        else {
          this.$router.push('/configurationStrategyIndex')
        }
      }
      else {
        this.$message.error('保存失败')
      }
    },
    // 手动调整权重权重大于100不让回测
    changeStatus (data) {
      this.canBackTest = false
      try {
        this.result[this.activeIndex].dotList[this.result[this.activeIndex].dotList.findIndex(item => item.flag == '1')].codeList = this.result[this.activeIndex]?.dotList[this.result[this.activeIndex].dotList.findIndex(item => item.flag == '1')].codeList.map(items => {
          return {
            ...items,
            weight: data.code == items.code ? data.weight / 100 : items.weight
          }

        })
      }
      catch (e) {
        console.log(this.result[this.activeIndex]?.dotList[this.result[this.activeIndex].dotList.findIndex(item => item.flag == '1')].codeList);
        console.log(this.result[this.activeIndex]?.dotList);
      }

      return
      let sun = 0
      for (let i = 0; i < this.tableData.length; i++) {
        sun = sun + this.tableData[i].weight
      }
      if (sun > 98 && sun < 101) this.canBackTest = false
      else this.canBackTest = true

    },
    // 时间切换
    changeTimeSelect (value) {
      let end_date = ''
      let start_date = ''
      end_date = this.moment(this.moment()).format('YYYY-MM-DD');
      switch (value) {
        case 0:
          start_date = this.quarterList?.[0] || this.moment(this.moment()).subtract(3, 'years').format('YYYY-MM-DD');
          break;
        // 近一月
        case 1:
          start_date = this.moment(this.moment()).subtract(1, 'months').format('YYYY-MM-DD');
          break;
        // 近三月
        case 2:
          start_date = this.moment(this.moment()).subtract(3, 'months').format('YYYY-MM-DD');
          break;
        // 近半年
        case 3:
          start_date = this.moment(this.moment()).subtract(6, 'months').format('YYYY-MM-DD');
          break;
        // 近一年
        case 4:
          start_date = this.moment(this.moment()).subtract(1, 'years').format('YYYY-MM-DD');
          break;
        // 近2年
        case 5:
          start_date = this.moment(this.moment()).subtract(2, 'years').format('YYYY-MM-DD');
          break;
        // 近3年
        case 6:
          start_date = this.moment(this.moment()).subtract(3, 'years').format('YYYY-MM-DD');
          break;
        // 今年以来
        case 7:
          start_date = this.moment(this.moment().year() + '-01-01').format('YYYY-MM-DD');
          break;
        // 成立以来
        case 8:
          start_date = '';
          end_date = '';
          break;
      }
      this.date = [start_date, end_date]
      this.founddate = start_date
    },
    // 获取基准列表
    async getBasicIndex (query) {
      this.loading = false;
      let data = await Search({ message: query, flag: '6' });
      if (data) {
        // let temparr = [
        //   {
        //     label: '基金基准',
        //     options: []
        //   }
        // ];
        // for (let i = 0; i < data.length; i++) {
        //   if (data[i].flag == 'index') {
        //     temparr[0].options.push(data[i]);
        //   }

        // this.listfront = data;
        this.havefundmanager = data?.data || [];
      }

    },
    // 选择基准
    selectData (e) {
      // console.log(e);
      // this.indexSelected = e
    },
    //获取数据
    async getResult () {
      this.loadingall = true
      // this.quarterList = ['2023-03-31', '2023-06-30', '2023-09-30', '2023-12-31']
      this.activeIndex = 0
      let { data, mtycode, message } = await modelCalculate({ parameterJson: this.filterChooses, marketType: this.isPortfolio ? 'fund' : 'index' })
      this.loadingall = false
      if (mtycode === '200') {
        this.quarterList = []
        this.activeIndex = 0
        for (let i = 0; i < data.length; i++) {
          this.quarterList.push(data[i].date)
        }
        this.result = data
        this.resultOrigin = JSON.parse(JSON.stringify(data))
        // 进入第一个时间的最优解
        this.filterData()
        this.backTest()
      }
      else {
        this.$message.error('模型无解')
      }
    },
    //已有方案获取数据
    async getResultHave () {
      // this.quarterList = ['2023-03-31', '2023-06-30', '2023-09-30', '2023-12-31']
      this.loadingall = true
      this.activeIndex = 0
      let { data, mtycode, message } = await retestDetails({ ployId: this.filterFlag, marketType: this.isPortfolio ? 'fund' : 'index' })
      this.loadingall = false
      if (mtycode === '200') {
        this.quarterList = []
        this.activeIndex = 0
        for (let i = 0; i < data.length; i++) {
          this.quarterList.push(data[i].date)
        }
        this.result = data
        this.resultOrigin = JSON.parse(JSON.stringify(data))
        // 进入第一个时间的最优解
        this.filterData()
        this.backTest()
      }
    },
    //切换日期
    handleSelect (e) {
      this.activeIndex = e
      // 进入切换日期的最优解
      this.filterData()
    },
    //挂载数据
    filterData (params) {
      // console.log(params, this.tableData);
      if (params) {
        // console.log(params, this.resultOrigin);
        let indexS = -1
        // this.result?.[this.activeIndex]?.dotList?.findIndex(item => { item?.aveRisk == params?.[0] && item?.expectReturn == params?.[1] }) || -1
        for (let i = 0; i < this.result?.[this.activeIndex]?.dotList.length; i++) {
          // console.log(this.result?.[this.activeIndex]?.dotList[i].aveRisk, params[0], this.result?.[this.activeIndex]?.dotList[i].aveRisk == params[0]);
          if (this.result?.[this.activeIndex]?.dotList[i].aveRisk == params[0] && this.result?.[this.activeIndex]?.dotList[i].expectReturn == params[1]) {
            indexS = i
            break
          }
        }
        // console.log(indexS, params, this.result);
        this.tableData = this.result?.[this.activeIndex]?.dotList?.[indexS]?.codeList || [] //params?.[2] || [] //点数据里的codelist
        this.tableData = this.tableData.map(item => {
          return {
            ...item,
            weight: (item.weight * 100)?.toFixed(2) || 0,
            position: (item.position * 100)?.toFixed(2) || 0,
            price: item.price == '--' ? '--' : (item.price * 1)?.toFixed(2) || 0,
          }
        })
        for (let j = 0; j < this.result?.[this.activeIndex].dotList.length; j++) {
          if (this.result?.[this.activeIndex]?.dotList?.[j]) {
            this.result[this.activeIndex].dotList[j] = {
              aveRisk: this.result[this.activeIndex].dotList[j].aveRisk,
              codeList: this.result[this.activeIndex].dotList[j].codeList,
              expectReturn: this.result[this.activeIndex].dotList[j].expectReturn,
              flag: j == indexS ? '1' : null
            }
          }

          // if(index == indexS)
        }
        // this.result?.[this.activeIndex].dotList.map((item, index) => {
        //   console.log(item, index, indexS, index == indexS, '?');

        //   return {
        //     aveRisk: item.aveRisk,
        //     codeList: item.codeList,
        //     expectReturn: item.expectReturn,
        //     flag: index == indexS ? '1' : null
        //   }


        // })
        // console.log(this.result?.[this.activeIndex].dotList)
        // if (indexS != -1) {
        //   this.resultOrigin[this.activeIndex].dotList[indexS].codeList = this.tableData
        // }
        // this.tableDataOrigin = JSON.parse(JSON.stringify(this.tableData))
        this.option = {
          grid: {
            top: '50px',
            // containLabel: true
            left: '64px',
            right: '72px',
            bottom: '50px'
          },
          backgroundColor: '#ecf5ff',
          color: ['#4096ff', '#4096ff', '#7388A9', '#E85D2D', '#9A89FF',
            '#6C96F2', '#FD6865', 'rgba(253, 156, 255, 1)', '#83D6AE', 'rgba(174, 201, 254, 1)',
            '#88C9E9', 'rgba(169, 244, 208, 1)', '#6F80DD', 'rgba(154, 137, 255, 1)', '#FD9CFF',
            'rgba(219, 174, 255, 1)', '#FED0EE', 'rgba(159, 212, 253, 1)', '#ED589D',
            '#FEAEAE', 'rgba(208, 232, 255, 1)', '#FDD09F', 'rgba(251, 227, 142, 1)',
            '#FBE38E', '#A9F4D0', 'rgba(253, 208, 159, 1)', '#D0E8FF', '#9FD4FD',
            'rgba(254, 174, 174, 1)', '#AEC9FE', '#DBAEFF', 'rgba(254, 208, 238, 1)', '#FA541C'],
          xAxis: {
            splitLine: {
              show: false,
              lineStyle: {
                type: 'dashed'
              }
            },
            name: '年化风险',
            type: 'value',
            // interval: 10,
            boundaryGap: false,
            // data: this.result?.[this.activeIndex]?.aveRiskList.concat(this.result?.[this.activeIndex]?.efficientFrontierRisks || []) || [],
            axisLabel: {
              formatter: function (value, index) {  //Y轴的自定义刻度值，对应上图
                return `${(value * 100)?.toFixed(0)}%`;
              },
            }
          },
          yAxis: {
            name: '收益',
            axisLine: {
              show: false,
              lineStyle: {
                // color: '#e9e9e9'
              }
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed'
              }
            },
            type: 'value',
            axisLabel: {
              formatter: function (value, index) {  //Y轴的自定义刻度值，对应上图
                return `${(value * 100)?.toFixed(0)}%`;
              }
            },
          },
          legend: {
            data: ['有效前沿']
          },
          tooltip: {
            padding: 16,
            backgroundColor: '#ffffff',
            trigger: 'axis',
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: 'line' // 默认为直线，可选为：'line' | 'shadow'
            },
            textStyle: { color: '#000000' },
            formatter: (paramsX) => {
              // console.log(paramsX);
              if (paramsX?.length > 0) {
                let str = ``;
                for (let i = 0; i < paramsX.length; i++) {
                  if (paramsX[i].seriesType == 'scatter') {
                    str = `年化风险: ${(paramsX?.[i]?.axisValue * 100)?.toFixed(2)}% <br /> 预期收益： ${(paramsX[i]?.data?.[1] * 100)?.toFixed(2)}%`
                    // that.filterData(params[i].value)
                  }
                  else {
                    if (paramsX[i]?.data?.length == 2) str = `年化风险: ${(paramsX?.[i]?.axisValue * 100)?.toFixed(2)}% <br /> 预期收益： ${(paramsX[i]?.data?.[1] * 100)?.toFixed(2)}%`
                    else str = `年化风险: ${(paramsX?.[i]?.axisValue * 100)?.toFixed(2)}% <br /> 预期收益： ${(paramsX[i]?.data * 100)?.toFixed(2)}%`
                  }
                }
                return str;
              }
              else {
                return '--'
              }

            }
          },
          series: [
            {
              name: '有效前沿',
              data: this.result?.[this.activeIndex]?.expectReturnList.map((item, index) => {
                return [this.result?.[this.activeIndex]?.aveRiskList[index], item]

              }) || [],
              type: 'line',
              symbol: 'none',
              smooth: true,
              itemStyle: {
                color: '#ffdaab'  // 设置散点颜色为绿色
              },
            },
            {
              itemStyle: {
                color: '#4096ff'  // 设置散点颜色为绿色
              },
              type: 'scatter',
              data: this.result?.[this.activeIndex]?.dotList?.filter(item => item.flag === '1')?.map(item => { return [item?.aveRisk, item?.expectReturn, item?.codeList] }) || []
            },
            {
              itemStyle: {
                color: '#bdc1cb'  // 设置散点颜色为绿色
              },
              type: 'scatter',
              data: this.result?.[this.activeIndex]?.dotList?.filter(item => item.flag != '1')?.map(item => { return [item?.aveRisk, item?.expectReturn, item?.codeList] }) || []
            },
            // {
            //   name: '有效前沿',
            //   data: this.result?.[this.activeIndex]?.efficientFrontierReturns?.map((item, index) => {
            //     return [this.result?.[this.activeIndex]?.efficientFrontierRisks[index], item]

            //   }) || [],
            //   type: 'line',
            //   symbol: 'none',
            //   smooth: true,
            //   itemStyle: {
            //     color: '#a3c6fb'  // 设置散点颜色为绿色
            //   },
            // }
          ]
        }
        // console.log(this.option);
      }
      else {
        // console.log(this.result?.[this.activeIndex]?.dotList?.filter(item => item.flag === '1'));
        let that = this
        this.combinationId = this.result?.[this.activeIndex]?.combinationId || ''
        // 最优解
        this.tableData = this.result?.[this.activeIndex]?.dotList?.filter(item => item.flag === '1')?.[0]?.codeList || []
        this.tableData = this.tableData.map(item => {
          return {
            ...item,
            weight: (item.weight * 100)?.toFixed(2) || 0,
            position: (item.position * 100)?.toFixed(2) || 0,
            price: item.price == '--' ? 0 : (item.price * 1)?.toFixed(2) || 0,
          }
        })
        // console.log(this.result?.[this.activeIndex]?.efficientFrontierReturns?.map((item, index) => {
        //   return [this.result?.[this.activeIndex]?.efficientFrontierRisks[index], item]

        // }) || []);
        this.tableDataOrigin = JSON.parse(JSON.stringify(this.result?.[this.activeIndex]?.dotList?.filter(item => item.flag === '1')?.[0]?.codeList || []))
        // 曲线及点
        this.option = {
          grid: {
            top: '50px',
            // containLabel: true
            left: '64px',
            right: '72px',
            bottom: '50px'
          },
          backgroundColor: '#ecf5ff',
          color: ['#4096ff', '#4096ff', '#7388A9', '#E85D2D', '#9A89FF',
            '#6C96F2', '#FD6865', 'rgba(253, 156, 255, 1)', '#83D6AE', 'rgba(174, 201, 254, 1)',
            '#88C9E9', 'rgba(169, 244, 208, 1)', '#6F80DD', 'rgba(154, 137, 255, 1)', '#FD9CFF',
            'rgba(219, 174, 255, 1)', '#FED0EE', 'rgba(159, 212, 253, 1)', '#ED589D',
            '#FEAEAE', 'rgba(208, 232, 255, 1)', '#FDD09F', 'rgba(251, 227, 142, 1)',
            '#FBE38E', '#A9F4D0', 'rgba(253, 208, 159, 1)', '#D0E8FF', '#9FD4FD',
            'rgba(254, 174, 174, 1)', '#AEC9FE', '#DBAEFF', 'rgba(254, 208, 238, 1)', '#FA541C'],
          xAxis: {
            splitLine: {
              show: false,
              lineStyle: {
                type: 'dashed'
              }
            },
            name: '年化风险',
            type: 'value',
            // interval: 10,
            boundaryGap: false,
            // data: this.result?.[this.activeIndex]?.aveRiskList.concat(this.result?.[this.activeIndex]?.efficientFrontierRisks || []) || [],
            axisLabel: {
              formatter: function (value, index) {  //Y轴的自定义刻度值，对应上图
                return `${(value * 100)?.toFixed(0)}%`;
              },
            }
          },
          yAxis: {
            name: '收益',
            axisLine: {
              show: false,
              lineStyle: {
                // color: '#e9e9e9'
              }
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed'
              }
            },
            type: 'value',
            axisLabel: {
              formatter: function (value, index) {  //Y轴的自定义刻度值，对应上图
                return `${(value * 100)?.toFixed(0)}%`;
              }
            },
          },
          legend: {
            data: ['有效前沿']
          },
          tooltip: {
            padding: 16,
            backgroundColor: '#ffffff',
            trigger: 'axis',
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: 'line' // 默认为直线，可选为：'line' | 'shadow'
            },
            textStyle: { color: '#000000' },
            formatter: (params) => {
              // console.log(params);
              if (params?.length > 0) {
                let str = ``;
                for (let i = 0; i < params.length; i++) {
                  if (params[i].seriesType == 'scatter') {
                    str = `年化风险: ${(params?.[i]?.axisValue * 100)?.toFixed(2)}% <br /> 预期收益： ${(params[i]?.data?.[1] * 100)?.toFixed(2)}%`
                    // that.filterData(params[i].value)
                  }
                  else {
                    if (params[i]?.data?.length == 2) str = `年化风险: ${(params?.[i]?.axisValue * 100)?.toFixed(2)}% <br /> 预期收益： ${(params[i]?.data?.[1] * 100)?.toFixed(2)}%`
                    else str = `年化风险: ${(params?.[i]?.axisValue * 100)?.toFixed(2)}% <br /> 预期收益： ${(params[i]?.data * 100)?.toFixed(2)}%`
                  }
                }
                return str;
              }
              else {
                return '--'
              }

            }
          },
          series: [
            {
              name: '有效前沿',
              data: this.result?.[this.activeIndex]?.expectReturnList.map((item, index) => {
                return [this.result?.[this.activeIndex]?.aveRiskList[index], item]

              }) || [],
              type: 'line',
              symbol: 'none',
              smooth: true,
              itemStyle: {
                color: '#ffdaab'  // 设置散点颜色为绿色
              },
            },
            {
              itemStyle: {
                color: '#4096ff'  // 设置散点颜色为绿色
              },
              type: 'scatter',
              data: this.result?.[this.activeIndex]?.dotList?.filter(item => item.flag === '1')?.map(item => { return [item?.aveRisk, item?.expectReturn, item?.codeList] }) || []
            },
            {
              itemStyle: {
                color: '#bdc1cb'  // 设置散点颜色为绿色
              },
              type: 'scatter',
              data: this.result?.[this.activeIndex]?.dotList?.filter(item => item.flag != '1')?.map(item => { return [item?.aveRisk, item?.expectReturn, item?.codeList] }) || []
            },
            // {
            //   name: '有效前沿',
            //   data: this.result?.[this.activeIndex]?.efficientFrontierReturns?.map((item, index) => {
            //     return [this.result?.[this.activeIndex]?.efficientFrontierRisks[index], item]

            //   }) || [],
            //   type: 'line',
            //   symbol: 'none',
            //   smooth: true,
            //   itemStyle: {
            //     color: '#a3c6fb'  // 设置散点颜色为绿色
            //   },
            // }
          ]
        }
      }
    },
    //------------------------------------------------------------------------------
    //回测 
    async backTest () {
      // console.log(this.quarterList, this.date);
      if (this.quarterList?.[0] > this.date?.[0]) {
        this.$message.warning('警告，回测日期大于构造日期')
      }
      this.showTestLoading = true
      // console.log(this.result);
      let params = this.result.map(item => {
        return {
          date: item.date,
          codeWeight: item.dotList?.filter(item => item.flag == '1')?.[0]?.codeList || []
        }
      })
      let { data, mtycode, mtymessage } = await calPortfolioBack({ arrays: [...params], type: this.isPortfolio ? 'fund' : 'index', money: this.parameterJson?.money || 100000000 })
      if (mtycode == '200') {
        this.combinationId = data?.combinationId || ''
        this.showTestLoading = false
        // 获取回测数据
        this.getHoldingRateTrend()//曲线数据
        this.getOverallRiskReturn()//曲线下表格数据
        this.getAllocation()//资产配置权重
        // this.getPerFormanceCombination()//方案表现
        this.returnAnalysis()//收益贡献分析
        this.handleCommand('近期业绩')//方案表现入口默认近期
      }
      else {
        this.showTestLoading = false
        this.$message.error(mtymessage || '回测失败')
      }

    },
    // 曲线数据
    async getHoldingRateTrend () {
      let { data, mtycode, message } = await holdingRateTrend({ startDate: this.date[0], endDate: this.date[1], indexCode: this.indexSelected, combinationId: this.combinationId })
      if (mtycode === '200') {
        this.showEmpty = false
        let date = []
        let withdrawal = []
        let hsData = []
        let allData = []
        let excess = []
        for (let i = 0; i < data.length; i++) {
          date.push(data[i].date)
          withdrawal.push((data[i].drawdown * 100).toFixed(2))
          hsData.push((data[i].contrastReturn * 100).toFixed(2))
          allData.push((data[i].returnCum * 100).toFixed(2))
          excess.push((data[i].excess * 100).toFixed(2))
        }
        const line = [
          {
            name: '回撤',
            symbol: 'none',
            type: 'line',
            yAxisIndex: 1,
            lineStyle: {
              opacity: 0
            },
            areaStyle: {
              color: '#E9E6ED',
              opacity: 1
            },
            color: '#E9E6ED',
            data: withdrawal
          },
          {
            name: '基准收益',
            type: 'line',
            symbol: 'none',
            lineStyle: {
              width: 2
            },
            data: hsData
          },
          {
            name: '分析对象',
            type: 'line',
            symbol: 'none',
            lineStyle: {
              width: 2
            },
            data: allData
          },
          {
            name: '超额收益',
            type: 'line',
            symbol: 'none',
            lineStyle: {
              opacity: 0
            },
            areaStyle: {
              color: '#7388A9',
              opacity: 0.5
            },
            color: '#7388A9',
            data: excess
          },
        ]
        this.optionsPerformance = lineChartOption({
          grid: { left: '48px', right: '48px', top: '48px', bottom: '48px' }, // 位置
          dataZoom: false,
          toolbox: false,
          legend: {
            data: ['分析对象', '基准收益', '超额收益', '回撤'],
          },
          tooltip: {
            formatter: function (obj) {
              let value = `<div style="font-size:14px;">` + obj?.[0].axisValue + `</div>`;
              for (let i = 0; i < obj.length; i++) {
                value +=
                  `<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
                  `<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
                  obj?.[i].color +
                  `;"></div>` +
                  `<div style="font-family: PingFang SC;">` +
                  obj?.[i].seriesName +
                  '</div></div>' +
                  `<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
                  (Number(obj?.[i].value)) +
                  '%</div>' +
                  `</div>`;
              }
              return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
            }
          },
          xAxis: [
            {
              name: '',
              data: date
            }
          ],
          series: line,
          yAxis: [
            {
              type: 'value',
              name: '收益率',
              nameTextStyle: {
                align: "right"
              },
              formatter: function (value, index) {  //Y轴的自定义刻度值，对应上图
                return `${value}%`;
              },
            },
            {
              type: 'value',
              name: '回撤',
              nameTextStyle: {
                align: "left"
              },
              formatter: function (value, index) {  //Y轴的自定义刻度值，对应上图
                return `${value}%`;
              },
            },
          ],
        });
      }
      else {
        this.showEmpty = true
      }
    },
    // 回撤数据
    async getOverallRiskReturn () {
      let { data, mtycode, message } = await overallRiskReturn({ startDate: this.date[0], endDate: this.date[1], indexCode: this.indexSelected, combinationId: this.combinationId })
      if (mtycode === '200') {
        // console.log(data);
        this.tableDataPerformance = [{
          key: '区间收益', allValue: (data?.productList?.cumReturn * 100).toFixed(2) + '%' || '--', value: (data?.referenceList?.cumReturn * 100).toFixed(2) + '%' || '--'
        },
        { key: '年化收益', allValue: (data?.productList?.aveReturn * 100).toFixed(2) + '%' || '--', value: (data?.referenceList?.aveReturn * 100).toFixed(2) + '%' || '--' },
        { key: '年化波动', allValue: (data?.productList?.volatility * 100).toFixed(2) + '%' || '--', value: (data?.referenceList?.volatility * 100).toFixed(2) + '%' || '--' },
        { key: '夏普率', allValue: (data?.productList?.sharp * 1).toFixed(2) || '--', value: (data?.referenceList?.sharp * 1).toFixed(2) || '--' },
        { key: '信息率', allValue: (data?.productList?.information * 100).toFixed(2) || '--', value: '--' },
        { key: '最大回撤', allValue: (data?.productList?.maxDrawdown * 100).toFixed(2) + '%' || '--', value: (data?.referenceList?.maxDrawdown * 100).toFixed(2) + '%' || '--' },
        { key: '正收益率占比', allValue: (data?.productList?.winRatio * 100).toFixed(2) + '%' || '--', value: (data?.referenceList?.winRatio * 100).toFixed(2) + '%' || '--' },
        { key: '负收益率占比', allValue: (data?.productList?.loseRatio * 100).toFixed(2) + '%' || '--', value: (data?.referenceList?.loseRatio * 100).toFixed(2) + '%' || '--' },
        { key: '最大连续上涨天数', allValue: (data?.productList?.winDays * 1).toFixed(0) || '--', value: (data?.referenceList?.winDays * 1).toFixed(0) || '--' },
        { key: '最大连续下跌天数', allValue: (data?.productList?.loseDays * 1).toFixed(0) || '--', value: (data?.referenceList?.loseDays * 1).toFixed(0) || '--' },
        ]
      }
      else {
        this.tableDataPerformance = []
      }
    },
    //资产配置权重
    async getAllocation () {
      let { data, mtycode, message } = await assetAllocationWeight({ startDate: this.date[0], endDate: this.date[1], indexCode: this.indexSelected, combinationId: this.combinationId, type: this.isPortfolio ? 'fund' : 'index' })
      if (mtycode === '200') {
        let holdStyleObjDate = []
        for (let k = 0; k < data.length; k++) {
          // console.log(data[k]);
          holdStyleObjDate = holdStyleObjDate.concat(data[k].date)
        }
        holdStyleObjDate = [...new Set(holdStyleObjDate)].sort()
        // console.log(holdStyleObjDate);
        let holdStyleObjData = []
        for (let i = 0; i < data.length; i++) {
          holdStyleObjData.push({
            "name": data[i].name,
            "type": "line",
            "stack": "Total",
            "emphasis": {
              "focus": "series"
            },
            "symbol": "none",
            "areaStyle": {},
            "data": data[i].weight.map((item, index) => {
              return [data[i].date[index], item]
            })
          })
        }
        this.showStyleEmpty = false
        this.styleOptions = lineChartOption({
          grid: { left: '36px', right: '36px', top: '36px', bottom: '60px' }, // 位置
          dataZoom: true,
          toolbox: false,
          // legend: {
          //   data: legendArray,
          //   type: 'plain'
          // },
          tooltip: {
            formatter: function (obj) {
              var value = `<div style="font-size:14px;">` + obj?.[0].axisValue + `</div>`;
              for (let i = 0; i < obj.length; i++) {
                value +=
                  `<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
                  `<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
                  obj?.[i].color +
                  `;"></div>` +
                  `<div style="font-family: PingFang SC;">` +
                  obj?.[i].seriesName +
                  '</div></div>' +
                  `<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
                  Number(obj?.[i].value[1].toFixed(2)) +
                  '%</div>' +
                  `</div>`;
              }
              return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
            }
          },
          xAxis: [
            {
              name: '日期',
              data: holdStyleObjDate
            }
          ],
          series: holdStyleObjData,
          yAxis: [
            {
              min: 0,
              type: 'value',
              min: 0,
              formatter: function (value, index) {
                //Y轴的自定义刻度值，对应上图
                return `${value.toFixed(0)}%`;
              }
            }
          ]
        });
      }
      else {
        this.showStyleEmpty = true
      }

    },
    // 切换时间区间
    handleCommand (command) {
      this.selectStep = command
      if (this.selectStep == '近期业绩') {
        this.getPerFormanceCombination('since')
      }
      else {
        this.getPerFormanceCombination2(command == '月度' ? 'monthly' : command == '季度' ? 'quarterly' : command == '半年' ? 'halfyearly' : command == '年度' ? 'yearly' : 'yearly')

      }
    },
    //方案表现
    async getPerFormanceCombination (params) {
      this.columnList = [{
        value: 'name',
        name: '指标名称'
      }, {
        value: 'lastWeek',
        name: '近一周'
      }, {
        value: 'lastMounth',
        name: '近一月'
      }, {
        value: 'lastSeason',
        name: '近一季'
      }, {
        value: 'lastHalfYears',
        name: '近半年'
      }, {
        value: 'lastYear',
        name: '近一年'
      }, {
        value: 'lastThreeYear',
        name: '近三年'
      }, {
        value: 'lastFiveYear',
        name: '近五年'
      }, {
        value: 'customTime',
        name: '区间内'
      }]
      let { data, mtycode, message } = await planIndexPerformance({
        scene: "backtest",
        measure: [
          "cum_return", "volatility", "sharpe", "maxdrawdown"
        ],
        flag: "combination",
        startDate: this.date[0],
        endDate: this.date[1],
        indexCode: this.indexSelected,
        combinationId: this.combinationId,
        codes: [this.combinationId],
        cutFlag: params,
        // params: params
      })
      if (mtycode === '200') {
        this.dataPerformance = data.recentList.map(item => {
          if (item.name == '累计收益率' || item.name == '累计收益' || item.name == '最大回撤' || item.name == '波动率') {
            return {
              name: (item.name),
              yesterday: (item.yesterday * 100).toFixed(2) + '%' || '--',
              yearToDate: (item.yearToDate * 100).toFixed(2) + '%' || '--',
              lastWeek: (item.lastWeek * 100).toFixed(2) + '%' || '--',
              lastMounth: (item.lastMounth * 100).toFixed(2) + '%' || '--',
              lastSeason: (item.lastSeason * 100).toFixed(2) + '%' || '--',
              lastHalfYears: (item.lastHalfYears * 100).toFixed(2) + '%' || '--',
              lastYear: (item.lastYear * 100).toFixed(2) + '%' || '--',
              lastThreeYear: (item.lastThreeYear * 100).toFixed(2) + '%' || '--',
              lastFiveYear: (item.lastFiveYear * 100).toFixed(2) + '%' || '--',
              customTime: (item.customTime * 100).toFixed(2) + '%' || '--',
            }
          }
          else {
            return {
              name: (item.name),
              yesterday: (item.yesterday * 1).toFixed(2) || '--',
              yearToDate: (item.yearToDate * 1).toFixed(2) || '--',
              lastWeek: (item.lastWeek * 1).toFixed(2) || '--',
              lastMounth: (item.lastMounth * 1).toFixed(2) || '--',
              lastSeason: (item.lastSeason * 1).toFixed(2) || '--',
              lastHalfYears: (item.lastHalfYears * 1).toFixed(2) || '--',
              lastYear: (item.lastYear * 1).toFixed(2) || '--',
              lastThreeYear: (item.lastThreeYear * 1).toFixed(2) || '--',
              lastFiveYear: (item.lastFiveYear * 1).toFixed(2) || '--',
              customTime: (item.customTime * 1).toFixed(2) || '--',
            }
          }
        })
      }
      else {
        this.dataPerformance = []
      }
    },
    //方案表现年度or不同区间
    async getPerFormanceCombination2 (params) {
      this.columnList = [{}]
      let { data, mtycode, message } = await planIndexPerformance({
        // start_date: this.date[0],
        // end_date: this.date[1],
        // indexCode: this.indexSelected,
        // flag: '4',
        // cut_flag: params,
        // measure: ['cum_return', 'maxdrawdown', 'sharpe', 'volatility'],
        // ids: [{
        //   code: this.combinationId,
        //   type: 'combination'
        // }]
        scene: "backtest",
        measure: [
          "cum_return", "volatility", "sharpe", "maxdrawdown"
        ],
        flag: "combination",
        startDate: this.date[0],
        endDate: this.date[1],
        indexCode: this.indexSelected,
        combinationId: this.combinationId,
        codes: [this.combinationId],
        cutFlag: params,
      })
      if (mtycode === '200') {
        this.columnList = [{
          value: 'name',
          name: '指标名称'
        }]
        let flag_list = []
        let flag_list_data_1 = {
          name: '收益率'
        }
        let flag_list_data_2 = {
          name: '最大回撤'
        }
        let flag_list_data_3 = {
          name: '夏普率'
        }
        let flag_list_data_4 = {
          name: '波动率'
        }
        for (let i = 0; i < data.periodList.length; i++) {
          if (flag_list.indexOf(data.periodList[i].flag) < 0) {
            flag_list.push(data.periodList[i].flag)
            this.columnList.push({
              value: data.periodList[i].flag,
              name: data.periodList[i].flag
            })
          }
          flag_list_data_1[data.periodList[i].flag] = (data.periodList[i].cumReturn * 100).toFixed(2) + '%' || '--'

          flag_list_data_2[data.periodList[i].flag] = (data.periodList[i].maxdrawdown * 100).toFixed(2) + '%' || '--'

          flag_list_data_3[data.periodList[i].flag] = (data.periodList[i].sharpe * 1).toFixed(2) || '--'

          flag_list_data_4[data.periodList[i].flag] = (data.periodList[i].volatility * 100).toFixed(2) + '%' || '--'

        }
        this.dataPerformance = [flag_list_data_1, flag_list_data_2, flag_list_data_3, flag_list_data_4]
      }
      else {
        this.columnList = []
        this.dataPerformance = []
      }
    },
    // 收益贡献分析
    async returnAnalysis (params) {
      let { data, mtycode, message } = await returnContributeAnalysis({ startDate: this.date[0], endDate: this.date[1], indexCode: this.indexSelected, combinationId: this.combinationId, params: params })
      if (mtycode === '200') {
        this.showPerEmpty = false
        let PerObjDate = data?.[0]?.date || []
        let PerObjData = []
        let legendArray = []
        for (let i = 0; i < data.length; i++) {
          legendArray.push(data[i].name)
          PerObjData.push({
            "name": data[i].name,
            "type": "bar",
            "stack": "Total",
            "emphasis": {
              "focus": "series"
            },
            "symbol": "none",
            "areaStyle": {},
            "data": data[i].cumReturn
          })
        }
        this.PerOptions = lineChartOption({
          grid: { left: '140px', right: '35px', top: '10px', bottom: '60px' }, // 位置
          dataZoom: true,
          toolbox: false,
          legend: {
            data: legendArray,
            type: 'plain'
          },
          tooltip: {
            formatter: function (obj) {
              var value = `<div style="font-size:14px;">` + obj?.[0].axisValue + `</div>`;
              for (let i = 0; i < obj.length; i++) {
                value +=
                  `<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
                  `<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
                  obj?.[i].color +
                  `;"></div>` +
                  `<div style="font-family: PingFang SC;">` +
                  obj?.[i].seriesName +
                  '</div></div>' +
                  `<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
                  (Number(obj?.[i].value) * 100).toFixed(2) +
                  '%</div>' +
                  `</div>`;
              }
              return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
            }
          },
          xAxis: [
            {
              name: '日期',
              data: PerObjDate
            }
          ],
          series: PerObjData,
          yAxis: [
            {
              type: 'value',
              formatter: function (value, index) {
                //Y轴的自定义刻度值，对应上图
                return `${(value * 100).toFixed(0)}%`;
              }
            }
          ]
        });
        // console.log(this.PerOptions);
      }
      else {
        this.showPerEmpty = true
        this.PerOptions = {}

      }
    },
  }
};
</script>
<style lang="scss" scoped>
.back-test-content {
	.box-container {
		::-webkit-scrollbar {
			width: 2px; /* 垂直滚动条的宽度 */
			height: 4px; /* 水平滚动条的宽度 */
		}
		border-radius: 4px;
		border: 1px solid #d4d8e5;
		background: #fff;
		box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.1);
		padding: 0 20px 20px;
		.back-test-content-performance {
			border-radius: 4px;
			border: 1px solid #d4d8e5;
			background: #fff;
			margin-bottom: 20px;
			.back-test-content-performance-box {
				padding: 20px;
				.back-test-content-performance-box-title {
					color: rgba(0, 0, 0, 0.85);
					font-size: 18px;
					font-style: normal;
					font-weight: 500;
					line-height: 26px; /* 144.444% */
					padding-bottom: 14px;
					border-bottom: 2px solid #e9e9e9;
					margin-bottom: 20px;
				}
			}
		}
	}
}
</style>
<style>
.FTBdialog .el-dialog__body {
	padding-top: 0 !important;
}
</style>
