<!--  -->
<template>
	<div class="industryTheme">
		<!-- <el-select @change="command2()" v-model="value" filterable :placeholder="'请选择' + haveName">
			<el-option v-for="item in quarterList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
		</el-select> -->
		<div>
			<operator @resolveMathRange="resolveMathRange"></operator>
			<el-cascader
				@change="command2()"
				popper-class="industrySum"
				placeholder="跟踪类型"
				:options="quarterList"
				v-model="value"
				:props="{ multiple: true }"
				filterable
			></el-cascader>
		</div>
	</div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import operator from '@/pages/filter/fund/beta/componentsFilter/components/operator.vue';

export default {
	props: {
		haveName: {
			type: String,
			default: ''
		},
		dataX: {
			type: Object,
			default: {}
		},
		placeholder: {
			type: String
		},
		indexFlag: {
			type: Number
		},
		baseIndexFlag: {
			type: Number
		},
		datacategories: {
			type: Object
		}
	},
	//import引入的组件需要注入到对象中才能使用
	components: { operator },
	data() {
		//这里存放数据
		return {
			value: '',
			iconFlag: '',
			showBox: false,
			input: '',
			quarterList: [],
			industry_name: '',
			industry_nameT: '',
			options: [],
			mathRange: { mathRange: 'avg' }
		};
	},
	//监听属性 类似于data概念
	computed: {},
	//监控data中的数据变化
	watch: {
		datacategories(val) {
			this.getObject();
		},
		dataX(val) {
			if (val.dataResult && val.dataResult.length > 0) {
				this.showBox = true;
				this.industry_name = val.dataResult[0].value;
				this.value = this.industry_name;
			}
		}
	},
	//方法集合
	methods: {
		resolveMathRange(obj) {
			this.mathRange = obj;
			this.resolveFather();
		},
		resolveFather() {
			this.$emit('datacategoriesc', this.baseIndexFlag, this.indexFlag, this.industry_name, this.mathRange);
		},
		command2() {
			this.industry_name = this.value;
			this.resolveFather();
		},
		returnNaME() {
			let list = [];
			for (let k = 0; k < this.value.length; k++) {
				let x = this.quarterList.findIndex((item) => item.label == this.value[k][0]);
				list.push(this.quarterList[x].children[this.quarterList[x].children.findIndex((item) => item.value == this.value[k][1])].label);
			}

			return list;
		},
		getObject() {
			console.log(this.datacategories);
			if (JSON.stringify(this.datacategories) != '{}') {
				this.quarterList = [
					{ label: '宽基', value: '宽基', children: this.datacategories.index },
					{ label: '行业', value: '行业', children: this.datacategories.industries },
					{
						label: '主题',
						value: '主题',
						children: this.datacategories.theme.map((item) => {
							return {
								label: item.lable,
								value: item.value
							};
						})
					},
					{
						label: 'smartbeta',
						value: 'smartbeta',
						children: this.datacategories.barra.map((item) => {
							return {
								label: item.lable,
								value: item.value
							};
						})
					},
					{ label: '港股', value: '港股', children: [{ label: '所有港股', value: '港股' }] }
				];
			}
			this.$nextTick(() => {
				// 添加这段代码
				const $el = document.querySelectorAll('.el-cascader-panel .el-cascader-node[aria-owns]');
				Array.from($el).map((item) => item.removeAttribute('aria-owns'));
			});
		}
	},
	//生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	//生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		if (JSON.stringify(this.dataX) != '{}') {
			if (this.dataX.dataResult && this.dataX.dataResult.length > 0) {
				this.showBox = true;
				this.industry_name = this.dataX.dataResult[0].value;
				this.value = this.industry_name;
			}
		}
		this.getObject();
	},
	beforeCreate() {}, //生命周期 - 创建之前
	beforeMount() {}, //生命周期 - 挂载之前
	beforeUpdate() {}, //生命周期 - 更新之前
	updated() {
		// console.log(this.quarterList);
		if (!this.FUNC.isEmpty(this.quarterList)) {
			// console.log('1');
			this.$nextTick(() => {
				this.getObject();
			});
		}
	}, //生命周期 - 更新之后
	beforeDestroy() {}, //生命周期 - 销毁之前
	destroyed() {}, //生命周期 - 销毁完成
	activated() {} //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style>
.industry_theme_drapDown {
	height: 300px !important;
	overflow: auto !important;
}
.industrySum .el-cascader-panel .el-cascader-node__label {
	margin-left: 8px !important;
}
</style>
<style lang="scss" scoped>
//@import url(); 引入公共css类
.industryTheme {
	display: flex;
	align-items: center;
}
</style>
