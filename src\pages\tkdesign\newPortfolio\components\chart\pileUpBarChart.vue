<template>
		<div class="charts_fill_class" v-loading="loading">
			<el-empty image-size="160" v-if="showEmpty"></el-empty>
			<v-chart
				v-else
				ref="companySizeChange"
				:options="option"
				element-loading-text="暂无数据"
				element-loading-spinner="el-icon-document-delete"
				element-loading-background="rgba(239, 239, 239, 0.5)"
				class="charts_one_class"
				autoresize
				@legendselectchanged="handleLegendSelectChanged"
				@zr:dblclick="handleDblClick"
			></v-chart>
		</div>
</template>

<script>
import VChart from 'vue-echarts';

import { barChartOption } from '@/utils/chartStyle.js';
import { combinationHoldReturn} from '@/api/pages/tkAnalysis/portfolio.js';
export default {
	components: { VChart },
	data() {
		return {
			option: {},
			loading: true,
			showEmpty: true,
			doubleClick:false,
			legendChanged:false,
			legendData:{}
		};
	},
	watch: {
		doubleClick: {
			handler(val) {
				if(val && this.legendChanged){
					const chart = this.$refs.companySizeChange;
					let legendWai = this.legendData.name;
					for (const element in this.legendData.selected) {
						//显示当前legent 关闭非当前legent
						if (legendWai == element) {
							chart.dispatchAction({
								type: 'legendSelect',
								name: element
							});
						} else {
							chart.dispatchAction({
								type: 'legendUnSelect',
								name: element
							});
						}
					}
					this.doubleClick = false;
					this.legendChanged = false;
				}
			},
			immediate: true,
		},
	},
	methods: {
		handleDblClick(){
			this.doubleClick = true;
		},
		handleLegendSelectChanged (params)  {
			this.legendChanged = true;
			this.legendData = params;
		
			
		},
		async getData(param) {
			let res = await combinationHoldReturn(param);
			if(res.mtycode != 200){
				return;
			}
			if(res.data.length >0){
            this.showEmpty = false;
        }else{
            this.showEmpty = true;
		}
			const {date_list,data1,lengdList} = this.filterData(res.data);
			this.$emit('tableData',{date_list,data1,lengdList});
			this.loading = false;
			this.option = barChartOption({
				toolbox:false,
				tooltip: {
					formatter: function (obj) {
						   //数据排序
						   let list = obj;
						list.sort((a,b)=>{
							if(a.value-b.value < 0){
								return 1;
							}else{
								return -1;
							}
						})
						var value = `<div style="font-size:14px;">` + list?.[0].axisValue + `</div>`;
						for (let i = 0; i < list.length; i++) {
							value +=
								`<div style="width:100%;margin-top:8px;display:flex;justify-content:space-between;align-items:center;">` +
								`<div style="display:flex;align-items:center;"><div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:` +
									list?.[i].color +
								`;"></div>` +
								`<div style="font-family: PingFang SC;">` +
									list?.[i].seriesName +
								'</div></div>' +
								`<div style="color: rgba(0, 0, 0, 0.85);font-weight: 500;">` +
								(Number(list?.[i].value) * 1).toFixed(2) +
								'%</div>' +
								`</div>`;
						}
						return `<div style="padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
					}
				},
				color:['#4096ff', '#4096ff', '#7388A9', '#6F80DD', '#6C96F2', '#FD6865', '#83D6AE'],
				legend: {
					bottom: '0px',
					data: lengdList
				},
				grid:{
					bottom:80,
					left:30,
				},
				dataZoom: {
					bottom: 30,
				},
				xAxis: [
					{
						boundaryGap: true,
						data: date_list,
						axisPointer: {
							type: 'shadow'
						},
						splitLine:{
							show:true,
							interval:0
						},
						splitNumber:0,
						interval:0,
					}
				],
				yAxis: [
					{
						type: 'value',
						name:'收益率',
						nameLocation: 'middle' ,
						nameGap: 30 ,
						nameTextStyle:{
							fontSize: 36
						}
					
					}
					
				],
				series: data1
			});
		},
		filterData(data){
			
			let dateList = []
			data.forEach((item)=>{
				item.date.forEach((date1)=>{
					dateList.push(date1)
				})
			});
			const date_list = [...new Set(dateList)].sort();
			const data1= data.map((item)=>{
				let data = [];
				date_list.forEach((date1)=>{
					let hasDate = item.date.indexOf(date1);
					if(hasDate !== -1){
						data.push(parseInt(item.cumReturn[hasDate]*10000) / 100)
					}else{
						data.push(0)
					}
				})
				return {
					name: item.name,
					type: 'bar',
					barWidth:"100%",
					stack: 'machine',
					data,
				}
			});
			const lengdList= data.map((item)=>{
				return {
					name: item.name,
					label:item.name,
					prop:item.name
				}
			});
			return {date_list,data1,lengdList}
		}
	}
};
</script>

<style>
.charts_one_class{
	padding: 0;
	box-shadow: none;
	height: 450px;
}
</style>
