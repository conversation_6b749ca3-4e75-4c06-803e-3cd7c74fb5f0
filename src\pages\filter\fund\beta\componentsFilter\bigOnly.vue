<!--  -->
<template>
	<div class="bigOnly">
		<!-- <el-select @change="changeNode" v-model="equitytype" placeholder="请选择大行业分类">
			<el-option v-for="item in fundType == 'equity' ? optionstype : ''" :key="item.value" :label="item.label" :value="item.value">
			</el-option>
		</el-select> -->
		<el-cascader
			v-model="equitytype"
			@change="changeNode"
			placeholder="请选择大行业分类"
			:options="fundType == 'equity' ? optionstype : ''"
			:props="{ expandTrigger: 'hover' }"
		>
		</el-cascader>
	</div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';

export default {
	props: {
		dataX: {
			type: Object,
			default: {}
		},
		placeholder: {
			type: String
		},
		indexFlag: {
			type: Number
		},
		baseIndexFlag: {
			type: Number
		},
		fundType: {
			type: String,
			default: 'equity'
		}
	},
	//import引入的组件需要注入到对象中才能使用
	components: {},
	data() {
		//这里存放数据
		return {
			equitytype: '',
			optionstype: [
				{
					label: '中周期',
					value: '中周期'
				},
				{
					label: '大金融',
					value: '大金融'
				},
				{
					label: '大科技',
					value: '大科技'
				},
				{
					label: '大周期',
					value: '大周期'
				},
				{
					label: '大消费',
					value: '大消费'
				},
				{
					label: '未突出',
					value: '未突出'
				}
			]
		};
	},
	//监听属性 类似于data概念
	computed: {},
	//监控data中的数据变化
	watch: {
		dataX(val) {
			if (val.dataResult && val.dataResult.length > 0) {
				this.equitytype = val.dataResult[0].value;
			}
		}
	},
	//方法集合
	methods: {
		changeNode(e) {
			// console.log(e);
			this.$emit('bigOnlyChange', this.baseIndexFlag, this.indexFlag, e, this.FUNC.isEmpty(e));
		}
	},
	//生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	//生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		if (JSON.stringify(this.dataX) != '{}') {
			if (this.dataX.dataResult && this.dataX.dataResult.length > 0) {
				this.equitytype = this.dataX.dataResult[0].value;
			}
		}
	},
	beforeCreate() {}, //生命周期 - 创建之前
	beforeMount() {}, //生命周期 - 挂载之前
	beforeUpdate() {}, //生命周期 - 更新之前
	updated() {}, //生命周期 - 更新之后
	beforeDestroy() {}, //生命周期 - 销毁之前
	destroyed() {}, //生命周期 - 销毁完成
	activated() {} //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
</style>
