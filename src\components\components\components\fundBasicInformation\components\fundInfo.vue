<template>
	<div class="px-20 py-20 basic_info" id="fundInfo">
		<basic-info-name-and-nav ref="basicInfoNameAndNav"></basic-info-name-and-nav>
		<!-- <association-pool></association-pool> -->
		<basic-return-info ref="basicReturnInfo"></basic-return-info>
		<basic-info-fund ref="basicInfoFund"></basic-info-fund>
		<basic-fund-capability ref="basicFundCapability"></basic-fund-capability>
		<basic-brinson-rank v-if="info.type != 'money'" ref="basicBrinsonRank"></basic-brinson-rank>
	</div>
</template>

<script>
// 基金基础信息&净值
import basicInfoNameAndNav from './components/basicInfoNameAndNav.vue';
// 关联的基金池
import associationPool from './components/associationPool.vue';
// 基础收益率
import basicReturnInfo from './components/basicReturnInfo.vue';
// 基金基础信息
import basicInfoFund from './components/basicInfoFund.vue';
// 基金基础能力项
import basicFundCapability from './components/basicFundCapability.vue';
// 基础信息Brinson排名
import basicBrinsonRank from './components/basicBrinsonRank.vue';
export default {
	components: {
		basicInfoNameAndNav,
		associationPool,
		basicReturnInfo,
		basicInfoFund,
		basicFundCapability,
		basicBrinsonRank
	},
	data() {
		return {
			info: {}
		};
	},
	methods: {
		getData(info) {
			this.info = info;
			this.$refs['basicInfoNameAndNav']?.getData(this.info);
			this.$refs['basicReturnInfo']?.getData(this.info);
			this.$refs['basicInfoFund']?.getData(this.info);
			this.$refs['basicFundCapability']?.getData(this.info);
			if (this.info.type != 'money') {
				this.$refs['basicBrinsonRank']?.getData(this.info);
			}
		},
		createPrintWord() {
			let list = [
				...this.$exportWord.exportFundBasicInfo(this.info, {}),
				...(this.$refs['basicInfoNameAndNav']?.createPrintWord() || []),
				...(this.$refs['basicReturnInfo']?.createPrintWord() || []),
				...(this.$refs['basicInfoFund']?.createPrintWord() || []),
				...(this.$refs['basicFundCapability']?.createPrintWord() || [])
			];
			if (this.info.type != 'money') {
				list.push(...(this.$refs['basicBrinsonRank']?.createPrintWord() || []));
			}
			return list;
		}
	}
};
</script>
<style lang="scss" scoped>
.basic_info {
	width: 100%;
	max-width: 652px;
	height: 660px;
	border: 1px solid #d9d9d9;
	border-radius: 4px;
	background-color: #ffffff;
	box-shadow: 0px 1px 2px 0px rgba(0, 0, 18, 0.1);
	font-family: Monospaced Number, Chinese Quote, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, PingFang SC, Hiragino Sans GB,
		Microsoft YaHei, Helvetica Neue, Helvetica, Arial, sans-serif;
}
</style>
